package main

//go:generate go run github.com/swaggo/swag/cmd/swag init -d "./http/router/" -g "routers.go" --parseInternal --parseDependency --quiet

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"

	"git.wndv.co/go/continuous-profiling/agent"
	"git.wndv.co/lineman/fleet-distribution/cmd/api/di"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

// @title Driver
// @version 1.0
func main() {
	config.AutomaticLookupEnv()
	defer safe.RecoverAlertPanic()
	containers, close, err := di.InitializeContainer()
	if err != nil {
		panic(fmt.Errorf("cannot initialize container: %v", err))
	}

	sig := make(chan os.Signal, 1)
	signal.Notify(sig, os.Interrupt, syscall.SIGTERM)

	logrus.Infof("Start load preload resource")
	if err := containers.Preload.Load(); err != nil {
		panic(fmt.Errorf("cannot load preload resource: %v", err))
	}
	logrus.Infof("Finish load preload resource")

	containers.CronIntervalRunner.Run()

	agent.New("lineman-driver", agent.WithCPUProfile(10*time.Second), agent.WithHeapProfile())

	containers.InternalServer.EnableHealthCheck()

	<-sig
	logrus.Info("received os signal, shutting down system...")
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	closeAllResource := func(ctx context.Context) error {
		containers.CleanupPriority.Cleanup()
		// close all resource
		close()

		return nil
	}

	if err := closeAllResource(ctx); err != nil {
		panic(fmt.Errorf("system is shutdown with error %v", err))
	} else {
		logrus.Info("System is shutdown successfully")
	}

}
