package main

import (
	"context"
	"time"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/go/pillars"
	consumerdi "git.wndv.co/lineman/fleet-distribution/cmd/distribution-consumer/di"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

func main() {
	config.AutomaticLookupEnv()
	logx.SetDefaultLinemanLogger()
	defer safe.RecoverAlertPanic()
	container, cleanup, err := consumerdi.InitializeContainer()
	if err != nil {
		panic(err)
	}
	go container.DistributionKafkaConsumer.Run(context.Background())

	container.PillarInternalServer.EnableHealthCheck()

	pillars.WaitForTerminatingSignal()

	shutdownChan := make(chan struct{})
	go func() {
		cleanup()
		shutdownChan <- struct{}{}
	}()

	select {
	case <-time.After(30 * time.Second):
		logx.Error().Msg("cannot finishing graceful shutdown within 30 seconds. force shutdown")
	case <-shutdownChan:
		logx.Info().Msg("graceful shutdown finish")
	}
}
