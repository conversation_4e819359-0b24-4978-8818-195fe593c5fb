//go:build integration_test

package incentives

import (
	"context"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"

	"go.mongodb.org/mongo-driver/bson"
)

const (
	DriverID = "DRV_CHIANG_MAI_2_ONLINE"
)

func TestIncentiveCronCompute(t *testing.T) {

	createContainer := func() *ittest.IntegrationTestContainer {
		container := ittest.NewContainer(t)

		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_incentive")
		if err != nil {
			t.Errorf("[TestAdminAPI_OrderDistanceCompensation] Unexpected error occurred at InitFixture: %v", err.Error())
		}

		return container
	}

	t.Run("compute incentive success with today order", func(tt *testing.T) {
		container := createContainer()

		now := timeutil.BangkokNow()
		d, err := timeutil.ParseStringToDate("today", now)
		require.NoError(tt, err)

		start := timeutil.DateTruncate(d).In(time.UTC)
		end := timeutil.DateCeiling(d).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Daily, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "today")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "today"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) > 0, nil
		})
	})

	t.Run("compute incentive success with yesterday order", func(tt *testing.T) {
		container := createContainer()

		now := timeutil.BangkokNow()
		d, err := timeutil.ParseStringToDate("yesterday", now)
		require.NoError(tt, err)

		start := timeutil.DateTruncate(d).In(time.UTC)
		end := timeutil.DateCeiling(d).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Daily, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "yesterday")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "yesterday"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) > 0, nil
		})
	})

	t.Run("compute incentive success with specific date", func(tt *testing.T) {
		container := createContainer()
		now := timeutil.BangkokNow()
		d, err := timeutil.ParseStringToDate("03/02/2022", now)
		require.NoError(tt, err)

		start := timeutil.DateTruncate(d).In(time.UTC)
		end := timeutil.DateCeiling(d).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Daily, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "03/02/2022")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "03/02/2022"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) > 0, nil
		})
	})

	t.Run("compute incentive should be not compute messenger order", func(tt *testing.T) {
		container := createContainer()
		now := timeutil.BangkokNow()
		d, err := timeutil.ParseStringToDate("today", now)
		require.NoError(tt, err)

		start := timeutil.DateTruncate(d).In(time.UTC)
		end := timeutil.DateCeiling(d).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Daily, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceMessenger, "today")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").Body().JSON(gin.H{
			"date": "today",
		}).Build().Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) == 0, nil
		})
	})

	t.Run("compute daily incentive yesterday success when date range it's not yesterday", func(tt *testing.T) {

		container := createContainer()

		now := timeutil.BangkokNow()
		fiveDaysAgo := now.Add(time.Hour * (24 * 5 * -1))
		nextFiveDays := now.Add(time.Hour * (24 * 5))

		start := timeutil.DateTruncate(fiveDaysAgo).In(time.UTC)
		end := timeutil.DateCeiling(nextFiveDays).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Daily, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "yesterday")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "yesterday"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) > 0, nil
		})
	})

	t.Run("compute incentive success with payment type PERIOD", func(tt *testing.T) {

		container := createContainer()

		now := timeutil.BangkokNow()
		fiveDaysAgo := now.Add(time.Hour * (24 * 5 * -1))
		d, err := timeutil.ParseStringToDate("yesterday", now)
		require.NoError(tt, err)

		yesterday := timeutil.DateCeiling(d)

		start := timeutil.DateTruncate(fiveDaysAgo).In(time.UTC)
		end := timeutil.DateCeiling(yesterday).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Period, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "yesterday")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "yesterday"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) > 0, nil
		})
	})

	t.Run("not process incentive when not in date range", func(tt *testing.T) {
		container := createContainer()

		now := timeutil.BangkokNow()
		tomorrow := now.Add(time.Hour * (24 * 1))
		nextFiveDays := now.Add(time.Hour * (24 * 5))

		start := timeutil.DateTruncate(tomorrow).In(time.UTC)
		end := timeutil.DateCeiling(nextFiveDays).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Daily, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "today")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "today"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) == 0, nil
		})

	})

	t.Run("compute incentive daily success with yesterday order with ar cr", func(tt *testing.T) {
		container := createContainer()

		now := timeutil.BangkokNow()
		d, err := timeutil.ParseStringToDate("yesterday", now)
		require.NoError(tt, err)

		start := timeutil.DateTruncate(d).In(time.UTC)
		end := timeutil.DateCeiling(d).In(time.UTC)

		ar := 80.00
		cr := 30.00
		prepareIncentive(container, tt, start, end, incentive.Daily, &ar, &cr)
		prepareDriverOderInfo(container, tt, DriverID)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "yesterday")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "yesterday"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) > 0, nil
		})
	})

	t.Run("compute incentive PERIOD success with yesterday order with ar cr", func(tt *testing.T) {
		container := createContainer()

		now := timeutil.BangkokNow()
		fiveDaysAgo := now.Add(time.Hour * (24 * 5 * -1))
		d, err := timeutil.ParseStringToDate("yesterday", now)
		require.NoError(tt, err)

		yesterday := timeutil.DateCeiling(d)

		start := timeutil.DateTruncate(fiveDaysAgo).In(time.UTC)
		end := timeutil.DateCeiling(yesterday).In(time.UTC)

		cr := 30.00
		ar := 80.00

		prepareIncentive(container, tt, start, end, incentive.Period, &ar, &cr)
		prepareDriverOderInfo(container, tt, DriverID)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "yesterday")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "yesterday"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) > 0, nil
		})
	})

	t.Run("not compute incentive daily yesterday order when not match ar cr", func(tt *testing.T) {
		container := createContainer()

		now := timeutil.BangkokNow()
		fiveDaysAgo := now.Add(time.Hour * (24 * 5 * -1))
		d, err := timeutil.ParseStringToDate("yesterday", now)
		require.NoError(tt, err)

		yesterday := timeutil.DateCeiling(d)

		start := timeutil.DateTruncate(fiveDaysAgo).In(time.UTC)
		end := timeutil.DateCeiling(yesterday).In(time.UTC)

		cr := 10.00
		ar := 100.00

		prepareIncentive(container, tt, start, end, incentive.Period, &ar, &cr)
		prepareDriverOderInfo(container, tt, DriverID)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")
		for i := 0; i < 10; i++ {
			createCompletedOrder(tt, container, model.ServiceFood, "yesterday")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "yesterday"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) == 0, nil
		})
	})

	t.Run("process incentive when complete time not matched, created time matched", func(tt *testing.T) {
		container := createContainer()

		now := timeutil.BangkokNow()
		d, err := timeutil.ParseStringToDate("today", now)
		require.NoError(tt, err)

		start := timeutil.DateTruncate(d).In(time.UTC)
		end := timeutil.DateCeiling(d).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Daily, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")

		for i := 0; i < 10; i++ {
			createCompletedOrderWithDiffCreatedCompletedTime(tt, container, model.ServiceFood, "today", "yesterday")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "today"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) > 0, nil
		})
	})

	t.Run("not process incentive when complete time matched, created time not matched", func(tt *testing.T) {
		container := createContainer()

		now := timeutil.BangkokNow()
		d, err := timeutil.ParseStringToDate("today", now)
		require.NoError(tt, err)

		start := timeutil.DateTruncate(d).In(time.UTC)
		end := timeutil.DateCeiling(d).In(time.UTC)

		prepareIncentive(container, tt, start, end, incentive.Daily, nil, nil)
		siDbHelp := testutil.NewDBHelper(tt, container.DBConnectionForTest, "setting_incentives")
		assert.True(tt, siDbHelp.Count(bson.M{"active": true, "region_code": "CHIANG_MAI_2"}) > 0, "have an active incentive")

		for i := 0; i < 10; i++ {
			createCompletedOrderWithDiffCreatedCompletedTime(tt, container, model.ServiceFood, "yesterday", "today")
		}

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/cron/incentives").
			Body().
			JSON(gin.H{"date": "today"}).
			Build().
			Send(container.GinEngineRouter).AssertResponseCode(tt, 202)

		CheckForSuccess(tt, func() (bool, error) {
			var resp []payment.TransactionResponse
			ctx := testutil.NewContextWithRecorder()
			ctx.SetGET("/v1/admin/transactions?driverId=%s&type[]=INCENTIVE", DriverID).
				Send(container.GinEngineRouter).
				AssertResponseCode(tt, 200).DecodeJSONResponse(&resp)
			return len(resp) == 0, nil
		})
	})
}

func createCompletedOrder(t *testing.T, container *ittest.IntegrationTestContainer, serviceType model.Service, time string) {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPOST("/v1/internal/incentive_order").Body().JSON(gin.H{
		"driverId":    DriverID,
		"serviceType": serviceType,
		"lat0":        18.79191774423444,
		"lng0":        98.97720336914062,
		"createdAt":   time,
		"region":      "CHIANG_MAI_2",
	}).Build()
	container.GinEngineRouter.HandleContext(ctx.GinCtx())
	ctx.AssertResponseCode(t, 200)
}

func createCompletedOrderWithDiffCreatedCompletedTime(t *testing.T, container *ittest.IntegrationTestContainer, serviceType model.Service, created string, completed string) {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPOST("/v1/internal/incentive_order").Body().JSON(gin.H{
		"driverId":    DriverID,
		"serviceType": serviceType,
		"lat0":        18.79191774423444,
		"lng0":        98.97720336914062,
		"createdAt":   created,
		"completedAt": completed,
		"region":      "CHIANG_MAI_2",
	}).Build()
	container.GinEngineRouter.HandleContext(ctx.GinCtx())
	ctx.AssertResponseCode(t, 200)
}

func CheckForSuccess(t *testing.T, fn func() (bool, error)) {
	timeout, cancelFunc := context.WithTimeout(context.Background(), time.Second*2)
	t.Cleanup(cancelFunc)
	ticker := time.NewTicker(time.Millisecond * 50)
	t.Cleanup(ticker.Stop)
	for {
		select {
		case <-timeout.Done():
			t.Error("timeout, not success")
			return
		case <-ticker.C:
			b, err := fn()
			if err != nil {
				t.Error(err)
				return
			}
			if b {
				return
			}
		}
	}
}

func prepareIncentive(ctn *ittest.IntegrationTestContainer, t testing.TB, start, end time.Time, inType incentive.PaymentType, ar, cr *float64) {
	inc1, err := ctn.DataStoreIncentiveRepository.Get(context.Background(), "1q2w3e4r5t6y7u8i")
	require.NoError(t, err)
	inc1.DateRange = incentive.DateRange{
		Start: start,
		End:   end,
	}

	inc1.PaymentType = inType

	if ar != nil {
		inc1.AR = ar
	}

	if cr != nil {
		inc1.CR = cr
	}

	err = ctn.DataStoreIncentiveRepository.Update(context.Background(), inc1.IncentiveID, inc1)
	require.NoError(t, err)
}

func prepareDriverOderInfo(ctn *ittest.IntegrationTestContainer, t testing.TB, driverID string) {
	ctx := context.Background()
	doi, err := ctn.DriverOrderInfoRepository.GetOrCreate(ctx, driverID)
	require.NoError(t, err)

	now := timeutil.BangkokNow()
	doi.DailyCounts = []model.DailyCount{
		{
			Year:                 now.Year(),
			Month:                now.Month(),
			Day:                  now.Day() - 1,
			AutoAssigned:         10,
			AutoAssignedAccepted: 9,
			CancelledNotFree:     2,
		},
	}

	update := bson.M{
		"$set": bson.M{"daily_counts": doi.DailyCounts},
	}

	err = ctn.DriverOrderInfoDataStore.Update(ctx, bson.M{"driver_id": DriverID}, update)
	require.NoError(t, err)
}
