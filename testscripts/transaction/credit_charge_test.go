//go:build integration_test
// +build integration_test

package transaction

import (
	"context"
	"testing"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestCreditChargeApproval(t *testing.T) {
	//Steps:
	//1. create new credit charge for approval
	//2. approve that request
	//3. check driver purchase credit

	ctn := ittest.NewContainer(t)
	driverId := "DRV_PATTAYA_ONLINE"
	driverTx, err := ctn.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverId)
	require.NoError(t, err)

	//step1: create approval
	createApprovalReq := testutil.NewContextWithRecorder().
		SetPOST("/v1/admin/approvals").
		Body().RawJSON(`
		{
			"category": "CREDIT",
			"action": "CHARGE",
			"requestedBy": "Operation Team",
			"info": {
				"driverId": "DRV_PATTAYA_ONLINE",
				"amount": 1,
				"subType": "WITHDRAW_ERROR_RECALL",
				"transRefId": "citi1"
			}
		}
		`).Build()
	var resp payment.ApprovalRes
	createApprovalReq.Send(ctn.GinEngineRouter).
		AssertResponseCode(t, 200).
		DecodeJSONResponse(&resp)

	//step2: approve
	approveApprovalReq := testutil.NewContextWithRecorder().
		SetPUT("/v1/admin/approvals/%s/approve", resp.ApprovalID)
	approveApprovalReq.Send(ctn.GinEngineRouter).
		AssertResponseCode(t, 200)

	//step3: check
	newDriverTx, err := ctn.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverId)
	require.NoError(t, err)
	assert.Equal(t, driverTx.PurchaseCreditBalance.Sub(types.NewMoney(1)), newDriverTx.PurchaseCreditBalance)
	tx, err := ctn.DataStoreTransactionRepository.FindByTransactionRefID(context.Background(), "citi1")
	require.NoError(t, err)
	assert.Equal(t, driverId, tx.Info.DriverID)
}
