---
kind: pipeline
type: docker
name: default

steps:
- name: go113 vendor
  image: golang:1.13.8-buster
  commands:
    - apt update && apt install --no-install-recommends -y git build-essential
    - go mod vendor

- name: go113 test
  image: golang:1.13.8-buster
  commands:
    - ./scripts/test.sh -mod=vendor

- name: go113 vet
  image: golang:1.13.8-buster
  commands:
    - go vet -mod=vendor ./...

trigger:
  branch:
    - develop
    - master
  ref:
    - refs/pull/*/head
