# This file is managed by makew
# DO NOT EDIT! IT WILL BE REPLACED

image: harbor.linecorp.com/lineman-infra/golang:1.24-ci
include:
- project: sre-lm/ci-cd-templates
  file:
  - jobs/env.yaml
  - jobs/deploy.yaml
  - jobs/notification.yaml
- project: sre-lm/ci-cd-templates
  file:
  - jobs/build.yaml
  - jobs/copy.yaml
- project: sre-lm/ci-cd-templates
  file:
  - argo/argomr.yaml
- local: /.gitlab-ci/.fleet-distribution-deploy.yml
- local: /.gitlab-ci/.release.yml
- local: /.gitlab-ci/.reset-branch.yml
variables:
  CGO_ENABLED: "1"
  ENABLE_MONGO_TRANSACTION: "true"
  FF_USE_FASTZIP: "true"
  GOMODCACHE: $CI_PROJECT_DIR/.go/mod
  GOTESTFLAGS: -tags integration_test
  GOTOOLCHAIN: local
  GO_VERSION: 1.24-ci
  LM_PROJECT_NAME: driver
  MAKEW_SKIP_BUILD:
    description: Skip build task if <PERSON><PERSON> is already built
    value: "false"
  MINIO_ACCESS_KEY: admin
  MINIO_SECRET_KEY: password
  MONGO_INITDB_ROOT_PASSWORD: test_password
  MONGO_INITDB_ROOT_USERNAME: test_user
  MONGO_TEST_HOST: mongodb
  MONGO_TEST_PORT: "27017"
  MONGO_TEST_VERSION: 4.2.12
  REDIS_TEST_HOST: redis
  REDIS_TEST_PORT: "6379"
  REP_CONNECTION_URI: amqp://guest:guest@rabbitmq:35672/test
  VOS_ENDPOINT: http://minio:9000
  VOS_INTERNAL_ENDPOINT: http://minio:9000
stages:
- test
- lint
- scan
- build
- deploy
- post-deploy
.before-tests:
  extends:
  - .basejob
  - .slack:msg
  rules:
  - if: $CI_PIPELINE_SOURCE == "merge_request_event"
  - if: "$CI_PIPELINE_SOURCE == \"push\" && ($CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH || $CI_COMMIT_BRANCH =~ /^release\\//)"
  before_script:
  - "/bin/echo -e \"machine git.wndv.co\\nlogin gitlab-ci-token\\npassword ${CI_JOB_TOKEN}\" > ~/.netrc"
  - go telemetry off
  - export PATH=.go/bin:$PATH
  - go install git.wndv.co/go/makew@v0.66.0
  - makew download --install --no-interactive --phase test
  variables:
    GOPATH: $CI_PROJECT_DIR/.go
  cache:
    key:
      prefix: $CI_COMMIT_REF_SLUG
      files:
      - go.mod
      - go.sum
    paths:
    - .go/
    when: always
lint:
  extends:
  - .before-tests
  stage: lint
  script:
  - make lint
  - go mod verify
  needs: []
test:
  extends:
  - .before-tests
  stage: test
  script:
  - make cover.xml
  coverage: "/Total Coverage: \\d+\\.\\d+%/"
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: cover.xml
      junit: report.xml
  services:
  - name: harbor.linecorp.com/lineman-infra/mongo:4.2.12.repl.2
    alias: mongodb
  - name: harbor.linecorp.com/lineman-infra/redis-for-test:4.0.10.6379
    alias: redis
  - name: harbor.linecorp.com/lineman-infra/minio-for-test:latest
    command:
    - server
    - /tmp
    alias: minio
  - name: harbor.linecorp.com/lineman-infra/rabbitmq-for-test:3
    alias: rabbitmq
  needs: []
build:
  extends:
  - .build:kinako
  - .slack:msg
  stage: build
  rules:
  - if: $MAKEW_SKIP_BUILD == "true"
    when: never
  - if: $CI_COMMIT_TAG
    when: never
  - if: $CI_COMMIT_BRANCH =~ /^(dev|develop|master)$/
    when: on_success
  - if: $CI_COMMIT_BRANCH == "beta"
    when: on_success
  - if: "$CI_COMMIT_BRANCH =~ /^release\\//"
    when: on_success
  - if: $CI_PIPELINE_SOURCE == "web"
    when: on_success
  variables:
    LM_APP_IMAGE: harbor.linecorp.com/lineman/fleet-distribution
tag:
  extends:
  - .copy_docker
  stage: build
  rules:
  - if: $CI_COMMIT_TAG
  variables:
    DEST: harbor.linecorp.com/lineman/fleet-distribution:$CI_COMMIT_TAG
    SRC: harbor.linecorp.com/lineman/fleet-distribution:$CI_COMMIT_SHORT_SHA
deploy to lm-ceylon/beta:
  extends:
  - .lm-argomr
  stage: deploy
  rules:
  - if: $CI_COMMIT_BRANCH == "beta"
    when: on_success
  environment: lm-ceylon/beta
  variables:
    NAMESPACE: lineman-beta
    VERSION_FILE_PATH: projects/lineman/driver/fleet-distribution/vars/vars.json
  tags: [lineman, nonprod]
deploy to lm-ceylon/beta2:
  extends:
  - .lm-argomr
  stage: deploy
  rules:
  - if: $CI_COMMIT_BRANCH =~ /^(dev|develop|master)$/
    when: manual
  - if: $CI_COMMIT_BRANCH == "beta"
    when: manual
  - if: $CI_PIPELINE_SOURCE == "web"
    when: manual
  environment: lm-ceylon/beta2
  variables:
    NAMESPACE: lineman-beta2
    VERSION_FILE_PATH: projects/lineman/driver/fleet-distribution/vars/vars.json
  tags: [lineman, nonprod]
deploy to lm-ceylon/rc:
  extends:
  - .lm-argomr
  stage: deploy
  rules:
  - if: $CI_COMMIT_BRANCH == "master"
    when: on_success
  environment: lm-ceylon/rc
  variables:
    NAMESPACE: lineman-rc
    VERSION_FILE_PATH: projects/lineman/driver/fleet-distribution/vars/vars.json
  tags: [lineman, rc]
deploy to lm-ceylon/prod:
  extends:
  - .lm-argomr
  stage: deploy
  rules:
  - if: $CI_COMMIT_TAG
    when: manual
  environment: lm-ceylon/prod
  variables:
    NAMESPACE: lineman-prod
    VERSION_FILE_PATH: projects/lineman/driver/fleet-distribution/vars/vars.json
  tags: [lineman, prod]
