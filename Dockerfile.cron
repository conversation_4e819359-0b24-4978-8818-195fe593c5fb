FROM harbor.linecorp.com/lineman-infra/golang:1.24-ci AS builder
ARG BIN_NAME=cron
ENV CGO_ENABLED 1
ENV GOPRIVATE git.wndv.co/lineman/*
COPY .netrc /root/
COPY . /lineman-driver/
WORKDIR /lineman-driver
ENV GOPATH /lineman-driver/.go
RUN go build -o ./bin/lineman-driver-$BIN_NAME -tags netgo git.wndv.co/lineman/fleet-distribution/cmd/$BIN_NAME

FROM harbor.linecorp.com/lineman-infra/debian-base:bookworm
ARG BIN_NAME=api
USER 0
COPY --from=builder /lineman-driver/bin/lineman-driver-$BIN_NAME /app/entrypoint
COPY --from=builder /lineman-driver/dev_kek.json /app/dev_kek.json
# Give permission for user 1000 to write to /app
RUN chown -R 1000:1000 /app
USER 1000
EXPOSE 8080
WORKDIR /
ENTRYPOINT ["/app/entrypoint"]
