# This file is managed by makew
# DO NOT EDIT! IT WILL BE REPLACED
# The canonical copy is at https://git.wndv.co/go/makew/-/blob/master/plugin/dockerfile/Dockerfile

FROM harbor.linecorp.com/lineman-infra/golang:1.24-ci AS builder
ARG CI_JOB_TOKEN
ENV GOTOOLCHAIN=local
RUN go telemetry off
RUN /bin/echo -e "machine git.wndv.co\nlogin gitlab-ci-token\npassword ${CI_JOB_TOKEN}" > ~/.netrc
RUN go install git.wndv.co/go/makew@v0.66.0
RUN go install github.com/golang/mock/mockgen@v1.6.0
RUN go install git.wndv.co/go/providergen@v1.3.0
RUN curl -sSL "https://github.com/bufbuild/buf/releases/download/v1.45.0/buf-$(uname -s)-$(uname -m)" -o "/usr/local/bin/buf" && chmod +x /usr/local/bin/buf
RUN go install google.golang.org/protobuf/cmd/protoc-gen-go@v1.35.1
COPY . /build
WORKDIR /build
RUN make

FROM harbor.linecorp.com/lineman-infra/debian-base:bookworm
USER 0
COPY --from=builder /build/.bin/api /opt/api
COPY --from=builder /build/.bin/distribution-consumer /opt/distribution-consumer
COPY ./dev_kek.json /opt/dev_kek.json
USER 1000
WORKDIR /
CMD ["/opt/."]
