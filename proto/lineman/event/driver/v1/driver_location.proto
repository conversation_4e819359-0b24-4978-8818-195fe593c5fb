syntax = "proto3";

package lineman.event.driver.v1;

import "google/protobuf/timestamp.proto";

message UpdateLocationEvent {
  // The Date and time in UTC when activity occurred
  google.protobuf.Timestamp client_timestamp = 1;
  // The Date and time in UTC when server receives event
  google.protobuf.Timestamp server_timestamp = 2;
  // The global unique sortable id which generated from server
  string event_id = 3;
  // The driver id itself
  string driver_id = 4;
  // The latitude of driver's location
  double lat = 5;
  // The longitude of driver's location
  double lng = 6;
  // The h3 resolution level 15 of driver location
  string h3_index = 7;
  // The driver's working region
  string driver_region = 8;
  // The current list of working orders
  repeated UpdateLocationOrder orders = 9;
  // The current of a working trip id
  string trip_id = 10;
  // The current of a working trip status
  string trip_status = 11;
  // The flagged that driver is inactive
  optional bool is_inactive = 12;
  // The Date and time in UTC when last update location attempted occurred
  optional google.protobuf.Timestamp last_update_location_attempt_timestamp = 13;
  // The cause of driver inactive
  optional InactiveCausedBy inactive_caused_by = 14;
  // The epoch unix timestamp in microseconds unit when activity occurred
  int64 client_timestamp_unix_micro = 16;
  // The epoch unix timestamp in microseconds unit when server receives event
  int64 server_timestamp_unix_micro = 17;
  // The orders on queue
  repeated UpdateLocationOrderQueue order_queue = 18;
  // The driver offline later status after complete working orders
  bool is_offline_later = 19;
  // The available driver capacity each service types
  repeated UpdateLocationAvailableCapacity available_capacity = 20;

  enum InactiveCausedBy {
    INACTIVE_CAUSED_BY_UNSPECIFIED = 0;
    INACTIVE_CAUSED_BY_EXCEED_LOCATION_UPDATE_THRESHOLD = 1;
    INACTIVE_CAUSED_BY_DRIVER_TURN_OFFLINE = 2;
  }
}

message UpdateLocationOrderQueue {
  string order_id = 1;
}

message UpdateLocationAvailableCapacity {
  string service_type = 1;
  int32 capacity = 2;
  bool enabled = 3;
}

message UpdateLocationOrder {
  // The current of a working order id
  string order_id = 1;
  // The current a working order status
  string order_status = 2;
}
