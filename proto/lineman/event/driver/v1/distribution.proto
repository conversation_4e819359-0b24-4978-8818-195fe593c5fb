syntax = "proto3";

package lineman.event.driver.v1;

message DistributeOrderEvent {
  string order_id = 1;
  optional int32 riders_tried_assigning = 2;

  // for backward compatibility - when order location change on single distribution, remaining candidate was skipped to re-optimization
  // TODO[LMF-16131] should review after separate service and revise flow if required
  // e.g. fleet-order update dispacted order on fleet-distribution, they can self decide which order properties changed might deoptimizated the result
  bool is_redistribution_required = 128; // if true, force re-optimization to find new candidate on single distribution
}
