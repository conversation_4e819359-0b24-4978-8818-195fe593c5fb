syntax = "proto3";

package lineman.event.driver.v1;

import "google/protobuf/timestamp.proto";

message SearchEvent {
  google.protobuf.Timestamp captured_at = 1;
  google.protobuf.Timestamp started_at = 2;
  string type = 3;
  string distribution_id = 4;
  string region = 5;
  optional string zone = 6;
  repeated string drivers = 7;
  repeated string orders = 8;
}

message OptimizeEvent {
  google.protobuf.Timestamp captured_at = 1;
  string type = 2;
  string distribution_id = 3;
  string region = 4;
  optional string zone = 5;
  repeated string drivers = 6;
  repeated string orders = 7;
  string optimization_round = 8;
  string optimization_response = 9;
}

message FilterEvent {
  google.protobuf.Timestamp captured_at = 1;
  string type = 2;
  string distribution_id = 3;
  string region = 4;
  optional string zone = 5;
  repeated string target_drivers = 6;
  repeated string target_orders = 7;
  optional string optimization_round = 8;
  string step = 9;
  string filter = 10;
}
