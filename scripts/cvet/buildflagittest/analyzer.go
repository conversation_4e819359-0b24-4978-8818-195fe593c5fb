package buildflagittest

import (
	"go/ast"
	"go/parser"
	"strings"

	"golang.org/x/tools/go/analysis"
)

var Analyzer = &analysis.Analyzer{
	Name: "buildflagittest",
	Doc:  "integration test must specify a correct build flag",
	Run:  run,
}

func run(pass *analysis.Pass) (interface{}, error) {
	for _, f := range pass.Files {
		checkGoFile(pass, f)
	}
	for _, name := range pass.IgnoredFiles {
		if strings.HasSuffix(name, ".go") {
			f, err := parser.ParseFile(pass.Fset, name, nil, parser.ParseComments)
			if err != nil {
				// Not valid Go source code - not our job to diagnose, so ignore.
				return nil, nil
			}
			checkGoFile(pass, f)
		}
	}
	return nil, nil
}

func checkGoFile(pass *analysis.Pass, f *ast.File) {
	for _, spec := range f.Imports {
		if strings.Contains(spec.Path.Value, "/ittest") {
			checkBuildTag(pass, f)
		}
	}
}

func checkBuildTag(pass *analysis.Pass, f *ast.File) {
	for _, group := range f.Comments {
		// A +build comment is ignored after or adjoining the package declaration.
		if group.End()+1 >= f.Package {
			return
		}

		// "+build" is ignored within or after a /*...*/ comment.
		if !strings.HasPrefix(group.List[0].Text, "//") {
			return
		}

		// Check each line of a //-comment.
		for _, c := range group.List {
			if !strings.Contains(c.Text, "+build") {
				continue
			}
			line := c.Text
			line = strings.TrimPrefix(line, "//")
			line = strings.TrimSpace(line)
			if !strings.HasPrefix(line, "+build") {
				continue
			}
			fields := strings.Fields(line)
			if len(fields) <= 1 {
				continue
			}
			for _, s := range fields[1:] {
				if s == "integration_test" {
					return
				}
			}
		}
	}
	pass.Report(analysis.Diagnostic{
		Pos:            f.Pos(),
		End:            0,
		Category:       "",
		Message:        "missing build tag for integration test",
		SuggestedFixes: nil,
		Related:        nil,
	})
}
