#!/bin/bash

# Disable for gitlab-ci
if [ "$CI" != "" ]; then
  go run ./scripts/init_test_bucket -access-key=$MINIO_ACCESS_KEY -secret-key=$MINIO_SECRET_KEY -endpoint=$VOS_ENDPOINT
  exit
fi

# Clean test cache
go clean -testcache

HAS_TEST_TAG=0
INTEGRATION_TEST=0
for word in $GOTESTFLAGS; do
  if [[ $HAS_TEST_TAG -eq 1 ]]; then
    if [[ $word =~ "integration_test" ]]; then
      INTEGRATION_TEST=1
    else
      break
    fi
  fi
  if [[ "$word" == "-tags" ]]; then
    HAS_TEST_TAG=1
  fi
done

if [[ $INTEGRATION_TEST -eq 0 ]]; then
  exit
fi

docker info >/dev/null 2>&1
if [[ $? -ne 0 ]]; then
  echo "please run docker daemon first"
  exit 1
fi

make start_integration_deps
