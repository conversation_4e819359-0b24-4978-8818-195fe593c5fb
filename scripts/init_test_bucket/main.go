package main

import (
	"flag"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/sirupsen/logrus"
)

const (
	LinemanBucket = "lineman"
)

func main() {
	accessKey := flag.String("access-key", "", "object storage access key")
	secretKey := flag.String("secret-key", "", "object storage secret key")
	endpoint := flag.String("endpoint", "", "object storage endpoint")
	bucket := LinemanBucket
	region := s3.BucketLocationConstraintApNortheast1
	flag.Parse()
	sess := session.Must(session.NewSession(&aws.Config{
		Credentials:      credentials.NewStaticCredentials(*accessKey, *secretKey, ""),
		Region:           &region,
		Endpoint:         aws.String(*endpoint),
		DisableSSL:       aws.<PERSON>ol(true),
		S3ForcePathStyle: aws.Bool(true),
	}))
	_, err := s3.New(sess).CreateBucket(&s3.CreateBucketInput{
		Bucket: &bucket,
	})
	if err != nil {
		if awsError, ok := err.(awserr.Error); !ok || awsError.Code() != s3.ErrCodeBucketAlreadyOwnedByYou {
			logrus.Fatal(err)
		}
	}
	return
}
