@Library('centralise-jenkins-pipelines') _
def sendslack = new sendSlackNotify()

UNIT_TEST_SLACK_REPORT_JOB = "LM_SET/unit-test-slack-report"
SLACK_NOTIFY_CHANNEL = "pj_lineman_unit_test"

pipeline{
    options {
        timeout(time: 7, unit: 'MINUTES')
        buildDiscarder(logRotator(numToKeepStr: '10', artifactNumToKeepStr: '10'))
    }
    environment {
        GIT_URL = 'ssh://***************:10022/lineman/fleet-distribution'.git'
        src = 'src/git.wndv.co/lineman/fleet-distribution'
        REPO = 'lineman/fleet-distribution'
        SONAR_KEY='lineman-driver'
        DOCKER_MONGO_NAME="lineman-driver-mongo${BUILD_ID}"
        sha1="pr/${ghprbPullId}/merge"
        GOPATH="${WORKSPACE}"
        PATH="${PATH}:/usr/local/go/bin:${GOPATH}/bin"
        report_path="${GOPATH}/${src}/cover_report"
        COMMIT_HASH = ''
        DOCKER_REGISTRY = "harbor.linecorp.com/fleet-distribution"
	    ORGANIZATION_NAME = 'lineman'
        SERVICE_NAME = 'lineman-driver'
        RANCHER_STACK = "DRIVER-${BUILD_ENV.toUpperCase()}"
        RANCHER_DEV_API = 'http://rancher.line-alpha.me:8080/v2-beta'
        RANCHER_DEV_ENV_ID = '1a1284'
        RANCHER_DEV_CREDENTIAL = '8b2b8561-b924-4dc9-84d6-5dbd04d343e7'
        RANCHER_API = 'http://rch.line-apps.com:8080/v2-beta'
        RANCHER_ENV_ID = '1a8345'
        RANCHER_CREDENTIAL = 'a3662a1d-40b3-4433-bab8-d65d19d84f85'
        RELEASE_VERSION = ''
        DOCKER_TAG = ''
        DOCKER_IMAGE = ''
        BUILD_DISPLAYNAME = ''

    }
    agent { label 'slave-dev' }
    stages {
        stage('Prepare') {
            steps {
                sh 'sudo chown -R irteamsu:irteamsu .'
                cleanWs deleteDirs: true, patterns: [[pattern: 'bin/**', type: 'EXCLUDE'], [pattern: 'pkg/**', type: 'EXCLUDE']]
                sh '''
                    echo "===After clean==="
                    ls -al ${src} || true
                    ls -al . || true
                    sudo docker rm -f $DOCKER_MONGO_NAME || true
                    rm -f cobertura.xml || true
                    printenv
                '''
                checkoutCode()
                dir ("${WORKSPACE}/${src}") {
                    script {
                        if (env.BUILD_ENV == 'release'){
                            RELEASE_VERSION = env.GIT_TAG_VERSION
                        } else {
                            RELEASE_VERSION = sh(returnStdout: true, script: "git tag --sort version:refname | tail -1").trim()
                        }
                        DOCKER_TAG = "${BUILD_ENV}_${RELEASE_VERSION}-${BUILD_NUMBER}"
                        DOCKER_IMAGE = "${DOCKER_REGISTRY}/${SERVICE_NAME}:${DOCKER_TAG}"
                        BUILD_DISPLAYNAME = "${DOCKER_TAG}"
                        currentBuild.displayName = "#${BUILD_DISPLAYNAME}-${COMMIT_HASH}"
                    }
                }
                sh """
                    if [[ "${RELEASE_VERSION}" == "" ]]; then
                        echo "ERROR: please check input parameters"
                        exit 209
                    fi
                """
                sh 'sudo docker pull harbor.linecorp.com/lineman/absinthe/godevtool'
                sh """
                sudo docker run --rm \\
                    -v $WORKSPACE:/go \\
                    -v ~/.netrc:/root/.netrc \\
                    -w /go/$src harbor.linecorp.com/lineman/absinthe/godevtool dep ensure -v
                """
            }
        }
        stage('Run UnitTest'){
            steps{
                createMongoDockerInitFile()
                sh '''#!/bin/bash
                    sudo docker run -d --name $DOCKER_MONGO_NAME --hostname lineman-mongo \\
                    -e MONGO_INITDB_ROOT_USERNAME=lineman \\
                    -e MONGO_INITDB_ROOT_PASSWORD=lineman \\
                    -e "MONGO_INITDB_DATABASE=payment lineman_test"\\
                    -e MONGODB_APPLICATION_USER=lineman \\
                    -e MONGODB_APPLICATION_PASS=lineman \\
                    -v $(pwd)/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh \\
                    mongo
                '''
                createDirIfNotExists("$WORKSPACE/$src/junit_report")
                createDirIfNotExists("$WORKSPACE/$src/cover_report")
                sh '''
                    #!/bin/bash
                    sudo docker run --rm \
                        -v $WORKSPACE:/go \
                        -w /go/$src \
                        -e ENV=unittest \
                        -e GIN_MODE=release \
                        --network=container:$DOCKER_MONGO_NAME \
                        harbor.linecorp.com/lineman/absinthe/godevtool:1.10 \
                        /usr/local/bin/ginkgo -noColor -r -cover -outputdir=/go/$src/cover_report -coverprofile=cover.out -keepGoing -p
                    sudo chown -R irteamsu:irteamsu .
                '''
            }
        }
        stage('Run Coverage'){
            steps{
                convertCoverOutToCobertura()
                step([$class: 'CoberturaPublisher', autoUpdateHealth: false, autoUpdateStability: false, coberturaReportFile: '**/cobertura.xml', failUnhealthy: false, failUnstable: false, maxNumberOfBuilds: 0, onlyStable: false, sourceEncoding: 'ASCII', zoomCoverageChart: false])
            }
        }
        stage('Run SonarScanner'){
            steps{
                createSonarFile()
                sh '''
                    cd $GOPATH/$src
                    cat sonar-project.properties
                    sonar-scanner
                '''
            }
        }
        stage('Build Docker'){
            steps {
                script {
                    if ( currentBuild.rawBuild.getCauses()[0].toString().contains('UserIdCause') && env.BUILD_ENV != 'unittest'){
                        sh """
                            cd $WORKSPACE/$src
                            sudo docker build \
                                --no-cache -t ${DOCKER_IMAGE}-${COMMIT_HASH} .
                            sudo docker push ${DOCKER_IMAGE}-${COMMIT_HASH}
                            sudo docker rmi -f ${DOCKER_IMAGE}-${COMMIT_HASH}
                        """
                    } else {
                        echo "SKIP"
                    }
                }
            }
        }
        stage('Deploy Rancher') {
            steps {
                script {
                    if ( currentBuild.rawBuild.getCauses()[0].toString().contains('UserIdCause') && env.BUILD_ENV != 'unittest'){
                        if (env.BUILD_ENV == 'release') {
                            rancher confirm: true, credentialId: "${RANCHER_CREDENTIAL}", endpoint: "${RANCHER_API}", environmentId: "${RANCHER_ENV_ID}", environments: '', image: "${DOCKER_IMAGE}-${COMMIT_HASH}", ports: '', service: "${RANCHER_STACK}/${SERVICE_NAME}", timeout: 50
                        } else {
                            rancher confirm: true, credentialId: "${RANCHER_DEV_CREDENTIAL}", endpoint: "${RANCHER_DEV_API}", environmentId: "${RANCHER_DEV_ENV_ID}", environments: '', image: "${DOCKER_IMAGE}-${COMMIT_HASH}", ports: '', service: "${RANCHER_STACK}/${SERVICE_NAME}", timeout: 50
                        }
                    } else {
                        echo "SKIP"
                    }
                }
            }
        }
        stage("get user trigger build") {
            steps {
                script {
                    wrap([$class: 'BuildUser']) {
		                if(env.BUILD_USER==null){
			                env.USERNAME="git_process"
			            }
			            else{
			                env.USERNAME="${BUILD_USER}"
			            }
                    }
		            echo "${USERNAME}"
                }
            }
        }
        stage('Post Check') {
            steps {
                sh """

                """
            }
        }
    }

    post {
        always {
            sh 'sudo docker rm -f $DOCKER_MONGO_NAME || true'
            junit "${src}/junit_report/*.xml"
            script {
                if(env.ghprbPullId == null){
                    build job: UNIT_TEST_SLACK_REPORT_JOB, wait: false, parameters: [
                        string(name: 'CURRENT_BUILD_URL', value: env.BUILD_URL),
                        string(name: 'COMPARE_BUILD_URL', value: currentBuild.previousBuild.absoluteUrl),
                        string(name: 'SLACK_NOTIFY_CHANNEL', value: SLACK_NOTIFY_CHANNEL),
                        string(name: 'SERVICE_NAME', value: SERVICE_NAME)
                    ]
                }
            }
            script{
                env.BUILD_STATUS="${currentBuild.currentResult}"
                env.commit="${COMMIT_HASH}"
                if(RELEASE_VERSION=="0"||RELEASE_VERSION==""){
                    env.vers="${commit}"
                }
                else {
                    env.vers=RELEASE_VERSION
                }
                echo "${vers}"
                sh '''curl --header "Content-Type: application/json" \
                     --request POST \
                     --data '{"service":"'"${ORGANIZATION_NAME}"-"${SERVICE_NAME}"'","version":"'"${vers}"'","user":"'"${USERNAME}"'","environment":"'"${BUILD_ENV}"'","step_name":"'"deploy_lineman-driver"'","sign_off":"'"1"'","build_result":"'"${BUILD_STATUS}"'","build_url":"'"${BUILD_URL}"'","build_number":"'"${BUILD_NUMBER}"'","commit_hash":"'"${commit}"'"}' \
                     https://jenkins-reporter.line-apps-beta.com/build_data'''
            }
        }
        success{
            script{
                if(env.ghprbPullId && env.ghprbPullId != ""){
                    commentCoverageToPR()
                }
            }
        }
    }
}

def createMongoDockerInitFile(){
    sh '''
        #!/bin/bash
        cat <<INITFILE >init-db.sh
#!/bin/bash
# Admin User
MONGODB_ADMIN_USER=\\${MONGO_INITDB_ROOT_USERNAME:-"lineman"}
MONGODB_ADMIN_PASS=\\${MONGO_INITDB_ROOT_PASSWORD:-"lineman"}

# Application Database User
MONGODB_APPLICATION_DATABASE=\\${MONGO_INITDB_DATABASE:-"admin"}
MONGODB_APPLICATION_USER=\\${MONGODB_APPLICATION_USER:-"lineman"}
MONGODB_APPLICATION_PASS=\\${MONGODB_APPLICATION_PASS:-"lineman"}


for DB in \\$MONGODB_APPLICATION_DATABASE
do
    mongo admin -u \\$MONGODB_ADMIN_USER -p \\$MONGODB_ADMIN_PASS << EOF
    echo "Using \\$DB database"
    use \\$DB
    db.createUser({user: \'\\$MONGODB_APPLICATION_USER\', pwd: \'\\$MONGODB_APPLICATION_PASS\', roles:["dbAdmin", "userAdmin", "readWrite"]})
EOF
done
INITFILE
chmod +x init-db.sh
    '''
}

def convertCoverOutToCobertura(){
    sh """
        #!/bin/bash
        echo 'convert report to cobertura format'
        go get github.com/t-yuki/gocover-cobertura
        echo 'remove duplicate word to make cover.out correct format'
        echo 'mode: atomic' > ${WORKSPACE}/tmpfile.t
        sed -n '/mode: atomic/!p' ${report_path}/cover.out >> ${WORKSPACE}/tmpfile.t
        mv ${WORKSPACE}/tmpfile.t ${report_path}/cover.out
        gocover-cobertura < ${report_path}/cover.out > cobertura.xml
    """
}

def commentCoverageToPR(){
    CHANGE_ID="${ghprbPullId}"
    CHANGE_URL="https://git.wndv.co/${REPO}"
    currentBuild.result = 'SUCCESS'
    step([$class: 'CompareCoverageAction', scmVars: [GIT_URL: env.GIT_URL]])
}

def createSonarFile(){
    if(env.ghprbPullId && env.ghprbPullId != ""){
        echo "PR - ${ghprbPullId}"
        createSonarPropFilePR("${SONAR_KEY}","${GIT_BRANCH}", "preview", "${ghprbPullId}", "${REPO}")
    } else if(env.GIT_BRANCH == "master" || env.GIT_BRANCH == "origin/master") {
        echo "${GIT_BRANCH}"
        createSonarPropFileBranch("${SONAR_KEY}","${GIT_BRANCH}", "publish")
    } else {
        echo "${GIT_BRANCH}"
        createSonarPropFileBranch("${SONAR_KEY}","${GIT_BRANCH}", "preview")
    }
}

def createSonarPropFileBranch(sonarKey, gitBranch, mode){
    sh """
    cd ${GOPATH}/${src}
    cat <<EOF > sonar-project.properties
sonar.projectKey=${sonarKey}
sonar.projectName=${sonarKey}
sonar.projectVersion=1.0
sonar.login=****************************************
sonar.host.url=https://sonarqube.linecorp-dev.com/
sonar.branch.name=${gitBranch}
sonar.sourceEncoding=UTF-8
sonar.sources=.
sonar.exclusions=**/*_test.go,**/vendor/**,**/*_test/**,**/testutils/**
sonar.tests=.
sonar.test.inclusions=**/*_test.go
sonar.test.exclusions=**/vendor/**
sonar.go.coverage.reportPaths=cover_report/**
sonar.analysis.mode=${mode}
EOF
    """
}

def createSonarPropFilePR(sonarKey, gitBranch, mode, pr, repo){
    sh """
        cd ${GOPATH}/${src}
        cat <<EOF > sonar-project.properties
sonar.projectKey=${sonarKey}
sonar.projectName=${sonarKey}
sonar.projectVersion=1.0
sonar.login=****************************************
sonar.host.url=https://sonarqube.linecorp-dev.com/
sonar.analysis.mode=${mode}
sonar.branch.name=${gitBranch}

sonar.sourceEncoding=UTF-8
sonar.sources=.
sonar.exclusions=**/*_test.go,**/vendor/**,**/*_test/**,**/testutils/**
sonar.tests=.
sonar.test.inclusions=**/*_test.go
sonar.test.exclusions=**/vendor/**
sonar.go.coverage.reportPaths=cover_report/**
sonar.github.repository=${repo}
sonar.github.pullRequest=${pr}
sonar.github.oauth=****************************************
sonar.github.endpoint=https://git.wndv.co/api/v3
EOF
    """
}

def checkoutCode(){
    def scmVars = ''
    if (env.ghprbPullId && env.ghprbPullId != ""){
        refspec='+refs/pull/*:refs/remotes/origin/pr/*'
        scmVars = checkout scm: [$class: 'GitSCM', branches: [[name: "${sha1}"]], doGenerateSubmoduleConfigurations: false, extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: "${src}"]], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'jpt0213-git', refspec: "${refspec}", url: "${GIT_URL}"]]]
    } else if (env.BUILD_ENV == 'release'){
        scmVars = checkout scm: [$class: 'GitSCM', branches: [[name: "refs/tags/${GIT_TAG_VERSION}"]], doGenerateSubmoduleConfigurations: false, extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: "${src}"]], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'jpt0213-git', url: "${GIT_URL}"]]]
    } else {
        scmVars = checkout scm: [$class: 'GitSCM', branches: [[name: "${GIT_BRANCH}"]], doGenerateSubmoduleConfigurations: false, extensions: [[$class: 'RelativeTargetDirectory', relativeTargetDir: "${src}"]], submoduleCfg: [], userRemoteConfigs: [[credentialsId: 'jpt0213-git', url: "${GIT_URL}"]]]
    }
    COMMIT_HASH = scmVars.GIT_COMMIT.substring(0, 7)
}
