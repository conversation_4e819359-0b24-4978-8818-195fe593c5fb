package croninterval

import (
	"context"
	"time"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

type RegionRefresherConfig struct {
	RegionRefreshInterval time.Duration `envconfig:"REGION_REFRESH_INTERVAL" default:"2m"`
}

type RegionRefresher struct {
	cfg        RegionRefresherConfig
	regionRepo repository.RegionRepository
}

func (u RegionRefresher) IntervalTime() time.Duration {
	return u.cfg.RegionRefreshInterval
}

func (u RegionRefresher) Run(ctx context.Context) error {
	return u.regionRepo.UpdateCache(ctx)
}

func ProvideRegionRefresher(regionRepo repository.RegionRepository) RegionRefresher {
	var cfg RegionRefresherConfig
	envconfig.MustProcess("", &cfg)

	return RegionRefresher{
		cfg:        cfg,
		regionRepo: regionRepo,
	}
}
