package croninterval

import (
	"context"
	"time"
)

type CronIntervalTask interface {
	Run(ctx context.Context) error
	IntervalTime() time.Duration
}

type CronIntervalRunner struct {
	tasks      []CronIntervalTask
	ctx        context.Context
	cancelFunc context.CancelFunc
}

func (c CronIntervalRunner) Run() {
	for _, task := range c.tasks {
		go func(ctx context.Context, t CronIntervalTask) {
			ticker := time.NewTicker(t.IntervalTime())
			for {
				select {
				case <-ticker.C:
					jobCtx, cancel := context.WithTimeout(ctx, 1*time.Minute)
					_ = t.Run(jobCtx)
					cancel()
				case <-ctx.Done():
					break
				}
			}
		}(c.ctx, task)
	}
}

func (c CronIntervalRunner) Stop() {
	c.cancelFunc()
}

func NewCronIntervalRunner(tasks ...CronIntervalTask) CronIntervalRunner {
	ctx, cancel := context.WithCancel(context.Background())
	return CronIntervalRunner{
		tasks:      tasks,
		ctx:        ctx,
		cancelFunc: cancel,
	}
}

func ProvideCronIntervalRunner(region RegionRefresher) (CronIntervalRunner, func()) {
	runner := NewCronIntervalRunner(region)
	return runner, runner.Stop
}
