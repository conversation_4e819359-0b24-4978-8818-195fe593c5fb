package messages

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/messages/mock_messages"
)

func TestLineBotNotificationServiceWorker_SendMessageContext(t *testing.T) {
	t.Parallel()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	msgsvc := mock_messages.NewMockMessageServiceInterface(ctrl)
	msgsvc.EXPECT().SendMessageContext(gomock.Any(), "<uid>", "hello").Return(nil)

	wrk, stop := ProvideLineBotNotificationServiceWorker(Config{}, httpclient.New())
	wrk.msgsvc = msgsvc

	require.NoError(t, wrk.SendMessageContext(context.Background(), "<uid>", "hello"))

	stop()
}
