// Package messages provide service for send message to LINE.

package messages

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"sync"

	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

// MessageServiceInterface ...
type MessageServiceInterface interface {
	// SendMessage send message to LINE Official Account.
	//
	// Deprecated: use SendMessageContext instead.
	SendMessage(uid, message string) error

	// SendMessage send message to LINE Official Account.
	SendMessageContext(ctx context.Context, uid, message string) error
}

// Config for LineBotNotificationService.
type Config struct {
	// LineAPIURL a LINE messaging api endpoint.
	LineAPIURL string `envconfig:"LINE_API_URL"`

	// LineAccessToken a LINE Official Account access token.
	LineAccessToken string `envconfig:"LINE_CHANNEL_ACCESS_TOKEN"`
}

// ProvideConfig load Config from environment variables.
func ProvideConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}

// LineBotNotificationService provides notification service for LINE Official Account.
type LineBotNotificationService struct {
	client *httpclient.Client
	cfg    Config
}

// NewLineBotNotificationService ...
func NewLineBotNotificationService(cfg Config, client *httpclient.Client) *LineBotNotificationService {
	return &LineBotNotificationService{
		client: client,
		cfg:    cfg,
	}
}

// SendMessage implements MessageServiceInterface.
func (svc *LineBotNotificationService) SendMessage(uid, message string) error {
	return svc.SendMessageContext(context.Background(), uid, message)
}

// SendMessageContext implements MessageServiceInterface.
func (svc *LineBotNotificationService) SendMessageContext(ctx context.Context, uid, message string) error {
	url := fmt.Sprintf("%s/bot/message/push", strings.TrimSuffix(svc.cfg.LineAPIURL, "/"))

	req := map[string]interface{}{
		"to": uid,
		"messages": []map[string]string{
			{
				"type": "text",
				"text": message,
			},
		},
	}

	header := make(http.Header)
	header.Set("Content-Type", "application/json; charset=utf-8")
	header.Set("Authorization", fmt.Sprintf("Bearer %s", svc.cfg.LineAccessToken))
	res, err := svc.client.Post(ctx, url, header, httpclient.JSON(req))
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		var response map[string]interface{}
		_ = safe.DecodeJSON(res.Body, &response)

		return fmt.Errorf("Can not send OA: %v", response["message"])
	}

	return nil
}

// LineBotNotificationServiceWorker is similiar to LineBotNotificationService but sending a message in background mode.
type LineBotNotificationServiceWorker struct {
	wg     sync.WaitGroup
	msgsvc MessageServiceInterface
}

// SendMessage implements MessageServiceInterface.
func (wrk *LineBotNotificationServiceWorker) SendMessage(uid, message string) error {
	return wrk.msgsvc.SendMessageContext(context.Background(), uid, message)
}

// SendMessageContext implements MessageServiceInterface.
func (wrk *LineBotNotificationServiceWorker) SendMessageContext(ctx context.Context, uid, message string) error {
	wrk.wg.Add(1)
	safe.GoFunc(func() {
		defer wrk.wg.Done()
		if err := wrk.msgsvc.SendMessageContext(ctx, uid, message); err != nil {
			logrus.WithField("method", "SendMessageContext").Errorf("cannot send message: %v", err)
		}
	})
	return nil
}

// Stop worker.
func (wrk *LineBotNotificationServiceWorker) Stop() {
	wrk.wg.Wait()
}

func ProvideLineBotNotificationServiceWorker(cfg Config, client *httpclient.Client) (*LineBotNotificationServiceWorker, func()) {
	msgsvc := NewLineBotNotificationService(cfg, client)
	svc := &LineBotNotificationServiceWorker{
		msgsvc: msgsvc,
	}
	return svc, func() { svc.Stop() }
}
