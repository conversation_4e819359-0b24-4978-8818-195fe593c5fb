// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: lineman/event/driver/v1/order_distribution_event.proto

package driverv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type OrderDistributionEvent struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	EventTime       *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=event_time,json=eventTime,proto3" json:"event_time,omitempty"`
	OrderId         string                 `protobuf:"bytes,2,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
	ServiceType     string                 `protobuf:"bytes,3,opt,name=service_type,json=serviceType,proto3" json:"service_type,omitempty"`
	DriverId        *string                `protobuf:"bytes,4,opt,name=driver_id,json=driverId,proto3,oneof" json:"driver_id,omitempty"`
	CreatedAt       *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	EventName       string                 `protobuf:"bytes,6,opt,name=event_name,json=eventName,proto3" json:"event_name,omitempty"`
	FromLocationLat float64                `protobuf:"fixed64,7,opt,name=from_location_lat,json=fromLocationLat,proto3" json:"from_location_lat,omitempty"`
	FromLocationLng float64                `protobuf:"fixed64,8,opt,name=from_location_lng,json=fromLocationLng,proto3" json:"from_location_lng,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *OrderDistributionEvent) Reset() {
	*x = OrderDistributionEvent{}
	mi := &file_lineman_event_driver_v1_order_distribution_event_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrderDistributionEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrderDistributionEvent) ProtoMessage() {}

func (x *OrderDistributionEvent) ProtoReflect() protoreflect.Message {
	mi := &file_lineman_event_driver_v1_order_distribution_event_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrderDistributionEvent.ProtoReflect.Descriptor instead.
func (*OrderDistributionEvent) Descriptor() ([]byte, []int) {
	return file_lineman_event_driver_v1_order_distribution_event_proto_rawDescGZIP(), []int{0}
}

func (x *OrderDistributionEvent) GetEventTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EventTime
	}
	return nil
}

func (x *OrderDistributionEvent) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

func (x *OrderDistributionEvent) GetServiceType() string {
	if x != nil {
		return x.ServiceType
	}
	return ""
}

func (x *OrderDistributionEvent) GetDriverId() string {
	if x != nil && x.DriverId != nil {
		return *x.DriverId
	}
	return ""
}

func (x *OrderDistributionEvent) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *OrderDistributionEvent) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *OrderDistributionEvent) GetFromLocationLat() float64 {
	if x != nil {
		return x.FromLocationLat
	}
	return 0
}

func (x *OrderDistributionEvent) GetFromLocationLng() float64 {
	if x != nil {
		return x.FromLocationLng
	}
	return 0
}

var File_lineman_event_driver_v1_order_distribution_event_proto protoreflect.FileDescriptor

const file_lineman_event_driver_v1_order_distribution_event_proto_rawDesc = "" +
	"\n" +
	"6lineman/event/driver/v1/order_distribution_event.proto\x12\x17lineman.event.driver.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf3\x02\n" +
	"\x16OrderDistributionEvent\x129\n" +
	"\n" +
	"event_time\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\teventTime\x12\x19\n" +
	"\border_id\x18\x02 \x01(\tR\aorderId\x12!\n" +
	"\fservice_type\x18\x03 \x01(\tR\vserviceType\x12 \n" +
	"\tdriver_id\x18\x04 \x01(\tH\x00R\bdriverId\x88\x01\x01\x129\n" +
	"\n" +
	"created_at\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x12\x1d\n" +
	"\n" +
	"event_name\x18\x06 \x01(\tR\teventName\x12*\n" +
	"\x11from_location_lat\x18\a \x01(\x01R\x0ffromLocationLat\x12*\n" +
	"\x11from_location_lng\x18\b \x01(\x01R\x0ffromLocationLngB\f\n" +
	"\n" +
	"_driver_idB\x95\x02\n" +
	"\x1bcom.lineman.event.driver.v1B\x1bOrderDistributionEventProtoP\x01ZZgit.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1;driverv1\xa2\x02\x03LED\xaa\x02\x17Lineman.Event.Driver.V1\xca\x02\x17Lineman\\Event\\Driver\\V1\xe2\x02#Lineman\\Event\\Driver\\V1\\GPBMetadata\xea\x02\x1aLineman::Event::Driver::V1b\x06proto3"

var (
	file_lineman_event_driver_v1_order_distribution_event_proto_rawDescOnce sync.Once
	file_lineman_event_driver_v1_order_distribution_event_proto_rawDescData []byte
)

func file_lineman_event_driver_v1_order_distribution_event_proto_rawDescGZIP() []byte {
	file_lineman_event_driver_v1_order_distribution_event_proto_rawDescOnce.Do(func() {
		file_lineman_event_driver_v1_order_distribution_event_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_lineman_event_driver_v1_order_distribution_event_proto_rawDesc), len(file_lineman_event_driver_v1_order_distribution_event_proto_rawDesc)))
	})
	return file_lineman_event_driver_v1_order_distribution_event_proto_rawDescData
}

var file_lineman_event_driver_v1_order_distribution_event_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_lineman_event_driver_v1_order_distribution_event_proto_goTypes = []any{
	(*OrderDistributionEvent)(nil), // 0: lineman.event.driver.v1.OrderDistributionEvent
	(*timestamppb.Timestamp)(nil),  // 1: google.protobuf.Timestamp
}
var file_lineman_event_driver_v1_order_distribution_event_proto_depIdxs = []int32{
	1, // 0: lineman.event.driver.v1.OrderDistributionEvent.event_time:type_name -> google.protobuf.Timestamp
	1, // 1: lineman.event.driver.v1.OrderDistributionEvent.created_at:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_lineman_event_driver_v1_order_distribution_event_proto_init() }
func file_lineman_event_driver_v1_order_distribution_event_proto_init() {
	if File_lineman_event_driver_v1_order_distribution_event_proto != nil {
		return
	}
	file_lineman_event_driver_v1_order_distribution_event_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_lineman_event_driver_v1_order_distribution_event_proto_rawDesc), len(file_lineman_event_driver_v1_order_distribution_event_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_lineman_event_driver_v1_order_distribution_event_proto_goTypes,
		DependencyIndexes: file_lineman_event_driver_v1_order_distribution_event_proto_depIdxs,
		MessageInfos:      file_lineman_event_driver_v1_order_distribution_event_proto_msgTypes,
	}.Build()
	File_lineman_event_driver_v1_order_distribution_event_proto = out.File
	file_lineman_event_driver_v1_order_distribution_event_proto_goTypes = nil
	file_lineman_event_driver_v1_order_distribution_event_proto_depIdxs = nil
}
