// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: lineman/event/driver/v1/distribution_log.proto

package driverv1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SearchEvent struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	CapturedAt     *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=captured_at,json=capturedAt,proto3" json:"captured_at,omitempty"`
	StartedAt      *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=started_at,json=startedAt,proto3" json:"started_at,omitempty"`
	Type           string                 `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	DistributionId string                 `protobuf:"bytes,4,opt,name=distribution_id,json=distributionId,proto3" json:"distribution_id,omitempty"`
	Region         string                 `protobuf:"bytes,5,opt,name=region,proto3" json:"region,omitempty"`
	Zone           *string                `protobuf:"bytes,6,opt,name=zone,proto3,oneof" json:"zone,omitempty"`
	Drivers        []string               `protobuf:"bytes,7,rep,name=drivers,proto3" json:"drivers,omitempty"`
	Orders         []string               `protobuf:"bytes,8,rep,name=orders,proto3" json:"orders,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SearchEvent) Reset() {
	*x = SearchEvent{}
	mi := &file_lineman_event_driver_v1_distribution_log_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchEvent) ProtoMessage() {}

func (x *SearchEvent) ProtoReflect() protoreflect.Message {
	mi := &file_lineman_event_driver_v1_distribution_log_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchEvent.ProtoReflect.Descriptor instead.
func (*SearchEvent) Descriptor() ([]byte, []int) {
	return file_lineman_event_driver_v1_distribution_log_proto_rawDescGZIP(), []int{0}
}

func (x *SearchEvent) GetCapturedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CapturedAt
	}
	return nil
}

func (x *SearchEvent) GetStartedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.StartedAt
	}
	return nil
}

func (x *SearchEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *SearchEvent) GetDistributionId() string {
	if x != nil {
		return x.DistributionId
	}
	return ""
}

func (x *SearchEvent) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SearchEvent) GetZone() string {
	if x != nil && x.Zone != nil {
		return *x.Zone
	}
	return ""
}

func (x *SearchEvent) GetDrivers() []string {
	if x != nil {
		return x.Drivers
	}
	return nil
}

func (x *SearchEvent) GetOrders() []string {
	if x != nil {
		return x.Orders
	}
	return nil
}

type OptimizeEvent struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	CapturedAt           *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=captured_at,json=capturedAt,proto3" json:"captured_at,omitempty"`
	Type                 string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	DistributionId       string                 `protobuf:"bytes,3,opt,name=distribution_id,json=distributionId,proto3" json:"distribution_id,omitempty"`
	Region               string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Zone                 *string                `protobuf:"bytes,5,opt,name=zone,proto3,oneof" json:"zone,omitempty"`
	Drivers              []string               `protobuf:"bytes,6,rep,name=drivers,proto3" json:"drivers,omitempty"`
	Orders               []string               `protobuf:"bytes,7,rep,name=orders,proto3" json:"orders,omitempty"`
	OptimizationRound    string                 `protobuf:"bytes,8,opt,name=optimization_round,json=optimizationRound,proto3" json:"optimization_round,omitempty"`
	OptimizationResponse string                 `protobuf:"bytes,9,opt,name=optimization_response,json=optimizationResponse,proto3" json:"optimization_response,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *OptimizeEvent) Reset() {
	*x = OptimizeEvent{}
	mi := &file_lineman_event_driver_v1_distribution_log_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OptimizeEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OptimizeEvent) ProtoMessage() {}

func (x *OptimizeEvent) ProtoReflect() protoreflect.Message {
	mi := &file_lineman_event_driver_v1_distribution_log_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OptimizeEvent.ProtoReflect.Descriptor instead.
func (*OptimizeEvent) Descriptor() ([]byte, []int) {
	return file_lineman_event_driver_v1_distribution_log_proto_rawDescGZIP(), []int{1}
}

func (x *OptimizeEvent) GetCapturedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CapturedAt
	}
	return nil
}

func (x *OptimizeEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *OptimizeEvent) GetDistributionId() string {
	if x != nil {
		return x.DistributionId
	}
	return ""
}

func (x *OptimizeEvent) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *OptimizeEvent) GetZone() string {
	if x != nil && x.Zone != nil {
		return *x.Zone
	}
	return ""
}

func (x *OptimizeEvent) GetDrivers() []string {
	if x != nil {
		return x.Drivers
	}
	return nil
}

func (x *OptimizeEvent) GetOrders() []string {
	if x != nil {
		return x.Orders
	}
	return nil
}

func (x *OptimizeEvent) GetOptimizationRound() string {
	if x != nil {
		return x.OptimizationRound
	}
	return ""
}

func (x *OptimizeEvent) GetOptimizationResponse() string {
	if x != nil {
		return x.OptimizationResponse
	}
	return ""
}

type FilterEvent struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	CapturedAt        *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=captured_at,json=capturedAt,proto3" json:"captured_at,omitempty"`
	Type              string                 `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	DistributionId    string                 `protobuf:"bytes,3,opt,name=distribution_id,json=distributionId,proto3" json:"distribution_id,omitempty"`
	Region            string                 `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	Zone              *string                `protobuf:"bytes,5,opt,name=zone,proto3,oneof" json:"zone,omitempty"`
	TargetDrivers     []string               `protobuf:"bytes,6,rep,name=target_drivers,json=targetDrivers,proto3" json:"target_drivers,omitempty"`
	TargetOrders      []string               `protobuf:"bytes,7,rep,name=target_orders,json=targetOrders,proto3" json:"target_orders,omitempty"`
	OptimizationRound *string                `protobuf:"bytes,8,opt,name=optimization_round,json=optimizationRound,proto3,oneof" json:"optimization_round,omitempty"`
	Step              string                 `protobuf:"bytes,9,opt,name=step,proto3" json:"step,omitempty"`
	Filter            string                 `protobuf:"bytes,10,opt,name=filter,proto3" json:"filter,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *FilterEvent) Reset() {
	*x = FilterEvent{}
	mi := &file_lineman_event_driver_v1_distribution_log_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FilterEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FilterEvent) ProtoMessage() {}

func (x *FilterEvent) ProtoReflect() protoreflect.Message {
	mi := &file_lineman_event_driver_v1_distribution_log_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FilterEvent.ProtoReflect.Descriptor instead.
func (*FilterEvent) Descriptor() ([]byte, []int) {
	return file_lineman_event_driver_v1_distribution_log_proto_rawDescGZIP(), []int{2}
}

func (x *FilterEvent) GetCapturedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CapturedAt
	}
	return nil
}

func (x *FilterEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *FilterEvent) GetDistributionId() string {
	if x != nil {
		return x.DistributionId
	}
	return ""
}

func (x *FilterEvent) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *FilterEvent) GetZone() string {
	if x != nil && x.Zone != nil {
		return *x.Zone
	}
	return ""
}

func (x *FilterEvent) GetTargetDrivers() []string {
	if x != nil {
		return x.TargetDrivers
	}
	return nil
}

func (x *FilterEvent) GetTargetOrders() []string {
	if x != nil {
		return x.TargetOrders
	}
	return nil
}

func (x *FilterEvent) GetOptimizationRound() string {
	if x != nil && x.OptimizationRound != nil {
		return *x.OptimizationRound
	}
	return ""
}

func (x *FilterEvent) GetStep() string {
	if x != nil {
		return x.Step
	}
	return ""
}

func (x *FilterEvent) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

var File_lineman_event_driver_v1_distribution_log_proto protoreflect.FileDescriptor

const file_lineman_event_driver_v1_distribution_log_proto_rawDesc = "" +
	"\n" +
	".lineman/event/driver/v1/distribution_log.proto\x12\x17lineman.event.driver.v1\x1a\x1fgoogle/protobuf/timestamp.proto\"\xae\x02\n" +
	"\vSearchEvent\x12;\n" +
	"\vcaptured_at\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"capturedAt\x129\n" +
	"\n" +
	"started_at\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\tstartedAt\x12\x12\n" +
	"\x04type\x18\x03 \x01(\tR\x04type\x12'\n" +
	"\x0fdistribution_id\x18\x04 \x01(\tR\x0edistributionId\x12\x16\n" +
	"\x06region\x18\x05 \x01(\tR\x06region\x12\x17\n" +
	"\x04zone\x18\x06 \x01(\tH\x00R\x04zone\x88\x01\x01\x12\x18\n" +
	"\adrivers\x18\a \x03(\tR\adrivers\x12\x16\n" +
	"\x06orders\x18\b \x03(\tR\x06ordersB\a\n" +
	"\x05_zone\"\xd9\x02\n" +
	"\rOptimizeEvent\x12;\n" +
	"\vcaptured_at\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"capturedAt\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12'\n" +
	"\x0fdistribution_id\x18\x03 \x01(\tR\x0edistributionId\x12\x16\n" +
	"\x06region\x18\x04 \x01(\tR\x06region\x12\x17\n" +
	"\x04zone\x18\x05 \x01(\tH\x00R\x04zone\x88\x01\x01\x12\x18\n" +
	"\adrivers\x18\x06 \x03(\tR\adrivers\x12\x16\n" +
	"\x06orders\x18\a \x03(\tR\x06orders\x12-\n" +
	"\x12optimization_round\x18\b \x01(\tR\x11optimizationRound\x123\n" +
	"\x15optimization_response\x18\t \x01(\tR\x14optimizationResponseB\a\n" +
	"\x05_zone\"\x84\x03\n" +
	"\vFilterEvent\x12;\n" +
	"\vcaptured_at\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"capturedAt\x12\x12\n" +
	"\x04type\x18\x02 \x01(\tR\x04type\x12'\n" +
	"\x0fdistribution_id\x18\x03 \x01(\tR\x0edistributionId\x12\x16\n" +
	"\x06region\x18\x04 \x01(\tR\x06region\x12\x17\n" +
	"\x04zone\x18\x05 \x01(\tH\x00R\x04zone\x88\x01\x01\x12%\n" +
	"\x0etarget_drivers\x18\x06 \x03(\tR\rtargetDrivers\x12#\n" +
	"\rtarget_orders\x18\a \x03(\tR\ftargetOrders\x122\n" +
	"\x12optimization_round\x18\b \x01(\tH\x01R\x11optimizationRound\x88\x01\x01\x12\x12\n" +
	"\x04step\x18\t \x01(\tR\x04step\x12\x16\n" +
	"\x06filter\x18\n" +
	" \x01(\tR\x06filterB\a\n" +
	"\x05_zoneB\x15\n" +
	"\x13_optimization_roundB\x8e\x02\n" +
	"\x1bcom.lineman.event.driver.v1B\x14DistributionLogProtoP\x01ZZgit.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1;driverv1\xa2\x02\x03LED\xaa\x02\x17Lineman.Event.Driver.V1\xca\x02\x17Lineman\\Event\\Driver\\V1\xe2\x02#Lineman\\Event\\Driver\\V1\\GPBMetadata\xea\x02\x1aLineman::Event::Driver::V1b\x06proto3"

var (
	file_lineman_event_driver_v1_distribution_log_proto_rawDescOnce sync.Once
	file_lineman_event_driver_v1_distribution_log_proto_rawDescData []byte
)

func file_lineman_event_driver_v1_distribution_log_proto_rawDescGZIP() []byte {
	file_lineman_event_driver_v1_distribution_log_proto_rawDescOnce.Do(func() {
		file_lineman_event_driver_v1_distribution_log_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_lineman_event_driver_v1_distribution_log_proto_rawDesc), len(file_lineman_event_driver_v1_distribution_log_proto_rawDesc)))
	})
	return file_lineman_event_driver_v1_distribution_log_proto_rawDescData
}

var file_lineman_event_driver_v1_distribution_log_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_lineman_event_driver_v1_distribution_log_proto_goTypes = []any{
	(*SearchEvent)(nil),           // 0: lineman.event.driver.v1.SearchEvent
	(*OptimizeEvent)(nil),         // 1: lineman.event.driver.v1.OptimizeEvent
	(*FilterEvent)(nil),           // 2: lineman.event.driver.v1.FilterEvent
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_lineman_event_driver_v1_distribution_log_proto_depIdxs = []int32{
	3, // 0: lineman.event.driver.v1.SearchEvent.captured_at:type_name -> google.protobuf.Timestamp
	3, // 1: lineman.event.driver.v1.SearchEvent.started_at:type_name -> google.protobuf.Timestamp
	3, // 2: lineman.event.driver.v1.OptimizeEvent.captured_at:type_name -> google.protobuf.Timestamp
	3, // 3: lineman.event.driver.v1.FilterEvent.captured_at:type_name -> google.protobuf.Timestamp
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_lineman_event_driver_v1_distribution_log_proto_init() }
func file_lineman_event_driver_v1_distribution_log_proto_init() {
	if File_lineman_event_driver_v1_distribution_log_proto != nil {
		return
	}
	file_lineman_event_driver_v1_distribution_log_proto_msgTypes[0].OneofWrappers = []any{}
	file_lineman_event_driver_v1_distribution_log_proto_msgTypes[1].OneofWrappers = []any{}
	file_lineman_event_driver_v1_distribution_log_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_lineman_event_driver_v1_distribution_log_proto_rawDesc), len(file_lineman_event_driver_v1_distribution_log_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_lineman_event_driver_v1_distribution_log_proto_goTypes,
		DependencyIndexes: file_lineman_event_driver_v1_distribution_log_proto_depIdxs,
		MessageInfos:      file_lineman_event_driver_v1_distribution_log_proto_msgTypes,
	}.Build()
	File_lineman_event_driver_v1_distribution_log_proto = out.File
	file_lineman_event_driver_v1_distribution_log_proto_goTypes = nil
	file_lineman_event_driver_v1_distribution_log_proto_depIdxs = nil
}
