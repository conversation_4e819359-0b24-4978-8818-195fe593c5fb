package metric_test

import (
	"context"
	"testing"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
)

func Test_MetricsRegistry(t *testing.T) {
	t.<PERSON>l()

	ctx := context.Background()
	sm := testmetric.NewStubMeter()
	registry := metric.ProvideMetricsRegistry(sm)

	t.Run("IncrAcceptOrderCounter", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"fleet_pool", "label2"}
		registry.IncrAcceptOrderCounter(ctx, labels...)
		counter := sm.FindCounter("accept_order_search_strategy_count")
		counter.AssertValue(tt, 1, labels...)
	})

	t.Run("IncrSearchDriverInRadiusCounter", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		registry.IncrSearchDriverInRadiusCounter(ctx, labels...)
		counter := sm.FindCounter("search_riders_in_radius_count")
		counter.AssertValue(tt, 1, labels...)
	})

	t.Run("IncrSearchDriverInMultiPolygonCounter", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		registry.IncrSearchDriverInMultiPolygonCounter(ctx, labels...)
		counter := sm.FindCounter("search_riders_in_multi_polygon_count")
		counter.AssertValue(tt, 1, labels...)
	})

	t.Run("ObserveSearchRidersInRadiusLatencyMetric", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		latencyMs := float64(123)
		registry.ObserveSearchRidersInRadiusLatencyMetric(ctx, latencyMs, labels...)
		histogram := sm.FindHistogram("search_riders_in_radius_latency")
		histogram.AssertValue(tt, 0, latencyMs, labels...)
	})

	t.Run("ObserveSearchRidersInMultiPolygonLatencyMetric", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		latencyMs := float64(123)
		registry.ObserveSearchRidersInMultiPolygonLatencyMetric(ctx, latencyMs, labels...)
		histogram := sm.FindHistogram("search_riders_in_multi_polygon_latency")
		histogram.AssertValue(tt, 0, latencyMs, labels...)
	})

	t.Run("IncrSearchNoDriverCountInInRadius", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		registry.IncrSearchNoDriverCountInInRadius(ctx, labels...)
		counter := sm.FindCounter("search_drivers_no_driver_in_radius_count")
		counter.AssertValue(tt, 1, labels...)
	})

	t.Run("IncrSearchNoDriverCountInMultiPolygon", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		registry.IncrSearchNoDriverCountInMultiPolygon(ctx, labels...)
		counter := sm.FindCounter("search_drivers_no_driver_in_multi_polygon_count")
		counter.AssertValue(tt, 1, labels...)
	})

	t.Run("RecordCounterMetric", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		registry.RecordCounterMetric(ctx, metric.CounterOpt{
			MetricName:  "test_counter_metric",
			Description: "Test counter metric",
			Tags:        []string{"tag1", "tag2"},
			Labels:      labels,
		})
		counter := sm.FindCounter("test_counter_metric")
		counter.AssertValue(tt, 1, labels...)
	})

	t.Run("AddCounterMetric", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		registry.AddCounterMetric(ctx, 5, metric.CounterOpt{
			MetricName:  "test_add_counter_metric",
			Description: "Test add counter metric",
			Tags:        []string{"tag1", "tag2"},
			Labels:      labels,
		})
		counter := sm.FindCounter("test_add_counter_metric")
		counter.AssertValue(tt, 5, labels...)
	})

	t.Run("RecordHistogramMetric", func(tt *testing.T) {
		tt.Parallel()
		labels := []string{"label1", "label2"}
		registry.RecordHistogramMetric(ctx, 200, metric.HistogramOpt{
			MetricName:  "test_histogram_metric",
			Description: "Test histogram metric",
			Boundaries:  []float64{100, 200, 300},
			Tags:        []string{"tag1", "tag2"},
			Labels:      labels,
		})
		histogram := sm.FindHistogram("test_histogram_metric")
		histogram.AssertValue(tt, 0, 200, labels...)
	})
}
