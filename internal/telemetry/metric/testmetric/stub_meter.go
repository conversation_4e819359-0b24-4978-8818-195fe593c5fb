// Package testmetric allows developer to unit-test metric with ease
package testmetric

import (
	"strings"
	"sync"
	"testing"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

var (
	_ metric.Meter = &StubMeter{}
)

// StubMeter is an implementation of metric.Meter with abilities to assert expected properties and values
type StubMeter struct {
	histogramsByName     map[string]*StubHistogram
	histogramsByNameLock sync.Mutex
	countersByName       map[string]*StubCounter
	countersByNameLock   sync.Mutex
}

func (sm *StubMeter) GetGauge(name string, labels ...string) metric.Gauge {
	panic("implement me")
}

// NewStubMeter creates new instance of StubMeter
func NewStubMeter() *StubMeter {
	return &StubMeter{
		histogramsByName: map[string]*StubHistogram{},
		countersByName:   map[string]*St<PERSON>Counter{},
	}
}

// GetHistogram returns new or already created StubHistogram of the same name
func (sm *StubMeter) GetHistogram(name string, desc string, buckets []float64, labels ...string) metric.Histogram {
	h := sm.histogramsByName[name]

	if h == nil {
		h = sm.createStubHistogram(name, desc, buckets, labels)
	}

	return h
}

func (sm *StubMeter) createStubHistogram(name string, desc string, buckets []float64, labels []string) *StubHistogram {
	sm.histogramsByNameLock.Lock()
	defer sm.histogramsByNameLock.Unlock()

	h := sm.histogramsByName[name]
	if h != nil {
		return h
	}

	h = &StubHistogram{Name: name, Desc: desc, Buckets: buckets, Labels: labels}

	sm.histogramsByName[name] = h

	return h
}

// FindHistogram returns name matched StubHistogram
func (sm *StubMeter) FindHistogram(name string) *StubHistogram {
	sm.histogramsByNameLock.Lock()
	defer sm.histogramsByNameLock.Unlock()

	return sm.histogramsByName[name]
}

// GetCounter returns new or already created StubCounter of the same name
func (sm *StubMeter) GetCounter(name string, desc string, labels ...string) metric.Counter {
	c := sm.countersByName[name]

	if c == nil {
		c = sm.createStubCounter(name, desc, labels)
	}

	return c
}

func (sm *StubMeter) RegisterCollector(c prometheus.Collector) {
}

func (sm *StubMeter) createStubCounter(name string, desc string, labels []string) *StubCounter {
	sm.countersByNameLock.Lock()
	defer sm.countersByNameLock.Unlock()

	c := sm.countersByName[name]
	if c != nil {
		return c
	}

	c = &StubCounter{Name: name, Desc: desc, Labels: labels, values: map[string]float64{}}

	sm.countersByName[name] = c

	return c
}

// FindCounter returns name matched StubCounter
func (sm *StubMeter) FindCounter(name string) *StubCounter {
	sm.countersByNameLock.Lock()
	defer sm.countersByNameLock.Unlock()

	return sm.countersByName[name]
}

// StubHistogram is an implementation of metric.Histogram which allows developer to assert properties and values
type StubHistogram struct {
	Name    string
	Desc    string
	Buckets []float64
	Labels  []string
	Values  []stubHistogramValue
}

type stubHistogramValue struct {
	Value       float64
	LabelValues []string
}

func (sh *StubHistogram) Observe(value float64, labelValues ...string) {
	sh.Values = append(sh.Values, stubHistogramValue{Value: value, LabelValues: labelValues})
}

// Assert histogram properties
func (sh *StubHistogram) Assert(t *testing.T, expected StubHistogram) {
	require.Equal(t, expected.Name, sh.Name)
	require.Equal(t, expected.Desc, sh.Desc)
	require.Equal(t, expected.Buckets, sh.Buckets)
	require.Equal(t, expected.Labels, sh.Labels)
}

// Assert histogram value of the given index
func (sh *StubHistogram) AssertValue(t *testing.T, index int, expectedValue float64, expectedValueLabels ...string) {
	v := sh.Values[index]

	require.Equal(t, expectedValue, v.Value)
	require.Equal(t, expectedValueLabels, v.LabelValues)
}

// StubCounter is an implementation of metric.Counter which allows developer to assert properties and values
type StubCounter struct {
	Name   string
	Desc   string
	Labels []string
	values map[string]float64
	mu     sync.RWMutex
}

func (sh *StubCounter) Inc(labelValues ...string) {
	sh.mu.Lock()
	defer sh.mu.Unlock()

	key := sh.key(labelValues)
	v, ok := sh.values[key]
	if !ok {
		v = 0
	}
	v = v + 1
	sh.values[key] = v
}

func (sh *StubCounter) Add(value float64, labelValues ...string) {
	sh.mu.Lock()
	defer sh.mu.Unlock()

	key := sh.key(labelValues)
	v, ok := sh.values[key]
	if !ok {
		v = 0
	}
	v = v + value
	sh.values[key] = v
}

// Assert counter properties
func (sh *StubCounter) Assert(t *testing.T, expected *StubCounter) {
	require.Equal(t, expected.Name, sh.Name)
	require.Equal(t, expected.Desc, sh.Desc)
	require.Equal(t, expected.Labels, sh.Labels)
}

// Assert counter value of the given label values
func (sh *StubCounter) AssertValue(t *testing.T, expectedValue float64, labelValues ...string) {
	sh.mu.RLock()
	defer sh.mu.RUnlock()

	key := sh.key(labelValues)
	v, ok := sh.values[key]
	if !ok {
		logrus.Infof("Cannot find counter value for %s. Available counters are %v", key, sh.values)
	}

	require.Equal(t, expectedValue, v)
}

func (sh *StubCounter) key(labelValues []string) string {
	return strings.Join(labelValues, ",")
}
