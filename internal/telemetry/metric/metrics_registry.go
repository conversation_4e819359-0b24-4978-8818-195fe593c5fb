package metric

//go:generate mockgen -source=./metrics_registry.go -destination=./mock_metrics/mock_metrics_registry.go -package=mock_metrics

import (
	"context"
	"sync"
)

type counterMap struct {
	rw sync.RWMutex
	m  map[string]Counter
}

func (c *counterMap) Get(name string) (Counter, bool) {
	c.rw.RLock()
	defer c.rw.RUnlock()
	v, ok := c.m[name]
	return v, ok
}

func (c *counterMap) Add(name string, m Counter) {
	c.rw.Lock()
	defer c.rw.Unlock()
	c.m[name] = m
}

type histogramMap struct {
	rw sync.RWMutex
	m  map[string]Histogram
}

func (h *histogramMap) Get(name string) (Histogram, bool) {
	h.rw.RLock()
	defer h.rw.RUnlock()
	v, ok := h.m[name]
	return v, ok
}

func (h *histogramMap) Add(name string, m Histogram) {
	h.rw.Lock()
	defer h.rw.Unlock()
	h.m[name] = m
}

type MetricsRegistry interface {
	IncrAcceptOrderCounter(ctx context.Context, labels ...string)
	IncrSearchDriverInRadiusCounter(ctx context.Context, labels ...string)
	IncrSearchDriverInMultiPolygonCounter(ctx context.Context, labels ...string)
	ObserveSearchRidersInRadiusLatencyMetric(ctx context.Context, latencyMs float64, labels ...string)
	ObserveSearchRidersInMultiPolygonLatencyMetric(ctx context.Context, latencyMs float64, labels ...string)
	IncrSearchNoDriverCountInInRadius(ctx context.Context, labels ...string)
	IncrSearchNoDriverCountInMultiPolygon(ctx context.Context, labels ...string)
	IncrValidateAutoAssignDriverIsEligibleCounter(ctx context.Context, labels ...string)
}

type MetricsRegistryImpl struct {
	mu         sync.RWMutex
	meter      Meter
	counters   *counterMap
	histograms *histogramMap
}

type HistogramOpt struct {
	Description string
	Meter       Meter
	MetricName  string
	Labels      []string
	Tags        []string
	Boundaries  []float64
}

type CounterOpt struct {
	Description string
	Meter       Meter
	MetricName  string
	Labels      []string
	Tags        []string
}

func ProvideMetricsRegistry(meter Meter) *MetricsRegistryImpl {
	return &MetricsRegistryImpl{
		meter:      meter,
		counters:   &counterMap{m: map[string]Counter{}},
		histograms: &histogramMap{m: map[string]Histogram{}},
	}
}

func (r *MetricsRegistryImpl) getHistogram(_ context.Context, opts HistogramOpt) Histogram {
	if h, ok := r.histograms.Get(opts.MetricName); ok {
		return h
	}
	r.mu.Lock()
	defer r.mu.Unlock()
	h := r.meter.GetHistogram(
		opts.MetricName,
		opts.Description,
		opts.Boundaries,
		opts.Tags...,
	)
	r.histograms.Add(opts.MetricName, h)

	return h
}

func (r *MetricsRegistryImpl) getCounter(ctx context.Context, opts CounterOpt) Counter {
	if c, ok := r.counters.Get(opts.MetricName); ok {
		return c
	}

	ct := r.meter.GetCounter(
		opts.MetricName,
		opts.Description,
		opts.Tags...,
	)
	r.counters.Add(opts.MetricName, ct)

	return ct
}

func (r *MetricsRegistryImpl) RecordCounterMetric(ctx context.Context, opts CounterOpt) {
	r.mu.Lock()
	defer r.mu.Unlock()

	c := r.getCounter(ctx, opts)
	c.Inc(opts.Labels...)
}

func (r *MetricsRegistryImpl) AddCounterMetric(ctx context.Context, value float64, opts CounterOpt) {
	c := r.getCounter(ctx, opts)
	c.Add(value, opts.Labels...)
}

func (r *MetricsRegistryImpl) RecordHistogramMetric(ctx context.Context, value int64, opts HistogramOpt) {
	h := r.getHistogram(ctx, opts)
	h.Observe(float64(value), opts.Labels...)
}

func (r *MetricsRegistryImpl) IncrAcceptOrderCounter(ctx context.Context, labels ...string) {
	if len(labels) < 1 || "fleet_pool" != labels[0] {
		return
	}
	r.RecordCounterMetric(ctx, CounterOpt{
		MetricName:  "accept_order_search_strategy_count",
		Description: "Count number of search rider strategy for accept order",
		Tags:        []string{"search_strategy", "zone_id", "region", "status"},
		Labels:      labels,
	})
}

func (r *MetricsRegistryImpl) IncrSearchNoDriverCountInMultiPolygon(ctx context.Context, labels ...string) {
	r.RecordCounterMetric(ctx, CounterOpt{
		MetricName:  "search_drivers_no_driver_in_multi_polygon_count",
		Description: "No driver result in multi polygon count",
		Tags:        []string{"strategy"},
		Labels:      labels,
	})
}
func (r *MetricsRegistryImpl) IncrSearchNoDriverCountInInRadius(ctx context.Context, labels ...string) {
	r.RecordCounterMetric(ctx, CounterOpt{
		MetricName:  "search_drivers_no_driver_in_radius_count",
		Description: "No driver result in radius count",
		Tags:        []string{"strategy", "region"},
		Labels:      labels,
	})
}

func (r *MetricsRegistryImpl) ObserveSearchRidersInRadiusLatencyMetric(ctx context.Context, latencyMs float64, labels ...string) {
	r.RecordHistogramMetric(ctx, int64(latencyMs), HistogramOpt{
		MetricName:  "search_riders_in_radius_latency",
		Description: "Latency of searching drivers in radius in milliseconds",
		Boundaries:  []float64{100, 250, 500, 1000, 5000, 10000, 30000},
		Tags:        []string{"strategy", "region"},
		Labels:      labels,
	})
}

func (r *MetricsRegistryImpl) ObserveSearchRidersInMultiPolygonLatencyMetric(ctx context.Context, latencyMs float64, labels ...string) {
	r.RecordHistogramMetric(ctx, int64(latencyMs), HistogramOpt{
		MetricName:  "search_riders_in_multi_polygon_latency",
		Description: "Latency of searching drivers in multi polygon in milliseconds",
		Boundaries:  []float64{100, 250, 500, 1000, 5000, 10000, 30000},
		Tags:        []string{"strategy"},
		Labels:      labels,
	})
}

func (r *MetricsRegistryImpl) IncrSearchDriverInRadiusCounter(ctx context.Context, labels ...string) {
	r.RecordCounterMetric(ctx, CounterOpt{
		MetricName:  "search_riders_in_radius_count",
		Description: "Count number of search rider in radius",
		Tags:        []string{"strategy", "region", "status"},
		Labels:      labels,
	})
}

func (r *MetricsRegistryImpl) IncrSearchDriverInMultiPolygonCounter(ctx context.Context, labels ...string) {
	r.RecordCounterMetric(ctx, CounterOpt{
		MetricName:  "search_riders_in_multi_polygon_count",
		Description: "Count number of search rider in multi polygon",
		Tags:        []string{"strategy", "status"},
		Labels:      labels,
	})
}

func (r *MetricsRegistryImpl) IncrValidateAutoAssignDriverIsEligibleCounter(ctx context.Context, labels ...string) {
	r.RecordCounterMetric(ctx, CounterOpt{
		MetricName:  "validate_auto_assign_driver_is_eligible_count",
		Description: "Count number of validate auto assign driver is eligible",
		Tags:        []string{"error_code", "region"},
		Labels:      labels,
	})
}
