package metric

import (
	"time"

	"github.com/pkg/errors"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
)

const (
	LabelAPILatencyName = "name"
)

type PrometheusCollector struct {
	apiLatencyHist *prometheus.HistogramVec
}

func NewPrometheusCollector() *PrometheusCollector {
	apiLatencyHist := prometheus.NewHistogramVec(prometheus.HistogramOpts{
		Namespace: "api",
		Name:      "latency_ms",
		Help:      "API latency",
		Buckets:   []float64{1, 5, 15, 20, 50, 70, 100, 200, 500, 700, 1000, 2000, 5000, 8000},
	}, []string{LabelAPILatencyName})

	if err := prometheus.Register(apiLatencyHist); err != nil {
		are, ok := err.(prometheus.AlreadyRegisteredError)
		if !ok {
			panic(err)
		}
		apiLatencyHist = are.ExistingCollector.(*prometheus.HistogramVec)
	}

	return &PrometheusCollector{apiLatencyHist: apiLatencyHist}
}

func (pc *PrometheusCollector) Save(m Measurement) error {
	switch t := m.(type) {
	case *PrometheusAPILatency:
		if err := m.Write(pc); err != nil {
			logrus.Errorf("PrometheusCollector.Save: %v", err)
		}
		for lap, ms := range t.Laps() {
			pc.apiLatencyHist.WithLabelValues(lap).Observe(float64(ms))
		}
	default:
		return errors.New("unknown measurement")
	}

	return nil
}

type lap struct {
	name string
	time time.Time
}

type PrometheusAPILatency struct {
	name  string
	start time.Time
	laps  []lap
}

func NewPrometheusAPILatency(name string) *PrometheusAPILatency {
	return &PrometheusAPILatency{
		name:  name,
		start: time.Now(),
		laps:  make([]lap, 0, 20),
	}
}

func (pl *PrometheusAPILatency) Start() APILatency {
	pl.start = time.Now()
	return pl
}

func (pl *PrometheusAPILatency) Lap(lapName string) {
	pl.laps = append(pl.laps, lap{
		name: pl.name + "_" + lapName,
		time: time.Now(),
	})
}

func (pl *PrometheusAPILatency) Stop() APILatency {
	if pl.tailLap().name == pl.name {
		return pl
	}

	pl.laps = append(pl.laps, lap{
		name: pl.name,
		time: time.Now(),
	})
	return pl
}

func (pl *PrometheusAPILatency) Laps() map[string]time.Duration {
	result := make(map[string]time.Duration)
	prevLap := lap{name: "start", time: pl.start}
	for _, lap := range pl.laps {
		if lap.name == pl.name {
			result[lap.name] = lap.time.Sub(pl.start) / time.Millisecond
		} else {
			result[lap.name] = lap.time.Sub(prevLap.time) / time.Millisecond
		}
		prevLap = lap
	}
	return result
}

func (pl *PrometheusAPILatency) Write(_ Collector) error {
	pl.Stop()
	return nil
}

func (pl *PrometheusAPILatency) tailLap() lap {
	size := len(pl.laps)
	if size == 0 {
		return lap{}
	}

	return pl.laps[size-1]
}

// PrometheusMeter is an implementation of metric.Meter which basically write values using prometheus api
type PrometheusMeter struct {
}

// ProvidePrometheusMeter creates new instance of PrometheusMeter
func ProvidePrometheusMeter() *PrometheusMeter {
	return &PrometheusMeter{}
}

func (pm *PrometheusMeter) GetGauge(name string, labels ...string) Gauge {
	vec := prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: name,
		Help: name,
	}, labels)

	if err := prometheus.Register(vec); err != nil {
		are, ok := err.(prometheus.AlreadyRegisteredError)
		if !ok {
			panic(err)
		}
		vec = are.ExistingCollector.(*prometheus.GaugeVec)
	}

	return &PrometheusGauge{
		gaugeVec: vec,
	}
}

// GetHistogram returns new or already created Histogram with the same name, buckets and labels
func (pm *PrometheusMeter) GetHistogram(name string, desc string, buckets []float64, labels ...string) Histogram {
	histogramVec := prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Name:    name,
			Help:    desc,
			Buckets: buckets,
		}, labels)

	if err := prometheus.Register(histogramVec); err != nil {
		are, ok := err.(prometheus.AlreadyRegisteredError)
		if !ok {
			panic(err)
		}
		histogramVec = are.ExistingCollector.(*prometheus.HistogramVec)
	}

	return &PrometheusHistogram{histogramVec: histogramVec}
}

// GetCounter returns new or already created Counter with the same name and labels
func (pm *PrometheusMeter) GetCounter(name string, desc string, labels ...string) Counter {
	counterVec := prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Name: name,
			Help: desc,
		}, labels)

	if err := prometheus.Register(counterVec); err != nil {
		are, ok := err.(prometheus.AlreadyRegisteredError)
		if !ok {
			panic(err)
		}
		counterVec = are.ExistingCollector.(*prometheus.CounterVec)
	}

	return &PrometheusCounter{counterVec: counterVec}
}

func (pm *PrometheusMeter) RegisterCollector(c prometheus.Collector) {
	if err := prometheus.Register(c); err != nil {
		_, ok := err.(prometheus.AlreadyRegisteredError)
		if !ok {
			panic(err)
		}
	}
}

// PrometheusHistogram is a prometheus implementation of Histogram
type PrometheusHistogram struct {
	histogramVec *prometheus.HistogramVec
}

// Observe writes given value and label values to underlying prometheus histogram
func (ph *PrometheusHistogram) Observe(value float64, labelValues ...string) {
	ph.histogramVec.WithLabelValues(labelValues...).Observe(value)
}

// Unwrap returns underlying prometheus histogram. This is primarily used for unit testing.
func (ph *PrometheusHistogram) Unwrap() *prometheus.HistogramVec {
	return ph.histogramVec
}

type PrometheusCounter struct {
	counterVec *prometheus.CounterVec
}

// Inc increments by 1 and label values to underlying prometheus counter
func (ph *PrometheusCounter) Inc(labelValues ...string) {
	ph.counterVec.WithLabelValues(labelValues...).Inc()
}

func (ph *PrometheusCounter) Add(value float64, labelValues ...string) {
	ph.counterVec.WithLabelValues(labelValues...).Add(value)
}

// Unwrap returns underlying prometheus counter. This is primarily used for unit testing.
func (ph *PrometheusCounter) Unwrap() *prometheus.CounterVec {
	return ph.counterVec
}

type PrometheusGauge struct {
	gaugeVec *prometheus.GaugeVec
}

func (p *PrometheusGauge) Set(val float64, labelValues ...string) {
	p.gaugeVec.WithLabelValues(labelValues...).Set(val)
}

func (p *PrometheusGauge) Add(val float64, labelValues ...string) {
	p.gaugeVec.WithLabelValues(labelValues...).Add(val)
}

func (p *PrometheusGauge) Sub(val float64, labelValues ...string) {
	p.gaugeVec.WithLabelValues(labelValues...).Sub(val)
}

func (p *PrometheusGauge) Unwrap() *prometheus.GaugeVec {
	return p.gaugeVec
}
