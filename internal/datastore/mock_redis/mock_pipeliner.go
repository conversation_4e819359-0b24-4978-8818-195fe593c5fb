// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/redis/go-redis/v9 (interfaces: Pipeliner)

// Package mock_redis is a generated GoMock package.
package mock_redis

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
	redis "github.com/redis/go-redis/v9"
)

// MockPipeliner is a mock of Pipeliner interface.
type MockPipeliner struct {
	ctrl     *gomock.Controller
	recorder *MockPipelinerMockRecorder
}

// MockPipelinerMockRecorder is the mock recorder for MockPipeliner.
type MockPipelinerMockRecorder struct {
	mock *MockPipeliner
}

// NewMockPipeliner creates a new mock instance.
func NewMockPipeliner(ctrl *gomock.Controller) *MockPipeliner {
	mock := &MockPipeliner{ctrl: ctrl}
	mock.recorder = &MockPipelinerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPipeliner) EXPECT() *MockPipelinerMockRecorder {
	return m.recorder
}

// ACLDryRun mocks base method.
func (m *MockPipeliner) ACLDryRun(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.StringCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ACLDryRun", varargs...)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ACLDryRun indicates an expected call of ACLDryRun.
func (mr *MockPipelinerMockRecorder) ACLDryRun(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ACLDryRun", reflect.TypeOf((*MockPipeliner)(nil).ACLDryRun), varargs...)
}

// ACLLog mocks base method.
func (m *MockPipeliner) ACLLog(arg0 context.Context, arg1 int64) *redis.ACLLogCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ACLLog", arg0, arg1)
	ret0, _ := ret[0].(*redis.ACLLogCmd)
	return ret0
}

// ACLLog indicates an expected call of ACLLog.
func (mr *MockPipelinerMockRecorder) ACLLog(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ACLLog", reflect.TypeOf((*MockPipeliner)(nil).ACLLog), arg0, arg1)
}

// ACLLogReset mocks base method.
func (m *MockPipeliner) ACLLogReset(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ACLLogReset", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ACLLogReset indicates an expected call of ACLLogReset.
func (mr *MockPipelinerMockRecorder) ACLLogReset(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ACLLogReset", reflect.TypeOf((*MockPipeliner)(nil).ACLLogReset), arg0)
}

// Append mocks base method.
func (m *MockPipeliner) Append(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Append", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Append indicates an expected call of Append.
func (mr *MockPipelinerMockRecorder) Append(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Append", reflect.TypeOf((*MockPipeliner)(nil).Append), arg0, arg1, arg2)
}

// Auth mocks base method.
func (m *MockPipeliner) Auth(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Auth", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Auth indicates an expected call of Auth.
func (mr *MockPipelinerMockRecorder) Auth(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Auth", reflect.TypeOf((*MockPipeliner)(nil).Auth), arg0, arg1)
}

// AuthACL mocks base method.
func (m *MockPipeliner) AuthACL(arg0 context.Context, arg1, arg2 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthACL", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// AuthACL indicates an expected call of AuthACL.
func (mr *MockPipelinerMockRecorder) AuthACL(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthACL", reflect.TypeOf((*MockPipeliner)(nil).AuthACL), arg0, arg1, arg2)
}

// BFAdd mocks base method.
func (m *MockPipeliner) BFAdd(arg0 context.Context, arg1 string, arg2 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFAdd", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// BFAdd indicates an expected call of BFAdd.
func (mr *MockPipelinerMockRecorder) BFAdd(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFAdd", reflect.TypeOf((*MockPipeliner)(nil).BFAdd), arg0, arg1, arg2)
}

// BFCard mocks base method.
func (m *MockPipeliner) BFCard(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFCard", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// BFCard indicates an expected call of BFCard.
func (mr *MockPipelinerMockRecorder) BFCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFCard", reflect.TypeOf((*MockPipeliner)(nil).BFCard), arg0, arg1)
}

// BFExists mocks base method.
func (m *MockPipeliner) BFExists(arg0 context.Context, arg1 string, arg2 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFExists", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// BFExists indicates an expected call of BFExists.
func (mr *MockPipelinerMockRecorder) BFExists(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFExists", reflect.TypeOf((*MockPipeliner)(nil).BFExists), arg0, arg1, arg2)
}

// BFInfo mocks base method.
func (m *MockPipeliner) BFInfo(arg0 context.Context, arg1 string) *redis.BFInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFInfo", arg0, arg1)
	ret0, _ := ret[0].(*redis.BFInfoCmd)
	return ret0
}

// BFInfo indicates an expected call of BFInfo.
func (mr *MockPipelinerMockRecorder) BFInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFInfo", reflect.TypeOf((*MockPipeliner)(nil).BFInfo), arg0, arg1)
}

// BFInfoArg mocks base method.
func (m *MockPipeliner) BFInfoArg(arg0 context.Context, arg1, arg2 string) *redis.BFInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFInfoArg", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BFInfoCmd)
	return ret0
}

// BFInfoArg indicates an expected call of BFInfoArg.
func (mr *MockPipelinerMockRecorder) BFInfoArg(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFInfoArg", reflect.TypeOf((*MockPipeliner)(nil).BFInfoArg), arg0, arg1, arg2)
}

// BFInfoCapacity mocks base method.
func (m *MockPipeliner) BFInfoCapacity(arg0 context.Context, arg1 string) *redis.BFInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFInfoCapacity", arg0, arg1)
	ret0, _ := ret[0].(*redis.BFInfoCmd)
	return ret0
}

// BFInfoCapacity indicates an expected call of BFInfoCapacity.
func (mr *MockPipelinerMockRecorder) BFInfoCapacity(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFInfoCapacity", reflect.TypeOf((*MockPipeliner)(nil).BFInfoCapacity), arg0, arg1)
}

// BFInfoExpansion mocks base method.
func (m *MockPipeliner) BFInfoExpansion(arg0 context.Context, arg1 string) *redis.BFInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFInfoExpansion", arg0, arg1)
	ret0, _ := ret[0].(*redis.BFInfoCmd)
	return ret0
}

// BFInfoExpansion indicates an expected call of BFInfoExpansion.
func (mr *MockPipelinerMockRecorder) BFInfoExpansion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFInfoExpansion", reflect.TypeOf((*MockPipeliner)(nil).BFInfoExpansion), arg0, arg1)
}

// BFInfoFilters mocks base method.
func (m *MockPipeliner) BFInfoFilters(arg0 context.Context, arg1 string) *redis.BFInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFInfoFilters", arg0, arg1)
	ret0, _ := ret[0].(*redis.BFInfoCmd)
	return ret0
}

// BFInfoFilters indicates an expected call of BFInfoFilters.
func (mr *MockPipelinerMockRecorder) BFInfoFilters(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFInfoFilters", reflect.TypeOf((*MockPipeliner)(nil).BFInfoFilters), arg0, arg1)
}

// BFInfoItems mocks base method.
func (m *MockPipeliner) BFInfoItems(arg0 context.Context, arg1 string) *redis.BFInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFInfoItems", arg0, arg1)
	ret0, _ := ret[0].(*redis.BFInfoCmd)
	return ret0
}

// BFInfoItems indicates an expected call of BFInfoItems.
func (mr *MockPipelinerMockRecorder) BFInfoItems(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFInfoItems", reflect.TypeOf((*MockPipeliner)(nil).BFInfoItems), arg0, arg1)
}

// BFInfoSize mocks base method.
func (m *MockPipeliner) BFInfoSize(arg0 context.Context, arg1 string) *redis.BFInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFInfoSize", arg0, arg1)
	ret0, _ := ret[0].(*redis.BFInfoCmd)
	return ret0
}

// BFInfoSize indicates an expected call of BFInfoSize.
func (mr *MockPipelinerMockRecorder) BFInfoSize(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFInfoSize", reflect.TypeOf((*MockPipeliner)(nil).BFInfoSize), arg0, arg1)
}

// BFInsert mocks base method.
func (m *MockPipeliner) BFInsert(arg0 context.Context, arg1 string, arg2 *redis.BFInsertOptions, arg3 ...interface{}) *redis.BoolSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BFInsert", varargs...)
	ret0, _ := ret[0].(*redis.BoolSliceCmd)
	return ret0
}

// BFInsert indicates an expected call of BFInsert.
func (mr *MockPipelinerMockRecorder) BFInsert(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFInsert", reflect.TypeOf((*MockPipeliner)(nil).BFInsert), varargs...)
}

// BFLoadChunk mocks base method.
func (m *MockPipeliner) BFLoadChunk(arg0 context.Context, arg1 string, arg2 int64, arg3 interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFLoadChunk", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// BFLoadChunk indicates an expected call of BFLoadChunk.
func (mr *MockPipelinerMockRecorder) BFLoadChunk(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFLoadChunk", reflect.TypeOf((*MockPipeliner)(nil).BFLoadChunk), arg0, arg1, arg2, arg3)
}

// BFMAdd mocks base method.
func (m *MockPipeliner) BFMAdd(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.BoolSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BFMAdd", varargs...)
	ret0, _ := ret[0].(*redis.BoolSliceCmd)
	return ret0
}

// BFMAdd indicates an expected call of BFMAdd.
func (mr *MockPipelinerMockRecorder) BFMAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFMAdd", reflect.TypeOf((*MockPipeliner)(nil).BFMAdd), varargs...)
}

// BFMExists mocks base method.
func (m *MockPipeliner) BFMExists(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.BoolSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BFMExists", varargs...)
	ret0, _ := ret[0].(*redis.BoolSliceCmd)
	return ret0
}

// BFMExists indicates an expected call of BFMExists.
func (mr *MockPipelinerMockRecorder) BFMExists(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFMExists", reflect.TypeOf((*MockPipeliner)(nil).BFMExists), varargs...)
}

// BFReserve mocks base method.
func (m *MockPipeliner) BFReserve(arg0 context.Context, arg1 string, arg2 float64, arg3 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFReserve", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// BFReserve indicates an expected call of BFReserve.
func (mr *MockPipelinerMockRecorder) BFReserve(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFReserve", reflect.TypeOf((*MockPipeliner)(nil).BFReserve), arg0, arg1, arg2, arg3)
}

// BFReserveExpansion mocks base method.
func (m *MockPipeliner) BFReserveExpansion(arg0 context.Context, arg1 string, arg2 float64, arg3, arg4 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFReserveExpansion", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// BFReserveExpansion indicates an expected call of BFReserveExpansion.
func (mr *MockPipelinerMockRecorder) BFReserveExpansion(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFReserveExpansion", reflect.TypeOf((*MockPipeliner)(nil).BFReserveExpansion), arg0, arg1, arg2, arg3, arg4)
}

// BFReserveNonScaling mocks base method.
func (m *MockPipeliner) BFReserveNonScaling(arg0 context.Context, arg1 string, arg2 float64, arg3 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFReserveNonScaling", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// BFReserveNonScaling indicates an expected call of BFReserveNonScaling.
func (mr *MockPipelinerMockRecorder) BFReserveNonScaling(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFReserveNonScaling", reflect.TypeOf((*MockPipeliner)(nil).BFReserveNonScaling), arg0, arg1, arg2, arg3)
}

// BFReserveWithArgs mocks base method.
func (m *MockPipeliner) BFReserveWithArgs(arg0 context.Context, arg1 string, arg2 *redis.BFReserveOptions) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFReserveWithArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// BFReserveWithArgs indicates an expected call of BFReserveWithArgs.
func (mr *MockPipelinerMockRecorder) BFReserveWithArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFReserveWithArgs", reflect.TypeOf((*MockPipeliner)(nil).BFReserveWithArgs), arg0, arg1, arg2)
}

// BFScanDump mocks base method.
func (m *MockPipeliner) BFScanDump(arg0 context.Context, arg1 string, arg2 int64) *redis.ScanDumpCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BFScanDump", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.ScanDumpCmd)
	return ret0
}

// BFScanDump indicates an expected call of BFScanDump.
func (mr *MockPipelinerMockRecorder) BFScanDump(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BFScanDump", reflect.TypeOf((*MockPipeliner)(nil).BFScanDump), arg0, arg1, arg2)
}

// BLMPop mocks base method.
func (m *MockPipeliner) BLMPop(arg0 context.Context, arg1 time.Duration, arg2 string, arg3 int64, arg4 ...string) *redis.KeyValuesCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BLMPop", varargs...)
	ret0, _ := ret[0].(*redis.KeyValuesCmd)
	return ret0
}

// BLMPop indicates an expected call of BLMPop.
func (mr *MockPipelinerMockRecorder) BLMPop(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BLMPop", reflect.TypeOf((*MockPipeliner)(nil).BLMPop), varargs...)
}

// BLMove mocks base method.
func (m *MockPipeliner) BLMove(arg0 context.Context, arg1, arg2, arg3, arg4 string, arg5 time.Duration) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BLMove", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// BLMove indicates an expected call of BLMove.
func (mr *MockPipelinerMockRecorder) BLMove(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BLMove", reflect.TypeOf((*MockPipeliner)(nil).BLMove), arg0, arg1, arg2, arg3, arg4, arg5)
}

// BLPop mocks base method.
func (m *MockPipeliner) BLPop(arg0 context.Context, arg1 time.Duration, arg2 ...string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BLPop", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// BLPop indicates an expected call of BLPop.
func (mr *MockPipelinerMockRecorder) BLPop(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BLPop", reflect.TypeOf((*MockPipeliner)(nil).BLPop), varargs...)
}

// BRPop mocks base method.
func (m *MockPipeliner) BRPop(arg0 context.Context, arg1 time.Duration, arg2 ...string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BRPop", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// BRPop indicates an expected call of BRPop.
func (mr *MockPipelinerMockRecorder) BRPop(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BRPop", reflect.TypeOf((*MockPipeliner)(nil).BRPop), varargs...)
}

// BRPopLPush mocks base method.
func (m *MockPipeliner) BRPopLPush(arg0 context.Context, arg1, arg2 string, arg3 time.Duration) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BRPopLPush", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// BRPopLPush indicates an expected call of BRPopLPush.
func (mr *MockPipelinerMockRecorder) BRPopLPush(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BRPopLPush", reflect.TypeOf((*MockPipeliner)(nil).BRPopLPush), arg0, arg1, arg2, arg3)
}

// BZMPop mocks base method.
func (m *MockPipeliner) BZMPop(arg0 context.Context, arg1 time.Duration, arg2 string, arg3 int64, arg4 ...string) *redis.ZSliceWithKeyCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BZMPop", varargs...)
	ret0, _ := ret[0].(*redis.ZSliceWithKeyCmd)
	return ret0
}

// BZMPop indicates an expected call of BZMPop.
func (mr *MockPipelinerMockRecorder) BZMPop(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BZMPop", reflect.TypeOf((*MockPipeliner)(nil).BZMPop), varargs...)
}

// BZPopMax mocks base method.
func (m *MockPipeliner) BZPopMax(arg0 context.Context, arg1 time.Duration, arg2 ...string) *redis.ZWithKeyCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BZPopMax", varargs...)
	ret0, _ := ret[0].(*redis.ZWithKeyCmd)
	return ret0
}

// BZPopMax indicates an expected call of BZPopMax.
func (mr *MockPipelinerMockRecorder) BZPopMax(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BZPopMax", reflect.TypeOf((*MockPipeliner)(nil).BZPopMax), varargs...)
}

// BZPopMin mocks base method.
func (m *MockPipeliner) BZPopMin(arg0 context.Context, arg1 time.Duration, arg2 ...string) *redis.ZWithKeyCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BZPopMin", varargs...)
	ret0, _ := ret[0].(*redis.ZWithKeyCmd)
	return ret0
}

// BZPopMin indicates an expected call of BZPopMin.
func (mr *MockPipelinerMockRecorder) BZPopMin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BZPopMin", reflect.TypeOf((*MockPipeliner)(nil).BZPopMin), varargs...)
}

// BgRewriteAOF mocks base method.
func (m *MockPipeliner) BgRewriteAOF(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BgRewriteAOF", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// BgRewriteAOF indicates an expected call of BgRewriteAOF.
func (mr *MockPipelinerMockRecorder) BgRewriteAOF(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BgRewriteAOF", reflect.TypeOf((*MockPipeliner)(nil).BgRewriteAOF), arg0)
}

// BgSave mocks base method.
func (m *MockPipeliner) BgSave(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BgSave", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// BgSave indicates an expected call of BgSave.
func (mr *MockPipelinerMockRecorder) BgSave(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BgSave", reflect.TypeOf((*MockPipeliner)(nil).BgSave), arg0)
}

// BitCount mocks base method.
func (m *MockPipeliner) BitCount(arg0 context.Context, arg1 string, arg2 *redis.BitCount) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BitCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// BitCount indicates an expected call of BitCount.
func (mr *MockPipelinerMockRecorder) BitCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitCount", reflect.TypeOf((*MockPipeliner)(nil).BitCount), arg0, arg1, arg2)
}

// BitField mocks base method.
func (m *MockPipeliner) BitField(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BitField", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// BitField indicates an expected call of BitField.
func (mr *MockPipelinerMockRecorder) BitField(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitField", reflect.TypeOf((*MockPipeliner)(nil).BitField), varargs...)
}

// BitFieldRO mocks base method.
func (m *MockPipeliner) BitFieldRO(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BitFieldRO", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// BitFieldRO indicates an expected call of BitFieldRO.
func (mr *MockPipelinerMockRecorder) BitFieldRO(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitFieldRO", reflect.TypeOf((*MockPipeliner)(nil).BitFieldRO), varargs...)
}

// BitOpAnd mocks base method.
func (m *MockPipeliner) BitOpAnd(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BitOpAnd", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// BitOpAnd indicates an expected call of BitOpAnd.
func (mr *MockPipelinerMockRecorder) BitOpAnd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitOpAnd", reflect.TypeOf((*MockPipeliner)(nil).BitOpAnd), varargs...)
}

// BitOpNot mocks base method.
func (m *MockPipeliner) BitOpNot(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BitOpNot", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// BitOpNot indicates an expected call of BitOpNot.
func (mr *MockPipelinerMockRecorder) BitOpNot(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitOpNot", reflect.TypeOf((*MockPipeliner)(nil).BitOpNot), arg0, arg1, arg2)
}

// BitOpOr mocks base method.
func (m *MockPipeliner) BitOpOr(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BitOpOr", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// BitOpOr indicates an expected call of BitOpOr.
func (mr *MockPipelinerMockRecorder) BitOpOr(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitOpOr", reflect.TypeOf((*MockPipeliner)(nil).BitOpOr), varargs...)
}

// BitOpXor mocks base method.
func (m *MockPipeliner) BitOpXor(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BitOpXor", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// BitOpXor indicates an expected call of BitOpXor.
func (mr *MockPipelinerMockRecorder) BitOpXor(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitOpXor", reflect.TypeOf((*MockPipeliner)(nil).BitOpXor), varargs...)
}

// BitPos mocks base method.
func (m *MockPipeliner) BitPos(arg0 context.Context, arg1 string, arg2 int64, arg3 ...int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BitPos", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// BitPos indicates an expected call of BitPos.
func (mr *MockPipelinerMockRecorder) BitPos(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitPos", reflect.TypeOf((*MockPipeliner)(nil).BitPos), varargs...)
}

// BitPosSpan mocks base method.
func (m *MockPipeliner) BitPosSpan(arg0 context.Context, arg1 string, arg2 int8, arg3, arg4 int64, arg5 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BitPosSpan", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// BitPosSpan indicates an expected call of BitPosSpan.
func (mr *MockPipelinerMockRecorder) BitPosSpan(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BitPosSpan", reflect.TypeOf((*MockPipeliner)(nil).BitPosSpan), arg0, arg1, arg2, arg3, arg4, arg5)
}

// CFAdd mocks base method.
func (m *MockPipeliner) CFAdd(arg0 context.Context, arg1 string, arg2 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFAdd", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// CFAdd indicates an expected call of CFAdd.
func (mr *MockPipelinerMockRecorder) CFAdd(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFAdd", reflect.TypeOf((*MockPipeliner)(nil).CFAdd), arg0, arg1, arg2)
}

// CFAddNX mocks base method.
func (m *MockPipeliner) CFAddNX(arg0 context.Context, arg1 string, arg2 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFAddNX", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// CFAddNX indicates an expected call of CFAddNX.
func (mr *MockPipelinerMockRecorder) CFAddNX(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFAddNX", reflect.TypeOf((*MockPipeliner)(nil).CFAddNX), arg0, arg1, arg2)
}

// CFCount mocks base method.
func (m *MockPipeliner) CFCount(arg0 context.Context, arg1 string, arg2 interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// CFCount indicates an expected call of CFCount.
func (mr *MockPipelinerMockRecorder) CFCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFCount", reflect.TypeOf((*MockPipeliner)(nil).CFCount), arg0, arg1, arg2)
}

// CFDel mocks base method.
func (m *MockPipeliner) CFDel(arg0 context.Context, arg1 string, arg2 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFDel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// CFDel indicates an expected call of CFDel.
func (mr *MockPipelinerMockRecorder) CFDel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFDel", reflect.TypeOf((*MockPipeliner)(nil).CFDel), arg0, arg1, arg2)
}

// CFExists mocks base method.
func (m *MockPipeliner) CFExists(arg0 context.Context, arg1 string, arg2 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFExists", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// CFExists indicates an expected call of CFExists.
func (mr *MockPipelinerMockRecorder) CFExists(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFExists", reflect.TypeOf((*MockPipeliner)(nil).CFExists), arg0, arg1, arg2)
}

// CFInfo mocks base method.
func (m *MockPipeliner) CFInfo(arg0 context.Context, arg1 string) *redis.CFInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFInfo", arg0, arg1)
	ret0, _ := ret[0].(*redis.CFInfoCmd)
	return ret0
}

// CFInfo indicates an expected call of CFInfo.
func (mr *MockPipelinerMockRecorder) CFInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFInfo", reflect.TypeOf((*MockPipeliner)(nil).CFInfo), arg0, arg1)
}

// CFInsert mocks base method.
func (m *MockPipeliner) CFInsert(arg0 context.Context, arg1 string, arg2 *redis.CFInsertOptions, arg3 ...interface{}) *redis.BoolSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CFInsert", varargs...)
	ret0, _ := ret[0].(*redis.BoolSliceCmd)
	return ret0
}

// CFInsert indicates an expected call of CFInsert.
func (mr *MockPipelinerMockRecorder) CFInsert(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFInsert", reflect.TypeOf((*MockPipeliner)(nil).CFInsert), varargs...)
}

// CFInsertNX mocks base method.
func (m *MockPipeliner) CFInsertNX(arg0 context.Context, arg1 string, arg2 *redis.CFInsertOptions, arg3 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CFInsertNX", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// CFInsertNX indicates an expected call of CFInsertNX.
func (mr *MockPipelinerMockRecorder) CFInsertNX(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFInsertNX", reflect.TypeOf((*MockPipeliner)(nil).CFInsertNX), varargs...)
}

// CFLoadChunk mocks base method.
func (m *MockPipeliner) CFLoadChunk(arg0 context.Context, arg1 string, arg2 int64, arg3 interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFLoadChunk", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CFLoadChunk indicates an expected call of CFLoadChunk.
func (mr *MockPipelinerMockRecorder) CFLoadChunk(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFLoadChunk", reflect.TypeOf((*MockPipeliner)(nil).CFLoadChunk), arg0, arg1, arg2, arg3)
}

// CFMExists mocks base method.
func (m *MockPipeliner) CFMExists(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.BoolSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CFMExists", varargs...)
	ret0, _ := ret[0].(*redis.BoolSliceCmd)
	return ret0
}

// CFMExists indicates an expected call of CFMExists.
func (mr *MockPipelinerMockRecorder) CFMExists(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFMExists", reflect.TypeOf((*MockPipeliner)(nil).CFMExists), varargs...)
}

// CFReserve mocks base method.
func (m *MockPipeliner) CFReserve(arg0 context.Context, arg1 string, arg2 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFReserve", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CFReserve indicates an expected call of CFReserve.
func (mr *MockPipelinerMockRecorder) CFReserve(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFReserve", reflect.TypeOf((*MockPipeliner)(nil).CFReserve), arg0, arg1, arg2)
}

// CFReserveBucketSize mocks base method.
func (m *MockPipeliner) CFReserveBucketSize(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFReserveBucketSize", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CFReserveBucketSize indicates an expected call of CFReserveBucketSize.
func (mr *MockPipelinerMockRecorder) CFReserveBucketSize(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFReserveBucketSize", reflect.TypeOf((*MockPipeliner)(nil).CFReserveBucketSize), arg0, arg1, arg2, arg3)
}

// CFReserveExpansion mocks base method.
func (m *MockPipeliner) CFReserveExpansion(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFReserveExpansion", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CFReserveExpansion indicates an expected call of CFReserveExpansion.
func (mr *MockPipelinerMockRecorder) CFReserveExpansion(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFReserveExpansion", reflect.TypeOf((*MockPipeliner)(nil).CFReserveExpansion), arg0, arg1, arg2, arg3)
}

// CFReserveMaxIterations mocks base method.
func (m *MockPipeliner) CFReserveMaxIterations(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFReserveMaxIterations", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CFReserveMaxIterations indicates an expected call of CFReserveMaxIterations.
func (mr *MockPipelinerMockRecorder) CFReserveMaxIterations(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFReserveMaxIterations", reflect.TypeOf((*MockPipeliner)(nil).CFReserveMaxIterations), arg0, arg1, arg2, arg3)
}

// CFReserveWithArgs mocks base method.
func (m *MockPipeliner) CFReserveWithArgs(arg0 context.Context, arg1 string, arg2 *redis.CFReserveOptions) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFReserveWithArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CFReserveWithArgs indicates an expected call of CFReserveWithArgs.
func (mr *MockPipelinerMockRecorder) CFReserveWithArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFReserveWithArgs", reflect.TypeOf((*MockPipeliner)(nil).CFReserveWithArgs), arg0, arg1, arg2)
}

// CFScanDump mocks base method.
func (m *MockPipeliner) CFScanDump(arg0 context.Context, arg1 string, arg2 int64) *redis.ScanDumpCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CFScanDump", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.ScanDumpCmd)
	return ret0
}

// CFScanDump indicates an expected call of CFScanDump.
func (mr *MockPipelinerMockRecorder) CFScanDump(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CFScanDump", reflect.TypeOf((*MockPipeliner)(nil).CFScanDump), arg0, arg1, arg2)
}

// CMSIncrBy mocks base method.
func (m *MockPipeliner) CMSIncrBy(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CMSIncrBy", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// CMSIncrBy indicates an expected call of CMSIncrBy.
func (mr *MockPipelinerMockRecorder) CMSIncrBy(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CMSIncrBy", reflect.TypeOf((*MockPipeliner)(nil).CMSIncrBy), varargs...)
}

// CMSInfo mocks base method.
func (m *MockPipeliner) CMSInfo(arg0 context.Context, arg1 string) *redis.CMSInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CMSInfo", arg0, arg1)
	ret0, _ := ret[0].(*redis.CMSInfoCmd)
	return ret0
}

// CMSInfo indicates an expected call of CMSInfo.
func (mr *MockPipelinerMockRecorder) CMSInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CMSInfo", reflect.TypeOf((*MockPipeliner)(nil).CMSInfo), arg0, arg1)
}

// CMSInitByDim mocks base method.
func (m *MockPipeliner) CMSInitByDim(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CMSInitByDim", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CMSInitByDim indicates an expected call of CMSInitByDim.
func (mr *MockPipelinerMockRecorder) CMSInitByDim(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CMSInitByDim", reflect.TypeOf((*MockPipeliner)(nil).CMSInitByDim), arg0, arg1, arg2, arg3)
}

// CMSInitByProb mocks base method.
func (m *MockPipeliner) CMSInitByProb(arg0 context.Context, arg1 string, arg2, arg3 float64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CMSInitByProb", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CMSInitByProb indicates an expected call of CMSInitByProb.
func (mr *MockPipelinerMockRecorder) CMSInitByProb(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CMSInitByProb", reflect.TypeOf((*MockPipeliner)(nil).CMSInitByProb), arg0, arg1, arg2, arg3)
}

// CMSMerge mocks base method.
func (m *MockPipeliner) CMSMerge(arg0 context.Context, arg1 string, arg2 ...string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CMSMerge", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CMSMerge indicates an expected call of CMSMerge.
func (mr *MockPipelinerMockRecorder) CMSMerge(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CMSMerge", reflect.TypeOf((*MockPipeliner)(nil).CMSMerge), varargs...)
}

// CMSMergeWithWeight mocks base method.
func (m *MockPipeliner) CMSMergeWithWeight(arg0 context.Context, arg1 string, arg2 map[string]int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CMSMergeWithWeight", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// CMSMergeWithWeight indicates an expected call of CMSMergeWithWeight.
func (mr *MockPipelinerMockRecorder) CMSMergeWithWeight(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CMSMergeWithWeight", reflect.TypeOf((*MockPipeliner)(nil).CMSMergeWithWeight), arg0, arg1, arg2)
}

// CMSQuery mocks base method.
func (m *MockPipeliner) CMSQuery(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CMSQuery", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// CMSQuery indicates an expected call of CMSQuery.
func (mr *MockPipelinerMockRecorder) CMSQuery(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CMSQuery", reflect.TypeOf((*MockPipeliner)(nil).CMSQuery), varargs...)
}

// ClientGetName mocks base method.
func (m *MockPipeliner) ClientGetName(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientGetName", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ClientGetName indicates an expected call of ClientGetName.
func (mr *MockPipelinerMockRecorder) ClientGetName(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientGetName", reflect.TypeOf((*MockPipeliner)(nil).ClientGetName), arg0)
}

// ClientID mocks base method.
func (m *MockPipeliner) ClientID(arg0 context.Context) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientID", arg0)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ClientID indicates an expected call of ClientID.
func (mr *MockPipelinerMockRecorder) ClientID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientID", reflect.TypeOf((*MockPipeliner)(nil).ClientID), arg0)
}

// ClientInfo mocks base method.
func (m *MockPipeliner) ClientInfo(arg0 context.Context) *redis.ClientInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientInfo", arg0)
	ret0, _ := ret[0].(*redis.ClientInfoCmd)
	return ret0
}

// ClientInfo indicates an expected call of ClientInfo.
func (mr *MockPipelinerMockRecorder) ClientInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientInfo", reflect.TypeOf((*MockPipeliner)(nil).ClientInfo), arg0)
}

// ClientKill mocks base method.
func (m *MockPipeliner) ClientKill(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientKill", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClientKill indicates an expected call of ClientKill.
func (mr *MockPipelinerMockRecorder) ClientKill(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientKill", reflect.TypeOf((*MockPipeliner)(nil).ClientKill), arg0, arg1)
}

// ClientKillByFilter mocks base method.
func (m *MockPipeliner) ClientKillByFilter(arg0 context.Context, arg1 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClientKillByFilter", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ClientKillByFilter indicates an expected call of ClientKillByFilter.
func (mr *MockPipelinerMockRecorder) ClientKillByFilter(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientKillByFilter", reflect.TypeOf((*MockPipeliner)(nil).ClientKillByFilter), varargs...)
}

// ClientList mocks base method.
func (m *MockPipeliner) ClientList(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientList", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ClientList indicates an expected call of ClientList.
func (mr *MockPipelinerMockRecorder) ClientList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientList", reflect.TypeOf((*MockPipeliner)(nil).ClientList), arg0)
}

// ClientPause mocks base method.
func (m *MockPipeliner) ClientPause(arg0 context.Context, arg1 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientPause", arg0, arg1)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// ClientPause indicates an expected call of ClientPause.
func (mr *MockPipelinerMockRecorder) ClientPause(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientPause", reflect.TypeOf((*MockPipeliner)(nil).ClientPause), arg0, arg1)
}

// ClientSetInfo mocks base method.
func (m *MockPipeliner) ClientSetInfo(arg0 context.Context, arg1 redis.LibraryInfo) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientSetInfo", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClientSetInfo indicates an expected call of ClientSetInfo.
func (mr *MockPipelinerMockRecorder) ClientSetInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientSetInfo", reflect.TypeOf((*MockPipeliner)(nil).ClientSetInfo), arg0, arg1)
}

// ClientSetName mocks base method.
func (m *MockPipeliner) ClientSetName(arg0 context.Context, arg1 string) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientSetName", arg0, arg1)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// ClientSetName indicates an expected call of ClientSetName.
func (mr *MockPipelinerMockRecorder) ClientSetName(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientSetName", reflect.TypeOf((*MockPipeliner)(nil).ClientSetName), arg0, arg1)
}

// ClientUnblock mocks base method.
func (m *MockPipeliner) ClientUnblock(arg0 context.Context, arg1 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientUnblock", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ClientUnblock indicates an expected call of ClientUnblock.
func (mr *MockPipelinerMockRecorder) ClientUnblock(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientUnblock", reflect.TypeOf((*MockPipeliner)(nil).ClientUnblock), arg0, arg1)
}

// ClientUnblockWithError mocks base method.
func (m *MockPipeliner) ClientUnblockWithError(arg0 context.Context, arg1 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientUnblockWithError", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ClientUnblockWithError indicates an expected call of ClientUnblockWithError.
func (mr *MockPipelinerMockRecorder) ClientUnblockWithError(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientUnblockWithError", reflect.TypeOf((*MockPipeliner)(nil).ClientUnblockWithError), arg0, arg1)
}

// ClientUnpause mocks base method.
func (m *MockPipeliner) ClientUnpause(arg0 context.Context) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClientUnpause", arg0)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// ClientUnpause indicates an expected call of ClientUnpause.
func (mr *MockPipelinerMockRecorder) ClientUnpause(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClientUnpause", reflect.TypeOf((*MockPipeliner)(nil).ClientUnpause), arg0)
}

// ClusterAddSlots mocks base method.
func (m *MockPipeliner) ClusterAddSlots(arg0 context.Context, arg1 ...int) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClusterAddSlots", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterAddSlots indicates an expected call of ClusterAddSlots.
func (mr *MockPipelinerMockRecorder) ClusterAddSlots(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterAddSlots", reflect.TypeOf((*MockPipeliner)(nil).ClusterAddSlots), varargs...)
}

// ClusterAddSlotsRange mocks base method.
func (m *MockPipeliner) ClusterAddSlotsRange(arg0 context.Context, arg1, arg2 int) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterAddSlotsRange", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterAddSlotsRange indicates an expected call of ClusterAddSlotsRange.
func (mr *MockPipelinerMockRecorder) ClusterAddSlotsRange(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterAddSlotsRange", reflect.TypeOf((*MockPipeliner)(nil).ClusterAddSlotsRange), arg0, arg1, arg2)
}

// ClusterCountFailureReports mocks base method.
func (m *MockPipeliner) ClusterCountFailureReports(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterCountFailureReports", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ClusterCountFailureReports indicates an expected call of ClusterCountFailureReports.
func (mr *MockPipelinerMockRecorder) ClusterCountFailureReports(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterCountFailureReports", reflect.TypeOf((*MockPipeliner)(nil).ClusterCountFailureReports), arg0, arg1)
}

// ClusterCountKeysInSlot mocks base method.
func (m *MockPipeliner) ClusterCountKeysInSlot(arg0 context.Context, arg1 int) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterCountKeysInSlot", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ClusterCountKeysInSlot indicates an expected call of ClusterCountKeysInSlot.
func (mr *MockPipelinerMockRecorder) ClusterCountKeysInSlot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterCountKeysInSlot", reflect.TypeOf((*MockPipeliner)(nil).ClusterCountKeysInSlot), arg0, arg1)
}

// ClusterDelSlots mocks base method.
func (m *MockPipeliner) ClusterDelSlots(arg0 context.Context, arg1 ...int) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ClusterDelSlots", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterDelSlots indicates an expected call of ClusterDelSlots.
func (mr *MockPipelinerMockRecorder) ClusterDelSlots(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterDelSlots", reflect.TypeOf((*MockPipeliner)(nil).ClusterDelSlots), varargs...)
}

// ClusterDelSlotsRange mocks base method.
func (m *MockPipeliner) ClusterDelSlotsRange(arg0 context.Context, arg1, arg2 int) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterDelSlotsRange", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterDelSlotsRange indicates an expected call of ClusterDelSlotsRange.
func (mr *MockPipelinerMockRecorder) ClusterDelSlotsRange(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterDelSlotsRange", reflect.TypeOf((*MockPipeliner)(nil).ClusterDelSlotsRange), arg0, arg1, arg2)
}

// ClusterFailover mocks base method.
func (m *MockPipeliner) ClusterFailover(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterFailover", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterFailover indicates an expected call of ClusterFailover.
func (mr *MockPipelinerMockRecorder) ClusterFailover(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterFailover", reflect.TypeOf((*MockPipeliner)(nil).ClusterFailover), arg0)
}

// ClusterForget mocks base method.
func (m *MockPipeliner) ClusterForget(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterForget", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterForget indicates an expected call of ClusterForget.
func (mr *MockPipelinerMockRecorder) ClusterForget(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterForget", reflect.TypeOf((*MockPipeliner)(nil).ClusterForget), arg0, arg1)
}

// ClusterGetKeysInSlot mocks base method.
func (m *MockPipeliner) ClusterGetKeysInSlot(arg0 context.Context, arg1, arg2 int) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterGetKeysInSlot", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ClusterGetKeysInSlot indicates an expected call of ClusterGetKeysInSlot.
func (mr *MockPipelinerMockRecorder) ClusterGetKeysInSlot(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterGetKeysInSlot", reflect.TypeOf((*MockPipeliner)(nil).ClusterGetKeysInSlot), arg0, arg1, arg2)
}

// ClusterInfo mocks base method.
func (m *MockPipeliner) ClusterInfo(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterInfo", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ClusterInfo indicates an expected call of ClusterInfo.
func (mr *MockPipelinerMockRecorder) ClusterInfo(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterInfo", reflect.TypeOf((*MockPipeliner)(nil).ClusterInfo), arg0)
}

// ClusterKeySlot mocks base method.
func (m *MockPipeliner) ClusterKeySlot(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterKeySlot", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ClusterKeySlot indicates an expected call of ClusterKeySlot.
func (mr *MockPipelinerMockRecorder) ClusterKeySlot(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterKeySlot", reflect.TypeOf((*MockPipeliner)(nil).ClusterKeySlot), arg0, arg1)
}

// ClusterLinks mocks base method.
func (m *MockPipeliner) ClusterLinks(arg0 context.Context) *redis.ClusterLinksCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterLinks", arg0)
	ret0, _ := ret[0].(*redis.ClusterLinksCmd)
	return ret0
}

// ClusterLinks indicates an expected call of ClusterLinks.
func (mr *MockPipelinerMockRecorder) ClusterLinks(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterLinks", reflect.TypeOf((*MockPipeliner)(nil).ClusterLinks), arg0)
}

// ClusterMeet mocks base method.
func (m *MockPipeliner) ClusterMeet(arg0 context.Context, arg1, arg2 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterMeet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterMeet indicates an expected call of ClusterMeet.
func (mr *MockPipelinerMockRecorder) ClusterMeet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterMeet", reflect.TypeOf((*MockPipeliner)(nil).ClusterMeet), arg0, arg1, arg2)
}

// ClusterMyShardID mocks base method.
func (m *MockPipeliner) ClusterMyShardID(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterMyShardID", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ClusterMyShardID indicates an expected call of ClusterMyShardID.
func (mr *MockPipelinerMockRecorder) ClusterMyShardID(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterMyShardID", reflect.TypeOf((*MockPipeliner)(nil).ClusterMyShardID), arg0)
}

// ClusterNodes mocks base method.
func (m *MockPipeliner) ClusterNodes(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterNodes", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ClusterNodes indicates an expected call of ClusterNodes.
func (mr *MockPipelinerMockRecorder) ClusterNodes(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterNodes", reflect.TypeOf((*MockPipeliner)(nil).ClusterNodes), arg0)
}

// ClusterReplicate mocks base method.
func (m *MockPipeliner) ClusterReplicate(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterReplicate", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterReplicate indicates an expected call of ClusterReplicate.
func (mr *MockPipelinerMockRecorder) ClusterReplicate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterReplicate", reflect.TypeOf((*MockPipeliner)(nil).ClusterReplicate), arg0, arg1)
}

// ClusterResetHard mocks base method.
func (m *MockPipeliner) ClusterResetHard(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterResetHard", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterResetHard indicates an expected call of ClusterResetHard.
func (mr *MockPipelinerMockRecorder) ClusterResetHard(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterResetHard", reflect.TypeOf((*MockPipeliner)(nil).ClusterResetHard), arg0)
}

// ClusterResetSoft mocks base method.
func (m *MockPipeliner) ClusterResetSoft(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterResetSoft", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterResetSoft indicates an expected call of ClusterResetSoft.
func (mr *MockPipelinerMockRecorder) ClusterResetSoft(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterResetSoft", reflect.TypeOf((*MockPipeliner)(nil).ClusterResetSoft), arg0)
}

// ClusterSaveConfig mocks base method.
func (m *MockPipeliner) ClusterSaveConfig(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterSaveConfig", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ClusterSaveConfig indicates an expected call of ClusterSaveConfig.
func (mr *MockPipelinerMockRecorder) ClusterSaveConfig(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterSaveConfig", reflect.TypeOf((*MockPipeliner)(nil).ClusterSaveConfig), arg0)
}

// ClusterShards mocks base method.
func (m *MockPipeliner) ClusterShards(arg0 context.Context) *redis.ClusterShardsCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterShards", arg0)
	ret0, _ := ret[0].(*redis.ClusterShardsCmd)
	return ret0
}

// ClusterShards indicates an expected call of ClusterShards.
func (mr *MockPipelinerMockRecorder) ClusterShards(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterShards", reflect.TypeOf((*MockPipeliner)(nil).ClusterShards), arg0)
}

// ClusterSlaves mocks base method.
func (m *MockPipeliner) ClusterSlaves(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterSlaves", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ClusterSlaves indicates an expected call of ClusterSlaves.
func (mr *MockPipelinerMockRecorder) ClusterSlaves(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterSlaves", reflect.TypeOf((*MockPipeliner)(nil).ClusterSlaves), arg0, arg1)
}

// ClusterSlots mocks base method.
func (m *MockPipeliner) ClusterSlots(arg0 context.Context) *redis.ClusterSlotsCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ClusterSlots", arg0)
	ret0, _ := ret[0].(*redis.ClusterSlotsCmd)
	return ret0
}

// ClusterSlots indicates an expected call of ClusterSlots.
func (mr *MockPipelinerMockRecorder) ClusterSlots(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ClusterSlots", reflect.TypeOf((*MockPipeliner)(nil).ClusterSlots), arg0)
}

// Command mocks base method.
func (m *MockPipeliner) Command(arg0 context.Context) *redis.CommandsInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Command", arg0)
	ret0, _ := ret[0].(*redis.CommandsInfoCmd)
	return ret0
}

// Command indicates an expected call of Command.
func (mr *MockPipelinerMockRecorder) Command(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Command", reflect.TypeOf((*MockPipeliner)(nil).Command), arg0)
}

// CommandGetKeys mocks base method.
func (m *MockPipeliner) CommandGetKeys(arg0 context.Context, arg1 ...interface{}) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CommandGetKeys", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// CommandGetKeys indicates an expected call of CommandGetKeys.
func (mr *MockPipelinerMockRecorder) CommandGetKeys(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommandGetKeys", reflect.TypeOf((*MockPipeliner)(nil).CommandGetKeys), varargs...)
}

// CommandGetKeysAndFlags mocks base method.
func (m *MockPipeliner) CommandGetKeysAndFlags(arg0 context.Context, arg1 ...interface{}) *redis.KeyFlagsCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CommandGetKeysAndFlags", varargs...)
	ret0, _ := ret[0].(*redis.KeyFlagsCmd)
	return ret0
}

// CommandGetKeysAndFlags indicates an expected call of CommandGetKeysAndFlags.
func (mr *MockPipelinerMockRecorder) CommandGetKeysAndFlags(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommandGetKeysAndFlags", reflect.TypeOf((*MockPipeliner)(nil).CommandGetKeysAndFlags), varargs...)
}

// CommandList mocks base method.
func (m *MockPipeliner) CommandList(arg0 context.Context, arg1 *redis.FilterBy) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CommandList", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// CommandList indicates an expected call of CommandList.
func (mr *MockPipelinerMockRecorder) CommandList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CommandList", reflect.TypeOf((*MockPipeliner)(nil).CommandList), arg0, arg1)
}

// ConfigGet mocks base method.
func (m *MockPipeliner) ConfigGet(arg0 context.Context, arg1 string) *redis.MapStringStringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigGet", arg0, arg1)
	ret0, _ := ret[0].(*redis.MapStringStringCmd)
	return ret0
}

// ConfigGet indicates an expected call of ConfigGet.
func (mr *MockPipelinerMockRecorder) ConfigGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigGet", reflect.TypeOf((*MockPipeliner)(nil).ConfigGet), arg0, arg1)
}

// ConfigResetStat mocks base method.
func (m *MockPipeliner) ConfigResetStat(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigResetStat", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ConfigResetStat indicates an expected call of ConfigResetStat.
func (mr *MockPipelinerMockRecorder) ConfigResetStat(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigResetStat", reflect.TypeOf((*MockPipeliner)(nil).ConfigResetStat), arg0)
}

// ConfigRewrite mocks base method.
func (m *MockPipeliner) ConfigRewrite(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigRewrite", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ConfigRewrite indicates an expected call of ConfigRewrite.
func (mr *MockPipelinerMockRecorder) ConfigRewrite(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigRewrite", reflect.TypeOf((*MockPipeliner)(nil).ConfigRewrite), arg0)
}

// ConfigSet mocks base method.
func (m *MockPipeliner) ConfigSet(arg0 context.Context, arg1, arg2 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConfigSet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ConfigSet indicates an expected call of ConfigSet.
func (mr *MockPipelinerMockRecorder) ConfigSet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConfigSet", reflect.TypeOf((*MockPipeliner)(nil).ConfigSet), arg0, arg1, arg2)
}

// Copy mocks base method.
func (m *MockPipeliner) Copy(arg0 context.Context, arg1, arg2 string, arg3 int, arg4 bool) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Copy", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Copy indicates an expected call of Copy.
func (mr *MockPipelinerMockRecorder) Copy(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Copy", reflect.TypeOf((*MockPipeliner)(nil).Copy), arg0, arg1, arg2, arg3, arg4)
}

// DBSize mocks base method.
func (m *MockPipeliner) DBSize(arg0 context.Context) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DBSize", arg0)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// DBSize indicates an expected call of DBSize.
func (mr *MockPipelinerMockRecorder) DBSize(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DBSize", reflect.TypeOf((*MockPipeliner)(nil).DBSize), arg0)
}

// DebugObject mocks base method.
func (m *MockPipeliner) DebugObject(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DebugObject", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// DebugObject indicates an expected call of DebugObject.
func (mr *MockPipelinerMockRecorder) DebugObject(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DebugObject", reflect.TypeOf((*MockPipeliner)(nil).DebugObject), arg0, arg1)
}

// Decr mocks base method.
func (m *MockPipeliner) Decr(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Decr", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Decr indicates an expected call of Decr.
func (mr *MockPipelinerMockRecorder) Decr(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Decr", reflect.TypeOf((*MockPipeliner)(nil).Decr), arg0, arg1)
}

// DecrBy mocks base method.
func (m *MockPipeliner) DecrBy(arg0 context.Context, arg1 string, arg2 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DecrBy", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// DecrBy indicates an expected call of DecrBy.
func (mr *MockPipelinerMockRecorder) DecrBy(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DecrBy", reflect.TypeOf((*MockPipeliner)(nil).DecrBy), arg0, arg1, arg2)
}

// Del mocks base method.
func (m *MockPipeliner) Del(arg0 context.Context, arg1 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Del", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Del indicates an expected call of Del.
func (mr *MockPipelinerMockRecorder) Del(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Del", reflect.TypeOf((*MockPipeliner)(nil).Del), varargs...)
}

// Discard mocks base method.
func (m *MockPipeliner) Discard() {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Discard")
}

// Discard indicates an expected call of Discard.
func (mr *MockPipelinerMockRecorder) Discard() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Discard", reflect.TypeOf((*MockPipeliner)(nil).Discard))
}

// Do mocks base method.
func (m *MockPipeliner) Do(arg0 context.Context, arg1 ...interface{}) *redis.Cmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Do", varargs...)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// Do indicates an expected call of Do.
func (mr *MockPipelinerMockRecorder) Do(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Do", reflect.TypeOf((*MockPipeliner)(nil).Do), varargs...)
}

// Dump mocks base method.
func (m *MockPipeliner) Dump(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Dump", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// Dump indicates an expected call of Dump.
func (mr *MockPipelinerMockRecorder) Dump(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Dump", reflect.TypeOf((*MockPipeliner)(nil).Dump), arg0, arg1)
}

// Echo mocks base method.
func (m *MockPipeliner) Echo(arg0 context.Context, arg1 interface{}) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Echo", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// Echo indicates an expected call of Echo.
func (mr *MockPipelinerMockRecorder) Echo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Echo", reflect.TypeOf((*MockPipeliner)(nil).Echo), arg0, arg1)
}

// Eval mocks base method.
func (m *MockPipeliner) Eval(arg0 context.Context, arg1 string, arg2 []string, arg3 ...interface{}) *redis.Cmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Eval", varargs...)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// Eval indicates an expected call of Eval.
func (mr *MockPipelinerMockRecorder) Eval(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Eval", reflect.TypeOf((*MockPipeliner)(nil).Eval), varargs...)
}

// EvalRO mocks base method.
func (m *MockPipeliner) EvalRO(arg0 context.Context, arg1 string, arg2 []string, arg3 ...interface{}) *redis.Cmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EvalRO", varargs...)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// EvalRO indicates an expected call of EvalRO.
func (mr *MockPipelinerMockRecorder) EvalRO(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvalRO", reflect.TypeOf((*MockPipeliner)(nil).EvalRO), varargs...)
}

// EvalSha mocks base method.
func (m *MockPipeliner) EvalSha(arg0 context.Context, arg1 string, arg2 []string, arg3 ...interface{}) *redis.Cmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EvalSha", varargs...)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// EvalSha indicates an expected call of EvalSha.
func (mr *MockPipelinerMockRecorder) EvalSha(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvalSha", reflect.TypeOf((*MockPipeliner)(nil).EvalSha), varargs...)
}

// EvalShaRO mocks base method.
func (m *MockPipeliner) EvalShaRO(arg0 context.Context, arg1 string, arg2 []string, arg3 ...interface{}) *redis.Cmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "EvalShaRO", varargs...)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// EvalShaRO indicates an expected call of EvalShaRO.
func (mr *MockPipelinerMockRecorder) EvalShaRO(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EvalShaRO", reflect.TypeOf((*MockPipeliner)(nil).EvalShaRO), varargs...)
}

// Exec mocks base method.
func (m *MockPipeliner) Exec(arg0 context.Context) ([]redis.Cmder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Exec", arg0)
	ret0, _ := ret[0].([]redis.Cmder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Exec indicates an expected call of Exec.
func (mr *MockPipelinerMockRecorder) Exec(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exec", reflect.TypeOf((*MockPipeliner)(nil).Exec), arg0)
}

// Exists mocks base method.
func (m *MockPipeliner) Exists(arg0 context.Context, arg1 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Exists", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Exists indicates an expected call of Exists.
func (mr *MockPipelinerMockRecorder) Exists(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Exists", reflect.TypeOf((*MockPipeliner)(nil).Exists), varargs...)
}

// Expire mocks base method.
func (m *MockPipeliner) Expire(arg0 context.Context, arg1 string, arg2 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Expire", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// Expire indicates an expected call of Expire.
func (mr *MockPipelinerMockRecorder) Expire(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Expire", reflect.TypeOf((*MockPipeliner)(nil).Expire), arg0, arg1, arg2)
}

// ExpireAt mocks base method.
func (m *MockPipeliner) ExpireAt(arg0 context.Context, arg1 string, arg2 time.Time) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireAt", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// ExpireAt indicates an expected call of ExpireAt.
func (mr *MockPipelinerMockRecorder) ExpireAt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireAt", reflect.TypeOf((*MockPipeliner)(nil).ExpireAt), arg0, arg1, arg2)
}

// ExpireGT mocks base method.
func (m *MockPipeliner) ExpireGT(arg0 context.Context, arg1 string, arg2 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireGT", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// ExpireGT indicates an expected call of ExpireGT.
func (mr *MockPipelinerMockRecorder) ExpireGT(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireGT", reflect.TypeOf((*MockPipeliner)(nil).ExpireGT), arg0, arg1, arg2)
}

// ExpireLT mocks base method.
func (m *MockPipeliner) ExpireLT(arg0 context.Context, arg1 string, arg2 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireLT", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// ExpireLT indicates an expected call of ExpireLT.
func (mr *MockPipelinerMockRecorder) ExpireLT(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireLT", reflect.TypeOf((*MockPipeliner)(nil).ExpireLT), arg0, arg1, arg2)
}

// ExpireNX mocks base method.
func (m *MockPipeliner) ExpireNX(arg0 context.Context, arg1 string, arg2 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireNX", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// ExpireNX indicates an expected call of ExpireNX.
func (mr *MockPipelinerMockRecorder) ExpireNX(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireNX", reflect.TypeOf((*MockPipeliner)(nil).ExpireNX), arg0, arg1, arg2)
}

// ExpireTime mocks base method.
func (m *MockPipeliner) ExpireTime(arg0 context.Context, arg1 string) *redis.DurationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireTime", arg0, arg1)
	ret0, _ := ret[0].(*redis.DurationCmd)
	return ret0
}

// ExpireTime indicates an expected call of ExpireTime.
func (mr *MockPipelinerMockRecorder) ExpireTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireTime", reflect.TypeOf((*MockPipeliner)(nil).ExpireTime), arg0, arg1)
}

// ExpireXX mocks base method.
func (m *MockPipeliner) ExpireXX(arg0 context.Context, arg1 string, arg2 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExpireXX", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// ExpireXX indicates an expected call of ExpireXX.
func (mr *MockPipelinerMockRecorder) ExpireXX(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExpireXX", reflect.TypeOf((*MockPipeliner)(nil).ExpireXX), arg0, arg1, arg2)
}

// FCall mocks base method.
func (m *MockPipeliner) FCall(arg0 context.Context, arg1 string, arg2 []string, arg3 ...interface{}) *redis.Cmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FCall", varargs...)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// FCall indicates an expected call of FCall.
func (mr *MockPipelinerMockRecorder) FCall(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FCall", reflect.TypeOf((*MockPipeliner)(nil).FCall), varargs...)
}

// FCallRO mocks base method.
func (m *MockPipeliner) FCallRO(arg0 context.Context, arg1 string, arg2 []string, arg3 ...interface{}) *redis.Cmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FCallRO", varargs...)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// FCallRO indicates an expected call of FCallRO.
func (mr *MockPipelinerMockRecorder) FCallRO(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FCallRO", reflect.TypeOf((*MockPipeliner)(nil).FCallRO), varargs...)
}

// FCallRo mocks base method.
func (m *MockPipeliner) FCallRo(arg0 context.Context, arg1 string, arg2 []string, arg3 ...interface{}) *redis.Cmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FCallRo", varargs...)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// FCallRo indicates an expected call of FCallRo.
func (mr *MockPipelinerMockRecorder) FCallRo(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FCallRo", reflect.TypeOf((*MockPipeliner)(nil).FCallRo), varargs...)
}

// FTAggregate mocks base method.
func (m *MockPipeliner) FTAggregate(arg0 context.Context, arg1, arg2 string) *redis.MapStringInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTAggregate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.MapStringInterfaceCmd)
	return ret0
}

// FTAggregate indicates an expected call of FTAggregate.
func (mr *MockPipelinerMockRecorder) FTAggregate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTAggregate", reflect.TypeOf((*MockPipeliner)(nil).FTAggregate), arg0, arg1, arg2)
}

// FTAggregateWithArgs mocks base method.
func (m *MockPipeliner) FTAggregateWithArgs(arg0 context.Context, arg1, arg2 string, arg3 *redis.FTAggregateOptions) *redis.AggregateCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTAggregateWithArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.AggregateCmd)
	return ret0
}

// FTAggregateWithArgs indicates an expected call of FTAggregateWithArgs.
func (mr *MockPipelinerMockRecorder) FTAggregateWithArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTAggregateWithArgs", reflect.TypeOf((*MockPipeliner)(nil).FTAggregateWithArgs), arg0, arg1, arg2, arg3)
}

// FTAliasAdd mocks base method.
func (m *MockPipeliner) FTAliasAdd(arg0 context.Context, arg1, arg2 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTAliasAdd", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTAliasAdd indicates an expected call of FTAliasAdd.
func (mr *MockPipelinerMockRecorder) FTAliasAdd(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTAliasAdd", reflect.TypeOf((*MockPipeliner)(nil).FTAliasAdd), arg0, arg1, arg2)
}

// FTAliasDel mocks base method.
func (m *MockPipeliner) FTAliasDel(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTAliasDel", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTAliasDel indicates an expected call of FTAliasDel.
func (mr *MockPipelinerMockRecorder) FTAliasDel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTAliasDel", reflect.TypeOf((*MockPipeliner)(nil).FTAliasDel), arg0, arg1)
}

// FTAliasUpdate mocks base method.
func (m *MockPipeliner) FTAliasUpdate(arg0 context.Context, arg1, arg2 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTAliasUpdate", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTAliasUpdate indicates an expected call of FTAliasUpdate.
func (mr *MockPipelinerMockRecorder) FTAliasUpdate(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTAliasUpdate", reflect.TypeOf((*MockPipeliner)(nil).FTAliasUpdate), arg0, arg1, arg2)
}

// FTAlter mocks base method.
func (m *MockPipeliner) FTAlter(arg0 context.Context, arg1 string, arg2 bool, arg3 []interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTAlter", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTAlter indicates an expected call of FTAlter.
func (mr *MockPipelinerMockRecorder) FTAlter(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTAlter", reflect.TypeOf((*MockPipeliner)(nil).FTAlter), arg0, arg1, arg2, arg3)
}

// FTConfigGet mocks base method.
func (m *MockPipeliner) FTConfigGet(arg0 context.Context, arg1 string) *redis.MapMapStringInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTConfigGet", arg0, arg1)
	ret0, _ := ret[0].(*redis.MapMapStringInterfaceCmd)
	return ret0
}

// FTConfigGet indicates an expected call of FTConfigGet.
func (mr *MockPipelinerMockRecorder) FTConfigGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTConfigGet", reflect.TypeOf((*MockPipeliner)(nil).FTConfigGet), arg0, arg1)
}

// FTConfigSet mocks base method.
func (m *MockPipeliner) FTConfigSet(arg0 context.Context, arg1 string, arg2 interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTConfigSet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTConfigSet indicates an expected call of FTConfigSet.
func (mr *MockPipelinerMockRecorder) FTConfigSet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTConfigSet", reflect.TypeOf((*MockPipeliner)(nil).FTConfigSet), arg0, arg1, arg2)
}

// FTCreate mocks base method.
func (m *MockPipeliner) FTCreate(arg0 context.Context, arg1 string, arg2 *redis.FTCreateOptions, arg3 ...*redis.FieldSchema) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FTCreate", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTCreate indicates an expected call of FTCreate.
func (mr *MockPipelinerMockRecorder) FTCreate(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTCreate", reflect.TypeOf((*MockPipeliner)(nil).FTCreate), varargs...)
}

// FTCursorDel mocks base method.
func (m *MockPipeliner) FTCursorDel(arg0 context.Context, arg1 string, arg2 int) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTCursorDel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTCursorDel indicates an expected call of FTCursorDel.
func (mr *MockPipelinerMockRecorder) FTCursorDel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTCursorDel", reflect.TypeOf((*MockPipeliner)(nil).FTCursorDel), arg0, arg1, arg2)
}

// FTCursorRead mocks base method.
func (m *MockPipeliner) FTCursorRead(arg0 context.Context, arg1 string, arg2, arg3 int) *redis.MapStringInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTCursorRead", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.MapStringInterfaceCmd)
	return ret0
}

// FTCursorRead indicates an expected call of FTCursorRead.
func (mr *MockPipelinerMockRecorder) FTCursorRead(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTCursorRead", reflect.TypeOf((*MockPipeliner)(nil).FTCursorRead), arg0, arg1, arg2, arg3)
}

// FTDictAdd mocks base method.
func (m *MockPipeliner) FTDictAdd(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FTDictAdd", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// FTDictAdd indicates an expected call of FTDictAdd.
func (mr *MockPipelinerMockRecorder) FTDictAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTDictAdd", reflect.TypeOf((*MockPipeliner)(nil).FTDictAdd), varargs...)
}

// FTDictDel mocks base method.
func (m *MockPipeliner) FTDictDel(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FTDictDel", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// FTDictDel indicates an expected call of FTDictDel.
func (mr *MockPipelinerMockRecorder) FTDictDel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTDictDel", reflect.TypeOf((*MockPipeliner)(nil).FTDictDel), varargs...)
}

// FTDictDump mocks base method.
func (m *MockPipeliner) FTDictDump(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTDictDump", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// FTDictDump indicates an expected call of FTDictDump.
func (mr *MockPipelinerMockRecorder) FTDictDump(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTDictDump", reflect.TypeOf((*MockPipeliner)(nil).FTDictDump), arg0, arg1)
}

// FTDropIndex mocks base method.
func (m *MockPipeliner) FTDropIndex(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTDropIndex", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTDropIndex indicates an expected call of FTDropIndex.
func (mr *MockPipelinerMockRecorder) FTDropIndex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTDropIndex", reflect.TypeOf((*MockPipeliner)(nil).FTDropIndex), arg0, arg1)
}

// FTDropIndexWithArgs mocks base method.
func (m *MockPipeliner) FTDropIndexWithArgs(arg0 context.Context, arg1 string, arg2 *redis.FTDropIndexOptions) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTDropIndexWithArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTDropIndexWithArgs indicates an expected call of FTDropIndexWithArgs.
func (mr *MockPipelinerMockRecorder) FTDropIndexWithArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTDropIndexWithArgs", reflect.TypeOf((*MockPipeliner)(nil).FTDropIndexWithArgs), arg0, arg1, arg2)
}

// FTExplain mocks base method.
func (m *MockPipeliner) FTExplain(arg0 context.Context, arg1, arg2 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTExplain", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FTExplain indicates an expected call of FTExplain.
func (mr *MockPipelinerMockRecorder) FTExplain(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTExplain", reflect.TypeOf((*MockPipeliner)(nil).FTExplain), arg0, arg1, arg2)
}

// FTExplainWithArgs mocks base method.
func (m *MockPipeliner) FTExplainWithArgs(arg0 context.Context, arg1, arg2 string, arg3 *redis.FTExplainOptions) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTExplainWithArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FTExplainWithArgs indicates an expected call of FTExplainWithArgs.
func (mr *MockPipelinerMockRecorder) FTExplainWithArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTExplainWithArgs", reflect.TypeOf((*MockPipeliner)(nil).FTExplainWithArgs), arg0, arg1, arg2, arg3)
}

// FTInfo mocks base method.
func (m *MockPipeliner) FTInfo(arg0 context.Context, arg1 string) *redis.FTInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTInfo", arg0, arg1)
	ret0, _ := ret[0].(*redis.FTInfoCmd)
	return ret0
}

// FTInfo indicates an expected call of FTInfo.
func (mr *MockPipelinerMockRecorder) FTInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTInfo", reflect.TypeOf((*MockPipeliner)(nil).FTInfo), arg0, arg1)
}

// FTSearch mocks base method.
func (m *MockPipeliner) FTSearch(arg0 context.Context, arg1, arg2 string) *redis.FTSearchCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTSearch", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.FTSearchCmd)
	return ret0
}

// FTSearch indicates an expected call of FTSearch.
func (mr *MockPipelinerMockRecorder) FTSearch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTSearch", reflect.TypeOf((*MockPipeliner)(nil).FTSearch), arg0, arg1, arg2)
}

// FTSearchWithArgs mocks base method.
func (m *MockPipeliner) FTSearchWithArgs(arg0 context.Context, arg1, arg2 string, arg3 *redis.FTSearchOptions) *redis.FTSearchCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTSearchWithArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.FTSearchCmd)
	return ret0
}

// FTSearchWithArgs indicates an expected call of FTSearchWithArgs.
func (mr *MockPipelinerMockRecorder) FTSearchWithArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTSearchWithArgs", reflect.TypeOf((*MockPipeliner)(nil).FTSearchWithArgs), arg0, arg1, arg2, arg3)
}

// FTSpellCheck mocks base method.
func (m *MockPipeliner) FTSpellCheck(arg0 context.Context, arg1, arg2 string) *redis.FTSpellCheckCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTSpellCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.FTSpellCheckCmd)
	return ret0
}

// FTSpellCheck indicates an expected call of FTSpellCheck.
func (mr *MockPipelinerMockRecorder) FTSpellCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTSpellCheck", reflect.TypeOf((*MockPipeliner)(nil).FTSpellCheck), arg0, arg1, arg2)
}

// FTSpellCheckWithArgs mocks base method.
func (m *MockPipeliner) FTSpellCheckWithArgs(arg0 context.Context, arg1, arg2 string, arg3 *redis.FTSpellCheckOptions) *redis.FTSpellCheckCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTSpellCheckWithArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.FTSpellCheckCmd)
	return ret0
}

// FTSpellCheckWithArgs indicates an expected call of FTSpellCheckWithArgs.
func (mr *MockPipelinerMockRecorder) FTSpellCheckWithArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTSpellCheckWithArgs", reflect.TypeOf((*MockPipeliner)(nil).FTSpellCheckWithArgs), arg0, arg1, arg2, arg3)
}

// FTSynDump mocks base method.
func (m *MockPipeliner) FTSynDump(arg0 context.Context, arg1 string) *redis.FTSynDumpCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTSynDump", arg0, arg1)
	ret0, _ := ret[0].(*redis.FTSynDumpCmd)
	return ret0
}

// FTSynDump indicates an expected call of FTSynDump.
func (mr *MockPipelinerMockRecorder) FTSynDump(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTSynDump", reflect.TypeOf((*MockPipeliner)(nil).FTSynDump), arg0, arg1)
}

// FTSynUpdate mocks base method.
func (m *MockPipeliner) FTSynUpdate(arg0 context.Context, arg1 string, arg2 interface{}, arg3 []interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTSynUpdate", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTSynUpdate indicates an expected call of FTSynUpdate.
func (mr *MockPipelinerMockRecorder) FTSynUpdate(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTSynUpdate", reflect.TypeOf((*MockPipeliner)(nil).FTSynUpdate), arg0, arg1, arg2, arg3)
}

// FTSynUpdateWithArgs mocks base method.
func (m *MockPipeliner) FTSynUpdateWithArgs(arg0 context.Context, arg1 string, arg2 interface{}, arg3 *redis.FTSynUpdateOptions, arg4 []interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTSynUpdateWithArgs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FTSynUpdateWithArgs indicates an expected call of FTSynUpdateWithArgs.
func (mr *MockPipelinerMockRecorder) FTSynUpdateWithArgs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTSynUpdateWithArgs", reflect.TypeOf((*MockPipeliner)(nil).FTSynUpdateWithArgs), arg0, arg1, arg2, arg3, arg4)
}

// FTTagVals mocks base method.
func (m *MockPipeliner) FTTagVals(arg0 context.Context, arg1, arg2 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FTTagVals", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// FTTagVals indicates an expected call of FTTagVals.
func (mr *MockPipelinerMockRecorder) FTTagVals(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FTTagVals", reflect.TypeOf((*MockPipeliner)(nil).FTTagVals), arg0, arg1, arg2)
}

// FT_List mocks base method.
func (m *MockPipeliner) FT_List(arg0 context.Context) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FT_List", arg0)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// FT_List indicates an expected call of FT_List.
func (mr *MockPipelinerMockRecorder) FT_List(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FT_List", reflect.TypeOf((*MockPipeliner)(nil).FT_List), arg0)
}

// FlushAll mocks base method.
func (m *MockPipeliner) FlushAll(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlushAll", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FlushAll indicates an expected call of FlushAll.
func (mr *MockPipelinerMockRecorder) FlushAll(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlushAll", reflect.TypeOf((*MockPipeliner)(nil).FlushAll), arg0)
}

// FlushAllAsync mocks base method.
func (m *MockPipeliner) FlushAllAsync(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlushAllAsync", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FlushAllAsync indicates an expected call of FlushAllAsync.
func (mr *MockPipelinerMockRecorder) FlushAllAsync(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlushAllAsync", reflect.TypeOf((*MockPipeliner)(nil).FlushAllAsync), arg0)
}

// FlushDB mocks base method.
func (m *MockPipeliner) FlushDB(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlushDB", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FlushDB indicates an expected call of FlushDB.
func (mr *MockPipelinerMockRecorder) FlushDB(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlushDB", reflect.TypeOf((*MockPipeliner)(nil).FlushDB), arg0)
}

// FlushDBAsync mocks base method.
func (m *MockPipeliner) FlushDBAsync(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlushDBAsync", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// FlushDBAsync indicates an expected call of FlushDBAsync.
func (mr *MockPipelinerMockRecorder) FlushDBAsync(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlushDBAsync", reflect.TypeOf((*MockPipeliner)(nil).FlushDBAsync), arg0)
}

// FunctionDelete mocks base method.
func (m *MockPipeliner) FunctionDelete(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionDelete", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FunctionDelete indicates an expected call of FunctionDelete.
func (mr *MockPipelinerMockRecorder) FunctionDelete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionDelete", reflect.TypeOf((*MockPipeliner)(nil).FunctionDelete), arg0, arg1)
}

// FunctionDump mocks base method.
func (m *MockPipeliner) FunctionDump(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionDump", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FunctionDump indicates an expected call of FunctionDump.
func (mr *MockPipelinerMockRecorder) FunctionDump(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionDump", reflect.TypeOf((*MockPipeliner)(nil).FunctionDump), arg0)
}

// FunctionFlush mocks base method.
func (m *MockPipeliner) FunctionFlush(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionFlush", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FunctionFlush indicates an expected call of FunctionFlush.
func (mr *MockPipelinerMockRecorder) FunctionFlush(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionFlush", reflect.TypeOf((*MockPipeliner)(nil).FunctionFlush), arg0)
}

// FunctionFlushAsync mocks base method.
func (m *MockPipeliner) FunctionFlushAsync(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionFlushAsync", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FunctionFlushAsync indicates an expected call of FunctionFlushAsync.
func (mr *MockPipelinerMockRecorder) FunctionFlushAsync(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionFlushAsync", reflect.TypeOf((*MockPipeliner)(nil).FunctionFlushAsync), arg0)
}

// FunctionKill mocks base method.
func (m *MockPipeliner) FunctionKill(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionKill", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FunctionKill indicates an expected call of FunctionKill.
func (mr *MockPipelinerMockRecorder) FunctionKill(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionKill", reflect.TypeOf((*MockPipeliner)(nil).FunctionKill), arg0)
}

// FunctionList mocks base method.
func (m *MockPipeliner) FunctionList(arg0 context.Context, arg1 redis.FunctionListQuery) *redis.FunctionListCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionList", arg0, arg1)
	ret0, _ := ret[0].(*redis.FunctionListCmd)
	return ret0
}

// FunctionList indicates an expected call of FunctionList.
func (mr *MockPipelinerMockRecorder) FunctionList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionList", reflect.TypeOf((*MockPipeliner)(nil).FunctionList), arg0, arg1)
}

// FunctionLoad mocks base method.
func (m *MockPipeliner) FunctionLoad(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionLoad", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FunctionLoad indicates an expected call of FunctionLoad.
func (mr *MockPipelinerMockRecorder) FunctionLoad(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionLoad", reflect.TypeOf((*MockPipeliner)(nil).FunctionLoad), arg0, arg1)
}

// FunctionLoadReplace mocks base method.
func (m *MockPipeliner) FunctionLoadReplace(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionLoadReplace", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FunctionLoadReplace indicates an expected call of FunctionLoadReplace.
func (mr *MockPipelinerMockRecorder) FunctionLoadReplace(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionLoadReplace", reflect.TypeOf((*MockPipeliner)(nil).FunctionLoadReplace), arg0, arg1)
}

// FunctionRestore mocks base method.
func (m *MockPipeliner) FunctionRestore(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionRestore", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// FunctionRestore indicates an expected call of FunctionRestore.
func (mr *MockPipelinerMockRecorder) FunctionRestore(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionRestore", reflect.TypeOf((*MockPipeliner)(nil).FunctionRestore), arg0, arg1)
}

// FunctionStats mocks base method.
func (m *MockPipeliner) FunctionStats(arg0 context.Context) *redis.FunctionStatsCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FunctionStats", arg0)
	ret0, _ := ret[0].(*redis.FunctionStatsCmd)
	return ret0
}

// FunctionStats indicates an expected call of FunctionStats.
func (mr *MockPipelinerMockRecorder) FunctionStats(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FunctionStats", reflect.TypeOf((*MockPipeliner)(nil).FunctionStats), arg0)
}

// GeoAdd mocks base method.
func (m *MockPipeliner) GeoAdd(arg0 context.Context, arg1 string, arg2 ...*redis.GeoLocation) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeoAdd", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// GeoAdd indicates an expected call of GeoAdd.
func (mr *MockPipelinerMockRecorder) GeoAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoAdd", reflect.TypeOf((*MockPipeliner)(nil).GeoAdd), varargs...)
}

// GeoDist mocks base method.
func (m *MockPipeliner) GeoDist(arg0 context.Context, arg1, arg2, arg3, arg4 string) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeoDist", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// GeoDist indicates an expected call of GeoDist.
func (mr *MockPipelinerMockRecorder) GeoDist(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoDist", reflect.TypeOf((*MockPipeliner)(nil).GeoDist), arg0, arg1, arg2, arg3, arg4)
}

// GeoHash mocks base method.
func (m *MockPipeliner) GeoHash(arg0 context.Context, arg1 string, arg2 ...string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeoHash", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// GeoHash indicates an expected call of GeoHash.
func (mr *MockPipelinerMockRecorder) GeoHash(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoHash", reflect.TypeOf((*MockPipeliner)(nil).GeoHash), varargs...)
}

// GeoPos mocks base method.
func (m *MockPipeliner) GeoPos(arg0 context.Context, arg1 string, arg2 ...string) *redis.GeoPosCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GeoPos", varargs...)
	ret0, _ := ret[0].(*redis.GeoPosCmd)
	return ret0
}

// GeoPos indicates an expected call of GeoPos.
func (mr *MockPipelinerMockRecorder) GeoPos(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoPos", reflect.TypeOf((*MockPipeliner)(nil).GeoPos), varargs...)
}

// GeoRadius mocks base method.
func (m *MockPipeliner) GeoRadius(arg0 context.Context, arg1 string, arg2, arg3 float64, arg4 *redis.GeoRadiusQuery) *redis.GeoLocationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeoRadius", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.GeoLocationCmd)
	return ret0
}

// GeoRadius indicates an expected call of GeoRadius.
func (mr *MockPipelinerMockRecorder) GeoRadius(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoRadius", reflect.TypeOf((*MockPipeliner)(nil).GeoRadius), arg0, arg1, arg2, arg3, arg4)
}

// GeoRadiusByMember mocks base method.
func (m *MockPipeliner) GeoRadiusByMember(arg0 context.Context, arg1, arg2 string, arg3 *redis.GeoRadiusQuery) *redis.GeoLocationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeoRadiusByMember", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.GeoLocationCmd)
	return ret0
}

// GeoRadiusByMember indicates an expected call of GeoRadiusByMember.
func (mr *MockPipelinerMockRecorder) GeoRadiusByMember(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoRadiusByMember", reflect.TypeOf((*MockPipeliner)(nil).GeoRadiusByMember), arg0, arg1, arg2, arg3)
}

// GeoRadiusByMemberStore mocks base method.
func (m *MockPipeliner) GeoRadiusByMemberStore(arg0 context.Context, arg1, arg2 string, arg3 *redis.GeoRadiusQuery) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeoRadiusByMemberStore", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// GeoRadiusByMemberStore indicates an expected call of GeoRadiusByMemberStore.
func (mr *MockPipelinerMockRecorder) GeoRadiusByMemberStore(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoRadiusByMemberStore", reflect.TypeOf((*MockPipeliner)(nil).GeoRadiusByMemberStore), arg0, arg1, arg2, arg3)
}

// GeoRadiusStore mocks base method.
func (m *MockPipeliner) GeoRadiusStore(arg0 context.Context, arg1 string, arg2, arg3 float64, arg4 *redis.GeoRadiusQuery) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeoRadiusStore", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// GeoRadiusStore indicates an expected call of GeoRadiusStore.
func (mr *MockPipelinerMockRecorder) GeoRadiusStore(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoRadiusStore", reflect.TypeOf((*MockPipeliner)(nil).GeoRadiusStore), arg0, arg1, arg2, arg3, arg4)
}

// GeoSearch mocks base method.
func (m *MockPipeliner) GeoSearch(arg0 context.Context, arg1 string, arg2 *redis.GeoSearchQuery) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeoSearch", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// GeoSearch indicates an expected call of GeoSearch.
func (mr *MockPipelinerMockRecorder) GeoSearch(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoSearch", reflect.TypeOf((*MockPipeliner)(nil).GeoSearch), arg0, arg1, arg2)
}

// GeoSearchLocation mocks base method.
func (m *MockPipeliner) GeoSearchLocation(arg0 context.Context, arg1 string, arg2 *redis.GeoSearchLocationQuery) *redis.GeoSearchLocationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeoSearchLocation", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.GeoSearchLocationCmd)
	return ret0
}

// GeoSearchLocation indicates an expected call of GeoSearchLocation.
func (mr *MockPipelinerMockRecorder) GeoSearchLocation(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoSearchLocation", reflect.TypeOf((*MockPipeliner)(nil).GeoSearchLocation), arg0, arg1, arg2)
}

// GeoSearchStore mocks base method.
func (m *MockPipeliner) GeoSearchStore(arg0 context.Context, arg1, arg2 string, arg3 *redis.GeoSearchStoreQuery) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GeoSearchStore", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// GeoSearchStore indicates an expected call of GeoSearchStore.
func (mr *MockPipelinerMockRecorder) GeoSearchStore(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GeoSearchStore", reflect.TypeOf((*MockPipeliner)(nil).GeoSearchStore), arg0, arg1, arg2, arg3)
}

// Get mocks base method.
func (m *MockPipeliner) Get(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockPipelinerMockRecorder) Get(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockPipeliner)(nil).Get), arg0, arg1)
}

// GetBit mocks base method.
func (m *MockPipeliner) GetBit(arg0 context.Context, arg1 string, arg2 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBit", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// GetBit indicates an expected call of GetBit.
func (mr *MockPipelinerMockRecorder) GetBit(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBit", reflect.TypeOf((*MockPipeliner)(nil).GetBit), arg0, arg1, arg2)
}

// GetDel mocks base method.
func (m *MockPipeliner) GetDel(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDel", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// GetDel indicates an expected call of GetDel.
func (mr *MockPipelinerMockRecorder) GetDel(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDel", reflect.TypeOf((*MockPipeliner)(nil).GetDel), arg0, arg1)
}

// GetEx mocks base method.
func (m *MockPipeliner) GetEx(arg0 context.Context, arg1 string, arg2 time.Duration) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEx", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// GetEx indicates an expected call of GetEx.
func (mr *MockPipelinerMockRecorder) GetEx(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEx", reflect.TypeOf((*MockPipeliner)(nil).GetEx), arg0, arg1, arg2)
}

// GetRange mocks base method.
func (m *MockPipeliner) GetRange(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// GetRange indicates an expected call of GetRange.
func (mr *MockPipelinerMockRecorder) GetRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRange", reflect.TypeOf((*MockPipeliner)(nil).GetRange), arg0, arg1, arg2, arg3)
}

// GetSet mocks base method.
func (m *MockPipeliner) GetSet(arg0 context.Context, arg1 string, arg2 interface{}) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// GetSet indicates an expected call of GetSet.
func (mr *MockPipelinerMockRecorder) GetSet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSet", reflect.TypeOf((*MockPipeliner)(nil).GetSet), arg0, arg1, arg2)
}

// HDel mocks base method.
func (m *MockPipeliner) HDel(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HDel", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// HDel indicates an expected call of HDel.
func (mr *MockPipelinerMockRecorder) HDel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HDel", reflect.TypeOf((*MockPipeliner)(nil).HDel), varargs...)
}

// HExists mocks base method.
func (m *MockPipeliner) HExists(arg0 context.Context, arg1, arg2 string) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HExists", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// HExists indicates an expected call of HExists.
func (mr *MockPipelinerMockRecorder) HExists(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HExists", reflect.TypeOf((*MockPipeliner)(nil).HExists), arg0, arg1, arg2)
}

// HExpire mocks base method.
func (m *MockPipeliner) HExpire(arg0 context.Context, arg1 string, arg2 time.Duration, arg3 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HExpire", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HExpire indicates an expected call of HExpire.
func (mr *MockPipelinerMockRecorder) HExpire(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HExpire", reflect.TypeOf((*MockPipeliner)(nil).HExpire), varargs...)
}

// HExpireAt mocks base method.
func (m *MockPipeliner) HExpireAt(arg0 context.Context, arg1 string, arg2 time.Time, arg3 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HExpireAt", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HExpireAt indicates an expected call of HExpireAt.
func (mr *MockPipelinerMockRecorder) HExpireAt(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HExpireAt", reflect.TypeOf((*MockPipeliner)(nil).HExpireAt), varargs...)
}

// HExpireAtWithArgs mocks base method.
func (m *MockPipeliner) HExpireAtWithArgs(arg0 context.Context, arg1 string, arg2 time.Time, arg3 redis.HExpireArgs, arg4 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HExpireAtWithArgs", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HExpireAtWithArgs indicates an expected call of HExpireAtWithArgs.
func (mr *MockPipelinerMockRecorder) HExpireAtWithArgs(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HExpireAtWithArgs", reflect.TypeOf((*MockPipeliner)(nil).HExpireAtWithArgs), varargs...)
}

// HExpireTime mocks base method.
func (m *MockPipeliner) HExpireTime(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HExpireTime", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HExpireTime indicates an expected call of HExpireTime.
func (mr *MockPipelinerMockRecorder) HExpireTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HExpireTime", reflect.TypeOf((*MockPipeliner)(nil).HExpireTime), varargs...)
}

// HExpireWithArgs mocks base method.
func (m *MockPipeliner) HExpireWithArgs(arg0 context.Context, arg1 string, arg2 time.Duration, arg3 redis.HExpireArgs, arg4 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HExpireWithArgs", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HExpireWithArgs indicates an expected call of HExpireWithArgs.
func (mr *MockPipelinerMockRecorder) HExpireWithArgs(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HExpireWithArgs", reflect.TypeOf((*MockPipeliner)(nil).HExpireWithArgs), varargs...)
}

// HGet mocks base method.
func (m *MockPipeliner) HGet(arg0 context.Context, arg1, arg2 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HGet", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// HGet indicates an expected call of HGet.
func (mr *MockPipelinerMockRecorder) HGet(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HGet", reflect.TypeOf((*MockPipeliner)(nil).HGet), arg0, arg1, arg2)
}

// HGetAll mocks base method.
func (m *MockPipeliner) HGetAll(arg0 context.Context, arg1 string) *redis.MapStringStringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HGetAll", arg0, arg1)
	ret0, _ := ret[0].(*redis.MapStringStringCmd)
	return ret0
}

// HGetAll indicates an expected call of HGetAll.
func (mr *MockPipelinerMockRecorder) HGetAll(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HGetAll", reflect.TypeOf((*MockPipeliner)(nil).HGetAll), arg0, arg1)
}

// HIncrBy mocks base method.
func (m *MockPipeliner) HIncrBy(arg0 context.Context, arg1, arg2 string, arg3 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HIncrBy", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// HIncrBy indicates an expected call of HIncrBy.
func (mr *MockPipelinerMockRecorder) HIncrBy(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HIncrBy", reflect.TypeOf((*MockPipeliner)(nil).HIncrBy), arg0, arg1, arg2, arg3)
}

// HIncrByFloat mocks base method.
func (m *MockPipeliner) HIncrByFloat(arg0 context.Context, arg1, arg2 string, arg3 float64) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HIncrByFloat", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// HIncrByFloat indicates an expected call of HIncrByFloat.
func (mr *MockPipelinerMockRecorder) HIncrByFloat(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HIncrByFloat", reflect.TypeOf((*MockPipeliner)(nil).HIncrByFloat), arg0, arg1, arg2, arg3)
}

// HKeys mocks base method.
func (m *MockPipeliner) HKeys(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HKeys", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// HKeys indicates an expected call of HKeys.
func (mr *MockPipelinerMockRecorder) HKeys(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HKeys", reflect.TypeOf((*MockPipeliner)(nil).HKeys), arg0, arg1)
}

// HLen mocks base method.
func (m *MockPipeliner) HLen(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HLen", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// HLen indicates an expected call of HLen.
func (mr *MockPipelinerMockRecorder) HLen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HLen", reflect.TypeOf((*MockPipeliner)(nil).HLen), arg0, arg1)
}

// HMGet mocks base method.
func (m *MockPipeliner) HMGet(arg0 context.Context, arg1 string, arg2 ...string) *redis.SliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HMGet", varargs...)
	ret0, _ := ret[0].(*redis.SliceCmd)
	return ret0
}

// HMGet indicates an expected call of HMGet.
func (mr *MockPipelinerMockRecorder) HMGet(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HMGet", reflect.TypeOf((*MockPipeliner)(nil).HMGet), varargs...)
}

// HMSet mocks base method.
func (m *MockPipeliner) HMSet(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HMSet", varargs...)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// HMSet indicates an expected call of HMSet.
func (mr *MockPipelinerMockRecorder) HMSet(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HMSet", reflect.TypeOf((*MockPipeliner)(nil).HMSet), varargs...)
}

// HPExpire mocks base method.
func (m *MockPipeliner) HPExpire(arg0 context.Context, arg1 string, arg2 time.Duration, arg3 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HPExpire", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HPExpire indicates an expected call of HPExpire.
func (mr *MockPipelinerMockRecorder) HPExpire(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HPExpire", reflect.TypeOf((*MockPipeliner)(nil).HPExpire), varargs...)
}

// HPExpireAt mocks base method.
func (m *MockPipeliner) HPExpireAt(arg0 context.Context, arg1 string, arg2 time.Time, arg3 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HPExpireAt", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HPExpireAt indicates an expected call of HPExpireAt.
func (mr *MockPipelinerMockRecorder) HPExpireAt(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HPExpireAt", reflect.TypeOf((*MockPipeliner)(nil).HPExpireAt), varargs...)
}

// HPExpireAtWithArgs mocks base method.
func (m *MockPipeliner) HPExpireAtWithArgs(arg0 context.Context, arg1 string, arg2 time.Time, arg3 redis.HExpireArgs, arg4 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HPExpireAtWithArgs", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HPExpireAtWithArgs indicates an expected call of HPExpireAtWithArgs.
func (mr *MockPipelinerMockRecorder) HPExpireAtWithArgs(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HPExpireAtWithArgs", reflect.TypeOf((*MockPipeliner)(nil).HPExpireAtWithArgs), varargs...)
}

// HPExpireTime mocks base method.
func (m *MockPipeliner) HPExpireTime(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HPExpireTime", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HPExpireTime indicates an expected call of HPExpireTime.
func (mr *MockPipelinerMockRecorder) HPExpireTime(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HPExpireTime", reflect.TypeOf((*MockPipeliner)(nil).HPExpireTime), varargs...)
}

// HPExpireWithArgs mocks base method.
func (m *MockPipeliner) HPExpireWithArgs(arg0 context.Context, arg1 string, arg2 time.Duration, arg3 redis.HExpireArgs, arg4 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HPExpireWithArgs", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HPExpireWithArgs indicates an expected call of HPExpireWithArgs.
func (mr *MockPipelinerMockRecorder) HPExpireWithArgs(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HPExpireWithArgs", reflect.TypeOf((*MockPipeliner)(nil).HPExpireWithArgs), varargs...)
}

// HPTTL mocks base method.
func (m *MockPipeliner) HPTTL(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HPTTL", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HPTTL indicates an expected call of HPTTL.
func (mr *MockPipelinerMockRecorder) HPTTL(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HPTTL", reflect.TypeOf((*MockPipeliner)(nil).HPTTL), varargs...)
}

// HPersist mocks base method.
func (m *MockPipeliner) HPersist(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HPersist", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HPersist indicates an expected call of HPersist.
func (mr *MockPipelinerMockRecorder) HPersist(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HPersist", reflect.TypeOf((*MockPipeliner)(nil).HPersist), varargs...)
}

// HRandField mocks base method.
func (m *MockPipeliner) HRandField(arg0 context.Context, arg1 string, arg2 int) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HRandField", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// HRandField indicates an expected call of HRandField.
func (mr *MockPipelinerMockRecorder) HRandField(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HRandField", reflect.TypeOf((*MockPipeliner)(nil).HRandField), arg0, arg1, arg2)
}

// HRandFieldWithValues mocks base method.
func (m *MockPipeliner) HRandFieldWithValues(arg0 context.Context, arg1 string, arg2 int) *redis.KeyValueSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HRandFieldWithValues", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.KeyValueSliceCmd)
	return ret0
}

// HRandFieldWithValues indicates an expected call of HRandFieldWithValues.
func (mr *MockPipelinerMockRecorder) HRandFieldWithValues(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HRandFieldWithValues", reflect.TypeOf((*MockPipeliner)(nil).HRandFieldWithValues), arg0, arg1, arg2)
}

// HScan mocks base method.
func (m *MockPipeliner) HScan(arg0 context.Context, arg1 string, arg2 uint64, arg3 string, arg4 int64) *redis.ScanCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HScan", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.ScanCmd)
	return ret0
}

// HScan indicates an expected call of HScan.
func (mr *MockPipelinerMockRecorder) HScan(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HScan", reflect.TypeOf((*MockPipeliner)(nil).HScan), arg0, arg1, arg2, arg3, arg4)
}

// HScanNoValues mocks base method.
func (m *MockPipeliner) HScanNoValues(arg0 context.Context, arg1 string, arg2 uint64, arg3 string, arg4 int64) *redis.ScanCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HScanNoValues", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.ScanCmd)
	return ret0
}

// HScanNoValues indicates an expected call of HScanNoValues.
func (mr *MockPipelinerMockRecorder) HScanNoValues(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HScanNoValues", reflect.TypeOf((*MockPipeliner)(nil).HScanNoValues), arg0, arg1, arg2, arg3, arg4)
}

// HSet mocks base method.
func (m *MockPipeliner) HSet(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HSet", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// HSet indicates an expected call of HSet.
func (mr *MockPipelinerMockRecorder) HSet(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HSet", reflect.TypeOf((*MockPipeliner)(nil).HSet), varargs...)
}

// HSetNX mocks base method.
func (m *MockPipeliner) HSetNX(arg0 context.Context, arg1, arg2 string, arg3 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HSetNX", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// HSetNX indicates an expected call of HSetNX.
func (mr *MockPipelinerMockRecorder) HSetNX(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HSetNX", reflect.TypeOf((*MockPipeliner)(nil).HSetNX), arg0, arg1, arg2, arg3)
}

// HTTL mocks base method.
func (m *MockPipeliner) HTTL(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "HTTL", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// HTTL indicates an expected call of HTTL.
func (mr *MockPipelinerMockRecorder) HTTL(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HTTL", reflect.TypeOf((*MockPipeliner)(nil).HTTL), varargs...)
}

// HVals mocks base method.
func (m *MockPipeliner) HVals(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HVals", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// HVals indicates an expected call of HVals.
func (mr *MockPipelinerMockRecorder) HVals(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HVals", reflect.TypeOf((*MockPipeliner)(nil).HVals), arg0, arg1)
}

// Hello mocks base method.
func (m *MockPipeliner) Hello(arg0 context.Context, arg1 int, arg2, arg3, arg4 string) *redis.MapStringInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Hello", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.MapStringInterfaceCmd)
	return ret0
}

// Hello indicates an expected call of Hello.
func (mr *MockPipelinerMockRecorder) Hello(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Hello", reflect.TypeOf((*MockPipeliner)(nil).Hello), arg0, arg1, arg2, arg3, arg4)
}

// Incr mocks base method.
func (m *MockPipeliner) Incr(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Incr", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Incr indicates an expected call of Incr.
func (mr *MockPipelinerMockRecorder) Incr(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Incr", reflect.TypeOf((*MockPipeliner)(nil).Incr), arg0, arg1)
}

// IncrBy mocks base method.
func (m *MockPipeliner) IncrBy(arg0 context.Context, arg1 string, arg2 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrBy", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// IncrBy indicates an expected call of IncrBy.
func (mr *MockPipelinerMockRecorder) IncrBy(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrBy", reflect.TypeOf((*MockPipeliner)(nil).IncrBy), arg0, arg1, arg2)
}

// IncrByFloat mocks base method.
func (m *MockPipeliner) IncrByFloat(arg0 context.Context, arg1 string, arg2 float64) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrByFloat", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// IncrByFloat indicates an expected call of IncrByFloat.
func (mr *MockPipelinerMockRecorder) IncrByFloat(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrByFloat", reflect.TypeOf((*MockPipeliner)(nil).IncrByFloat), arg0, arg1, arg2)
}

// Info mocks base method.
func (m *MockPipeliner) Info(arg0 context.Context, arg1 ...string) *redis.StringCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Info", varargs...)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// Info indicates an expected call of Info.
func (mr *MockPipelinerMockRecorder) Info(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Info", reflect.TypeOf((*MockPipeliner)(nil).Info), varargs...)
}

// JSONArrAppend mocks base method.
func (m *MockPipeliner) JSONArrAppend(arg0 context.Context, arg1, arg2 string, arg3 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JSONArrAppend", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// JSONArrAppend indicates an expected call of JSONArrAppend.
func (mr *MockPipelinerMockRecorder) JSONArrAppend(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONArrAppend", reflect.TypeOf((*MockPipeliner)(nil).JSONArrAppend), varargs...)
}

// JSONArrIndex mocks base method.
func (m *MockPipeliner) JSONArrIndex(arg0 context.Context, arg1, arg2 string, arg3 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JSONArrIndex", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// JSONArrIndex indicates an expected call of JSONArrIndex.
func (mr *MockPipelinerMockRecorder) JSONArrIndex(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONArrIndex", reflect.TypeOf((*MockPipeliner)(nil).JSONArrIndex), varargs...)
}

// JSONArrIndexWithArgs mocks base method.
func (m *MockPipeliner) JSONArrIndexWithArgs(arg0 context.Context, arg1, arg2 string, arg3 *redis.JSONArrIndexArgs, arg4 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JSONArrIndexWithArgs", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// JSONArrIndexWithArgs indicates an expected call of JSONArrIndexWithArgs.
func (mr *MockPipelinerMockRecorder) JSONArrIndexWithArgs(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONArrIndexWithArgs", reflect.TypeOf((*MockPipeliner)(nil).JSONArrIndexWithArgs), varargs...)
}

// JSONArrInsert mocks base method.
func (m *MockPipeliner) JSONArrInsert(arg0 context.Context, arg1, arg2 string, arg3 int64, arg4 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2, arg3}
	for _, a := range arg4 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JSONArrInsert", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// JSONArrInsert indicates an expected call of JSONArrInsert.
func (mr *MockPipelinerMockRecorder) JSONArrInsert(arg0, arg1, arg2, arg3 interface{}, arg4 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2, arg3}, arg4...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONArrInsert", reflect.TypeOf((*MockPipeliner)(nil).JSONArrInsert), varargs...)
}

// JSONArrLen mocks base method.
func (m *MockPipeliner) JSONArrLen(arg0 context.Context, arg1, arg2 string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONArrLen", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// JSONArrLen indicates an expected call of JSONArrLen.
func (mr *MockPipelinerMockRecorder) JSONArrLen(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONArrLen", reflect.TypeOf((*MockPipeliner)(nil).JSONArrLen), arg0, arg1, arg2)
}

// JSONArrPop mocks base method.
func (m *MockPipeliner) JSONArrPop(arg0 context.Context, arg1, arg2 string, arg3 int) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONArrPop", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// JSONArrPop indicates an expected call of JSONArrPop.
func (mr *MockPipelinerMockRecorder) JSONArrPop(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONArrPop", reflect.TypeOf((*MockPipeliner)(nil).JSONArrPop), arg0, arg1, arg2, arg3)
}

// JSONArrTrim mocks base method.
func (m *MockPipeliner) JSONArrTrim(arg0 context.Context, arg1, arg2 string) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONArrTrim", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// JSONArrTrim indicates an expected call of JSONArrTrim.
func (mr *MockPipelinerMockRecorder) JSONArrTrim(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONArrTrim", reflect.TypeOf((*MockPipeliner)(nil).JSONArrTrim), arg0, arg1, arg2)
}

// JSONArrTrimWithArgs mocks base method.
func (m *MockPipeliner) JSONArrTrimWithArgs(arg0 context.Context, arg1, arg2 string, arg3 *redis.JSONArrTrimArgs) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONArrTrimWithArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// JSONArrTrimWithArgs indicates an expected call of JSONArrTrimWithArgs.
func (mr *MockPipelinerMockRecorder) JSONArrTrimWithArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONArrTrimWithArgs", reflect.TypeOf((*MockPipeliner)(nil).JSONArrTrimWithArgs), arg0, arg1, arg2, arg3)
}

// JSONClear mocks base method.
func (m *MockPipeliner) JSONClear(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONClear", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// JSONClear indicates an expected call of JSONClear.
func (mr *MockPipelinerMockRecorder) JSONClear(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONClear", reflect.TypeOf((*MockPipeliner)(nil).JSONClear), arg0, arg1, arg2)
}

// JSONDebugMemory mocks base method.
func (m *MockPipeliner) JSONDebugMemory(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONDebugMemory", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// JSONDebugMemory indicates an expected call of JSONDebugMemory.
func (mr *MockPipelinerMockRecorder) JSONDebugMemory(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONDebugMemory", reflect.TypeOf((*MockPipeliner)(nil).JSONDebugMemory), arg0, arg1, arg2)
}

// JSONDel mocks base method.
func (m *MockPipeliner) JSONDel(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONDel", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// JSONDel indicates an expected call of JSONDel.
func (mr *MockPipelinerMockRecorder) JSONDel(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONDel", reflect.TypeOf((*MockPipeliner)(nil).JSONDel), arg0, arg1, arg2)
}

// JSONForget mocks base method.
func (m *MockPipeliner) JSONForget(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONForget", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// JSONForget indicates an expected call of JSONForget.
func (mr *MockPipelinerMockRecorder) JSONForget(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONForget", reflect.TypeOf((*MockPipeliner)(nil).JSONForget), arg0, arg1, arg2)
}

// JSONGet mocks base method.
func (m *MockPipeliner) JSONGet(arg0 context.Context, arg1 string, arg2 ...string) *redis.JSONCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JSONGet", varargs...)
	ret0, _ := ret[0].(*redis.JSONCmd)
	return ret0
}

// JSONGet indicates an expected call of JSONGet.
func (mr *MockPipelinerMockRecorder) JSONGet(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONGet", reflect.TypeOf((*MockPipeliner)(nil).JSONGet), varargs...)
}

// JSONGetWithArgs mocks base method.
func (m *MockPipeliner) JSONGetWithArgs(arg0 context.Context, arg1 string, arg2 *redis.JSONGetArgs, arg3 ...string) *redis.JSONCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JSONGetWithArgs", varargs...)
	ret0, _ := ret[0].(*redis.JSONCmd)
	return ret0
}

// JSONGetWithArgs indicates an expected call of JSONGetWithArgs.
func (mr *MockPipelinerMockRecorder) JSONGetWithArgs(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONGetWithArgs", reflect.TypeOf((*MockPipeliner)(nil).JSONGetWithArgs), varargs...)
}

// JSONMGet mocks base method.
func (m *MockPipeliner) JSONMGet(arg0 context.Context, arg1 string, arg2 ...string) *redis.JSONSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JSONMGet", varargs...)
	ret0, _ := ret[0].(*redis.JSONSliceCmd)
	return ret0
}

// JSONMGet indicates an expected call of JSONMGet.
func (mr *MockPipelinerMockRecorder) JSONMGet(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONMGet", reflect.TypeOf((*MockPipeliner)(nil).JSONMGet), varargs...)
}

// JSONMSet mocks base method.
func (m *MockPipeliner) JSONMSet(arg0 context.Context, arg1 ...interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "JSONMSet", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// JSONMSet indicates an expected call of JSONMSet.
func (mr *MockPipelinerMockRecorder) JSONMSet(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONMSet", reflect.TypeOf((*MockPipeliner)(nil).JSONMSet), varargs...)
}

// JSONMSetArgs mocks base method.
func (m *MockPipeliner) JSONMSetArgs(arg0 context.Context, arg1 []redis.JSONSetArgs) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONMSetArgs", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// JSONMSetArgs indicates an expected call of JSONMSetArgs.
func (mr *MockPipelinerMockRecorder) JSONMSetArgs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONMSetArgs", reflect.TypeOf((*MockPipeliner)(nil).JSONMSetArgs), arg0, arg1)
}

// JSONMerge mocks base method.
func (m *MockPipeliner) JSONMerge(arg0 context.Context, arg1, arg2, arg3 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONMerge", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// JSONMerge indicates an expected call of JSONMerge.
func (mr *MockPipelinerMockRecorder) JSONMerge(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONMerge", reflect.TypeOf((*MockPipeliner)(nil).JSONMerge), arg0, arg1, arg2, arg3)
}

// JSONNumIncrBy mocks base method.
func (m *MockPipeliner) JSONNumIncrBy(arg0 context.Context, arg1, arg2 string, arg3 float64) *redis.JSONCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONNumIncrBy", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.JSONCmd)
	return ret0
}

// JSONNumIncrBy indicates an expected call of JSONNumIncrBy.
func (mr *MockPipelinerMockRecorder) JSONNumIncrBy(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONNumIncrBy", reflect.TypeOf((*MockPipeliner)(nil).JSONNumIncrBy), arg0, arg1, arg2, arg3)
}

// JSONObjKeys mocks base method.
func (m *MockPipeliner) JSONObjKeys(arg0 context.Context, arg1, arg2 string) *redis.SliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONObjKeys", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.SliceCmd)
	return ret0
}

// JSONObjKeys indicates an expected call of JSONObjKeys.
func (mr *MockPipelinerMockRecorder) JSONObjKeys(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONObjKeys", reflect.TypeOf((*MockPipeliner)(nil).JSONObjKeys), arg0, arg1, arg2)
}

// JSONObjLen mocks base method.
func (m *MockPipeliner) JSONObjLen(arg0 context.Context, arg1, arg2 string) *redis.IntPointerSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONObjLen", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntPointerSliceCmd)
	return ret0
}

// JSONObjLen indicates an expected call of JSONObjLen.
func (mr *MockPipelinerMockRecorder) JSONObjLen(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONObjLen", reflect.TypeOf((*MockPipeliner)(nil).JSONObjLen), arg0, arg1, arg2)
}

// JSONSet mocks base method.
func (m *MockPipeliner) JSONSet(arg0 context.Context, arg1, arg2 string, arg3 interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONSet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// JSONSet indicates an expected call of JSONSet.
func (mr *MockPipelinerMockRecorder) JSONSet(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONSet", reflect.TypeOf((*MockPipeliner)(nil).JSONSet), arg0, arg1, arg2, arg3)
}

// JSONSetMode mocks base method.
func (m *MockPipeliner) JSONSetMode(arg0 context.Context, arg1, arg2 string, arg3 interface{}, arg4 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONSetMode", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// JSONSetMode indicates an expected call of JSONSetMode.
func (mr *MockPipelinerMockRecorder) JSONSetMode(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONSetMode", reflect.TypeOf((*MockPipeliner)(nil).JSONSetMode), arg0, arg1, arg2, arg3, arg4)
}

// JSONStrAppend mocks base method.
func (m *MockPipeliner) JSONStrAppend(arg0 context.Context, arg1, arg2, arg3 string) *redis.IntPointerSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONStrAppend", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntPointerSliceCmd)
	return ret0
}

// JSONStrAppend indicates an expected call of JSONStrAppend.
func (mr *MockPipelinerMockRecorder) JSONStrAppend(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONStrAppend", reflect.TypeOf((*MockPipeliner)(nil).JSONStrAppend), arg0, arg1, arg2, arg3)
}

// JSONStrLen mocks base method.
func (m *MockPipeliner) JSONStrLen(arg0 context.Context, arg1, arg2 string) *redis.IntPointerSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONStrLen", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntPointerSliceCmd)
	return ret0
}

// JSONStrLen indicates an expected call of JSONStrLen.
func (mr *MockPipelinerMockRecorder) JSONStrLen(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONStrLen", reflect.TypeOf((*MockPipeliner)(nil).JSONStrLen), arg0, arg1, arg2)
}

// JSONToggle mocks base method.
func (m *MockPipeliner) JSONToggle(arg0 context.Context, arg1, arg2 string) *redis.IntPointerSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONToggle", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntPointerSliceCmd)
	return ret0
}

// JSONToggle indicates an expected call of JSONToggle.
func (mr *MockPipelinerMockRecorder) JSONToggle(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONToggle", reflect.TypeOf((*MockPipeliner)(nil).JSONToggle), arg0, arg1, arg2)
}

// JSONType mocks base method.
func (m *MockPipeliner) JSONType(arg0 context.Context, arg1, arg2 string) *redis.JSONSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "JSONType", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.JSONSliceCmd)
	return ret0
}

// JSONType indicates an expected call of JSONType.
func (mr *MockPipelinerMockRecorder) JSONType(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "JSONType", reflect.TypeOf((*MockPipeliner)(nil).JSONType), arg0, arg1, arg2)
}

// Keys mocks base method.
func (m *MockPipeliner) Keys(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Keys", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// Keys indicates an expected call of Keys.
func (mr *MockPipelinerMockRecorder) Keys(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Keys", reflect.TypeOf((*MockPipeliner)(nil).Keys), arg0, arg1)
}

// LCS mocks base method.
func (m *MockPipeliner) LCS(arg0 context.Context, arg1 *redis.LCSQuery) *redis.LCSCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LCS", arg0, arg1)
	ret0, _ := ret[0].(*redis.LCSCmd)
	return ret0
}

// LCS indicates an expected call of LCS.
func (mr *MockPipelinerMockRecorder) LCS(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LCS", reflect.TypeOf((*MockPipeliner)(nil).LCS), arg0, arg1)
}

// LIndex mocks base method.
func (m *MockPipeliner) LIndex(arg0 context.Context, arg1 string, arg2 int64) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LIndex", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// LIndex indicates an expected call of LIndex.
func (mr *MockPipelinerMockRecorder) LIndex(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LIndex", reflect.TypeOf((*MockPipeliner)(nil).LIndex), arg0, arg1, arg2)
}

// LInsert mocks base method.
func (m *MockPipeliner) LInsert(arg0 context.Context, arg1, arg2 string, arg3, arg4 interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LInsert", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LInsert indicates an expected call of LInsert.
func (mr *MockPipelinerMockRecorder) LInsert(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LInsert", reflect.TypeOf((*MockPipeliner)(nil).LInsert), arg0, arg1, arg2, arg3, arg4)
}

// LInsertAfter mocks base method.
func (m *MockPipeliner) LInsertAfter(arg0 context.Context, arg1 string, arg2, arg3 interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LInsertAfter", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LInsertAfter indicates an expected call of LInsertAfter.
func (mr *MockPipelinerMockRecorder) LInsertAfter(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LInsertAfter", reflect.TypeOf((*MockPipeliner)(nil).LInsertAfter), arg0, arg1, arg2, arg3)
}

// LInsertBefore mocks base method.
func (m *MockPipeliner) LInsertBefore(arg0 context.Context, arg1 string, arg2, arg3 interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LInsertBefore", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LInsertBefore indicates an expected call of LInsertBefore.
func (mr *MockPipelinerMockRecorder) LInsertBefore(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LInsertBefore", reflect.TypeOf((*MockPipeliner)(nil).LInsertBefore), arg0, arg1, arg2, arg3)
}

// LLen mocks base method.
func (m *MockPipeliner) LLen(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LLen", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LLen indicates an expected call of LLen.
func (mr *MockPipelinerMockRecorder) LLen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LLen", reflect.TypeOf((*MockPipeliner)(nil).LLen), arg0, arg1)
}

// LMPop mocks base method.
func (m *MockPipeliner) LMPop(arg0 context.Context, arg1 string, arg2 int64, arg3 ...string) *redis.KeyValuesCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LMPop", varargs...)
	ret0, _ := ret[0].(*redis.KeyValuesCmd)
	return ret0
}

// LMPop indicates an expected call of LMPop.
func (mr *MockPipelinerMockRecorder) LMPop(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LMPop", reflect.TypeOf((*MockPipeliner)(nil).LMPop), varargs...)
}

// LMove mocks base method.
func (m *MockPipeliner) LMove(arg0 context.Context, arg1, arg2, arg3, arg4 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LMove", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// LMove indicates an expected call of LMove.
func (mr *MockPipelinerMockRecorder) LMove(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LMove", reflect.TypeOf((*MockPipeliner)(nil).LMove), arg0, arg1, arg2, arg3, arg4)
}

// LPop mocks base method.
func (m *MockPipeliner) LPop(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LPop", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// LPop indicates an expected call of LPop.
func (mr *MockPipelinerMockRecorder) LPop(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPop", reflect.TypeOf((*MockPipeliner)(nil).LPop), arg0, arg1)
}

// LPopCount mocks base method.
func (m *MockPipeliner) LPopCount(arg0 context.Context, arg1 string, arg2 int) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LPopCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// LPopCount indicates an expected call of LPopCount.
func (mr *MockPipelinerMockRecorder) LPopCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPopCount", reflect.TypeOf((*MockPipeliner)(nil).LPopCount), arg0, arg1, arg2)
}

// LPos mocks base method.
func (m *MockPipeliner) LPos(arg0 context.Context, arg1, arg2 string, arg3 redis.LPosArgs) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LPos", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LPos indicates an expected call of LPos.
func (mr *MockPipelinerMockRecorder) LPos(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPos", reflect.TypeOf((*MockPipeliner)(nil).LPos), arg0, arg1, arg2, arg3)
}

// LPosCount mocks base method.
func (m *MockPipeliner) LPosCount(arg0 context.Context, arg1, arg2 string, arg3 int64, arg4 redis.LPosArgs) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LPosCount", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// LPosCount indicates an expected call of LPosCount.
func (mr *MockPipelinerMockRecorder) LPosCount(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPosCount", reflect.TypeOf((*MockPipeliner)(nil).LPosCount), arg0, arg1, arg2, arg3, arg4)
}

// LPush mocks base method.
func (m *MockPipeliner) LPush(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LPush", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LPush indicates an expected call of LPush.
func (mr *MockPipelinerMockRecorder) LPush(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPush", reflect.TypeOf((*MockPipeliner)(nil).LPush), varargs...)
}

// LPushX mocks base method.
func (m *MockPipeliner) LPushX(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LPushX", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LPushX indicates an expected call of LPushX.
func (mr *MockPipelinerMockRecorder) LPushX(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LPushX", reflect.TypeOf((*MockPipeliner)(nil).LPushX), varargs...)
}

// LRange mocks base method.
func (m *MockPipeliner) LRange(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// LRange indicates an expected call of LRange.
func (mr *MockPipelinerMockRecorder) LRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LRange", reflect.TypeOf((*MockPipeliner)(nil).LRange), arg0, arg1, arg2, arg3)
}

// LRem mocks base method.
func (m *MockPipeliner) LRem(arg0 context.Context, arg1 string, arg2 int64, arg3 interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LRem", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LRem indicates an expected call of LRem.
func (mr *MockPipelinerMockRecorder) LRem(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LRem", reflect.TypeOf((*MockPipeliner)(nil).LRem), arg0, arg1, arg2, arg3)
}

// LSet mocks base method.
func (m *MockPipeliner) LSet(arg0 context.Context, arg1 string, arg2 int64, arg3 interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LSet", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// LSet indicates an expected call of LSet.
func (mr *MockPipelinerMockRecorder) LSet(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LSet", reflect.TypeOf((*MockPipeliner)(nil).LSet), arg0, arg1, arg2, arg3)
}

// LTrim mocks base method.
func (m *MockPipeliner) LTrim(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LTrim", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// LTrim indicates an expected call of LTrim.
func (mr *MockPipelinerMockRecorder) LTrim(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LTrim", reflect.TypeOf((*MockPipeliner)(nil).LTrim), arg0, arg1, arg2, arg3)
}

// LastSave mocks base method.
func (m *MockPipeliner) LastSave(arg0 context.Context) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LastSave", arg0)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// LastSave indicates an expected call of LastSave.
func (mr *MockPipelinerMockRecorder) LastSave(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LastSave", reflect.TypeOf((*MockPipeliner)(nil).LastSave), arg0)
}

// Len mocks base method.
func (m *MockPipeliner) Len() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Len")
	ret0, _ := ret[0].(int)
	return ret0
}

// Len indicates an expected call of Len.
func (mr *MockPipelinerMockRecorder) Len() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Len", reflect.TypeOf((*MockPipeliner)(nil).Len))
}

// MGet mocks base method.
func (m *MockPipeliner) MGet(arg0 context.Context, arg1 ...string) *redis.SliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MGet", varargs...)
	ret0, _ := ret[0].(*redis.SliceCmd)
	return ret0
}

// MGet indicates an expected call of MGet.
func (mr *MockPipelinerMockRecorder) MGet(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MGet", reflect.TypeOf((*MockPipeliner)(nil).MGet), varargs...)
}

// MSet mocks base method.
func (m *MockPipeliner) MSet(arg0 context.Context, arg1 ...interface{}) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MSet", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// MSet indicates an expected call of MSet.
func (mr *MockPipelinerMockRecorder) MSet(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MSet", reflect.TypeOf((*MockPipeliner)(nil).MSet), varargs...)
}

// MSetNX mocks base method.
func (m *MockPipeliner) MSetNX(arg0 context.Context, arg1 ...interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MSetNX", varargs...)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// MSetNX indicates an expected call of MSetNX.
func (mr *MockPipelinerMockRecorder) MSetNX(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MSetNX", reflect.TypeOf((*MockPipeliner)(nil).MSetNX), varargs...)
}

// MemoryUsage mocks base method.
func (m *MockPipeliner) MemoryUsage(arg0 context.Context, arg1 string, arg2 ...int) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MemoryUsage", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// MemoryUsage indicates an expected call of MemoryUsage.
func (mr *MockPipelinerMockRecorder) MemoryUsage(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MemoryUsage", reflect.TypeOf((*MockPipeliner)(nil).MemoryUsage), varargs...)
}

// Migrate mocks base method.
func (m *MockPipeliner) Migrate(arg0 context.Context, arg1, arg2, arg3 string, arg4 int, arg5 time.Duration) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Migrate", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Migrate indicates an expected call of Migrate.
func (mr *MockPipelinerMockRecorder) Migrate(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Migrate", reflect.TypeOf((*MockPipeliner)(nil).Migrate), arg0, arg1, arg2, arg3, arg4, arg5)
}

// ModuleLoadex mocks base method.
func (m *MockPipeliner) ModuleLoadex(arg0 context.Context, arg1 *redis.ModuleLoadexConfig) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ModuleLoadex", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ModuleLoadex indicates an expected call of ModuleLoadex.
func (mr *MockPipelinerMockRecorder) ModuleLoadex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ModuleLoadex", reflect.TypeOf((*MockPipeliner)(nil).ModuleLoadex), arg0, arg1)
}

// Move mocks base method.
func (m *MockPipeliner) Move(arg0 context.Context, arg1 string, arg2 int) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Move", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// Move indicates an expected call of Move.
func (mr *MockPipelinerMockRecorder) Move(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Move", reflect.TypeOf((*MockPipeliner)(nil).Move), arg0, arg1, arg2)
}

// ObjectEncoding mocks base method.
func (m *MockPipeliner) ObjectEncoding(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ObjectEncoding", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ObjectEncoding indicates an expected call of ObjectEncoding.
func (mr *MockPipelinerMockRecorder) ObjectEncoding(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ObjectEncoding", reflect.TypeOf((*MockPipeliner)(nil).ObjectEncoding), arg0, arg1)
}

// ObjectFreq mocks base method.
func (m *MockPipeliner) ObjectFreq(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ObjectFreq", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ObjectFreq indicates an expected call of ObjectFreq.
func (mr *MockPipelinerMockRecorder) ObjectFreq(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ObjectFreq", reflect.TypeOf((*MockPipeliner)(nil).ObjectFreq), arg0, arg1)
}

// ObjectIdleTime mocks base method.
func (m *MockPipeliner) ObjectIdleTime(arg0 context.Context, arg1 string) *redis.DurationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ObjectIdleTime", arg0, arg1)
	ret0, _ := ret[0].(*redis.DurationCmd)
	return ret0
}

// ObjectIdleTime indicates an expected call of ObjectIdleTime.
func (mr *MockPipelinerMockRecorder) ObjectIdleTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ObjectIdleTime", reflect.TypeOf((*MockPipeliner)(nil).ObjectIdleTime), arg0, arg1)
}

// ObjectRefCount mocks base method.
func (m *MockPipeliner) ObjectRefCount(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ObjectRefCount", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ObjectRefCount indicates an expected call of ObjectRefCount.
func (mr *MockPipelinerMockRecorder) ObjectRefCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ObjectRefCount", reflect.TypeOf((*MockPipeliner)(nil).ObjectRefCount), arg0, arg1)
}

// PExpire mocks base method.
func (m *MockPipeliner) PExpire(arg0 context.Context, arg1 string, arg2 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PExpire", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// PExpire indicates an expected call of PExpire.
func (mr *MockPipelinerMockRecorder) PExpire(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PExpire", reflect.TypeOf((*MockPipeliner)(nil).PExpire), arg0, arg1, arg2)
}

// PExpireAt mocks base method.
func (m *MockPipeliner) PExpireAt(arg0 context.Context, arg1 string, arg2 time.Time) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PExpireAt", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// PExpireAt indicates an expected call of PExpireAt.
func (mr *MockPipelinerMockRecorder) PExpireAt(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PExpireAt", reflect.TypeOf((*MockPipeliner)(nil).PExpireAt), arg0, arg1, arg2)
}

// PExpireTime mocks base method.
func (m *MockPipeliner) PExpireTime(arg0 context.Context, arg1 string) *redis.DurationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PExpireTime", arg0, arg1)
	ret0, _ := ret[0].(*redis.DurationCmd)
	return ret0
}

// PExpireTime indicates an expected call of PExpireTime.
func (mr *MockPipelinerMockRecorder) PExpireTime(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PExpireTime", reflect.TypeOf((*MockPipeliner)(nil).PExpireTime), arg0, arg1)
}

// PFAdd mocks base method.
func (m *MockPipeliner) PFAdd(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PFAdd", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// PFAdd indicates an expected call of PFAdd.
func (mr *MockPipelinerMockRecorder) PFAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PFAdd", reflect.TypeOf((*MockPipeliner)(nil).PFAdd), varargs...)
}

// PFCount mocks base method.
func (m *MockPipeliner) PFCount(arg0 context.Context, arg1 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PFCount", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// PFCount indicates an expected call of PFCount.
func (mr *MockPipelinerMockRecorder) PFCount(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PFCount", reflect.TypeOf((*MockPipeliner)(nil).PFCount), varargs...)
}

// PFMerge mocks base method.
func (m *MockPipeliner) PFMerge(arg0 context.Context, arg1 string, arg2 ...string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PFMerge", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// PFMerge indicates an expected call of PFMerge.
func (mr *MockPipelinerMockRecorder) PFMerge(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PFMerge", reflect.TypeOf((*MockPipeliner)(nil).PFMerge), varargs...)
}

// PTTL mocks base method.
func (m *MockPipeliner) PTTL(arg0 context.Context, arg1 string) *redis.DurationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PTTL", arg0, arg1)
	ret0, _ := ret[0].(*redis.DurationCmd)
	return ret0
}

// PTTL indicates an expected call of PTTL.
func (mr *MockPipelinerMockRecorder) PTTL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PTTL", reflect.TypeOf((*MockPipeliner)(nil).PTTL), arg0, arg1)
}

// Persist mocks base method.
func (m *MockPipeliner) Persist(arg0 context.Context, arg1 string) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Persist", arg0, arg1)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// Persist indicates an expected call of Persist.
func (mr *MockPipelinerMockRecorder) Persist(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Persist", reflect.TypeOf((*MockPipeliner)(nil).Persist), arg0, arg1)
}

// Ping mocks base method.
func (m *MockPipeliner) Ping(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ping", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Ping indicates an expected call of Ping.
func (mr *MockPipelinerMockRecorder) Ping(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ping", reflect.TypeOf((*MockPipeliner)(nil).Ping), arg0)
}

// Pipeline mocks base method.
func (m *MockPipeliner) Pipeline() redis.Pipeliner {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Pipeline")
	ret0, _ := ret[0].(redis.Pipeliner)
	return ret0
}

// Pipeline indicates an expected call of Pipeline.
func (mr *MockPipelinerMockRecorder) Pipeline() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Pipeline", reflect.TypeOf((*MockPipeliner)(nil).Pipeline))
}

// Pipelined mocks base method.
func (m *MockPipeliner) Pipelined(arg0 context.Context, arg1 func(redis.Pipeliner) error) ([]redis.Cmder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Pipelined", arg0, arg1)
	ret0, _ := ret[0].([]redis.Cmder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Pipelined indicates an expected call of Pipelined.
func (mr *MockPipelinerMockRecorder) Pipelined(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Pipelined", reflect.TypeOf((*MockPipeliner)(nil).Pipelined), arg0, arg1)
}

// Process mocks base method.
func (m *MockPipeliner) Process(arg0 context.Context, arg1 redis.Cmder) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Process", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Process indicates an expected call of Process.
func (mr *MockPipelinerMockRecorder) Process(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Process", reflect.TypeOf((*MockPipeliner)(nil).Process), arg0, arg1)
}

// PubSubChannels mocks base method.
func (m *MockPipeliner) PubSubChannels(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PubSubChannels", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// PubSubChannels indicates an expected call of PubSubChannels.
func (mr *MockPipelinerMockRecorder) PubSubChannels(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PubSubChannels", reflect.TypeOf((*MockPipeliner)(nil).PubSubChannels), arg0, arg1)
}

// PubSubNumPat mocks base method.
func (m *MockPipeliner) PubSubNumPat(arg0 context.Context) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PubSubNumPat", arg0)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// PubSubNumPat indicates an expected call of PubSubNumPat.
func (mr *MockPipelinerMockRecorder) PubSubNumPat(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PubSubNumPat", reflect.TypeOf((*MockPipeliner)(nil).PubSubNumPat), arg0)
}

// PubSubNumSub mocks base method.
func (m *MockPipeliner) PubSubNumSub(arg0 context.Context, arg1 ...string) *redis.MapStringIntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PubSubNumSub", varargs...)
	ret0, _ := ret[0].(*redis.MapStringIntCmd)
	return ret0
}

// PubSubNumSub indicates an expected call of PubSubNumSub.
func (mr *MockPipelinerMockRecorder) PubSubNumSub(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PubSubNumSub", reflect.TypeOf((*MockPipeliner)(nil).PubSubNumSub), varargs...)
}

// PubSubShardChannels mocks base method.
func (m *MockPipeliner) PubSubShardChannels(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PubSubShardChannels", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// PubSubShardChannels indicates an expected call of PubSubShardChannels.
func (mr *MockPipelinerMockRecorder) PubSubShardChannels(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PubSubShardChannels", reflect.TypeOf((*MockPipeliner)(nil).PubSubShardChannels), arg0, arg1)
}

// PubSubShardNumSub mocks base method.
func (m *MockPipeliner) PubSubShardNumSub(arg0 context.Context, arg1 ...string) *redis.MapStringIntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PubSubShardNumSub", varargs...)
	ret0, _ := ret[0].(*redis.MapStringIntCmd)
	return ret0
}

// PubSubShardNumSub indicates an expected call of PubSubShardNumSub.
func (mr *MockPipelinerMockRecorder) PubSubShardNumSub(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PubSubShardNumSub", reflect.TypeOf((*MockPipeliner)(nil).PubSubShardNumSub), varargs...)
}

// Publish mocks base method.
func (m *MockPipeliner) Publish(arg0 context.Context, arg1 string, arg2 interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Publish", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Publish indicates an expected call of Publish.
func (mr *MockPipelinerMockRecorder) Publish(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Publish", reflect.TypeOf((*MockPipeliner)(nil).Publish), arg0, arg1, arg2)
}

// Quit mocks base method.
func (m *MockPipeliner) Quit(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Quit", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Quit indicates an expected call of Quit.
func (mr *MockPipelinerMockRecorder) Quit(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Quit", reflect.TypeOf((*MockPipeliner)(nil).Quit), arg0)
}

// RPop mocks base method.
func (m *MockPipeliner) RPop(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RPop", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// RPop indicates an expected call of RPop.
func (mr *MockPipelinerMockRecorder) RPop(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPop", reflect.TypeOf((*MockPipeliner)(nil).RPop), arg0, arg1)
}

// RPopCount mocks base method.
func (m *MockPipeliner) RPopCount(arg0 context.Context, arg1 string, arg2 int) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RPopCount", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// RPopCount indicates an expected call of RPopCount.
func (mr *MockPipelinerMockRecorder) RPopCount(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPopCount", reflect.TypeOf((*MockPipeliner)(nil).RPopCount), arg0, arg1, arg2)
}

// RPopLPush mocks base method.
func (m *MockPipeliner) RPopLPush(arg0 context.Context, arg1, arg2 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RPopLPush", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// RPopLPush indicates an expected call of RPopLPush.
func (mr *MockPipelinerMockRecorder) RPopLPush(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPopLPush", reflect.TypeOf((*MockPipeliner)(nil).RPopLPush), arg0, arg1, arg2)
}

// RPush mocks base method.
func (m *MockPipeliner) RPush(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RPush", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// RPush indicates an expected call of RPush.
func (mr *MockPipelinerMockRecorder) RPush(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPush", reflect.TypeOf((*MockPipeliner)(nil).RPush), varargs...)
}

// RPushX mocks base method.
func (m *MockPipeliner) RPushX(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RPushX", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// RPushX indicates an expected call of RPushX.
func (mr *MockPipelinerMockRecorder) RPushX(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RPushX", reflect.TypeOf((*MockPipeliner)(nil).RPushX), varargs...)
}

// RandomKey mocks base method.
func (m *MockPipeliner) RandomKey(arg0 context.Context) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RandomKey", arg0)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// RandomKey indicates an expected call of RandomKey.
func (mr *MockPipelinerMockRecorder) RandomKey(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RandomKey", reflect.TypeOf((*MockPipeliner)(nil).RandomKey), arg0)
}

// ReadOnly mocks base method.
func (m *MockPipeliner) ReadOnly(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadOnly", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ReadOnly indicates an expected call of ReadOnly.
func (mr *MockPipelinerMockRecorder) ReadOnly(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadOnly", reflect.TypeOf((*MockPipeliner)(nil).ReadOnly), arg0)
}

// ReadWrite mocks base method.
func (m *MockPipeliner) ReadWrite(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReadWrite", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ReadWrite indicates an expected call of ReadWrite.
func (mr *MockPipelinerMockRecorder) ReadWrite(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReadWrite", reflect.TypeOf((*MockPipeliner)(nil).ReadWrite), arg0)
}

// Rename mocks base method.
func (m *MockPipeliner) Rename(arg0 context.Context, arg1, arg2 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Rename", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Rename indicates an expected call of Rename.
func (mr *MockPipelinerMockRecorder) Rename(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Rename", reflect.TypeOf((*MockPipeliner)(nil).Rename), arg0, arg1, arg2)
}

// RenameNX mocks base method.
func (m *MockPipeliner) RenameNX(arg0 context.Context, arg1, arg2 string) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RenameNX", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// RenameNX indicates an expected call of RenameNX.
func (mr *MockPipelinerMockRecorder) RenameNX(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RenameNX", reflect.TypeOf((*MockPipeliner)(nil).RenameNX), arg0, arg1, arg2)
}

// Restore mocks base method.
func (m *MockPipeliner) Restore(arg0 context.Context, arg1 string, arg2 time.Duration, arg3 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Restore", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Restore indicates an expected call of Restore.
func (mr *MockPipelinerMockRecorder) Restore(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Restore", reflect.TypeOf((*MockPipeliner)(nil).Restore), arg0, arg1, arg2, arg3)
}

// RestoreReplace mocks base method.
func (m *MockPipeliner) RestoreReplace(arg0 context.Context, arg1 string, arg2 time.Duration, arg3 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestoreReplace", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// RestoreReplace indicates an expected call of RestoreReplace.
func (mr *MockPipelinerMockRecorder) RestoreReplace(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreReplace", reflect.TypeOf((*MockPipeliner)(nil).RestoreReplace), arg0, arg1, arg2, arg3)
}

// SAdd mocks base method.
func (m *MockPipeliner) SAdd(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SAdd", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SAdd indicates an expected call of SAdd.
func (mr *MockPipelinerMockRecorder) SAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SAdd", reflect.TypeOf((*MockPipeliner)(nil).SAdd), varargs...)
}

// SCard mocks base method.
func (m *MockPipeliner) SCard(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SCard", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SCard indicates an expected call of SCard.
func (mr *MockPipelinerMockRecorder) SCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SCard", reflect.TypeOf((*MockPipeliner)(nil).SCard), arg0, arg1)
}

// SDiff mocks base method.
func (m *MockPipeliner) SDiff(arg0 context.Context, arg1 ...string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SDiff", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// SDiff indicates an expected call of SDiff.
func (mr *MockPipelinerMockRecorder) SDiff(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SDiff", reflect.TypeOf((*MockPipeliner)(nil).SDiff), varargs...)
}

// SDiffStore mocks base method.
func (m *MockPipeliner) SDiffStore(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SDiffStore", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SDiffStore indicates an expected call of SDiffStore.
func (mr *MockPipelinerMockRecorder) SDiffStore(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SDiffStore", reflect.TypeOf((*MockPipeliner)(nil).SDiffStore), varargs...)
}

// SInter mocks base method.
func (m *MockPipeliner) SInter(arg0 context.Context, arg1 ...string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SInter", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// SInter indicates an expected call of SInter.
func (mr *MockPipelinerMockRecorder) SInter(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SInter", reflect.TypeOf((*MockPipeliner)(nil).SInter), varargs...)
}

// SInterCard mocks base method.
func (m *MockPipeliner) SInterCard(arg0 context.Context, arg1 int64, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SInterCard", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SInterCard indicates an expected call of SInterCard.
func (mr *MockPipelinerMockRecorder) SInterCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SInterCard", reflect.TypeOf((*MockPipeliner)(nil).SInterCard), varargs...)
}

// SInterStore mocks base method.
func (m *MockPipeliner) SInterStore(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SInterStore", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SInterStore indicates an expected call of SInterStore.
func (mr *MockPipelinerMockRecorder) SInterStore(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SInterStore", reflect.TypeOf((*MockPipeliner)(nil).SInterStore), varargs...)
}

// SIsMember mocks base method.
func (m *MockPipeliner) SIsMember(arg0 context.Context, arg1 string, arg2 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SIsMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// SIsMember indicates an expected call of SIsMember.
func (mr *MockPipelinerMockRecorder) SIsMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SIsMember", reflect.TypeOf((*MockPipeliner)(nil).SIsMember), arg0, arg1, arg2)
}

// SMIsMember mocks base method.
func (m *MockPipeliner) SMIsMember(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.BoolSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SMIsMember", varargs...)
	ret0, _ := ret[0].(*redis.BoolSliceCmd)
	return ret0
}

// SMIsMember indicates an expected call of SMIsMember.
func (mr *MockPipelinerMockRecorder) SMIsMember(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SMIsMember", reflect.TypeOf((*MockPipeliner)(nil).SMIsMember), varargs...)
}

// SMembers mocks base method.
func (m *MockPipeliner) SMembers(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SMembers", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// SMembers indicates an expected call of SMembers.
func (mr *MockPipelinerMockRecorder) SMembers(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SMembers", reflect.TypeOf((*MockPipeliner)(nil).SMembers), arg0, arg1)
}

// SMembersMap mocks base method.
func (m *MockPipeliner) SMembersMap(arg0 context.Context, arg1 string) *redis.StringStructMapCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SMembersMap", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringStructMapCmd)
	return ret0
}

// SMembersMap indicates an expected call of SMembersMap.
func (mr *MockPipelinerMockRecorder) SMembersMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SMembersMap", reflect.TypeOf((*MockPipeliner)(nil).SMembersMap), arg0, arg1)
}

// SMove mocks base method.
func (m *MockPipeliner) SMove(arg0 context.Context, arg1, arg2 string, arg3 interface{}) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SMove", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// SMove indicates an expected call of SMove.
func (mr *MockPipelinerMockRecorder) SMove(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SMove", reflect.TypeOf((*MockPipeliner)(nil).SMove), arg0, arg1, arg2, arg3)
}

// SPop mocks base method.
func (m *MockPipeliner) SPop(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SPop", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// SPop indicates an expected call of SPop.
func (mr *MockPipelinerMockRecorder) SPop(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SPop", reflect.TypeOf((*MockPipeliner)(nil).SPop), arg0, arg1)
}

// SPopN mocks base method.
func (m *MockPipeliner) SPopN(arg0 context.Context, arg1 string, arg2 int64) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SPopN", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// SPopN indicates an expected call of SPopN.
func (mr *MockPipelinerMockRecorder) SPopN(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SPopN", reflect.TypeOf((*MockPipeliner)(nil).SPopN), arg0, arg1, arg2)
}

// SPublish mocks base method.
func (m *MockPipeliner) SPublish(arg0 context.Context, arg1 string, arg2 interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SPublish", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SPublish indicates an expected call of SPublish.
func (mr *MockPipelinerMockRecorder) SPublish(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SPublish", reflect.TypeOf((*MockPipeliner)(nil).SPublish), arg0, arg1, arg2)
}

// SRandMember mocks base method.
func (m *MockPipeliner) SRandMember(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SRandMember", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// SRandMember indicates an expected call of SRandMember.
func (mr *MockPipelinerMockRecorder) SRandMember(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SRandMember", reflect.TypeOf((*MockPipeliner)(nil).SRandMember), arg0, arg1)
}

// SRandMemberN mocks base method.
func (m *MockPipeliner) SRandMemberN(arg0 context.Context, arg1 string, arg2 int64) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SRandMemberN", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// SRandMemberN indicates an expected call of SRandMemberN.
func (mr *MockPipelinerMockRecorder) SRandMemberN(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SRandMemberN", reflect.TypeOf((*MockPipeliner)(nil).SRandMemberN), arg0, arg1, arg2)
}

// SRem mocks base method.
func (m *MockPipeliner) SRem(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SRem", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SRem indicates an expected call of SRem.
func (mr *MockPipelinerMockRecorder) SRem(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SRem", reflect.TypeOf((*MockPipeliner)(nil).SRem), varargs...)
}

// SScan mocks base method.
func (m *MockPipeliner) SScan(arg0 context.Context, arg1 string, arg2 uint64, arg3 string, arg4 int64) *redis.ScanCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SScan", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.ScanCmd)
	return ret0
}

// SScan indicates an expected call of SScan.
func (mr *MockPipelinerMockRecorder) SScan(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SScan", reflect.TypeOf((*MockPipeliner)(nil).SScan), arg0, arg1, arg2, arg3, arg4)
}

// SUnion mocks base method.
func (m *MockPipeliner) SUnion(arg0 context.Context, arg1 ...string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SUnion", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// SUnion indicates an expected call of SUnion.
func (mr *MockPipelinerMockRecorder) SUnion(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SUnion", reflect.TypeOf((*MockPipeliner)(nil).SUnion), varargs...)
}

// SUnionStore mocks base method.
func (m *MockPipeliner) SUnionStore(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SUnionStore", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SUnionStore indicates an expected call of SUnionStore.
func (mr *MockPipelinerMockRecorder) SUnionStore(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SUnionStore", reflect.TypeOf((*MockPipeliner)(nil).SUnionStore), varargs...)
}

// Save mocks base method.
func (m *MockPipeliner) Save(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Save", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Save indicates an expected call of Save.
func (mr *MockPipelinerMockRecorder) Save(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockPipeliner)(nil).Save), arg0)
}

// Scan mocks base method.
func (m *MockPipeliner) Scan(arg0 context.Context, arg1 uint64, arg2 string, arg3 int64) *redis.ScanCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scan", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.ScanCmd)
	return ret0
}

// Scan indicates an expected call of Scan.
func (mr *MockPipelinerMockRecorder) Scan(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scan", reflect.TypeOf((*MockPipeliner)(nil).Scan), arg0, arg1, arg2, arg3)
}

// ScanType mocks base method.
func (m *MockPipeliner) ScanType(arg0 context.Context, arg1 uint64, arg2 string, arg3 int64, arg4 string) *redis.ScanCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScanType", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.ScanCmd)
	return ret0
}

// ScanType indicates an expected call of ScanType.
func (mr *MockPipelinerMockRecorder) ScanType(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScanType", reflect.TypeOf((*MockPipeliner)(nil).ScanType), arg0, arg1, arg2, arg3, arg4)
}

// ScriptExists mocks base method.
func (m *MockPipeliner) ScriptExists(arg0 context.Context, arg1 ...string) *redis.BoolSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScriptExists", varargs...)
	ret0, _ := ret[0].(*redis.BoolSliceCmd)
	return ret0
}

// ScriptExists indicates an expected call of ScriptExists.
func (mr *MockPipelinerMockRecorder) ScriptExists(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScriptExists", reflect.TypeOf((*MockPipeliner)(nil).ScriptExists), varargs...)
}

// ScriptFlush mocks base method.
func (m *MockPipeliner) ScriptFlush(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScriptFlush", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ScriptFlush indicates an expected call of ScriptFlush.
func (mr *MockPipelinerMockRecorder) ScriptFlush(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScriptFlush", reflect.TypeOf((*MockPipeliner)(nil).ScriptFlush), arg0)
}

// ScriptKill mocks base method.
func (m *MockPipeliner) ScriptKill(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScriptKill", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ScriptKill indicates an expected call of ScriptKill.
func (mr *MockPipelinerMockRecorder) ScriptKill(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScriptKill", reflect.TypeOf((*MockPipeliner)(nil).ScriptKill), arg0)
}

// ScriptLoad mocks base method.
func (m *MockPipeliner) ScriptLoad(arg0 context.Context, arg1 string) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScriptLoad", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// ScriptLoad indicates an expected call of ScriptLoad.
func (mr *MockPipelinerMockRecorder) ScriptLoad(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScriptLoad", reflect.TypeOf((*MockPipeliner)(nil).ScriptLoad), arg0, arg1)
}

// Select mocks base method.
func (m *MockPipeliner) Select(arg0 context.Context, arg1 int) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Select", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Select indicates an expected call of Select.
func (mr *MockPipelinerMockRecorder) Select(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Select", reflect.TypeOf((*MockPipeliner)(nil).Select), arg0, arg1)
}

// Set mocks base method.
func (m *MockPipeliner) Set(arg0 context.Context, arg1 string, arg2 interface{}, arg3 time.Duration) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Set", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Set indicates an expected call of Set.
func (mr *MockPipelinerMockRecorder) Set(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Set", reflect.TypeOf((*MockPipeliner)(nil).Set), arg0, arg1, arg2, arg3)
}

// SetArgs mocks base method.
func (m *MockPipeliner) SetArgs(arg0 context.Context, arg1 string, arg2 interface{}, arg3 redis.SetArgs) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// SetArgs indicates an expected call of SetArgs.
func (mr *MockPipelinerMockRecorder) SetArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetArgs", reflect.TypeOf((*MockPipeliner)(nil).SetArgs), arg0, arg1, arg2, arg3)
}

// SetBit mocks base method.
func (m *MockPipeliner) SetBit(arg0 context.Context, arg1 string, arg2 int64, arg3 int) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetBit", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SetBit indicates an expected call of SetBit.
func (mr *MockPipelinerMockRecorder) SetBit(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetBit", reflect.TypeOf((*MockPipeliner)(nil).SetBit), arg0, arg1, arg2, arg3)
}

// SetEx mocks base method.
func (m *MockPipeliner) SetEx(arg0 context.Context, arg1 string, arg2 interface{}, arg3 time.Duration) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetEx", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// SetEx indicates an expected call of SetEx.
func (mr *MockPipelinerMockRecorder) SetEx(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetEx", reflect.TypeOf((*MockPipeliner)(nil).SetEx), arg0, arg1, arg2, arg3)
}

// SetNX mocks base method.
func (m *MockPipeliner) SetNX(arg0 context.Context, arg1 string, arg2 interface{}, arg3 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetNX", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// SetNX indicates an expected call of SetNX.
func (mr *MockPipelinerMockRecorder) SetNX(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetNX", reflect.TypeOf((*MockPipeliner)(nil).SetNX), arg0, arg1, arg2, arg3)
}

// SetRange mocks base method.
func (m *MockPipeliner) SetRange(arg0 context.Context, arg1 string, arg2 int64, arg3 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SetRange indicates an expected call of SetRange.
func (mr *MockPipelinerMockRecorder) SetRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRange", reflect.TypeOf((*MockPipeliner)(nil).SetRange), arg0, arg1, arg2, arg3)
}

// SetXX mocks base method.
func (m *MockPipeliner) SetXX(arg0 context.Context, arg1 string, arg2 interface{}, arg3 time.Duration) *redis.BoolCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetXX", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.BoolCmd)
	return ret0
}

// SetXX indicates an expected call of SetXX.
func (mr *MockPipelinerMockRecorder) SetXX(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetXX", reflect.TypeOf((*MockPipeliner)(nil).SetXX), arg0, arg1, arg2, arg3)
}

// Shutdown mocks base method.
func (m *MockPipeliner) Shutdown(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Shutdown", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Shutdown indicates an expected call of Shutdown.
func (mr *MockPipelinerMockRecorder) Shutdown(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Shutdown", reflect.TypeOf((*MockPipeliner)(nil).Shutdown), arg0)
}

// ShutdownNoSave mocks base method.
func (m *MockPipeliner) ShutdownNoSave(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShutdownNoSave", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ShutdownNoSave indicates an expected call of ShutdownNoSave.
func (mr *MockPipelinerMockRecorder) ShutdownNoSave(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutdownNoSave", reflect.TypeOf((*MockPipeliner)(nil).ShutdownNoSave), arg0)
}

// ShutdownSave mocks base method.
func (m *MockPipeliner) ShutdownSave(arg0 context.Context) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ShutdownSave", arg0)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// ShutdownSave indicates an expected call of ShutdownSave.
func (mr *MockPipelinerMockRecorder) ShutdownSave(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ShutdownSave", reflect.TypeOf((*MockPipeliner)(nil).ShutdownSave), arg0)
}

// SlaveOf mocks base method.
func (m *MockPipeliner) SlaveOf(arg0 context.Context, arg1, arg2 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SlaveOf", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// SlaveOf indicates an expected call of SlaveOf.
func (mr *MockPipelinerMockRecorder) SlaveOf(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SlaveOf", reflect.TypeOf((*MockPipeliner)(nil).SlaveOf), arg0, arg1, arg2)
}

// SlowLogGet mocks base method.
func (m *MockPipeliner) SlowLogGet(arg0 context.Context, arg1 int64) *redis.SlowLogCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SlowLogGet", arg0, arg1)
	ret0, _ := ret[0].(*redis.SlowLogCmd)
	return ret0
}

// SlowLogGet indicates an expected call of SlowLogGet.
func (mr *MockPipelinerMockRecorder) SlowLogGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SlowLogGet", reflect.TypeOf((*MockPipeliner)(nil).SlowLogGet), arg0, arg1)
}

// Sort mocks base method.
func (m *MockPipeliner) Sort(arg0 context.Context, arg1 string, arg2 *redis.Sort) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Sort", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// Sort indicates an expected call of Sort.
func (mr *MockPipelinerMockRecorder) Sort(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Sort", reflect.TypeOf((*MockPipeliner)(nil).Sort), arg0, arg1, arg2)
}

// SortInterfaces mocks base method.
func (m *MockPipeliner) SortInterfaces(arg0 context.Context, arg1 string, arg2 *redis.Sort) *redis.SliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SortInterfaces", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.SliceCmd)
	return ret0
}

// SortInterfaces indicates an expected call of SortInterfaces.
func (mr *MockPipelinerMockRecorder) SortInterfaces(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortInterfaces", reflect.TypeOf((*MockPipeliner)(nil).SortInterfaces), arg0, arg1, arg2)
}

// SortRO mocks base method.
func (m *MockPipeliner) SortRO(arg0 context.Context, arg1 string, arg2 *redis.Sort) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SortRO", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// SortRO indicates an expected call of SortRO.
func (mr *MockPipelinerMockRecorder) SortRO(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortRO", reflect.TypeOf((*MockPipeliner)(nil).SortRO), arg0, arg1, arg2)
}

// SortStore mocks base method.
func (m *MockPipeliner) SortStore(arg0 context.Context, arg1, arg2 string, arg3 *redis.Sort) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SortStore", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// SortStore indicates an expected call of SortStore.
func (mr *MockPipelinerMockRecorder) SortStore(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SortStore", reflect.TypeOf((*MockPipeliner)(nil).SortStore), arg0, arg1, arg2, arg3)
}

// StrLen mocks base method.
func (m *MockPipeliner) StrLen(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StrLen", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// StrLen indicates an expected call of StrLen.
func (mr *MockPipelinerMockRecorder) StrLen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StrLen", reflect.TypeOf((*MockPipeliner)(nil).StrLen), arg0, arg1)
}

// SwapDB mocks base method.
func (m *MockPipeliner) SwapDB(arg0 context.Context, arg1, arg2 int) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SwapDB", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// SwapDB indicates an expected call of SwapDB.
func (mr *MockPipelinerMockRecorder) SwapDB(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SwapDB", reflect.TypeOf((*MockPipeliner)(nil).SwapDB), arg0, arg1, arg2)
}

// TDigestAdd mocks base method.
func (m *MockPipeliner) TDigestAdd(arg0 context.Context, arg1 string, arg2 ...float64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TDigestAdd", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TDigestAdd indicates an expected call of TDigestAdd.
func (mr *MockPipelinerMockRecorder) TDigestAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestAdd", reflect.TypeOf((*MockPipeliner)(nil).TDigestAdd), varargs...)
}

// TDigestByRank mocks base method.
func (m *MockPipeliner) TDigestByRank(arg0 context.Context, arg1 string, arg2 ...uint64) *redis.FloatSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TDigestByRank", varargs...)
	ret0, _ := ret[0].(*redis.FloatSliceCmd)
	return ret0
}

// TDigestByRank indicates an expected call of TDigestByRank.
func (mr *MockPipelinerMockRecorder) TDigestByRank(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestByRank", reflect.TypeOf((*MockPipeliner)(nil).TDigestByRank), varargs...)
}

// TDigestByRevRank mocks base method.
func (m *MockPipeliner) TDigestByRevRank(arg0 context.Context, arg1 string, arg2 ...uint64) *redis.FloatSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TDigestByRevRank", varargs...)
	ret0, _ := ret[0].(*redis.FloatSliceCmd)
	return ret0
}

// TDigestByRevRank indicates an expected call of TDigestByRevRank.
func (mr *MockPipelinerMockRecorder) TDigestByRevRank(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestByRevRank", reflect.TypeOf((*MockPipeliner)(nil).TDigestByRevRank), varargs...)
}

// TDigestCDF mocks base method.
func (m *MockPipeliner) TDigestCDF(arg0 context.Context, arg1 string, arg2 ...float64) *redis.FloatSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TDigestCDF", varargs...)
	ret0, _ := ret[0].(*redis.FloatSliceCmd)
	return ret0
}

// TDigestCDF indicates an expected call of TDigestCDF.
func (mr *MockPipelinerMockRecorder) TDigestCDF(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestCDF", reflect.TypeOf((*MockPipeliner)(nil).TDigestCDF), varargs...)
}

// TDigestCreate mocks base method.
func (m *MockPipeliner) TDigestCreate(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TDigestCreate", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TDigestCreate indicates an expected call of TDigestCreate.
func (mr *MockPipelinerMockRecorder) TDigestCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestCreate", reflect.TypeOf((*MockPipeliner)(nil).TDigestCreate), arg0, arg1)
}

// TDigestCreateWithCompression mocks base method.
func (m *MockPipeliner) TDigestCreateWithCompression(arg0 context.Context, arg1 string, arg2 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TDigestCreateWithCompression", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TDigestCreateWithCompression indicates an expected call of TDigestCreateWithCompression.
func (mr *MockPipelinerMockRecorder) TDigestCreateWithCompression(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestCreateWithCompression", reflect.TypeOf((*MockPipeliner)(nil).TDigestCreateWithCompression), arg0, arg1, arg2)
}

// TDigestInfo mocks base method.
func (m *MockPipeliner) TDigestInfo(arg0 context.Context, arg1 string) *redis.TDigestInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TDigestInfo", arg0, arg1)
	ret0, _ := ret[0].(*redis.TDigestInfoCmd)
	return ret0
}

// TDigestInfo indicates an expected call of TDigestInfo.
func (mr *MockPipelinerMockRecorder) TDigestInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestInfo", reflect.TypeOf((*MockPipeliner)(nil).TDigestInfo), arg0, arg1)
}

// TDigestMax mocks base method.
func (m *MockPipeliner) TDigestMax(arg0 context.Context, arg1 string) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TDigestMax", arg0, arg1)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// TDigestMax indicates an expected call of TDigestMax.
func (mr *MockPipelinerMockRecorder) TDigestMax(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestMax", reflect.TypeOf((*MockPipeliner)(nil).TDigestMax), arg0, arg1)
}

// TDigestMerge mocks base method.
func (m *MockPipeliner) TDigestMerge(arg0 context.Context, arg1 string, arg2 *redis.TDigestMergeOptions, arg3 ...string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TDigestMerge", varargs...)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TDigestMerge indicates an expected call of TDigestMerge.
func (mr *MockPipelinerMockRecorder) TDigestMerge(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestMerge", reflect.TypeOf((*MockPipeliner)(nil).TDigestMerge), varargs...)
}

// TDigestMin mocks base method.
func (m *MockPipeliner) TDigestMin(arg0 context.Context, arg1 string) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TDigestMin", arg0, arg1)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// TDigestMin indicates an expected call of TDigestMin.
func (mr *MockPipelinerMockRecorder) TDigestMin(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestMin", reflect.TypeOf((*MockPipeliner)(nil).TDigestMin), arg0, arg1)
}

// TDigestQuantile mocks base method.
func (m *MockPipeliner) TDigestQuantile(arg0 context.Context, arg1 string, arg2 ...float64) *redis.FloatSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TDigestQuantile", varargs...)
	ret0, _ := ret[0].(*redis.FloatSliceCmd)
	return ret0
}

// TDigestQuantile indicates an expected call of TDigestQuantile.
func (mr *MockPipelinerMockRecorder) TDigestQuantile(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestQuantile", reflect.TypeOf((*MockPipeliner)(nil).TDigestQuantile), varargs...)
}

// TDigestRank mocks base method.
func (m *MockPipeliner) TDigestRank(arg0 context.Context, arg1 string, arg2 ...float64) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TDigestRank", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// TDigestRank indicates an expected call of TDigestRank.
func (mr *MockPipelinerMockRecorder) TDigestRank(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestRank", reflect.TypeOf((*MockPipeliner)(nil).TDigestRank), varargs...)
}

// TDigestReset mocks base method.
func (m *MockPipeliner) TDigestReset(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TDigestReset", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TDigestReset indicates an expected call of TDigestReset.
func (mr *MockPipelinerMockRecorder) TDigestReset(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestReset", reflect.TypeOf((*MockPipeliner)(nil).TDigestReset), arg0, arg1)
}

// TDigestRevRank mocks base method.
func (m *MockPipeliner) TDigestRevRank(arg0 context.Context, arg1 string, arg2 ...float64) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TDigestRevRank", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// TDigestRevRank indicates an expected call of TDigestRevRank.
func (mr *MockPipelinerMockRecorder) TDigestRevRank(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestRevRank", reflect.TypeOf((*MockPipeliner)(nil).TDigestRevRank), varargs...)
}

// TDigestTrimmedMean mocks base method.
func (m *MockPipeliner) TDigestTrimmedMean(arg0 context.Context, arg1 string, arg2, arg3 float64) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TDigestTrimmedMean", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// TDigestTrimmedMean indicates an expected call of TDigestTrimmedMean.
func (mr *MockPipelinerMockRecorder) TDigestTrimmedMean(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TDigestTrimmedMean", reflect.TypeOf((*MockPipeliner)(nil).TDigestTrimmedMean), arg0, arg1, arg2, arg3)
}

// TFCall mocks base method.
func (m *MockPipeliner) TFCall(arg0 context.Context, arg1, arg2 string, arg3 int) *redis.Cmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFCall", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// TFCall indicates an expected call of TFCall.
func (mr *MockPipelinerMockRecorder) TFCall(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFCall", reflect.TypeOf((*MockPipeliner)(nil).TFCall), arg0, arg1, arg2, arg3)
}

// TFCallASYNC mocks base method.
func (m *MockPipeliner) TFCallASYNC(arg0 context.Context, arg1, arg2 string, arg3 int) *redis.Cmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFCallASYNC", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// TFCallASYNC indicates an expected call of TFCallASYNC.
func (mr *MockPipelinerMockRecorder) TFCallASYNC(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFCallASYNC", reflect.TypeOf((*MockPipeliner)(nil).TFCallASYNC), arg0, arg1, arg2, arg3)
}

// TFCallASYNCArgs mocks base method.
func (m *MockPipeliner) TFCallASYNCArgs(arg0 context.Context, arg1, arg2 string, arg3 int, arg4 *redis.TFCallOptions) *redis.Cmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFCallASYNCArgs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// TFCallASYNCArgs indicates an expected call of TFCallASYNCArgs.
func (mr *MockPipelinerMockRecorder) TFCallASYNCArgs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFCallASYNCArgs", reflect.TypeOf((*MockPipeliner)(nil).TFCallASYNCArgs), arg0, arg1, arg2, arg3, arg4)
}

// TFCallArgs mocks base method.
func (m *MockPipeliner) TFCallArgs(arg0 context.Context, arg1, arg2 string, arg3 int, arg4 *redis.TFCallOptions) *redis.Cmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFCallArgs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.Cmd)
	return ret0
}

// TFCallArgs indicates an expected call of TFCallArgs.
func (mr *MockPipelinerMockRecorder) TFCallArgs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFCallArgs", reflect.TypeOf((*MockPipeliner)(nil).TFCallArgs), arg0, arg1, arg2, arg3, arg4)
}

// TFunctionDelete mocks base method.
func (m *MockPipeliner) TFunctionDelete(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFunctionDelete", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TFunctionDelete indicates an expected call of TFunctionDelete.
func (mr *MockPipelinerMockRecorder) TFunctionDelete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFunctionDelete", reflect.TypeOf((*MockPipeliner)(nil).TFunctionDelete), arg0, arg1)
}

// TFunctionList mocks base method.
func (m *MockPipeliner) TFunctionList(arg0 context.Context) *redis.MapStringInterfaceSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFunctionList", arg0)
	ret0, _ := ret[0].(*redis.MapStringInterfaceSliceCmd)
	return ret0
}

// TFunctionList indicates an expected call of TFunctionList.
func (mr *MockPipelinerMockRecorder) TFunctionList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFunctionList", reflect.TypeOf((*MockPipeliner)(nil).TFunctionList), arg0)
}

// TFunctionListArgs mocks base method.
func (m *MockPipeliner) TFunctionListArgs(arg0 context.Context, arg1 *redis.TFunctionListOptions) *redis.MapStringInterfaceSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFunctionListArgs", arg0, arg1)
	ret0, _ := ret[0].(*redis.MapStringInterfaceSliceCmd)
	return ret0
}

// TFunctionListArgs indicates an expected call of TFunctionListArgs.
func (mr *MockPipelinerMockRecorder) TFunctionListArgs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFunctionListArgs", reflect.TypeOf((*MockPipeliner)(nil).TFunctionListArgs), arg0, arg1)
}

// TFunctionLoad mocks base method.
func (m *MockPipeliner) TFunctionLoad(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFunctionLoad", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TFunctionLoad indicates an expected call of TFunctionLoad.
func (mr *MockPipelinerMockRecorder) TFunctionLoad(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFunctionLoad", reflect.TypeOf((*MockPipeliner)(nil).TFunctionLoad), arg0, arg1)
}

// TFunctionLoadArgs mocks base method.
func (m *MockPipeliner) TFunctionLoadArgs(arg0 context.Context, arg1 string, arg2 *redis.TFunctionLoadOptions) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TFunctionLoadArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TFunctionLoadArgs indicates an expected call of TFunctionLoadArgs.
func (mr *MockPipelinerMockRecorder) TFunctionLoadArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TFunctionLoadArgs", reflect.TypeOf((*MockPipeliner)(nil).TFunctionLoadArgs), arg0, arg1, arg2)
}

// TSAdd mocks base method.
func (m *MockPipeliner) TSAdd(arg0 context.Context, arg1 string, arg2 interface{}, arg3 float64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSAdd", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// TSAdd indicates an expected call of TSAdd.
func (mr *MockPipelinerMockRecorder) TSAdd(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSAdd", reflect.TypeOf((*MockPipeliner)(nil).TSAdd), arg0, arg1, arg2, arg3)
}

// TSAddWithArgs mocks base method.
func (m *MockPipeliner) TSAddWithArgs(arg0 context.Context, arg1 string, arg2 interface{}, arg3 float64, arg4 *redis.TSOptions) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSAddWithArgs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// TSAddWithArgs indicates an expected call of TSAddWithArgs.
func (mr *MockPipelinerMockRecorder) TSAddWithArgs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSAddWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSAddWithArgs), arg0, arg1, arg2, arg3, arg4)
}

// TSAlter mocks base method.
func (m *MockPipeliner) TSAlter(arg0 context.Context, arg1 string, arg2 *redis.TSAlterOptions) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSAlter", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TSAlter indicates an expected call of TSAlter.
func (mr *MockPipelinerMockRecorder) TSAlter(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSAlter", reflect.TypeOf((*MockPipeliner)(nil).TSAlter), arg0, arg1, arg2)
}

// TSCreate mocks base method.
func (m *MockPipeliner) TSCreate(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSCreate", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TSCreate indicates an expected call of TSCreate.
func (mr *MockPipelinerMockRecorder) TSCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSCreate", reflect.TypeOf((*MockPipeliner)(nil).TSCreate), arg0, arg1)
}

// TSCreateRule mocks base method.
func (m *MockPipeliner) TSCreateRule(arg0 context.Context, arg1, arg2 string, arg3 redis.Aggregator, arg4 int) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSCreateRule", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TSCreateRule indicates an expected call of TSCreateRule.
func (mr *MockPipelinerMockRecorder) TSCreateRule(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSCreateRule", reflect.TypeOf((*MockPipeliner)(nil).TSCreateRule), arg0, arg1, arg2, arg3, arg4)
}

// TSCreateRuleWithArgs mocks base method.
func (m *MockPipeliner) TSCreateRuleWithArgs(arg0 context.Context, arg1, arg2 string, arg3 redis.Aggregator, arg4 int, arg5 *redis.TSCreateRuleOptions) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSCreateRuleWithArgs", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TSCreateRuleWithArgs indicates an expected call of TSCreateRuleWithArgs.
func (mr *MockPipelinerMockRecorder) TSCreateRuleWithArgs(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSCreateRuleWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSCreateRuleWithArgs), arg0, arg1, arg2, arg3, arg4, arg5)
}

// TSCreateWithArgs mocks base method.
func (m *MockPipeliner) TSCreateWithArgs(arg0 context.Context, arg1 string, arg2 *redis.TSOptions) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSCreateWithArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TSCreateWithArgs indicates an expected call of TSCreateWithArgs.
func (mr *MockPipelinerMockRecorder) TSCreateWithArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSCreateWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSCreateWithArgs), arg0, arg1, arg2)
}

// TSDecrBy mocks base method.
func (m *MockPipeliner) TSDecrBy(arg0 context.Context, arg1 string, arg2 float64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSDecrBy", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// TSDecrBy indicates an expected call of TSDecrBy.
func (mr *MockPipelinerMockRecorder) TSDecrBy(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSDecrBy", reflect.TypeOf((*MockPipeliner)(nil).TSDecrBy), arg0, arg1, arg2)
}

// TSDecrByWithArgs mocks base method.
func (m *MockPipeliner) TSDecrByWithArgs(arg0 context.Context, arg1 string, arg2 float64, arg3 *redis.TSIncrDecrOptions) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSDecrByWithArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// TSDecrByWithArgs indicates an expected call of TSDecrByWithArgs.
func (mr *MockPipelinerMockRecorder) TSDecrByWithArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSDecrByWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSDecrByWithArgs), arg0, arg1, arg2, arg3)
}

// TSDel mocks base method.
func (m *MockPipeliner) TSDel(arg0 context.Context, arg1 string, arg2, arg3 int) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSDel", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// TSDel indicates an expected call of TSDel.
func (mr *MockPipelinerMockRecorder) TSDel(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSDel", reflect.TypeOf((*MockPipeliner)(nil).TSDel), arg0, arg1, arg2, arg3)
}

// TSDeleteRule mocks base method.
func (m *MockPipeliner) TSDeleteRule(arg0 context.Context, arg1, arg2 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSDeleteRule", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TSDeleteRule indicates an expected call of TSDeleteRule.
func (mr *MockPipelinerMockRecorder) TSDeleteRule(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSDeleteRule", reflect.TypeOf((*MockPipeliner)(nil).TSDeleteRule), arg0, arg1, arg2)
}

// TSGet mocks base method.
func (m *MockPipeliner) TSGet(arg0 context.Context, arg1 string) *redis.TSTimestampValueCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSGet", arg0, arg1)
	ret0, _ := ret[0].(*redis.TSTimestampValueCmd)
	return ret0
}

// TSGet indicates an expected call of TSGet.
func (mr *MockPipelinerMockRecorder) TSGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSGet", reflect.TypeOf((*MockPipeliner)(nil).TSGet), arg0, arg1)
}

// TSGetWithArgs mocks base method.
func (m *MockPipeliner) TSGetWithArgs(arg0 context.Context, arg1 string, arg2 *redis.TSGetOptions) *redis.TSTimestampValueCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSGetWithArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.TSTimestampValueCmd)
	return ret0
}

// TSGetWithArgs indicates an expected call of TSGetWithArgs.
func (mr *MockPipelinerMockRecorder) TSGetWithArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSGetWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSGetWithArgs), arg0, arg1, arg2)
}

// TSIncrBy mocks base method.
func (m *MockPipeliner) TSIncrBy(arg0 context.Context, arg1 string, arg2 float64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSIncrBy", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// TSIncrBy indicates an expected call of TSIncrBy.
func (mr *MockPipelinerMockRecorder) TSIncrBy(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSIncrBy", reflect.TypeOf((*MockPipeliner)(nil).TSIncrBy), arg0, arg1, arg2)
}

// TSIncrByWithArgs mocks base method.
func (m *MockPipeliner) TSIncrByWithArgs(arg0 context.Context, arg1 string, arg2 float64, arg3 *redis.TSIncrDecrOptions) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSIncrByWithArgs", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// TSIncrByWithArgs indicates an expected call of TSIncrByWithArgs.
func (mr *MockPipelinerMockRecorder) TSIncrByWithArgs(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSIncrByWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSIncrByWithArgs), arg0, arg1, arg2, arg3)
}

// TSInfo mocks base method.
func (m *MockPipeliner) TSInfo(arg0 context.Context, arg1 string) *redis.MapStringInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSInfo", arg0, arg1)
	ret0, _ := ret[0].(*redis.MapStringInterfaceCmd)
	return ret0
}

// TSInfo indicates an expected call of TSInfo.
func (mr *MockPipelinerMockRecorder) TSInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSInfo", reflect.TypeOf((*MockPipeliner)(nil).TSInfo), arg0, arg1)
}

// TSInfoWithArgs mocks base method.
func (m *MockPipeliner) TSInfoWithArgs(arg0 context.Context, arg1 string, arg2 *redis.TSInfoOptions) *redis.MapStringInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSInfoWithArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.MapStringInterfaceCmd)
	return ret0
}

// TSInfoWithArgs indicates an expected call of TSInfoWithArgs.
func (mr *MockPipelinerMockRecorder) TSInfoWithArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSInfoWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSInfoWithArgs), arg0, arg1, arg2)
}

// TSMAdd mocks base method.
func (m *MockPipeliner) TSMAdd(arg0 context.Context, arg1 [][]interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSMAdd", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// TSMAdd indicates an expected call of TSMAdd.
func (mr *MockPipelinerMockRecorder) TSMAdd(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSMAdd", reflect.TypeOf((*MockPipeliner)(nil).TSMAdd), arg0, arg1)
}

// TSMGet mocks base method.
func (m *MockPipeliner) TSMGet(arg0 context.Context, arg1 []string) *redis.MapStringSliceInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSMGet", arg0, arg1)
	ret0, _ := ret[0].(*redis.MapStringSliceInterfaceCmd)
	return ret0
}

// TSMGet indicates an expected call of TSMGet.
func (mr *MockPipelinerMockRecorder) TSMGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSMGet", reflect.TypeOf((*MockPipeliner)(nil).TSMGet), arg0, arg1)
}

// TSMGetWithArgs mocks base method.
func (m *MockPipeliner) TSMGetWithArgs(arg0 context.Context, arg1 []string, arg2 *redis.TSMGetOptions) *redis.MapStringSliceInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSMGetWithArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.MapStringSliceInterfaceCmd)
	return ret0
}

// TSMGetWithArgs indicates an expected call of TSMGetWithArgs.
func (mr *MockPipelinerMockRecorder) TSMGetWithArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSMGetWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSMGetWithArgs), arg0, arg1, arg2)
}

// TSMRange mocks base method.
func (m *MockPipeliner) TSMRange(arg0 context.Context, arg1, arg2 int, arg3 []string) *redis.MapStringSliceInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSMRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.MapStringSliceInterfaceCmd)
	return ret0
}

// TSMRange indicates an expected call of TSMRange.
func (mr *MockPipelinerMockRecorder) TSMRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSMRange", reflect.TypeOf((*MockPipeliner)(nil).TSMRange), arg0, arg1, arg2, arg3)
}

// TSMRangeWithArgs mocks base method.
func (m *MockPipeliner) TSMRangeWithArgs(arg0 context.Context, arg1, arg2 int, arg3 []string, arg4 *redis.TSMRangeOptions) *redis.MapStringSliceInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSMRangeWithArgs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.MapStringSliceInterfaceCmd)
	return ret0
}

// TSMRangeWithArgs indicates an expected call of TSMRangeWithArgs.
func (mr *MockPipelinerMockRecorder) TSMRangeWithArgs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSMRangeWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSMRangeWithArgs), arg0, arg1, arg2, arg3, arg4)
}

// TSMRevRange mocks base method.
func (m *MockPipeliner) TSMRevRange(arg0 context.Context, arg1, arg2 int, arg3 []string) *redis.MapStringSliceInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSMRevRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.MapStringSliceInterfaceCmd)
	return ret0
}

// TSMRevRange indicates an expected call of TSMRevRange.
func (mr *MockPipelinerMockRecorder) TSMRevRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSMRevRange", reflect.TypeOf((*MockPipeliner)(nil).TSMRevRange), arg0, arg1, arg2, arg3)
}

// TSMRevRangeWithArgs mocks base method.
func (m *MockPipeliner) TSMRevRangeWithArgs(arg0 context.Context, arg1, arg2 int, arg3 []string, arg4 *redis.TSMRevRangeOptions) *redis.MapStringSliceInterfaceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSMRevRangeWithArgs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.MapStringSliceInterfaceCmd)
	return ret0
}

// TSMRevRangeWithArgs indicates an expected call of TSMRevRangeWithArgs.
func (mr *MockPipelinerMockRecorder) TSMRevRangeWithArgs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSMRevRangeWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSMRevRangeWithArgs), arg0, arg1, arg2, arg3, arg4)
}

// TSQueryIndex mocks base method.
func (m *MockPipeliner) TSQueryIndex(arg0 context.Context, arg1 []string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSQueryIndex", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// TSQueryIndex indicates an expected call of TSQueryIndex.
func (mr *MockPipelinerMockRecorder) TSQueryIndex(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSQueryIndex", reflect.TypeOf((*MockPipeliner)(nil).TSQueryIndex), arg0, arg1)
}

// TSRange mocks base method.
func (m *MockPipeliner) TSRange(arg0 context.Context, arg1 string, arg2, arg3 int) *redis.TSTimestampValueSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.TSTimestampValueSliceCmd)
	return ret0
}

// TSRange indicates an expected call of TSRange.
func (mr *MockPipelinerMockRecorder) TSRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSRange", reflect.TypeOf((*MockPipeliner)(nil).TSRange), arg0, arg1, arg2, arg3)
}

// TSRangeWithArgs mocks base method.
func (m *MockPipeliner) TSRangeWithArgs(arg0 context.Context, arg1 string, arg2, arg3 int, arg4 *redis.TSRangeOptions) *redis.TSTimestampValueSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSRangeWithArgs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.TSTimestampValueSliceCmd)
	return ret0
}

// TSRangeWithArgs indicates an expected call of TSRangeWithArgs.
func (mr *MockPipelinerMockRecorder) TSRangeWithArgs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSRangeWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSRangeWithArgs), arg0, arg1, arg2, arg3, arg4)
}

// TSRevRange mocks base method.
func (m *MockPipeliner) TSRevRange(arg0 context.Context, arg1 string, arg2, arg3 int) *redis.TSTimestampValueSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSRevRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.TSTimestampValueSliceCmd)
	return ret0
}

// TSRevRange indicates an expected call of TSRevRange.
func (mr *MockPipelinerMockRecorder) TSRevRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSRevRange", reflect.TypeOf((*MockPipeliner)(nil).TSRevRange), arg0, arg1, arg2, arg3)
}

// TSRevRangeWithArgs mocks base method.
func (m *MockPipeliner) TSRevRangeWithArgs(arg0 context.Context, arg1 string, arg2, arg3 int, arg4 *redis.TSRevRangeOptions) *redis.TSTimestampValueSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TSRevRangeWithArgs", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.TSTimestampValueSliceCmd)
	return ret0
}

// TSRevRangeWithArgs indicates an expected call of TSRevRangeWithArgs.
func (mr *MockPipelinerMockRecorder) TSRevRangeWithArgs(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TSRevRangeWithArgs", reflect.TypeOf((*MockPipeliner)(nil).TSRevRangeWithArgs), arg0, arg1, arg2, arg3, arg4)
}

// TTL mocks base method.
func (m *MockPipeliner) TTL(arg0 context.Context, arg1 string) *redis.DurationCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TTL", arg0, arg1)
	ret0, _ := ret[0].(*redis.DurationCmd)
	return ret0
}

// TTL indicates an expected call of TTL.
func (mr *MockPipelinerMockRecorder) TTL(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TTL", reflect.TypeOf((*MockPipeliner)(nil).TTL), arg0, arg1)
}

// Time mocks base method.
func (m *MockPipeliner) Time(arg0 context.Context) *redis.TimeCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Time", arg0)
	ret0, _ := ret[0].(*redis.TimeCmd)
	return ret0
}

// Time indicates an expected call of Time.
func (mr *MockPipelinerMockRecorder) Time(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Time", reflect.TypeOf((*MockPipeliner)(nil).Time), arg0)
}

// TopKAdd mocks base method.
func (m *MockPipeliner) TopKAdd(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TopKAdd", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// TopKAdd indicates an expected call of TopKAdd.
func (mr *MockPipelinerMockRecorder) TopKAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKAdd", reflect.TypeOf((*MockPipeliner)(nil).TopKAdd), varargs...)
}

// TopKCount mocks base method.
func (m *MockPipeliner) TopKCount(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TopKCount", varargs...)
	ret0, _ := ret[0].(*redis.IntSliceCmd)
	return ret0
}

// TopKCount indicates an expected call of TopKCount.
func (mr *MockPipelinerMockRecorder) TopKCount(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKCount", reflect.TypeOf((*MockPipeliner)(nil).TopKCount), varargs...)
}

// TopKIncrBy mocks base method.
func (m *MockPipeliner) TopKIncrBy(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TopKIncrBy", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// TopKIncrBy indicates an expected call of TopKIncrBy.
func (mr *MockPipelinerMockRecorder) TopKIncrBy(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKIncrBy", reflect.TypeOf((*MockPipeliner)(nil).TopKIncrBy), varargs...)
}

// TopKInfo mocks base method.
func (m *MockPipeliner) TopKInfo(arg0 context.Context, arg1 string) *redis.TopKInfoCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TopKInfo", arg0, arg1)
	ret0, _ := ret[0].(*redis.TopKInfoCmd)
	return ret0
}

// TopKInfo indicates an expected call of TopKInfo.
func (mr *MockPipelinerMockRecorder) TopKInfo(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKInfo", reflect.TypeOf((*MockPipeliner)(nil).TopKInfo), arg0, arg1)
}

// TopKList mocks base method.
func (m *MockPipeliner) TopKList(arg0 context.Context, arg1 string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TopKList", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// TopKList indicates an expected call of TopKList.
func (mr *MockPipelinerMockRecorder) TopKList(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKList", reflect.TypeOf((*MockPipeliner)(nil).TopKList), arg0, arg1)
}

// TopKListWithCount mocks base method.
func (m *MockPipeliner) TopKListWithCount(arg0 context.Context, arg1 string) *redis.MapStringIntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TopKListWithCount", arg0, arg1)
	ret0, _ := ret[0].(*redis.MapStringIntCmd)
	return ret0
}

// TopKListWithCount indicates an expected call of TopKListWithCount.
func (mr *MockPipelinerMockRecorder) TopKListWithCount(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKListWithCount", reflect.TypeOf((*MockPipeliner)(nil).TopKListWithCount), arg0, arg1)
}

// TopKQuery mocks base method.
func (m *MockPipeliner) TopKQuery(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.BoolSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TopKQuery", varargs...)
	ret0, _ := ret[0].(*redis.BoolSliceCmd)
	return ret0
}

// TopKQuery indicates an expected call of TopKQuery.
func (mr *MockPipelinerMockRecorder) TopKQuery(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKQuery", reflect.TypeOf((*MockPipeliner)(nil).TopKQuery), varargs...)
}

// TopKReserve mocks base method.
func (m *MockPipeliner) TopKReserve(arg0 context.Context, arg1 string, arg2 int64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TopKReserve", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TopKReserve indicates an expected call of TopKReserve.
func (mr *MockPipelinerMockRecorder) TopKReserve(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKReserve", reflect.TypeOf((*MockPipeliner)(nil).TopKReserve), arg0, arg1, arg2)
}

// TopKReserveWithOptions mocks base method.
func (m *MockPipeliner) TopKReserveWithOptions(arg0 context.Context, arg1 string, arg2, arg3, arg4 int64, arg5 float64) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TopKReserveWithOptions", arg0, arg1, arg2, arg3, arg4, arg5)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// TopKReserveWithOptions indicates an expected call of TopKReserveWithOptions.
func (mr *MockPipelinerMockRecorder) TopKReserveWithOptions(arg0, arg1, arg2, arg3, arg4, arg5 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TopKReserveWithOptions", reflect.TypeOf((*MockPipeliner)(nil).TopKReserveWithOptions), arg0, arg1, arg2, arg3, arg4, arg5)
}

// Touch mocks base method.
func (m *MockPipeliner) Touch(arg0 context.Context, arg1 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Touch", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Touch indicates an expected call of Touch.
func (mr *MockPipelinerMockRecorder) Touch(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Touch", reflect.TypeOf((*MockPipeliner)(nil).Touch), varargs...)
}

// TxPipeline mocks base method.
func (m *MockPipeliner) TxPipeline() redis.Pipeliner {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TxPipeline")
	ret0, _ := ret[0].(redis.Pipeliner)
	return ret0
}

// TxPipeline indicates an expected call of TxPipeline.
func (mr *MockPipelinerMockRecorder) TxPipeline() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TxPipeline", reflect.TypeOf((*MockPipeliner)(nil).TxPipeline))
}

// TxPipelined mocks base method.
func (m *MockPipeliner) TxPipelined(arg0 context.Context, arg1 func(redis.Pipeliner) error) ([]redis.Cmder, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TxPipelined", arg0, arg1)
	ret0, _ := ret[0].([]redis.Cmder)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TxPipelined indicates an expected call of TxPipelined.
func (mr *MockPipelinerMockRecorder) TxPipelined(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TxPipelined", reflect.TypeOf((*MockPipeliner)(nil).TxPipelined), arg0, arg1)
}

// Type mocks base method.
func (m *MockPipeliner) Type(arg0 context.Context, arg1 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Type", arg0, arg1)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// Type indicates an expected call of Type.
func (mr *MockPipelinerMockRecorder) Type(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Type", reflect.TypeOf((*MockPipeliner)(nil).Type), arg0, arg1)
}

// Unlink mocks base method.
func (m *MockPipeliner) Unlink(arg0 context.Context, arg1 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Unlink", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// Unlink indicates an expected call of Unlink.
func (mr *MockPipelinerMockRecorder) Unlink(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlink", reflect.TypeOf((*MockPipeliner)(nil).Unlink), varargs...)
}

// XAck mocks base method.
func (m *MockPipeliner) XAck(arg0 context.Context, arg1, arg2 string, arg3 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "XAck", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XAck indicates an expected call of XAck.
func (mr *MockPipelinerMockRecorder) XAck(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XAck", reflect.TypeOf((*MockPipeliner)(nil).XAck), varargs...)
}

// XAdd mocks base method.
func (m *MockPipeliner) XAdd(arg0 context.Context, arg1 *redis.XAddArgs) *redis.StringCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XAdd", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringCmd)
	return ret0
}

// XAdd indicates an expected call of XAdd.
func (mr *MockPipelinerMockRecorder) XAdd(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XAdd", reflect.TypeOf((*MockPipeliner)(nil).XAdd), arg0, arg1)
}

// XAutoClaim mocks base method.
func (m *MockPipeliner) XAutoClaim(arg0 context.Context, arg1 *redis.XAutoClaimArgs) *redis.XAutoClaimCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XAutoClaim", arg0, arg1)
	ret0, _ := ret[0].(*redis.XAutoClaimCmd)
	return ret0
}

// XAutoClaim indicates an expected call of XAutoClaim.
func (mr *MockPipelinerMockRecorder) XAutoClaim(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XAutoClaim", reflect.TypeOf((*MockPipeliner)(nil).XAutoClaim), arg0, arg1)
}

// XAutoClaimJustID mocks base method.
func (m *MockPipeliner) XAutoClaimJustID(arg0 context.Context, arg1 *redis.XAutoClaimArgs) *redis.XAutoClaimJustIDCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XAutoClaimJustID", arg0, arg1)
	ret0, _ := ret[0].(*redis.XAutoClaimJustIDCmd)
	return ret0
}

// XAutoClaimJustID indicates an expected call of XAutoClaimJustID.
func (mr *MockPipelinerMockRecorder) XAutoClaimJustID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XAutoClaimJustID", reflect.TypeOf((*MockPipeliner)(nil).XAutoClaimJustID), arg0, arg1)
}

// XClaim mocks base method.
func (m *MockPipeliner) XClaim(arg0 context.Context, arg1 *redis.XClaimArgs) *redis.XMessageSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XClaim", arg0, arg1)
	ret0, _ := ret[0].(*redis.XMessageSliceCmd)
	return ret0
}

// XClaim indicates an expected call of XClaim.
func (mr *MockPipelinerMockRecorder) XClaim(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XClaim", reflect.TypeOf((*MockPipeliner)(nil).XClaim), arg0, arg1)
}

// XClaimJustID mocks base method.
func (m *MockPipeliner) XClaimJustID(arg0 context.Context, arg1 *redis.XClaimArgs) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XClaimJustID", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// XClaimJustID indicates an expected call of XClaimJustID.
func (mr *MockPipelinerMockRecorder) XClaimJustID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XClaimJustID", reflect.TypeOf((*MockPipeliner)(nil).XClaimJustID), arg0, arg1)
}

// XDel mocks base method.
func (m *MockPipeliner) XDel(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "XDel", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XDel indicates an expected call of XDel.
func (mr *MockPipelinerMockRecorder) XDel(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XDel", reflect.TypeOf((*MockPipeliner)(nil).XDel), varargs...)
}

// XGroupCreate mocks base method.
func (m *MockPipeliner) XGroupCreate(arg0 context.Context, arg1, arg2, arg3 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XGroupCreate", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// XGroupCreate indicates an expected call of XGroupCreate.
func (mr *MockPipelinerMockRecorder) XGroupCreate(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XGroupCreate", reflect.TypeOf((*MockPipeliner)(nil).XGroupCreate), arg0, arg1, arg2, arg3)
}

// XGroupCreateConsumer mocks base method.
func (m *MockPipeliner) XGroupCreateConsumer(arg0 context.Context, arg1, arg2, arg3 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XGroupCreateConsumer", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XGroupCreateConsumer indicates an expected call of XGroupCreateConsumer.
func (mr *MockPipelinerMockRecorder) XGroupCreateConsumer(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XGroupCreateConsumer", reflect.TypeOf((*MockPipeliner)(nil).XGroupCreateConsumer), arg0, arg1, arg2, arg3)
}

// XGroupCreateMkStream mocks base method.
func (m *MockPipeliner) XGroupCreateMkStream(arg0 context.Context, arg1, arg2, arg3 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XGroupCreateMkStream", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// XGroupCreateMkStream indicates an expected call of XGroupCreateMkStream.
func (mr *MockPipelinerMockRecorder) XGroupCreateMkStream(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XGroupCreateMkStream", reflect.TypeOf((*MockPipeliner)(nil).XGroupCreateMkStream), arg0, arg1, arg2, arg3)
}

// XGroupDelConsumer mocks base method.
func (m *MockPipeliner) XGroupDelConsumer(arg0 context.Context, arg1, arg2, arg3 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XGroupDelConsumer", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XGroupDelConsumer indicates an expected call of XGroupDelConsumer.
func (mr *MockPipelinerMockRecorder) XGroupDelConsumer(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XGroupDelConsumer", reflect.TypeOf((*MockPipeliner)(nil).XGroupDelConsumer), arg0, arg1, arg2, arg3)
}

// XGroupDestroy mocks base method.
func (m *MockPipeliner) XGroupDestroy(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XGroupDestroy", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XGroupDestroy indicates an expected call of XGroupDestroy.
func (mr *MockPipelinerMockRecorder) XGroupDestroy(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XGroupDestroy", reflect.TypeOf((*MockPipeliner)(nil).XGroupDestroy), arg0, arg1, arg2)
}

// XGroupSetID mocks base method.
func (m *MockPipeliner) XGroupSetID(arg0 context.Context, arg1, arg2, arg3 string) *redis.StatusCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XGroupSetID", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StatusCmd)
	return ret0
}

// XGroupSetID indicates an expected call of XGroupSetID.
func (mr *MockPipelinerMockRecorder) XGroupSetID(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XGroupSetID", reflect.TypeOf((*MockPipeliner)(nil).XGroupSetID), arg0, arg1, arg2, arg3)
}

// XInfoConsumers mocks base method.
func (m *MockPipeliner) XInfoConsumers(arg0 context.Context, arg1, arg2 string) *redis.XInfoConsumersCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XInfoConsumers", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.XInfoConsumersCmd)
	return ret0
}

// XInfoConsumers indicates an expected call of XInfoConsumers.
func (mr *MockPipelinerMockRecorder) XInfoConsumers(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XInfoConsumers", reflect.TypeOf((*MockPipeliner)(nil).XInfoConsumers), arg0, arg1, arg2)
}

// XInfoGroups mocks base method.
func (m *MockPipeliner) XInfoGroups(arg0 context.Context, arg1 string) *redis.XInfoGroupsCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XInfoGroups", arg0, arg1)
	ret0, _ := ret[0].(*redis.XInfoGroupsCmd)
	return ret0
}

// XInfoGroups indicates an expected call of XInfoGroups.
func (mr *MockPipelinerMockRecorder) XInfoGroups(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XInfoGroups", reflect.TypeOf((*MockPipeliner)(nil).XInfoGroups), arg0, arg1)
}

// XInfoStream mocks base method.
func (m *MockPipeliner) XInfoStream(arg0 context.Context, arg1 string) *redis.XInfoStreamCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XInfoStream", arg0, arg1)
	ret0, _ := ret[0].(*redis.XInfoStreamCmd)
	return ret0
}

// XInfoStream indicates an expected call of XInfoStream.
func (mr *MockPipelinerMockRecorder) XInfoStream(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XInfoStream", reflect.TypeOf((*MockPipeliner)(nil).XInfoStream), arg0, arg1)
}

// XInfoStreamFull mocks base method.
func (m *MockPipeliner) XInfoStreamFull(arg0 context.Context, arg1 string, arg2 int) *redis.XInfoStreamFullCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XInfoStreamFull", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.XInfoStreamFullCmd)
	return ret0
}

// XInfoStreamFull indicates an expected call of XInfoStreamFull.
func (mr *MockPipelinerMockRecorder) XInfoStreamFull(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XInfoStreamFull", reflect.TypeOf((*MockPipeliner)(nil).XInfoStreamFull), arg0, arg1, arg2)
}

// XLen mocks base method.
func (m *MockPipeliner) XLen(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XLen", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XLen indicates an expected call of XLen.
func (mr *MockPipelinerMockRecorder) XLen(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XLen", reflect.TypeOf((*MockPipeliner)(nil).XLen), arg0, arg1)
}

// XPending mocks base method.
func (m *MockPipeliner) XPending(arg0 context.Context, arg1, arg2 string) *redis.XPendingCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XPending", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.XPendingCmd)
	return ret0
}

// XPending indicates an expected call of XPending.
func (mr *MockPipelinerMockRecorder) XPending(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XPending", reflect.TypeOf((*MockPipeliner)(nil).XPending), arg0, arg1, arg2)
}

// XPendingExt mocks base method.
func (m *MockPipeliner) XPendingExt(arg0 context.Context, arg1 *redis.XPendingExtArgs) *redis.XPendingExtCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XPendingExt", arg0, arg1)
	ret0, _ := ret[0].(*redis.XPendingExtCmd)
	return ret0
}

// XPendingExt indicates an expected call of XPendingExt.
func (mr *MockPipelinerMockRecorder) XPendingExt(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XPendingExt", reflect.TypeOf((*MockPipeliner)(nil).XPendingExt), arg0, arg1)
}

// XRange mocks base method.
func (m *MockPipeliner) XRange(arg0 context.Context, arg1, arg2, arg3 string) *redis.XMessageSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.XMessageSliceCmd)
	return ret0
}

// XRange indicates an expected call of XRange.
func (mr *MockPipelinerMockRecorder) XRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XRange", reflect.TypeOf((*MockPipeliner)(nil).XRange), arg0, arg1, arg2, arg3)
}

// XRangeN mocks base method.
func (m *MockPipeliner) XRangeN(arg0 context.Context, arg1, arg2, arg3 string, arg4 int64) *redis.XMessageSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XRangeN", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.XMessageSliceCmd)
	return ret0
}

// XRangeN indicates an expected call of XRangeN.
func (mr *MockPipelinerMockRecorder) XRangeN(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XRangeN", reflect.TypeOf((*MockPipeliner)(nil).XRangeN), arg0, arg1, arg2, arg3, arg4)
}

// XRead mocks base method.
func (m *MockPipeliner) XRead(arg0 context.Context, arg1 *redis.XReadArgs) *redis.XStreamSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XRead", arg0, arg1)
	ret0, _ := ret[0].(*redis.XStreamSliceCmd)
	return ret0
}

// XRead indicates an expected call of XRead.
func (mr *MockPipelinerMockRecorder) XRead(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XRead", reflect.TypeOf((*MockPipeliner)(nil).XRead), arg0, arg1)
}

// XReadGroup mocks base method.
func (m *MockPipeliner) XReadGroup(arg0 context.Context, arg1 *redis.XReadGroupArgs) *redis.XStreamSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XReadGroup", arg0, arg1)
	ret0, _ := ret[0].(*redis.XStreamSliceCmd)
	return ret0
}

// XReadGroup indicates an expected call of XReadGroup.
func (mr *MockPipelinerMockRecorder) XReadGroup(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XReadGroup", reflect.TypeOf((*MockPipeliner)(nil).XReadGroup), arg0, arg1)
}

// XReadStreams mocks base method.
func (m *MockPipeliner) XReadStreams(arg0 context.Context, arg1 ...string) *redis.XStreamSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "XReadStreams", varargs...)
	ret0, _ := ret[0].(*redis.XStreamSliceCmd)
	return ret0
}

// XReadStreams indicates an expected call of XReadStreams.
func (mr *MockPipelinerMockRecorder) XReadStreams(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XReadStreams", reflect.TypeOf((*MockPipeliner)(nil).XReadStreams), varargs...)
}

// XRevRange mocks base method.
func (m *MockPipeliner) XRevRange(arg0 context.Context, arg1, arg2, arg3 string) *redis.XMessageSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XRevRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.XMessageSliceCmd)
	return ret0
}

// XRevRange indicates an expected call of XRevRange.
func (mr *MockPipelinerMockRecorder) XRevRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XRevRange", reflect.TypeOf((*MockPipeliner)(nil).XRevRange), arg0, arg1, arg2, arg3)
}

// XRevRangeN mocks base method.
func (m *MockPipeliner) XRevRangeN(arg0 context.Context, arg1, arg2, arg3 string, arg4 int64) *redis.XMessageSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XRevRangeN", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.XMessageSliceCmd)
	return ret0
}

// XRevRangeN indicates an expected call of XRevRangeN.
func (mr *MockPipelinerMockRecorder) XRevRangeN(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XRevRangeN", reflect.TypeOf((*MockPipeliner)(nil).XRevRangeN), arg0, arg1, arg2, arg3, arg4)
}

// XTrimMaxLen mocks base method.
func (m *MockPipeliner) XTrimMaxLen(arg0 context.Context, arg1 string, arg2 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XTrimMaxLen", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XTrimMaxLen indicates an expected call of XTrimMaxLen.
func (mr *MockPipelinerMockRecorder) XTrimMaxLen(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XTrimMaxLen", reflect.TypeOf((*MockPipeliner)(nil).XTrimMaxLen), arg0, arg1, arg2)
}

// XTrimMaxLenApprox mocks base method.
func (m *MockPipeliner) XTrimMaxLenApprox(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XTrimMaxLenApprox", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XTrimMaxLenApprox indicates an expected call of XTrimMaxLenApprox.
func (mr *MockPipelinerMockRecorder) XTrimMaxLenApprox(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XTrimMaxLenApprox", reflect.TypeOf((*MockPipeliner)(nil).XTrimMaxLenApprox), arg0, arg1, arg2, arg3)
}

// XTrimMinID mocks base method.
func (m *MockPipeliner) XTrimMinID(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XTrimMinID", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XTrimMinID indicates an expected call of XTrimMinID.
func (mr *MockPipelinerMockRecorder) XTrimMinID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XTrimMinID", reflect.TypeOf((*MockPipeliner)(nil).XTrimMinID), arg0, arg1, arg2)
}

// XTrimMinIDApprox mocks base method.
func (m *MockPipeliner) XTrimMinIDApprox(arg0 context.Context, arg1, arg2 string, arg3 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "XTrimMinIDApprox", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// XTrimMinIDApprox indicates an expected call of XTrimMinIDApprox.
func (mr *MockPipelinerMockRecorder) XTrimMinIDApprox(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "XTrimMinIDApprox", reflect.TypeOf((*MockPipeliner)(nil).XTrimMinIDApprox), arg0, arg1, arg2, arg3)
}

// ZAdd mocks base method.
func (m *MockPipeliner) ZAdd(arg0 context.Context, arg1 string, arg2 ...redis.Z) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZAdd", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZAdd indicates an expected call of ZAdd.
func (mr *MockPipelinerMockRecorder) ZAdd(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZAdd", reflect.TypeOf((*MockPipeliner)(nil).ZAdd), varargs...)
}

// ZAddArgs mocks base method.
func (m *MockPipeliner) ZAddArgs(arg0 context.Context, arg1 string, arg2 redis.ZAddArgs) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZAddArgs", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZAddArgs indicates an expected call of ZAddArgs.
func (mr *MockPipelinerMockRecorder) ZAddArgs(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZAddArgs", reflect.TypeOf((*MockPipeliner)(nil).ZAddArgs), arg0, arg1, arg2)
}

// ZAddArgsIncr mocks base method.
func (m *MockPipeliner) ZAddArgsIncr(arg0 context.Context, arg1 string, arg2 redis.ZAddArgs) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZAddArgsIncr", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// ZAddArgsIncr indicates an expected call of ZAddArgsIncr.
func (mr *MockPipelinerMockRecorder) ZAddArgsIncr(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZAddArgsIncr", reflect.TypeOf((*MockPipeliner)(nil).ZAddArgsIncr), arg0, arg1, arg2)
}

// ZAddGT mocks base method.
func (m *MockPipeliner) ZAddGT(arg0 context.Context, arg1 string, arg2 ...redis.Z) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZAddGT", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZAddGT indicates an expected call of ZAddGT.
func (mr *MockPipelinerMockRecorder) ZAddGT(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZAddGT", reflect.TypeOf((*MockPipeliner)(nil).ZAddGT), varargs...)
}

// ZAddLT mocks base method.
func (m *MockPipeliner) ZAddLT(arg0 context.Context, arg1 string, arg2 ...redis.Z) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZAddLT", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZAddLT indicates an expected call of ZAddLT.
func (mr *MockPipelinerMockRecorder) ZAddLT(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZAddLT", reflect.TypeOf((*MockPipeliner)(nil).ZAddLT), varargs...)
}

// ZAddNX mocks base method.
func (m *MockPipeliner) ZAddNX(arg0 context.Context, arg1 string, arg2 ...redis.Z) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZAddNX", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZAddNX indicates an expected call of ZAddNX.
func (mr *MockPipelinerMockRecorder) ZAddNX(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZAddNX", reflect.TypeOf((*MockPipeliner)(nil).ZAddNX), varargs...)
}

// ZAddXX mocks base method.
func (m *MockPipeliner) ZAddXX(arg0 context.Context, arg1 string, arg2 ...redis.Z) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZAddXX", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZAddXX indicates an expected call of ZAddXX.
func (mr *MockPipelinerMockRecorder) ZAddXX(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZAddXX", reflect.TypeOf((*MockPipeliner)(nil).ZAddXX), varargs...)
}

// ZCard mocks base method.
func (m *MockPipeliner) ZCard(arg0 context.Context, arg1 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZCard", arg0, arg1)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZCard indicates an expected call of ZCard.
func (mr *MockPipelinerMockRecorder) ZCard(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZCard", reflect.TypeOf((*MockPipeliner)(nil).ZCard), arg0, arg1)
}

// ZCount mocks base method.
func (m *MockPipeliner) ZCount(arg0 context.Context, arg1, arg2, arg3 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZCount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZCount indicates an expected call of ZCount.
func (mr *MockPipelinerMockRecorder) ZCount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZCount", reflect.TypeOf((*MockPipeliner)(nil).ZCount), arg0, arg1, arg2, arg3)
}

// ZDiff mocks base method.
func (m *MockPipeliner) ZDiff(arg0 context.Context, arg1 ...string) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZDiff", varargs...)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZDiff indicates an expected call of ZDiff.
func (mr *MockPipelinerMockRecorder) ZDiff(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZDiff", reflect.TypeOf((*MockPipeliner)(nil).ZDiff), varargs...)
}

// ZDiffStore mocks base method.
func (m *MockPipeliner) ZDiffStore(arg0 context.Context, arg1 string, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZDiffStore", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZDiffStore indicates an expected call of ZDiffStore.
func (mr *MockPipelinerMockRecorder) ZDiffStore(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZDiffStore", reflect.TypeOf((*MockPipeliner)(nil).ZDiffStore), varargs...)
}

// ZDiffWithScores mocks base method.
func (m *MockPipeliner) ZDiffWithScores(arg0 context.Context, arg1 ...string) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0}
	for _, a := range arg1 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZDiffWithScores", varargs...)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZDiffWithScores indicates an expected call of ZDiffWithScores.
func (mr *MockPipelinerMockRecorder) ZDiffWithScores(arg0 interface{}, arg1 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0}, arg1...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZDiffWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZDiffWithScores), varargs...)
}

// ZIncrBy mocks base method.
func (m *MockPipeliner) ZIncrBy(arg0 context.Context, arg1 string, arg2 float64, arg3 string) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZIncrBy", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// ZIncrBy indicates an expected call of ZIncrBy.
func (mr *MockPipelinerMockRecorder) ZIncrBy(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZIncrBy", reflect.TypeOf((*MockPipeliner)(nil).ZIncrBy), arg0, arg1, arg2, arg3)
}

// ZInter mocks base method.
func (m *MockPipeliner) ZInter(arg0 context.Context, arg1 *redis.ZStore) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZInter", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZInter indicates an expected call of ZInter.
func (mr *MockPipelinerMockRecorder) ZInter(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZInter", reflect.TypeOf((*MockPipeliner)(nil).ZInter), arg0, arg1)
}

// ZInterCard mocks base method.
func (m *MockPipeliner) ZInterCard(arg0 context.Context, arg1 int64, arg2 ...string) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZInterCard", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZInterCard indicates an expected call of ZInterCard.
func (mr *MockPipelinerMockRecorder) ZInterCard(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZInterCard", reflect.TypeOf((*MockPipeliner)(nil).ZInterCard), varargs...)
}

// ZInterStore mocks base method.
func (m *MockPipeliner) ZInterStore(arg0 context.Context, arg1 string, arg2 *redis.ZStore) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZInterStore", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZInterStore indicates an expected call of ZInterStore.
func (mr *MockPipelinerMockRecorder) ZInterStore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZInterStore", reflect.TypeOf((*MockPipeliner)(nil).ZInterStore), arg0, arg1, arg2)
}

// ZInterWithScores mocks base method.
func (m *MockPipeliner) ZInterWithScores(arg0 context.Context, arg1 *redis.ZStore) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZInterWithScores", arg0, arg1)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZInterWithScores indicates an expected call of ZInterWithScores.
func (mr *MockPipelinerMockRecorder) ZInterWithScores(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZInterWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZInterWithScores), arg0, arg1)
}

// ZLexCount mocks base method.
func (m *MockPipeliner) ZLexCount(arg0 context.Context, arg1, arg2, arg3 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZLexCount", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZLexCount indicates an expected call of ZLexCount.
func (mr *MockPipelinerMockRecorder) ZLexCount(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZLexCount", reflect.TypeOf((*MockPipeliner)(nil).ZLexCount), arg0, arg1, arg2, arg3)
}

// ZMPop mocks base method.
func (m *MockPipeliner) ZMPop(arg0 context.Context, arg1 string, arg2 int64, arg3 ...string) *redis.ZSliceWithKeyCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1, arg2}
	for _, a := range arg3 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZMPop", varargs...)
	ret0, _ := ret[0].(*redis.ZSliceWithKeyCmd)
	return ret0
}

// ZMPop indicates an expected call of ZMPop.
func (mr *MockPipelinerMockRecorder) ZMPop(arg0, arg1, arg2 interface{}, arg3 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1, arg2}, arg3...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZMPop", reflect.TypeOf((*MockPipeliner)(nil).ZMPop), varargs...)
}

// ZMScore mocks base method.
func (m *MockPipeliner) ZMScore(arg0 context.Context, arg1 string, arg2 ...string) *redis.FloatSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZMScore", varargs...)
	ret0, _ := ret[0].(*redis.FloatSliceCmd)
	return ret0
}

// ZMScore indicates an expected call of ZMScore.
func (mr *MockPipelinerMockRecorder) ZMScore(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZMScore", reflect.TypeOf((*MockPipeliner)(nil).ZMScore), varargs...)
}

// ZPopMax mocks base method.
func (m *MockPipeliner) ZPopMax(arg0 context.Context, arg1 string, arg2 ...int64) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZPopMax", varargs...)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZPopMax indicates an expected call of ZPopMax.
func (mr *MockPipelinerMockRecorder) ZPopMax(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZPopMax", reflect.TypeOf((*MockPipeliner)(nil).ZPopMax), varargs...)
}

// ZPopMin mocks base method.
func (m *MockPipeliner) ZPopMin(arg0 context.Context, arg1 string, arg2 ...int64) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZPopMin", varargs...)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZPopMin indicates an expected call of ZPopMin.
func (mr *MockPipelinerMockRecorder) ZPopMin(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZPopMin", reflect.TypeOf((*MockPipeliner)(nil).ZPopMin), varargs...)
}

// ZRandMember mocks base method.
func (m *MockPipeliner) ZRandMember(arg0 context.Context, arg1 string, arg2 int) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRandMember", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZRandMember indicates an expected call of ZRandMember.
func (mr *MockPipelinerMockRecorder) ZRandMember(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRandMember", reflect.TypeOf((*MockPipeliner)(nil).ZRandMember), arg0, arg1, arg2)
}

// ZRandMemberWithScores mocks base method.
func (m *MockPipeliner) ZRandMemberWithScores(arg0 context.Context, arg1 string, arg2 int) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRandMemberWithScores", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZRandMemberWithScores indicates an expected call of ZRandMemberWithScores.
func (mr *MockPipelinerMockRecorder) ZRandMemberWithScores(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRandMemberWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZRandMemberWithScores), arg0, arg1, arg2)
}

// ZRange mocks base method.
func (m *MockPipeliner) ZRange(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZRange indicates an expected call of ZRange.
func (mr *MockPipelinerMockRecorder) ZRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRange", reflect.TypeOf((*MockPipeliner)(nil).ZRange), arg0, arg1, arg2, arg3)
}

// ZRangeArgs mocks base method.
func (m *MockPipeliner) ZRangeArgs(arg0 context.Context, arg1 redis.ZRangeArgs) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRangeArgs", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZRangeArgs indicates an expected call of ZRangeArgs.
func (mr *MockPipelinerMockRecorder) ZRangeArgs(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRangeArgs", reflect.TypeOf((*MockPipeliner)(nil).ZRangeArgs), arg0, arg1)
}

// ZRangeArgsWithScores mocks base method.
func (m *MockPipeliner) ZRangeArgsWithScores(arg0 context.Context, arg1 redis.ZRangeArgs) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRangeArgsWithScores", arg0, arg1)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZRangeArgsWithScores indicates an expected call of ZRangeArgsWithScores.
func (mr *MockPipelinerMockRecorder) ZRangeArgsWithScores(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRangeArgsWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZRangeArgsWithScores), arg0, arg1)
}

// ZRangeByLex mocks base method.
func (m *MockPipeliner) ZRangeByLex(arg0 context.Context, arg1 string, arg2 *redis.ZRangeBy) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRangeByLex", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZRangeByLex indicates an expected call of ZRangeByLex.
func (mr *MockPipelinerMockRecorder) ZRangeByLex(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRangeByLex", reflect.TypeOf((*MockPipeliner)(nil).ZRangeByLex), arg0, arg1, arg2)
}

// ZRangeByScore mocks base method.
func (m *MockPipeliner) ZRangeByScore(arg0 context.Context, arg1 string, arg2 *redis.ZRangeBy) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRangeByScore", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZRangeByScore indicates an expected call of ZRangeByScore.
func (mr *MockPipelinerMockRecorder) ZRangeByScore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRangeByScore", reflect.TypeOf((*MockPipeliner)(nil).ZRangeByScore), arg0, arg1, arg2)
}

// ZRangeByScoreWithScores mocks base method.
func (m *MockPipeliner) ZRangeByScoreWithScores(arg0 context.Context, arg1 string, arg2 *redis.ZRangeBy) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRangeByScoreWithScores", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZRangeByScoreWithScores indicates an expected call of ZRangeByScoreWithScores.
func (mr *MockPipelinerMockRecorder) ZRangeByScoreWithScores(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRangeByScoreWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZRangeByScoreWithScores), arg0, arg1, arg2)
}

// ZRangeStore mocks base method.
func (m *MockPipeliner) ZRangeStore(arg0 context.Context, arg1 string, arg2 redis.ZRangeArgs) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRangeStore", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZRangeStore indicates an expected call of ZRangeStore.
func (mr *MockPipelinerMockRecorder) ZRangeStore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRangeStore", reflect.TypeOf((*MockPipeliner)(nil).ZRangeStore), arg0, arg1, arg2)
}

// ZRangeWithScores mocks base method.
func (m *MockPipeliner) ZRangeWithScores(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRangeWithScores", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZRangeWithScores indicates an expected call of ZRangeWithScores.
func (mr *MockPipelinerMockRecorder) ZRangeWithScores(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRangeWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZRangeWithScores), arg0, arg1, arg2, arg3)
}

// ZRank mocks base method.
func (m *MockPipeliner) ZRank(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRank", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZRank indicates an expected call of ZRank.
func (mr *MockPipelinerMockRecorder) ZRank(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRank", reflect.TypeOf((*MockPipeliner)(nil).ZRank), arg0, arg1, arg2)
}

// ZRankWithScore mocks base method.
func (m *MockPipeliner) ZRankWithScore(arg0 context.Context, arg1, arg2 string) *redis.RankWithScoreCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRankWithScore", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.RankWithScoreCmd)
	return ret0
}

// ZRankWithScore indicates an expected call of ZRankWithScore.
func (mr *MockPipelinerMockRecorder) ZRankWithScore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRankWithScore", reflect.TypeOf((*MockPipeliner)(nil).ZRankWithScore), arg0, arg1, arg2)
}

// ZRem mocks base method.
func (m *MockPipeliner) ZRem(arg0 context.Context, arg1 string, arg2 ...interface{}) *redis.IntCmd {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ZRem", varargs...)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZRem indicates an expected call of ZRem.
func (mr *MockPipelinerMockRecorder) ZRem(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRem", reflect.TypeOf((*MockPipeliner)(nil).ZRem), varargs...)
}

// ZRemRangeByLex mocks base method.
func (m *MockPipeliner) ZRemRangeByLex(arg0 context.Context, arg1, arg2, arg3 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRemRangeByLex", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZRemRangeByLex indicates an expected call of ZRemRangeByLex.
func (mr *MockPipelinerMockRecorder) ZRemRangeByLex(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRemRangeByLex", reflect.TypeOf((*MockPipeliner)(nil).ZRemRangeByLex), arg0, arg1, arg2, arg3)
}

// ZRemRangeByRank mocks base method.
func (m *MockPipeliner) ZRemRangeByRank(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRemRangeByRank", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZRemRangeByRank indicates an expected call of ZRemRangeByRank.
func (mr *MockPipelinerMockRecorder) ZRemRangeByRank(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRemRangeByRank", reflect.TypeOf((*MockPipeliner)(nil).ZRemRangeByRank), arg0, arg1, arg2, arg3)
}

// ZRemRangeByScore mocks base method.
func (m *MockPipeliner) ZRemRangeByScore(arg0 context.Context, arg1, arg2, arg3 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRemRangeByScore", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZRemRangeByScore indicates an expected call of ZRemRangeByScore.
func (mr *MockPipelinerMockRecorder) ZRemRangeByScore(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRemRangeByScore", reflect.TypeOf((*MockPipeliner)(nil).ZRemRangeByScore), arg0, arg1, arg2, arg3)
}

// ZRevRange mocks base method.
func (m *MockPipeliner) ZRevRange(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRange", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZRevRange indicates an expected call of ZRevRange.
func (mr *MockPipelinerMockRecorder) ZRevRange(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRange", reflect.TypeOf((*MockPipeliner)(nil).ZRevRange), arg0, arg1, arg2, arg3)
}

// ZRevRangeByLex mocks base method.
func (m *MockPipeliner) ZRevRangeByLex(arg0 context.Context, arg1 string, arg2 *redis.ZRangeBy) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRangeByLex", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZRevRangeByLex indicates an expected call of ZRevRangeByLex.
func (mr *MockPipelinerMockRecorder) ZRevRangeByLex(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRangeByLex", reflect.TypeOf((*MockPipeliner)(nil).ZRevRangeByLex), arg0, arg1, arg2)
}

// ZRevRangeByScore mocks base method.
func (m *MockPipeliner) ZRevRangeByScore(arg0 context.Context, arg1 string, arg2 *redis.ZRangeBy) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRangeByScore", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZRevRangeByScore indicates an expected call of ZRevRangeByScore.
func (mr *MockPipelinerMockRecorder) ZRevRangeByScore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRangeByScore", reflect.TypeOf((*MockPipeliner)(nil).ZRevRangeByScore), arg0, arg1, arg2)
}

// ZRevRangeByScoreWithScores mocks base method.
func (m *MockPipeliner) ZRevRangeByScoreWithScores(arg0 context.Context, arg1 string, arg2 *redis.ZRangeBy) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRangeByScoreWithScores", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZRevRangeByScoreWithScores indicates an expected call of ZRevRangeByScoreWithScores.
func (mr *MockPipelinerMockRecorder) ZRevRangeByScoreWithScores(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRangeByScoreWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZRevRangeByScoreWithScores), arg0, arg1, arg2)
}

// ZRevRangeWithScores mocks base method.
func (m *MockPipeliner) ZRevRangeWithScores(arg0 context.Context, arg1 string, arg2, arg3 int64) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRangeWithScores", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZRevRangeWithScores indicates an expected call of ZRevRangeWithScores.
func (mr *MockPipelinerMockRecorder) ZRevRangeWithScores(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRangeWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZRevRangeWithScores), arg0, arg1, arg2, arg3)
}

// ZRevRank mocks base method.
func (m *MockPipeliner) ZRevRank(arg0 context.Context, arg1, arg2 string) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRank", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZRevRank indicates an expected call of ZRevRank.
func (mr *MockPipelinerMockRecorder) ZRevRank(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRank", reflect.TypeOf((*MockPipeliner)(nil).ZRevRank), arg0, arg1, arg2)
}

// ZRevRankWithScore mocks base method.
func (m *MockPipeliner) ZRevRankWithScore(arg0 context.Context, arg1, arg2 string) *redis.RankWithScoreCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZRevRankWithScore", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.RankWithScoreCmd)
	return ret0
}

// ZRevRankWithScore indicates an expected call of ZRevRankWithScore.
func (mr *MockPipelinerMockRecorder) ZRevRankWithScore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZRevRankWithScore", reflect.TypeOf((*MockPipeliner)(nil).ZRevRankWithScore), arg0, arg1, arg2)
}

// ZScan mocks base method.
func (m *MockPipeliner) ZScan(arg0 context.Context, arg1 string, arg2 uint64, arg3 string, arg4 int64) *redis.ScanCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZScan", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*redis.ScanCmd)
	return ret0
}

// ZScan indicates an expected call of ZScan.
func (mr *MockPipelinerMockRecorder) ZScan(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZScan", reflect.TypeOf((*MockPipeliner)(nil).ZScan), arg0, arg1, arg2, arg3, arg4)
}

// ZScore mocks base method.
func (m *MockPipeliner) ZScore(arg0 context.Context, arg1, arg2 string) *redis.FloatCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZScore", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.FloatCmd)
	return ret0
}

// ZScore indicates an expected call of ZScore.
func (mr *MockPipelinerMockRecorder) ZScore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZScore", reflect.TypeOf((*MockPipeliner)(nil).ZScore), arg0, arg1, arg2)
}

// ZUnion mocks base method.
func (m *MockPipeliner) ZUnion(arg0 context.Context, arg1 redis.ZStore) *redis.StringSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZUnion", arg0, arg1)
	ret0, _ := ret[0].(*redis.StringSliceCmd)
	return ret0
}

// ZUnion indicates an expected call of ZUnion.
func (mr *MockPipelinerMockRecorder) ZUnion(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZUnion", reflect.TypeOf((*MockPipeliner)(nil).ZUnion), arg0, arg1)
}

// ZUnionStore mocks base method.
func (m *MockPipeliner) ZUnionStore(arg0 context.Context, arg1 string, arg2 *redis.ZStore) *redis.IntCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZUnionStore", arg0, arg1, arg2)
	ret0, _ := ret[0].(*redis.IntCmd)
	return ret0
}

// ZUnionStore indicates an expected call of ZUnionStore.
func (mr *MockPipelinerMockRecorder) ZUnionStore(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZUnionStore", reflect.TypeOf((*MockPipeliner)(nil).ZUnionStore), arg0, arg1, arg2)
}

// ZUnionWithScores mocks base method.
func (m *MockPipeliner) ZUnionWithScores(arg0 context.Context, arg1 redis.ZStore) *redis.ZSliceCmd {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ZUnionWithScores", arg0, arg1)
	ret0, _ := ret[0].(*redis.ZSliceCmd)
	return ret0
}

// ZUnionWithScores indicates an expected call of ZUnionWithScores.
func (mr *MockPipelinerMockRecorder) ZUnionWithScores(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ZUnionWithScores", reflect.TypeOf((*MockPipeliner)(nil).ZUnionWithScores), arg0, arg1)
}
