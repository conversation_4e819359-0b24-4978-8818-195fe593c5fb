package pdf_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/pdf"
	"git.wndv.co/lineman/fleet-distribution/internal/pdf/mock_pdf"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestGeneratePDF(t *testing.T) {
	t.Run("generate tawi50 pdf correctly", func(t *testing.T) {
		pdfBuilder, deps, finish := newMockPDFBuilder(t)
		defer finish()
		year, month, day := timeutil.BangkokNow().Date()
		address := model.Address{
			HouseNumber: crypt.NewLazyEncryptedString("998/271 อาคาร The One"),
			Moo:         crypt.NewLazyEncryptedString("7"),
			Subdistrict: crypt.NewLazyEncryptedString("ลาดยาว"),
			District:    crypt.NewLazyEncryptedString("จตุจักตร"),
			Province:    crypt.NewLazyEncryptedString("กรุงเทพมหานคร"),
			Zipcode:     crypt.NewLazyEncryptedString("10900"),
		}
		mock := model.WithholdingTaxCertificate{
			Year:        "2567",
			Seq:         200,
			AmountPaid:  55552.12,
			TaxWithheld: 421.17,
			DriverProfile: model.DriverProfile{
				Firstname: crypt.NewLazyEncryptedString("สรพงษ์"),
				Lastname:  crypt.NewLazyEncryptedString("ขับเก่ง"),
				CitizenID: crypt.NewLazyEncryptedString("1488500187789"),
				Address:   address,
			},
			IncomeSummary: model.IncomeSummary{
				SumOfAgentIncome:                2222222,
				SumOfTaxBaseAndSumOfAgentIncome: 4444444,
				SumOfTaxBase:                    2222222,
				SumOfWithHoldingTax:             6666666,
			},
		}

		taxpayer := pdf.Tawi50TaxPayer{
			TaxpayerName:        "บริษัท ไลน์แมน (ประเทศไทย) จำกัด",
			TaxIdentificationNo: []string{"0", "1", "0", "5", "5", "6", "2", "1", "6", "0", "7", "2", "1", "2", "1", "2"},
			TaxpayerAddress:     "เลขที่ 195 วัน แบงค็อก ทาวเวอร์ 4 ห้องเลขที่ 2411 - 2412 ชั้น 24 และ ห้องเลขที่ 2501-2514 ชั้น 25 ถนนวิทยุ แขวงลุมพินี เขตปทุมวัน กรุงเทพมหานคร 10330",
			IssuingDate:         day,
			IssuingMonth:        month,
			IssuingYear:         year + 543,
		}
		deps.pdfGenerator.EXPECT().LoadPDFTemplate(gomock.Any(), "tawi-template.pdf").Return(0, nil)
		deps.pdfGenerator.EXPECT().LoadPDFTemplate(gomock.Any(), "income-summary-template.pdf").Return(0, nil)
		deps.pdfGenerator.EXPECT().SetA4Resolution(gomock.Any(), 0).Times(2)

		_, err := pdfBuilder.GenerateTawi50(taxpayer, mock)
		assert.NoError(t, err)
	})

	t.Run("generate bnpl pdf correctly", func(t *testing.T) {
		pdfBuilder, deps, finish := newMockPDFBuilder(t)
		defer finish()

		deps.pdfGenerator.EXPECT().LoadPDFTemplate(gomock.Any(), "soa-bnpl-template.pdf").Return(0, nil)
		deps.pdfGenerator.EXPECT().SetA4Resolution(gomock.Any(), 0)
		_, err := pdfBuilder.GenerateSOAForBNPL(pdf.SOAForBNPLInfo{
			HeadContent: pdf.InstallmentHeadContent{
				FileNumber:   "20220901001",
				CurrentDate:  "12/09/2022",
				DueDate:      "13/09/2022",
				PayerName:    "นนทพัทธ์ เนาวสัยศรี",
				PayerAddress: "99/22 ถนนพันธุ์ ถนนพันธุ์ ถนนพันธุ์ ถนนพันธุ์ ถนนพันธุ์ 10220",
				DriverID:     "LMDV8RAZW",
			},
			InstallmentDetail: pdf.InstallmentDetail{
				Installments: []pdf.Installment{
					{
						ProductName:           "XIAOMI 11 LITE 5G NE 128GB SNOWFLAKE WHITE",
						StartDate:             "01/06/2022",
						TotalInstallmentCount: 180,
						Price:                 23000,
						Amount:                1,
						DebtAmount:            23000,
						PaidAmount:            5000,
						RemainingAmount:       18000,
						OverdueDate:           "02/08/2022",
					},
					{
						ProductName:           "XIAOMI 11 LITE 5G NE 128GB BEBBLEGUM BLUE",
						StartDate:             "01/06/2022",
						TotalInstallmentCount: 135,
						Price:                 15450,
						Amount:                1,
						DebtAmount:            3000,
						PaidAmount:            2500,
						RemainingAmount:       500,
						OverdueDate:           "06/09/2022",
					},
				},
				FollowingFees: []pdf.BNPLFollowingFee{
					{
						Date:            "28/10/2022",
						Price:           100,
						Amount:          1,
						DebtAmount:      100,
						PaidAmount:      0,
						RemainingAmount: 100,
					},
					{
						Date:            "31/10/2022",
						Price:           100,
						Amount:          1,
						DebtAmount:      100,
						PaidAmount:      0,
						RemainingAmount: 100,
					},
				},
				DueDate:     "12/09/2022",
				TotalAmount: 18700,
			},
			InstallmentTable: pdf.InstallmentTable{
				PaidSummary: pdf.InstallmentPaidSummary{
					DebtTotal:     9500,
					Interest:      0,
					OtherFee:      0,
					FollowDebtFee: 0,
				},
				OverdueSummary: pdf.InstallmentOverdueSummary{
					Date:          "ตั้งแต่วันที่ 02/08/2022",
					Amount:        18500,
					Interest:      0,
					OtherFee:      0,
					FollowDebtFee: 200,
				},
				CurrentBillSummary: pdf.InstallmentCurrentBillSummary{
					Date:   "13/09/2022",
					Amount: 18700,
				},
			},
			BankInfos: pdf.InstallmentDriverBankInfos{
				CITINumber: "**********",
				UOBNumber:  "**********",
			},
		})

		assert.NoError(t, err)
	})

	etaxInvoiceInfoDataAfterMoveOffice := pdf.ETaxData{
		InvoiceNo:     "LMINEGS2023033000152",
		FullName:      "นาย BBB",
		Address:       "25-27 ซ.ท่าเตียน ถ.มหาราช พระบรมมหาราชวัง พระนคร กทม. 10200 25-27 ซ.ท่าเตียน ถ.มหาราช พระบรมมหาราชวัง พระนคร กทม. 10200",
		NationalID:    "************",
		ProductName:   "แพ็กคู่ \"Xiaomi Redmi Note11 Pro 5G 8/128 GB + หูฟัง ไร้สาย Marshall Minor III Truewireless\") รับที่ Jaymart",
		ProductAmount: 93.65,
		VATAmount:     6.56,
		TotalAmount:   **********.21,
	}

	t.Run("generate e-tax invoice pdf correctly", func(t *testing.T) {
		pdfBuilder, deps, finish := newMockPDFBuilder(t)
		defer finish()

		deps.pdfGenerator.EXPECT().LoadPDFTemplate(gomock.Any(), "e-tax-invoice.pdf").Return(0, nil)
		deps.pdfGenerator.EXPECT().SetA4Resolution(gomock.Any(), 0)
		_, err := pdfBuilder.GenerateETaxInvoice(pdf.ETaxInvoiceInfo{Data: etaxInvoiceInfoDataAfterMoveOffice})

		assert.NoError(t, err)
	})

	t.Run("generate e-tax invoice replacement pdf correctly", func(t *testing.T) {
		pdfBuilder, deps, finish := newMockPDFBuilder(t)
		defer finish()

		deps.pdfGenerator.EXPECT().LoadPDFTemplate(gomock.Any(), "e-tax-invoice-replacement.pdf").Return(0, nil)
		deps.pdfGenerator.EXPECT().SetA4Resolution(gomock.Any(), 0)
		_, err := pdfBuilder.GenerateETaxInvoiceReplacement(pdf.ETaxInvoiceReplacementInfo{
			Data:              etaxInvoiceInfoDataAfterMoveOffice,
			PreviousInvoiceNo: "LMINEGS2023033000152",
			PreviousDate:      timeutil.BangkokNow(),
			Purpose:           "เปลี่ยนชื่อนามสกุล ที่อยู่ และ บัตรประจำตัวประชาชน เปลี่ยนชื่อนามสกุล ที่อยู่ และ บัตรประจำตัวประชาชน เปลี่ยนชื่อนามสกุล ที่อยู่ และ บัตรประจำตัวประชาชน เปลี่ยนชื่อนามสกุล ที่อยู่ และ บัตรประจำตัวประชาชน เปลี่ยนชื่อนามสกุล ที่อยู่ และ บัตรประจำตัวประชาชน",
		})

		assert.NoError(t, err)
	})

	t.Run("generate e-tax credit note pdf correctly", func(t *testing.T) {
		pdfBuilder, deps, finish := newMockPDFBuilder(t)
		defer finish()

		deps.pdfGenerator.EXPECT().LoadPDFTemplate(gomock.Any(), "e-tax-credit-note.pdf").Return(0, nil)
		deps.pdfGenerator.EXPECT().SetA4Resolution(gomock.Any(), 0)
		_, err := pdfBuilder.GenerateETaxCreditNote(pdf.ETaxCreditNoteInfo{
			Data:              etaxInvoiceInfoDataAfterMoveOffice,
			PreviousInvoiceNo: "LMINEGS2023033000152",
			Purpose:           "เจ้าหน้าที่ตรวจสอบพบว่าสินค้าชำรุด เสียหาย และ คืนค่ามัดจำให้ทางบริษัท เจ้าหน้าที่ตรวจสอบพบว่าสินค้าชำรุด เสียหาย และ คืนค่ามัดจำให้ทางบริษัท เจ้าหน้าที่ตรวจสอบพบว่าสินค้าชำรุด เสียหาย และ คืนค่ามัดจำให้ทางบริษัท เจ้าหน้าที่ตรวจสอบพบว่าสินค้าชำรุด เสียหาย และ คืนค่ามัดจำให้ทางบริษัท",
		})

		assert.NoError(t, err)

	})
}

type pdfBuilderDeps struct {
	cfg          pdf.PDFBuilderCfg
	pdfGenerator *mock_pdf.MockPDFGenerator
}

func newMockPDFBuilder(r gomock.TestReporter) (*pdf.PDFBuilderImpl, *pdfBuilderDeps, func()) {
	ctrl := gomock.NewController(r)

	cfg := pdf.ProvidePDFBuilderCfg()
	mockPDFGenerator := mock_pdf.NewMockPDFGenerator(ctrl)

	pdfDeps := &pdfBuilderDeps{
		cfg:          cfg,
		pdfGenerator: mockPDFGenerator,
	}

	pdfBuilder := pdf.ProvidePDFBuilder(cfg, mockPDFGenerator)
	return pdfBuilder, pdfDeps, func() {
		ctrl.Finish()
	}
}
