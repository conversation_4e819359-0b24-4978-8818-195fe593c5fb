//go:generate mockgen -source=./pdf_generator.go -destination=./mock_pdf/mock_pdf_generator.go -package=mock_pdf

package pdf

import (
	"errors"
	"fmt"
	"io"

	"github.com/signintech/gopdf"
)

type PDFGeneratorImpl struct{}

type PDFGenerator interface {
	LoadPDFTemplate(pdf *gopdf.GoPdf, filename string) (int, error)
	SetA4Resolution(pdf *gopdf.GoPdf, tplid int)
}

func ProvidePDFGenerator() PDFGenerator {
	return &PDFGeneratorImpl{}
}

func (p PDFGeneratorImpl) LoadPDFTemplate(pdf *gopdf.GoPdf, filename string) (int, error) {
	if fs, err := resources.Open(fmt.Sprintf("templates/%s", filename)); err != nil {
		return 0, err
	} else {
		template, ok := fs.(io.ReadSeeker)
		if !ok {
			return 0, errors.New("unable to cast fs to io.ReadSeeker")
		}

		return pdf.ImportPageStream(&template, 1, "/MediaBox"), nil
	}
}

func (p PDFGeneratorImpl) SetA4Resolution(pdf *gopdf.GoPdf, tplid int) {
	pdf.UseImportedTemplate(tplid, 0, 0, 595, 842)
}
