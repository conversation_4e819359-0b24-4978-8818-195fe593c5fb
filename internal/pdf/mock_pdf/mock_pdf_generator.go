// Code generated by MockGen. DO NOT EDIT.
// Source: ./pdf_generator.go

// Package mock_pdf is a generated GoMock package.
package mock_pdf

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	gopdf "github.com/signintech/gopdf"
)

// MockPDFGenerator is a mock of PDFGenerator interface.
type MockPDFGenerator struct {
	ctrl     *gomock.Controller
	recorder *MockPDFGeneratorMockRecorder
}

// MockPDFGeneratorMockRecorder is the mock recorder for MockPDFGenerator.
type MockPDFGeneratorMockRecorder struct {
	mock *MockPDFGenerator
}

// NewMockPDFGenerator creates a new mock instance.
func NewMockPDFGenerator(ctrl *gomock.Controller) *MockPDFGenerator {
	mock := &MockPDFGenerator{ctrl: ctrl}
	mock.recorder = &MockPDFGeneratorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPDFGenerator) EXPECT() *MockPDFGeneratorMockRecorder {
	return m.recorder
}

// LoadPDFTemplate mocks base method.
func (m *MockPDFGenerator) LoadPDFTemplate(pdf *gopdf.GoPdf, filename string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LoadPDFTemplate", pdf, filename)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LoadPDFTemplate indicates an expected call of LoadPDFTemplate.
func (mr *MockPDFGeneratorMockRecorder) LoadPDFTemplate(pdf, filename interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LoadPDFTemplate", reflect.TypeOf((*MockPDFGenerator)(nil).LoadPDFTemplate), pdf, filename)
}

// SetA4Resolution mocks base method.
func (m *MockPDFGenerator) SetA4Resolution(pdf *gopdf.GoPdf, tplid int) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetA4Resolution", pdf, tplid)
}

// SetA4Resolution indicates an expected call of SetA4Resolution.
func (mr *MockPDFGeneratorMockRecorder) SetA4Resolution(pdf, tplid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetA4Resolution", reflect.TypeOf((*MockPDFGenerator)(nil).SetA4Resolution), pdf, tplid)
}
