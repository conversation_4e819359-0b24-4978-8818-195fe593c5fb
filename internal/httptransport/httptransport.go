package httptransport

import (
	"bufio"
	"bytes"
	"net/http"

	"github.com/sirupsen/logrus"
)

// readResponse parse response content into http.Response. This function
// built-in parse a request because Response needs to have request inside
// of it.
func readResponse(respb []byte, reqb []byte) *http.Response {
	req, err := readRequest(reqb)
	if err != nil {
		logrus.Errorf("cannot reading request: %v", err)
		return nil
	}
	resp, err := http.ReadResponse(bufio.NewReader(bytes.NewBuffer(respb)), req)
	if err != nil {
		logrus.Errorf("cannot reading response: %v", err)
		return nil
	}
	return resp
}

func readRequest(reqb []byte) (*http.Request, error) {
	return http.ReadRequest(bufio.NewReader(bytes.NewBuffer(reqb)))
}
