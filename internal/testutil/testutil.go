package testutil

import (
	"strconv"
	"strings"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
)

type ParameterHolder struct {
	Values []interface{}
}

func (ph *ParameterHolder) Matches(x interface{}) bool {
	ph.Values = append(ph.Values, x)

	return true
}

func (ph *ParameterHolder) String() string {
	return "holder"
}

type DateTimeMatcher struct {
	t           time.Time
	granularity time.Duration
}

// DateTimeEq matches time.Time data type use in the mock function.
// It will compare two time.Time in second resolution by default, but it can be configured the compared resolution by passing duration as a second parameter.
func DateTimeEq(t time.Time, resolution ...time.Duration) *DateTimeMatcher {
	useGranularity := time.Second
	if len(resolution) > 0 {
		useGranularity = resolution[0]
	}
	return &DateTimeMatcher{
		t:           t,
		granularity: useGranularity,
	}
}

func (dtm *DateTimeMatcher) Matches(x interface{}) bool {
	t1 := dtm.t.Truncate(dtm.granularity)
	t2 := x.(time.Time).Truncate(dtm.granularity)
	return t1.Equal(t2)
}

func (dtm *DateTimeMatcher) String() string {
	return "datetime matcher in specific granularity"
}

func GeneratePlanRoutes(planRoutesStr string) []prediction.PlanRoute {
	var planRoutes []prediction.PlanRoute
	for _, planRouteStr := range strings.Split(planRoutesStr, " ") {
		if len(planRouteStr) == 0 {
			continue
		}
		if len(planRouteStr) != 2 {
			panic("cannot generate plan routes")
		}

		orderID, _ := strconv.Atoi(string(planRouteStr[1]))
		if planRouteStr[0] == 'P' {
			planRoutes = append(planRoutes, prediction.PlanRoute{
				OrderID:    strconv.Itoa(orderID),
				ActionType: prediction.PickupAction,
			})
		} else {
			planRoutes = append(planRoutes, prediction.PlanRoute{
				OrderID:    strconv.Itoa(orderID),
				ActionType: prediction.DropOffAction,
			})
		}
	}
	return planRoutes
}
