package providers

import (
	"context"
	"errors"

	"github.com/twpayne/go-geom"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
)

var _ polygon.Polygon = &StubPolygonApi{}

type StubPolygonApi struct {
	regions map[string][]string
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideStubPolygonApiForTest(cfg config.ServiceAreaConfig) *StubPolygonApi {
	regions := make(map[string][]string)
	regions[cfg.PolygonFleetServiceName] = []string{"BKK", "ITTREGION1", "ITTREGION2", "ITTREGION3"}
	regions[cfg.PolygonFoodServiceName] = []string{"BKK", "ITTREGION1", "ITTREGION2", "ITTREGION3"}

	return &StubPolygonApi{
		regions: regions,
	}
}

func (sp StubPolygonApi) ListRegionByService(_ context.Context, service string) ([]string, error) {
	return sp.regions[service], nil
}

func (sp StubPolygonApi) GetRegionByLocation(ctx context.Context, _ polygon.Location) (string, error) {
	if region, ok := ctx.Value("X-Region").(string); ok {
		return region, nil
	}
	return "", errors.New("Stub region not found")
}

func (sp StubPolygonApi) GetRawRegion(context.Context, string) ([]byte, error) {
	panic("implement me")
}

func (sp StubPolygonApi) GetRegion(ctx context.Context, region string) (polygon.GetRawRegionRes, error) {
	return polygon.GetRawRegionRes{
		Region: region,
		Geometry: polygon.Geometry{
			Type:        "MultiPolygon",
			Coordinates: [][]geom.Coord{{{100.12939453125, 16.193574826697834}, {99.86572265625, 15.347761924346937}, {101.6455078125, 14.796127603627053}, {100.12939453125, 16.193574826697834}}},
		},
	}, nil
}
