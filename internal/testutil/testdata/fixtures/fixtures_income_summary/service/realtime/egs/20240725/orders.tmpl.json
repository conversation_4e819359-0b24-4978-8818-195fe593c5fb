[{"_id": {"$oid": "66a129e5aafc63994edc4efa"}, "metadata": {"deliveryMode": "NORMAL"}, "quote_id": "LMFQ-31985459", "user_id": "U736767881645228", "service_type": "food", "price_scheme": "RMS", "autostart": false, "routes": [{"id": "598197jX", "name": "<PERSON><PERSON> <PERSON> (RJ <PERSON>) <PERSON><PERSON>ong", "address": "121011, 92 พระโขนง คลองเตย กรุงเทพมหานคร", "phones": ["0822222222"], "location": {"lat": 13.72069761220181, "lng": 100.58311107343292}, "memo": "ข้างๆท้องฟ้าจำลอง", "memo_th": "", "memoType": "TEXT", "picking_items": [{"name": "ลูกค้าจ่ายเงินแล้ว", "price": 0, "quantity": 0, "memo": "", "options": [], "image": ""}, {"name": "ส่วนลด ค่า อาหาร 10.00 บ.", "price": 0, "quantity": 0, "memo": "", "options": [], "image": ""}, {"name": "กระเพรา + โค้ก", "price": 195, "quantity": 5, "memo": "", "options": [{"name": "", "display_name": ""}], "image": ""}], "delivery_items": [], "collect_payment": false, "items_price": 185, "items_price_before_discount": 195, "price_summary": {"delivery_fee": {"additional_fee": {}, "payment_method": "", "price_scheme_ref_id": "", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "experimental_delivery_fee": {"additional_fee": {}, "payment_method": "", "price_scheme_ref_id": "", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "item_fee": {"payment_method": "", "discounts": [], "quote_item_fee_before_discount": 0, "item_fee": 0, "sub_total": 0, "total": 0}, "total": 0}, "estimated_delivery_time": 164, "distance": 1208.5, "experimental_distance": 0, "source_snapping_distance": 0, "experimental_source_snapping_distance": 0, "target_snapping_distance": 0, "experimental_target_snapping_distance": 0, "experimental_estimated_delivery_time": 0, "delivery_status": "PICK_UP_DONE", "delivery_history": {"ON_THE_WAY": {"$date": "2024-07-25T16:21:57.142Z"}, "ARRIVED": {"$date": "2024-07-25T16:22:02.907Z"}, "PICK_UP_DONE": {"$date": "2024-07-25T16:22:12.120Z"}, "INIT": {"$date": "2024-07-25T16:20:52.097Z"}}, "info": {"service_type": "food", "estimated_cooking_time": 898, "price_scheme": "RMS", "restaurant_type": "RMS", "restaurant_direction": {"restaurant_id": "", "parking": "", "direction": "", "memo": "", "updated_at": {"$date": {"$numberLong": "-62135596800000"}}}}, "pauses": {}, "metadata": {}, "do_not_disturb": false, "is_rain": false, "continue_reason": ""}, {"id": "ChIJXTgRAv-f4jARCLTXaxIXYao", "name": "test", "address": "เลขที่ 8 ที-วัน บิวดิ้ง ชั้น14 1405 <PERSON><PERSON> 40, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>p <PERSON> 10110, Thailand", "phones": ["088888888"], "location": {"lat": 13.7223561, "lng": 100.5804125}, "memo": "mock p1d1 LMD0HDWL5", "memo_th": "", "memoType": "TEXT", "picking_items": [], "delivery_items": [{"name": "ลูกค้าจ่ายเงินแล้ว", "price": 0, "quantity": 0, "memo": "", "options": [], "image": ""}, {"name": "ส่วนลด ค่า อาหาร 10.00 บ.", "price": 0, "quantity": 0, "memo": "", "options": [], "image": ""}, {"name": "กระเพรา + โค้ก", "price": 195, "quantity": 5, "memo": "", "options": [{"name": "", "display_name": ""}], "image": ""}], "collect_payment": true, "items_price": 185, "items_price_before_discount": 195, "price_summary": {"delivery_fee": {"additional_fee": {}, "payment_method": "RLP", "price_scheme_ref_id": "3ece153a9c5345d38068b2a2b1328e43", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [{"id": "9ad86cb0eb9744a6830d28077f8d4e08", "scheme": "INSTALLMENT", "name": "tonnam_installment_ontop", "incentive_name": "", "type": "", "incentive_sources": ["BONUS"], "reference_ids": [], "amount": 99, "bundle_amount": 99, "coin": 0, "bundle_coin": 0}], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 43, "base_fee": 43, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 43, "commission": 0, "withholding_tax": 1.29, "on_top_fare": 99, "raw_on_top_fare": 99, "raw_bundle_on_top_fare": 99, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 2.97, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 43, "total": 43}, "experimental_delivery_fee": {"additional_fee": {}, "payment_method": "RLP", "price_scheme_ref_id": "3ece153a9c5345d38068b2a2b1328e43", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "item_fee": {"item_fee_before_discount": 195, "payment_method": "RLP", "discounts": [], "quote_item_fee_before_discount": 185, "item_fee": 185, "sub_total": 185, "total": 185}, "total": 228}, "estimated_delivery_time": 39, "distance": 402.9, "experimental_distance": 0, "source_snapping_distance": 19.512375, "experimental_source_snapping_distance": 0, "target_snapping_distance": 31.06928, "experimental_target_snapping_distance": 0, "experimental_estimated_delivery_time": 0, "delivery_status": "DROP_OFF_DONE", "delivery_history": {"ARRIVED": {"$date": "2024-07-25T16:22:17.706Z"}, "ON_THE_WAY": {"$date": "2024-07-25T16:22:12.120Z"}, "INIT": {"$date": "2024-07-25T16:20:52.097Z"}, "DROP_OFF_DONE": {"$date": "2024-07-25T16:22:32.063Z"}}, "info": {"service_type": "food", "estimated_cooking_time": 0, "price_scheme": "RMS", "restaurant_type": "RMS", "restaurant_direction": {"restaurant_id": "", "parking": "", "direction": "", "memo": "", "updated_at": {"$date": {"$numberLong": "-62135596800000"}}}}, "pauses": {}, "metadata": {}, "do_not_disturb": false, "is_rain": true, "continue_reason": ""}], "distribution_regions": ["BKK"], "distance": 403, "experimental_distance": 0, "created_at": {"$date": "2024-07-25T16:20:53.556Z"}, "updated_at": {"$date": "2024-07-25T16:22:32.130Z"}, "options": {"user_preferred_lang": "TRANSLATION_LANGUAGE_UNSPECIFIED"}, "note_to_driver": "", "pay_at_stop": 1, "special_event": [], "revenueprincipalmodel": true, "map_provider": "OSRM", "store_accept_half_half": false, "restaurant_chain_id": "2238", "ignore_driver_too_far_validation": false, "user_region": "BKK", "is_experimental_success": false, "user_placed_time": {"$date": "2024-07-25T16:20:48.692Z"}, "order_id": "LMF-240724-055668761", "status": "COMPLETED", "head_to": 0, "revamped_status": false, "driver": "LMD0HDWL5", "region": "BKK", "history": {"DRIVER_MATCHED": {"$date": "2024-07-25T16:21:23.198Z"}, "RESTAURANT_ACCEPTED": {"$date": "2024-07-25T16:21:53.478Z"}, "COMPLETED": {"$date": "2024-07-25T16:22:32.130Z"}, "DROP_OFF_DONE": {"$date": "2024-07-25T16:22:32.063Z"}, "ARRIVED": {"$date": "2024-07-25T16:22:17.706Z"}, "DRIVER_TO_RESTAURANT": {"$date": "2024-07-25T16:21:57.142Z"}, "ASSIGNING_DRIVER": {"$date": "2024-07-25T16:20:53.556Z"}, "DRIVER_ARRIVED_RESTAURANT": {"$date": "2024-07-25T16:22:02.907Z"}, "DRIVER_TO_DESTINATION": {"$date": "2024-07-25T16:22:12.120Z"}}, "expire_at": {"$date": "2024-07-25T16:30:53.556Z"}, "rating_score": 0, "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": "", "label": "", "name": "", "ban_duration_in_minute": 0, "cancelled_with_quota": false, "cancellationratefree": false, "force_cancellation_rate_free": false, "should_auto_claim": false, "is_reassign": false, "reassign_only": false, "cancellation_source": "", "message": {"wma": {"en": "", "th": ""}}}, "cancel_detail_log": [], "is_fraud": false, "distribution": "AUTO_ASSIGN", "remarks": [], "fraud_status": "", "verified_rider_photo_urls": [{"photo_url": "fleet_private/verified_rider_photo/LMF-240724-055668761/verified_rider_photo_ref__36c25c1225a94c97858ee185715a8b53", "is_compare_face_success": false, "created_at": {"$date": "2024-07-25T16:22:30.520Z"}}], "should_verify": true, "delivering_photo_status": "", "delivering_photo_urls": [], "is_require_delivering_photo": true, "shifts": [], "prediction": {"estimated_cooking_time_second": 747, "estimated_user_waiting_time_second": 1511}, "is_auto_assigned": true, "is_auto_accept": false, "is_fully_auto_accept": false, "assigned_to_queue": false, "is_b2b": false, "is_mo_secondary": false, "is_distributed": true, "defer_duration": 0, "is_throttled": true, "is_deferred": false, "throttled_round": 3, "accepted_with_batch_optimized": true, "delivering_round": 0, "trip_id": "TRIP-240724-986825551", "verified_device_id": "1721808526042-1741386409975736869", "accepted_device_id": "1721808526042-1741386409975736869", "delivering_device_id": "", "predicted_completed_time": {"$date": "2024-07-25T16:20:49.692Z"}, "history_location": {"ARRIVED": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-25T16:22:17.706Z"}}, "COMPLETED": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-25T16:22:32.063Z"}}, "DRIVER_TO_DESTINATION": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-25T16:22:12.120Z"}}, "DRIVER_MATCHED": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-25T16:21:23.182Z"}}, "DRIVER_TO_RESTAURANT": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-25T16:21:57.142Z"}}, "DRIVER_ARRIVED_RESTAURANT": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-25T16:22:02.907Z"}}}, "has_exempted_words": false, "exempted_words": [], "driver_action_history": {}, "actual_assigning_at": {"$date": "2024-07-25T16:20:53.556Z"}, "first_routed_uwt": 1, "leave_prev_stop_at": {"$date": "2024-07-25T16:21:57.150Z"}, "processed_by_single_distribution": false, "is_skip_incomplete_qr_promptpay_payment": false, "driver_type": "TESTER", "driver_vendor_id": "", "owner_changed_at": {"$date": "2024-07-25T16:20:52.129Z"}}, {"_id": {"$oid": "669f9d875c5bdcc70be43c61"}, "metadata": {"deliveryMode": "NORMAL"}, "quote_id": "LMFQ-31984872", "user_id": "U736767881645228", "service_type": "food", "price_scheme": "RMS", "autostart": false, "routes": [{"id": "598197jX", "name": "<PERSON><PERSON> <PERSON> (RJ <PERSON>) <PERSON><PERSON>ong", "address": "121011, 92 พระโขนง คลองเตย กรุงเทพมหานคร", "phones": ["0822222222"], "location": {"lat": 13.72069761220181, "lng": 100.58311107343292}, "memo": "ข้างๆท้องฟ้าจำลอง", "memo_th": "", "memoType": "TEXT", "picking_items": [{"name": "ลูกค้าจ่ายเงินแล้ว", "price": 0, "quantity": 0, "memo": "", "options": [], "image": ""}, {"name": "ส่วนลด ค่า อาหาร 10.00 บ.", "price": 0, "quantity": 0, "memo": "", "options": [], "image": ""}, {"name": "กระเพรา + โค้ก", "price": 195, "quantity": 5, "memo": "", "options": [{"name": "", "display_name": ""}], "image": ""}], "delivery_items": [], "collect_payment": false, "items_price": 185, "items_price_before_discount": 195, "price_summary": {"delivery_fee": {"additional_fee": {}, "payment_method": "", "price_scheme_ref_id": "", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "experimental_delivery_fee": {"additional_fee": {}, "payment_method": "", "price_scheme_ref_id": "", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "item_fee": {"payment_method": "", "discounts": [], "quote_item_fee_before_discount": 0, "item_fee": 0, "sub_total": 0, "total": 0}, "total": 0}, "estimated_delivery_time": 164, "distance": 1208.5, "experimental_distance": 0, "source_snapping_distance": 0, "experimental_source_snapping_distance": 0, "target_snapping_distance": 0, "experimental_target_snapping_distance": 0, "experimental_estimated_delivery_time": 0, "delivery_status": "PICK_UP_DONE", "delivery_history": {"PICK_UP_DONE": {"$date": "2024-07-23T12:10:48.419Z"}, "INIT": {"$date": "2024-07-23T12:09:41.470Z"}, "ON_THE_WAY": {"$date": "2024-07-23T12:10:36.325Z"}, "ARRIVED": {"$date": "2024-07-23T12:10:39.914Z"}}, "info": {"service_type": "food", "estimated_cooking_time": 898, "price_scheme": "RMS", "restaurant_type": "RMS", "restaurant_direction": {"restaurant_id": "", "parking": "", "direction": "", "memo": "", "updated_at": {"$date": {"$numberLong": "-62135596800000"}}}}, "pauses": {}, "metadata": {}, "do_not_disturb": false, "is_rain": false, "continue_reason": ""}, {"id": "ChIJXTgRAv-f4jARCLTXaxIXYao", "name": "test", "address": "เลขที่ 8 ที-วัน บิวดิ้ง ชั้น14 1405 <PERSON><PERSON> 40, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>p <PERSON> 10110, Thailand", "phones": ["0888888888"], "location": {"lat": 13.7223561, "lng": 100.5804125}, "memo": "mock p1d1 LMD0HDWL5", "memo_th": "", "memoType": "TEXT", "picking_items": [], "delivery_items": [{"name": "ลูกค้าจ่ายเงินแล้ว", "price": 0, "quantity": 0, "memo": "", "options": [], "image": ""}, {"name": "ส่วนลด ค่า อาหาร 10.00 บ.", "price": 0, "quantity": 0, "memo": "", "options": [], "image": ""}, {"name": "กระเพรา + โค้ก", "price": 195, "quantity": 5, "memo": "", "options": [{"name": "", "display_name": ""}], "image": ""}], "collect_payment": true, "items_price": 185, "items_price_before_discount": 195, "price_summary": {"delivery_fee": {"additional_fee": {}, "payment_method": "RLP", "price_scheme_ref_id": "3ece153a9c5345d38068b2a2b1328e43", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [{"id": "9ad86cb0eb9744a6830d28077f8d4e08", "scheme": "INSTALLMENT", "name": "tonnam_installment_ontop", "incentive_name": "", "type": "", "incentive_sources": ["BONUS"], "reference_ids": [], "amount": 99, "bundle_amount": 99, "coin": 0, "bundle_coin": 0}], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 43, "base_fee": 43, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 43, "commission": 0, "withholding_tax": 1.29, "on_top_fare": 99, "raw_on_top_fare": 99, "raw_bundle_on_top_fare": 99, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 2.97, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 43, "total": 43}, "experimental_delivery_fee": {"additional_fee": {}, "payment_method": "RLP", "price_scheme_ref_id": "3ece153a9c5345d38068b2a2b1328e43", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "item_fee": {"item_fee_before_discount": 195, "payment_method": "RLP", "discounts": [], "quote_item_fee_before_discount": 185, "item_fee": 185, "sub_total": 185, "total": 185}, "total": 228}, "estimated_delivery_time": 39, "distance": 402.9, "experimental_distance": 0, "source_snapping_distance": 19.512375, "experimental_source_snapping_distance": 0, "target_snapping_distance": 31.06928, "experimental_target_snapping_distance": 0, "experimental_estimated_delivery_time": 0, "delivery_status": "DROP_OFF_DONE", "delivery_history": {"INIT": {"$date": "2024-07-23T12:09:41.470Z"}, "ARRIVED": {"$date": "2024-07-23T12:10:53.326Z"}, "DROP_OFF_DONE": {"$date": "2024-07-23T12:10:57.892Z"}, "ON_THE_WAY": {"$date": "2024-07-23T12:10:48.419Z"}}, "info": {"service_type": "food", "estimated_cooking_time": 0, "price_scheme": "RMS", "restaurant_type": "RMS", "restaurant_direction": {"restaurant_id": "", "parking": "", "direction": "", "memo": "", "updated_at": {"$date": {"$numberLong": "-62135596800000"}}}}, "pauses": {}, "metadata": {}, "do_not_disturb": false, "is_rain": true, "continue_reason": ""}], "distribution_regions": ["BKK"], "distance": 403, "experimental_distance": 0, "created_at": {"$date": "2024-07-23T12:09:43.487Z"}, "updated_at": {"$date": "2024-07-23T12:10:57.987Z"}, "options": {}, "note_to_driver": "", "pay_at_stop": 1, "special_event": [], "revenueprincipalmodel": true, "map_provider": "OSRM", "store_accept_half_half": false, "restaurant_chain_id": "2238", "ignore_driver_too_far_validation": false, "user_region": "BKK", "is_experimental_success": false, "user_placed_time": {"$date": "2024-07-23T12:09:40.375Z"}, "order_id": "LMF-240723-014803093", "status": "COMPLETED", "head_to": 0, "revamped_status": false, "driver": "LMD0HDWL5", "region": "BKK", "history": {"ASSIGNING_DRIVER": {"$date": "2024-07-23T12:09:43.487Z"}, "DRIVER_TO_RESTAURANT": {"$date": "2024-07-23T12:10:36.325Z"}, "DRIVER_TO_DESTINATION": {"$date": "2024-07-23T12:10:48.419Z"}, "ARRIVED": {"$date": "2024-07-23T12:10:53.326Z"}, "DROP_OFF_DONE": {"$date": "2024-07-23T12:10:57.892Z"}, "COMPLETED": {"$date": "2024-07-23T12:10:57.987Z"}, "DRIVER_MATCHED": {"$date": "2024-07-23T12:10:07.598Z"}, "RESTAURANT_ACCEPTED": {"$date": "2024-07-23T12:10:34.403Z"}, "DRIVER_ARRIVED_RESTAURANT": {"$date": "2024-07-23T12:10:39.914Z"}}, "expire_at": {"$date": "2024-07-23T12:19:43.487Z"}, "rating_score": 0, "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": "", "label": "", "name": "", "ban_duration_in_minute": 0, "cancelled_with_quota": false, "cancellationratefree": false, "force_cancellation_rate_free": false, "should_auto_claim": false, "is_reassign": false, "reassign_only": false, "cancellation_source": "", "message": {"wma": {"en": "", "th": ""}}}, "cancel_detail_log": [], "is_fraud": false, "distribution": "AUTO_ASSIGN", "remarks": [], "fraud_status": "", "verified_rider_photo_urls": [], "should_verify": false, "delivering_photo_status": "", "delivering_photo_urls": [], "is_require_delivering_photo": true, "shifts": [], "prediction": {"estimated_cooking_time_second": 657, "estimated_user_waiting_time_second": 1331}, "is_auto_assigned": true, "is_auto_accept": false, "is_fully_auto_accept": false, "assigned_to_queue": false, "is_b2b": false, "is_mo_secondary": false, "is_distributed": true, "defer_duration": 0, "is_throttled": true, "is_deferred": false, "throttled_round": 1, "accepted_with_batch_optimized": true, "delivering_round": 0, "trip_id": "TRIP-240723-289092747", "verified_device_id": "", "accepted_device_id": "1721297029485-2010532416406104943", "delivering_device_id": "", "predicted_completed_time": {"$date": "2024-07-23T12:09:41.375Z"}, "history_location": {"DRIVER_MATCHED": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T12:10:07.592Z"}}, "DRIVER_TO_RESTAURANT": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T12:10:36.325Z"}}, "DRIVER_ARRIVED_RESTAURANT": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T12:10:39.914Z"}}, "DRIVER_TO_DESTINATION": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T12:10:48.419Z"}}, "ARRIVED": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T12:10:53.326Z"}}, "COMPLETED": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T12:10:57.892Z"}}}, "has_exempted_words": false, "exempted_words": [], "driver_action_history": {}, "actual_assigning_at": {"$date": "2024-07-23T12:09:43.487Z"}, "first_routed_uwt": 1, "leave_prev_stop_at": {"$date": "2024-07-23T12:10:36.334Z"}, "processed_by_single_distribution": false, "is_skip_incomplete_qr_promptpay_payment": false, "driver_type": "TESTER", "driver_vendor_id": "", "owner_changed_at": {"$date": "2024-07-23T12:09:41.499Z"}}, {"_id": {"$oid": "669f2996057c3a9257293ade"}, "metadata": {"deliveryMode": "NORMAL"}, "quote_id": "LMFQ-31967037", "user_id": "U461408536042283", "service_type": "food", "price_scheme": "RMS", "autostart": true, "routes": [{"id": "387980Lp", "name": "แตงโม ทองหล่อ", "address": "8 <PERSON><PERSON><PERSON><PERSON><PERSON>, 40 Alley คลองตันเหนือ วัฒนา กรุงเทพมหานคร", "phones": ["0891243460"], "location": {"lat": 13.7264476, "lng": 100.5790084}, "memo": "เลี้ยวซ้าย เลี้ยวขวา เดินหน้า ถอยหลัง", "memoType": "TEXT", "picking_items": [{"name": "ข้าวมันไก่", "price": 100, "quantity": 1, "memo": "", "options": [{"name": "", "display_name": ""}], "image": ""}], "delivery_items": [], "collect_payment": false, "items_price": 100, "items_price_before_discount": 100, "price_summary": {"delivery_fee": {"additional_fee": {}, "payment_method": "", "price_scheme_ref_id": "", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "experimental_delivery_fee": {"additional_fee": {}, "payment_method": "", "price_scheme_ref_id": "", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 0, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "item_fee": {"payment_method": "", "discounts": [], "quote_item_fee_before_discount": 0, "item_fee": 0, "sub_total": 0, "total": 0}, "total": 0}, "estimated_delivery_time": 96, "distance": 768, "experimental_distance": 0, "source_snapping_distance": 0, "experimental_source_snapping_distance": 0, "target_snapping_distance": 0, "experimental_target_snapping_distance": 0, "experimental_estimated_delivery_time": 0, "delivery_status": "PICK_UP_DONE", "delivery_history": {"PICK_UP_DONE": {"$date": "2024-07-23T04:00:20.047Z"}, "ON_THE_WAY": {"$date": "2024-07-23T03:59:57.958Z"}, "ARRIVED": {"$date": "2024-07-23T04:00:11.127Z"}, "INIT": {"$date": "2024-07-23T03:54:59.865Z"}}, "info": {"service_type": "food", "estimated_cooking_time": 900, "price_scheme": "RMS", "restaurant_type": "RMS", "restaurant_direction": {"restaurant_id": "", "parking": "", "direction": "", "memo": "", "updated_at": {"$date": {"$numberLong": "-62135596800000"}}}}, "pauses": {}, "metadata": {}, "do_not_disturb": false, "is_rain": false, "continue_reason": ""}, {"id": "", "name": "LT", "address": "930/4 so<PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> 10110, Thailand", "phones": ["021239612"], "location": {"lat": 13.7226605, "lng": 100.5806703}, "memo": "mock p1d1 LMD0HDWL5", "memoType": "TEXT", "picking_items": [], "delivery_items": [{"name": "ข้าวมันไก่", "price": 100, "quantity": 1, "memo": "", "options": [{"name": "", "display_name": ""}], "image": ""}], "collect_payment": true, "items_price": 100, "items_price_before_discount": 100, "price_summary": {"delivery_fee": {"additional_fee": {}, "payment_method": "CASH", "price_scheme_ref_id": "3ece153a9c5345d38068b2a2b1328e43", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [{"id": "9ad86cb0eb9744a6830d28077f8d4e08", "scheme": "INSTALLMENT", "name": "tonnam_installment_ontop", "incentive_name": "", "type": "", "incentive_sources": ["BONUS"], "reference_ids": [], "amount": 99, "bundle_amount": 99, "coin": 0, "bundle_coin": 0}], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 43, "base_fee": 43, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 43, "commission": 0, "withholding_tax": 1.29, "on_top_fare": 99, "raw_on_top_fare": 99, "raw_bundle_on_top_fare": 99, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 2.97, "user_delivery_fee": 5, "user_delivery_fee_before_discount": 5, "distance_unit_fee": 0, "sub_total": 43, "total": 43}, "experimental_delivery_fee": {"additional_fee": {}, "payment_method": "CASH", "price_scheme_ref_id": "3ece153a9c5345d38068b2a2b1328e43", "custom_ontops": [], "discounts": [], "extra_charges": [], "on_top_scheme": [], "coupon": {"code": "", "discount": 0, "max_deduction": 0}, "additional_service_fee": {"additional_service_items": [], "total": 0, "commission": 0}, "raw_base_fee": 0, "base_fee": 0, "road_fee": 0, "shift_price_value": 0, "mo_saving": 0, "custom_ontop": 0, "commission_rate": 0, "starting_fee": 0, "commission": 0, "withholding_tax": 0, "on_top_fare": 0, "raw_on_top_fare": 0, "raw_bundle_on_top_fare": 0, "coin": 0, "raw_coin": 0, "raw_bundle_coin": 0, "on_top_commission_fare": 0, "on_top_withholding_tax": 0, "user_delivery_fee": 5, "user_delivery_fee_before_discount": 0, "distance_unit_fee": 0, "sub_total": 0, "total": 0}, "item_fee": {"item_fee_before_discount": 100, "payment_method": "CASH", "discounts": [], "quote_item_fee_before_discount": 100, "item_fee": 100, "sub_total": 100, "total": 100}, "total": 143}, "estimated_delivery_time": 88, "distance": 780.7, "experimental_distance": 0, "source_snapping_distance": 3.677529, "experimental_source_snapping_distance": 0, "target_snapping_distance": 0.702256, "experimental_target_snapping_distance": 0, "experimental_estimated_delivery_time": 0, "delivery_status": "DROP_OFF_DONE", "delivery_history": {"INIT": {"$date": "2024-07-23T03:54:59.865Z"}, "ARRIVED": {"$date": "2024-07-23T04:00:25.568Z"}, "ON_THE_WAY": {"$date": "2024-07-23T04:00:20.047Z"}, "DROP_OFF_DONE": {"$date": "2024-07-23T04:00:39.511Z"}}, "info": {"service_type": "food", "estimated_cooking_time": 0, "price_scheme": "RMS", "restaurant_type": "RMS", "restaurant_direction": {"restaurant_id": "", "parking": "", "direction": "", "memo": "", "updated_at": {"$date": {"$numberLong": "-62135596800000"}}}}, "pauses": {}, "metadata": {}, "do_not_disturb": false, "is_rain": true, "continue_reason": ""}], "distribution_regions": ["BKK"], "distance": 781, "experimental_distance": 0, "created_at": {"$date": "2024-07-23T03:55:02.326Z"}, "updated_at": {"$date": "2024-07-23T04:00:39.551Z"}, "options": {"driver_money_flow": "CASH_COLLECTION"}, "note_to_driver": "", "pay_at_stop": 1, "special_event": [], "revenueprincipalmodel": true, "map_provider": "OSRM", "store_accept_half_half": false, "restaurant_chain_id": "", "ignore_driver_too_far_validation": false, "user_region": "BKK", "is_experimental_success": false, "user_placed_time": {"$date": "2024-07-23T03:54:57.137Z"}, "order_id": "LMF-240723-025021216", "status": "COMPLETED", "head_to": 0, "revamped_status": false, "driver": "LMD0HDWL5", "region": "BKK", "history": {"DRIVER_TO_DESTINATION": {"$date": "2024-07-23T04:00:20.047Z"}, "DROP_OFF_DONE": {"$date": "2024-07-23T04:00:39.511Z"}, "RESTAURANT_ACCEPTED": {"$date": "2024-07-23T03:58:32.888Z"}, "DRIVER_MATCHED": {"$date": "2024-07-23T03:59:57.958Z"}, "DRIVER_TO_RESTAURANT": {"$date": "2024-07-23T03:59:57.958Z"}, "DRIVER_ARRIVED_RESTAURANT": {"$date": "2024-07-23T04:00:11.127Z"}, "ARRIVED": {"$date": "2024-07-23T04:00:25.568Z"}, "ASSIGNING_DRIVER": {"$date": "2024-07-23T03:59:39.139Z"}, "COMPLETED": {"$date": "2024-07-23T04:00:39.551Z"}}, "expire_at": {"$date": "2024-07-23T04:09:39.139Z"}, "rating_score": 0, "comment": "", "cancel_detail": {"reason": "", "remark": "", "cancelled_by": "", "source": "", "requestor": "", "label": "", "name": "", "ban_duration_in_minute": 0, "cancelled_with_quota": false, "cancellationratefree": false, "force_cancellation_rate_free": false, "should_auto_claim": false, "is_reassign": false, "reassign_only": false, "cancellation_source": "", "message": {"wma": {"en": "", "th": ""}}}, "cancel_detail_log": [], "is_fraud": false, "distribution": "AUTO_ASSIGN", "remarks": [], "fraud_status": "", "verified_rider_photo_urls": [{"photo_url": "fleet_private/verified_rider_photo/LMF-240723-025021216/verified_rider_photo_ref__782a442598524217952f80fed0e67102", "is_compare_face_success": false, "created_at": {"$date": "2024-07-23T04:00:38.434Z"}}], "should_verify": true, "delivering_photo_status": "", "delivering_photo_urls": [], "is_require_delivering_photo": true, "shifts": [], "prediction": {"estimated_cooking_time_second": 120, "estimated_user_waiting_time_second": 120}, "is_auto_assigned": true, "is_auto_accept": false, "is_fully_auto_accept": false, "assigned_to_queue": false, "is_b2b": false, "is_mo_secondary": false, "is_distributed": true, "defer_duration": 0, "is_throttled": true, "is_deferred": false, "throttled_round": 1, "accepted_with_batch_optimized": true, "delivering_round": 1, "trip_id": "TRIP-240723-390544303", "verified_device_id": "1721297029485-2010532416406104943", "accepted_device_id": "1721297029485-2010532416406104943", "delivering_device_id": "", "predicted_completed_time": {"$date": "2024-07-23T03:54:58.137Z"}, "history_location": {"DRIVER_ARRIVED_RESTAURANT": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T04:00:11.127Z"}}, "ASSIGNING_DRIVER": {"lat": 13.722683, "lng": 100.580679, "updated_at": {"$date": "2024-07-23T03:59:33.017Z"}}, "DRIVER_TO_RESTAURANT": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T03:59:57.952Z"}}, "DRIVER_TO_DESTINATION": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T04:00:20.047Z"}}, "ARRIVED": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T04:00:25.568Z"}}, "COMPLETED": {"lat": 13.7223483, "lng": 100.5804233, "updated_at": {"$date": "2024-07-23T04:00:39.511Z"}}}, "has_exempted_words": false, "exempted_words": [], "driver_action_history": {}, "actual_assigning_at": {"$date": "2024-07-23T03:55:02.326Z"}, "first_routed_uwt": 1, "leave_prev_stop_at": {"$date": "2024-07-23T03:59:58.191Z"}, "processed_by_single_distribution": false, "is_skip_incomplete_qr_promptpay_payment": false, "driver_type": "TESTER", "driver_vendor_id": "", "owner_changed_at": {"$date": "2024-07-23T03:54:59.894Z"}}]