[{"_id": {"$oid": "{{111999 | id }}"}, "trip_id": "TRIP_CS_CANCEL", "driver_id": "DRIVER_CS_CANCEL", "status": "INIT", "revenueprincipalmodel": true, "routes": [{"id": "PICKUP_1", "action": "PICKUP", "orders": [{"order_id": "LMF-FULL-TIME-ONGOING-TRIP-1", "stop_id": 0, "done": true}]}, {"id": "PICKUP_2", "action": "PICKUP", "orders": [{"order_id": "LMF-FULL-TIME-ONGOING-TRIP-2", "stop_id": 0, "done": true}]}, {"id": "PICKUP_3", "action": "PICKUP", "orders": [{"order_id": "LMF-FULL-TIME-ONGOING-TRIP-3", "stop_id": 0, "done": true}]}, {"id": "DROP_OFF_1", "action": "DROP_OFF", "orders": [{"order_id": "LMF-FULL-TIME-ONGOING-TRIP-1", "stop_id": 1, "done": false}]}, {"id": "DROP_OFF_2", "action": "DROP_OFF", "orders": [{"order_id": "LMF-FULL-TIME-ONGOING-TRIP-2", "stop_id": 1, "done": false}]}, {"id": "DROP_OFF_3", "action": "DROP_OFF", "orders": [{"order_id": "LMF-FULL-TIME-ONGOING-TRIP-3", "stop_id": 1, "done": false}]}], "orders": [{"order_id": "BCP_CS_CANCEL_2", "status": "DRIVER_TO_RESTAURANT", "service_type": "food"}], "driver_wage_summary": {"base_wage": 40, "distance_wage": 0, "extra_charge": 0, "addition_service_wage": 0, "shift_deduction": 0, "total_driver_wage": 40, "commission_rate": 0, "commission": 0, "addition_service_commission": 0, "withholding_tax": 1.29, "transfer_amount": 0, "outstanding": 0}}]