[{"_id": {"$oid": "{{rand_objid}}"}, "name": "food", "region_code": "CHIANG_MAI_2", "incentive_id": "1q2w3e4r5t6y7u8i", "description": "just description", "active": true, "payment_type": "DAILY", "order_tier": [{"min_order_amount": 1, "max_order_amount": 10, "incentive_amount": 150}], "geometry": {"type": "Multipolygon", "coordinates": [[[[98.26995849609375, 18.243698796576282], [99.52789306640625, 18.243698796576282], [99.52789306640625, 19.210022196386085], [98.26995849609375, 19.210022196386085], [98.26995849609375, 18.243698796576282]]]]}, "service_types": ["food"], "date_range": {"start": {"$date": "2020-12-23T00:01:48.581Z"}, "end": {"$date": "2020-12-23T23:01:48.581Z"}}, "times": [{"start": "8:00:00", "end": "17:59:59"}, {"start": "18:00:00", "end": "21:00:00"}]}, {"_id": {"$oid": "{{rand_objid}}"}, "name": "mart", "region_code": "CHIANG_MAI_2", "incentive_id": "1q2w3e4r5t6y7u8i", "description": "just description", "active": true, "payment_type": "DAILY", "order_tier": [{"min_order_amount": 1, "max_order_amount": 10, "incentive_amount": 150}], "geometry": {"type": "Multipolygon", "coordinates": [[[[98.26995849609375, 18.243698796576282], [99.52789306640625, 18.243698796576282], [99.52789306640625, 19.210022196386085], [98.26995849609375, 19.210022196386085], [98.26995849609375, 18.243698796576282]]]]}, "service_types": ["mart"], "date_range": {"start": {"$date": "2020-12-23T00:01:48.581Z"}, "end": {"$date": "2020-12-23T23:01:48.581Z"}}, "times": [{"start": "8:00:00", "end": "17:59:59"}, {"start": "18:00:00", "end": "21:00:00"}]}, {"_id": {"$oid": "{{rand_objid}}"}, "name": "messenger", "region_code": "CHIANG_MAI_2", "incentive_id": "1q2w3e4r5t6y7u8i", "description": "just description", "active": true, "payment_type": "DAILY", "order_tier": [{"min_order_amount": 1, "max_order_amount": 10, "incentive_amount": 150}], "geometry": {"type": "Multipolygon", "coordinates": [[[[98.26995849609375, 18.243698796576282], [99.52789306640625, 18.243698796576282], [99.52789306640625, 19.210022196386085], [98.26995849609375, 19.210022196386085], [98.26995849609375, 18.243698796576282]]]]}, "service_types": ["messenger"], "date_range": {"start": {"$date": "2020-12-23T00:01:48.581Z"}, "end": {"$date": "2020-12-23T23:01:48.581Z"}}, "times": [{"start": "8:00:00", "end": "17:59:59"}, {"start": "18:00:00", "end": "21:00:00"}]}, {"_id": {"$oid": "{{rand_objid}}"}, "name": "food,mart", "region_code": "CHIANG_MAI_2", "incentive_id": "1q2w3e4r5t6y7u8i", "description": "just description", "active": true, "payment_type": "DAILY", "order_tier": [{"min_order_amount": 1, "max_order_amount": 10, "incentive_amount": 150}], "geometry": {"type": "Multipolygon", "coordinates": [[[[98.26995849609375, 18.243698796576282], [99.52789306640625, 18.243698796576282], [99.52789306640625, 19.210022196386085], [98.26995849609375, 19.210022196386085], [98.26995849609375, 18.243698796576282]]]]}, "service_types": ["food", "mart"], "date_range": {"start": {"$date": "2020-12-23T00:01:48.581Z"}, "end": {"$date": "2020-12-23T23:01:48.581Z"}}, "times": [{"start": "8:00:00", "end": "17:59:59"}, {"start": "18:00:00", "end": "21:00:00"}]}, {"_id": {"$oid": "{{rand_objid}}"}, "name": "food,messenger", "region_code": "CHIANG_MAI_2", "incentive_id": "1q2w3e4r5t6y7u8i", "description": "just description", "active": true, "payment_type": "DAILY", "order_tier": [{"min_order_amount": 1, "max_order_amount": 10, "incentive_amount": 150}], "geometry": {"type": "Multipolygon", "coordinates": [[[[98.26995849609375, 18.243698796576282], [99.52789306640625, 18.243698796576282], [99.52789306640625, 19.210022196386085], [98.26995849609375, 19.210022196386085], [98.26995849609375, 18.243698796576282]]]]}, "service_types": ["food", "messenger"], "date_range": {"start": {"$date": "2020-12-23T00:01:48.581Z"}, "end": {"$date": "2020-12-23T23:01:48.581Z"}}, "times": [{"start": "8:00:00", "end": "17:59:59"}, {"start": "18:00:00", "end": "21:00:00"}]}, {"_id": {"$oid": "{{rand_objid}}"}, "name": "mart,messenger", "region_code": "CHIANG_MAI_2", "incentive_id": "1q2w3e4r5t6y7u8i", "description": "just description", "active": true, "payment_type": "DAILY", "order_tier": [{"min_order_amount": 1, "max_order_amount": 10, "incentive_amount": 150}], "geometry": {"type": "Multipolygon", "coordinates": [[[[98.26995849609375, 18.243698796576282], [99.52789306640625, 18.243698796576282], [99.52789306640625, 19.210022196386085], [98.26995849609375, 19.210022196386085], [98.26995849609375, 18.243698796576282]]]]}, "service_types": ["mart", "messenger"], "date_range": {"start": {"$date": "2020-12-23T00:01:48.581Z"}, "end": {"$date": "2020-12-23T23:01:48.581Z"}}, "times": [{"start": "8:00:00", "end": "17:59:59"}, {"start": "18:00:00", "end": "21:00:00"}]}, {"_id": {"$oid": "{{rand_objid}}"}, "name": "food,mart,messenger", "region_code": "CHIANG_MAI_2", "incentive_id": "1q2w3e4r5t6y7u8i", "description": "just description", "active": true, "payment_type": "DAILY", "order_tier": [{"min_order_amount": 1, "max_order_amount": 10, "incentive_amount": 150}], "geometry": {"type": "Multipolygon", "coordinates": [[[[98.26995849609375, 18.243698796576282], [99.52789306640625, 18.243698796576282], [99.52789306640625, 19.210022196386085], [98.26995849609375, 19.210022196386085], [98.26995849609375, 18.243698796576282]]]]}, "service_types": ["food", "mart", "messenger"], "date_range": {"start": {"$date": "2020-12-23T00:01:48.581Z"}, "end": {"$date": "2020-12-23T23:01:48.581Z"}}, "times": [{"start": "8:00:00", "end": "17:59:59"}, {"start": "18:00:00", "end": "21:00:00"}]}]