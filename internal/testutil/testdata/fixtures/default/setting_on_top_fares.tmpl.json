[{"_id": {"$oid": "{{1 | id}}"}, "on_top_fare_id": "1a375981ac874d37a655cf43cf72110c", "label": "เพิ่ม 10 บาทจากค่าส่ง", "name": "on-top-it-1", "title": "ทดสอบหัวข้อ", "description": "ทดสอบคำอธิบาย", "region": "BAKGKOK", "created_at": {"$date": "2020-12-23T18:01:48.581Z"}, "status": "ACTIVE", "scheme": "BASKET_SIZE", "conditions": [{"flat_rate_amount": 10.0, "basket_prices": [{"from": 1.0, "to": 10.0, "amount": 10.0}], "status": "INACTIVE", "days": ["SUN"], "time": [{"begin": "10:00", "end": "16:00"}], "payment_for_basket": {"cash": false, "cash_collection": false, "cash_advance_coupon": false, "cash_advance_e_payment": false, "rlp": true, "credit_card": true}, "service_types": ["food", "mart"]}], "geometry": {"type": "MultiPolygon", "coordinates": [[[[100.522155761719, 13.7754002023634], [100.457096099854, 13.7670638630723], [100.456581115723, 13.7453879904786], [100.455722808838, 13.7088679472215], [100.503273010254, 13.690022049868], [100.555629730225, 13.7021970058976], [100.56884765625, 13.7288796340912], [100.563182830811, 13.7628955819699], [100.522155761719, 13.7754002023634]]]]}}, {"_id": {"$oid": "{{2 | id}}"}, "on_top_fare_id": "1a375981ac874d37a655cf43cf72110d", "name": "on-top-it-2-no-geo", "region": "BAKGKOK", "created_at": {"$date": "2020-12-23T18:01:48.581Z"}, "restaurant_ids": ["1727MA"], "scheme": "FLAT_RATE", "status": "ACTIVE", "conditions": [{"flat_rate_amount": 10.0, "basket_prices": [], "status": "INACTIVE", "days": ["SUN"], "time": [{"begin": "10:00", "end": "16:00"}], "service_types": ["food", "mart"]}]}, {"_id": {"$oid": "{{3 | id}}"}, "on_top_fare_id": "1a375981ac874d37a655cf43cf72110e", "label": "โบนัสไลน์แมนให้ผ่อน", "name": "ontop-installment-it", "title": "ทดสอบหัวข้อ", "description": "ทดสอบคำอธิบาย", "region": "BAKGKOK", "created_at": {"$date": "2020-12-23T18:01:48.581Z"}, "status": "ACTIVE", "scheme": "INSTALLMENT", "conditions": [{"flat_rate_amount": 10.0, "status": "INACTIVE", "days": ["SUN"], "time": [{"begin": "10:00", "end": "16:00"}], "payment_for_basket": {"cash": false, "cash_collection": false, "cash_advance_coupon": false, "cash_advance_e_payment": false, "rlp": true, "credit_card": true}, "service_types": ["food", "mart"]}], "geometry": {"type": "MultiPolygon", "coordinates": [[[[100.522155761719, 13.7754002023634], [100.457096099854, 13.7670638630723], [100.456581115723, 13.7453879904786], [100.455722808838, 13.7088679472215], [100.503273010254, 13.690022049868], [100.555629730225, 13.7021970058976], [100.56884765625, 13.7288796340912], [100.563182830811, 13.7628955819699], [100.522155761719, 13.7754002023634]]]]}}]