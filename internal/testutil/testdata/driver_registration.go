package testdata

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"go.mongodb.org/mongo-driver/bson"
)

func ProvideDriverRegistrationTestData(dataStore persistence.DriverRegistrationDataStore) *DriverRegistrationTestData {
	return &DriverRegistrationTestData{
		DataStore: dataStore,
	}
}

type DriverRegistrationTestData struct {
	DataStore persistence.DriverRegistrationDataStore
}

func (d *DriverRegistrationTestData) AdminReviewedWithRequestedUpdate(lineUID string) {
	m := bson.M{"line_uid": Encrypt(lineUID)}
	var count int
	if err := d.DataStore.Count(context.Background(), m, &count); err != nil {
		panic(err)
	}
	err := d.DataStore.Update(context.Background(), m, bson.M{
		"$set": bson.M{
			"status": "REQUESTED_UPDATE",
			"reason": "Need more info",
		},
	})
	if err != nil {
		panic(err)
	}
}

func (d *DriverRegistrationTestData) FindOneByLineID(lineUID string) *model.DriverRegistration {
	var drv model.DriverRegistration
	err := d.DataStore.FindOne(context.Background(), bson.M{"line_uid": Encrypt(lineUID)}, &drv)
	if err != nil {
		panic(err)
	}
	return &drv
}
