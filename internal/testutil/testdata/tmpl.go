package testdata

import (
	"bytes"
	"fmt"
	"math/rand"
	"strconv"
	"text/template"
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

func RenderTemplate(doc string) (string, error) {
	tmpl, err := template.New("root").Funcs(template.FuncMap{
		"dec":               Decrypt,
		"enc":               Encrypt,
		"rand_objid":        RandomObjectId,
		"id":                ObjectId,
		"isodate_now":       ISODateNow,
		"isodate_fromnow":   ISODateFromNow,
		"isodate_beforenow": ISODateBeforeNow,
		"thai_nation_id":    ThaiNationID,
		"uuid":              UUID,
	}).Parse(doc)
	if err != nil {
		return "", err
	}

	buf := &bytes.Buffer{}
	if err := tmpl.Execute(buf, nil); err != nil {
		return "", err
	}
	return buf.String(), nil
}

func Decrypt(data string) (string, error) {
	return crypt.EncryptedString(data).Decrypt()
}

func Encrypt(data string) string {
	return crypt.EncryptedString(data).Encrypt()
}

func ISODateNow() string {
	//"2000-01-01T00:00:00.000Z"
	return fmt.Sprintf(`%s`, time.Now().UTC().Format("2006-01-02T15:04:05.999Z"))
}

func ISODateFromNow(duration string) string {
	dur, err := time.ParseDuration(duration)
	if err != nil {
		panic(fmt.Errorf("isodate_fromnow got invalid duration format: %v", err))
	}
	//"2000-01-01T00:00:00.000Z"
	tn := time.Now().UTC().Add(dur)
	return fmt.Sprintf(`%s`, tn.Format("2006-01-02T15:04:05.999Z"))
}

func ISODateBeforeNow(duration string) string {
	dur, err := time.ParseDuration(duration)
	if err != nil {
		panic(fmt.Errorf("isodate_fromnow got invalid duration format: %v", err))
	}
	//"2000-01-01T00:00:00.000Z"
	tn := time.Now().UTC().Add(-1 * dur)
	return fmt.Sprintf(`%s`, tn.Format("2006-01-02T15:04:05.999Z"))
}

func RandomObjectId() string {
	return primitive.NewObjectID().Hex()
}

func ObjectId(num int) string {
	var i uint32
	i = uint32(num)
	var b [12]byte
	b[9] = byte(i >> 16)
	b[10] = byte(i >> 8)
	b[11] = byte(i)

	oid := primitive.ObjectID(b)
	hex := oid.Hex()
	return hex
}

// ThaiNationID generates random thai nation id
func ThaiNationID() string {
	num := 100000000000 + rand.Int63n(100000000000)
	copynum := num

	var sum int64
	for i := 1; i < 13; i++ {
		sum += int64(i+1) * (num % 10)
		num /= 10
	}

	sum = sum % 11
	check := 11 - sum
	tssn := (copynum * 10) + (check % 10)
	return strconv.FormatInt(tssn, 10)
}

func UUID() string {
	return utils.GenerateUUID()
}
