package testdata_test

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
)

func TestFixtures_InitFixture(t *testing.T) {
	t.<PERSON>llel()
	fixtures := &testdata.Fixtures{}
	ctn := ittest.NewContainer(t)

	err := fixtures.InitFixture(ctn.DBConnectionForTest, "not-exist-directory")
	assert.Error(t, err)
}

func TestFixtures_InitDefault(t *testing.T) {
	t.Parallel()
	ctn := ittest.NewContainer(t)
	for i := 0; i < 1000; i++ {
		err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_unit_test")
		assert.NoError(t, err)
	}
}
