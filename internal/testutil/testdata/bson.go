package testdata

import (
	"io"
	"os"
	"path/filepath"
	"runtime"

	"go.mongodb.org/mongo-driver/bson"
)

func TemplateToBsonSlice(data string) ([]interface{}, error) {
	data, err := RenderTemplate(data)
	if err != nil {
		return nil, err
	}

	var out []interface{}
	err = bson.UnmarshalExtJSON([]byte(data), false, &out)
	if err != nil {
		return nil, err
	}

	return out, nil
}

func TemplateFileToBsonSlice(absPath string) ([]interface{}, error) {
	file, err := os.Open(absPath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	data, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}

	out, err := TemplateToBsonSlice(string(data))
	if err != nil {
		return nil, err
	}

	return out, nil
}

// filedir compute directory of caller file
func filedir() string {
	_, b, _, _ := runtime.Caller(0)
	basepath := filepath.Dir(b)
	return basepath
}
