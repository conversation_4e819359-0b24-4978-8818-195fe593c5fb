package testdata

import (
	"context"
	"fmt"

	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"go.mongodb.org/mongo-driver/bson"
)

func ProvideProvincesTestData(datastore persistence.ProvinceDataStore) *ProvincesTestData {
	return &ProvincesTestData{datastore: datastore}
}

type ProvincesTestData struct {
	datastore persistence.ProvinceDataStore
}

func (td *ProvincesTestData) SetNormalAutoApprove(regionName string) {
	cond := bson.M{"name": regionName}
	if exist := td.datastore.IsExist(context.Background(), cond); !exist {
		panic(fmt.Sprintf("expect province to be exist name=%s", regionName))
	}
	err := td.datastore.Update(context.Background(), cond, bson.M{
		"$set": bson.M{
			"is_normal_auto_approve": true,
		},
	})
	if err != nil {
		panic(fmt.Errorf("failed to set normal auto approve. %w", err))
	}
}

func (td *ProvincesTestData) SetWhitelistAutoApprove(regionName string) {
	cond := bson.M{"name": regionName}
	if exist := td.datastore.IsExist(context.Background(), cond); !exist {
		panic(fmt.Sprintf("expect province to be exist name=%s", regionName))
	}
	err := td.datastore.Update(context.Background(), cond, bson.M{
		"$set": bson.M{
			"is_whitelist_auto_approve": true,
		},
	})
	if err != nil {
		panic(fmt.Errorf("failed to set normal auto approve. %w", err))
	}
}

func (td *ProvincesTestData) SetAllWhitelistAutoApprove(v bool) {
	_, err := td.datastore.UpdateAll(context.Background(), bson.M{}, bson.M{
		"$set": bson.M{
			"is_whitelist_auto_approve": v,
		},
	})
	if err != nil {
		panic(fmt.Errorf("failed to set all whitelist auto approve. %w", err))
	}
}
