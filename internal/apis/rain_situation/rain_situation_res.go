package rain_situation

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type WeatherRes struct {
	Status string `json:"status"`
	Total  int    `json:"total"`
}

type RainSituationRes struct {
	ID                 string               `json:"id"`
	Name               string               `json:"name"`
	Region             string               `json:"region"`
	RainStatus         model.RainStatus     `json:"rainStatus"`
	OverrideRainStatus model.RainStatus     `json:"overrideRainStatus"`
	OverridePeriod     model.OverridePeriod `json:"overridePeriod"`
	OverrideAt         time.Time            `json:"overrideAt"`
	CreatedAt          time.Time            `json:"createdAt"`
	CreatedBy          string               `json:"createdBy"`
	UpdatedAt          time.Time            `json:"updatedAt"`
	UpdatedBy          string               `json:"updatedBy"`
	Remarks            []model.Remark       `bson:"remarks"`
	Coordinates        Coordinates          `json:"coordinates"`
}

func NewRainSituationRes(r model.RainSituation) RainSituationRes {
	return RainSituationRes{
		ID:                 r.ID.Hex(),
		Name:               r.Name,
		Region:             r.Region,
		RainStatus:         r.Rain<PERSON>tatus,
		OverrideRainStatus: r.OverrideRainStatus,
		OverridePeriod:     r.OverridePeriod,
		OverrideAt:         r.OverrideAt,
		Remarks:            r.Remarks,
		CreatedBy:          r.CreatedBy,
		CreatedAt:          r.CreatedAt,
		UpdatedAt:          r.UpdatedAt,
		UpdatedBy:          r.UpdatedBy,
		Coordinates:        Coordinates(r.Geometry.Coordinates),
	}
}

type RainSituationMapRes struct {
	Region         string             `json:"region"`
	RegionGeometry polygon.Geometry   `json:"regionGeometry"`
	RainSituations []RainSituationMap `json:"rainSituations"`
}
type RainSituationGeometryRes struct {
	Type        string      `json:"type"`
	Coordinates Coordinates `json:"coordinates"`
}

// RainSituationMap represents a rain situation on the map.
type RainSituationMap struct {
	DisplayName      string                      `json:"displayName"`
	Name             string                      `json:"name"`
	OnTopAmount      float64                     `json:"onTopAmount"`
	Distance         float64                     `json:"distance"` // Distance in kilometers
	ShortestLocation model.Location              `json:"shortestLocation"`
	Geometry         model.RainSituationGeometry `json:"geometry"`
}

type Location struct {
}

type BulkUpdateRainSituationRes struct {
	Name            string `json:"name"`
	RainStatus      string `json:"rainStatus"`
	RainStatusEndAt string `json:"rainStatusEndAt"`
	Error           string `json:"error"`
}
