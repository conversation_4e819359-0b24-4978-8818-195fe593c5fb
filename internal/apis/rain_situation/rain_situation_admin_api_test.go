package rain_situation_test

import (
	"context"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rain_situation"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack/mock_slack"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type rainSituationAdminApiDeps struct {
	rainSituationRepo    *mock_repository.MockRainSituationRepository
	rainSituationService *mock_service.MockRainSituationService
	transactionService   *mock_transaction.MockTxnHelper
	slack                *mock_slack.MockSlack
}

func newTestRainSituationAdmin(t gomock.TestReporter, config rain_situation.Config, rainSituationServiceCfg *service.AtomicRainSituationConfig, globalConfig config.GlobalConfig) (*rain_situation.RainSituationAdminAPI, *rainSituationAdminApiDeps, func()) {
	ctrl := gomock.NewController(t)

	deps := &rainSituationAdminApiDeps{
		rainSituationRepo:    mock_repository.NewMockRainSituationRepository(ctrl),
		rainSituationService: mock_service.NewMockRainSituationService(ctrl),
		transactionService:   mock_transaction.NewMockTxnHelper(ctrl),
		slack:                mock_slack.NewMockSlack(ctrl),
	}

	return rain_situation.ProvideRainSituationAdminAPI(
			config,
			deps.rainSituationRepo,
			deps.rainSituationService,
			deps.transactionService,
			deps.slack,
			rainSituationServiceCfg,
			globalConfig,
		), deps, func() {
			ctrl.Finish()
		}
}

type ListRainSituationRes struct {
	CountTotal int                               `json:"countTotal"`
	Data       []rain_situation.RainSituationRes `json:"data"`
}

func TestRainSituationAdminAPI_ListRainSituations(t *testing.T) {
	t.Parallel()

	req := func(req *rain_situation.ListRainSituationReq) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("GET", "/v1/admin/rain-situations", testutil.JSON(req))
	}

	t.Run("should return bad request if request binding fails", func(tt *testing.T) {
		tt.Parallel()

		ctx, recorder := testutil.TestRequestContext("POST", "/v1/admin/rain-situations", nil)
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		deps.rainSituationRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RainSituation{}, nil).Times(0)
		deps.rainSituationRepo.EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(2, nil).Times(0)

		api.ListRainSituations(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should return internal server error if List fails", func(tt *testing.T) {
		tt.Parallel()
		r := &rain_situation.ListRainSituationReq{}
		ctx, recorder := req(r)
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.rainSituationRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RainSituation{}, errors.New("error"))
		deps.rainSituationRepo.EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(2, nil).Times(0)

		api.ListRainSituations(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return internal server error if CountWithQuery fails", func(tt *testing.T) {
		tt.Parallel()
		r := &rain_situation.ListRainSituationReq{}
		ctx, recorder := req(r)
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.rainSituationRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RainSituation{}, nil)
		deps.rainSituationRepo.EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(0, errors.New("count error"))

		api.ListRainSituations(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return success response if List and CountWithQuery succeed", func(tt *testing.T) {
		tt.Parallel()
		r := &rain_situation.ListRainSituationReq{}
		ctx, recorder := req(r)
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.rainSituationRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RainSituation{{Region: "BKK"}, {Region: "TAK"}}, nil)
		deps.rainSituationRepo.EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(2, nil)

		api.ListRainSituations(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual ListRainSituationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, actual.CountTotal)
		require.Equal(tt, "BKK", actual.Data[0].Region)
		require.Equal(tt, "TAK", actual.Data[1].Region)
	})

	t.Run("should return success response if List with isDownload flag", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := testutil.TestRequestContext("GET", "/v1/admin/rain-situations?isDownload=true", nil)
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.rainSituationRepo.EXPECT().FindWithQueryAndSortSelector(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RainSituation{{Name: "BKK_1"}, {Name: "BKK_2"}}, nil)
		deps.rainSituationRepo.EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(2, nil)

		api.ListRainSituations(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual ListRainSituationRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, actual.CountTotal)
		require.Equal(tt, "BKK_1", actual.Data[0].Name)
		require.Equal(tt, "BKK_2", actual.Data[1].Name)
	})
}

func TestRainSituationAdminAPI_CreateRainSituation(t *testing.T) {
	t.Parallel()

	cReq := &rain_situation.CreateRainSituationReq{
		Name:   "Test Rain Situation",
		Region: "BKK",
	}

	req := func(req *rain_situation.CreateRainSituationReq) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/admin/rain-situations", testutil.JSON(req))
	}

	t.Run("should return bad request if request binding fails", func(tt *testing.T) {
		tt.Parallel()

		ctx, recorder := testutil.TestRequestContext("POST", "/v1/admin/rain-situations", nil)
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		deps.rainSituationRepo.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil).Times(0)

		api.CreateRainSituation(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should return success no content response if Create succeed", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req(cReq)
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.rainSituationRepo.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		api.CreateRainSituation(ctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})
}

func TestRainSituationAdminAPI_DeleteRainSituation(t *testing.T) {
	t.Parallel()

	t.Run("should return internal server error if Delete fails", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := testutil.TestRequestContext("DELETE", "/v1/admin/rain-situations/123", nil)
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: "123"},
		}
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		deps.rainSituationRepo.EXPECT().Delete(gomock.Any(), "123").Return(errors.New("error"))

		api.DeleteRainSituation(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return success no content response if Delete succeeds", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := testutil.TestRequestContext("DELETE", "/v1/admin/rain-situations/4848484848484", nil)
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: "4848484848484"},
		}
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		deps.rainSituationRepo.EXPECT().Delete(gomock.Any(), "4848484848484").Return(nil)

		api.DeleteRainSituation(ctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})
}

func TestRainSituationAdminAPI_UpdateRainSituation(t *testing.T) {
	t.Parallel()
	n := time.Now()
	a := "admin"
	uReq := &rain_situation.UpdateRainSituationReq{
		Name:               "Updated Rain Situation",
		Region:             "BKK",
		RainStatus:         model.RainStatusHeavyRain,
		OverrideRainStatus: model.RainStatusLightRain,
		OverridePeriod: model.OverridePeriod{
			Start: n,
			End:   n,
		},
		OverrideAt: n,
		Remarks:    []model.Remark{{Message: "remark"}},
		UpdatedBy:  a,
	}

	req := func(req *rain_situation.UpdateRainSituationReq) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/admin/rain-situations/123", testutil.JSON(req))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: "123"},
		}

		return ctx, recorder
	}

	t.Run("should return bad request if request binding fails", func(tt *testing.T) {
		tt.Parallel()

		ctx, _ := testutil.TestRequestContext("PUT", "/v1/admin/rain-situations/123", nil)
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: "123"},
		}
		api, _, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		api.UpdateRainSituation(ctx)

		require.Equal(tt, http.StatusBadRequest, ctx.Writer.Status())
	})

	t.Run("should return internal server error if Get fails", func(tt *testing.T) {
		tt.Parallel()
		ctx, _ := testutil.TestRequestContext("PUT", "/v1/admin/rain-situations/123", testutil.JSON(uReq))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: "123"},
		}
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		deps.rainSituationRepo.EXPECT().Get(gomock.Any(), "123").
			Return(nil, errors.New("error"))

		api.UpdateRainSituation(ctx)

		require.Equal(tt, http.StatusInternalServerError, ctx.Writer.Status())
	})

	t.Run("should return not found if the RainSituation does not exist", func(tt *testing.T) {
		tt.Parallel()
		ctx, _ := testutil.TestRequestContext("PUT", "/v1/admin/rain-situations/123", testutil.JSON(uReq))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: "123"},
		}
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		deps.rainSituationRepo.EXPECT().Get(gomock.Any(), "123").
			Return(nil, nil)

		api.UpdateRainSituation(ctx)

		require.Equal(tt, http.StatusNotFound, ctx.Writer.Status())
	})

	t.Run("should return internal server error if Update fails", func(tt *testing.T) {
		tt.Parallel()
		ctx, _ := testutil.TestRequestContext("PUT", "/v1/admin/rain-situations/123", testutil.JSON(uReq))
		ctx.Params = gin.Params{
			gin.Param{Key: "id", Value: "123"},
		}
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		existingRainSituation := &model.RainSituation{
			Region: "Old Region",
		}

		deps.rainSituationRepo.EXPECT().Get(gomock.Any(), "123").
			Return(existingRainSituation, nil)
		deps.rainSituationRepo.EXPECT().Update(gomock.Any(), "123", existingRainSituation, gomock.Any()).
			Return(errors.New("update error"))

		api.UpdateRainSituation(ctx)

		require.Equal(tt, http.StatusInternalServerError, ctx.Writer.Status())
	})

	t.Run("should return success response if Update succeed", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req(uReq)
		api, deps, finish := newTestRainSituationAdmin(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()

		existingRainSituation := &model.RainSituation{
			Region: "Old Region",
		}

		deps.rainSituationRepo.EXPECT().Get(gomock.Any(), "123").
			Return(existingRainSituation, nil)
		deps.rainSituationRepo.EXPECT().Update(gomock.Any(), "123", existingRainSituation, gomock.Any()).
			Return(nil)

		api.UpdateRainSituation(ctx)

		require.Equal(tt, http.StatusOK, ctx.Writer.Status())

		var response rain_situation.RainSituationRes
		testutil.DecodeJSON(tt, recorder.Body, &response)
		require.Equal(tt, "Updated Rain Situation", response.Name)
		require.Equal(tt, "BKK", response.Region)
		require.Equal(tt, model.RainStatusHeavyRain, response.RainStatus)
		require.Equal(tt, model.RainStatusLightRain, response.OverrideRainStatus)
	})
}

func TestRainSituationAdminAPI_BulkUpdateRainSituation(t *testing.T) {
	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/admin/bulk/rain-situations")
		gctx.Body().MultipartForm().
			File("file", "file.csv", content).
			Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	type rainSituationUpdateRepo func(ctx context.Context, id string, rainSituation *model.RainSituation, rainingStatus types.StringSet, opts ...repository.Option) error
	checkingUpdateRainSituationFunc := func(ttt *testing.T, exptSrc model.RainSituation, exptErr error) rainSituationUpdateRepo {
		return func(ctx context.Context, id string, rainSituation *model.RainSituation, rainingStatus types.StringSet, opts ...repository.Option) error {
			require.NotNil(ttt, rainSituation)
			require.Equal(ttt, exptSrc, *rainSituation)
			return exptErr
		}
	}

	adminPageUrl := "https://lm-admin.line-apps-beta.com/rain-situations"
	wikiPageUrl := "https://linemanwongnai.atlassian.net/wiki/spaces/LMFleet/pages/853608726/How+to+-+Rain+notification+handbook#Message"
	defaultConfig := rain_situation.Config{
		RainSituationSlackNotificationEnabled:      true,
		RainSituationSlackNotificationLimit:        30,
		RainSituationSlackNotificationAdminPageUrl: adminPageUrl,
		RainSituationSlackNotificationWikiPageUrl:  wikiPageUrl,
	}

	defaultRainSituationConfig := &service.AtomicRainSituationConfig{
		Config: service.RainSituationCfg{
			RainingStatuses: types.NewStringSet("LIGHT_RAIN", "HEAVY_RAIN"),
		},
	}

	type ExpectSlackNotifyData struct {
		rainingOverrides  []string
		rainingWeatherAPI []string
		undetected        []string
	}

	expectSlackNotify := func(ttt *testing.T, deps *rainSituationAdminApiDeps, expected ExpectSlackNotifyData) {
		deps.slack.EXPECT().
			Notify(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, req *slack.NotifyRequest) error {
				for _, x := range expected.rainingOverrides {
					require.Contains(ttt, req.Payload.Attachments[0].Blocks[1].Text.Text, x)
				}
				for _, x := range expected.rainingWeatherAPI {
					require.Contains(ttt, req.Payload.Attachments[1].Blocks[1].Text.Text, x)
				}
				for _, x := range expected.undetected {
					require.Contains(ttt, req.Payload.Attachments[2].Blocks[1].Text.Text, x)
				}
				require.Contains(ttt, *req.Payload.Attachments[0].Blocks[0].Accessory.Url, adminPageUrl)
				require.Contains(ttt, *req.Payload.Attachments[1].Blocks[0].Accessory.Url, adminPageUrl)
				require.Contains(ttt, *req.Payload.Attachments[2].Blocks[0].Accessory.Url, adminPageUrl)
				require.Contains(ttt, *(*req.Payload.Attachments[3].Blocks[0].Elements)[0].Url, wikiPageUrl)
				return nil
			})
	}

	expectFind := func(ttt *testing.T, deps *rainSituationAdminApiDeps, expected []model.RainSituation) {
		deps.rainSituationRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(expected, nil)
	}

	t.Run("should update rain situation successfully", func(tt *testing.T) {
		api, deps, finish := newTestRainSituationAdmin(tt, defaultConfig, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		mockedTimeNow := time.Date(2023, 07, 26, 14, 00, 00, 0, timeutils.BangkokLocation())
		timeutils.FreezeWithTime(mockedTimeNow.Unix() * 1000)
		defer timeutils.Unfreeze()

		ctx, recorder := makeReq(`District Name,Rain Status,Rain Status End At
BKK_DISTRICT,HEAVY_RAIN,2023-07-26T15:00:00.000+07:00
CNX_DISTRICT,LIGHT_RAIN,2023-07-26T16:00:00+07:00
`)

		ctx.Set("REQUEST_USER_EMAIL", "<EMAIL>")

		deps.transactionService.EXPECT().
			WithTxn(ctx, gomock.Any(), gomock.Any()).
			Return(nil, nil).
			Times(1)

		hexIDs := []primitive.ObjectID{
			primitive.NewObjectID(),
			primitive.NewObjectID(),
		}
		deps.rainSituationRepo.EXPECT().
			GetByName(ctx, "BKK_DISTRICT", gomock.Any()).
			Return(&model.RainSituation{
				ID: hexIDs[0],
			}, nil)

		deps.rainSituationRepo.EXPECT().
			GetByName(ctx, "CNX_DISTRICT", gomock.Any()).
			Return(&model.RainSituation{
				ID: hexIDs[1],
			}, nil)

		deps.rainSituationRepo.EXPECT().
			Update(ctx, hexIDs[0].Hex(), gomock.Any(), gomock.Any()).
			DoAndReturn(checkingUpdateRainSituationFunc(tt, model.RainSituation{
				ID:                 hexIDs[0],
				OverrideRainStatus: "HEAVY_RAIN",
				OverridePeriod: model.OverridePeriod{
					Start: mockedTimeNow,
					End:   time.Date(2023, 07, 26, 15, 00, 00, 0, timeutils.BangkokLocation()),
				},
				UpdatedBy:  "<EMAIL>",
				OverrideAt: mockedTimeNow,
			}, nil))

		deps.rainSituationRepo.EXPECT().
			Update(ctx, hexIDs[1].Hex(), gomock.Any(), gomock.Any()).
			DoAndReturn(checkingUpdateRainSituationFunc(tt, model.RainSituation{
				ID:                 hexIDs[1],
				OverrideRainStatus: "LIGHT_RAIN",
				OverridePeriod: model.OverridePeriod{
					Start: mockedTimeNow,
					End:   time.Date(2023, 07, 26, 16, 00, 00, 0, timeutils.BangkokLocation()),
				},
				UpdatedBy:  "<EMAIL>",
				OverrideAt: mockedTimeNow,
			}, nil))

		// expected to find overrides
		expectFind(tt, deps, []model.RainSituation{{
			ID:                 hexIDs[1],
			Name:               "CNX_DISTRICT",
			OverrideRainStatus: "LIGHT_RAIN",
			OverridePeriod: model.OverridePeriod{
				Start: time.Now().Add(-time.Hour),
				End:   time.Now().Add(time.Hour),
			},
			UpdatedBy:  "<EMAIL>",
			OverrideAt: mockedTimeNow,
		}, {
			ID:                 hexIDs[0],
			Name:               "BKK_DISTRICT",
			OverrideRainStatus: "HEAVY_RAIN",
			OverridePeriod: model.OverridePeriod{
				Start: time.Now().Add(-time.Hour),
				End:   time.Now().Add(time.Hour),
			},
			UpdatedBy:  "<EMAIL>",
			OverrideAt: mockedTimeNow,
		}})

		expectSlackNotify(tt, deps, ExpectSlackNotifyData{
			rainingWeatherAPI: []string{},
			rainingOverrides:  []string{"BKK_DISTRICT", "CNX_DISTRICT"},
			undetected:        []string{},
		})

		api.BulkUpdateRainSituation(ctx)

		require.Equal(tt, http.StatusOK, ctx.Writer.Status())
		var response map[string]any
		testutil.DecodeJSON(tt, recorder.Body, &response)
		successResp, _ := response["success"].([]any)
		require.Len(tt, successResp, 2)
		require.Equal(tt, map[string]any{
			"name":            "BKK_DISTRICT",
			"rainStatus":      "HEAVY_RAIN",
			"rainStatusEndAt": "2023-07-26T15:00:00+07:00",
			"error":           "",
		}, successResp[0])
		require.Equal(tt, map[string]any{
			"name":            "CNX_DISTRICT",
			"rainStatus":      "LIGHT_RAIN",
			"rainStatusEndAt": "2023-07-26T16:00:00+07:00",
			"error":           "",
		}, successResp[1])
	})

	t.Run("should update rain situation failed", func(tt *testing.T) {
		api, deps, finish := newTestRainSituationAdmin(tt, defaultConfig, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		mockedTimeNow := time.Date(2023, 07, 26, 14, 00, 00, 0, timeutils.BangkokLocation())
		timeutils.FreezeWithTime(mockedTimeNow.Unix() * 1000)
		defer timeutils.Unfreeze()

		ctx, recorder := makeReq(`District Name,Rain Status,Rain Status End At
DISTRICT_INVALID_RAIN_STATUS,HARD_RAIN,2023-07-26T15:00:00.000+07:00
DISTRICT_INVALID_TIME_FORMAT,HEAVY_RAIN,2023-07-26T15:00:00
DISTRICT_TIME_PASSED,LIGHT_RAIN,2023-07-26T13:00:00.000+07:00
DISTRICT_NOT_FOUND,LIGHT_RAIN,2023-07-26T15:00:00.000+07:00`)

		ctx.Set("REQUEST_USER_EMAIL", "<EMAIL>")

		deps.transactionService.EXPECT().
			WithTxn(ctx, gomock.Any(), gomock.Any()).
			Return(nil, nil).
			Times(1)

		deps.rainSituationRepo.EXPECT().
			GetByName(ctx, "DISTRICT_NOT_FOUND", gomock.Any()).
			Return(nil, repository.ErrNotFound)

		// expected to find no overrides
		expectFind(tt, deps, []model.RainSituation{})

		api.BulkUpdateRainSituation(ctx)

		require.Equal(tt, http.StatusOK, ctx.Writer.Status())
		var response map[string]any
		testutil.DecodeJSON(tt, recorder.Body, &response)
		successResp, _ := response["success"].([]any)
		require.Len(tt, successResp, 0)
		failureResp, _ := response["failure"].([]any)
		require.Len(tt, failureResp, 4)

		exptFailureResp := []any{
			map[string]any{
				"name":            "DISTRICT_INVALID_RAIN_STATUS",
				"rainStatus":      "HARD_RAIN",
				"rainStatusEndAt": "2023-07-26T15:00:00.000+07:00",
				"error":           "invalid RAIN_STATUS: HARD_RAIN",
			},
			map[string]any{
				"name":            "DISTRICT_INVALID_TIME_FORMAT",
				"rainStatus":      "HEAVY_RAIN",
				"rainStatusEndAt": "2023-07-26T15:00:00",
				"error":           "invalid time format: 2023-07-26T15:00:00",
			},
			map[string]any{
				"name":            "DISTRICT_TIME_PASSED",
				"rainStatus":      "LIGHT_RAIN",
				"rainStatusEndAt": "2023-07-26T13:00:00.000+07:00",
				"error":           "input time has passed: 2023-07-26T13:00:00.000+07:00",
			},
			map[string]any{
				"name":            "DISTRICT_NOT_FOUND",
				"rainStatus":      "LIGHT_RAIN",
				"rainStatusEndAt": "2023-07-26T15:00:00+07:00",
				"error":           "data not found",
			},
		}
		require.Equal(tt, exptFailureResp, failureResp)
	})
}
