package rain_situation_test

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/rain_situation"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/mock_mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon/mock_polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack/mock_slack"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/bulkutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type rainSituationAPIDeps struct {
	rainSituationService *mock_service.MockRainSituationService
	driverRepository     *mock_repository.MockDriverRepository
	polygonService       *mock_polygon.MockPolygon
	rainSituationRepo    *mock_repository.MockRainSituationRepository
	auditRepo            *mock_repository.MockAuditLogRepository
	slack                *mock_slack.MockSlack
	mapService           *mock_mapservice.MockMapService
}

func newTestRainSituationAPI(t gomock.TestReporter, cfg rain_situation.Config, rainSituationServiceCfg *service.AtomicRainSituationConfig, globalConfig config.GlobalConfig) (*rain_situation.RainSituationAPI, *rainSituationAPIDeps, func()) {
	ctrl := gomock.NewController(t)
	deps := &rainSituationAPIDeps{
		rainSituationService: mock_service.NewMockRainSituationService(ctrl),
		driverRepository:     mock_repository.NewMockDriverRepository(ctrl),
		polygonService:       mock_polygon.NewMockPolygon(ctrl),
		rainSituationRepo:    mock_repository.NewMockRainSituationRepository(ctrl),
		auditRepo:            mock_repository.NewMockAuditLogRepository(ctrl),
		slack:                mock_slack.NewMockSlack(ctrl),
		mapService:           mock_mapservice.NewMockMapService(ctrl),
	}

	return rain_situation.ProvideRainSituationAPI(
			cfg,
			deps.rainSituationService,
			deps.driverRepository,
			deps.polygonService,
			deps.rainSituationRepo,
			deps.auditRepo,
			deps.slack,
			rainSituationServiceCfg,
			globalConfig,
			deps.mapService,
		), deps, func() {
			ctrl.Finish()
		}
}

func TestRainSituationAPI_GetRainSituationMap(t *testing.T) {
	t.Parallel()

	req := func(q ...string) (*gin.Context, *httptest.ResponseRecorder) {
		query := ""
		if len(q) != 0 {
			query = q[0]
		}
		return testutil.TestRequestContext("GET", fmt.Sprintf("/v1/driver/rain-situation-map%s", query), nil)
	}
	polyGeo := polygon.Geometry{
		Coordinates: [][]geom.Coord{
			{
				{100.6270408630371, 14.735540398030539},
				{100.65845489501953, 14.719602330888959},
				{100.667724609375, 14.752141311434283},
				{100.63253402709961, 14.756457341526813},
				{100.6270408630371, 14.735540398030539},
			},
		},
	}
	coordinates := model.RainSituationCoordinates{{{
		{100.57537078857422, 14.421380422198462},
		{100.46585083007812, 14.30530611750917},
		{100.69381713867186, 14.304640761934547},
		{100.57537078857422, 14.421380422198462},
	}}}
	rainGeo := model.RainSituationGeometry{
		Type:        "MultiPolygon",
		Coordinates: coordinates,
	}
	t.Run("should return not found if driver is not found", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req()
		api, deps, finish := newTestRainSituationAPI(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.driverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("driver not found"))

		api.GetRainSituationMap(ctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("should return region not found if polygon service returns error not found", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req()
		api, deps, finish := newTestRainSituationAPI(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.driverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver"}, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), gomock.Any()).Return(polygon.GetRawRegionRes{}, polygon.ErrNotFound)

		api.GetRainSituationMap(ctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("should return region not found if polygon service returns error", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req()
		api, deps, finish := newTestRainSituationAPI(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.driverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver"}, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), gomock.Any()).Return(polygon.GetRawRegionRes{}, errors.New("eror"))

		api.GetRainSituationMap(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return internal error if rain situation service returns error", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req()
		api, deps, finish := newTestRainSituationAPI(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.driverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver"}, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), gomock.Any()).Return(polygon.GetRawRegionRes{}, nil)
		deps.rainSituationService.EXPECT().GetRainSituationByRegion(gomock.Any(), gomock.Any()).Return([]model.RainSituation{}, errors.New("Error"))

		api.GetRainSituationMap(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return rain situation map correctly without location query", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req()
		api, deps, finish := newTestRainSituationAPI(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.driverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver"}, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), gomock.Any()).Return(polygon.GetRawRegionRes{Name: "BKK", Region: "BKK", Geometry: polyGeo}, nil)
		deps.rainSituationService.EXPECT().GetRainSituationByRegion(gomock.Any(), gomock.Any()).Return([]model.RainSituation{{Name: "bearing", Geometry: rainGeo}, {Name: "siam", Geometry: rainGeo}}, nil)
		deps.rainSituationService.EXPECT().GetDisplayName(gomock.Any(), gomock.Any()).Return("เขตที่แสดง").AnyTimes()
		deps.rainSituationService.EXPECT().GetOntopAmountByRainRegions(gomock.Any(), []model.RainSituation{{Name: "bearing", Geometry: rainGeo}, {Name: "siam", Geometry: rainGeo}}).
			Return(map[string]float64{
				"bearing": 5,
			}, nil)

		api.GetRainSituationMap(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual rain_situation.RainSituationMapRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, polyGeo, actual.RegionGeometry)
		require.Equal(tt, rainGeo, actual.RainSituations[0].Geometry)
		require.Equal(tt, 0.0, actual.RainSituations[0].Distance)
		require.Equal(tt, 2, len(actual.RainSituations))
		require.Equal(tt, "เขตที่แสดง", actual.RainSituations[0].DisplayName)
		require.Equal(tt, 5.0, actual.RainSituations[0].OnTopAmount)
		require.Equal(tt, 0.0, actual.RainSituations[1].OnTopAmount)
	})

	t.Run("should return rain situation map correctly with location query", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req("?lat=13.692260336474845&lng=100.66380286266497")
		api, deps, finish := newTestRainSituationAPI(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		coordinates := model.RainSituationCoordinates{{{
			{100.39244402088565, 13.76162994554781},
			{100.39244402088565, 13.585660628301824},
			{100.53444461024702, 13.585660628301824},
			{100.53444461024702, 13.76162994554781},
			{100.39244402088565, 13.76162994554781},
		}}}
		rainGeo := model.RainSituationGeometry{
			Type:        "MultiPolygon",
			Coordinates: coordinates,
		}
		deps.driverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver"}, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), gomock.Any()).Return(polygon.GetRawRegionRes{Name: "BKK", Region: "BKK", Geometry: polyGeo}, nil)
		deps.rainSituationService.EXPECT().GetRainSituationByRegion(gomock.Any(), gomock.Any()).Return([]model.RainSituation{{Name: "bearing", Geometry: rainGeo}, {Name: "siam", Geometry: rainGeo}}, nil)
		deps.rainSituationService.EXPECT().GetDisplayName(gomock.Any(), gomock.Any()).Return("เขตที่แสดง").AnyTimes()
		deps.rainSituationService.EXPECT().GetOntopAmountByRainRegions(gomock.Any(), []model.RainSituation{{Name: "bearing", Geometry: rainGeo}, {Name: "siam", Geometry: rainGeo}}).
			Return(map[string]float64{
				"bearing": 5,
				"siam":    10,
			}, nil)
		// 2 rain situations: bearing and siam
		deps.mapService.EXPECT().FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.MapRoute{Distance: 1900, Duration: 4}, []model.MapWaypoint{}, nil)
		deps.mapService.EXPECT().FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.MapRoute{Distance: 1900, Duration: 4}, []model.MapWaypoint{}, nil)

		api.GetRainSituationMap(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual rain_situation.RainSituationMapRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, polyGeo, actual.RegionGeometry)
		require.Equal(tt, rainGeo, actual.RainSituations[0].Geometry)
		require.Equal(tt, 1.9, actual.RainSituations[0].Distance)
		require.Equal(tt, 2, len(actual.RainSituations))
		require.Equal(tt, "เขตที่แสดง", actual.RainSituations[0].DisplayName)
		require.Equal(tt, 5.0, actual.RainSituations[0].OnTopAmount)
		require.Equal(tt, 10.0, actual.RainSituations[1].OnTopAmount)
	})

	t.Run("unable to get PIP ontop but should return rain situation map correctly without location query", func(tt *testing.T) {
		tt.Parallel()
		ctx, recorder := req()
		api, deps, finish := newTestRainSituationAPI(tt, rain_situation.Config{}, &service.AtomicRainSituationConfig{}, config.GlobalConfig{})
		defer finish()
		deps.driverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver"}, nil)
		deps.polygonService.EXPECT().GetRegion(gomock.Any(), gomock.Any()).Return(polygon.GetRawRegionRes{Name: "BKK", Region: "BKK", Geometry: polyGeo}, nil)
		deps.rainSituationService.EXPECT().GetRainSituationByRegion(gomock.Any(), gomock.Any()).Return([]model.RainSituation{{Name: "bearing", Geometry: rainGeo}, {Name: "siam", Geometry: rainGeo}}, nil)
		deps.rainSituationService.EXPECT().GetDisplayName(gomock.Any(), gomock.Any()).Return("เขตที่แสดง").AnyTimes()
		deps.rainSituationService.EXPECT().GetOntopAmountByRainRegions(gomock.Any(), []model.RainSituation{{Name: "bearing", Geometry: rainGeo}, {Name: "siam", Geometry: rainGeo}}).
			Return(nil, errors.New("unable to get PIP ontop"))

		api.GetRainSituationMap(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual rain_situation.RainSituationMapRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, polyGeo, actual.RegionGeometry)
		require.Equal(tt, rainGeo, actual.RainSituations[0].Geometry)
		require.Equal(tt, 0.0, actual.RainSituations[0].Distance)
		require.Equal(tt, 2, len(actual.RainSituations))
		require.Equal(tt, 0.0, actual.RainSituations[0].OnTopAmount)
		require.Equal(tt, 0.0, actual.RainSituations[1].OnTopAmount)
	})
}

func TestRainSituationAPI_BulkUpsertRainSituation(t *testing.T) {
	t.Parallel()

	makeReq := func(req io.Reader) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", "/v1/internal/rain-situations", req)
		return gctx, recorder
	}

	adminPageUrl := "https://lm-admin.line-apps-beta.com/rain-situations"
	wikiPageUrl := "https://linemanwongnai.atlassian.net/wiki/spaces/LMFleet/pages/853608726/How+to+-+Rain+notification+handbook#Message"
	cfg := rain_situation.Config{
		UpdateRainSituationEnabled:                 true,
		RainSituationSlackNotificationEnabled:      true,
		RainSituationSlackNotificationLimit:        30,
		RainSituationSlackNotificationAdminPageUrl: adminPageUrl,
		RainSituationSlackNotificationWikiPageUrl:  wikiPageUrl,
	}

	coordinates := model.RainSituationCoordinates{{{
		{100.57537078857422, 14.421380422198462},
		{100.46585083007812, 14.30530611750917},
		{100.69381713867186, 14.304640761934547},
		{100.57537078857422, 14.421380422198462},
	}}}

	requestedBy := "<EMAIL>"

	changedCoordinates := model.RainSituationCoordinates{{{
		{100.1, 14.421380422198462},
		{100.46585083007812, 14.30530611750917},
		{100.69381713867186, 14.304640761934547},
		{100.1, 14.421380422198462},
	}}}

	defaultRainSituationConfig := &service.AtomicRainSituationConfig{
		Config: service.RainSituationCfg{
			RainingStatuses: types.NewStringSet("LIGHT_RAIN", "HEAVY_RAIN"),
		},
	}

	expectFind := func(ttt *testing.T, deps *rainSituationAPIDeps, expected []model.RainSituation) {
		deps.rainSituationRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(expected, nil)
	}

	type ExpectUpdateData struct {
		coordinates model.RainSituationCoordinates
		rainStatus  model.RainStatus
		requestedBy string
	}

	expectUpdate := func(ttt *testing.T, deps *rainSituationAPIDeps, expected ExpectUpdateData) {
		deps.rainSituationRepo.EXPECT().
			Update(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(
				func(ctx context.Context, id string, rainSituation *model.RainSituation, rainingStatus types.StringSet, opts ...repository.Option) error {
					require.Equal(ttt, expected.coordinates, rainSituation.Geometry.Coordinates)
					require.Equal(ttt, expected.rainStatus, rainSituation.RainStatus)
					require.NotEmpty(ttt, rainSituation.UpdatedAt)
					require.Equal(ttt, expected.requestedBy, rainSituation.UpdatedBy)
					return nil
				},
			)

		deps.auditRepo.EXPECT().
			Insert(gomock.Any(), gomock.Any()).
			Return(nil)
	}

	expectInsert := func(ttt *testing.T, deps *rainSituationAPIDeps) {
		deps.rainSituationRepo.EXPECT().
			Create(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		deps.auditRepo.EXPECT().
			Insert(gomock.Any(), gomock.Any()).
			Return(nil)
	}

	type ExpectSlackNotifyData struct {
		rainingOverrides  []string
		rainingWeatherAPI []string
		undetected        []string
	}

	expectSlackNotify := func(ttt *testing.T, deps *rainSituationAPIDeps, expected ExpectSlackNotifyData) {
		deps.slack.EXPECT().
			Notify(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, req *slack.NotifyRequest) error {
				for _, x := range expected.rainingOverrides {
					require.Contains(ttt, req.Payload.Attachments[0].Blocks[1].Text.Text, x)
				}
				for _, x := range expected.rainingWeatherAPI {
					require.Contains(ttt, req.Payload.Attachments[1].Blocks[1].Text.Text, x)
				}
				for _, x := range expected.undetected {
					require.Contains(ttt, req.Payload.Attachments[2].Blocks[1].Text.Text, x)
				}
				require.Contains(ttt, *req.Payload.Attachments[0].Blocks[0].Accessory.Url, adminPageUrl)
				require.Contains(ttt, *req.Payload.Attachments[1].Blocks[0].Accessory.Url, adminPageUrl)
				require.Contains(ttt, *req.Payload.Attachments[2].Blocks[0].Accessory.Url, adminPageUrl)
				require.Contains(ttt, *(*req.Payload.Attachments[3].Blocks[0].Elements)[0].Url, wikiPageUrl)
				return nil
			})
	}

	t.Run("should return 200 when update rain situation success", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		json := testutil.JSON(rain_situation.BulkUpsertRainSituationReq{
			RequestedBy: requestedBy,
			TimeStamp:   time.Now(),
			WeatherRecords: []rain_situation.WeatherRecord{
				{
					PolygonName: "Phra Nakhon",
					Region:      "BKK",
					GeoJSON:     coordinates,
					RainStatus:  model.RainStatusLightRain,
				},
			},
		})

		gctx, recorder := makeReq(json)

		api, deps, finish := newTestRainSituationAPI(tt, cfg, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		expectFind(tt, deps, []model.RainSituation{
			{
				Name: "Phra Nakhon", Region: "BKK", Geometry: model.RainSituationGeometry{
					Type:        "MultiPolygon",
					Coordinates: coordinates,
				},
			},
		})

		expectUpdate(tt, deps, ExpectUpdateData{coordinates: coordinates, rainStatus: model.RainStatusLightRain, requestedBy: requestedBy})
		expectSlackNotify(tt, deps, ExpectSlackNotifyData{})

		api.UpsertRainSituations(gctx)

		var actualRes bulkutil.BulkResp
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Len(tt, actualRes.Success, 1)
		require.Len(tt, actualRes.Fail, 0)
	})

	t.Run("should return 200 when update rain situation success (geometry changed)", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		json := testutil.JSON(rain_situation.BulkUpsertRainSituationReq{
			RequestedBy: requestedBy,
			TimeStamp:   time.Now(),
			WeatherRecords: []rain_situation.WeatherRecord{
				{
					PolygonName: "Phra Nakhon",
					Region:      "BKK",
					GeoJSON:     changedCoordinates,
					RainStatus:  model.RainStatusHeavyRain,
				},
			},
		})

		gctx, recorder := makeReq(json)

		api, deps, finish := newTestRainSituationAPI(tt, cfg, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		expectFind(tt, deps, []model.RainSituation{
			{
				Name:       "Phra Nakhon",
				Region:     "BKK",
				RainStatus: model.RainStatusHeavyRain,
			},
		})

		expectUpdate(tt, deps, ExpectUpdateData{coordinates: changedCoordinates, rainStatus: model.RainStatusHeavyRain, requestedBy: requestedBy})
		expectSlackNotify(tt, deps, ExpectSlackNotifyData{})

		api.UpsertRainSituations(gctx)

		var actualRes bulkutil.BulkResp
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Len(tt, actualRes.Success, 1)
		require.Len(tt, actualRes.Fail, 0)
	})

	t.Run("should return 200 when update rain situation success when feature flag is disabled", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		json := testutil.JSON(rain_situation.BulkUpsertRainSituationReq{
			RequestedBy: requestedBy,
			TimeStamp:   time.Now(),
			WeatherRecords: []rain_situation.WeatherRecord{
				{
					PolygonName: "Bangkok",
					Region:      "BKK",
					GeoJSON:     coordinates,
					RainStatus:  model.RainStatusNone,
				},
			},
		})

		gctx, recorder := makeReq(json)

		api, _, finish := newTestRainSituationAPI(tt, rain_situation.Config{UpdateRainSituationEnabled: false}, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		api.UpsertRainSituations(gctx)

		var actualRes bulkutil.BulkResp
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Len(tt, actualRes.Success, 0)
		require.Len(tt, actualRes.Fail, 0)

	})

	t.Run("should return 200 when update rain situation partial success and partial fail", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		json := testutil.JSON(rain_situation.BulkUpsertRainSituationReq{
			RequestedBy: requestedBy,
			TimeStamp:   time.Now(),
			WeatherRecords: []rain_situation.WeatherRecord{
				{
					PolygonName: "Phra Nakhon",
					Region:      "BKK",
					GeoJSON:     coordinates,
					RainStatus:  model.RainStatusNone,
				},
				{
					PolygonName: "Chatuchak",
					Region:      "BKK",
					GeoJSON:     coordinates,
					RainStatus:  "invalid_rain_status",
				},
			},
		})

		gctx, recorder := makeReq(json)

		api, deps, finish := newTestRainSituationAPI(tt, cfg, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		expectFind(tt, deps, []model.RainSituation{{Name: "Phra Nakhon"}, {Name: "Chatuchak"}})

		expectUpdate(tt, deps, ExpectUpdateData{coordinates: coordinates, rainStatus: model.RainStatusNone, requestedBy: requestedBy})
		expectSlackNotify(tt, deps, ExpectSlackNotifyData{})

		api.UpsertRainSituations(gctx)

		var actualRes bulkutil.BulkResp
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Len(tt, actualRes.Success, 1)
		require.Len(tt, actualRes.Fail, 1)
	})

	t.Run("should update rain situation and notify slack correctly when rain status is changed", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		json := testutil.JSON(rain_situation.BulkUpsertRainSituationReq{
			RequestedBy: requestedBy,
			TimeStamp:   time.Now(),
			WeatherRecords: []rain_situation.WeatherRecord{
				{
					PolygonName: "Phra Nakhon",
					Region:      "BKK",
					GeoJSON:     coordinates,
					RainStatus:  model.RainStatusHeavyRain,
				},
			},
		})

		gctx, recorder := makeReq(json)

		api, deps, finish := newTestRainSituationAPI(tt, cfg, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		expectFind(tt, deps, []model.RainSituation{{
			Name:       "Phra Nakhon",
			RainStatus: model.RainStatusNone,
		}})

		expectUpdate(tt, deps, ExpectUpdateData{coordinates: coordinates, rainStatus: model.RainStatusHeavyRain, requestedBy: requestedBy})
		expectSlackNotify(tt, deps, ExpectSlackNotifyData{
			rainingWeatherAPI: []string{"Phra Nakhon"},
			rainingOverrides:  []string{},
			undetected:        []string{},
		})

		api.UpsertRainSituations(gctx)

		var actualRes bulkutil.BulkResp
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Len(tt, actualRes.Success, 1)
		require.Len(tt, actualRes.Fail, 0)
	})

	t.Run("should insert new rain situation and notify slack correctly", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		json := testutil.JSON(rain_situation.BulkUpsertRainSituationReq{
			RequestedBy: requestedBy,
			TimeStamp:   time.Now(),
			WeatherRecords: []rain_situation.WeatherRecord{
				{
					PolygonName: "Phra Nakhon",
					Region:      "BKK",
					GeoJSON:     coordinates,
					RainStatus:  model.RainStatusLightRain,
				},
			},
		})

		gctx, recorder := makeReq(json)

		api, deps, finish := newTestRainSituationAPI(tt, cfg, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		expectFind(tt, deps, []model.RainSituation{})

		expectInsert(tt, deps)
		expectSlackNotify(tt, deps, ExpectSlackNotifyData{
			rainingWeatherAPI: []string{"Phra Nakhon"},
			rainingOverrides:  []string{},
			undetected:        []string{},
		})

		api.UpsertRainSituations(gctx)

		var actualRes bulkutil.BulkResp
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Len(tt, actualRes.Success, 1)
		require.Len(tt, actualRes.Fail, 0)
	})

	t.Run("should update rain situation and notify slack for overrides correctly", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		json := testutil.JSON(rain_situation.BulkUpsertRainSituationReq{
			RequestedBy: requestedBy,
			TimeStamp:   time.Now(),
			WeatherRecords: []rain_situation.WeatherRecord{
				{
					PolygonName: "Sathorn",
					Region:      "BKK",
					GeoJSON:     coordinates,
					RainStatus:  model.RainStatusNone,
				},
			},
		})

		gctx, recorder := makeReq(json)

		api, deps, finish := newTestRainSituationAPI(tt, cfg, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		deps.rainSituationRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RainSituation{
				{
					Name: "Sathorn", OverrideRainStatus: model.RainStatusHeavyRain,
					OverridePeriod: model.OverridePeriod{
						Start: time.Time{},
						End:   timeutils.Now().Add(time.Hour * 24),
					},
				},
			}, nil)

		expectUpdate(tt, deps, ExpectUpdateData{coordinates: coordinates, rainStatus: model.RainStatusNone, requestedBy: requestedBy})
		expectSlackNotify(tt, deps, ExpectSlackNotifyData{
			rainingWeatherAPI: []string{},
			rainingOverrides:  []string{"Sathorn"},
			undetected:        []string{},
		})

		api.UpsertRainSituations(gctx)

		var actualRes bulkutil.BulkResp
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Len(tt, actualRes.Success, 1)
		require.Len(tt, actualRes.Fail, 0)
	})

	t.Run("should notify slack for undetected correctly", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		json := testutil.JSON(rain_situation.BulkUpsertRainSituationReq{
			RequestedBy:    requestedBy,
			TimeStamp:      time.Now(),
			WeatherRecords: []rain_situation.WeatherRecord{},
		})

		gctx, recorder := makeReq(json)

		api, deps, finish := newTestRainSituationAPI(tt, cfg, defaultRainSituationConfig, config.GlobalConfig{})
		defer finish()

		expectFind(tt, deps, []model.RainSituation{{Name: "Chatuchak", RainStatus: model.RainStatusHeavyRain}})
		expectSlackNotify(tt, deps, ExpectSlackNotifyData{
			rainingWeatherAPI: []string{"Chatuchak"},
			rainingOverrides:  []string{},
			undetected:        []string{"Chatuchak"},
		})

		api.UpsertRainSituations(gctx)

		var actualRes bulkutil.BulkResp
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Len(tt, actualRes.Success, 0)
		require.Len(tt, actualRes.Fail, 0)
	})

}
