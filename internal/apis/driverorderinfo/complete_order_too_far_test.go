package driverorderinfo_test

import (
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func TestDriverOrderInfoAPI_BanDriverCompleteOrderTooFar(t *testing.T) {
	t.Run("should ban rider", func(tt *testing.T) {
		api, deps, finish := newDriverOrderInfoAP(tt, newEnableAutoBanCompleteOrderTooOftenCfg(), newEnableAutoBanCompleteOrderTooFarCfg())
		defer finish()

		doi := &model.DriverOrderInfo{
			DriverID:                "U-1",
			LastTooFarCompleteOrder: 3,
		}

		driverID := "U-1"

		d := model.Driver{
			DriverID: driverID,
		}

		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), driverID).Return(&d, nil)
		deps.banService.EXPECT().BanAndSaveHistory(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.banService.EXPECT().BanWithdrawAndSaveHistory(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverOrderInfoRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.shiftService.EXPECT().RemoveShiftByTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil)
		err := api.BanDriverCompleteOrderTooFar(doi)
		require.NoError(tt, err)
	})

	t.Run("should not ban tester rider", func(tt *testing.T) {
		api, deps, finish := newDriverOrderInfoAP(tt, newEnableAutoBanCompleteOrderTooOftenCfg(), newEnableAutoBanCompleteOrderTooFarCfg())
		defer finish()

		driverID := "U-1"

		doi := &model.DriverOrderInfo{
			DriverID:                driverID,
			LastTooFarCompleteOrder: 3,
		}

		d := model.Driver{
			DriverID: driverID,
			BaseDriver: model.BaseDriver{
				DriverType: crypt.NewLazyEncryptedString(string(model.DriverTypeTester)),
			},
		}

		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), driverID).Return(&d, nil)

		err := api.BanDriverCompleteOrderTooFar(doi)
		require.NoError(tt, err)
	})

	t.Run("should not ban when disable feature", func(tt *testing.T) {
		api, _, finish := newDriverOrderInfoAP(tt, newDisableAutoBanCompleteOrderTooOftenCfg(), newDisableAutoBanCompleteOrderTooFarCfg())
		defer finish()

		driverID := "U-1"

		doi := &model.DriverOrderInfo{
			DriverID:                driverID,
			LastTooFarCompleteOrder: 3,
		}

		err := api.BanDriverCompleteOrderTooFar(doi)
		require.NoError(tt, err)
	})
}
