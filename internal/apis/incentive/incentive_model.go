package incentive

import (
	"fmt"
	"sort"
	"time"

	"github.com/pkg/errors"
	"github.com/twpayne/go-geom"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"gopkg.in/guregu/null.v4"

	absintheUtils "git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type PaymentType string

const (
	// Daily will evaluate at the end of the day
	Daily PaymentType = "DAILY"

	// Period will evaluate at the end of period
	Period PaymentType = "PERIOD"

	// PeriodStreak
	PeriodStreak PaymentType = "STREAK"
)

type OrderShiftType string

const (
	AllOrderType     OrderShiftType = "ALL"
	OnlyShiftType    OrderShiftType = "SHIFT"
	OnlyNonShiftType OrderShiftType = "NON_SHIFT"
)

type ValidationData struct {
	DriverID string
	AR       *float64
	CR       *float64
	Rating   *float64
	Box      *bool
	Jacket   *bool
	Tier     *model.DriverTier
}

type Incentive struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	IncentiveID    string             `bson:"incentive_id"`
	Name           string             `bson:"name"`
	DisplayName    string             `bson:"display_name"`
	LocationName   string             `bson:"location_name"`
	Description    string             `bson:"description"`
	Region         model.RegionCode   `bson:"region_code"`
	PaymentType    PaymentType        `bson:"payment_type"`
	Geometry       Geometry           `bson:"geometry,omitempty"`
	ServiceTypes   []model.Service    `bson:"service_types"`
	DateRange      DateRange          `bson:"date_range"`
	Times          []Times            `bson:"times"`
	OrderTier      []OrderTier        `bson:"order_tier"`
	AR             *float64           `bson:"ar"`
	CR             *float64           `bson:"cr"`
	Rating         *float64           `bson:"rating"`
	Tiers          []string           `bson:"tiers"`
	Box            *bool              `bson:"box"`
	Jacket         *bool              `bson:"jacket"`
	Active         bool               `bson:"active"`
	CreatedAt      time.Time          `bson:"created_at"`
	UpdatedAt      time.Time          `bson:"updated_at"`
	CreatedBy      string             `bson:"created_by"`
	Sources        []string           `bson:"sources"`
	UpdatedBy      string             `bson:"updated_by"`
	WhitelistIDs   []string           `bson:"whitelist_ids"`
	BlacklistIDs   []string           `bson:"blacklist_ids"`
	OrderShiftType OrderShiftType     `bson:"order_type,omitempty"`
	ZoneCode       string             `bson:"zone_code,omitempty"`
	Streak         Streak             `bson:"streak,omitempty"`
	Zone           model.Zone         `bson:"-"`
}

func (i Incentive) GetEffectiveGeometry() Geometry {
	if !i.Geometry.IsZero() {
		return i.Geometry
	}

	if i.ZoneCode != "" && !i.Zone.Geometry.IsZero() {
		g := i.Zone.Geometry
		return Geometry{
			Type:        g.Type,
			Coordinates: Coordinates(g.Coordinates),
		}
	}

	return i.Geometry
}

type OrderTier struct {
	MinOrderAmount int64 `bson:"min_order_amount"`
	MaxOrderAmount int64 `bson:"max_order_amount"`

	IncentiveAmount types.Money `bson:"incentive_amount"`
}

func (t OrderTier) IsEmpty() bool {
	return t.MaxOrderAmount == 0 && t.MinOrderAmount == 0 && t.IncentiveAmount == 0
}

type Streak struct {
	NoOfDays     uint `json:"noOfDays" bson:"no_of_days"`
	OrdersPerDay uint `json:"ordersPerDay" bson:"orders_per_day"`
	Amount       uint `json:"amount" bson:"amount"`
}

func (s Streak) Validate() error {
	blank := Streak{}

	if s == blank {
		return errors.New("invalid streak: blank streak")
	}

	if s.NoOfDays == 0 {
		return errors.New("invalid streak: 0 number of days")
	}

	if s.OrdersPerDay == 0 {
		return errors.New("invalid streak: 0 orders per day")
	}

	if s.Amount == 0 {
		return errors.New("invalid streak: amount is 0")
	}

	return nil
}

type DateRange struct {
	// Start date of the incentive
	Start time.Time `bson:"start"`

	// End date of the incentive
	End time.Time `bson:"end"`
}

type Times struct {
	Start string `bson:"start"`
	End   string `bson:"end"`
}

type Coordinates [][][]geom.Coord

type Geometry struct {
	Type        string      `bson:"type"`
	Coordinates Coordinates `bson:"coordinates"`
}

func (geo Geometry) IsZero() bool {
	return geo.Type == "" && len(geo.Coordinates) == 0
}

// CalculatePrice return the price that driver obtains from this incentive based on order count
func (t OrderTier) CalculatePrice(count int64) types.Money {
	if t.MinOrderAmount <= count {
		return t.IncentiveAmount
	}
	return 0
}

// CalculatePrice return the price that driver obtains from this incentive based on order count
// Rider will get the price from every tiers that the rider has been passed the minimum.
// So we only pick the greatest price from the tiers.
func (inc Incentive) CalculatePrice(count int64) types.Money {
	var price types.Money
	for _, tier := range inc.OrderTier {
		if p := tier.CalculatePrice(count); p > price {
			price = p
		}
	}
	return price
}

func (inc Incentive) ValidateStreakARCR(doi model.GetARCRDriverOrderInfo, start, end time.Time) bool {
	dates := inc.validStreakARCRByDate(doi, start, end)
	return len(dates) >= int(inc.Streak.NoOfDays)
}

func (inc Incentive) validStreakARCRByDate(doi model.GetARCRDriverOrderInfo, start, end time.Time) []time.Time {
	var dates []time.Time
	for date := range timeutil.LoopDateRangeByDateBKKChannel(start, end) {
		start := timeutil.DateTruncateBKK(date)
		if inc.ValidateDailyARCR(date, doi) {
			dates = append(dates, start)
		}
	}
	return dates
}

func (inc Incentive) CalculateStreakPrice(ocp OrderCountProvider, doi model.GetARCRDriverOrderInfo) types.Money {
	achieveCount := 0
	for _, date := range inc.validStreakARCRByDate(doi, inc.DateRange.Start, inc.DateRange.End) {
		if ocp.GetOrderCountByDate(date) >= int(inc.Streak.OrdersPerDay) {
			achieveCount++
		}
	}
	if achieveCount >= int(inc.Streak.NoOfDays) {
		return types.Money(inc.Streak.Amount)
	}
	return 0
}

type OrderCountProvider interface {
	GetOrderCountByDate(date time.Time) int
}

type incpOrderCountProvider struct {
	countMap map[int64]int
}

func (p *incpOrderCountProvider) GetOrderCountByDate(date time.Time) int {
	return p.countMap[date.Unix()]
}

func NewIncentiveProgressOrderCountProvider(incps []IncentiveProgress) OrderCountProvider {
	countMap := make(map[int64]int, len(incps))
	for _, incp := range incps {
		date := timeutil.DateTruncateBKK(incp.Date)
		countMap[date.Unix()] = int(incp.ProgressCount)
	}
	return &incpOrderCountProvider{countMap: countMap}
}

type realOrderCountProvider struct {
	countMap map[int64]int
}

func (p *realOrderCountProvider) GetOrderCountByDate(date time.Time) int {
	return p.countMap[date.Unix()]
}

func NewRealOrderCountProvider(io []model.Order) OrderCountProvider {
	countMap := make(map[int64]int)
	for _, i := range io {
		date := timeutil.DateTruncateBKK(i.CreatedAt)
		countMap[date.Unix()]++
	}
	return &realOrderCountProvider{countMap: countMap}
}

// GetNextTier returns incentive tier that the driver is going to get if he/she completes more order.
// returns nil if the driver already pass all tiers.
func (inc Incentive) GetNextTier(orderCount int64) OrderTier {
	sort.SliceStable(inc.OrderTier, func(i, j int) bool {
		return inc.OrderTier[i].MinOrderAmount < inc.OrderTier[j].MinOrderAmount
	})
	for _, tier := range inc.OrderTier {
		if orderCount < tier.MinOrderAmount {
			return tier
		}
	}
	return OrderTier{}
}

func (inc Incentive) ValidateAR(ar float64) bool {
	if inc.AR == nil {
		return true
	}
	return ar >= *inc.AR
}

func (inc Incentive) ValidateCR(cr float64) bool {
	if inc.CR == nil {
		return true
	}
	return cr <= *inc.CR
}

func (inc Incentive) ValidateRating(rating float64) bool {
	if inc.Rating == nil {
		return true
	}
	return rating >= *inc.Rating
}

func (inc Incentive) ValidateHasBox(box bool) bool {
	if !null.BoolFromPtr(inc.Box).Bool {
		return true
	}
	return box
}

func (inc Incentive) ValidateHasJacket(jacket bool) bool {
	if !null.BoolFromPtr(inc.Jacket).Bool {
		return true
	}
	return jacket
}

func (inc Incentive) ValidateDriverTier(driverTier model.DriverTier) bool {
	if len(inc.Tiers) == 0 {
		return true
	}
	return absintheUtils.StrContains(string(driverTier), inc.Tiers)
}

// Deprecated: Should revise incentive condition validation for support daily AR and CR validation.
func (inc Incentive) Validate(data ValidationData) bool {
	if len(inc.WhitelistIDs) > 0 {
		if !stringutil.IsStringInList(inc.WhitelistIDs, data.DriverID) {
			return false
		}
	}
	if inc.AR != nil {
		if data.AR == nil || !inc.ValidateAR(*data.AR) {
			return false
		}
	}
	if inc.CR != nil {
		if data.CR == nil || !inc.ValidateCR(*data.CR) {
			return false
		}
	}
	if inc.Rating != nil {
		if data.Rating == nil || !inc.ValidateRating(*data.Rating) {
			return false
		}
	}
	if null.BoolFromPtr(inc.Box).Bool {
		if data.Box == nil || !inc.ValidateHasBox(*data.Box) {
			return false
		}
	}
	if null.BoolFromPtr(inc.Jacket).Bool {
		if data.Jacket == nil || !inc.ValidateHasJacket(*data.Jacket) {
			return false
		}
	}
	if len(inc.Tiers) > 0 {
		if data.Tier == nil || !inc.ValidateDriverTier(*data.Tier) {
			return false
		}
	}
	return true
}

func (inc Incentive) ValidateJacketBoxTier(data ValidationData) bool {
	if null.BoolFromPtr(inc.Box).Bool {
		if data.Box == nil || !inc.ValidateHasBox(*data.Box) {
			return false
		}
	}
	if null.BoolFromPtr(inc.Jacket).Bool {
		if data.Jacket == nil || !inc.ValidateHasJacket(*data.Jacket) {
			return false
		}
	}
	if len(inc.Tiers) > 0 {
		if data.Tier == nil || !inc.ValidateDriverTier(*data.Tier) {
			return false
		}
	}
	return true
}

func (inc Incentive) ValidateDailyARCR(date time.Time, doi model.GetARCRDriverOrderInfo) bool {
	start := timeutil.DateTruncateBKK(date)
	end := timeutil.DateCeilingBKK(date)

	if inc.AR != nil {
		_, ar, _ := doi.GetAR(start, end)
		if !inc.ValidateAR(ar) {
			return false
		}
	}

	if inc.CR != nil {
		_, cr, _ := doi.GetCR(start, end)
		if !inc.ValidateCR(cr) {
			return false
		}
	}

	return true
}

func (inc Incentive) GetDateRange(queryDate time.Time) (DateRange, error) {
	switch inc.PaymentType {
	case Daily:
		return DateRange{
			Start: timeutil.DateTruncateTZ(queryDate, timeutil.BangkokLocation()),
			End:   timeutil.DateCeilingTZ(queryDate, timeutil.BangkokLocation()),
		}, nil
	case Period, PeriodStreak:
		return DateRange{
			Start: timeutil.DateTruncateTZ(inc.DateRange.Start, timeutil.BangkokLocation()),
			End:   timeutil.DateCeilingTZ(inc.DateRange.End, timeutil.BangkokLocation()),
		}, nil
	}

	return DateRange{}, fmt.Errorf("invalid payment type, type=%v", inc.PaymentType)
}

// IncentiveProgress is Daily Incentive progress
type IncentiveProgress struct {
	// IncentiveID, Date, and  DriverID are consider as the key for the progress.
	// If the incentive ID is the same but the payout date is different, it won't share the same progress.
	IncentiveID string    `bson:"incentive_id"`
	Date        time.Time `bson:"date"`
	DriverID    string    `bson:"driver_id"`

	// ProgressCount usually be the completed order.
	ProgressCount int64 `bson:"progress_count"`

	CreatedAt time.Time `bson:"created_at"`
	UpdatedAt time.Time `bson:"updated_at"`
}
