package incentive_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/icrowley/fake"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive/mock_incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon/mock_polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func incentiveCreateRequest() incentive.Request {
	return incentive.Request{
		Name:        "just_name",
		Description: "just_description",
		ServiceTypes: []model.Service{
			"food",
		},
		PaymentType: "DAILY",
		Active:      true,
		AR:          createPointerFloat64(80.51),
		CR:          createPointerFloat64(88.32),
		Rating:      createPointerFloat64(4.30),
		Box:         createPointerBoolean(true),
		Tiers: []string{
			"t",
		},
		OrderTier: []incentive.OrderTierReq{
			{
				MinOrderAmount:  5,
				MaxOrderAmount:  10,
				IncentiveAmount: 15,
			},
		},
		DateRange: incentive.DateRangeReq{
			Start: time.Date(2022, 1, 22, 12, 0, 0, 0, timeutil.BangkokLocation()),
			End:   time.Date(2022, 1, 22, 13, 0, 0, 0, timeutil.BangkokLocation()),
		},
		Times: []incentive.TimesReq{
			{
				Start: "01:05:05",
				End:   "16:22:22",
			},
		},
		Coordinates: incentive.Coordinates{{
			{
				{100.6270408630371, 14.735540398030539},
				{100.65845489501953, 14.719602330888959},
				{100.667724609375, 14.752141311434283},
				{100.63253402709961, 14.756457341526813},
				{100.6270408630371, 14.735540398030539},
			},
		}},
		Sources: []string{
			"foo",
			"bar",
		},
		Region: "KORAT_CITY",
		Email:  "<EMAIL>",
	}
}

func incentiveCreateWithStreakRequest(streak incentive.Streak, dateRange incentive.DateRangeReq) incentive.Request {
	r := incentiveCreateRequest()
	r.PaymentType = incentive.PeriodStreak
	r.Streak = streak
	r.DateRange = dateRange

	return r
}

func incentiveCreateWithoutCoordinatesRequest() incentive.Request {
	return incentive.Request{
		Name:        "just_name",
		Description: "just_description",
		ServiceTypes: []model.Service{
			"food",
		},
		PaymentType: "DAILY",
		Active:      true,
		AR:          createPointerFloat64(80.51),
		CR:          createPointerFloat64(88.32),
		Rating:      createPointerFloat64(4.30),
		Box:         createPointerBoolean(true),
		Tiers: []string{
			"t",
		},
		OrderTier: []incentive.OrderTierReq{
			{
				MinOrderAmount:  5,
				MaxOrderAmount:  10,
				IncentiveAmount: 15,
			},
		},
		DateRange: incentive.DateRangeReq{
			Start: time.Date(2022, 1, 22, 12, 0, 0, 0, timeutil.BangkokLocation()),
			End:   time.Date(2022, 1, 22, 13, 0, 0, 0, timeutil.BangkokLocation()),
		},
		Times: []incentive.TimesReq{
			{
				Start: "01:05:05",
				End:   "16:22:22",
			},
		},
		Sources: []string{
			"foo",
			"bar",
		},
		Region: "KORAT_CITY",
		Email:  "<EMAIL>",
	}
}

func incentiveCreateWithoutCoordinatesWithStreakRequest(streak incentive.Streak, dateRange incentive.DateRangeReq) incentive.Request {
	r := incentiveCreateWithoutCoordinatesRequest()
	r.PaymentType = incentive.PeriodStreak
	r.Streak = streak
	r.DateRange = dateRange

	return r
}

func TestIncentiveAPI_Create(t *testing.T) {
	req := func(req incentive.Request) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/admin/incentive-settings", testutil.JSON(req))
	}

	t.Run("create incentive success with coordinates", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveCreateRequest()
		gctx, recorder := req(bodyReq)

		expectedIncentive, _ := bodyReq.ToCreateModel(bodyReq.Email, func(ctx context.Context, region string) bool { return true })

		deps.incentiveRepo.EXPECT().
			Create(gctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, incentive *incentive.Incentive) (string, error) {
				expectedIncentive.IncentiveID = incentive.IncentiveID
				expectedIncentive.CreatedAt = incentive.CreatedAt
				expectedIncentive.UpdatedAt = incentive.UpdatedAt
				expectedIncentive.DateRange = incentive.DateRange
				require.Equal(tt, expectedIncentive, incentive)
				return "", nil
			})

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.regionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Return(true)

		incentiveAPI.Create(gctx)

		require.Equal(tt, http.StatusCreated, recorder.Code)
	})

	t.Run("create ok with coordinates and with streak", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()

		streak := incentive.Streak{
			NoOfDays:     1,
			OrdersPerDay: 2,
			Amount:       3,
		}
		start := time.Now()
		dateRange := incentive.DateRangeReq{
			Start: start,
			End:   start.Add(time.Hour * 24 * 2),
		}

		bodyReq := incentiveCreateWithStreakRequest(streak, dateRange)

		gctx, recorder := req(bodyReq)
		expectedIncentive, err := bodyReq.ToCreateModel(bodyReq.Email, func(ctx context.Context, region string) bool { return true })
		require.NoError(tt, err)

		deps.incentiveRepo.EXPECT().
			Create(gctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, inc *incentive.Incentive) (string, error) {
				expectedIncentive.IncentiveID = inc.IncentiveID
				expectedIncentive.CreatedAt = inc.CreatedAt
				expectedIncentive.UpdatedAt = inc.UpdatedAt
				expectedIncentive.DateRange = inc.DateRange
				require.Equal(tt, expectedIncentive, inc)

				require.Equal(tt, incentive.PeriodStreak, inc.PaymentType)
				require.Equal(tt, streak, inc.Streak)
				return "", nil
			})

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.regionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Return(true)

		incentiveAPI.Create(gctx)

		require.Equal(tt, http.StatusCreated, recorder.Code)
	})

	t.Run("create error with too long streak no of days", func(tt *testing.T) {
		incentiveAPI, _, finish := newTestIncentiveAPI(tt)
		defer finish()

		streak := incentive.Streak{
			NoOfDays:     10,
			OrdersPerDay: 2,
			Amount:       3,
		}
		start := time.Now()
		dateRange := incentive.DateRangeReq{
			Start: start,
			End:   start.Add(time.Hour * 24 * 2),
		}

		bodyReq := incentiveCreateWithStreakRequest(streak, dateRange)

		gctx, recorder := req(bodyReq)
		_, err := bodyReq.ToCreateModel(bodyReq.Email, func(ctx context.Context, region string) bool { return true })
		require.Error(t, err)

		incentiveAPI.Create(gctx)
		require.NotEqual(tt, http.StatusCreated, recorder.Code)
	})

	t.Run("create incentive success without coordinates", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveCreateWithoutCoordinatesRequest()
		gctx, recorder := req(bodyReq)

		newIncentive, _ := bodyReq.ToCreateModel(bodyReq.Email, func(ctx context.Context, region string) bool { return true })
		expectedRawRegion := polygon.GetRawRegionRes{
			Region: newIncentive.Region.String(),
			Geometry: polygon.Geometry{
				Coordinates: [][]geom.Coord{
					{
						{100.6270408630371, 14.735540398030539},
						{100.65845489501953, 14.719602330888959},
						{100.667724609375, 14.752141311434283},
						{100.63253402709961, 14.756457341526813},
						{100.6270408630371, 14.735540398030539},
					},
				},
			},
		}

		reqBodyBytes := new(bytes.Buffer)
		json.NewEncoder(reqBodyBytes).Encode(expectedRawRegion)

		reqBodyBytes.Bytes()
		deps.polygonService.EXPECT().
			GetRawRegion(gomock.Any(), newIncentive.Region.String()).
			Return(reqBodyBytes.Bytes(), nil)

		newIncentive.Geometry.Type = "MultiPolygon"
		newIncentive.Geometry.Coordinates = incentive.Coordinates{expectedRawRegion.Geometry.Coordinates}

		deps.incentiveRepo.EXPECT().
			Create(gctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, incentive *incentive.Incentive) (string, error) {
				newIncentive.IncentiveID = incentive.IncentiveID
				newIncentive.CreatedAt = incentive.CreatedAt
				newIncentive.UpdatedAt = incentive.UpdatedAt
				newIncentive.DateRange = incentive.DateRange
				require.Equal(tt, newIncentive, incentive)
				return "", nil
			})

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.regionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Return(true)

		incentiveAPI.Create(gctx)

		require.Equal(tt, http.StatusCreated, recorder.Code)
	})

	t.Run("create incentive success without coordinates but with streak", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()

		streak := incentive.Streak{
			NoOfDays:     1,
			OrdersPerDay: 2,
			Amount:       3,
		}
		start := time.Now()
		dateRange := incentive.DateRangeReq{
			Start: start,
			End:   start.Add(time.Second * 60 * 60 * 24 * 2),
		}

		bodyReq := incentiveCreateWithoutCoordinatesWithStreakRequest(streak, dateRange)

		gctx, recorder := req(bodyReq)
		newIncentive, _ := bodyReq.ToCreateModel(bodyReq.Email, func(ctx context.Context, region string) bool { return true })
		expectedRawRegion := polygon.GetRawRegionRes{
			Region: newIncentive.Region.String(),
			Geometry: polygon.Geometry{
				Coordinates: [][]geom.Coord{
					{
						{100.6270408630371, 14.735540398030539},
						{100.65845489501953, 14.719602330888959},
						{100.667724609375, 14.752141311434283},
						{100.63253402709961, 14.756457341526813},
						{100.6270408630371, 14.735540398030539},
					},
				},
			},
		}

		reqBodyBytes := new(bytes.Buffer)
		json.NewEncoder(reqBodyBytes).Encode(expectedRawRegion)

		reqBodyBytes.Bytes()
		deps.polygonService.EXPECT().
			GetRawRegion(gomock.Any(), newIncentive.Region.String()).
			Return(reqBodyBytes.Bytes(), nil)

		newIncentive.Geometry.Type = "MultiPolygon"
		newIncentive.Geometry.Coordinates = incentive.Coordinates{expectedRawRegion.Geometry.Coordinates}

		deps.incentiveRepo.EXPECT().
			Create(gctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, inc *incentive.Incentive) (string, error) {
				newIncentive.IncentiveID = inc.IncentiveID
				newIncentive.CreatedAt = inc.CreatedAt
				newIncentive.UpdatedAt = inc.UpdatedAt
				newIncentive.DateRange = inc.DateRange
				require.Equal(tt, newIncentive, inc)

				require.Equal(tt, streak, inc.Streak)
				require.Equal(tt, incentive.PeriodStreak, inc.PaymentType)

				return "", nil
			})

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.regionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Return(true)

		incentiveAPI.Create(gctx)

		require.Equal(tt, http.StatusCreated, recorder.Code)
	})

	t.Run("not 201 created if invalid payment type for streak", func(tt *testing.T) {
		incentiveAPI, _, finish := newTestIncentiveAPI(tt)
		defer finish()

		streak := incentive.Streak{
			NoOfDays:     1,
			OrdersPerDay: 2,
			Amount:       3,
		}
		start := time.Now()
		dateRange := incentive.DateRangeReq{
			Start: start,
			End:   start.Add(time.Second * 60 * 60 * 24 * time.Duration(streak.NoOfDays)),
		}
		bodyReq := incentiveCreateWithStreakRequest(streak, dateRange)

		// Invalid type!
		bodyReq.PaymentType = incentive.Daily

		gctx, recorder := req(bodyReq)

		incentiveAPI.Create(gctx)

		// Not http.StatusCreated!
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("404 when get region not found", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveCreateWithoutCoordinatesRequest()
		gctx, recorder := req(bodyReq)

		deps.polygonService.EXPECT().
			GetRawRegion(gomock.Any(), gomock.Any()).
			Return(nil, polygon.ErrNotFound)

		incentiveAPI.Create(gctx)
		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("500 when get region error", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveCreateWithoutCoordinatesRequest()
		gctx, recorder := req(bodyReq)

		deps.polygonService.EXPECT().
			GetRawRegion(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("error"))

		incentiveAPI.Create(gctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func incentiveUpdateRequest() incentive.Request {
	return incentive.Request{
		Name:        "just_name",
		Description: "just_description",
		ServiceTypes: []model.Service{
			"food",
		},
		PaymentType: "DAILY",
		Active:      true,
		AR:          createPointerFloat64(80.51),
		CR:          createPointerFloat64(88.32),
		Rating:      createPointerFloat64(4.30),
		Box:         createPointerBoolean(true),
		Tiers: []string{
			"t",
		},
		OrderTier: []incentive.OrderTierReq{
			{
				MinOrderAmount:  5,
				MaxOrderAmount:  10,
				IncentiveAmount: 15,
			},
		},
		DateRange: incentive.DateRangeReq{
			Start: time.Date(2022, 1, 22, 12, 0, 0, 0, timeutil.BangkokLocation()),
			End:   time.Date(2022, 1, 22, 13, 0, 0, 0, timeutil.BangkokLocation()),
		},
		Times: []incentive.TimesReq{
			{
				Start: "01:05:05",
				End:   "16:22:22",
			},
		},
		Coordinates: incentive.Coordinates{{
			{
				{100.6270408630371, 14.735540398030539},
				{100.65845489501953, 14.719602330888959},
				{100.667724609375, 14.752141311434283},
				{100.63253402709961, 14.756457341526813},
				{100.6270408630371, 14.735540398030539},
			},
		}},
		Sources: []string{
			"foo",
			"bar",
		},
		Region: "KORAT_CITY",
		Email:  "<EMAIL>",
	}
}

func incentiveUpdateWithStreakRequest(target incentive.Streak, dateRange incentive.DateRangeReq) incentive.Request {
	r := incentiveUpdateRequest()
	r.PaymentType = incentive.PeriodStreak
	r.Streak = target
	r.DateRange = dateRange

	return r
}

func incentiveUpdateWithoutCoordinatesRequest() incentive.Request {
	return incentive.Request{
		Name:        "just_name",
		Description: "just_description",
		ServiceTypes: []model.Service{
			"food",
		},
		PaymentType: "DAILY",
		Active:      true,
		AR:          createPointerFloat64(80.51),
		CR:          createPointerFloat64(88.32),
		Rating:      createPointerFloat64(4.30),
		Box:         createPointerBoolean(true),
		Tiers: []string{
			"t",
		},
		OrderTier: []incentive.OrderTierReq{
			{
				MinOrderAmount:  5,
				MaxOrderAmount:  10,
				IncentiveAmount: 15,
			},
		},
		DateRange: incentive.DateRangeReq{
			Start: time.Date(2022, 1, 22, 12, 0, 0, 0, timeutil.BangkokLocation()),
			End:   time.Date(2022, 1, 22, 13, 0, 0, 0, timeutil.BangkokLocation()),
		},
		Times: []incentive.TimesReq{
			{
				Start: "01:05:05",
				End:   "16:22:22",
			},
		},
		Sources: []string{
			"foo",
			"bar",
		},
		Region: "KORAT_CITY",
		Email:  "<EMAIL>",
	}
}

func incentiveUpdateWithoutCoordinatesWithStreakRequest(streak incentive.Streak, dateRange incentive.DateRangeReq) incentive.Request {
	r := incentiveUpdateWithoutCoordinatesRequest()
	r.PaymentType = incentive.PeriodStreak
	r.Streak = streak
	r.DateRange = dateRange

	return r
}

func TestIncentiveAPI_Update(t *testing.T) {
	req := func(id string, req incentive.Request) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/incentive-settings/%v", id), testutil.JSON(req))
	}

	id := fake.CharactersN(8)

	t.Run("update incentive success with coordinates", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveUpdateRequest()
		gctx, recorder := req(id, bodyReq)

		deps.incentiveRepo.EXPECT().
			Get(gctx, gomock.Any()).
			Return(&incentive.Incentive{}, nil)

		deps.incentiveRepo.EXPECT().
			Update(gctx, gomock.Any(), gomock.Any()).
			Return(nil)
		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.regionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Return(true)

		incentiveAPI.Update(gctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("update incentive success with coordinates and streak", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()

		streak := incentive.Streak{
			NoOfDays:     1,
			OrdersPerDay: 2,
			Amount:       3,
		}
		start := time.Now()
		dateRange := incentive.DateRangeReq{
			Start: start,
			End:   start.Add(time.Hour * 24 * 2),
		}
		bodyReq := incentiveUpdateWithStreakRequest(streak, dateRange)

		gctx, recorder := req(id, bodyReq)

		deps.incentiveRepo.EXPECT().
			Get(gctx, gomock.Any()).
			Return(&incentive.Incentive{}, nil)

		deps.incentiveRepo.EXPECT().
			Update(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, _ string, inc *incentive.Incentive) error {
				require.Equal(t, incentive.PeriodStreak, inc.PaymentType)
				require.Equal(t, streak, inc.Streak)
				return nil
			})

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.regionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Return(true)

		incentiveAPI.Update(gctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("update incentive success without coordinates", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveUpdateWithoutCoordinatesRequest()
		gctx, recorder := req(id, bodyReq)

		deps.incentiveRepo.EXPECT().
			Get(gctx, gomock.Any()).
			Return(&incentive.Incentive{}, nil)

		expectedRawRegion := polygon.GetRawRegionRes{
			Geometry: polygon.Geometry{
				Coordinates: [][]geom.Coord{
					{
						{100.6270408630371, 14.735540398030539},
						{100.65845489501953, 14.719602330888959},
						{100.667724609375, 14.752141311434283},
						{100.63253402709961, 14.756457341526813},
						{100.6270408630371, 14.735540398030539},
					},
				},
			},
		}

		reqBodyBytes := new(bytes.Buffer)
		json.NewEncoder(reqBodyBytes).Encode(expectedRawRegion)

		reqBodyBytes.Bytes()
		deps.polygonService.EXPECT().
			GetRawRegion(gomock.Any(), gomock.Any()).
			Return(reqBodyBytes.Bytes(), nil)

		deps.incentiveRepo.EXPECT().
			Update(gctx, gomock.Any(), gomock.Any()).
			Return(nil)

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.regionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Return(true)

		incentiveAPI.Update(gctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("update incentive success without coordinates but with streak", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()

		streak := incentive.Streak{
			NoOfDays:     1,
			OrdersPerDay: 2,
			Amount:       3,
		}
		start := time.Now()
		dateRange := incentive.DateRangeReq{
			Start: start,
			End:   start.Add(time.Hour * 24 * 2),
		}
		bodyReq := incentiveUpdateWithoutCoordinatesWithStreakRequest(streak, dateRange)

		gctx, recorder := req(id, bodyReq)

		deps.incentiveRepo.EXPECT().
			Get(gctx, gomock.Any()).
			Return(&incentive.Incentive{}, nil)

		expectedRawRegion := polygon.GetRawRegionRes{
			Geometry: polygon.Geometry{
				Coordinates: [][]geom.Coord{
					{
						{100.6270408630371, 14.735540398030539},
						{100.65845489501953, 14.719602330888959},
						{100.667724609375, 14.752141311434283},
						{100.63253402709961, 14.756457341526813},
						{100.6270408630371, 14.735540398030539},
					},
				},
			},
		}

		reqBodyBytes := new(bytes.Buffer)
		json.NewEncoder(reqBodyBytes).Encode(expectedRawRegion)

		reqBodyBytes.Bytes()
		deps.polygonService.EXPECT().
			GetRawRegion(gomock.Any(), gomock.Any()).
			Return(reqBodyBytes.Bytes(), nil)

		deps.incentiveRepo.EXPECT().
			Update(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, id string, inc *incentive.Incentive) error {
				require.Equal(t, incentive.PeriodStreak, inc.PaymentType)
				require.Equal(t, streak, inc.Streak)
				return nil
			})

		deps.auditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)
		deps.regionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Return(true)

		incentiveAPI.Update(gctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("not 201 created if invalid payment type for streak", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()

		streak := incentive.Streak{
			NoOfDays:     1,
			OrdersPerDay: 2,
			Amount:       3,
		}
		start := time.Now()
		dateRange := incentive.DateRangeReq{
			Start: start,
			End:   start.Add(time.Hour * 24 * 2),
		}
		bodyReq := incentiveUpdateWithStreakRequest(streak, dateRange)

		// Invalid type!
		bodyReq.PaymentType = incentive.Daily
		gctx, recorder := req(id, bodyReq)

		deps.incentiveRepo.EXPECT().
			Get(gctx, gomock.Any()).
			Return(&incentive.Incentive{}, nil)

		incentiveAPI.Update(gctx)

		// Not http.StatusCreated!
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("404 when get region not found", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveUpdateWithoutCoordinatesRequest()
		gctx, recorder := req(id, bodyReq)

		deps.incentiveRepo.EXPECT().
			Get(gctx, gomock.Any()).
			Return(&incentive.Incentive{}, nil)

		deps.polygonService.EXPECT().
			GetRawRegion(gomock.Any(), gomock.Any()).
			Return(nil, polygon.ErrNotFound)

		incentiveAPI.Update(gctx)
		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("500 when get region error", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveUpdateWithoutCoordinatesRequest()
		gctx, recorder := req(id, bodyReq)

		deps.incentiveRepo.EXPECT().
			Get(gctx, gomock.Any()).
			Return(&incentive.Incentive{}, nil)

		deps.polygonService.EXPECT().
			GetRawRegion(gomock.Any(), gomock.Any()).
			Return(nil, errors.New("error"))

		incentiveAPI.Update(gctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestIncentiveAPI_List(t *testing.T) {
	req := func(_ incentive.Request) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("GET", "/v1/admin/incentive-settings?region=BKK", nil)
	}

	t.Run("get list success", func(tt *testing.T) {
		incentiveAPI, deps, finish := newTestIncentiveAPI(tt)
		defer finish()
		bodyReq := incentiveUpdateWithoutCoordinatesRequest()
		gctx, recorder := req(bodyReq)

		incentives := []*incentive.Incentive{
			{
				Name: "1",
			},
		}

		deps.incentiveRepo.EXPECT().
			List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(incentives, nil)

		deps.incentiveRepo.EXPECT().
			Count(gomock.Any(), gomock.Any()).
			Return(1, nil)

		incentiveAPI.List(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func createPointerFloat64(f float64) *float64 {
	return &f
}

func createPointerBoolean(b bool) *bool {
	return &b
}

type incentiveAPIDeps struct {
	incentiveRepo    *mock_incentive.MockIncentiveRepository
	auditLogRepo     *mock_repository.MockAuditLogRepository
	polygonService   *mock_polygon.MockPolygon
	regionRepository *mock_repository.MockRegionRepository
}

func newTestIncentiveAPI(t gomock.TestReporter) (*incentive.IncentiveAPI, *incentiveAPIDeps, func()) {
	ctrl := gomock.NewController(t)
	deps := &incentiveAPIDeps{
		incentiveRepo:    mock_incentive.NewMockIncentiveRepository(ctrl),
		auditLogRepo:     mock_repository.NewMockAuditLogRepository(ctrl),
		polygonService:   mock_polygon.NewMockPolygon(ctrl),
		regionRepository: mock_repository.NewMockRegionRepository(ctrl),
	}
	return incentive.ProvideIncentiveAPI(deps.incentiveRepo, deps.auditLogRepo, deps.polygonService, deps.regionRepository), deps, func() {
		ctrl.Finish()
	}
}
