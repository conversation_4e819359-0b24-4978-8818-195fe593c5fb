// Code generated by metricproxy. DO NOT EDIT.

package incentive

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyIncentiveProgressRepository(delegate IncentiveProgressRepository, meter metric.Meter) *ProxyIncentiveProgressRepository {
	return &ProxyIncentiveProgressRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyIncentiveProgressRepository-tracer"),
	}
}

type ProxyIncentiveProgressRepository struct {
	Delegate         IncentiveProgressRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyIncentiveProgressRepository) GetByDateRange(i0 context.Context, i1 string, i2 string, i3 time.Time, i4 time.Time) ([]IncentiveProgress, error) {

	_, span := p.Tracer.Start(i0, "IncentiveProgressRepository.GetByDateRange")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetByDateRange(i0, i1, i2, i3, i4)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "IncentiveProgressRepository.GetByDateRange")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyIncentiveProgressRepository) Upsert(i0 context.Context, i1 *IncentiveProgress, i2 ...repository.Option) error {

	_, span := p.Tracer.Start(i0, "IncentiveProgressRepository.Upsert")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Upsert(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "IncentiveProgressRepository.Upsert")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
