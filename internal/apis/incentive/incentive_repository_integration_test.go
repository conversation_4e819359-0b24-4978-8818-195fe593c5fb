//go:build integration_test
// +build integration_test

package incentive_test

import (
	"context"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"github.com/stretchr/testify/require"
	"testing"
	"time"
)

const (
	driverId1 = "TEST-DRIVER-1"
	driverId2 = "TEST-DRIVER-2"
	driverId3 = "TEST-DRIVER-3"
)

func TestIncentiveRepository_GetActiveIncentiveByDriverAndTime(t *testing.T) {
	t.Parallel()
	ctn := ittest.NewContainer(t)
	err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_incentive/whitelist_blacklist")
	require.NoError(t, err, "unable to setup fixture")

	exceptIncentive := make(map[string]incentive.Incentive)
	exceptIncentive["1d0f4934c2884f81845c99a5795fee23"] = incentive.Incentive{
		IncentiveID: "1d0f4934c2884f81845c99a5795fee23",
		Active:      true,
		DateRange: incentive.DateRange{
			Start: time.Date(2023, 4, 19, 17, 0, 0, 0, time.UTC),
			End:   time.Date(2023, 4, 20, 16, 59, 59, 999000000, time.UTC),
		},
		Name:         "Test Daily Whitelist Incentive",
		Region:       "BKK",
		WhitelistIDs: []string{"TEST-DRIVER-1"},
		BlacklistIDs: []string{},
	}
	exceptIncentive["1d0f4934c2884f81845c99a5795fee24"] = incentive.Incentive{
		IncentiveID: "1d0f4934c2884f81845c99a5795fee24",
		Active:      true,
		DateRange: incentive.DateRange{
			Start: time.Date(2023, 4, 19, 17, 0, 0, 0, time.UTC),
			End:   time.Date(2023, 4, 20, 16, 59, 59, 999000000, time.UTC),
		},
		Name:         "Test Daily Normal Incentive",
		Region:       "BKK",
		WhitelistIDs: []string{},
		BlacklistIDs: []string{},
	}

	exceptIncentive["1d0f4934c2884f81845c99a5795fee26"] = incentive.Incentive{
		IncentiveID: "1d0f4934c2884f81845c99a5795fee26",
		Active:      true,
		DateRange: incentive.DateRange{
			Start: time.Date(2023, 4, 19, 17, 0, 0, 0, time.UTC),
			End:   time.Date(2023, 4, 20, 16, 59, 59, 999000000, time.UTC),
		},
		Name:         "Test Daily Whitelist Blacklist Incentive",
		Region:       "BKK",
		WhitelistIDs: []string{"TEST-DRIVER", "TEST-DRIVER-1"},
		BlacklistIDs: []string{"TEST-DRIVER"},
	}

	exceptIncentive["1d0f4934c2884f81845c99a5795fee6e"] = incentive.Incentive{
		IncentiveID: "1d0f4934c2884f81845c99a5795fee6e",
		Active:      true,
		DateRange: incentive.DateRange{
			Start: time.Date(2023, 4, 19, 17, 0, 0, 0, time.UTC),
			End:   time.Date(2023, 4, 20, 16, 59, 59, 999000000, time.UTC),
		},
		Name:         "Test Daily Blacklist Incentive",
		Region:       "BKK",
		WhitelistIDs: []string{},
		BlacklistIDs: []string{"TEST-DRIVER"},
	}

	t.Run("get incentive with blacklist driverID", func(t *testing.T) {
		queryDate := time.Date(2023, 04, 20, 0, 0, 0, 0, time.UTC)
		driver := &model.Driver{
			DriverID: driverId1,
			Region:   "BKK",
		}
		incs, err := ctn.DataStoreIncentiveRepository.GetActiveIncentiveByDriverAndTime(context.Background(), driver, queryDate, false)
		require.NoError(t, err)
		require.Equal(t, 1, len(incs))

		inc := incs[0]
		require.Equal(t, "1d0f4934c2884f81845c99a5795fee24", inc.IncentiveID)
		exInc, has := exceptIncentive[inc.IncentiveID]
		require.True(t, has)
		require.Equal(t, exInc.IncentiveID, inc.IncentiveID)
		require.Equal(t, exInc.Name, inc.Name)
		require.Equal(t, exInc.Active, inc.Active)
		require.Equal(t, exInc.DateRange.Start, inc.DateRange.Start)
		require.Equal(t, exInc.DateRange.End, inc.DateRange.End)
		require.Equal(t, exInc.Region, inc.Region)

		expWhitelistSet := types.NewStringSet(exInc.WhitelistIDs...)
		expBlacklistSet := types.NewStringSet(exInc.BlacklistIDs...)
		require.True(t, expWhitelistSet.HasAll(inc.WhitelistIDs...))
		require.True(t, expBlacklistSet.HasAll(inc.BlacklistIDs...))

	})

	t.Run("get incentive with none blacklist driverID", func(t *testing.T) {
		queryDate := time.Date(2023, 04, 20, 0, 0, 0, 0, time.UTC)
		driver := &model.Driver{
			DriverID: driverId2,
			Region:   "BKK",
		}
		incs, err := ctn.DataStoreIncentiveRepository.GetActiveIncentiveByDriverAndTime(context.Background(), driver, queryDate, false)
		require.NoError(t, err)
		require.Equal(t, 4, len(incs))
		for _, inc := range incs {
			exInc, has := exceptIncentive[inc.IncentiveID]
			require.True(t, has)
			require.Equal(t, exInc.IncentiveID, inc.IncentiveID)
			require.Equal(t, exInc.Name, inc.Name)
			require.Equal(t, exInc.Active, inc.Active)
			require.Equal(t, exInc.DateRange.Start, inc.DateRange.Start)
			require.Equal(t, exInc.DateRange.End, inc.DateRange.End)
			require.Equal(t, exInc.Region, inc.Region)

			expWhitelistSet := types.NewStringSet(exInc.WhitelistIDs...)
			expBlacklistSet := types.NewStringSet(exInc.BlacklistIDs...)
			require.True(t, expWhitelistSet.HasAll(inc.WhitelistIDs...))
			require.True(t, expBlacklistSet.HasAll(inc.BlacklistIDs...))
		}

	})

}

func TestIncentiveRepository_GetIncentiveByIdAndDriverId(t *testing.T) {
	t.Parallel()
	ctn := ittest.NewContainer(t)
	err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_incentive/whitelist_blacklist")
	require.NoError(t, err, "unable to setup fixture")

	t.Run("Should get incentive that match Id", func(t *testing.T) {
		incId := "1d0f4934c2884f81845c99a5795fee24"
		inc, err := ctn.DataStoreIncentiveRepository.GetIncentiveByIdAndDriverId(context.Background(), incId, driverId1)

		require.NoError(t, err)
		require.Equal(t, "Test Daily Normal Incentive", inc.Name)
		require.Equal(t, 0, len(inc.WhitelistIDs))
		require.Equal(t, 0, len(inc.BlacklistIDs))
	})
	t.Run("Should not get Incentive by ID that driverID contain in blacklist", func(t *testing.T) {
		incId := "1d0f4934c2884f81845c99a5795fee6e"
		t.Parallel()

		t.Run("get incentive with blacklist driverId", func(t *testing.T) {
			_, err := ctn.DataStoreIncentiveRepository.GetIncentiveByIdAndDriverId(context.Background(), incId, driverId1)
			require.Errorf(t, err, "Data not found")
		})

		t.Run("get incentive with not blacklist driverId", func(t *testing.T) {
			inc, err := ctn.DataStoreIncentiveRepository.GetIncentiveByIdAndDriverId(context.Background(), incId, driverId2)
			require.NoError(t, err)
			require.Equal(t, "Test Daily Blacklist Incentive", inc.Name)
			require.Equal(t, 0, len(inc.WhitelistIDs))
			require.Equal(t, 1, len(inc.BlacklistIDs))
		})

	})
	t.Run("Should get Incentive By ID that driverID contain in whitelist", func(t *testing.T) {
		incId := "1d0f4934c2884f81845c99a5795fee23"
		t.Parallel()

		t.Run("get incentive with whitelist driverId", func(t *testing.T) {
			inc, err := ctn.DataStoreIncentiveRepository.GetIncentiveByIdAndDriverId(context.Background(), incId, driverId2)
			require.NoError(t, err)
			whiteListSet := types.NewStringSet(inc.WhitelistIDs...)
			require.Equal(t, "Test Daily Whitelist Incentive", inc.Name)
			require.Equal(t, len(inc.WhitelistIDs), 1)
			require.True(t, whiteListSet.Has(driverId2))
		})

		t.Run("get incentive with none whitelist driverId", func(t *testing.T) {
			_, err := ctn.DataStoreIncentiveRepository.GetIncentiveByIdAndDriverId(context.Background(), incId, driverId1)
			require.Errorf(t, err, "Data not found")
		})

	})

	t.Run("Should not get Incentive by Id that driverID contain in blacklist and whitelist", func(t *testing.T) {
		incId := "1d0f4934c2884f81845c99a5795fee26"
		t.Parallel()
		t.Run("get incentive with blacklist driverId", func(t *testing.T) {
			_, err := ctn.DataStoreIncentiveRepository.GetIncentiveByIdAndDriverId(context.Background(), incId, driverId1)
			require.Errorf(t, err, "Data not found")
		})

		t.Run("get incentive with none blacklist and none whitelist driverId", func(t *testing.T) {
			_, err := ctn.DataStoreIncentiveRepository.GetIncentiveByIdAndDriverId(context.Background(), incId, driverId3)
			require.Errorf(t, err, "Data not found")
		})

		t.Run("get incentive with whitelist driverId", func(t *testing.T) {
			inc, err := ctn.DataStoreIncentiveRepository.GetIncentiveByIdAndDriverId(context.Background(), incId, driverId2)
			require.NoError(t, err)
			whiteListSet := types.NewStringSet(inc.WhitelistIDs...)
			require.Equal(t, "Test Daily Whitelist Blacklist Incentive", inc.Name)
			require.Equal(t, len(inc.WhitelistIDs), 2)
			require.True(t, whiteListSet.Has(driverId2))
		})

	})

}
