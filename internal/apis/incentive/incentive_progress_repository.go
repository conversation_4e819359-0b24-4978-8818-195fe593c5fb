package incentive

//go:generate mockgen -source=./incentive_progress_repository.go -destination=./mock_incentive/mock_incentive_progress_repository.go -package=mock_incentive

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type IncentiveProgressRepository interface {
	GetByDateRange(ctx context.Context, driverID string, incentiveId string, startDate time.Time, endDate time.Time) ([]IncentiveProgress, error)
	Upsert(ctx context.Context, progress *IncentiveProgress, opts ...repository.Option) error
}

type IncentiveProgressDataStore mongodb.DataStoreInterface

type DataStoreIncentiveProgressRepository struct {
	datastore IncentiveProgressDataStore
}

func (d *DataStoreIncentiveProgressRepository) GetByDateRange(ctx context.Context, driverID string, incentiveId string, startDate time.Time, endDate time.Time) ([]IncentiveProgress, error) {
	var incp []IncentiveProgress

	opts := repository.ToDBOptions([]repository.Option{repository.WithReadSecondaryPreferred})
	err := d.datastore.FindAndSort(
		ctx,
		bson.M{
			"driver_id":    driverID,
			"incentive_id": incentiveId,
			"date": bson.M{
				"$gte": startDate,
				"$lte": endDate,
			},
		},
		0,
		0,
		[]string{},
		&incp,
		opts...,
	)
	if err != nil {
		return nil, err
	}
	return incp, nil
}

func (d *DataStoreIncentiveProgressRepository) Upsert(ctx context.Context, progress *IncentiveProgress, opts ...repository.Option) error {
	progress.Date = timeutil.DateTruncateTZ(progress.Date, timeutil.BangkokLocation())
	result, err := d.datastore.Upsert(ctx, bson.M{
		"driver_id":    progress.DriverID,
		"date":         progress.Date,
		"incentive_id": progress.IncentiveID,
	}, progress, repository.ToDBOptions(opts)...)

	if result.UpsertedCount+result.ModifiedCount == 0 {
		return fmt.Errorf("no updateed/upserted document")
	}

	return err
}

func ProvideIncentiveProgressDataStore(conn *mongodb.Conn) IncentiveProgressDataStore {
	return mongodb.NewDataStoreWithConn(conn, "incentive_progress")
}

func ProvideDataStoreIncentiveProgressRepository(ds IncentiveProgressDataStore, meter metric.Meter) *ProxyIncentiveProgressRepository {
	return NewLatencyProxyIncentiveProgressRepository(&DataStoreIncentiveProgressRepository{datastore: ds}, meter)
}
