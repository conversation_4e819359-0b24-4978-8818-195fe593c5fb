package incentive

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

type IncentiveAPI struct {
	repo             IncentiveRepository
	auditRepo        repository.AuditLogRepository
	polygonService   polygon.Polygon
	regionRepository repository.RegionRepository
}

func ProvideIncentiveAPI(repo IncentiveRepository, auditRepo repository.AuditLogRepository, polygonService polygon.Polygon, regionRepository repository.RegionRepository) *IncentiveAPI {
	return &IncentiveAPI{
		repo:             repo,
		auditRepo:        auditRepo,
		polygonService:   polygonService,
		regionRepository: regionRepository,
	}
}

func (ic *IncentiveAPI) Create(gctx *gin.Context) {
	var req Request
	if err := gctx.BindJSON(&req); err != nil {
		_ = gctx.Error(err)
		return
	}

	email := req.Email

	// Set to coordinates to the whole of region, if request coordinate is nil
	err := req.ValidateCoordinateEmpty(gctx, ic.polygonService)
	if err != nil {
		if err == polygon.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, errors.New("region not found")))
			return
		}
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, errors.New("Cannot get region from polygon-service")))
		return
	}

	checkRegionExistsFunc := CheckRegionExistsFn(ic.regionRepository.IsExists)
	m, err := req.ToCreateModel(email, checkRegionExistsFunc)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}
	_, err = ic.repo.Create(gctx, m)
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	auditLog := model.NewAuditLog(
		model.AuditLogActor{ID: email},
		model.AuditObject{ObjectType: "setting_incentive", ID: m.IncentiveID},
		model.CreateAction,
		nil,
		m,
	)

	if err := ic.auditRepo.Insert(gctx, &auditLog); err != nil {
		logrus.Errorf("cannot insert auditlog of updating a driver status: %s", err)
	}

	apiutil.Created(gctx, NewResponse(m))
}

func (ic *IncentiveAPI) Update(gctx *gin.Context) {
	var req Request
	if err := gctx.BindJSON(&req); err != nil {
		_ = gctx.Error(err)
		return
	}

	id := gctx.Param("incentive_id")
	email := req.Email

	res, err := ic.repo.Get(gctx, id)
	if err != nil && res == nil {
		_ = gctx.Error(err)
		return
	}

	err = req.ValidateCoordinateEmpty(gctx, ic.polygonService)
	if err != nil {
		if err == polygon.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, errors.New("region not found")))
			return
		}
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, errors.New("Cannot get region from polygon-service")))
		return
	}

	checkRegionExistsFunc := CheckRegionExistsFn(ic.regionRepository.IsExists)
	m, err := req.ToUpdateModel(res, email, checkRegionExistsFunc)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	err = ic.repo.Update(gctx, id, m)
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	auditLog := model.NewAuditLog(
		model.AuditLogActor{ID: email},
		model.AuditObject{ObjectType: "setting_incentive", ID: m.ID.Hex()},
		model.UpdateAction,
		res,
		m,
	)

	if err := ic.auditRepo.Insert(gctx, &auditLog); err != nil {
		logrus.Errorf("cannot insert auditlog of updating a driver status: %s", err)
	}

	apiutil.NoContent(gctx)
}

func (ic *IncentiveAPI) Get(gctx *gin.Context) {
	inc, err := ic.repo.Get(gctx, gctx.Param("incentive_id"))
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	apiutil.OK(gctx, NewResponse(inc))
}

func (ic *IncentiveAPI) List(gctx *gin.Context) {
	var req ListIncentiveReq
	if err := gctx.Bind(&req); err != nil {
		_ = gctx.Error(apiError.ErrInvalidRequest(err))
		return
	}

	skip, limit := utils.ParsePagination(gctx)
	var sort []string
	if req.Sort != "" {
		s := fmt.Sprintf("-%s", req.Sort)
		if req.Direction == "ASC" {
			s = req.Sort
		}
		sort = append(sort, s)
	}

	incs, err := ic.repo.List(gctx, req.toQuery(), skip, limit, sort)
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	count, err := ic.repo.Count(gctx, req.toQuery())
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	response := make([]*Response, len(incs))
	for i, inc := range incs {
		response[i] = NewResponse(inc)
	}

	apiutil.OKList(gctx, response, count)
}

func (ic *IncentiveAPI) Delete(gctx *gin.Context) {
	id := gctx.Param("incentive_id")
	err := ic.repo.Delete(gctx, id)
	if err != nil {
		_ = gctx.Error(err)
		return
	}
	apiutil.NoContent(gctx)
}
