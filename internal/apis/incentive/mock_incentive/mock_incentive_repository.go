// Code generated by MockGen. DO NOT EDIT.
// Source: ../incentive_repository.go

// Package mock_incentive is a generated GoMock package.
package mock_incentive

import (
	context "context"
	reflect "reflect"
	time "time"

	incentive "git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockIncentiveRepository is a mock of IncentiveRepository interface.
type MockIncentiveRepository struct {
	ctrl     *gomock.Controller
	recorder *MockIncentiveRepositoryMockRecorder
}

// MockIncentiveRepositoryMockRecorder is the mock recorder for MockIncentiveRepository.
type MockIncentiveRepositoryMockRecorder struct {
	mock *MockIncentiveRepository
}

// NewMockIncentiveRepository creates a new mock instance.
func NewMockIncentiveRepository(ctrl *gomock.Controller) *MockIncentiveRepository {
	mock := &MockIncentiveRepository{ctrl: ctrl}
	mock.recorder = &MockIncentiveRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIncentiveRepository) EXPECT() *MockIncentiveRepositoryMockRecorder {
	return m.recorder
}

// Count mocks base method.
func (m *MockIncentiveRepository) Count(ctx context.Context, query *incentive.ListQuery) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", ctx, query)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockIncentiveRepositoryMockRecorder) Count(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockIncentiveRepository)(nil).Count), ctx, query)
}

// Create mocks base method.
func (m *MockIncentiveRepository) Create(ctx context.Context, incentive *incentive.Incentive) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, incentive)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Create indicates an expected call of Create.
func (mr *MockIncentiveRepositoryMockRecorder) Create(ctx, incentive interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockIncentiveRepository)(nil).Create), ctx, incentive)
}

// Delete mocks base method.
func (m *MockIncentiveRepository) Delete(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockIncentiveRepositoryMockRecorder) Delete(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockIncentiveRepository)(nil).Delete), ctx, id)
}

// FindByIDs mocks base method.
func (m *MockIncentiveRepository) FindByIDs(ctx context.Context, ids []string) ([]incentive.Incentive, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByIDs", ctx, ids)
	ret0, _ := ret[0].([]incentive.Incentive)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDs indicates an expected call of FindByIDs.
func (mr *MockIncentiveRepositoryMockRecorder) FindByIDs(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDs", reflect.TypeOf((*MockIncentiveRepository)(nil).FindByIDs), ctx, ids)
}

// FindByIDsWithSelector mocks base method.
func (m *MockIncentiveRepository) FindByIDsWithSelector(ctx context.Context, ids []string, opts ...repository.Option) ([]incentive.Incentive, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, ids}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByIDsWithSelector", varargs...)
	ret0, _ := ret[0].([]incentive.Incentive)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDsWithSelector indicates an expected call of FindByIDsWithSelector.
func (mr *MockIncentiveRepositoryMockRecorder) FindByIDsWithSelector(ctx, ids interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, ids}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDsWithSelector", reflect.TypeOf((*MockIncentiveRepository)(nil).FindByIDsWithSelector), varargs...)
}

// Get mocks base method.
func (m *MockIncentiveRepository) Get(ctx context.Context, id string) (*incentive.Incentive, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*incentive.Incentive)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockIncentiveRepositoryMockRecorder) Get(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockIncentiveRepository)(nil).Get), ctx, id)
}

// GetActiveIncentiveByDriverAndTime mocks base method.
func (m *MockIncentiveRepository) GetActiveIncentiveByDriverAndTime(ctx context.Context, driver *model.Driver, t time.Time, withGeoJson bool) ([]incentive.Incentive, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveIncentiveByDriverAndTime", ctx, driver, t, withGeoJson)
	ret0, _ := ret[0].([]incentive.Incentive)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveIncentiveByDriverAndTime indicates an expected call of GetActiveIncentiveByDriverAndTime.
func (mr *MockIncentiveRepositoryMockRecorder) GetActiveIncentiveByDriverAndTime(ctx, driver, t, withGeoJson interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveIncentiveByDriverAndTime", reflect.TypeOf((*MockIncentiveRepository)(nil).GetActiveIncentiveByDriverAndTime), ctx, driver, t, withGeoJson)
}

// GetActiveIncentiveByRegion mocks base method.
func (m *MockIncentiveRepository) GetActiveIncentiveByRegion(ctx context.Context, region string, start, end time.Time) (*incentive.Incentive, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveIncentiveByRegion", ctx, region, start, end)
	ret0, _ := ret[0].(*incentive.Incentive)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveIncentiveByRegion indicates an expected call of GetActiveIncentiveByRegion.
func (mr *MockIncentiveRepositoryMockRecorder) GetActiveIncentiveByRegion(ctx, region, start, end interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveIncentiveByRegion", reflect.TypeOf((*MockIncentiveRepository)(nil).GetActiveIncentiveByRegion), ctx, region, start, end)
}

// GetActiveIncentiveByTime mocks base method.
func (m *MockIncentiveRepository) GetActiveIncentiveByTime(ctx context.Context, targetDate time.Time) ([]incentive.Incentive, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveIncentiveByTime", ctx, targetDate)
	ret0, _ := ret[0].([]incentive.Incentive)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveIncentiveByTime indicates an expected call of GetActiveIncentiveByTime.
func (mr *MockIncentiveRepositoryMockRecorder) GetActiveIncentiveByTime(ctx, targetDate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveIncentiveByTime", reflect.TypeOf((*MockIncentiveRepository)(nil).GetActiveIncentiveByTime), ctx, targetDate)
}

// GetIncentiveByIdAndDriverId mocks base method.
func (m *MockIncentiveRepository) GetIncentiveByIdAndDriverId(ctx context.Context, id, driverId string) (*incentive.Incentive, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIncentiveByIdAndDriverId", ctx, id, driverId)
	ret0, _ := ret[0].(*incentive.Incentive)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIncentiveByIdAndDriverId indicates an expected call of GetIncentiveByIdAndDriverId.
func (mr *MockIncentiveRepositoryMockRecorder) GetIncentiveByIdAndDriverId(ctx, id, driverId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIncentiveByIdAndDriverId", reflect.TypeOf((*MockIncentiveRepository)(nil).GetIncentiveByIdAndDriverId), ctx, id, driverId)
}

// List mocks base method.
func (m *MockIncentiveRepository) List(ctx context.Context, query *incentive.ListQuery, skip, limit int, sort []string) ([]*incentive.Incentive, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "List", ctx, query, skip, limit, sort)
	ret0, _ := ret[0].([]*incentive.Incentive)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockIncentiveRepositoryMockRecorder) List(ctx, query, skip, limit, sort interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockIncentiveRepository)(nil).List), ctx, query, skip, limit, sort)
}

// Update mocks base method.
func (m *MockIncentiveRepository) Update(ctx context.Context, id string, incentive *incentive.Incentive) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, id, incentive)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockIncentiveRepositoryMockRecorder) Update(ctx, id, incentive interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockIncentiveRepository)(nil).Update), ctx, id, incentive)
}
