package summaryofchange

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"git.wndv.co/go/logx/v2"
	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

func (api *SummaryOfChangeAPI) Create(ctx *gin.Context) {
	summary, err := api.parseSummaryOfChangeRequest(ctx)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}
	summaryOfChange := summary.toModel()
	admin := getAdminUser(ctx)
	summaryOfChange.CreatedBy = admin.GetEmail()
	summaryOfChange.UpdatedBy = admin.GetEmail()
	summaryOfChange.Status = model.SummaryOfChangeStatusActive
	err = api.repo.Create(ctx, &summaryOfChange)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	auditLog := model.NewAuditLog(
		model.AuditLogActor{ID: admin.GetEmail()},
		model.AuditObject{ObjectType: model.SummaryOfChangeObject, ID: summaryOfChange.ID.Hex()},
		model.CreateAction,
		nil,
		summaryOfChange,
	)
	auditLog.Event = model.AuditEventCreateSummaryOfChange
	if err := api.auditRepo.Insert(ctx, &auditLog); err != nil {
		logx.Error().Err(err).Msg("SummaryOfChangeAPI Create. save audit log err")
	}

	apiutil.Created(ctx, NewSummaryOfChangeRes(summaryOfChange))
}

func getAdminUser(ctx *gin.Context) auth.AdminUser {
	if user, isExist := auth.GetAdminUserFromGctx(ctx); isExist {
		return user
	}
	return auth.NewAdminUser("", "", "n/a", types.NewStringSet())
}

func (api *SummaryOfChangeAPI) Update(ctx *gin.Context) {
	id := ctx.Param("id")
	summary, err := api.parseSummaryOfChangeRequest(ctx)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	existingSummary, err := api.repo.Get(ctx, id)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_FOUND, err))
		return
	}
	admin := getAdminUser(ctx)
	summaryOfChangeBeforeUpdate := existingSummary
	updateSummary(existingSummary, summary, admin.GetEmail())

	err = api.repo.Update(ctx, existingSummary)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}
	auditLog := model.NewAuditLog(
		model.AuditLogActor{ID: admin.GetEmail()},
		model.AuditObject{ObjectType: model.SummaryOfChangeObject, ID: ""},
		model.UpdateAction,
		*summaryOfChangeBeforeUpdate,
		*existingSummary,
	)
	auditLog.Event = model.AuditEventUpdateSummaryOfChange
	if err := api.auditRepo.Insert(ctx, &auditLog); err != nil {
		logx.Error().Err(err).Msg("SummaryOfChangeAPI Create. save audit log err")
	}
	apiutil.OK(ctx, NewSummaryOfChangeRes(*existingSummary))
}

func updateSummary(existingSummary *model.SummaryOfChange, summary SummaryOfChangeReq, admin string) {
	existingSummary.SummaryOfChangeDate = summary.SummaryOfChangeDate
	existingSummary.Status = summary.Status
	existingSummary.Content = summary.Content
	existingSummary.Name = summary.Name
	existingSummary.UpdatedBy = admin
}
func (api *SummaryOfChangeAPI) Get(ctx *gin.Context) {
	id := ctx.Param("id")

	summary, err := api.repo.Get(ctx, id)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	apiutil.OK(ctx, NewSummaryOfChangeRes(*summary))
}

func (api *SummaryOfChangeAPI) GetAll(ctx *gin.Context) {
	var req GetAllSummaryOfChangeAdminReq
	if err := ctx.ShouldBind(&req); err != nil {
		ctx.JSON(http.StatusBadRequest, utils.ErrorStructResponse(err))
		return
	}
	skip, limit := utils.OptionalParsePagination(ctx)
	query := repository.NewSummaryOfChangeQuery().SetStatus(req.Status).Query()
	summaries, err := api.repo.FindWithQueryAndSortSummaryOfChange(ctx, query, skip, limit, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	apiutil.OK(ctx, NewSummaryOfChangeListRes(summaries))
}

func (api *SummaryOfChangeAPI) Archived(ctx *gin.Context) {
	id := ctx.Param("id")
	admin := getAdminUser(ctx)
	err := api.repo.Archived(ctx, id, admin.GetEmail())
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	auditLog := model.NewAuditLog(
		model.AuditLogActor{ID: admin.GetEmail()},
		model.AuditObject{ObjectType: model.SummaryOfChangeObject, ID: id},
		model.UpdateAction,
		nil,
		nil,
	)
	auditLog.Event = model.AuditEventArchivedSummaryOfChange
	if err := api.auditRepo.Insert(ctx, &auditLog); err != nil {
		logx.Error().Err(err).Msg("SummaryOfChangeAPI Create. save audit log err")
	}

	apiutil.OK(ctx, gin.H{"status": "deleted"})
}

func (api *SummaryOfChangeAPI) parseSummaryOfChangeRequest(ctx *gin.Context) (SummaryOfChangeReq, error) {
	var summary SummaryOfChangeReq
	if err := ctx.ShouldBindJSON(&summary); err != nil {
		return summary, err
	}
	return summary, nil
}
