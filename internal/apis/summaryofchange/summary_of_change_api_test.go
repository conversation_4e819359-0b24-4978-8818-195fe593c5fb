package summaryofchange_test

import (
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/summaryofchange"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestSummaryOfChangeAPI_GetSummaryOfChanges(t *testing.T) {
	t.Parallel()

	makeReq := func(query string) (*gin.Context, *httptest.ResponseRecorder) {
		url := "/v1/driver/summary-of-changes"
		if query != "" {
			url += fmt.Sprintf("?%s", query)
		}
		return testutil.TestRequestContext("GET", url, nil)
	}

	existingSummaries := []model.SummaryOfChange{
		{
			Content: "SUMMARY_ID_1",
		},
		{
			Content: "SUMMARY_ID_2",
		},
	}

	t.Run("should return 200 and summary of changes correctly", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		deps.summaryOfChangeRepo.
			EXPECT().FindWithQueryAndSortSummaryOfChange(gomock.Any(), gomock.Any(), 0, 0, gomock.Any()).
			Return(existingSummaries, nil)

		gctx, recorder := makeReq("")

		api.GetSummaryOfChanges(gctx)

		var actual summaryofchange.ClientSummaryOfChangeRes

		testutil.DecodeJSON(t, recorder.Body, &actual)

		require.Equal(t, http.StatusOK, recorder.Code)
		require.Equal(t, existingSummaries[0].Content, actual.SummaryOfChanges[0].Content)
	})

	t.Run("should return 200 and summary of changes correctly when with limit = 1", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		deps.summaryOfChangeRepo.
			EXPECT().FindWithQueryAndSortSummaryOfChange(gomock.Any(), gomock.Any(), 0, 1, gomock.Any()).
			Return(existingSummaries, nil)

		gctx, recorder := makeReq("limit=1")

		api.GetSummaryOfChanges(gctx)

		var actual summaryofchange.ClientSummaryOfChangeRes

		testutil.DecodeJSON(t, recorder.Body, &actual)

		require.Equal(t, http.StatusOK, recorder.Code)
		require.Equal(t, existingSummaries[0].Content, actual.SummaryOfChanges[0].Content)
	})

	t.Run("should return 200 and summary of changes correctly when with limit < 0", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		deps.summaryOfChangeRepo.
			EXPECT().FindWithQueryAndSortSummaryOfChange(gomock.Any(), gomock.Any(), 0, 0, gomock.Any()).
			Return(existingSummaries, nil)

		gctx, recorder := makeReq("limit=-99")

		api.GetSummaryOfChanges(gctx)

		var actual summaryofchange.ClientSummaryOfChangeRes

		testutil.DecodeJSON(t, recorder.Body, &actual)

		require.Equal(t, http.StatusOK, recorder.Code)
		require.Equal(t, existingSummaries[0].Content, actual.SummaryOfChanges[0].Content)
	})

	t.Run("should return error when retrieving summary of changes error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestSummaryOfChangeAPI(t)
		defer finish()

		errMsg := "get summary of changes error"

		deps.summaryOfChangeRepo.
			EXPECT().FindWithQueryAndSortSummaryOfChange(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, errors.New(errMsg))

		gctx, recorder := makeReq("")

		api.GetSummaryOfChanges(gctx)

		var actual absintheapi.Error

		testutil.DecodeJSON(t, recorder.Body, &actual)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
		require.Equal(t, errMsg, actual.Message)
	})
}

type summaryOfChangeAPIDeps struct {
	summaryOfChangeRepo *mock_repository.MockSummaryOfChangeRepository
	auditRepo           *mock_repository.MockAuditLogRepository
}

func newTestSummaryOfChangeAPI(t gomock.TestReporter) (*summaryofchange.SummaryOfChangeAPI, *summaryOfChangeAPIDeps, func()) {
	ctrl := gomock.NewController(t)

	deps := &summaryOfChangeAPIDeps{
		summaryOfChangeRepo: mock_repository.NewMockSummaryOfChangeRepository(ctrl),
		auditRepo:           mock_repository.NewMockAuditLogRepository(ctrl),
	}
	return summaryofchange.ProvideSummaryOfChangeAPI(
		deps.summaryOfChangeRepo,
		deps.auditRepo,
	), deps, func() { ctrl.Finish() }
}
