package bulkincentivecsv

import (
	"context"
	"sync"

	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func (of *BulkIncentiveCSVAPI) bulkInsertToDB(ctx context.Context, operations []insertOperationRequest) bulkImportCSVCreateRes {
	wg := &sync.WaitGroup{}
	wk, release := safe.NewWorker(of.config.BulkUploadIncentiveCSVWorkerPool)
	defer release()
	wg.Add(len(operations))
	res := bulkImportCSVCreateRes{
		m:         &sync.Mutex{},
		Successes: []success{},
		Failures:  []fail{},
	}
	for idx, operation := range operations {
		rowIndex := idx + 1
		wk.GoFuncWithPool(workerInsertOperationFunc(ctx, wg, of.incentiveRepo, of.auditLogRepo, rowIndex, operation, &res))
	}

	wg.Wait()
	return res
}

func (of *BulkIncentiveCSVAPI) bulkUpdateToDB(ctx context.Context, operations []updateOperationRequest) bulkImportCSVUpdateRes {
	wg := &sync.WaitGroup{}
	wk, release := safe.NewWorker(of.config.BulkUploadIncentiveCSVWorkerPool)
	defer release()
	wg.Add(len(operations))
	res := bulkImportCSVUpdateRes{
		m:         &sync.Mutex{},
		Successes: []success{},
		Failures:  []fail{},
	}
	for idx, operation := range operations {
		rowIndex := idx + 1
		wk.GoFuncWithPool(workerUpdateOperationFunc(ctx, wg, of.incentiveRepo, of.auditLogRepo, rowIndex, operation, &res))
	}

	wg.Wait()
	return res
}

func (of *BulkIncentiveCSVAPI) bulkDeleteToDB(ctx context.Context, operations []deleteOperationRequest) bulkImportCSVDeleteRes {
	wg := &sync.WaitGroup{}
	wk, release := safe.NewWorker(of.config.BulkUploadIncentiveCSVWorkerPool)
	defer release()
	wg.Add(len(operations))
	res := bulkImportCSVDeleteRes{
		m:         &sync.Mutex{},
		Successes: []success{},
		Failures:  []fail{},
	}
	for idx, operation := range operations {
		rowIndex := idx + 1
		wk.GoFuncWithPool(workerDeleteOperationFunc(ctx, wg, of.incentiveRepo, of.auditLogRepo, rowIndex, operation, &res))
	}

	wg.Wait()
	return res
}

func newInsertAuditLog(id, email string, m incentive.Incentive) model.AuditLog {
	return model.AuditLog{
		Object: model.AuditObject{
			ObjectType: model.IncentiveObject,
			ID:         id,
		},
		Actor: model.AuditLogActor{
			ID: email,
		},
		Timestamp: timeutil.BangkokNow(),
		Action:    model.CreateAction,
		Before:    nil,
		After:     m,
		Event:     model.AuditEventBulkUploadCSVCreateIncentive,
	}
}

func newUpdateAuditLog(id, email string, before, after incentive.Incentive) model.AuditLog {
	return model.AuditLog{
		Object: model.AuditObject{
			ObjectType: model.IncentiveObject,
			ID:         id,
		},
		Actor: model.AuditLogActor{
			ID: email,
		},
		Timestamp: timeutil.BangkokNow(),
		Action:    model.UpdateAction,
		Before:    before,
		After:     after,
		Event:     model.AuditEventBulkUploadCSVUpdateIncentive,
	}
}

func newDeleteAuditLog(id, email string, before incentive.Incentive) model.AuditLog {
	return model.AuditLog{
		Object: model.AuditObject{
			ObjectType: model.IncentiveObject,
			ID:         id,
		},
		Actor: model.AuditLogActor{
			ID: email,
		},
		Timestamp: timeutil.BangkokNow(),
		Action:    model.DeleteAction,
		Before:    before,
		After:     nil,
		Event:     model.AuditEventBulkUploadCSVDeleteIncentive,
	}
}

func createAuditLogAsync(ctx context.Context, repo repository.AuditLogRepository, a model.AuditLog) {
	goRoutineCtx := safe.NewContextWithSameWaitGroup(ctx)
	safe.GoFuncWithCtx(goRoutineCtx, func() {
		if err := repo.Insert(context.Background(), &a); err != nil {
			logrus.Errorf("cannot create audit log: %v\n", err)
		}
	})
}

func workerInsertOperationFunc(ctx context.Context, wg *sync.WaitGroup, incentiveRepo incentive.IncentiveRepository, auditLogRepo repository.AuditLogRepository, rowIndex int, operation insertOperationRequest, res *bulkImportCSVCreateRes) func() {
	return func() {
		defer wg.Done()

		if _, err := incentiveRepo.Create(ctx, &operation.model); err != nil {
			res.AddFailure(rowIndex, operation.Id, err.Error())
			return
		}

		createAuditLogAsync(ctx, auditLogRepo, operation.auditLog)

		res.AddSuccess(rowIndex, operation.model.IncentiveID)
	}
}

func workerUpdateOperationFunc(ctx context.Context, wg *sync.WaitGroup, incentiveRepo incentive.IncentiveRepository, auditLogRepo repository.AuditLogRepository, rowIndex int, operation updateOperationRequest, res *bulkImportCSVUpdateRes) func() {
	return func() {
		defer wg.Done()

		if err := incentiveRepo.Update(ctx, operation.id, &operation.model); err != nil {
			res.AddFailure(rowIndex, operation.id, err.Error())
			return
		}

		createAuditLogAsync(ctx, auditLogRepo, operation.auditLog)

		res.AddSuccess(rowIndex, operation.id)
	}
}

func workerDeleteOperationFunc(ctx context.Context, wg *sync.WaitGroup, incentiveRepo incentive.IncentiveRepository, auditLogRepo repository.AuditLogRepository, rowIndex int, operation deleteOperationRequest, res *bulkImportCSVDeleteRes) func() {
	return func() {
		defer wg.Done()

		if err := incentiveRepo.Delete(ctx, operation.id); err != nil {
			res.AddFailure(rowIndex, operation.id, err.Error())
			return
		}

		createAuditLogAsync(ctx, auditLogRepo, operation.auditLog)

		res.AddSuccess(rowIndex, operation.id)
	}
}
