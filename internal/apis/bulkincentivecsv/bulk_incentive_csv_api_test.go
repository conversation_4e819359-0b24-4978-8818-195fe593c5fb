package bulkincentivecsv

import (
	"context"
	"embed"
	"encoding/csv"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/jszwec/csvutil"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"
	"gopkg.in/guregu/null.v4"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive/mock_incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon/mock_polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
)

//go:embed testdata/*
var testdata embed.FS

type bulkOnTopCSVAPIDep struct {
	IncentiveRepo          *mock_incentive.MockIncentiveRepository
	AuditLogRepo           *mock_repository.MockAuditLogRepository
	IncentiveSourceService *mock_service.MockIncentiveSourceService
	ZoneService            *mock_service.MockZoneService
	PolygonService         *mock_polygon.MockPolygon
	RegionRepository       *mock_repository.MockRegionRepository
}

func newTestBulkCSVConfig() Config {
	cfg := ProvideBulkIncentiveCSVConfig()
	cfg.BulkUploadIncentiveCSVGzipEnabled = false // disable gzip on test
	return cfg
}

func newTestBulkIncentiveCSVAPI(ctrl *gomock.Controller, cfg Config) (*BulkIncentiveCSVAPI, *bulkOnTopCSVAPIDep) {
	deps := &bulkOnTopCSVAPIDep{
		IncentiveRepo:          mock_incentive.NewMockIncentiveRepository(ctrl),
		AuditLogRepo:           mock_repository.NewMockAuditLogRepository(ctrl),
		IncentiveSourceService: mock_service.NewMockIncentiveSourceService(ctrl),
		ZoneService:            mock_service.NewMockZoneService(ctrl),
		PolygonService:         mock_polygon.NewMockPolygon(ctrl),
		RegionRepository:       mock_repository.NewMockRegionRepository(ctrl),
	}
	api := ProvideBulkIncentiveCSVAPI(cfg, deps.IncentiveRepo, deps.AuditLogRepo, deps.IncentiveSourceService, deps.ZoneService, deps.PolygonService, deps.RegionRepository)
	return api, deps
}

func makeBulkUploadCSVReq(content string) (*gin.Context, *httptest.ResponseRecorder) {
	recorder := testutil.NewContextWithRecorder().
		Body().
		MultipartForm().
		File("file", "test.csv", content).
		Build()

	return recorder.GinCtx(), recorder.ResponseRecorder
}

func makeBulkDeleteCSVReq(content string) (*gin.Context, *httptest.ResponseRecorder) {
	recorder := testutil.NewContextWithRecorder().
		Body().
		MultipartForm().
		File("file", "test.csv", content).
		Build()

	return recorder.GinCtx(), recorder.ResponseRecorder
}

func makeBulkExportCSVReq() (*gin.Context, *httptest.ResponseRecorder) {
	recorder := testutil.NewContextWithRecorder()
	return recorder.GinCtx(), recorder.ResponseRecorder
}

type exportedData struct {
	Map map[string]string `csv:"-"`
}

func readExportResults(reader io.Reader) ([]exportedData, error) {
	csvReader := csv.NewReader(reader)

	decoder, _ := csvutil.NewDecoder(csvReader)
	header := decoder.Header()
	var exportedDatas []exportedData
	for {
		e := exportedData{Map: make(map[string]string)}

		if err := decoder.Decode(&e); err == io.EOF {
			break
		} else if err != nil {
			log.Fatal(err)
		}

		for _, i := range decoder.Unused() {
			e.Map[header[i]] = decoder.Record()[i]
		}
		exportedDatas = append(exportedDatas, e)
	}

	return exportedDatas, nil
}

func createTestExportData(incentiveID, name string) *incentive.Incentive {
	return &incentive.Incentive{
		ID:           [12]byte{},
		IncentiveID:  incentiveID,
		Name:         name,
		DisplayName:  "DisplayName",
		LocationName: "LocationName",
		Description:  "Description",
		Region:       "BKK",
		PaymentType:  incentive.Daily,
		Geometry: incentive.Geometry{
			Type: "MultiPolygon",
			Coordinates: incentive.Coordinates{
				[][]geom.Coord{
					{
						geom.Coord{1, 2},
					},
				},
			},
		},
		ServiceTypes: []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
		DateRange: incentive.DateRange{
			Start: time.Date(2023, 5, 9, 12, 0, 0, 0, time.UTC),
			End:   time.Date(2023, 5, 9, 16, 0, 0, 0, time.UTC),
		},
		Times: []incentive.Times{
			{
				Start: "12:00:00",
				End:   "13:00:00",
			},
		},
		OrderTier: []incentive.OrderTier{
			{
				MinOrderAmount:  1,
				MaxOrderAmount:  2,
				IncentiveAmount: 3,
			},
			{
				MinOrderAmount:  4,
				MaxOrderAmount:  5,
				IncentiveAmount: 6,
			},
		},
		AR:     null.FloatFrom(30).Ptr(),
		CR:     null.FloatFrom(30).Ptr(),
		Rating: null.FloatFrom(5).Ptr(),
		Tiers: []string{
			string(model.DriverTierMember),
			string(model.DriverTierBasic),
			string(model.DriverTierStar),
			string(model.DriverTierPro),
			string(model.DriverTierProPlus),
		},
		Box:            null.BoolFrom(true).Ptr(),
		Jacket:         null.BoolFrom(true).Ptr(),
		Active:         true,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
		CreatedBy:      "admin",
		Sources:        []string{"AA", "BB", "CC"},
		UpdatedBy:      "admin",
		WhitelistIDs:   []string{"D1", "D2", "D3"},
		OrderShiftType: incentive.AllOrderType,
		ZoneCode:       "ABC",
	}
}

func assertEmptyFile(t *testing.T, recorder *httptest.ResponseRecorder) {
	var apiErr api.Error
	testutil.DecodeJSON(t, recorder.Body, &apiErr)
	require.Equal(t, "EMPTY_FILE_OR_HEADER", apiErr.Code)
	require.Equal(t, "file empty", apiErr.Message)
}

func assertInvalidCSVFormat(t *testing.T, recorder *httptest.ResponseRecorder) {
	var resp bulkResultResponse
	testutil.DecodeJSON(t, recorder.Body, &resp)
	require.Equal(t, "FAILED", resp.Status)
	require.Equal(t, 0, resp.TotalSuccess)
	require.Equal(t, 2, resp.TotalFailed)
}

func assertOnlyHeader(t *testing.T, recorder *httptest.ResponseRecorder) {
	var apiErr api.Error
	testutil.DecodeJSON(t, recorder.Body, &apiErr)
	require.Equal(t, "NO_DATA_ROWS", apiErr.Code)
	require.Equal(t, "please add data rows", apiErr.Message)
}

func assertExportData(t *testing.T, row exportedData) {
	assert.Equal(t, "DisplayName", row.Map["display_name"])
	assert.Equal(t, "LocationName", row.Map["location_name"])
	assert.Equal(t, "Description", row.Map["description"])
	assert.Equal(t, "BKK", row.Map["region"])
	assert.Equal(t, "DAILY", row.Map["payment_type"])
	assert.Equal(t, "[[[[1,2]]]]", row.Map["effective_area.coordinates"])
	assert.Equal(t, "FOOD,MART,MESSENGER", row.Map["service_types"])
	assert.Equal(t, "2023-05-09|2023-05-09", row.Map["date_range"])
	assert.Equal(t, "12:00:00|13:00:00", row.Map["time_ranges"])
	assert.Equal(t, "1|2|3.00,4|5|6.00", row.Map["incentive_order_tiers"])
	assert.Equal(t, "30.00", row.Map["acceptance_rate"])
	assert.Equal(t, "30.00", row.Map["cancellation_rate"])
	assert.Equal(t, "5.00", row.Map["rating"])
	assert.Equal(t, "MEMBER,BASIC,STAR,PRO,PRO+", row.Map["tiers"])
	assert.Equal(t, "TRUE", row.Map["have_box"])
	assert.Equal(t, "TRUE", row.Map["have_jacket"])
	assert.Equal(t, "TRUE", row.Map["active"])
	assert.Equal(t, "AA,BB,CC", row.Map["incentive_sources"])
	assert.Equal(t, "D1,D2,D3", row.Map["whitelist_ids"])
	assert.Equal(t, "ALL", row.Map["order_type"])
	assert.Equal(t, "ABC", row.Map["effective_area.zone"])
}

func TestBulkImportCSVCreate(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, deps := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"AA", "BB", "CC", "DD"}, nil).AnyTimes()
		deps.ZoneService.EXPECT().FindBriefZones(gomock.Any(), gomock.Any()).Return(service.FindBriefZoneResponse{
			Zones: []model.BriefZone{
				model.NewBriefZone("best", "display name", "AYUTTHAYA"),
			},
			TotalCount: 1,
		}, nil)
		deps.IncentiveRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(2).Return("incentive id", nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(2).Return(nil)
		deps.RegionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Times(2).Return(true)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("success with partial failures due to db error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, deps := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"AA", "BB", "CC", "DD"}, nil).AnyTimes()
		deps.ZoneService.EXPECT().FindBriefZones(gomock.Any(), gomock.Any()).Return(service.FindBriefZoneResponse{
			Zones: []model.BriefZone{
				model.NewBriefZone("best", "display name", "AYUTTHAYA"),
			},
			TotalCount: 1,
		}, nil)
		deps.IncentiveRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, model *incentive.Incentive) (string, error) {
			if model.Name == "Test Scheme 1" {
				return "incentive id", nil
			}
			return "", fmt.Errorf("error db")
		})
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(1).Return(nil)
		deps.RegionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Times(2).Return(true)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)

	})

	t.Run("success with partial failures due to validation error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 2, resp.TotalFailed)
	})

	t.Run("empty file", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/empty.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		bulkCSV.BulkImportCSVCreate(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertEmptyFile(t, recorder)
	})

	t.Run("only header but no data row", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())
		b, err := testdata.ReadFile("testdata/only_header.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		bulkCSV.BulkImportCSVCreate(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertOnlyHeader(t, recorder)
	})

	t.Run("invalid csv file format", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/invalid_csv_format.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		bulkCSV.BulkImportCSVCreate(gctx)

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		assertInvalidCSVFormat(t, recorder)
	})

}

func TestBulkImportCSVUpdate(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, deps := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"AA", "BB", "CC", "DD"}, nil).AnyTimes()
		deps.ZoneService.EXPECT().FindBriefZones(gomock.Any(), gomock.Any()).Return(service.FindBriefZoneResponse{
			Zones: []model.BriefZone{
				model.NewBriefZone("best", "display name", "AYUTTHAYA"),
			},
			TotalCount: 1,
		}, nil)
		deps.IncentiveRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]incentive.Incentive{
			{IncentiveID: "1", Name: "Incentive A"},
			{IncentiveID: "2", Name: "Incentive B"},
		}, nil)
		deps.IncentiveRepo.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Times(2).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(2).Return(nil)
		deps.RegionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Times(2).Return(true)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("success with partial failures due to db error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, deps := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"AA", "BB", "CC", "DD"}, nil).AnyTimes()
		deps.ZoneService.EXPECT().FindBriefZones(gomock.Any(), gomock.Any()).Return(service.FindBriefZoneResponse{
			Zones: []model.BriefZone{
				model.NewBriefZone("best", "display name", "AYUTTHAYA"),
			},
			TotalCount: 1,
		}, nil)
		deps.IncentiveRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]incentive.Incentive{
			{IncentiveID: "1", Name: "Incentive A"},
			{IncentiveID: "2", Name: "Incentive B"},
		}, nil)
		deps.IncentiveRepo.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Times(2).DoAndReturn(func(ctx context.Context, id string, model *incentive.Incentive) error {
			if model.Name == "Test Scheme 1" {
				return nil
			}
			return fmt.Errorf("error db")
		})
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(1).Return(nil)
		deps.RegionRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).Times(2).Return(true)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)
	})

	t.Run("success with partial failures due to validation error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 2, resp.TotalFailed)
	})

	t.Run("empty file", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/empty.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		bulkCSV.BulkImportCSVUpdate(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertEmptyFile(t, recorder)
	})

	t.Run("only header but no data row", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/only_header.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		bulkCSV.BulkImportCSVUpdate(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertOnlyHeader(t, recorder)
	})

	t.Run("invalid csv file format", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/invalid_csv_format.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b))
		bulkCSV.BulkImportCSVUpdate(gctx)

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		assertInvalidCSVFormat(t, recorder)
	})

}

func TestBulkImportCSVDelete(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, deps := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/bulk_delete.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]incentive.Incentive{
			{IncentiveID: "1", Name: "Incentive A"},
			{IncentiveID: "2", Name: "Incentive B"},
			{IncentiveID: "3", Name: "Incentive C"},
			{IncentiveID: "4", Name: "Incentive D"},
		}, nil)
		deps.IncentiveRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).Times(4).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(4).Return(nil)

		// When
		gctx, recorder := makeBulkDeleteCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkImportCSVDelete(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 4, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("success with partial failures due to db error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, deps := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/bulk_delete.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		deps.IncentiveRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]incentive.Incentive{
			{IncentiveID: "1", Name: "Incentive A"},
			{IncentiveID: "2", Name: "Incentive B"},
			{IncentiveID: "3", Name: "Incentive C"},
			{IncentiveID: "4", Name: "Incentive D"},
		}, nil)

		deps.IncentiveRepo.EXPECT().Delete(gomock.Any(), gomock.Any()).Times(4).DoAndReturn(func(ctx context.Context, id string) error {
			if stringutil.IsStringInList([]string{"1", "2"}, id) {
				return nil
			}
			return fmt.Errorf("error db")
		}).Times(4)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(2).Return(nil)

		// When
		gctx, recorder := makeBulkDeleteCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkImportCSVDelete(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 2, resp.TotalFailed)
	})

	t.Run("empty file", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/empty.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkDeleteCSVReq(string(b))

		// When
		bulkCSV.BulkImportCSVDelete(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertEmptyFile(t, recorder)
	})

	t.Run("only header but no data row", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/only_header.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkDeleteCSVReq(string(b))

		// When
		bulkCSV.BulkImportCSVDelete(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertOnlyHeader(t, recorder)
	})

	t.Run("invalid csv file format", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, _ := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/invalid_csv_format.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkDeleteCSVReq(string(b))

		// When
		bulkCSV.BulkImportCSVDelete(gctx)

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		assertInvalidCSVFormat(t, recorder)
	})

}

func TestBulkExportCSV(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkCSV, deps := newTestBulkIncentiveCSVAPI(ctrl, newTestBulkCSVConfig())

		deps.IncentiveRepo.EXPECT().List(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]*incentive.Incentive{
			createTestExportData("1", "A"),
			createTestExportData("2", "B"),
			createTestExportData("3", "C"),
			createTestExportData("4", "D"),
		}, nil)

		// When
		gctx, recorder := makeBulkExportCSVReq()
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkCSV.BulkExportCSV(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		exportedDatas, err := readExportResults(recorder.Body)
		require.NoError(t, err)
		require.Len(t, exportedDatas, 4)

		row := exportedDatas[0]
		assert.Equal(t, "1", row.Map["id"])
		assert.Equal(t, "A", row.Map["name"])
		assertExportData(t, row)

		row = exportedDatas[1]
		assert.Equal(t, "2", row.Map["id"])
		assert.Equal(t, "B", row.Map["name"])
		assertExportData(t, row)

		row = exportedDatas[2]
		assert.Equal(t, "3", row.Map["id"])
		assert.Equal(t, "C", row.Map["name"])
		assertExportData(t, row)

		row = exportedDatas[3]
		assert.Equal(t, "4", row.Map["id"])
		assert.Equal(t, "D", row.Map["name"])
		assertExportData(t, row)
	})

}
