package region

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestRegionAdminAPI_List(t *testing.T) {
	req := func() (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext(http.MethodGet, "/v1/regions", nil)
	}

	t.Run("Should return list of region with http code 200", func(tt *testing.T) {
		gctx, record := req()
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, dep := newRegionAdminAPI(ctrl)

		expects := []model.Region{
			*model.NewRegion("AYUTTHAYA", "Ayutthaya"),
			*model.NewRegion("CHIANG_MAI", "Chiang Mai"),
		}

		dep.regionRepo.EXPECT().
			List(ctx).
			Return(expects, nil)

		dep.regionRepo.EXPECT().
			Count(ctx).
			Return(len(expects), nil)

		api.AvailableRegions(gctx)

		require.Equal(tt, http.StatusOK, record.Code)

		var actual struct {
			CountTotal int
			Data       []RegionRes
		}

		testutil.DecodeJSON(tt, record.Body, &actual)
		expectedRes := make([]RegionRes, len(expects))
		for i, res := range expects {
			expectedRes[i] = NewRegionRes(res)
		}
		require.Equal(tt, expectedRes, actual.Data)
	})
}

type regionAPIDep struct {
	regionRepo *mock_repository.MockRegionRepository
}

func newRegionAdminAPI(ctrl *gomock.Controller) (*RegionAdminAPI, *regionAPIDep) {
	deps := &regionAPIDep{
		regionRepo: mock_repository.NewMockRegionRepository(ctrl),
	}
	return ProvideRegionAdminAPI(deps.regionRepo), deps
}
