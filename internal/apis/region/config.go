package region

import (
	"git.wndv.co/lineman/fleet-distribution/internal/config"
)

type RegionConfig struct {
	HideSpecialProvincesFromNonWhitelist bool `envconfig:"HIDE_SPECIAL_PROVINCES_FROM_NON_WHITELIST" default:"true"`
}

func ProvideAtomicRegionConfig(configUpdater *config.DBConfigUpdater) *AtomicRegionConfig {
	var cfg AtomicRegionConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type AtomicRegionConfig = config.AtomicWrapper[RegionConfig]
