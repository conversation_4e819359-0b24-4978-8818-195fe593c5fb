package zone

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type CreateZoneRes struct {
	ID string `json:"id"`
}

type GetZoneRes struct {
	Zone
}

type Zone struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	DisplayName string      `json:"displayName"`
	Region      string      `json:"region"`
	ZoneCode    string      `json:"zoneCode"`
	Coordinates Coordinates `json:"coordinates"`
	Active      bool        `json:"active"`
	CreatedAt   time.Time   `json:"createdAt"`
	UpdatedAt   time.Time   `json:"updatedAt"`
	DeletedAt   *time.Time  `json:"deletedAt,omitempty"`
}

func fromZoneModel(m model.Zone) Zone {
	return Zone{
		ID:          m.ID.Hex(),
		Name:        m.Name,
		DisplayName: m.DisplayName,
		Region:      m.Region,
		ZoneCode:    m.ZoneCode,
		Coordinates: Coordinates(m.Geometry.Coordinates),
		Active:      m.Active,
		CreatedAt:   m.<PERSON>t,
		UpdatedAt:   m.UpdatedAt,
		DeletedAt:   m.DeletedAt,
	}
}

func fromZoneModels(m []model.Zone) []Zone {
	zz := []Zone{}
	for _, v := range m {
		z := fromZoneModel(v)
		zz = append(zz, z)
	}
	return zz
}

func zoneCodesFromBriefZoneModels(m []model.BriefZone) []Zone {
	var zz []Zone
	for _, v := range m {
		z := Zone{
			ID:       v.ID.Hex(),
			ZoneCode: v.ZoneCode,
		}
		zz = append(zz, z)
	}
	return zz
}
