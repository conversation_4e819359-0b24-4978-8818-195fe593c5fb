package srvarea

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack/mock_slack"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

type createAreaReq struct {
	Region       string                       `json:"region"`
	Benefit      string                       `json:"benefit"`
	Distribution interface{}                  `json:"distribution,omitempty"`
	TopUpConfig  model.ServiceAreaTopUpConfig `json:"topUpConfig"`
}

type createAutoAssignReq struct {
	Logic        model.DistributionLogic `json:"logic"`
	SkipQuota    int                     `json:"skipQuota,omitempty"`
	BanPeriod    int                     `json:"banPeriod,omitempty"`
	QuotaRefresh int                     `json:"quotaRefresh,omitempty"`
}

type updateAreaReq struct {
	Distribution                interface{}                  `json:"distribution,omitempty"`
	TopUpConfig                 model.ServiceAreaTopUpConfig `json:"topUpConfig"`
	Benefit                     string                       `json:"benefit"`
	ShiftDriverIDs              string                       `json:"shiftDriverIds"`
	BlackListBookShiftDriverIDs string                       `json:"blackListBookShiftDriverIDs"`
}

type updateAutoAssignReq struct {
	Logic                    model.DistributionLogic      `json:"logic"`
	SkipQuota                int                          `json:"skipQuota,omitempty"`
	BanPeriod                int                          `json:"banPeriod,omitempty"`
	QuotaRefresh             int                          `json:"quotaRefresh,omitempty"`
	NotifyViaSocketIOEnabled bool                         `json:"notifyViaSocketIOEnabled"`
	TopUpConfig              model.ServiceAreaTopUpConfig `json:"topUpConfig" binding:"required"`

	PriorityDedicatedRidersEnabled                bool            `json:"priorityDedicatedRidersEnabled"`
	PriorityDedicatedRidersDistance               float64         `json:"priorityDedicatedRidersDistance"`
	PriorityDedicatedRidersAllowOrderServiceTypes []model.Service `json:"priorityDedicatedRidersAllowOrderServiceTypes"`

	SmartDistributionGoodnessBiasLevel prediction.BiasLevelType `json:"smartDistributionGoodnessBiasLevel"`
}

func TestServiceAreaSettingAPI_Create(t *testing.T) {
	makeReq := func(req createAreaReq) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/admin/service-area-settings", testutil.JSON(&req))
	}

	t.Run("success create should return 201 with response", func(tt *testing.T) {
		req := createAreaReq{
			Region: "AYUTTHAYA",
			Distribution: &createAutoAssignReq{
				Logic: model.DistributionLogicAutoAssign,
			},
			TopUpConfig: model.NewServiceAreaTopUpConfig(),
		}
		gctx, recorder := makeReq(req)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newServiceAreaSettingAPI(ctrl)

		deps.regionRepo.EXPECT().
			Get(ctx, "AYUTTHAYA").
			Return(*model.NewRegion("AYUTTHAYA", "AYUTTHAYA"), nil)

		deps.srvareaRepo.EXPECT().
			Create(ctx, gomock.Any()).
			Return(nil)

		api.Create(gctx)

		require.Equal(tt, http.StatusCreated, recorder.Code)

		var res ServiceAreaRes
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.NotEmpty(tt, res.ID)
		require.Equal(tt, req.Region, res.Region)
	})

	t.Run("non-exists region should return 400", func(tt *testing.T) {
		req := createAreaReq{
			Region: "NON_EXISTS",
			Distribution: &createAutoAssignReq{
				Logic: model.DistributionLogicAutoAssign,
			},
			TopUpConfig: model.NewServiceAreaTopUpConfig(),
		}
		gctx, recorder := makeReq(req)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		areaAPI, deps := newServiceAreaSettingAPI(ctrl)

		deps.regionRepo.EXPECT().
			Get(ctx, "NON_EXISTS").
			Return(model.Region{}, repository.ErrNotFound)

		areaAPI.Create(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)

		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, api.ERRCODE_INVALID_REQUEST, err.Code)
		payload := err.Info["errors"].([]interface{})[0].(map[string]interface{})
		require.Equal(tt, "NON_EXISTS", payload["field"])
	})

	t.Run("duplicate region should return 400", func(tt *testing.T) {
		req := createAreaReq{
			Region: "AYUTTHAYA",
			Distribution: &createAutoAssignReq{
				Logic: model.DistributionLogicAutoAssign,
			},
			TopUpConfig: model.NewServiceAreaTopUpConfig(),
		}
		gctx, recorder := makeReq(req)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newServiceAreaSettingAPI(ctrl)

		deps.regionRepo.EXPECT().
			Get(ctx, "AYUTTHAYA").
			Return(*model.NewRegion("AYUTTHAYA", "AYUTTHAYA"), nil)

		deps.srvareaRepo.EXPECT().
			Create(ctx, gomock.Any()).
			Return(repository.ErrServiceAreaRegionDuplicate)

		api.Create(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

}

func TestServiceAreaSettingAPI_Get(t *testing.T) {
	makeReq := func(id string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/service-area-settings/%s", id), nil)
		gctx.Params = gin.Params{
			gin.Param{Key: KeyServiceAreaSettingID, Value: id},
		}

		return gctx, recorder
	}

	t.Run("success should return 200", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		expect := model.NewServiceArea(model.GenerateServiceAreaID(), "AYUTTHAYA")
		expect.SetHeatMapType(model.DemandHm)
		expect.SetTopUpConfig(model.NewServiceAreaTopUpConfig())
		expect.SetHeatMapByTimes([]model.HeatMapByTime{
			{
				StartTime:   "00:00",
				EndTime:     "14:00",
				HeatmapType: model.DemandHm,
			},
		})

		gctx, recorder := makeReq(expect.ID.String())
		ctx := gctx.Request.Context()

		ssapi, dep := newServiceAreaSettingAPI(ctrl)

		dep.srvAreaService.EXPECT().
			GetByID(ctx, expect.ID.String()).
			Return(expect, nil)

		ssapi.Get(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var res ServiceAreaRes
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Equal(tt, NewServiceAreaRes(*expect), &res)
		require.Equal(tt, expect.HeatmapType, res.HeatmapType)
		require.Equal(tt, expect.TopUpConfig, res.TopUpConfig)
		// Fallback check goodness bias level
		require.Equal(tt, expect.Distribution.SmartDistributionGoodnessBiasLevel, prediction.BiasUnspecified)
	})

	t.Run("success smart distribution goodness bias level", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		expect := model.NewServiceArea(model.GenerateServiceAreaID(), "AYUTTHAYA")
		expect.SetDistribution(&model.AutoAssignDistribution{
			SmartDistributionGoodnessBiasLevel: prediction.BiasL0,
		})

		gctx, recorder := makeReq(expect.ID.String())
		ctx := gctx.Request.Context()

		ssapi, dep := newServiceAreaSettingAPI(ctrl)

		dep.srvAreaService.EXPECT().
			GetByID(ctx, expect.ID.String()).
			Return(expect, nil)

		ssapi.Get(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res ServiceAreaRes
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Equal(tt, expect.Distribution.SmartDistributionGoodnessBiasLevel, prediction.BiasL0)
	})

	t.Run("not found service-area-setting should return 404", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		id := "not-found"

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		ssapi, dep := newServiceAreaSettingAPI(ctrl)

		dep.srvAreaService.EXPECT().
			GetByID(ctx, id).
			Return(nil, service.ErrServiceAreaNotFound)

		ssapi.Get(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("unknown error should return 500", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		id := "not-found"

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		ssapi, dep := newServiceAreaSettingAPI(ctrl)

		dep.srvAreaService.EXPECT().
			GetByID(ctx, id).
			Return(nil, errors.New("unknown"))

		ssapi.Get(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestServiceAreaSettingAPI_List(t *testing.T) {
	makeReq := func() (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("GET", "/v1/admin/service-areas?size=10&page=1", nil)
	}

	t.Run("success should return 200", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		a1 := *model.NewServiceArea(model.GenerateServiceAreaID(), "AYUTTHAYA")
		a1.SetHeatMapType(model.DemandHm)
		a1.SetTopUpConfig(model.NewServiceAreaTopUpConfig())
		a2 := *model.NewServiceArea(model.GenerateServiceAreaID(), "CHIANG_MAI")
		a2.SetHeatMapType(model.MatchingRateHm)
		a2.SetTopUpConfig(model.NewServiceAreaTopUpConfig())
		expect := []model.ServiceArea{
			a1,
			a2,
		}

		gctx, recorder := makeReq()

		ssapi, dep := newServiceAreaSettingAPI(ctrl)

		dep.srvareaRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(expect, nil)
		dep.srvareaRepo.EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).Return(10, nil)

		ssapi.List(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual = struct {
			Count int              `json:"countTotal"`
			Data  []ServiceAreaRes `json:"data"`
		}{}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, []ServiceAreaRes(NewListServiceAreaSettingRes(expect)), actual.Data)
	})

	t.Run("unknown error should return 500", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		gctx, recorder := makeReq()

		ssapi, dep := newServiceAreaSettingAPI(ctrl)

		dep.srvareaRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.ServiceArea{}, errors.New("unknown"))

		ssapi.List(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

}

func TestServiceAreaSettingAPI_Update(t *testing.T) {
	makeReq := func(id string, req updateAreaReq) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/service-area-settings/%s", id), testutil.JSON(&req))
		gctx.Params = gin.Params{
			gin.Param{Key: KeyServiceAreaSettingID, Value: id},
		}

		return gctx, recorder
	}

	t.Run("update success should return 200", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		expect := model.NewServiceArea(model.GenerateServiceAreaID(), "region")
		expect.SetHeatMapType(model.MatchingRateHm)

		expectTopUpConfig := model.NewServiceAreaTopUpConfig()
		expectTopUpConfig.EnabledCiti = true
		expectTopUpConfig.EnabledUob = true
		expectTopUpConfig.GenerateUobRefID = true
		expect.SetTopUpConfig(expectTopUpConfig)

		gctx, recorder := makeReq(
			expect.ID.String(),
			updateAreaReq{
				Distribution: &updateAutoAssignReq{Logic: model.DistributionLogicAutoAssign},
				TopUpConfig: model.ServiceAreaTopUpConfig{
					EnabledCiti:      true,
					EnabledUob:       true,
					GenerateUobRefID: true,
				},
			},
		)
		ctx := gctx.Request.Context()
		areaAPI, deps := newServiceAreaSettingAPI(ctrl)

		deps.srvareaRepo.EXPECT().
			Get(ctx, expect.ID.String()).
			Return(expect, nil)

		deps.srvareaRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.auditRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		areaAPI.Update(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actualRes ServiceAreaRes
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		require.Equal(tt, expect.DistributionLogic, actualRes.Distribution.GetLogic())
		require.Equal(tt, expect.HeatmapType, actualRes.HeatmapType)
		require.Equal(tt, expect.TopUpConfig.EnabledCiti, actualRes.TopUpConfig.EnabledCiti)
		require.Equal(tt, expect.TopUpConfig.EnabledUob, actualRes.TopUpConfig.EnabledUob)
		require.Equal(tt, expect.TopUpConfig.GenerateUobRefID, actualRes.TopUpConfig.GenerateUobRefID)
	})

	t.Run("not found service area should return 404", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		id := "not-found"

		gctx, recorder := makeReq(id, updateAreaReq{Distribution: updateAutoAssignReq{
			Logic: model.DistributionLogicAutoAssign,
		}})
		ctx := gctx.Request.Context()
		areaAPI, deps := newServiceAreaSettingAPI(ctrl)

		deps.srvareaRepo.EXPECT().
			Get(ctx, id).
			Return(nil, repository.ErrNotFound)

		areaAPI.Update(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("return error when update shift whitelist remove shift error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		expect := model.NewServiceArea(model.GenerateServiceAreaID(), "region")
		expect.SetHeatMapType(model.MatchingRateHm)

		expectTopUpConfig := model.NewServiceAreaTopUpConfig()
		expectTopUpConfig.EnabledCiti = true
		expectTopUpConfig.EnabledUob = true
		expectTopUpConfig.GenerateUobRefID = true
		expect.SetTopUpConfig(expectTopUpConfig)

		gctx, recorder := makeReq(
			expect.ID.String(),
			updateAreaReq{
				Distribution: &updateAutoAssignReq{
					Logic: model.DistributionLogicAutoAssign,
				},
				TopUpConfig: model.ServiceAreaTopUpConfig{
					EnabledCiti:      true,
					EnabledUob:       true,
					GenerateUobRefID: true,
				},
				ShiftDriverIDs: "d1",
			},
		)
		ctx := gctx.Request.Context()
		areaAPI, deps := newServiceAreaSettingAPI(ctrl)

		expect.SetShiftDriverIDs([]string{"d1", "d2"})

		deps.srvareaRepo.EXPECT().
			Get(ctx, expect.ID.String()).
			Return(expect, nil)

		deps.shiftService.EXPECT().
			RemoveShiftByTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]string{}, repository.ErrNotFound)

		areaAPI.Update(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should not remove shift if add more driver", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		expect := model.NewServiceArea(model.GenerateServiceAreaID(), "region")
		expect.SetHeatMapType(model.MatchingRateHm)

		expectTopUpConfig := model.NewServiceAreaTopUpConfig()
		expectTopUpConfig.EnabledCiti = true
		expectTopUpConfig.EnabledUob = true
		expectTopUpConfig.GenerateUobRefID = true
		expect.SetTopUpConfig(expectTopUpConfig)

		gctx, recorder := makeReq(
			expect.ID.String(),
			updateAreaReq{
				Distribution: &updateAutoAssignReq{
					Logic: model.DistributionLogicAutoAssign,
				},
				TopUpConfig: model.ServiceAreaTopUpConfig{
					EnabledCiti:      true,
					EnabledUob:       true,
					GenerateUobRefID: true,
				},
				ShiftDriverIDs: "d1, d2, d3",
			},
		)
		ctx := gctx.Request.Context()
		areaAPI, deps := newServiceAreaSettingAPI(ctrl)

		expect.SetShiftDriverIDs([]string{"d1", "d2"})

		deps.srvareaRepo.EXPECT().
			Get(ctx, expect.ID.String()).
			Return(expect, nil)

		deps.srvareaRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.auditRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		areaAPI.Update(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should not remove shift if reorder driver", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		expect := model.NewServiceArea(model.GenerateServiceAreaID(), "region")
		expect.SetHeatMapType(model.MatchingRateHm)

		expectTopUpConfig := model.NewServiceAreaTopUpConfig()
		expectTopUpConfig.EnabledCiti = true
		expectTopUpConfig.EnabledUob = true
		expectTopUpConfig.GenerateUobRefID = true
		expect.SetTopUpConfig(expectTopUpConfig)

		gctx, recorder := makeReq(
			expect.ID.String(),
			updateAreaReq{
				Distribution: &updateAutoAssignReq{
					Logic: model.DistributionLogicAutoAssign,
				},
				TopUpConfig: model.ServiceAreaTopUpConfig{
					EnabledCiti:      true,
					EnabledUob:       true,
					GenerateUobRefID: true,
				},
				ShiftDriverIDs: "d2, d1, d3",
			},
		)
		ctx := gctx.Request.Context()
		areaAPI, deps := newServiceAreaSettingAPI(ctrl)

		expect.SetShiftDriverIDs([]string{"d1", "d2"})

		deps.srvareaRepo.EXPECT().
			Get(ctx, expect.ID.String()).
			Return(expect, nil)

		deps.srvareaRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.auditRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		areaAPI.Update(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should not remove shift if driver in blacklist", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		expect := model.NewServiceArea(model.GenerateServiceAreaID(), "region")
		expect.SetHeatMapType(model.MatchingRateHm)

		expectTopUpConfig := model.NewServiceAreaTopUpConfig()
		expectTopUpConfig.EnabledCiti = true
		expectTopUpConfig.EnabledUob = true
		expectTopUpConfig.GenerateUobRefID = true
		expect.SetTopUpConfig(expectTopUpConfig)

		gctx, recorder := makeReq(
			expect.ID.String(),
			updateAreaReq{
				Distribution: &updateAutoAssignReq{
					Logic: model.DistributionLogicAutoAssign,
				},
				TopUpConfig: model.ServiceAreaTopUpConfig{
					EnabledCiti:      true,
					EnabledUob:       true,
					GenerateUobRefID: true,
				},
				ShiftDriverIDs:              "d2",
				BlackListBookShiftDriverIDs: "d1",
			},
		)
		ctx := gctx.Request.Context()
		areaAPI, deps := newServiceAreaSettingAPI(ctrl)

		expect.SetShiftDriverIDs([]string{"d1", "d2"})
		expect.SetBlackListBookShiftDriverIDs([]string{"d1"})

		deps.srvareaRepo.EXPECT().
			Get(ctx, expect.ID.String()).
			Return(expect, nil)

		deps.srvareaRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Return(nil)

		deps.auditRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		areaAPI.Update(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("update smart distribution bias level", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		mockServiceArea := model.NewServiceArea(model.GenerateServiceAreaID(), "region")

		gctx, recorder := makeReq(
			mockServiceArea.ID.String(),
			updateAreaReq{
				Distribution: &updateAutoAssignReq{
					Logic:                              model.DistributionLogicAutoAssign,
					SmartDistributionGoodnessBiasLevel: prediction.BiasL0,
				},
			})
		ctx := gctx.Request.Context()
		areaAPI, deps := newServiceAreaSettingAPI(ctrl)

		deps.srvareaRepo.EXPECT().
			Get(ctx, mockServiceArea.ID.String()).
			Return(mockServiceArea, nil)

		deps.srvareaRepo.EXPECT().
			Update(ctx, gomock.Any()).
			Do(func(ctx context.Context, val *model.ServiceArea) {
				require.Equal(tt, model.DistributionLogicAutoAssign, val.Distribution.Logic())
				require.Equal(tt, prediction.BiasL0, val.Distribution.SmartDistributionGoodnessBiasLevel)
			}).
			Return(nil)

		deps.auditRepo.EXPECT().Insert(gctx, gomock.Any()).Return(nil)

		areaAPI.Update(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)

		var actualRes ServiceAreaRes
		testutil.DecodeJSON(tt, recorder.Body, &actualRes)
		actualDist := actualRes.Distribution.(*AutoAssignDistributionRes)
		require.Equal(tt, mockServiceArea.Distribution.SmartDistributionGoodnessBiasLevel, actualDist.SmartDistributionGoodnessBiasLevel)
	})
}

type serviceAreaSettingAPIDeps struct {
	srvareaRepo    *mock_repository.MockServiceAreaRepository
	regionRepo     *mock_repository.MockRegionRepository
	auditRepo      *mock_repository.MockAuditLogRepository
	shiftService   *mock_service.MockShiftServices
	txnHelper      *mock_transaction.MockTxnHelper
	slack          *mock_slack.MockSlack
	zoneRepo       *mock_repository.MockZoneRepository
	srvAreaService *mock_service.MockServiceAreaService
}

func newServiceAreaSettingAPIWithCfg(ctrl *gomock.Controller, cfg config.ServiceAreaConfig, globalCfg config.GlobalConfig) (*ServiceAreaSettingAPI, *serviceAreaSettingAPIDeps) {
	deps := &serviceAreaSettingAPIDeps{
		srvareaRepo:    mock_repository.NewMockServiceAreaRepository(ctrl),
		regionRepo:     mock_repository.NewMockRegionRepository(ctrl),
		zoneRepo:       mock_repository.NewMockZoneRepository(ctrl),
		auditRepo:      mock_repository.NewMockAuditLogRepository(ctrl),
		shiftService:   mock_service.NewMockShiftServices(ctrl),
		txnHelper:      mock_transaction.NewMockTxnHelper(ctrl),
		slack:          mock_slack.NewMockSlack(ctrl),
		srvAreaService: mock_service.NewMockServiceAreaService(ctrl),
	}
	return ProvideServiceAreaSettingAPI(deps.srvareaRepo, deps.regionRepo, deps.auditRepo, deps.shiftService, deps.txnHelper, deps.slack, cfg, globalCfg, deps.zoneRepo, deps.srvAreaService), deps

}

func newServiceAreaSettingAPI(ctrl *gomock.Controller) (*ServiceAreaSettingAPI, *serviceAreaSettingAPIDeps) {
	return newServiceAreaSettingAPIWithCfg(ctrl, config.ServiceAreaConfig{
		PolygonFleetServiceName: "origin-fleet",
		PolygonFoodServiceName:  "origin-food",
	}, config.GlobalConfig{})
}
