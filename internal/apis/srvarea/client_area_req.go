package srvarea

import (
	"github.com/gin-gonic/gin"
	"gopkg.in/go-playground/validator.v9"

	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	apiValidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

type CreateClientAreaReq struct {
	Area        string   `json:"clientArea" binding:"required"`
	ServiceType string   `json:"serviceType" binding:"required,oneof=food messenger mart bike"`
	AreaRegions []string `json:"serviceAreas" binding:"required,gte=1"`

	areaIds []model.RegionCode
}

func NewCreateClientAreaReq(gctx *gin.Context, areaRepo repository.ServiceAreaRepository) (*CreateClientAreaReq, error) {
	var req CreateClientAreaReq
	if err := gctx.BindJSON(&req); err != nil {
		if validErrors, ok := err.(validator.ValidationErrors); ok {
			errs := apiErrors.NewMultipleError()
			for _, verr := range validErrors {
				errs.AddError(apiErrors.NewValidatorError(verr, apiValidator.GetTranslator(gctx.GetHeader("Accept-Language"))))
			}

			return nil, errs.NewAPIError()
		}
		return nil, err
	}

	req.areaIds = make([]model.RegionCode, len(req.AreaRegions))
	areas, err := areaRepo.FindByRegions(gctx.Request.Context(), req.AreaRegions)
	if err != nil {
		return nil, err
	}

	foundSet := make(map[string]model.RegionCode)
	for _, a := range areas {
		foundSet[a.Region.String()] = a.Region
	}

	errs := apiErrors.NewMultipleError()
	for i, area := range req.AreaRegions {
		rc, found := foundSet[area]
		if !found {
			errs.AddError(apiErrors.NewFieldError(area, "not exists"))
		} else {
			req.areaIds[i] = rc
		}
	}

	if errs.HasError() {
		return nil, errs.NewAPIError()
	}

	return &req, nil
}

func (cr *CreateClientAreaReq) ClientArea() (*model.ClientArea, error) {
	id, err := model.GenerateClientAreaID()
	if err != nil {
		return nil, err
	}
	result := model.NewClientArea(id, cr.Area, model.Service(cr.ServiceType))

	for _, a := range cr.areaIds {
		if err := result.AddServiceArea(a); err != nil {
			return nil, err
		}
	}

	return result, nil
}

type UpdateClientAreaReq struct {
	AreaRegions []string `json:"serviceAreas" binding:"required,gte=1"`

	clientAreaID string
	areaIds      []model.RegionCode
}

func NewUpdateClientAreaReq(gctx *gin.Context, areaRepo repository.ServiceAreaRepository) (*UpdateClientAreaReq, error) {
	var req UpdateClientAreaReq
	req.clientAreaID = gctx.Param(KeyClientAreaID)

	if err := gctx.BindJSON(&req); err != nil {
		if validErrors, ok := err.(validator.ValidationErrors); ok {
			errs := apiErrors.NewMultipleError()
			for _, verr := range validErrors {
				errs.AddError(apiErrors.NewValidatorError(verr, apiValidator.GetTranslator(gctx.GetHeader("Accept-Language"))))
			}

			return nil, errs.NewAPIError()
		}
		return nil, err
	}

	req.areaIds = make([]model.RegionCode, len(req.AreaRegions))
	areas, err := areaRepo.FindByRegions(gctx.Request.Context(), req.AreaRegions)
	if err != nil {
		return nil, err
	}

	foundSet := make(map[string]model.RegionCode)
	for _, a := range areas {
		foundSet[a.Region.String()] = a.Region
	}

	errs := apiErrors.NewMultipleError()
	for i, area := range req.AreaRegions {
		rc, found := foundSet[area]
		if !found {
			errs.AddError(apiErrors.NewFieldError(area, "not exists"))
		} else {
			req.areaIds[i] = rc
		}
	}

	if errs.HasError() {
		return nil, errs.NewAPIError()
	}

	return &req, nil
}

func (ur UpdateClientAreaReq) ClientAreaID() string {
	return ur.clientAreaID
}

func (ur *UpdateClientAreaReq) Update(area *model.ClientArea) error {
	return area.SetServiceAreas(ur.areaIds)
}
