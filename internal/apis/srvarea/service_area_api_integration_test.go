//go:build integration_test
// +build integration_test

package srvarea_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/srvarea"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const IttRegionName = "ITTREGION1"

func TestServiceAreaAPI(t *testing.T) {
	t.Run("Read existing with empty setting should get default setting.", func(tt *testing.T) {
		st := newServiceAreaTest(tt)

		saft := ServiceAreaForTest{
			ID:                "abcdefg",
			Region:            IttRegionName,
			DistributionLogic: model.DistributionLogicAutoAssign.String(),
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
		}
		st.container.ServiceAreaDataStore.Insert(context.Background(), &saft)

		getResp := st.getServiceArea(tt, saft.ID)
		require.Equal(tt, saft.ID, getResp.ID)
		require.Equal(tt, srvarea.NewAutoAssignDistributionRes(*model.NewAutoAssignDistribution()), getResp.Distribution)
	})

	t.Run("update service area with shift driver id", func(t *testing.T) {
		st := newServiceAreaTest(t)
		id := "abcdefg"
		saft := ServiceAreaForTest{
			ID:                id,
			Region:            IttRegionName,
			DistributionLogic: model.DistributionLogicAutoAssign.String(),
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
			ShiftDriverIDs:    []string{"id1", "id2", "id3"},
		}
		st.container.ServiceAreaDataStore.Insert(context.Background(), &saft)

		updateReq := srvarea.UpdateServiceAreaReq{
			Distribution: &srvarea.DistributionReq{
				Logic: model.DistributionLogicAutoAssign,
			},
			RequiredPhotoOfJacketAndBoxes: []string{},
			ShiftDriverIDs:                "id1, id2, id3, id4, id5",
		}

		_ = st.updateServiceArea(t, id, updateReq)

		res, err := st.container.MongoServiceAreaRepository.Get(context.Background(), id)
		require.NoError(t, err)
		require.Equal(t, []string{"id1", "id2", "id3", "id4", "id5"}, res.ShiftDriverIDs)

		au, err := st.container.AuditLogRepository.FindOneByID(context.Background(), id)
		require.NoError(t, err)
		require.Equal(t, model.ObjectType("service_area"), au.Object.ObjectType)
	})

	t.Run("get valid shift driver id", func(t *testing.T) {
		st := newServiceAreaTest(t)
		id := "abcdefg"
		dIDs := []string{"id1", "id2", "id3"}
		saft := ServiceAreaForTest{
			ID:                id,
			Region:            IttRegionName,
			DistributionLogic: model.DistributionLogicAutoAssign.String(),
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
			ShiftDriverIDs:    dIDs,
		}
		st.container.ServiceAreaDataStore.Insert(context.Background(), &saft)

		getResp := st.getServiceArea(t, id)
		actualText := strings.Join(dIDs, ", ")

		require.Equal(t, actualText, getResp.ShiftDriverIDs)
	})

	t.Run("remove shift from driver id when admin remove it from whitelist", func(t *testing.T) {
		dID1 := "DRV_PATTAYA_ONLINE"
		dID2 := "TESTER_DRV_PATTAYA_ONLINE"
		st := newServiceAreaTest(t)
		sIDs := st.createShift(t, []string{dID1, dID2})

		id := "abcdefg"
		saft := ServiceAreaForTest{
			ID:                id,
			Region:            IttRegionName,
			DistributionLogic: model.DistributionLogicAutoAssign.String(),
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
			ShiftDriverIDs:    []string{dID1, dID2},
		}
		st.container.ServiceAreaDataStore.Insert(context.Background(), &saft)

		updateReq := srvarea.UpdateServiceAreaReq{
			Distribution: &srvarea.DistributionReq{
				Logic: model.DistributionLogicAutoAssign,
			},
			RequiredPhotoOfJacketAndBoxes: []string{},
			ShiftDriverIDs:                dID1,
		}

		_ = st.updateServiceArea(t, id, updateReq)

		ctx := context.Background()
		drv, err := st.container.DriverRepository.GetProfile(ctx, dID2)
		require.NoError(t, err)
		require.Equal(t, []string{sIDs[0]}, drv.Shifts)

		s1, err := st.container.ShiftRepository.FindByID(ctx, sIDs[1])
		require.NoError(t, err)
		require.Equal(t, []string{dID1}, s1.DriverIDs)

		s2, err := st.container.ShiftRepository.FindByID(ctx, sIDs[2])
		require.NoError(t, err)
		require.Equal(t, []string{dID1}, s2.DriverIDs)
	})

	t.Run("should not remove shift if driver whitelist is not update", func(t *testing.T) {
		dID1 := "DRV_PATTAYA_ONLINE"
		dID2 := "TESTER_DRV_PATTAYA_ONLINE"
		st := newServiceAreaTest(t)
		sIDs := st.createShift(t, []string{dID1, dID2})

		id := "abcdefg"
		saft := ServiceAreaForTest{
			ID:                id,
			Region:            IttRegionName,
			DistributionLogic: model.DistributionLogicAutoAssign.String(),
			CreatedAt:         time.Now(),
			UpdatedAt:         time.Now(),
			ShiftDriverIDs:    []string{dID1, dID2},
		}
		st.container.ServiceAreaDataStore.Insert(context.Background(), &saft)

		updateReq := srvarea.UpdateServiceAreaReq{
			Distribution: &srvarea.DistributionReq{
				Logic: model.DistributionLogicAutoAssign,
			},
			RequiredPhotoOfJacketAndBoxes: []string{},
			ShiftDriverIDs:                fmt.Sprintf("%s, %s", dID1, dID2),
		}

		_ = st.updateServiceArea(t, id, updateReq)

		ctx := context.Background()
		drv, err := st.container.DriverRepository.GetProfile(ctx, dID2)
		require.NoError(t, err)
		require.Equal(t, []string{sIDs[0], sIDs[1], sIDs[2]}, drv.Shifts)

		s1, err := st.container.ShiftRepository.FindByID(ctx, sIDs[1])
		require.NoError(t, err)
		require.Equal(t, []string{dID1, dID2}, s1.DriverIDs)

		s2, err := st.container.ShiftRepository.FindByID(ctx, sIDs[2])
		require.NoError(t, err)
		require.Equal(t, []string{dID1, dID2}, s2.DriverIDs)
	})

	t.Run("smart distribution blacklist zone codes", func(t *testing.T) {
		ctx := context.Background()
		api := newServiceAreaTest(t)

		// prepare zone
		err := api.container.ZoneDataStore.InsertMany(ctx, []any{
			model.Zone{Region: "A", ZoneCode: "ZONE_1", Active: true},
			model.Zone{Region: "A", ZoneCode: "ZONE_2", Active: true},
			model.Zone{Region: "A", ZoneCode: "ZONE_I", Active: false},
			model.Zone{Region: "B", ZoneCode: "ZONE_3", Active: true},
			model.Zone{Region: "B", ZoneCode: "ZONE_4", Active: true},
		})
		require.NoError(t, err)

		t.Parallel()

		testcases := []struct {
			name               string
			region             string
			initZoneCodes      []string
			zoneCodesUpdater   []string
			expectedZoneCodes  []string
			expectedHttpStatus int
		}{
			{
				name:               "insert 1 valid zone",
				region:             "A",
				initZoneCodes:      []string{},
				zoneCodesUpdater:   []string{"ZONE_1"},
				expectedZoneCodes:  []string{"ZONE_1"},
				expectedHttpStatus: http.StatusOK,
			},
			{
				name:               "insert 2 valid zones",
				region:             "A",
				initZoneCodes:      []string{},
				zoneCodesUpdater:   []string{"ZONE_1", "ZONE_2"},
				expectedZoneCodes:  []string{"ZONE_1", "ZONE_2"},
				expectedHttpStatus: http.StatusOK,
			},
			{
				name:               "remove 1 zone",
				region:             "A",
				initZoneCodes:      []string{"ZONE_1", "ZONE_2"},
				zoneCodesUpdater:   []string{"ZONE_2"},
				expectedZoneCodes:  []string{"ZONE_2"},
				expectedHttpStatus: http.StatusOK,
			},
			{
				name:               "insert 1 other region's zone",
				region:             "A",
				initZoneCodes:      []string{},
				zoneCodesUpdater:   []string{"ZONE_3"},
				expectedZoneCodes:  []string{},
				expectedHttpStatus: http.StatusBadRequest,
			},
			{
				name:               "insert 1 invalid zone",
				region:             "A",
				initZoneCodes:      []string{},
				zoneCodesUpdater:   []string{"ZONE_99"},
				expectedZoneCodes:  []string{},
				expectedHttpStatus: http.StatusBadRequest,
			},
			{
				name:               "insert duplicate zones",
				region:             "A",
				initZoneCodes:      []string{},
				zoneCodesUpdater:   []string{"ZONE_1", "ZONE_2", "ZONE_1"},
				expectedZoneCodes:  []string{},
				expectedHttpStatus: http.StatusBadRequest,
			},
			{
				name:               "insert zones with a invalid zone",
				region:             "A",
				initZoneCodes:      []string{"ZONE_2"},
				zoneCodesUpdater:   []string{"ZONE_1", "ZONE_2", "ZONE_99"},
				expectedZoneCodes:  []string{"ZONE_2"},
				expectedHttpStatus: http.StatusBadRequest,
			},
			{
				name:               "insert zones with a inactive zone",
				region:             "A",
				initZoneCodes:      []string{"ZONE_2"},
				zoneCodesUpdater:   []string{"ZONE_1", "ZONE_2", "ZONE_I"},
				expectedZoneCodes:  []string{"ZONE_2"},
				expectedHttpStatus: http.StatusBadRequest,
			},
		}

		for _, tc := range testcases {
			t.Run(tc.name, func(t *testing.T) {
				id := model.GenerateServiceAreaID()

				// prepare service area
				sa := model.ServiceArea{
					ID:                id,
					Region:            model.RegionCode(tc.region),
					DistributionLogic: model.DistributionLogicAutoAssign,
					Distribution:      model.AutoAssignDistribution{SmartDistributionBlacklistZoneCodes: tc.initZoneCodes},
				}
				err = api.container.ServiceAreaDataStore.Insert(ctx, &sa)
				require.NoError(t, err)

				// perform update
				sa.Distribution.SmartDistributionBlacklistZoneCodes = tc.zoneCodesUpdater

				res := api.updateServiceAreaWithModel(t, id.String(), sa)
				res.AssertResponseCode(t, tc.expectedHttpStatus)

				// expect service area
				var result model.ServiceArea
				err = api.container.ServiceAreaDataStore.FindOne(ctx, bson.M{"service_area_id": id.String()}, &result)
				require.NoError(t, err)

				require.Equal(t, tc.expectedZoneCodes, result.Distribution.SmartDistributionBlacklistZoneCodes)
			})
		}
	})
}

func TestServiceAreaAPI_UpdateBias(t *testing.T) {
	testcases := []struct {
		value              prediction.BiasLevelType
		expectedStatusCode int
	}{
		{value: prediction.BiasL0, expectedStatusCode: http.StatusOK},
		{value: prediction.BiasL1, expectedStatusCode: http.StatusOK},
		{value: prediction.BiasL2, expectedStatusCode: http.StatusOK},
		{value: prediction.BiasL3, expectedStatusCode: http.StatusOK},
		{value: prediction.BiasL4, expectedStatusCode: http.StatusOK},
		{value: prediction.BiasL5, expectedStatusCode: http.StatusOK},
		{value: prediction.BiasL6, expectedStatusCode: http.StatusOK},
		{value: prediction.BiasL7, expectedStatusCode: http.StatusOK},
		{value: "", expectedStatusCode: http.StatusOK},
		{value: "invalid", expectedStatusCode: http.StatusBadRequest},
	}

	for _, tc := range testcases {
		t.Run("update goodness bias", func(t *testing.T) {
			st := newServiceAreaTest(t)

			id := "abcdefg"
			saft := ServiceAreaForTest{
				ID:                id,
				Region:            IttRegionName,
				DistributionLogic: model.DistributionLogicAutoAssign.String(),
				CreatedAt:         time.Now(),
				UpdatedAt:         time.Now(),
			}
			st.container.ServiceAreaDataStore.Insert(context.Background(), &saft)

			updateReq := updateServiceAreaJSON{
				DistributionLogic: model.DistributionLogicAutoAssign,
				Distribution: distributionJSON{
					Logic:                              model.DistributionLogicAutoAssign,
					SmartDistributionGoodnessBiasLevel: tc.value,
				},
			}

			gctx := st.updateServiceAreaWithJSON(id, updateReq)
			gctx.AssertResponseCode(t, tc.expectedStatusCode)

			ctx := context.Background()
			s2, err := st.container.MongoServiceAreaRepository.Get(ctx, id)
			require.NoError(t, err)

			if tc.expectedStatusCode == http.StatusOK {
				require.Equal(t, tc.value, s2.Distribution.SmartDistributionGoodnessBiasLevel)
			} else {
				require.Equal(t, prediction.BiasUnspecified, s2.Distribution.SmartDistributionGoodnessBiasLevel)
			}
		})
	}
}

func TestTierBenefitAPI(t *testing.T) {
	benefitHTML := "<h1>New benefit</h1>"

	createUpdateTierBenefitReq := func() srvarea.UpdateTierBenefitReq {
		return srvarea.UpdateTierBenefitReq{
			Benefit: benefitHTML,
		}
	}

	t.Run("return 204, update a tier benfit for a region", func(tt *testing.T) {
		regionID := "sa1"
		st := newServiceAreaTest(tt)

		req := createUpdateTierBenefitReq()
		ctx := st.updateTierBenefit(tt, regionID, req)
		region, err := st.container.MongoServiceAreaRepository.Get(context.Background(), regionID)
		assert.NoError(tt, err)

		var resp srvarea.ServiceAreaRes
		testutil.DecodeJSON(tt, ctx.ResponseRecorder.Body, &resp)
		ctx.AssertResponseCode(tt, 200)
		assert.Equal(tt, benefitHTML, region.GetBenefit())
		assert.Equal(tt, benefitHTML, resp.Benefit)
		assert.Equal(tt, regionID, resp.ID)
		assert.Equal(tt, "BKK", resp.Region)
	})

	t.Run("return 404, if a region id is not found.", func(tt *testing.T) {
		regionID := "something-wrong"
		st := newServiceAreaTest(tt)

		req := createUpdateTierBenefitReq()
		ctx := st.updateTierBenefit(tt, regionID, req)

		ctx.AssertResponseCode(tt, 404)
	})
}

func newFloat(v float64) *float64 {
	return &v
}

func newInt(v int64) *int64 {
	return &v
}

func containServiceArea(listResp ListResp, id string) bool {
	for _, s := range listResp.Data {
		if s.ID == id {
			return true
		}
	}
	return false
}

type serviceAreaApiTest struct {
	container *ittest.IntegrationTestContainer
}

func newServiceAreaTest(t *testing.T) *serviceAreaApiTest {
	st := serviceAreaApiTest{
		container: ittest.NewContainer(t),
	}
	st.container.RegionRepository.UpdateCache(context.Background())

	return &st
}

func (st serviceAreaApiTest) createServiceArea(t *testing.T, req srvarea.CreateServiceAreaReq) srvarea.ServiceAreaRes {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPOST("/v1/admin/service-areas")
	reqBody, _ := json.Marshal(&req)
	logrus.Info(string(reqBody))
	ctx.SetBody(bytes.NewReader(reqBody))
	ctx.Send(st.container.GinEngineRouter)
	ctx.AssertResponseCode(t, http.StatusCreated)

	resp := srvarea.ServiceAreaRes{}
	ctx.DecodeJSONResponse(&resp)

	return resp
}

func (st serviceAreaApiTest) getServiceArea(t *testing.T, id string) srvarea.ServiceAreaRes {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetGET("%s", "/v1/admin/service-areas/"+id)
	ctx.Send(st.container.GinEngineRouter)
	ctx.AssertResponseCode(t, http.StatusOK)

	resp := srvarea.ServiceAreaRes{}
	ctx.DecodeJSONResponse(&resp)

	return resp
}

func (st serviceAreaApiTest) listServiceAreas(t *testing.T) ListResp {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetGET("/v1/admin/service-areas")
	st.container.GinEngineRouter.HandleContext(ctx.GinCtx())
	ctx.AssertResponseCode(t, http.StatusOK)

	resp := ListResp{}
	ctx.DecodeJSONResponse(&resp)

	return resp
}

func (st serviceAreaApiTest) updateServiceArea(t *testing.T, id string, req srvarea.UpdateServiceAreaReq) srvarea.ServiceAreaRes {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPUT("%s", "/v1/admin/service-areas/"+id)
	reqBody, _ := json.Marshal(&req)
	logrus.Info(string(reqBody))
	ctx.SetBody(bytes.NewReader(reqBody))
	ctx.Send(st.container.GinEngineRouter)
	ctx.AssertResponseCode(t, http.StatusOK)

	resp := srvarea.ServiceAreaRes{}
	ctx.DecodeJSONResponse(&resp)

	return resp
}

type updateServiceAreaJSON struct {
	DistributionLogic model.DistributionLogic `json:"distributionLogic"`
	Distribution      distributionJSON        `json:"distribution"`
}

type distributionJSON struct {
	Logic                              model.DistributionLogic  `json:"logic"`
	SmartDistributionGoodnessBiasLevel prediction.BiasLevelType `json:"smartDistributionGoodnessBiasLevel"`
}

func (st serviceAreaApiTest) updateServiceAreaWithJSON(id string, req updateServiceAreaJSON) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPUT("%s", "/v1/admin/service-areas/"+id)
	reqBody, _ := json.Marshal(&req)
	ctx.SetBody(bytes.NewReader(reqBody))
	ctx.Send(st.container.GinEngineRouter)
	return ctx
}

func (st serviceAreaApiTest) updateServiceAreaWithModel(t *testing.T, id string, req model.ServiceArea) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPUT("%s", "/v1/admin/service-areas/"+id)

	body, _ := json.Marshal(&req)
	ctx.SetBody(bytes.NewReader(body))

	ctx.Send(st.container.GinEngineRouter)

	return ctx
}

func (st serviceAreaApiTest) updateServiceAreaDistribution(t *testing.T, id string, req interface{}, statusCode int) UpdateDistributionResp {
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPUT("%s", "/v1/admin/service-areas/"+id+"/distribution")
	reqBody, _ := json.Marshal(&req)
	logrus.Info(string(reqBody))
	ctx.SetBody(bytes.NewReader(reqBody))
	ctx.Send(st.container.GinEngineRouter)
	ctx.AssertResponseCode(t, statusCode)

	resp := UpdateDistributionResp{}
	ctx.DecodeJSONResponse(&resp)

	return resp
}

func (st serviceAreaApiTest) updateTierBenefit(t *testing.T, id string, req srvarea.UpdateTierBenefitReq) *testutil.GinContextWithRecorder {
	ctx := testutil.NewContextWithRecorder()
	reqBody, _ := json.Marshal(&req)
	ctx.SetPUT("%s", fmt.Sprintf("/v1/admin/service-areas/%s/tier-benefit", id)).AdminAuthorized("<EMAIL>").SetBody(bytes.NewReader(reqBody))

	ctx.Send(st.container.GinEngineRouter)

	return ctx
}

func (st serviceAreaApiTest) createShift(t *testing.T, driverIDs []string) []string {
	now := timeutil.BangkokNow()
	sID1 := primitive.NewObjectID()
	sID2 := primitive.NewObjectID()
	sID3 := primitive.NewObjectID()

	shifts := []model.Shift{
		{
			ID:           sID1,
			Region:       "BKK",
			Active:       true,
			Quota:        10,
			BookedAmount: 2,
			DriverIDs:    driverIDs,
			Start:        now.Add(time.Minute * -30),
			End:          now.Add(time.Minute * 30),
			CreatedAt:    now,
			UpdatedAt:    now,
		},
		{
			ID:           sID2,
			Region:       "BKK",
			Active:       true,
			Quota:        10,
			BookedAmount: 2,
			DriverIDs:    driverIDs,
			Start:        now.Add(time.Hour * 2),
			End:          now.Add(time.Hour * 3),
			CreatedAt:    now,
			UpdatedAt:    now,
		},
		{
			ID:           sID3,
			Region:       "BKK",
			Active:       true,
			Quota:        10,
			BookedAmount: 2,
			DriverIDs:    driverIDs,
			Start:        now.Add(time.Hour * 4),
			End:          now.Add(time.Hour * 5),
			CreatedAt:    now,
			UpdatedAt:    now,
		},
	}

	ctx := context.Background()
	err := st.container.ShiftRepository.CreateAll(ctx, shifts)
	require.NoError(t, err)

	d1, err := st.container.DriverRepository.GetProfile(ctx, driverIDs[0])
	require.NoError(t, err)
	d1.Shifts = []string{sID1.Hex(), sID2.Hex(), sID3.Hex()}
	err = st.container.DriverRepository.Update(ctx, d1)
	require.NoError(t, err)

	d2, err := st.container.DriverRepository.GetProfile(ctx, driverIDs[1])
	require.NoError(t, err)
	d2.Shifts = []string{sID1.Hex(), sID2.Hex(), sID3.Hex()}
	err = st.container.DriverRepository.Update(ctx, d2)
	require.NoError(t, err)

	return []string{sID1.Hex(), sID2.Hex(), sID3.Hex()}
}

func assertDefaultAutoAssignDist(t *testing.T, resp srvarea.AutoAssignDistributionRes) {
	daa := model.NewAutoAssignDistribution()
	require.Equal(t, daa.MinRadiusInKm, resp.MinRadiusInKm)
	require.Equal(t, daa.MaxRadiusInKm, resp.MaxRadiusInKm)
	require.Equal(t, daa.GroupDistInKm, resp.GroupDistInKm)
	require.Equal(t, daa.DistanceScoreWeight, resp.DistanceScoreWeight)
	require.Equal(t, daa.AcceptingDurationInSecond, resp.AcceptingDurationInSecond)
	require.Equal(t, daa.AutoAcceptScore, resp.AutoAcceptScore)
	require.Equal(t, daa.NewbieMaxDays, resp.NewbieMaxDays)
	require.Equal(t, daa.NewbieScoreWeight, resp.NewbieScoreWeight)
	require.Equal(t, daa.AcceptancePositiveRate, resp.AcceptancePositiveRate)
	require.Equal(t, daa.AcceptanceMinOrders, resp.AcceptanceMinOrders)
	require.Equal(t, daa.AcceptanceScoreWeight, resp.AcceptanceScoreWeight)
	require.Equal(t, daa.IncentiveScoreA, resp.IncentiveScoreA)
	require.Equal(t, daa.IncentiveScoreWeight, resp.IncentiveScoreWeight)
	require.Equal(t, daa.MinIdleInMinute, resp.MinIdleInMinute)
	require.Equal(t, daa.MaxIdleInMinute, resp.MaxIdleInMinute)
	require.Equal(t, daa.IdleScoreWeight, resp.IdleScoreWeight)
	require.Equal(t, daa.NotifyViaSocketIOEnabled, resp.NotifyViaSocketIOEnabled)
}

func assertAutoAssignDistEqual(t *testing.T, req aaReq, aaUpdateResp UpdateDistributionResp) {
	require.Equal(t, *req.MinRadiusInKm, aaUpdateResp.MinRadiusInKm)
	require.Equal(t, *req.MaxRadiusInKm, aaUpdateResp.MaxRadiusInKm)
	require.Equal(t, *req.GroupDistInKm, aaUpdateResp.GroupDistInKm)
	require.Equal(t, *req.DistanceScoreWeight, aaUpdateResp.DistanceScoreWeight)
	require.Equal(t, *req.AcceptingDurationInSecond, aaUpdateResp.AcceptingDurationInSecond)
	require.Equal(t, *req.AutoAcceptScore, aaUpdateResp.AutoAcceptScore)
	require.Equal(t, *req.NewbieMaxDays, aaUpdateResp.NewbieMaxDays)
	require.Equal(t, *req.NewbieScoreWeight, aaUpdateResp.NewbieScoreWeight)
	require.Equal(t, *req.AcceptancePositiveRate, aaUpdateResp.AcceptancePositiveRate)
	require.Equal(t, *req.AcceptanceMinOrders, aaUpdateResp.AcceptanceMinOrders)
	require.Equal(t, *req.AcceptanceScoreWeight, aaUpdateResp.AcceptanceScoreWeight)
	require.Equal(t, *req.IncentiveScoreA, aaUpdateResp.IncentiveScoreA)
	require.Equal(t, *req.IncentiveScoreWeight, aaUpdateResp.IncentiveScoreWeight)
	require.Equal(t, *req.MinIdleInMinute, aaUpdateResp.MinIdleInMinute)
	require.Equal(t, *req.MaxIdleInMinute, aaUpdateResp.MaxIdleInMinute)
	require.Equal(t, *req.IdleScoreWeight, aaUpdateResp.IdleScoreWeight)
	require.Equal(t, req.NotifyViaSocketIOEnabled, aaUpdateResp.NotifyViaSocketIOEnabled)
}

type ListResp struct {
	Data       srvarea.ListServiceAreaSettingRes `json:"data"`
	CountTotal int
}

type aaReq struct {
	Logic                     model.DistributionLogic `json:"logic"`
	MinRadiusInKm             *float64                `json:"minRadiusInKm"`
	MaxRadiusInKm             *float64                `json:"maxRadiusInKm"`
	GroupDistInKm             *float64                `json:"groupDistInKm"`
	DistanceScoreWeight       *float64                `json:"distanceScoreWeight"`
	AcceptingDurationInSecond *int64                  `json:"acceptingDurationInSecond"`
	AutoAcceptScore           *float64                `json:"autoAcceptScore"`
	NewbieMaxDays             *float64                `json:"newbieMaxDays"`
	NewbieScoreWeight         *float64                `json:"newbieScoreWeight"`
	AcceptancePositiveRate    *float64                `json:"acceptancePositiveRate"`
	AcceptanceMinOrders       *int64                  `json:"acceptanceMinOrders"`
	AcceptanceScoreWeight     *float64                `json:"acceptanceScoreWeight"`
	IncentiveScoreA           *float64                `json:"incentiveScoreA"`
	IncentiveScoreWeight      *float64                `json:"incentiveScoreWeight"`
	MinIdleInMinute           *int64                  `json:"minIdleInMinute"`
	MaxIdleInMinute           *int64                  `json:"maxIdleInMinute"`
	IdleScoreWeight           *float64                `json:"idleScoreWeight"`
	NotifyViaSocketIOEnabled  bool                    `json:"notifyViaSocketIOEnabled"`
}

type EmptyUpdateDistributionReq struct {
	Logic model.DistributionLogic `json:"logic"`
}

type UpdateDistributionResp struct {
	srvarea.AutoAssignDistributionRes
}

type ServiceAreaForTest struct {
	ID                string    `bson:"service_area_id"`
	Region            string    `bson:"region"`
	DistributionLogic string    `bson:"distribution_logic"`
	CreatedAt         time.Time `bson:"created_at"`
	UpdatedAt         time.Time `bson:"updated_at"`
	ShiftDriverIDs    []string  `bson:"shift_driver_ids"`
}
