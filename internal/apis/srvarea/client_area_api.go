package srvarea

import (
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

const (
	KeyClientAreaID      = "client-area-id"
	KeyClientAreaArea    = "client-area-area"
	KeyClientServiceType = "client-service-type"
)

type ClientAreaAPI struct {
	ClientAreaRepo  repository.ClientAreaRepository
	ServiceAreaRepo repository.ServiceAreaRepository
}

func (pa *ClientAreaAPI) Create(gctx *gin.Context) {
	req, err := NewCreateClientAreaReq(gctx, pa.ServiceAreaRepo)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		default:
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	area, err := req.ClientArea()
	if err != nil {
		if err == model.ErrDuplicatedArea {
			apiutil.ErrBadRequest(gctx, apiErrors.ErrServiceAreasDuplicate())
		} else {
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	ctx := gctx.Request.Context()
	if err := pa.ClientAreaRepo.Create(ctx, area); err != nil {
		if errors.Cause(err) == repository.ErrClientAreaDuplicate {
			apiutil.ErrBadRequest(gctx, apiErrors.ErrClientAreaDuplicate())
		} else {
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	apiutil.Created(gctx, NewClientAreaRes(*area))
}

func (pa *ClientAreaAPI) Get(gctx *gin.Context) {
	id := gctx.Param(KeyClientAreaID)

	ctx := gctx.Request.Context()
	area, err := pa.ClientAreaRepo.Get(ctx, id)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiErrors.ErrClientAreaNotExists())
		} else {
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}

		return
	}

	apiutil.OK(gctx, NewClientAreaRes(*area))
}

func (pa *ClientAreaAPI) List(gctx *gin.Context) {
	skip, size := utils.OptionalParsePagination(gctx)

	ctx := gctx.Request.Context()
	sortBy := "area"
	area := gctx.Query("area")

	areas, err := pa.ClientAreaRepo.FindWithQueryAndSort(ctx, repository.ClientAreaQuery{Area: area}, skip, size, []string{sortBy}, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	count, err := pa.ClientAreaRepo.CountWithQuery(ctx, repository.ClientAreaQuery{Area: area})
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	apiutil.OKList(gctx, NewListClientAreaRes(areas), count)
}

func (pa *ClientAreaAPI) Update(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	req, err := NewUpdateClientAreaReq(gctx, pa.ServiceAreaRepo)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		default:
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	cArea, err := pa.ClientAreaRepo.Get(ctx, req.ClientAreaID())
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, apiErrors.ErrClientAreaNotExists())
		} else {
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	if err := req.Update(cArea); err != nil {
		if err == model.ErrDuplicatedArea {
			apiutil.ErrBadRequest(gctx, apiErrors.ErrServiceAreasDuplicate())
		} else {
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	if err := pa.ClientAreaRepo.Update(ctx, cArea); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}

func ProvideClientAreaAPI(cAreaRepo repository.ClientAreaRepository, sAreaRepo repository.ServiceAreaRepository) *ClientAreaAPI {
	return &ClientAreaAPI{
		ClientAreaRepo:  cAreaRepo,
		ServiceAreaRepo: sAreaRepo,
	}
}
