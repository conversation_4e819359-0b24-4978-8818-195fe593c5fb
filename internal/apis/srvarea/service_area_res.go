package srvarea

import (
	"encoding/json"
	"strings"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type ServiceAreaRes struct {
	ID      string `json:"id"`
	Region  string `json:"region"`
	Benefit string `json:"benefit"`
	//Deprecated: should only use logic inside distribution field
	Logic                              model.DistributionLogic         `json:"distributionLogic"`
	Distribution                       DistributionRes                 `json:"distribution"`
	HeatmapType                        model.HeatMapType               `json:"heatmapType"`
	HeatmapByTimes                     []HeatmapByTimeRes              `json:"heatmapByTimes"`
	VerifyDriverPhotoRate              int                             `json:"verifyDriverPhotoRate"`
	RequiredPhotoOfJacketAndBoxes      []string                        `json:"requiredPhotoOfJacketAndBoxes"`
	TopUpConfig                        model.ServiceAreaTopUpConfig    `json:"topUpConfig"`
	ShiftDriverIDs                     string                          `json:"shiftDriverIds"`
	BlackListBookShiftDriverIDs        string                          `json:"blackListBookShiftDriverIDs"`
	NegativeBalanceGroups              []NegativeBalanceGroupConfigRes `json:"negativeBalanceGroups"`
	TierNegativeBalanceConfig          TierNegativeBalanceConfigRes    `json:"tierNegativeBalanceConfig"`
	SupplyPositioningServices          model.Services                  `json:"supplyPositioningServices"`
	ForcedPODConfig                    model.ForcedPODConfig           `json:"forcedPODConfig"`
	MessengerCompletedOrdersLimit      int                             `json:"messengerCompletedOrdersLimit"`
	WhitelistUsersEnabled              bool                            `json:"whitelistUsersEnabled"`
	WhitelistUsers                     string                          `json:"whitelistUsers"`
	CookingTimeDelayReassignEnabled    bool                            `json:"cookingTimeDelayReassignEnabled"`
	CookingTimeDelayThresholdSeconds   Second                          `json:"cookingTimeDelayThresholdSeconds"`
	ShowSubRegionsPolygon              bool                            `json:"showSubRegionsPolygon"`
	ShowProductivityIncentive          bool                            `json:"showProductivityIncentive"`
	ShowCoinIncentive                  bool                            `json:"showCoinIncentive"`
	OfflineLaterBreakDurationInMinutes Minute                          `json:"offlineLaterBreakDurationInMinutes"`
	UpdatedAt                          time.Time                       `json:"updatedAt"`
}

type HeatmapByTimeRes struct {
	StartTime   string            `json:"startTime"`
	EndTime     string            `json:"endTime"`
	HeatmapType model.HeatMapType `json:"heatmapType"`
}

func NewHeatmapByTimesRes(hms []model.HeatMapByTime) []HeatmapByTimeRes {
	r := make([]HeatmapByTimeRes, 0, len(hms))
	for _, v := range hms {
		r = append(r, HeatmapByTimeRes{
			StartTime:   v.StartTime,
			EndTime:     v.EndTime,
			HeatmapType: v.HeatmapType,
		})
	}
	return r
}

type NegativeBalanceGroupConfigRes struct {
	Name                 string  `json:"name"`
	Enabled              bool    `json:"enabled"`
	MinimumCreditBalance float64 `json:"minimumCreditBalance"`
}

type NegativeBalanceConfigRes struct {
	Enabled              bool    `json:"enabled"`
	MinimumCreditBalance float64 `json:"minimumCreditBalance"`
	DriverIDs            string  `json:"driverIDs"`
}

func NewNegativeBalanceGroupsRes(cfg []model.NegativeBalanceGroupConfig) []NegativeBalanceGroupConfigRes {
	r := make([]NegativeBalanceGroupConfigRes, 0, len(cfg))
	for _, v := range cfg {
		r = append(r, NegativeBalanceGroupConfigRes{
			Name:                 v.Name,
			Enabled:              v.Enabled,
			MinimumCreditBalance: v.MinimumCreditBalance,
		})
	}
	return r
}

type TierNegativeBalanceConfigRes struct {
	Enabled               bool                `json:"enabled"`
	NegativeBalanceByTier model.DriverTierMap `json:"negativeBalanceByTier"`
	BlacklistIDs          string              `json:"blackListIDs"`
}

func NewTierNegativeBalanceConfigRes(config model.TierNegativeBalanceConfig) TierNegativeBalanceConfigRes {
	tier := model.DefaultDriverTierMap
	var mergedItems = model.DriverTierMap{}
	for key, element := range tier {
		if val, ok := config.NegativeBalanceByTier[key]; ok {
			mergedItems[key] = val
		} else {
			mergedItems[key] = element
		}
	}
	return TierNegativeBalanceConfigRes{
		Enabled:               config.Enabled,
		NegativeBalanceByTier: mergedItems,
		BlacklistIDs:          joinString(config.BlacklistIDs),
	}
}

type BulkResp struct {
	Success []BulkRespInfo `json:"success"`
	Fail    []BulkRespInfo `json:"fail"`
}

type BulkRespInfo struct {
	ID     string `json:"id"`
	Detail string `json:"detail"`
}

func (b *BulkResp) AddSuccess(id, detail string) {
	b.Success = append(b.Success, BulkRespInfo{
		ID:     id,
		Detail: detail,
	})
}

func (b *BulkResp) AddFail(id, detail string) {
	b.Fail = append(b.Fail, BulkRespInfo{
		ID:     id,
		Detail: detail,
	})
}

func NewServiceAreaRes(area model.ServiceArea) *ServiceAreaRes {
	sr := &ServiceAreaRes{
		ID:                                 area.ID.String(),
		Region:                             area.Region.String(),
		Benefit:                            area.GetBenefit(),
		Logic:                              area.DistributionLogic,
		Distribution:                       NewDistributionRes(area.Distribution, area.DistributionLogic),
		HeatmapType:                        area.HeatmapType,
		HeatmapByTimes:                     NewHeatmapByTimesRes(area.HeatMapByTimes),
		VerifyDriverPhotoRate:              area.VerifyDriverPhotoRate,
		RequiredPhotoOfJacketAndBoxes:      area.RequiredPhotoOfJacketAndBoxes,
		TopUpConfig:                        area.TopUpConfig,
		NegativeBalanceGroups:              NewNegativeBalanceGroupsRes(area.NegativeBalanceGroups),
		TierNegativeBalanceConfig:          NewTierNegativeBalanceConfigRes(area.TierNegativeBalanceConfig),
		SupplyPositioningServices:          area.SupplyPositioningServices,
		MessengerCompletedOrdersLimit:      area.MessengerCompletedOrdersLimit,
		WhitelistUsersEnabled:              area.WhitelistUsersEnabled,
		CookingTimeDelayReassignEnabled:    area.CookingTimeDelayReassignEnabled,
		CookingTimeDelayThresholdSeconds:   Second(area.CookingTimeDelayThreshold.Seconds()),
		ShowSubRegionsPolygon:              area.ShowSubRegionsPolygon,
		ShowProductivityIncentive:          area.ShowProductivityIncentive,
		ShowCoinIncentive:                  area.ShowCoinIncentive,
		OfflineLaterBreakDurationInMinutes: Minute(area.OfflineLaterBreakDuration.Minutes()),
		UpdatedAt:                          area.UpdatedAt,
	}

	sr.ShiftDriverIDs = joinString(area.ShiftDriverIDs)
	sr.BlackListBookShiftDriverIDs = joinString(area.BlackListBookShiftDriverIDs)
	sr.WhitelistUsers = joinString(area.WhitelistUsers)

	// For compatible with old service area.
	if area.RequiredPhotoOfJacketAndBox && !area.IsRequiredPhotoOfJacketAndBoxes(model.ServiceFood) {
		sr.RequiredPhotoOfJacketAndBoxes = append(sr.RequiredPhotoOfJacketAndBoxes, string(model.ServiceFood))
	}

	return sr
}

func joinString(ids []string) string {
	var driverIDs string
	if len(ids) == 0 {
		return driverIDs
	}

	return strings.Join(ids, ", ")
}

func (sr *ServiceAreaRes) UnmarshalJSON(data []byte) error {
	var areaRes struct {
		ID                            string                          `json:"id"`
		Region                        string                          `json:"region"`
		Benefit                       string                          `json:"benefit"`
		Logic                         model.DistributionLogic         `json:"distributionLogic"`
		Distribution                  BaseDistributionRes             `json:"distribution"`
		HeatmapType                   model.HeatMapType               `json:"heatmapType"`
		HeatmapByTimes                []HeatmapByTimeRes              `json:"heatmapByTimes"`
		RequiredPhotoOfJacketAndBox   bool                            `json:"requiredPhotoOfJacketAndBox"`
		RequiredPhotoOfJacketAndBoxes []string                        `json:"requiredPhotoOfJacketAndBoxes"`
		TopUpConfig                   model.ServiceAreaTopUpConfig    `json:"topUpConfig"`
		NegativeBalanceGroups         []NegativeBalanceGroupConfigRes `json:"negativeBalanceGroups"`
		ShiftDriverIDs                string                          `json:"shiftDriverIds"`
		BlackListBookShiftDriverIDs   string                          `json:"blackListBookShiftDriverIDs"`
		NegativeBalanceConfig         NegativeBalanceConfigRes        `json:"negativeBalanceConfig"`
		TierNegativeBalanceConfig     TierNegativeBalanceConfigRes    `json:"tierNegativeBalanceConfig"`
		SupplyPositioningServices     model.Services                  `json:"supplyPositioningServices"`
		UpdatedAt                     time.Time                       `json:"updatedAt"`
	}
	if err := json.Unmarshal(data, &areaRes); err != nil {
		return err
	}

	sr.ID = areaRes.ID
	sr.Region = areaRes.Region
	sr.Logic = areaRes.Logic
	sr.Benefit = areaRes.Benefit
	sr.HeatmapType = areaRes.HeatmapType
	sr.RequiredPhotoOfJacketAndBoxes = areaRes.RequiredPhotoOfJacketAndBoxes
	sr.TopUpConfig = areaRes.TopUpConfig
	sr.ShiftDriverIDs = areaRes.ShiftDriverIDs
	sr.BlackListBookShiftDriverIDs = areaRes.BlackListBookShiftDriverIDs
	sr.TierNegativeBalanceConfig = areaRes.TierNegativeBalanceConfig
	sr.SupplyPositioningServices = areaRes.SupplyPositioningServices
	sr.NegativeBalanceGroups = areaRes.NegativeBalanceGroups
	sr.HeatmapByTimes = areaRes.HeatmapByTimes
	sr.UpdatedAt = areaRes.UpdatedAt

	switch areaRes.Distribution.GetLogic() {
	case model.DistributionLogicAutoAssign:
		distRes := struct {
			Distribution AutoAssignDistributionRes `json:"distribution"`
		}{}
		if err := json.Unmarshal(data, &distRes); err != nil {
			return err
		}
		sr.Distribution = &distRes.Distribution
	default:
		sr.Distribution = areaRes.Distribution
	}

	return nil
}

type DistributionRes interface {
	GetLogic() model.DistributionLogic
}

type BaseDistributionRes struct {
	Logic model.DistributionLogic `json:"logic"`
}

func NewBaseDistributionRes(logic model.DistributionLogic) *BaseDistributionRes {
	return &BaseDistributionRes{Logic: logic}
}

func (br BaseDistributionRes) GetLogic() model.DistributionLogic {
	return br.Logic
}

func NewDistributionRes(dist model.AutoAssignDistribution, distLogic model.DistributionLogic) DistributionRes {
	switch distLogic {
	case model.DistributionLogicAutoAssign:
		return NewAutoAssignDistributionRes(dist)
	}

	return NewBaseDistributionRes(distLogic)
}

type AutoAssignDistributionRes struct {
	BaseDistributionRes
	MinRadiusInKm                          float64                            `json:"minRadiusInKm"`
	MaxRadiusInKm                          float64                            `json:"maxRadiusInKm"`
	GroupDistInKm                          float64                            `json:"groupDistInKm"`
	DistanceScoreWeight                    float64                            `json:"distanceScoreWeight"`
	AcceptingDurationInSecond              int64                              `json:"acceptingDurationInSecond"`
	AutoAcceptScore                        float64                            `json:"autoAcceptScore"`
	NewbieMaxDays                          float64                            `json:"newbieMaxDays"`
	NewbieScoreWeight                      float64                            `json:"newbieScoreWeight"`
	AcceptancePositiveRate                 float64                            `json:"acceptancePositiveRate"`
	AcceptanceMinOrders                    int64                              `json:"acceptanceMinOrders"`
	AcceptanceScoreWeight                  float64                            `json:"acceptanceScoreWeight"`
	IncentiveScoreA                        float64                            `json:"incentiveScoreA"`
	IncentiveScoreWeight                   float64                            `json:"incentiveScoreWeight"`
	MinIdleInMinute                        int64                              `json:"minIdleInMinute"`
	MaxIdleInMinute                        int64                              `json:"maxIdleInMinute"`
	IdleScoreWeight                        float64                            `json:"idleScoreWeight"`
	BoxTypeScoreEnabled                    bool                               `json:"boxTypeScoreEnabled"`
	BoxTypeScoreWeight                     float64                            `json:"boxTypeScoreWeight"`
	BoxTypeScoreSettings                   map[string]float64                 `json:"boxTypeScoreSettings"`
	BoxTypeScoreServiceTypes               []string                           `json:"boxTypeScoreServiceTypes"`
	PredictionServiceEnabled               bool                               `json:"predictionServiceEnabled"`
	PredictionPredictVersion               string                             `json:"predictionPredictVersion"`
	PredictionOptimizeVersion              string                             `json:"predictionOptimizeVersion"`
	PredictionBatchOptimizeVersion         string                             `json:"predictionBatchOptimizeVersion"`
	PredictionRouteVersion                 string                             `json:"predictionRouteVersion"`
	NotifyViaSocketIOEnabled               bool                               `json:"notifyViaSocketIOEnabled"`
	BackToBackExtendStatusConfig           bool                               `json:"backToBackExtendStatusConfig"`
	BackToBackAllowStatus                  string                             `json:"backToBackAllowStatus"`
	AssignmentType                         prediction.AssignmentType          `json:"assignmentType"`
	MOType                                 prediction.MOType                  `json:"moType"`
	BypassIdleTime                         bool                               `json:"bypassIdleTime"`
	DistanceFromZoneLimit                  float64                            `json:"distanceFromZoneLimit"`
	PredictionWhitelist                    []string                           `json:"predictionWhitelist"`
	PredictionBlacklist                    []string                           `json:"predictionBlacklist"`
	PredictionRestaurantWhitelist          []string                           `json:"predictionRestaurantWhitelist"`
	PredictionRestaurantBlacklist          []string                           `json:"predictionRestaurantBlacklist"`
	PredictionRestaurantSingleModeOnly     []string                           `json:"predictionRestaurantSingleModeOnly"`
	RedistributionDelay                    Second                             `json:"redistributionDelay"`
	OptimizationTopN                       int64                              `json:"optimizationTopN"`
	OptimizationCandidateNumber            int64                              `json:"optimizationCandidateNumber"`
	MultipleOrderAggressiveLevel           string                             `json:"multipleOrderAggressiveLevel"`
	PredictionRestaurantBlacklistTimeSlots model.RestaurantBlacklistTimeSlots `json:"predictionRestaurantBlacklistTimeSlots"`

	SkipQuota    int `json:"skipQuota"`
	BanPeriod    int `json:"banPeriod"`
	QuotaRefresh int `json:"quotaRefresh"`

	// priority for dedicated riders
	PriorityDedicatedRidersEnabled                bool            `json:"priorityDedicatedRidersEnabled"`
	PriorityDedicatedRidersDistance               float64         `json:"priorityDedicatedRidersDistance"`
	PriorityDedicatedRidersAllowOrderServiceTypes []model.Service `json:"priorityDedicatedRidersAllowOrderServiceTypes"`
	PriorityDedicatedRidersOnlySingleService      []model.Service `json:"priorityDedicatedRidersOnlySingleService"`

	CompleteTimeCalculation model.CompleteTimeCalculation         `json:"completeTimeCalculation"`
	RadiusTimeSlots         model.RadiusTimeSlotsWithServiceTypes `json:"radiusTimeSlots"`

	// Deferred dispatch
	DeferredDispatchFeatureEnabled        bool     `json:"deferredDispatchFeatureEnabled"`
	DeferredDispatchFeatureEnabledMart    bool     `json:"deferredDispatchFeatureEnabledMart"`
	DeferredDispatchRestaurantWhitelist   []string `json:"deferredDispatchRestaurantWhitelist"`
	DeferredDispatchRestaurantBlacklist   []string `json:"deferredDispatchRestaurantBlacklist"`
	DeferredDispatchDrivingDurationMinute int      `json:"deferredDispatchDrivingDurationMinute"`
	DeferredDispatchBufferDurationMinute  int      `json:"deferredDispatchBufferDurationMinute"`

	BatchAssignmentEnabled bool `json:"batchAssignmentEnabled"`
	FullyAutoAcceptEnabled bool `json:"fullyAutoAcceptEnabled"`
	DalianOSRMEnabled      bool `json:"dalianOSRMEnabled"`
	RushDalianEnabled      bool `json:"rushDalianEnabled"`

	RushExperimentWhitelist   []string `json:"rushExperimentWhitelist"`
	NoRushExperimentWhitelist []string `json:"noRushExperimentWhitelist"`

	SmartDistributionDeprioritizationRatio float64                  `json:"smartDistributionDeprioritizationRatio"`
	SmartDistributionGoodnessBiasLevel     prediction.BiasLevelType `json:"smartDistributionGoodnessBiasLevel"`
	SmartDistributionBlacklistZoneCodes    []string                 `json:"smartDistributionBlacklistZoneCodes"`
	MaxOrdersPerRider                      int64                    `json:"maxOrdersPerRider"`
}

func NewAutoAssignDistributionRes(dist model.AutoAssignDistribution) *AutoAssignDistributionRes {
	return &AutoAssignDistributionRes{
		BaseDistributionRes:                    *NewBaseDistributionRes(dist.Logic()),
		MinRadiusInKm:                          dist.MinRadiusInKm,
		MaxRadiusInKm:                          dist.MaxRadiusInKm,
		GroupDistInKm:                          dist.GroupDistInKm,
		DistanceScoreWeight:                    dist.DistanceScoreWeight,
		AcceptingDurationInSecond:              dist.AcceptingDurationInSecond,
		AutoAcceptScore:                        dist.AutoAcceptScore,
		NewbieMaxDays:                          dist.NewbieMaxDays,
		NewbieScoreWeight:                      dist.NewbieScoreWeight,
		AcceptancePositiveRate:                 dist.AcceptancePositiveRate,
		AcceptanceMinOrders:                    dist.AcceptanceMinOrders,
		AcceptanceScoreWeight:                  dist.AcceptanceScoreWeight,
		IncentiveScoreA:                        dist.IncentiveScoreA,
		IncentiveScoreWeight:                   dist.IncentiveScoreWeight,
		MinIdleInMinute:                        dist.MinIdleInMinute,
		MaxIdleInMinute:                        dist.MaxIdleInMinute,
		IdleScoreWeight:                        dist.IdleScoreWeight,
		BoxTypeScoreEnabled:                    dist.BoxTypeScoreEnabled,
		BoxTypeScoreWeight:                     dist.BoxTypeScoreWeight,
		BoxTypeScoreSettings:                   dist.BoxTypeScoreSettings,
		BoxTypeScoreServiceTypes:               dist.BoxTypeScoreServiceTypes,
		AssignmentType:                         dist.AssignmentType,
		MOType:                                 dist.MOType,
		BypassIdleTime:                         dist.BypassIdleTime,
		DistanceFromZoneLimit:                  dist.DistanceFromZoneLimit,
		PredictionWhitelist:                    dist.PredictionWhitelist,
		PredictionBlacklist:                    dist.PredictionBlacklist,
		PredictionRestaurantWhitelist:          dist.PredictionRestaurantWhitelist,
		PredictionRestaurantBlacklistTimeSlots: dist.PredictionRestaurantBlacklistTimeSlots,
		PredictionServiceEnabled:               dist.PredictionServiceEnabled,
		PredictionPredictVersion:               dist.PredictionPredictVersion,
		PredictionOptimizeVersion:              dist.PredictionOptimizeVersion,
		PredictionBatchOptimizeVersion:         dist.PredictionBatchOptimizeVersion,
		PredictionRouteVersion:                 dist.PredictionRouteVersion,
		NotifyViaSocketIOEnabled:               dist.NotifyViaSocketIOEnabled,
		BackToBackExtendStatusConfig:           dist.BackToBackExtendStatusConfig,
		BackToBackAllowStatus:                  model.StringFromStatusList(dist.BackToBackAllowStatus),
		RedistributionDelay:                    Second(dist.RedistributionDelay.Seconds()),
		OptimizationTopN:                       dist.OptimizationTopN,
		OptimizationCandidateNumber:            dist.OptimizationCandidateNumber,
		MultipleOrderAggressiveLevel:           dist.MultipleOrderAggressiveLevel,

		SkipQuota:    dist.SkipQuota,
		BanPeriod:    int(dist.BanPeriod.Minutes()),
		QuotaRefresh: int(dist.QuotaRefresh.Minutes()),

		PriorityDedicatedRidersEnabled:                dist.PriorityDedicatedRidersEnabled,
		PriorityDedicatedRidersDistance:               dist.PriorityDedicatedRidersDistance,
		PriorityDedicatedRidersAllowOrderServiceTypes: dist.PriorityDedicatedRidersAllowOrderServiceTypes,
		PriorityDedicatedRidersOnlySingleService:      dist.PriorityDedicatedRidersOnlySingleService,

		CompleteTimeCalculation: dist.CompleteTimeCalculation,
		RadiusTimeSlots:         dist.RadiusTimeSlots,

		// Deferred dispatch
		DeferredDispatchFeatureEnabled:        dist.DeferredDispatchFeatureEnabled,
		DeferredDispatchFeatureEnabledMart:    dist.DeferredDispatchFeatureEnabledMart,
		DeferredDispatchRestaurantWhitelist:   dist.DeferredDispatchRestaurantWhitelist,
		DeferredDispatchRestaurantBlacklist:   dist.DeferredDispatchRestaurantBlacklist,
		DeferredDispatchDrivingDurationMinute: int(dist.DeferredDispatchDrivingDuration.Minutes()),
		DeferredDispatchBufferDurationMinute:  int(dist.DeferredDispatchBufferDuration.Minutes()),

		BatchAssignmentEnabled: dist.BatchAssignmentEnabled,
		FullyAutoAcceptEnabled: dist.FullyAutoAcceptEnabled,
		DalianOSRMEnabled:      dist.DalianOSRMEnabled,
		RushDalianEnabled:      dist.RushDalianEnabled,

		RushExperimentWhitelist:   dist.RushExperimentWhitelist,
		NoRushExperimentWhitelist: dist.NoRushExperimentWhitelist,

		SmartDistributionDeprioritizationRatio: dist.SmartDistributionDeprioritizationRatio,
		SmartDistributionBlacklistZoneCodes:    dist.SmartDistributionBlacklistZoneCodes,
		MaxOrdersPerRider:                      dist.MaxOrdersPerRider,
	}
}

type BroadcastDistributionRes struct {
	BaseDistributionRes
	DistributionDistance     float64 `json:"distributionDistance"`
	DistributionFirstRadius  float64 `json:"distributionFirstRadius"`
	DistributionInterval     Second  `json:"distributionInterval"`
	RedistributionDelay      Second  `json:"redistributionDelay"`
	DistributionTimes        int64   `json:"distributionTimes"`
	NotifyViaSocketIOEnabled bool    `json:"notifyViaSocketIOEnabled"`
	AutoAssignDistributionRes
	ShiftModelLimitTotalDriver              int     `json:"shiftModelLimitTotalDriver"`
	ShiftModelDistributionInterval          int     `json:"shiftModelDistributionInterval"`
	PriorityRoundDedicatedRidersEnabled     bool    `json:"priorityRoundDedicatedRidersEnabled"`
	PriorityRoundDedicatedRidersDistance    float64 `json:"priorityRoundDedicatedRidersDistance"`
	PriorityRoundDedicatedRidersIntervalSec Second  `json:"priorityRoundDedicatedRidersIntervalSec"`
}

type ListServiceAreaSettingRes []ServiceAreaRes

func NewListServiceAreaSettingRes(models []model.ServiceArea) ListServiceAreaSettingRes {
	res := make([]ServiceAreaRes, len(models))
	for i, m := range models {
		res[i] = *NewServiceAreaRes(m)
	}

	return res
}
