package bulk_incentive

import (
	"context"
	"encoding/csv"
	"io"
	"slices"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/admin"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type DeleteBulkIncentive struct {
	BaseBulkIncentive
}

func ProvideDeleteBulkIncentive(ds incentive.IncentiveDataStore, cfg admin.Config) *DeleteBulkIncentive {
	return &DeleteBulkIncentive{
		BaseBulkIncentive: BaseBulkIncentive{
			ds: ds,
		},
	}
}

func (d *DeleteBulkIncentive) Type() string {
	return string(model.BulkIncentiveDelete)
}

func (d *DeleteBulkIncentive) ConvertData(_ context.Context, file io.Reader) ([]string, error) {
	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, err
	}
	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	ids := make([]string, len(rows))

	for i, row := range rows {
		ids[i] = row[0]
	}

	slices.Sort(ids)

	// remove duplicates
	ids = slices.Compact(ids)

	return ids, nil

}
