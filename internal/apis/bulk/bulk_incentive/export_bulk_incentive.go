package bulk_incentive

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/admin"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulk/bulk_incentive/bulk_incentive_model"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type ExportBulkIncentive struct {
	BaseBulkIncentive
}

func ProvideExporterBulkIncentive(ds incentive.IncentiveDataStore, cfg admin.Config) *ExportBulkIncentive {
	return &ExportBulkIncentive{
		BaseBulkIncentive{
			ds:  ds,
			cfg: cfg,
		},
	}
}

func (e *ExportBulkIncentive) Exporter(gctx *gin.Context, _ []string) bson.M {
	// add filter by service type
	return bson.M{}
}

func (e *ExportBulkIncentive) Type() string {
	return string(model.BulkIncentiveExport)
}

func (e *ExportBulkIncentive) Query(gctx *gin.Context, query bson.M) ([]map[string]string, error) {
	var values []*incentive.Incentive
	err := e.ds.FindAndSort(gctx, query, 0, 0, []string{"-created_at"}, &values)
	if err != nil {
		return nil, err
	}
	result, err := e.toMap(values)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (e *ExportBulkIncentive) GetFileName(gctx *gin.Context) (filename string, err error) {
	return fmt.Sprintf("bulk_export_incentive_%s.csv", time.Now().Format("2006_01_02")), nil
}

func (e *ExportBulkIncentive) toMap(data []*incentive.Incentive) ([]map[string]string, error) {
	result := make([]map[string]string, len(data))

	for i, d := range data {
		isActive, err := bulk_incentive_model.BooleanColumn{Active: d.Active}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		incentiveSource, err := bulk_incentive_model.IncentiveSourcesColumn{IncentiveSources: d.Sources}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		serviceTypes, err := bulk_incentive_model.ServiceTypesColumn{Services: d.ServiceTypes}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		paymentType, err := bulk_incentive_model.PaymentTypeColumn{PaymentType: d.PaymentType}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		orderType, err := bulk_incentive_model.OrderTypeColumn{OrderType: d.OrderShiftType}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		incentiveOrderTiers, err := bulk_incentive_model.IncentiveOrderTiersColumn{OrderTiers: d.OrderTier}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		ar, err := bulk_incentive_model.Float64NullableColumn{Value: d.AR}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		cr, err := bulk_incentive_model.Float64NullableColumn{Value: d.CR}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		rating, err := bulk_incentive_model.Float64NullableColumn{Value: d.Rating}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		haveBox, err := bulk_incentive_model.BooleanNullableColumn{Active: d.Box}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		haveJacket, err := bulk_incentive_model.BooleanNullableColumn{Active: d.Jacket}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		whitelistIds, err := bulk_incentive_model.WhitelistColumn{WhiteLists: d.WhitelistIDs}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		tiers, err := bulk_incentive_model.DriverTierColumn{Tiers: d.Tiers}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		dateRange, err := bulk_incentive_model.DateRangeColumn{DateRange: d.DateRange}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		timeRange, err := bulk_incentive_model.TimeRangeColumn{TimeRanges: d.Times}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		coordinates, err := bulk_incentive_model.CoordinatesColumn{Coordinates: d.Geometry.Coordinates}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		streakIncentive, err := bulk_incentive_model.StreakIncentiveColumn{Streak: d.Streak}.MarshalCSV()
		if err != nil {
			return nil, err
		}
		result[i] = map[string]string{
			"id":                         d.IncentiveID,
			"name":                       d.Name,
			"description":                d.Description,
			"region":                     d.Region.String(),
			"active":                     string(isActive),
			"display_name":               d.DisplayName,
			"incentive_sources":          string(incentiveSource),
			"service_types":              string(serviceTypes),
			"payment_type":               string(paymentType),
			"order_type":                 string(orderType),
			"incentive_order_tiers":      string(incentiveOrderTiers),
			"acceptance_rate":            string(ar),
			"cancellation_rate":          string(cr),
			"rating":                     string(rating),
			"have_box":                   string(haveBox),
			"have_jacket":                string(haveJacket),
			"whitelist_ids":              string(whitelistIds),
			"tiers":                      string(tiers),
			"date_range":                 string(dateRange),
			"time_ranges":                string(timeRange),
			"location_name":              d.LocationName,
			"effective_area.type":        e.mapEffectiveAreaType(bulk_incentive_model.CoordinatesColumn{Coordinates: d.Geometry.Coordinates}, d.ZoneCode).String(),
			"effective_area.zone":        d.ZoneCode,
			"effective_area.coordinates": string(coordinates),
			"streak":                     string(streakIncentive),
		}
	}
	return result, nil
}

func (e *ExportBulkIncentive) GetHeaders() []string {
	return []string{
		"id", "name", "description", "region", "active", "display_name", "incentive_sources", "service_types",
		"payment_type", "order_type", "incentive_order_tiers", "acceptance_rate", "cancellation_rate", "rating", "have_box",
		"have_jacket", "whitelist_ids", "tiers", "date_range", "time_ranges", "location_name", "effective_area.type",
		"effective_area.zone", "effective_area.coordinates", "streak",
	}
}

func (e *ExportBulkIncentive) mapEffectiveAreaType(geoJson bulk_incentive_model.CoordinatesColumn, zone string) bulk_incentive_model.EffectiveArea {
	if len(geoJson.Coordinates) > 0 {
		return bulk_incentive_model.EFFECTIVE_AREA_COORDINATES
	}
	if zone != "" {
		return bulk_incentive_model.EFFECTIVE_AREA_ZONE
	}
	return bulk_incentive_model.EFFECTIVE_AREA_UNKNOWN
}
