package bulk_incentive

import (
	"errors"
	"fmt"
	"time"

	"github.com/jszwec/csvutil"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/admin"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
)

type BaseBulkIncentive struct {
	ds  incentive.IncentiveDataStore
	cfg admin.Config
}

func (b *BaseBulkIncentive) Type() string {
	return ""
}

func (b *BaseBulkIncentive) GetBatchSize() int {
	// return 0 mean use default batch size
	return 0
}

func (b *BaseBulkIncentive) GetBatchDelay() time.Duration {
	// no delay between batch by default
	return 0
}

func (b *BaseBulkIncentive) DataStore() mongodb.DataStoreInterface {
	return b.ds
}

func (b *BaseBulkIncentive) getMaxColumn() int {
	// number of columns, used by create, update operations
	return 25
}

func (b *BaseBulkIncentive) handleValidationError(err error) string {
	var e *csvutil.DecodeError
	if errors.As(err, &e) {
		return fmt.Sprintf("%s: field %q line %d", e.Err, e.Field, e.Line)
	}
	return e.Error()
}
