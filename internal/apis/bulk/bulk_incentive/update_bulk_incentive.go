package bulk_incentive

import (
	"context"
	"encoding/csv"
	"errors"
	"io"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/jszwec/csvutil"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/admin"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulk/bulk_incentive/bulk_incentive_model"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

type UpdateBulkIncentive struct {
	BaseBulkIncentive
	polygonService polygon.Polygon
	zoneService    service.ZoneService
}

func ProvideUpdateBulkIncentive(ds incentive.IncentiveDataStore, cfg admin.Config, pgs polygon.Polygon, zs service.ZoneService) *UpdateBulkIncentive {
	return &UpdateBulkIncentive{
		polygonService: pgs,
		zoneService:    zs,
		BaseBulkIncentive: BaseBulkIncentive{
			ds:  ds,
			cfg: cfg,
		},
	}
}

func (u *UpdateBulkIncentive) Type() string {
	return string(model.BulkIncentiveUpdate)
}

func (u *UpdateBulkIncentive) ConvertData(ctx context.Context, file io.Reader) (map[string]map[string]interface{}, error) {
	csvSize := u.getMaxColumn()
	reader := csv.NewReader(file)
	decoder, err := csvutil.NewDecoder(reader)
	if err != nil {
		// TODO : think of some error here
		return nil, errors.New(err.Error())
	}

	maxSize := u.cfg.BulkUploadIncentiveCSVMaxSchemeSize

	payload := make(map[string]map[string]interface{})

	if len(decoder.Header()) != csvSize {
		return nil, admin.NewValidateError(admin.ErrInvalidUpdateIncentiveStructure)
	}

	for {
		if len(payload) > maxSize {
			return nil, admin.NewValidateError(admin.ErrMaximumRow)
		}
		var decoded bulk_incentive_model.IncentiveModelCSV
		err := decoder.Decode(&decoded)
		if err == io.EOF {
			break
		} else if err != nil {
			return nil, admin.NewValidateError(u.handleValidationError(err))
		}
		if reasons, isValid := decoded.Validate(ctx, u.zoneService); !isValid {
			reason := strings.Join(reasons, ",")
			return nil, admin.NewValidateError(reason)
		}
		mapPayload, err := decoded.ToMap(u.polygonService)
		user, exist := auth.GetAdminUserFromGctx(ctx.(*gin.Context))
		if !exist {
			return nil, errors.New("required user authorization")
		}
		mapPayload["updated_by"] = user.GetEmail()
		// created_at and created_by should not be updated
		delete(mapPayload, "created_at")
		delete(mapPayload, "created_by")
		if err != nil {
			return nil, err
		}
		payload[decoded.Id] = mapPayload
	}

	return payload, nil
}

func (u *UpdateBulkIncentive) Updater(key string, data map[string]interface{}) bson.M {
	return bson.M{
		"$set": data,
	}
}
