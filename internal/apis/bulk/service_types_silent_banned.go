package bulk

import (
	"context"
	"encoding/csv"
	"errors"
	"io"
	"sort"
	"strings"
	"time"

	"github.com/kelseyhightower/envconfig"
	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/admin"
	"git.wndv.co/lineman/fleet-distribution/internal/bulk"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
)

type BulkUpdateServiceTypeSilentBanned struct {
	// TODO: Make generic config
	cfg      admin.Config
	ds       persistence.DriversDataStore
	dsConfig bulk.DataStoreConfig
}

func ProvideBulkUpdateServiceTypeSilentBanned(config admin.Config, ds persistence.DriversDataStore) *BulkUpdateServiceTypeSilentBanned {
	var dsConfig bulk.DataStoreConfig
	envconfig.MustProcess("DRIVER_DATA_STORE", &dsConfig)
	return &BulkUpdateServiceTypeSilentBanned{
		cfg:      config,
		ds:       ds,
		dsConfig: dsConfig,
	}
}

func (b *BulkUpdateServiceTypeSilentBanned) Type() string {
	return string(model.BulkProcessDriverServiceTypeSilentBanned)
}

func (b *BulkUpdateServiceTypeSilentBanned) GetBatchSize() int {
	return b.dsConfig.BatchSize
}

func (b *BulkUpdateServiceTypeSilentBanned) GetBatchDelay() time.Duration {
	return b.dsConfig.BulkDriverServiceTypeSilentBannedBatchDelay
}

func (b *BulkUpdateServiceTypeSilentBanned) DataStore() mongodb.DataStoreInterface {
	return b.ds
}

func (b *BulkUpdateServiceTypeSilentBanned) ConvertData(ctx context.Context, file io.Reader) (map[string]map[string]interface{}, error) {
	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, errors.New(err.Error())
	}

	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	if len(rows) > b.cfg.BulkUpdateServiceTypesSilentBannedMaxCSVRows {
		return nil, admin.NewValidateError(admin.ErrMaximumRow)
	}

	payload := make(map[string]map[string]interface{}, len(rows))
	for _, columns := range rows {
		if len(columns) != 2 {
			return nil, admin.NewValidateError(admin.ErrInvalidServiceTypesSilentBannedStructure)
		}

		driverID := strings.TrimSpace(columns[0])
		serviceTypes, err := b.getSortedServiceTypes(columns[1])
		if err != nil {
			return nil, admin.NewValidateError(admin.ErrInvalidServiceTypes)
		}

		payload[driverID] = map[string]interface{}{
			"service_types_silent_banned": serviceTypes,
		}
	}

	return payload, nil
}

func (b *BulkUpdateServiceTypeSilentBanned) Updater(_ string, data map[string]interface{}) bson.M {
	return bson.M{
		"$set": data,
	}
}

func (b *BulkUpdateServiceTypeSilentBanned) getSortedServiceTypes(str string) ([]model.Service, error) {
	serviceTypesStr := strings.TrimSpace(str)
	if serviceTypesStr == "" {
		return nil, nil
	}
	raw := strings.Split(serviceTypesStr, "|")
	for _, serviceType := range raw {
		if !model.Service(serviceType).IsValid() {
			return nil, admin.NewValidateError(admin.ErrInvalidServiceTypes)
		}
	}
	sort.Strings(raw)
	parsedServiceType := make([]model.Service, len(raw))
	for i, s := range raw {
		parsedServiceType[i] = model.Service(s)
	}
	return parsedServiceType, nil
}
