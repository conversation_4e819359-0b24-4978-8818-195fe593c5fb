package partnerAuth_test

import (
	"crypto/rsa"
	"crypto/x509"
	"encoding/pem"
	"testing"

	"github.com/beevik/etree"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners/auth"
)

func TestEncryption(t *testing.T) {
	cfg := partnerAuth.Config{
		CitiEncryptionCertificateFile: citiEncryptCertFile,
		CitiSigningCertificateFile:    citiSigningCertFile,
		PrivateKeyFile:                lmwnPrivateKeyFile,
		CertificateFile:               lmwnCertFile,
	}
	citiAuth := newCitiAuthenticationService(cfg)

	signing, err := citiAuth.SigningXML()
	require.NoError(t, err)

	_, err = citiAuth.Encrypt([]byte(signing))
	require.NoError(t, err)
}

func TestDecryption(t *testing.T) {

	cfg := partnerAuth.Config{
		CitiEncryptionCertificateFile: citiEncryptCertFile,
		CitiSigningCertificateFile:    citiSigningCertFile,
		PrivateKeyFile:                lmwnPrivateKeyFile,
		CertificateFile:               lmwnCertFile,
	}

	citiAuth := newCitiAuthenticationService(cfg)
	fileBytes := []byte(`<xenc:EncryptedData Type="http://www.w3.org/2001/04/xmlenc#Element" xmlns:xenc="http://www.w3.org/2001/04/xmlenc#"><xenc:EncryptionMethod Algorithm="http://www.w3.org/2001/04/xmlenc#tripledes-cbc"/><dsig:KeyInfo xmlns:dsig="http://www.w3.org/2000/09/xmldsig#"><xenc:EncryptedKey Recipient="name:e0fe1ccd-0fae-4477-92c4-2ce749605879"><xenc:EncryptionMethod Algorithm="http://www.w3.org/2001/04/xmlenc#rsa-1_5"/><dsig:KeyInfo><dsig:KeyName>e0fe1ccd-0fae-4477-92c4-2ce749605879</dsig:KeyName></dsig:KeyInfo><xenc:CipherData><xenc:CipherValue>XbaTKgL+LY4oe2TjMXtRAAquDx7og72wstQ5E1kI1aXYos6wqMUKTrJdE6+edB4bkWjdtAU9V1pqnRinmWWzBYZCpHxZnZGYWDM24NePpcW9jHD6D49taP+TQ6JxbeKGb61osoeq//mNVKua0bsQi5RidM9rZ816etADGItu59bp2e6ol2DpIBAGS12VIxptsVYhLzhFtcmg7F8FFjfQ6axo0ELwOPbMSanLMaDtr4Qsr6n82olmHE9cyrqUoEhn3koR2cxxuIUawDbI1pnQV9ZibbPN/lVwCNSNMpX13m43nlxzMAZ1vpkXbdghgP4w5Ir9WWRJUuW0xoDc/+WEhQ==</xenc:CipherValue></xenc:CipherData></xenc:EncryptedKey></dsig:KeyInfo><xenc:CipherData><xenc:CipherValue>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</xenc:CipherValue></xenc:CipherData></xenc:EncryptedData>`)
	res, err := citiAuth.Decrypt(fileBytes)
	require.NoError(t, err)

	doc := etree.NewDocument()
	err = doc.ReadFromString(res)
	require.NoError(t, err)

	expected := "5D8wmXg9IWmyrza3U6a+O5t3bjHhtUa2R41dwxp6wHVtLwZuwmU4HaZa3K6dm0Y/5eyYvl8G8D2KSXHefepNOTlu39jgMe8IfnJzMXSn1hd4LAUUyElE1SinyAbEBry2LGubaLNZEaswnu9d5wbEsvkb7sT3Q7I5+VHdTL0Zvfeecm4smbLmz03p32SsE4oxJbbKY0wPwWumn1HtyMtHWzJ9gBXNLjJIr15TGk9Jmtj6PvvHMH+lj0WZqog6ruiTQFaHl7X6ktf6cm4GJ66oFEjRlSlbejWBI4rB1jVEQVtdfFq/4W4vHWa3LYs/5ZQPou8YUm/eA8ZPx0Ty2klrCSd7/Jbh8Unqy6SBZOWQ0NSGAy1mYyerLmr8iC4DdxeX+xzdZn34pYxRgV0gKPG1dZH6LgpCRrwa9OPlPDgdM1QYnQyVbuu2hulXRwG2vdPqMrA9ejpxITCiDyOKYxR8kkPr//XOEdroHvTBysOvftuqnVdm/UkM0Ap9kV/18jAjyamfqFYRUf/aOJmzMlaXw/Wn1gTBVnLzUY3qwJqFfm26pAEXmMKV0ApooT/3DUnILzqYC8u/wvkrP0kpBWH/SgDuGs/eg7fNMKmJfHOWCq6vsL5VCZQSNisMXVN7wrZpQGUFYiC373/5PgK+jZEVKLrvyv4JpJdDdVSS/RSVkvqH7TrO83dKxFplOehgPQhDNm+Ym6gLrzxQUYnoYZBQpagWYJ6IGRmgA3vDI0vruaRkmyFSL385gPxfBz+mnQJmt+Qumb1puQGWMYm16JptVg=="
	root := doc.Root()
	accessTokenEl := root.FindElement("./access_token")
	require.NotNil(t, accessTokenEl)
	require.Equal(t, expected, accessTokenEl.Text())
}

func newCitiAuthenticationService(cfg partnerAuth.Config) *partnerAuth.CitiAuthenticationService {
	return partnerAuth.ProvideCitiAuthenticationService(cfg)
}

// this is LMWN rc cert and key file for connect with citi

var lmwnPrivateKeyFile = `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

var lmwnCertFile = `-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----`

var citiSigningCertFile = `-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----`

var citiEncryptCertFile = `-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----`

func mustReadCertFile(certBytes []byte) *x509.Certificate {
	certBlock, _ := pem.Decode(certBytes)
	cert, err := x509.ParseCertificate(certBlock.Bytes)
	if err != nil {
		panic(err)
	}

	return cert
}

func mustReadPrivateKeyFile(keyBytes []byte) *rsa.PrivateKey {
	keyBlock, _ := pem.Decode(keyBytes)
	key, err := x509.ParsePKCS1PrivateKey(keyBlock.Bytes)
	if err != nil {
		keyInf, err := x509.ParsePKCS8PrivateKey(keyBytes)
		if err != nil {
			panic(err)
		}

		keyType, ok := keyInf.(*rsa.PrivateKey)
		if !ok {
			panic("keyinf not ok")
		}
		key = keyType
	}

	return key
}
