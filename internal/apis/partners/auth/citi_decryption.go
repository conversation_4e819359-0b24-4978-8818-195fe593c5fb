package partnerAuth

import (
	"encoding/base64"
	"errors"
	"fmt"
	"strings"

	"github.com/beevik/etree"
)

func (auth *CitiAuthenticationService) Decrypt(plaintext []byte) (string, error) {
	documentToDecrypt := etree.NewDocument()
	if err := documentToDecrypt.ReadFromString(string(plaintext)); err != nil {
		return "", err
	}

	root := documentToDecrypt.Root()
	encryptedKey := root.FindElement("./KeyInfo/EncryptedKey")
	if encryptedKey == nil {
		return "", errors.New("unable to find xml tag \"KeyInfo/EncryptedKey\"")
	}

	keyCipherText, err := auth.getCiphertext(encryptedKey)
	if err != nil {
		return "", err
	}

	bytesData, err := auth.keyDecryptor.Decrypt(auth.LmwnPrivateKey, keyCipherText)
	if err != nil {
		return "", err
	}

	dataCiphertext, err := auth.getCiphertext(root)
	if err != nil {
		return "", err
	}
	resByte, err := auth.dataDecryptor.Decrypt(bytesData, dataCiphertext)
	if err != nil {
		return "", err
	}

	return string(resByte), nil
}

// getCiphertext - find an element `./CipherData/CipherValue` from input element
// then return an inner text as `Base64`
func (auth *CitiAuthenticationService) getCiphertext(encryptedKey *etree.Element) ([]byte, error) {
	ciphertextEl := encryptedKey.FindElement("./CipherData/CipherValue")
	if ciphertextEl == nil {
		return nil, fmt.Errorf("cannot find CipherData element containing a CipherValue element")
	}

	// return []byte(ciphertextEl.Text()), nil
	ciphertext, err := base64.StdEncoding.DecodeString(strings.TrimSpace(ciphertextEl.Text()))
	if err != nil {
		return nil, err
	}
	return ciphertext, nil
}
