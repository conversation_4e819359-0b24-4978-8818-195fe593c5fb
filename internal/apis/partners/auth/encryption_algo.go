package partnerAuth

import (
	"bytes"
	"crypto/cipher"
	"crypto/des"
	"crypto/rand"
	"crypto/rsa"
)

type AsymmetricEncryption interface {
	Encrypt(pubKey *rsa.PublicKey, plaintext []byte) ([]byte, error)
	Decrypt(privKey *rsa.PrivateKey, ciphertext []byte) ([]byte, error)
}

type PKCS1v15 struct{}

var _ AsymmetricEncryption = &PKCS1v15{}

func (alg *PKCS1v15) Encrypt(pubKey *rsa.PublicKey, plaintext []byte) ([]byte, error) {
	return rsa.EncryptPKCS1v15(rand.<PERSON>, pubKey, plaintext)
}

func (alg *PKCS1v15) Decrypt(privKey *rsa.PrivateKey, ciphertext []byte) ([]byte, error) {
	return rsa.DecryptPKCS1v15(rand.<PERSON>, privKey, ciphertext)
}

type SymmetricEncryption interface {
	Encrypt(key []byte, plaintext []byte) ([]byte, error)
	Decrypt(key []byte, ciphertext []byte) ([]byte, error)
}

type TripleDES struct{}

var _ SymmetricEncryption = &TripleDES{}

func (alg *TripleDES) Decrypt(key []byte, ciphertext []byte) ([]byte, error) {
	// Create TripleDES block cipher
	block, err := des.NewTripleDESCipher(key)
	if err != nil {
		panic(err)
	}

	// Create CBC mode cipher
	iv := make([]byte, block.BlockSize())
	mode := cipher.NewCBCDecrypter(block, iv)

	// Decrypt ciphertext
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	// Remove padding
	plaintext = PKCS5Unpadding(plaintext, block.BlockSize())

	return plaintext, nil
}

func (alg *TripleDES) Encrypt(key []byte, origData []byte) ([]byte, error) {
	block, err := des.NewTripleDESCipher(key)
	if err != nil {
		return nil, err
	}
	plaintext := PKCS5Padding(origData, block.BlockSize())

	// Create CBC mode cipher
	iv := make([]byte, block.BlockSize())
	mode := cipher.NewCBCEncrypter(block, iv)

	// Encrypt plaintext
	ciphertext := make([]byte, len(plaintext))
	mode.CryptBlocks(ciphertext, plaintext)

	cipherResult := make([]byte, 0)
	cipherResult = append(cipherResult, iv...)
	cipherResult = append(cipherResult, ciphertext...)
	return cipherResult, nil
}

// ECB PKCS5Padding
func PKCS5Padding(ciphertext []byte, blockSize int) []byte {
	padding := blockSize - len(ciphertext)%blockSize
	padtext := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(ciphertext, padtext...)
}

// ECB PKCS5Unpadding
func PKCS5Unpadding(origData []byte, blockSize int) []byte {
	length := len(origData)
	unpadding := int(origData[length-1])
	return origData[blockSize:(length - unpadding)]
}
