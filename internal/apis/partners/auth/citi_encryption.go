package partnerAuth

import (
	"crypto/rand"
	"encoding/base64"

	"github.com/beevik/etree"
)

func (auth *CitiAuthenticationService) Encrypt(plaintext []byte) (string, error) {

	documentToEncrypt := etree.NewDocument()
	if err := documentToEncrypt.ReadFromString(string(plaintext)); err != nil {
		return "", err
	}

	// generate a key
	key := make([]byte, 24)
	if _, err := rand.Reader.Read(key); err != nil {
		return "", err
	}

	keyInfoEl := etree.NewElement("KeyInfo")
	keyInfoEl.CreateAttr("xmlns", "http://www.w3.org/2000/09/xmldsig#")
	encryptedKey := keyInfoEl.CreateElement("EncryptedKey")
	encryptedKey.CreateAttr("xmlns", "http://www.w3.org/2001/04/xmlenc#")
	// --- Encrypted Data ---
	encrypter := &TripleDES{}
	encryptedCipher, err := encrypter.Encrypt(key, plaintext)
	if err != nil {
		return "", err
	}
	encryptedDataEl := etree.NewElement("EncryptedData")
	encryptedDataEl.CreateAttr("xmlns", "http://www.w3.org/2001/04/xmlenc#")
	em := encryptedDataEl.CreateElement("EncryptionMethod")
	em.CreateAttr("Algorithm", "http://www.w3.org/2001/04/xmlenc#tripledes-cbc")
	cipherData := encryptedDataEl.CreateElement("CipherData")
	cipherData.CreateElement("CipherValue").SetText(base64.StdEncoding.EncodeToString(encryptedCipher))
	encryptedDataEl.InsertChildAt(encryptedDataEl.FindElement("./CipherData").Index(), keyInfoEl)
	// --- Encrypted Key ---
	encryptionMethodEl := encryptedKey.CreateElement("EncryptionMethod")
	encryptionMethodEl.CreateAttr("Algorithm", "http://www.w3.org/2001/04/xmlenc#rsa-1_5")
	keyEncrypter := &PKCS1v15{}
	buf, err := keyEncrypter.Encrypt(auth.citiPubKey, key)
	if err != nil {
		return "", err
	}
	cipherDataKey := encryptedKey.CreateElement("CipherData")
	bufText := base64.StdEncoding.EncodeToString(buf)
	cipherDataKey.CreateElement("CipherValue").SetText(bufText)
	// ---
	encryptedDataEl.CreateAttr("Type", "http://www.w3.org/2001/04/xmlenc#Element")

	doc := etree.NewDocument()
	doc.SetRoot(encryptedDataEl)
	str, err := doc.WriteToString()
	if err != nil {
		return "", err
	}
	return str, nil
}
