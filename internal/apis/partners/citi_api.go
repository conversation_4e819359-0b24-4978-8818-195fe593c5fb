package partners

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	absapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

// CitiAPI provides an api to let CITI Bank call to us.
type CitiAPI struct {
	Notifier                 service.Notifier
	DriverRepo               repository.DriverRepository
	DriverTransactionService payment.DriverTransactionService
	TransactionService       payment.TransactionService
	citiConfig               *AtomicCitiConfig
}

func (api *CitiAPI) TopupNotificationV2(gctx *gin.Context) {
	method := "CitiTopupNotificationV2"
	// TODO: Check and remove WithoutCancel if it's not needed
	ctx := context.WithoutCancel(gctx.Request.Context())
	var req TopupNotificationRequestV2
	if err := gctx.ShouldBindJSON(&req); err != nil {
		logx.Error().Msgf("cannot bind json request: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	// Ignore false alarm for wongnai account and others
	if shouldIgnoreCitiTopupFalseAlarm(req, api.citiConfig.Get()) {
		logx.Warn().Str(logutil.Method, method).Msgf("Ignore topup request account %v, name %v", req.Debtor.Account, req.Debtor.Name)
		apiutil.OK(gctx, gin.H{})
		return
	}

	if req.Transaction.TransactionID == "" {
		logx.Error().Msgf("Citi Topup Notification error: transactionID is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("transaction TransactionID is empty")))
		return
	}

	if err := checkTransactionStatus(req, api.citiConfig.Get()); err != nil {
		logx.Error().Msgf("Citi Topup Notification error: Transaction is a duplicate.")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("transaction is a duplicate")))
		return
	}

	transactionDateTime, _ := getTransactionDateTimeAndTransRefId(req.Transaction.Status.DateTime, req.Transaction.TransactionID)

	existsTrans, err := api.TransactionService.ExistsTransactionByRefID(ctx, req.Transaction.TransactionID)
	if err != nil {
		logx.Error().Msgf("Citi Topup Notification error: cannot check exists transaction")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("cannot check exists transaction")))
		return
	}

	if existsTrans {
		logx.Error().Msgf("Citi Topup Notification error: duplicate TransactionID, %s", req.Transaction.TransactionID)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("duplicate TransactionID")))
		return
	}

	creditVirtualAccount := req.Creditor.VirtualAccount

	if creditVirtualAccount == "" {
		logx.Error().Msgf("Citi Topup Notification error: Driver PayeeAccountNumber is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("driver CreditVirtualAccount is empty")))
		return
	}
	amount := req.Transaction.InstructedAmount
	if amount == 0 {
		logx.Error().Msgf("Citi Topup Notification error: TransactionAmount is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("TransactionAmount is empty")))
		return
	}
	var driverID string
	transactionStatus := model.SuccessTransactionStatus
	driver, err := api.DriverRepo.GetProfileByRefID(ctx, creditVirtualAccount)
	if err != nil {
		logx.Error().Msgf("GetProfileByRefID err: %v", err)
		transactionStatus = model.UnmatchedTransactionStatus
		driverID = ""
	} else {
		driverID = driver.DriverID
	}

	transactionInfo := model.NewPurchaseCreditWithSourceTransactionInfo(model.TransactionSourceCITI, driverID, types.NewMoney(amount))
	transactionInfo.RequestedTime = time.Now().UTC()
	transactionInfo.VanRefID = creditVirtualAccount
	transactionInfo.TransactionDateTime = transactionDateTime
	transactionInfo.BankRefID = req.Transaction.TransactionID
	transactionInfo.RemarkCreatedBy = model.ActorSystemCreated
	transInfoList := []model.TransactionInfo{*transactionInfo}

	_, _, err = api.DriverTransactionService.ProcessDriverTransaction(
		ctx,
		driverID,
		model.UserTransactionChannel,
		model.CreditTopUpTransactionAction,
		transactionStatus,
		service.TransactionInfos(transInfoList...),
		service.WithTransactionOptions(model.WithTransactionRefID(req.Transaction.TransactionID)),
	)
	if err != nil {
		logx.Error().Msgf("Citi - add driver transaction error: driverID=%s TransactionAmount=%v TransactionID=%s time=%v err=%v", driverID, amount, req.Transaction.TransactionID, timeutil.BangkokNow(), err)
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, fmt.Errorf("AddDriverTransaction: %v", err)))
		return
	}

	if transactionStatus == model.SuccessTransactionStatus {
		dateTime := strings.Split(timeutil.BangkokNow().Format("01-02-2006 15:04:05"), " ")
		if err = api.Notifier.Notify(ctx, []string{driverID}, model.EventTopUpNotification(dateTime, amount)); err != nil {
			logrus.WithFields(logrus.Fields{
				"error": err.Error(),
			}).Errorf("Notify Partners Top Up error driverID: %s, transactionID: %s", driverID, req.Transaction.TransactionID)
		}
	}

	apiutil.OK(gctx, gin.H{})
}

func checkTransactionStatus(req TopupNotificationRequestV2, citiConfig CitiConfig) error {
	if !utils.StrContains(req.Transaction.Status.Code, citiConfig.RejectStatusCode) {
		return nil
	}

	for _, reason := range req.Transaction.Reasons {
		if utils.StrContains(reason.Code, citiConfig.RejectReasonCode) {
			logrus.Error("Citi Topup Notification error: Transaction is a duplicate.")
			return apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("Transaction is a duplicate."))
		}
	}

	return nil
}
func (api *CitiAPI) TopupNotification(gctx *gin.Context) {
	var req TopupNotificationRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		logx.Error().Msgf("cannot bind json request: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if req.PushNotificationFromCiti.TxnRefNo == "" {
		logx.Error().Msgf("Citi Topup Notification error: transaction reference ID is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("transaction reference ID is empty")))
		return
	}

	transactionDateTime, _ := getTransactionDateTimeAndTransRefId(req.PushNotificationFromCiti.TimeStamp, req.PushNotificationFromCiti.TxnRefNo)

	existsTrans, err := api.TransactionService.ExistsTransactionByRefID(gctx, req.PushNotificationFromCiti.TxnRefNo)
	if err != nil {
		logx.Error().Msgf("Citi Topup Notification error: cannot check exists transaction")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("cannot check exists transaction")))
		return
	}

	if existsTrans {
		logx.Error().Msgf("Citi Topup Notification error: duplicate reference id, %s", req.PushNotificationFromCiti.TxnRefNo)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("duplicate reference id")))
		return
	}

	if req.PushNotificationFromCiti.PayeeID == "" {
		logx.Error().Msgf("Citi Topup Notification error: Driver reference ID is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("driver reference ID is empty")))
		return
	}
	if req.PushNotificationFromCiti.TransactionAmount == 0 {
		logx.Error().Msgf("Citi Topup Notification error: Amount is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("Amount is empty")))
		return
	}
	var driverID string
	transactionStatus := model.SuccessTransactionStatus
	driver, err := api.DriverRepo.GetProfileByRefID(gctx, req.PushNotificationFromCiti.PayeeID)
	if err != nil {
		logx.Error().Msgf("GetProfileByRefID err: %v", err)
		transactionStatus = model.UnmatchedTransactionStatus
		driverID = ""
	} else {
		driverID = driver.DriverID
	}

	transactionInfo := model.NewPurchaseCreditWithSourceTransactionInfo(model.TransactionSourceCITI, driverID, types.NewMoney(req.PushNotificationFromCiti.TransactionAmount))
	transactionInfo.RequestedTime = time.Now().UTC()
	transactionInfo.VanRefID = req.PushNotificationFromCiti.PayeeID
	transactionInfo.TransactionDateTime = transactionDateTime
	transactionInfo.BankRefID = req.PushNotificationFromCiti.TxnRefNo
	transactionInfo.RemarkCreatedBy = model.ActorSystemCreated
	transInfoList := []model.TransactionInfo{*transactionInfo}

	_, _, err = api.DriverTransactionService.ProcessDriverTransaction(
		gctx,
		driverID,
		model.UserTransactionChannel,
		model.CreditTopUpTransactionAction,
		transactionStatus,
		service.TransactionInfos(transInfoList...),
		service.WithTransactionOptions(model.WithTransactionRefID(req.PushNotificationFromCiti.TxnRefNo)),
	)
	if err != nil {
		logx.Error().Msgf("Citi - add driver transaction error: driverId=%s amount=%v CitiTranRef=%s time=%v err=%v", driverID, req.PushNotificationFromCiti.TransactionAmount, req.PushNotificationFromCiti.TxnRefNo, timeutil.BangkokNow(), err)
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, fmt.Errorf("AddDriverTransaction: %v", err)))
		return
	}

	if transactionStatus == model.SuccessTransactionStatus {
		dateTime := strings.Split(timeutil.BangkokNow().Format("01-02-2006 15:04:05"), " ")
		if err = api.Notifier.Notify(gctx, []string{driverID}, model.EventTopUpNotification(dateTime, req.PushNotificationFromCiti.TransactionAmount)); err != nil {
			logrus.WithFields(logrus.Fields{
				"error": err.Error(),
			}).Errorf("Notify Partners Top Up error driverID: %s, transactionID: %s", driverID, req.PushNotificationFromCiti.TxnRefNo)
		}
	}

	apiutil.OK(gctx, gin.H{})
}

func shouldIgnoreCitiTopupFalseAlarm(req TopupNotificationRequestV2, cfg CitiConfig) bool {
	if !cfg.CitiTopupIgnoreDebtorAccounts.IsInitialized() {
		return false
	}

	return cfg.CitiTopupIgnoreDebtorAccounts.Has(req.Debtor.Account)
}

func getTransactionDateTimeAndTransRefId(tTime string, transactionID string) (time.Time, string) {
	transactionDateTime := timeutil.BangkokNow()
	dateTime, err := time.Parse(time.RFC3339, tTime)
	if err != nil {
		logx.Error().Msgf("Citi Topup Notification Parse TimeStamp Error: %v", err)
	} else {
		transactionDateTime = dateTime.In(timeutil.BangkokLocation())
	}
	transRefID := fmt.Sprintf("%s%s", transactionDateTime.Format("**************"), transactionID)
	return transactionDateTime, transRefID
}

func ProvideCitiAPI(notify service.Notifier, driverRepository repository.DriverRepository, driverTransactionService payment.DriverTransactionService, transactionService payment.TransactionService, citiConfig *AtomicCitiConfig) *CitiAPI {
	return &CitiAPI{
		Notifier:                 notify,
		DriverTransactionService: driverTransactionService,
		DriverRepo:               driverRepository,
		TransactionService:       transactionService,
		citiConfig:               citiConfig,
	}
}
