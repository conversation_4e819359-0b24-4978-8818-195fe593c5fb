package partners_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment/mock_payment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/sets"
)

func TestCitiApi_TopupNotification(t *testing.T) {
	t.Parallel()

	req := func(req partners.TopupNotificationRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/partners/citi/hooks", testutil.JSON(req))
	}
	d := &model.Driver{DriverID: "test-driverID"}

	t.Run("Should return status code 200 and topup credit correctly", func(tt *testing.T) {
		r := partners.TopupNotificationRequest{PushNotificationFromCiti: partners.PushNotificationFromCiti{
			TimeStamp:         "2022-06-27T13:06:18+05:30",
			TxnRefNo:          "BNK4806401652263",
			PayeeID:           "4717058148",
			TransactionAmount: 999,
		}}
		ctx, recorder := req(r)
		api, deps, finish := newCitiApiAPI(tt)
		defer finish()

		deps.TransactionService.EXPECT().ExistsTransactionByRefID(ctx, "BNK4806401652263").Return(false, nil)
		deps.DriverRepo.EXPECT().GetProfileByRefID(ctx, gomock.Any()).Return(d, nil)
		deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
		deps.Notifier.EXPECT().Notify(ctx, gomock.Any(), gomock.Any()).Return(nil)

		api.TopupNotification(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should return status code 200 and parse datetime error", func(tt *testing.T) {
		r := partners.TopupNotificationRequest{PushNotificationFromCiti: partners.PushNotificationFromCiti{
			TimeStamp:         "2022-0err-27T13:06:18+05:30",
			TxnRefNo:          "BNK4806401652263",
			PayeeID:           "4717058148",
			TransactionAmount: 999,
		}}
		ctx, recorder := req(r)
		api, deps, finish := newCitiApiAPI(tt)
		defer finish()

		deps.TransactionService.EXPECT().ExistsTransactionByRefID(ctx, gomock.Any()).Return(false, nil)
		deps.DriverRepo.EXPECT().GetProfileByRefID(ctx, gomock.Any()).Return(d, nil)
		deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
		deps.Notifier.EXPECT().Notify(ctx, gomock.Any(), gomock.Any()).Return(nil)

		api.TopupNotification(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestCitiApi_TopupNotificationV2(t *testing.T) {
	t.Parallel()

	req := func(req partners.TopupNotificationRequestV2) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/partners/citi/v2/hooks", testutil.JSON(req))
	}
	d := &model.Driver{DriverID: "test-driverID"}

	t.Run("Should return status code 200 and topup credit correctly", func(tt *testing.T) {
		r := partners.TopupNotificationRequestV2{
			Creditor: partners.ExpressCreditor{
				VirtualAccount: "**********",
			},
			Transaction: partners.ExpressTransaction{
				InstructedAmount: 20,
				TransactionID:    "166677",
				Status: partners.ExpressStatus{
					DateTime: "2022-09-13T08:23:56.223Z",
					Code:     "ACCC",
				},
			},
		}
		ctx, recorder := req(r)
		api, deps, finish := newCitiApiAPI(tt, partners.CitiConfig{CitiTopupIgnoreDebtorAccounts: sets.OfString{Of: sets.New([]string{"ignore"}...)}})
		defer finish()

		deps.TransactionService.EXPECT().ExistsTransactionByRefID(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.DriverRepo.EXPECT().GetProfileByRefID(gomock.Any(), gomock.Any()).Return(d, nil)
		deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
		deps.Notifier.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		api.TopupNotificationV2(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should return status code 200 and parse datetime error", func(tt *testing.T) {
		r := partners.TopupNotificationRequestV2{
			Creditor: partners.ExpressCreditor{
				VirtualAccount: "**********",
			},
			Transaction: partners.ExpressTransaction{
				InstructedAmount: 20,
				TransactionID:    "166677",
				Status: partners.ExpressStatus{
					DateTime: "2022-errr-13T08:23:56.223Z",
					Code:     "ACCC",
				},
			},
		}
		ctx, recorder := req(r)
		api, deps, finish := newCitiApiAPI(tt)
		defer finish()

		deps.TransactionService.EXPECT().ExistsTransactionByRefID(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.DriverRepo.EXPECT().GetProfileByRefID(gomock.Any(), gomock.Any()).Return(d, nil)
		deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
		deps.Notifier.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		api.TopupNotificationV2(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should return status code 200 and ignore account", func(tt *testing.T) {
		r := partners.TopupNotificationRequestV2{
			Creditor: partners.ExpressCreditor{
				Account:        "",
				VirtualAccount: "**********",
			},
			Debtor: partners.ExpressDebtor{
				Account: "ignore",
				Name:    "ignore account",
			},
			Transaction: partners.ExpressTransaction{
				InstructedAmount: 20,
				TransactionID:    "166677",
				Status: partners.ExpressStatus{
					DateTime: "2022-09-13T08:23:56.223Z",
					Code:     "ACCC",
				},
			},
		}
		ctx, recorder := req(r)
		api, _, finish := newCitiApiAPI(tt, partners.CitiConfig{CitiTopupIgnoreDebtorAccounts: sets.OfString{Of: sets.New([]string{"ignore"}...)}})
		defer finish()

		api.TopupNotificationV2(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestCitiApi_TopupNotificationV2_CheckTransactionStatus(t *testing.T) {
	t.Parallel()
	req := func(req partners.TopupNotificationRequestV2) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/partners/citi/v2/hooks", testutil.JSON(req))
	}
	d := &model.Driver{DriverID: "test-driverID"}
	tests := []struct {
		name          string
		req           partners.TopupNotificationRequestV2
		citiConfig    partners.CitiConfig
		expectedError int
	}{
		{
			name: "Status code not in RejectStatusCode",
			req: partners.TopupNotificationRequestV2{
				Creditor: partners.ExpressCreditor{
					VirtualAccount: "**********",
				},
				Transaction: partners.ExpressTransaction{
					InstructedAmount: 20,
					TransactionID:    "166677",
					Status: partners.ExpressStatus{
						DateTime: "2022-09-13T08:23:56.223Z",
						Code:     "ACCC",
					},
					Reasons: []partners.ExpressReason{{Code: ""}},
				},
			},
			citiConfig:    partners.CitiConfig{RejectStatusCode: []string{"RJCT"}},
			expectedError: 200,
		},
		{
			name: "Status code in RejectStatusCode but no reason code in RejectReasonCode",
			req: partners.TopupNotificationRequestV2{
				Creditor: partners.ExpressCreditor{
					VirtualAccount: "**********",
				},
				Transaction: partners.ExpressTransaction{
					InstructedAmount: 20,
					TransactionID:    "166677",
					Status:           partners.ExpressStatus{DateTime: "2022-09-13T08:23:56.223Z", Code: "RJCT"},
					Reasons:          []partners.ExpressReason{{Code: "OTHER"}},
				},
			},
			citiConfig:    partners.CitiConfig{RejectStatusCode: []string{"RJCT"}, RejectReasonCode: []string{"DUPL"}},
			expectedError: 200,
		},
		{
			name: "Status code in RejectStatusCode and reason code in RejectReasonCode",
			req: partners.TopupNotificationRequestV2{
				Creditor: partners.ExpressCreditor{
					VirtualAccount: "**********",
				},
				Transaction: partners.ExpressTransaction{
					InstructedAmount: 20,
					TransactionID:    "166677",
					Status:           partners.ExpressStatus{DateTime: "2022-09-13T08:23:56.223Z", Code: "RJCT"},
					Reasons:          []partners.ExpressReason{{Code: "DUPL"}},
				},
			},
			citiConfig:    partners.CitiConfig{RejectStatusCode: []string{"RJCT"}, RejectReasonCode: []string{"DUPL"}},
			expectedError: 400,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gctx, recorder := req(tt.req)
			ctx := context.WithoutCancel(gctx.Request.Context())
			api, deps, finish := newCitiApiAPI(t, tt.citiConfig)
			defer finish()

			if tt.expectedError == 200 {
				deps.TransactionService.EXPECT().ExistsTransactionByRefID(ctx, gomock.Any()).Return(false, nil)
				deps.DriverRepo.EXPECT().GetProfileByRefID(ctx, gomock.Any()).Return(d, nil)
				deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
					gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
				deps.Notifier.EXPECT().Notify(ctx, gomock.Any(), gomock.Any()).Return(nil)
			}

			api.TopupNotificationV2(gctx)

			require.Equal(t, tt.expectedError, recorder.Code)
		})
	}
}

type CitiAPIDeps struct {
	Notifier                 *mock_service.MockNotifier
	DriverRepo               *mock_repository.MockDriverRepository
	DriverTransactionService *mock_payment.MockDriverTransactionService
	TransactionService       *mock_payment.MockTransactionService
}

func newCitiApiAPI(r gomock.TestReporter, config ...partners.CitiConfig) (*partners.CitiAPI, CitiAPIDeps, func()) {
	ctrl := gomock.NewController(r)
	deps := CitiAPIDeps{
		mock_service.NewMockNotifier(ctrl),
		mock_repository.NewMockDriverRepository(ctrl),
		mock_payment.NewMockDriverTransactionService(ctrl),
		mock_payment.NewMockTransactionService(ctrl),
	}

	c := partners.CitiConfig{}
	if len(config) > 0 {
		c = config[0]
	}

	return partners.ProvideCitiAPI(
			deps.Notifier,
			deps.DriverRepo,
			deps.DriverTransactionService,
			deps.TransactionService,
			&partners.AtomicCitiConfig{Config: c},
		), deps,
		func() { ctrl.Finish() }
}
