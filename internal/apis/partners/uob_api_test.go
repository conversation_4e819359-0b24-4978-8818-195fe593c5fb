package partners_test

import (
	"bytes"
	"context"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"math/big"
	"net/http"
	"net/url"
	"strings"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment/mock_payment"
	"git.wndv.co/lineman/fleet-distribution/internal/cryptography"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

const (
	aesAdditionalData = "lm-partner-uob.line-apps-rc.com"
	aesKey            = "********************************"
	aesIv             = "123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456"
)

func TestUOBApi_TopupNotification(t *testing.T) {

	t.Run("Should return status code 200 and topup credit correctly", func(tt *testing.T) {
		nrm, err := getRequestPayload()
		require.Nil(t, err)
		key := []byte(aesKey)
		iv := []byte(aesIv)
		pkey, err := getPkey()
		require.Nil(t, err)
		pub, err := getPubKeyFromPkey(pkey)
		require.Nil(t, err)
		pkeyStr, err := savePEMPrivatePKCS1Key(pkey)
		require.Nil(t, err)
		uobGenerator, err := cryptography.NewRsaGenerator(2048)
		require.Nil(t, err)
		uobPub, err := uobGenerator.GenerateX509Certificate(getDefaultCaCert())
		require.Nil(t, err)
		c := partners.Config{LMWNPrivate: pkeyStr, LMWNPublicKey: pub, UOBCertificate: uobPub, GcmAdditionalData: aesAdditionalData, UOBTopUpEnabled: true}
		api, deps, finish := newUOBApiAPI(tt, c)
		defer finish()
		aes := cryptography.AesGCM{GcmStandardNonceSize: 96, GcmKeySize: 32}
		encryptedPayload, err := aes.Encrypt(nrm, key, iv, []byte(aesAdditionalData))
		require.Nil(t, err)
		encryptedSessionKey, err := cryptography.RSAEncrypt([]byte(base64.StdEncoding.EncodeToString(key)), []byte(pub))
		require.Nil(t, err)

		form := url.Values{}
		form.Add("encryptedPayload", base64.StdEncoding.EncodeToString(encryptedPayload))
		form.Add("encryptedSessionKey", string(encryptedSessionKey))
		form.Add("iv", base64.StdEncoding.EncodeToString(iv))
		sign, err := cryptography.RSASign(string(encryptedPayload), uobGenerator.PrivateKey)
		require.Nil(t, err)
		form.Add("payloadSignature", sign)

		ctx, recorder := testutil.TestRequestContext("POST", "partner/uob/hooks", strings.NewReader(form.Encode()))
		ctx.Request.Header.Add("content-type", "application/x-www-form-urlencoded")
		ctx.Request.Header.Add("X-B3-TraceId", "1234567890")

		d := &model.Driver{DriverID: "test-driverID"}
		deps.TransactionService.EXPECT().ExistsTransactionByRefID(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.DriverRepo.EXPECT().GetProfileByUOBRefID(gomock.Any(), gomock.Any()).Return(d, nil)
		deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
		deps.Notifier.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		api.TopupNotification(ctx)

		var actual partners.UobResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should return status code 200 and topup credit without verify signature correctly", func(tt *testing.T) {
		nrm, err := getRequestPayload()
		require.Nil(t, err)
		key := []byte(aesKey)
		iv := []byte(aesIv)
		pkey, err := getPkey()
		require.Nil(t, err)
		pub, err := getPubKeyFromPkey(pkey)
		require.Nil(t, err)
		pkeyStr, err := savePEMPrivatePKCS1Key(pkey)
		require.Nil(t, err)
		c := partners.Config{LMWNPrivate: pkeyStr, LMWNPublicKey: pub, GcmAdditionalData: aesAdditionalData, UOBTopUpEnabled: true}
		api, deps, finish := newUOBApiAPI(tt, c)
		defer finish()
		aes := cryptography.AesGCM{GcmStandardNonceSize: 96, GcmKeySize: 32}
		encryptedPayload, err := aes.Encrypt(nrm, key, iv, []byte(aesAdditionalData))
		require.Nil(t, err)
		encryptedSessionKey, err := cryptography.RSAEncrypt([]byte(base64.StdEncoding.EncodeToString(key)), []byte(pub))
		require.Nil(t, err)
		form := url.Values{}
		form.Add("encryptedPayload", base64.StdEncoding.EncodeToString(encryptedPayload))
		form.Add("encryptedSessionKey", string(encryptedSessionKey))
		form.Add("iv", base64.StdEncoding.EncodeToString(iv))
		form.Add("payloadSignature", "")

		ctx, recorder := testutil.TestRequestContext("POST", "partner/uob/hooks", strings.NewReader(form.Encode()))
		ctx.Request.Header.Add("content-type", "application/x-www-form-urlencoded")

		d := &model.Driver{DriverID: "test-driverID"}
		deps.TransactionService.EXPECT().ExistsTransactionByRefID(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.DriverRepo.EXPECT().GetProfileByUOBRefID(gomock.Any(), gomock.Any()).Return(d, nil)
		deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
		deps.Notifier.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		api.TopupNotification(ctx)

		var actual partners.UobResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should return status code 200 and topup event doesn't equal credit", func(tt *testing.T) {
		nr := partners.UobNotificationRequest{Event: partners.DEBIT}
		nrm, err := json.Marshal(nr)
		require.Nil(t, err)
		key := []byte(aesKey)
		iv := []byte(aesIv)
		pkey, err := getPkey()
		require.Nil(t, err)
		pub, err := getPubKeyFromPkey(pkey)
		require.Nil(t, err)
		pkeyStr, err := savePEMPrivatePKCS1Key(pkey)
		require.Nil(t, err)
		c := partners.Config{LMWNPrivate: pkeyStr, LMWNPublicKey: pub, GcmAdditionalData: aesAdditionalData, UOBTopUpEnabled: true}
		api, _, finish := newUOBApiAPI(tt, c)
		defer finish()
		aes := cryptography.AesGCM{GcmStandardNonceSize: 96, GcmKeySize: 32}
		encryptedPayload, err := aes.Encrypt(nrm, key, iv, []byte(aesAdditionalData))
		require.Nil(t, err)
		encryptedSessionKey, err := cryptography.RSAEncrypt([]byte(base64.StdEncoding.EncodeToString(key)), []byte(pub))
		require.Nil(t, err)
		form := url.Values{}
		form.Add("encryptedPayload", base64.StdEncoding.EncodeToString(encryptedPayload))
		form.Add("encryptedSessionKey", string(encryptedSessionKey))
		form.Add("iv", base64.StdEncoding.EncodeToString(iv))
		form.Add("payloadSignature", "")
		ctx, recorder := testutil.TestRequestContext("POST", "partner/uob/hooks", strings.NewReader(form.Encode()))
		ctx.Request.Header.Add("content-type", "application/x-www-form-urlencoded")

		api.TopupNotification(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should return status code 501 when uob notification is disabled", func(tt *testing.T) {
		c := partners.Config{UOBTopUpEnabled: false}
		api, _, finish := newUOBApiAPI(tt, c)
		defer finish()
		form := url.Values{}
		form.Add("payloadSignature", "sign")

		ctx, recorder := testutil.TestRequestContext("POST", "partner/uob/hooks", strings.NewReader(form.Encode()))
		ctx.Request.Header.Add("content-type", "application/x-www-form-urlencoded")
		api.TopupNotification(ctx)

		require.Equal(tt, http.StatusNotImplemented, recorder.Code)
	})

	t.Run("Should return status code 400 when bind request error", func(tt *testing.T) {
		c := partners.Config{UOBTopUpEnabled: true}
		api, _, finish := newUOBApiAPI(tt, c)
		defer finish()
		form := url.Values{}
		ctx, recorder := testutil.TestRequestContext("POST", "partner/uob/hooks", strings.NewReader(form.Encode()))
		ctx.Request.Header.Add("content-type", "application/x-www-form-urlencoded")
		api.TopupNotification(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return status code 200 and topup credit without verify signature if disabled", func(tt *testing.T) {
		nrm, err := getRequestPayload()
		require.Nil(t, err)
		key := []byte(aesKey)
		iv := []byte(aesIv)
		pkey, err := getPkey()
		require.Nil(t, err)
		pub, err := getPubKeyFromPkey(pkey)
		require.Nil(t, err)
		pkeyStr, err := savePEMPrivatePKCS1Key(pkey)
		require.Nil(t, err)
		c := partners.Config{LMWNPrivate: pkeyStr, LMWNPublicKey: pub, GcmAdditionalData: aesAdditionalData, UOBTopUpEnabled: true}
		api, deps, finish := newUOBApiAPI(tt, c)
		defer finish()
		aes := cryptography.AesGCM{GcmStandardNonceSize: 96, GcmKeySize: 32}
		encryptedPayload, err := aes.Encrypt(nrm, key, iv, []byte(aesAdditionalData))
		require.Nil(t, err)
		encryptedSessionKey, err := cryptography.RSAEncrypt([]byte(base64.StdEncoding.EncodeToString(key)), []byte(pub))
		require.Nil(t, err)
		form := url.Values{}
		form.Add("encryptedPayload", base64.StdEncoding.EncodeToString(encryptedPayload))
		form.Add("encryptedSessionKey", string(encryptedSessionKey))
		form.Add("iv", base64.StdEncoding.EncodeToString(iv))
		form.Add("payloadSignature", "fake payload signature")

		ctx, recorder := testutil.TestRequestContext("POST", "partner/uob/hooks", strings.NewReader(form.Encode()))
		ctx.Request.Header.Add("content-type", "application/x-www-form-urlencoded")

		d := &model.Driver{DriverID: "test-driverID"}
		deps.TransactionService.EXPECT().ExistsTransactionByRefID(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.DriverRepo.EXPECT().GetProfileByUOBRefID(gomock.Any(), gomock.Any()).Return(d, nil)
		deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
		deps.Notifier.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		api.TopupNotification(ctx)

		var actual partners.UobResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should return status code 400 when OurReference is empty", func(tt *testing.T) {
		nd := partners.UobNotificationData{NotificationId: "AKB48NMB48", YourReference: "BNK48GCM48", Amount: 48.0}
		nr := partners.UobNotificationRequest{Event: partners.CREDIT, Data: nd}
		nrm, err := json.Marshal(nr)
		require.Nil(t, err)
		key := []byte(aesKey)
		iv := []byte(aesIv)
		pkey, err := getPkey()
		require.Nil(t, err)
		pub, err := getPubKeyFromPkey(pkey)
		require.Nil(t, err)
		pkeyStr, err := savePEMPrivatePKCS1Key(pkey)
		require.Nil(t, err)
		c := partners.Config{LMWNPrivate: pkeyStr, LMWNPublicKey: pub, GcmAdditionalData: aesAdditionalData, UOBTopUpEnabled: true}
		api, _, finish := newUOBApiAPI(tt, c)
		defer finish()
		aes := cryptography.AesGCM{GcmStandardNonceSize: 96, GcmKeySize: 32}
		encryptedPayload, err := aes.Encrypt(nrm, key, iv, []byte(aesAdditionalData))
		require.Nil(t, err)
		encryptedSessionKey, err := cryptography.RSAEncrypt([]byte(base64.StdEncoding.EncodeToString(key)), []byte(pub))
		require.Nil(t, err)
		form := url.Values{}
		form.Add("encryptedPayload", base64.StdEncoding.EncodeToString(encryptedPayload))
		form.Add("encryptedSessionKey", string(encryptedSessionKey))
		form.Add("iv", base64.StdEncoding.EncodeToString(iv))
		form.Add("payloadSignature", "")
		ctx, recorder := testutil.TestRequestContext("POST", "partner/uob/hooks", strings.NewReader(form.Encode()))
		ctx.Request.Header.Add("content-type", "application/x-www-form-urlencoded")

		api.TopupNotification(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return status code 200 and transaction status is unmatche and driver id is empty when not found driver", func(tt *testing.T) {
		nrm, err := getRequestPayload()
		require.Nil(t, err)
		key := []byte(aesKey)
		iv := []byte(aesIv)
		pkey, err := getPkey()
		require.Nil(t, err)
		pub, err := getPubKeyFromPkey(pkey)
		require.Nil(t, err)
		pkeyStr, err := savePEMPrivatePKCS1Key(pkey)
		require.Nil(t, err)
		uobGenerator, err := cryptography.NewRsaGenerator(2048)
		require.Nil(t, err)
		uobPub, err := uobGenerator.GenerateX509Certificate(getDefaultCaCert())
		require.Nil(t, err)
		c := partners.Config{LMWNPrivate: pkeyStr, LMWNPublicKey: pub, UOBCertificate: uobPub, GcmAdditionalData: aesAdditionalData, UOBTopUpEnabled: true}
		api, deps, finish := newUOBApiAPI(tt, c)
		defer finish()
		aes := cryptography.AesGCM{GcmStandardNonceSize: 96, GcmKeySize: 32}
		encryptedPayload, err := aes.Encrypt(nrm, key, iv, []byte(aesAdditionalData))
		require.Nil(t, err)
		encryptedSessionKey, err := cryptography.RSAEncrypt([]byte(base64.StdEncoding.EncodeToString(key)), []byte(pub))
		require.Nil(t, err)

		form := url.Values{}
		form.Add("encryptedPayload", base64.StdEncoding.EncodeToString(encryptedPayload))
		form.Add("encryptedSessionKey", string(encryptedSessionKey))
		form.Add("iv", base64.StdEncoding.EncodeToString(iv))
		sign, err := cryptography.RSASign(string(encryptedPayload), uobGenerator.PrivateKey)
		require.Nil(t, err)
		form.Add("payloadSignature", sign)

		ctx, recorder := testutil.TestRequestContext("POST", "partner/uob/hooks", strings.NewReader(form.Encode()))
		ctx.Request.Header.Add("content-type", "application/x-www-form-urlencoded")
		ctx.Request.Header.Add("X-B3-TraceId", "1234567890")

		d := &model.Driver{}
		deps.TransactionService.EXPECT().ExistsTransactionByRefID(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.DriverRepo.EXPECT().GetProfileByUOBRefID(gomock.Any(), gomock.Any()).Return(d, errors.New("driver ID is empty"))
		deps.DriverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(), "", gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverID string, _ model.TransactionChannel, _ model.TransactionAction, status model.TransactionStatus, _ service.TransactionInfosBuilder, _ ...func(option *service.ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error) {
				assert.Equal(tt, model.UnmatchedTransactionStatus, status)
				return model.DriverTransaction{}, []model.Transaction{}, nil
			})
		api.TopupNotification(ctx)

		var actual partners.UobResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

}

func getRequestPayload() ([]byte, error) {
	nd := partners.UobNotificationData{
		AccountName:         "AC NAME1 ********** AC NAME2 **********",
		AccountType:         "S",
		AccountNumber:       "**********",
		AccountCurrency:     "THB",
		Amount:              50.05,
		TransactionType:     "C",
		OurReference:        "04B04B1A6884234",
		YourReference:       "**********",
		TransactionText:     "FUNDS TRF",
		TransactionDateTime: "03-03-2020 19:42:56",
		BusinessDate:        "200832",
		NotificationId:      "2205200415383340190334",
	}
	nr := partners.UobNotificationRequest{Event: partners.CREDIT, Data: nd}
	nrm, err := json.Marshal(nr)
	return nrm, err
}

func getDefaultCaCert() *x509.Certificate {
	return &x509.Certificate{
		SerialNumber: big.NewInt(2021),
		Subject: pkix.Name{
			Organization: []string{"LMWN."},
			Country:      []string{"TH"},
			Province:     []string{"BKK"},
			Locality:     []string{"BKK"},
			PostalCode:   []string{"10250"},
		},
		NotBefore:             time.Now(),
		NotAfter:              time.Now().AddDate(10, 0, 0),
		IsCA:                  true,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageClientAuth, x509.ExtKeyUsageServerAuth},
		KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageCertSign,
		BasicConstraintsValid: true,
	}
}

type UobAPIDeps struct {
	Notifier                 *mock_service.MockNotifier
	DriverRepo               *mock_repository.MockDriverRepository
	DriverTransactionService *mock_payment.MockDriverTransactionService
	TransactionService       *mock_payment.MockTransactionService
}

func newUOBApiAPI(r gomock.TestReporter, config partners.Config) (*partners.UobAPI, UobAPIDeps, func()) {
	ctrl := gomock.NewController(r)
	deps := UobAPIDeps{
		mock_service.NewMockNotifier(ctrl),
		mock_repository.NewMockDriverRepository(ctrl),
		mock_payment.NewMockDriverTransactionService(ctrl),
		mock_payment.NewMockTransactionService(ctrl),
	}

	return partners.ProvideUobAPI(
			config,
			deps.Notifier,
			deps.DriverRepo,
			deps.DriverTransactionService,
			deps.TransactionService,
		), deps,
		func() { ctrl.Finish() }
}

func getPkey() (*rsa.PrivateKey, error) {
	privKey := `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	block, _ := pem.Decode([]byte(privKey))
	if block == nil {
		return nil, fmt.Errorf("failed to parse PEM block containing the key")
	}

	priv, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}

	return priv, nil
}

func getPubKeyFromPkey(pkey *rsa.PrivateKey) (string, error) {
	pubKey := &pkey.PublicKey
	asn1Bytes, err := x509.MarshalPKIXPublicKey(pubKey)
	if err != nil {
		return "", err
	}
	var pemKey = &pem.Block{
		Type:  "PUBLIC KEY",
		Bytes: asn1Bytes,
	}
	return pemToString(pemKey)
}

func savePEMPrivatePKCS1Key(privateKey *rsa.PrivateKey) (string, error) {
	asn1Bytes := x509.MarshalPKCS1PrivateKey(privateKey)
	var pemKey = &pem.Block{
		Type:  "RSA PRIVATE KEY",
		Bytes: asn1Bytes,
	}
	return pemToString(pemKey)
}

func pemToString(b *pem.Block) (string, error) {
	buffer := &bytes.Buffer{}
	if err := pem.Encode(buffer, b); err != nil {
		return "", err
	}
	return string(buffer.Bytes()), nil
}
