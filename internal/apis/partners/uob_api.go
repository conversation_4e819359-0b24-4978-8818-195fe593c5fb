package partners

import (
	"context"
	"errors"
	"fmt"
	"net/http/httputil"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	absapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

// UobAPI provides an api to let UOB Bank call to us.
type UobAPI struct {
	Notifier                 service.Notifier
	DriverRepo               repository.DriverRepository
	DriverTransactionService payment.DriverTransactionService
	TransactionService       payment.TransactionService
	Config                   Config
}

func (api *UobAPI) TopupNotification(gctx *gin.Context) {
	// TODO: Check and remove WithoutCancel if it's not needed
	ctx := context.WithoutCancel(gctx.Request.Context())

	b, err := httputil.DumpRequest(gctx.Request, true)
	if err != nil {
		logrus.WithContext(ctx).Errorf("Uob topup notification cannot dump request: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}
	logrus.WithContext(ctx).WithField("method", "UobTopupNotification").Info(string(b))

	if !api.Config.UOBTopUpEnabled {
		logrus.WithContext(ctx).Errorf("UOBTopupNotification is disabled")
		apiutil.ErrNotImplemented(gctx, apiutil.NewFromError(absapi.ERRCODE_NOT_IMPLEMENT, errors.New("not implement")))
		return
	}

	reqForm := &UobFormRequest{}
	err = gctx.ShouldBind(&reqForm)
	if err != nil {
		logrus.WithContext(ctx).Errorf("cannot bind form request: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	req, jReq, err := reqForm.toUobRequest(gctx, api.Config)
	if err != nil {
		logrus.WithContext(ctx).Errorf("cannot decrypt UOB payload: %v", err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	logrus.WithContext(ctx).WithField("method", "UobTopupNotification").Info(jReq)

	if req.Event != CREDIT {
		logrus.WithContext(ctx).Infof("UOB Topup Notification. Event: %v ,NotificationId: %v, YourReference: %v , OurReference: %v",
			req.Event, req.Data.NotificationId, req.Data.YourReference, req.Data.OurReference)

		apiutil.OK(gctx, UobResponse{
			req.Data.NotificationId,
		})
		return
	}

	if req.Data.NotificationId == "" {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification error: notification id is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("transaction reference ID is empty")))
		return
	}

	if req.Data.OurReference == "" {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification error: OurReference id is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("transaction reference ID is empty")))
		return
	}

	if req.Data.YourReference == "" {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification error: Driver reference ID is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("driver reference ID is empty")))
		return
	}

	if req.Data.Amount == 0 {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification error: Amount is empty")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("amount is empty")))
		return
	}

	transRefID, err := parseTransRefID(req.Data.TransactionDateTime, req.Data.OurReference)
	if err != nil {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification parseTransRefID error: TransactionDateTime [%v] invalid: %v", req.Data.TransactionDateTime, err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("TransactionDateTime invalid")))
	}

	transactionDateTime, err := parseTransactionDateTime(req.Data.TransactionDateTime)
	if err != nil {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification parseTransactionDateTime error: TransactionDateTime [%v] invalid: %v", req.Data.TransactionDateTime, err)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("TransactionDateTime invalid")))
		return
	}
	transactionDateTime = transactionDateTime.In(timeutil.BangkokLocation())

	existsTrans, err := api.TransactionService.ExistsTransactionByRefID(ctx, transRefID)
	if err != nil {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification error: cannot check exists transaction")
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, errors.New("cannot check exists transaction")))
		return
	}

	if existsTrans {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification error: duplicate OurReference %s with TransactionDateTime %s", req.Data.OurReference, req.Data.TransactionDateTime)
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(absapi.ERRCODE_INVALID_REQUEST, fmt.Errorf("duplicate OurReference %s with TransactionDateTime %s", req.Data.OurReference, req.Data.TransactionDateTime)))
		return
	}

	var driverID string
	transactionStatus := model.SuccessTransactionStatus
	driver, err := api.DriverRepo.GetProfileByUOBRefID(ctx, req.Data.YourReference)
	if err != nil {
		logrus.WithContext(ctx).Errorf("UOB Topup Notification error: driver %v not found: %v", req.Data.YourReference, err)
		transactionStatus = model.UnmatchedTransactionStatus
		driverID = ""
	} else {
		driverID = driver.DriverID
	}

	transactionInfo := model.NewPurchaseCreditWithSourceTransactionInfo(model.TransactionSourceUOB, driverID, types.NewMoney(req.Data.Amount))
	transactionInfo.RequestedTime = timeutil.BangkokNow().UTC()
	transactionInfo.UobNotificationId = req.Data.NotificationId
	transactionInfo.VanRefID = req.Data.YourReference
	transactionInfo.TransactionDateTime = transactionDateTime
	transactionInfo.BankRefID = req.Data.OurReference
	transactionInfo.RemarkCreatedBy = model.ActorSystemCreated
	transInfoList := []model.TransactionInfo{*transactionInfo}

	_, _, err = api.DriverTransactionService.ProcessDriverTransaction(ctx,
		driverID,
		model.UserTransactionChannel,
		model.CreditTopUpTransactionAction,
		transactionStatus,
		service.TransactionInfos(transInfoList...),
		service.WithTransactionOptions(model.WithTransactionRefID(transRefID)),
	)

	if err != nil {
		logrus.Errorf("UOB - add driver transaction error: driverId=%s amount=%v tranRef=%s time=%v err=%v", driverID, req.Data.Amount, transRefID, timeutil.BangkokNow(), err)
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absapi.ERRCODE_INTERNAL_ERROR, fmt.Errorf("AddDriverTransaction: %v", err)))
		return
	}

	if transactionStatus == model.SuccessTransactionStatus {
		api.notifyTopupSuccess(gctx, driverID, req)
	}

	apiutil.OK(gctx, UobResponse{
		req.Data.NotificationId,
	})
}

func parseTransRefID(dateTime string, ourRef string) (string, error) {
	d := strings.Split(dateTime, " ")
	if len(d) != 2 {
		return "", fmt.Errorf("split date time error with format 03-03-2020 19:42:56 - [%v]", dateTime)
	}

	dt := strings.Split(d[0], "-") // split year month date
	if len(dt) != 3 {
		return "", fmt.Errorf("split date error with format DD-MM-YYYY - [%v]", dt)
	}
	tt := strings.Split(d[1], ":") // split time
	if len(tt) != 3 {
		return "", fmt.Errorf("split time error with format HH:MM:SS - [%v]", tt)
	}

	time := fmt.Sprintf("%s%s%s", tt[0], tt[1], tt[2]) // HHMMSS
	ymd := fmt.Sprintf("%s%s%s", dt[2], dt[1], dt[0])  // YYYYMMDD

	s := fmt.Sprintf("%s%s%s", ymd, time, ourRef)
	return s, nil
}

func parseTransactionDateTime(dateTime string) (time.Time, error) {
	d := strings.Split(dateTime, " ")
	if len(d) != 2 {
		return time.Time{}, fmt.Errorf("split date time error with format 03-03-2020 19:42:56 - [%v]", dateTime)
	}

	dt := strings.Split(d[0], "-")
	if len(dt) != 3 {
		return time.Time{}, fmt.Errorf("split date error with format DD-MM-YYYY - [%v]", dt)
	}
	tt := strings.Split(d[1], ":")
	if len(tt) != 3 {
		return time.Time{}, fmt.Errorf("split time error with format HH:MM:SS - [%v]", tt)
	}

	date, err := strconv.Atoi(dt[0])
	if err != nil {
		return time.Time{}, fmt.Errorf("convert date to int error - [%v]", dt[0])
	}

	month, err := strconv.Atoi(dt[1])
	if err != nil {
		return time.Time{}, fmt.Errorf("convert month to int error - [%v]", dt[1])
	}

	year, err := strconv.Atoi(dt[2])
	if err != nil {
		return time.Time{}, fmt.Errorf("convert year to int error - [%v]", dt[2])
	}

	hour, err := strconv.Atoi(tt[0])
	if err != nil {
		return time.Time{}, fmt.Errorf("convert hour to int error - [%v]", tt[0])
	}
	minute, err := strconv.Atoi(tt[1])
	if err != nil {
		return time.Time{}, fmt.Errorf("convert minute to int error - [%v]", tt[1])
	}
	second, err := strconv.Atoi(tt[2])
	if err != nil {
		return time.Time{}, fmt.Errorf("convert second to int error - [%v]", tt[2])
	}

	ft := time.Date(year, time.Month(month), date, hour, minute, second, 0, timeutil.BangkokLocation())

	return ft, nil

}

func (api *UobAPI) notifyTopupSuccess(gctx *gin.Context, driverID string, req UobNotificationRequest) {
	dateTime := strings.Split(timeutil.BangkokNow().Format("01-02-2006 15:04:05"), " ")
	if err := api.Notifier.Notify(gctx, []string{driverID}, model.EventTopUpNotification(dateTime, req.Data.Amount)); err != nil {
		logrus.WithFields(logrus.Fields{
			"error": err.Error(),
		}).Error("Notify Partners Top Up error")
	}
}

func ProvideUobAPI(cfg Config, notify service.Notifier, driverRepository repository.DriverRepository, driverTransactionService payment.DriverTransactionService, transactionService payment.TransactionService) *UobAPI {
	return &UobAPI{
		Config:                   cfg,
		Notifier:                 notify,
		DriverTransactionService: driverTransactionService,
		DriverRepo:               driverRepository,
		TransactionService:       transactionService,
	}
}
