package cron

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/cron"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

type CronAPI struct {
	Executor cron.TaskExecutor
	Tasks    cron.TaskProvider
}

func (ca *CronAPI) RunIncentiveBatch(ctx *gin.Context) {
	req := struct {
		Date string `json:"date"`
	}{
		Date: "today",
	}

	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(ctx)
		return
	}

	job, err := ca.Executor.Execute(context.Background(), ca.ProvideAPITask(cron.TaskNameIncentive), req.Date)
	if err != nil {
		ctx.Error(err)
		return
	}

	apiutil.Accepted(ctx, job)
}

func (ca *CronAPI) RunUpdateTodayEarning(ctx *gin.Context) {
	var req RunTodayEarningReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(ctx, utils.ErrorStructResponse(err))
		return
	}

	reqCtx := ctx.Request.Context()
	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameTodayEarning), req.DriverIDs)

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunRemoveExpiredFreeCredit(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameExpiredFreeCredit))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunDailyUnban(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameDailyUnban))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) ForceCompleteOrder(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameForceCompleteOrder))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunShiftInstructionNotification(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameShiftInstructionNotification))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) ProvideAPITask(name cron.TaskName) cron.Task {
	tasks := ca.Tasks.Provide(name)
	return tasks
}

func (ca *CronAPI) RunTawi50Generator(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameTawi50Generator))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunPayoutWithdrawalTask(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNamePayoutWithdrawal))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunPayoutWithdrawalDailyEmailTask(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNamePayoutWithdrawalDailyEmail))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunATTRLogArchive(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	var req RunATTRLogArchiveReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(ctx, utils.ErrorStructResponse(err))
		return
	}

	err := req.Validate()
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiErrors.ErrInvalidRequest(err))
		return
	}

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameATTRLogArchive), req.Date)

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunExpiredAttendanceStat(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	var req RunExpiredAttendanceStatReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(ctx, utils.ErrorStructResponse(err))
		return
	}

	err := req.Validate()
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiErrors.ErrInvalidRequest(err))
		return
	}

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameExpiredAttendanceStat), req.Date)

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunInstallmentDailyDeductionTask(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameInstallmentDailyDeduction))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunGroupTransactionTask(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameGroupTransaction))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunBNPLSOATask(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameBNPLSOAFile))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunDailyIncomeTask(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()
	argsI := []interface{}{}
	queryParams := ctx.QueryArray("args")
	for _, s := range queryParams {
		argsI = append(argsI, s)
	}

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameDailyIncome), argsI...)

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunEffectBanTime(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()
	argsI := []interface{}{}
	queryParams := ctx.QueryArray("args")
	for _, s := range queryParams {
		argsI = append(argsI, s)
	}

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameEffectBanTime), argsI...)

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunEtaxInvoiceGeneratorTask(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameEtaxInvoiceGenerator))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunCitiItemizeReport(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameCitiItemizeReport))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunCitiDailyReconcile(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()
	var args []any
	if nowStr := ctx.Query("now"); nowStr != "" {
		now, err := time.Parse("2006-01-02T15:04:05Z07:00", nowStr)
		if err != nil {
			apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
		}
		args = append(args, &now)
	}

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameCitiDailyReconcile), args...)

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func (ca *CronAPI) RunConvertCoinToCash(ctx *gin.Context) {
	reqCtx := ctx.Request.Context()

	job, err := ca.Executor.Execute(reqCtx, ca.ProvideAPITask(cron.TaskNameConvertCoinToCash))

	if err != nil {
		apiutil.ErrInternalError(ctx, apiErrors.ErrInternal(err))
	} else {
		apiutil.Accepted(ctx, job)
	}
}

func ProvideCronAPI(executor cron.TaskExecutor, tasks cron.TaskProvider) *CronAPI {
	return &CronAPI{
		Executor: executor,
		Tasks:    tasks,
	}
}
