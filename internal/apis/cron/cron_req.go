package cron

import (
	"regexp"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

var validDate = regexp.MustCompile(`\d{1,2}/\d{1,2}/\d{4}`)

func ParseIncentiveTime(date string) error {
	if date == "yesterday" || date == "today" {
		return nil
	}

	if !validDate.MatchString(date) {
		return errors.New("invalid format date DD/MM/YYYY")
	}

	return nil
}

type RunIncentiveBatchReq struct {
	Date string `form:"date"`
}

func newRunIncentiveBatchReq(gctx *gin.Context) (*RunIncentiveBatchReq, error) {
	var req RunIncentiveBatchReq

	if err := gctx.ShouldBindJSON(&req); err != nil {
		return nil, utils.ErrorStructResponse(err)
	}

	err := ParseIncentiveTime(req.Date)
	if err != nil {
		return nil, apiErrors.ErrInvalidRequest(err)
	}

	return &req, nil
}

type RunTodayEarningReq struct {
	DriverIDs []string `json:"driverIds,omitempty"`
}

type RunATTRLogArchiveReq struct {
	Date string `json:"date,omitempty"`
}

func (r RunATTRLogArchiveReq) Validate() error {
	if r.Date == "yesterday" || r.Date == "today" {
		return nil
	}

	if !validDate.MatchString(r.Date) {
		return errors.New("invalid format date DD/MM/YYYY")
	}

	return nil
}

type RunExpiredAttendanceStatReq struct {
	Date string `json:"date,omitempty"`
}

func (r RunExpiredAttendanceStatReq) Validate() error {
	if r.Date == "yesterday" || r.Date == "today" {
		return nil
	}

	if !validDate.MatchString(r.Date) {
		return errors.New("invalid format date DD/MM/YYYY")
	}

	return nil
}
