package formservice

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"git.wndv.co/go/logx/v2"
	commonv1 "git.wndv.co/go/proto/lineman/egs/common/v1"
	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	imsPb "git.wndv.co/go/proto/lineman/inventory/v1"
	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type Config struct {
	FormEGSUnacceptableBanWithin int

	FormEGSMinimunCompletedOrderEachDay int

	// The number of days that the order completed
	FormEGSCompletedOrderDayAmount int

	// Range to count the completed order
	FormEGSCompletedOrderWithin int

	// Maximum Days Past Due (DPD) allowed for each installment
	FormEGSMaximumDPDAllowed int

	// Using to validate max exposure and max tenor that was set by admin
	IsEGSFinancialRiskValidatorEnabled bool
}

type Validator func(ctx context.Context, driver model.Driver) (apiErr *api.Error)

func CombineValidators(validators ...Validator) Validator {
	return func(ctx context.Context, driver model.Driver) *api.Error {
		validationErrors := make([]api.Error, 0, len(validators))

		for _, validator := range validators {
			if apiErr := validator(ctx, driver); apiErr != nil {
				if apiErr.Code == api.ERRCODE_INTERNAL_ERROR {
					return apiErr
				}
				validationErrors = append(validationErrors, *apiErr)
			}
		}

		if len(validationErrors) > 0 {
			return ErrFormIsRejected(validationErrors...)
		}

		return nil
	}
}

func CompletedOrderValidator(driverOrderRepository repository.DriverOrderInfoRepository, cfg Config, timeNow time.Time) Validator {
	return func(ctx context.Context, driv model.Driver) *api.Error {
		// start is 00.00.00 (yesterday - config range)
		// end is 23.59.59 of yesterday (not count today)
		endDate := timeutil.DateCeiling(timeNow.AddDate(0, 0, -1))
		startDate := timeutil.DateTruncate(endDate.AddDate(0, 0, -(cfg.FormEGSCompletedOrderWithin)))

		drvOrderInfo, err := driverOrderRepository.GetDailyCounts(ctx, driv.DriverID, startDate, endDate)
		if err != nil {
			logx.Error().Err(err).Msg("cannot GetDailyCounts")
			return apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
		}

		if drvOrderInfo == nil {
			return ErrDriverOrderInfoIsEmpty()
		}

		if len(drvOrderInfo.DailyCounts) < cfg.FormEGSCompletedOrderDayAmount {
			return ErrCompletedOrderTooLow()
		}

		var completedOrderDayCount int
		for _, dailyCount := range drvOrderInfo.DailyCounts {
			if dailyCount.Completed >= cfg.FormEGSMinimunCompletedOrderEachDay {
				completedOrderDayCount++
			}

			if completedOrderDayCount >= cfg.FormEGSCompletedOrderDayAmount {
				break
			}
		}

		if completedOrderDayCount < cfg.FormEGSCompletedOrderDayAmount {
			return ErrCompletedOrderTooLow()
		}

		return nil
	}
}

func BanValidatorForTenorInstallment(banHistoryRepository repository.BanHistoryRepository, cfg Config, timeNow time.Time) Validator {
	return func(ctx context.Context, driv model.Driver) *api.Error {
		if apiErr := validateBanFromDriverModel(driv); apiErr != nil {
			return apiErr
		}

		banHistories, err := banHistoryRepository.FindHistory(ctx, driv.DriverID)
		if err != nil {
			logx.Error().Err(err).Msg("cannot FindBanHistory")
			return apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
		}

		return validateBanFromHistories(banHistories, timeNow, cfg.FormEGSUnacceptableBanWithin)
	}
}

func BanValidatorForOneTimeInstallment(banHistoryRepository repository.BanHistoryRepository) Validator {
	isBanTakeOrderPermanent := func(h model.BanHistory) bool {
		return driver.IsBanTakeOrderAction(h.Action) && driver.IsBanTypePermanent(h.Type)
	}
	isBanWithdraw := fp.Compose(driver.IsBanWithdrawAction, driver.BanHistoryToAction)
	isUnbanTakeOrder := fp.Compose(driver.IsUnbanTakeOrderAction, driver.BanHistoryToAction)
	isUnbanWithdraw := fp.Compose(driver.IsUnbanWithdrawAction, driver.BanHistoryToAction)

	return func(ctx context.Context, driv model.Driver) *api.Error {

		banHistories, err := banHistoryRepository.FindHistory(ctx, driv.DriverID)
		if err != nil {
			logx.Error().Err(err).Msg("cannot FindBanHistory")
			return apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
		}

		sort.Slice(banHistories, func(i, j int) bool {
			return banHistories[i].CreatedAt.Before(banHistories[j].CreatedAt)
		})

		bannedTakeOrderPermanent, bannedWithdraw := false, false
		for _, his := range banHistories {
			if isBanTakeOrderPermanent(his) {
				bannedTakeOrderPermanent = true
			} else if isBanWithdraw(his) {
				bannedWithdraw = true
			} else if isUnbanTakeOrder(his) {
				bannedTakeOrderPermanent = false
			} else if isUnbanWithdraw(his) {
				bannedWithdraw = false
			}
		}
		if bannedTakeOrderPermanent || bannedWithdraw {
			return ErrUnacceptableBanHistoryWithoutDays()
		}
		return nil
	}
}

func BanValidator(banHistoryRepository repository.BanHistoryRepository, cfg Config, timeNow time.Time) Validator {
	return func(ctx context.Context, driv model.Driver) *api.Error {
		if apiErr := validateBanFromDriverModel(driv); apiErr != nil {
			return apiErr
		}

		banHistories, err := banHistoryRepository.FindHistory(ctx, driv.DriverID)
		if err != nil {
			logx.Error().Err(err).Msg("cannot FindBanHistory")
			return apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
		}

		return validateBanFromHistories(banHistories, timeNow, cfg.FormEGSUnacceptableBanWithin)
	}
}

func validateBanFromDriverModel(driv model.Driver) *api.Error {
	if driv.Status == model.StatusBanned && !driv.IsBanTemporary() {
		return ErrDriverIsBanned()
	}

	if driv.BanLater && !driv.IsBanTemporary() {
		return ErrDriverIsBannedLater()
	}
	return nil
}

func validateBanFromHistories(banHistories []model.BanHistory, timeNow time.Time, unacceptableBanWithin int) *api.Error {
	startDate := timeNow.AddDate(0, 0, -unacceptableBanWithin)

	for _, banHistory := range banHistories {
		// ignore ban-unban withdraw

		// check unban
		if banHistory.Action == driver.ACTION_ADMIN_UNBAN_TAKEORDER {
			// latest out of range
			if !timeutil.IsBetweenEqual(banHistory.CreatedAt.In(timeutil.BangkokLocation()), startDate, timeNow) {
				break
			}
			continue
		}
		if banHistory.Action == driver.ACTION_ADMIN_BAN_TAKEORDER || // check ban
			banHistory.Action == driver.ACTION_SYSTEM_BAN_TAKEORDER ||
			banHistory.Action == "BAN_TAKEORDER" {
			if banHistory.Type == string(model.BanTypePermanent) {
				return ErrUnacceptableBanHistory()
			}
			// ignore temporary
		}
	}
	return nil
}

func PurchasingPowerValidator(egsService service.EGSService, cfg Config, driverId string, batchId string, dailyAmount float64, netPrice float64, tenor int) Validator {
	return func(ctx context.Context, driver model.Driver) *api.Error {
		pp, maxExposure, maxTenor, err := egsService.PurchasingPower(ctx, driverId, batchId, timeutil.BangkokNow())

		if err != nil {
			logx.Warnf(ctx, err, "Driver %s PurchasingPower Err", driverId)
			pp = 0
		}

		if dailyAmount > pp {
			return ErrDriverPurchasingPowerTooLow()
		}

		if cfg.IsEGSFinancialRiskValidatorEnabled {
			if netPrice > maxExposure {
				logx.Info().Context(ctx).Str("method", "PurchasingPowerValidator").
					Msgf("driver max exposure [%v] too low : Driver ID [%v], Product net price [%v]", maxExposure, driverId, netPrice)
				return ErrDriverFinancialRiskNotPassed()
			}

			if tenor > maxTenor {

				logx.Info().Context(ctx).Str("method", "PurchasingPowerValidator").
					Msgf("driver max tenor [%v] too low : Driver ID [%v], Product tenor [%v]", maxTenor, driverId, tenor)
				return ErrDriverFinancialRiskNotPassed()
			}
		}

		return nil
	}
}

func DPDValidator(installmentRepo repository.InstallmentRepository, cfg Config) Validator {
	return func(ctx context.Context, driv model.Driver) *api.Error {

		q := persistence.BuildInstallmentQuery().
			WithDriverID(driv.DriverID).
			WithStatuses([]model.InstallmentStatus{
				model.InstallmentActive, model.InstallmentInactive, model.InstallmentCompleted,
			})

		installments, err := installmentRepo.FindWithQueryAndSort(ctx, q, 0, 0, repository.WithReadSecondaryPreferred)
		if err != nil {
			logx.Error().Err(err).Msg("cannot FindWithQueryAndSort")
			return apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
		}

		for _, inst := range installments {
			if inst.DPD > cfg.FormEGSMaximumDPDAllowed {
				return ErrDriverInstallmentDPDTooHigh(cfg.FormEGSMaximumDPDAllowed)
			}
		}

		return nil
	}
}

func convertFromProductLimitResponseToCountProductOrder(productLimit []*imsPb.GetProductPurchaseLimitBySKUResponse_Data) []*egsv1.CountOrderRequest_ProductSKU {
	countOrderRequest := make([]*egsv1.CountOrderRequest_ProductSKU, 0, len(productLimit))
	for _, limit := range productLimit {
		if limit.Limit <= 0 || limit.Interval == nil {
			logx.Warn().
				Str("product_sku", limit.Sku).
				Int("limit_purchase", int(limit.Limit)).
				Str("interval_period", fmt.Sprintf("%+v", limit.Interval)).
				Msg("product limit contain some invalid data")
			continue
		}
		countOrderRequest = append(countOrderRequest, &egsv1.CountOrderRequest_ProductSKU{
			ProductSku: limit.Sku,
			CreatedAt: &egsv1.TimeRange{
				Start: limit.Interval.Start,
				End:   limit.Interval.End,
			},
		})
	}
	return countOrderRequest
}

func PurchaseProductLimitationValidator(
	grpcIMSProduct imsPb.ProductServiceClient,
	grpcOrder egsv1.EGSOrderServiceClient,
	configSet types.StringSet,
	batchGroupType model.FormEGSBatchGroupType,
	productSkuList ...string,
) Validator {
	return func(ctx context.Context, driv model.Driver) *api.Error {
		if !configSet.Has(string(batchGroupType)) {
			return nil
		}

		getPurchaseLimitRequest := imsPb.GetProductPurchaseLimitBySKURequest{
			Sku: productSkuList,
		}
		limitResponse, err := grpcIMSProduct.GetProductPurchaseLimitBySKU(ctx, &getPurchaseLimitRequest)
		if err != nil {
			logx.Error().
				Err(err).
				Str("driver_id", driv.DriverID).
				Str("product_sku_list", strings.Join(productSkuList, ",")).
				Msg("cannot get purchase product limit")
			return apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
		}

		if len(limitResponse.Data) == 0 {
			logx.Error().
				Str("product_sku_list", strings.Join(productSkuList, ",")).
				Msg("product sku limit is not found")
			return nil
		}

		mapProductSku := make(map[string]*imsPb.GetProductPurchaseLimitBySKUResponse_Data, len(limitResponse.Data))

		for _, limit := range limitResponse.Data {
			mapProductSku[limit.Sku] = limit
		}

		ProductSkuListRequest := convertFromProductLimitResponseToCountProductOrder(limitResponse.Data)

		if len(ProductSkuListRequest) == 0 {
			return nil
		}

		countOrderRequest := egsv1.CountOrderRequest{
			DriverId:       driv.DriverID,
			ProductSkuList: ProductSkuListRequest,
			Status: []commonv1.EGSOrderStatus{
				commonv1.EGSOrderStatus_EGS_ORDER_STATUS_UNSPECIFIED,
				commonv1.EGSOrderStatus_EGS_ORDER_STATUS_PENDING,
				commonv1.EGSOrderStatus_EGS_ORDER_STATUS_PROCESSING,
				commonv1.EGSOrderStatus_EGS_ORDER_STATUS_READY_TO_PICKUP,
				commonv1.EGSOrderStatus_EGS_ORDER_STATUS_DELIVERING,
				commonv1.EGSOrderStatus_EGS_ORDER_STATUS_DELIVERED,
				commonv1.EGSOrderStatus_EGS_ORDER_STATUS_PICKED_UP,
				commonv1.EGSOrderStatus_EGS_ORDER_STATUS_COMPLETED,
			},
		}

		ordersAmountResponse, err := grpcOrder.CountOrder(ctx, &countOrderRequest)
		if err != nil {
			logx.Error().
				Err(err).
				Str("driver_id", driv.DriverID).
				Str("product_sku_list", strings.Join(productSkuList, ",")).
				Msg("cannot get order amount")
			return apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
		}

		for _, orderAmount := range ordersAmountResponse.Data {
			var productSku *imsPb.GetProductPurchaseLimitBySKUResponse_Data
			var ok bool
			if productSku, ok = mapProductSku[orderAmount.Sku]; !ok {
				logx.Warn().
					Str("driver_id", driv.DriverID).
					Str("product_sku_list", strings.Join(productSkuList, ",")).
					Str("product_sku", orderAmount.Sku).
					Msg("product sku limit is not found")
				continue
			}

			if orderAmount.Amount >= int64(productSku.Limit) {
				logx.Error().
					Str("driver_id", driv.DriverID).
					Str("product_sku_list", strings.Join(productSkuList, ",")).
					Str("product_sku", orderAmount.Sku).
					Int("limit_purchase", int(productSku.Limit)).
					Int("order_amount", int(orderAmount.Amount)).
					Msg("purchase product exceed limit")
				return ErrPurchaseProductLimitExceed()
			}
		}

		return nil
	}
}
