package installment

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"

	helpers "git.wndv.co/lineman/fleet-distribution/internal/apis/installment/helpers"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type BulkUploadResponse struct {
	Successes []InstallmentResponse `json:"successes"`
	Failures  []helpers.ErrorReport `json:"failures"`
}

type InstallmentResponse struct {
	ID                       string                    `json:"id"`
	RefinancedInstallmentIDs []string                  `json:"refinancedInstallmentIDs,omitempty"`
	DriverID                 string                    `json:"driverId"`
	ProductName              string                    `json:"productName,omitempty"`
	SKU                      string                    `json:"sku,omitempty"`
	Start                    time.Time                 `json:"start,omitempty"`
	End                      time.Time                 `json:"end,omitempty"`
	InitialAmount            types.Money               `json:"initialAmount,omitempty"`
	FinancingTerm            string                    `json:"financingTerm,omitempty"`
	DailyAmount              types.Money               `json:"dailyAmount,omitempty"`
	OutstandingAmount        types.Money               `json:"outstandingAmount"`
	OverdueAmount            types.Money               `json:"overdueAmount"`
	Principal                types.Money               `json:"principal"`
	DaysPastDue              int                       `json:"daysPastDue"`
	MessageDisplay           string                    `json:"messageDisplay,omitempty"`
	Status                   model.InstallmentStatus   `json:"status,omitempty"`
	Remarks                  []model.InstallmentRemark `json:"remarks,omitempty"`
	CreatedBy                string                    `json:"createdBy,omitempty"`
	CreatedAt                time.Time                 `json:"createdAt,omitempty"`
	UpdatedAt                time.Time                 `json:"updatedAt,omitempty"`
	ExpectedDeliveryDate     time.Time                 `json:"expectedDeliveryDate,omitempty"`
	InstallmentLogs          []model.InstallmentLog    `json:"installmentLogs,omitempty"`
}

type InstallmentsResponse struct {
	installments []model.Installment
	count        int
}

func NewInstallmentResponse(ism model.Installment) InstallmentResponse {
	return InstallmentResponse{
		ID:                       ism.ID.Hex(),
		RefinancedInstallmentIDs: ism.RefinancedInstallmentIDs,
		DriverID:                 ism.DriverID,
		ProductName:              ism.ProductName,
		SKU:                      ism.SKU,
		Start:                    ism.Start,
		End:                      ism.End,
		InitialAmount:            ism.InitialAmount,
		FinancingTerm:            ism.FinancingTerm,
		DailyAmount:              ism.DailyAmount,
		MessageDisplay:           ism.MessageDisplay,
		Status:                   ism.Status,
		Remarks:                  ism.Remarks,
		CreatedBy:                ism.CreatedBy,
		CreatedAt:                ism.CreatedAt,
		UpdatedAt:                ism.UpdatedAt,
		OutstandingAmount:        ism.OutstandingAmount,
		OverdueAmount:            ism.OverdueAmount,
		DaysPastDue:              ism.DPD,
		ExpectedDeliveryDate:     ism.ExpectedDeliveryDate,
		Principal:                ism.Principal,
	}
}

func NewInstallmentWithLogsResponse(ism model.Installment) InstallmentResponse {
	res := NewInstallmentResponse(ism)
	res.InstallmentLogs = ism.InstallmentLogs
	return res
}

type DriverAdminResponseWriter interface {
	Write(gctx *gin.Context)
}

type CSVInstallmentResponseWriter struct {
	installments []model.Installment
}

func (csvWriter *CSVInstallmentResponseWriter) Write(gctx *gin.Context) error {
	b := &bytes.Buffer{}
	writer := csv.NewWriter(b)

	_ = writer.Write([]string{
		"installment_id",
		"driver_id",
		"product_name",
		"sku",
		"initial_amount",
		"financing_term",
		"daily_amount",
		"start_date",
		"end_date",
		"deduction_status",
		"outstanding_principal",
		"overdue_principal",
		"DPD",
		"refinanced_installment_ids",
		"remark",
		"exported_at",
	})
	exportedAt := timeutil.BangkokNow().Format("02/01/2006 15:04:05")
	for _, item := range csvWriter.installments {
		var remarks []string
		for _, remark := range item.Remarks {
			if len(remark.Remark) > 0 {
				remarks = append(remarks, remark.Remark)
			}
		}
		_ = writer.Write([]string{
			item.ID.Hex(),
			item.DriverID,
			item.ProductName,
			item.SKU,
			fmt.Sprint(item.InitialAmount),
			item.FinancingTerm,
			fmt.Sprint(item.DailyAmount),
			item.Start.In(timeutil.BangkokLocation()).Format("02/01/2006"),
			item.End.In(timeutil.BangkokLocation()).Format("02/01/2006"),
			string(item.Status),
			fmt.Sprint(item.OutstandingAmount),
			fmt.Sprint(item.OverdueAmount),
			fmt.Sprint(item.DPD),
			fmt.Sprint(item.RefinancedInstallmentIDs),
			strings.Join(remarks, ", "),
			exportedAt,
		})
	}

	writer.Flush()
	if err := writer.Error(); err != nil {
		return err
	}

	filename := fmt.Sprintf("drivers_%s.csv", time.Now().Format("2006_01_02"))
	gctx.Writer.Header().Set("content-disposition", fmt.Sprintf("attachment;filename=%s", filename))
	gctx.Data(http.StatusOK, "text/csv", b.Bytes())
	return nil
}

type AdvanceInstallmentSuccessInfo struct {
	InstallmentID           string      `json:"installmentId"`
	DriverID                string      `json:"driverId"`
	AdvancementAmount       types.Money `json:"advancementAmount"`
	InstallmentAmount       types.Money `json:"installmentAmount"`
	OutstandingAmount       types.Money `json:"outstandingAmount"`
	OverdueAmount           types.Money `json:"overdueAmount"`
	ActualAdvancementAmount types.Money `json:"actualAdvancementAmount"`
}

type AdvanceInstallmentFailInfo struct {
	InstallmentID string `json:"installmentId"`
	DriverID      string `json:"driverId"`
	Error         string `json:"error"`
}

type AdvanceInstallmentResponse struct {
	Successes []AdvanceInstallmentSuccessInfo `json:"successes"`
	Failures  []AdvanceInstallmentFailInfo    `json:"failures"`
}

type BulkCancelInstallmentResponse struct {
	Successes []CancelInstallmentSuccessInfo `json:"successes"`
	Failures  []helpers.ErrorReport          `json:"failures"`
}

type CancelInstallmentSuccessInfo struct {
	Row                 int    `json:"row"`
	CancelInstallmentID string `json:"cancelInstallmentId"`
	DriverID            string `json:"driverId"`
	IsCancellationFee   bool   `json:"isCancellationFee"`
}

func (r *BulkCancelInstallmentResponse) AddSuccess(success CancelInstallmentSuccessInfo) {
	r.Successes = append(r.Successes, success)
}

func (r *BulkCancelInstallmentResponse) AddFailure(failure helpers.ErrorReport) {
	r.Failures = append(r.Failures, failure)
}

type BulkForceUpdateProductDetailResponse struct {
	Successes []BulkForceUpdateProductDetailInfo `json:"successes"`
	Failures  []BulkForceUpdateProductDetailInfo `json:"failures"`
}

type BulkForceUpdateProductDetailInfo struct {
	Row           int    `json:"row"`
	InstallmentID string `json:"installmentId"`
	DriverID      string `json:"driverId"`
	Reason        string `json:"reason"`
}

func (r *BulkForceUpdateProductDetailResponse) AddSuccess(success BulkForceUpdateProductDetailInfo) {
	r.Successes = append(r.Successes, success)
}

func (r *BulkForceUpdateProductDetailResponse) AddFailure(failure BulkForceUpdateProductDetailInfo) {
	r.Failures = append(r.Failures, failure)
}

type BulkResetEtaxInstallmentResponse struct {
	Successes []BulkResetEtaxInstallmentInfo `json:"successes"`
	Failures  []BulkResetEtaxInstallmentInfo `json:"failures"`
}

type BulkResetEtaxInstallmentInfo struct {
	Row int `json:"row"`

	InstallmentID        string     `json:"installmentId"`
	Strategy             string     `json:"strategy"`
	ExpectedDeliveryDate *time.Time `json:"expectedDeliveryDate,omitempty"`

	Reason string `json:"reason"`
}

func (r *BulkResetEtaxInstallmentInfo) String() string {
	return fmt.Sprintf("%s, row: %d, installmentId: %s, strategy: %s", r.Reason, r.Row, r.InstallmentID, r.Strategy)
}

func (r *BulkResetEtaxInstallmentResponse) AddSuccess(success BulkResetEtaxInstallmentInfo) {
	r.Successes = append(r.Successes, success)
}

func (r *BulkResetEtaxInstallmentResponse) AddFailure(failure BulkResetEtaxInstallmentInfo) {
	r.Failures = append(r.Failures, failure)
}
