package installment

import (
	"context"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	inventoryPb "git.wndv.co/go/proto/lineman/inventory/v1"
	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	helpers "git.wndv.co/lineman/fleet-distribution/internal/apis/installment/helpers"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type InstallmentAPI struct {
	cfg                      Config
	driverTransactionService service.DriverTransactionServiceV2
	installmentRepo          repository.InstallmentRepository
	driverRepo               repository.DriverRepository
	productRepo              repository.ProductRepository
	auditLogRepo             repository.AuditLogRepository
	driverTransctionRepo     repository.DriverTransactionRepository
	productGroupRepo         repository.ProductGroupRepository
	txnHelper                transaction.TxnHelper
	productService           inventoryPb.ProductServiceClient
	eTaxInvoiceRepo          repository.EtaxInvoiceRepository
	eTaxInvoiceService       service.EtaxInvoiceService
	vosService               service.VOSService
	installmentService       service.InstallmentService
	documentRepo             repository.DriverDocumentRepository
}

func (ia *InstallmentAPI) BulkCreate(ctx *gin.Context) {
	if !ia.cfg.CreateInstallmentEnabled {
		logrus.WithContext(ctx).Errorf("CreateInstallmentAPI is disabled")
		apiutil.ErrNotImplemented(ctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_IMPLEMENT, errors.New("not implement")))
		return
	}
	var req BulkInsertInstallmentRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}
	admin := auth.GetAdminEmailFromGctx(ctx)
	validatedInstallments, errorReporter, err := req.GetInstallmentData(admin, ia.cfg)
	if err != nil {
		switch t := err.(type) {
		case helpers.ValidateError:
			apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, t))
			return
		default:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}
	}

	skus := getUniqueSkus(validatedInstallments)

	products, err := ia.productRepo.FindProductsBySKUs(ctx, skus, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Errorf("BulkCreate FindProductsBySKUs error: %v", err)
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	validInstallments, driverIDErrReporter, err := ValidateExistedDriverIdAndSku(ctx, ia.driverRepo, validatedInstallments, products)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	service.AttachBatchTypeFromProductMapToInstallments(validInstallments, products)

	if !driverIDErrReporter.IsEmpty() {
		errorReporter.Append(driverIDErrReporter)
	}

	response := BulkUploadResponse{
		Failures:  make([]helpers.ErrorReport, 0),
		Successes: make([]InstallmentResponse, 0),
	}
	if len(validInstallments) > 0 {
		reqCtx := ctx.Request.Context()
		if err := ia.installmentRepo.CreateAll(reqCtx, validInstallments); err != nil {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}

		response.Successes = make([]InstallmentResponse, len(validInstallments))
		createInstallmentAuditLogs := make([]InstallmentAuditLog, len(validInstallments))
		for i := range validInstallments {
			response.Successes[i] = NewInstallmentResponse(validInstallments[i])
			createInstallmentAuditLogs[i] = InstallmentAuditLog{
				after: validInstallments[i],
			}
		}

		ia.insertInstallmentAuditLogAsync(ctx, admin, createInstallmentAuditLogs, model.CreateAction)
	}

	if !errorReporter.IsEmpty() {
		response.Failures = errorReporter.ConvertToReport()
	}

	apiutil.OK(ctx, response)
}

func getUniqueSkus(validatedInstallments []InstallmentModelWithRowIndex) []string {
	skus := types.NewStringSet()
	for _, installment := range validatedInstallments {
		skuList := strings.Split(installment.SKU, "/")
		for _, s := range skuList {
			skus.Add(strings.TrimSpace(s))
		}

	}
	return skus.GetElements()
}

func getUniqueInstallments(validatedInstallments []InstallmentModelWithRowIndex) ([]string, helpers.ErrorReporter) {
	installments := types.NewStringSet()
	var errorReporter helpers.ErrorReporter
	for _, installment := range validatedInstallments {
		for _, refinancedInstallmentID := range installment.RefinancedInstallmentIDs {
			installmentID := strings.TrimSpace(refinancedInstallmentID)
			if installments.Has(installmentID) {
				errorReporter.AddErrors(installment.RowIndex, 0, helpers.ColumnErrorResult{
					Header:       "Refinanced Installment IDs",
					Value:        installmentID,
					ColumnErrors: []string{fmt.Sprintf("refinanced installment id %s is duplicate", installmentID)},
				})
			} else {
				installments.Add(installmentID)
			}
		}
	}
	return installments.GetElements(), errorReporter
}

func (ia *InstallmentAPI) BulkUpdateStatus(ctx *gin.Context) {
	if !ia.cfg.UpdateInstallmentEnabled {
		logrus.WithContext(ctx).Errorf("UpdateInstallmentAPI is disabled")
		apiutil.ErrNotImplemented(ctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_IMPLEMENT, errors.New("not implement")))
		return
	}

	var req BulkUpdateInstallmentRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	admin := auth.GetAdminEmailFromGctx(ctx)
	validatedInstallments, errorReporter, err := req.GetInstallmentData(admin)
	if err != nil {
		switch t := err.(type) {
		case helpers.ValidateError:
			apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, t))
			return
		default:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}
	}
	cronTime := timeutil.DateTruncateBKK(timeutil.BangkokNow()).Add(ia.cfg.CronTime)
	validInstallments, updateInstallmentAuditLogs, installmentIDErrorReport, err := ValidateExistedInstallmentIdAndSku(ctx, ia.installmentRepo, validatedInstallments, nil, cronTime, false)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if !installmentIDErrorReport.IsEmpty() {
		errorReporter.Append(installmentIDErrorReport)
	}

	reponse := BulkUploadResponse{
		Successes: make([]InstallmentResponse, 0),
		Failures:  make([]helpers.ErrorReport, 0),
	}

	if len(validInstallments) > 0 {
		reqCtx := ctx.Request.Context()
		if err := ia.installmentRepo.UpdateInstallmentsStatus(reqCtx, validInstallments); err != nil {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}

		reponse.Successes = make([]InstallmentResponse, len(validInstallments))
		for i := range validInstallments {
			reponse.Successes[i] = NewInstallmentResponse(validInstallments[i])
		}

		ia.insertInstallmentAuditLogAsync(ctx, admin, updateInstallmentAuditLogs, model.UpdateAction)
	}

	if !errorReporter.IsEmpty() {
		reponse.Failures = errorReporter.ConvertToReport()
	}
	apiutil.OK(ctx, reponse)
}

func (ia *InstallmentAPI) BulkUpdate(ctx *gin.Context) {
	if !ia.cfg.UpdateInstallmentEnabled {
		logrus.WithContext(ctx).Errorf("UpdateInstallmentAPI is disabled")
		apiutil.ErrNotImplemented(ctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_IMPLEMENT, errors.New("not implement")))
		return
	}

	var req BulkUpdateInstallmentRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	admin := auth.GetAdminEmailFromGctx(ctx)
	cronTime := timeutil.DateTruncateBKK(timeutil.BangkokNow()).Add(ia.cfg.CronTime)
	validatedInstallments, errorReporter, err := req.GetUpdateInstallmentData(admin, cronTime, ia.cfg)
	if err != nil {
		switch t := err.(type) {
		case helpers.ValidateError:
			apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, t))
			return
		default:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}
	}
	skus := getUniqueSkus(validatedInstallments)

	products, err := ia.productRepo.FindProductsBySKUs(ctx, skus, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Errorf("BulkUpdate FindProductsBySKUs error: %v", err)
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	validInstallments, updateInstallmentAuditLogs, installmentIDErrorReport, err := ValidateExistedInstallmentIdAndSku(ctx, ia.installmentRepo, validatedInstallments, products, cronTime, true)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	service.AttachBatchTypeFromProductMapToInstallments(validInstallments, products)

	if !installmentIDErrorReport.IsEmpty() {
		errorReporter.Append(installmentIDErrorReport)
	}

	response := BulkUploadResponse{
		Successes: make([]InstallmentResponse, 0),
		Failures:  make([]helpers.ErrorReport, 0),
	}

	if len(validInstallments) > 0 {
		reqCtx := ctx.Request.Context()
		if err := ia.installmentRepo.UpdateInstallments(reqCtx, validInstallments); err != nil {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}

		response.Successes = make([]InstallmentResponse, len(validInstallments))
		for i := range validInstallments {
			response.Successes[i] = NewInstallmentResponse(validInstallments[i])
		}

		ia.insertInstallmentAuditLogAsync(ctx, admin, updateInstallmentAuditLogs, model.UpdateAction)
	}

	if !errorReporter.IsEmpty() {
		response.Failures = errorReporter.ConvertToReport()
	}
	apiutil.OK(ctx, response)
}

func (ia *InstallmentAPI) ListInstallments(gctx *gin.Context) {
	var req ListInstallmentRequest

	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	skip, size := utils.ParsePagination(gctx)
	query := req.Query()
	installments, err := ia.installmentRepo.FindWithQueryAndSort(gctx, query, skip, size, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	count, err := ia.installmentRepo.CountInstallmentWithQuery(gctx, query, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}
	if installments == nil {
		installments = []model.Installment{}
	}
	response := make([]InstallmentResponse, len(installments))

	for i, ism := range installments {
		response[i] = NewInstallmentResponse(ism)
	}

	apiutil.OKList(gctx, response, count)
}

func (ia *InstallmentAPI) insertInstallmentAuditLogAsync(gctx *gin.Context, admin string, logs []InstallmentAuditLog, action model.AuditAction) {
	ctx := gctx.Copy()

	safe.GoFuncWithCtx(ctx, func() {
		auditLogs := make([]*model.AuditLog, len(logs))
		for i, l := range logs {
			id := "n/a"
			var before *model.Installment = nil
			if action == model.UpdateAction {
				id = l.before.ID.Hex()
				before = &l.before
			}

			auditLogs[i] = &model.AuditLog{
				Object: model.AuditObject{
					ObjectType: model.InstallmentObject,
					ID:         id,
				},
				Actor: model.AuditLogActor{
					ID: admin,
				},
				Timestamp: timeutil.BangkokNow(),
				Action:    action,
				Before:    before,
				After:     l.after,
			}
		}

		if err := ia.auditLogRepo.InsertMany(gctx, auditLogs); err != nil {
			logrus.Errorf("cannot insert auditlog of installment: %s", err)
		}
	})
}

func (ia *InstallmentAPI) insertSingleInstallmentAuditLogAsync(ctx context.Context, admin string, log InstallmentAuditLog, action model.AuditAction) {

	safe.GoFuncWithCtx(ctx, func() {
		id := "n/a"
		var before *model.Installment = nil
		if action == model.UpdateAction {
			id = log.before.ID.Hex()
			before = &log.before
		}

		auditLog := &model.AuditLog{
			Object: model.AuditObject{
				ObjectType: model.InstallmentObject,
				ID:         id,
			},
			Actor: model.AuditLogActor{
				ID: admin,
			},
			Timestamp: timeutil.BangkokNow(),
			Action:    action,
			Before:    before,
			After:     log.after,
		}

		if err := ia.auditLogRepo.Insert(ctx, auditLog); err != nil {
			logrus.Errorf("cannot insert auditlog of installment: %s", err)
		}
	})
}

func (ia *InstallmentAPI) GetById(ctx *gin.Context) {
	id := ctx.Param("installment_id")
	res, err := ia.installmentRepo.FindById(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(ctx, apiutil.NewFromString("INSTALLMENT_OT_FOUND", "installment not found"))
			return
		}
		apiutil.ErrInternalError(ctx, apiError.ErrInvalidRequest(err))
		return
	}
	apiutil.OK(ctx, NewInstallmentWithLogsResponse(*res))
}

func (ia *InstallmentAPI) BulkCreateRefinancedInstallments(ctx *gin.Context) {
	if !ia.cfg.CreateRefinancingEnabled {
		logrus.WithContext(ctx).Errorf("CreateRefinancingAPI is disabled")
		apiutil.ErrNotImplemented(ctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_IMPLEMENT, errors.New("not implement")))
		return
	}

	var req BulkCreateRefinancedInstallmentRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	admin := auth.GetAdminEmailFromGctx(ctx)

	refinancedInstallments, errorReporter, err := req.GetRefinancedInstallmentData(admin, ia.cfg)
	if err != nil {
		switch t := err.(type) {
		case helpers.ValidateError:
			apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, t))
			return
		default:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}
	}

	validateRefinance, err := ValidateRefinancedInstallment(
		ctx,
		ia.installmentRepo,
		ia.driverRepo,
		ia.productGroupRepo,
		ia.productService,
		refinancedInstallments,
		ia.cfg.EnableInventoryManagementService,
	)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if !validateRefinance.ErrorReporter.IsEmpty() {
		errorReporter.Append(validateRefinance.ErrorReporter)
	}

	response := BulkUploadResponse{
		Failures:  make([]helpers.ErrorReport, 0),
		Successes: make([]InstallmentResponse, 0),
	}

	if !errorReporter.IsEmpty() {
		response.Failures = errorReporter.ConvertToReport()
		apiutil.OK(ctx, response)
		return
	}

	reqCtx := ctx.Request.Context()
	_, txnError := ia.txnHelper.WithTxn(reqCtx, func(reqCtx context.Context) (interface{}, error) {

		if len(validateRefinance.ValidRefinancedInstallments) <= 0 {
			return nil, nil
		}

		if err := ia.installmentRepo.CreateAll(reqCtx, validateRefinance.ValidRefinancedInstallments); err != nil {
			return nil, err
		}

		if err := ia.installmentRepo.ReplaceAll(reqCtx, validateRefinance.ValidUpdateInstallments); err != nil {
			return nil, err
		}

		for driverID, installmentMap := range validateRefinance.ReturnCreditMap {
			for installmentID, amount := range installmentMap {
				if _, _, err := ia.driverTransactionService.ProcessDriverTransaction(
					reqCtx,
					driverID,
					model.SystemTransactionChannel,
					model.AdjustNegativeBalanceTransactionAction,
					model.SuccessTransactionStatus,
					returnCreditTransactionInfosBuilder(amount, installmentID),
				); err != nil && !errors.Is(err, service.ProcessDriverTransactionNoInfoError) {
					return nil, err
				}
			}
		}
		return nil, nil
	}, transaction.WithLabel("InstallmentAPI.BulkCreateRefinancedInstallments"))
	if txnError != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, txnError))
		return
	}

	response.Successes = make([]InstallmentResponse, len(validateRefinance.ValidRefinancedInstallments))
	createInstallmentAuditLogs := make([]InstallmentAuditLog, len(validateRefinance.ValidRefinancedInstallments))
	for i := range validateRefinance.ValidRefinancedInstallments {
		response.Successes[i] = NewInstallmentResponse(validateRefinance.ValidRefinancedInstallments[i])
		createInstallmentAuditLogs[i] = InstallmentAuditLog{
			after: validateRefinance.ValidRefinancedInstallments[i],
		}
	}

	ia.insertInstallmentAuditLogAsync(ctx, admin, createInstallmentAuditLogs, model.CreateAction)
	ia.insertInstallmentAuditLogAsync(ctx, admin, validateRefinance.ValidInstallmentAuditLog, model.UpdateAction)

	apiutil.OK(ctx, response)
}

func returnCreditTransactionInfosBuilder(amount types.Money, installmentID string) service.TransactionInfosBuilder {
	return func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
		var infos []model.TransactionInfo
		txnInfos, err := dt.ReturnCredit(amount, installmentID)
		if err != nil {
			logrus.WithFields(logrus.Fields{
				"method": "returnCreditTransactionInfosBuilder",
			}).Errorf("cannot generate return credit: %v, driver id %s", err, dt.DriverID)
			return nil, err
		}

		infos = append(infos, txnInfos...)
		return infos, nil
	}
}

func (ia *InstallmentAPI) ListETaxInvoice(gctx *gin.Context) {
	installmentId := gctx.Param("installment_id")

	mongoQuery := persistence.BuildEtaxInvoiceQuery().WithInstallmentID(installmentId)
	items, err := ia.eTaxInvoiceRepo.FindWithQueryAndSort(gctx, mongoQuery, 0, 0, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	response := make([]ETaxInvoiceResponseItem, len(items))
	for i, item := range items {
		resItem := ETaxInvoiceResponseItem{
			DocumentNumber:       item.InvoiceNo,
			Type:                 string(item.Type),
			ProductType:          string(item.ProductType),
			DocumentStatus:       string(item.EtaxInvoiceStatus),
			RelatedInstallmentID: item.RelatedInstallmentID,
		}
		if !item.CreatedAt.IsZero() {
			resItem.CreatedAt = &items[i].CreatedAt
		}
		if !item.UpdatedAt.IsZero() {
			resItem.UpdatedAt = &items[i].UpdatedAt
		}
		if !item.PublishedAt.IsZero() {
			resItem.PublishedAt = &items[i].PublishedAt
		}
		if item.Document.PdfFileID != "" {
			presignedUrl, err := ia.vosService.GetCDNPreSignedUrl(gctx, item.Document.PdfFileID, ia.cfg.EtaxPDFPresignedUrlTTL)
			if err != nil {
				logrus.WithContext(gctx).Errorf("unable to get presigned url: %v", err)
				apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			}
			resItem.PDFUrl = &presignedUrl
		}
		response[i] = resItem
	}

	apiutil.OKList(gctx, response, len(response))
}

func (ia *InstallmentAPI) CancelEtaxInvoice(gctx *gin.Context) {
	installmentId := gctx.Param("installment_id")
	if installmentId == "" {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(errors.New("invalid installment id")))
		return
	}

	installment, err := ia.installmentRepo.FindById(gctx, installmentId, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(errors.New("invalid installment")))
		return
	}

	var request CancelEtaxInvoiceRequest
	if err := gctx.ShouldBind(&request); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	if request.DocumentIssueDate.IsZero() {
		err := fmt.Errorf("document issue date is mandatory")
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	cancelEtaxInvoiceRequest, err := request.GetData()
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	err = ia.eTaxInvoiceService.CancelEtaxInvoice(gctx, *installment, *cancelEtaxInvoiceRequest)

	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, gin.H{"status": "success"})
}

func (ia *InstallmentAPI) ReplaceEtaxInvoice(gctx *gin.Context) {
	installmentId := gctx.Param("installment_id")
	if installmentId == "" {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(errors.New("invalid installment id")))
		return
	}

	installment, err := ia.installmentRepo.FindById(gctx, installmentId, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(errors.New("invalid installment")))
		return
	}

	var request ReplaceEtaxInvoiceRequest
	if err := gctx.ShouldBind(&request); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	replaceEtaxInvoiceRequest, err := request.GetData()
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	err = ia.eTaxInvoiceService.ReplaceEtaxInvoice(gctx, *installment, *replaceEtaxInvoiceRequest)

	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(gctx, gin.H{"status": "success"})
}

func (ia *InstallmentAPI) BulkUpdateDetail(ctx *gin.Context) {
	if !ia.cfg.UpdateInstallmentEnabled {
		logrus.WithContext(ctx).Errorf("UpdateInstallmentAPI BulkUpdateDetail is disabled")
		apiutil.ErrNotImplemented(ctx, apiutil.NewFromError(absintheapi.ERRCODE_NOT_IMPLEMENT, errors.New("not implement")))
		return
	}

	var req BulkUpdateInstallmentRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	admin := auth.GetAdminEmailFromGctx(ctx)
	validatedInstallments, errorReporter, err := req.GetInstallmentDetailData(admin)
	if err != nil {
		switch t := err.(type) {
		case helpers.ValidateError:
			apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, t))
			return
		default:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}
	}
	cronTime := timeutil.DateTruncateBKK(timeutil.BangkokNow()).Add(ia.cfg.CronTime)
	validInstallments, updateInstallmentAuditLogs, installmentIDErrorReport, err := ValidateExistedInstallmentIdAndSku(ctx, ia.installmentRepo, validatedInstallments, nil, cronTime, false)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if !installmentIDErrorReport.IsEmpty() {
		errorReporter.Append(installmentIDErrorReport)
	}

	reponse := BulkUploadResponse{
		Successes: make([]InstallmentResponse, 0),
		Failures:  make([]helpers.ErrorReport, 0),
	}

	if len(validInstallments) > 0 {
		reqCtx := ctx.Request.Context()
		if err := ia.installmentRepo.UpdateInstallmentsDetail(reqCtx, validInstallments); err != nil {
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
			return
		}

		reponse.Successes = make([]InstallmentResponse, len(validInstallments))
		for i := range validInstallments {
			reponse.Successes[i] = NewInstallmentResponse(validInstallments[i])
		}

		ia.insertInstallmentAuditLogAsync(ctx, admin, updateInstallmentAuditLogs, model.UpdateAction)
	}

	if !errorReporter.IsEmpty() {
		reponse.Failures = errorReporter.ConvertToReport()
	}
	apiutil.OK(ctx, reponse)
}

func (ia *InstallmentAPI) PushInstallmentLogAmount(ctx *gin.Context) {
	installmentId := ctx.Param("installment_id")
	if installmentId == "" {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(errors.New("invalid installment id")))
		return
	}

	var req PushInstallmentLogAmountRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	amount := req.Amount
	processBy := req.ProcessBy

	if amount.LT(0) {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(errors.New("amount must not be less than 0")))
		return
	}

	installment, err := ia.installmentRepo.FindById(ctx, installmentId)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(errors.New("invalid installment")))
		return
	}

	if installment.Status != model.InstallmentActive && installment.Status != model.InstallmentCompleted {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(errors.New(fmt.Sprintf("installment status must be %s or %s", model.InstallmentActive, model.InstallmentCompleted))))
		return
	}

	if len(installment.InstallmentLogs) == 0 {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(errors.New("no installment log found")))
		return
	}

	timeNowBKK := timeutil.BangkokNow()

	for i, installmentLog := range installment.InstallmentLogs {
		if amount.LTE(0) {
			break
		}
		if installmentLog.InstallmentPaymentStatus != model.InstallmentPaymentStatusPending {
			continue
		}

		totalActualAmount := types.Money(0.0)
		for _, actualAmountLog := range installmentLog.ActualAmountLogs {
			totalActualAmount = totalActualAmount.Add(actualAmountLog.ActualAmount)
		}

		latestActualAmountLog := amount

		if totalActualAmount.Add(amount).GTE(installmentLog.Amount) {
			latestActualAmountLog = installment.DailyAmount.Sub(totalActualAmount)
			installment.InstallmentLogs[i].InstallmentPaymentStatus = model.InstallmentPaymentStatusSuccessful
		}

		amount = amount.Sub(latestActualAmountLog)
		installment.InstallmentLogs[i].ActualAmountLogs = append(installment.InstallmentLogs[i].ActualAmountLogs, model.ActualAmountLog{
			ActualAmount: latestActualAmountLog,
			CreatedAt:    timeNowBKK,
		})
	}

	totalOverdue, totalActualAmount, _ := service.CalculateTotalOverdueAndActualAmount(installment.InstallmentLogs)
	installment.InstallmentAmount = totalActualAmount
	installment.OutstandingAmount = installment.InitialAmount.Sub(totalActualAmount)
	installment.OverdueAmount = totalOverdue

	installment.Remarks = append(installment.Remarks, model.InstallmentRemark{
		Remark:    "Manually adjust installment log without updating rider's balance",
		CreatedAt: timeNowBKK,
		CreatedBy: processBy,
	})

	if installment.OutstandingAmount.LT(0) {
		installment.OutstandingAmount = types.NewMoney(0.0)
	}

	if installment.OverdueAmount.LT(0) {
		installment.OverdueAmount = types.NewMoney(0.0)
	}

	installment.DPD = installment.CalculateDPD(timeNowBKK)

	if err := ia.installmentRepo.Update(ctx, *installment); err != nil {
		apiutil.ErrInternalError(ctx, apiError.ErrInternal(errors.New("update installment error")))
		return
	}

	if req.UnsetSentLastSoaFileTime {
		err = ia.installmentRepo.UnsetSentLastSoaFileTime(ctx, installment.ID)
		if err != nil {
			apiutil.ErrInternalError(ctx, apiError.ErrInternal(errors.New("unset field sent_last_soa_file_time error")))
			return
		}
	}

	apiutil.OK(ctx, gin.H{"status": "success"})
}

func ProvideInstallmentAPI(
	cfg Config,
	installmentRepo repository.InstallmentRepository,
	driverRepo repository.DriverRepository,
	productRepo repository.ProductRepository,
	productGroupRepo repository.ProductGroupRepository,
	auditLogRepo repository.AuditLogRepository,
	driverTransactionService service.DriverTransactionServiceV2,
	driverTransactionRepo repository.DriverTransactionRepository,
	txnHelper transaction.TxnHelper,
	productService inventoryPb.ProductServiceClient,
	eTaxInvoiceRepo repository.EtaxInvoiceRepository,
	eTaxInvoiceService service.EtaxInvoiceService,
	vosService service.VOSService,
	installmentService service.InstallmentService,
	documentRepo repository.DriverDocumentRepository,
) *InstallmentAPI {
	return &InstallmentAPI{
		cfg:                      cfg,
		driverTransactionService: driverTransactionService,
		installmentRepo:          installmentRepo,
		driverRepo:               driverRepo,
		auditLogRepo:             auditLogRepo,
		productRepo:              productRepo,
		driverTransctionRepo:     driverTransactionRepo,
		productGroupRepo:         productGroupRepo,
		txnHelper:                txnHelper,
		productService:           productService,
		eTaxInvoiceRepo:          eTaxInvoiceRepo,
		eTaxInvoiceService:       eTaxInvoiceService,
		vosService:               vosService,
		installmentService:       installmentService,
		documentRepo:             documentRepo,
	}
}

func (ia *InstallmentAPI) ExportCSVInstallments(gctx *gin.Context) {
	var request ListInstallmentRequest

	if err := gctx.ShouldBind(&request); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	query := request.Query()
	query.WithProjection(installmentExportFields)
	installments, err := ia.installmentRepo.FindWithQueryAndSortSelector(gctx, query, 0, ia.cfg.MaxExportedDriverInstallmentInfoCSVRows, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	responseWriter := NewCSVDriverInstallmentInfoResponseWriter(installments)
	if err := responseWriter.Write(gctx); err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(absintheapi.ERRCODE_INTERNAL_ERROR, err))
	}
}

func NewCSVDriverInstallmentInfoResponseWriter(installments []model.Installment) CSVInstallmentResponseWriter {
	return CSVInstallmentResponseWriter{
		installments: installments,
	}
}
