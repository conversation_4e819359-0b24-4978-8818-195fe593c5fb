//go:build integration_test
// +build integration_test

package installment_test

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/installment"
	helpers "git.wndv.co/lineman/fleet-distribution/internal/apis/installment/helpers"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestBulkCancelInstallmentTriggerDryRunIMS(t *testing.T) {
	type DriverBalance struct {
		Wallet            types.Money
		Credit            types.Money
		InstallmentAmount types.Money
	}

	type ExpectInstallment struct {
		InstallmentAmount types.Money
		OverdueAmount     types.Money
		ActualAmounts     []types.Money
		Status            model.InstallmentStatus
	}

	type ExpectFeeInstallment struct {
		DriverId      string
		Sku           string
		InitialAmount types.Money
		DailyAmount   types.Money
		Status        model.InstallmentStatus
	}

	type BaseInstallmentSet struct {
		baseInstallments  []model.Installment
		baseProducts      []model.Product
		baseProductGroups []model.ProductGroup
	}
	type GetBaseInstallmentSet func() BaseInstallmentSet

	type TestData struct {
		Name                  string
		requestBody           string
		baseDriverBalance     DriverBalance
		baseInstallmentSet    GetBaseInstallmentSet
		isCancellationFee     bool
		expectedDriverBalance DriverBalance
		expectInstallments    map[string]ExpectInstallment
		expectFeeInstallments []ExpectFeeInstallment
		expectedFailedSize    int
		expectResponses       installment.BulkCancelInstallmentResponse
	}

	os.Setenv("EnableInventoryManagementService", "true")
	container := ittest.NewContainer(t)

	productGroups, _ := generateProductGroup(t, container)
	products, _ := generateProduct(t, container)
	installments, _ := generateInstallment(t, container)
	makeReq := func(content string) *testutil.GinContextWithRecorder {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/cancel-installments").
			AdminAuthorized("<EMAIL>")
		ctx.Body().MultipartForm().
			File("file", "file.csv", content).
			String("requestedBy", "anonymous").
			Build()
		return ctx
	}

	targetTestDriverID := "DRV_PATTAYA_ONLINE"

	timeNow := time.Now()
	instGenerator := InstallmentGenerator{
		DailyAmount: 100,
		DayPeriod:   2,
		Timenow:     timeNow,
		SKU:         "PRODUCT_1",
	}
	productGnrtr := InstallmentProductGenerator{}
	productGGnrtr := InstallmentProductGroupGenerator{}

	makeCancellationRequestBody := func(installmentId string, isCancellationFee bool, sku string, initialAmount types.Money, dailyAmount types.Money, expectedDeliveryDate time.Time) string {
		if !isCancellationFee {
			return fmt.Sprintf("%s\n%s,%s,%s,,,,,,,,,,,,,",
				"Installment ID,Driver ID,Cancellation Fee,Product Name,SKU,Start,End,Initial Amount,Financing Term,Daily Amount,Message Display,New Installment Status,Remark,Batch,Principal,Expected Delivery Date",
				installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "NO",
			)
		}
		startDateString := timeNow.Format("02/01/2006")
		dayPeriod := int(initialAmount.Div(dailyAmount))
		endDateString := timeNow.AddDate(0, 0, dayPeriod-1).Format("02/01/2006")
		return fmt.Sprintf("%s\n%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s",
			"Installment ID,Driver ID,Cancellation Fee,Product Name,SKU,Start,End,Initial Amount,Financing Term,Daily Amount,Message Display,New Installment Status,Remark,Batch,Principal,Expected Delivery Date",
			installments[0].ID.Hex(), "DRV_PATTAYA_ONLINE", "YES", "CANCELLATION FEE", sku, startDateString, endDateString, initialAmount, "งวด", dailyAmount, "CANCELLATION FEE", "ACTIVE", "CANCELLATION FEE", "1", "2000", timeutil.ToDDMMYYYY(expectedDeliveryDate),
		)
	}

	getBaseInstallmentSet := func() BaseInstallmentSet {
		return BaseInstallmentSet{
			baseInstallments: []model.Installment{
				instGenerator.GetFreshInstallment(installments[0].ID, targetTestDriverID),
			},
			baseProducts: []model.Product{
				productGnrtr.UpdatePriority(products[0], 1),
			},
			baseProductGroups: []model.ProductGroup{
				productGGnrtr.UpdatePriority(productGroups[0], 1),
			},
		}
	}

	testSet := []TestData{
		{
			Name:        "Installment is deducted only 1 time successfully",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            20,
				Credit:            1300,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 150, 150, 0, 150, model.InstallmentActive),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            170,
				Credit:            1300,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 150,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{150},
					Status:            model.InstallmentCancelled,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   false,
					},
				},
			},
		},
		{
			Name:        "Installment is deducted multiple times successfully",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            200,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 150, 450, 0, 150, model.InstallmentActive),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            450,
				Credit:            200,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 450,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{150, 150, 150},
					Status:            model.InstallmentCancelled,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   false,
					},
				},
			},
		},
		{
			Name:        "Installment is deducted and remaining overdue amount ",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -100,
				InstallmentAmount: -100,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 200, 100, 100, 200, model.InstallmentActive),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
					Status:            model.InstallmentCancelled,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   false,
					},
				},
			},
		},
		{
			Name:        "Installment is deducted with all overdue amount",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -200,
				InstallmentAmount: -200,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 100, 0, 200, 100, model.InstallmentActive),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
					Status:            model.InstallmentCancelled,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   false,
					},
				},
			},
		},
		{
			Name:        "Installment isn't deducted yet",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            200,
				Credit:            50,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 300, 0, 0, 300, model.InstallmentActive),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            200,
				Credit:            50,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
					Status:            model.InstallmentCancelled,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   false,
					},
				},
			},
		},
		{
			Name:        "[cancel with fee] Installment is deducted multiple times successfully",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), true, "PRODUCT_3", 1000, 1000, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            500,
				Credit:            250,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 100, 300, 0, 100, model.InstallmentActive),
				}
				return result
			},
			isCancellationFee: true,
			expectedDriverBalance: DriverBalance{
				Wallet:            800,
				Credit:            250,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 300,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100, 100},
					Status:            model.InstallmentCancelled,
				},
			},
			expectFeeInstallments: []ExpectFeeInstallment{
				{
					DriverId:      "DRV_PATTAYA_ONLINE",
					Sku:           "PRODUCT_3",
					InitialAmount: 1000,
					DailyAmount:   1000,
					Status:        model.InstallmentActive,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   true,
					},
				},
			},
		},
		{
			Name:        "[cancel with fee] Installment is deducted and remaining overdue amount ",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), true, "PRODUCT_4", 400, 400, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -400,
				InstallmentAmount: -400,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 150, 100, 200, 150, model.InstallmentActive),
					instGenerator.GetNewInstallmentV2(installments[1].ID, targetTestDriverID, 100, 0, 200, 100, model.InstallmentActive),
				}
				return result
			},
			isCancellationFee: true,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            -200,
				InstallmentAmount: -200,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 300,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 50, 150},
					Status:            model.InstallmentCancelled,
				},
				installments[1].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     200,
					ActualAmounts:     []types.Money{},
					Status:            model.InstallmentActive,
				},
			},
			expectFeeInstallments: []ExpectFeeInstallment{
				{
					DriverId:      "DRV_PATTAYA_ONLINE",
					Sku:           "PRODUCT_4",
					InitialAmount: 400,
					DailyAmount:   400,
					Status:        model.InstallmentActive,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   true,
					},
				},
			},
		},
		{
			Name:        "[cancel with fee] Installment is deducted with all overdue amount",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), true, "PRODUCT_5", 500, 500, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -300,
				InstallmentAmount: -300,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 200, 100, 300, 200, model.InstallmentActive),
				}
				return result
			},
			isCancellationFee: true,
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 400,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100, 200},
					Status:            model.InstallmentCancelled,
				},
			},
			expectFeeInstallments: []ExpectFeeInstallment{
				{
					DriverId:      "DRV_PATTAYA_ONLINE",
					Sku:           "PRODUCT_5",
					InitialAmount: 500,
					DailyAmount:   500,
					Status:        model.InstallmentActive,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   true,
					},
				},
			},
		},
		{
			Name:        "[cancel with fee] Inactive installment is deducted multiple times successfully",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), true, "PRODUCT_6", 200, 200, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            20,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 100, 200, 0, 100, model.InstallmentInactive),
				}
				return result
			},
			isCancellationFee: true,
			expectedDriverBalance: DriverBalance{
				Wallet:            200,
				Credit:            20,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
					Status:            model.InstallmentCancelled,
				},
			},
			expectFeeInstallments: []ExpectFeeInstallment{
				{
					DriverId:      "DRV_PATTAYA_ONLINE",
					Sku:           "PRODUCT_6",
					InitialAmount: 200,
					DailyAmount:   200,
					Status:        model.InstallmentActive,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   true,
					},
				},
			},
		},
		{
			Name:        "[cancel with fee] Inactive installment is deducted multiple times successfully",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), true, "PRODUCT_7", 300, 300, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            20,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 100, 200, 0, 100, model.InstallmentInactive),
				}
				return result
			},
			isCancellationFee: true,
			expectedDriverBalance: DriverBalance{
				Wallet:            200,
				Credit:            20,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 200,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{100, 100},
					Status:            model.InstallmentCancelled,
				},
			},
			expectFeeInstallments: []ExpectFeeInstallment{
				{
					DriverId:      "DRV_PATTAYA_ONLINE",
					Sku:           "PRODUCT_7",
					InitialAmount: 300,
					DailyAmount:   300,
					Status:        model.InstallmentActive,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   true,
					},
				},
			},
		},
		{
			Name:        "[cancel with fee] Inactive installment is deducted and remaining overdue amount",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), true, "PRODUCT_8", 400, 400, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -80,
				InstallmentAmount: -70,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 120, 50, 70, 120, model.InstallmentInactive),
				}
				return result
			},
			isCancellationFee: true,
			expectedDriverBalance: DriverBalance{
				Wallet:            50,
				Credit:            -10,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 120,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{50, 70},
					Status:            model.InstallmentCancelled,
				},
			},
			expectFeeInstallments: []ExpectFeeInstallment{
				{
					DriverId:      "DRV_PATTAYA_ONLINE",
					Sku:           "PRODUCT_8",
					InitialAmount: 400,
					DailyAmount:   400,
					Status:        model.InstallmentActive,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   true,
					},
				},
			},
		},
		{
			Name:        "[cancel with fee] Installment is deducted and remaining ops expense (-50)",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), true, "PRODUCT_9", 100, 100, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -1550,
				InstallmentAmount: -1501,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 500, 0, 1500, 500, model.InstallmentActive),
				}
				return result
			},
			isCancellationFee: true,
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            -50,
				InstallmentAmount: -1,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 1500,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{500, 500, 500},
					Status:            model.InstallmentCancelled,
				},
			},
			expectFeeInstallments: []ExpectFeeInstallment{
				{
					DriverId:      "DRV_PATTAYA_ONLINE",
					Sku:           "PRODUCT_9",
					InitialAmount: 100,
					DailyAmount:   100,
					Status:        model.InstallmentActive,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   true,
					},
				},
			},
		},
		{
			Name:        "[cancel with fee] Inactive installment isn't deducted yet",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), true, "PRODUCT_10", 100, 100, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            100,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 100, 0, 0, 100, model.InstallmentInactive),
				}
				return result
			},
			isCancellationFee: true,
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            100,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
					Status:            model.InstallmentCancelled,
				},
			},
			expectFeeInstallments: []ExpectFeeInstallment{
				{
					DriverId:      "DRV_PATTAYA_ONLINE",
					Sku:           "PRODUCT_10",
					InitialAmount: 100,
					DailyAmount:   100,
					Status:        model.InstallmentActive,
				},
			},
			expectResponses: installment.BulkCancelInstallmentResponse{
				Successes: []installment.CancelInstallmentSuccessInfo{
					{
						Row:                 1,
						CancelInstallmentID: installments[0].ID.Hex(),
						DriverID:            targetTestDriverID,
						IsCancellationFee:   true,
					},
				},
			},
		},
		{
			Name:        "Installment already completed",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            20,
				Credit:            -1500,
				InstallmentAmount: -1500,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 500, 0, 1500, 0, model.InstallmentCompleted),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            20,
				Credit:            -1500,
				InstallmentAmount: -1500,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     1500,
					ActualAmounts:     []types.Money{},
					Status:            model.InstallmentCompleted,
				},
			},
			expectedFailedSize: 1,
			expectResponses: installment.BulkCancelInstallmentResponse{
				Failures: []helpers.ErrorReport{
					{
						Row:           1,
						DriverID:      targetTestDriverID,
						InstallmentID: installments[0].ID.Hex(),
					},
				},
			},
		},
		{
			Name:        "Installment already refinanced",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 200, 0, 800, 200, model.InstallmentRefinanced),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            0,
				Credit:            0,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     800,
					ActualAmounts:     []types.Money{},
					Status:            model.InstallmentRefinanced,
				},
			},
			expectedFailedSize: 1,
			expectResponses: installment.BulkCancelInstallmentResponse{
				Failures: []helpers.ErrorReport{
					{
						Row:           1,
						DriverID:      targetTestDriverID,
						InstallmentID: installments[0].ID.Hex(),
					},
				},
			},
		},
		{
			Name:        "Installment already cancelled",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            200,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 500, 500, 0, 500, model.InstallmentCancelled),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            200,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 500,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{500},
					Status:            model.InstallmentCancelled,
				},
			},
			expectedFailedSize: 1,
			expectResponses: installment.BulkCancelInstallmentResponse{
				Failures: []helpers.ErrorReport{
					{
						Row:           1,
						DriverID:      targetTestDriverID,
						InstallmentID: installments[0].ID.Hex(),
					},
				},
			},
		},
		{
			Name:        "Installment already terminated",
			requestBody: makeCancellationRequestBody(installments[0].ID.Hex(), false, "", 0, 0, timeNow),
			baseDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            200,
				InstallmentAmount: 0,
			},
			baseInstallmentSet: func() BaseInstallmentSet {
				result := getBaseInstallmentSet()
				result.baseInstallments = []model.Installment{
					instGenerator.GetNewInstallmentV2(installments[0].ID, targetTestDriverID, 500, 0, 0, 500, model.InstallmentTerminated),
				}
				return result
			},
			expectedDriverBalance: DriverBalance{
				Wallet:            100,
				Credit:            200,
				InstallmentAmount: 0,
			},
			expectInstallments: map[string]ExpectInstallment{
				installments[0].ID.Hex(): {
					InstallmentAmount: 0,
					OverdueAmount:     0,
					ActualAmounts:     []types.Money{},
					Status:            model.InstallmentTerminated,
				},
			},
			expectedFailedSize: 1,
			expectResponses: installment.BulkCancelInstallmentResponse{
				Failures: []helpers.ErrorReport{
					{
						Row:           1,
						DriverID:      targetTestDriverID,
						InstallmentID: installments[0].ID.Hex(),
					},
				},
			},
		},
	}

	for index := range testSet {
		testData := testSet[index]
		t.Run(testData.Name, func(tt *testing.T) {
			requestBody := testData.requestBody
			ctx := makeReq(requestBody)

			dt, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)
			dt.PurchaseCreditBalance = testData.baseDriverBalance.Credit
			dt.WalletBalance = testData.baseDriverBalance.Wallet
			dt.InstallmentAmount = testData.baseDriverBalance.InstallmentAmount

			baseInstallmentSet := testData.baseInstallmentSet()
			updateDatabase(ctx.GinCtx(), tt, container,
				dt,
				baseInstallmentSet.baseInstallments,
				[]model.Product{},
				[]model.ProductGroup{},
			)
			setProductServiceStub("", container.StubGRPCProductService, baseInstallmentSet.baseProducts, baseInstallmentSet.baseProductGroups)

			container.GinEngineRouter.HandleContext(ctx.GinCtx())
			ctx.AssertResponseCode(tt, http.StatusOK)

			var actualResponse installment.BulkCancelInstallmentResponse
			testutil.DecodeJSON(tt, ctx.ResponseRecorder.Body, &actualResponse)
			assert.Len(tt, actualResponse.Failures, testData.expectedFailedSize)
			assert.Len(tt, actualResponse.Successes, len(testData.expectResponses.Successes))

			for index, expectSuccesses := range testData.expectResponses.Successes {
				actualSuccess := actualResponse.Successes[index]
				assert.Equal(tt, expectSuccesses.CancelInstallmentID, actualSuccess.CancelInstallmentID)
				assert.Equal(tt, expectSuccesses.DriverID, actualSuccess.DriverID)
				assert.Equal(tt, expectSuccesses.IsCancellationFee, actualSuccess.IsCancellationFee)
			}
			for index, expectFailure := range testData.expectResponses.Failures {
				actualFailure := actualResponse.Failures[index]
				assert.Equal(tt, expectFailure.InstallmentID, actualFailure.InstallmentID)
				assert.Equal(tt, expectFailure.DriverID, actualFailure.DriverID)
				assert.Equal(tt, expectFailure.SKU, actualFailure.SKU)
			}

			dtResult, err := container.DataStoreDriverTransactionRepository.FindByID(context.Background(), "DRV_PATTAYA_ONLINE")
			require.NoError(tt, err)

			assert.Equal(tt, testData.expectedDriverBalance.Wallet, dtResult.WalletBalance)
			assert.Equal(tt, testData.expectedDriverBalance.Credit, dtResult.CreditBalance())
			assert.Equal(tt, testData.expectedDriverBalance.InstallmentAmount, dtResult.InstallmentAmount)

			installmentObjectIDs := make([]primitive.ObjectID, len(baseInstallmentSet.baseInstallments))
			for index := range baseInstallmentSet.baseInstallments {
				installmentObjectIDs[index] = baseInstallmentSet.baseInstallments[index].ID
			}
			var installmentResults []model.Installment
			err = container.InstallmentDataStore.Find(ctx.GinCtx(),
				primitive.M{"_id": primitive.M{"$in": installmentObjectIDs}},
				0, 0, &installmentResults)
			require.NoError(tt, err)

			assert.Len(tt, installmentResults, len(installmentObjectIDs))
			for _, item := range installmentResults {

				expectedInstallment, existed := testData.expectInstallments[item.ID.Hex()]
				assert.True(tt, existed)

				assert.Equal(tt, expectedInstallment.InstallmentAmount, item.InstallmentAmount)
				assert.Equal(tt, expectedInstallment.OverdueAmount, item.OverdueAmount)
				var indexer int
				for _, item := range item.InstallmentLogs {
					for _, item := range item.ActualAmountLogs {
						assert.Equal(tt, expectedInstallment.ActualAmounts[indexer], item.ActualAmount)
						indexer++
					}
				}
				assert.Equal(tt, expectedInstallment.Status, item.Status)
				assert.Equal(tt, len(expectedInstallment.ActualAmounts), indexer)
			}

			if testData.isCancellationFee {
				var feeInstallmentResults []model.Installment
				err = container.InstallmentDataStore.Find(ctx.GinCtx(),
					primitive.M{"sku": testData.expectFeeInstallments[0].Sku},
					0, 0, &feeInstallmentResults)
				require.NoError(tt, err)
				for index, expectFeeInstallment := range testData.expectFeeInstallments {
					actualFeeInstallment := feeInstallmentResults[index]
					assert.Equal(tt, expectFeeInstallment.DriverId, actualFeeInstallment.DriverID)
					assert.Equal(tt, expectFeeInstallment.Sku, actualFeeInstallment.SKU)
					assert.Equal(tt, expectFeeInstallment.InitialAmount, actualFeeInstallment.InitialAmount)
					assert.Equal(tt, expectFeeInstallment.DailyAmount, actualFeeInstallment.DailyAmount)
					assert.Equal(tt, expectFeeInstallment.Status, actualFeeInstallment.Status)
				}
			}
		})
	}
}
