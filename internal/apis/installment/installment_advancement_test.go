package installment_test

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	inventoryPb "git.wndv.co/go/proto/lineman/inventory/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/installment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestBulkUploadAdvanceInstallments(tt *testing.T) {
	type TestData struct {
		Name                 string
		FileContent          string
		ReturnInstallments   func() []model.Installment
		ReturnDriverTxns     []model.DriverTransaction
		IsValidatedError     bool
		ExpectedHttpCode     int
		ExpectSuccesses      []installment.AdvanceInstallmentSuccessInfo
		ExpectContainInErros []string
	}

	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/upload-advance-installments")
		gctx.Body().MultipartForm().
			File("file", "file.csv", content).
			String("requestedBy", "anonymous").
			Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	makeFileContent := func(installmentIDs []string, driverIDs []string, amount []types.Money) string {
		var contents []string
		header := "installment id, driver id, advancement amount"
		for index := range installmentIDs {
			contents = append(contents, fmt.Sprintf("%s,%s,%v", installmentIDs[index], driverIDs[index], amount[index]))
		}
		return fmt.Sprintf("%s\n%s", header, strings.Join(contents, "\n"))
	}

	objectIDs := []primitive.ObjectID{primitive.NewObjectID(), primitive.NewObjectID(), primitive.NewObjectID()}
	driverIDTestSet := []string{"DRIVER_ID", "DRIVER_ID_2"}

	installmentGenerator := InstallmentGenerator{
		DailyAmount: 100,
		DayPeriod:   1,
		Timenow:     time.Now(),
	}

	testSet := []TestData{
		{
			Name: "advance-1",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{100},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 100,
					OutstandingAmount: -100,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "rebalance-1",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{100},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: -100,
					InstallmentAmount:     -100,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 100,
					OutstandingAmount: -100,
					OverdueAmount:     -100,
				},
			},
		},
		{
			Name: "rebalance-half-advance-1-log",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{150},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPaidPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1, types.NewMoney(50)),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         150,
					PurchaseCreditBalance: -50,
					InstallmentAmount:     -50,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 150,
					InstallmentAmount: 150,
					OutstandingAmount: -150,
					OverdueAmount:     -50,
				},
			},
		},
		{
			Name: "rebalance-half",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{100},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPaidPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1, types.NewMoney(50)),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: -50,
					InstallmentAmount:     -50,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 50,
					OutstandingAmount: -50,
					OverdueAmount:     -50,
				},
			},
		},
		{
			Name: "advance-more-than-balance",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{120},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 120,
					InstallmentAmount: 100,
					OutstandingAmount: -100,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "error-invalid-id",
			FileContent: makeFileContent(
				[]string{"invalid_id"},
				[]string{driverIDTestSet[0]},
				[]types.Money{120},
			),
			ReturnInstallments: func() []model.Installment {
				return nil
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			IsValidatedError:     true,
			ExpectedHttpCode:     http.StatusOK,
			ExpectSuccesses:      []installment.AdvanceInstallmentSuccessInfo{},
			ExpectContainInErros: []string{"invalid bson id"},
		},
		{
			Name: "advance-amount-less-than-daily",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{90},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         90,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 90,
					InstallmentAmount: 0,
					OutstandingAmount: 0,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "advance-amount-not-complete-clear-dpd",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{50},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         50,
					PurchaseCreditBalance: -100,
					InstallmentAmount:     -100,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 50,
					InstallmentAmount: 50,
					OutstandingAmount: -50,
					OverdueAmount:     -50,
				},
			},
		},
		{
			Name: "advance-amount-not-complete-clear-dpd",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{50},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         50,
					PurchaseCreditBalance: -100,
					InstallmentAmount:     -100,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 50,
					InstallmentAmount: 50,
					OutstandingAmount: -50,
					OverdueAmount:     -50,
				},
			},
		},
		{
			Name: "rebalancing-clear-only-expense",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{50},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         50,
					PurchaseCreditBalance: -150,
					InstallmentAmount:     -100,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 50,
					InstallmentAmount: 0,
					OutstandingAmount: 0,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "advance-2",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex(), objectIDs[1].Hex()},
				[]string{driverIDTestSet[0], driverIDTestSet[0]},
				[]types.Money{200, 100},
			),
			ReturnInstallments: func() []model.Installment {
				result := []model.Installment{
					installmentGenerator.SetDayPeriod(3).GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
					installmentGenerator.SetDayPeriod(3).GetFreshInstallment(objectIDs[1], driverIDTestSet[0]),
				}
				result[0].Product = &model.Product{
					Priority: 1,
				}
				result[1].Product = &model.Product{
					Priority: 2,
				}
				return result
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         400,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 200,
					InstallmentAmount: 200,
					OutstandingAmount: -200,
					OverdueAmount:     0,
				},
				{
					InstallmentID:     objectIDs[1].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 100,
					OutstandingAmount: -100,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "rebalance-2",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex(), objectIDs[1].Hex()},
				[]string{driverIDTestSet[0], driverIDTestSet[0]},
				[]types.Money{400, 100},
			),
			ReturnInstallments: func() []model.Installment {
				result := []model.Installment{
					installmentGenerator.SetDayPeriod(3).GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
					installmentGenerator.SetDayPeriod(3).GetPastDueInstallment(objectIDs[1], driverIDTestSet[0], 3),
				}
				result[0].Product = &model.Product{
					Priority: 1,
				}
				result[1].Product = &model.Product{
					Priority: 2,
				}
				return result
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         600,
					PurchaseCreditBalance: -400,
					InstallmentAmount:     -400,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 400,
					InstallmentAmount: 200,
					OutstandingAmount: -200,
					OverdueAmount:     -100,
				},
				{
					InstallmentID:     objectIDs[1].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 300,
					OutstandingAmount: -300,
					OverdueAmount:     -300,
				},
			},
		},
		{
			Name: "advance-1-over-installment",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{300},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.SetDayPeriod(1).GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         500,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 300,
					InstallmentAmount: 200,
					OutstandingAmount: -200,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "advance-3-but-swap-return-inst",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex(), objectIDs[1].Hex(), objectIDs[2].Hex()},
				[]string{driverIDTestSet[0], driverIDTestSet[0], driverIDTestSet[0]},
				[]types.Money{400, 100, 100},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.SetDayPeriod(2).GetFreshInstallment(objectIDs[2], driverIDTestSet[0]),
					installmentGenerator.SetDayPeriod(4).GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
					installmentGenerator.SetDayPeriod(0).GetFreshInstallment(objectIDs[1], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         300,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 400,
					InstallmentAmount: 300,
					OutstandingAmount: -300,
					OverdueAmount:     0,
				},
				{
					InstallmentID:     objectIDs[1].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 0,
					OutstandingAmount: 0,
					OverdueAmount:     0,
				},
				{
					InstallmentID:     objectIDs[2].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 0,
					OutstandingAmount: 0,
					OverdueAmount:     0,
				},
			},
		},
	}

	onceIfFalse := func(src bool) int {
		if !src {
			return 1
		}
		return 0
	}

	for index := range testSet {
		testData := testSet[index]
		tt.Run(testData.Name, func(t *testing.T) {
			gctx, recorder := makeReq(testData.FileContent)
			api, deps, finish := newTestInstallmentAPI(tt)
			defer finish()

			callTime := onceIfFalse(testData.IsValidatedError)
			// convertRequestToDataset
			deps.installmentRepo.EXPECT().
				FindWithQueryAndSort(gctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(testData.ReturnInstallments(), nil).
				Times(callTime)

			deps.txnHelper.EXPECT().
				WithTxn(gctx, gomock.Any(), gomock.Any()).
				Return(nil, nil).
				Times(callTime)

			deps.installmentRepo.EXPECT().
				FindByIDsWithProduct(gctx, gomock.Any(), gomock.Any()).
				Return(testData.ReturnInstallments(), nil).
				Times(callTime)

			deps.productGroupRepo.EXPECT().
				FindWithQueryAndSort(gctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.ProductGroup{}, nil).
				Times(callTime)

			deps.driverTransactionService.EXPECT().
				ProcessDriverTransaction(
					gctx,
					gomock.Any(),
					model.SystemTransactionChannel,
					model.AutoTransferWalletToCreditTransactionAction,
					model.SuccessTransactionStatus,
					gomock.Any(),
					gomock.Any(),
					gomock.Any(),
					gomock.Any(),
				).AnyTimes()

			deps.driverTransactionService.EXPECT().
				ProcessDriverTransaction(
					gctx,
					gomock.Any(),
					model.SystemTransactionChannel,
					model.InstallmentDeductionTransactionAction,
					model.SuccessTransactionStatus,
					gomock.Any(),
					gomock.Any(),
					gomock.Any(),
				).AnyTimes()

			api.BulkUploadAdvanceInstallments(gctx)

			require.Equal(tt, testData.ExpectedHttpCode, recorder.Code)
		})
	}
}

func TestBulkUploadAdvanceInstallmentsIMS(tt *testing.T) {
	type TestData struct {
		Name                 string
		FileContent          string
		ReturnInstallments   func() []model.Installment
		ReturnDriverTxns     []model.DriverTransaction
		IsValidatedError     bool
		ExpectedHttpCode     int
		ExpectSuccesses      []installment.AdvanceInstallmentSuccessInfo
		ExpectContainInErros []string
	}

	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/upload-advance-installments")
		gctx.Body().MultipartForm().
			File("file", "file.csv", content).
			String("requestedBy", "anonymous").
			Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	makeFileContent := func(installmentIDs []string, driverIDs []string, amount []types.Money) string {
		var contents []string
		header := "installment id, driver id, advancement amount"
		for index := range installmentIDs {
			contents = append(contents, fmt.Sprintf("%s,%s,%v", installmentIDs[index], driverIDs[index], amount[index]))
		}
		return fmt.Sprintf("%s\n%s", header, strings.Join(contents, "\n"))
	}

	objectIDs := []primitive.ObjectID{primitive.NewObjectID(), primitive.NewObjectID(), primitive.NewObjectID()}
	driverIDTestSet := []string{"DRIVER_ID", "DRIVER_ID_2"}

	installmentGenerator := InstallmentGenerator{
		DailyAmount: 100,
		DayPeriod:   1,
		Timenow:     time.Now(),
	}

	testSet := []TestData{
		{
			Name: "advance-1",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{100},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 100,
					OutstandingAmount: -100,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "rebalance-1",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{100},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: -100,
					InstallmentAmount:     -100,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 100,
					OutstandingAmount: -100,
					OverdueAmount:     -100,
				},
			},
		},
		{
			Name: "rebalance-half-advance-1-log",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{150},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPaidPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1, types.NewMoney(50)),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         150,
					PurchaseCreditBalance: -50,
					InstallmentAmount:     -50,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 150,
					InstallmentAmount: 150,
					OutstandingAmount: -150,
					OverdueAmount:     -50,
				},
			},
		},
		{
			Name: "rebalance-half",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{100},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPaidPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1, types.NewMoney(50)),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: -50,
					InstallmentAmount:     -50,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 50,
					OutstandingAmount: -50,
					OverdueAmount:     -50,
				},
			},
		},
		{
			Name: "advance-more-than-balance",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{120},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 120,
					InstallmentAmount: 100,
					OutstandingAmount: -100,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "error-invalid-id",
			FileContent: makeFileContent(
				[]string{"invalid_id"},
				[]string{driverIDTestSet[0]},
				[]types.Money{120},
			),
			ReturnInstallments: func() []model.Installment {
				return nil
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         100,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			IsValidatedError:     true,
			ExpectedHttpCode:     http.StatusOK,
			ExpectSuccesses:      []installment.AdvanceInstallmentSuccessInfo{},
			ExpectContainInErros: []string{"invalid bson id"},
		},
		{
			Name: "advance-amount-less-than-daily",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{90},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         90,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 90,
					InstallmentAmount: 0,
					OutstandingAmount: 0,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "advance-amount-not-complete-clear-dpd",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{50},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         50,
					PurchaseCreditBalance: -100,
					InstallmentAmount:     -100,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 50,
					InstallmentAmount: 50,
					OutstandingAmount: -50,
					OverdueAmount:     -50,
				},
			},
		},
		{
			Name: "advance-amount-not-complete-clear-dpd",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{50},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         50,
					PurchaseCreditBalance: -100,
					InstallmentAmount:     -100,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 50,
					InstallmentAmount: 50,
					OutstandingAmount: -50,
					OverdueAmount:     -50,
				},
			},
		},
		{
			Name: "rebalancing-clear-only-expense",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{50},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         50,
					PurchaseCreditBalance: -150,
					InstallmentAmount:     -100,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 50,
					InstallmentAmount: 0,
					OutstandingAmount: 0,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "advance-2",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex(), objectIDs[1].Hex()},
				[]string{driverIDTestSet[0], driverIDTestSet[0]},
				[]types.Money{200, 100},
			),
			ReturnInstallments: func() []model.Installment {
				result := []model.Installment{
					installmentGenerator.SetDayPeriod(3).GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
					installmentGenerator.SetDayPeriod(3).GetFreshInstallment(objectIDs[1], driverIDTestSet[0]),
				}
				result[0].Product = &model.Product{
					Priority: 1,
				}
				result[1].Product = &model.Product{
					Priority: 2,
				}
				return result
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         400,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 200,
					InstallmentAmount: 200,
					OutstandingAmount: -200,
					OverdueAmount:     0,
				},
				{
					InstallmentID:     objectIDs[1].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 100,
					OutstandingAmount: -100,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "rebalance-2",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex(), objectIDs[1].Hex()},
				[]string{driverIDTestSet[0], driverIDTestSet[0]},
				[]types.Money{400, 100},
			),
			ReturnInstallments: func() []model.Installment {
				result := []model.Installment{
					installmentGenerator.SetDayPeriod(3).GetPastDueInstallment(objectIDs[0], driverIDTestSet[0], 1),
					installmentGenerator.SetDayPeriod(3).GetPastDueInstallment(objectIDs[1], driverIDTestSet[0], 3),
				}
				result[0].Product = &model.Product{
					Priority: 1,
				}
				result[1].Product = &model.Product{
					Priority: 2,
				}
				return result
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         600,
					PurchaseCreditBalance: -400,
					InstallmentAmount:     -400,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 400,
					InstallmentAmount: 200,
					OutstandingAmount: -200,
					OverdueAmount:     -100,
				},
				{
					InstallmentID:     objectIDs[1].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 300,
					OutstandingAmount: -300,
					OverdueAmount:     -300,
				},
			},
		},
		{
			Name: "advance-1-over-installment",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex()},
				[]string{driverIDTestSet[0]},
				[]types.Money{300},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.SetDayPeriod(1).GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         500,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 300,
					InstallmentAmount: 200,
					OutstandingAmount: -200,
					OverdueAmount:     0,
				},
			},
		},
		{
			Name: "advance-3-but-swap-return-inst",
			FileContent: makeFileContent(
				[]string{objectIDs[0].Hex(), objectIDs[1].Hex(), objectIDs[2].Hex()},
				[]string{driverIDTestSet[0], driverIDTestSet[0], driverIDTestSet[0]},
				[]types.Money{400, 100, 100},
			),
			ReturnInstallments: func() []model.Installment {
				return []model.Installment{
					installmentGenerator.SetDayPeriod(2).GetFreshInstallment(objectIDs[2], driverIDTestSet[0]),
					installmentGenerator.SetDayPeriod(4).GetFreshInstallment(objectIDs[0], driverIDTestSet[0]),
					installmentGenerator.SetDayPeriod(0).GetFreshInstallment(objectIDs[1], driverIDTestSet[0]),
				}
			},
			ReturnDriverTxns: []model.DriverTransaction{
				{
					DriverID:              driverIDTestSet[0],
					WalletBalance:         300,
					PurchaseCreditBalance: 0,
					InstallmentAmount:     0,
				},
			},
			ExpectedHttpCode: http.StatusOK,
			ExpectSuccesses: []installment.AdvanceInstallmentSuccessInfo{
				{
					InstallmentID:     objectIDs[0].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 400,
					InstallmentAmount: 300,
					OutstandingAmount: -300,
					OverdueAmount:     0,
				},
				{
					InstallmentID:     objectIDs[1].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 0,
					OutstandingAmount: 0,
					OverdueAmount:     0,
				},
				{
					InstallmentID:     objectIDs[2].Hex(),
					DriverID:          driverIDTestSet[0],
					AdvancementAmount: 100,
					InstallmentAmount: 0,
					OutstandingAmount: 0,
					OverdueAmount:     0,
				},
			},
		},
	}

	onceIfFalse := func(src bool) int {
		if !src {
			return 1
		}
		return 0
	}

	for index := range testSet {
		testData := testSet[index]
		tt.Run(testData.Name, func(t *testing.T) {
			gctx, recorder := makeReq(testData.FileContent)
			api, deps, finish := newTestInstallmentAPI(tt, installment.Config{EnableInventoryManagementService: true})
			defer finish()

			callTime := onceIfFalse(testData.IsValidatedError)
			// convertRequestToDataset
			deps.installmentRepo.EXPECT().
				FindWithQueryAndSort(gctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(testData.ReturnInstallments(), nil).
				Times(callTime)

			deps.txnHelper.EXPECT().
				WithTxn(gctx, gomock.Any(), gomock.Any()).
				Return(nil, nil).
				Times(callTime)

			deps.installmentRepo.EXPECT().
				FindByIDsWithProduct(gctx, gomock.Any(), gomock.Any()).
				Return(testData.ReturnInstallments(), nil).
				Times(callTime)

			grpcPriorityGroupResponse := inventoryPb.GetProductWithPriorityGroupResponse{}
			deps.productService.EXPECT().
				GetProductWithPriorityGroup(gctx, gomock.Any()).
				Return(&grpcPriorityGroupResponse, nil).
				Times(callTime)

			deps.driverTransactionService.EXPECT().
				ProcessDriverTransaction(
					gctx,
					gomock.Any(),
					model.SystemTransactionChannel,
					model.AutoTransferWalletToCreditTransactionAction,
					model.SuccessTransactionStatus,
					gomock.Any(),
					gomock.Any(),
					gomock.Any(),
					gomock.Any(),
				).AnyTimes()

			deps.driverTransactionService.EXPECT().
				ProcessDriverTransaction(
					gctx,
					gomock.Any(),
					model.SystemTransactionChannel,
					model.InstallmentDeductionTransactionAction,
					model.SuccessTransactionStatus,
					gomock.Any(),
					gomock.Any(),
					gomock.Any(),
				).AnyTimes()

			api.BulkUploadAdvanceInstallments(gctx)

			require.Equal(tt, testData.ExpectedHttpCode, recorder.Code)
		})
	}
}
