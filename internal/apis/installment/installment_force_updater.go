package installment

import (
	"context"

	"github.com/gin-gonic/gin"

	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func (ia *InstallmentAPI) BulkForceUpdateProductDetail(ctx *gin.Context) {
	var req BulkForceUpdateProductDetailRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(absintheapi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	admin := auth.GetAdminEmailFromGctx(ctx)

	bulkData, err := req.GetCSVData()
	if err != nil {
		apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
		return
	}

	response := BulkForceUpdateProductDetailResponse{
		Successes: make([]BulkForceUpdateProductDetailInfo, 0),
		Failures:  make([]BulkForceUpdateProductDetailInfo, 0),
	}

	for _, data := range bulkData {
		if !data.ErrorReporter.IsEmpty() {
			response.Failures = append(response.Failures, BulkForceUpdateProductDetailInfo{
				Row:           data.RowIndex + 1,
				InstallmentID: data.UpdateData.InstallmentID,
				DriverID:      data.UpdateData.DriverID,
				Reason:        data.ErrorReporter.ConvertToReport()[0].Error,
			})
			continue
		}
		ia.doForceUpdateProductDetail(ctx, admin, data, &response)
	}

	apiutil.OK(ctx, response)
}

func (ia *InstallmentAPI) doForceUpdateProductDetail(ctx context.Context, requester string, data ForceUpdateInstallmentProductDetailData, resp *BulkForceUpdateProductDetailResponse) {
	updateData := data.UpdateData

	newBulkForceUpdateProductDetailInfo := BulkForceUpdateProductDetailInfo{
		Row:           data.RowIndex + 1,
		InstallmentID: updateData.InstallmentID,
		DriverID:      updateData.DriverID,
	}

	if updateData.InstallmentID == "" || updateData.DriverID == "" {
		newBulkForceUpdateProductDetailInfo.Reason = "installment id and driver id cannot empty"
		resp.AddFailure(newBulkForceUpdateProductDetailInfo)
		return
	}

	err := ia.installmentService.ForceUpdateInstallmentsProductDetail(ctx, requester, model.InstallmentProductDetailForceUpdater{
		InstallmentID:                  updateData.InstallmentID,
		DriverID:                       updateData.DriverID,
		ProductName:                    updateData.ProductName,
		SKU:                            updateData.SKU,
		Principal:                      updateData.Principal,
		Batch:                          updateData.Batch,
		ProductDetailSKU:               updateData.ProductDetailSKU,
		NewProductDetailSKU:            updateData.NewProductDetailSKU,
		NewProductDetailStockID:        updateData.NewProductDetailStockID,
		NewProductDetailStockPriority:  updateData.NewProductDetailStockPriority,
		NewProductDetailPrincipalPrice: updateData.NewProductDetailPrincipalPrice,
		NewProductDetailBarcode:        updateData.NewProductDetailBarcode,
	})
	if err != nil {
		newBulkForceUpdateProductDetailInfo.Reason = err.Error()
		resp.AddFailure(newBulkForceUpdateProductDetailInfo)
		return
	}

	resp.AddSuccess(newBulkForceUpdateProductDetailInfo)

}
