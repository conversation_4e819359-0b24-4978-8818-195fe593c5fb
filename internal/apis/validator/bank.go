package validator

import (
	ut "github.com/go-playground/universal-translator"
	"gopkg.in/go-playground/validator.v9"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type Bank struct {
	errorMessage map[string]string
}

func (p *Bank) Validator() func(fl validator.FieldLevel) bool {
	return func(fl validator.FieldLevel) bool {
		bank := model.BankByName(fl.Field().String())
		return bank != model.UndefinedBank
	}
}

func (p *Bank) Name() string {
	return "bank"
}

func (p *Bank) RegisterTranslation(v *validator.Validate, uni *ut.UniversalTranslator) {
	for _, locale := range AvailableLocales {
		tran, found := uni.GetTranslator(locale)
		msg, ok := p.errorMessage[locale]
		if !ok {
			msg = p.errorMessage[DefaultLocale]
		}
		if found {
			err := v.RegisterTranslation(p.Name(), tran, func(tr ut.Translator) error {
				return tr.Add(p.Name(), msg, false)
			}, func(tr ut.Translator, fe validator.FieldError) string {
				msg, _ := tr.T(p.Name(), fe.Field())
				return msg
			})
			if err != nil {
				panic(err)
			}
		}
	}
	err := v.RegisterValidation(p.Name(), p.Validator())
	if err != nil {
		panic(err)
	}
}

func NewBank() *Bank {
	return &Bank{
		errorMessage: map[string]string{
			LocaleEN: "{0} must be bank correct bank code",
			LocaleTH: "{0} ไม่ถูกต้อง",
		},
	}
}
