package validator

import (
	"regexp"

	ut "github.com/go-playground/universal-translator"
	"gopkg.in/go-playground/validator.v9"
)

type ReferrerCode struct {
	referrerCodeRegex *regexp.Regexp
	errorMessage      map[string]string
}

func (p *ReferrerCode) Validator() func(fl validator.FieldLevel) bool {
	return func(fl validator.FieldLevel) bool {
		return p.referrerCodeRegex.MatchString(fl.Field().String())
	}
}

func (p *ReferrerCode) Name() string {
	return "referrerCode"
}

func (p *ReferrerCode) RegisterTranslation(v *validator.Validate, uni *ut.UniversalTranslator) {
	for _, locale := range AvailableLocales {
		tran, found := uni.GetTranslator(locale)
		msg, ok := p.errorMessage[locale]
		if !ok {
			msg = p.errorMessage[DefaultLocale]
		}
		if found {
			err := v.RegisterTranslation(p.Name(), tran, func(tr ut.Translator) error {
				return tr.Add(p.Name(), msg, false)
			}, func(tr ut.Translator, fe validator.FieldError) string {
				msg, _ := tr.T(p.Name(), fe.Field())
				return msg
			})
			if err != nil {
				panic(err)
			}
		}
	}
	err := v.RegisterValidation(p.Name(), p.Validator())
	if err != nil {
		panic(err)
	}
}

func NewReferrerCode() *ReferrerCode {
	regx, err := regexp.Compile("^[a-zA-z0-9]{4,10}$")
	if err != nil {
		panic(err)
	}
	return &ReferrerCode{
		referrerCodeRegex: regx,
		errorMessage: map[string]string{
			LocaleEN: "{0} Must be only mix letters and number length between 4 - 10",
			LocaleTH: "{0} ตัวเลขและตัวอักษาภาษาอังกฤษเท่านั้นและมีความยาว 4 ถึง 10 ตัวอักษร",
		},
	}
}
