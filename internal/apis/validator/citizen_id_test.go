package validator

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"gopkg.in/go-playground/validator.v9"
)

func TestCitizenID_Validator(t *testing.T) {
	type CitizenIDStruct struct {
		CitizenID string `validate:"citizen-id"`
	}

	testcases := []struct {
		number string
		error  bool
	}{
		{number: "1333572828703", error: false},
		{number: "1858941665036", error: false},
		{number: "6813526025624", error: false},
		{number: "5673202454248", error: false},
		{number: "3010297581796", error: false},
		{number: "1333272828703", error: true},
		{number: "1358941665036", error: true},
		{number: "6213526025624", error: true},
		{number: "2633202454248", error: true},
		{number: "3001297581796", error: true},
	}

	citizenValidator := NewCitizenID()

	validate := validator.New()
	validate.RegisterValidation(citizenValidator.Name(), citizenValidator.Validator(), true)
	for id, tc := range testcases {
		err := validate.Struct(CitizenIDStruct{
			CitizenID: tc.number,
		})

		if tc.error {
			require.Error(t, err, fmt.Sprintf("row : %d", id+1))
		} else {
			require.NoError(t, err, fmt.Sprintf("row : %d", id+1))
		}
	}
}
