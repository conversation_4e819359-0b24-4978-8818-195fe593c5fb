package th

import (
	"fmt"
	"log"
	"reflect"
	"strconv"
	"strings"
	"time"

	ut "github.com/go-playground/universal-translator"
	"gopkg.in/go-playground/validator.v9"
)

// RegisterDefaultTranslations registers a set of default translations
// for all built in tag's in validator; you may add your own as desired.
func RegisterDefaultTranslations(v *validator.Validate, trans ut.Translator) (err error) {

	translations := []struct {
		tag             string
		translation     string
		override        bool
		customRegisFunc validator.RegisterTranslationsFunc
		customTransFunc validator.TranslationFunc
	}{
		{
			tag:         "required",
			translation: "กรุณาระบุ {0}",
			override:    false,
		},
		{
			tag: "len",
			customRegisFunc: func(ut ut.Translator) (err error) {

				if err = ut.Add("len-string", "{0} ต้องมีความยาว {1} ตัวอักษร", false); err != nil {
					return
				}

				if err = ut.Add("len-number", "{0} ต้องเท่ากับ {1}", false); err != nil {
					return
				}

				if err = ut.Add("len-items", "{0} ต้องมีทั้งหมด {1} ตัว", false); err != nil {
					return
				}

				return

			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string

				var digits uint64
				var kind reflect.Kind

				if idx := strings.Index(fe.Param(), "."); idx != -1 {
					digits = uint64(len(fe.Param()[idx+1:]))
				}

				f64, err := strconv.ParseFloat(fe.Param(), 64)
				if err != nil {
					goto END
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:
					t, err = ut.T("len-string", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Slice, reflect.Map, reflect.Array:
					t, err = ut.T("len-items", fe.Field(), ut.FmtNumber(f64, digits))
				default:
					t, err = ut.T("len-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "min",
			customRegisFunc: func(ut ut.Translator) (err error) {

				if err = ut.Add("min-string", "{0} ต้องมีความยาวอย่างน้อย {1} ตัวอักษร", false); err != nil {
					return
				}

				if err = ut.Add("min-number", "{0} ต้องมีค่ามากกว่าหรือเท่ากับ {1}", false); err != nil {
					return
				}

				if err = ut.Add("min-items", "{0} ต้องมีอย่างน้อย {1} ตัว", false); err != nil {
					return
				}

				return

			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string

				var digits uint64
				var kind reflect.Kind

				if idx := strings.Index(fe.Param(), "."); idx != -1 {
					digits = uint64(len(fe.Param()[idx+1:]))
				}

				f64, err := strconv.ParseFloat(fe.Param(), 64)
				if err != nil {
					goto END
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:
					t, err = ut.T("min-string", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Slice, reflect.Map, reflect.Array:
					t, err = ut.T("min-items", fe.Field(), ut.FmtNumber(f64, digits))
				default:
					t, err = ut.T("min-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "max",
			customRegisFunc: func(ut ut.Translator) (err error) {

				if err = ut.Add("max-string", "{0} มีความยาวได้มากที่สุด {1} ตัวอักษร", false); err != nil {
					return
				}

				if err = ut.Add("max-number", "{0} ต้องมีค่าน้อยกว่าหรือเท่ากับ {1}", false); err != nil {
					return
				}

				if err = ut.Add("max-items", "{0} มีได้มากที่สุด {1} ตัว", false); err != nil {
					return
				}

				return

			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string

				var digits uint64
				var kind reflect.Kind

				if idx := strings.Index(fe.Param(), "."); idx != -1 {
					digits = uint64(len(fe.Param()[idx+1:]))
				}

				f64, err := strconv.ParseFloat(fe.Param(), 64)
				if err != nil {
					goto END
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:
					t, err = ut.T("max-string", fe.Field(), ut.FmtNumber(f64, digits))

				case reflect.Slice, reflect.Map, reflect.Array:
					t, err = ut.T("max-items", fe.Field(), ut.FmtNumber(f64, digits))

				default:
					t, err = ut.T("max-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag:         "eq",
			translation: "{0} is not equal to {1}",
			override:    false,
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				t, err := ut.T(fe.Tag(), fe.Field(), fe.Param())
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %#v", fe)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag:         "ne",
			translation: "{0} should not be equal to {1}",
			override:    false,
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				t, err := ut.T(fe.Tag(), fe.Field(), fe.Param())
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %#v", fe)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "lt",
			customRegisFunc: func(ut ut.Translator) (err error) {

				if err = ut.Add("lt-string", "{0} ต้องมีความยาวน้อยกว่า {1} ตัวอักษร", false); err != nil {
					return
				}

				if err = ut.Add("lt-number", "{0} ต้องน้อยกว่า {1}", false); err != nil {
					return
				}

				if err = ut.Add("lt-items", "{0} ต้องมีน้อยกว่า {1} ตัว", false); err != nil {
					return
				}

				if err = ut.Add("lt-datetime", "{0} ต้องเกิดก่อนเวลาปัจจุบัน", false); err != nil {
					return
				}

				return

			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string
				var f64 float64
				var digits uint64
				var kind reflect.Kind

				fn := func() (err error) {

					if idx := strings.Index(fe.Param(), "."); idx != -1 {
						digits = uint64(len(fe.Param()[idx+1:]))
					}

					f64, err = strconv.ParseFloat(fe.Param(), 64)

					return
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:
					t, err = ut.T("lt-string", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Slice, reflect.Map, reflect.Array:
					t, err = ut.T("lt-items", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Struct:
					if fe.Type() != reflect.TypeOf(time.Time{}) {
						err = fmt.Errorf("tag '%s' cannot be used on a struct type", fe.Tag())
						goto END
					}

					t, err = ut.T("lt-datetime", fe.Field())

				default:
					err = fn()
					if err != nil {
						goto END
					}

					t, err = ut.T("lt-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "lte",
			customRegisFunc: func(ut ut.Translator) (err error) {

				if err = ut.Add("lte-string", "{0} ต้องมีความยาวน้อยกว่าหรือเท่ากับ {1} ตัวอักษร", false); err != nil {
					return
				}

				if err = ut.Add("lte-number", "{0} ต้องน้อยกว่าหรือเท่ากับ {1}", false); err != nil {
					return
				}

				if err = ut.Add("lte-items", "{0} ต้องมีน้อยกว่าหรือเท่ากับ {1} ตัว", false); err != nil {
					return
				}

				if err = ut.Add("lte-datetime", "{0} ต้องเกิดก่อนหรือเทียบเท่าเวลาปัจจุบัน", false); err != nil {
					return
				}

				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string
				var f64 float64
				var digits uint64
				var kind reflect.Kind

				fn := func() (err error) {

					if idx := strings.Index(fe.Param(), "."); idx != -1 {
						digits = uint64(len(fe.Param()[idx+1:]))
					}

					f64, err = strconv.ParseFloat(fe.Param(), 64)

					return
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:
					t, err = ut.T("lte-string", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Slice, reflect.Map, reflect.Array:
					t, err = ut.T("lte-items", fe.Field(), ut.FmtNumber(f64, digits))

				case reflect.Struct:
					if fe.Type() != reflect.TypeOf(time.Time{}) {
						err = fmt.Errorf("tag '%s' cannot be used on a struct type", fe.Tag())
						goto END
					}

					t, err = ut.T("lte-datetime", fe.Field())

				default:
					err = fn()
					if err != nil {
						goto END
					}

					t, err = ut.T("lte-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "gt",
			customRegisFunc: func(ut ut.Translator) (err error) {

				if err = ut.Add("gt-string", "{0} ต้องมีความยาวมากกว่า {1} ตัวอักษร", false); err != nil {
					return
				}

				if err = ut.Add("gt-number", "{0} ต้องมากกว่า {1}", false); err != nil {
					return
				}

				if err = ut.Add("gt-items", "{0} ต้องมีมากกว่า {1} ตัว", false); err != nil {
					return
				}

				if err = ut.Add("gt-datetime", "{0} ต้องเกิดหลังเวลาปัจจุบัน", false); err != nil {
					return
				}

				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string
				var f64 float64
				var digits uint64
				var kind reflect.Kind

				fn := func() (err error) {

					if idx := strings.Index(fe.Param(), "."); idx != -1 {
						digits = uint64(len(fe.Param()[idx+1:]))
					}

					f64, err = strconv.ParseFloat(fe.Param(), 64)

					return
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:
					t, err = ut.T("gt-string", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Slice, reflect.Map, reflect.Array:
					t, err = ut.T("gt-items", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Struct:
					if fe.Type() != reflect.TypeOf(time.Time{}) {
						err = fmt.Errorf("tag '%s' cannot be used on a struct type", fe.Tag())
						goto END
					}

					t, err = ut.T("gt-datetime", fe.Field())

				default:
					err = fn()
					if err != nil {
						goto END
					}

					t, err = ut.T("gt-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
		{
			tag: "gte",
			customRegisFunc: func(ut ut.Translator) (err error) {

				if err = ut.Add("gte-string", "{0} ต้องมีความยาวมากกว่าหรือเท่ากับ {1} ตัวอักษร", false); err != nil {
					return
				}

				if err = ut.Add("gte-number", "{0} ต้องมากกว่าหรือเท่ากับ {1}", false); err != nil {
					return
				}

				if err = ut.Add("gte-items", "{0} ต้องมีมากกว่าหรือเท่ากับ {1} ตัว", false); err != nil {
					return
				}

				if err = ut.Add("gte-datetime", "{0} ต้องเกิดหลังหรือเทียบเท่าเวลาปัจจุบัน", false); err != nil {
					return
				}

				return
			},
			customTransFunc: func(ut ut.Translator, fe validator.FieldError) string {

				var err error
				var t string
				var f64 float64
				var digits uint64
				var kind reflect.Kind

				fn := func() (err error) {

					if idx := strings.Index(fe.Param(), "."); idx != -1 {
						digits = uint64(len(fe.Param()[idx+1:]))
					}

					f64, err = strconv.ParseFloat(fe.Param(), 64)

					return
				}

				kind = fe.Kind()
				if kind == reflect.Ptr {
					kind = fe.Type().Elem().Kind()
				}

				switch kind {
				case reflect.String:
					t, err = ut.T("gte-string", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Slice, reflect.Map, reflect.Array:
					t, err = ut.T("gte-items", fe.Field(), ut.FmtNumber(f64, digits))
				case reflect.Struct:
					if fe.Type() != reflect.TypeOf(time.Time{}) {
						err = fmt.Errorf("tag '%s' cannot be used on a struct type", fe.Tag())
						goto END
					}

					t, err = ut.T("gte-datetime", fe.Field())
				default:
					err = fn()
					if err != nil {
						goto END
					}

					t, err = ut.T("gte-number", fe.Field(), ut.FmtNumber(f64, digits))
				}

			END:
				if err != nil {
					fmt.Printf("warning: error translating FieldError: %s", err)
					return fe.(error).Error()
				}

				return t
			},
		},
	}

	for _, t := range translations {

		if t.customTransFunc != nil && t.customRegisFunc != nil {

			err = v.RegisterTranslation(t.tag, trans, t.customRegisFunc, t.customTransFunc)

		} else if t.customTransFunc != nil && t.customRegisFunc == nil {

			err = v.RegisterTranslation(t.tag, trans, registrationFunc(t.tag, t.translation, t.override), t.customTransFunc)

		} else if t.customTransFunc == nil && t.customRegisFunc != nil {

			err = v.RegisterTranslation(t.tag, trans, t.customRegisFunc, translateFunc)

		} else {
			err = v.RegisterTranslation(t.tag, trans, registrationFunc(t.tag, t.translation, t.override), translateFunc)
		}

		if err != nil {
			return
		}
	}

	return
}

func registrationFunc(tag string, translation string, override bool) validator.RegisterTranslationsFunc {

	return func(ut ut.Translator) (err error) {

		if err = ut.Add(tag, translation, override); err != nil {
			return
		}

		return

	}

}

func translateFunc(ut ut.Translator, fe validator.FieldError) string {

	t, err := ut.T(fe.Tag(), fe.Field())
	if err != nil {
		log.Printf("warning: error translating FieldError: %#v", fe)
		return fe.(error).Error()
	}

	return t
}
