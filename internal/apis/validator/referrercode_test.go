package validator

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
	"gopkg.in/go-playground/validator.v9"
)

func TestReferrerCode_Name(t *testing.T) {
	type ReferrerCode struct {
		ReferrerCode string `validate:"referrerCode"`
	}

	testcases := []struct {
		text  string
		error bool
	}{
		{text: "1q2w3e4r5t", error: false},
		{text: "1q2w", error: false},
		{text: "1q2w3e4R5T", error: false},
		{text: "1q2w3e4R5T     ", error: true},
		{text: "1q2w3e4r5t6y7u", error: true},
		{text: "1234567890aaaaa", error: true},
	}

	referrerCodeValidator := NewReferrerCode()

	validate := validator.New()
	validate.RegisterValidation(referrerCodeValidator.Name(), referrerCodeValidator.Validator(), true)
	for id, tc := range testcases {
		err := validate.Struct(ReferrerCode{
			ReferrerCode: tc.text,
		})
		if tc.error {
			require.Error(t, err, fmt.Sprintf("row : %d", id+1))
		} else {
			require.NoError(t, err, fmt.Sprintf("row : %d", id+1))
		}
	}
}
