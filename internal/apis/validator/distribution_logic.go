package validator

import (
	ut "github.com/go-playground/universal-translator"
	"gopkg.in/go-playground/validator.v9"
)

type DistributionLogic struct {
	logics       []string
	errorMessage map[string]string
}

func (p *DistributionLogic) Validator() func(fl validator.FieldLevel) bool {
	return func(fl validator.FieldLevel) bool {
		word := fl.Field().String()

		for _, l := range p.logics {
			if word == l {
				return true
			}
		}

		return false
	}
}

func (p *DistributionLogic) Name() string {
	return "distribution-logic"
}

func (p *DistributionLogic) RegisterTranslation(v *validator.Validate, uni *ut.UniversalTranslator) {
	for _, locale := range AvailableLocales {
		tran, found := uni.GetTranslator(locale)
		msg, ok := p.errorMessage[locale]
		if !ok {
			msg = p.errorMessage[DefaultLocale]
		}
		if found {
			err := v.RegisterTranslation(p.Name(), tran, func(tr ut.Translator) error {
				return tr.Add(p.Name(), msg, false)
			}, func(tr ut.Translator, fe validator.FieldError) string {
				msg, _ := tr.T(p.Name(), fe.Field())
				return msg
			})
			if err != nil {
				panic(err)
			}
		}
	}
	err := v.RegisterValidation(p.Name(), p.Validator())
	if err != nil {
		panic(err)
	}
}

func NewDistributionLogic() *DistributionLogic {
	return &DistributionLogic{
		logics: []string{"AUTO_ASSIGN"},
		errorMessage: map[string]string{
			LocaleEN: "{0} is invalid",
			LocaleTH: "{0} ไม่ถูกต้อง",
		},
	}
}
