package payment

import (
	"errors"
	"fmt"

	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const (
	GroupTransactionIdParamKey = "id"
)

type GroupTransactionAPI struct {
	GroupTxnRepo          repository.GroupTransactionRepository
	DriverTxnRepo         repository.DriverTransactionRepository
	TxnRepo               repository.TransactionRepository
	OrderRepo             repository.OrderRepository
	TransactionSchemeRepo repository.TransactionSchemeRepository
	PaymentConfig         config.PaymentConfig
}

func ProvideGroupTransactionAPI(groupTxnRepo repository.GroupTransactionRepository, driverTxnRepo repository.DriverTransactionRepository, txnRepo repository.TransactionRepository, orderRepo repository.OrderRepository, transactionSchemeRepo repository.TransactionSchemeRepository, cfg config.PaymentConfig) *GroupTransactionAPI {
	return &GroupTransactionAPI{
		GroupTxnRepo:          groupTxnRepo,
		DriverTxnRepo:         driverTxnRepo,
		TransactionSchemeRepo: transactionSchemeRepo,
		TxnRepo:               txnRepo,
		OrderRepo:             orderRepo,
		PaymentConfig:         cfg,
	}
}

func (gta *GroupTransactionAPI) Create(gctx *gin.Context) {
	var req CreateGroupTransactionReq
	if err := gctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(gctx, utils.ErrorStructResponse(err))
		return
	}

	ctx := gctx.Request.Context()

	if !req.EffectiveTime.IsZero() {
		if req.EffectiveTime.Before(timeutil.BangkokNow()) {
			apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.New("cannot set effective time equal or less than now")))
			return
		}
	}

	groupTxn, errs := req.GroupTransaction(ctx, gta.OrderRepo, gta.TransactionSchemeRepo, gta.PaymentConfig.WithHoldingTax)
	if errs != nil {
		switch t := errs.(type) {
		case apiError.MultipleError:
			apiutil.ErrBadRequest(gctx, t.APIError())
		default:
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(t))
		}
		return
	}

	if !req.EffectiveTime.IsZero() {
		if len(groupTxn.Items()) > gta.PaymentConfig.GroupTransactionWithEffectiveTimeItemsLimit {
			apiutil.ErrInternalError(
				gctx,
				apiutil.NewFromError(
					api.ERRCODE_INVALID_REQUEST,
					fmt.Errorf("group transaction items are limited at %v for group transaction with effective time", gta.PaymentConfig.GroupTransactionWithEffectiveTimeItemsLimit),
				),
			)
			return
		}
	}

	if err := gta.GroupTxnRepo.Create(ctx, groupTxn); err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	apiutil.Created(gctx, NewGroupTransactionDetailRes(*groupTxn))
}

func (gta *GroupTransactionAPI) Get(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	id := gctx.Param(GroupTransactionIdParamKey)

	groupTxn, err := gta.GroupTxnRepo.Get(ctx, id)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, ErrGroupTransactionNotFound())
		} else {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		}
		return
	}
	apiutil.OK(gctx, NewGroupTransactionDetailRes(*groupTxn))
}

func (gta *GroupTransactionAPI) List(gctx *gin.Context) {
	var req ListGroupTransactionReq
	if err := gctx.ShouldBindQuery(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	ctx := gctx.Request.Context()
	query := req.Query()
	skip, size := utils.ParsePagination(gctx)

	groupTxns, err := gta.GroupTxnRepo.Find(ctx, query, skip, size)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	count, err := gta.GroupTxnRepo.Count(ctx, query)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	res := make([]GroupTransactionRes, len(groupTxns))
	for i, groupTxn := range groupTxns {
		res[i] = NewGroupTransactionRes(groupTxn)
	}

	apiutil.OKList(gctx, res, count)
}

func (gta *GroupTransactionAPI) Approve(gctx *gin.Context) {
	req := ApprovedGroupTransactionReq{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	ctx := gctx.Request.Context()
	id := gctx.Param(GroupTransactionIdParamKey)

	gt, err := gta.GroupTxnRepo.Get(ctx, id, repository.WithReadPrimary)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, ErrGroupTransactionNotFound())
		} else {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		}
		return
	}

	driverTxns, err := gta.DriverTxnRepo.FindByIDs(ctx, gt.UniqueDriverIDs(), repository.WithReadPrimary)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	updateDriverTxns := []model.DriverTransaction{}
	txns := []model.Transaction{}
	if gt.EffectiveTime() != nil {
		if !gt.EffectiveTime().IsZero() {
			if gt.EffectiveTime().Before(timeutil.BangkokNow()) {
				err = gt.Reject(req.RequestedBy)
				if err != nil {
					apiutil.ErrBadRequest(gctx, ErrGroupTransactionInvalidTransitStatus(
						gt.Status(),
						model.GroupTransactionStatusRejected,
					))
					return
				}
			} else {
				err = gt.Approve(req.RequestedBy)
				if err != nil {
					apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
					return
				}
			}
		} else {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(errors.New("effective time error")))
			return
		}
	} else {
		err = gt.Approve(req.RequestedBy)
		if err != nil {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
			return
		}
		updateDriverTxns, txns, err = gt.Process(req.RequestedBy, driverTxns, model.AdminTransactionChannel)
		if err != nil {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
			return
		}
	}

	err = gta.GroupTxnRepo.Update(ctx, gt)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	for i, size := 0, len(updateDriverTxns); i < size; i++ {
		updatedDriverTxn := &updateDriverTxns[i]
		err := gta.DriverTxnRepo.Update(ctx, updatedDriverTxn)
		if err != nil {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
			return
		}
	}

	if len(txns) > 0 {
		err = gta.TxnRepo.CreateAll(ctx, txns)
		if err != nil {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
			return
		}
	}
	if gt.Status() == model.GroupTransactionStatusRejected {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(errors.New("Group transaction is rejected, cannot approve after the effective time is pass. Please bulk wallet/credit request again.")))
	} else {
		apiutil.OK(gctx, NewUpdateGroupTransactionRes(txns))
	}
}

func (gta *GroupTransactionAPI) Reject(gctx *gin.Context) {
	req := RejectGroupTransactionReq{}
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiError.ErrInvalidRequest(err))
		return
	}

	ctx := gctx.Request.Context()
	id := gctx.Param(GroupTransactionIdParamKey)

	gt, err := gta.GroupTxnRepo.Get(ctx, id, repository.WithReadPrimary)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gctx, ErrGroupTransactionNotFound())
		} else {
			apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		}
		return
	}

	err = gt.Reject(req.RequestedBy)
	if err != nil {
		apiutil.ErrBadRequest(gctx, ErrGroupTransactionInvalidTransitStatus(
			gt.Status(),
			model.GroupTransactionStatusRejected,
		))
		return
	}

	err = gta.GroupTxnRepo.Update(ctx, gt)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiError.ErrInternal(err))
		return
	}

	apiutil.NoContent(gctx)
}
