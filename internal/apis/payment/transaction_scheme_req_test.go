package payment

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func TestCreateTransactionSchemeReq(t *testing.T) {
	t.Run("should be valid", func(tt *testing.T) {
		testcases := []CreateTransactionSchemeReq{
			{
				Name:            "Test scheme",
				DisplayText:     "ทดสอบเติมเงิน",
				Category:        model.WalletTransactionCategory,
				Type:            model.CompensationTransactionType,
				SubType:         "TEST_TEST",
				Operator:        model.AdditionOperator,
				RequiredOrderID: false,
				RequiredTax:     true,
			},
			{
				Name:            "Test scheme",
				DisplayText:     "ทดสอบหักเงิน",
				Category:        model.CreditTransactionCategory,
				Type:            model.ChargeTransactionType,
				SubType:         "TEST_CHARGE",
				Operator:        model.SubtractionOperator,
				RequiredOrderID: false,
				RequiredTax:     false,
			},
		}

		for i, tc := range testcases {
			tt.Run(fmt.Sprintf("testcase #%v", i+1), func(ttt *testing.T) {
				err := tc.Validate()
				require.NoError(tt, err)
			})
		}
	})

	t.Run("should return err if invalid required tax", func(tt *testing.T) {
		txnSchemeReq := CreateTransactionSchemeReq{
			Name:            "Test scheme",
			DisplayText:     "ทดสอบหักเงิน",
			Category:        model.CreditTransactionCategory,
			Type:            model.ChargeTransactionType,
			SubType:         "TEST_CHARGE",
			Operator:        model.SubtractionOperator,
			RequiredOrderID: false,
			RequiredTax:     true,
		}
		err := txnSchemeReq.Validate()
		require.EqualError(tt, err, ErrInvalidRequiredTaxCondition.Error())
	})
}

func TestListTransactionSchemesReq_toQuery(t *testing.T) {
	t.Run("should add Query correctly ", func(tt *testing.T) {
		testcases := []ListTransactionSchemesReq{
			{
				Category: model.WalletTransactionCategory,
				Status:   model.ActiveTransactionScheme,
			},
			{
				Category: model.WalletTransactionCategory,
				Status:   model.ArchivedTransactionScheme,
			},
			{
				Category: model.CreditTransactionCategory,
				Status:   model.ActiveTransactionScheme,
			},
			{
				Category: model.CreditTransactionCategory,
				Status:   model.ArchivedTransactionScheme,
			},
		}

		for i, tc := range testcases {
			tt.Run(fmt.Sprintf("testcase #%v", i+1), func(ttt *testing.T) {
				query := tc.toQuery()
				q, err := query.Query()

				require.NoError(tt, err)
				require.Equal(tt, tc.Status, q["status"])
				require.Equal(tt, tc.Category, q["category"])
			})
		}
	})
}
