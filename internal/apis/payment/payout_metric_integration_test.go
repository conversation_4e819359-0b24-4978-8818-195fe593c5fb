//go:build integration_test
// +build integration_test

package payment_test

import (
	"context"
	"testing"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
)

func TestUOBPayoutMetricGetWithdrawalTransactionCount(t *testing.T) {
	ctn := ittest.NewContainer(t)
	ctx := context.Background()
	ds := ctn.TransactionDataStore
	_, err := ds.RemoveAll(ctx, bson.M{})
	require.NoError(t, err)
	now := timeutil.BangkokNow()
	twoDaysAgo := now.Add(-48 * time.Hour)
	err = ds.InsertMany(ctx, []interface{}{
		makeWithdrawalTransaction(model.PendingTransactionStatus, now),
		makeWithdrawalTransaction(model.PendingTransactionStatus, now),
		makeWithdrawalTransaction(model.ProcessingTransactionStatus, now),
		makeWithdrawalTransaction(model.ProcessingTransactionStatus, now),
		makeWithdrawalTransaction(model.SuccessTransactionStatus, now),
		makeWithdrawalTransaction(model.FailTransactionStatus, now),
		makeWithdrawalTransaction(model.SuccessTransactionStatus, twoDaysAgo),
	})
	require.NoError(t, err)

	payoutMetric := payment.ProvideUOBPayoutMetric(
		ds,
		config.ProvidePaymentConfig(),
		nil,
	)

	t.Run("should correctly return the number of withdrawal transactions", func(tt *testing.T) {
		expect := []payment.WithdrawalTransactionCount{
			{Status: model.PendingTransactionStatus, Count: 2},
			{Status: model.ProcessingTransactionStatus, Count: 2},
			{Status: model.SuccessTransactionStatus, Count: 1},
			{Status: model.FailTransactionStatus, Count: 1},
		}

		last24Hours := timeutil.BangkokNow().Add(-24 * time.Hour)
		result, err := payoutMetric.GetWithdrawalTransactionCount(ctx, last24Hours)
		require.NoError(tt, err)

		require.ElementsMatch(tt, expect, result)
	})
}

func makeWithdrawalTransaction(status model.TransactionStatus, createAt time.Time) model.Transaction {
	return model.Transaction{
		Status: status,
		Action: model.WithdrawTransactionAction,
		Info: model.TransactionInfo{
			Category: model.WalletTransactionCategory,
			Type:     model.WithdrawTransactionType,
		},
		CreatedAt: createAt,
	}
}
