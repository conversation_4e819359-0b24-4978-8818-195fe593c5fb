package payment

//go:generate mockgen -source=./approval_service.go -destination=./mock_approval_service.go -package=payment

import (
	"context"
	"sync"

	"github.com/pkg/errors"

	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var (
	ErrApprovalTransactionNotExists = errors.New("transaction is not exists")
	ErrApprovalDriverNotExists      = errors.New("driver is not exists")
)

type ApprovalService interface {
	CreateApprovable(category model.ApprovalCategory, action model.ApprovalAction) (Approvable, error)
	ApproveApproval(ctx context.Context, approval *model.Approval) (txnIDs []string, err error)
	RejectApproval(ctx context.Context, approval *model.Approval) (txnIDs []string, err error)
}

type ApprovalServiceImpl struct {
	approvalRepo             repository.ApprovalRepository
	drivTransRepo            repository.DriverTransactionRepository
	transactionRepo          repository.TransactionRepository
	driverRepository         repository.DriverRepository
	driverTransactionService service.DriverTransactionServiceV2
	txnHelper                transaction.TxnHelper
	l                        sync.Mutex
}

func (svc *ApprovalServiceImpl) CreateApprovable(category model.ApprovalCategory, action model.ApprovalAction) (Approvable, error) {
	var approver Approvable
	switch {
	case model.CreditCategory == category && model.PurchaseAction == action:
		approver = NewCreditPurchaseApprover(svc.drivTransRepo, svc.transactionRepo, svc.driverTransactionService)
	case model.CreditCategory == category && model.VoidPurchaseAction == action:
		approver = NewCreditVoidPurchaseApprover(svc.drivTransRepo, svc.transactionRepo, svc.driverTransactionService, svc.txnHelper)
	case model.CreditCategory == category && model.VoidAction == action:
		approver = NewCreditVoidApprover(svc.drivTransRepo, svc.transactionRepo)
	case model.CreditCategory == category && model.VoidChargeAction == action:
		approver = NewCreditVoidChargeApprover(svc.drivTransRepo, svc.transactionRepo)
	case model.CreditCategory == category && model.ChargeAction == action:
		approver = NewCreditChargeApprover(svc.drivTransRepo, svc.transactionRepo)
	case model.WalletCategory == category && model.VoidAction == action:
		approver = NewWalletVoidApprover(svc.drivTransRepo, svc.transactionRepo)
	case model.WalletCategory == category && model.VoidAddAction == action:
		approver = NewWalletVoidAddApprover(svc.drivTransRepo, svc.transactionRepo)
	default:
		return nil, errors.New("unknown approval type")
	}
	return approver, nil
}

func (svc *ApprovalServiceImpl) ApproveApproval(ctx context.Context, approval *model.Approval) ([]string, error) {
	info := approval.Info
	approver, err := svc.CreateApprovable(info.Category(), info.Action())
	if err != nil {
		return nil, err
	}

	err = approval.Approve()
	if err != nil {
		return nil, NewErrInvalidTransitStatus(string(approval.Status), string(model.ApprovedApproval))
	}

	txns, err := approver.Approve(ctx, *approval)
	if err != nil {
		return nil, err
	}

	err = svc.approvalRepo.Update(ctx, approval)
	if err != nil {
		return nil, err
	}

	transactionIds := make([]string, len(txns))
	for i, t := range txns {
		transactionIds[i] = t.TransactionID
	}

	return transactionIds, nil
}

func (svc *ApprovalServiceImpl) RejectApproval(ctx context.Context, approval *model.Approval) ([]string, error) {
	info := approval.Info
	approver, err := svc.CreateApprovable(info.Category(), info.Action())
	if err != nil {
		return nil, err
	}

	txns, err := approver.Reject(ctx, *approval)
	if err != nil {
		return nil, err
	}

	err = approval.Reject()
	if err != nil {
		return nil, NewErrInvalidTransitStatus(string(approval.Status), string(model.RejectedApproval))
	}

	err = svc.approvalRepo.Update(ctx, approval)
	if err != nil {
		return nil, err
	}

	transactionIds := make([]string, len(txns))
	for i, t := range txns {
		transactionIds[i] = t.TransactionID
	}

	return transactionIds, nil
}

type Approvable interface {
	Approve(ctx context.Context, approval model.Approval) ([]model.Transaction, error)
	Reject(ctx context.Context, approval model.Approval) ([]model.Transaction, error)
}

type CreditPurchaseApprover struct {
	driverTxnRepo            repository.DriverTransactionRepository
	txnRepo                  repository.TransactionRepository
	driverTransactionService service.DriverTransactionServiceV2
}

func NewCreditPurchaseApprover(
	driverTxnRepo repository.DriverTransactionRepository,
	txnRepo repository.TransactionRepository,
	driverTxnService service.DriverTransactionServiceV2,
) *CreditPurchaseApprover {
	return &CreditPurchaseApprover{
		driverTxnRepo:            driverTxnRepo,
		txnRepo:                  txnRepo,
		driverTransactionService: driverTxnService,
	}
}

func (cpa *CreditPurchaseApprover) Approve(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditPurchaseInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	var txnInfosBuilder service.TransactionInfosBuilder = func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
		if info.IsFree() {
			if !info.ExpirationDate().IsZero() && info.ExpirationDate().Before(timeutil.BangkokNow()) {
				return nil, errors.New("free credit expiration date has passed")
			}
			return []model.TransactionInfo{*dt.NewFreeCreditTransaction(info.TxnRefID(), info.Amount(), model.WithExpirationDate(info.ExpirationDate()))}, nil
		} else if info.IsPositiveCredit() {
			txnInfo := *model.NewPositiveCreditTransactionInfo(info.DriverID(), info.Amount())
			txnInfo.TransRefID = info.TxnRefID()
			return []model.TransactionInfo{
				txnInfo,
			}, nil
		}
		return []model.TransactionInfo{*dt.NewCreditTransaction(info.TxnRefID(), info.Amount())}, nil
	}

	addRemarkTxnOpts := func(t *model.Transaction) {
		for _, tr := range approval.Remarks {
			t.AddRemark(tr.Message, tr.CreatedBy)
		}
		t.Info.RequestedBy = approval.RequestBy
		t.Info.RequestedTime = approval.CreatedAt
	}

	_, txns, err := cpa.driverTransactionService.ProcessDriverTransaction(
		ctx,
		info.DriverID(),
		model.AdminTransactionChannel,
		action,
		model.SuccessTransactionStatus,
		txnInfosBuilder,
		service.WithTransactionOptions(addRemarkTxnOpts),
	)
	if err != nil {
		return nil, errors.WithMessage(err, "fail to add void txn to driver")
	}

	return txns, nil
}

func (cpa *CreditPurchaseApprover) Reject(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditPurchaseInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	rejectInfo := model.NewFreeCreditTransactionInfo(info.DriverID(), info.Amount())
	rejectInfo.TransRefID = info.TxnRefID()
	rejectInfo.RequestedBy = approval.RequestBy
	rejectInfo.RequestedTime = approval.CreatedAt

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	rejectTxn := model.NewTransaction(
		utils.GenerateUUID(),
		model.AdminTransactionChannel,
		action,
		model.RejectedTransactionStatus,
		*rejectInfo,
	)

	for _, tr := range rejectTxn.Remarks {
		rejectTxn.AddRemark(tr.Message, tr.CreatedBy)
	}
	rejectTxn.Info.RequestedBy = approval.RequestBy
	rejectTxn.Info.RequestedTime = approval.CreatedAt

	if err := cpa.txnRepo.Create(ctx, rejectTxn); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return []model.Transaction{*rejectTxn}, nil
}

type CreditVoidApprover struct {
	driverTxnRepo repository.DriverTransactionRepository
	txnRepo       repository.TransactionRepository
}

func NewCreditVoidApprover(driverTxnRepo repository.DriverTransactionRepository, txnRepo repository.TransactionRepository) *CreditVoidApprover {
	return &CreditVoidApprover{driverTxnRepo: driverTxnRepo, txnRepo: txnRepo}
}

func (cva *CreditVoidApprover) Approve(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditVoidInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := cva.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	voidTxn, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}

	driverTxn, err := cva.driverTxnRepo.FindByID(ctx, txn.Info.DriverID)
	if err != nil {
		return nil, ErrApprovalDriverNotExists
	}

	infos, err := driverTxn.AddTransaction([]model.TransactionInfo{*voidTxn})
	if err != nil {
		return nil, errors.WithMessage(err, "fail to add txn to driver")
	}

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	txns := make([]model.Transaction, len(infos))
	for i, info := range infos {
		uuid := utils.GenerateUUID()
		newTxn := model.NewTransaction(
			uuid,
			model.AdminTransactionChannel,
			action,
			model.SuccessTransactionStatus,
			info,
		)

		for _, tr := range approval.Remarks {
			newTxn.AddRemark(tr.Message, tr.CreatedBy)
		}
		newTxn.Info.RequestedBy = approval.RequestBy
		newTxn.Info.RequestedTime = approval.CreatedAt

		txns[i] = *newTxn
	}

	if err := cva.driverTxnRepo.Update(ctx, driverTxn); err != nil {
		return nil, errors.WithMessage(err, "fail to update driver transaction")
	}

	if err := cva.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return txns, nil
}

func (cva *CreditVoidApprover) Reject(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditVoidInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := cva.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	rejectInfo, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}
	rejectInfo.RequestedBy = approval.RequestBy
	rejectInfo.RequestedTime = approval.CreatedAt

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	rejectTxn := model.NewTransaction(
		utils.GenerateUUID(),
		model.AdminTransactionChannel,
		action,
		model.RejectedTransactionStatus,
		*rejectInfo,
	)

	for _, tr := range rejectTxn.Remarks {
		rejectTxn.AddRemark(tr.Message, tr.CreatedBy)
	}
	rejectTxn.Info.RequestedBy = approval.RequestBy
	rejectTxn.Info.RequestedTime = approval.CreatedAt

	if err := cva.txnRepo.Create(ctx, rejectTxn); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return []model.Transaction{*rejectTxn}, nil
}

type CreditVoidPurchaseApprover struct {
	driverTxnRepo            repository.DriverTransactionRepository
	txnRepo                  repository.TransactionRepository
	driverTransactionService service.DriverTransactionServiceV2
	txnHelper                transaction.TxnHelper
}

func NewCreditVoidPurchaseApprover(
	driverTxnRepo repository.DriverTransactionRepository,
	txnRepo repository.TransactionRepository,
	driverTransactionService service.DriverTransactionServiceV2,
	txnHelper transaction.TxnHelper,
) *CreditVoidPurchaseApprover {
	return &CreditVoidPurchaseApprover{
		driverTxnRepo:            driverTxnRepo,
		txnRepo:                  txnRepo,
		driverTransactionService: driverTransactionService,
		txnHelper:                txnHelper,
	}
}

func (cvpa *CreditVoidPurchaseApprover) Approve(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditVoidPurchaseInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := cvpa.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	voidTxn, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "fail to get action from approval")
	}

	if info.PurchaseDriverID() != txn.Info.DriverID {
		if _, err := cvpa.driverTxnRepo.FindByID(ctx, info.PurchaseDriverID()); err != nil {
			return nil, ErrApprovalDriverNotExists
		}
	}

	txns := make([]model.Transaction, 0)
	if _, err := cvpa.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		addRemarkTxnOpts := func(t *model.Transaction) {
			for _, tr := range approval.Remarks {
				t.AddRemark(tr.Message, tr.CreatedBy)
			}
			t.Info.RequestedBy = approval.RequestBy
			t.Info.RequestedTime = approval.CreatedAt
		}

		_, infos, err := cvpa.driverTransactionService.ProcessDriverTransaction(
			sessCtx,
			txn.Info.DriverID,
			model.AdminTransactionChannel,
			action,
			model.SuccessTransactionStatus,
			service.TransactionInfos(*voidTxn),
			service.WithTransactionOptions(addRemarkTxnOpts),
		)
		if err != nil {
			return nil, errors.WithMessage(err, "fail to add void txn to driver")
		}

		purchaseTxnsBuilder := func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
			purchaseTxns := dt.NewCreditTransaction(txn.Info.TransRefID, info.Amount())
			return []model.TransactionInfo{*purchaseTxns}, nil
		}
		_, purchaseDriverTxnInfos, err := cvpa.driverTransactionService.ProcessDriverTransaction(
			sessCtx,
			info.PurchaseDriverID(),
			model.AdminTransactionChannel,
			action,
			model.SuccessTransactionStatus,
			purchaseTxnsBuilder,
			service.WithTransactionOptions(addRemarkTxnOpts),
		)
		if err != nil {
			return nil, errors.WithMessage(err, "fail to add txn to driver")
		}

		txns = append(txns, infos...)
		txns = append(txns, purchaseDriverTxnInfos...)
		return nil, nil
	}, transaction.WithLabel("CreditVoidPurchaseApprover.Approve")); err != nil {
		return nil, err
	}

	return txns, nil
}

func (cvpa *CreditVoidPurchaseApprover) Reject(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditVoidPurchaseInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := cvpa.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	vInfo, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}
	vInfo.RequestedBy = approval.RequestBy
	vInfo.RequestedTime = approval.CreatedAt

	pInfo := model.NewPurchaseCreditTransactionInfo(info.PurchaseDriverID(), info.Amount())
	pInfo.TransRefID = vInfo.TransRefID
	pInfo.RequestedBy = approval.RequestBy
	pInfo.RequestedTime = approval.CreatedAt

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	txns := make([]model.Transaction, 2)
	for i, t := range []model.TransactionInfo{*vInfo, *pInfo} {
		txns[i] = *model.NewTransaction(
			utils.GenerateUUID(),
			model.AdminTransactionChannel,
			action,
			model.RejectedTransactionStatus,
			t,
		)

		for _, tr := range txns[i].Remarks {
			txns[i].AddRemark(tr.Message, tr.CreatedBy)
		}
		txns[i].Info.RequestedBy = approval.RequestBy
		txns[i].Info.RequestedTime = approval.CreatedAt
	}

	if err := cvpa.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return txns, nil
}

type CreditVoidChargeApprover struct {
	driverTxnRepo repository.DriverTransactionRepository
	txnRepo       repository.TransactionRepository
}

func NewCreditVoidChargeApprover(driverTxnRepo repository.DriverTransactionRepository, txnRepo repository.TransactionRepository) *CreditVoidChargeApprover {
	return &CreditVoidChargeApprover{driverTxnRepo: driverTxnRepo, txnRepo: txnRepo}
}

func (cva *CreditVoidChargeApprover) Approve(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditVoidChargeInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := cva.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	if txn.Info.Category == model.CreditTransactionCategory && txn.Info.Type != model.ItemFeeTransactionType && txn.Info.Type != model.UserDeliveryFeeType {
		return nil, ErrTransactionTypeNotAllowVoid()
	}

	voidTxn, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}

	voidChargeDriver, err := cva.driverTxnRepo.FindByID(ctx, txn.Info.DriverID)
	if err != nil {
		return nil, ErrApprovalDriverNotExists
	}

	chargeTxns := voidChargeDriver.NewCreditChargeTransaction(txn.Info.TransRefID, info.Amount(), model.TransactionSubType(txn.Info.Type), info.OrderID())
	voidChargeInfos, err := voidChargeDriver.AddTransaction([]model.TransactionInfo{*chargeTxns, *voidTxn})
	if err != nil {
		return nil, errors.WithMessage(err, "fail to add txn to driver")
	}

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "fail to get action from approval")
	}

	txns := make([]model.Transaction, len(voidChargeInfos))
	for i, tInfo := range voidChargeInfos {
		uuid := utils.GenerateUUID()
		newTxn := model.NewTransaction(
			uuid,
			model.AdminTransactionChannel,
			action,
			model.SuccessTransactionStatus,
			tInfo,
		)

		for _, tr := range approval.Remarks {
			newTxn.AddRemark(tr.Message, tr.CreatedBy)
		}
		newTxn.Info.RequestedBy = approval.RequestBy
		newTxn.Info.RequestedTime = approval.CreatedAt

		txns[i] = *newTxn
	}

	if err := cva.driverTxnRepo.Update(ctx, voidChargeDriver); err != nil {
		return nil, errors.WithMessage(err, "fail to update void-charge driver transaction")
	}

	if err := cva.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return txns, nil
}

func (cva *CreditVoidChargeApprover) Reject(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditVoidPurchaseInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := cva.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	vInfo, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}
	vInfo.RequestedBy = approval.RequestBy
	vInfo.RequestedTime = approval.CreatedAt

	pInfo := model.NewPurchaseCreditTransactionInfo(info.PurchaseDriverID(), info.Amount())
	pInfo.TransRefID = vInfo.TransRefID
	pInfo.RequestedBy = approval.RequestBy
	pInfo.RequestedTime = approval.CreatedAt

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	txns := make([]model.Transaction, 2)
	for i, t := range []model.TransactionInfo{*vInfo, *pInfo} {
		txns[i] = *model.NewTransaction(
			utils.GenerateUUID(),
			model.AdminTransactionChannel,
			action,
			model.RejectedTransactionStatus,
			t,
		)

		for _, tr := range txns[i].Remarks {
			txns[i].AddRemark(tr.Message, tr.CreatedBy)
		}
		txns[i].Info.RequestedBy = approval.RequestBy
		txns[i].Info.RequestedTime = approval.CreatedAt
	}

	if err := cva.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return []model.Transaction{}, nil
}

type CreditChargeApprover struct {
	driverTxnRepo repository.DriverTransactionRepository
	txnRepo       repository.TransactionRepository
}

func NewCreditChargeApprover(driverTxnRepo repository.DriverTransactionRepository, txnRepo repository.TransactionRepository) *CreditChargeApprover {
	return &CreditChargeApprover{driverTxnRepo: driverTxnRepo, txnRepo: txnRepo}
}

func (cca *CreditChargeApprover) Approve(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditChargeInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	chargeDriver, err := cca.driverTxnRepo.FindByID(ctx, approval.Info.DriverID())
	if err != nil {
		return nil, ErrApprovalDriverNotExists
	}
	approval.Info.Action()

	chargeTxns := chargeDriver.NewCreditChargeTransaction(info.TransRefID(), info.Amount(), model.TransactionSubType(info.SubType()), info.OrderID())
	chargeInfos, err := chargeDriver.AddTransaction([]model.TransactionInfo{*chargeTxns})
	if err != nil {
		return nil, errors.WithMessage(err, "fail to add txn to driver")
	}

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	txns := make([]model.Transaction, len(chargeInfos))
	for i, tInfo := range chargeInfos {
		uuid := utils.GenerateUUID()
		newTxn := model.NewTransaction(
			uuid,
			model.AdminTransactionChannel,
			action,
			model.SuccessTransactionStatus,
			tInfo,
		)

		for _, tr := range approval.Remarks {
			newTxn.AddRemark(tr.Message, tr.CreatedBy)
		}
		newTxn.Info.RequestedBy = approval.RequestBy
		newTxn.Info.RequestedTime = approval.CreatedAt

		txns[i] = *newTxn
	}

	if err := cca.driverTxnRepo.Update(ctx, chargeDriver); err != nil {
		return nil, errors.WithMessage(err, "fail to update purchase driver transaction")
	}

	if err := cca.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return txns, nil
}

func (cca *CreditChargeApprover) Reject(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.CreditVoidPurchaseInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := cca.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	vInfo, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}
	vInfo.RequestedBy = approval.RequestBy
	vInfo.RequestedTime = approval.CreatedAt

	pInfo := model.NewPurchaseCreditTransactionInfo(info.PurchaseDriverID(), info.Amount())
	pInfo.TransRefID = vInfo.TransRefID
	pInfo.RequestedBy = approval.RequestBy
	pInfo.RequestedTime = approval.CreatedAt

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	txns := make([]model.Transaction, 2)
	for i, t := range []model.TransactionInfo{*vInfo, *pInfo} {
		txns[i] = *model.NewTransaction(
			utils.GenerateUUID(),
			model.AdminTransactionChannel,
			action,
			model.RejectedTransactionStatus,
			t,
		)
		for _, tr := range txns[i].Remarks {
			txns[i].AddRemark(tr.Message, tr.CreatedBy)
		}
		txns[i].Info.RequestedBy = approval.RequestBy
		txns[i].Info.RequestedTime = approval.CreatedAt
	}

	if err := cca.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return []model.Transaction{}, nil
}

type WalletVoidApprover struct {
	driverTxnRepo repository.DriverTransactionRepository
	txnRepo       repository.TransactionRepository
}

func NewWalletVoidApprover(driverTxnRepo repository.DriverTransactionRepository, txnRepo repository.TransactionRepository) *WalletVoidApprover {
	return &WalletVoidApprover{driverTxnRepo: driverTxnRepo, txnRepo: txnRepo}
}

func (wva *WalletVoidApprover) Approve(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.WalletVoidInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := wva.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	voidTxn, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}

	driverTxn, err := wva.driverTxnRepo.FindByID(ctx, txn.Info.DriverID)
	if err != nil {
		return nil, ErrApprovalDriverNotExists
	}

	infos, err := driverTxn.AddTransaction([]model.TransactionInfo{*voidTxn})
	if err != nil {
		return nil, errors.WithMessage(err, "fail to add txn to driver")
	}

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	txns := make([]model.Transaction, len(infos))
	for i, info := range infos {
		uuid := utils.GenerateUUID()
		newTxn := model.NewTransaction(
			uuid,
			model.AdminTransactionChannel,
			action,
			model.SuccessTransactionStatus,
			info,
		)
		for _, tr := range approval.Remarks {
			newTxn.AddRemark(tr.Message, tr.CreatedBy)
		}
		newTxn.Info.RequestedBy = approval.RequestBy
		newTxn.Info.RequestedTime = approval.CreatedAt

		txns[i] = *newTxn
	}

	if err := wva.driverTxnRepo.Update(ctx, driverTxn); err != nil {
		return nil, errors.WithMessage(err, "fail to update driver transaction")
	}

	if err := wva.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return txns, nil
}

func (wva *WalletVoidApprover) Reject(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.WalletVoidInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := wva.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	rejectInfo, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}
	rejectInfo.RequestedBy = approval.RequestBy
	rejectInfo.RequestedTime = approval.CreatedAt

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	rejectTxn := model.NewTransaction(
		utils.GenerateUUID(),
		model.AdminTransactionChannel,
		action,
		model.RejectedTransactionStatus,
		*rejectInfo,
	)
	for _, tr := range rejectTxn.Remarks {
		rejectTxn.AddRemark(tr.Message, tr.CreatedBy)
	}
	rejectTxn.Info.RequestedBy = approval.RequestBy
	rejectTxn.Info.RequestedTime = approval.CreatedAt

	if err := wva.txnRepo.Create(ctx, rejectTxn); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return []model.Transaction{*rejectTxn}, nil
}

type WalletVoidAddApprover struct {
	driverTxnRepo repository.DriverTransactionRepository
	txnRepo       repository.TransactionRepository
}

func NewWalletVoidAddApprover(driverTxnRepo repository.DriverTransactionRepository, txnRepo repository.TransactionRepository) *WalletVoidAddApprover {
	return &WalletVoidAddApprover{driverTxnRepo: driverTxnRepo, txnRepo: txnRepo}
}

func (wva *WalletVoidAddApprover) Approve(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.WalletVoidAddInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := wva.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	voidTxn, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}

	driverTxn, err := wva.driverTxnRepo.FindByID(ctx, txn.Info.DriverID)
	if err != nil {
		return nil, ErrApprovalDriverNotExists
	}

	addedTxn := txn.Info
	addedTxn.Amount = info.Amount()

	infos, err := driverTxn.AddTransaction([]model.TransactionInfo{*voidTxn, addedTxn})
	if err != nil {
		return nil, errors.WithMessage(err, "fail to add txn to driver")
	}

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	txns := make([]model.Transaction, len(infos))
	for i, info := range infos {
		uuid := utils.GenerateUUID()
		newTxn := model.NewTransaction(
			uuid,
			model.AdminTransactionChannel,
			action,
			model.SuccessTransactionStatus,
			info,
		)
		for _, tr := range approval.Remarks {
			newTxn.AddRemark(tr.Message, tr.CreatedBy)
		}
		newTxn.Info.RequestedBy = approval.RequestBy
		newTxn.Info.RequestedTime = approval.CreatedAt

		txns[i] = *newTxn
	}

	if err := wva.driverTxnRepo.Update(ctx, driverTxn); err != nil {
		return nil, errors.WithMessage(err, "fail to update driver transaction")
	}

	if err := wva.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return txns, nil
}

func (wva *WalletVoidAddApprover) Reject(ctx context.Context, approval model.Approval) ([]model.Transaction, error) {
	info, ok := approval.Info.(*model.WalletVoidAddInfo)
	if !ok {
		return nil, errors.New("invalid info type")
	}

	txn, err := wva.txnRepo.FindByID(ctx, info.TxnID())
	if err != nil {
		return nil, ErrApprovalTransactionNotExists
	}

	vInfo, err := txn.Void()
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to void transaction %s", info.TxnID())
	}
	vInfo.RequestedBy = approval.RequestBy
	vInfo.RequestedTime = approval.CreatedAt

	pInfo := txn.Info
	pInfo.TransRefID = vInfo.TransRefID
	pInfo.RequestedBy = approval.RequestBy
	pInfo.RequestedTime = approval.CreatedAt

	action, err := approval.TransactionAction()
	if err != nil {
		return nil, errors.WithMessage(err, "failt to get action from approval")
	}

	txns := make([]model.Transaction, 2)
	for i, t := range []model.TransactionInfo{*vInfo, pInfo} {
		txns[i] = *model.NewTransaction(
			utils.GenerateUUID(),
			model.AdminTransactionChannel,
			action,
			model.RejectedTransactionStatus,
			t,
		)
		for _, tr := range txns[i].Remarks {
			txns[i].AddRemark(tr.Message, tr.CreatedBy)
		}
		txns[i].Info.RequestedBy = approval.RequestBy
		txns[i].Info.RequestedTime = approval.CreatedAt
	}

	if err := wva.txnRepo.CreateAll(ctx, txns); err != nil {
		return nil, errors.WithMessage(err, "fail to create transactions")
	}

	return txns, nil
}

func ProvideApprovalService(
	appRepo repository.ApprovalRepository,
	drivTransRepo repository.DriverTransactionRepository,
	tranRepo repository.TransactionRepository,
	driverRepository repository.DriverRepository,
	driverTransactionService service.DriverTransactionServiceV2,
	txnHelper transaction.TxnHelper,
) ApprovalService {
	return &ApprovalServiceImpl{
		approvalRepo:             appRepo,
		drivTransRepo:            drivTransRepo,
		transactionRepo:          tranRepo,
		driverRepository:         driverRepository,
		driverTransactionService: driverTransactionService,
		txnHelper:                txnHelper,
	}
}
