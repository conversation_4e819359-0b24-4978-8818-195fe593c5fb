package payment

import (
	"sync"
	"time"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uobclient"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type ExportTransaction struct {
	DriverID                   string    `json:"driverId"`
	RefID                      string    `json:"refId"`
	FirstName                  string    `json:"firstName"`
	LastName                   string    `json:"lastName"`
	AccountNumber              string    `json:"accountNumber"`
	AccountName                string    `json:"accountName"`
	BankName                   string    `json:"bankName"`
	BankCode                   string    `json:"bankCode"`
	BranchCode                 string    `json:"branchCode"`
	RequestedAmount            string    `json:"requestedAmount"`
	ReceivedAmount             string    `json:"receivedAmount"`
	TransactionFee             string    `json:"transactionFee"`
	CountRecords               string    `json:"countRecords"`
	TransactionID              string    `json:"-"`
	UOBWithdrawalProcessedAt   time.Time `json:"-"`
	UOBWithdrawalMakePaymentAt time.Time `json:"-"`
}

type UpdateWithdrawResultRes struct {
	Successes []string `json:"successes"`
	Fails     []string `json:"fails"`
}

type UploadedFilesRes struct {
	FileLocations []string `json:"files"`
}

type TransactionResponse struct {
	TransactionID string `json:"transactionId"`

	Channel model.TransactionChannel `json:"channel"`
	Status  model.TransactionStatus  `json:"status"`
	Action  model.TransactionAction  `json:"action"`

	Info    TransactionInfo `json:"info"`
	Remarks []Remark        `json:"remarks"`

	Audits []Audit `json:"audits"`

	IsBannedWithDraw  bool `json:"isBannedWithdraw"`
	IsBannedTakeOrder bool `json:"isBannedTakeOrder"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type TransactionInfo struct {
	Category model.TransactionCategory `json:"category"`
	Type     model.TransactionType     `json:"type"`
	SubType  model.TransactionSubType  `json:"subType"`

	DriverID      string                      `json:"driverId,omitempty"`
	DriverRefID   string                      `json:"driverRefId,omitempty"`
	TransRefID    crypt.EncryptedString       `json:"transRefId,omitempty"`
	WithdrawRefID crypt.EncryptedString       `json:"withdrawRefId,omitempty"`
	OrderID       string                      `json:"orderId,omitempty"`
	Orders        []model.TransactionOrders   `json:"orders,omitempty"`
	TripID        string                      `json:"tripId,omitempty"`
	CreditType    model.TransactionCreditType `json:"creditType,omitempty"`
	WalletType    model.TransactionWalletType `json:"walletType,omitempty"`
	ExpiredDate   time.Time                   `json:"expiredDate,omitempty"`
	RefIDs        []string                    `json:"refIds,omitempty"`
	RefID         string                      `json:"refId,omitempty"`
	Amount        types.Money                 `json:"amount"`

	RequestedBy   string               `json:"requestedBy,omitempty"`
	RequestedTime time.Time            `json:"requestedTime,omitempty"`
	WithdrawInfo  WithdrawInfoResponse `json:"withdrawInfo,omitempty"`
	DriverRegion  model.RegionCode     `json:"driverRegion,omitempty"`
	WalletAfter   types.Money          `json:"walletAfter"`
	CreditAfter   types.Money          `json:"creditAfter"`
}

type WithdrawInfoResponse struct {
	Account       crypt.EncryptedString `json:"account,omitempty"`
	BankName      crypt.EncryptedString `json:"bankName,omitempty"`
	AccountHolder crypt.EncryptedString `json:"accountHolder,omitempty"`
}

type Remark struct {
	Message   string    `json:"message"`
	CreatedBy string    `json:"createdBy"`
	CreatedAt time.Time `json:"createdAt"`
}

type Audit struct {
	Action model.AdminAction `json:"action,omitempty"`
	Time   time.Time         `json:"time,omitempty"`
	By     string            `json:"by,omitempty"`
}

type MultipleApproveResponse struct {
	SuccessCount        int      `json:"successCount"`
	FailCount           int      `json:"totalFailCount"`
	SuccessTransactions []string `json:"successTransaction"` // for backward compatibility
	InvalidTransactions []string `json:"failedTransaction"`  // for backward compatibility
}

func NewMultipleApproveResponse() MultipleApproveResponse {
	return MultipleApproveResponse{
		SuccessTransactions: []string{},
		InvalidTransactions: []string{},
	}
}

type ListTransactionIDsResponse struct {
	TransIDs    []string `json:"transactionIds"`
	TotalAmount float64  `json:"totalAmount"`
}

func NewListTransactionIDsResponse() ListTransactionIDsResponse {
	return ListTransactionIDsResponse{
		TransIDs: []string{},
	}
}

func ParseTransactionResponse(tran *model.Transaction, isBannedWithdraw, isBannedTakeOrder bool) TransactionResponse {
	transRes := NewTransactionResponse(tran)
	transRes.IsBannedWithDraw = isBannedWithdraw
	transRes.IsBannedTakeOrder = isBannedTakeOrder
	return transRes
}

func NewTransactionResponse(tran *model.Transaction) TransactionResponse {
	transRes := TransactionResponse{
		TransactionID: tran.TransactionID,
		Channel:       tran.Channel,
		Status:        tran.Status,
		Action:        tran.Action,
		CreatedAt:     tran.CreatedAt,
		UpdatedAt:     tran.UpdatedAt,
	}

	wInfo := tran.Info.WithdrawInfo

	transRes.Info = TransactionInfo{
		Category:      tran.Info.Category,
		Type:          tran.Info.Type,
		SubType:       tran.Info.SubType,
		DriverID:      tran.Info.DriverID,
		DriverRefID:   tran.Info.DriverRefID,
		TransRefID:    tran.Info.TransRefID,
		WithdrawRefID: tran.Info.WithdrawRefID,
		OrderID:       tran.Info.OrderID,
		Orders:        tran.Info.Orders,
		TripID:        tran.Info.TripID,
		CreditType:    tran.Info.CreditType,
		WalletType:    tran.Info.WalletType,
		ExpiredDate:   tran.Info.ExpiredDate,
		RefIDs:        tran.Info.RefIDs,
		RefID:         tran.Info.RefID,
		Amount:        tran.Info.Amount,
		WithdrawInfo: WithdrawInfoResponse{
			Account:       wInfo.Account,
			BankName:      wInfo.BankName,
			AccountHolder: wInfo.AccountHolder,
		},
		RequestedBy:   tran.Info.RequestedBy,
		RequestedTime: tran.Info.RequestedTime,
		DriverRegion:  tran.Info.DriverRegion,
		CreditAfter:   tran.Info.CreditAfter,
		WalletAfter:   tran.Info.WalletAfter,
	}

	audits := make([]Audit, len(tran.Audits))
	for i, t := range tran.Audits {
		audits[i] = Audit{
			Action: t.Action,
			Time:   t.Time,
			By:     t.By,
		}
	}
	transRes.Audits = audits

	remarks := make([]Remark, len(tran.Remarks))
	for i, rm := range tran.Remarks {
		remarks[i] = Remark{
			Message:   rm.Message,
			CreatedAt: rm.CreatedAt,
			CreatedBy: rm.CreatedBy,
		}
	}
	transRes.Remarks = remarks

	return transRes
}

type UobBulkWithdrawResultRes struct {
	l          sync.Mutex
	Successes  []UobBulkWithdrawResult `json:"successes"`
	Failures   []UobBulkWithdrawResult `json:"failures"`
	Processing []UobProcessingResult   `json:"processing"`
}

func NewUobBulkWithdrawResultRes() *UobBulkWithdrawResultRes {
	return &UobBulkWithdrawResultRes{
		Successes: []UobBulkWithdrawResult{},
		Failures:  []UobBulkWithdrawResult{},
	}
}

func (u *UobBulkWithdrawResultRes) AddSuccess(r UobBulkWithdrawResult) {
	u.Successes = append(u.Successes, r)
}

func (u *UobBulkWithdrawResultRes) AddFails(r UobBulkWithdrawResult) {
	u.Failures = append(u.Failures, r)
}

func (u *UobBulkWithdrawResultRes) Size() int {
	return len(u.Successes) + len(u.Failures) + len(u.Processing)
}

type UobBulkWithdrawResult struct {
	WithdrawRefID                            string               `json:"withdrawRefID"`
	BankReferenceNumber                      string               `json:"bankReferenceNumber"`
	IsUobMakePaymentSuccess                  bool                 `json:"isUobMakePaymentSuccess"`
	IsUpdateWithdrawTransactionStatusSuccess bool                 `json:"isUpdateWithdrawTransactionStatusSuccess"`
	UobMakePaymentStatusCode                 string               `json:"uobMakePaymentStatusCode"`
	UobMakePaymentStatusDescription          string               `json:"uobMakePaymentStatusDescription"`
	UobMakePaymentHttpStatusCode             int                  `json:"uobMakePaymentHttpStatusCode"`
	ExportData                               uobclient.ExportData `json:"-"`
}

type UobProcessingResult struct {
	WithdrawRefID                   string               `json:"withdrawRefID"`
	BankReferenceNumber             string               `json:"bankReferenceNumber"`
	IsUobMakePaymentSuccess         bool                 `json:"isUobMakePaymentSuccess"`
	UobMakePaymentStatusCode        string               `json:"uobMakePaymentStatusCode"`
	UobMakePaymentStatusDescription string               `json:"uobMakePaymentStatusDescription"`
	UobMakePaymentHttpStatusCode    int                  `json:"uobMakePaymentHttpStatusCode"`
	UobMakeEnquireStatusCode        string               `json:"uobEnquireStatusCode"`
	UobMakeEnquireStatusDescription string               `json:"uobEnquireStatusDescription"`
	UobMakeEnquireHttpStatusCode    int                  `json:"uobMakeEnquireHttpStatusCode"`
	ExportData                      uobclient.ExportData `json:"-"`
}
