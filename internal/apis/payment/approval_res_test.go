package payment

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestNewListApprovalRes(t *testing.T) {

	t.Run("should create approvalRes correct", func(tt *testing.T) {
		actual := NewApprovalRes(*model.NewApproval("approval-1", "admin", model.NewCreditPurchaseInfo(
			"driver-1",
			"txn-ref-1",
			200.0,
		)))

		require.Equal(tt, "approval-1", actual.ApprovalID)
		require.Equal(tt, "admin", actual.RequestBy)
		require.NotNil(tt, string(model.PendingApproval), actual.Status)
		require.NotNil(tt, "admin", actual.CreatedAt)
		require.NotNil(tt, "admin", actual.UpdatedAt)
		require.NotNil(tt, actual.Info)
	})

	t.Run("should success to new info credit purchase", func(tt *testing.T) {
		info := NewApprovalRes(*model.NewApproval("approval-1", "admin", model.NewCreditPurchaseInfo(
			"driver-1",
			"txn-ref-1",
			200.0,
		))).Info

		actual, ok := info.(*CreditPurchaseInfoRes)
		require.True(tt, ok)
		require.Equal(tt, string(model.CreditCategory), actual.Category)
		require.Equal(tt, string(model.PurchaseAction), actual.Action)
		require.Equal(tt, "driver-1", actual.DriverID)
		require.Equal(tt, crypt.EncryptedString("txn-ref-1"), actual.TransRefID)
	})

	t.Run("should success to new info credit void", func(tt *testing.T) {
		info := NewApprovalRes(*model.NewApproval("approval-1", "admin", model.NewCreditVoidInfo(
			"txn-1",
			"driver-1",
			200.0,
		))).Info

		actual, ok := info.(*CreditVoidInfoRes)
		require.True(tt, ok)
		require.Equal(tt, string(model.CreditCategory), actual.Category)
		require.Equal(tt, string(model.VoidAction), actual.Action)
		require.Equal(tt, "txn-1", actual.VoidTxnID)
	})

	t.Run("should success to new approval credit void purchase", func(tt *testing.T) {
		info := NewApprovalRes(*model.NewApproval("approval-1", "admin", model.NewCreditVoidPurchaseInfo(
			"txn-1",
			"driver-1",
			"driver-1",
			200.0,
		))).Info

		actual, ok := info.(*CreditVoidPurchaseInfoRes)
		require.True(tt, ok)
		require.Equal(tt, string(model.CreditCategory), actual.Category)
		require.Equal(tt, string(model.VoidPurchaseAction), actual.Action)
		require.Equal(tt, "txn-1", actual.VoidTxnID)
		require.Equal(tt, "driver-1", actual.PurchaseDriverID)
		require.Equal(tt, types.NewMoney(200.0), actual.Amount)
	})

	t.Run("should success to new approval wallet void", func(tt *testing.T) {
		info := NewApprovalRes(*model.NewApproval("approval-1", "admin", model.NewWalletVoidInfo(
			"txn-1",
			"driver-1",
			200.0,
		))).Info

		actual, ok := info.(*WalletVoidInfoRes)
		require.True(tt, ok)
		require.Equal(tt, string(model.WalletCategory), actual.Category)
		require.Equal(tt, string(model.VoidAction), actual.Action)
		require.Equal(tt, "txn-1", actual.VoidTxnID)
	})

	t.Run("should success to new approval wallet void add", func(tt *testing.T) {
		info := NewApprovalRes(*model.NewApproval("approval-1", "admin", model.NewWalletVoidAddInfo(
			"txn-1",
			"driver-1",
			200.0,
		))).Info

		actual, ok := info.(*WalletVoidAddInfoRes)
		require.True(tt, ok)
		require.Equal(tt, string(model.WalletCategory), actual.Category)
		require.Equal(tt, string(model.VoidAddAction), actual.Action)
		require.Equal(tt, "txn-1", actual.VoidTxnID)
		require.Equal(tt, types.NewMoney(200.0), actual.Amount)
	})

}
