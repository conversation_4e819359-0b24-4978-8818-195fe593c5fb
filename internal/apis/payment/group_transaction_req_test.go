package payment

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestCreateGroupTransactionReq_GroupTransaction(t *testing.T) {

	newFile := func(content string) *multipart.FileHeader {
		var b bytes.Buffer
		w := multipart.NewWriter(&b)

		fw, _ := w.CreateFormFile("file", "file.txt")
		io.Copy(fw, strings.NewReader(content))
		w.Close()

		r := multipart.NewReader(&b, w.<PERSON>())
		form, _ := r.ReadForm(20000)
		file := form.File["file"][0]

		return file
	}

	t.Run("valid file should give group transaction (WALLET)", func(tt *testing.T) {
		content := `date,driverId,category,type,subtype,amount,order id,incentive_name,incentve_sources,remark,form id
	20/05/2020,driver-1,WALLET,INCENTIVE,,200.00,,,,remark1,form id
	30/05/2020,driver-2,WALLET,CLAIM,,300.00,LM-01,,,remark2,form id
	10/05/2020,driver-3,WALLET,COMPENSATION,,20.00,,,,remark3,form id
	22/05/2020,driver-4,WALLET,SUBSIDIZE,,200.45,LM-02,,,remark4,form id
	22/05/2020,driver-5,WALLET,CASH_ADVANCE_COUPON,,100,LM-03,,,remark4,form id
	22/05/2020,driver-6,WALLET,RIDER_REFERRAL_INCENTIVE,,130,,,,remark4,form id
	26/05/2020,driver-7,WALLET,NEW_RIDER_INCENTIVE,,400.00,,,,remark5,form id
	26/05/2020,driver-8,WALLET,SUBSIDIZE,PARKING_FEE,120.00,LM-04,,,remark6,form id
	26/05/2020,driver-9,WALLET,OTHER_INCENTIVE,RESTAURANT_GP_JOIN,120.00,LM-04,,,remark7,form id
	26/05/2020,driver-10,WALLET,NEW_TYPE,NEW_SUB_TYPE,120.00,LM-04,,,remark,form id
	26/05/2020,driver-11,WALLET,INCENTIVE,NEW_SUB_TYPE,120.00,LM-04,,,remark,form id
	26/05/2020,driver-12,WALLET,INCENTIVE,NEW_SUB_TYPE,120.00,LM-04,Incentive-A,"A,B,C",remark,form id`

		req := CreateGroupTransactionReq{
			File:                     newFile(content),
			DryRun:                   false,
			GroupTransactionCategory: "WALLET",
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound).Times(9) // row 1-9
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(true, nil).Times(9)                    // row 1-9
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.TransactionScheme{Category: model.WalletTransactionCategory, Type: "NEW_TYPE", SubType: "NEW_SUB_TYPE", RequiredTax: true}, nil)
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.TransactionScheme{Category: model.WalletTransactionCategory, Type: "INCENTIVE", SubType: "NEW_SUB_TYPE"}, nil).Times(2)

		ids := []string{"LM-01", "LM-02", "LM-03", "LM-04"}
		for _, id := range ids {
			deps.ordeRepo.EXPECT().
				IsExist(gomock.Any(), id).
				Return(true)
		}

		gt, err := req.GroupTransaction(context.Background(), api.OrderRepo, api.TransactionSchemeRepo, 0.0)

		require.NoError(tt, err)
		require.NotNil(tt, gt)
		require.Len(tt, gt.Items(), 19)

		items := make([]model.GroupTransactionItem, 0)

		for _, i := range gt.Items() {
			if i.Type() != model.WithholdingTransactionType {
				items = append(items, i)
			}
		}

		item := items[0]
		require.Equal(tt, "driver-1", item.DriverID())
		require.Equal(tt, model.IncentiveTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, types.NewMoney(200.00), item.Amount())

		item = items[1]
		require.Equal(tt, "driver-2", item.DriverID())
		require.Equal(tt, model.ClaimTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, types.NewMoney(300.00), item.Amount())

		item = items[2]
		require.Equal(tt, "driver-3", item.DriverID())
		require.Equal(tt, model.CompensationTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, types.NewMoney(20.00), item.Amount())

		item = items[3]
		require.Equal(tt, "driver-4", item.DriverID())
		require.Equal(tt, model.SubsidizeTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, types.NewMoney(200.45), item.Amount())

		item = items[4]
		require.Equal(tt, "driver-5", item.DriverID())
		require.Equal(tt, model.CashAdvanceCouponTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, types.NewMoney(100.00), item.Amount())

		item = items[5]
		require.Equal(tt, "driver-6", item.DriverID())
		require.Equal(tt, model.RiderReferralIncentiveTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, types.NewMoney(130.00), item.Amount())

		item = items[6]
		require.Equal(tt, "driver-7", item.DriverID())
		require.Equal(tt, model.NewRiderIncentiveTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, types.NewMoney(400.00), item.Amount())

		item = items[7]
		require.Equal(tt, "driver-8", item.DriverID())
		require.Equal(tt, model.SubsidizeTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, model.ParkingFeeTransactionSubType, item.SubType())
		require.Equal(tt, types.NewMoney(120.00), item.Amount())

		item = items[8]
		require.Equal(tt, "driver-9", item.DriverID())
		require.Equal(tt, model.OtherIncentiveTransactionType, item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, model.RestaurantGPJoinTransactionSubType, item.SubType())
		require.Equal(tt, types.NewMoney(120.00), item.Amount())

		item = items[9]
		require.Equal(tt, "driver-10", item.DriverID())
		require.Equal(tt, model.TransactionType("NEW_TYPE"), item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, model.TransactionSubType("NEW_SUB_TYPE"), item.SubType())
		require.Equal(tt, types.NewMoney(120.00), item.Amount())

		item = items[10]
		require.Equal(tt, "driver-11", item.DriverID())
		require.Equal(tt, model.TransactionType("INCENTIVE"), item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, model.TransactionSubType("NEW_SUB_TYPE"), item.SubType())
		require.Equal(tt, types.NewMoney(120.00), item.Amount())

		// LMF-4497 Add two new fields (incentive name, incentive sources)
		item = items[11]
		require.Equal(tt, "driver-12", item.DriverID())
		require.Equal(tt, model.TransactionType("INCENTIVE"), item.Type())
		require.Equal(tt, model.WalletTransactionCategory, item.Category())
		require.Equal(tt, model.TransactionSubType("NEW_SUB_TYPE"), item.SubType())
		require.Equal(tt, types.NewMoney(120.00), item.Amount())
		require.Equal(tt, []string{"Incentive-A"}, item.IncentiveName())
		require.ElementsMatch(tt, []string{"A", "B", "C"}, item.IncentiveSources())
	})

	t.Run("valid file should give group transaction (CREDIT)", func(tt *testing.T) {
		content := `date,driverId,category,type,subtype,amount,order id,remark
	20/05/2020,driver-1,CREDIT,CHARGE,RIDER_GEAR,200.00,,remark1
	10/05/2020,driver-2,CREDIT,CHARGE,RIDER_GEAR,300.00,,remark2
	30/05/2020,driver-3,CREDIT,CHARGE,RIDER_GEAR,400.00,LM-01,remark3`

		req := CreateGroupTransactionReq{
			File:                     newFile(content),
			DryRun:                   false,
			GroupTransactionCategory: "CREDIT",
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound).Times(1)                                                                                                       // row 1
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(true, nil).Times(1)                                                                                                                          // row 1
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.TransactionScheme{Category: model.CreditTransactionCategory, Type: "NEW_TYPE", SubType: "NEW_SUB_TYPE", RequiredTax: true}, nil)     // row 2
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.TransactionScheme{Category: model.CreditTransactionCategory, Type: "NEW_TYPE", SubType: "NEW_SUB_TYPE", RequiredOrderID: true}, nil) // row 3
		ids := []string{"LM-01"}
		for _, id := range ids {
			deps.ordeRepo.EXPECT().
				IsExist(gomock.Any(), id).
				Return(true)
		}

		gt, err := req.GroupTransaction(context.Background(), api.OrderRepo, api.TransactionSchemeRepo, 0.0)

		require.NoError(tt, err)
		require.NotNil(tt, gt)
		require.Len(tt, gt.Items(), 4)

		items := make([]model.GroupTransactionItem, 0)

		for _, i := range gt.Items() {
			if i.Type() != model.WithholdingTransactionType {
				items = append(items, i)
			}
		}

		item := items[0]
		require.Equal(tt, "driver-1", item.DriverID())
		require.Equal(tt, model.ChargeTransactionType, item.Type())
		require.Equal(tt, model.CreditTransactionCategory, item.Category())
		require.Equal(tt, types.NewMoney(200.00), item.Amount())

		item = items[1]
		require.Equal(tt, "driver-2", item.DriverID())
		require.Equal(tt, model.TransactionType("NEW_TYPE"), item.Type())
		require.Equal(tt, model.CreditTransactionCategory, item.Category())
		require.Equal(tt, model.TransactionSubType("NEW_SUB_TYPE"), item.SubType())
		require.Equal(tt, types.NewMoney(300.00), item.Amount())

		item = items[2]
		require.Equal(tt, "driver-3", item.DriverID())
		require.Equal(tt, model.TransactionType("NEW_TYPE"), item.Type())
		require.Equal(tt, model.CreditTransactionCategory, item.Category())
		require.Equal(tt, model.TransactionSubType("NEW_SUB_TYPE"), item.SubType())
		require.Equal(tt, types.NewMoney(400.00), item.Amount())
		require.Equal(tt, "LM-01", item.OrderID())
	})

	t.Run("should return error when content in row is invalid", func(tt *testing.T) {
		content := `date,driverId,category,type,subtype,amount,order id, incentive_name,incentve_sources,remark,form id
	20/05/2020,driver-1,WALLET,INCENTIVE,,200.00,LM-01,,,remark1,form id
	30/05/2020,driver-2,WALLET,INVALID,,xxxx,LM-02,,,remark2,form id
	30/05/2020,driver-2,WALLET,CHARGE,RIDER_INSURANCE_LIFE,200.0,LM-02,,,remark2,form id
	10/05/2020,driver-3,WALLET,COMPENSATION,,300.00,LM-03,,,remark3,form id
	22/05/2020,driver-4,NOTEXISTS,INCENTIVE,,200.45,LM-04,,,remark4,form id
	22/05/2020,driver-4,WALLET,INCENTIVE,,-100.0,LM-04,,,remark5,form id
	22/05/2020,driver-4,WALLET,NEW_TYPE,NEW_SUB_TYPE,100.0,,,,remark4,form id`

		req := CreateGroupTransactionReq{
			File:   newFile(content),
			DryRun: false,
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound).Times(6)
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(true, nil)           // row 1
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(false, nil).Times(2) // row 2, 3
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(true, nil).Times(3)  // row 4, 6
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.TransactionScheme{Category: model.WalletTransactionCategory, Type: "NEW_TYPE", SubType: "NEW_SUB_TYPE", RequiredTax: true, RequiredOrderID: true}, nil)

		gt, errs := req.GroupTransaction(context.Background(), api.OrderRepo, api.TransactionSchemeRepo, 0.0)

		require.Error(tt, errs)
		require.Nil(tt, gt)
		require.Len(tt, errs, 6)
		require.IsType(tt, errors.MultipleError{}, errs)

		multiErr := errs.(errors.MultipleError)
		err := multiErr[0]
		require.Equal(tt, err.Payload(), map[string]interface{}{
			"row":    2,
			"field":  "amount",
			"reason": "invalid number format",
		})
		err = multiErr[1]
		require.Equal(tt, err.Payload(), map[string]interface{}{
			"row":    2,
			"field":  "type",
			"reason": "invalid type or subtype",
		})
		err = multiErr[2]
		require.Equal(tt, err.Payload(), map[string]interface{}{
			"row":    3,
			"field":  "type",
			"reason": "invalid type or subtype",
		})
		err = multiErr[3]
		require.Equal(tt, err.Payload(), map[string]interface{}{
			"row":    5,
			"field":  "category",
			"reason": "invalid category",
		})
		err = multiErr[4]
		require.Equal(tt, err.Payload(), map[string]interface{}{
			"row":    6,
			"field":  "amount",
			"reason": "amount must be greater than zero",
		})

		err = multiErr[5]
		require.Equal(tt, err.Payload(), map[string]interface{}{
			"row":    7,
			"field":  "orderID",
			"reason": "orderID is required",
		})
	})

	t.Run("should return error when scheme is reserved for system operation", func(tt *testing.T) {
		content := `date,driverId,category,type,subtype,amount,order id,incentive_name,incentve_sources,remark,form id
	20/05/2020,driver-1,WALLET,INCENTIVE,,200.00,LM-01,,,remark1,form id
	30/05/2020,driver-2,WALLET,WITHDRAW,,200.00,LM-02,,,remark2,form id`

		req := CreateGroupTransactionReq{
			File:   newFile(content),
			DryRun: false,
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound).Times(2)
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(true, nil).Times(2)

		gt, errs := req.GroupTransaction(context.Background(), api.OrderRepo, api.TransactionSchemeRepo, 0.0)

		require.Error(tt, errs)
		require.Nil(tt, gt)
		require.Len(tt, errs, 1)
		require.IsType(tt, errors.MultipleError{}, errs)

		multiErr := errs.(errors.MultipleError)
		err := multiErr[0]
		require.Equal(tt, err.Payload(), map[string]interface{}{
			"row":    2,
			"field":  "type",
			"reason": "reserved scheme for system operation",
		})
	})

	t.Run("should return error when row more than max group transaction items size", func(tt *testing.T) {
		content := strings.Builder{}
		content.WriteString("date,driverId,category,type,subtype,amount,order id,incentive_name,incentve_sources,remark,form id\n")
		rows := MaxGroupTxnItemsSize + 1
		for i := 0; i < rows; i++ {
			content.WriteString("20/05/2020,driver-1,WALLET,INCENTIVE,,200.00,,,LM-01,REMARK 1,form id\n")
		}

		req := CreateGroupTransactionReq{
			File:   newFile(content.String()),
			DryRun: false,
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound).Times(MaxGroupTxnItemsSize)
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(true, nil).Times(MaxGroupTxnItemsSize)

		gt, errs := req.GroupTransaction(context.Background(), api.OrderRepo, api.TransactionSchemeRepo, 0.0)
		require.Error(tt, errs)
		require.Nil(tt, gt)
		require.Len(tt, errs, 1)
		require.IsType(tt, errors.MultipleError{}, errs)

		multiErr := errs.(errors.MultipleError)
		err := multiErr[0]
		reason := fmt.Sprintf("number of row exceed limit %d rows", MaxGroupTxnItemsSize)
		require.Equal(tt, err.Payload(), map[string]interface{}{
			"row":    rows,
			"field":  "row",
			"reason": reason,
		})
	})

}
