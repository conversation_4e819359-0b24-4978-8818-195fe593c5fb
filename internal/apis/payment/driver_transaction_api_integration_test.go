//go:build integration_test
// +build integration_test

package payment_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

const driverCHAINGMAI = "DRV_CHIANG_MAI_2_ONLINE"
const driverBANNED = "DRV_BANNED"

func TestDriverTransactionAPI_BulkUnbanWithdraw(t *testing.T) {
	t.Run("unban success", func(t *testing.T) {
		t.<PERSON>()
		ctn := ittest.NewContainer(t)
		csvContent := fmt.Sprintf("driver id, reason\n%s,just reason\n%s,", driverCHAINGMAI, driverBANNED)
		ctx := requestBulkUnbanWithdraw(t, ctn, csvContent)
		ctn.GinEngineRouter.HandleContext(ctx.GinCtx())
		ctx.AssertResponseCode(t, http.StatusOK)
		ch, err := ctn.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverCHAINGMAI)
		require.NoError(t, err)
		require.Equal(t, model.BanWithdrawDetail{}, ch.BanWithdrawDetail)

		db, err := ctn.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverBANNED)
		require.NoError(t, err)
		require.Equal(t, model.BanWithdrawDetail{}, db.BanWithdrawDetail)
	})

	t.Run("resp error not empty if driver not found", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		csvContent := fmt.Sprintf("driver id, reason\n%s,just reason\n%s,", driverCHAINGMAI, "fake_id")
		ctx := requestBulkUnbanWithdraw(t, ctn, csvContent)
		ctn.GinEngineRouter.HandleContext(ctx.GinCtx())
		ctx.AssertResponseCode(t, http.StatusOK)

		ch, err := ctn.DataStoreDriverTransactionRepository.FindByID(context.Background(), driverCHAINGMAI)
		require.NoError(t, err)
		require.Equal(t, model.BanWithdrawDetail{}, ch.BanWithdrawDetail)

		var resp *payment.BulkUnbanWithdrawResponse
		ctx.DecodeJSONResponse(&resp)

		require.Equal(t, 1, len(resp.Successes))
		require.Equal(t, 1, len(resp.Failures))

		require.Equal(t, "DRV_CHIANG_MAI_2_ONLINE", resp.Successes[0])
		require.Equal(t, "fake_id", resp.Failures[0].DriverID)
		require.Equal(t, "Data not found", resp.Failures[0].Reason)
	})

	t.Run("error when no csv file", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPUT("/v1/admin/bulk/driver-transactions/unban").
			Body().MultipartForm().
			String("requestedBy", "Automated").
			Build()

		ctn.GinEngineRouter.HandleContext(ctx.GinCtx())
		ctx.AssertResponseCode(t, http.StatusBadRequest)
	})
}

func requestBulkUnbanWithdraw(t *testing.T, ctn *ittest.IntegrationTestContainer, csvContent string) *testutil.GinContextWithRecorder {
	err := ctn.DataStoreDriverTransactionRepository.Create(context.Background(), &model.DriverTransaction{
		DriverID:              driverCHAINGMAI,
		PurchaseCreditBalance: 100,
		WalletBalance:         100,
		BanWithdrawDetail: model.BanWithdrawDetail{
			Reason:  "just test",
			BanAt:   time.Now().Add(-(time.Minute * 10)),
			OrderID: "O_1",
			BanBy:   "test",
		},
	})

	require.NoError(t, err)
	ctx := testutil.NewContextWithRecorder()
	ctx.SetPUT("/v1/admin/bulk/driver-transactions/unban").
		Body().MultipartForm().
		File("file", "unban_withdraw.csv", csvContent).
		String("requestedBy", "Automated").
		Build()

	return ctx
}

func TestDriverTransactionAPI_ProcessDriverTransaction(tt *testing.T) {
	type Request struct {
		DriverID   string
		TxnChannel model.TransactionChannel
		TxnAction  model.TransactionAction
		TxnStatus  model.TransactionStatus
		TxnBuilder service.TransactionInfosBuilder
		TxnOpts    []func(*service.ProcessDriverTransactionOption)
	}

	type Response struct {
		DriverTxn model.DriverTransaction
		Txns      []model.Transaction
		Error     error
	}

	type Testdata struct {
		Name             string
		TargetFixture    string
		Request          Request
		ExpectedResponse Response
	}

	testSet := []Testdata{
		{
			Name: "purchase 100 credit to driver id 'DRV_WB0_CB0_FC100_PC0_IA0'",
			Request: Request{
				DriverID:   "DRV_WB0_CB0_FC100_PC0_IA0",
				TxnChannel: model.SystemTransactionChannel,
				TxnAction:  model.PurchaseTransactionAction,
				TxnStatus:  model.SuccessTransactionStatus,
				TxnBuilder: func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
					return []model.TransactionInfo{
						*model.NewPurchaseCreditTransactionInfo(
							"DRV_WB0_CB0_FC100_PC0_IA0",
							100,
						),
					}, nil
				},
			},
			ExpectedResponse: Response{
				DriverTxn: model.DriverTransaction{
					DriverID: "DRV_WB0_CB0_FC100_PC0_IA0",
					FreeCreditTransactions: []model.TransactionInfo{
						{
							Category:   model.CreditTransactionCategory,
							Type:       model.PurchaseTransactionType,
							DriverID:   "LMDXXXXXX",
							CreditType: model.FreeTransactionCreditType,
							Amount:     types.NewMoney(100),
						},
					},
					PurchaseCreditBalance: types.Money(100),
					WalletBalance:         types.NewMoney(0),
				},
				Txns: []model.Transaction{
					{
						Channel: model.SystemTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchaseTransactionAction,
						Info: model.TransactionInfo{
							Category:      model.CreditTransactionCategory,
							Type:          model.PurchaseTransactionType,
							Operator:      model.AdditionOperator,
							DriverID:      "DRV_WB0_CB0_FC100_PC0_IA0",
							Amount:        types.NewMoney(100),
							WalletBalance: types.NewMoney(0),
							CreditBalance: types.NewMoney(100),
							WalletAfter:   types.NewMoney(0),
							CreditAfter:   types.NewMoney(200),
						},
						Remarks: []model.Remark{
							{
								Message:   "Before Credit Balance: 100.00 THB\nBefore Wallet Balance: 0.00 THB\nBefore Installment Amount: 0.00 THB",
								CreatedBy: "SYSTEM",
							},
						},
					},
				},
				Error: nil,
			},
		},
		{
			Name: "purchase 100 positive credit to driver id 'DRV_WB0_CB0_FC0_PC0_IA0'",
			Request: Request{
				DriverID:   "DRV_WB0_CB0_FC0_PC0_IA0",
				TxnChannel: model.SystemTransactionChannel,
				TxnAction:  model.PurchasePositiveCreditTransactionAction,
				TxnStatus:  model.SuccessTransactionStatus,
				TxnBuilder: func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
					return []model.TransactionInfo{
						*model.NewPositiveCreditTransactionInfo(
							"DRV_WB0_CB0_FC0_PC0_IA0",
							100,
						),
					}, nil
				},
			},
			ExpectedResponse: Response{
				DriverTxn: model.DriverTransaction{
					DriverID:               "DRV_WB0_CB0_FC0_PC0_IA0",
					FreeCreditTransactions: []model.TransactionInfo{},
					PurchaseCreditBalance:  types.Money(0),
					WalletBalance:          types.NewMoney(0),
					PositiveCreditBalance:  types.NewMoney(100),
				},
				Txns: []model.Transaction{
					{
						Channel: model.SystemTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchasePositiveCreditTransactionAction,
						Info: model.TransactionInfo{
							Category:      model.CreditTransactionCategory,
							Type:          model.PurchaseTransactionType,
							SubType:       model.PositiveCreditTransactionSubType,
							CreditType:    model.PositiveCreditTransactionCreditType,
							Operator:      model.AdditionOperator,
							DriverID:      "DRV_WB0_CB0_FC0_PC0_IA0",
							Amount:        types.NewMoney(100),
							WalletBalance: types.NewMoney(0),
							CreditBalance: types.NewMoney(0),
							WalletAfter:   types.NewMoney(0),
							CreditAfter:   types.NewMoney(100),
						},
						Remarks: []model.Remark{
							{
								Message:   "Before Credit Balance: 0.00 THB\nBefore Wallet Balance: 0.00 THB\nBefore Installment Amount: 0.00 THB",
								CreatedBy: "SYSTEM",
							},
						},
					},
				},
				Error: nil,
			},
		},
		{
			Name: "purchase 100 positive credit to driver id 'DRV_WB0_CB-100_FC0_PC0_IA0'",
			Request: Request{
				DriverID:   "DRV_WB0_CB-100_FC0_PC0_IA0",
				TxnChannel: model.SystemTransactionChannel,
				TxnAction:  model.PurchasePositiveCreditTransactionAction,
				TxnStatus:  model.SuccessTransactionStatus,
				TxnBuilder: func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
					return []model.TransactionInfo{
						*model.NewPositiveCreditTransactionInfo(
							"DRV_WB0_CB-100_FC0_PC0_IA0",
							100,
						),
					}, nil
				},
			},
			ExpectedResponse: Response{
				DriverTxn: model.DriverTransaction{
					DriverID:               "DRV_WB0_CB-100_FC0_PC0_IA0",
					FreeCreditTransactions: []model.TransactionInfo{},
					PurchaseCreditBalance:  types.Money(0),
					WalletBalance:          types.NewMoney(0),
					PositiveCreditBalance:  types.NewMoney(0),
				},
				Txns: []model.Transaction{
					{
						Channel: model.SystemTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchasePositiveCreditTransactionAction,
						Info: model.TransactionInfo{
							Category:      model.CreditTransactionCategory,
							Type:          model.PurchaseTransactionType,
							SubType:       model.PositiveCreditTransactionSubType,
							CreditType:    model.PositiveCreditTransactionCreditType,
							Operator:      model.AdditionOperator,
							DriverID:      "DRV_WB0_CB-100_FC0_PC0_IA0",
							Amount:        types.NewMoney(100),
							WalletBalance: types.NewMoney(0),
							CreditBalance: types.NewMoney(-100),
							WalletAfter:   types.NewMoney(0),
							CreditAfter:   types.NewMoney(0),
						},
						Remarks: []model.Remark{
							{
								Message:   "Before Credit Balance: -100.00 THB\nBefore Wallet Balance: 0.00 THB\nBefore Installment Amount: 0.00 THB",
								CreatedBy: "SYSTEM",
							},
						},
					},
				},
				Error: nil,
			},
		},
		{
			Name: "purchase 150 positive credit to driver id 'DRV_WB0_CB-100_FC0_PC0_IA0'",
			Request: Request{
				DriverID:   "DRV_WB0_CB-100_FC0_PC0_IA0",
				TxnChannel: model.SystemTransactionChannel,
				TxnAction:  model.PurchasePositiveCreditTransactionAction,
				TxnStatus:  model.SuccessTransactionStatus,
				TxnBuilder: func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
					return []model.TransactionInfo{
						*model.NewPositiveCreditTransactionInfo(
							"DRV_WB0_CB-100_FC0_PC0_IA0",
							150,
						),
					}, nil
				},
			},
			ExpectedResponse: Response{
				DriverTxn: model.DriverTransaction{
					DriverID:               "DRV_WB0_CB-100_FC0_PC0_IA0",
					FreeCreditTransactions: []model.TransactionInfo{},
					PurchaseCreditBalance:  types.Money(0),
					WalletBalance:          types.NewMoney(0),
					PositiveCreditBalance:  types.NewMoney(50),
				},
				Txns: []model.Transaction{
					{
						Channel: model.SystemTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchasePositiveCreditTransactionAction,
						Info: model.TransactionInfo{
							Category:      model.CreditTransactionCategory,
							Type:          model.PurchaseTransactionType,
							SubType:       model.PositiveCreditTransactionSubType,
							CreditType:    model.PositiveCreditTransactionCreditType,
							Operator:      model.AdditionOperator,
							DriverID:      "DRV_WB0_CB-100_FC0_PC0_IA0",
							Amount:        types.NewMoney(150),
							WalletBalance: types.NewMoney(0),
							CreditBalance: types.NewMoney(-100),
							WalletAfter:   types.NewMoney(0),
							CreditAfter:   types.NewMoney(50),
						},
						Remarks: []model.Remark{
							{
								Message:   "Before Credit Balance: -100.00 THB\nBefore Wallet Balance: 0.00 THB\nBefore Installment Amount: 0.00 THB",
								CreatedBy: "SYSTEM",
							},
						},
					},
				},
				Error: nil,
			},
		},
		{
			Name: "purchase 125 positive credit to driver id 'DRV_WB0_CB-100_FC0_PC0_IA-50'",
			Request: Request{
				DriverID:   "DRV_WB0_CB-100_FC0_PC0_IA-50",
				TxnChannel: model.SystemTransactionChannel,
				TxnAction:  model.PurchasePositiveCreditTransactionAction,
				TxnStatus:  model.SuccessTransactionStatus,
				TxnBuilder: func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
					return []model.TransactionInfo{
						*model.NewPositiveCreditTransactionInfo(
							"DRV_WB0_CB-100_FC0_PC0_IA-50",
							125,
						),
					}, nil
				},
			},
			ExpectedResponse: Response{
				DriverTxn: model.DriverTransaction{
					DriverID:               "DRV_WB0_CB-100_FC0_PC0_IA-50",
					FreeCreditTransactions: []model.TransactionInfo{},
					PurchaseCreditBalance:  types.Money(0),
					WalletBalance:          types.NewMoney(0),
					PositiveCreditBalance:  types.NewMoney(25),
				},
				Txns: []model.Transaction{
					{
						Channel: model.SystemTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchasePositiveCreditTransactionAction,
						Info: model.TransactionInfo{
							Category:          model.CreditTransactionCategory,
							Type:              model.PurchaseTransactionType,
							SubType:           model.PositiveCreditTransactionSubType,
							CreditType:        model.PositiveCreditTransactionCreditType,
							Operator:          model.AdditionOperator,
							DriverID:          "DRV_WB0_CB-100_FC0_PC0_IA-50",
							Amount:            types.NewMoney(125),
							WalletBalance:     types.NewMoney(0),
							CreditBalance:     types.NewMoney(-100),
							InstallmentAmount: types.NewMoney(-50),
							WalletAfter:       types.NewMoney(0),
							CreditAfter:       types.NewMoney(25),
							InstallmentAfter:  types.NewMoney(0),
						},
						Remarks: []model.Remark{
							{
								Message:   "Before Credit Balance: -100.00 THB\nBefore Wallet Balance: 0.00 THB\nBefore Installment Amount: -50.00 THB",
								CreatedBy: "SYSTEM",
							},
						},
					},
				},
				Error: nil,
			},
		},
		{
			Name: "purchase 50 positive credit to driver id 'DRV_WB0_CB-100_FC0_PC0_IA-50'",
			Request: Request{
				DriverID:   "DRV_WB0_CB-100_FC0_PC0_IA-50",
				TxnChannel: model.SystemTransactionChannel,
				TxnAction:  model.PurchasePositiveCreditTransactionAction,
				TxnStatus:  model.SuccessTransactionStatus,
				TxnBuilder: func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
					return []model.TransactionInfo{
						*model.NewPositiveCreditTransactionInfo(
							"DRV_WB0_CB-100_FC0_PC0_IA-50",
							50,
						),
					}, nil
				},
			},
			ExpectedResponse: Response{
				DriverTxn: model.DriverTransaction{
					DriverID:               "DRV_WB0_CB-100_FC0_PC0_IA-50",
					FreeCreditTransactions: []model.TransactionInfo{},
					PurchaseCreditBalance:  types.NewMoney(-50),
					WalletBalance:          types.NewMoney(0),
					PositiveCreditBalance:  types.NewMoney(0),
					InstallmentAmount:      types.NewMoney(-50),
					NegativeCreditDetail: model.NegativeCreditDetail{
						IsCreditNegative: true,
					},
				},
				Txns: []model.Transaction{
					{
						Channel: model.SystemTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchasePositiveCreditTransactionAction,
						Info: model.TransactionInfo{
							Category:          model.CreditTransactionCategory,
							Type:              model.PurchaseTransactionType,
							SubType:           model.PositiveCreditTransactionSubType,
							CreditType:        model.PositiveCreditTransactionCreditType,
							Operator:          model.AdditionOperator,
							DriverID:          "DRV_WB0_CB-100_FC0_PC0_IA-50",
							Amount:            types.NewMoney(50),
							WalletBalance:     types.NewMoney(0),
							CreditBalance:     types.NewMoney(-100),
							InstallmentAmount: types.NewMoney(-50),
							WalletAfter:       types.NewMoney(0),
							CreditAfter:       types.NewMoney(-50),
							InstallmentAfter:  types.NewMoney(-50),
						},
						Remarks: []model.Remark{
							{
								Message:   "Before Credit Balance: -100.00 THB\nBefore Wallet Balance: 0.00 THB\nBefore Installment Amount: -50.00 THB",
								CreatedBy: "SYSTEM",
							},
						},
					},
					{
						Channel: model.SystemTransactionChannel,
						Status:  model.SuccessTransactionStatus,
						Action:  model.PurchasePositiveCreditTransactionAction,
						Info: model.TransactionInfo{
							Category:          model.CreditTransactionCategory,
							Type:              model.OutstandingTransactionType,
							DriverID:          "DRV_WB0_CB-100_FC0_PC0_IA-50",
							Amount:            types.NewMoney(50),
							WalletBalance:     types.NewMoney(0),
							CreditBalance:     types.NewMoney(0),
							InstallmentAmount: types.NewMoney(0),
							WalletAfter:       types.NewMoney(0),
							CreditAfter:       types.NewMoney(-50),
							InstallmentAfter:  types.NewMoney(-50),
						},
					},
				},
				Error: nil,
			},
		},
	}

	ctn := ittest.NewContainer(tt)
	if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_driver_transactions"); err != nil {
		tt.Errorf("Unexpected error initfixture: %v", err)
	}

	for _, testUnit := range testSet {
		tt.Run(testUnit.Name, func(t *testing.T) {
			ctx := context.Background()

			var bakDriverTxn model.DriverTransaction
			dbErr := ctn.DriverTransactionDataStore.FindOne(ctx, bson.M{
				"driver_id": testUnit.Request.DriverID,
			}, &bakDriverTxn)
			require.NoError(t, dbErr)
			defer func() {
				dbErr := ctn.DriverTransactionDataStore.Replace(ctx, bson.M{
					"driver_id": testUnit.Request.DriverID,
				}, &bakDriverTxn)
				require.NoError(t, dbErr)
			}()

			driverTxn, txns, err := ctn.Locator.DriverTransactionServiceV2.ProcessDriverTransaction(
				ctx,
				testUnit.Request.DriverID,
				testUnit.Request.TxnChannel,
				testUnit.Request.TxnAction,
				testUnit.Request.TxnStatus,
				testUnit.Request.TxnBuilder,
				testUnit.Request.TxnOpts...,
			)

			require.ErrorIs(t, err, testUnit.ExpectedResponse.Error)
			// Driver Transactions
			{
				testUnit.ExpectedResponse.DriverTxn.CreatedAt = driverTxn.CreatedAt
				testUnit.ExpectedResponse.DriverTxn.UpdatedAt = driverTxn.UpdatedAt

				if testUnit.ExpectedResponse.DriverTxn.NegativeCreditDetail.IsCreditNegative == true {
					require.NotNil(t, driverTxn.NegativeCreditDetail.NegativeCreditStartTime)
				}
				testUnit.ExpectedResponse.DriverTxn.NegativeCreditDetail.NegativeCreditStartTime = driverTxn.NegativeCreditDetail.NegativeCreditStartTime

				require.Len(t, driverTxn.FreeCreditTransactions, len(testUnit.ExpectedResponse.DriverTxn.FreeCreditTransactions))
				{
					for index := range testUnit.ExpectedResponse.DriverTxn.FreeCreditTransactions {
						exptTxn := testUnit.ExpectedResponse.DriverTxn.FreeCreditTransactions[index]
						actlTxn := driverTxn.FreeCreditTransactions[index]
						exptTxn.ExpiredDate = actlTxn.ExpiredDate
						require.Equal(t, exptTxn, actlTxn)
					}
				}

				testUnit.ExpectedResponse.DriverTxn.FreeCreditTransactions = driverTxn.FreeCreditTransactions
				require.Equal(t, testUnit.ExpectedResponse.DriverTxn, driverTxn)
			}
			// Transactions
			{
				require.Len(t, txns, len(testUnit.ExpectedResponse.Txns))
				for index := range testUnit.ExpectedResponse.Txns {
					exptTxn := testUnit.ExpectedResponse.Txns[index]
					actlTxn := txns[index]

					require.Len(t, actlTxn.Remarks, len(exptTxn.Remarks))
					for index := range exptTxn.Remarks {
						exptTxn.Remarks[index].CreatedAt = actlTxn.Remarks[index].CreatedAt
					}
					require.Equal(t, exptTxn.Remarks, actlTxn.Remarks)

					exptTxn.TransactionID = actlTxn.TransactionID
					exptTxn.CreatedAt = actlTxn.CreatedAt
					exptTxn.UpdatedAt = actlTxn.UpdatedAt
					exptTxn.Info.TransactionDate = actlTxn.Info.TransactionDate
					exptTxn.Info.TransactionDateTime = actlTxn.Info.TransactionDateTime
					exptTxn.Remarks = actlTxn.Remarks
					require.Equal(t, exptTxn, actlTxn)
				}
			}
		})
	}
}
