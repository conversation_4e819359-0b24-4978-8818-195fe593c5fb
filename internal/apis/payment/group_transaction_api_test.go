package payment

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"strconv"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestGroupTransactionAPI_Create(t *testing.T) {
	req := func(content string, requestedBy string, effectiveTime time.Time) (*gin.Context, *httptest.ResponseRecorder) {
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		defer w.Close()

		var fw io.Writer
		fw, _ = w.CreateFormField("dryRun")
		fw.Write([]byte(strconv.FormatBool(false)))

		fw, _ = w.CreateFormField("requestedBy")
		fw.Write([]byte(requestedBy))

		fw, _ = w.CreateFormField("effectiveTime")
		fw.Write([]byte(effectiveTime.Format("2006-01-02T15:04:05Z")))

		fw, _ = w.CreateFormFile("file", "file.txt")
		fw.Write([]byte(content))

		ctx, recorder := testutil.TestRequestContext("POST", "/v1/group-transactions", &b)
		ctx.Request.Header.Set("Content-Type", w.FormDataContentType())

		return ctx, recorder
	}

	t.Run("should return 201 when success", func(tt *testing.T) {
		content := `CreatedAt,driverID,Category,Type,Amount,Order,incentive_name,incentive_sources,remark`
		gctx, record := req(content, "admin", time.Time{})
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		deps.groupTxnRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(ctx2 context.Context, gt *model.GroupTransaction) error {
				require.NotNil(tt, gt)
				return nil
			})

		api.Create(gctx)

		require.Equal(tt, http.StatusCreated, record.Code)
	})

	t.Run("should return 201 when success with effective time", func(tt *testing.T) {
		content := `CreatedAt,driverID,Category,Type,Amount,Order,incentive_name,incentive_sources,remark`
		gctx, record := req(content, "admin", time.Now().Add(time.Hour*(1)))
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		deps.groupTxnRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(ctx2 context.Context, gt *model.GroupTransaction) error {
				require.NotNil(tt, gt)
				return nil
			})

		api.Create(gctx)

		require.Equal(tt, http.StatusCreated, record.Code)
	})

	t.Run("should return 500 when effective time less than time now", func(tt *testing.T) {
		content := `CreatedAt,driverID,Category,Type,Amount,Order,incentive_name,incentive_sources,remark`
		gctx, record := req(content, "admin", time.Now().Add(time.Hour*(-24)))

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, _ := newGroupTransactionAPI(ctrl)

		api.Create(gctx)

		require.Equal(tt, http.StatusInternalServerError, record.Code)
	})

	t.Run("should return 400 when file invalid", func(tt *testing.T) {
		content := `date,driverId,category,type,sub type,amount,order id,incentive_name,incentive_sources,remark,form id
20/05/2020,driver-1,WALLET,INCENTIVE,,200.00,LM-001,,,remark1,form id
30/05/2020,driver-2,WALLET,INVALID,,xxxx,LM-001,,,remark2,form id
10/05/2020,driver-3,WALLET,COMPENSATION,,300.00,LM-001,,,remark3,form id
22/05/2020,driver-4,NOTEXISTS,INCENTIVE,,200.45,LM-001,,,remark4,form id
22/05/2020,driver-4,WALLET,COMPENSATION,,-100.00,LM-001,,,remark5,form id
`
		gctx, record := req(content, "admin", time.Time{})

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		a, deps := newGroupTransactionAPI(ctrl)
		deps.txnSchemeRepo.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound).Times(5)
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(true, nil)          // row 1
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(false, nil)         // row 2
		deps.txnSchemeRepo.EXPECT().IsExistOnLocalScheme(gomock.Any(), gomock.Any()).Return(true, nil).Times(3) // row 3-5
		a.Create(gctx)

		require.Equal(tt, http.StatusBadRequest, record.Code)

		var err api.Error
		testutil.DecodeJSON(tt, record.Body, &err)

		require.EqualValues(tt, map[string]interface{}{
			"errors": []interface{}{
				map[string]interface{}{
					"row":    2.0,
					"field":  "amount",
					"reason": "invalid number format",
				},
				map[string]interface{}{
					"row":    2.0,
					"field":  "type",
					"reason": "invalid type or subtype",
				},
				map[string]interface{}{
					"row":    4.0,
					"field":  "category",
					"reason": "invalid category",
				},
				map[string]interface{}{
					"row":    5.0,
					"field":  "amount",
					"reason": "amount must be greater than zero",
				},
			},
		}, err.Info)
	})

	t.Run("should return 400 when argument is invalue", func(tt *testing.T) {
		content := `date,driverId,category,type,sub type,amount,order id,incentive_name,incentive_sources,remark
20/05/2020,driver-1,WALLET,INCENTIVE,,200.00,LM-001,,,remark1
30/05/2020,driver-2,WALLET,INVALID,,xxxx,LM-002,,,remark2
10/05/2020,driver-3,WALLET,COMPENSATION,,300.00,LM-003,,,remark3
22/05/2020,driver-4,NOTEXISTS,INCENTIVE,,200.45,LM-004,,,remark4
`
		gctx, record := req(content, "", time.Time{})

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		a, _ := newGroupTransactionAPI(ctrl)
		a.Create(gctx)

		require.Equal(tt, http.StatusBadRequest, record.Code)

		var err api.Error
		testutil.DecodeJSON(tt, record.Body, &err)
	})
}

func TestGroupTransactionAPI_Get(t *testing.T) {
	req := func(id string) (*gin.Context, *httptest.ResponseRecorder) {
		url := fmt.Sprintf("/v1/group-transactions/%s", id)
		ctx, recorder := testutil.TestRequestContext("GET", url, nil)
		ctx.Params = gin.Params{
			{Key: "id", Value: id},
		}

		return ctx, recorder
	}

	t.Run("should return 200 when success", func(tt *testing.T) {
		gctx, record := req("id-1")
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		txnScheme := &model.TransactionScheme{
			Category: model.CreditTransactionCategory,
			Type:     "NEW_TYPE",
			SubType:  "NEW_SUB_TYPE",
		}

		item1 := model.NewCompensationGroupTransactionItem("driver-1", 200.0, "", "")
		item1.SetRemark("remark A")
		item2 := model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "LMF-2485555", "")
		item2.SetRemark("remark B")
		item3 := model.NewClaimGroupTransactionItem("driver-2", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark C")
		item4 := model.NewGroupTransactionItemFromScheme(txnScheme, "driver-3", "", 30.0)
		item5 := model.NewGroupTransactionItemFromScheme(txnScheme, "driver-3", "", 30.0)
		item5.SetIncentiveName("Incentive-A")
		item5.SetIncentiveSources([]string{"A", "B", "C"})

		expect := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction)
		expect.AddItem(*item1)
		expect.AddItem(*item2)
		expect.AddItem(*item3)
		expect.AddItem(*item4)
		expect.AddItem(*item5)

		deps.groupTxnRepo.EXPECT().
			Get(ctx, "id-1").
			Return(expect, nil)

		api.Get(gctx)

		require.Equal(tt, http.StatusOK, record.Code)

		var actual GroupTransactionDetailRes
		testutil.DecodeJSON(tt, record.Body, &actual)

		items := make([]GroupTransactionItemRes, len(expect.Items()))
		for i, item := range expect.Items() {
			items[i] = GroupTransactionItemRes{
				DriverID:         item.DriverID(),
				Category:         string(item.Category()),
				Type:             string(item.Type()),
				SubType:          string(item.SubType()),
				Amount:           item.Amount(),
				Status:           string(item.Status()),
				OrderID:          item.OrderID(),
				Remark:           item.Remark(),
				IncentiveNames:   item.IncentiveName(),
				IncentiveSources: item.IncentiveSources(),
			}
		}
		require.Equal(tt, GroupTransactionDetailRes{
			GroupTransactionRes: GroupTransactionRes{
				ID:                       expect.ID(),
				CreatedAt:                expect.CreatedAt(),
				UpdatedAt:                expect.UpdatedAt(),
				TotalTransaction:         uint(len(expect.Items())),
				TotalAmount:              types.NewMoney(580.00),
				TotalDriver:              uint(len(expect.UniqueDriverIDs())),
				RequestedBy:              expect.RequestedBy(),
				Status:                   string(expect.Status()),
				GroupTransactionCategory: expect.GroupTransactionCategory(),
				EffectiveTime:            expect.EffectiveTime(),
			},
			Transactions: items,
			AuditLogs:    []AuditLogRes{},
		}, actual)
	})

	t.Run("should return 404 when id not found", func(tt *testing.T) {
		gctx, record := req("non-exists")
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		deps.groupTxnRepo.EXPECT().
			Get(ctx, "non-exists").
			Return(nil, repository.ErrNotFound)

		api.Get(gctx)

		require.Equal(tt, http.StatusNotFound, record.Code)
	})

}

func TestGroupTransactionAPI_List(t *testing.T) {
	req := func() (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("GET", "/v1/group-transactions", nil)
		return ctx, recorder
	}

	t.Run("should return 200 when success", func(tt *testing.T) {
		gctx, record := req()
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		txnScheme := &model.TransactionScheme{
			Category: model.CreditTransactionCategory,
			Type:     "NEW_TYPE",
			SubType:  "NEW_SUB_TYPE",
		}

		expects := make([]model.GroupTransaction, 3)
		item1 := model.NewCompensationGroupTransactionItem("driver-1", 200.0, "LMF-2485555", "")
		item1.SetRemark("remark A")
		item2 := model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "", "")
		item2.SetRemark("remark B")
		item3 := model.NewClaimGroupTransactionItem("driver-2", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark C")
		item4 := model.NewGroupTransactionItemFromScheme(txnScheme, "driver-3", "", 30.0)

		gt1 := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction)
		gt1.AddItem(*item1)
		gt1.AddItem(*item2)
		gt1.AddItem(*item3)
		gt1.AddItem(*item4)
		expects[0] = *gt1

		item1 = model.NewCompensationGroupTransactionItem("driver-1", 200.0, "LMF-2485555", "")
		item1.SetRemark("remark D")
		item2 = model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "", "")
		item2.SetRemark("remark E")
		item3 = model.NewClaimGroupTransactionItem("driver-2", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark F")
		item4 = model.NewGroupTransactionItemFromScheme(txnScheme, "driver-3", "", 30.0)

		gt2 := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction)
		gt2.AddItem(*item1)
		gt2.AddItem(*item2)
		gt2.AddItem(*item3)
		gt2.AddItem(*item4)
		expects[1] = *gt2

		item1 = model.NewCompensationGroupTransactionItem("driver-1", 200.0, "LMF-2485555", "")
		item1.SetRemark("remark G")
		item2 = model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "", "")
		item2.SetRemark("remark H")
		item3 = model.NewClaimGroupTransactionItem("driver-2", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark I")
		item4 = model.NewGroupTransactionItemFromScheme(txnScheme, "driver-3", "", 30.0)

		gt3 := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction)
		gt3.AddItem(*item1)
		gt3.AddItem(*item2)
		gt3.AddItem(*item3)
		gt3.AddItem(*item4)
		expects[2] = *gt3

		deps.groupTxnRepo.EXPECT().
			Find(ctx, gomock.Any(), gomock.Any(), gomock.Any()).
			Return(expects, nil)

		deps.groupTxnRepo.EXPECT().
			Count(ctx, gomock.Any()).
			Return(20, nil)

		api.List(gctx)

		require.Equal(tt, http.StatusOK, record.Code)

		var actual struct {
			Data []GroupTransactionRes `json:"data"`
		}
		testutil.DecodeJSON(tt, record.Body, &actual)

		expectedRes := make([]GroupTransactionRes, len(expects))
		for i, res := range expects {
			expectedRes[i] = GroupTransactionRes{
				ID:                       res.ID(),
				CreatedAt:                res.CreatedAt(),
				UpdatedAt:                res.UpdatedAt(),
				TotalTransaction:         uint(len(res.Items())),
				TotalDriver:              uint(len(res.UniqueDriverIDs())),
				TotalAmount:              types.NewMoney(550.00),
				RequestedBy:              res.RequestedBy(),
				Status:                   string(res.Status()),
				GroupTransactionCategory: res.GroupTransactionCategory(),
				EffectiveTime:            res.EffectiveTime(),
			}
		}

		require.Equal(tt, expectedRes, actual.Data)
	})

}

func TestGroupTransactionAPI_Approve(t *testing.T) {
	req := func(id string) (*gin.Context, *httptest.ResponseRecorder) {
		url := fmt.Sprintf("/v1/group-transactions/%s/status/approved", id)
		ctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(RejectGroupTransactionReq{RequestedBy: "admin"}))
		ctx.Params = gin.Params{
			{Key: "id", Value: id},
		}
		return ctx, recorder
	}

	t.Run("approve and process success should return 204", func(tt *testing.T) {
		id := "id-1"

		gctx, record := req(id)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		item1 := model.NewCompensationGroupTransactionItem("driver-1", 200.0, "LMF-2485555", "")
		item1.SetRemark("remark A")
		item2 := model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "", "")
		item2.SetRemark("remark B")
		item3 := model.NewClaimGroupTransactionItem("driver-3", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark B")

		gt := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction)
		gt.AddItem(*item1)
		gt.AddItem(*item2)
		gt.AddItem(*item3)

		deps.groupTxnRepo.EXPECT().
			Get(ctx, id, gomock.Any()).
			Return(gt, nil)

		drivers := []model.DriverTransaction{
			*model.NewDriverTransaction("driver-1"),
			*model.NewDriverTransaction("driver-2"),
			*model.NewDriverTransaction("driver-3"),
		}

		deps.driverTxnRepo.EXPECT().
			FindByIDs(ctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverIDs []string, opts ...repository.Option) ([]model.DriverTransaction, error) {
				require.ElementsMatch(tt, []string{"driver-1", "driver-2", "driver-3"}, driverIDs)
				return drivers, nil
			})

		deps.groupTxnRepo.EXPECT().
			Update(ctx, gt).
			Return(nil)

		expectUpdated := map[string]types.Money{
			"driver-1": 200.0,
			"driver-2": 300.0,
			"driver-3": 20.0,
		}
		deps.driverTxnRepo.EXPECT().
			Update(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, driver *model.DriverTransaction) error {
				amount, found := expectUpdated[driver.DriverID]
				require.True(tt, found)
				require.Equal(tt, amount, driver.WalletBalance)

				delete(expectUpdated, driver.DriverID)

				return nil
			}).
			Times(3)

		deps.txnRepo.EXPECT().
			CreateAll(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, txns []model.Transaction) error {
				require.Len(tt, txns, 3)
				require.Equal(tt, txns[0].Action, model.WalletTopUpTransactionAction)
				require.Equal(tt, txns[0].Channel, model.AdminTransactionChannel)
				require.Equal(tt, txns[0].Info.Category, model.WalletTransactionCategory)
				require.Equal(tt, txns[0].Info.Amount, types.NewMoney(200.0))
				require.Equal(tt, txns[0].Info.OrderID, "LMF-2485555")

				require.Equal(tt, txns[1].Action, model.WalletTopUpTransactionAction)
				require.Equal(tt, txns[1].Channel, model.AdminTransactionChannel)
				require.Equal(tt, txns[1].Info.Category, model.WalletTransactionCategory)
				require.Equal(tt, txns[1].Info.Amount, types.NewMoney(300.0))

				require.Equal(tt, txns[2].Action, model.WalletTopUpTransactionAction)
				require.Equal(tt, txns[2].Channel, model.AdminTransactionChannel)
				require.Equal(tt, txns[2].Info.Category, model.WalletTransactionCategory)
				require.Equal(tt, txns[2].Info.Amount, types.NewMoney(20.0))
				require.Equal(tt, txns[2].Info.OrderID, "LMF-31566145")

				return nil
			})

		api.Approve(gctx)

		require.Equal(tt, http.StatusOK, record.Code)

		res := UpdateGroupTransactionRes{}
		testutil.DecodeJSON(tt, record.Body, &res)

		require.Len(tt, res.TransactionIDs, 3)
	})

	t.Run("id not found should return 404", func(tt *testing.T) {
		id := "not-found"

		gctx, record := req(id)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, dep := newGroupTransactionAPI(ctrl)

		dep.groupTxnRepo.EXPECT().
			Get(ctx, id, gomock.Any()).
			Return(nil, repository.ErrNotFound)

		api.Reject(gctx)

		require.Equal(tt, http.StatusNotFound, record.Code)
	})

	t.Run("error during process save should return 500", func(tt *testing.T) {
		id := "id-1"

		gctx, record := req(id)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		item1 := model.NewCompensationGroupTransactionItem("driver-1", 200.0, "LMF-2485555", "")
		item1.SetRemark("remark A")
		item2 := model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "", "")
		item2.SetRemark("remark B")
		item3 := model.NewClaimGroupTransactionItem("driver-3", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark C")

		gt := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction)
		gt.AddItem(*item1)
		gt.AddItem(*item2)
		gt.AddItem(*item3)

		deps.groupTxnRepo.EXPECT().
			Get(ctx, id, gomock.Any()).
			Return(gt, nil)

		drivers := []model.DriverTransaction{
			*model.NewDriverTransaction("driver-1"),
			*model.NewDriverTransaction("driver-2"),
			*model.NewDriverTransaction("driver-3"),
		}

		deps.driverTxnRepo.EXPECT().
			FindByIDs(ctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverIDs []string, opts ...repository.Option) ([]model.DriverTransaction, error) {
				require.ElementsMatch(tt, []string{"driver-1", "driver-2", "driver-3"}, driverIDs)
				return drivers, nil
			})

		deps.groupTxnRepo.EXPECT().
			Update(ctx, gt).
			Return(errors.New("error"))

		api.Approve(gctx)

		require.Equal(tt, http.StatusInternalServerError, record.Code)
	})

	t.Run("not output transaction should not cause error", func(tt *testing.T) {
		id := "id-1"

		gctx, record := req(id)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		item1 := model.NewCompensationGroupTransactionItem("not-found", 200.0, "LMF-2485555", "")
		item1.SetRemark("remark A")
		item2 := model.NewIncentiveGroupTransactionItem("not-found", 300.0, "", "")
		item2.SetRemark("remark B")

		gt := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction)
		gt.AddItem(*item1)
		gt.AddItem(*item2)

		deps.groupTxnRepo.EXPECT().
			Get(ctx, id, gomock.Any()).
			Return(gt, nil)

		deps.driverTxnRepo.EXPECT().
			FindByIDs(ctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverIDs []string, opts ...repository.Option) ([]model.DriverTransaction, error) {
				return []model.DriverTransaction{}, nil
			})

		deps.groupTxnRepo.EXPECT().
			Update(ctx, gt).
			DoAndReturn(func(ctx context.Context, gt *model.GroupTransaction) error {
				require.Equal(tt, model.GroupTransactionStatusFail, gt.Status())
				return nil
			})

		api.Approve(gctx)

		require.Equal(tt, http.StatusOK, record.Code)
	})

	t.Run("success with effective time when admin approve before effective time should return 204", func(tt *testing.T) {
		id := "id-1"

		gctx, record := req(id)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		item1 := model.NewCompensationGroupTransactionItem("driver-1", 200.0, "LMF-2485555", "")
		item1.SetRemark("remark A")
		item2 := model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "", "")
		item2.SetRemark("remark B")
		item3 := model.NewClaimGroupTransactionItem("driver-3", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark B")

		gt := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction, timeutil.BangkokNow().Add(time.Hour*(1)))
		gt.AddItem(*item1)
		gt.AddItem(*item2)
		gt.AddItem(*item3)

		deps.groupTxnRepo.EXPECT().
			Get(ctx, id, gomock.Any()).
			Return(gt, nil)

		drivers := []model.DriverTransaction{
			*model.NewDriverTransaction("driver-1"),
			*model.NewDriverTransaction("driver-2"),
			*model.NewDriverTransaction("driver-3"),
		}

		deps.driverTxnRepo.EXPECT().
			FindByIDs(ctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverIDs []string, opts ...repository.Option) ([]model.DriverTransaction, error) {
				require.ElementsMatch(tt, []string{"driver-1", "driver-2", "driver-3"}, driverIDs)
				return drivers, nil
			})

		deps.groupTxnRepo.EXPECT().
			Update(ctx, gt).
			DoAndReturn(func(ctx context.Context, gt *model.GroupTransaction) error {
				require.Equal(tt, model.GroupTransactionStatusApproved, gt.Status())
				return nil
			})

		api.Approve(gctx)
		require.Equal(tt, http.StatusOK, record.Code)
	})

	t.Run("reject with effective time when admin approve after effective time is pass should return 400", func(tt *testing.T) {
		id := "id-1"

		gctx, record := req(id)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, deps := newGroupTransactionAPI(ctrl)

		item1 := model.NewCompensationGroupTransactionItem("driver-1", 200.0, "LMF-2485555", "")
		item1.SetRemark("remark A")
		item2 := model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "", "")
		item2.SetRemark("remark B")
		item3 := model.NewClaimGroupTransactionItem("driver-3", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark B")

		gt := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction, timeutil.BangkokNow().Add(time.Hour*(-1)))
		gt.AddItem(*item1)
		gt.AddItem(*item2)
		gt.AddItem(*item3)

		deps.groupTxnRepo.EXPECT().
			Get(ctx, id, gomock.Any()).
			Return(gt, nil)

		drivers := []model.DriverTransaction{
			*model.NewDriverTransaction("driver-1"),
			*model.NewDriverTransaction("driver-2"),
			*model.NewDriverTransaction("driver-3"),
		}

		deps.driverTxnRepo.EXPECT().
			FindByIDs(ctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverIDs []string, opts ...repository.Option) ([]model.DriverTransaction, error) {
				require.ElementsMatch(tt, []string{"driver-1", "driver-2", "driver-3"}, driverIDs)
				return drivers, nil
			})

		deps.groupTxnRepo.EXPECT().
			Update(ctx, gt).
			DoAndReturn(func(ctx context.Context, gt *model.GroupTransaction) error {
				require.Equal(tt, model.GroupTransactionStatusRejected, gt.Status())
				return nil
			})

		api.Approve(gctx)
		require.Equal(tt, http.StatusInternalServerError, record.Code)

	})

}

func TestGroupTransactionAPI_Reject(t *testing.T) {
	req := func(id string) (*gin.Context, *httptest.ResponseRecorder) {
		url := fmt.Sprintf("/v1/group-transactions/%s/status/rejected", id)
		ctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(RejectGroupTransactionReq{RequestedBy: "admin"}))
		ctx.Params = gin.Params{
			{Key: "id", Value: id},
		}
		return ctx, recorder
	}

	t.Run("success should return 204", func(tt *testing.T) {
		id := "id-1"

		gctx, record := req(id)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, dep := newGroupTransactionAPI(ctrl)

		item1 := model.NewCompensationGroupTransactionItem("driver-1", 200.0, "LMF-2485555", "")
		//item1.SetOrderID("LMF-31566145")
		item1.SetRemark("remark A")
		item2 := model.NewIncentiveGroupTransactionItem("driver-2", 300.0, "", "")
		//item2.SetOrderID("LMF-31566145")
		item2.SetRemark("remark B")
		item3 := model.NewClaimGroupTransactionItem("driver-2", 20.0, "LMF-31566145", "")
		item3.SetRemark("remark C")

		gt := model.NewGroupTransaction("id-1", "admin", "WALLET", model.WalletTopUpTransactionAction)
		gt.AddItem(*item1)
		gt.AddItem(*item2)
		gt.AddItem(*item3)

		dep.groupTxnRepo.EXPECT().
			Get(ctx, id, gomock.Any()).
			Return(gt, nil)

		dep.groupTxnRepo.EXPECT().
			Update(ctx, gomock.Any()).
			DoAndReturn(func(_ context.Context, updatedGt *model.GroupTransaction) error {
				require.Equal(tt, updatedGt.Status(), model.GroupTransactionStatusRejected)
				return nil
			})

		api.Reject(gctx)

		require.Equal(tt, http.StatusNoContent, record.Code)
	})

	t.Run("id not found should return 404", func(tt *testing.T) {
		id := "not-found"

		gctx, record := req(id)
		ctx := gctx.Request.Context()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		api, dep := newGroupTransactionAPI(ctrl)

		dep.groupTxnRepo.EXPECT().
			Get(ctx, id, gomock.Any()).
			Return(nil, repository.ErrNotFound)

		api.Reject(gctx)

		require.Equal(tt, http.StatusNotFound, record.Code)
	})

}

type groupTransactionAPIDep struct {
	groupTxnRepo  *mock_repository.MockGroupTransactionRepository
	driverTxnRepo *mock_repository.MockDriverTransactionRepository
	txnRepo       *mock_repository.MockTransactionRepository
	ordeRepo      *mock_repository.MockOrderRepository
	txnSchemeRepo *mock_repository.MockTransactionSchemeRepository
	cfg           config.PaymentConfig
}

func newGroupTransactionAPI(ctrl *gomock.Controller) (*GroupTransactionAPI, *groupTransactionAPIDep) {
	deps := &groupTransactionAPIDep{
		groupTxnRepo:  mock_repository.NewMockGroupTransactionRepository(ctrl),
		driverTxnRepo: mock_repository.NewMockDriverTransactionRepository(ctrl),
		txnRepo:       mock_repository.NewMockTransactionRepository(ctrl),
		ordeRepo:      mock_repository.NewMockOrderRepository(ctrl),
		txnSchemeRepo: mock_repository.NewMockTransactionSchemeRepository(ctrl),
		cfg:           config.PaymentConfig{},
	}

	return ProvideGroupTransactionAPI(deps.groupTxnRepo, deps.driverTxnRepo, deps.txnRepo, deps.ordeRepo, deps.txnSchemeRepo, deps.cfg), deps
}
