//go:build integration_test
// +build integration_test

package throttleddispatchdetail_test

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttleddispatchdetail"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/zone"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

type Admin struct {
	Email       string
	Permissions []string
}

func NewAdminTest() Admin {
	return Admin{
		Email: "<EMAIL>",
	}
}

func TestThrottledDispatchDetailAdminAPI_Create(t *testing.T) {
	t.<PERSON>llel()
	ctn := ittest.NewContainer(t)
	admin := NewAdminTest()
	if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_zones"); err != nil {
		t.<PERSON>("Unexpected error initfixture: %v", err)
	}

	t.<PERSON>("create works", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/throttled-dispatch-details")
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)

		gctx.Body().RawJSON(`
{
	"zoneId": "63fcd33481b11f2f49c9bb67",
	"intervalInSeconds": 50.5,
	"enabled": false,
	"h3PartitioningResolution": 7
}
		`).Build()
		gctx.Send(ctn.GinEngineRouter)

		var response zone.CreateZoneRes
		gctx.DecodeJSONResponse(&response)
		gctx.AssertResponseCode(t, http.StatusCreated)
	})

	t.Run("can't create if no such zone", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/throttled-dispatch-details")
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)

		gctx.Body().RawJSON(`
{
	"zoneId": "63fcd33481b11f2f49c9bb98",
	"intervalInSeconds": 50.5,
	"enabled": false
}
		`).Build()
		gctx.Send(ctn.GinEngineRouter)

		gctx.AssertResponseCode(t, http.StatusBadRequest)
	})

	t.Run("doesn't allow empty zone", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/throttled-dispatch-details")
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)

		gctx.Body().RawJSON(`
{
	"zoneId": "",
	"intervalInSeconds": 50,
	"enabled": false
}
		`).Build()
		gctx.Send(ctn.GinEngineRouter)

		var response zone.CreateZoneRes
		gctx.DecodeJSONResponse(&response)
		gctx.AssertResponseCode(t, http.StatusBadRequest)
	})
	t.Run("doesn't allow 0 interval", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/throttled-dispatch-details")
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)

		gctx.Body().RawJSON(`
{
	"zoneId": "63fcd33481b11f2f49c9bb67",
	"intervalInSeconds": 0,
	"enabled": true
}
		`).Build()
		gctx.Send(ctn.GinEngineRouter)

		var response zone.CreateZoneRes
		gctx.DecodeJSONResponse(&response)
		gctx.AssertResponseCode(t, http.StatusBadRequest)
	})
}

func TestThrottledDispatchDetailAdminAPI_Get(t *testing.T) {
	t.Parallel()
	ctn := ittest.NewContainer(t)
	admin := NewAdminTest()
	if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_zones"); err != nil {
		t.Errorf("Unexpected error initfixture: %v", err)
	}

	t.Run("return detail", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/admin/throttled-dispatch-details/63fcd35a81b11f2f49c9bb68")
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)

		gctx.Send(ctn.GinEngineRouter)

		type detailRes struct {
			ZoneID            string  `json:"zoneId"`
			ZoneCode          string  `json:"zoneCode"`
			IntervalInSeconds float64 `json:"intervalInSeconds"`
			Enabled           bool    `json:"enabled"`
		}
		res := detailRes{}

		gctx.DecodeJSONResponse(&res)
		gctx.AssertResponseCode(t, http.StatusOK)
		assert.Equal(t, detailRes{
			ZoneID:            "63fcd35a81b11f2f49c9bb68",
			ZoneCode:          "BKK-BANGPRAKAEW",
			IntervalInSeconds: 50.5,
			Enabled:           true,
		}, res)
	})

	t.Run("not found return error", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/admin/throttled-dispatch-details/notfound")
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)

		gctx.Send(ctn.GinEngineRouter)

		gctx.AssertResponseCode(t, http.StatusBadRequest)
	})
}

func TestThrottledDispatchDetailAdminAPI_List(t *testing.T) {
	t.Parallel()
	ctn := ittest.NewContainer(t)
	admin := NewAdminTest()
	if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_zones"); err != nil {
		t.Errorf("Unexpected error initfixture: %v", err)
	}

	t.Run("return all details", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/admin/throttled-dispatch-details")
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)

		gctx.Send(ctn.GinEngineRouter)

		type detailRes struct {
			ZoneID            string  `json:"zoneId"`
			ZoneCode          string  `json:"zoneCode"`
			IntervalInSeconds float64 `json:"intervalInSeconds"`
			Enabled           bool    `json:"enabled"`
		}

		var ListResponse struct {
			Data       []detailRes
			CountTotal int
		}

		gctx.DecodeJSONResponse(&ListResponse)
		gctx.AssertResponseCode(t, http.StatusOK)
		assert.GreaterOrEqual(t, len(ListResponse.Data), 2)
		assert.GreaterOrEqual(t, ListResponse.CountTotal, 2)
		assert.Contains(t, ListResponse.Data, detailRes{
			ZoneID:            "63fcd35a81b11f2f49c9bb68",
			ZoneCode:          "BKK-BANGPRAKAEW",
			IntervalInSeconds: 50.5,
			Enabled:           true,
		})
		assert.Contains(t, ListResponse.Data, detailRes{
			ZoneID:            "63fcd4bf81b11f2f49c9bb6a",
			ZoneCode:          "BKK-THAWIWATTHANA",
			IntervalInSeconds: 30,
			Enabled:           false,
		})
	})
}

func TestThrottledDispatchDetailAdminAPI_Update(t *testing.T) {
	t.Parallel()
	ctn := ittest.NewContainer(t)
	admin := NewAdminTest()
	if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_zones"); err != nil {
		t.Errorf("Unexpected error initfixture: %v", err)
	}

	zoneId := "63fcd35a81b11f2f49c9bb68"

	t.Run("should return 204 code and updated zone", func(t *testing.T) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("%s", fmt.Sprintf("/v1/admin/throttled-dispatch-details/%v", zoneId))
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)
		gctx.Body().RawJSON(`
{
	"intervalInSeconds": 30.5,
	"enabled": false,
	"h3PartitioningResolution": 7
}
			`).Build()
		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(t, http.StatusNoContent)

		gctx = testutil.NewContextWithRecorder()
		gctx.SetGET("%s", fmt.Sprintf("/v1/admin/throttled-dispatch-details/%v", zoneId))
		gctx.AdminAuthorized(admin.Email, admin.Permissions...)
		gctx.Send(ctn.GinEngineRouter)

		var checkResponse throttleddispatchdetail.ThrottledDispatchDetailRes
		gctx.DecodeJSONResponse(&checkResponse)
		gctx.AssertResponseCode(t, http.StatusOK)

		assert.Equal(t, 30.5, checkResponse.IntervalInSeconds)
		assert.Equal(t, false, checkResponse.Enabled)
	})
}
