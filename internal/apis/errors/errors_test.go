package errors

import (
	"testing"

	"github.com/stretchr/testify/require"
)

func TestErrDriverCreditNotEnough(t *testing.T) {
	t.<PERSON>llel()
	err := ErrDriverCreditNotEnough(20)
	require.Equal(t, "MINIMUM_CREDIT_NOT_ENOUGH", err.Code)
	require.Equal(t, 20.0, err.Info["minimumCreditRequired"].(float64))
}

func TestErrOnTopFareNotFound(t *testing.T) {
	t.Parallel()
	err := ErrOnTopFareNotFound()
	require.Equal(t, "ON_TOP_FARE_NOT_EXISTS", err.Code)
}

func TestErrDriverNotOnline(t *testing.T) {
	t.<PERSON>llel()
	err := ErrDriverNotOnline()
	require.Equal(t, "DRIVER_NOT_ONLINE", err.Code)
}

func TestErrUpdateExceedQuota(t *testing.T) {
	t.<PERSON>()
	err := ErrUpdateExceedQuota(7)
	require.Equal(t, "UPDATE_EXCEED_QUOTA", err.Code)
	require.Equal(t, "คุณสามารถขอเปลี่ยนแปลงข้อมูลนี้ได้เพียง 1 ครั้ง ในระยะเวลา 7 วัน ขออภัยในความไม่สะดวก", err.Info["detail"])
}

func TestErrTripNotFound(t *testing.T) {
	t.Parallel()
	err := ErrTripNotFound()
	require.Equal(t, "TRIP_NOT_FOUND", err.Code)
}
