package deliveryfee

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type DeliveryFeeAPI struct {
	DeliveryFeeRepo                   repository.DeliveryFeeSettingRepository
	RegionRepo                        repository.RegionRepository
	SettingDeliveryFeePriceSchemeRepo repository.SettingDeliveryFeePriceSchemesRepository
}

func ProvideDeliveryFeeAPI(
	deliverfeeRepo repository.DeliveryFeeSettingRepository,
	regionRepo repository.RegionRepository,
	settingDeliveryFeePriceSchemeRepo repository.SettingDeliveryFeePriceSchemesRepository) *DeliveryFeeAPI {
	return &DeliveryFeeAPI{
		DeliveryFeeRepo:                   deliverfeeRepo,
		RegionRepo:                        regionRepo,
		SettingDeliveryFeePriceSchemeRepo: settingDeliveryFeePriceSchemeRepo,
	}
}

// Create Delivery fee setting
func (df *DeliveryFeeAPI) Create(gCtx *gin.Context) {
	req, err := NewCreateDeliveryFeeSettingReq(gCtx, df.RegionRepo)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gCtx, t)
		default:
			apiutil.ErrInternalError(gCtx, apiErrors.ErrInternal(t))
		}
		return
	}
	ctx := gCtx.Request.Context()

	isExists := df.DeliveryFeeRepo.IsExistsByRegionAndService(ctx, req.Region, model.Service(req.ServiceType), repository.WithReadSecondaryPreferred)
	if isExists {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.New("region & service type already exists")))
		return
	}

	priceScheme, err := df.SettingDeliveryFeePriceSchemeRepo.FindById(ctx, req.PriceSchemeRefId)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, errors.New("delivery fee price scheme does not exists")))
			return
		}
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	setting := req.DeliveryFeeSetting(*priceScheme)

	if err = df.DeliveryFeeRepo.Create(ctx, &setting); err != nil {
		logrus.Errorf("create delivery fee error : %v", err)
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.Created(gCtx, NewDeliveryFeeSettingResponse(setting))
}

// Update Delivery fee setting
func (df *DeliveryFeeAPI) Update(gCtx *gin.Context) {
	req, err := NewUpdateDeliveryFeeSettingReq(gCtx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gCtx, t)
		default:
			apiutil.ErrInternalError(gCtx, apiErrors.ErrInternal(t))
		}
		return
	}
	id := gCtx.Param("id")
	ctx := gCtx.Request.Context()

	deliveryFee, err := df.DeliveryFeeRepo.Get(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Errorf("Get delivery fee error : %v", err)
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	_, err = GetDefaultSchemeKey(req.SchemeInfos)
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	newPriceSchemes := make([]model.DeliveryFeeSettingScheme, len(req.SchemeInfos))
	for index := range req.SchemeInfos {
		priceScheme, err := df.SettingDeliveryFeePriceSchemeRepo.FindById(ctx, req.SchemeInfos[index].PriceSchemeRefId)
		if err != nil {
			if err == repository.ErrNotFound {
				apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
				return
			}
			apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
			return
		}
		newPriceSchemes[index] = model.NewDeliveryFeeSettingScheme(req.SchemeInfos[index].Key.ToModel(), *priceScheme)
	}
	deliveryFee.SetSchemes(newPriceSchemes)

	if err = df.DeliveryFeeRepo.Update(ctx, &deliveryFee); err != nil {
		logrus.Errorf("Update delivery fee error : %v", err)
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	gCtx.JSON(http.StatusOK, gin.H{})
}

// GetDetail Delivery fee setting
func (df *DeliveryFeeAPI) GetDetail(ctx *gin.Context) {
	deliveryFee, err := df.DeliveryFeeRepo.Get(ctx, ctx.Param("id"), repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.WithFields(logrus.Fields{"error": err.Error()}).Error("Get delivery fee error")
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	ctx.JSON(http.StatusOK, NewDeliveryFeeSettingResponse(deliveryFee))
	return
}

type ListDeliveryFeeReq struct {
	Region    string `form:"region"`
	Sort      string `form:"sort"`
	Direction string `form:"direction"`
}

// List Delivery fee setting
func (df *DeliveryFeeAPI) List(ctx *gin.Context) {
	var req ListDeliveryFeeReq
	if err := ctx.Bind(&req); err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	skip, limit := utils.ParsePagination(ctx)
	q := bson.M{}

	if req.Region != "" {
		q = bson.M{
			"region": req.Region,
		}
	}

	var sort []string
	if req.Sort != "" {
		s := fmt.Sprintf("-%s", req.Sort)
		if req.Direction == "ASC" {
			s = req.Sort
		}
		sort = append(sort, s)
	}

	deliveryFees, err := df.DeliveryFeeRepo.ListAndSort(ctx, q, limit, skip, sort, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.WithFields(logrus.Fields{"error": err.Error()}).Error("Get List delivery fee error")
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	totalCount, err := df.DeliveryFeeRepo.Count(ctx, q, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.WithFields(logrus.Fields{"error": err.Error()}).Error("Count List delivery fee error")
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	var responses []DeliveryFeeSettingResponse
	for _, deliveryFee := range deliveryFees {
		responses = append(responses, NewDeliveryFeeSettingResponse(deliveryFee))
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":       responses,
		"totalCount": totalCount,
	})
	return
}

// Get scheme by key
func (df *DeliveryFeeAPI) GetScheme(ctx *gin.Context) {
	var schemeURIReq SchemeURIReq
	if err := ctx.ShouldBindUri(&schemeURIReq); err != nil {
		apiutil.ErrBadRequest(ctx, apiErrors.ErrInvalidRequest(err))
		return
	}

	deliveryFee, err := df.DeliveryFeeRepo.Get(ctx, schemeURIReq.ID, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Errorf("GetScheme - Get delivery fee error : %v", err)
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	scheme, err := deliveryFee.GetScheme(schemeURIReq.Key.ToModel())
	if err != nil {
		logrus.Errorf("GetScheme - Get new delivery fee scheme error : %v", err)
		apiutil.ErrNotFound(ctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
		return
	}

	ctx.JSON(http.StatusOK, newSchemeResponse(scheme))
}

// List schemes
func (df *DeliveryFeeAPI) ListScheme(ctx *gin.Context) {
	id := ctx.Param("id")
	deliveryFee, err := df.DeliveryFeeRepo.Get(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Errorf("ListScheme - Get delivery fee error : %v", err)
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	ctx.JSON(http.StatusOK, newSchemesResponse(deliveryFee.Schemes()))
}

// CreatePriceScheme Create Delivery fee price scheme
func (df *DeliveryFeeAPI) CreatePriceScheme(gCtx *gin.Context) {
	req, err := NewSettingDeliveryFeePriceSchemesReq(gCtx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gCtx, t)
		default:
			apiutil.ErrInternalError(gCtx, apiErrors.ErrInternal(t))
		}
		return
	}
	ctx := gCtx.Request.Context()

	priceScheme, err := req.CreateSettingDeliveryFeePriceScheme()
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if found := df.SettingDeliveryFeePriceSchemeRepo.IsExistsByName(ctx, priceScheme.Name(), priceScheme.Key(), repository.WithReadSecondaryPreferred); found {
		err := errors.New("price scheme name already exist")
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err = df.SettingDeliveryFeePriceSchemeRepo.Create(ctx, &priceScheme); err != nil {
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.Created(gCtx, NewSettingDeliveryFeePriceSchemesRes(priceScheme))
}

// UpdatePriceScheme update delivery fee price scheme
func (df *DeliveryFeeAPI) UpdatePriceScheme(gCtx *gin.Context) {
	req, err := NewSettingDeliveryFeePriceSchemesReq(gCtx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gCtx, t)
		default:
			apiutil.ErrInternalError(gCtx, apiErrors.ErrInternal(t))
		}
		return
	}
	id := gCtx.Param("setting_delivery_fee_price_schemes_id")
	ctx := gCtx.Request.Context()

	priceScheme, err := df.SettingDeliveryFeePriceSchemeRepo.FindById(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiErrors.ErrSettingDeliveryFeePriceSchemeNotFound())
		} else {
			apiutil.ErrInternalError(gCtx, apiErrors.ErrInternal(err))
		}
		return
	}

	newPriceScheme, err := req.UpdateSettingDeliveryFeePriceScheme(*priceScheme)
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if found := df.SettingDeliveryFeePriceSchemeRepo.IsExistsByName(ctx, newPriceScheme.Name(), priceScheme.Key(), repository.WithReadSecondaryPreferred); found {
		err := errors.New("price scheme name already exist")
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err = df.SettingDeliveryFeePriceSchemeRepo.Update(ctx, &newPriceScheme); err != nil {
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.NoContent(gCtx)
}

// GetPriceSchemes get list of setting delivery fee price scheme
func (df *DeliveryFeeAPI) GetPriceSchemes(gCtx *gin.Context) {
	var req GetPriceSchemesReq
	if err := gCtx.ShouldBindQuery(&req); err != nil {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}
	ctx := gCtx.Request.Context()
	skip, size := utils.OptionalParsePagination(gCtx)

	schemes, err := df.SettingDeliveryFeePriceSchemeRepo.FindSorted(ctx, req.ToQuery(), skip, size, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	count, err := df.SettingDeliveryFeePriceSchemeRepo.Count(ctx, req.ToQuery(), repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	var res []SettingDeliveryFeePriceSchemesRes
	for _, scheme := range schemes {
		res = append(res, NewSettingDeliveryFeePriceSchemesRes(scheme))
	}

	apiutil.OKList(gCtx, res, count)
}

// GetPriceScheme get setting delivery fee price scheme
func (df *DeliveryFeeAPI) GetPriceScheme(gCtx *gin.Context) {
	id := gCtx.Param("setting_delivery_fee_price_schemes_id")
	ctx := gCtx.Request.Context()

	schemes, err := df.SettingDeliveryFeePriceSchemeRepo.FindById(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiErrors.ErrSettingDeliveryFeePriceSchemeNotFound())
		} else {
			apiutil.ErrInternalError(gCtx, apiErrors.ErrInternal(err))
		}
		return
	}

	apiutil.OK(gCtx, NewSettingDeliveryFeePriceSchemesRes(*schemes))
}

// DeletePriceScheme delete setting delivery fee price scheme
func (df *DeliveryFeeAPI) DeletePriceScheme(gCtx *gin.Context) {
	id := gCtx.Param("setting_delivery_fee_price_schemes_id")
	ctx := gCtx.Request.Context()

	settings, err := df.DeliveryFeeRepo.FindByPriceSchemeRefId(ctx, id, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiErrors.ErrInternal(err))
		return
	}
	if len(settings) > 0 {
		settingIds := make([]string, len(settings))
		for i := range settings {
			settingIds[i] = settings[i].Region().String()
		}
		err := fmt.Errorf("price scheme using by %v", settingIds)
		apiutil.ErrBadRequest(gCtx, apiErrors.ErrInvalidRequest(err))
		return
	}

	if err := df.SettingDeliveryFeePriceSchemeRepo.Delete(ctx, id); err != nil {
		apiutil.ErrInternalError(gCtx, apiErrors.ErrInternal(err))
		return
	}

	apiutil.NoContent(gCtx)
}

// AddPricePolygon add price polygon to specific delivery fee setting
func (df *DeliveryFeeAPI) AddPricePolygon(gCtx *gin.Context) {
	var req PricePolygonReq
	if err := gCtx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}
	ctx := gCtx.Request.Context()
	settingDeliveryFeeId := gCtx.Param("id")

	setting, err := df.DeliveryFeeRepo.Get(ctx, settingDeliveryFeeId, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
			return
		}
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	pricePolygon, err := df.createPricePolygon(req, ctx)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
			return
		}
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	setting.AddPricePolygon(pricePolygon)

	if err := df.DeliveryFeeRepo.Update(ctx, &setting); err != nil {
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	apiutil.Created(gCtx, NewDeliveryFeeSettingResponse(setting))
}

func (df *DeliveryFeeAPI) UpdatePricePolygon(gCtx *gin.Context) {
	var req PricePolygonReq
	if err := gCtx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}
	ctx := gCtx.Request.Context()
	settingDeliveryFeeId := gCtx.Param("id")
	pricePolygonId := gCtx.Param("price_polygon_id")

	setting, err := df.DeliveryFeeRepo.Get(ctx, settingDeliveryFeeId, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
			return
		}
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	originalPricePolygon, err := setting.GetPricePolygonById(pricePolygonId)
	if err != nil {
		apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
		return
	}

	pricePolygon, err := df.updatePricePolygon(req, ctx, originalPricePolygon)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
			return
		}
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	setting.UpdatePricePolygon(pricePolygon)

	if err := df.DeliveryFeeRepo.Update(ctx, &setting); err != nil {
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	apiutil.NoContent(gCtx)
}

// GetPricePolygon  get price polygon from specific delivery fee setting
func (df *DeliveryFeeAPI) GetPricePolygon(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	settingDeliveryFeeId := gCtx.Param("id")
	pricePolygonId := gCtx.Param("price_polygon_id")

	setting, err := df.DeliveryFeeRepo.Get(ctx, settingDeliveryFeeId, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
			return
		}
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	pricePolygon, err := setting.GetPricePolygonById(pricePolygonId)
	if err != nil {
		apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
		return
	}
	apiutil.OK(gCtx, NewPricePolygonResponse(pricePolygon))
}

func (df *DeliveryFeeAPI) DeletePricePolygon(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	settingDeliveryFeeId := gCtx.Param("id")
	pricePolygonId := gCtx.Param("price_polygon_id")

	setting, err := df.DeliveryFeeRepo.Get(ctx, settingDeliveryFeeId, repository.WithReadSecondaryPreferred)
	if err != nil {
		if err == repository.ErrNotFound {
			apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
			return
		}
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	pricePolygon, err := setting.GetPricePolygonById(pricePolygonId)
	if err != nil {
		apiutil.ErrNotFound(gCtx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
		return
	}

	setting.DeletePricePolygon(pricePolygon)

	if err := df.DeliveryFeeRepo.Update(ctx, &setting); err != nil {
		apiutil.ErrInternalError(gCtx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	apiutil.NoContent(gCtx)
}

func WithZoneOption(title, desc string, color model.ZoneColor) model.PricePolygonOptionFunc {
	return func(c *model.ZonePayloadOption) {
		c.Title = title
		c.Description = desc
		c.ZoneColor = color
	}
}

func (df *DeliveryFeeAPI) createPricePolygon(req PricePolygonReq, ctx context.Context) (model.PricePolygon, error) {
	var schemes []model.DeliveryFeeSettingScheme
	for index := range req.SchemeInfos {
		priceScheme, err := df.SettingDeliveryFeePriceSchemeRepo.FindById(ctx, req.SchemeInfos[index].PriceSchemeRefId)
		if err != nil {
			return model.PricePolygon{}, err
		}
		scheme := model.NewDeliveryFeeSettingScheme(
			req.SchemeInfos[index].Key.ToModel(),
			*priceScheme,
		)
		schemes = append(schemes, scheme)
	}
	return model.NewPricePolygon(req.Name, req.Coordinates, schemes,
		WithZoneOption(req.Title, req.Description, req.ZoneColor)), nil
}

func (df *DeliveryFeeAPI) updatePricePolygon(req PricePolygonReq, ctx context.Context, originalPricePolygon model.PricePolygon) (model.PricePolygon, error) {
	var schemes []model.DeliveryFeeSettingScheme
	for index := range req.SchemeInfos {
		priceScheme, err := df.SettingDeliveryFeePriceSchemeRepo.FindById(ctx, req.SchemeInfos[index].PriceSchemeRefId)
		if err != nil {
			return model.PricePolygon{}, err
		}
		scheme := model.NewDeliveryFeeSettingScheme(
			req.SchemeInfos[index].Key.ToModel(),
			*priceScheme,
		)
		schemes = append(schemes, scheme)
	}
	originalPricePolygon.SetName(req.Name)
	originalPricePolygon.SetSchemes(schemes)
	originalPricePolygon.SetCoordinates(model.GeometryToModel(req.Coordinates))
	originalPricePolygon.SetTitle(req.Title)
	originalPricePolygon.SetDesc(req.Description)
	originalPricePolygon.SetZoneColor(req.ZoneColor)
	originalPricePolygon.SetUpdatedAt(timeutil.BangkokNow())
	return originalPricePolygon, nil
}
