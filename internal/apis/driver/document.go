package driver

import (
	"context"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

// for solve import cyclic problem
type DriverDocumentConfig struct {
	MaxTawi50                int           `envconfig:"MAX_TAWI50" default:"1"`
	DriverDocumentExpiration time.Duration `envconfig:"DRIVER_DOCUMENT_EXPIRATION" default:"15m"`
	VOSEndpoint              string        `envconfig:"VOS_ENDPOINT" required:"true"`
	VOSPublicCdnEndpoint     string        `envconfig:"VOS_PUBLIC_CDN_ENDPOINT" default:""`
}

type Document struct {
	whtRepo    repository.WithholdingTaxCertificateRepository
	docRepo    repository.DriverDocumentRepository
	cfg        DriverDocumentConfig
	vosService service.VOSService
}

func ProvideDriverDocumentConfig() (cfg DriverDocumentConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func (d *Document) Get(ctx *gin.Context) {
	driverID := DriverIDFromGinContext(ctx)
	fileType := ctx.Query("type")
	date := ctx.Query("date")

	q := persistence.DriverDocumentQuery{
		FileType: fileType,
		DriverID: driverID,
		Date:     timeutil.ParseDateStringToDate(date),
	}
	docsOpts := d.getDocumentResOption(ctx)

	// We can remove `if` check file type if WithholdingTaxCertificate use collection driver document
	var docs []model.DriverDocument
	if fileType == "" || fileType != string(model.DriverDocumentWHTCType) {
		dcs, err := d.docRepo.GetByQuery(ctx, q.ToQuery(), repository.WithReadSecondaryPreferred)
		if err != nil {
			apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
			return
		}
		docs = dcs
	}

	// remove this block if wht use driver document collection
	var wht []model.WithholdingTaxCertificate
	if fileType == "" || fileType == string(model.DriverDocumentWHTCType) {
		w, err := d.whtRepo.FindByDriverIdOrderByYearDesc(ctx, driverID, 0, d.cfg.MaxTawi50, repository.WithReadSecondaryPreferred)
		if err != nil {
			apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
			return
		}
		wht = w
	}

	res := NewDocumentRes(docs, wht, docsOpts...)
	r := d.signDocumentFileURL(ctx, driverID, *res)

	// Get one document format
	if fileType != "" && len(res.Documents) == 1 {
		apiutil.OK(ctx, r.Documents[0])
		return
	}

	apiutil.OK(ctx, r)
}

func (d *Document) signDocumentFileURL(ctx context.Context, driverID string, res DocumentRes) DocumentRes {

	for i, v := range res.Documents {
		for j, r := range v.Files {
			res.Documents[i].Files[j].URL = d.signURL(ctx, driverID, r.FileID)
		}
	}

	return res
}

func (d *Document) signURL(ctx context.Context, driverID, fileID string) string {
	fileLocation, err := d.vosService.GetCDNPreSignedUrl(ctx, fileID, d.cfg.DriverDocumentExpiration)
	if err == nil {
		if d.cfg.VOSPublicCdnEndpoint != "" {
			fileLocation = strings.Replace(fileLocation, d.cfg.VOSEndpoint, d.cfg.VOSPublicCdnEndpoint, 1)
		}
	} else {
		logrus.Warnf("singDocsFileURL driver id : %v, error: %v", driverID, err)
	}

	return fileLocation
}

func (d *Document) getDocumentResOption(ctx *gin.Context) []DocumentResOption {

	exclude := ctx.Query("exclude")
	fileType := ctx.Query("type")

	var ops []DocumentResOption

	if exclude == "files" {
		ops = append(ops, DocumentResExcludeFilesOption())
	}

	if fileType != "" {
		ops = append(ops, DocumentResFilterByTypeOption(fileType))
	}

	return ops

}

func ProvideDocument(whtRepo repository.WithholdingTaxCertificateRepository, docRepo repository.DriverDocumentRepository, vosService service.VOSService, cfg DriverDocumentConfig) *Document {
	return &Document{
		whtRepo:    whtRepo,
		docRepo:    docRepo,
		vosService: vosService,
		cfg:        cfg,
	}
}
