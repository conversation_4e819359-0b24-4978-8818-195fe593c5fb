package driver

import (
	"bytes"
	"errors"
	"io"
	"mime/multipart"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestBulkUpdateDriverCriminalStatusReq_BulkUpdateDriverCriminalStatus(t *testing.T) {
	assertDataEqual := func(tt *testing.T, expectedDriverStatus map[string]interface{}, actual *BulkUpdateDS) {
		statusToID := make(map[interface{}]map[string]struct{})
		for driverID, status := range expectedDriverStatus {
			if statusToID[status] == nil {
				statusToID[status] = make(map[string]struct{})
			}
			statusToID[status][driverID] = struct{}{}
		}
		expected := &BulkUpdateDS{
			statusToID,
			expectedDriverStatus,
		}

		require.NotNil(tt, actual)
		require.Equal(tt, expected, actual)
	}

	t.Run("swap column still work", func(tt *testing.T) {
		content := `Driver ID,Name,Family Name,Criminal Result,National ID
test-1,tester1,line man,PASS,123456789012
test-2,tester2,line man,NOT_PASS,123456789012
test-3,tester3,line man,IN_REVIEW,123456789012
`
		expected := map[string]interface{}{
			"test-1": model.CriminalStatusPass,
			"test-2": model.CriminalStatusNotPass,
			"test-3": model.CriminalStatusInReview,
		}

		req := BulkUpdateDriverCriminalStatusReq{
			File: newFile(content),
		}

		actual, err := req.ParseRequest()

		require.NoError(tt, err)
		assertDataEqual(tt, expected, actual)
	})

	t.Run("duplicate column is error", func(tt *testing.T) {
		content := `Driver ID,Name,Family Name,Criminal Result,National ID,Criminal Result
test-1,tester1,line man,PASS,123456789012,NOT_PASS
test-2,tester2,line man,NOT_PASS,123456789012,PASS
test-3,tester3,line man,IN_REVIEW,123456789012,IN_REVIEW
`
		req := BulkUpdateDriverCriminalStatusReq{
			File: newFile(content),
		}

		_, err := req.ParseRequest()

		require.Error(tt, err)
	})

	t.Run("missing column return 400", func(tt *testing.T) {
		content := `Driver ID,Name,Family Name,Criminal Result
test-1,tester1,line man,PASS
test-2,tester2,line man,NOT_PASS
test-3,tester3,line man,IN_REVIEW
`
		req := BulkUpdateDriverCriminalStatusReq{
			File: newFile(content),
		}

		_, err := req.ParseRequest()

		require.Error(tt, err)
	})

	t.Run("additional column still work if required column is still remain", func(tt *testing.T) {
		content := `Driver ID,Name,Family Name,National ID,Driver Status,Criminal Result
test-1,tester1,line man,123456789012,ONLINE,PASS
test-2,tester2,line man,123456789012,ONLINE,NOT_PASS
test-3,tester3,line man,123456789012,ONLINE,IN_REVIEW
`
		expected := map[string]interface{}{
			"test-1": model.CriminalStatusPass,
			"test-2": model.CriminalStatusNotPass,
			"test-3": model.CriminalStatusInReview,
		}

		req := BulkUpdateDriverCriminalStatusReq{
			File: newFile(content),
		}

		actual, err := req.ParseRequest()

		require.NoError(tt, err)
		assertDataEqual(tt, expected, actual)
	})

	t.Run("should work on duplicate driverID", func(tt *testing.T) {
		testcases := []struct {
			content  string
			expected map[string]interface{}
		}{
			{
				content: `Driver ID,Name,Family Name,Criminal Result,National ID
test-1,tester1,line man,PASS,123456789012
test-1,tester1,line man,PASS,123456789012
test-2,tester2,line man,NOT_PASS,123456789012
`,
				expected: map[string]interface{}{
					"test-1": model.CriminalStatusPass,
					"test-2": model.CriminalStatusNotPass,
				}},
			{
				content: `Driver ID,Name,Family Name,Criminal Result,National ID
test-1,tester1,line man,PASS,123456789012
test-1,tester1,line man,PASS,123456789012
test-2,tester2,line man,NOT_PASS,123456789012
test-2,tester2,line man,NOT_PASS,123456789012
`,
				expected: map[string]interface{}{
					"test-1": model.CriminalStatusPass,
					"test-2": model.CriminalStatusNotPass,
				}},
			{
				content: `Driver ID,Name,Family Name,Criminal Result,National ID
test-1,tester1,line man,PASS,123456789012
test-1,tester1,line man,PASS,123456789012
test-2,tester2,line man,PASS,123456789012
test-2,tester2,line man,PASS,123456789012
`,
				expected: map[string]interface{}{
					"test-1": model.CriminalStatusPass,
					"test-2": model.CriminalStatusPass,
				}},
		}
		for _, tc := range testcases {
			req := BulkUpdateDriverCriminalStatusReq{
				File: newFile(tc.content),
			}

			actual, err := req.ParseRequest()

			require.NoError(tt, err)
			assertDataEqual(tt, tc.expected, actual)
		}
	})
}

func newFile(content string) *multipart.FileHeader {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)

	fw, _ := w.CreateFormFile("file", "file.txt")
	io.Copy(fw, strings.NewReader(content))
	w.Close()

	r := multipart.NewReader(&b, w.Boundary())
	form, _ := r.ReadForm(20000)
	file := form.File["file"][0]

	return file
}

func TestBulkUpdateDriverRegionReq_BulkUpdateDriverRegion(t *testing.T) {

	t.Run("Should parse update info correct", func(tt *testing.T) {
		content := `Driver ID, Region
driver-1, bkk
driver-2, non`
		req := BulkUpdateDriverRegionReq{
			File: newFile(content),
		}

		info, err := req.BulkUpdateDriverRegionInfo()

		require.NoError(tt, err)
		require.Equal(tt, info[0].DriverID, "driver-1")
		require.Equal(tt, info[0].Region, "bkk")
		require.Equal(tt, info[1].DriverID, "driver-2")
		require.Equal(tt, info[1].Region, "non")
	})

	t.Run("Invalid CSV format should be error", func(tt *testing.T) {
		content := `Driver ID, Region
driver-1, bkk, invalid field
driver-2, non`
		req := BulkUpdateDriverRegionReq{
			File: newFile(content),
		}

		_, err := req.BulkUpdateDriverRegionInfo()

		require.EqualError(tt, err, "cannot read csv file")
	})

}

func TestUpdateDriverFinancialRiskControlData_GetDSCREffectiveDate(t *testing.T) {
	type TestCase struct {
		name          string
		currentTime   time.Time
		data          UpdateDriverFinancialRiskControlData
		expectedData  types.Period
		expectedError error
	}

	now := time.Date(2024, 03, 16, 2, 0, 0, 0, timeutil.BangkokLocation())

	testCases := []TestCase{
		{
			name:        "should success when DSCR effective date is empty",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "",
				DSCREffectiveDateEndAt:   "",
			},
			expectedData:  types.Period{},
			expectedError: nil,
		},
		{
			name:        "should error when start at is empty",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "",
				DSCREffectiveDateEndAt:   "2024-03-16",
			},
			expectedData:  types.Period{},
			expectedError: errors.New("DSCR Effective Date Value should be both valid format or empty"),
		},
		{
			name:        "should error when end at is empty",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-16",
				DSCREffectiveDateEndAt:   "",
			},
			expectedData:  types.Period{},
			expectedError: errors.New("DSCR Effective Date Value should be both valid format or empty"),
		},
		{
			name:        "should error when start at is invalid format",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "20240316",
				DSCREffectiveDateEndAt:   "2024-03-16",
			},
			expectedData:  types.Period{},
			expectedError: errors.New("DSCR Effective Date Start At Value should be in 2006-01-02 format"),
		},
		{
			name:        "should error when end at is invalid format",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-16",
				DSCREffectiveDateEndAt:   "20240316",
			},
			expectedData:  types.Period{},
			expectedError: errors.New("DSCR Effective Date End At Value should be in 2006-01-02 format"),
		},
		{
			name:        "should error when start at is after end at",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-16",
				DSCREffectiveDateEndAt:   "2024-03-15",
			},
			expectedData:  types.Period{},
			expectedError: errors.New("DSCR Effective Date Start At Value should be before end at"),
		},
		{
			name:        "should error when start at is before today and end at is before today",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-15",
				DSCREffectiveDateEndAt:   "2024-03-15",
			},
			expectedData:  types.Period{},
			expectedError: errors.New("DSCR Effective Date Start At Value should be today or after"),
		},
		{
			name:        "should error when start at is before today and end at is after today",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-15",
				DSCREffectiveDateEndAt:   "2024-03-17",
			},
			expectedData:  types.Period{},
			expectedError: errors.New("DSCR Effective Date Start At Value should be today or after"),
		},
		{
			name:        "should success when both are today",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-16",
				DSCREffectiveDateEndAt:   "2024-03-16",
			},
			expectedData: types.Period{
				StartAt: time.Date(2024, 03, 16, 0, 0, 0, 0, timeutil.BangkokLocation()),
				EndAt:   time.Date(2024, 03, 16, 23, 59, 59, 1e9-1, timeutil.BangkokLocation()),
			},
			expectedError: nil,
		},
		{
			name:        "should success when start at is today",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-16",
				DSCREffectiveDateEndAt:   "2024-03-17",
			},
			expectedData: types.Period{
				StartAt: time.Date(2024, 03, 16, 0, 0, 0, 0, timeutil.BangkokLocation()),
				EndAt:   time.Date(2024, 03, 17, 23, 59, 59, 1e9-1, timeutil.BangkokLocation()),
			},
			expectedError: nil,
		},
		{
			name:        "should success when both are after today",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-17",
				DSCREffectiveDateEndAt:   "2024-03-17",
			},
			expectedData: types.Period{
				StartAt: time.Date(2024, 03, 17, 0, 0, 0, 0, timeutil.BangkokLocation()),
				EndAt:   time.Date(2024, 03, 17, 23, 59, 59, 1e9-1, timeutil.BangkokLocation()),
			},
			expectedError: nil,
		},
		{
			name:        "should error when start at is after today",
			currentTime: now,
			data: UpdateDriverFinancialRiskControlData{
				DSCREffectiveDateStartAt: "2024-03-17",
				DSCREffectiveDateEndAt:   "2024-03-18",
			},
			expectedData: types.Period{
				StartAt: time.Date(2024, 03, 17, 0, 0, 0, 0, timeutil.BangkokLocation()),
				EndAt:   time.Date(2024, 03, 18, 23, 59, 59, 1e9-1, timeutil.BangkokLocation()),
			},
			expectedError: nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actualData, actualError := tc.data.GetDSCREffectiveDate(tc.currentTime)
			require.Equal(t, tc.expectedData, actualData)
			require.Equal(t, tc.expectedError, actualError)
		})
	}
}

func TestUpdateDriverFinancialRiskControlData_GetRemark(t *testing.T) {
	type TestCase struct {
		name                  string
		currentTime           time.Time
		requestor             string
		withDSCREffectiveDate bool
		data                  UpdateDriverFinancialRiskControlData
		expected              model.DriverRemark
	}

	now := time.Date(2024, 03, 16, 2, 0, 0, 0, timeutil.BangkokLocation())
	requestor := "<EMAIL>"

	testCases := []TestCase{
		{
			name:                  "with dscr effective date",
			currentTime:           now,
			requestor:             requestor,
			withDSCREffectiveDate: true,
			data: UpdateDriverFinancialRiskControlData{
				DSCR:                     100.5,
				DSCREffectiveDateStartAt: "2024-03-17",
				DSCREffectiveDateEndAt:   "2024-03-17",
				MaxTenor:                 5000,
				MaxExposure:              180.0,
			},
			expected: model.DriverRemark{
				Remark:    "Update driver DSCR to 100.5, DSCR Effective Date to (2024-03-17, 2024-03-17), Max tenor to 5000, Max exposure to 180",
				CreatedBy: requestor,
				CreatedAt: now,
			},
		},
		{
			name:                  "without dscr effective date",
			currentTime:           now,
			requestor:             requestor,
			withDSCREffectiveDate: false,
			data: UpdateDriverFinancialRiskControlData{
				DSCR:                     40.5,
				DSCREffectiveDateStartAt: "",
				DSCREffectiveDateEndAt:   "",
				MaxTenor:                 7500,
				MaxExposure:              100,
			},
			expected: model.DriverRemark{
				Remark:    "Update driver DSCR to 40.5, Max tenor to 7500, Max exposure to 100",
				CreatedBy: requestor,
				CreatedAt: now,
			},
		}}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			actual := tc.data.GetRemark(tc.currentTime, tc.requestor, tc.withDSCREffectiveDate)
			require.Equal(t, tc.expected, actual)
		})
	}

}
