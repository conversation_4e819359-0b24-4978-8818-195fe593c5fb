package driver

import (
	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type AnswerRequest struct {
	Data    []AnswerReqPayload `json:"data"`
	lineUID string
}

type AnswerReqPayload struct {
	QuestionID int `json:"questionId"`
	AnswerID   int `json:"answerId"`
}

func (a *AnswerRequest) LineUID() crypt.LazyEncryptedString {
	return crypt.NewLazyEncryptedString(a.lineUID)
}

func (a *AnswerRequest) GetAnswerRequest() []model.UserAnswer {
	var userAnswers []model.UserAnswer
	for _, v := range a.Data {
		userAnswers = append(userAnswers, model.UserAnswer{
			QuestionID: v.QuestionID,
			AnswerID:   v.AnswerID,
		})
	}

	return userAnswers
}

func NewAnswerReq(gctx *gin.Context, lineUid string) (*AnswerRequest, error) {
	req := &AnswerRequest{}
	req.lineUID = lineUid
	if err := gctx.ShouldBindJSON(req); err != nil {
		return nil, err
	}
	return req, nil
}
