package driver

import (
	"fmt"
)

// ErrDriverNotExists happens when driver doesn't exists in our system.
type ErrDriverNotExists struct{}

func (e *ErrDriverNotExists) Error() string {
	return "driverId is not exists"
}

// ErrDriverIsBanned happens when driver got status BANNED.
type ErrDriverIsBanned struct {
	driverID string
}

func (e *ErrDriverIsBanned) DriverID() string {
	return e.driverID
}

func (e *ErrDriverIsBanned) Error() string {
	return fmt.Sprintf("driverId %s is banned", e.driverID)
}
