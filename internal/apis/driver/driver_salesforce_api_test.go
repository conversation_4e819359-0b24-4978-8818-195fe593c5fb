package driver

import (
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/icrowley/fake"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestDriverSalesforceAPI_GetDriver(t *testing.T) {
	makeReq := func(id string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/drivers/%s", id), nil)
		gctx.Params = gin.Params{
			gin.Param{Key: "driver_id", Value: id},
		}
		return gctx, recorder
	}

	generateBanHistory := func(driverID string) model.BanHistory {
		now := time.Now().UTC()
		next := now.AddDate(0, 0, 7)
		return model.BanHistory{
			DriverID:        driverID,
			Type:            fake.CharactersN(10),
			Action:          fake.CharactersN(10),
			Value:           fake.CharactersN(10),
			Reason:          fake.CharactersN(10),
			Category:        fake.CharactersN(10),
			MessageToDriver: fake.CharactersN(10),
			CreatedBy:       fake.CharactersN(10),
			CreatedAt:       now,
			BannedUntil:     next,
		}
	}

	dummyAttendance := service.AttendanceRate{}

	t.Run("success should return 200 with converted tier MEMBER", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverSalesforceAPI(tt)
		defer finish()

		id := "driver-1"
		profile := generateDriver(id)

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(profile, nil)

		deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverRegistration{}, nil)

		deps.banHistorySvc.EXPECT().
			FindHistory(ctx, id, gomock.Any()).
			Return([]model.BanHistory{
				generateBanHistory(id),
				generateBanHistory(id),
			}, nil)

		deps.attendance.EXPECT().
			GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(dummyAttendance, nil)

		deps.riderLevel.EXPECT().
			Get(ctx, id).
			Return(prediction.RiderLevelDefault, nil)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual struct {
			Driver struct {
				ID              string   `json:"driverId"`
				RegistrationIDs []string `json:"registrationIds"`
				Goodness        Goodness `json:"goodness"`
				DriverTier      string   `json:"driverTier"`
			} `json:"driver"`
			Histories []interface{} `json:"histories"`
		}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, id, actual.Driver.ID)
		require.Len(tt, actual.Driver.RegistrationIDs, 2)
		require.Equal(tt, actual.Driver.RegistrationIDs[0], profile.RegistrationIDs[0].Hex())
		require.Len(tt, actual.Histories, 2)
		require.Equal(tt, actual.Driver.Goodness.LevelDefault, prediction.RiderLevelDefault.String())
		require.Equal(tt, "BRONZE", actual.Driver.DriverTier)
	})

	t.Run("success should return 200 with converted tier PRO+", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverSalesforceAPI(tt)
		defer finish()

		id := "driver-1"
		profile := generateDriver(id)
		profile.DriverTier = model.DriverTierProPlus

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(profile, nil)

		deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverRegistration{}, nil)

		deps.banHistorySvc.EXPECT().
			FindHistory(ctx, id, gomock.Any()).
			Return([]model.BanHistory{
				generateBanHistory(id),
				generateBanHistory(id),
			}, nil)

		deps.attendance.EXPECT().
			GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(dummyAttendance, nil)

		deps.riderLevel.EXPECT().
			Get(ctx, id).
			Return(prediction.RiderLevelDefault, nil)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual struct {
			Driver struct {
				ID              string   `json:"driverId"`
				RegistrationIDs []string `json:"registrationIds"`
				Goodness        Goodness `json:"goodness"`
				DriverTier      string   `json:"driverTier"`
			} `json:"driver"`
			Histories []interface{} `json:"histories"`
		}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, id, actual.Driver.ID)
		require.Len(tt, actual.Driver.RegistrationIDs, 2)
		require.Equal(tt, actual.Driver.RegistrationIDs[0], profile.RegistrationIDs[0].Hex())
		require.Len(tt, actual.Histories, 2)
		require.Equal(tt, actual.Driver.Goodness.LevelDefault, prediction.RiderLevelDefault.String())
		require.Equal(tt, "LEGEND", actual.Driver.DriverTier)
	})

	t.Run("success should return 200 with invalid tier", func(tt *testing.T) {
		driverAPI, deps, finish := newDriverSalesforceAPI(tt)
		defer finish()

		id := "driver-1"
		profile := generateDriver(id)
		profile.DriverTier = model.DriverTier("INVALID_TIER")

		gctx, recorder := makeReq(id)
		ctx := gctx.Request.Context()

		deps.driverRepo.EXPECT().
			FindDriverID(ctx, id, gomock.Any()).
			Return(profile, nil)

		deps.driverRegisRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverRegistration{}, nil)

		deps.banHistorySvc.EXPECT().
			FindHistory(ctx, id, gomock.Any()).
			Return([]model.BanHistory{
				generateBanHistory(id),
				generateBanHistory(id),
			}, nil)

		deps.attendance.EXPECT().
			GetAttendance(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(dummyAttendance, nil)

		deps.riderLevel.EXPECT().
			Get(ctx, id).
			Return(prediction.RiderLevelDefault, nil)

		driverAPI.GetDriver(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual struct {
			Driver struct {
				ID              string   `json:"driverId"`
				RegistrationIDs []string `json:"registrationIds"`
				Goodness        Goodness `json:"goodness"`
				DriverTier      string   `json:"driverTier"`
			} `json:"driver"`
			Histories []interface{} `json:"histories"`
		}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, id, actual.Driver.ID)
		require.Len(tt, actual.Driver.RegistrationIDs, 2)
		require.Equal(tt, actual.Driver.RegistrationIDs[0], profile.RegistrationIDs[0].Hex())
		require.Len(tt, actual.Histories, 2)
		require.Equal(tt, actual.Driver.Goodness.LevelDefault, prediction.RiderLevelDefault.String())
		require.Equal(tt, "", actual.Driver.DriverTier)
	})
}

func newDriverSalesforceAPI(r gomock.TestReporter) (*DriverSalesforceAPI, driverAPIDeps, func()) {
	ctrl := gomock.NewController(r)

	driverAdminAPI, deps, finish := newDriverAdminAPI(r)

	driverSalesforceAPI := ProvideDriverSalesforceAPI(driverAdminAPI)

	return driverSalesforceAPI, deps, func() {
		finish()
		ctrl.Finish()
	}
}
