package driver

import (
	"context"
	"encoding/base64"
	"errors"
	"strings"
	"sync"
	"time"

	"github.com/r3labs/diff/v3"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/topkek/topkek"
	absintheUtils "git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type AdminDriverAddressResponse struct {
	HouseNumber string `json:"houseNumber,omitempty"`
	Moo         string `json:"moo,omitempty"`
	Subdistrict string `json:"subdistrict,omitempty"`
	District    string `json:"district,omitempty"`
	Province    string `json:"province,omitempty"`
	Zipcode     string `json:"zipcode,omitempty"`
}

type AdminDriverLicenseInfoResponse struct {
	ID             string     `json:"id,omitempty"`
	ExpirationDate *time.Time `json:"expirationDate,omitempty"`
	PhotoURL       string     `json:"photoUrl,omitempty"`
}

type AdminDriverVehicleInfoResponse struct {
	RegistrationDate       *time.Time `json:"registrationDate,omitempty"`
	RegistrationPhotoURL   string     `json:"registrationPhotoUrl,omitempty"`
	PlateNumber            string     `json:"plateNumber,omitempty"`
	PhotoURL               string     `json:"photoUrl,omitempty"`
	LegislationPhotoURL    string     `json:"legislationPhotoUrl,omitempty"`
	LendingVehiclePhotoURL string     `json:"lendingVehiclePhotoUrl,omitempty"`
	LegislationExpiredDate *time.Time `json:"legislationExpiredDate,omitempty"`
}

type AdminDriverOnTopQuotaResponse struct {
	model.OnTopQuota
	Title       string                `json:"onTopTitle"`
	Description string                `json:"onTopDescription"`
	Name        string                `json:"onTopName" binding:"required"`
	Label       string                `json:"onTopLabel,omitempty"`
	Status      model.OnTopFareStatus `json:"onTopStatus"`
	EndDate     time.Time             `json:"onTopEndDate,omitempty"`
	Amount      types.Money           `json:"amount"`
	ServiceType []model.Service       `json:"onTopServiceType"`
}

type AdminDriverBankingInfoResponse struct {
	// Account is an book bank account number.
	Account string `json:"account,omitempty"`

	// BankName is a bank name in book bank.
	BankName string `json:"bankName,omitempty"`

	// AccountHolder is owner's name of account in book bank.
	AccountHolder string `json:"accountHolder,omitempty"`

	// RefID is citi bank reference id for system to indicate that transaction belong to which driver
	RefID string `json:"refId,omitempty"`

	// CitiRefID is citi bank reference id for system to indicate that transaction belong to which driver
	CitiRefID string `json:"citiRefID,omitempty"`

	// UOBRefID is uob bank reference id for system to indicate that transaction belong to which driver
	UOBRefID string `json:"uobRefId,omitempty"`

	// PhotoURL is a first page of book bank.
	PhotoURL string `json:"photoUrl,omitempty"`

	// BranchCode is a bank's branch code.
	BranchCode string `json:"branchCode,omitempty"`
}

type AdminDriverResponse struct {
	Title                 string           `json:"title"`
	Firstname             string           `json:"firstname"`
	Lastname              string           `json:"lastname"`
	Phone                 string           `json:"phone"`
	EmergencyPhone        string           `json:"emergencyPhone"`
	StrongEmergencyPhone  string           `json:"strongEmergencyPhone"`
	AvatarURL             string           `json:"avatarUrl"`
	CitizenID             string           `json:"citizenId,omitempty"`
	CitizenIDCardPhotoURL string           `json:"citizenIdCardPhotoUrl,omitempty"`
	CitizenIDExpiredDate  *time.Time       `json:"citizenIdExpiredDate,omitempty"`
	Birthday              *time.Time       `json:"birthday,omitempty"`
	CanExceedAgeLimit     bool             `json:"canExceedAgeLimit"`
	DriverType            string           `json:"driverType"`
	DriverRole            model.DriverRole `json:"driverRole"`
	DriverTier            model.DriverTier `json:"driverTier"`

	LineUID string `json:"lineUid,omitempty"`

	Address       AdminDriverAddressResponse     `json:"address"`
	DriverLicense AdminDriverLicenseInfoResponse `json:"driverLicense,omitempty"`
	Vehicle       AdminDriverVehicleInfoResponse `json:"vehicle,omitempty"`
	Banking       AdminDriverBankingInfoResponse `json:"banking,omitempty"`

	Trained             bool   `json:"trained"`
	InterestingProvince string `json:"interestingProvince"`

	AcceptedConsentVersion int64 `json:"acceptedConsentVersion"`

	// ProfileStatus is information status to inform if this driver was filled info completed yet.
	ProfileStatus model.ProfileStatus `json:"profileStatus"`

	ProfileStatusReason string `json:"profileStatusReason"`

	CriminalCheckStatus                model.CriminalStatus `json:"criminalCheckStatus,omitempty"`
	StrongEncryptedCriminalCheckStatus model.CriminalStatus `json:"strongEncryptedCriminalCheckStatus,omitempty"`

	DriverID string `json:"driverId"`

	Reason      string     `bson:"reason" json:"reason"`
	BannedUntil *time.Time `json:"bannedUntil"`

	Remarks []*model.DriverRemark `json:"remarks"`

	Region model.RegionCode   `json:"region,omitempty"`
	Status model.DriverStatus `json:"status"`

	BanLater bool `json:"banLater"`

	CurrentOrder   string   `json:"currentOrder"`
	CurrentTrip    string   `json:"currentTrip"`
	QueueingTrips  []string `json:"queueingTrips"`
	QueueingOrders []string `json:"queueingOrders"`

	RatingScore float64 `json:"ratingScore"`

	Options model.Options `json:"options"`

	ProfileExpirationDate *time.Time `json:"profileExpirationDate,omitempty"`

	IsProfileExpired bool `json:"isProfileExpired"`

	BankRefID string `json:"refId,omitempty"`

	AssignedReviewer string `json:"assignedReviewer,omitempty"`

	CreatedAt            time.Time                 `json:"createdAt"`
	UpdatedAt            time.Time                 `json:"updatedAt"`
	IsTransferred        bool                      `json:"isTransferred"`
	RequestProfileUpdate []RequestUpdateProfileRes `json:"requestUpdateProfile"`

	Shifts []string `json:"shifts"`
	AR     ArRes    `json:"ar"`

	RegistrationIDs []string `json:"registrationIds"`

	ServiceTypes             []model.Service `json:"serviceTypes"`
	ServiceTypesSilentBanned []model.Service `json:"serviceTypesSilentBanned"`
	DedicatedZones           []string        `json:"dedicatedZones"`

	DSCR              float64       `json:"dscr"`
	DSCREffectiveDate *types.Period `json:"dscrEffectiveDate,omitempty"`
	MaxTenor          int           `json:"maxTenor"`
	MaxExposure       float64       `json:"maxExposure"`

	ReferrerCode string `json:"referrerCode"`

	NegativeBalanceGroup string `json:"negativeBalanceGroup"`

	IsDeprioritized bool `json:"isDeprioritized"`

	Goodness Goodness `json:"goodness"`

	IsSupplyPositioning bool `json:"isSupplyPositioning"`

	DriverVendor string `json:"driverVendor"`

	OnTopQuotas []AdminDriverOnTopQuotaResponse `json:"onTopQuotas,omitempty"`
}

type Goodness struct {
	LevelDefault   string `json:"defaultLevel"`
	Level          string `json:"level"`
	LevelExpiredAt string `json:"levelExpiredAt"`
}

type ArRes struct {
	Daily   float64 `json:"daily"`
	Weekly  float64 `json:"weekly"`
	Monthly float64 `json:"monthly"`
}

type RequestUpdateProfileRes struct {
	CreatedAt             time.Time                      `json:"createdAt"`
	UpdatedAt             time.Time                      `json:"updatedAt"`
	Status                model.ProfileStatus            `json:"status"`
	Remark                string                         `json:"remark"`
	AvatarURL             string                         `json:"avatarUrl"`
	DriverLicense         AdminDriverLicenseInfoResponse `json:"driverLicense,omitempty"`
	VehicleURL            string                         ` json:"vehicleUrl,omitempty"`
	Vehicle               AdminDriverVehicleInfoResponse `json:"vehicle,omitempty"`
	EmergencyPhone        string                         `json:"emergencyPhone"`
	CitizenIdExpiredDate  *time.Time                     `json:"citizenIdExpiredDate,omitempty"`
	CitizenIdCardPhotoUrl string                         `json:"citizenIdCardPhotoUrl,omitempty"`
}

func NewRequestUpdateProfileRes(requestProfileUpdates []model.RequestUpdateProfile) []RequestUpdateProfileRes {
	var res []RequestUpdateProfileRes
	for _, requestProfileUpdate := range requestProfileUpdates {
		rpu := RequestUpdateProfileRes{
			CreatedAt: requestProfileUpdate.CreatedAt.In(timeutil.BangkokLocation()),
			UpdatedAt: requestProfileUpdate.UpdatedAt.In(timeutil.BangkokLocation()),
			Status:    requestProfileUpdate.Status,
			Remark:    requestProfileUpdate.Remark,
			AvatarURL: requestProfileUpdate.AvatarURL,
			DriverLicense: AdminDriverLicenseInfoResponse{
				ID:             requestProfileUpdate.DriverLicense.ID.String(),
				ExpirationDate: timeutil.NilIfZero(requestProfileUpdate.DriverLicense.ExpirationDate),
				PhotoURL:       requestProfileUpdate.DriverLicense.PhotoURL,
			},
			Vehicle: AdminDriverVehicleInfoResponse{
				RegistrationDate:       timeutil.NilIfZero(requestProfileUpdate.Vehicle.RegistrationDate),
				RegistrationPhotoURL:   requestProfileUpdate.Vehicle.RegistrationPhotoURL,
				PhotoURL:               requestProfileUpdate.Vehicle.PhotoURL,
				PlateNumber:            requestProfileUpdate.Vehicle.PlateNumber.String(),
				LegislationPhotoURL:    requestProfileUpdate.Vehicle.LegislationPhotoURL,
				LendingVehiclePhotoURL: requestProfileUpdate.Vehicle.LendingVehiclePhotoURL,
				LegislationExpiredDate: requestProfileUpdate.Vehicle.LegislationExpiredDate,
			},
			EmergencyPhone:        requestProfileUpdate.EmergencyPhone.String(),
			CitizenIdExpiredDate:  requestProfileUpdate.CitizenIdExpiredDate,
			CitizenIdCardPhotoUrl: requestProfileUpdate.CitizenIdCardPhotoURL,
		}
		res = append(res, rpu)
	}
	return res
}

func NewAdminDriverResponse(ctx context.Context, d *model.Driver, defaultRiderLevel prediction.RiderLevel) AdminDriverResponse {
	registrationIDs := make([]string, len(d.RegistrationIDs))
	for i, objectID := range d.RegistrationIDs {
		registrationIDs[i] = objectID.Hex()
	}

	now := timeutil.GetTimeFromContext(ctx)
	goodnessLevel, goodnessExpiredAt := d.Goodness.DisplayInAdminProfile(now)

	res := AdminDriverResponse{
		Title:                 d.Title.String(),
		Firstname:             d.Firstname.String(),
		Lastname:              d.Lastname.String(),
		Phone:                 d.Phone.String(),
		EmergencyPhone:        d.EmergencyPhone.String(),
		StrongEmergencyPhone:  d.StrongEmergencyPhone.String(),
		AvatarURL:             d.AvatarURL,
		CitizenID:             d.CitizenID.String(),
		CitizenIDCardPhotoURL: d.CitizenIDCardPhotoURL,
		CitizenIDExpiredDate:  d.CitizenIDExpiredDate,
		Birthday:              timeutil.NilIfZero(d.Birthday),
		CanExceedAgeLimit:     d.CanExceedAgeLimit,
		DriverType:            d.DriverType.String(),
		DriverTier:            d.GetDriverTier(),
		DriverRole:            d.DriverRole,
		LineUID:               d.LineUID.String(), // this is admin API, it may safe to expose UID internally. Do not expose to public/user.
		Address: AdminDriverAddressResponse{
			HouseNumber: d.Address.HouseNumber.String(),
			Moo:         d.Address.Moo.String(),
			Subdistrict: d.Address.Subdistrict.String(),
			District:    d.Address.District.String(),
			Province:    d.Address.Province.String(),
			Zipcode:     d.Address.Zipcode.String(),
		},
		DriverLicense: AdminDriverLicenseInfoResponse{
			ID:             d.DriverLicense.ID.String(),
			ExpirationDate: timeutil.NilIfZero(d.DriverLicense.ExpirationDate),
			PhotoURL:       d.DriverLicense.PhotoURL,
		},
		Vehicle: AdminDriverVehicleInfoResponse{
			RegistrationDate:       timeutil.NilIfZero(d.Vehicle.RegistrationDate),
			RegistrationPhotoURL:   d.Vehicle.RegistrationPhotoURL,
			PlateNumber:            d.Vehicle.PlateNumber.String(),
			PhotoURL:               d.Vehicle.PhotoURL,
			LegislationPhotoURL:    d.Vehicle.LegislationPhotoURL,
			LendingVehiclePhotoURL: d.Vehicle.LendingVehiclePhotoURL,
			LegislationExpiredDate: d.Vehicle.LegislationExpiredDate,
		},
		Banking: AdminDriverBankingInfoResponse{
			Account:       d.Banking.Account.String(),
			BankName:      d.Banking.BankName.String(),
			AccountHolder: d.Banking.AccountHolder.String(),
			RefID:         d.DriverCitiRefID(),
			CitiRefID:     d.Banking.CitiRefID,
			UOBRefID:      d.Banking.UOBRefID,
			PhotoURL:      d.Banking.PhotoURL,
			BranchCode:    d.Banking.BranchCode.String(),
		},
		Trained:                            d.Trained,
		InterestingProvince:                d.InterestingProvince,
		AcceptedConsentVersion:             d.AcceptedConsentVersion,
		ProfileStatus:                      d.ProfileStatus,
		ProfileStatusReason:                d.ProfileStatusReason,
		CriminalCheckStatus:                d.GetCriminalCheckStatus(),
		StrongEncryptedCriminalCheckStatus: model.CriminalStatus(d.StrongEncryptedCriminalCheckStatus.String()),
		DriverID:                           d.DriverID,
		Reason:                             d.Reason,
		BannedUntil:                        timeutil.NilIfZero(d.BannedUntil),
		Remarks:                            d.Remarks,
		Region:                             d.Region,
		Status:                             d.Status,
		BanLater:                           d.BanLater || d.TemporaryBanHistory != nil,
		CurrentOrder:                       d.CurrentOrder,
		CurrentTrip:                        d.CurrentTrip,
		QueueingOrders:                     d.QueueingOrders,
		QueueingTrips:                      d.QueueingTrips,
		RatingScore:                        d.SMARatingScore,
		Options:                            d.Options,
		ProfileExpirationDate:              timeutil.NilIfZero(d.ProfileExpirationDate),
		IsProfileExpired:                   d.IsProfileExpired,
		BankRefID:                          d.DriverCitiRefID(),
		AssignedReviewer:                   d.AssignedReviewer,
		CreatedAt:                          d.CreatedAt,
		UpdatedAt:                          d.UpdatedAt,
		IsTransferred:                      d.IsTransferred,
		RequestProfileUpdate:               NewRequestUpdateProfileRes(d.RequestUpdateProfile),
		Shifts:                             d.Shifts,
		AR:                                 ArRes{},
		RegistrationIDs:                    registrationIDs,
		ServiceTypes:                       d.ServiceTypes,
		ServiceTypesSilentBanned:           d.ServiceTypesSilentBanned,
		DedicatedZones:                     d.DedicatedZones,
		DSCR:                               d.DSCR,
		MaxTenor:                           d.MaxTenor,
		MaxExposure:                        d.MaxExposure,
		ReferrerCode:                       d.ReferrerCode,
		NegativeBalanceGroup:               d.NegativeBalanceGroup,
		IsDeprioritized:                    d.IsDeprioritized,
		Goodness: Goodness{
			LevelDefault:   defaultRiderLevel.String(),
			Level:          goodnessLevel,
			LevelExpiredAt: goodnessExpiredAt,
		},
		IsSupplyPositioning: d.IsSupplyPositioning,
		DriverVendor:        d.DriverVendorID,
	}

	if !d.DSCREffectiveDate.IsZero() {
		res.DSCREffectiveDate = &d.DSCREffectiveDate
	}

	res.ServiceTypes = d.GetEligibleServices()

	return res
}

type UpdateSuccess struct {
	DriverID string `json:"driverId"`
}

type UpdateFail struct {
	DriverID string `json:"driverId"`
	Reason   string `json:"reason"`
}

type BulkUpdateResp struct {
	l         sync.Mutex
	Successes []UpdateSuccess `json:"successes"`
	Failures  []UpdateFail    `json:"failures"`
}

func NewBulkUpdateResp() *BulkUpdateResp {
	return &BulkUpdateResp{
		Successes: []UpdateSuccess{},
		Failures:  []UpdateFail{},
	}
}

func (resp *BulkUpdateResp) AddSuccess(driverId string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Successes = append(resp.Successes, UpdateSuccess{DriverID: driverId})
}

func (resp *BulkUpdateResp) AddSuccesses(driverIDs []string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	for _, id := range driverIDs {
		resp.Successes = append(resp.Successes, UpdateSuccess{DriverID: id})
	}
}

func (resp *BulkUpdateResp) AddFail(driverId, reason string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Failures = append(resp.Failures, UpdateFail{DriverID: driverId, Reason: reason})
}

func (resp *BulkUpdateResp) AddFails(driverIDs []string, reason string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	for _, id := range driverIDs {
		resp.Failures = append(resp.Failures, UpdateFail{DriverID: id, Reason: reason})
	}
}

type DeactivateDriverSuccess struct {
	DriverID string `json:"driverId"`
}

type DeactivateDriverFailure struct {
	DriverID string `json:"driverId"`
	Reason   string `json:"reason"`
}

type BulkDeactivateDriverResp struct {
	l         sync.Mutex
	Successes []DeactivateDriverSuccess `json:"successes"`
	Failures  []DeactivateDriverFailure `json:"failures"`
}

func NewBulkDeactivateDriverResp() *BulkDeactivateDriverResp {
	return &BulkDeactivateDriverResp{
		Successes: []DeactivateDriverSuccess{},
		Failures:  []DeactivateDriverFailure{},
	}
}

func (resp *BulkDeactivateDriverResp) AddSuccess(driverId string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Successes = append(resp.Successes, DeactivateDriverSuccess{DriverID: driverId})
}

func (resp *BulkDeactivateDriverResp) AddFail(driverId, reason string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Failures = append(resp.Failures, DeactivateDriverFailure{DriverID: driverId, Reason: reason})
}

type UpdateDriverRegionSuccess struct {
	DriverID string `json:"driverId"`
}

type UpdateDriverRegionFailure struct {
	DriverID string `json:"driverId"`
	Reason   string `json:"reason"`
}

type BulkUpdateDriverRegionResp struct {
	l         sync.Mutex
	Successes []UpdateDriverRegionSuccess `json:"successes"`
	Failures  []UpdateDriverRegionFailure `json:"failures"`
}

func NewBulkUpdateDriverRegionResp() *BulkUpdateDriverRegionResp {
	return &BulkUpdateDriverRegionResp{
		Successes: []UpdateDriverRegionSuccess{},
		Failures:  []UpdateDriverRegionFailure{},
	}
}

func (resp *BulkUpdateDriverRegionResp) AddSuccess(driverId string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Successes = append(resp.Successes, UpdateDriverRegionSuccess{DriverID: driverId})
}

func (resp *BulkUpdateDriverRegionResp) AddFail(driverId, reason string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Failures = append(resp.Failures, UpdateDriverRegionFailure{DriverID: driverId, Reason: reason})
}

type AuditLogDiffRes struct {
	Actor     string           `json:"actor"`
	Changes   []AuditLogChange `json:"changes"`
	CreatedAt time.Time        `json:"createdAt"`
	UpdatedAt time.Time        `json:"updatedAt"`
}

type AuditLogChange struct {
	Name string      `json:"name"`
	From interface{} `json:"from"`
	To   interface{} `json:"to"`
}

func NewAuditLogDiffRes(aud []model.AuditLog) []AuditLogDiffRes {
	var res []AuditLogDiffRes
	var changes []AuditLogChange
	for _, v := range aud {
		d, err := diff.Diff(v.Before, v.After)
		if err != nil {
			logrus.Errorf("NewAuditLogDiffRes diff audit err: %v", err)
			continue
		}

		changes = getHistoryChange(d)
		if len(changes) > 0 {
			res = append(res, AuditLogDiffRes{
				Actor:     v.Actor.ID,
				Changes:   changes,
				CreatedAt: v.Timestamp,
				UpdatedAt: v.Timestamp,
			})
		}
	}

	return res
}

func getHistoryChange(cl diff.Changelog) []AuditLogChange {
	var res []AuditLogChange

	for _, c := range cl {
		// ignore field updated_at on BaseDriver or other
		if absintheUtils.StrContains("updated_at", c.Path) || len(c.Path) == 0 {
			continue
		}

		s := transformFieldSnakeToCaMel(c.Path)

		decryptedChangeFrom, decryptedChangeTo, err := decryptChangeLog(c.From, c.To)
		if err == nil {
			c.From = decryptedChangeFrom
			c.To = decryptedChangeTo
		}

		res = append(res, AuditLogChange{
			Name: strings.Join(s, " > "),
			From: c.From,
			To:   c.To,
		})
	}
	return res
}

func transformFieldSnakeToCaMel(field []string) []string {
	var res []string

	if len(field) == 0 {
		return res
	}

	// lazy encrypt field
	if field[len(field)-1] == "plain" {
		field = field[:len(field)-1]
	}

	// baseDriver is embed field
	if field[0] == "BaseDriver" {
		field = field[1:]
	}

	for _, s := range field {
		res = append(res, stringutil.SnakeCaseToCamelCase(s))
	}

	return res
}

func decryptChangeLog(encryptedChangeFrom interface{}, encryptedChangeTo interface{}) (string, string, error) {
	errUnsupportedType := errors.New("only string supported")
	topkekDecrypt := func(src interface{}) (string, error) {
		if str, ok := src.(string); ok {
			if src == "" {
				return "", nil
			}

			bCipherText, err := base64.StdEncoding.DecodeString(str)
			if err != nil {
				return str, err
			}

			ctx := context.Background()
			deter, err := topkek.NewDeterministicEncryptedFromCiphertext(ctx, bCipherText, []byte(""))
			if err != nil {
				return str, err
			}

			resBytes, err := deter.Bytes(ctx)
			if err != nil {
				return str, err
			}
			return string(resBytes), nil
		}
		return "", errUnsupportedType
	}

	decryptChangeFrom, decryptChangeFromErr := topkekDecrypt(encryptedChangeFrom)
	if errors.Is(decryptChangeFromErr, errUnsupportedType) {
		// ignore error from decryption, return encrypted string
		return "", "", decryptChangeFromErr
	}

	decryptChangeTo, decryptChangeToErr := topkekDecrypt(encryptedChangeTo)
	if errors.Is(decryptChangeToErr, errUnsupportedType) {
		// ignore error from decryption, return encrypted string
		return "", "", decryptChangeToErr
	}

	return decryptChangeFrom, decryptChangeTo, nil
}

type BulkUpdateDriverFinancialRiskControlRes struct {
	l         sync.Mutex
	Successes []DriverSuccessTask `json:"successes"`
	Failures  []DriverFailedTask  `json:"failures"`
}

type DriverSuccessTask struct {
	DriverID string `json:"driverId"`
}

type DriverFailedTask struct {
	DriverID string `json:"driverId"`
	Reason   string `json:"reason"`
}

func NewBulkUpdateDriverFinancialRiskControlRes() *BulkUpdateDriverFinancialRiskControlRes {
	return &BulkUpdateDriverFinancialRiskControlRes{
		Successes: []DriverSuccessTask{},
		Failures:  []DriverFailedTask{},
	}
}

func (resp *BulkUpdateDriverFinancialRiskControlRes) AddSuccess(driverId string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Successes = append(resp.Successes, DriverSuccessTask{DriverID: driverId})
}

func (resp *BulkUpdateDriverFinancialRiskControlRes) AddFail(driverId, reason string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Failures = append(resp.Failures, DriverFailedTask{DriverID: driverId, Reason: reason})
}
