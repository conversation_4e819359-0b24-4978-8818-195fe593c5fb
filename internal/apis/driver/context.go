package driver

import (
	"context"

	"github.com/gin-gonic/gin"
)

const key = "driverID"

// DriverIDFromGinContext get driver id from gin.Context.
func DriverIDFromGinContext(ctx *gin.Context) (driverID string) {
	return ctx.GetString(key)
}

func DriverIDFromContext(ctx context.Context) (driverID string) {
	value, ok := ctx.Value(key).(string)
	if !ok {
		return ""
	}

	return value
}

func IsDriverLoggedIn(ctx *gin.Context) bool {
	return ctx.Request.Header.Get("X-DRIVER-ID") != ""
}

func DeviceIDFromGinContext(ctx *gin.Context) string {
	return ctx.Request.Header.Get("X-DEVICE-ID")
}

// SetDriverIDToContext set driver id into gin.Context.
func SetDriverIDToContext(ctx *gin.Context, driverID string) {
	ctx.Set(key, driverID)
	reqCtx := ctx.Request.Context()
	reqCtx = context.WithValue(reqCtx, key, driverID)
	ctx.Request = ctx.Request.WithContext(reqCtx)
}
