package hook

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"github.com/pkg/errors"
	"google.golang.org/genproto/googleapis/type/decimal"
	"google.golang.org/protobuf/types/known/timestamppb"

	"git.wndv.co/go/logx/v2"
	commonv1 "git.wndv.co/go/proto/lineman/egs/common/v1"
	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	imsPb "git.wndv.co/go/proto/lineman/inventory/v1"
	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type DeliveryMethod string

const (
	DeliveryMethodDPD         DeliveryMethod = "DPD"
	DeliveryMethodBranch      DeliveryMethod = "BRANCH"
	DeliveryMethodUnspecified DeliveryMethod = "UNSPECIFIED"
)

func (h HookEventReq) getRewardSku() string {
	paymentTenorInterface, _ := h.Value["paymentTenor"].(map[string]interface{})
	rewardSku, _ := paymentTenorInterface["rewardSku"].(string)
	return rewardSku
}

func (h HookEventReq) getBundleSkus() []string {
	productBundleSkusInterface, _ := h.Value["productBundleSkus"].([]interface{})
	productBundleSkus := make([]string, len(productBundleSkusInterface))
	for index, productBundleSku := range productBundleSkusInterface {
		productBundleSkus[index] = fmt.Sprint(productBundleSku)
	}

	return productBundleSkus
}

func (h HookEventReq) GetEGSCreateOrderRequest(cpRes *imsPb.CheckoutProductResponse) (*egsv1.CreateOrderRequest, error) {
	driverId, _ := h.Value["driverId"].(string)
	productName, _ := h.Value["productName"].(string)
	productSku, _ := h.Value["productSku"].(string)
	batchId, _ := h.Value["batchId"].(string)
	productImageUrls, ok := h.Value["productImageUrls"].([]interface{})

	productImageUrl := ""
	if ok && len(productImageUrls) > 0 {
		if imageUrl, ok := productImageUrls[0].(string); ok {
			productImageUrl = imageUrl
		}
	}

	deliverBy, _ := h.Value["deliverBy"].(string)
	batchGroupType, _ := h.Value["batchGroupType"].(string)

	netPrice, _, err := h.getNetPriceAndTenor()
	if err != nil {
		logx.Error().Err(err).Msg("getEGSCreateOrderRequest get net price and tenor error")
		return &egsv1.CreateOrderRequest{}, err
	}

	paymentOption, err := h.getOrderPaymentOption()
	if err != nil {
		logx.Error().Err(err).Msg("getEGSCreateOrderRequest get order payment option error")
		return &egsv1.CreateOrderRequest{}, err
	}

	recipient := h.getRecipient()

	shippingDetail, err := h.getOrderShippingDetail(cpRes)
	if err != nil {
		logx.Error().Err(err).Msg("getEGSCreateOrderRequest get order shipping detail error")
		return &egsv1.CreateOrderRequest{}, err
	}

	orderProductDetails := h.createOrderProductDetailsGRPC(cpRes)

	newReq := &egsv1.CreateOrderRequest{
		DriverId:         driverId,
		FormId:           h.FormID,
		ProductName:      productName,
		ProductSku:       productSku,
		BatchLabel:       batchId,
		ProductImageUrl:  productImageUrl,
		DeliverBy:        deliverBy,
		Price:            types.Money(netPrice).ToGRPCDecimalPtr(),
		PaymentOption:    &paymentOption,
		Recipient:        &recipient,
		ShippingDetail:   &shippingDetail,
		ProductDetails:   orderProductDetails,
		ProductBatchType: egsv1.BatchGroupType(egsv1.BatchGroupType_value[batchGroupType]),
	}

	return newReq, nil

}

func (h HookEventReq) getNetPriceAndTenor() (float64, int, error) {
	paymentTenor, _ := h.Value["paymentTenor"].(map[string]interface{})

	netPrice, _ := paymentTenor["netPrice"].(map[string]interface{})
	netPriceValueString, _ := netPrice["value"].(string)
	netPriceAmountValue, err := strconv.ParseFloat(netPriceValueString, 64)
	if err != nil {
		logx.Error().Err(err).Msg("getNetPriceAndTenor parse net price error")
		return 0.0, 0, err
	}

	tenor, ok := paymentTenor["tenor"].(float64)
	if !ok {
		logx.Error().Err(err).Msg("getNetPriceAndTenor parse tenor error")
		return 0.0, 0, err
	}
	return netPriceAmountValue, int(tenor), nil
}

func (h HookEventReq) getPaymentTenor() (model.PaymentTenor, error) {
	paymentTenor, _ := h.Value["paymentTenor"].(map[string]interface{})

	netPrice, _ := paymentTenor["netPrice"].(map[string]interface{})
	netPriceValueString, _ := netPrice["value"].(string)
	netPriceAmountValue, err := strconv.ParseFloat(netPriceValueString, 64)
	if err != nil {
		logx.Error().Err(err).Msg("getPaymentTenor parse net price error")
		return model.PaymentTenor{}, err
	}

	tenor, ok := paymentTenor["tenor"].(float64)
	if !ok {
		err := fmt.Errorf("cannot get tenor")
		logx.Error().Msgf("getPaymentTenor %v", err)
		return model.PaymentTenor{}, err
	}

	dailyAmount, _ := paymentTenor["dailyAmount"].(map[string]interface{})
	dailyAmountValueString, _ := dailyAmount["value"].(string)
	dailyAmountValue, err := strconv.ParseFloat(dailyAmountValueString, 64)
	if err != nil {
		logx.Error().Err(err).Msg("getPaymentTenor parse daily amount error")
		return model.PaymentTenor{}, err
	}

	rewardLabel, _ := paymentTenor["rewardLabel"].(string)
	rewardSku, _ := paymentTenor["rewardSku"].(string)

	paymentTenorModel := model.PaymentTenor{
		NetPrice:     netPriceAmountValue,
		Tenor:        int(tenor),
		DailyAmount:  dailyAmountValue,
		RewardSku:    rewardSku,
		RewardLabel:  rewardLabel,
		RewardAmount: 0,
	}

	rewardAmount, _ := paymentTenor["rewardAmount"].(map[string]interface{})
	rewardAmountValueString, _ := rewardAmount["value"].(string)
	if rewardAmountValueString != "" {
		rewardAmountValue, err := strconv.ParseFloat(rewardAmountValueString, 64)
		if err != nil {
			logx.Error().Err(err).Msg("getPaymentTenor parse reward amount error")
			return model.PaymentTenor{}, err
		}

		paymentTenorModel.RewardAmount = rewardAmountValue
	}

	return paymentTenorModel, nil
}

func (h HookEventReq) calculateDailyAmount() types.Money {
	paymentTenor, _ := h.Value["paymentTenor"].(map[string]interface{})

	netPrice, _ := paymentTenor["dailyAmount"].(map[string]interface{})
	netPriceValueString, _ := netPrice["value"].(string)
	dailyAmountMoney := types.NewMoney(0.0)
	if netPriceValueString != "" {
		dailyAmountValue, err := strconv.ParseFloat(netPriceValueString, 64)
		if err == nil {
			dailyAmountMoney = types.NewMoney(dailyAmountValue)
		}
	}
	return dailyAmountMoney
}

func (h HookEventReq) getOrderPaymentOption() (egsv1.OrderPaymentOption, error) {
	paymentTenorInterface, _ := h.Value["paymentTenor"].(map[string]interface{})

	paymentType, _ := paymentTenorInterface["type"].(string)
	tenor, ok := paymentTenorInterface["tenor"].(float64)
	if !ok {
		err := fmt.Errorf("unble to get payment tenor")
		logx.Error().Err(err).Msgf("getOrderPaymentOption parse tenor %v error", tenor)
		return egsv1.OrderPaymentOption{}, err
	}
	dailyAmount, _ := paymentTenorInterface["dailyAmount"].(map[string]interface{})
	dailyAmountValueString, _ := dailyAmount["value"].(string)
	dailyAmountValue, err := strconv.ParseFloat(dailyAmountValueString, 64)
	if err != nil {
		logx.Error().Err(err).Msgf("getOrderPaymentOption parse daily amount %v error", dailyAmountValueString)
		return egsv1.OrderPaymentOption{}, err
	}

	rewardSku, _ := paymentTenorInterface["rewardSku"].(string)
	rewardLabel, _ := paymentTenorInterface["rewardLabel"].(string)

	rewardAmount, _ := paymentTenorInterface["rewardAmount"].(map[string]interface{})
	rewardAmountString, _ := rewardAmount["value"].(string)
	rewardAmountValue, err := strconv.ParseFloat(rewardAmountString, 64)
	if err != nil {
		logx.Error().Err(err).Msg("getPaymentTenor parse reward amount error")
		return egsv1.OrderPaymentOption{}, err
	}

	return egsv1.OrderPaymentOption{
		Type:         convertToEGSPaymentOptionType(paymentType),
		Tenor:        int32(tenor),
		DailyAmount:  types.Money(dailyAmountValue).ToGRPCDecimalPtr(),
		RewardSku:    rewardSku,
		RewardLabel:  rewardLabel,
		RewardAmount: types.Money(rewardAmountValue).ToGRPCDecimalPtr(),
	}, nil
}

func convertToEGSPaymentOptionType(paymentTenorType string) egsv1.PaymentOptionType {
	paymentTenorTypeGRPC := egsv1.PaymentTenorType(egsv1.PaymentTenorType_value[paymentTenorType])
	switch paymentTenorTypeGRPC {
	case egsv1.PaymentTenorType_PAYMENT_TENOR_TYPE_ONETIME:
		return egsv1.PaymentOptionType_PAYMENT_OPTION_TYPE_ONETIME
	case egsv1.PaymentTenorType_PAYMENT_TENOR_TYPE_TENOR:
		return egsv1.PaymentOptionType_PAYMENT_OPTION_TYPE_TENOR
	default:
		return egsv1.PaymentOptionType_PAYMENT_OPTION_TYPE_UNSPECIFIED
	}
}
func convertGRPCPaymentOptionTypeToInstallmentPaymentType(paymentType egsv1.PaymentOptionType) model.InstallmentPaymentType {
	switch paymentType {
	case egsv1.PaymentOptionType_PAYMENT_OPTION_TYPE_ONETIME:
		return model.InstallmentPaymentTypeOneTime
	case egsv1.PaymentOptionType_PAYMENT_OPTION_TYPE_TENOR:
		return model.InstallmentPaymentTypeTenor
	default:
		return model.InstallmentPaymentTypeUnspecified
	}
}

func (h HookEventReq) getRecipient() egsv1.OrderReceipient {
	firstName, _ := h.Value["firstName"].(string)
	lastName, _ := h.Value["lastName"].(string)
	email, _ := h.Value["email"].(string)
	phoneNumber, _ := h.Value["phoneNumber"].(string)
	reservedPhoneNumber, _ := h.Value["reservedPhoneNumber"].(string)

	return egsv1.OrderReceipient{
		Name:                 fmt.Sprintf("%s %s", firstName, lastName),
		PhoneNumber:          phoneNumber,
		Email:                email,
		EmergencyPhoneNumber: reservedPhoneNumber,
	}
}

func (h HookEventReq) getOrderShippingDetail(cpRes *imsPb.CheckoutProductResponse) (egsv1.OrderShippingDetail, error) {
	shippingAddress := h.getOrderShippingAddress()
	branchID, _ := h.Value["deliveryBranch"].(string)
	branchName, _ := h.Value["deliveryBranchName"].(string)
	deliverBy, _ := h.Value["deliverBy"].(string)
	vendorID, _ := h.Value["deliveryVendor"].(string)
	vendorName, _ := h.Value["deliveryVendorName"].(string)

	pickupStartStr, _ := h.Value["deliveryBranchPickupStartDate"].(string)
	pickupStart, _ := time.Parse(time.RFC3339, pickupStartStr)
	pickupEndStr, _ := h.Value["deliveryBranchPickupEndDate"].(string)
	pickupEnd, _ := time.Parse(time.RFC3339, pickupEndStr)

	return egsv1.OrderShippingDetail{
		Type:    h.getOrderShippingType(),
		Address: &shippingAddress,
		Branch: &egsv1.OrderShippingBranch{
			Id:         branchID,
			Name:       branchName,
			VendorId:   vendorID,
			VendorName: vendorName,
			PickupTimeRange: &egsv1.TimeRange{
				Start: timestamppb.New(pickupStart),
				End:   timestamppb.New(pickupEnd),
			},
		},
		DeliveryBy: deliverBy,
	}, nil
}

func (h HookEventReq) getOrderShippingType() egsv1.OrderShippingType {
	deliveryMethodString, _ := h.Value["deliveryMethod"].(string)

	shippingTypeGRPC := imsPb.ShippingType(imsPb.ShippingType_value[deliveryMethodString])

	var deliveryMethod DeliveryMethod
	switch shippingTypeGRPC {
	case imsPb.ShippingType_SHIPPING_TYPE_BRANCH:
		deliveryMethod = DeliveryMethodBranch
	case imsPb.ShippingType_SHIPPING_TYPE_DTD:
		deliveryMethod = DeliveryMethodDPD
	default:
		deliveryMethod = DeliveryMethodUnspecified
	}

	switch deliveryMethod {
	case DeliveryMethodDPD:
		return egsv1.OrderShippingType_ORDER_SHIPPING_TYPE_DELIVERY
	case DeliveryMethodBranch:
		return egsv1.OrderShippingType_ORDER_SHIPPING_TYPE_PICKUP
	default:
		return egsv1.OrderShippingType_ORDER_SHIPPING_TYPE_UNSPECIFIED
	}
}

func (h HookEventReq) getOrderShippingAddress() egsv1.OrderShippingAddress {
	addressInterface, _ := h.Value["address"].(map[string]interface{})

	firstName, _ := h.Value["firstName"].(string)
	lastName, _ := h.Value["lastName"].(string)

	houseNumberAndMoo, hasHouseNumberAndMoo := addressInterface["houseNumberAndMoo"].(string)
	address := houseNumberAndMoo

	// TODO: these line is for backward compatibility, remove when `houseNumberAndMoo` works fine (https://linemanwongnai.atlassian.net/browse/LMF-10003)
	if !hasHouseNumberAndMoo {
		houseNumber, _ := addressInterface["houseNumber"].(string)
		moo, _ := addressInterface["moo"].(string)
		address = fmt.Sprintf("%s %s", houseNumber, moo)
	}

	subdistrict, _ := addressInterface["subdistrict"].(string)
	district, _ := addressInterface["district"].(string)
	province, _ := addressInterface["province"].(string)
	zipcode, _ := addressInterface["zipcode"].(string)

	return egsv1.OrderShippingAddress{
		Name:        fmt.Sprintf("%s %s", firstName, lastName),
		Address:     address,
		Subdistrict: subdistrict,
		District:    district,
		Province:    province,
		PostalCode:  zipcode,
	}
}

func (h HookEventReq) createOrderProductDetailsGRPC(cpRes *imsPb.CheckoutProductResponse) []*egsv1.OrderProductDetail {
	orderProductDetail := make([]*egsv1.OrderProductDetail, 0)
	for _, p := range cpRes.Products {
		pp := types.NewMoneyWithDecimal(p.PrincipalPrice).ToGRPCDecimalPtr()
		orderProductDetail = append(orderProductDetail, &egsv1.OrderProductDetail{
			Name:           p.Name,
			Sku:            p.Sku,
			Barcode:        p.Barcode,
			StockId:        p.StockId,
			PrincipalPrice: pp,
			StockKey:       p.StockKey,
			StockPriority:  p.StockPriority,
			IsReward:       p.IsReward,
		})
	}
	return orderProductDetail
}

func (h HookEventReq) getPaymentTenorDailyAmount() (float64, error) {
	paymentTenor, _ := h.Value["paymentTenor"].(map[string]interface{})
	dailyAmount, _ := paymentTenor["dailyAmount"].(map[string]interface{})
	dailyAmountValueString, _ := dailyAmount["value"].(string)
	dailyAmountValue, err := strconv.ParseFloat(dailyAmountValueString, 64)
	if err != nil {
		logx.Error().Err(err).Msg("getPaymentTenor parse daily amount error")
		return 0, err
	}

	return dailyAmountValue, nil
}

func (h HookEventReq) getPaymentTenorRewardLabel() string {
	paymentTenor, _ := h.Value["paymentTenor"].(map[string]interface{})
	rewardLabel, _ := paymentTenor["rewardLabel"].(string)
	return rewardLabel
}

func (h HookEventReq) getPaymentTenorNetPrice() (float64, error) {
	paymentTenor, _ := h.Value["paymentTenor"].(map[string]interface{})
	netPrice, _ := paymentTenor["netPrice"].(map[string]interface{})
	netPriceValueString, _ := netPrice["value"].(string)
	netPriceAmountValue, err := strconv.ParseFloat(netPriceValueString, 64)
	if err != nil {
		logx.Error().Err(err).Msg("getPaymentTenor parse net price error")
		return 0, err
	}

	return netPriceAmountValue, nil
}

func (h HookEventReq) getPaymentTenorTenor() (int, error) {
	paymentTenor, _ := h.Value["paymentTenor"].(map[string]interface{})
	tenor, ok := paymentTenor["tenor"].(float64)
	if !ok {
		err := fmt.Errorf("cannot get tenor")
		logx.Error().Msgf("getPaymentTenor %v", err)
		return 0, err
	}

	return int(tenor), nil
}

type EGSFinancialRiskInfo struct {
	DailyAmount float64
	NetPrice    float64
	Tenor       int
}

func (h HookEventReq) getEGSFinancialRiskInfo() (EGSFinancialRiskInfo, error) {
	dailyAmount, err := h.getPaymentTenorDailyAmount()
	if err != nil {
		return EGSFinancialRiskInfo{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	netPrice, err := h.getPaymentTenorNetPrice()
	if err != nil {
		return EGSFinancialRiskInfo{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	tenor, err := h.getPaymentTenorTenor()
	if err != nil {
		return EGSFinancialRiskInfo{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	return EGSFinancialRiskInfo{
		DailyAmount: dailyAmount,
		NetPrice:    netPrice,
		Tenor:       tenor,
	}, nil
}

func convertStringToPaymentTenorType(t string) egsv1.PaymentTenorType {
	switch t {
	case "PAYMENT_TENOR_TYPE_ONETIME":
		return egsv1.PaymentTenorType_PAYMENT_TENOR_TYPE_ONETIME
	case "PAYMENT_TENOR_TYPE_TENOR":
		return egsv1.PaymentTenorType_PAYMENT_TENOR_TYPE_TENOR
	default:
		return egsv1.PaymentTenorType_PAYMENT_TENOR_TYPE_UNSPECIFIED
	}
}

func convertPaymentTenorTypeToPaymentMethodType(paymentTenorType egsv1.PaymentTenorType) commonv1.PaymentMethodType {
	switch paymentTenorType {
	case egsv1.PaymentTenorType_PAYMENT_TENOR_TYPE_ONETIME:
		return commonv1.PaymentMethodType_PAYMENT_METHOD_TYPE_ONE_TIME
	case egsv1.PaymentTenorType_PAYMENT_TENOR_TYPE_TENOR:
		return commonv1.PaymentMethodType_PAYMENT_METHOD_TYPE_INSTALLMENT
	default:
		return commonv1.PaymentMethodType_PAYMENT_METHOD_TYPE_UNSPECIFIED
	}
}

type paymentTenorValue struct {
	Value string `json:"value,omitempty"`
}

type paymentTenorData struct {
	DailyAmount  paymentTenorValue `json:"dailyAmount"`
	NetPrice     paymentTenorValue `json:"netPrice"`
	RewardAmount paymentTenorValue `json:"rewardAmount"`
	RewardLabel  string            `json:"rewardLabel,omitempty"`
	RewardSku    string            `json:"rewardSku,omitempty"`
	Tenor        int               `json:"tenor"`
	Type         string            `json:"type"`
}

func (h HookEventReq) GetPaymentMethod() (*egsv1.PaymentMethod, error) {
	var pt paymentTenorData

	paymentTenorStr, ok := h.Value["paymentTenor"].(map[string]interface{})
	if !ok {
		return nil, errors.New("error while getting paymentTenor value")
	}
	paymentTenorJSON, _ := json.Marshal(paymentTenorStr)
	err := json.Unmarshal(paymentTenorJSON, &pt)
	if err != nil {
		return nil, err
	}

	paymentTenorTypeStr := convertStringToPaymentTenorType(pt.Type)
	paymentMethod := convertPaymentTenorTypeToPaymentMethodType(paymentTenorTypeStr)

	return &egsv1.PaymentMethod{
		Type: paymentMethod,
		InstallmentPayment: &egsv1.PaymentMethod_InstallmentPayment{
			Tenor:       int32(pt.Tenor),
			DailyAmount: &decimal.Decimal{Value: pt.DailyAmount.Value},
		},
		NetAmount:    &decimal.Decimal{Value: pt.NetPrice.Value},
		CurrencyType: commonv1.PaymentMethodCurrencyType_PAYMENT_METHOD_CURRENCY_TYPE_WALLET_AND_CREDIT,
	}, nil
}
