package hook_test

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/hook"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestHooks_EventRejectedInsuranceRegistrationNotification(t *testing.T) {

	makeReq := func(req hook.HookEventReq) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("POST", "/v1/form/events", testutil.JSON(req))
		return gctx, recorder
	}

	t.Run("send reject notification with reason", func(tt *testing.T) {
		tt.Parallel()
		req := hook.HookEventReq{
			EventType: hook.EventRejected,
			FormType:  model.FormTypeInsuranceRegistration,
			Value: map[string]interface{}{
				"status":   "REJECTED",
				"driverId": "driver-id",
				"message":  "rejected",
			},
		}

		gctx, recorder := makeReq(req)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.notiService.EXPECT().
			Notify(gctx,
				[]string{"driver-id"},
				map[string]string{
					"action":   "INSURANCE_FORM_UPDATED",
					"driverId": "driver-id",
					"title":    "[ความคุ้มครอง] ลงทะเบียนไม่สำเร็จ",
					"body":     "ไม่สามารถอนุมัติได้เนื่องจาก “rejected” คุณสามารถลงทะเบียนใหม่ได้ในระยะเวลาที่เปิดสมัคร",
				},
				gomock.Any(), // service.WithFirebase,
				gomock.Any(), // service.WithSocketIO,
			).Return(nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("send reject notification without reason", func(tt *testing.T) {
		tt.Parallel()
		req := hook.HookEventReq{
			EventType: hook.EventRejected,
			FormType:  model.FormTypeInsuranceRegistration,
			Value: map[string]interface{}{
				"status":   "REJECTED",
				"driverId": "driver-id",
				"message":  "",
			},
		}

		gctx, recorder := makeReq(req)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.notiService.EXPECT().
			Notify(gctx,
				[]string{"driver-id"},
				map[string]string{
					"action":   "INSURANCE_FORM_UPDATED",
					"driverId": "driver-id",
					"title":    "[ความคุ้มครอง] ลงทะเบียนไม่สำเร็จ",
					"body":     "ไม่สามารถอนุมัติได้เนื่องจากเงื่อนไขหรือเอกสารไม่ถูกต้อง คุณสามารถลงทะเบียนใหม่ได้ในระยะเวลาที่เปิดสมัคร",
				},
				gomock.Any(), // service.WithFirebase,
				gomock.Any(), // service.WithSocketIO,
			).Return(nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestHooks_EventAutoApproveInsuranceRegistration(t *testing.T) {

	makeReq := func(req hook.HookEventReq) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("POST", "/v1/form/events", testutil.JSON(req))
		return gctx, recorder
	}

	autoApprovePAReq := hook.HookEventReq{
		EventType: hook.EventAutoApprove,
		FormType:  model.FormTypeInsuranceRegistration,
		Value: map[string]interface{}{
			"type":        "PA",
			"tier":        "PRO",
			"driverId":    "driver-id",
			"firstName":   "first-name",
			"lastName":    "last-name",
			"citizenId":   "citizen-id",
			"birthday":    "2023-02-19",
			"phoneNumber": "phone-number",
		},
	}

	autoApproveMotorReq := hook.HookEventReq{
		EventType: hook.EventAutoApprove,
		FormType:  model.FormTypeInsuranceRegistration,
		Value: map[string]interface{}{
			"type":                        "Motor",
			"tier":                        "PRO",
			"driverId":                    "driver-id",
			"firstName":                   "first-name",
			"lastName":                    "last-name",
			"citizenId":                   "citizen-id",
			"birthday":                    "2023-02-19",
			"phoneNumber":                 "phone-number",
			"vehicleBrand":                "vehicle-brand",
			"vehicleModel":                "vehicle-model",
			"vehicleRegistrationDate":     "2020",
			"chassisNumber":               "chassis-number",
			"licensePlateNumber":          "license-plate-number",
			"vehicleRegistrationLocation": "vehicle-registration-location",
			"vehicleColor":                "vehicle-color",
			"vehicleCc":                   "vehicle-cc",
			"vehicleType":                 "vehicle-type",
		},
	}

	autoApproveMotorAndPAReq := hook.HookEventReq{
		EventType: hook.EventAutoApprove,
		FormType:  model.FormTypeInsuranceRegistration,
		Value: map[string]interface{}{
			"type":                        "Motor+PA",
			"tier":                        "PRO",
			"driverId":                    "driver-id",
			"firstName":                   "first-name",
			"lastName":                    "last-name",
			"citizenId":                   "citizen-id",
			"birthday":                    "2023-02-19",
			"phoneNumber":                 "phone-number",
			"vehicleBrand":                "vehicle-brand",
			"vehicleModel":                "vehicle-model",
			"vehicleRegistrationDate":     "2020",
			"chassisNumber":               "chassis-number",
			"licensePlateNumber":          "license-plate-number",
			"vehicleRegistrationLocation": "vehicle-registration-location",
			"vehicleColor":                "vehicle-color",
			"vehicleCc":                   "vehicle-cc",
			"vehicleType":                 "vehicle-type",
		},
	}

	t.Run("auto approve personal accident form pass (compare with personal accident form)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApprovePAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITPersonalAccident,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:       crypt.NewLazyEncryptedString("first-name"),
						LastName:        crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber: crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:     "2023-02-19",
						PhoneNumber:     crypt.NewLazyEncryptedString("phone-number"),
					},
				},
			}, nil)

		deps.driverInsuranceService.EXPECT().ProcessApproveInsurance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("auto approve personal accident form fail (compare with personal accident form and data has changed)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApprovePAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITPersonalAccident,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:       crypt.NewLazyEncryptedString("first-name"),
						LastName:        crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber: crypt.NewLazyEncryptedString("citizen-id-change"),
						DateOfBirth:     "2023-02-19",
						PhoneNumber:     crypt.NewLazyEncryptedString("phone-number"),
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("auto approve personal accident form pass (compare with motor form)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApprovePAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		deps.driverInsuranceService.EXPECT().ProcessApproveInsurance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("auto approve personal accident form fail (compare with motor form and data has changed)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApprovePAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id-change"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("auto approve motor form pass (compare with motor form)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("auto approve motor form fail (compare with motor form and data has changed)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id-change"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("auto approve motor form fail (the latest is personal accident form)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITPersonalAccident,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:       crypt.NewLazyEncryptedString("first-name"),
						LastName:        crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber: crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:     "2023-02-19",
						PhoneNumber:     crypt.NewLazyEncryptedString("phone-number"),
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("auto approve motor + personal accident form pass (compare with motor + personal accident form)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorAndPAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITPersonalAccident,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:       crypt.NewLazyEncryptedString("first-name"),
						LastName:        crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber: crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:     "2023-02-19",
						PhoneNumber:     crypt.NewLazyEncryptedString("phone-number"),
					},
				},
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("auto approve motor + personal accident form pass 2 (compare with motor + personal accident form)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorAndPAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITPersonalAccident,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:       crypt.NewLazyEncryptedString("first-name"),
						LastName:        crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber: crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:     "2023-02-19",
						PhoneNumber:     crypt.NewLazyEncryptedString("phone-number"),
					},
				},
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id-change"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("auto approve motor + personal accident form fail personal change (compare with motor + personal accident form and data has changed)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorAndPAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITPersonalAccident,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:       crypt.NewLazyEncryptedString("first-name"),
						LastName:        crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber: crypt.NewLazyEncryptedString("citizen-id-change"),
						DateOfBirth:     "2023-02-19",
						PhoneNumber:     crypt.NewLazyEncryptedString("phone-number"),
					},
				},
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("auto approve motor accident form fail motor change (compare with motor + personal accident form and data has changed)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITPersonalAccident,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:       crypt.NewLazyEncryptedString("first-name"),
						LastName:        crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber: crypt.NewLazyEncryptedString("citizen-id-change"),
						DateOfBirth:     "2023-02-19",
						PhoneNumber:     crypt.NewLazyEncryptedString("phone-number"),
					},
				},
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id-change-2"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("auto approve motor + personal accident form pass (compare with motor form)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorAndPAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{
				{
					DriverID: "driver-id",
					Type:     model.DITMotorcycle,
					Status:   model.DISActive,
					Detail: model.DriverInsuranceDetail{
						FirstName:                   crypt.NewLazyEncryptedString("first-name"),
						LastName:                    crypt.NewLazyEncryptedString("last-name"),
						CitizenIDNumber:             crypt.NewLazyEncryptedString("citizen-id"),
						DateOfBirth:                 "2023-02-19",
						PhoneNumber:                 crypt.NewLazyEncryptedString("phone-number"),
						VehicleBrand:                "vehicle-brand",
						VehicleModel:                "vehicle-model",
						VehicleYear:                 "2020",
						VehicleChassisNumber:        "chassis-number",
						VehicleLicensePlateNumber:   crypt.NewLazyEncryptedString("license-plate-number"),
						VehicleRegistrationLocation: "vehicle-registration-location",
						VehicleColor:                "vehicle-color",
						VehicleCC:                   "vehicle-cc",
						VehicleType:                 "vehicle-type",
					},
				},
			}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("auto approve motor + personal accident form fail (the latest form not found)", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := makeReq(autoApproveMotorAndPAReq)
		api, deps, finish := newTestHookAPI(tt)
		defer finish()

		deps.driverInsuranceRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverInsurance{}, nil)

		api.HooksEvent(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}
