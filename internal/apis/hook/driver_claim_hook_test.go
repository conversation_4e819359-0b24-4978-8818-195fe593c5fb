package hook_test

import (
	"context"
	"errors"
	"net/http"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/suite"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/hook"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func TestHookTestSuite(t *testing.T) {
	suite.Run(t, new(HookTestSuite))
}

const (
	MockOrderID  = "mocked-orderId"
	MockTripID   = "mocked-tripId"
	MockDriverID = "mocked-driverId"
)

var (
	ErrMocked = errors.New("mocked error")
)

type HookTestSuite struct {
	suite.Suite

	ctx       context.Context
	container *hookAPIDeps
	cleanUp   func()

	underTest *hook.HookAPI
}

func (s *HookTestSuite) SetupSubTest() {
	s.ctx = context.Background()
	hookApi, container, cleanUp := newTestHookAPI(s.T())

	s.underTest = hookApi
	s.container = container
	s.cleanUp = cleanUp
}

func (s *HookTestSuite) TestEventRejectedDriversClaim() {
	s.Run("Driver2WQRPaymentClaim - success", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventRejected,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": MockOrderID,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		s.container.orderService.EXPECT().
			UpdateQRPromptPayInfoStatusAndUpdatePendingTransactionStatus(
				s.ctx,
				MockOrderID,
				model.QRPromptPayStatusRejectedByAdmin,
			).
			Return(nil)
		actualStatusCode, err := s.underTest.EventRejectedDriversClaim(s.ctx, req)
		s.Nil(err)
		s.Equal(http.StatusOK, actualStatusCode)
	})
	s.Run("Driver2WQRPaymentClaim - return error if OrderService return error", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventRejected,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": MockOrderID,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		s.container.orderService.EXPECT().
			UpdateQRPromptPayInfoStatusAndUpdatePendingTransactionStatus(
				s.ctx,
				MockOrderID,
				model.QRPromptPayStatusRejectedByAdmin,
			).
			Return(ErrMocked)
		actualStatusCode, err := s.underTest.EventRejectedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": ErrMocked.Error(),
		})
		s.Equal(http.StatusInternalServerError, actualStatusCode)
	})
	s.Run("Driver2WQRPaymentClaim - error no orderId", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventRejected,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status": "SOMETHING",
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		actualStatusCode, err := s.underTest.EventRejectedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": hook.ErrNoOrderID.Error(),
		})
		s.Equal(http.StatusBadRequest, actualStatusCode)
	})
	s.Run("Driver2WQRPaymentClaim - error unparsable orderId", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventRejected,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": 123,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		actualStatusCode, err := s.underTest.EventRejectedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": hook.ErrUnparsableOrderID.Error(),
		})
		s.Equal(http.StatusBadRequest, actualStatusCode)
	})
}

func (s *HookTestSuite) TestEventApprovedDriversClaim() {
	s.Run("Driver2WQRPaymentClaim - success", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				// status to push noti
				"status":   "APPROVED",
				"orderId":  MockOrderID,
				"tripId":   MockTripID,
				"driverId": MockDriverID,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		s.container.tripService.EXPECT().GetTripByID(s.ctx, MockTripID, gomock.Any()).Return(model.Trip{}, nil)
		s.container.tripService.EXPECT().ProcessPendingFee(s.ctx, gomock.Any(), true).Return(nil)
		s.container.orderService.EXPECT().
			UpdateQRPromptPayInfoStatusAndUpdatePendingTransactionStatus(
				s.ctx,
				MockOrderID,
				model.QRPromptPayStatusResolvedByAdmin,
			).
			Return(nil)
		s.container.notiService.EXPECT().Notify(s.ctx, []string{MockDriverID}, gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Nil(err)
		s.Equal(http.StatusOK, actualStatusCode)
	})

	s.Run("Driver2WQRPaymentClaim - success", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				// status to push noti
				"status":   "APPROVED",
				"orderId":  MockOrderID,
				"tripId":   MockTripID,
				"driverId": MockDriverID,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		s.container.tripService.EXPECT().GetTripByID(s.ctx, MockTripID, gomock.Any()).Return(model.Trip{}, nil)
		s.container.tripService.EXPECT().ProcessPendingFee(s.ctx, gomock.Any(), true).Return(nil)
		s.container.orderService.EXPECT().
			UpdateQRPromptPayInfoStatusAndUpdatePendingTransactionStatus(
				s.ctx,
				MockOrderID,
				model.QRPromptPayStatusResolvedByAdmin,
			).
			Return(nil)
		s.container.notiService.EXPECT().Notify(s.ctx, []string{MockDriverID}, gomock.Any(), gomock.Any(), gomock.Any()).Return(ErrMocked)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": ErrMocked.Error(),
		})
		s.Equal(http.StatusInternalServerError, actualStatusCode)
	})

	s.Run("Driver2WQRPaymentClaim - update qr error", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": MockOrderID,
				"tripId":  MockTripID,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		s.container.tripService.EXPECT().GetTripByID(s.ctx, MockTripID, gomock.Any()).Return(model.Trip{}, nil)
		s.container.tripService.EXPECT().ProcessPendingFee(s.ctx, gomock.Any(), true).Return(nil)
		s.container.orderService.EXPECT().
			UpdateQRPromptPayInfoStatusAndUpdatePendingTransactionStatus(
				s.ctx,
				MockOrderID,
				model.QRPromptPayStatusResolvedByAdmin,
			).
			Return(ErrMocked)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": ErrMocked.Error(),
		})
		s.Equal(http.StatusInternalServerError, actualStatusCode)
	})

	s.Run("Driver2WQRPaymentClaim - process pending fee error", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": MockOrderID,
				"tripId":  MockTripID,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		s.container.tripService.EXPECT().GetTripByID(s.ctx, MockTripID, gomock.Any()).Return(model.Trip{}, nil)
		s.container.tripService.EXPECT().ProcessPendingFee(s.ctx, gomock.Any(), true).Return(ErrMocked)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": ErrMocked.Error(),
		})
		s.Equal(http.StatusInternalServerError, actualStatusCode)
	})

	s.Run("Driver2WQRPaymentClaim - get trip error", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": MockOrderID,
				"tripId":  MockTripID,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)
		s.container.tripService.EXPECT().GetTripByID(s.ctx, MockTripID, gomock.Any()).Return(model.Trip{}, ErrMocked)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": ErrMocked.Error(),
		})
		s.Equal(http.StatusInternalServerError, actualStatusCode)
	})

	s.Run("Driver2WQRPaymentClaim - error no tripId", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": MockOrderID,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": hook.ErrNoTripID.Error(),
		})
		s.Equal(http.StatusBadRequest, actualStatusCode)
	})

	s.Run("Driver2WQRPaymentClaim - error unparsable tripId", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": MockOrderID,
				"tripId":  123,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": hook.ErrUnparsableTripID.Error(),
		})
		s.Equal(http.StatusBadRequest, actualStatusCode)
	})

	s.Run("Driver2WQRPaymentClaim - error no orderId", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status": "SOMETHING",
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": hook.ErrNoOrderID.Error(),
		})
		s.Equal(http.StatusBadRequest, actualStatusCode)
	})

	s.Run("Driver2WQRPaymentClaim - error unparsable orderId", func() {
		req := hook.HookEventReq{
			EventType:   hook.EventApproved,
			FormSubtype: model.FormSubtypeDriver2WQRPaymentClaim,
			Value: map[string]interface{}{
				"status":  "SOMETHING",
				"orderId": 123,
			},
		}

		s.container.txnHelper.EXPECT().WithTxn(s.ctx, gomock.Any(), gomock.Any()).Return(nil, nil)

		actualStatusCode, err := s.underTest.EventApprovedDriversClaim(s.ctx, req)
		s.Equal(err.Info, map[string]interface{}{
			"NonFieldsError": hook.ErrUnparsableOrderID.Error(),
		})
		s.Equal(http.StatusBadRequest, actualStatusCode)
	})
}
