package hook

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/mitchellh/mapstructure"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverinsurance"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type NotifyInsuranceRegistrationReq struct {
	Status   NotifyInsuranceRegistrationStatus `json:"status"`
	DriverID string                            `json:"driverId"`
	Message  string                            `json:"message"`
}

func (hookapi *HookAPI) eventRejectedInsuranceRegistration(ctx context.Context, req HookEventReq) (int, *api.Error) {
	var notiReq NotifyInsuranceRegistrationReq
	if err := mapstructure.Decode(req.Value, &notiReq); err != nil {
		logrus.Errorf("eventRejectedInsuranceRegistration: decode error - %s", err.Error())
		return http.StatusBadRequest, utils.ErrorStructResponse(err)
	}

	if notiReq.Status == NotifyInsuranceRegistrationRejected {
		if err := hookapi.notifierService.Notify(
			ctx,
			[]string{notiReq.DriverID},
			service.EventRejectedInsuranceRegistration(notiReq.DriverID, notiReq.Message),
			service.WithFirebase,
			service.WithSocketIO,
		); err != nil {
			logrus.Errorf("eventRejectedInsuranceRegistration: push notification error - %s", err.Error())
			return http.StatusInternalServerError, utils.ErrorStructResponse(err)
		}
	}

	return http.StatusOK, nil
}

func (hookapi *HookAPI) eventAutoApproveInsuranceRegistration(ctx context.Context, req HookEventReq) (int, *api.Error) {
	if err := req.ValidateRequest(); err != nil {
		return http.StatusBadRequest, utils.ErrorStructResponse(err)
	}

	approveInsuranceRequest, err := req.ConvertToApproveInsuranceRequest()
	if err != nil {
		return http.StatusBadRequest, utils.ErrorStructResponse(err)
	}

	newDriverInsurances, imageField, err := approveInsuranceRequest.ConvertToModels(
		hookapi.driverInsuranceCfg.SponsorPersonalAccidentDriverTiers,
		hookapi.driverInsuranceCfg.SponsorMotorcycleDriverTiers,
	)
	if err != nil || len(newDriverInsurances) == 0 {
		logrus.Errorf("AutoApproveInsurance ConvertToModels err: %v", err)
		return http.StatusBadRequest, utils.ErrorStructResponse(err)
	}

	driverID := newDriverInsurances[0].DriverID
	currentTime := timeutil.BangkokNow()
	firstDoM := timeutil.DateTruncate(timeutil.GetFirstDayOfMonthFromTime(currentTime))
	createdAtAfter := firstDoM.AddDate(0, -1*hookapi.cfg.FormInsuranceAutoApproveMonthDuration, 0)

	driverInsuranceQueryBuilder := persistence.BuildDriverInsuranceQuery().
		WithDriverID(driverID).
		WithStatus(model.DISActive).
		WithCreatedAtAfter(createdAtAfter)

	existedDriverInsurances, err := hookapi.driverInsuranceRepository.FindWithQueryAndSort(ctx, driverInsuranceQueryBuilder, 0, 2, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Errorf("AutoApproveInsurance find existed Driver: %s Error: %s", driverID, err)
		return http.StatusInternalServerError, errors.ErrInternal(err)
	}

	if len(existedDriverInsurances) == 0 {
		err := fmt.Errorf("the latest driver insurance not found")
		logrus.Errorf("AutoApproveInsurance find existed Driver: %s Error: %s", driverID, err)
		return http.StatusInternalServerError, errors.ErrInternal(err)
	}

	isDataChanged, err := hookapi.doDataChangeComparingWithLatestDriverInsurance(newDriverInsurances, existedDriverInsurances, driverID, createdAtAfter)
	if err != nil {
		logrus.Errorf("AutoApproveInsurance data has changed comparing Driver: %s Error: %s", driverID, err)
		return http.StatusInternalServerError, errors.ErrInternal(err)
	}

	if isDataChanged {
		// Do not fix the error message
		return http.StatusInternalServerError, errors.ErrInternal(fmt.Errorf("insurance data has changed"))
	}

	if len(newDriverInsurances) == 1 && newDriverInsurances[0].Type == model.DITPersonalAccident {
		if err := hookapi.driverInsuranceService.ProcessApproveInsurance(
			ctx,
			newDriverInsurances,
			imageField,
			"SYSTEM",
		); err != nil {
			logrus.Errorf("DoApproveInsurance Driver:%s, err: %v", driverID, err)
			return http.StatusInternalServerError, errors.ErrInternal(err)
		}
	}

	return http.StatusOK, nil
}

func (h HookEventReq) ConvertToApproveInsuranceRequest() (driverinsurance.ApproveInsuranceRequest, error) {
	var approveInsuranceRequest driverinsurance.ApproveInsuranceRequest

	// Convert json string to struct
	jsonValue, err := json.Marshal(h.Value)
	if err != nil {
		return approveInsuranceRequest, err
	}

	if err := json.Unmarshal(jsonValue, &approveInsuranceRequest.Value); err != nil {
		return approveInsuranceRequest, err
	}

	// we check exist at the req validator
	approveInsuranceRequest.FormId, _ = h.Value["formId"].(string)
	approveInsuranceRequest.Admin = "SYSTEM"

	return approveInsuranceRequest, nil

}

func (hookapi *HookAPI) doDataChangeComparingWithLatestDriverInsurance(newDriverInsurances []model.DriverInsurance, existedDriverInsurances []model.DriverInsurance, driverId string, createdAtAfter time.Time) (bool, error) {
	var newPersonalAccidentInsurance *model.DriverInsurance
	var newMotorInsurance *model.DriverInsurance

	for _, newDriverInsurance := range newDriverInsurances {
		ndi := newDriverInsurance
		if newDriverInsurance.Type == model.DITPersonalAccident {
			newPersonalAccidentInsurance = &ndi
		} else if newDriverInsurance.Type == model.DITMotorcycle {
			newMotorInsurance = &ndi
		}
	}

	// we can use motor insurance to compare with all type
	// PA's data is subset of Motor's data
	if existedDriverInsurances[0].Type == model.DITMotorcycle && existedDriverInsurances[0].Status == model.DISActive {
		if newPersonalAccidentInsurance != nil {
			return personalAccidentCompare(*newPersonalAccidentInsurance, &existedDriverInsurances[0])
		}

		if newMotorInsurance != nil {
			return motorCompare(*newMotorInsurance, &existedDriverInsurances[0])
		}
	} else {
		var oldPersonalAccidentInsurance *model.DriverInsurance
		var oldMotorInsurance *model.DriverInsurance
		for _, existedDriverInsurance := range existedDriverInsurances {
			edi := existedDriverInsurance
			switch existedDriverInsurance.Type {
			case model.DITPersonalAccident:
				if oldPersonalAccidentInsurance == nil {
					oldPersonalAccidentInsurance = &edi
				}

			case model.DITMotorcycle:
				if oldMotorInsurance == nil {
					oldMotorInsurance = &edi
				}
			}
		}

		// personal accident form data comparing
		if newPersonalAccidentInsurance != nil {
			return personalAccidentCompare(*newPersonalAccidentInsurance, oldPersonalAccidentInsurance)
		}

		// motor form data comparing
		if newMotorInsurance != nil {
			return motorCompare(*newMotorInsurance, oldMotorInsurance)
		}
	}

	return false, nil
}

func personalAccidentCompare(newDriverInsurance model.DriverInsurance, oldDriverInsurance *model.DriverInsurance) (bool, error) {
	if oldDriverInsurance == nil {
		return false, fmt.Errorf("the latest driver insurance not found (pa)")
	}

	if (newDriverInsurance.Detail.FirstName.String() != oldDriverInsurance.Detail.FirstName.String()) ||
		(newDriverInsurance.Detail.LastName.String() != oldDriverInsurance.Detail.LastName.String()) ||
		(newDriverInsurance.Detail.CitizenIDNumber.String() != oldDriverInsurance.Detail.CitizenIDNumber.String()) ||
		(newDriverInsurance.Detail.DateOfBirth != oldDriverInsurance.Detail.DateOfBirth) ||
		(newDriverInsurance.Detail.PhoneNumber.String() != oldDriverInsurance.Detail.PhoneNumber.String()) {
		return true, nil
	}

	return false, nil

}

func motorCompare(newDriverInsurance model.DriverInsurance, oldDriverInsurance *model.DriverInsurance) (bool, error) {
	if oldDriverInsurance == nil {
		return false, fmt.Errorf("the latest driver insurance not found (motor)")
	}

	if (newDriverInsurance.Detail.FirstName.String() != oldDriverInsurance.Detail.FirstName.String()) ||
		(newDriverInsurance.Detail.LastName.String() != oldDriverInsurance.Detail.LastName.String()) ||
		(newDriverInsurance.Detail.CitizenIDNumber.String() != oldDriverInsurance.Detail.CitizenIDNumber.String()) ||
		(newDriverInsurance.Detail.DateOfBirth != oldDriverInsurance.Detail.DateOfBirth) ||
		(newDriverInsurance.Detail.PhoneNumber.String() != oldDriverInsurance.Detail.PhoneNumber.String()) ||
		(newDriverInsurance.Detail.Address.District.String() != oldDriverInsurance.Detail.Address.District.String()) ||
		(newDriverInsurance.Detail.Address.HouseNumber.String() != oldDriverInsurance.Detail.Address.HouseNumber.String()) ||
		(newDriverInsurance.Detail.Address.Moo.String() != oldDriverInsurance.Detail.Address.Moo.String()) ||
		(newDriverInsurance.Detail.Address.Province.String() != oldDriverInsurance.Detail.Address.Province.String()) ||
		(newDriverInsurance.Detail.Address.Subdistrict.String() != oldDriverInsurance.Detail.Address.Subdistrict.String()) ||
		(newDriverInsurance.Detail.Address.Zipcode.String() != oldDriverInsurance.Detail.Address.Zipcode.String()) ||
		(newDriverInsurance.Detail.VehicleBrand != oldDriverInsurance.Detail.VehicleBrand) ||
		(newDriverInsurance.Detail.VehicleYear != oldDriverInsurance.Detail.VehicleYear) ||
		(newDriverInsurance.Detail.VehicleChassisNumber != oldDriverInsurance.Detail.VehicleChassisNumber) ||
		(newDriverInsurance.Detail.VehicleLicensePlateNumber.String() != oldDriverInsurance.Detail.VehicleLicensePlateNumber.String()) ||
		(newDriverInsurance.Detail.VehicleRegistrationLocation != oldDriverInsurance.Detail.VehicleRegistrationLocation) ||
		(newDriverInsurance.Detail.VehicleColor != oldDriverInsurance.Detail.VehicleColor) ||
		(newDriverInsurance.Detail.VehicleCC != oldDriverInsurance.Detail.VehicleCC) ||
		(newDriverInsurance.Detail.VehicleModel != oldDriverInsurance.Detail.VehicleModel) ||
		(newDriverInsurance.Detail.VehicleType != oldDriverInsurance.Detail.VehicleType) ||
		newDriverInsurance.Detail.Email != oldDriverInsurance.Detail.Email {
		return true, nil
	}

	return false, nil

}
