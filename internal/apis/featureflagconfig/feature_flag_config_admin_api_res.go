package featureflagconfig

import (
	"strings"
	"time"

	"github.com/r3labs/diff/v3"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/go/unleash/admin"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
)

type GetFeatureFlagConfigRes struct {
	Label             string                  `json:"label"`
	Name              string                  `json:"name"`
	FeatureFlagConfig admin.FeatureFlagConfig `json:"featureFlagConfig"`
}

func NewFeatureFlagConfigsRes(label, name string, featureFlagConfig admin.FeatureFlagConfig) GetFeatureFlagConfigRes {
	return GetFeatureFlagConfigRes{
		Label:             label,
		Name:              name,
		FeatureFlagConfig: featureFlagConfig,
	}
}

type AuditLogDiffRes struct {
	Actor       string           `json:"actor"`
	FeatureName string           `json:"featureName"`
	Changes     []AuditLogChange `json:"changes"`
	CreatedAt   time.Time        `json:"createdAt"`
	UpdatedAt   time.Time        `json:"updatedAt"`
}

type AuditLogChange struct {
	Name string      `json:"name"`
	Path string      `json:"path"`
	From interface{} `json:"from"`
	To   interface{} `json:"to"`
}

func NewAuditLogDiffRes(auditLogs []model.AuditLog) []AuditLogDiffRes {
	var res []AuditLogDiffRes
	var changes []AuditLogChange
	for _, auditLog := range auditLogs {
		d, err := diff.Diff(auditLog.Before, auditLog.After)
		if err != nil {
			logx.Error().Err(err).Msg("NewAuditLogDiffRes diff audit err")
			continue
		}

		changes = getHistoryChange(auditLog, d)
		if len(changes) > 0 {
			res = append(res, AuditLogDiffRes{
				Actor:       auditLog.Actor.ID,
				FeatureName: changes[0].Name,
				Changes:     changes,
				CreatedAt:   auditLog.Timestamp,
				UpdatedAt:   auditLog.Timestamp,
			})
		}
	}

	return res
}

func getHistoryChange(auditLog model.AuditLog, cl diff.Changelog) []AuditLogChange {
	var res []AuditLogChange

	for _, c := range cl {
		s := transformFieldSnakeToCamel(c.Path)

		decryptedChangeFrom, decryptedChangeTo, err := model.DecryptChangeLog(c.From, c.To)
		if err == nil {
			c.From = decryptedChangeFrom
			c.To = decryptedChangeTo
		}

		res = append(res, AuditLogChange{
			Name: auditLog.Object.ID,
			Path: strings.Join(s, " > "),
			From: c.From,
			To:   c.To,
		})
	}
	return res
}

func transformFieldSnakeToCamel(field []string) []string {
	if len(field) == 0 {
		return nil
	}

	res := make([]string, 0, len(field))
	for _, s := range field {
		res = append(res, stringutil.SnakeCaseToCamelCase(s))
	}

	return res
}
