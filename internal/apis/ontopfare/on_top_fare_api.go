package ontopfare

import (
	"context"
	"fmt"
	"sync"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/absinthe/api"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const (
	ParamID = "id"
)

type OnTopFareAPI struct {
	OnTopFareRepo  repository.OnTopFareRepository
	AuditLogRepo   repository.AuditLogRepository
	PolygonService polygon.Polygon
}

func ProvideOnTopFareAPI(otRepo repository.OnTopFareRepository, alRepo repository.AuditLogRepository, polygonService polygon.Polygon) *OnTopFareAPI {
	return &OnTopFareAPI{
		OnTopFareRepo:  otRepo,
		AuditLogRepo:   alRepo,
		PolygonService: polygonService,
	}
}

func (of *OnTopFareAPI) Create(gctx *gin.Context) {
	req, err := NewCreateOnTopFareReq(gctx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		default:
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	ctx := gctx.Request.Context()

	otf, err := req.ToModel()
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiErrors.ErrInvalidRequest(err))
		return
	}

	if err := of.OnTopFareRepo.Create(ctx, *otf); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	goroutineCtx := gctx.Copy()
	safe.GoFuncWithCtx(goroutineCtx, func() {
		if err = of.AuditLogRepo.Insert(goroutineCtx, &model.AuditLog{
			Object: model.AuditObject{
				ObjectType: model.OnTopFareObject,
				ID:         otf.ID,
			},
			Actor: model.AuditLogActor{
				ID: req.Email,
			},
			Timestamp: timeutil.BangkokNow(),
			Action:    model.CreateAction,
			Before:    nil,
			After:     otf,
		}); err != nil {
			logrus.Errorf("create on top fare cannot create audit log: %v\n", err)
		}
	})

	apiutil.Created(gctx, NewOnTopFareRes(*otf))
}

func (of *OnTopFareAPI) BulkCreate(gctx *gin.Context) {
	req, err := NewBulkCreateOnTopFareReq(gctx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		default:
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}
	ctx := gctx.Request.Context()
	otfs, err := req.ToModel()
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiErrors.ErrInvalidRequest(err))
		return
	}
	if err := of.OnTopFareRepo.Create(ctx, otfs...); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	goroutineCtx := gctx.Copy()
	safe.GoFuncWithCtx(goroutineCtx, func() {
		auditLogs := make([]*model.AuditLog, len(otfs))
		for i, otf := range otfs {
			auditLogs[i] = &model.AuditLog{
				Object: model.AuditObject{
					ObjectType: model.OnTopFareObject,
					ID:         otf.ID,
				},
				Actor: model.AuditLogActor{
					ID: req.Email,
				},
				Timestamp: timeutil.BangkokNow(),
				Action:    model.CreateAction,
				Before:    nil,
				After:     otf,
			}
		}
		if err = of.AuditLogRepo.InsertMany(goroutineCtx, auditLogs); err != nil {
			logrus.Errorf("bulk create on top fare cannot create audit log: %v\n", err)
		}
	})

	res := NewBulkOnTopFareRes()
	for _, otf := range otfs {
		res.AddSuccess(NewOnTopFareRes(otf))
	}

	apiutil.Created(gctx, res)
}

func (of *OnTopFareAPI) List(gctx *gin.Context) {
	req, err := NewListOnTopFareReq(gctx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		default:
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	skip, size := utils.ParsePagination(gctx)
	ctx := gctx.Request.Context()

	var sort []string
	if req.Sort != "" {
		s := fmt.Sprintf("-%s", req.Sort)
		if req.Direction == "ASC" {
			s = req.Sort
		}
		sort = append(sort, s)
	}

	otf, err := of.OnTopFareRepo.Find(ctx, req.Query(), skip, size, sort)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	count, err := of.OnTopFareRepo.Count(ctx, req.Query())
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	res := make([]OnTopFareRes, len(otf))
	for i, in := range otf {
		res[i] = *NewOnTopFareRes(in)
	}

	apiutil.OKList(gctx, res, count)
}

func (of *OnTopFareAPI) Get(gctx *gin.Context) {
	id := gctx.Param("id")
	ctx := gctx.Request.Context()

	otf, err := of.OnTopFareRepo.Get(ctx, id)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			apiutil.ErrNotFound(gctx, apiErrors.ErrSettingIncentiveNotFound())
			return
		}
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, NewOnTopFareRes(*otf))
}

func (of *OnTopFareAPI) Update(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	id := gctx.Param(ParamID)
	otf, err := of.OnTopFareRepo.Get(ctx, id)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			apiutil.ErrNotFound(gctx, apiErrors.ErrOnTopFareNotFound())
			return
		}
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	req, err := NewUpdateOnTopFareReq(gctx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		default:
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	auditLog := &model.AuditLog{
		Object: model.AuditObject{
			ObjectType: model.OnTopFareObject,
			ID:         otf.ID,
		},
		Actor: model.AuditLogActor{
			ID: req.Email,
		},
		Timestamp: timeutil.BangkokNow(),
		Action:    model.UpdateAction,
		Before:    *otf,
	}

	if err = req.ToModel(otf); err != nil {
		apiutil.ErrBadRequest(gctx, apiErrors.ErrInvalidRequest(err))
		return
	}

	if err := of.OnTopFareRepo.Update(ctx, *otf); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	goroutineCtx := gctx.Copy()
	safe.GoFuncWithCtx(goroutineCtx, func() {
		auditLog.After = *otf
		if err = of.AuditLogRepo.Insert(goroutineCtx, auditLog); err != nil {
			logrus.Errorf("update on top fare cannot create audit log: %v\n", err)
		}
	})

	apiutil.OK(gctx, NewOnTopFareRes(*otf))
}

func (of *OnTopFareAPI) BulkUpdate(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	req, err := NewBulkUpdateOnTopFareReq(gctx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		default:
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}

	ids := make([]string, len(req.Data))
	otfsHashMap := make(map[string]BulkUpdateOnTopFareBody)

	for i, otf := range req.Data {
		ids[i] = otf.ID
		otfsHashMap[otf.ID] = otf
	}

	otfs, err := of.OnTopFareRepo.FindByIDs(ctx, ids)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}
	worker, release := safe.NewWorker(10)
	defer release()
	wg := &sync.WaitGroup{}
	res := NewBulkOnTopFareRes()
	for _, otf := range otfs {
		rb := otfsHashMap[otf.ID]
		delete(otfsHashMap, otf.ID)
		of.backgroundUpdateOnTopFare(ctx, wg, worker, rb, otf, req.Email, res)
	}
	for id := range otfsHashMap {
		res.AddFail(NewBulkOnTopFareError(id, errors.New("not found")))
	}
	wg.Wait()
	apiutil.OK(gctx, res)
}

func (of *OnTopFareAPI) backgroundUpdateOnTopFare(ctx context.Context, wg *sync.WaitGroup, wk *safe.Worker, body BulkUpdateOnTopFareBody, otf model.OnTopFare, email string, res *BulkOnTopFareRes) {
	wg.Add(1)
	wk.GoFuncWithPool(func() {
		defer wg.Done()
		auditLog := &model.AuditLog{
			Object: model.AuditObject{
				ObjectType: model.OnTopFareObject,
				ID:         otf.ID,
			},
			Actor: model.AuditLogActor{
				ID: email,
			},
			Timestamp: timeutil.BangkokNow(),
			Action:    model.UpdateAction,
			Before:    otf,
		}
		if err := body.ToModel(&otf, email); err != nil {
			res.AddFail(NewBulkOnTopFareError(otf.ID, err))
			return
		}
		if err := of.OnTopFareRepo.Update(ctx, otf); err != nil {
			res.AddFail(NewBulkOnTopFareError(otf.ID, err))
			return
		}
		auditLog.After = otf
		res.AddSuccess(NewOnTopFareRes(otf))
		if err := of.AuditLogRepo.Insert(context.Background(), auditLog); err != nil {
			logrus.Errorf("bulk update cannot create audit log: %v\n", err)
		}
	})
}

func (of *OnTopFareAPI) Delete(gctx *gin.Context) {
	ctx := gctx.Request.Context()
	id := gctx.Param(ParamID)
	var req DeleteOnTopFareReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiErrors.ErrInvalidRequest(err))
		return
	}

	otf, err := of.OnTopFareRepo.Get(ctx, id)

	if err != nil {
		if err == mongodb.ErrDataNotFound {
			apiutil.ErrNotFound(gctx, apiErrors.ErrOnTopFareNotFound())
			return
		}
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	err = of.OnTopFareRepo.Delete(ctx, *otf)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	goroutineCtx := gctx.Copy()
	safe.GoFuncWithCtx(goroutineCtx, func() {
		auditLog := &model.AuditLog{
			Object: model.AuditObject{
				ObjectType: model.OnTopFareObject,
				ID:         otf.ID,
			},
			Actor: model.AuditLogActor{
				ID: req.Email,
			},
			Timestamp: timeutil.BangkokNow(),
			Action:    model.DeleteAction,
			Before:    otf,
			After:     nil,
		}
		if err = of.AuditLogRepo.Insert(goroutineCtx, auditLog); err != nil {
			logrus.Errorf("delete on top fare cannot create audit log: %v\n", err)
		}
	})

	apiutil.OK(gctx, apiutil.EmptyBody())
}

func (of *OnTopFareAPI) BulkDelete(gctx *gin.Context) {
	req, err := NewBulkDeleteOnTopFareReq(gctx)
	if err != nil {
		apiutil.ErrBadRequest(gctx, apiErrors.ErrInvalidRequest(err))
		return
	}

	ctx := gctx.Request.Context()

	otfs, err := of.OnTopFareRepo.FindByIDs(ctx, req.Ids)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	if err = of.OnTopFareRepo.DeleteByIDs(ctx, req.Ids); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	goroutineCtx := gctx.Copy()
	safe.GoFuncWithCtx(goroutineCtx, func() {
		auditLogs := make([]*model.AuditLog, len(otfs))
		for i, otf := range otfs {
			auditLogs[i] = &model.AuditLog{
				Object: model.AuditObject{
					ObjectType: model.OnTopFareObject,
					ID:         otf.ID,
				},
				Actor: model.AuditLogActor{
					ID: req.Email,
				},
				Timestamp: timeutil.BangkokNow(),
				Action:    model.DeleteAction,
				Before:    otf,
				After:     nil,
			}
		}
		if err = of.AuditLogRepo.InsertMany(goroutineCtx, auditLogs); err != nil {
			logrus.Errorf("bulk delete on top fare cannot create audit log: %v\n", err)
		}
	})

	apiutil.OK(gctx, apiutil.EmptyBody())
}
