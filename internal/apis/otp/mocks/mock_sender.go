// Code generated by MockGen. DO NOT EDIT.
// Source: ./sender.go

// Package otp_mock is a generated GoMock package.
package otp_mock

import (
	gomock "github.com/golang/mock/gomock"
	reflect "reflect"
)

// MockSender is a mock of Sender interface
type MockSender struct {
	ctrl     *gomock.Controller
	recorder *MockSenderMockRecorder
}

// MockSenderMockRecorder is the mock recorder for MockSender
type MockSenderMockRecorder struct {
	mock *MockSender
}

// NewMockSender creates a new mock instance
func NewMockSender(ctrl *gomock.Controller) *MockSender {
	mock := &MockSender{ctrl: ctrl}
	mock.recorder = &MockSenderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockSender) EXPECT() *MockSenderMockRecorder {
	return m.recorder
}

// SendOTP mocks base method
func (m *MockSender) SendOTP(phoneNumber, otp string, tryCount int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendOTP", phoneNumber, otp, tryCount)
	ret0, _ := ret[0].(error)
	return ret0
}

// SendOTP indicates an expected call of SendOTP
func (mr *MockSenderMockRecorder) SendOTP(phoneNumber, otp, tryCount interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendOTP", reflect.TypeOf((*MockSender)(nil).SendOTP), phoneNumber, otp, tryCount)
}
