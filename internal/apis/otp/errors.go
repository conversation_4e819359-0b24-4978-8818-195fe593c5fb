package otp

import (
	"math"
	"time"

	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
)

func validateBanError(expiration time.Time) *api.Error {
	return apiutil.New(
		"OTP_MAXIMUM_FAILS_REACHED",
		"Temporarily locked, due to too many failed validations.",
		gin.H{
			"banDurationRemainingInSec": durationRemainingInSec(expiration),
		},
	)
}

func genIntervalError(expiration time.Time) *api.Error {
	return apiutil.New(
		"OTP_NOT_OVER_INTERVAL_LIMIT",
		"Cannot request OTP rapidly",
		gin.H{
			"banDurationRemainingInSec": durationRemainingInSec(expiration),
		},
	)
}

func genBanError(expiration time.Time) *api.Error {
	return apiutil.New(
		"OTP_MAXIMUM_GENERATIONS_REACHED",
		"Temporarily locked, due to too many requests.",
		gin.H{
			"banDurationRemainingInSec": durationRemainingInSec(expiration),
		},
	)
}

func wrongOTPError(tryRemain int) *api.Error {
	return apiutil.New(
		"OTP_INCORRECT_VALUE",
		"input OTP is not the same as sent OTP",
		gin.H{
			"validationRemainingTries": tryRemain,
		},
	)
}

func expiredError(tryRemain int) *api.Error {
	return apiutil.New(
		"OTP_EXPIRED",
		"Input OTP already expired, please request OTP.",
		gin.H{
			"validationRemainingTries": tryRemain,
		},
	)
}

func durationRemainingInSec(due time.Time) int {
	duration := time.Until(due)
	return int(math.Round(duration.Seconds()))
}
