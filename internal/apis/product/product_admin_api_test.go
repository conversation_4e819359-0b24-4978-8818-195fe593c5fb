package product_test

import (
	"bytes"
	"context"
	"encoding/csv"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/product"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestProductGroupAPI_ListProducts(t *testing.T) {
	t.Parallel()

	req := func(req *product.ListProductGroupRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/admin/product-groups", testutil.JSON(req))
	}

	t.Run("should return 200 and product groups correctly", func(tt *testing.T) {
		tt.Parallel()
		r := &product.ListProductGroupRequest{}
		ctx, recorder := req(r)
		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		productGroups := []model.ProductGroup{{Name: "Name1"}, {Name: "Name2"}}
		deps.productGroupRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(productGroups, nil)
		deps.productGroupRepo.EXPECT().CountProductGroupWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(2, nil)

		api.ListProductGroups(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual ListProductGroupResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, actual.CountTotal)
		require.Equal(tt, "Name1", actual.Data[0].Name)
	})

	t.Run("should return error when get product groups error", func(tt *testing.T) {
		tt.Parallel()
		r := &product.ListProductGroupRequest{}
		ctx, recorder := req(r)
		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productGroupRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.ProductGroup{}, errors.New("error"))
		deps.productGroupRepo.EXPECT().CountProductGroupWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(2, nil).Times(0)

		api.ListProductGroups(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return error when count product groups error", func(tt *testing.T) {
		tt.Parallel()
		r := &product.ListProductGroupRequest{}
		ctx, recorder := req(r)
		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productGroupRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.ProductGroup{}, nil)
		deps.productGroupRepo.EXPECT().CountProductGroupWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(0, errors.New("error"))

		api.ListProductGroups(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestProductGroupAPI_BulkCreate(t *testing.T) {
	t.Parallel()

	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/upload-product-groups")
		gctx.Body().MultipartForm().File("file", "file.csv", content).String("requestedBy", "worada").Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("should return 200 and create product groups correctly", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Group Name,Priority
P1,1
P2,2`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productGroupRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil)

		api.BulkUploadProductGroup(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual product.BulkUploadProductGroupResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, "P1", actual.Successes[0].Name)
		require.Equal(tt, "P2", actual.Successes[1].Name)
	})

	t.Run("should return 200 and fail all", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Group Name,Priority
P1,failed
P2,failed`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productGroupRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProductGroup(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual product.BulkUploadProductGroupResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Equal(tt, 0, len(actual.Successes))

		failedProducts := []string{
			"P1", "P2",
		}
		assert.Equal(tt, len(failedProducts), len(actual.Failures))
		for index := range failedProducts {
			assert.Equal(tt, failedProducts[index], actual.Failures[index].Name)
		}
	})

	t.Run("should return 200 and success all", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Group Name,Priority
P1,1
P2,2`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productGroupRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil)

		api.BulkUploadProductGroup(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual product.BulkUploadProductGroupResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Equal(tt, 0, len(actual.Failures))

		successDriverIDs := []string{
			"P1", "P2",
		}
		assert.Equal(tt, len(successDriverIDs), len(actual.Successes))
		for index := range successDriverIDs {
			assert.Equal(tt, successDriverIDs[index], actual.Successes[index].Name)
		}
	})

	t.Run("should return an error when upload product group is disabled", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Group Name,Priority
P1,1
P2,2`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPIWithConfig(tt, product.Config{CreateProductGroupEnabled: false})
		defer finish()

		deps.productGroupRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProductGroup(gctx)

		require.Equal(tt, http.StatusNotImplemented, recorder.Code)
	})

	t.Run("should return an error when upload product group file is empty", func(tt *testing.T) {
		tt.Parallel()

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/upload-product-groups")
		gctx, recorder := ctx.GinCtx(), ctx.ResponseRecorder

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productGroupRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProductGroup(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})
	t.Run("should return 500 when priority duplicate", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Group Name,Priority
P1,1
P2,2
P3,1`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productGroupRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProductGroup(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestProductGroupAPI_GetById(t *testing.T) {
	t.Parallel()

	req := func(id string) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/product-groups/%s", id), nil)
	}

	t.Run("should return 200 and product groups correctly", func(tt *testing.T) {
		tt.Parallel()

		id := "fakeId"
		ctx, recorder := req(id)
		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		productGroup := model.ProductGroup{Name: "Name1"}
		deps.productGroupRepo.EXPECT().GetById(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&productGroup, nil)

		api.GetProductGroupById(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual product.ProductGroupResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, "Name1", actual.Name)
	})
}

func TestProductAPI_ListProducts(t *testing.T) {
	t.Parallel()

	req := func(req *product.ListProductRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/admin/products", testutil.JSON(req))
	}

	t.Run("should return 200 and product list correctly", func(tt *testing.T) {
		tt.Parallel()
		r := &product.ListProductRequest{}
		ctx, recorder := req(r)
		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		productGroups := []model.Product{{Name: "Name1"}, {Name: "Name2"}}
		deps.productRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(productGroups, nil)
		deps.productRepo.EXPECT().CountProductWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(2, nil)

		api.ListProducts(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual ListProductResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, actual.CountTotal)
		require.Equal(tt, "Name1", actual.Data[0].Name)
	})

	t.Run("should return error when get product list error", func(tt *testing.T) {
		tt.Parallel()
		r := &product.ListProductRequest{}
		ctx, recorder := req(r)
		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.Product{}, errors.New("error"))
		deps.productRepo.EXPECT().CountProductWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(2, nil).Times(0)

		api.ListProducts(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return error when count product list error", func(tt *testing.T) {
		tt.Parallel()
		r := &product.ListProductRequest{}
		ctx, recorder := req(r)
		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.Product{}, nil)
		deps.productRepo.EXPECT().CountProductWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(0, errors.New("error"))

		api.ListProducts(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestProductAPI_BulkCreate(t *testing.T) {
	t.Parallel()

	bsonID1, _ := primitive.ObjectIDFromHex("6284768ef66bcf12a077bc2b")
	bsonID2, _ := primitive.ObjectIDFromHex("6284768ef66bcf12a077bc2a")
	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/upload-products")
		gctx.Body().MultipartForm().File("file", "file.csv", content).String("requestedBy", "worada").Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("should return 200 and create product list correctly", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Name,SKU,Priority,Product Group ID,Price,Description
P1,SKU_001,1,6284768ef66bcf12a077bc2b,19999,Details
P2,SKU_002,2,6284768ef66bcf12a077bc2a,19999,Details`
		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		productGroups := []model.ProductGroup{
			{ID: bsonID1},
			{ID: bsonID2},
		}
		deps.productGroupRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(productGroups, nil)
		deps.productRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil)

		api.BulkUploadProduct(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual product.BulkUploadProductResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, "P1", actual.Successes[0].Name)
		require.Equal(tt, "P2", actual.Successes[1].Name)
	})

	t.Run("should return 200 and fail all", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Name,SKU,Priority,Product Group ID,Price,Description
P1,SKU_001,failed,6284768ef66bcf12a077bc2b,19999,Details
P2,SKU_002,failed,6284768ef66bcf12a077bc2a,19999,Details`
		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProduct(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual product.BulkUploadProductResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Equal(tt, 0, len(actual.Successes))

		failedProducts := []string{
			"P1", "P2",
		}
		assert.Equal(tt, len(failedProducts), len(actual.Failures))
		for index := range failedProducts {
			assert.Equal(tt, failedProducts[index], actual.Failures[index].Name)
		}
	})

	t.Run("should return 200 and success all", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Name,SKU,Priority,Product Group ID,Price,Description
P1,SKU_001,1,6284768ef66bcf12a077bc2b,19999,Details
P2,SKU_002,1,6284768ef66bcf12a077bc2a,19999,Details`
		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		productGroups := []model.ProductGroup{
			{ID: bsonID1},
			{ID: bsonID2},
		}
		deps.productGroupRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(productGroups, nil)
		deps.productRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil)

		api.BulkUploadProduct(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual product.BulkUploadProductResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)

		successProductName := []string{
			"P1", "P2",
		}

		assert.Equal(tt, len(successProductName), len(actual.Successes))
		for index := range successProductName {
			assert.Equal(tt, successProductName[index], actual.Successes[index].Name)
		}
	})

	t.Run("should return an error when upload product is disabled", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Name,SKU,Priority,Product Group ID,Price,Description
P1,SKU_001,1,6284768ef66bcf12a077bc2b,19999,Details
P2,SKU_002,1,6284768ef66bcf12a077bc2a,19999,Details`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPIWithConfig(tt, product.Config{CreateProductEnabled: false})
		defer finish()

		deps.productRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProduct(gctx)

		require.Equal(tt, http.StatusNotImplemented, recorder.Code)
	})

	t.Run("should return an error when upload product file is empty", func(tt *testing.T) {
		tt.Parallel()

		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/upload-products")
		gctx, recorder := ctx.GinCtx(), ctx.ResponseRecorder

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		deps.productRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProduct(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should return 200 and priority duplicate", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Name,SKU,Priority,Product Group ID,Price,Description
P1,SKU_001,1,6284768ef66bcf12a077bc2b,19999,Details
P2,SKU_002,2,6284768ef66bcf12a077bc2a,19999,Details
P3,SKU_003,1,6284768ef66bcf12a077bc2b,19999,Details`
		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		productGroups := []model.ProductGroup{
			{ID: bsonID1},
			{ID: bsonID2},
		}
		deps.productGroupRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(productGroups, nil)
		deps.productRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProduct(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual product.BulkUploadProductResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Equal(tt, 0, len(actual.Successes))

		failedProducts := []string{
			"P3",
		}
		assert.Equal(tt, len(failedProducts), len(actual.Failures))
		for index := range failedProducts {
			assert.Equal(tt, failedProducts[index], actual.Failures[index].Name)
		}
	})

	t.Run("should return 200 and sku duplicate", func(tt *testing.T) {
		tt.Parallel()

		content := `Product Name,SKU,Priority,Product Group ID,Price,Description
P1,SKU_001,1,6284768ef66bcf12a077bc2b,19999,Details
P2,SKU_002,2,6284768ef66bcf12a077bc2a,19999,Details
P3,SKU_001,1,6284768ef66bcf12a077bc2b,19999,Details`
		gctx, recorder := makeReq(content)

		api, deps, finish := newTestProductAPI(tt)
		defer finish()

		productGroups := []model.ProductGroup{
			{ID: bsonID1},
			{ID: bsonID2},
		}
		deps.productGroupRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(productGroups, nil)
		deps.productRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil).Times(0)

		api.BulkUploadProduct(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual product.BulkUploadProductResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		assert.Equal(tt, 0, len(actual.Successes))

		failedProducts := []string{
			"P3",
		}
		assert.Equal(tt, len(failedProducts), len(actual.Failures))
		for index := range failedProducts {
			assert.Equal(tt, failedProducts[index], actual.Failures[index].Name)
		}
	})
}

func TestProductAPI_UpdateProductGroupDetail(t *testing.T) {
	t.Parallel()

	type jsonUpdate struct {
		Name     string `json:"name"`
		Priority int    `json:"priority"`
	}

	type testData struct {
		targetProductGroupID  string
		jsonBody              jsonUpdate
		RepositoryError       error
		expectedHttpErrorCode int
		isErrorBeforeUpdate   bool
	}

	testTable := map[string]testData{
		"success": {
			targetProductGroupID: primitive.NewObjectID().Hex(),
			jsonBody: jsonUpdate{
				Name:     "Updated Name",
				Priority: 1,
			},
			RepositoryError:       nil,
			expectedHttpErrorCode: http.StatusOK,
		},
		"update_fail": {
			targetProductGroupID: primitive.NewObjectID().Hex(),
			jsonBody: jsonUpdate{
				Name:     "Updated Name",
				Priority: 1,
			},
			RepositoryError:       errors.New("some error"),
			expectedHttpErrorCode: http.StatusInternalServerError,
		},
		"invalid_bson_id": {
			targetProductGroupID: "",
			jsonBody: jsonUpdate{
				Name:     "Updated Name",
				Priority: 1,
			},
			RepositoryError:       errors.New("some error"),
			expectedHttpErrorCode: http.StatusBadRequest,
			isErrorBeforeUpdate:   true,
		},
	}

	makeReq := func(targetProductGroupID string, src jsonUpdate) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/admin/product-groups/%s", targetProductGroupID)
		gctx.SetGinParams(gin.Params{gin.Param{Key: "product_group_id", Value: targetProductGroupID}})
		gctx.Body().JSON(src).Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	for testName, data := range testTable {
		testData := data
		t.Run(testName, func(tt *testing.T) {
			tt.Parallel()
			gctx, recorder := makeReq(testData.targetProductGroupID, testData.jsonBody)
			api, deps, finish := newTestProductAPI(tt)
			defer finish()

			bsonID, _ := primitive.ObjectIDFromHex(testData.targetProductGroupID)

			toUpdateProductGroup := model.ProductGroup{
				ID:        bsonID,
				Name:      testData.jsonBody.Name,
				Priority:  testData.jsonBody.Priority,
				UpdatedBy: "n/a",
			}

			gomockCall := deps.productGroupRepo.EXPECT().
				Update(gomock.Any(), toUpdateProductGroup).
				Return(testData.RepositoryError)
			if testData.isErrorBeforeUpdate {
				gomockCall.Times(0)
			}

			api.UpdateProductGroupDetail(gctx)

			require.Equal(tt, testData.expectedHttpErrorCode, recorder.Code)
		})
	}
}

func TestProductAPI_ExportCSVProducts(tt *testing.T) {
	tt.Parallel()

	makeReq := func(req product.ListProductRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/admin/products-exporter", testutil.JSON(req))
	}

	makeExpectedCSV := func(products []model.Product) bytes.Buffer {
		b := &bytes.Buffer{}
		writer := csv.NewWriter(b)

		_ = writer.Write([]string{
			"Product ID",
			"Product Name",
			"SKU",
			"Priority",
			"Product Group ID",
			"Price",
			"Description",
			"Exported At",
		})
		exportedAt := timeutil.BangkokNow().Format("02/01/2006 15:04:05")
		for _, item := range products {
			_ = writer.Write([]string{
				item.ID.Hex(),
				item.Name,
				item.SKU,
				fmt.Sprint(item.Priority),
				item.ProductGroup.Hex(),
				fmt.Sprint(item.Price),
				item.Description,
				exportedAt,
			})
		}
		writer.Flush()
		return *b
	}

	type TestData struct {
		request            product.ListProductRequest
		expectedMongoQuery persistence.MongoProductQuery
		returnedProducts   []model.Product
	}
	productGroupID := primitive.NewObjectID().Hex()
	productGroupBsonID, _ := primitive.ObjectIDFromHex(productGroupID)
	testMapper := map[string]TestData{
		"no_filter": {
			request:            product.ListProductRequest{},
			expectedMongoQuery: persistence.MongoProductQuery{},
			returnedProducts: []model.Product{
				{
					ID:           primitive.NewObjectID(),
					SKU:          "SKU_01",
					ProductGroup: primitive.NewObjectID(),
				},
			},
		},
		"add_filter": {
			request: product.ListProductRequest{
				PrioritySort: "ASC",
				ProductGroup: productGroupID,
				MinimumPrice: 100,
				MaximumPrice: 1000,
			},
			expectedMongoQuery: persistence.MongoProductQuery{
				PrioritySort: "ASC",
				ProductGroup: productGroupID,
				MinimumPrice: 100,
				MaximumPrice: 1000,
			},
			returnedProducts: []model.Product{
				{
					Name:         "Product_01",
					Priority:     1,
					ProductGroup: productGroupBsonID,
					Price:        100,
				},

				{
					Name:         "Product_02",
					Priority:     2,
					ProductGroup: productGroupBsonID,
					Price:        500,
				},
				{
					Name:         "Product_03",
					Priority:     3,
					ProductGroup: productGroupBsonID,
					Price:        1000,
				},
			},
		},
	}

	for testName, data := range testMapper {
		testData := data
		tt.Run(testName, func(t *testing.T) {
			t.Parallel()
			api, deps, finish := newTestProductAPI(tt)
			defer finish()
			deps.productRepo.EXPECT().
				FindWithQueryAndSort(gomock.Any(), &productQueryMatcher{src: testData.expectedMongoQuery}, 0, gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
				Return(testData.returnedProducts, nil)

			gctx, recorder := makeReq(testData.request)
			gctx.Request.Header.Set("Content-Type", "application/json")
			api.ExportCSVProducts(gctx)

			require.Equal(tt, http.StatusOK, recorder.Code)
			csvBytes := makeExpectedCSV(testData.returnedProducts)
			require.Equal(tt, csvBytes.String(), recorder.Body.String())
		})
	}
}

type productQueryMatcher struct {
	src persistence.MongoProductQuery
}

func (iqm *productQueryMatcher) Matches(x interface{}) bool {
	convertedSrc, ok := x.(*persistence.MongoProductQuery)
	if !ok {
		return false
	}

	return convertedSrc.Name == iqm.src.Name &&
		convertedSrc.SKU == iqm.src.SKU &&
		convertedSrc.PrioritySort == iqm.src.PrioritySort &&
		convertedSrc.ProductGroup == iqm.src.ProductGroup &&
		convertedSrc.MinimumPrice == iqm.src.MinimumPrice &&
		convertedSrc.MaximumPrice == iqm.src.MaximumPrice
}

func (iqm *productQueryMatcher) String() string {
	return fmt.Sprint(&iqm.src)
}

func TestProductAPI_BulkUpdate(t *testing.T) {
	t.Parallel()

	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/admin/update-products")
		gctx.Body().MultipartForm().File("file", "file.csv", content).String("requestedBy", "worada").Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	type TestData struct {
		csvContent           string
		existedProductGroups []model.ProductGroup
		existedProducts      []model.Product
		successProductIDs    []string
		failedProductIDs     []string
	}

	productID1, _ := primitive.ObjectIDFromHex("0123456789101112131415a1")
	productID2, _ := primitive.ObjectIDFromHex("0123456789101112131415a2")
	productGroupID1, _ := primitive.ObjectIDFromHex("0123456789101112131415b1")
	productGroupID2, _ := primitive.ObjectIDFromHex("0123456789101112131415b2")

	testTable := map[string]TestData{
		"success": {
			csvContent: `Product ID,Product Name,SKU,Priority,Product Group ID,Price,Description
0123456789101112131415a1,Name,SKU_X1,1,0123456789101112131415b1,19999,Details
0123456789101112131415a2,Name,SKU_X2,1,0123456789101112131415b2,19999,Details`,
			existedProductGroups: []model.ProductGroup{
				{
					ID: productGroupID1,
				},
				{
					ID: productGroupID2,
				},
			},
			existedProducts: []model.Product{
				{
					ID:           productID1,
					ProductGroup: productGroupID1,
				},
				{
					ID:           productID2,
					ProductGroup: productGroupID2,
				},
			},
			successProductIDs: []string{
				productID1.Hex(),
				productID2.Hex(),
			},
		},
		"1_fail_when_not_found_product_group": {
			csvContent: `Product ID,Product Name,SKU,Priority,Product Group ID,Price,Description
0123456789101112131415a1,Name,SKU_X1,1,0123456789101112131415b1,19999,Details
0123456789101112131415a2,Name,SKU_X2,1,0123456789101112131415b2,19999,Details`,
			existedProductGroups: []model.ProductGroup{
				{
					ID: productGroupID1,
				},
			},
			existedProducts: []model.Product{
				{
					ID:           productID1,
					ProductGroup: productGroupID1,
				},
				{
					ID:           productID2,
					ProductGroup: productGroupID2,
				},
			},
			failedProductIDs: []string{
				productID2.Hex(),
			},
		},
		"1_fail_when_not_found_product": {
			csvContent: `Product ID,Product Name,SKU,Priority,Product Group ID,Price,Description
0123456789101112131415a1,Name,SKU_X1,1,0123456789101112131415b1,19999,Details
0123456789101112131415a2,Name,SKU_X2,1,0123456789101112131415b2,19999,Details`,
			existedProductGroups: []model.ProductGroup{
				{
					ID: productGroupID1,
				},
				{
					ID: productGroupID2,
				},
			},
			existedProducts: []model.Product{
				{
					ID:           productID2,
					ProductGroup: productGroupID2,
				},
			},
			failedProductIDs: []string{
				productID1.Hex(),
			},
		},
		"1_fail_when_sku_duplicate": {
			csvContent: `Product ID,Product Name,SKU,Priority,Product Group ID,Price,Description
0123456789101112131415a1,Name,SKU_X1,1,0123456789101112131415b1,19999,Details
0123456789101112131415a2,Name,SKU_X1,1,0123456789101112131415b2,19999,Details`,
			existedProductGroups: []model.ProductGroup{
				{
					ID: productGroupID1,
				},
				{
					ID: productGroupID2,
				},
			},
			existedProducts: []model.Product{
				{
					ID:           productID1,
					ProductGroup: productGroupID1,
				},
				{
					ID:           productID2,
					ProductGroup: productGroupID2,
				},
			},
			failedProductIDs: []string{
				productID2.Hex(),
			},
		},
		"1_fail_when_priority_duplicate": {
			csvContent: `Product ID,Product Name,SKU,Priority,Product Group ID,Price,Description
0123456789101112131415a1,Name,SKU_X1,1,0123456789101112131415b1,19999,Details
0123456789101112131415a2,Name,SKU_X2,1,0123456789101112131415a2,19999,Details`,
			existedProductGroups: []model.ProductGroup{
				{
					ID: productGroupID1,
				},
			},
			existedProducts: []model.Product{
				{
					ID:           productID1,
					ProductGroup: productGroupID1,
				},
				{
					ID:           productID2,
					ProductGroup: productGroupID1,
				},
			},
			failedProductIDs: []string{
				productID2.Hex(),
			},
		},
	}
	for testName, data := range testTable {
		testData := data
		t.Run(testName, func(tt *testing.T) {
			tt.Parallel()
			gctx, recorder := makeReq(testData.csvContent)

			api, deps, finish := newTestProductAPI(tt)
			defer finish()
			deps.productGroupRepo.EXPECT().
				FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
				Return(testData.existedProductGroups, nil)

			deps.productRepo.EXPECT().
				FindByIDsAndLookupInstallment(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
				Return(testData.existedProducts, nil)

			if testData.successProductIDs != nil {

				deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

				deps.productRepo.EXPECT().
					UpdateAll(gomock.Any(), gomock.Any()).Return(nil)

				deps.installmentRepo.EXPECT().
					ReplaceAll(gomock.Any(), gomock.Any()).Return(nil)
			}
			api.BulkUpdate(gctx)

			var actual product.BulkUploadProductResponse
			testutil.DecodeJSON(tt, recorder.Body, &actual)
			assert.Equal(tt, len(testData.successProductIDs), len(actual.Successes))

			for index := range testData.successProductIDs {
				assert.Equal(tt, testData.successProductIDs[index], actual.Successes[index].ID)
			}

			assert.Equal(tt, len(testData.failedProductIDs), len(actual.Failures))
			for index := range testData.failedProductIDs {
				assert.Equal(tt, testData.failedProductIDs[index], actual.Failures[index].ID)
			}
		})
	}
}

func TestProductAPI_GetProductById(t *testing.T) {
	t.Parallel()
	productID1, _ := primitive.ObjectIDFromHex("0123456789101112131415a1")
	productGroupID1, _ := primitive.ObjectIDFromHex("0123456789101112131415b1")

	t.Run("Should return 200 correctly", func(tt *testing.T) {
		api, deps, finish := newTestProductAPI(tt)
		defer finish()
		productModel := &model.Product{
			ID:           productID1,
			ProductGroup: productGroupID1,
			SKU:          "sku",
		}
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/products/%v", productID1.Hex()), nil)

		deps.productRepo.EXPECT().FindByIDAndLookupInstallment(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(productModel, nil)

		api.GetProductById(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)

		var resp product.ProductResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, resp.ID, productID1.Hex())
	})

	t.Run("Should return 404 when product not found", func(tt *testing.T) {
		api, deps, finish := newTestProductAPI(tt)
		defer finish()
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/products/%v", productID1.Hex()), nil)

		deps.productRepo.EXPECT().FindByIDAndLookupInstallment(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(nil, repository.ErrNotFound)

		api.GetProductById(ctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("Should return 500 when get product error", func(tt *testing.T) {
		api, deps, finish := newTestProductAPI(tt)
		defer finish()
		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/products/%v", productID1.Hex()), nil)

		deps.productRepo.EXPECT().FindByIDAndLookupInstallment(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(nil, errors.New("error"))

		api.GetProductById(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestProductAPI_UpdateProductById(t *testing.T) {
	t.Parallel()

	productID1, _ := primitive.ObjectIDFromHex("0123456789101112131415a1")
	productGroupID1, _ := primitive.ObjectIDFromHex("0123456789101112131415b1")
	productGroupID2, _ := primitive.ObjectIDFromHex("0123456789101112131415b2")
	makeReq := func(id string, req *product.ProductRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/products/%s", id), testutil.JSON(req))
	}

	type TestData struct {
		productRequest           product.ProductRequest
		existsProduct            *model.Product
		txnHelperErr             error
		updateProductErr         error
		replaceAllInstallmentErr error
	}

	testTable := map[string]TestData{
		"update_product_and_installments_successs": {
			productRequest: product.ProductRequest{
				Name:         "product 1",
				SKU:          "new sku",
				Priority:     2,
				ProductGroup: productGroupID2,
				Price:        1000,
				Description:  "new description",
			},
			existsProduct: &model.Product{
				ID:           productID1,
				Name:         "product 1",
				SKU:          "old sku",
				Priority:     1,
				ProductGroup: productGroupID1,
				Price:        100,
				Description:  "old description",
				Installments: []model.Installment{
					{
						SKU: "old sku",
					},
					{
						SKU: "old sku",
					},
				},
			},
		},
		"update_product_error": {
			productRequest: product.ProductRequest{
				Name:         "product 1",
				SKU:          "new sku",
				Priority:     2,
				ProductGroup: productGroupID2,
				Price:        1000,
				Description:  "new description",
			},
			existsProduct: &model.Product{
				ID:           productID1,
				Name:         "product 1",
				SKU:          "old sku",
				Priority:     1,
				ProductGroup: productGroupID1,
				Price:        100,
				Description:  "old description",
				Installments: []model.Installment{
					{SKU: "old sku"},
				},
			},
			txnHelperErr:     errors.New("error"),
			updateProductErr: errors.New("error"),
		},
		"replace_all_installment_error": {
			productRequest: product.ProductRequest{
				Name:         "product 1",
				SKU:          "new sku",
				Priority:     2,
				ProductGroup: productGroupID2,
				Price:        1000,
				Description:  "new description",
			},
			existsProduct: &model.Product{
				ID:           productID1,
				Name:         "product 1",
				SKU:          "old sku",
				Priority:     1,
				ProductGroup: productGroupID1,
				Price:        100,
				Description:  "old description",
				Installments: []model.Installment{
					{SKU: "old sku"},
				},
			},
			txnHelperErr:             errors.New("error"),
			replaceAllInstallmentErr: errors.New("error"),
		},
	}
	for testName, data := range testTable {
		testData := data
		t.Run(testName, func(tt *testing.T) {
			tt.Parallel()

			gctx, recorder := makeReq(testData.existsProduct.ID.Hex(), &testData.productRequest)
			api, deps, finish := newTestProductAPI(tt)
			defer finish()
			deps.productGroupRepo.EXPECT().
				IsExists(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
				Return(true)

			deps.productRepo.EXPECT().
				FindByIDAndLookupInstallment(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
				Return(testData.existsProduct, nil)

			deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			if testData.txnHelperErr == nil {
				deps.productRepo.EXPECT().
					Update(gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, updateProduct *model.Product) error {
						require.Equal(tt, testData.productRequest.SKU, updateProduct.SKU)
						require.Equal(tt, testData.productRequest.Priority, updateProduct.Priority)
						require.Equal(tt, testData.productRequest.ProductGroup, updateProduct.ProductGroup)
						require.Equal(tt, testData.productRequest.Price, updateProduct.Price)
						require.Equal(tt, testData.productRequest.Description, updateProduct.Description)
						return nil
					})

				if testData.existsProduct.Installments != nil {
					deps.installmentRepo.EXPECT().
						ReplaceAll(gomock.Any(), gomock.Any()).
						DoAndReturn(func(ctx context.Context, updateInstallments []model.Installment) error {
							for _, installment := range updateInstallments {
								require.Equal(tt, testData.productRequest.SKU, installment.SKU)
							}
							return nil
						})
				}
			} else {
				deps.productRepo.EXPECT().
					Update(gomock.Any(), gomock.Any()).Return(testData.updateProductErr)
				if testData.updateProductErr == nil {
					deps.installmentRepo.EXPECT().
						ReplaceAll(gomock.Any(), gomock.Any()).Return(testData.replaceAllInstallmentErr)
				}
			}

			api.UpdateProductById(gctx)

			if testData.txnHelperErr == nil {
				require.Equal(t, http.StatusOK, recorder.Code)
			} else {
				require.Equal(t, http.StatusInternalServerError, recorder.Code)
			}
		})
	}
}

type ListProductGroupResponse struct {
	CountTotal int                            `json:"countTotal"`
	Data       []product.ProductGroupResponse `json:"data"`
}

type ListProductResponse struct {
	CountTotal int                            `json:"countTotal"`
	Data       []product.ProductGroupResponse `json:"data"`
}

type productAPIDeps struct {
	productRepo      *mock_repository.MockProductRepository
	productGroupRepo *mock_repository.MockProductGroupRepository
	auditLog         *mock_repository.MockAuditLogRepository
	installmentRepo  *mock_repository.MockInstallmentRepository
	txnHelper        *mock_transaction.MockTxnHelper
}

func newTestProductAPI(t gomock.TestReporter) (*product.ProductAPI, *productAPIDeps, func()) {
	config := product.Config{
		CreateProductGroupEnabled: true,
		CreateProductEnabled:      true,
		UpdateProductEnabled:      true,
	}
	return newTestProductAPIWithConfig(t, config)
}

func newTestProductAPIWithConfig(t gomock.TestReporter, cfg product.Config) (*product.ProductAPI, *productAPIDeps, func()) {
	ctrl := gomock.NewController(t)
	deps := &productAPIDeps{
		productRepo:      mock_repository.NewMockProductRepository(ctrl),
		productGroupRepo: mock_repository.NewMockProductGroupRepository(ctrl),
		installmentRepo:  mock_repository.NewMockInstallmentRepository(ctrl),
		txnHelper:        mock_transaction.NewMockTxnHelper(ctrl),
		auditLog:         mock_repository.NewMockAuditLogRepository(ctrl),
	}
	return product.ProvideProductAPI(cfg, deps.productRepo, deps.productGroupRepo, deps.installmentRepo, deps.txnHelper), deps, func() {
		ctrl.Finish()
	}
}
