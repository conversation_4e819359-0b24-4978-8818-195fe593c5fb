package order

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	formServicePb "git.wndv.co/go/proto/lineman/form_service/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestDeliveryPortalAPI_Continue(t *testing.T) {
	t.Parallel()
	req := func(req *ContinueRequest, serviceType model.Service) (*gin.Context, *httptest.ResponseRecorder) {
		orderID := "LMF-11111111"
		if serviceType == domainModel.ServiceBike {
			orderID = "LMT-11111111"
		}
		ctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v2/delivery/orders/%v/routes/1", orderID), testutil.JSON(req))
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
			{
				Key:   "pos",
				Value: "1",
			},
		}
		return ctx, recorder
	}

	t.Run("remove qr payment pause when continue with qr payment pause", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Driver: "isme",
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodQRPromptPay,
							},
						},
						Pauses: map[domainModel.Pause]bool{
							domainModel.PauseQRPayment: true,
						},
					},
				},
			},
			Status: domainModel.StatusArrivedAt,
		}

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).Return(false, nil).Times(4)
		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).Return(true, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(ord, nil)

		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order, opts ...repository.Option) error {
				require.Equal(tt, 0, len(ord.Routes[1].Pauses))
				return nil
			})

		deps.notifier.EXPECT().Notify(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, drv []string, evt map[string]string, opts ...service.NotifyOption) error {
			require.Equal(tt, string(service.ActionQRPaid), evt["action"])
			require.Equal(tt, "[QR พร้อมเพย์] ลูกค้าชำระเงินแล้ว", evt["title"])
			require.Equal(tt, "ยืนยันการส่งอาหารได้เลย ไม่ต้องรับเงินสดจากลูกค้า", evt["body"])
			return nil
		})
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any())

		ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceFood)
		fp.Continue(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("not remove qr payment pause when continue with not qr payment pause", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Driver: "isme",
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodQRPromptPay,
							},
						},
						Pauses: map[domainModel.Pause]bool{
							domainModel.PauseQRPayment: true,
							domainModel.PauseCheckout:  true,
						},
					},
				},
			},
			Status: domainModel.StatusArrivedAt,
		}

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).Return(true, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(ord, nil)

		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order, opts ...repository.Option) error {
				require.Equal(tt, 1, len(ord.Routes[1].Pauses))
				require.Equal(tt, true, ord.Routes[1].Pauses[domainModel.PauseQRPayment])
				return nil
			})

		deps.notifier.EXPECT().Notify(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, drv []string, evt map[string]string, opts ...service.NotifyOption) error {
			require.Equal(tt, string(service.ActionOrderUpdated), evt["action"])
			return nil
		})
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any())

		ctx, recorder := req(&ContinueRequest{Pause: domainModel.PauseCheckout}, model.ServiceFood)
		fp.Continue(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("not remove qr payment pause when lock error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).Return(false, nil).Times(5)

		ctx, recorder := req(&ContinueRequest{Pause: domainModel.PauseCheckout}, model.ServiceFood)
		fp.Continue(ctx)
		require.Equal(tt, http.StatusLocked, recorder.Code)
	})

	t.Run("remove qr payment pause when continue with qr payment pause for messenger case", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Driver: "isme",
			Quote: domainModel.Quote{
				ServiceType: domainModel.ServiceMessenger,
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodQRPromptPay,
							},
						},
						Pauses: map[domainModel.Pause]bool{
							domainModel.PauseQRPayment: true,
						},
					},
				},
			},
			Status: domainModel.StatusArrivedAt,
		}

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).Return(true, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(ord, nil)

		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order, opts ...repository.Option) error {
				require.Equal(tt, 0, len(ord.Routes[1].Pauses))
				return nil
			})

		deps.notifier.EXPECT().Notify(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, drv []string, evt map[string]string, opts ...service.NotifyOption) error {
			require.Equal(tt, string(service.ActionQRPaid), evt["action"])
			require.Equal(tt, "[เก็บค่าสินค้าปลายทาง] ผู้ส่งยืนยันการชำระเงินแล้ว", evt["title"])
			require.Equal(tt, "ยืนยันการจัดส่งและดำเนินการต่อได้เลย", evt["body"])
			return nil
		})
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any())

		ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceFood)
		fp.Continue(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("display notification with tip when the order already tip", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Status:  model.StatusDriverArrived,
			OrderID: "LMF-11111111",
			Driver:  "isme",
			Quote: domainModel.Quote{
				ServiceType: model.ServiceFood,
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodQRPromptPay,
							},
						},
						Pauses: map[domainModel.Pause]bool{
							domainModel.PauseQRPayment: true,
						},
					},
				},
			},
			TipAmount: 100,
			Tips: []model.TipRecord{
				{
					ID:          "TIP_ID",
					Amount:      100,
					OrderStatus: string(model.StatusDriverArrived),
					CreatedAt:   timeutil.BangkokNow(),
				},
			},
		}

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).Return(true, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(ord, nil)

		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order, opts ...repository.Option) error {
				require.Equal(tt, 0, len(ord.Routes[1].Pauses))
				return nil
			})

		deps.notifier.EXPECT().Notify(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, drv []string, evt map[string]string, opts ...service.NotifyOption) error {
			require.Equal(tt, string(service.ActionQRPaid), evt["action"])
			require.Equal(tt, "[QR พร้อมเพย์] ลูกค้าได้ชำระเงินและให้ทิปคุณเพิ่ม", evt["title"])
			// got order id from gin context
			require.Equal(tt, "ลูกค้าได้ชำระเงินและให้ทิปคุณเพิ่ม จากคำสั่งซื้อ LMF-11111111 คุณจะได้รับทิปเต็มจำนวนเข้าวอลเล็ตหลังจบงาน", evt["body"])
			return nil
		})
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any())

		ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceFood)
		fp.Continue(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("remove qr payment pause when continue with qr payment pause with reason SLIP_UPLOADED", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Driver: "isme",
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodQRPromptPay,
							},
						},
						Pauses: map[domainModel.Pause]bool{
							domainModel.PauseQRPayment: true,
						},
					},
				},
			},
			Status: domainModel.StatusArrivedAt,
		}

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).Return(true, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(ord, nil)

		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order, opts ...repository.Option) error {
				require.Equal(tt, 0, len(ord.Routes[1].Pauses))
				return nil
			})

		deps.notifier.EXPECT().Notify(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(ctx context.Context, drv []string, evt map[string]string, opts ...service.NotifyOption) error {
			require.Equal(tt, string(service.ActionSlipUploaded), evt["action"])
			require.Equal(tt, "[QR พร้อมเพย์] ลูกค้าชำระเงินแล้ว", evt["title"])
			require.Equal(tt, "ลูกค้าได้ชำระเงินผ่าน QR พร้อมเพย์ จากคำสั่งชื้อ LMF-11111111 ยืนยันการส่งอาหารได้เลย ไม่ต้องรับเงินสดจากลูกค้า", evt["body"])
			return nil
		})
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any())

		ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT", Reason: model.ContinueReasonSlipUploaded}, model.ServiceFood)
		fp.Continue(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("remove qr payment pause on bike service", func(tt *testing.T) {
		tt.Parallel()
		mockOrder := func(status domainModel.Status, ppInfo model.QRPromptPayInfo) *model.Order {
			ord := &domainModel.Order{
				Driver: "isme",
				Quote: domainModel.Quote{
					ServiceType: domainModel.ServiceBike,
					PayAtStop:   1,
					Routes: []domainModel.Stop{
						{},
						{
							Pauses: map[domainModel.Pause]bool{
								domainModel.PauseQRPayment: true,
							},
						},
					},
				},
				Status: status,
			}

			ord.Routes[ord.PayAtStop].PriceSummary.DeliveryFee.QRPromptPayInfo = ppInfo

			return ord
		}

		expectPauseQRRemove := func(tt *testing.T, deps *foodProviderDeps, ord *model.Order, err error, expectedContinueReason model.ContinueReason, expectedStatus model.QRPromptPayStatus) {
			deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(ord, nil)

			deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, err)

			deps.orderService.EXPECT().SetQRPromptPayInfo(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, order model.Order, qrPromptPayInfo model.QRPromptPayInfo, opts ...model.Option) error {
				require.Equal(tt, expectedStatus, qrPromptPayInfo.Status, "should set qr status to resolved by user")
				require.Equal(tt, true, qrPromptPayInfo.IsUserResolveQR, "should set is user resolve qr to true")
				require.Zero(tt, ord.Routes[1].Pauses[model.PauseQRPayment], "should remove qr pause flag")
				require.Equal(tt, expectedContinueReason, ord.Routes[1].ContinueReason, "should set continue reason")
				return nil
			})
		}

		expectProcessFee := func(_ *testing.T, deps *foodProviderDeps, err error) {
			deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{}, nil)
			deps.tripServices.EXPECT().ProcessPendingFee(gomock.Any(), gomock.Any(), gomock.Any()).Return(err)
		}

		expectNotification := func(tt *testing.T, deps *foodProviderDeps, action service.ActionKind) {
			deps.notifier.EXPECT().Notify(
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
			).DoAndReturn(func(ctx context.Context, drv []string, evt map[string]string, opts ...service.NotifyOption) error {
				require.Equal(tt, string(action), evt["action"])
				require.Equal(tt, "[QR พร้อมเพย์] ผู้โดยสารชำระเงินแล้ว", evt["title"])
				require.Equal(tt, "ยืนยันการส่งผู้โดยสารได้เลย ไม่ต้องรับเงินสด", evt["body"])
				return nil
			})
		}

		expectCallFormService := func(_ *testing.T, deps *foodProviderDeps, ord model.Order, err error) {
			deps.formService.EXPECT().UpdateFormStatus(gomock.Any(), ord.PriceSummary().DeliveryFee.QRPromptPayInfo.FormID, formServicePb.FormStatus_FORM_STATUS_APPROVED).Return(err)
			return
		}

		tt.Run("when arrived at destination", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			ord := mockOrder(domainModel.StatusArrivedAt, domainModel.QRPromptPayInfo{
				Status:          domainModel.QRPromptPayStatusWaitingForPayment,
				IsUserResolveQR: false,
				FormID:          "",
			})

			expectPauseQRRemove(tt, deps, ord, nil, "", model.QRPromptPayStatusResolvedByUser)
			expectNotification(tt, deps, service.ActionQRPaid)

			ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceBike)
			fp.Continue(ctx)
			require.Equal(tt, http.StatusOK, recorder.Code)
		})

		tt.Run("when completed", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			ord := mockOrder(domainModel.StatusCompleted, domainModel.QRPromptPayInfo{
				Status:          domainModel.QRPromptPayStatusWaitingForPayment,
				IsUserResolveQR: false,
				FormID:          "",
			})

			expectPauseQRRemove(tt, deps, ord, nil, "", model.QRPromptPayStatusResolvedByUser)
			expectProcessFee(tt, deps, nil)
			expectNotification(tt, deps, service.ActionQRPaid)

			ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceBike)
			fp.Continue(ctx)
			require.Equal(tt, http.StatusOK, recorder.Code)
		})

		tt.Run("when completed with slip uploaded", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			ord := mockOrder(domainModel.StatusCompleted, domainModel.QRPromptPayInfo{
				Status:          domainModel.QRPromptPayStatusWaitingForPayment,
				IsUserResolveQR: false,
				FormID:          "",
			})

			expectPauseQRRemove(tt, deps, ord, nil, model.ContinueReasonSlipUploaded, model.QRPromptPayStatusResolvedByUser)
			expectProcessFee(tt, deps, nil)
			expectNotification(tt, deps, service.ActionSlipUploaded)

			ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT", Reason: model.ContinueReasonSlipUploaded}, model.ServiceBike)
			fp.Continue(ctx)
			require.Equal(tt, http.StatusOK, recorder.Code)
		})

		tt.Run("when completed with resolved by admin", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			ord := mockOrder(domainModel.StatusCompleted, domainModel.QRPromptPayInfo{
				Status:          domainModel.QRPromptPayStatusResolvedByAdmin,
				IsUserResolveQR: false,
				FormID:          "",
			})

			expectPauseQRRemove(tt, deps, ord, nil, "", model.QRPromptPayStatusResolvedByAdmin)
			expectNotification(tt, deps, service.ActionQRPaid)

			ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceBike)
			fp.Continue(ctx)
			require.Equal(tt, http.StatusOK, recorder.Code)
		})

		tt.Run("when completed with error", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			ord := mockOrder(domainModel.StatusCompleted, domainModel.QRPromptPayInfo{
				Status:          domainModel.QRPromptPayStatusWaitingForPayment,
				IsUserResolveQR: false,
				FormID:          "",
			})

			expectPauseQRRemove(tt, deps, ord, errors.New("unable to process fee"), "", model.QRPromptPayStatusResolvedByUser)
			expectProcessFee(tt, deps, errors.New("unable to process fee"))

			ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceBike)
			fp.Continue(ctx)
			require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		})

		tt.Run("when completed with update form", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			ord := mockOrder(domainModel.StatusCompleted, domainModel.QRPromptPayInfo{
				Status:          domainModel.QRPromptPayStatusWaitingForPayment,
				IsUserResolveQR: false,
				FormID:          "FORM_ID",
			})

			expectPauseQRRemove(tt, deps, ord, nil, "", model.QRPromptPayStatusResolvedByUser)
			expectProcessFee(tt, deps, nil)
			expectCallFormService(tt, deps, *ord, nil)
			expectNotification(tt, deps, service.ActionQRPaid)

			ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceBike)
			fp.Continue(ctx)
			require.Equal(tt, http.StatusOK, recorder.Code)
		})

		tt.Run("when completed with update form - form return some error", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			ord := mockOrder(domainModel.StatusCompleted, domainModel.QRPromptPayInfo{
				Status:          domainModel.QRPromptPayStatusWaitingForPayment,
				IsUserResolveQR: false,
				FormID:          "FORM_ID",
			})

			expectPauseQRRemove(tt, deps, ord, nil, "", model.QRPromptPayStatusResolvedByUser)
			expectProcessFee(tt, deps, nil)
			expectCallFormService(tt, deps, *ord, ErrMocked)
			expectNotification(tt, deps, service.ActionQRPaid)

			ctx, recorder := req(&ContinueRequest{Pause: "WAITING_QR_PAYMENT"}, model.ServiceBike)
			fp.Continue(ctx)
			require.Equal(tt, http.StatusOK, recorder.Code)
		})
	})
}

func TestEvaluateMartItemsWithDefaultGroup(t *testing.T) {
	t.Run("with happy case keywords", func(t *testing.T) {
		keywords := []string{"   ", "โปร", "pro"}

		expectedItems := []model.Item{
			{
				Groups:       []string{},
				DefaultGroup: "อื่น ๆ",
			},
			{
				Groups:       []string{"โปรโมชั่น"},
				DefaultGroup: "โปรโมชั่น",
			},
			{
				Groups:       []string{"โปรโมชั่น", "Promotion"},
				DefaultGroup: "โปรโมชั่น",
			},
			{
				Groups:       []string{"PROMOTION", "โปรโมชั่น"},
				DefaultGroup: "PROMOTION",
			},
			{
				Groups:       []string{"โปรหนึ่งแถมหนึ่ง", "เนื้อสัตว์"},
				DefaultGroup: "เนื้อสัตว์",
			},
			{
				Groups:       []string{"โปรหนึ่งแถมหนึ่ง", "เครื่องดื่ม", "เนื้อสัตว์"},
				DefaultGroup: "เครื่องดื่ม",
			},
			{
				Groups:       []string{"เนื้อสัตว์"},
				DefaultGroup: "เนื้อสัตว์",
			},
			{
				Groups:       []string{"เนื้อสัตว์", "โปรหนึ่งแถมหนึ่ง", "เครื่องดื่ม"},
				DefaultGroup: "เนื้อสัตว์",
			},
		}

		// clear default group to re-evaluate
		items := make([]model.Item, len(expectedItems))
		for i, item := range expectedItems {
			items[i] = item
			items[i].DefaultGroup = ""
		}

		items = martItemsWithDefaultGroup(items, keywords)
		for i := 0; i < len(items); i++ {
			require.Equal(t, expectedItems[i], items[i], fmt.Sprintf("index: %d", i))
		}
	})

	t.Run("with empty keywords", func(t *testing.T) {
		keywords := []string{"   "}

		expectedItems := []model.Item{
			{
				Groups:       []string{},
				DefaultGroup: "อื่น ๆ",
			},
			{
				Groups:       []string{"โปรโมชั่น"},
				DefaultGroup: "โปรโมชั่น",
			},
			{
				Groups:       []string{"PROMOTION", "โปรโมชั่น"},
				DefaultGroup: "PROMOTION",
			},
			{
				Groups:       []string{"โปรหนึ่งแถมหนึ่ง", "เนื้อสัตว์"},
				DefaultGroup: "โปรหนึ่งแถมหนึ่ง",
			},
		}

		// clear default group to re-evaluate
		items := make([]model.Item, len(expectedItems))
		for i, item := range expectedItems {
			items[i] = item
			items[i].DefaultGroup = ""
		}

		items = martItemsWithDefaultGroup(items, keywords)
		for i := 0; i < len(items); i++ {
			require.Equal(t, expectedItems[i], items[i], fmt.Sprintf("index: %d", i))
		}
	})
}

func TestEvaluateMartRMSItemsWithoutDirtyFields(t *testing.T) {
	items := []model.Item{
		{
			Image:            "https://test-image", // dirty
			FullPricePerItem: 299,                  // dirty
			PricePerItem:     298,
		},
	}

	expectedItems := []model.Item{
		{
			Image:            "",
			FullPricePerItem: 298,
			PricePerItem:     298,
		},
	}

	items = martRMSItemsWithoutDirtyFields(items)
	for i := 0; i < len(items); i++ {
		require.Equal(t, expectedItems[i], items[i], fmt.Sprintf("index: %d", i))
	}
}
