package order

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/wrapperspb"

	transportationV1 "git.wndv.co/go/proto/lineman/transportation_price_intervention/v1"
	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/delivery-service/pkg/client"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fraudadvisor"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model/mock_model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestBikeQuoteHandlerInternal(t *testing.T) {
	t.Parallel()

	t.Run("should be able to create non-existing quote", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: model.ServiceBike,
			QuoteID:     "LMFQ-1231541",
			Routes: []model.Stop{
				{
					CollectPayment: false,
					Location: model.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: true,
					Location: model.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: model.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
		}
		expectedRegion := "UTHAI_THANI"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType model.Service, area string, loc service.Location) ([]model.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []model.RegionCode{model.RegionCode(expectedRegion)}, nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 100.54702667857362, Lng: 13.743937518724506}, mapservice.Location{Lat: 100.5072724, Lng: 13.7487528}, gomock.Any()).
			Return(&model.MapRoute{Distance: 5930}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewDeliveryFee(30.0, 0.0, 5930))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

		resp, err := testapi.quote(context.Background(), q, "UTHAI_THANI")
		require.NoError(tt, err)
		require.Equal(tt, types.Distance(5930), resp.Distance, "should be able to calculate distance from manmap and response back")
	})

	t.Run("should throw error when collect payment is required at least one stop", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: model.ServiceBike,
			QuoteID:     "LMFQ-1231541",
			Routes: []model.Stop{
				{
					CollectPayment: false,
					Location: model.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: false,
					Location: model.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			PayAtStop: 3,
			DeliveryFee: model.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
		}
		expectedRegion := "UTHAI_THANI"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
			Return([]model.RegionCode{model.RegionCode(expectedRegion)}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 100.54702667857362, Lng: 13.743937518724506}, mapservice.Location{Lat: 100.5072724, Lng: 13.7487528}, gomock.Any()).
			Return(&model.MapRoute{Distance: 5930}, nil, nil)

		defaultScheme := model.NewLegacyDeliveryFeeSettingScheme(nil, types.NewMoney(40))
		calculator := model.NewDeliveryFeeSetting("<delivery-fee-setting-1>", model.ServiceBike, model.RegionCode(expectedRegion), defaultScheme)
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)

		_, err := testapi.quote(context.Background(), q, "UTHAI_THANI")
		require.EqualError(tt, err, "collect payment is required at one stop")
	})

	t.Run("should be able to calculate base fee and send quote correctly", func(t *testing.T) {
		t.Run("with collect payment at route 0 - principal", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			var (
				expectedBase        = 40.0
				expectedRoad        = 10.0
				expectedDeliveryFee = 0.0
				expectedTotal       = 0.0
			)

			qreq := &QuoteRequest{
				QuoteID:     "LMFQ-1231541",
				UserID:      "U1231542",
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{
						ID:   "125891ka",
						Name: "Jim Burger",
						Location: model.Location{
							Lat: 100.54702667857362,
							Lng: 13.743937518724506,
						},
						CollectPayment: true,
						Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
						Phones:         []string{"0891231234"},
						PickingItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						Memo: "4th floor, DedicatedZone Eden.",
					},
					{
						ID:   "",
						Name: "Uncle Tob",
						Location: model.Location{
							Lat: 100.5072724,
							Lng: 13.7487528,
						},
						CollectPayment: false,
						Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
						DeliveryItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						ItemsPrice: 150,
						Memo:       "Near Tesco lotus",
					},
				},
				PaymentMethod:     model.PaymentMethodCash,
				RevenueAgentModel: false,
				DeliveryFee: model.DeliveryFeeSummary{
					AdditionalFee: map[string]float64{},
					Discounts: []model.Discount{
						{Type: model.DiscountTypeCoupon, Code: "HELLO70", Discount: 70},
					},
				},
			}
			expectedRegion := "UTHAI_THANI"

			api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

			deps.areaSvc.EXPECT().
				GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
				Return([]model.RegionCode{model.RegionCode(expectedRegion)}, nil)

			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
				Return(&model.ServiceArea{}, nil)
			deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&model.MapRoute{Distance: 1000}, nil, nil)

			calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
			calculator.EXPECT().
				Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(model.NewDeliveryFee(expectedBase, expectedRoad, 1000))
			priceScheme := CreateValidPriceScheme()
			deps.deliveryFeeSvc.EXPECT().
				GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
				Return(calculator, priceScheme, nil)
			deps.onTopFareRepo.EXPECT().
				GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.OnTopFare{}, nil)
			deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
			deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

			q, err := api.quote(context.Background(), qreq, "UTHAI_THANI")
			require.NoError(tt, err)
			require.Equal(tt, qreq.QuoteID, q.QuoteID)
			require.Equal(tt, qreq.UserID, q.UserID)
			require.Equal(tt, qreq.ServiceType, q.ServiceType)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
			require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
			require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
			require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
			require.NotEqual(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
			require.NotEqual(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
			require.Equal(tt, types.Distance(1000), q.Distance)
			require.False(tt, q.IsEnabledAgentModelCommissionAndTaxV2, "IsEnabledAgentModelCommissionAndTaxV2 flag should be false")

			require.Equal(tt, 0.03, q.Routes[0].PriceSummary.DeliveryFee.OnTopWithholdingTaxRate)
			// Since it is the principal model. So no commission rate
			require.Equal(tt, float64(0), q.Routes[0].PriceSummary.DeliveryFee.CommissionRate)
		})

		t.Run("with collect payment at route 0 - agent model", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			var (
				expectedBase        = 40.0
				expectedRoad        = 10.0
				expectedDeliveryFee = 0.0
				expectedTotal       = 0.0
			)

			qreq := &QuoteRequest{
				QuoteID:     "LMFQ-1231541",
				UserID:      "U1231542",
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{
						ID:   "125891ka",
						Name: "Jim Burger",
						Location: model.Location{
							Lat: 100.54702667857362,
							Lng: 13.743937518724506,
						},
						CollectPayment: true,
						Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
						Phones:         []string{"0891231234"},
						PickingItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						Memo: "4th floor, DedicatedZone Eden.",
					},
					{
						ID:   "",
						Name: "Uncle Tob",
						Location: model.Location{
							Lat: 100.5072724,
							Lng: 13.7487528,
						},
						CollectPayment: false,
						Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
						DeliveryItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						ItemsPrice: 150,
						Memo:       "Near Tesco lotus",
					},
				},
				PaymentMethod:     model.PaymentMethodCash,
				RevenueAgentModel: true,
				DeliveryFee: model.DeliveryFeeSummary{
					AdditionalFee: map[string]float64{},
					Discounts: []model.Discount{
						{Type: model.DiscountTypeCoupon, Code: "HELLO70", Discount: 70},
					},
				},
			}
			expectedRegion := "UTHAI_THANI"

			api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

			deps.areaSvc.EXPECT().
				GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
				Return([]model.RegionCode{model.RegionCode(expectedRegion)}, nil)

			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
				Return(&model.ServiceArea{}, nil)
			deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&model.MapRoute{Distance: 1000}, nil, nil)

			calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
			calculator.EXPECT().
				Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(model.NewDeliveryFee(expectedBase, expectedRoad, 1000))
			priceScheme := CreateValidPriceScheme()
			deps.deliveryFeeSvc.EXPECT().
				GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
				Return(calculator, priceScheme, nil)
			deps.onTopFareRepo.EXPECT().
				GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.OnTopFare{}, nil)
			deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
			deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

			q, err := api.quote(context.Background(), qreq, "UTHAI_THANI")
			require.NoError(tt, err)
			require.Equal(tt, qreq.QuoteID, q.QuoteID)
			require.Equal(tt, qreq.UserID, q.UserID)
			require.Equal(tt, qreq.ServiceType, q.ServiceType)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
			require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
			require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
			require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
			require.NotEqual(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
			require.NotEqual(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
			require.Equal(tt, types.Distance(1000), q.Distance)
			require.False(tt, q.IsEnabledAgentModelCommissionAndTaxV2, "IsEnabledAgentModelCommissionAndTaxV2 flag should be false")

			require.Equal(tt, 0.0, q.Routes[0].PriceSummary.DeliveryFee.OnTopWithholdingTaxRate)
			// Since the commission rate setting is disabled, So it will always use the same configured rate 0.15
			require.Equal(tt, 0.15, q.Routes[0].PriceSummary.DeliveryFee.CommissionRate)
		})

		t.Run("with collect payment at route 0 - agent-model - IsEnableCommissionAndTaxV2 true", func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			var (
				expectedBase        = 40.0
				expectedRoad        = 10.0
				expectedDeliveryFee = 0.0
				expectedTotal       = 0.0
			)

			qreq := &QuoteRequest{
				QuoteID:     "LMFQ-1231541",
				UserID:      "U1231542",
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{
						ID:   "125891ka",
						Name: "Jim Burger",
						Location: model.Location{
							Lat: 100.54702667857362,
							Lng: 13.743937518724506,
						},
						CollectPayment: true,
						Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
						Phones:         []string{"0891231234"},
						PickingItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						Memo: "4th floor, DedicatedZone Eden.",
					},
					{
						ID:   "",
						Name: "Uncle Tob",
						Location: model.Location{
							Lat: 100.5072724,
							Lng: 13.7487528,
						},
						CollectPayment: false,
						Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
						DeliveryItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						ItemsPrice: 150,
						Memo:       "Near Tesco lotus",
					},
				},
				PaymentMethod:     model.PaymentMethodCash,
				RevenueAgentModel: true,
				DeliveryFee: model.DeliveryFeeSummary{
					AdditionalFee: map[string]float64{},
					Discounts: []model.Discount{
						{Type: model.DiscountTypeCoupon, Code: "HELLO70", Discount: 70},
					},
				},
			}
			expectedRegion := "UTHAI_THANI"

			api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

			deps.areaSvc.EXPECT().
				GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
				Return([]model.RegionCode{model.RegionCode(expectedRegion)}, nil)

			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
				Return(&model.ServiceArea{}, nil)
			deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&model.MapRoute{Distance: 1000}, nil, nil)

			calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
			calculator.EXPECT().
				Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(model.NewDeliveryFee(expectedBase, expectedRoad, 1000))
			priceScheme := CreateValidPriceScheme()
			deps.deliveryFeeSvc.EXPECT().
				GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
				Return(calculator, priceScheme, nil)
			deps.onTopFareRepo.EXPECT().
				GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.OnTopFare{}, nil)
			deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
			deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(true)

			q, err := api.quote(context.Background(), qreq, "UTHAI_THANI")
			require.NoError(tt, err)
			require.Equal(tt, qreq.QuoteID, q.QuoteID)
			require.Equal(tt, qreq.UserID, q.UserID)
			require.Equal(tt, qreq.ServiceType, q.ServiceType)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
			require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
			require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
			require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
			require.NotEqual(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
			require.NotEqual(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
			require.True(tt, q.IsEnabledAgentModelCommissionAndTaxV2, "IsEnabledAgentModelCommissionAndTaxV2 flag should be true")
			require.Equal(tt, 0.03, q.Routes[0].PriceSummary.DeliveryFee.OnTopWithholdingTaxRate)
			// Since the commission rate setting is disabled, So it will always use the same configured rate 0.15
			require.Equal(tt, 0.15, q.Routes[0].PriceSummary.DeliveryFee.CommissionRate)

			require.Equal(tt, types.Distance(1000), q.Distance)
		})

		t.Run("with additional service", func(tt *testing.T) {
			tt.Parallel()
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			var (
				expectedBase          = 40.0
				expectedRoad          = 10.0
				expectedDeliveryFee   = expectedBase + expectedRoad
				expectedAdditionalFee = 40.0
				expectedTotal         = expectedDeliveryFee + expectedAdditionalFee
			)

			qreq := &QuoteRequest{
				QuoteID:     "LMFQ-1231541",
				UserID:      "U1231542",
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{
						ID:   "125891ka",
						Name: "Jim Burger",
						Location: model.Location{
							Lat: 100.54702667857362,
							Lng: 13.743937518724506,
						},
						CollectPayment: true,
						Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
						Phones:         []string{"0891231234"},
						PickingItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						Memo: "4th floor, DedicatedZone Eden.",
					},
					{
						ID:   "",
						Name: "Uncle Tob",
						Location: model.Location{
							Lat: 100.5072724,
							Lng: 13.7487528,
						},
						CollectPayment: false,
						Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
						DeliveryItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						ItemsPrice: 150,
						Memo:       "Near Tesco lotus",
					},
				},
				AdditionalServiceFee: AdditionalServiceReq(AdditionalServiceRequest{
					Total: expectedAdditionalFee,
					AdditionalServiceItems: []AdditionalServiceItemRequest{
						{
							Topic:       "BAG",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_bag.jpg",
							Title:       "กระเป๋าส่งของ",
							Description: "ขอไรเดอร์ที่มีกระเป๋าหลัง",
							SortNumber:  0,
							Price:       0,
						}, {
							Topic:       "DOCUMENT_PROCESSING",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_document_processing.jpg",
							Title:       "เดินเอกสาร",
							Description: "ช่วยวางบิล, ทำเอกสาร, ขอลายเซ็น",
							SortNumber:  1,
							Price:       20.0,
						}, {
							Topic:       "POSTAL_AND_SHIPPING",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_shiping.jpg",
							Title:       "ฝากส่งไปรษณีย์หรือบริการขนส่ง",
							Description: "แพ็กพัสดุไว้ให้พร้อมก็พอแล้ว",
							SortNumber:  300,
							Price:       20.0,
						},
					},
				}),
				PaymentMethod: model.PaymentMethodCash,
				DeliveryFee: model.DeliveryFeeSummary{
					AdditionalFee: map[string]float64{},
				},
			}
			expectedRegion := "UTHAI_THANI"

			api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

			deps.areaSvc.EXPECT().
				GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
				Return([]model.RegionCode{model.RegionCode(expectedRegion)}, nil)

			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
				Return(&model.ServiceArea{}, nil)
			deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&model.MapRoute{Distance: 1000}, nil, nil)

			calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
			calculator.EXPECT().
				Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(model.NewDeliveryFee(expectedBase, expectedRoad, 1000))
			priceScheme := CreateValidPriceScheme()
			deps.deliveryFeeSvc.EXPECT().
				GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
				Return(calculator, priceScheme, nil)
			deps.onTopFareRepo.EXPECT().
				GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.OnTopFare{}, nil)
			deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
			deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

			q, err := api.quote(context.Background(), qreq, "UTHAI_THANI")
			require.NoError(tt, err)
			require.Equal(tt, qreq.QuoteID, q.QuoteID)
			require.Equal(tt, qreq.UserID, q.UserID)
			require.Equal(tt, qreq.ServiceType, q.ServiceType)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
			require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
			require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
			require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
			require.Equal(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
			require.Equal(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
			require.Equal(tt, types.Distance(1000), q.Distance)
		})

		t.Run("with additional service and partial discount", func(tt *testing.T) {
			tt.Parallel()
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			var (
				expectedBase          = 40.0
				expectedRoad          = 10.0
				expectedAdditionalFee = 40.0
				expectedDeliveryFee   = 50.0
				expectedDiscount      = 70.0
				expectedTotal         = 20.0
			)

			qreq := &QuoteRequest{
				QuoteID:     "LMFQ-1231541",
				UserID:      "U1231542",
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{
						ID:   "125891ka",
						Name: "Jim Burger",
						Location: model.Location{
							Lat: 100.54702667857362,
							Lng: 13.743937518724506,
						},
						CollectPayment: true,
						Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
						Phones:         []string{"0891231234"},
						PickingItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						Memo: "4th floor, DedicatedZone Eden.",
					},
					{
						ID:   "",
						Name: "Uncle Tob",
						Location: model.Location{
							Lat: 100.5072724,
							Lng: 13.7487528,
						},
						CollectPayment: false,
						Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
						DeliveryItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						ItemsPrice: 150,
						Memo:       "Near Tesco lotus",
					},
				},
				AdditionalServiceFee: AdditionalServiceReq(AdditionalServiceRequest{
					Total: expectedAdditionalFee,
					AdditionalServiceItems: []AdditionalServiceItemRequest{
						{
							Topic:       "BAG",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_bag.jpg",
							Title:       "กระเป๋าส่งของ",
							Description: "ขอไรเดอร์ที่มีกระเป๋าหลัง",
							SortNumber:  0,
							Price:       0,
						}, {
							Topic:       "DOCUMENT_PROCESSING",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_document_processing.jpg",
							Title:       "เดินเอกสาร",
							Description: "ช่วยวางบิล, ทำเอกสาร, ขอลายเซ็น",
							SortNumber:  1,
							Price:       20.0,
						}, {
							Topic:       "POSTAL_AND_SHIPPING",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_shiping.jpg",
							Title:       "ฝากส่งไปรษณีย์หรือบริการขนส่ง",
							Description: "แพ็กพัสดุไว้ให้พร้อมก็พอแล้ว",
							SortNumber:  300,
							Price:       20.0,
						},
					},
				}),
				PaymentMethod: model.PaymentMethodCash,
				Discounts: []DiscountReq{
					{Type: model.DiscountTypeCoupon, Code: "HELLO70", Discount: expectedDiscount},
				},
			}
			expectedRegion := "UTHAI_THANI"

			api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

			deps.areaSvc.EXPECT().
				GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
				Return([]model.RegionCode{model.RegionCode(expectedRegion)}, nil)

			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
				Return(&model.ServiceArea{}, nil)
			deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&model.MapRoute{Distance: 1000}, nil, nil)

			calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
			calculator.EXPECT().
				Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(model.NewDeliveryFee(expectedBase, expectedRoad, 1000))
			priceScheme := CreateValidPriceScheme()
			deps.deliveryFeeSvc.EXPECT().
				GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
				Return(calculator, priceScheme, nil)
			deps.onTopFareRepo.EXPECT().
				GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.OnTopFare{}, nil)
			deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
			deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

			q, err := api.quote(context.Background(), qreq, "UTHAI_THANI")
			require.NoError(tt, err)
			require.Equal(tt, qreq.QuoteID, q.QuoteID)
			require.Equal(tt, qreq.UserID, q.UserID)
			require.Equal(tt, qreq.ServiceType, q.ServiceType)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
			require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
			require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
			require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
			require.Equal(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
			require.Equal(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
			require.Equal(tt, types.Distance(1000), q.Distance)
		})

		t.Run("with additional service and fully discount", func(tt *testing.T) {
			tt.Parallel()
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			var (
				expectedBase          = 50.0
				expectedRoad          = 10.0
				expectedAdditionalFee = 40.0
				expectedDeliveryFee   = 60.0
				expectedDiscount      = 100.0
				expectedTotal         = 0.0
			)

			qreq := &QuoteRequest{
				QuoteID:     "LMFQ-1231541",
				UserID:      "U1231542",
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{
						ID:   "125891ka",
						Name: "Jim Burger",
						Location: model.Location{
							Lat: 100.54702667857362,
							Lng: 13.743937518724506,
						},
						CollectPayment: true,
						Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
						Phones:         []string{"0891231234"},
						PickingItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						Memo: "4th floor, DedicatedZone Eden.",
					},
					{
						ID:   "",
						Name: "Uncle Tob",
						Location: model.Location{
							Lat: 100.5072724,
							Lng: 13.7487528,
						},
						CollectPayment: false,
						Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
						DeliveryItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						ItemsPrice: 150,
						Memo:       "Near Tesco lotus",
					},
				},
				AdditionalServiceFee: AdditionalServiceReq(AdditionalServiceRequest{
					Total: expectedAdditionalFee,
					AdditionalServiceItems: []AdditionalServiceItemRequest{
						{
							Topic:       "BAG",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_bag.jpg",
							Title:       "กระเป๋าส่งของ",
							Description: "ขอไรเดอร์ที่มีกระเป๋าหลัง",
							SortNumber:  0,
							Price:       0,
						}, {
							Topic:       "DOCUMENT_PROCESSING",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_document_processing.jpg",
							Title:       "เดินเอกสาร",
							Description: "ช่วยวางบิล, ทำเอกสาร, ขอลายเซ็น",
							SortNumber:  1,
							Price:       20.0,
						}, {
							Topic:       "POSTAL_AND_SHIPPING",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_shiping.jpg",
							Title:       "ฝากส่งไปรษณีย์หรือบริการขนส่ง",
							Description: "แพ็กพัสดุไว้ให้พร้อมก็พอแล้ว",
							SortNumber:  300,
							Price:       20.0,
						},
					},
				}),
				PaymentMethod: model.PaymentMethodCash,
				Discounts: []DiscountReq{
					{Type: model.DiscountTypeCoupon, Code: "HELLO70", Discount: expectedDiscount},
				},
			}
			expectedRegion := "UTHAI_THANI"

			api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

			deps.areaSvc.EXPECT().
				GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
				Return([]model.RegionCode{model.RegionCode(expectedRegion)}, nil)

			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
				Return(&model.ServiceArea{}, nil)
			deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&model.MapRoute{Distance: 1000}, nil, nil)

			calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
			calculator.EXPECT().
				Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(model.NewDeliveryFee(expectedBase, expectedRoad, 1000))
			priceScheme := CreateValidPriceScheme()
			deps.deliveryFeeSvc.EXPECT().
				GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
				Return(calculator, priceScheme, nil)
			deps.onTopFareRepo.EXPECT().
				GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.OnTopFare{}, nil)
			deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
			deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

			q, err := api.quote(context.Background(), qreq, "UTHAI_THANI")
			require.NoError(tt, err)
			require.Equal(tt, qreq.QuoteID, q.QuoteID)
			require.Equal(tt, qreq.UserID, q.UserID)
			require.Equal(tt, qreq.ServiceType, q.ServiceType)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
			require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
			require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
			require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
			require.Equal(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
			require.Equal(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
			require.Equal(tt, types.Distance(1000), q.Distance)
		})

		t.Run("with additional service and delivery discount", func(tt *testing.T) {
			tt.Parallel()
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			var (
				expectedBase          = 50.0
				expectedRoad          = 10.0
				expectedAdditionalFee = 40.0
				expectedDeliveryFee   = 60.0
				expectedDiscount      = 60.0
				expectedTotal         = 40.0
			)

			qreq := &QuoteRequest{
				QuoteID:     "LMFQ-1231541",
				UserID:      "U1231542",
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{
						ID:   "125891ka",
						Name: "Jim Burger",
						Location: model.Location{
							Lat: 100.54702667857362,
							Lng: 13.743937518724506,
						},
						CollectPayment: true,
						Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
						Phones:         []string{"0891231234"},
						PickingItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						Memo: "4th floor, DedicatedZone Eden.",
					},
					{
						ID:   "",
						Name: "Uncle Tob",
						Location: model.Location{
							Lat: 100.5072724,
							Lng: 13.7487528,
						},
						CollectPayment: false,
						Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
						DeliveryItems: []model.Item{
							{
								Name:     "Cheeseburger",
								Quantity: 1,
								Price:    150,
								Memo:     "Without prickle",
							},
						},
						ItemsPrice: 150,
						Memo:       "Near Tesco lotus",
					},
				},
				AdditionalServiceFee: AdditionalServiceReq(AdditionalServiceRequest{
					Total: expectedAdditionalFee,
					AdditionalServiceItems: []AdditionalServiceItemRequest{
						{
							Topic:       "BAG",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_bag.jpg",
							Title:       "กระเป๋าส่งของ",
							Description: "ขอไรเดอร์ที่มีกระเป๋าหลัง",
							SortNumber:  0,
							Price:       0,
						}, {
							Topic:       "DOCUMENT_PROCESSING",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_document_processing.jpg",
							Title:       "เดินเอกสาร",
							Description: "ช่วยวางบิล, ทำเอกสาร, ขอลายเซ็น",
							SortNumber:  1,
							Price:       20.0,
						}, {
							Topic:       "POSTAL_AND_SHIPPING",
							ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_shiping.jpg",
							Title:       "ฝากส่งไปรษณีย์หรือบริการขนส่ง",
							Description: "แพ็กพัสดุไว้ให้พร้อมก็พอแล้ว",
							SortNumber:  300,
							Price:       20.0,
						},
					},
				}),
				PaymentMethod: model.PaymentMethodCash,
				Discounts: []DiscountReq{
					{Type: model.DiscountTypeCoupon, Code: "HELLO70", Discount: expectedDiscount},
				},
			}
			expectedRegion := "UTHAI_THANI"

			api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

			deps.areaSvc.EXPECT().
				GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
				Return([]model.RegionCode{model.RegionCode(expectedRegion)}, nil)

			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
				Return(&model.ServiceArea{}, nil)
			deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(&model.MapRoute{Distance: 1000}, nil, nil)

			calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
			calculator.EXPECT().
				Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(model.NewDeliveryFee(expectedBase, expectedRoad, 1000))
			priceScheme := CreateValidPriceScheme()
			deps.deliveryFeeSvc.EXPECT().
				GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
				Return(calculator, priceScheme, nil)
			deps.onTopFareRepo.EXPECT().
				GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.OnTopFare{}, nil)
			deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
			deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

			q, err := api.quote(context.Background(), qreq, "UTHAI_THANI")
			require.NoError(tt, err)
			require.Equal(tt, qreq.QuoteID, q.QuoteID)
			require.Equal(tt, qreq.UserID, q.UserID)
			require.Equal(tt, qreq.ServiceType, q.ServiceType)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
			require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
			require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
			require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
			require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
			require.Equal(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
			require.Equal(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
			require.Equal(tt, types.Distance(1000), q.Distance)
		})
	})

	t.Run("should throw error when get distribute region error", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: model.ServiceBike,
			QuoteID:     "LMFQ-1231541",
			Routes: []model.Stop{
				{
					CollectPayment: false,
					Location: model.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: true,
					Location: model.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
		}

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), model.ServiceBike, gomock.Any(), gomock.Any()).
			Return([]model.RegionCode{}, errors.New("errors"))

		_, err := testapi.quote(context.Background(), q, "UTHAI_THANI")
		require.Error(tt, err)
	})

	t.Run("should throw error when Routes has less than 2 stop", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: model.ServiceBike,
			QuoteID:     "LMFQ-1231541",
			Routes: []model.Stop{
				{
					CollectPayment: true,
					Location: model.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
			},
		}

		testapi, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		_, err := testapi.quote(context.Background(), q, "UTHAI_THANI")
		require.Error(tt, err)
	})

	t.Run("agent model quote should calculate commission and withholding tax - IsEnabledAgentModelCommissionAndTaxV2 false", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMBQ-1231541",
			ServiceType: model.ServiceBike,
			PayAtStop:   1,
			Routes: []model.Stop{
				{},
				{},
			},
			RevenueAgentModel: true,
		}

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RegionCode{""}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), "").
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		priceScheme := CreateValidPriceScheme()
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewDeliveryFee(100.0, 0.0, 1000))
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.OnTopFare{}, nil)

		testapi.Cfg = OrderAPIConfig{AtomicOrderDBConfig: &AtomicOrderDBConfig{
			Config: OrderDBConfig{
				Commisson:                     0.6,
				CommissionRateSettingsEnabled: true,
				CommissionRateSettings: map[string]float64{
					"bike":      0.05,
					"messenger": 0.15,
				},
				WithHoldingTax: 0.2,
			},
		}}
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "")
		require.NoError(tt, err)
		require.Equal(tt, 5.0, actualQ.Routes[1].PriceSummary.DeliveryFee.Commission)
		require.Equal(tt, 0.0, actualQ.Routes[1].PriceSummary.DeliveryFee.WithholdingTax)
	})

	t.Run("agent model quote should calculate commission and withholding tax - PIP enabled - IsEnabledAgentModelCommissionAndTaxV2 true", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMBQ-1231541",
			ServiceType: model.ServiceBike,
			PayAtStop:   1,
			Routes: []model.Stop{
				{},
				{},
			},
			RevenueAgentModel: true,
		}

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		testapi.Cfg = OrderAPIConfig{AtomicOrderDBConfig: &AtomicOrderDBConfig{
			Config: OrderDBConfig{
				Commisson:                     0.6,
				CommissionRateSettingsEnabled: true,
				CommissionRateSettings: map[string]float64{
					"bike":      0.05,
					"messenger": 0.15,
				},
				WithHoldingTax: 0.03,
			}}}

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RegionCode{""}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), "").
			Return(&model.ServiceArea{}, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		priceScheme := CreateValidPriceScheme()
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewDeliveryFee(100.0, 0.0, 1000))
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.OnTopFare{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(true)
		// Since we don't focus on the PIP calling in this section, So Use any to skip the testing.
		deps.priceInterventionClient.EXPECT().GetEstimatedUserFareOnTopRequest(gomock.Any(), gomock.Any()).Return(&transportationV1.GetEstimatedUserFareOntopResponse{
			EstimatedUserFares: []*transportationV1.EstimatedUserFareOntopResponse{
				{
					DriverOntopReferences: []*transportationV1.Ontop{
						{
							Amount: wrapperspb.Double(7),
						},
					},
				},
			},
		}, nil)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(true)

		actualQ, err := testapi.quote(context.Background(), q, "")
		require.NoError(tt, err)
		// Since the CommissionRateSettings is enabled and the bike commission is set to 0.05
		require.Equal(tt, 0.05, actualQ.Routes[1].PriceSummary.DeliveryFee.CommissionRate)
		require.Equal(tt, 5.35, actualQ.Routes[1].PriceSummary.DeliveryFee.Commission)
		require.Equal(tt, float64(0), actualQ.Routes[1].PriceSummary.DeliveryFee.OnTopWithholdingTax)
		require.Equal(tt, 0.03, actualQ.Routes[1].PriceSummary.DeliveryFee.OnTopWithholdingTaxRate)
		require.True(tt, actualQ.IsEnabledAgentModelCommissionAndTaxV2, "Flag IsEnabledAgentModelCommissionAndTaxV2 enabled")
	})
}

func TestBikeDeliveryAPI_CreateOrder(t *testing.T) {
	t.Parallel()
	req := func(req *CreateOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/bike/order/create", testutil.JSON(req))
		return ctx, recorder
	}
	t.Run("create order from quote", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMPQ-1231451"
			ordid = "LMPQ-1231451"
			resID = "res-id"
		)

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&model.Quote{
				ServiceType: model.ServiceBike,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []model.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []model.RegionCode{"UTHAI_THANI"},
			}, nil)
		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *model.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.True(tt, order.RevampedStatus)
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())

		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), mp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, ordid, resp.OrderID)
		require.Equal(t, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("should omit qr payment pause and qr info when total delivery fee is zero", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMPQ-1231451"
			ordid = "LMPQ-1231451"
			resID = "res-id"
		)

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&model.Quote{
				ServiceType: model.ServiceBike,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []model.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
								RawBaseFee:    67,
								BaseFee:       67,
								SubTotal:      67,
								Total:         67,
								QRPromptPayInfo: model.QRPromptPayInfo{
									Status:          model.QRPromptPayStatusWaitingForPayment,
									IsUserResolveQR: false,
								},
							},
							Total: 67,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []model.RegionCode{"UTHAI_THANI"},
			}, nil)
		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *model.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				require.Equal(tt, 67.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, 0.0, order.Routes[1].PriceSummary.DeliveryFee.Total)
				require.True(tt, order.RevampedStatus)
				require.False(tt, order.Quote.Routes[1].Pauses[model.PauseQRPayment])
				require.Equal(tt, model.QRPromptPayInfo{Status: model.QRPromptPayStatusResolvedByUser, IsUserResolveQR: true}, order.PriceSummary().DeliveryFee.QRPromptPayInfo)
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())

		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), mp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Discounts: DiscountListReq{{
			Code:     "BIKE500",
			Type:     model.DiscountTypeCoupon,
			Discount: 67.00,
		}}})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, ordid, resp.OrderID)
		require.Equal(t, 67.0, resp.DeliveryFee.BaseFee)
		require.Equal(t, 0.0, resp.DeliveryFee.Total)
	})

	t.Run("should not omit qr payment pause and qr info when total delivery fee is not zero", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMPQ-1231451"
			ordid = "LMPQ-1231451"
			resID = "res-id"
		)

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&model.Quote{
				ServiceType: model.ServiceBike,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []model.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
								RawBaseFee:    67,
								BaseFee:       67,
								SubTotal:      67,
								Total:         67,
								QRPromptPayInfo: model.QRPromptPayInfo{
									Status:          model.QRPromptPayStatusWaitingForPayment,
									IsUserResolveQR: false,
								},
							},
							Total: 67,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []model.RegionCode{"UTHAI_THANI"},
			}, nil)
		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *model.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				require.Equal(tt, 67.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, 67.0, order.Routes[1].PriceSummary.DeliveryFee.Total)
				require.True(tt, order.RevampedStatus)
				require.True(tt, order.Quote.Routes[1].Pauses[model.PauseQRPayment])
				require.Equal(tt, model.QRPromptPayInfo{Status: model.QRPromptPayStatusWaitingForPayment, IsUserResolveQR: false}, order.PriceSummary().DeliveryFee.QRPromptPayInfo)
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())

		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), mp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, ordid, resp.OrderID)
		require.Equal(t, 67.0, resp.DeliveryFee.BaseFee)
		require.Equal(t, 67.0, resp.DeliveryFee.Total)
	})

	t.Run("create order from quote with idempotency", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&model.Quote{
				ServiceType: model.ServiceBike,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []model.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
								RawBaseFee:    67,
								BaseFee:       67,
								SubTotal:      67,
								Total:         67,
							},
							Total: 67,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []model.RegionCode{"UTHAI_THANI"},
			}, nil)
		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *model.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				require.Equal(tt, 67.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, 67.0, order.Routes[1].PriceSummary.DeliveryFee.Total)
				require.True(tt, order.RevampedStatus)
				require.True(tt, order.Quote.Routes[1].Pauses[model.PauseQRPayment])
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), mp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp api.Error
		testutil.DecodeJSON(t, recorder.Body, &resp)
	})

	t.Run("create order from quote with idempotency and should return previous data response from cache", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return(fmt.Sprintf(`{"orderId": "%s"}`, ordid))

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, ordid, resp.OrderID)
	})

	t.Run("create order from quote with idempotency and should return an error order locked with the idempotencyKey", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&model.Quote{
				ServiceType: model.ServiceBike,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []model.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
								RawBaseFee:    67,
								BaseFee:       67,
								SubTotal:      67,
								Total:         67,
							},
							Total: 67,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []model.RegionCode{"UTHAI_THANI"},
			}, nil)

		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(false, nil)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusBadRequest, recorder.Code)
		var resp api.Error
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, fmt.Sprintf("order was locked by idempotencyKey %s", qid), resp.Message)
	})
}

func TestBikeDeliveryAPI_AcceptOrderHandler(t *testing.T) {
	t.Parallel()

	req := func(orderID string, driverID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/order/"+orderID+"/accept", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		driver.SetDriverIDToContext(ctx, driverID)
		return ctx, recorder
	}

	t.Run("should be able to accept a bike order", func(tt *testing.T) {
		tt.Parallel()

		driverID := "DRIVER_ID"
		orderID := "LMF-1"
		pickupLocation := model.Location{Lat: 14.0, Lng: 101.0}
		dropoffLocation := model.Location{Lat: 15.0, Lng: 102.0}

		ctx, recorder := req(orderID, driverID)
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ordapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		defer deps.stop()

		mockedOrder := mockOrder(orderID, pickupLocation, dropoffLocation)
		mockedProfile := mockOnlineDriver(driverID)
		mockedTxn := mockDriverTransaction(driverID)
		deps.driverService.EXPECT().DeductOnTopQuota(gomock.Any(), gomock.Any(), gomock.Any())
		deps.metricsRegistry.EXPECT().IncrAcceptOrderCounter(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

		expectAcceptOrder(ctx, deps, expectAcceptOrderParams{
			orderID:        orderID,
			driverTrans:    mockedTxn,
			driverID:       driverID,
			driverProfile:  mockedProfile,
			pickupLocation: pickupLocation,
		})

		wg := safe.CreateWaitGroupOnGctx(ctx)
		ordapi.AcceptOrderHandler(ctx, mockedOrder, mockedOrder.OrderID, driverID, "test-device-id")
		wg.Wait()

		actual := UpdateStatusRes{}

		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestBikeDeliveryAPI_AcceptAssignmentHandler(t *testing.T) {
	t.Parallel()

	req := func(assignmentID string, driverID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/assignment/"+assignmentID+"/accept", nil)
		ctx.Params = gin.Params{
			{
				Key:   "assignmentID",
				Value: assignmentID,
			},
		}
		driver.SetDriverIDToContext(ctx, driverID)
		return ctx, recorder
	}

	t.Run("should be able to accept a bike assignment", func(tt *testing.T) {
		tt.Parallel()

		driverID := "DRIVER_ID"
		orderID := "LMF-1"
		assignmentID := "ASSIGNMENT_ID"
		pickupLocation := model.Location{Lat: 14.0, Lng: 101.0}
		dropoffLocation := model.Location{Lat: 15.0, Lng: 102.0}

		ctx, recorder := req(orderID, driverID)
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ordapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		defer deps.stop()

		mockedOrder := mockOrder(orderID, pickupLocation, dropoffLocation)
		mockedDriver := mockOnlineDriver(driverID)
		mockedTxn := mockDriverTransaction(driverID)

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).Return(nil)

		deps.driverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&mockedDriver, nil)
		deps.driverService.EXPECT().DeductOnTopQuota(gomock.Any(), gomock.Any(), gomock.Any())

		gomock.InOrder(
			deps.assignmentRepo.EXPECT().
				FindByID(gomock.Any(), gomock.Any()).Return(&model.Assignment{
				AssignmentID: model.AssignmentID(assignmentID),
				DriverID:     driverID,
				OrderIDs:     []string{orderID},
				PlanRoute: model.PlanRoute{
					RouteResponse: prediction.RouteResponse{PlanRoutes: []prediction.PlanRoute{
						{
							OrderID:    "LMF-1",
							ActionType: "pickup",
						},
						{
							OrderID:    "LMF-1",
							ActionType: "dropoff",
						},
					}},
				},
			}, nil),
			deps.orderService.EXPECT().Get(gomock.Any(), orderID).Return(mockedOrder, nil),
			deps.assignmentRepo.EXPECT().SetTriedAccepting(gomock.Any(), model.AssignmentID(assignmentID)),
		)
		deps.metricsRegistry.EXPECT().IncrAcceptOrderCounter(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
		expectAcceptOrder(ctx, deps, expectAcceptOrderParams{
			orderID:        orderID,
			driverTrans:    mockedTxn,
			driverID:       driverID,
			driverProfile:  mockedDriver,
			pickupLocation: pickupLocation,
		})

		wg := safe.CreateWaitGroupOnGctx(ctx)
		ordapi.AcceptAssignmentHandler(ctx, model.AssignmentID(assignmentID), driverID, "test-device-id")
		wg.Wait()

		var actual UpdateStatusRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestBikeDeliveryAPI_UpdateOrderStatus(t *testing.T) {
	t.Parallel()
	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		location := model.Location{}
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/order/"+orderID+"/status", testutil.JSON(location))
		ctx.Params = gin.Params{{Key: "orderID", Value: orderID}}
		return ctx, recorder
	}

	type TestCase struct {
		name          string
		existedOrder  *model.Order
		expectedOrder *model.Order
	}

	testCases := []TestCase{
		{
			name: "`DRIVER_TO_0` to `ARRIVE_AT_0`",
			existedOrder: &model.Order{
				Status: model.StatusDriveTo,
				HeadTo: 0,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusOnTheWay},
						{DeliveryStatus: model.DeliveryStatusInit},
					},
				},
			},
			expectedOrder: &model.Order{
				Status: model.StatusArrivedAt,
				HeadTo: 0,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusArrived},
						{DeliveryStatus: model.DeliveryStatusInit},
					},
				},
			},
		},
		{
			name: "`ARRIVE_AT_0` to `DRIVER_TO_1`",
			existedOrder: &model.Order{
				Status: model.StatusArrivedAt,
				HeadTo: 0,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusArrived},
						{DeliveryStatus: model.DeliveryStatusInit},
					},
				},
			},
			expectedOrder: &model.Order{
				Status: model.StatusDriveTo,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusOnTheWay},
					},
				},
			},
		},
		{
			name: "`DRIVER_TO_1` to `ARRIVE_AT_1`",
			existedOrder: &model.Order{
				Status: model.StatusDriveTo,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusOnTheWay},
					},
				},
			},
			expectedOrder: &model.Order{
				Status: model.StatusArrivedAt,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusArrived},
					},
				},
			},
		},
		{
			name: "`ARRIVE_AT_1` to `COMPLETED`",
			existedOrder: &model.Order{
				Status: model.StatusArrivedAt,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusArrived},
					},
				},
			},
			expectedOrder: &model.Order{
				Status: model.StatusCompleted,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusDropOffDone},
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		orderAPIConfig := OrderAPIConfig{PreventVerifyDriverPhotoService: []model.Service{model.ServiceMessenger, model.ServiceBike}}
		existedOrder := tc.existedOrder
		driverID := "<driver_id>"
		existedOrder.OrderID = "<order_id>"
		existedOrder.Driver = driverID
		existedOrder.TripID = "<trip_id>"
		existedOrder.ServiceType = model.ServiceBike
		existedOrder.RevampedStatus = true
		existedDriver := mockAssignedDriver(driverID)
		expectedOrder := tc.expectedOrder
		t.Run(tc.name, func(tt *testing.T) {
			tt.Parallel()
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			api, deps := newTestFoodProvider(ctrl, orderAPIConfig, &AtomicContingencyConfig{})
			ctx, recorder := req(existedOrder.OrderID)

			deps.tripServices.EXPECT().SyncTripOrderStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), gomock.Eq(featureflag.IsPublishMissionLogOrderCompletedEnabled.Name)).Return(true).AnyTimes()

			if expectedOrder.Status == model.StatusCompleted {
				expectCompleteOrder(ctx, deps, existedDriver)
				deps.tripServices.EXPECT().GetTripByID(gomock.Any(), gomock.Any()).Return(model.Trip{}, nil)
			} else {
				expectNextStatusOrder(tt, ctx, deps, expectedOrder)
			}

			api.UpdateOrderStatus(ctx, existedOrder, existedOrder.OrderID, existedOrder.Driver)

			require.Equal(tt, http.StatusOK, recorder.Code)
		})
	}
}

func TestBikeDeliveryAPI_UpdateMultipleOrderStatus(t *testing.T) {
	t.Parallel()
	req := func(driverID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/orders/next-status", testutil.JSON("a"))
		driver.SetDriverIDToContext(ctx, driverID)
		return ctx, recorder
	}

	type TestCase struct {
		name          string
		existedOrder  *model.Order
		expectedOrder *model.Order
	}

	testCases := []TestCase{
		{
			name: "`DRIVER_TO_0` to `ARRIVE_AT_0`",
			existedOrder: &model.Order{
				Status: model.StatusDriveTo,
				HeadTo: 0,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusOnTheWay},
						{DeliveryStatus: model.DeliveryStatusInit},
					},
				},
			},
			expectedOrder: &model.Order{
				Status: model.StatusArrivedAt,
				HeadTo: 0,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusArrived},
						{DeliveryStatus: model.DeliveryStatusInit},
					},
				},
			},
		},
		{
			name: "`ARRIVE_AT_0` to `DRIVER_TO_1`",
			existedOrder: &model.Order{
				Status: model.StatusArrivedAt,
				HeadTo: 0,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusArrived},
						{DeliveryStatus: model.DeliveryStatusInit},
					},
				},
			},
			expectedOrder: &model.Order{
				Status: model.StatusDriveTo,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusOnTheWay},
					},
				},
			},
		},
		{
			name: "`DRIVER_TO_1` to `ARRIVE_AT_1`",
			existedOrder: &model.Order{
				Status: model.StatusDriveTo,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusOnTheWay},
					},
				},
			},
			expectedOrder: &model.Order{
				Status: model.StatusArrivedAt,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusArrived},
					},
				},
			},
		},
		{
			name: "`ARRIVE_AT_1` to `COMPLETED`",
			existedOrder: &model.Order{
				Status: model.StatusArrivedAt,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusArrived},
					},
				},
			},
			expectedOrder: &model.Order{
				Status: model.StatusCompleted,
				HeadTo: 1,
				Quote: model.Quote{
					Routes: []model.Stop{
						{DeliveryStatus: model.DeliveryStatusPickupDone},
						{DeliveryStatus: model.DeliveryStatusDropOffDone},
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		orderAPIConfig := OrderAPIConfig{PreventVerifyDriverPhotoService: []model.Service{model.ServiceMessenger, model.ServiceBike}}
		existedOrder := tc.existedOrder
		driverID := "<driver_id>"
		existedOrder.OrderID = "<order_id>"
		existedOrder.Driver = driverID
		existedOrder.TripID = "<trip_id>"
		existedOrder.ServiceType = model.ServiceBike
		existedOrder.RevampedStatus = true
		existedDriver := mockAssignedDriver(driverID)
		expectedOrder := tc.expectedOrder
		t.Run(tc.name, func(tt *testing.T) {
			tt.Parallel()
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			api, deps := newTestFoodProvider(ctrl, orderAPIConfig, &AtomicContingencyConfig{})
			ctx, recorder := req(existedOrder.Driver)

			deps.tripServices.EXPECT().SyncTripOrderStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), gomock.Eq(featureflag.IsPublishMissionLogOrderCompletedEnabled.Name)).Return(true).AnyTimes()

			if expectedOrder.Status == model.StatusCompleted {
				expectCompleteOrder(ctx, deps, existedDriver)
			} else {
				expectNextStatusOrder(tt, ctx, deps, expectedOrder)
			}

			_, err := api.UpdateOrderStatusV2(ctx, existedOrder, existedOrder.Driver, &model.Location{}, []client.ProofOfDeliveryImage{}, nil)
			require.NoError(tt, err)
			require.Equal(tt, http.StatusOK, recorder.Code)
		})
	}
}

func mockOnlineDriver(driverID string) (d model.Driver) {
	d.Firstname = crypt.NewLazyEncryptedString("Uncle")
	d.Lastname = crypt.NewLazyEncryptedString("Tol3")
	d.DriverID = driverID
	d.Phone = crypt.NewLazyEncryptedString("**********")
	d.AvatarURL = "http://AvatarURL.go"
	d.Vehicle.PlateNumber = crypt.NewLazyEncryptedString("ปจด 6666")
	d.Region = "BKK"
	d.Status = model.StatusOnline
	d.CurrentOrder = ""
	d.CurrentTrip = ""
	return d
}

func mockAssignedDriver(driverID string) *model.Driver {
	d := &model.Driver{}
	d.BaseDriver = model.BaseDriver{Banking: model.BankingInfo{UOBRefID: "<uob-ref-id>"}}
	return d
}

func mockOrder(orderID string, pickupLocation, dropoffLocation model.Location) *model.Order {
	o := model.NewOrder(model.Quote{
		ServiceType: model.ServiceBike,
		Routes: []model.Stop{
			{
				ID:       "pickup",
				Location: pickupLocation,
				Info:     model.StopInfoCollector{StopInfo: &model.StopInfoFood{PriceScheme: model.PriceSchemeDefault}},
			},
			{
				ID:             "dropoff",
				Location:       dropoffLocation,
				CollectPayment: true,
				PriceSummary: model.PriceSummary{
					DeliveryFee: model.DeliveryFeeSummary{
						SubTotal: 40,
					},
				},
			},
		},
		DistributeRegions: []model.RegionCode{"BKK"},
	}, orderID)
	o.SetStatus(model.StatusAssigningDriver)
	o.Distribution = model.DistributionLogicAutoAssign
	o.ExpireAt = time.Now().Add(5 * time.Minute)
	return o
}

func mockDriverTransaction(driverID string) model.DriverTransaction {
	txn := *model.NewDriverTransaction(driverID)
	addCredit := model.NewPurchaseCreditTransactionInfo(driverID, 5)
	AddWallet := model.NewIncentiveTransactionInfo(driverID, 5, []string{})
	txn.AddTransaction([]model.TransactionInfo{*addCredit, *AddWallet})
	return txn
}

type expectAcceptOrderParams struct {
	orderID        string
	driverTrans    model.DriverTransaction
	driverID       string
	driverProfile  model.Driver
	pickupLocation model.Location
}

func expectAcceptOrder(ctx interface{}, deps *foodProviderDeps, params expectAcceptOrderParams) {
	gomock.InOrder(
		deps.driverRepository.EXPECT().
			GetProfile(gomock.Any(), params.driverID).
			Return(&params.driverProfile, nil).AnyTimes(),
		deps.assignmentLog.EXPECT().
			FilterOrdersAcceptableByRider(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]string{params.orderID}, nil),
		deps.assignmentLog.EXPECT().
			AutoAssignedDrivers(gomock.Any(), gomock.Any()).
			Return([]model.Record{{
				DriverID:      params.driverID,
				AllowQueueing: true,
				PushAt:        time.Date(2021, 8, 26, 0, 0, 0, 0, time.UTC),
				PlanRoute: model.PlanRoute{
					RouteResponse: prediction.RouteResponse{
						AssignedOrders: []prediction.AssignedOrder{
							{OrderID: params.orderID, OrderFlag: []prediction.OrderFlag{}},
						},
						PlanRoutes: []prediction.PlanRoute{
							{
								OrderID:    params.orderID,
								ActionType: "pickup",
							},
							{
								OrderID:    params.orderID,
								ActionType: "dropoff",
							},
						},
					},
				},
			}}, model.StrategyNameFleetPool, nil),
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), "BKK").Return(&model.ServiceArea{}, nil),
		deps.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), params.driverID, gomock.Any()).
			Return(params.driverTrans, nil),
		deps.driverService.EXPECT().UpdateDriverLastAttempt(gomock.Any(), params.driverID).Return(nil),
		deps.driverLocationService.EXPECT().GetDriverLocation(gomock.Any(), gomock.Any()).Return(&model.DriverLocation{}, nil),
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 0.0, Lng: 0.0}, mapservice.Location{Lat: params.pickupLocation.Lat, Lng: params.pickupLocation.Lng}, gomock.Any()).
			Return(&model.MapRoute{Distance: 5930}, nil, nil),
		deps.delivery.EXPECT().DriverAccepted(gomock.Any(), gomock.Any()).Return(nil),
		deps.onTopFareService.EXPECT().GetOnTopFareAtAccept(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{}, nil),
		deps.tripServices.EXPECT().
			CreateTripFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.Trip{TripID: "trip-id"}, nil),
		deps.tripServices.EXPECT().TryUpdateDistance0(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()),
		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil),
		deps.driverService.EXPECT().
			AssignOrderToDriver(gomock.Any(), params.driverID, params.orderID, false, "trip-id", gomock.Any(), gomock.Any(), gomock.Any()),
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()),
		deps.repSvc.EXPECT().Publish(rep.EventOrderAccepted, gomock.Any()),
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderAccepted, gomock.Any()),
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any()).Return(nil),
		deps.assignmentLog.EXPECT().UpdateDriverTryAcceptAutoAssignedOrder(gomock.Any(), params.orderID, params.driverID).Return(nil),
	)
}

func expectNextStatusOrder(tt require.TestingT, ctx interface{}, deps *foodProviderDeps, expectedOrder *model.Order) {
	deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, order *model.Order, opts ...repository.Option) error {
		require.Equal(tt, order.Status, expectedOrder.Status)
		require.Equal(tt, order.HeadTo, expectedOrder.HeadTo)
		require.Equal(tt, order.Quote.Routes[0].DeliveryStatus, expectedOrder.Quote.Routes[0].DeliveryStatus)
		require.Equal(tt, order.Quote.Routes[1].DeliveryStatus, expectedOrder.Quote.Routes[1].DeliveryStatus)
		return nil
	})
	deps.tripServices.EXPECT().OnOrderChanged(ctx, gomock.Any()).Return(model.Trip{}, nil)
	deps.repSvc.EXPECT().Publish(rep.EventOrderUpdated, gomock.Any()).Return(nil)
	deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderUpdated, gomock.Any()).Return(nil)
}

func expectCompleteOrder(ctx interface{}, deps *foodProviderDeps, existedDriver *model.Driver) {
	deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
	deps.orderService.EXPECT().UpdateAndUpsertRevision(gomock.Any(), gomock.Any()).Return(nil)
	deps.driverRepository.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(existedDriver, nil)
	deps.driverService.EXPECT().AddCompletedOrderQuota(gomock.Any(), gomock.Any()).Return(nil)
	deps.driverRepository.EXPECT().SetCompletedOrderTime(gomock.Any(), gomock.Any()).Return(nil)
	deps.delivery.EXPECT().UpdateStatus(gomock.Any(), gomock.Any()).Return(nil)
	deps.repSvc.EXPECT().Publish(rep.EventOrderCompleted, gomock.Any()).Return(nil)
	deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCompleted, gomock.Any()).Return(nil)
	// TODO: Fix flaky test
	deps.fraudAdvisor.EXPECT().CheckCompleteOrderFraud(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]fraudadvisor.FraudRule{}, nil).AnyTimes()
	deps.statisticService.EXPECT().UpdateDone(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	deps.tripServices.EXPECT().OnOrderChanged(ctx, gomock.Any()).Return(model.Trip{}, nil)
}

func Test_BikeQuoteCustomOntop(t *testing.T) {
	t.Parallel()

	t.Run("create quote successfully", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMBQ-1231541",
			ServiceType: model.ServiceBike,
			Routes: []model.Stop{
				{
					Location: model.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					Location: model.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
					PriceSummary: model.PriceSummary{
						DeliveryFee: model.DeliveryFeeSummary{
							OnTopScheme: []model.OnTopScheme{
								{
									Scheme: model.UserPriceInterventionOnTopScheme,
								},
							},
						},
					},
				},
			},
			DeliveryFee: model.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType model.Service, area string, loc service.Location) ([]model.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []model.RegionCode{model.RegionCode(expectedRegion)}, nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.NewDeliveryFee(30.0, 0.0, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), model.ServiceBike, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(true)
		cot := &transportationV1.GetEstimatedUserFareOntopResponse{}
		deps.priceInterventionClient.EXPECT().GetEstimatedUserFareOnTopRequest(gomock.Any(), gomock.Any()).Return(cot, nil)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), model.ServiceBike).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.NoError(tt, err)

		require.Equal(tt, expectedRegion, actualQ.DistributeRegions.DefaultRegion().String())
	})
}
