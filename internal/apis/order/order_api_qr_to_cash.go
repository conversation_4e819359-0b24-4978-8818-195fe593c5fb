package order

import (
	"context"
	"fmt"
	"time"

	"git.wndv.co/go/logx/v2"
	typesv2 "git.wndv.co/lineman/delivery-service/types/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

func (ordapi *OrderAPI) handleChangedPaymentMethodFromQRToCashBikeServiceType(ctx context.Context, order *model.Order, res *typesv2.ConfirmConvertQRPaymentToCashResponse, currentTime time.Time) error {
	order.SetDriverActionHistory(model.DriverActionChangedPaymentMethodFromQRToCash, currentTime)
	ordapi.handleCouponUnapplied(order, res)
	if err := order.ChangeDeliveryFeePaymentMethod(model.PaymentMethodCash, model.PaymentMethodCash, model.PaymentMethodCash); err != nil {
		logx.Error().Err(err).Msgf("[handleChangedPaymentMethodFromQRToCashBikeServiceType] unable to change PaymentMethodCash delivery fee payment method: [%s]", order.OrderID)
		return err
	}
	if err := ordapi.OrderRepository.UpdateOrder(ctx, order); err != nil {
		logx.Error().Err(err).Msgf("[handleChangedPaymentMethodFromQRToCashBikeServiceType] unable to update order: [%s]", order.OrderID)
		return err
	}

	orderPrice := order.Routes[order.PayAtStop].PriceSummary.Total
	if res.NewPrice != orderPrice {
		warningMsg := fmt.Sprintf("[ChangedPaymentMethodFromQRToCashBikePriceDiff] new price is not equal to order price: [%s] .new price : %v, order price: %v", order.OrderID, res.NewPrice, orderPrice)
		safe.SentryErrorMessage(warningMsg, safe.WithDriverID(order.Driver), safe.WithTripID(order.TripID), safe.WithOrderID(order.OrderID))
	}
	return nil
}

func (ordapi *OrderAPI) handleCouponUnapplied(order *model.Order, res *typesv2.ConfirmConvertQRPaymentToCashResponse) {
	if res.IsCouponUnapplied {
		order.SetDiscount(model.NewDiscountList())
		order.ExperimentalSetDiscount(model.NewDiscountList())
		order.Summary(GetCommissionRate(ordapi.cfg, order.ServiceType.String()), ordapi.cfg.AtomicOrderDBConfig.Get().WithHoldingTax)
	}
}
