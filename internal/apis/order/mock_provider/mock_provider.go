// Code generated by MockGen. DO NOT EDIT.
// Source: ./provider.go

// Package mock_provider is a generated GoMock package.
package mock_provider

import (
	context "context"
	reflect "reflect"
	sync "sync"

	client "git.wndv.co/lineman/delivery-service/pkg/client"
	order "git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockFoodProvider is a mock of FoodProvider interface.
type MockFoodProvider struct {
	ctrl     *gomock.Controller
	recorder *MockFoodProviderMockRecorder
}

// MockFoodProviderMockRecorder is the mock recorder for MockFoodProvider.
type MockFoodProviderMockRecorder struct {
	mock *MockFoodProvider
}

// NewMockFoodProvider creates a new mock instance.
func NewMockFoodProvider(ctrl *gomock.Controller) *MockFoodProvider {
	mock := &MockFoodProvider{ctrl: ctrl}
	mock.recorder = &MockFoodProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFoodProvider) EXPECT() *MockFoodProviderMockRecorder {
	return m.recorder
}

// AcceptAssignmentHandler mocks base method.
func (m *MockFoodProvider) AcceptAssignmentHandler(ctx *gin.Context, assignmentID model.AssignmentID, driverID, deviceID string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AcceptAssignmentHandler", ctx, assignmentID, driverID, deviceID)
}

// AcceptAssignmentHandler indicates an expected call of AcceptAssignmentHandler.
func (mr *MockFoodProviderMockRecorder) AcceptAssignmentHandler(ctx, assignmentID, driverID, deviceID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptAssignmentHandler", reflect.TypeOf((*MockFoodProvider)(nil).AcceptAssignmentHandler), ctx, assignmentID, driverID, deviceID)
}

// AcceptOrderHandler mocks base method.
func (m *MockFoodProvider) AcceptOrderHandler(ctx *gin.Context, order *model.Order, orderID, driverID, deviceID string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AcceptOrderHandler", ctx, order, orderID, driverID, deviceID)
}

// AcceptOrderHandler indicates an expected call of AcceptOrderHandler.
func (mr *MockFoodProviderMockRecorder) AcceptOrderHandler(ctx, order, orderID, driverID, deviceID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptOrderHandler", reflect.TypeOf((*MockFoodProvider)(nil).AcceptOrderHandler), ctx, order, orderID, driverID, deviceID)
}

// AdminCompleteOrder mocks base method.
func (m *MockFoodProvider) AdminCompleteOrder(ctx *gin.Context, order *model.Order, orderID, adminEmail string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AdminCompleteOrder", ctx, order, orderID, adminEmail)
}

// AdminCompleteOrder indicates an expected call of AdminCompleteOrder.
func (mr *MockFoodProviderMockRecorder) AdminCompleteOrder(ctx, order, orderID, adminEmail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminCompleteOrder", reflect.TypeOf((*MockFoodProvider)(nil).AdminCompleteOrder), ctx, order, orderID, adminEmail)
}

// CheckOrderStatusBeforeAdminCompleteOrder mocks base method.
func (m *MockFoodProvider) CheckOrderStatusBeforeAdminCompleteOrder(order *model.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckOrderStatusBeforeAdminCompleteOrder", order)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckOrderStatusBeforeAdminCompleteOrder indicates an expected call of CheckOrderStatusBeforeAdminCompleteOrder.
func (mr *MockFoodProviderMockRecorder) CheckOrderStatusBeforeAdminCompleteOrder(order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckOrderStatusBeforeAdminCompleteOrder", reflect.TypeOf((*MockFoodProvider)(nil).CheckOrderStatusBeforeAdminCompleteOrder), order)
}

// PerformAdminCompleteOrder mocks base method.
func (m *MockFoodProvider) PerformAdminCompleteOrder(ctx *gin.Context, order *model.Order, allowComplete, userProcessPayment bool, comment, admin string, remarks ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order, allowComplete, userProcessPayment, comment, admin}
	for _, a := range remarks {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PerformAdminCompleteOrder", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PerformAdminCompleteOrder indicates an expected call of PerformAdminCompleteOrder.
func (mr *MockFoodProviderMockRecorder) PerformAdminCompleteOrder(ctx, order, allowComplete, userProcessPayment, comment, admin interface{}, remarks ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order, allowComplete, userProcessPayment, comment, admin}, remarks...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PerformAdminCompleteOrder", reflect.TypeOf((*MockFoodProvider)(nil).PerformAdminCompleteOrder), varargs...)
}

// UpdateOrderStatus mocks base method.
func (m *MockFoodProvider) UpdateOrderStatus(ctx *gin.Context, order *model.Order, orderID, driverID string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateOrderStatus", ctx, order, orderID, driverID)
}

// UpdateOrderStatus indicates an expected call of UpdateOrderStatus.
func (mr *MockFoodProviderMockRecorder) UpdateOrderStatus(ctx, order, orderID, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderStatus", reflect.TypeOf((*MockFoodProvider)(nil).UpdateOrderStatus), ctx, order, orderID, driverID)
}

// UpdateOrderStatusV2 mocks base method.
func (m *MockFoodProvider) UpdateOrderStatusV2(ctx context.Context, ord *model.Order, driverID string, driverLocation *model.Location, pods []client.ProofOfDeliveryImage, tripMtx *sync.Mutex) (*order.UpdateStatusRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderStatusV2", ctx, ord, driverID, driverLocation, pods, tripMtx)
	ret0, _ := ret[0].(*order.UpdateStatusRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateOrderStatusV2 indicates an expected call of UpdateOrderStatusV2.
func (mr *MockFoodProviderMockRecorder) UpdateOrderStatusV2(ctx, ord, driverID, driverLocation, pods, tripMtx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderStatusV2", reflect.TypeOf((*MockFoodProvider)(nil).UpdateOrderStatusV2), ctx, ord, driverID, driverLocation, pods, tripMtx)
}

// UpdatePrice mocks base method.
func (m *MockFoodProvider) UpdatePrice(ctx *gin.Context, order *model.Order, orderID, driverID string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdatePrice", ctx, order, orderID, driverID)
}

// UpdatePrice indicates an expected call of UpdatePrice.
func (mr *MockFoodProviderMockRecorder) UpdatePrice(ctx, order, orderID, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePrice", reflect.TypeOf((*MockFoodProvider)(nil).UpdatePrice), ctx, order, orderID, driverID)
}
