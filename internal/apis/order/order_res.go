package order

import (
	"regexp"
	"strings"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/mathutil"
)

var RegxGroupPrefixMap = map[int]string{
	1: "ตำบล",
	2: "แขวง",
	3: "Khwaeng",
	4: "Tambon",
}

var covid19WarningNote = " || 😷😷 รายการนี้ถูกสั่งซื้อเพื่อนำส่งให้กลุ่มกักตัว/ติดเชื้อ กรุณาติดต่อผู้รับทางโทรศัพท์เท่านั้นเพื่อแจ้งสถานะและจุดนำส่ง สวมหน้ากากอนามัย งดการสัมผัสทุกกรณี 😷😷"

// subdistrictRegxp captures (Thai) subdistrict from a Thai or English address
// The reason for the separation of ตำบล and แขวง is there exists ตำบล that ends in เขต.
var subdistrictRegxp = regexp.MustCompile("(?:(?:ตำบล ?(.+?)(?:อำเภอ|,| ))|(?:แขวง ?(.+?)(?:เขต|,| ))|(?:Khwaeng ?(.+?)(?:Khet|,))|(?:Tambon ?(.+?)(?:Amphoe|,)))")

type DriverPaymentMethod string

const (
	DriverPaymentMethodCashAdvance         DriverPaymentMethod = "CASH_ADVANCE"
	DriverPaymentMethodCashCollection      DriverPaymentMethod = "CASH_COLLECTION"
	DriverPaymentMethodCashAdvanceEpayment DriverPaymentMethod = "CASH_ADVANCE_EPAYMENT"
	DriverPaymentMethodUnknown             DriverPaymentMethod = "UNKNOWN"
)

type OrderDetailRes struct {
	OrderID  string `json:"orderId"`
	QuoteID  string `json:"quoteId"`
	UserID   string `json:"userId"`
	DriverID string `json:"driver"`

	ServiceType model.Service    `json:"serviceType"`
	Region      model.RegionCode `json:"region"`
	// TODO PaymentMethod has become misleading and redundant. Client doesn't use it. Should remove its dependency from food, admin, etc. then remove this field.
	// deprecated
	PaymentMethod       model.PaymentMethod `json:"paymentMethod"`
	DriverPaymentMethod DriverPaymentMethod `json:"driverPaymentMethod"`
	Distance            types.Distance      `json:"distance"`
	Options             model.OrderOptions  `json:"options"`
	Status              model.Status        `json:"status"`
	HeadTo              int                 `json:"headTo"`
	RatingScore         uint32              `json:"ratingScore"`
	Comment             string              `json:"comment"`
	CancelDetail        model.CancelDetail  `json:"cancelDetail"`
	NoteToDriver        string              `json:"noteToDriver"`
	TipAmount           types.Money         `json:"tipAmount"`
	OnGoingTipAmount    types.Money         `json:"onGoingTipAmount"`
	HistoryLocation     []HistoryLocation   `json:"historyLocation"`

	// Routes ...
	Routes               []StopRes            `json:"routes"`
	History              map[string]time.Time `json:"history"`
	AllDeliveringHistory []HistoryEntry       `json:"allDeliveringHistory"`

	Commission          types.Money `json:"commission"`
	CommissionRate      int         `json:"commissionRate"`
	WithholdingTax      types.Money `json:"withholdingTax"`
	OnTopWithholdingTax types.Money `json:"onTopWithholdingTax"`
	OnTopFare           types.Money `json:"onTopFare"`
	EGSOnTopFare        types.Money `json:"egsOnTopFare"`
	TotalOnTopFare      types.Money `json:"totalOnTopFare"`
	OnTopFareCommission types.Money `json:"onTopFareCommission"`
	TotalTax            types.Money `json:"totalTax"`
	Coin                int         `json:"coin"`

	SpecialEvent []string `json:"specialEvent"`

	ExpireAt  time.Time `json:"expireAt"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`

	IsLowCredit       bool `json:"isLowCredit,omitempty"`
	RevenueAgentModel bool `json:"revenueAgentModel"`

	IsB2B            bool   `json:"isB2B"`
	QueuedAfterOrder string `json:"queuedAfterOrder"`

	PayAtStop                   int                         `json:"payAtStop"`
	DeliveringPhotoStatus       model.DeliveringPhotoStatus `json:"deliveringPhotoStatus"`
	DeliveringPhotoURLs         []string                    `json:"deliveringPhotoUrls"`
	IsRequireDeliveringPhotoURL bool                        `json:"isRequireDeliveredPhotoURL"`
	IsAutoAssigned              bool                        `json:"isAutoAssigned"`

	// Verify driver credentials
	ShouldVerify bool `json:"shouldVerify"`

	PreviousCancelDetails  []DriverCanceledDetails `json:"previousCancelDetails"`
	HasPickupDistanceOnTop bool                    `json:"hasPickupDistanceOnTop"`

	DeferDurationInSeconds             float64              `json:"deferDurationInSeconds"`
	IsRain                             bool                 `json:"isRain"`
	DriverActionHistory                map[string]time.Time `json:"driverActionHistory"`
	IsSkipIncompleteQRPromptpayPayment bool                 `json:"isSkipIncompleteQRPromptpayPayment"`
}

type DriverCanceledDetails struct {
	model.CancelDetail

	// the driver associated with the order before the cancellation
	DriverID string `json:"driverID"`

	// the delivery round of the order
	DeliveryRound int `json:"deliveryRound"`

	// cancellation timestamp
	CanceledAt time.Time `json:"canceledAt"`
}

type HistoryEntry struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
}

type HistoryLocation struct {
	Status   string                      `json:"status"`
	Location model.LocationWithUpdatedAt `json:"location"`
}

func NewOrderDetailRes(order model.Order, revisions []model.Order, drv *model.Driver, dt *model.DriverTransaction, config OrderAPIConfig, contingencyCfg ContingencyConfig, negativeBalanceCfg model.NegativeBalanceGroupConfig, tierNegBalCfg model.TierNegativeBalanceConfig, useRevampedStatus bool) OrderDetailRes {
	if len(order.Routes) <= 1 {
		return OrderDetailRes{}
	}

	priceSummary := order.PriceSummary()
	var isLowCredit bool
	if config.CashCollectionEnabled && config.CheckGetOrderDriverBalanceEnoughEnabled && order.IsCashCollection() && dt != nil {
		enough, _, outstanding := model.ValidateDriverBalanceEnough(*dt, &order)
		isLowCredit = !enough
		if !enough && isOutstandingWithinMinimumCreditBalance(dt.DriverID, outstanding, contingencyCfg, negativeBalanceCfg,
			tierNegBalCfg, drv.DriverTier) {
			isLowCredit = false
		}
	}

	specialNote := []model.SpecialNote{
		{
			Service:         model.ServiceMessenger,
			PrefixCondition: "WEHELP",
			Note:            covid19WarningNote,
		},
	}

	var allDeliveringHistory []HistoryEntry
	var previousCancelDetails []DriverCanceledDetails

	for _, r := range revisions {
		allDeliveringHistory = append(allDeliveringHistory, mapHistoryToRes(r.History)...)

		// only append to `previousCancelDetails` only reassign canceled order
		if r.Status == model.StatusCanceled && r.CancelDetail.IsReassign {
			previousCancelDetails = append(previousCancelDetails, DriverCanceledDetails{
				CancelDetail:  r.CancelDetail,
				DriverID:      r.Driver,
				DeliveryRound: r.DeliveringRound,
				CanceledAt:    r.History[string(model.StatusCanceled)],
			})
		}
	}

	if !(order.Status == model.StatusCompleted || order.Status == model.StatusExpired || order.Status == model.StatusCanceled) || len(revisions) == 0 {
		allDeliveringHistory = append(allDeliveringHistory, mapHistoryToRes(order.History)...)
	}

	var tipAmount types.Money = order.TipAmount
	var onGoingTipAmount types.Money
	if len(order.Tips) > 0 {
		tipAmount, onGoingTipAmount = order.Tips.CalculateTipAmount()
	}

	egsInstallmentOnTop := types.NewMoney(order.PriceSummary().DeliveryFee.CalculateInstallmentOnTopFare())
	normalOnTop := types.Money(priceSummary.DeliveryFee.OnTopFare).Sub(egsInstallmentOnTop)

	res := &OrderDetailRes{
		OrderID:     order.OrderID,
		QuoteID:     order.QuoteID,
		UserID:      order.UserID,
		DriverID:    order.Driver,
		ServiceType: order.ServiceType,
		Region:      order.Region,
		// TODO PaymentMethod has become misleading and redundant. Client doesn't use it. Should remove its dependency from food, admin, etc. then remove this field.
		// The reason I used ItemFee's paymentmethod here is to preserve behaviour.
		PaymentMethod:                      priceSummary.ItemFee.PaymentMethod,
		Distance:                           order.Distance,
		Options:                            order.Options,
		Status:                             order.Status,
		HeadTo:                             order.HeadTo,
		RatingScore:                        order.RatingScore,
		TipAmount:                          tipAmount,
		OnGoingTipAmount:                   onGoingTipAmount,
		Comment:                            order.Comment,
		CancelDetail:                       order.CancelDetail,
		History:                            order.History,
		AllDeliveringHistory:               allDeliveringHistory,
		Commission:                         types.Money(priceSummary.DeliveryFee.Commission),
		CommissionRate:                     int(priceSummary.DeliveryFee.CommissionRate * 100),
		WithholdingTax:                     types.Money(priceSummary.DeliveryFee.WithholdingTax),
		OnTopWithholdingTax:                types.Money(priceSummary.DeliveryFee.OnTopWithholdingTax),
		OnTopFare:                          normalOnTop,
		EGSOnTopFare:                       egsInstallmentOnTop,
		TotalOnTopFare:                     normalOnTop + egsInstallmentOnTop,
		OnTopFareCommission:                types.Money(priceSummary.DeliveryFee.OnTopCommissionFare),
		Coin:                               priceSummary.DeliveryFee.Coin,
		TotalTax:                           types.Money(priceSummary.DeliveryFee.TotalTax()),
		NoteToDriver:                       order.NoteToDriver,
		SpecialEvent:                       order.SpecialEvent,
		ExpireAt:                           order.ExpireAt,
		CreatedAt:                          order.CreatedAt,
		UpdatedAt:                          order.UpdatedAt,
		DriverPaymentMethod:                driverPaymentMethod(priceSummary, order),
		IsLowCredit:                        isLowCredit,
		RevenueAgentModel:                  !order.RevenuePrincipalModel,
		IsB2B:                              order.IsB2B,
		QueuedAfterOrder:                   order.QueuedAfterOrder,
		ShouldVerify:                       order.ShouldVerify,
		PayAtStop:                          order.GetPayAtStop(),
		DeliveringPhotoStatus:              order.DeliveringPhotoStatus,
		DeliveringPhotoURLs:                order.DeliveringPhotoURLs,
		IsRequireDeliveringPhotoURL:        order.IsRequireDeliveringPhotoURL,
		IsAutoAssigned:                     order.IsAutoAssigned,
		PreviousCancelDetails:              previousCancelDetails,
		HasPickupDistanceOnTop:             order.HasPickupDistanceOnTopFare(),
		DeferDurationInSeconds:             order.DeferDuration.Seconds(),
		DriverActionHistory:                order.DriverActionHistory,
		IsSkipIncompleteQRPromptpayPayment: order.IsSkipIncompleteQRPromptpayPayment,
	}

	if len(res.DeliveringPhotoURLs) == 0 {
		res.DeliveringPhotoURLs = []string{}
	}

	historyLocation := make([]HistoryLocation, 0, len(order.HistoryLocation))
	for status, data := range order.HistoryLocation {
		historyLocation = append(historyLocation, HistoryLocation{
			Status:   status,
			Location: data,
		})
	}

	res.HistoryLocation = historyLocation

	res.IsRequireDeliveringPhotoURL = order.IsRequireDeliveringPhotoURL &&
		order.Routes[order.RevampHeadTo()].Pauses[model.PauseDeliveringPhoto]

	if order.IsRequireDeliveringPhotoURL {
		res.IsRain = order.Routes[order.RevampHeadTo()].IsRain
		// if `IsRain` = false, do nothing with `IsRequireDeliveringPhotoURL`
		if res.IsRain {
			res.IsRequireDeliveringPhotoURL = false
		}
	}

	res.Routes = make([]StopRes, len(order.Routes))
	for i, r := range order.Routes {
		res.Routes[i] = *res.newStopRes(r)

		if !config.EnableEarning {
			res.Routes[i].PriceSummary.Earning = nil
		}
	}

	if order.IsCashCollection() {
		res.Routes[order.GetPayAtStop()].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCashCollection
	}

	res.calculateShiftModel(drv, &order, config)
	res.calculateHalfHalf(&order)

	discounts := res.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee.Discounts

	for _, ds := range discounts {
		for _, n := range specialNote {
			if strings.HasPrefix(ds.Code, n.PrefixCondition) {
				res.NoteToDriver += n.Note
			}
		}
	}

	if len(order.VerifiedRiderPhotoUrls) > 0 {
		res.ShouldVerify = false
	}

	if !useRevampedStatus {
		res.Status = order.GetLegacyStatus()
	}

	if config.AtomicOrderDBConfig.Get().DisableVOSPhoto {
		res.ShouldVerify = false
		res.IsRequireDeliveringPhotoURL = false
		res.IsRain = false
	}

	return *res
}

type StopRes struct {
	ID                    string                  `json:"id"`
	Name                  string                  `json:"name"`
	Address               string                  `json:"address"`
	Subdistrict           string                  `json:"subdistrict,omitempty"`
	Phones                []string                `json:"phones"`
	Location              model.Location          `json:"location"`
	Memo                  string                  `json:"memo,omitempty"`
	MemoTH                string                  `json:"memoTH,omitempty"`
	MemoType              model.MemoType          `json:"memoType,omitempty"`
	PickingItems          []model.Item            `json:"pickingItems"`
	DeliveryItems         []model.Item            `json:"deliveryItems"`
	CollectPayment        bool                    `json:"collectPayment"`
	ItemsPrice            float64                 `json:"itemsPrice"`
	PriceSummary          PriceSummaryRes         `json:"priceSummary,omitempty"`
	EstimatedDeliveryTime model.DurationSecond    `json:"estimatedDeliveryTime,omitempty"`
	Distance              types.Distance          `json:"distance,omitempty"`
	DeliveryStatus        model.DeliveryStatus    `json:"deliveryStatus,omitempty"`
	Info                  model.StopInfoCollector `json:"info,omitempty"`
	DoNotDisturb          bool                    `json:"doNotDisturb"`
	Pauses                model.PauseSet          `json:"pauses,omitempty"`
	Point                 *model.Point            `json:"point,omitempty"`
	// UI still shows the qr detail section, while a rider is allowed to bypass QR payment process, completing order.
	// Only valid when stop contains `PauseQRPayment`
	BypassQRPayment bool `json:"bypassQRPayment,omitempty"`
}

func (os *OrderDetailRes) newStopRes(s model.Stop) *StopRes {
	return &StopRes{
		ID:                    s.ID,
		Name:                  s.Name,
		Address:               s.Address,
		Phones:                s.Phones,
		Location:              s.Location,
		Memo:                  s.Memo,
		MemoType:              s.MemoType,
		MemoTH:                s.MemoTH,
		PickingItems:          s.PickingItems,
		DeliveryItems:         s.DeliveryItems,
		CollectPayment:        s.CollectPayment,
		ItemsPrice:            s.ItemsPrice,
		PriceSummary:          *os.PriceSummaryRes(s.PriceSummary),
		EstimatedDeliveryTime: s.EstimatedDeliveryTime,
		Distance:              s.Distance,
		DeliveryStatus:        s.DeliveryStatus,
		Info:                  s.Info,
		DoNotDisturb:          s.DoNotDisturb,
		Pauses:                s.Pauses,
		Point:                 s.Point,
	}
}

type OrderHistoryRes struct {
	OrderID  string `json:"orderId"`
	DriverID string `json:"driver"`

	ServiceType         model.Service         `json:"serviceType"`
	Status              model.Status          `json:"status"`
	NoteToDriver        string                `json:"noteToDriver"`
	Routes              []OrderHistoryStopRes `json:"routes"`
	TotalTax            types.Money           `json:"totalTax"`
	TipAmount           types.Money           `json:"tipAmount"`
	SpecialEvent        []string              `json:"specialEvent"`
	DriverPaymentMethod DriverPaymentMethod   `json:"driverPaymentMethod"`
	RevenueAgentModel   bool                  `json:"revenueAgentModel"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type OrderHistoryStopRes struct {
	Name           string                  `json:"name,omitempty"`
	Address        string                  `json:"address,omitempty"`
	Subdistrict    string                  `json:"subdistrict,omitempty"`
	Location       model.Location          `json:"location"`
	Memo           string                  `json:"memo,omitempty"`
	PickingItems   []model.Item            `json:"pickingItems"`
	DeliveryItems  []model.Item            `json:"deliveryItems"`
	CollectPayment bool                    `json:"collectPayment"`
	ItemsPrice     float64                 `json:"itemsPrice"`
	PriceSummary   PriceSummaryRes         `json:"priceSummary,omitempty"`
	DeliveryStatus model.DeliveryStatus    `json:"deliveryStatus,omitempty"`
	Info           model.StopInfoCollector `json:"info,omitempty"`
}

func (os *OrderDetailRes) calculateHalfHalf(order *model.Order) {
	earning := os.Routes[order.GetPayAtStop()].PriceSummary.Earning
	if earning == nil {
		return
	}

	if !order.IsHalfHalf() {
		return
	}
	os.Routes[order.GetPayAtStop()].PriceSummary.Total = earning.Cash
}

func (os *OrderDetailRes) calculateShiftModel(driver *model.Driver, order *model.Order, cfg OrderAPIConfig) {
	if driver == nil || order == nil {
		return
	}

	if os.Status != model.StatusAssigningDriver {
		return
	}

	if len(order.Shifts) == 0 || len(driver.Shifts) == 0 {
		return
	}

	if !os.validService(order.ServiceType) {
		return
	}

	dc := order.GetCurrentShiftDiscountPrice(driver)

	os.Routes[order.GetPayAtStop()].PriceSummary.Earning.Base -= dc
	os.Routes[order.GetPayAtStop()].PriceSummary.Earning.Total -= dc
	os.Routes[order.GetPayAtStop()].PriceSummary.Earning.Wallet -= dc

	base := os.Routes[order.GetPayAtStop()].PriceSummary.Earning.Base
	ontop := os.Routes[order.GetPayAtStop()].PriceSummary.Earning.OnTop
	ontopTax := order.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee.OnTopWithholdingTax
	baseTax := mathutil.RoundFloat64(base * cfg.AtomicOrderDBConfig.Get().WithHoldingTax)
	os.Routes[order.GetPayAtStop()].PriceSummary.Earning.TotalAfterTax = (base + ontop) - (ontopTax + baseTax)
}

func (os OrderDetailRes) validService(service model.Service) bool {
	return service == model.ServiceFood || service == model.ServiceMart || service == model.ServiceMessenger
}

func (os OrderDetailRes) ToOrderHistoryRes() OrderHistoryRes {
	orderHistoryStopRes := make([]OrderHistoryStopRes, len(os.Routes))
	for i, r := range os.Routes {
		isRemoveSensitiveInfo := os.ServiceType.IsRevampedStatus() || (os.ServiceType == model.ServiceFood || os.ServiceType == model.ServiceMart) && i == os.PayAtStop
		orderHistoryStopRes[i] = r.ToOrderHistoryStopRes(isRemoveSensitiveInfo)
	}

	return OrderHistoryRes{
		OrderID:             os.OrderID,
		DriverID:            os.DriverID,
		ServiceType:         os.ServiceType,
		Status:              os.Status,
		NoteToDriver:        os.NoteToDriver,
		Routes:              orderHistoryStopRes,
		SpecialEvent:        os.SpecialEvent,
		DriverPaymentMethod: os.DriverPaymentMethod,
		TotalTax:            os.TotalTax,
		TipAmount:           os.TipAmount,
		CreatedAt:           os.CreatedAt,
		UpdatedAt:           os.UpdatedAt,
		RevenueAgentModel:   os.RevenueAgentModel,
	}
}

func (s StopRes) ToOrderHistoryStopRes(isRemoveSensitiveInfo bool) OrderHistoryStopRes {
	os := OrderHistoryStopRes{
		Name:           s.Name,
		Address:        s.Address,
		Subdistrict:    s.Subdistrict,
		Location:       s.Location,
		Memo:           s.Memo,
		PickingItems:   s.PickingItems,
		DeliveryItems:  s.DeliveryItems,
		CollectPayment: s.CollectPayment,
		ItemsPrice:     s.ItemsPrice,
		PriceSummary:   s.PriceSummary,
		DeliveryStatus: s.DeliveryStatus,
		Info:           s.Info,
	}

	if isRemoveSensitiveInfo {
		os.Name = ""
		os.Address = ""
		os.Subdistrict = ""
		os.Location = model.Location{}
	}

	return os
}

func (os *OrderDetailRes) ExtractSubdistrict() {
	for i, r := range os.Routes {
		os.Routes[i].Subdistrict = extractSubdistrict(r.Address)
	}
}

func extractSubdistrict(addr string) string {
	for i, group := range subdistrictRegxp.FindStringSubmatch(addr) {
		if i == 0 { // first group is useless full match
			continue
		}
		subd := strings.TrimSpace(group)
		if len(subd) > 0 {
			return RegxGroupPrefixMap[i] + " " + subd
		}
	}
	return ""
}

func (os *OrderDetailRes) SetBypassQRPayment() {
	os.Routes[os.PayAtStop].BypassQRPayment = true
}

func (os *OrderDetailRes) OmitPauseQRPayment() {
	if os.Routes[os.PayAtStop].Pauses != nil {
		delete(os.Routes[os.PayAtStop].Pauses, model.PauseQRPayment)
	}
}

type PriceSummaryRes struct {
	DeliveryFee DeliveryFeeRes `json:"deliveryFee"`
	ItemFee     ItemFeeRes     `json:"itemFee"`
	// TODO OrderPrice is redundant. Client doesn't use it. Should remove its dependency from food, admin, etc. then remove this field.
	// deprecated
	OrderPrice float64        `json:"orderPrice"`
	Total      float64        `json:"total"`
	Earning    *model.Earning `json:"earning,omitempty"`
}

func HasNotEPayment(deliveryFeePaymentMethod model.PaymentMethod) bool {
	return !deliveryFeePaymentMethod.IsLINEMANEPaymentType()
}

func IsEPaymentNotResolved(deliveryFeePaymentMethod, itemFeePaymentMethod model.PaymentMethod) bool {
	isCashOnDelivery := deliveryFeePaymentMethod == model.PaymentMethodCash

	return isCashOnDelivery && itemFeePaymentMethod.IsLINEMANEPaymentType()
}

func (os *OrderDetailRes) PriceSummaryRes(ps model.PriceSummary) *PriceSummaryRes {
	res := &PriceSummaryRes{
		DeliveryFee: *os.deliveryFeeSummaryRes(ps.DeliveryFee),
		ItemFee:     *os.itemFeeRes(ps.ItemFee),
		OrderPrice:  ps.ItemFee.ItemFee, // TODO OrderPrice is redundant. Client doesn't use it. Should remove its dependency from food, admin, etc. then remove this field.
	}

	total := 0.0
	if !res.ItemFee.PaymentMethod.IsEPayment() {
		total += ps.ItemFee.Total
	}

	if !res.DeliveryFee.PaymentMethod.IsEPayment() {
		if !os.RevenueAgentModel {
			total += ps.DeliveryFee.UserDeliveryFee
		} else {
			total += ps.DeliveryFee.Total
		}
	}

	res.Total = total

	installmentEGSOnTop := ps.DeliveryFee.CalculateInstallmentOnTopFare()
	totalOnTop := ps.DeliveryFee.OnTopFare // normalOnTop + installmentEGSOnTop
	normalOnTop := totalOnTop - installmentEGSOnTop
	totalAdditional := ps.DeliveryFee.AdditionalServiceFee.Total

	// E-payment
	wallet := ps.DeliveryFee.SubTotal + totalOnTop + totalAdditional
	cash := 0.0

	// cash
	if HasNotEPayment(ps.DeliveryFee.PaymentMethod) {
		if !os.RevenueAgentModel { // principle model
			wallet = ps.DeliveryFee.SubTotal + totalOnTop
			cash = ps.DeliveryFee.UserDeliveryFee
		} else {
			wallet = ps.DeliveryFee.TotalDiscount() + totalOnTop
			cash = ps.DeliveryFee.Total
		}
	}

	// E-payment cannot resolve
	if IsEPaymentNotResolved(ps.DeliveryFee.PaymentMethod, ps.ItemFee.PaymentMethod) {
		if !os.RevenueAgentModel { // principle model
			wallet = ps.DeliveryFee.SubTotal + totalOnTop
			cash = ps.DeliveryFee.UserDeliveryFee
		} else {
			wallet = ps.DeliveryFee.TotalDiscount() + totalOnTop
			cash = ps.DeliveryFee.Total
		}
	}

	if !os.RevenueAgentModel { // principle model
		res.Earning = &model.Earning{
			Cash:           cash,
			Wallet:         wallet,
			Total:          wallet,
			Base:           ps.DeliveryFee.SubTotal,
			OnTop:          normalOnTop,
			EGSOnTopFare:   installmentEGSOnTop,
			TotalOnTopFare: totalOnTop,
			TotalAfterTax:  ps.DeliveryFee.TotalEarningAfterDeduction(!os.RevenueAgentModel),
		}
	} else {
		res.Earning = &model.Earning{
			Cash:              cash,
			Wallet:            wallet,
			Total:             ps.DeliveryFee.SubTotal + totalOnTop + totalAdditional,
			Base:              ps.DeliveryFee.SubTotal,
			OnTop:             normalOnTop,
			EGSOnTopFare:      installmentEGSOnTop,
			TotalOnTopFare:    totalOnTop,
			AdditionalService: totalAdditional,
			TotalAfterTax:     ps.DeliveryFee.TotalEarningAfterDeduction(!os.RevenueAgentModel),
		}
	}

	return res
}

type DeliveryFeeRes struct {
	BaseFee       float64            `json:"baseFee"`
	RoadFee       float64            `json:"roadFee"`
	AdditionalFee map[string]float64 `json:"additionalFee"`
	// Deprecated: Use discounts
	Coupon               model.Coupon                   `json:"coupon"`
	Discounts            []model.Discount               `json:"discounts"`
	ExtraCharges         []model.ExtraCharge            `json:"extracharges"`
	PaymentMethod        model.PaymentMethod            `json:"method"`
	TotalDiscount        float64                        `json:"totalDiscount"`
	SubTotal             float64                        `json:"subTotal"`
	Total                float64                        `json:"total"`
	UserDeliveryFee      float64                        `json:"userDeliveryFee"`
	AdditionalServiceFee model.AdditionalServiceSummary `json:"additionalServiceFee"`
	DistanceUnitFee      float64                        `json:"distanceUnitFee"`
	StartingFee          float64                        `json:"startingFee"`
	QRPromptPayInfo      model.QRPromptPayInfo          `json:"qrPromptPayInfo" bson:"qr_promptpay_info"` // Valid for qr payment method bike service
}

func (os *OrderDetailRes) deliveryFeeSummaryRes(df model.DeliveryFeeSummary) *DeliveryFeeRes {
	res := &DeliveryFeeRes{
		PaymentMethod:        df.PaymentMethod,
		BaseFee:              df.BaseFee,
		RoadFee:              df.RoadFee,
		AdditionalFee:        df.AdditionalFee,
		Discounts:            df.Discounts,
		ExtraCharges:         df.ExtraCharges,
		TotalDiscount:        df.Discounts.Total(),
		SubTotal:             df.SubTotal,
		Total:                df.Total,
		AdditionalServiceFee: df.AdditionalServiceFee,
		DistanceUnitFee:      df.DistanceUnitFee,
		StartingFee:          df.StartingFee,
		QRPromptPayInfo:      df.QRPromptPayInfo,
	}

	if !os.RevenueAgentModel {
		res.UserDeliveryFee = df.UserDeliveryFee
	}

	if df.Discounts != nil && len(df.Discounts) > 0 {
		d := df.Discounts[0]
		res.Coupon = model.Coupon{
			Code:         d.Code,
			Discount:     df.Discounts.Total(),
			MaxDeduction: 0,
		}
		res.Discounts = df.Discounts
	} else if df.Coupon != (model.Coupon{}) {
		res.Coupon = df.Coupon

		var t model.DiscountType
		if df.Coupon.IsGP() {
			t = model.DiscountTypeSubsidize
		} else {
			t = model.DiscountTypeCoupon
		}

		res.Discounts = append(model.NewDiscountList(), model.Discount{
			Type:     t,
			Category: "",
			Code:     df.Coupon.Code,
			Discount: df.Coupon.Discount,
		})
	}

	return res
}

type ItemFeeRes struct {
	Discounts     []model.Discount    `json:"discounts"`
	PaymentMethod model.PaymentMethod `json:"method"`
	TotalDiscount float64             `json:"totalDiscount"`
	SubTotal      float64             `json:"subTotal"`
	Total         float64             `json:"total"`

	QuoteItemFeeBeforeDiscount float64 `json:"quoteItemFeeBeforeDiscount"`
}

func (os *OrderDetailRes) itemFeeRes(itemFee model.ItemFeeSummary) *ItemFeeRes {
	res := &ItemFeeRes{
		PaymentMethod:              itemFee.PaymentMethod,
		Discounts:                  itemFee.Discounts,
		TotalDiscount:              itemFee.Discounts.Total(),
		SubTotal:                   itemFee.SubTotal,
		Total:                      itemFee.Total,
		QuoteItemFeeBeforeDiscount: itemFee.QuoteItemFeeBeforeDiscount,
	}

	return res
}

func NewUpdateStatusInfoRes(transAmount, outstanding types.Money) UpdateStatusInfoRes {
	var code UpdateStatusCode
	if transAmount.Equal(0) && outstanding.Equal(0) {
		code = CreditEnoughCashUpdateStatusCode
	} else {
		code = TransferredWalletUpdateStatusCode
	}
	return UpdateStatusInfoRes{
		Code:              code,
		TransferredAmount: transAmount.Float64(),
		OutstandingAmount: outstanding.Float64(),
	}
}

func NewUpdateStatusResponse(status model.Status, transAmount, outstanding types.Money) *UpdateStatusRes {
	return &UpdateStatusRes{
		Status: status,
		Info:   NewUpdateStatusInfoRes(transAmount, outstanding),
	}
}

func driverPaymentMethod(ps model.PriceSummary, order model.Order) DriverPaymentMethod {
	if order.IsCashAdvancementEpayment() {
		return DriverPaymentMethodCashAdvanceEpayment
	}

	itemFeeDiscounts := ps.ItemFee.Discounts.ItemFeeDiscounts()
	for _, discount := range itemFeeDiscounts {
		if discount.Type == model.DiscountTypeCouponAdvance {
			return DriverPaymentMethodCashAdvance
		}
	}

	if order.IsCashCollection() {
		return DriverPaymentMethodCashCollection
	}

	return DriverPaymentMethodUnknown
}

type UpdateStatusCode string

const (
	TransferredWalletUpdateStatusCode UpdateStatusCode = "TRANSFERRED_WALLET"
	CreditEnoughCashUpdateStatusCode  UpdateStatusCode = "ENOUGH_CREDIT"
)

type UpdateStatusInfoRes struct {
	Code              UpdateStatusCode `json:"code"`
	Message           string           `json:"message"`
	TransferredAmount float64          `json:"transferredAmount"`
	OutstandingAmount float64          `json:"outstandingAmount,omitempty"`
}

type UpdateStatusRes struct {
	Status model.Status        `json:"status"`
	Info   UpdateStatusInfoRes `json:"info"`
}

type AcceptAssignmentRes struct {
	TripID         string           `json:"tripId"`
	PartialSuccess bool             `json:"partialSuccess"`
	DriverType     model.DriverType `json:"driverType"`
}

type AcceptOrderRes struct {
	TripID string              `json:"tripId"`
	Status model.Status        `json:"status"`
	Info   UpdateStatusInfoRes `json:"info"`
}

func NewAcceptOrderRes(order model.Order, moneyTransferWallet types.Money) AcceptOrderRes {
	return AcceptOrderRes{
		TripID: order.TripID,
		Status: order.Status,
		Info:   NewUpdateStatusInfoRes(moneyTransferWallet, 0.0),
	}
}

type CancelReasonsResponse []reasonResponse

type reasonResponse struct {
	Name               string `json:"name"`
	Label              string `json:"label"`
	Target             string `json:"target"`
	Instruction        string `json:"instruction,omitempty"`
	PreviewImageURL    string `json:"previewImageUrl,omitempty"`
	CallToActionText   string `json:"callToActionText,omitempty"`
	WarningMessage     string `json:"warningMessage,omitempty"`
	HintTitle          string `json:"hintTitle,omitempty"`
	HintMessage        string `json:"hintMessage,omitempty"`
	MaximumUploadPhoto int    `json:"maximumUploadPhoto,omitempty"`
}

func (r *reasonResponse) injectPhoto(photo model.Photo) {
	r.Instruction = photo.Instruction
	r.PreviewImageURL = photo.PreviewImageURL
	r.CallToActionText = photo.CallToActionText
}

type resReason model.CancelReason

func (r resReason) response() reasonResponse {
	res := reasonResponse{
		Name:  r.Name,
		Label: r.Label,
	}
	if r.CancelByRider.IsAllowed {
		res.Target = "LM_DRIVER"
		if r.CancelByRider.IsPhotoRequired {
			res.injectPhoto(r.Photo)
			res.MaximumUploadPhoto = r.CancelByRider.MaximumUploadPhoto
		}
	} else {
		res.Target = "ZENDESK"
		if r.IsPhotoRequiredForZendesk {
			res.injectPhoto(r.Photo)
		}
	}
	res.WarningMessage = r.WarningMessage

	if r.CancelByRider.BanDurationInMinute > 0 {
		res.HintTitle = r.HintTitle
		res.HintMessage = r.HintMessage
	}

	return res
}

type resReasons []model.CancelReason

func (r resReasons) response() CancelReasonsResponse {
	res := make([]reasonResponse, len(r))
	for i, reason := range r {
		res[i] = resReason(reason).response()
	}
	return res
}

type QRPaymentDetailResponse struct {
	QRCode    string    `json:"qrCode"`
	PromptPay string    `json:"promptPay"`
	Amount    *float64  `json:"amount"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"createdAt"`
	ExpiredAt time.Time `json:"expiredAt"`
	PayeeName string    `json:"payeeName"`
}

type ValidateConvertQRToCashResponse struct {
	DriverLabel           string   `json:"driverLabel"`
	CustomerOriginalPrice *float64 `json:"customersOriginalPrice,omitempty"`
	CustomerNewPrice      float64  `json:"customersNewPrice"`
	DriverCreditDeduction *float64 `json:"driversCreditDeduction,omitempty"`
}

func (res *ValidateConvertQRToCashResponse) GenerateDriverLabel(order *model.Order, newUserPriceIncludeDeliveryFee float64, isCashCollection bool) {
	// Price that Driver paid for item in `ADVANCEMENT` Money Flow
	cashPay := order.PriceSummary().ItemFee.SubTotal
	// Price that User paid for item only
	newUserPriceExcludeDeliveryFee := newUserPriceIncludeDeliveryFee - order.PriceSummary().DeliveryFee.UserDeliveryFee

	if isCashCollection {
		if order.RevenuePrincipalModel {
			res.DriverCreditDeduction = &newUserPriceIncludeDeliveryFee
		}
	}

	res.DriverLabel = getDriverLabel(order, newUserPriceExcludeDeliveryFee, cashPay)
}

func getDriverLabel(order *model.Order, newUserPriceExcludeDeliveryFee, cashPay float64) string {
	if order.Options.DriverMoneyFlow == model.FlowCashAdvancementEpayment {
		if newUserPriceExcludeDeliveryFee < cashPay {
			return "กรุณาเก็บเงินสดค่าอาหารจากลูกค้า\nคุณจะได้รับส่วนต่างที่คุณจ่ายร้านคืนผ่านวอลเล็ต"
		}
		return "กรุณาเก็บเงินสดค่าอาหารจากลูกค้า"
	}

	if order.ServiceType == model.ServiceBike {
		return "กรุณาเก็บเงินสดค่าโดยสารจากลูกค้า"
	}

	if order.ServiceType == model.ServiceMart {
		return `หากชำระด้วยเงินสด คุณจะถูกตัดค่าสินค้าผ่านเครดิต
กรุณาเก็บเงินสดค่าสินค้าจากลูกค้า`
	}
	return `หากชำระด้วยเงินสด คุณจะถูกตัดค่าอาหารผ่านเครดิต
กรุณาเก็บเงินสดค่าอาหารจากลูกค้า`
}
