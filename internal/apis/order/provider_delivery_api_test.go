package order

import (
	"context"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/absinthe/api"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiValidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model/mock_model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var ErrMocked = errors.New("mocked error")

func TestDeliveryPortalAPI_ChangePaymentMethod(t *testing.T) {
	t.Parallel()
	req := func(req *PaymentMethodRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/food/order/LMF-11111111/payment-method", testutil.JSON(req))
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: "LMF-11111111",
			},
		}
		return ctx, recorder
	}

	t.Run("notification to driver when change qr to cash", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Driver: "isme",
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodQRPromptPay,
							},
						},
					},
				},
			},
		}
		ord.PayAtStop = 1
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ord, nil)

		deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)

		deps.notifier.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		ctx, recorder := req(&PaymentMethodRequest{
			PaymentMethod: domainModel.PaymentMethodCash,
		})
		fp.ChangePaymentMethod(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("error when change from qr to rlp", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Driver: "isme",
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodQRPromptPay,
							},
						},
					},
				},
			},
		}
		ord.PayAtStop = 1
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ord, nil)

		ctx, recorder := req(&PaymentMethodRequest{
			PaymentMethod: domainModel.PaymentMethodRLP,
		})
		fp.ChangePaymentMethod(ctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("delete Pause when change from qr to cash", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Driver: "isme",
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodQRPromptPay,
							},
						},
						Pauses: map[domainModel.Pause]bool{
							domainModel.PauseQRPayment: true,
						},
					},
				},
			},
		}
		ord.PayAtStop = 1
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ord, nil)

		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order, opts ...repository.Option) error {
				require.Equal(t, 0, len(ord.Routes[1].Pauses))
				return nil
			})

		deps.notifier.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		ctx, recorder := req(&PaymentMethodRequest{
			PaymentMethod: domainModel.PaymentMethodCash,
		})
		fp.ChangePaymentMethod(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("notification to driver when change cash to qr", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			OrderID: "ORDER_ID",
			Driver:  "isme",
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								PaymentMethod: domainModel.PaymentMethodCash,
							},
						},
					},
				},
			},
		}
		ord.PayAtStop = 1
		deps.orderService.EXPECT().Get(gomock.Any(), gomock.Any()).Return(ord, nil)

		deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)

		exptEvt := service.EventChangePaymentMethodFromCashtoQR("ORDER_ID")
		deps.notifier.EXPECT().Notify(gomock.Any(), gomock.Any(), exptEvt, gomock.Any(), gomock.Any()).Return(nil)

		ctx, recorder := req(&PaymentMethodRequest{
			PaymentMethod: domainModel.PaymentMethodQRPromptPay,
		})
		fp.ChangePaymentMethod(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestFoodDeliveryFeeAPI_CalculateDeliveryPrice(t *testing.T) {
	makeReq := func(req CalculateDeliveryFeeReq) (*gin.Context, *httptest.ResponseRecorder) {
		url := "/v1/delivery/driver/price"

		gctx, recorder := testutil.TestRequestContext("POST", url, testutil.JSON(req))
		gctx.Request.Header.Add("X-Region", req.Region)

		return gctx, recorder
	}

	t.Run("Success calculating delivery fee should return 200", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"
		scheme := domainModel.NewLegacyDeliveryFeeSettingScheme(domainModel.PriceSchemeKeyDefault, types.Money(40))
		calculator := domainModel.NewDeliveryFeeSetting("<delivery-fee-setting-1>", domainModel.ServiceFood, domainModel.RegionCode(region), scheme)

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, region, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: "food",
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Success calculating delivery fee should return 200", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"
		scheme := domainModel.NewLegacyDeliveryFeeSettingScheme(domainModel.PriceSchemeKeyDefault, types.Money(40))
		calculator := domainModel.NewDeliveryFeeSetting("<delivery-fee-setting-1>", domainModel.ServiceMessenger, domainModel.RegionCode(region), scheme)

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: "messenger",
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
				{Lat: 3, Lng: 3},
			},
			ExtraCharges: []domainModel.ExtraCharge{
				{
					Type:   domainModel.ExtraChargeTypeFixed,
					Name:   "ROUND_TRIP",
					Amount: 72,
				},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual CalculateDeliveryFeeRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, float64(10), actual.ExtraCharges[0].Amount)
		require.Equal(tt, "STOP_PRICE", actual.ExtraCharges[0].Name)
	})

	t.Run("Cannot call area service should return 500", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"
		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{}, errors.New("mock area service error"))

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: "food",
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("map service error should return 500", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"
		scheme := domainModel.NewLegacyDeliveryFeeSettingScheme(domainModel.PriceSchemeKeyDefault, types.Money(40))
		calculator := domainModel.NewDeliveryFeeSetting("<delivery-fee-setting-1>", domainModel.ServiceFood, domainModel.RegionCode(region), scheme)

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, region, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil, errors.New("mock error"))

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: "food",
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("Delivery fee calculator not found should return 500", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, region, gomock.Any(), gomock.Any()).
			Return(domainModel.DeliveryFeeSetting{}, domainModel.SettingDeliveryFeePriceScheme{}, repository.ErrNotFound)

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: "food",
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("Delivery fee calculator and calculate total with additionalService correctly", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"
		scheme := domainModel.NewLegacyDeliveryFeeSettingScheme(domainModel.PriceSchemeKeyDefault, types.Money(40))
		calculator := domainModel.NewDeliveryFeeSetting("<delivery-fee-setting-1>", domainModel.ServiceMessenger, domainModel.RegionCode(region), scheme)

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: "messenger",
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
				{Lat: 3, Lng: 3},
			},
			ExtraCharges: []domainModel.ExtraCharge{
				{
					Type:   domainModel.ExtraChargeTypeFixed,
					Name:   "ROUND_TRIP",
					Amount: 72,
				},
			},
			AdditionalServiceDetail: AdditionalServiceReq{
				Total: 20.0,
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual CalculateDeliveryFeeRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, float64(10), actual.ExtraCharges[0].Amount)
		require.Equal(tt, "STOP_PRICE", actual.ExtraCharges[0].Name)
		require.Equal(tt, float64(70), actual.BaseFee.Float64()+actual.ExtraCharges[0].Amount+actual.DeliveryFeeSummary.AdditionalServiceFee.Total)
	})
}

func doAssert(deps *foodProviderDeps, schemes []domainModel.OnTopFare) {
	if schemes != nil {
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any()).Return(schemes, nil)
	} else {
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
	}
	deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
}

func TestFoodDeliveryFeeAPI_GetOnTopScheme(t *testing.T) {
	onTopBKK := []domainModel.OnTopFare{
		{
			Scheme: domainModel.FlatRateScheme,
			Status: domainModel.StatusActive,
			Region: "BKK",
			Name:   "BKK1",
			Conditions: []domainModel.OntopCondition{
				{
					Status: domainModel.StatusActive,
					Days: []domainModel.Days{
						domainModel.Monday,
						domainModel.Wednesday,
						domainModel.Friday,
					},
					Time: []domainModel.StartEndTime{
						{
							Begin: "11:00",
							End:   "12:00",
						},
					},
					FlatRateAmount: 10,
					ServiceTypes:   []domainModel.Service{domainModel.ServiceFood},
				},
			},
			IncentiveSources: []string{"AA", "BB"},
		},
	}
	multiOnTop := []domainModel.OnTopFare{
		{
			Scheme: domainModel.FlatRateScheme,
			Status: domainModel.StatusActive,
			Region: "BKK",
			Name:   "BKK3",
			Conditions: []domainModel.OntopCondition{
				{
					Status: domainModel.StatusActive,
					Days: []domainModel.Days{
						domainModel.Monday,
						domainModel.Wednesday,
						domainModel.Friday,
					},
					Time: []domainModel.StartEndTime{
						{
							Begin: "16:00",
							End:   "21:00",
						},
					},
					FlatRateAmount: 10,
					ServiceTypes:   []domainModel.Service{domainModel.ServiceFood},
				},
			},
		},
		{
			Scheme: domainModel.FlatRateScheme,
			Status: domainModel.StatusActive,
			Region: "BKK",
			Name:   "BKK4",
			Conditions: []domainModel.OntopCondition{
				{
					Status: domainModel.StatusActive,
					Days: []domainModel.Days{
						domainModel.Wednesday,
					},
					Time: []domainModel.StartEndTime{
						{
							Begin: "18:00",
							End:   "23:59",
						},
					},
					FlatRateAmount: 30,
					ServiceTypes:   []domainModel.Service{domainModel.ServiceFood},
				},
			},
		},
	}
	onTopRestaurants := []domainModel.OnTopFare{
		{
			Scheme: domainModel.FlatRateScheme,
			Status: domainModel.StatusActive,
			Region: "BKK",
			Name:   "BKK5",
			Conditions: []domainModel.OntopCondition{
				{
					Status: domainModel.StatusActive,
					Days: []domainModel.Days{
						domainModel.Monday,
					},
					Time: []domainModel.StartEndTime{
						{
							Begin: "11:00",
							End:   "12:00",
						},
					},
					FlatRateAmount: 55,
					ServiceTypes:   []domainModel.Service{domainModel.ServiceFood},
				},
			},
			Restaurants: []domainModel.OnTopRestaurant{
				{
					RestaurantId: "res-id",
				},
			},
		},
		{
			Scheme: domainModel.FlatRateScheme,
			Status: domainModel.StatusActive,
			Region: "BKK",
			Name:   "BKK6",
			Conditions: []domainModel.OntopCondition{
				{
					Status: domainModel.StatusActive,
					Days: []domainModel.Days{
						domainModel.Monday,
					},
					Time: []domainModel.StartEndTime{
						{
							Begin: "11:00",
							End:   "12:00",
						},
					},
					FlatRateAmount: 100,
					ServiceTypes:   []domainModel.Service{domainModel.ServiceFood},
				},
			},
			RestaurantIDs: []string{"res-id"},
		},
	}
	onTopRestaurantsNotMatchedId := []domainModel.OnTopFare{
		{
			Scheme: domainModel.FlatRateScheme,
			Status: domainModel.StatusActive,
			Region: "BKK",
			Name:   "BKK7",
			Conditions: []domainModel.OntopCondition{
				{
					Status: domainModel.StatusActive,
					Days: []domainModel.Days{
						domainModel.Monday,
					},
					Time: []domainModel.StartEndTime{
						{
							Begin: "11:00",
							End:   "12:00",
						},
					},
					FlatRateAmount: 55,
					ServiceTypes:   []domainModel.Service{domainModel.ServiceFood},
				},
			},
			RestaurantIDs: []string{"res-id"},
			Restaurants: []domainModel.OnTopRestaurant{
				{
					RestaurantId: "not-matched-id",
				},
			},
		},
	}

	restaurant := domainModel.Stop{
		ID: "res-id",
		Info: domainModel.StopInfoCollector{
			StopInfo: domainModel.NewStopInfoFood(1, domainModel.PriceSchemeRMS, domainModel.RestaurantDirection{}),
		},
	}
	dest := domainModel.Stop{
		ID: "dest-id",
		Info: domainModel.StopInfoCollector{
			StopInfo: domainModel.NewStopInfoFood(1, domainModel.PriceSchemeRMS, domainModel.RestaurantDirection{}),
		},
	}

	quote := domainModel.Quote{
		ServiceType: domainModel.ServiceFood,
		Routes:      []domainModel.Stop{restaurant, dest},
	}

	regions := []domainModel.RegionCode{
		"BKK",
	}
	loc, _ := time.LoadLocation("Asia/Bangkok")

	ctrl := gomock.NewController(t)
	testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

	t.Run("get scheme if valid", func(tt *testing.T) {
		monTime := time.Date(2021, 3, 22, 11, 0, 0, 0, loc)
		doAssert(deps, onTopBKK)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, monTime)
		require.Equal(t, []domainModel.OnTopScheme{
			{
				ID:               "",
				Name:             "BKK1",
				Scheme:           domainModel.FlatRateScheme,
				Amount:           10,
				BundleAmount:     10,
				IncentiveSources: []string{"AA", "BB"},
			},
		}, scheme)
	})

	t.Run("get scheme and support second", func(tt *testing.T) {
		defer func() { onTopBKK[0].Conditions[0].Time[0].Begin = "11:00" }()
		second := onTopBKK
		second[0].Conditions[0].Time[0].Begin = "11:59:59"
		second[0].Conditions[0].Time[0].End = "12:00:00"
		monTime := time.Date(2021, 3, 22, 11, 59, 59, 0, loc)
		doAssert(deps, onTopBKK)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, monTime)
		require.Equal(t, []domainModel.OnTopScheme{
			{
				ID:               "",
				Name:             "BKK1",
				Amount:           10,
				BundleAmount:     10,
				Scheme:           domainModel.FlatRateScheme,
				IncentiveSources: []string{"AA", "BB"},
			},
		}, scheme)
	})

	t.Run("not get scheme if wrong day", func(tt *testing.T) {
		tueTime := time.Date(2021, 3, 23, 11, 0, 0, 0, loc)
		doAssert(deps, onTopBKK)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, tueTime)
		require.Equal(t, 0, len(scheme))
	})

	t.Run("not get scheme if wrong time", func(tt *testing.T) {
		tueTime := time.Date(2021, 3, 22, 14, 0, 0, 0, loc)
		doAssert(deps, onTopBKK)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, tueTime)
		require.Equal(t, 0, len(scheme))
	})

	t.Run("not get scheme if wrong restaurant id", func(tt *testing.T) {
		tueTime := time.Date(2021, 3, 22, 11, 0, 0, 0, loc)
		doAssert(deps, nil)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, tueTime)
		require.Equal(t, 0, len(scheme))
	})

	t.Run("not get scheme if INACTIVE", func(tt *testing.T) {
		defer func() { onTopBKK[0].Status = domainModel.StatusActive }()
		inactive := onTopBKK
		inactive[0].Status = domainModel.StatusInactive
		monTime := time.Date(2021, 3, 22, 11, 0, 0, 0, loc)
		doAssert(deps, inactive)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, monTime)
		require.Equal(t, 0, len(scheme))
	})

	t.Run("get scheme if bkk3 matched", func(tt *testing.T) {
		monTime := time.Date(2021, 3, 22, 20, 0, 0, 0, loc)
		doAssert(deps, multiOnTop)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, monTime)
		require.Equal(t, []domainModel.OnTopScheme{
			{
				ID:           "",
				Name:         "BKK3",
				Scheme:       domainModel.FlatRateScheme,
				Amount:       10,
				BundleAmount: 10,
			},
		}, scheme)
	})

	t.Run("get scheme if bkk4 matched", func(tt *testing.T) {
		wedTime := time.Date(2021, 3, 24, 23, 0, 0, 0, loc)
		doAssert(deps, multiOnTop)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, wedTime)
		require.Equal(t, []domainModel.OnTopScheme{
			{
				ID:           "",
				Name:         "BKK4",
				Scheme:       domainModel.FlatRateScheme,
				Amount:       30,
				BundleAmount: 30,
			},
		}, scheme)
	})

	t.Run("get scheme if bkk3, bbk4 matched", func(tt *testing.T) {
		wedTime := time.Date(2021, 3, 24, 18, 0, 0, 0, loc)
		doAssert(deps, multiOnTop)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, wedTime)
		require.Equal(t, []domainModel.OnTopScheme{
			{
				ID:           "",
				Name:         "BKK3",
				Scheme:       domainModel.FlatRateScheme,
				Amount:       10,
				BundleAmount: 10,
			},
			{
				ID:           "",
				Name:         "BKK4",
				Scheme:       domainModel.FlatRateScheme,
				Amount:       30,
				BundleAmount: 30,
			},
		}, scheme)
	})

	t.Run("get scheme bkk4 if both match but inactive bkk3", func(tt *testing.T) {
		inactive := multiOnTop
		inactive[0].Status = domainModel.StatusInactive
		wedTime := time.Date(2021, 3, 24, 18, 0, 0, 0, loc)
		doAssert(deps, multiOnTop)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, regions, wedTime)
		require.Equal(t, []domainModel.OnTopScheme{
			{
				ID:           "",
				Name:         "BKK4",
				Scheme:       domainModel.FlatRateScheme,
				Amount:       30,
				BundleAmount: 30,
			},
		}, scheme)
	})

	t.Run("not get scheme when distRegions not include region scheme", func(tt *testing.T) {
		monTime := time.Date(2021, 3, 22, 11, 0, 0, 0, loc)
		invalidDistRegions := []domainModel.RegionCode{
			"AYUTTHAYA",
		}
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.OnTopFare{}, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.OnTopFare{}, nil)

		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, invalidDistRegions, monTime)
		require.Equal(t, 0, len(scheme))
	})

	t.Run("get scheme when distRegions include region scheme", func(tt *testing.T) {
		monTime := time.Date(2021, 3, 22, 11, 0, 0, 0, loc)
		validDistRegions := []domainModel.RegionCode{
			"AYUTTHAYA",
			"BKK",
		}
		doAssert(deps, onTopBKK)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, validDistRegions, monTime)
		require.Equal(t, []domainModel.OnTopScheme{
			{
				ID:               "",
				Name:             "BKK1",
				Amount:           10,
				BundleAmount:     10,
				Scheme:           domainModel.FlatRateScheme,
				IncentiveSources: []string{"AA", "BB"},
			},
		}, scheme)
	})

	t.Run("get scheme when restaurants matched", func(tt *testing.T) {
		monTime := time.Date(2021, 3, 22, 11, 0, 0, 0, loc)
		validDistRegions := []domainModel.RegionCode{
			"AYUTTHAYA",
			"BKK",
		}
		doAssert(deps, onTopRestaurants)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, validDistRegions, monTime)
		require.Equal(t, []domainModel.OnTopScheme{
			{
				ID:           "",
				Name:         "BKK5",
				Scheme:       domainModel.FlatRateScheme,
				Amount:       55,
				BundleAmount: 55,
			},
			{
				ID:           "",
				Name:         "BKK6",
				Scheme:       domainModel.FlatRateScheme,
				Amount:       100,
				BundleAmount: 100,
			},
		}, scheme)
	})

	t.Run("not get scheme when restaurants not matched", func(tt *testing.T) {
		monTime := time.Date(2021, 3, 22, 11, 0, 0, 0, loc)
		validDistRegions := []domainModel.RegionCode{
			"AYUTTHAYA",
			"BKK",
		}
		doAssert(deps, onTopRestaurantsNotMatchedId)
		scheme, _ := testapi.GetOnTopScheme(context.Background(), quote, validDistRegions, monTime)
		require.Equal(t, 0, len(scheme))
	})
}

func TestFoodQuoteHandlerInternal(t *testing.T) {
	t.Parallel()

	t.Run("create quote with backward compat", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						EstimatedCookingTime: domainModel.DurationSecond(300),
						PriceScheme:          domainModel.PriceSchemeRMS,
						RestaurantDirection:  domainModel.RestaurantDirection{Memo: "from restaurant direction"},
					}},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						EstimatedCookingTime: domainModel.DurationSecond(100),
						PriceScheme:          domainModel.PriceSchemeRMS,
						RestaurantDirection:  domainModel.RestaurantDirection{Memo: "to restaurant direction"},
					}},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{
			{ThrottledDispatchDetail: domainModel.ThrottledDispatchDetail{ZoneID: primitive.NewObjectID()}},
		}, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(30.0, 0.0, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.NoError(tt, err)
		fromStopInfo, validFromStopInfo := actualQ.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
		toStopInfo, validToStopInfo := actualQ.Routes[1].Info.StopInfo.(*domainModel.StopInfoFood)
		require.Equal(tt, true, validFromStopInfo)
		require.Equal(tt, true, validToStopInfo)
		require.Equal(tt, fromStopInfo.EstimatedCookingTime, actualQ.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood).EstimatedCookingTime)
		require.Equal(tt, fromStopInfo.PriceScheme, actualQ.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood).PriceScheme)
		require.Equal(tt, fromStopInfo.RestaurantDirection, actualQ.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood).RestaurantDirection)
		require.Equal(tt, toStopInfo.EstimatedCookingTime, actualQ.Routes[1].Info.StopInfo.(*domainModel.StopInfoFood).EstimatedCookingTime)
		require.Equal(tt, toStopInfo.PriceScheme, actualQ.Routes[1].Info.StopInfo.(*domainModel.StopInfoFood).PriceScheme)
		require.Equal(tt, toStopInfo.RestaurantDirection, actualQ.Routes[1].Info.StopInfo.(*domainModel.StopInfoFood).RestaurantDirection)
		require.Equal(tt, expectedRegion, actualQ.DistributeRegions.DefaultRegion().String())
	})

	t.Run("create non-existing quote", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		now := timeutil.BangkokNow()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{}},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
			Options: OptionsReq{
				RequireBox:   true,
				MpDeferUntil: &now,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{
				Distribution: domainModel.AutoAssignDistribution{OverrideOrderMPDeferUntil: false},
			}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Equal(tt, expectedRegion, actualQ.DistributeRegions.DefaultRegion().String())
		require.True(tt, q.Options.RequireBox)
		require.NotZero(tt, q.Options.MpDeferUntil)
		require.NoError(tt, err)
	})

	t.Run("create non-existing quote (with override mp_defer_until)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		now := timeutil.BangkokNow()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{}},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
			Options: OptionsReq{
				RequireBox:   true,
				MpDeferUntil: &now,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{
				Distribution: model.AutoAssignDistribution{OverrideOrderMPDeferUntil: true},
			}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Equal(tt, expectedRegion, actualQ.DistributeRegions.DefaultRegion().String())
		require.True(tt, q.Options.RequireBox)
		require.Nil(tt, q.Options.MpDeferUntil)
		require.NoError(tt, err)
	})

	t.Run("error on get distribute region should return error", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
		}

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{}, errors.New("errors"))

		_, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Error(tt, err)
	})

	t.Run("calculate distance from manmap and response back", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{}},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 100.54702667857362, Lng: 13.743937518724506}, mapservice.Location{Lat: 100.5072724, Lng: 13.7487528}, false).
			Return(&domainModel.MapRoute{Distance: 5930}, nil, nil)

		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		actualQ, _ := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Equal(tt, types.Distance(5930), actualQ.Distance)
	})

	t.Run("returns error if Routes has less than 2 stop", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
			},
		}

		testapi, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		_, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Error(tt, err)
	})

	t.Run("calculate normal coin", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		now := timeutil.BangkokNow()

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.OnTopFare{
			{
				Name:   "mock",
				Scheme: domainModel.FlexibleFlatRateScheme,
				Status: domainModel.StatusActive,
				Conditions: []domainModel.OntopCondition{
					{
						Status:       domainModel.StatusActive,
						ServiceTypes: domainModel.Services{domainModel.ServiceFood},
						Days: []domainModel.Days{
							domainModel.Monday, domainModel.Tuesday, domainModel.Wednesday,
							domainModel.Thursday, domainModel.Friday, domainModel.Saturday, domainModel.Sunday,
						},
						Time: []domainModel.StartEndTime{
							{Begin: "00:00", End: "23:59"},
						},
						FlatRatePrices: []domainModel.FlatRatePrice{
							{
								FlatRateType:     domainModel.DefaultFlatRate,
								NormalCoinAmount: 8,
							},
						},
					},
				},
				StartTime: now.Add(-1 * time.Hour),
				EndTime:   now.Add(time.Hour),
			},
		}, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Equal(tt, 8, actualQ.Routes[0].PriceSummary.DeliveryFee.OnTopScheme[0].Coin)
		require.NoError(tt, err)
	})

	t.Run("calculate bundle coin", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		now := timeutil.BangkokNow()

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.OnTopFare{
			{
				Name:   "mock",
				Scheme: domainModel.FlexibleFlatRateScheme,
				Status: domainModel.StatusActive,
				Conditions: []domainModel.OntopCondition{
					{
						Status:       domainModel.StatusActive,
						ServiceTypes: domainModel.Services{domainModel.ServiceFood},
						Days: []domainModel.Days{
							domainModel.Monday, domainModel.Tuesday, domainModel.Wednesday,
							domainModel.Thursday, domainModel.Friday, domainModel.Saturday, domainModel.Sunday,
						},
						Time: []domainModel.StartEndTime{
							{Begin: "00:00", End: "23:59"},
						},
						FlatRatePrices: []domainModel.FlatRatePrice{
							{
								FlatRateType:     domainModel.FlexibleFlatRate,
								BundleCoinAmount: 9,
							},
						},
					},
				},
				StartTime: now.Add(-1 * time.Hour),
				EndTime:   now.Add(time.Hour),
			},
		}, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Equal(tt, 9, actualQ.Routes[0].PriceSummary.DeliveryFee.OnTopScheme[0].BundleCoin)
		require.NoError(tt, err)
	})

	t.Run("calculate base fee & on-top fare", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			expectedBase = 40.0
			expectedRoad = 0.0
		)

		qreq := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			UserID:      "********",
			ServiceType: domainModel.ServiceFood,
			PayAtStop:   foodPayAtStop,
			Routes: []domainModel.Stop{
				{
					ID:   "125891ka",
					Name: "Jim Burger",
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					Address: "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
					Phones:  []string{"0891231234"},
					PickingItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					Memo: "4th floor, DedicatedZone Eden.",
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{}},
				},
				{
					ID:   "",
					Name: "Uncle Tob",
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
					Address: "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
					DeliveryItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					CollectPayment: true,
					ItemsPrice:     150,
					Memo:           "Near Tesco lotus",
				},
			},
			PaymentMethod: domainModel.PaymentMethodCash,
			DeliveryFee: domainModel.DeliveryFeeSummary{
				AdditionalFee: map[string]float64{},
				Discounts: []domainModel.Discount{
					{Type: domainModel.DiscountTypeCoupon, Code: "HELLO70", Discount: 70},
				},
			},
			UserOnTops: []UserOnTop{{
				Title:  "RAIN_SURGE",
				Amount: 20,
			}},
		}
		expectedRegion := "AYUTTHAYA"

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		actualQ, err := api.quote(context.Background(), qreq, "AYUTTHAYA")
		require.NoError(tt, err)
		require.Equal(tt, expectedBase, actualQ.Routes[1].PriceSummary.DeliveryFee.BaseFee)
		require.Equal(tt, qreq.QuoteID, actualQ.QuoteID)
		require.Equal(tt, qreq.UserID, actualQ.UserID)
		require.Equal(tt, qreq.ServiceType, actualQ.ServiceType)
		require.Equal(tt, expectedBase, actualQ.Routes[1].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
		require.Equal(tt, expectedRoad, actualQ.Routes[1].PriceSummary.DeliveryFee.RoadFee)
		require.Equal(tt, map[string]float64{}, actualQ.Routes[1].PriceSummary.DeliveryFee.AdditionalFee)
		require.NotEqual(tt, 0, actualQ.Routes[1].PriceSummary.DeliveryFee.SubTotal)
		require.NotEqual(tt, 0, actualQ.Routes[1].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, types.Distance(1000), actualQ.Distance)
		require.Equal(tt, float64(20), actualQ.Routes[1].PriceSummary.DeliveryFee.OnTopFare)
	})

	t.Run("calculates withholding tax", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			PayAtStop:   foodPayAtStop,
			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{},
			},
		}

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{""}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), "").
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		priceScheme := CreateValidPriceScheme()
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(55.0, 0.0, 1000))
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		testapi.Cfg = OrderAPIConfig{AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{
			Commisson:      0.6,
			WithHoldingTax: 0.2,
		}}}
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "")
		require.NoError(tt, err)
		require.Equal(tt, 0.0, actualQ.Routes[1].PriceSummary.DeliveryFee.Commission)
		require.Equal(tt, 11.0, actualQ.Routes[1].PriceSummary.DeliveryFee.WithholdingTax)
	})

	t.Run("agent model quote should calculate commission but ignore withholding tax", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			ServiceType: domainModel.ServiceFood,
			PayAtStop:   foodPayAtStop,
			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{},
			},
			RevenueAgentModel: true,
		}

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{""}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), "").
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())
		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		priceScheme := CreateValidPriceScheme()
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(55.0, 0.0, 1000))
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		testapi.Cfg = OrderAPIConfig{AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{Commisson: 0.6, WithHoldingTax: 0.2}}}
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		actualQ, err := testapi.quote(context.Background(), q, "")
		require.NoError(tt, err)
		require.Equal(tt, 33.0, actualQ.Routes[1].PriceSummary.DeliveryFee.Commission)
		require.Equal(tt, 0.0, actualQ.Routes[1].PriceSummary.DeliveryFee.WithholdingTax)
	})
}

func TestFoodDeliveryAPI_CreateOrder(t *testing.T) {
	t.Parallel()
	req := func(req *CreateOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/food/order/create", testutil.JSON(req))
		return ctx, recorder
	}

	t.Run("create order from quote", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		mockOrderDistEventMgr := mock_service.NewMockOrderDistributionEventManager(ctrl)
		fp.OrderDistributionEventManager = mockOrderDistEventMgr
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		mockOrderDistEventMgr.EXPECT().PublishCreated(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
			require.Equal(tt, ordid, order.OrderID)
			require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
			return nil
		})
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote when enabled distribution-service flag correctly", func(tt *testing.T) {
		tt.Parallel()

		tt.Run("succesfully", func(tt *testing.T) {
			tt.Parallel()

			const (
				qid   = "LMFQ-1231451"
				ordid = "LMF-1231451"
				resID = "res-id"
			)
			dirs := []domainModel.RestaurantDirection{{
				RestaurantID: resID,
				Parking:      "parking",
				Direction:    "direction",
				Memo:         "memo",
			}}

			expireAt := time.Now().Add(10 * time.Minute)

			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			mockOrderDistEventMgr := mock_service.NewMockOrderDistributionEventManager(ctrl)
			fp.OrderDistributionEventManager = mockOrderDistEventMgr
			deps.quoteService.EXPECT().
				Find(gomock.Any(), qid, gomock.Any()).
				Return(&domainModel.Quote{
					QuoteID:     qid,
					Distance:    types.Distance(1000),
					ServiceType: domainModel.ServiceFood,
					Routes: []domainModel.Stop{
						{
							ID: resID,
						},
						{
							CollectPayment: true,
							PriceSummary: domainModel.PriceSummary{
								DeliveryFee: domainModel.DeliveryFeeSummary{
									RawBaseFee: 60,
									BaseFee:    60,
									RoadFee:    55,
									SubTotal:   55,
									Total:      55,
								},
								Total: 0,
							},
						},
					},
					DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
					RevenuePrincipalModel: true,
				}, nil)

			deps.distributionConfigValidator.EXPECT().
				CanDalianMP(gomock.Any(), gomock.Any()).
				Return(false, nil)
			mockOrderDistEventMgr.EXPECT().PublishCreated(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
				require.Equal(tt, ordid, order.OrderID)
				require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
				return nil
			})
			deps.orderService.EXPECT().
				CreateOrder(gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
					require.Equal(tt, ordid, order.OrderID)
					fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
					require.True(tt, valid)
					require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
					require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
					require.Equal(tt, order.DistributeRegions[0], order.Region)
					require.False(tt, order.RevampedStatus)

					order.ExpireAt = expireAt
					return nil
				})
			deps.orderService.EXPECT().
				GetOtherActiveMP(gomock.Any(), gomock.Any()).
				Return(nil, nil)
			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
			deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), ordid).Return(nil)
			deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
			deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
			deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
			deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
			deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
			deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

			ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
			wg := safe.CreateWaitGroupOnGctx(ctx)
			fp.CreateOrder(ctx)
			wg.Wait()
			require.Equal(tt, http.StatusCreated, recorder.Code)
			var resp CreateOrderResponse
			testutil.DecodeJSON(tt, recorder.Body, &resp)
			require.Equal(tt, ordid, resp.OrderID)
			require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
		})

		tt.Run("error and fallback", func(tt *testing.T) {
			tt.Parallel()

			const (
				qid   = "LMFQ-1231451"
				ordid = "LMF-1231451"
				resID = "res-id"
			)
			dirs := []domainModel.RestaurantDirection{{
				RestaurantID: resID,
				Parking:      "parking",
				Direction:    "direction",
				Memo:         "memo",
			}}

			expireAt := time.Now().Add(10 * time.Minute)

			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			mockOrderDistEventMgr := mock_service.NewMockOrderDistributionEventManager(ctrl)
			fp.OrderDistributionEventManager = mockOrderDistEventMgr
			deps.quoteService.EXPECT().
				Find(gomock.Any(), qid, gomock.Any()).
				Return(&domainModel.Quote{
					QuoteID:     qid,
					Distance:    types.Distance(1000),
					ServiceType: domainModel.ServiceFood,
					Routes: []domainModel.Stop{
						{
							ID: resID,
						},
						{
							CollectPayment: true,
							PriceSummary: domainModel.PriceSummary{
								DeliveryFee: domainModel.DeliveryFeeSummary{
									RawBaseFee: 60,
									BaseFee:    60,
									RoadFee:    55,
									SubTotal:   55,
									Total:      55,
								},
								Total: 0,
							},
						},
					},
					DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
					RevenuePrincipalModel: true,
				}, nil)

			deps.distributionConfigValidator.EXPECT().
				CanDalianMP(gomock.Any(), gomock.Any()).
				Return(false, nil)
			mockOrderDistEventMgr.EXPECT().PublishCreated(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
				require.Equal(tt, ordid, order.OrderID)
				require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
				return nil
			})
			deps.orderService.EXPECT().
				CreateOrder(gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
					require.Equal(tt, ordid, order.OrderID)
					fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
					require.True(tt, valid)
					require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
					require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
					require.Equal(tt, order.DistributeRegions[0], order.Region)
					require.False(tt, order.RevampedStatus)

					order.ExpireAt = expireAt
					return nil
				})
			deps.orderService.EXPECT().
				GetOtherActiveMP(gomock.Any(), gomock.Any()).
				Return(nil, nil)
			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
			deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), ordid)
			deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
			deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
			deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
			deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
			deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
			deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

			ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
			wg := safe.CreateWaitGroupOnGctx(ctx)
			fp.CreateOrder(ctx)
			wg.Wait()
			require.Equal(tt, http.StatusCreated, recorder.Code)
			var resp CreateOrderResponse
			testutil.DecodeJSON(tt, recorder.Body, &resp)
			require.Equal(tt, ordid, resp.OrderID)
			require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
		})
	})

	t.Run("create order from quote with discount", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)
				require.Equal(tt, 30.0, order.Routes[1].PriceSummary.DeliveryFee.Discounts[0].Discount)
				require.Equal(tt, 20.0, order.Routes[1].PriceSummary.DeliveryFee.Discounts[1].Discount)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), ordid)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs, Discounts: []DiscountReq{
			{
				Type:     model.DiscountTypeCoupon,
				Category: "category-coupon",
				Code:     "code-coupon",
				Discount: 30.0,
			},
			{
				Type:     model.DiscountTypeSubsidize,
				Category: "category-subsidize",
				Code:     "code-subsidize",
				Discount: 20.0,
			},
		}})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create switch-flow order from quote", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
				Options: domainModel.OrderOptions{
					SwitchFlow: true,
				},
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			Get(gomock.Any(), ordid).
			Return(nil, repository.ErrNotFound)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)
				require.Equal(tt, true, order.IsSwitchFlow())

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create re-assigned switch-flow order from quote", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
				Options: domainModel.OrderOptions{
					SwitchFlow: true,
				},
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			Get(gomock.Any(), ordid).
			Return(&domainModel.Order{
				DeliveringRound: 1,
			}, nil)
		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order, opts ...domainModel.Option) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)
				require.Equal(tt, 1, order.DeliveringRound)
				require.Equal(tt, true, order.IsSwitchFlow())

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create food order with require delivery photo", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		svc := domainModel.NewServiceArea("id1", "AYUTTHAYA")
		svc.SetRequiredPhotoOfJacketAndBoxes([]string{"food"})

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: false,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Empty(tt, order.Routes[0].Pauses)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.Equal(tt, true, order.IsRequireDeliveringPhotoURL)
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(svc, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
	})

	t.Run("create messenger order with require delivery photo", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceMessenger,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: false,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Empty(tt, order.Routes[0].Pauses)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.Equal(tt, true, order.Routes[1].Pauses[domainModel.PauseDeliveringPhoto])
				require.Equal(tt, true, order.IsRequireDeliveringPhotoURL)

				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		svc := domainModel.NewServiceArea("id1", "AYUTTHAYA")
		svc.SetRequiredPhotoOfJacketAndBoxes([]string{"messenger"})
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(svc, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
	})

	t.Run("create messenger round-trip order with require delivery photo", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceMessenger,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: false,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
					{
						CollectPayment: false,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Empty(tt, order.Routes[0].Pauses)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.Equal(tt, true, order.Routes[1].Pauses[domainModel.PauseDeliveringPhoto])
				require.Equal(tt, true, order.Routes[2].Pauses[domainModel.PauseDeliveringPhoto])
				require.Equal(tt, true, order.IsRequireDeliveringPhotoURL)

				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		svc := domainModel.NewServiceArea("id1", "AYUTTHAYA")
		svc.SetRequiredPhotoOfJacketAndBoxes([]string{"messenger"})
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(svc, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
	})

	t.Run("create food order from quote when user is in whitelist do not disturb", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				UserID:      "user-A",
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)
				require.True(tt, order.Routes[1].DoNotDisturb)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		sa := &domainModel.ServiceArea{}
		sa.SetWhitelistUsersEnabled(true)
		sa.SetWhitelistUsers([]string{"user-A"})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(sa, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create food order from quote with removing pause flag", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid    = "LMFQ-1231451"
			ordid  = "LMFQ-1231451"
			resID  = "res-id"
			stopID = ""
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						ID:             stopID,
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, model.PauseSet{}, order.Routes[1].Pauses)
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{
			QuoteID: qid, OrderID: ordid, Remarks: dirs, Routes: []CreateOrderRouteRequest{
				{ID: resID, Pauses: model.PauseSet{}}, {ID: stopID, Pauses: model.PauseSet{}},
			},
		})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create food order from quote with adding pause flag", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid    = "LMFQ-1231451"
			ordid  = "LMFQ-1231451"
			resID  = "res-id"
			stopID = ""
		)

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						ID:             stopID,
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, model.PauseSet{
					model.PauseQRPayment: true,
				}, order.Routes[1].Pauses)
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{
			QuoteID: qid, OrderID: ordid, Remarks: nil, Routes: []CreateOrderRouteRequest{
				{ID: resID, Pauses: model.PauseSet{}}, {ID: stopID, Pauses: model.PauseSet{model.PauseQRPayment: true}},
			},
		})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create messenger order with adding pause flag shoud not changed", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid    = "LMFQ-1231451"
			ordid  = "LMFQ-1231451"
			resID  = "res-id"
			stopID = ""
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceMessenger,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						ID:             stopID,
						CollectPayment: false,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Empty(tt, order.Routes[0].Pauses)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.Equal(tt, true, order.Routes[1].Pauses[domainModel.PauseDeliveringPhoto])
				require.Equal(tt, true, order.IsRequireDeliveringPhotoURL)

				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		svc := domainModel.NewServiceArea("id1", "AYUTTHAYA")
		svc.SetRequiredPhotoOfJacketAndBoxes([]string{"messenger"})
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(svc, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs, Routes: []CreateOrderRouteRequest{
			{ID: resID, Pauses: model.PauseSet{}}, {ID: stopID, Pauses: model.PauseSet{model.PauseQRPayment: true}},
		}})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
	})

	t.Run("create food order from quote with nil pause flag", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid    = "LMFQ-1231451"
			ordid  = "LMFQ-1231451"
			resID  = "res-id"
			stopID = ""
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						ID:             stopID,
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, model.PauseSet{}, order.Routes[0].Pauses)
				require.Equal(tt, model.PauseSet{}, order.Routes[1].Pauses)
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{
			QuoteID: qid, OrderID: ordid, Remarks: dirs, Routes: []CreateOrderRouteRequest{
				{ID: resID, Pauses: model.PauseSet{}}, {ID: stopID, Pauses: nil},
			},
		})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create food order from quote with nil pause flag and have tip info", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid    = "LMFQ-1231451"
			ordid  = "LMFQ-1231451"
			resID  = "res-id"
			stopID = ""
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						ID:             stopID,
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().CanDalianMP(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, model.PauseSet{}, order.Routes[0].Pauses)
				require.Equal(tt, model.PauseSet{}, order.Routes[1].Pauses)
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)
				require.Len(tt, order.Tips, 1)
				require.Equal(tt, order.TipAmount, types.Money(50))
				expectedTip := model.TipRecord{
					ID:          "NEW_TIP_ID",
					Amount:      50,
					OrderStatus: "", // the status will be `model.StatusAssigningDriver` at repository step
					CreatedAt:   order.Tips[0].CreatedAt,
				}
				require.Equal(tt, expectedTip, order.Tips[0])
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{
			QuoteID: qid, OrderID: ordid, Remarks: dirs, Routes: []CreateOrderRouteRequest{
				{ID: resID, Pauses: model.PauseSet{}}, {ID: stopID, Pauses: nil},
			},
			Tip: model.TipRecord{
				ID:     "NEW_TIP_ID",
				Amount: 50,
			},
		})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote with thai memo", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
						Memo: "เทส",
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote with thai and english memo", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
						Memo: "เทส memo",
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote with english memo", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
						Memo: "test memo",
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		// feature flag for async memo translation v2
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsOrderMemoTranslationAsyncEnabled.Name, gomock.Any()).Return(false)

		deps.translationService.EXPECT().TranslateENToTH(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, message string) string {
			require.Equal(tt, "test memo", message)
			return "เทสบันทึก"
		})

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote with async english memo translation", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
						Memo: "test memo",
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		// feature flag for async memo translation v2
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsOrderMemoTranslationAsyncEnabled.Name, gomock.Any()).Return(true)

		deps.translationService.EXPECT().TranslateENToTH(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, message string) string {
			require.Equal(tt, "test memo", message)
			return "เทสบันทึก"
		})

		deps.orderService.EXPECT().SetMemoTranslation(gomock.Any(), ordid, 1, "เทสบันทึก", model.TranslationSuccess).Return(nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote with async english memo translation failed", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
						Memo: "test memo",
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		// feature flag for async memo translation v2
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsOrderMemoTranslationAsyncEnabled.Name, gomock.Any()).Return(true)

		deps.translationService.EXPECT().TranslateENToTH(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, message string) string {
			require.Equal(tt, "test memo", message)
			return "test memo"
		})

		deps.orderService.EXPECT().SetMemoTranslation(gomock.Any(), ordid, 1, "", model.TranslationFail).Return(nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote with idempotency", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&model.Quote{
				ServiceType: model.ServiceBike,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []model.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
								RawBaseFee:    67,
								BaseFee:       67,
								SubTotal:      67,
								Total:         67,
							},
							Total: 67,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []model.RegionCode{"UTHAI_THANI"},
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *model.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				require.Equal(tt, 67.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, 67.0, order.Routes[1].PriceSummary.DeliveryFee.Total)
				require.True(tt, order.RevampedStatus)
				require.True(tt, order.Quote.Routes[1].Pauses[model.PauseQRPayment])
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp api.Error
		testutil.DecodeJSON(t, recorder.Body, &resp)
	})

	t.Run("create order from quote with idempotency and should return previous data response from cache", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return(fmt.Sprintf(`{"orderId": "%s"}`, ordid))

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, ordid, resp.OrderID)
	})

	t.Run("create order from quote with idempotency and should return an error order locked with the idempotencyKey", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&model.Quote{
				ServiceType: model.ServiceBike,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []model.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
								RawBaseFee:    67,
								BaseFee:       67,
								SubTotal:      67,
								Total:         67,
							},
							Total: 67,
						},
						Pauses: model.PauseSet{model.PauseQRPayment: true},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []model.RegionCode{"UTHAI_THANI"},
			}, nil)

		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(false, nil)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusBadRequest, recorder.Code)
		var resp api.Error
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, fmt.Sprintf("order was locked by idempotencyKey %s", qid), resp.Message)
	})

	t.Run("create order from quote and IsDalianMP should be true if canDefer and is MP order", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
				Options: domainModel.OrderOptions{
					MpID:     "mp-1",
					CanDefer: true,
				},
			}, nil)

		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.Equal(tt, true, order.IsDalianMP)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{
			Distribution: domainModel.AutoAssignDistribution{
				EnableBlacklistWhitelistForMPATR: true,
			},
		}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote and IsDalianMP should be as canDalianMP if it is MP order", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
				Options: domainModel.OrderOptions{
					MpID: "mp-1",
				},
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.Equal(tt, false, order.IsDalianMP)
				require.False(tt, order.RevampedStatus)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{
			Distribution: domainModel.AutoAssignDistribution{
				EnableBlacklistWhitelistForMPATR: true,
			},
		}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("HasAnotherMPOrder should be false if there is no another mp order", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
				Options: domainModel.OrderOptions{
					MpID: "mp-1",
				},
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, repository.ErrNotFound)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.Equal(tt, false, order.IsDalianMP)
				require.False(tt, order.RevampedStatus)
				require.False(tt, *order.HasAnotherMPOrder)

				order.ExpireAt = expireAt
				return nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{
			Distribution: domainModel.AutoAssignDistribution{
				EnableBlacklistWhitelistForMPATR: true,
			},
		}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("HasAnotherMPOrder should be true if there is another mp order", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceFood,
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
				Options: domainModel.OrderOptions{
					MpID: "mp-1",
				},
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(&domainModel.Order{
				OrderID: "mock-order-mp-id",
			}, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.Equal(tt, false, order.IsDalianMP)
				require.False(tt, order.RevampedStatus)
				require.True(tt, *order.HasAnotherMPOrder)

				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			SetHasAnotherMPOrder(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, orderID string, hasAnotherMPOrder *bool, opts ...repository.Option) error {
				require.Equal(tt, "mock-order-mp-id", orderID)
				require.True(tt, *hasAnotherMPOrder)
				return nil
			})
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{
			Distribution: domainModel.AutoAssignDistribution{
				EnableBlacklistWhitelistForMPATR: true,
			},
		}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})
}

func TestFoodDeliveryAPI_CanDefer(t *testing.T) {
	t.Parallel()
	req := func(req *CanDeferRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/delivery/food/order/can-defer", testutil.JSON(req))
		ctx.Request.Header.Set("X-Region", "AYUTTHAYA")
		return ctx, recorder
	}

	t.Run("can defer should be checked from isDalianMP when EnableBlacklistWhitelistForMPATR is false", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid            = "LMFQ-1231451"
			ordid          = "LMFQ-1231451"
			expectedRegion = "AYUTTHAYA"
		)

		q := &QuoteRequest{
			QuoteID:     qid,
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{}},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
			Options: OptionsReq{
				RequireBox: true,
				MpID:       "fake",
			},
		}
		pois := []domainModel.PointOfInterest{
			{
				ID: "tan",
			},
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{expectedRegion}, nil
			}).Times(2)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.distributionConfigValidator.EXPECT().GetMatchRestaurantFirstMRThresholdInZone(gomock.Any(), gomock.Any(), gomock.Any()).Return(30.0, true, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{
			Distribution: domainModel.AutoAssignDistribution{
				EnableBlacklistWhitelistForMPATR: false,
			},
		}, nil).Times(3)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(true, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDefer(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order) (bool, error) {
				require.Equal(tt, pois, ord.PointOfInterests)
				return false, nil
			})

		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		ctx, recorder := req(&CanDeferRequest{QuoteRequest: *q, OrderID: ordid, PointOfInterests: pois, MatchRate: types.NewFloat64(50)})
		fp.CanDefer(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var resp CanDeferResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, true, resp.CanDefer)
	})

	t.Run("can defer should be checked from isDeferPossible when EnableBlacklistWhitelistForMPATR is false", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid            = "LMFQ-1231451"
			ordid          = "LMFQ-1231451"
			expectedRegion = "AYUTTHAYA"
		)

		q := &QuoteRequest{
			QuoteID:     qid,
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{}},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
			Options: OptionsReq{
				RequireBox: true,
			},
		}
		pois := []domainModel.PointOfInterest{
			{
				ID: "tan",
			},
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{expectedRegion}, nil
			}).Times(2)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.distributionConfigValidator.EXPECT().GetMatchRestaurantFirstMRThresholdInZone(gomock.Any(), gomock.Any(), gomock.Any()).Return(30.0, true, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{
			Distribution: domainModel.AutoAssignDistribution{
				EnableBlacklistWhitelistForMPATR: false,
			},
		}, nil).Times(3)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDefer(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order) (bool, error) {
				require.Equal(tt, pois, ord.PointOfInterests)
				return true, nil
			})

		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		ctx, recorder := req(&CanDeferRequest{QuoteRequest: *q, OrderID: ordid, PointOfInterests: pois, MatchRate: types.NewFloat64(50)})
		fp.CanDefer(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var resp CanDeferResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, true, resp.CanDefer)
	})

	t.Run("can defer should be checked from CanDefer only when EnableBlacklistWhitelistForMPATR is true", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid            = "LMFQ-1231451"
			ordid          = "LMFQ-1231451"
			expectedRegion = "AYUTTHAYA"
		)

		q := &QuoteRequest{
			QuoteID:     qid,
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{}},
				},
				{
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
			Options: OptionsReq{
				RequireBox: true,
			},
		}
		pois := []domainModel.PointOfInterest{
			{
				ID: "tan",
			},
		}

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{expectedRegion}, nil
			}).Times(2)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.distributionConfigValidator.EXPECT().GetMatchRestaurantFirstMRThresholdInZone(gomock.Any(), gomock.Any(), gomock.Any()).Return(30.0, true, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)
		deps.onTopFareRepo.EXPECT().GetOnTopFareByRestaurantID(gomock.Any(), gomock.Any(), gomock.Any())
		deps.onTopFareRepo.EXPECT().GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any())

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, expectedRegion, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{
			Distribution: domainModel.AutoAssignDistribution{
				EnableBlacklistWhitelistForMPATR: true,
			},
		}, nil).Times(3)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(true, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDefer(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, ord *domainModel.Order) (bool, error) {
				require.Equal(tt, pois, ord.PointOfInterests)
				return true, nil
			})

		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), domainModel.ServiceFood).Return(false)

		ctx, recorder := req(&CanDeferRequest{QuoteRequest: *q, OrderID: ordid, PointOfInterests: pois, MatchRate: types.NewFloat64(50)})
		fp.CanDefer(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var resp CanDeferResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, true, resp.CanDefer)
	})
}

func TestFoodDeliveryAPI_ConfirmPrice(t *testing.T) {
	t.Parallel()

	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/delivery/food/order/LMF-13151/confirm-price", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("update order status after received request", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const (
			driverID = "<driver_id_1>"
			orderID  = "LMF-123151"
		)
		order := &domainModel.Order{
			OrderID: orderID,
			Status:  domainModel.StatusWaitingUserConfirmPrice,
			Driver:  driverID,
			History: map[string]time.Time{},
			Quote: domainModel.Quote{
				QuoteID:     "LMFQ-123151",
				UserID:      "<user_id_1>",
				ServiceType: domainModel.ServiceFood,

				Routes: []domainModel.Stop{
					{
						Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
							PriceScheme: "EMENU",
						}},
					},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee:    55,
								RoadFee:       100,
								Total:         155,
								AdditionalFee: map[string]float64{},
							},
							Total: 0,
						},
					},
				},
				Distance:  393,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
		}

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order, opts ...repository.Option) error {
				require.Equal(t, domainModel.StatusUserConfirmedPrice, order.Status)
				return nil
			})
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderUpdated(orderID), gomock.Any()).Return(nil)

		dapi.ConfirmPrice(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("not allow every status except WAITINIG_USER_CONFIRM_PRICE", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"
		order := &domainModel.Order{
			OrderID: orderID,
			Status:  domainModel.StatusDriverMatched,
			Quote: domainModel.Quote{
				QuoteID:     "LMFQ-123151",
				UserID:      "<user_id_1>",
				ServiceType: domainModel.ServiceFood,

				Routes: []domainModel.Stop{
					{
						Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
							PriceScheme: "EMENU",
						}},
					},
					{
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee:    55,
								RoadFee:       100,
								Total:         155,
								AdditionalFee: map[string]float64{},
							},
							Total: 0,
						},
					},
				},
				Distance:  393,
				CreatedAt: time.Now(),
				UpdatedAt: time.Now(),
			},
		}

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		dapi.ConfirmPrice(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("not allow status CANCELED", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: "EMENU",
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusCanceled)

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		dapi.ConfirmPrice(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "ORDER_CANCELED", err.Code)
	})

	t.Run("not allow status COMPLETED", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: "EMENU",
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusCompleted)

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		dapi.ConfirmPrice(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "ORDER_COMPLETED", err.Code)
	})
}

func TestFoodDeliveryAPI_CancelOrder(t *testing.T) {
	t.Parallel()

	const (
		orderID  = "LMF-123151"
		tripID   = "trip-a"
		driverID = "<driver_id_1>"
	)

	createOrder := func(driverID string) *domainModel.Order {
		nq, err := domainModel.NewQuote(
			"quote_1",
			"user-1",
			domainModel.ServiceFood,
			[]domainModel.Stop{{Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}}}, {}},
			string(domainModel.PriceSchemeRMS),
			false,
			domainModel.NewDeliveryFee(70.0, 30.0, 1000),
			domainModel.NewDeliveryFee(70.0, 30.0, 1000),
			[]domainModel.RegionCode{"AYUTTHAYA"},
			0,
		)
		require.NoError(t, err)
		ord := domainModel.NewOrder(*nq, orderID)

		ord.ExpireAt = time.Now().Add(time.Hour)

		if driverID != "" {
			ord.Driver = driverID
		}
		return ord
	}

	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/food/order/"+orderID+"/cancel", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("cancel order and unassign driver", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil).AnyTimes()

		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)
		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{DriverID: driverID, BanLater: false, CurrentOrder: orderID, Status: domainModel.StatusAssigned}, nil)
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("cancel order and unassign driver", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil).AnyTimes()

		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)
		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{DriverID: driverID, BanLater: false, CurrentOrder: orderID, Status: domainModel.StatusAssigned}, nil)
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("cancel order, unassign driver and reassign order successfully", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		gctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ordWithStatus := func(status domainModel.Status) *domainModel.Order {
			ord := createOrder(driverID)
			ord.Status = status
			ord.PhoneContact = domainModel.PhoneContactData{
				DriverID: driverID,
				Time:     time.Now(),
			}
			return ord
		}

		cancelDetail := domainModel.CancelDetail{
			CancelledBy: "LINEMAN_RIDER",
			Source:      domainModel.SourceDriver,
			IsReassign:  true,
		}

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ordWithStatus(domainModel.StatusRestaurantAccepted), nil).Times(2)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{DriverID: driverID, BanLater: false, CurrentOrder: orderID, Status: domainModel.StatusAssigned}, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.assignmentLog.EXPECT().SoftDelete(gomock.Any(), orderID, 0).Return(nil)
		deps.orderService.EXPECT().UpsertCancelRevision(gomock.Any(), gomock.Any(), cancelDetail, gomock.Any()).Return(nil)
		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)
		deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, order *domainModel.Order, opts ...repository.Option) error {
			require.Empty(tt, order.Driver)
			require.Equal(tt, domainModel.StatusAssigningDriver, order.Status)
			require.Equal(tt, 1, order.DeliveringRound)
			require.True(tt, order.PhoneContact.IsZero(), "should be able to clear phone contact info for reassignment")
			return nil
		})
		deps.delivery.EXPECT().RejectOrder(gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledReassignViaDispatcherAPI.Name, gomock.Any()).Return(false)
		deps.distributor.EXPECT().Distribute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(func() {}, nil)
		stubLockDriver(tt, deps.driverService)

		orderID := gctx.Param("orderID")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		if err := dapi.Canceller.CancelOrder(gctx.Request.Context(), orderID, cancelDetail, NoopStatusValidator, NoopServiceValidator, nil, false); err != nil {
			_ = gctx.Error(err)
			return
		}
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("cancel order, unassign driver and expire reassign order when has error in distribution", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		gctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusRestaurantAccepted
		cancelDetail := domainModel.CancelDetail{
			CancelledBy: "LINEMAN_RIDER",
			Source:      domainModel.SourceDriver,
			IsReassign:  true,
		}

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil).AnyTimes()
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{DriverID: driverID, BanLater: false, CurrentOrder: orderID, Status: domainModel.StatusAssigned}, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
		deps.assignmentLog.EXPECT().SoftDelete(gomock.Any(), orderID, ord.DeliveringRound).Return(nil)
		deps.orderService.EXPECT().UpsertCancelRevision(gomock.Any(), gomock.Any(), cancelDetail, gomock.Any()).Return(nil)
		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)
		deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, order *domainModel.Order, opts ...repository.Option) error {
			require.Empty(tt, order.Driver)
			require.Equal(tt, domainModel.StatusAssigningDriver, order.Status)
			require.Equal(tt, 1, order.DeliveringRound)
			return nil
		})
		deps.delivery.EXPECT().RejectOrder(gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledReassignViaDispatcherAPI.Name, gomock.Any()).Return(false)
		deps.distributor.EXPECT().Distribute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(func() {}, errors.New("something went wrong"))
		deps.delivery.EXPECT().CSCancelOrder(gomock.Any(), gomock.Any()).Return(nil)
		deps.orderService.EXPECT().UpdateAndUpsertCanceledRevision(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.assignmentLog.EXPECT().
			AssignedDrivers(gomock.Any(), orderID).
			Return([]domainModel.Record{
				{
					DriverID:     "driver-id-1",
					PushAt:       timeutil.BangkokNow().Add(-time.Hour),
					AcceptBefore: timeutil.BangkokNow().Add(-time.Hour).Add(15 * time.Second),
				},
				{
					DriverID:     "driver-id-2",
					PushAt:       timeutil.BangkokNow(),
					AcceptBefore: timeutil.BangkokNow().Add(15 * time.Second),
				},
			}, nil)

		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{"driver-id-2"}, service.EventOrderCanceled(orderID, false, true, false), gomock.Any(), gomock.Any()).Return(nil)
		stubLockDriver(tt, deps.driverService)

		orderID := gctx.Param("orderID")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		if err := dapi.Canceller.CancelOrder(gctx.Request.Context(), orderID, cancelDetail, NoopStatusValidator, NoopServiceValidator, nil, false); err != nil {
			_ = gctx.Error(err)
			return
		}
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("do not unassign driver is driver is empty", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder("")
		ord.Status = domainModel.StatusAssigningDriver

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil).AnyTimes()

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())

		deps.assignmentLog.EXPECT().
			AssignedDrivers(gomock.Any(), orderID).
			Return([]domainModel.Record{
				{
					DriverID:     "driver-id-1",
					PushAt:       timeutil.BangkokNow().Add(-time.Hour),
					AcceptBefore: timeutil.BangkokNow().Add(-time.Hour).Add(15 * time.Second),
				},
				{
					DriverID:     "driver-id-2",
					PushAt:       timeutil.BangkokNow(),
					AcceptBefore: timeutil.BangkokNow().Add(15 * time.Second),
				},
			}, nil)

		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{"driver-id-2"}, service.EventOrderCanceled(orderID, false, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("LMFS-458: cancel expired order", func(tt *testing.T) {
		tt.Skip("til confirm with po")
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "ORDER_EXPIRED", err.Code)
	})

	t.Run("cancel order and Ban driver when BanLater is true", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched
		t := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		driver := &domainModel.Driver{DriverID: driverID, BanLater: true, BannedUntil: &t, CurrentOrder: orderID, Status: domainModel.StatusAssigned}

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil).AnyTimes()

		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any())

		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(driver, nil)
		deps.bansvc.EXPECT().
			Ban(gomock.Any(), driver, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("cancel order and driver carry b2b", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched
		ord.TripID = "trip-a"

		deps.tripServices.EXPECT().GetOngoingOrdersByTripID(gomock.Any(), gomock.Any()).Return([]string{orderID}, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), gomock.Any()).
			Return(ord, nil).AnyTimes()

		deps.tripServices.EXPECT().OnOrderCanceled(gomock.Any(), gomock.Any()).Return(domainModel.Trip{Status: domainModel.TripStatusCanceled}, nil)

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.delivery.EXPECT().EmbarkedTripOrders(gomock.Any(), gomock.Any())
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventQueueingOrderCanceled(orderID), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{
			DriverID: driverID, BanLater: false, CurrentOrder: orderID, CurrentTrip: tripID, Status: domainModel.StatusAssigned, QueueingOrders: []string{"LMF-123151"},
		}, nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		deps.tripServices.EXPECT().GetOrCreateTripFromOrderID(gomock.Any(), gomock.Any()).Return(domainModel.Trip{TripID: "b2b-trip", Orders: model.TripOrders{{OrderID: "LMF-123151"}}}, nil)
		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("cancel order then trip status CANCELED", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched
		ord.TripID = "trip-a"

		deps.tripServices.EXPECT().GetOngoingOrdersByTripID(gomock.Any(), gomock.Any()).Return([]string{orderID}, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), gomock.Any()).
			Return(ord, nil).AnyTimes()

		deps.tripServices.EXPECT().OnOrderCanceled(gomock.Any(), gomock.Any()).Return(domainModel.Trip{Status: domainModel.TripStatusCanceled}, nil)

		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{
			DriverID: driverID, BanLater: false, CurrentOrder: orderID, CurrentTrip: tripID, Status: domainModel.StatusAssigned,
		}, nil)

		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("cancel order then trip status DRIVE_TO", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Routes = []domainModel.Stop{
			{
				Location: domainModel.Location{
					Lat: 1.1,
					Lng: 1.1,
				},
			},
			{
				Location: domainModel.Location{
					Lat: 1.2,
					Lng: 1.2,
				},
			},
		}
		ord.Status = domainModel.StatusDriverMatched
		ord.TripID = "trip-a"

		tripAfterCancel := domainModel.Trip{
			TripID: "trip-a",
			Routes: domainModel.TripRoutes{
				{
					Location: domainModel.Location{
						Lat: 1.1,
						Lng: 1.1,
					},
					StopOrders: domainModel.TripStopOrders{
						{
							OrderID: "order-B",
						},
					},
				},
				{
					Location: domainModel.Location{
						Lat: 1.1,
						Lng: 1.1,
					},
					StopOrders: domainModel.TripStopOrders{
						{
							OrderID: "order-B",
						},
					},
				},
			},
			Orders: domainModel.TripOrders{
				{
					OrderID: orderID,
					Status:  domainModel.StatusCanceled,
				},
				{
					OrderID: "order-B",
					Status:  domainModel.StatusDriverToRestaurant,
				},
			},
			Status: domainModel.TripStatusDriveTo,
		}

		deps.tripServices.EXPECT().GetOngoingOrdersByTripID(gomock.Any(), gomock.Any()).Return([]string{orderID}, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), gomock.Any()).
			Return(ord, nil).AnyTimes()

		deps.tripServices.EXPECT().OnOrderCanceled(gomock.Any(), gomock.Any()).Return(tripAfterCancel, nil)

		deps.driverLocationService.EXPECT().GetDriverLocation(gomock.Any(), gomock.Any()).Return(&domainModel.DriverLocation{Location: domainModel.Location{Lat: 1, Lng: 1}}, nil)

		deps.mapService.EXPECT().FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 1, Lng: 1}, mapservice.Location{Lat: 1.1, Lng: 1.1}, gomock.Any()).Return(&domainModel.MapRoute{Distance: 2000, Duration: 30}, []domainModel.MapWaypoint{}, nil)

		deps.onTopFareService.EXPECT().GetOnTopFareAtCancel(gomock.Any(), gomock.Any()).Return([]domainModel.OnTopFare{}, nil)

		deps.orderService.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil)

		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{
			DriverID: driverID, BanLater: false, CurrentOrder: orderID, CurrentTrip: tripID, Status: domainModel.StatusAssigned,
		}, nil)

		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("cancel order then trip status COMPLETED", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched
		ord.TripID = "trip-a"

		deps.tripServices.EXPECT().GetOngoingOrdersByTripID(gomock.Any(), gomock.Any()).Return([]string{orderID}, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), gomock.Any()).
			Return(ord, nil).AnyTimes()

		deps.tripServices.EXPECT().OnOrderCanceled(gomock.Any(), gomock.Any()).Return(domainModel.Trip{Status: domainModel.TripStatusCompleted}, nil)

		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&domainModel.LocationWithUpdatedAt{}, nil)

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())
		deps.driverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{
			DriverID: driverID, BanLater: false, CurrentOrder: orderID, CurrentTrip: tripID, Status: domainModel.StatusAssigned,
		}, nil)

		deps.tripServices.EXPECT().ProcessFee(gomock.Any(), gomock.Any())

		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.tripServices.EXPECT().PublishMissionLogTripCompletedEvent(gomock.Any(), gomock.Any())
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})
}

func TestFoodDeliveryAPI_SystemCancelOrder(t *testing.T) {
	t.Parallel()

	const expectedOrderID = "order-id"

	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/delivery/food/order/%s/system-cancel", orderID), nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("Should OK when cancel order while order status is ASSIGNING_DRIVER or EXPIRED", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, recorder := req(expectedOrderID)
		sapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			OrderID: expectedOrderID,
			Quote:   domainModel.Quote{ServiceType: domainModel.ServiceFood},
			History: map[string]time.Time{},
			Status:  domainModel.StatusAssigningDriver,
		}
		ord.Quote.Routes = []domainModel.Stop{{Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}}}, {}}
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), gomock.Any()).
			Return(ord, nil).AnyTimes()

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(ctx, gomock.Any(), gomock.Any()).
			Return(nil)

		deps.assignmentLog.EXPECT().
			AssignedDrivers(ctx, expectedOrderID).
			Return([]domainModel.Record{
				{
					DriverID:     "driver-id-1",
					PushAt:       timeutil.BangkokNow().Add(-time.Hour),
					AcceptBefore: timeutil.BangkokNow().Add(-time.Hour).Add(15 * time.Second),
				},
				{
					DriverID:     "driver-id-2",
					PushAt:       timeutil.BangkokNow(),
					AcceptBefore: timeutil.BangkokNow().Add(15 * time.Second),
				},
			}, nil)

		deps.notifier.EXPECT().
			Notify(ctx, []string{"driver-id-2"}, service.EventOrderCanceled(expectedOrderID, false, false, false), gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())

		sapi.SystemCancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should error when cancel order while order status is not ASSIGNING_DRIVER or EXPIRED", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, _ := req(expectedOrderID)
		sapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(ctx, expectedOrderID).
			Return(&domainModel.Order{
				History: map[string]time.Time{},
				Status:  domainModel.StatusDriverMatched,
			}, nil)

		sapi.SystemCancelOrder(ctx)

		require.Contains(tt, ctx.Errors.Errors(), "ORDER_STATUS_NOT_ALLOWED: order status is not allowed")
	})

	t.Run("Should not notify the latest driver that is not currently accepting", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, recorder := req(expectedOrderID)
		sapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			OrderID: expectedOrderID,
			Quote:   domainModel.Quote{ServiceType: domainModel.ServiceFood},
			History: map[string]time.Time{},
			Status:  domainModel.StatusAssigningDriver,
		}
		ord.Quote.Routes = []domainModel.Stop{{Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}}}, {}}
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), gomock.Any()).
			Return(ord, nil).AnyTimes()

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(ctx, gomock.Any(), gomock.Any()).
			Return(nil)

		deps.assignmentLog.EXPECT().
			AssignedDrivers(ctx, expectedOrderID).
			Return([]domainModel.Record{
				{
					DriverID:     "driver-id-1",
					PushAt:       timeutil.BangkokNow().Add(-time.Hour),
					AcceptBefore: timeutil.BangkokNow().Add(-time.Hour).Add(15 * time.Second),
				},
			}, nil)

		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())

		sapi.SystemCancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should do nothing when there is no driver got assigned", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, recorder := req(expectedOrderID)
		sapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			OrderID: expectedOrderID,
			Quote:   domainModel.Quote{ServiceType: domainModel.ServiceFood},
			History: map[string]time.Time{},
			Status:  domainModel.StatusAssigningDriver,
		}
		ord.Quote.Routes = []domainModel.Stop{{Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}}}, {}}
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		deps.orderService.EXPECT().
			Get(gomock.Any(), gomock.Any()).
			Return(ord, nil).AnyTimes()

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(ctx, gomock.Any(), gomock.Any()).
			Return(nil)

		deps.assignmentLog.EXPECT().
			AssignedDrivers(ctx, expectedOrderID).
			Return([]domainModel.Record{}, nil)

		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any())

		sapi.SystemCancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestFoodDeliveryAPI_RestaurantAccept(t *testing.T) {
	t.Parallel()

	req := func(orderID string, req *RestaurantAcceptedOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/food/order/"+orderID+"/restaurant-accepted", testutil.JSON(req))
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("not allow status CANCELED", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: "EMENU",
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusCanceled)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "ORDER_CANCELED", err.Code)
	})

	t.Run("not allow status COMPLETED", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: "EMENU",
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusCompleted)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "ORDER_COMPLETED", err.Code)
	})

	t.Run("update total price and estimated cooking time to order", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{
			EstimatedCookingTimeInSecond: 20,
		})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mockRepositoryFind(tt, deps.orderService, orderID, order)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			stopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
			require.True(tt, valid)
			require.Equal(tt, domainModel.DurationSecond(20), stopInfo.EstimatedCookingTime)
			return nil
		})

		dapi.RestaurantAccepted(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("transition to restaurant accepted status if order is not revamped status", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{
			EstimatedCookingTimeInSecond: 20,
		})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mockRepositoryFind(tt, deps.orderService, orderID, order)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			stopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
			require.True(tt, valid)
			require.Equal(tt, domainModel.DurationSecond(20), stopInfo.EstimatedCookingTime)
			require.Equal(tt, domainModel.StatusRestaurantAccepted, order.Status)
			return nil
		})

		dapi.RestaurantAccepted(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("transition to ready status if order is revamped status", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.RevampedStatus = true

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{
			EstimatedCookingTimeInSecond: 20,
		})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mockRepositoryFind(tt, deps.orderService, orderID, order)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			stopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
			require.True(tt, valid)
			require.Equal(tt, domainModel.DurationSecond(20), stopInfo.EstimatedCookingTime)
			require.Equal(tt, domainModel.StatusReady, order.Status)
			return nil
		})

		dapi.RestaurantAccepted(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("order switch flow", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"
		order := &domainModel.Order{Quote: domainModel.Quote{Options: domainModel.OrderOptions{
			SwitchFlow: true,
		}}}

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("find order error: invalid order id", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{}, repository.ErrInvalidOrderID)

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "INVALID_REQUEST", err.Code)
		require.Equal(tt, "invalid order id", err.Message)
	})

	t.Run("find order error: data not found", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{}, repository.ErrNotFound)

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "NOT_FOUND", err.Code)
		require.Equal(tt, "data not found", err.Message)
	})

	t.Run("find order error: unhandled error", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{}, errors.New("unhandled error"))

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "SERVICE_INTERNAL_ERROR", err.Code)
		require.Equal(tt, "unhandled error", err.Message)
	})

	t.Run("cannot update price for RMS order", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: "EMENU",
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Autostart: true,
		}
		order := domainModel.NewOrder(q, orderID)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "INVALID_REQUEST", err.Code)
		require.Equal(tt, "cannot update price for RMS order", err.Message)
	})

	t.Run("error driver status is not DRIVER_MATCHED", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverArrived)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "INVALID_REQUEST", err.Code)
		require.Equal(tt, domainModel.ErrStatusNotAllowed.Error(), err.Message)
	})

	t.Run("unable to updateOrder: ErrDataNotFound", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{
			EstimatedCookingTimeInSecond: 20,
		})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			Return(mongodb.ErrDataNotFound)

		dapi.RestaurantAccepted(ctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "SERVICE_INTERNAL_ERROR", err.Code)
		require.Equal(tt, repository.ErrNotFound.Error(), err.Message)
	})

	t.Run("unable to updateOrder: unhandled error", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{
			EstimatedCookingTimeInSecond: 20,
		})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		stubLockDriverWithRetryDelayAndRetryAttempts(tt, deps.driverService)

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			Return(errors.New("unhandled error"))

		dapi.RestaurantAccepted(ctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "SERVICE_INTERNAL_ERROR", err.Code)
		require.Equal(tt, "unhandled error", err.Message)
	})

	t.Run("should error when cannot acquire lock driver (error locked)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,

			Routes: []domainModel.Stop{
				{
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{
			EstimatedCookingTimeInSecond: 20,
		})

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.driverService.EXPECT().TryLockDriver(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("something went wrong"))

		dapi.RestaurantAccepted(ctx)
		require.Equal(tt, http.StatusLocked, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, apiutil.ErrCodeResourceLocked, err.Code)
	})
}

// test auto transition that will call after accepted order
func TestFoodDeliveryAPI_RestaurantAccept_AutoTransitionOrder(t *testing.T) {
	t.Parallel()

	type assertFunc func(t *testing.T, deps *foodProviderDeps)

	mockOrder := func(tripID string, orderID string, status domainModel.Status, restaurantID string, dropOffID string) *domainModel.Order {
		return &domainModel.Order{
			TripID:  tripID,
			OrderID: orderID,
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{ID: restaurantID},
					{ID: dropOffID},
				},
			},
			Status: status,
		}
	}

	driverID := "lmdrv-001"
	orderID1 := "lmf-001"
	orderID2 := "lmf-002"
	orderID3 := "lmf-003"
	restaurant1 := "restaurant-1"
	restaurant2 := "restaurant-2"
	dropOff1 := "d1"
	dropOff2 := "d2"
	dropOff3 := "d3"
	tripID := "TRIP-001"

	type testcase struct {
		name          string
		acceptedOrder *domainModel.Order
		trip          domainModel.Trip
		assertFunc    []assertFunc
	}

	testcases := []testcase{
		{
			name:          "[AAA] no transition",
			assertFunc:    []assertFunc{},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant1, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusReady,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0}, {OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAA] transition to DRIVER_TO_RESTAURANT",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant1, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusDriveTo,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToRestaurant},
					{OrderID: orderID2, Status: domainModel.StatusDriverToRestaurant},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0}, {OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAA] transition to DRIVER_ARRIVED_RESTAURANT",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant, assertNextStatusDriverArrivedRestaurant},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant1, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusArrivedAt,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: domainModel.StatusDriverArrivedRestaurant},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0}, {OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAA][Reassigned] transition to DRIVER_TO_RESTAURANT with trip status is INIT",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant1, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusInit,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverMatched},
					{OrderID: orderID2, Status: domainModel.StatusDriverToRestaurant},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0}, {OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAA][Reassigned] transition to DRIVER_TO_RESTAURANT with trip status is INIT",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant1, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusInit,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverMatched},
					{OrderID: orderID2, Status: domainModel.StatusDriverToRestaurant},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0}, {OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAA] transition to DRIVER_TO_RESTAURANT when current route has DRIVER_TO_DESTINATION",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant, assertNextStatusDriverArrivedRestaurant},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant1, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusArrivedAt,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToDestination},
					{OrderID: orderID2, Status: domainModel.StatusDriverMatched},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0, Done: true}, {OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAA] transition to DRIVER_TO_RESTAURANT when current route has DRIVER_TO_DESTINATION",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant, assertNextStatusDriverArrivedRestaurant},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant1, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusArrivedAt,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToDestination},
					{OrderID: orderID2, Status: domainModel.StatusDriverArrivedRestaurant},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0, Done: true}, {OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAB] no transition when restaurant accept order in pickup 2",
			assertFunc:    []assertFunc{},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant2, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusReady,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0}, {OrderID: orderID2, StopID: 0}}},
					{ID: restaurant2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAB] transition to DRIVER_TO_RESTAURANT",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant},
			acceptedOrder: mockOrder(tripID, orderID2, domainModel.StatusRestaurantAccepted, restaurant2, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusDriveTo,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToRestaurant},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusDriverMatched},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0}, {OrderID: orderID2, StopID: 0}}},
					{ID: restaurant2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAB] transition to DRIVER_ARRIVED_RESTAURANT",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant, assertNextStatusDriverArrivedRestaurant},
			acceptedOrder: mockOrder(tripID, orderID2, domainModel.StatusRestaurantAccepted, restaurant2, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusArrivedAt,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusDriverMatched},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0}, {OrderID: orderID2, StopID: 0}}},
					{ID: restaurant2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[AAB] transition to DRIVER_ARRIVED_RESTAURANT when current route has DRIVER_TO_DESTINATION",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant, assertNextStatusDriverArrivedRestaurant},
			acceptedOrder: mockOrder(tripID, orderID2, domainModel.StatusRestaurantAccepted, restaurant2, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   0,
				Status:   domainModel.TripStatusArrivedAt,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToDestination},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusDriverMatched},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0, Done: true}, {OrderID: orderID2, StopID: 0}}},
					{ID: restaurant2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[ABB] no transition",
			assertFunc:    []assertFunc{},
			acceptedOrder: mockOrder(tripID, orderID3, domainModel.StatusRestaurantAccepted, restaurant2, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   1,
				Status:   domainModel.TripStatusDriveTo,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToDestination},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusRestaurantAccepted},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0, Done: true}}},
					{ID: restaurant2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[ABB] transition to DRIVER_TO_RESTAURANT",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant},
			acceptedOrder: mockOrder(tripID, orderID2, domainModel.StatusRestaurantAccepted, restaurant2, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   1,
				Status:   domainModel.TripStatusDriveTo,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToDestination},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusDriverToRestaurant},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0, Done: true}}},
					{ID: restaurant2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[ABB] transition to DRIVER_ARRIVED_RESTAURANT",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant, assertNextStatusDriverArrivedRestaurant},
			acceptedOrder: mockOrder(tripID, orderID2, domainModel.StatusRestaurantAccepted, restaurant2, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   1,
				Status:   domainModel.TripStatusArrivedAt,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToDestination},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusDriverArrivedRestaurant},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0, Done: true}}},
					{ID: restaurant2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
		{
			name:          "[ABB] transition to DRIVER_ARRIVED_RESTAURANT when current route has DRIVER_TO_DESTINATION",
			assertFunc:    []assertFunc{assertNextStatusDriverToRestaurant, assertNextStatusDriverArrivedRestaurant},
			acceptedOrder: mockOrder(tripID, orderID2, domainModel.StatusRestaurantAccepted, restaurant2, dropOff3),
			trip: domainModel.Trip{
				DriverID: driverID,
				TripID:   tripID,
				HeadTo:   1,
				Status:   domainModel.TripStatusArrivedAt,
				Orders: domainModel.TripOrders{
					{OrderID: orderID1, Status: domainModel.StatusDriverToDestination},
					{OrderID: orderID2, Status: domainModel.StatusRestaurantAccepted},
					{OrderID: orderID3, Status: domainModel.StatusDriverToDestination},
				},
				Routes: domainModel.TripRoutes{
					{ID: restaurant1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 0, Done: true}}},
					{ID: restaurant2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 0}, {OrderID: orderID3, StopID: 0, Done: true}}},
					{ID: dropOff1, StopOrders: domainModel.TripStopOrders{{OrderID: orderID1, StopID: 1}}},
					{ID: dropOff2, StopOrders: domainModel.TripStopOrders{{OrderID: orderID2, StopID: 1}}},
					{ID: dropOff3, StopOrders: domainModel.TripStopOrders{{OrderID: orderID3, StopID: 1}}},
				},
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			ctx, wg := safe.CreateWaitGroupOnContext(context.Background())

			dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			for _, assertFunc := range tc.assertFunc {
				assertFunc(tt, deps)
			}
			deps.repSvc.EXPECT().Publish(gomock.Any(), gomock.Any()).AnyTimes()
			deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{}, nil).AnyTimes()

			dapi.autoTransitionOrder(ctx, tc.acceptedOrder, tc.trip)
			wg.Wait()
		})
	}
}

func assertNextStatusDriverToRestaurant(t *testing.T, deps *foodProviderDeps) {
	deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *domainModel.Order, _ ...interface{}) error {
		require.Equal(t, domainModel.StatusDriverToRestaurant, order.Status)
		return nil
	})
	deps.tripServices.EXPECT().OnOrderChanged(gomock.Any(), gomock.Any()).Return(domainModel.Trip{}, nil)
}

func assertNextStatusDriverArrivedRestaurant(t *testing.T, deps *foodProviderDeps) {
	deps.tripServices.EXPECT().SyncTripOrderStatus(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *domainModel.Order, _ ...interface{}) error {
		require.Equal(t, domainModel.StatusDriverArrivedRestaurant, order.Status)
		return nil
	})
	deps.tripServices.EXPECT().OnOrderChanged(gomock.Any(), gomock.Any()).Return(domainModel.Trip{}, nil)
}

func TestFoodDeliveryAPI_EditOrder(t *testing.T) {
	t.Parallel()

	const (
		orderID  = "LMF-123151"
		driverID = "<driver_id1>"
	)

	req := func(req *EditOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/food/order/edit", testutil.JSON(req))
		return ctx, recorder
	}

	t.Run("replace picking items in routes[0] and delivery items in routes[1] and calculate price summary", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		items := []domainModel.Item{
			{
				Name:     "A",
				Price:    150,
				Quantity: 3,
			},
		}
		ctx, recorder := req(&EditOrderRequest{
			OrderID:    orderID,
			Items:      items,
			ItemsPrice: 450,
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		pickingItemsChangeDetail := domainModel.Items(q.Routes[0].PickingItems).CompareWithNewItems(items)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderItemsChanged(order, &pickingItemsChangeDetail), gomock.Any()).Return(nil)

		mockRepositoryFind(tt, deps.orderService, orderID, order)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			require.Equal(tt, items, order.Routes[0].PickingItems)
			require.Equal(tt, items, order.Routes[1].DeliveryItems)
			require.Equal(tt, 450.0, order.Routes[1].PriceSummary.ItemFee.ItemFee)
			require.Equal(tt, 55.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee, "base fee should not change")

			return nil
		})
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsVerifyOrderTimestampEnabled.Name).Return(false)

		api.EditOrder(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("dont send push notification when driver in order is empty", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = ""

		items := []domainModel.Item{
			{
				Name:     "A",
				Price:    150,
				Quantity: 3,
			},
		}
		ctx, recorder := req(&EditOrderRequest{
			OrderID:    orderID,
			Items:      items,
			ItemsPrice: 450,
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		mockRepositoryFind(tt, deps.orderService, orderID, order)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			require.Equal(tt, items, order.Routes[0].PickingItems)
			require.Equal(tt, 450.0, order.Routes[0].ItemsPrice)
			require.Equal(tt, items, order.Routes[1].DeliveryItems)
			require.Equal(tt, 450.0, order.Routes[1].PriceSummary.ItemFee.ItemFee)
			require.Equal(tt, 55.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee, "base fee should not change")

			return nil
		})
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsVerifyOrderTimestampEnabled.Name).Return(false)

		api.EditOrder(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("should update items discount if discount exists", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					CollectPayment: true,
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		items := []domainModel.Item{
			{
				Name:     "A",
				Price:    150,
				Quantity: 3,
			},
		}
		ctx, recorder := req(&EditOrderRequest{
			OrderID:    orderID,
			Items:      items,
			ItemsPrice: 450,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCouponAdvance,
					Category: "",
					Code:     "code20",
					Discount: 20.0,
				},
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		pickingItemsChangeDetail := domainModel.Items(q.Routes[0].PickingItems).CompareWithNewItems(items)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderItemsChanged(order, &pickingItemsChangeDetail), gomock.Any()).Return(nil)

		mockRepositoryFind(tt, deps.orderService, orderID, order)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			priceSummary := order.PriceSummary()
			require.Equal(tt, items, order.Routes[0].PickingItems)
			require.Equal(tt, items, order.Routes[1].DeliveryItems)
			require.Equal(tt, 450.0, order.Routes[1].PriceSummary.ItemFee.ItemFee)
			require.Equal(tt, 55.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee, "base fee should not change")
			require.Len(tt, priceSummary.DeliveryFee.Discounts, 0)
			require.Len(tt, priceSummary.ItemFee.Discounts, 1)
			require.Equal(tt, priceSummary.ItemFee.Discounts[0], domainModel.Discount{
				Type:     domainModel.DiscountTypeCouponAdvance,
				Category: "",
				Code:     "code20",
				Discount: 20.0,
			})

			return nil
		})
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsVerifyOrderTimestampEnabled.Name).Return(false)

		api.EditOrder(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("should update when updated_at field later than owner_changed_at field", func(tt *testing.T) {
		now := timeutil.BangkokNow().UTC()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					CollectPayment: true,
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID
		order.OwnerChangedAt = now.Add(-time.Second)

		items := []domainModel.Item{
			{
				Name:     "A",
				Price:    150,
				Quantity: 3,
			},
		}
		ctx, recorder := req(&EditOrderRequest{
			OrderID:    orderID,
			Items:      items,
			ItemsPrice: 450,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCouponAdvance,
					Category: "",
					Code:     "code20",
					Discount: 20.0,
				},
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
			UpdatedAt: now,
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		pickingItemsChangeDetail := domainModel.Items(q.Routes[0].PickingItems).CompareWithNewItems(items)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderItemsChanged(order, &pickingItemsChangeDetail), gomock.Any()).Return(nil)

		mockRepositoryFind(tt, deps.orderService, orderID, order)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			priceSummary := order.PriceSummary()
			require.Equal(tt, items, order.Routes[0].PickingItems)
			require.Equal(tt, items, order.Routes[1].DeliveryItems)
			require.Equal(tt, 450.0, order.Routes[1].PriceSummary.ItemFee.ItemFee)
			require.Equal(tt, 55.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee, "base fee should not change")
			require.Len(tt, priceSummary.DeliveryFee.Discounts, 0)
			require.Len(tt, priceSummary.ItemFee.Discounts, 1)
			require.Equal(tt, priceSummary.ItemFee.Discounts[0], domainModel.Discount{
				Type:     domainModel.DiscountTypeCouponAdvance,
				Category: "",
				Code:     "code20",
				Discount: 20.0,
			})

			return nil
		})
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsVerifyOrderTimestampEnabled.Name).Return(true)

		api.EditOrder(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("should not update if updated_at is not later than owner_changed_at field", func(tt *testing.T) {
		now := timeutil.BangkokNow().UTC()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					CollectPayment: true,
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID
		order.OwnerChangedAt = now

		items := []domainModel.Item{
			{
				Name:     "A",
				Price:    150,
				Quantity: 3,
			},
		}
		ctx, recorder := req(&EditOrderRequest{
			OrderID:    orderID,
			Items:      items,
			ItemsPrice: 450,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCouponAdvance,
					Category: "",
					Code:     "code20",
					Discount: 20.0,
				},
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
			UpdatedAt: now,
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		mockRepositoryFind(tt, deps.orderService, orderID, order)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsVerifyOrderTimestampEnabled.Name).Return(true)

		api.EditOrder(ctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})

	t.Run("should update delivery fee, if it is existing in request", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					CollectPayment: true,
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 0,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		items := []domainModel.Item{
			{
				Name:     "A",
				Price:    150,
				Quantity: 3,
			},
		}
		ctx, recorder := req(&EditOrderRequest{
			OrderID:                       orderID,
			Items:                         items,
			ItemsPrice:                    450,
			UserDeliveryFee:               types.Ptr(20.0),
			UserDeliveryFeeBeforeDiscount: types.Ptr(40.0),
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		pickingItemsChangeDetail := domainModel.Items(q.Routes[0].PickingItems).CompareWithNewItems(items)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderItemsChanged(order, &pickingItemsChangeDetail), gomock.Any()).Return(nil)

		mockRepositoryFind(tt, deps.orderService, orderID, order)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			priceSummary := order.PriceSummary()
			assert.Equal(tt, items, order.Routes[0].PickingItems)
			assert.Equal(tt, items, order.Routes[1].DeliveryItems)
			assert.Equal(tt, 450.0, order.Routes[1].PriceSummary.ItemFee.ItemFee)
			assert.Equal(tt, 55.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee, "base fee should not change")
			assert.Equal(tt, 20.0, order.Routes[1].PriceSummary.DeliveryFee.UserDeliveryFee)
			assert.Equal(tt, 40.0, order.Routes[1].PriceSummary.DeliveryFee.UserDeliveryFeeBeforeDiscount)
			assert.Len(tt, priceSummary.DeliveryFee.Discounts, 0)
			return nil
		})
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsVerifyOrderTimestampEnabled.Name).Return(false)

		api.EditOrder(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})
}

func TestFoodDeliveryAPI_UpdateDeliveryLocation(t *testing.T) {
	t.Parallel()

	const (
		region   = "AYUTTHAYA"
		driverID = "<driver_id>"
		orderID  = "<order_id>"

		memoOld     = "leave with mom"
		memoTypeOld = model.MemoTypeLeaveWithPerson
		memoNew     = "leave at the door"
		memoTypeNew = model.MemoTypeLeaveAtPlace
	)

	req := func(req *UpdateDeliveryLocationRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/delivery/food/order/update-delivery-location", testutil.JSON(req))
		ctx.Request.Header.Add("X-Region", "AYUTTHAYA")
		return ctx, recorder
	}

	t.Run("should update delivery location and price summary (document with deprecated restaurant type)", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			PayAtStop:   1,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							SubTotal:      155,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
					Memo:     memoOld,
					MemoType: memoTypeOld,
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}

		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "test name",
			Phone:       "**********",
			Address:     "address",
			AddressMemo: "address memo",
			Location:    location,
			Memo:        memoNew, // Should be used instead of AddressMemo "address memo"
			MemoType:    memoTypeNew,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
			UserOnTops: []UserOnTop{{
				Title:  "RAIN_SURGE",
				Amount: 50,
			}},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, region, scheme)

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, region, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		mockRepositoryFind(tt, deps.orderService, orderID, order)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		deps.tripServices.EXPECT().OnUpdateDeliveryLocation(gomock.Any(), gomock.Any()).Return(domainModel.Trip{}, nil)

		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			require.Equal(tt, "test name", order.Routes[1].Name)
			require.Equal(tt, "**********", order.Routes[1].Phones[0])
			require.Equal(tt, "address", order.Routes[1].Address)
			require.Equal(tt, memoNew, order.Routes[1].Memo)
			require.Equal(tt, memoTypeNew, order.Routes[1].MemoType)
			require.Equal(tt, domainModel.Location{
				Lat: location.Lat,
				Lng: location.Lng,
			}, order.Routes[1].Location)

			require.Equal(tt, order.Routes[1].Distance, types.Distance(1.5))
			require.Equal(tt, order.Distance, types.Distance(1.5))
			require.Equal(tt, order.Routes[1].EstimatedDeliveryTime, domainModel.DurationSecond(100))
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.SubTotal, 40.0)
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.Total, 10.0)
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.Commission, 0.0)     // 0.15 * subTotal
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.WithholdingTax, 1.2) // 0.03 * (subTotal - commission)
			require.Equal(tt, order.Routes[1].PriceSummary.Total, 165.0)
			require.Equal(tt, []domainModel.OnTopScheme{{
				Scheme:           domainModel.UserPriceInterventionOnTopScheme,
				Name:             "RAIN_SURGE",
				Amount:           50,
				BundleAmount:     50,
				IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
			}}, order.Routes[1].PriceSummary.DeliveryFee.OnTopScheme)

			return nil
		})
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderItemsChanged(order, nil), gomock.Any()).Return(nil)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("should update delivery location and price summary", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			PayAtStop:   1,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						Service:     domainModel.ServiceFood,
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "test name",
			Phone:       "**********",
			Address:     "address",
			AddressMemo: memoNew, // Backward compat test
			MemoType:    memoTypeNew,

			Location: location,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
			UserOnTops: []UserOnTop{{
				Title:  "RAIN_SURGE",
				Amount: 50,
			}},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, region, scheme)

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceFood, region, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		mockRepositoryFind(tt, deps.orderService, orderID, order)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		deps.tripServices.EXPECT().OnUpdateDeliveryLocation(gomock.Any(), gomock.Any()).Return(domainModel.Trip{}, nil)

		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			require.Equal(tt, order.Routes[1].Name, "test name")
			require.Equal(tt, order.Routes[1].Phones[0], "**********")
			require.Equal(tt, order.Routes[1].Address, "address")
			require.Equal(tt, order.Routes[1].Memo, memoNew)
			require.Equal(tt, order.Routes[1].MemoType, memoTypeNew)
			require.Equal(tt, order.Routes[1].Location, domainModel.Location{
				Lat: location.Lat,
				Lng: location.Lng,
			})
			require.Equal(tt, order.Routes[1].Distance, types.Distance(1.5))
			require.Equal(tt, order.Distance, types.Distance(1.5))
			require.Equal(tt, order.Routes[1].EstimatedDeliveryTime, domainModel.DurationSecond(100))
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.SubTotal, 40.0)
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.Total, 10.0)
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.Commission, 0.0) // 0.15 * subTotal
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.WithholdingTax, 1.2)
			require.Equal(tt, order.Routes[1].PriceSummary.Total, 165.0)
			require.Equal(tt, []domainModel.OnTopScheme{{
				Scheme:           domainModel.UserPriceInterventionOnTopScheme,
				Name:             "RAIN_SURGE",
				Amount:           50,
				BundleAmount:     50,
				IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
			}}, order.Routes[1].PriceSummary.DeliveryFee.OnTopScheme)

			return nil
		})
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderItemsChanged(order, nil), gomock.Any()).Return(nil)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})
	t.Run("should update delivery location and price summary of agent model order should calculate commission but ignore withholding tax", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceFood,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						Service:     domainModel.ServiceFood,
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "test name",
			Phone:       "**********",
			Address:     "address",
			AddressMemo: "",
			Memo:        "",
			MemoType:    "", // Should be Text
			Location:    location,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
			RevenueAgentModel: true,
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, region, scheme)

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		mockRepositoryFind(tt, deps.orderService, orderID, order)

		deps.tripServices.EXPECT().OnUpdateDeliveryLocation(gomock.Any(), gomock.Any()).Return(domainModel.Trip{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.Commission, 6.0) // 0.15 * subTotal
			require.Equal(tt, order.Routes[1].PriceSummary.DeliveryFee.WithholdingTax, 0.0)
			require.Equal(tt, order.Routes[1].PriceSummary.Total, 165.0)
			require.Equal(tt, order.Routes[1].Memo, "")
			require.Equal(tt, order.Routes[1].MemoType, model.MemoTypeText)

			return nil
		})
		deps.notifier.EXPECT().
			Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("should update delivery location on messenger round trip", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "A",
							Price:    155,
							Quantity: 1,
						},
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{
						Service:     domainModel.ServiceFood,
						PriceScheme: domainModel.PriceSchemeRMS,
					}},
				},
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
				},
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "C",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
				},
			},
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.PriceSchemeKeyDefault, priceScheme)
		calculator := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceMessenger, region, scheme)

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:           orderID,
			Name:              "test name",
			Phone:             "**********",
			Address:           "address",
			AddressMemo:       "address memo",
			Location:          location,
			Discounts:         []DiscountReq{},
			RevenueAgentModel: true,
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()
		mockRepositoryFind(tt, deps.orderService, orderID, order)
		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{region}, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 10}, nil, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 10, Duration: 300}, nil, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)
		deps.orderService.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.tripServices.EXPECT().OnUpdateDeliveryLocation(gomock.Any(), gomock.Any()).Return(domainModel.Trip{}, nil)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})
}

func TestFoodDeliveryAPI_RatingDriver(t *testing.T) {
	t.Parallel()

	const (
		driverID = "<driver_id>"
		orderID  = "<order_id>"
	)

	req := func(req *RatingDriverRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/driver/rating", testutil.JSON(req))
		return ctx, recorder
	}

	t.Run("bad request when status is not expected", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		order := &domainModel.Order{
			Status: domainModel.StatusDriverMatched,
			Driver: driverID,
		}
		var tags []string

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.driverService.EXPECT().
			RatingDriver(gomock.Any(), uint32(5), "", tags, order).
			Return(service.ErrRatingInvalidOrderStatus)
		ctx, recorder := req(&RatingDriverRequest{
			OrderID: orderID,
			Score:   5,
		})
		api.RatingDriver(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})
}

func TestFoodDeliveryAPI_TipDriver(t *testing.T) {
	t.Parallel()

	const (
		driverID = "<driver_id>"
		orderID  = "<order_id>"
		tripID   = "<trip_id>"
	)

	req := func(req *TipDriverRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/delivery/driver/tip", testutil.JSON(req))
		return ctx, recorder
	}

	t.Run("add tip transaction to driver when success", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		api.Cfg = OrderAPIConfig{
			TipDriverTitle: "title",
			TipDriverBody:  "body [oid] end",
		}

		tip := types.Money(10)
		driverTxn := domainModel.DriverTransaction{
			WalletBalance: 100,
		}
		expectedDriverTxn := driverTxn
		expectedDriverTxn.WalletBalance = 100.0 + tip

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{
				Status:  domainModel.StatusCompleted,
				Driver:  driverID,
				OrderID: orderID,
				TripID:  tripID,
			}, nil)
		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.driverRepository.EXPECT().
			GetTodayEarning(gomock.Any(), gomock.Any()).
			Return(*domainModel.NewDriverEarning(driverID, 20, 10, 15, 0), nil)
		deps.driverRepository.EXPECT().
			SetTodayEarning(gomock.Any(), gomock.Any()).
			Return(nil)
		deps.orderService.EXPECT().
			UpdateOrderByField(gomock.Any(), orderID, gomock.Any()).DoAndReturn(func(ctx context.Context, orderID string, srcUpdater model.OrderUpdater, opts ...repository.Option) error {
			pushQuery := srcUpdater["$push"].(bson.M)
			tipRec := pushQuery["tips"].(model.TipRecord)
			require.NotEmpty(tt, tipRec.CreatedAt)

			exptUpdater := model.NewOrderUpdater().
				AddTipAmount(10).AddTipRecord(model.TipRecord{
				ID:          "",
				OrderStatus: "COMPLETED",
				Amount:      10,
				CreatedAt:   tipRec.CreatedAt,
			})
			require.Equal(tt, exptUpdater, srcUpdater)
			return nil
		})
		deps.driverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(),
			driverID,
			domainModel.SystemTransactionChannel,
			domainModel.WalletTopUpTransactionAction,
			domainModel.SuccessTransactionStatus,
			gomock.Any(), // transaction info builder service.TransactionInfos(*domainModel.NewDriverTipWithTipID(driverID, orderID, tip, "INCOMING_TIP_ID")),
			gomock.Any(), // transactionOptions
		).DoAndReturn(func(_ context.Context, _ string, _ model.TransactionChannel, _ model.TransactionAction, _ model.TransactionStatus,
			builder service.TransactionInfosBuilder, _ ...func(*service.ProcessDriverTransactionOption),
		) (model.DriverTransaction, []model.Transaction, error) {
			dt := domainModel.DriverTransaction{}
			infos, _ := builder(dt)
			require.Contains(tt, infos, *domainModel.NewDriverTipWithTipID(driverID, orderID, tip, ""))
			return dt, nil, nil
		})
		deps.orderService.EXPECT().
			UpdateOrderRevisionByField(gomock.Any(), orderID, 0, gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventTipDriver(tripID, orderID, "title", "body <order_id> end")).Return(nil)
		ctx, recorder := req(&TipDriverRequest{
			OrderID: orderID,
			Amount:  tip,
		})
		api.TipDriver(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("allow tip once per order", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{
				Status:    domainModel.StatusCompleted,
				Driver:    driverID,
				TipAmount: 100,
				Tips: domainModel.TipRecords{domainModel.TipRecord{
					ID:        "",
					Amount:    100,
					CreatedAt: time.Now(),
				}},
			}, nil)
		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)

		ctx, recorder := req(&TipDriverRequest{
			OrderID: orderID,
			Amount:  10,
		})
		api.TipDriver(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})

	t.Run("add tip transaction to driver with Tip ID when success", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		api.Cfg = OrderAPIConfig{
			TipDriverTitle: "title",
			TipDriverBody:  "body [oid] end",
		}

		tip := types.Money(10)
		driverTxn := domainModel.DriverTransaction{
			WalletBalance: 100,
		}
		expectedDriverTxn := driverTxn
		expectedDriverTxn.WalletBalance = 100.0 + tip

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{
				Status: domainModel.StatusCompleted,
				Quote: model.Quote{
					ServiceType: model.ServiceFood,
				},
				Driver:  driverID,
				OrderID: orderID,
				TripID:  tripID,
			}, nil)
		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.driverRepository.EXPECT().
			GetTodayEarning(gomock.Any(), gomock.Any()).
			Return(*domainModel.NewDriverEarning(driverID, 20, 10, 15, 0), nil)
		deps.driverRepository.EXPECT().
			SetTodayEarning(gomock.Any(), gomock.Any()).
			Return(nil)
		deps.orderService.EXPECT().
			UpdateOrderByField(gomock.Any(), orderID, gomock.Any()).DoAndReturn(func(ctx context.Context, orderID string, srcUpdater model.OrderUpdater, opts ...repository.Option) error {
			pushQuery := srcUpdater["$push"].(bson.M)
			tipRec := pushQuery["tips"].(model.TipRecord)
			require.NotEmpty(tt, tipRec.CreatedAt)

			exptUpdater := model.NewOrderUpdater().
				AddTipAmount(10).AddTipRecord(model.TipRecord{
				ID:          "INCOMING_TIP_ID",
				OrderStatus: "COMPLETED",
				Amount:      10,
				CreatedAt:   tipRec.CreatedAt,
			})
			require.Equal(tt, exptUpdater, srcUpdater)
			return nil
		})
		deps.driverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(),
			driverID,
			domainModel.SystemTransactionChannel,
			domainModel.WalletTopUpTransactionAction,
			domainModel.SuccessTransactionStatus,
			gomock.Any(), // transaction info builder service.TransactionInfos(*domainModel.NewDriverTipWithTipID(driverID, orderID, tip, "INCOMING_TIP_ID")),
			gomock.Any(), // transactionOptions
		).DoAndReturn(func(_ context.Context, _ string, _ model.TransactionChannel, _ model.TransactionAction, _ model.TransactionStatus,
			builder service.TransactionInfosBuilder, _ ...func(*service.ProcessDriverTransactionOption),
		) (model.DriverTransaction, []model.Transaction, error) {
			dt := domainModel.DriverTransaction{}
			infos, _ := builder(dt)
			require.Contains(tt, infos, *domainModel.NewDriverTipWithTipID(driverID, orderID, tip, "INCOMING_TIP_ID"))
			return dt, nil, nil
		})
		deps.orderService.EXPECT().
			UpdateOrderRevisionByField(gomock.Any(), orderID, 0, gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventTipDriver(tripID, orderID, "title", "body <order_id> end")).Return(nil)
		ctx, recorder := req(&TipDriverRequest{
			OrderID: orderID,
			Amount:  tip,
			TipID:   "INCOMING_TIP_ID",
		})
		api.TipDriver(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
		var tipOrderResponse TipOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &tipOrderResponse)
		require.Equal(tt, "<driver_id>", tipOrderResponse.DriverID)
		require.Equal(tt, "COMPLETED", tipOrderResponse.OrderStatus)
	})

	t.Run("just stamp tip info to driver with Tip ID when got tip request while order not completed", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		api.Cfg = OrderAPIConfig{
			TipDriverTitle: "title",
			TipDriverBody:  "body [oid] end",
		}

		tip := types.Money(10)

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{
				Status: domainModel.StatusDriverToDestination,
				Quote: model.Quote{
					ServiceType: model.ServiceFood,
				},
				Driver:  driverID,
				OrderID: orderID,
				TripID:  tripID,
			}, nil)
		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.orderService.EXPECT().
			UpdateOrderByField(gomock.Any(), orderID, gomock.Any()).DoAndReturn(func(ctx context.Context, orderID string, srcUpdater model.OrderUpdater, opts ...repository.Option) error {
			pushQuery := srcUpdater["$push"].(bson.M)
			tipRec := pushQuery["tips"].(model.TipRecord)
			require.NotEmpty(tt, tipRec.CreatedAt)

			exptUpdater := model.NewOrderUpdater().
				AddTipAmount(10).AddTipRecord(model.TipRecord{
				ID:          "IMCOMING_TIP_ID",
				OrderStatus: "DRIVER_TO_DESTINATION",
				Amount:      10,
				CreatedAt:   tipRec.CreatedAt,
			})
			require.Equal(tt, exptUpdater, srcUpdater)
			return nil
		})

		ctx, recorder := req(&TipDriverRequest{
			OrderID: orderID,
			Amount:  tip,
			TipID:   "IMCOMING_TIP_ID",
		})
		api.TipDriver(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
		var tipOrderResponse TipOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &tipOrderResponse)
		require.Equal(tt, "<driver_id>", tipOrderResponse.DriverID)
		require.Equal(tt, "DRIVER_TO_DESTINATION", tipOrderResponse.OrderStatus)
	})

	t.Run("return error when found existed tip id", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		fapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		fapi.Cfg = OrderAPIConfig{
			TipDriverTitle: "title",
			TipDriverBody:  "body [oid] end",
		}

		tip := types.Money(10)
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{
				Status: domainModel.StatusCompleted,
				Quote: model.Quote{
					ServiceType: model.ServiceFood,
				},
				Driver:  driverID,
				OrderID: orderID,
				TripID:  tripID,
				Tips: model.TipRecords{
					{
						ID:          "EXISTED_TIP_ID",
						Amount:      10,
						OrderStatus: string(model.StatusDriverToDestination),
						CreatedAt:   timeutil.BangkokNow(),
					},
				},
			}, nil)
		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)
		ctx, recorder := req(&TipDriverRequest{
			OrderID: orderID,
			Amount:  tip,
			TipID:   "EXISTED_TIP_ID",
		})
		fapi.TipDriver(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
		var apiErr api.Error
		testutil.DecodeJSON(tt, recorder.Body, &apiErr)
		exptErr := AlreadyTip()
		require.Equal(tt, exptErr.Code, apiErr.Code)
		require.Equal(tt, exptErr.Message, apiErr.Message)
	})

	t.Run("success when tip a completed bike order", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		api.Cfg = OrderAPIConfig{
			TipDriverTitle: "title",
			TipDriverBody:  "body [oid] end",
		}

		tip := types.Money(10)
		driverTxn := domainModel.DriverTransaction{
			WalletBalance: 100,
		}
		expectedDriverTxn := driverTxn
		expectedDriverTxn.WalletBalance = 100.0 + tip

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{
				Quote:   domainModel.Quote{ServiceType: domainModel.ServiceBike},
				Status:  domainModel.StatusCompleted,
				Driver:  driverID,
				OrderID: orderID,
				TripID:  tripID,
			}, nil)
		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.driverRepository.EXPECT().
			GetTodayEarning(gomock.Any(), gomock.Any()).
			Return(*domainModel.NewDriverEarning(driverID, 20, 10, 15, 0), nil)
		deps.driverRepository.EXPECT().
			SetTodayEarning(gomock.Any(), gomock.Any()).
			Return(nil)
		deps.orderService.EXPECT().
			UpdateOrderByField(gomock.Any(), orderID, gomock.Any()).DoAndReturn(func(ctx context.Context, orderID string, srcUpdater model.OrderUpdater, opts ...repository.Option) error {
			pushQuery := srcUpdater["$push"].(bson.M)
			tipRec := pushQuery["tips"].(model.TipRecord)
			require.NotEmpty(tt, tipRec.CreatedAt)

			exptUpdater := model.NewOrderUpdater().
				AddTipAmount(10).AddTipRecord(model.TipRecord{
				ID:          "",
				OrderStatus: "COMPLETED",
				Amount:      10,
				CreatedAt:   tipRec.CreatedAt,
			})
			require.Equal(tt, exptUpdater, srcUpdater)
			return nil
		})
		deps.driverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(),
			driverID,
			domainModel.SystemTransactionChannel,
			domainModel.WalletTopUpTransactionAction,
			domainModel.SuccessTransactionStatus,
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(_ context.Context, _ string, _ model.TransactionChannel, _ model.TransactionAction, _ model.TransactionStatus,
			builder service.TransactionInfosBuilder, _ ...func(*service.ProcessDriverTransactionOption),
		) (model.DriverTransaction, []model.Transaction, error) {
			dt := domainModel.DriverTransaction{}
			infos, _ := builder(dt)
			require.Contains(tt, infos, *domainModel.NewDriverTipWithTipID(driverID, orderID, tip, ""))
			return dt, nil, nil
		})
		deps.orderService.EXPECT().
			UpdateOrderRevisionByField(gomock.Any(), orderID, 0, gomock.Any()).Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventTipDriver(tripID, orderID, "title", "body <order_id> end")).Return(nil)
		ctx, recorder := req(&TipDriverRequest{
			OrderID: orderID,
			Amount:  tip,
		})
		api.TipDriver(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("return error when tip an incomplete bike order", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		fapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		tip := types.Money(10)

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{
				Quote:   domainModel.Quote{ServiceType: domainModel.ServiceBike},
				Status:  domainModel.StatusDriveTo,
				Driver:  driverID,
				OrderID: orderID,
				TripID:  tripID,
			}, nil)
		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)

		ctx, recorder := req(&TipDriverRequest{
			OrderID: orderID,
			Amount:  tip,
		})

		fapi.TipDriver(ctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
		var apiErr api.Error
		testutil.DecodeJSON(tt, recorder.Body, &apiErr)
		exptErr := ErrOrderStatusNotAllowed()
		require.Equal(tt, exptErr.Code, apiErr.Code)
		require.Equal(tt, exptErr.Message, apiErr.Message)
	})

	t.Run("should not process earnings and notify when db error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		api.Cfg = OrderAPIConfig{
			TipDriverTitle: "title",
			TipDriverBody:  "body [oid] end",
		}

		tip := types.Money(10)
		driverTxn := domainModel.DriverTransaction{
			WalletBalance: 100,
		}
		expectedDriverTxn := driverTxn
		expectedDriverTxn.WalletBalance = 100.0 + tip

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(&domainModel.Order{
				Status:  domainModel.StatusCompleted,
				Driver:  driverID,
				OrderID: orderID,
				TripID:  tripID,
			}, nil)
		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.orderService.EXPECT().
			UpdateOrderByField(gomock.Any(), orderID, gomock.Any()).DoAndReturn(func(ctx context.Context, orderID string, srcUpdater model.OrderUpdater, opts ...repository.Option) error {
			pushQuery := srcUpdater["$push"].(bson.M)
			tipRec := pushQuery["tips"].(model.TipRecord)
			require.NotEmpty(tt, tipRec.CreatedAt)

			exptUpdater := model.NewOrderUpdater().
				AddTipAmount(10).AddTipRecord(model.TipRecord{
				ID:          "",
				OrderStatus: "COMPLETED",
				Amount:      10,
				CreatedAt:   tipRec.CreatedAt,
			})
			require.Equal(tt, exptUpdater, srcUpdater)
			return nil
		})
		deps.driverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(),
			driverID,
			domainModel.SystemTransactionChannel,
			domainModel.WalletTopUpTransactionAction,
			domainModel.SuccessTransactionStatus,
			gomock.Any(), // transaction info builder service.TransactionInfos(*domainModel.NewDriverTipWithTipID(driverID, orderID, tip, "INCOMING_TIP_ID")),
			gomock.Any(), // transactionOptions
		).DoAndReturn(func(_ context.Context, _ string, _ model.TransactionChannel, _ model.TransactionAction, _ model.TransactionStatus,
			builder service.TransactionInfosBuilder, _ ...func(*service.ProcessDriverTransactionOption),
		) (model.DriverTransaction, []model.Transaction, error) {
			dt := domainModel.DriverTransaction{}
			infos, _ := builder(dt)
			require.Contains(tt, infos, *domainModel.NewDriverTipWithTipID(driverID, orderID, tip, ""))
			return dt, nil, nil
		})

		deps.orderService.EXPECT().
			UpdateOrderRevisionByField(gomock.Any(), orderID, 0, gomock.Any()).
			Return(errors.New("error"))
		deps.driverRepository.EXPECT().
			GetTodayEarning(gomock.Any(), gomock.Any()).
			Times(0)
		deps.driverRepository.EXPECT().
			SetTodayEarning(gomock.Any(), gomock.Any()).
			Times(0)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), gomock.Any(), gomock.Any()).
			Times(0)

		ctx, recorder := req(&TipDriverRequest{
			OrderID: orderID,
			Amount:  tip,
		})
		api.TipDriver(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code, recorder.Body.String())
	})
}

func TestFoodDeliveryAPI_GetOrderStatus(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	const (
		orderID = "<order_id>"
	)
	now := time.Now()

	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("GET", "/v1/delivery/food/order/"+orderID+"/status", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

	deps.orderService.EXPECT().
		Get(gomock.Any(), orderID).
		Return(&domainModel.Order{
			Status: domainModel.StatusDriverMatched,
			History: map[string]time.Time{
				string(domainModel.StatusDriverMatched): now,
			},
		}, nil)

	ctx, recorder := req(orderID)
	api.GetOrderStatus(ctx)

	require.Equal(t, http.StatusOK, recorder.Code)
	var resp GetOrderStatusResponse
	testutil.DecodeJSON(t, recorder.Body, &resp)
	require.Equal(t, string(domainModel.StatusDriverMatched), resp.Status)
	require.True(t, resp.Time.Equal(now))
}

func TestOrder_SetRestaurantDirections(t *testing.T) {
	t.Run("set correctly", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		order := &domainModel.Order{
			Quote: domainModel.Quote{
				Routes: []domainModel.Stop{
					{ID: "res-1"},
					{ID: "res-2"},
					{ID: "LINEMANUSER"},
				},
			},
		}
		res1Dir := domainModel.RestaurantDirection{
			RestaurantID: "res-1",
			Parking:      "res-1's parking",
			Direction:    "res-1's direction",
			Memo:         "res-1's memo",
			UpdatedAt:    time.Now(),
		}
		res2Dir := domainModel.RestaurantDirection{
			RestaurantID: "res-2",
			Parking:      "res-2's parking",
			Direction:    "res-2's direction",
			Memo:         "res-2's memo",
			UpdatedAt:    time.Now(),
		}

		api, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		dirs := []domainModel.RestaurantDirection{res1Dir, res2Dir}
		err := api.setRestaurantDirection(order, dirs)
		require.NoError(tt, err)
		require.Equal(tt, res1Dir, order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood).RestaurantDirection)
		require.Equal(tt, res2Dir, order.Routes[1].Info.StopInfo.(*domainModel.StopInfoFood).RestaurantDirection)
	})
}

func CreateValidPriceScheme() domainModel.SettingDeliveryFeePriceScheme {
	name := "price-scheme-01"
	ps := domainModel.NewSettingDeliveryFeePriceScheme(name, 40, domainModel.RoadFeeTiers{}, []float64{1, 0.5, 0.33}, types.Money(10), 10)
	return *ps
}

func Test_isUserRequiredDoNotDisturb(t *testing.T) {
	type args struct {
		whiteListUsers []string
		order          domainModel.Order
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "food order",
			args: args{
				whiteListUsers: []string{"user-A"},
				order: domainModel.Order{
					Quote: domainModel.Quote{
						Routes: []domainModel.Stop{
							{},
							{},
						},
						UserID:      "user-A",
						ServiceType: domainModel.ServiceFood,
					},
				},
			},
			want: true,
		},
		{
			name: "mart order",
			args: args{
				whiteListUsers: []string{"user-A"},
				order: domainModel.Order{
					Quote: domainModel.Quote{
						Routes: []domainModel.Stop{
							{},
							{},
						},
						UserID:      "user-A",
						ServiceType: domainModel.ServiceMart,
					},
				},
			},
			want: false,
		},
		{
			name: "messenger order",
			args: args{
				whiteListUsers: []string{"user-A"},
				order: domainModel.Order{
					Quote: domainModel.Quote{
						Routes: []domainModel.Stop{
							{},
							{},
						},
						UserID:      "user-A",
						ServiceType: domainModel.ServiceMessenger,
					},
				},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := isUserRequiredDoNotDisturb(tt.args.whiteListUsers, tt.args.order); got != tt.want {
				t.Errorf("isUserRequiredDoNotDisturb() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestFoodDeliveryAPI_RefundOrder(t *testing.T) {
	t.Parallel()

	driverID := "LMD1"
	orderID := "LMF-1"
	orderID2 := "LMF-2"
	tripID := "TRIP-1"

	makeReq := func(req RefundOrderRequest, orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/delivery/orders/%v/refund", orderID)
		gctx.SetGinParams(gin.Params{{Key: "orderID", Value: orderID}})
		gctx.Body().JSON(req).Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	createOrder := func(orderId string, itemFee float64, discounts domainModel.DiscountList) *domainModel.Order {
		nq, err := domainModel.NewQuote(
			orderId,
			"user-1",
			domainModel.ServiceFood,
			[]domainModel.Stop{
				{},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							PaymentMethod: domainModel.PaymentMethodLinemanWallet,
						},
						ItemFee: domainModel.ItemFeeSummary{
							Discounts:     discounts,
							ItemFee:       itemFee,
							SubTotal:      0,
							Total:         0,
							PaymentMethod: domainModel.PaymentMethodLinemanWallet,
						},
					},
				},
			},
			string(domainModel.PriceSchemeRMS),
			false,
			domainModel.NewDeliveryFee(70.0, 30.0, 1000),
			domainModel.NewDeliveryFee(70.0, 30.0, 1000),
			[]domainModel.RegionCode{"AYUTTHAYA"},
			1,
		)
		require.NoError(t, err)
		ord := domainModel.NewOrder(*nq, orderId)
		ord.Summary(0.15, 0.03)
		ord.Driver = driverID
		ord.Status = domainModel.StatusCompleted
		ord.TripID = tripID
		return ord
	}

	createEpaymentOrder := func(orderId string, itemFee float64, discounts domainModel.DiscountList) *domainModel.Order {
		return createOrder(orderId, itemFee-discounts.Total(), nil)
	}

	createCashAdvEpaymentOrder := func(orderId string, itemFee float64, discounts domainModel.DiscountList) *domainModel.Order {
		order := createOrder(orderId, itemFee-discounts.Total(), discounts)
		order.Options.DriverMoneyFlow = domainModel.FlowCashAdvancementEpayment
		return order
	}

	t.Run("should return bad request error when has validation error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		req := RefundOrderRequest{
			ItemRefundAmount: 0,
			VoidRiderWage:    false,
			RequestID:        "request-1",
			RequestedBy:      "<EMAIL>",
			Remark:           "คนขับจบงานไม่ได้้ส่งจริง",
		}
		order := createEpaymentOrder(orderID, 50.0, nil)
		order.TripID = tripID
		trip, err := domainModel.NewEndedTrip(tripID, *order)
		require.NoError(tt, err)
		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, NewValidationError("validation error"))
		deps.orderService.EXPECT().Get(gomock.Any(), orderID, gomock.Any()).Return(order, nil)
		deps.tripServices.EXPECT().GetTripByID(gomock.Any(), tripID).Return(trip, nil)
		gctx, recorder := makeReq(req, orderID)
		api.RefundOrder(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})

	t.Run("should return internal error when has others error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		req := RefundOrderRequest{
			ItemRefundAmount: 0,
			VoidRiderWage:    false,
			RequestID:        "request-1",
			RequestedBy:      "<EMAIL>",
			Remark:           "คนขับจบงานไม่ได้้ส่งจริง",
		}
		order1 := createEpaymentOrder(orderID, 50.0, nil)
		order1.TripID = tripID
		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("something went wrong"))
		deps.orderService.EXPECT().Get(gomock.Any(), orderID, gomock.Any()).Return(order1, errors.New("something went wrong"))
		gctx, recorder := makeReq(req, orderID)
		api.RefundOrder(gctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code, recorder.Body.String())
	})

	t.Run("refund driver wage on single order", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)

		order := createEpaymentOrder(orderID, 50.0, nil)
		trip, err := domainModel.NewEndedTrip(tripID, *order)
		require.NoError(tt, err)
		trip.DriverWageSummary.TotalDriverWage = 50.0
		orderTrans := []domainModel.Transaction{
			{
				TransactionID: "on-top-1",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   10.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.OnTopTransactionType,
				},
			},
			{
				TransactionID: "on-top-2",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   10.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.OnTopTransactionType,
				},
			},
		}
		lastWageTrans := []domainModel.Transaction{
			{
				TransactionID: "wage-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   50.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.DriverWageType,
				},
			},
		}
		refundRecord := &domainModel.TripRefundRecord{OrderID: orderID}
		expectedInfos := []expectedTransactionInfo{
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "wage-transaction",
				Amount:   50.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeWageTransactionSubType,
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "on-top-1",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeOnTopTransactionSubType,
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "on-top-2",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeOnTopTransactionSubType,
			},
		}
		expectedRefundRecord := &domainModel.TripRefundRecord{
			OrderID:          orderID,
			ItemRefundAmount: 0,
			VoidRiderWage:    true,
			WageChange:       -50.0,
			Wage:             0,
			ChargedOnTop:     20.0,
		}

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.transactionRpo.EXPECT().FindOnTopsByOrderID(gomock.Any(), order.OrderID).Return(orderTrans, nil)
		deps.driverTransactionService.EXPECT().GetWageTransactionFromTrip(gomock.Any(), order.TripID).Return(lastWageTrans, nil)

		infos, err := api.refundDriverWage(context.Background(), order, &trip, refundRecord)
		require.NoError(tt, err)
		require.Equal(tt, true, order.IsVoidedWage)
		require.Equal(tt, true, trip.Orders[0].IsVoidedWage)
		require.ElementsMatch(tt, expectedInfos, mapActualTransactionInfos(infos))
		require.Equal(tt, expectedRefundRecord, refundRecord)
	})

	t.Run("refund driver wage on bundled 2 orders", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)

		latestWage := types.NewMoney(50.0)
		newWage := types.NewMoney(35.0)
		order1 := createEpaymentOrder(orderID, 50.0, nil)
		order2 := createEpaymentOrder(orderID2, 50.0, nil)
		trip, err := domainModel.NewEndedTrip(tripID, *order1)
		require.NoError(tt, err)
		err = trip.AddNewOrder(order2, 0, 2)
		require.NoError(tt, err)
		trip.DriverWageSummary.TotalDriverWage = latestWage

		orderTrans := []domainModel.Transaction{}
		lastWageTrans := []domainModel.Transaction{
			{
				TransactionID: "wage-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.DriverWageType,
					Amount:   latestWage,
				},
			},
		}
		refundRecord := &domainModel.TripRefundRecord{OrderID: orderID}
		expectedInfos := []expectedTransactionInfo{
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "wage-transaction",
				Amount:   15.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeWageTransactionSubType,
			},
		}
		expectedRefundRecord := &domainModel.TripRefundRecord{
			OrderID:          orderID,
			ItemRefundAmount: 0,
			VoidRiderWage:    true,
			WageChange:       -15.0,
			Wage:             newWage,
			ChargedOnTop:     0,
		}

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.transactionRpo.EXPECT().FindOnTopsByOrderID(gomock.Any(), order1.OrderID).Return(orderTrans, nil)
		deps.driverTransactionService.EXPECT().GetWageTransactionFromTrip(gomock.Any(), order1.TripID).Return(lastWageTrans, nil)
		deps.tripServices.EXPECT().RecalculateTripWage(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, trip *domainModel.Trip, optFns ...interface{}) error {
			trip.DriverWageSummary.TotalDriverWage = newWage
			return nil
		})

		infos, err := api.refundDriverWage(context.Background(), order1, &trip, refundRecord)
		require.NoError(tt, err)
		require.Equal(tt, true, order1.IsVoidedWage)
		require.Equal(tt, true, trip.Orders[0].IsVoidedWage)
		require.ElementsMatch(tt, expectedInfos, mapActualTransactionInfos(infos))
		require.Equal(tt, expectedRefundRecord, refundRecord)
	})

	t.Run("refund all driver wage on bundled 2 orders", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)

		latestWage := types.NewMoney(35.0)
		newWage := types.NewMoney(0.0)
		order1 := createEpaymentOrder(orderID, 50.0, nil)
		order2 := createEpaymentOrder(orderID2, 50.0, nil)
		order2.IsVoidedWage = true
		trip, err := domainModel.NewEndedTrip(tripID, *order1)
		require.NoError(tt, err)
		err = trip.AddNewOrder(order2, 0, 2)
		require.NoError(tt, err)
		trip.Orders[1].IsVoidedWage = true
		trip.DriverWageSummary.TotalDriverWage = latestWage
		trip.AddRefundRecord(domainModel.TripRefundRecord{
			OrderID:          order2.OrderID,
			ItemRefundAmount: 0,
			VoidRiderWage:    true,
			WageChange:       -15.0,
			Wage:             35,
			ChargedOnTop:     0,
		})

		orderTrans := []domainModel.Transaction{}
		lastWageTrans := []domainModel.Transaction{
			{
				TransactionID: "wage-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.DriverWageType,
					Amount:   latestWage,
				},
			},
		}
		refundRecord := &domainModel.TripRefundRecord{OrderID: orderID}
		expectedInfos := []expectedTransactionInfo{
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "wage-transaction",
				Amount:   latestWage,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeWageTransactionSubType,
			},
		}
		expectedRefundRecord := &domainModel.TripRefundRecord{
			OrderID:          orderID,
			ItemRefundAmount: 0,
			VoidRiderWage:    true,
			WageChange:       latestWage.Neg(),
			Wage:             newWage,
			ChargedOnTop:     0,
		}

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.transactionRpo.EXPECT().FindOnTopsByOrderID(gomock.Any(), order1.OrderID).Return(orderTrans, nil)
		deps.driverTransactionService.EXPECT().GetWageTransactionFromTrip(gomock.Any(), order1.TripID).Return(lastWageTrans, nil)

		infos, err := api.refundDriverWage(context.Background(), order1, &trip, refundRecord)
		require.NoError(tt, err)
		require.Equal(tt, true, order1.IsVoidedWage)
		require.Equal(tt, true, trip.Orders[0].IsVoidedWage)
		require.Equal(tt, true, trip.Orders[1].IsVoidedWage)
		require.ElementsMatch(tt, expectedInfos, mapActualTransactionInfos(infos))
		require.Equal(tt, expectedRefundRecord, refundRecord)
	})

	t.Run("should refund item fee for order e-payment correctly", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)

		itemRefundAmount := types.NewMoney(20.0)
		order := createEpaymentOrder(orderID, 50.0, nil)
		refundRecord := &domainModel.TripRefundRecord{OrderID: orderID}
		expectedInfos := []expectedTransactionInfo{
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "",
				Amount:   20.0,
				Category: domainModel.CreditTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeItemFeeTransactionSubType,
			},
		}
		expectedRefundRecord := &domainModel.TripRefundRecord{
			OrderID:          orderID,
			ItemRefundAmount: 20.0,
			VoidRiderWage:    false,
			WageChange:       0,
			Wage:             0,
			ChargedOnTop:     0,
		}

		api, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		infos, err := api.refundItemFee(context.Background(), order, itemRefundAmount, refundRecord)
		require.NoError(tt, err)
		require.ElementsMatch(tt, expectedInfos, mapActualTransactionInfos(infos))
		require.Equal(tt, expectedRefundRecord, refundRecord)
	})

	t.Run("should refund item fee for order cash adv e-payment correctly", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)

		itemRefundAmount := types.NewMoney(50.0)
		order := createCashAdvEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 10.0}})
		stringutil.JsonPrint(order.Routes[1].PriceSummary.ItemFee)
		orderTrans := []domainModel.Transaction{
			{
				TransactionID: "item-fee-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.ItemFeeTransactionType,
					Amount:   40.0,
				},
			},
			{
				TransactionID: "coupon-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.CouponTransactionType,
					Amount:   10.0,
				},
			},
			{
				TransactionID: "other-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.OnTopTransactionType,
					Amount:   10.0,
				},
			},
		}
		refundRecord := &domainModel.TripRefundRecord{OrderID: orderID}
		expectedInfos := []expectedTransactionInfo{
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "item-fee-transaction",
				Amount:   40.0,
				Category: domainModel.CreditTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeItemFeeTransactionSubType,
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "coupon-transaction",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeCouponTransactionSubType,
			},
		}
		expectedRefundRecord := &domainModel.TripRefundRecord{
			OrderID:          orderID,
			ItemRefundAmount: 50.0,
			VoidRiderWage:    false,
			WageChange:       0,
			Wage:             0,
			ChargedOnTop:     0,
		}

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.transactionRpo.EXPECT().FindByOrderID(gomock.Any(), orderID).Return(orderTrans, nil)

		infos, err := api.refundItemFee(context.Background(), order, itemRefundAmount, refundRecord)
		require.NoError(tt, err)
		require.ElementsMatch(tt, expectedInfos, mapActualTransactionInfos(infos))
		require.Equal(tt, expectedRefundRecord, refundRecord)
	})

	t.Run("should fully refund correctly", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		req := RefundOrderRequest{
			ItemRefundAmount: 50.0,
			VoidRiderWage:    true,
			RequestID:        "request-1",
			RequestedBy:      "<EMAIL>",
			Remark:           "คนขับจบงานไม่ได้้ส่งจริง",
		}

		lastWageTrans := []domainModel.Transaction{
			{
				TransactionID: "wage-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   50.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.DriverWageType,
				},
			},
		}
		orderTrans := []domainModel.Transaction{
			{
				TransactionID: "item-fee-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.ItemFeeTransactionType,
					Amount:   40.0,
				},
			},
			{
				TransactionID: "coupon-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.CouponTransactionType,
					Amount:   10.0,
				},
			},
			{
				TransactionID: "other-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.WithholdingTransactionType,
					Amount:   10.0,
				},
			},
		}
		onTopTrans := []domainModel.Transaction{
			{
				TransactionID: "on-top-1",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   10.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.OnTopTransactionType,
				},
			},
			{
				TransactionID: "on-top-2",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   10.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.OnTopTransactionType,
				},
			},
		}

		order := createCashAdvEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 10.0}})
		trip, err := domainModel.NewEndedTrip(tripID, *order)
		require.NoError(tt, err)
		trip.DriverWageSummary.TotalDriverWage = 50.0

		expectedInfos := []expectedTransactionInfo{
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "item-fee-transaction",
				Amount:   40.0,
				Category: domainModel.CreditTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeItemFeeTransactionSubType,
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "coupon-transaction",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeCouponTransactionSubType,
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "wage-transaction",
				Amount:   50.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeWageTransactionSubType,
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "on-top-1",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeOnTopTransactionSubType,
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "on-top-2",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeOnTopTransactionSubType,
			},
		}
		expectedRefundRecord := domainModel.TripRefundRecord{
			OrderID:          orderID,
			ItemRefundAmount: 50.0,
			VoidRiderWage:    true,
			WageChange:       -50.0,
			Wage:             0.0,
			ChargedOnTop:     20.0,
		}

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), order.OrderID, gomock.Any()).Return(order, nil)
		deps.tripServices.EXPECT().GetTripByID(gomock.Any(), order.TripID).Return(trip, nil)
		deps.transactionRpo.EXPECT().FindByOrderID(gomock.Any(), orderID).Return(orderTrans, nil)
		deps.transactionRpo.EXPECT().FindOnTopsByOrderID(gomock.Any(), order.OrderID).Return(onTopTrans, nil)
		deps.driverTransactionService.EXPECT().GetWageTransactionFromTrip(gomock.Any(), order.TripID).Return(lastWageTrans, nil)
		deps.driverTransactionService.EXPECT().ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, driverID string, channel domainModel.TransactionChannel, action domainModel.TransactionAction, status domainModel.TransactionStatus, builder service.TransactionInfosBuilder, _ interface{}) (domainModel.DriverTransaction, []domainModel.Transaction, error) {
				require.Equal(tt, order.Driver, driverID)
				require.Equal(tt, domainModel.AdminTransactionChannel, channel)
				require.Equal(tt, domainModel.DeductChargeTransactionAction, action)
				require.Equal(tt, domainModel.SuccessTransactionStatus, status)
				infos, err := builder(domainModel.DriverTransaction{})
				require.NoError(tt, err)
				require.ElementsMatch(tt, expectedInfos, mapActualTransactionInfos(infos))
				return domainModel.DriverTransaction{}, []domainModel.Transaction{}, nil
			})
		deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		deps.tripRepo.EXPECT().SetRefundData(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, t domainModel.Trip) error {
			rec := t.RefundHistory[0]
			actualRecord := domainModel.TripRefundRecord{
				OrderID:          rec.OrderID,
				ItemRefundAmount: rec.ItemRefundAmount,
				VoidRiderWage:    rec.VoidRiderWage,
				WageChange:       rec.WageChange,
				Wage:             rec.Wage,
				ChargedOnTop:     rec.ChargedOnTop,
			}
			require.Equal(tt, expectedRefundRecord, actualRecord)
			return nil
		})

		gctx, recorder := makeReq(req, orderID)
		api.RefundOrder(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("should fully refund correctly with delay", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		req := RefundOrderRequest{
			ItemRefundAmount: 50.0,
			VoidRiderWage:    true,
			RequestID:        "request-1",
			RequestedBy:      "<EMAIL>",
			Remark:           "คนขับจบงานไม่ได้้ส่งจริง",
		}

		lastWageTrans := []domainModel.Transaction{
			{
				TransactionID: "wage-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   50.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.DriverWageType,
				},
			},
		}
		orderTrans := []domainModel.Transaction{
			{
				TransactionID: "item-fee-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.ItemFeeTransactionType,
					Amount:   40.0,
				},
			},
			{
				TransactionID: "coupon-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.CouponTransactionType,
					Amount:   10.0,
				},
			},
			{
				TransactionID: "other-transaction",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.WithholdingTransactionType,
					Amount:   10.0,
				},
			},
		}
		onTopTrans := []domainModel.Transaction{
			{
				TransactionID: "on-top-1",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   10.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.OnTopTransactionType,
				},
			},
			{
				TransactionID: "on-top-2",
				Info: domainModel.TransactionInfo{
					TripID:   tripID,
					Amount:   10.0,
					Category: domainModel.WalletTransactionCategory,
					Type:     domainModel.OnTopTransactionType,
				},
			},
		}

		order := createCashAdvEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 10.0}})
		trip, err := domainModel.NewEndedTrip(tripID, *order)
		require.NoError(tt, err)
		trip.DriverWageSummary.TotalDriverWage = 50.0

		expectedWalletItems := []expectedTransactionItem{
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "coupon-transaction",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeCouponTransactionSubType,
				Remark:   "คนขับจบงานไม่ได้้ส่งจริง",
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "wage-transaction",
				Amount:   50.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeWageTransactionSubType,
				Remark:   "คนขับจบงานไม่ได้้ส่งจริง",
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "on-top-1",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeOnTopTransactionSubType,
				Remark:   "คนขับจบงานไม่ได้้ส่งจริง",
			},
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "on-top-2",
				Amount:   10.0,
				Category: domainModel.WalletTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeOnTopTransactionSubType,
				Remark:   "คนขับจบงานไม่ได้้ส่งจริง",
			},
		}
		expectedCreditItems := []expectedTransactionItem{
			{
				OrderID:  orderID,
				TripID:   tripID,
				RefID:    "item-fee-transaction",
				Amount:   40.0,
				Category: domainModel.CreditTransactionCategory,
				Type:     domainModel.PenaltyChargeTransactionType,
				SubType:  domainModel.PenaltyChargeItemFeeTransactionSubType,
				Remark:   "คนขับจบงานไม่ได้้ส่งจริง",
			},
		}
		expectedRefundRecord := domainModel.TripRefundRecord{
			OrderID:          orderID,
			ItemRefundAmount: 50.0,
			VoidRiderWage:    true,
			WageChange:       -50.0,
			Wage:             0.0,
			ChargedOnTop:     20.0,
		}

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{
			AtomicOrderDBConfig: NewAtomicOrderDBConfig(OrderDBConfig{
				DelayRefundExecution: true,
				RefundEffectiveTime:  "21:30",
			}),
		}, &AtomicContingencyConfig{})
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), order.OrderID, gomock.Any()).Return(order, nil)
		deps.tripServices.EXPECT().GetTripByID(gomock.Any(), order.TripID).Return(trip, nil)
		deps.transactionRpo.EXPECT().FindByOrderID(gomock.Any(), orderID).Return(orderTrans, nil)
		deps.transactionRpo.EXPECT().FindOnTopsByOrderID(gomock.Any(), order.OrderID).Return(onTopTrans, nil)
		deps.driverTransactionService.EXPECT().GetWageTransactionFromTrip(gomock.Any(), order.TripID).Return(lastWageTrans, nil)
		deps.groupTransactionRepo.EXPECT().CreateMany(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, groupTxns []domainModel.GroupTransaction) error {
				require.Len(tt, groupTxns, 2)

				creditGroupTxn := groupTxns[0]
				walletGroupTxn := groupTxns[1]

				require.Len(tt, creditGroupTxn.Items(), len(expectedCreditItems))
				require.Len(tt, walletGroupTxn.Items(), len(expectedWalletItems))
				require.Equal(tt, creditGroupTxn.EffectiveTime().Hour(), 21)
				require.Equal(tt, creditGroupTxn.EffectiveTime().Minute(), 30)
				require.Equal(tt, walletGroupTxn.EffectiveTime().Hour(), 21)
				require.Equal(tt, walletGroupTxn.EffectiveTime().Minute(), 30)
				require.Equal(tt, creditGroupTxn.RequestedBy(), req.RequestedBy)
				require.Equal(tt, walletGroupTxn.RequestedBy(), req.RequestedBy)
				require.ElementsMatch(tt, expectedCreditItems, mapActualTransactionItems(creditGroupTxn.Items()))
				require.ElementsMatch(tt, expectedWalletItems, mapActualTransactionItems(walletGroupTxn.Items()))

				return nil
			})
		deps.orderService.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		deps.tripRepo.EXPECT().SetRefundData(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, t domainModel.Trip) error {
			rec := t.RefundHistory[0]
			actualRecord := domainModel.TripRefundRecord{
				OrderID:          rec.OrderID,
				ItemRefundAmount: rec.ItemRefundAmount,
				VoidRiderWage:    rec.VoidRiderWage,
				WageChange:       rec.WageChange,
				Wage:             rec.Wage,
				ChargedOnTop:     rec.ChargedOnTop,
			}
			require.Equal(tt, expectedRefundRecord, actualRecord)
			return nil
		})

		gctx, recorder := makeReq(req, orderID)
		api.RefundOrder(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("validation should work correctly", func(tt *testing.T) {
		type testcase struct {
			name             string
			itemRefundAmount types.Money
			voidRiderWage    bool
			order            *domainModel.Order
			before           func(o *domainModel.Order, t *domainModel.Trip)
			expectedErr      bool
		}

		testcases := []testcase{
			{
				name:             "status not COMPLETED",
				expectedErr:      true,
				itemRefundAmount: 10.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					o.Status = domainModel.StatusDriverMatched
				},
			},
			{
				name:             "item fee payment method isn't e-payment",
				expectedErr:      true,
				itemRefundAmount: 10.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					o.Routes[1].PriceSummary.ItemFee.PaymentMethod = domainModel.PaymentMethodCash
				},
			},
			{
				name:             "delivery fee payment method isn't e-payment",
				expectedErr:      true,
				itemRefundAmount: 10.0,
				voidRiderWage:    true,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					o.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = domainModel.PaymentMethodCash
				},
			},
			{
				name:             "item fee payment method isn't e-payment",
				expectedErr:      true,
				itemRefundAmount: 10.0,
				voidRiderWage:    true,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					o.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = domainModel.PaymentMethodCash
				},
			},
			{
				name:             "serivce type isn't FOOD",
				expectedErr:      true,
				itemRefundAmount: 10.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					o.ServiceType = domainModel.ServiceMessenger
				},
			},
			{
				name:             "negative item refund amount",
				expectedErr:      true,
				itemRefundAmount: -1,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, nil),
			},
			{
				name:             "refund item fee on fraud order",
				expectedErr:      false,
				itemRefundAmount: 10.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					err := o.SetFraudStatus(domainModel.FraudStatusFraud)
					require.NoError(tt, err)
				},
			},
			{
				name:             "refund item fee on voided wage order",
				expectedErr:      false,
				itemRefundAmount: 10.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					o.MarkVoidedWage()
				},
			},
			{
				name:             "refund item fee equal to subTotal",
				expectedErr:      false,
				itemRefundAmount: 50.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before:           func(o *domainModel.Order, t *domainModel.Trip) {},
			},
			{
				name:             "refund item fee over than limit",
				expectedErr:      true,
				itemRefundAmount: 151.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before:           func(o *domainModel.Order, t *domainModel.Trip) {},
			},
			{
				name:             "refund item fee within limit with coupon",
				expectedErr:      false,
				itemRefundAmount: 30.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 20}}),
				before:           func(o *domainModel.Order, t *domainModel.Trip) {},
			},
			{
				name:             "refund item fee within subTotal with coupon",
				expectedErr:      false,
				itemRefundAmount: 50.0,
				voidRiderWage:    false,
				order:            createEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 20}}),
				before:           func(o *domainModel.Order, t *domainModel.Trip) {},
			},
			{
				name:             "partial refund coupon (cash adv e-pay)",
				expectedErr:      true,
				itemRefundAmount: 31.0,
				voidRiderWage:    false,
				order:            createCashAdvEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 20}}),
				before:           func(o *domainModel.Order, t *domainModel.Trip) {},
			},
			{
				name:             "refund all item fee (cash adv e-pay)",
				expectedErr:      false,
				itemRefundAmount: 50.0,
				voidRiderWage:    false,
				order:            createCashAdvEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 20}}),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					o.Options.DriverMoneyFlow = domainModel.FlowCashAdvancementEpayment
					o.Routes[1].PriceSummary.ItemFee.ItemFee = 30.0
					o.Routes[1].PriceSummary.ItemFee.Discounts = domainModel.DiscountList{{Discount: 20}}
					o.Summary(0.15, 0.03)
				},
			},
			{
				name:             "void rider wage on on fraud order",
				expectedErr:      true,
				itemRefundAmount: 10.0,
				voidRiderWage:    true,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					err := o.SetFraudStatus(domainModel.FraudStatusFraud)
					require.NoError(tt, err)
				},
			},
			{
				name:             "void rider wage on voided wage order",
				expectedErr:      true,
				itemRefundAmount: 10.0,
				voidRiderWage:    true,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					o.MarkVoidedWage()
				},
			},
			{
				name:             "void rider wage but has some fraud order",
				expectedErr:      true,
				itemRefundAmount: 10.0,
				voidRiderWage:    true,
				order:            createEpaymentOrder(orderID, 50.0, nil),
				before: func(o *domainModel.Order, t *domainModel.Trip) {
					order2 := createEpaymentOrder(orderID2, 50.0, nil)
					order2.FraudStatus = domainModel.FraudStatusFraud
					err := t.AddNewOrder(order2, 0, 2)
					require.NoError(tt, err)
					t.Orders[1].IsFraud = true
				},
			},
		}

		for _, tc := range testcases {
			tt.Run(tc.name, func(ttt *testing.T) {
				order := tc.order
				trip, err := domainModel.NewEndedTrip(tripID, *order)
				require.NoError(ttt, err)

				if tc.before != nil {
					tc.before(order, &trip)
				}
				validationErr := validateRefundOrder(*order, trip, tc.itemRefundAmount, tc.voidRiderWage, "request-id-1", 100.0)
				if tc.expectedErr {
					require.Error(ttt, validationErr)
				} else {
					require.NoError(ttt, validationErr)
				}
			})
		}
	})
}

func TestFoodDeliveryAPI_ValidateRefundOrder(t *testing.T) {
	t.Parallel()

	driverID := "LMD1"
	orderID := "LMF-1"
	tripID := "TRIP-1"

	makeReq := func(req RefundOrderRequest, orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/delivery/orders/%v/refund", orderID)
		gctx.SetGinParams(gin.Params{{Key: "orderID", Value: orderID}})
		gctx.Body().JSON(req).Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	createOrder := func(orderId string, itemFee float64, discounts domainModel.DiscountList) *domainModel.Order {
		nq, err := domainModel.NewQuote(
			orderId,
			"user-1",
			domainModel.ServiceFood,
			[]domainModel.Stop{
				{},
				{
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							PaymentMethod: domainModel.PaymentMethodLinemanWallet,
						},
						ItemFee: domainModel.ItemFeeSummary{
							Discounts:     discounts,
							ItemFee:       itemFee,
							SubTotal:      0,
							Total:         0,
							PaymentMethod: domainModel.PaymentMethodLinemanWallet,
						},
					},
				},
			},
			string(domainModel.PriceSchemeRMS),
			false,
			domainModel.NewDeliveryFee(70.0, 30.0, 1000),
			domainModel.NewDeliveryFee(70.0, 30.0, 1000),
			[]domainModel.RegionCode{"AYUTTHAYA"},
			1,
		)
		require.NoError(t, err)
		ord := domainModel.NewOrder(*nq, orderId)
		ord.Summary(0.15, 0.03)
		ord.Driver = driverID
		ord.Status = domainModel.StatusCompleted
		ord.TripID = tripID
		return ord
	}

	createEpaymentOrder := func(orderId string, itemFee float64, discounts domainModel.DiscountList) *domainModel.Order {
		return createOrder(orderId, itemFee-discounts.Total(), nil)
	}

	createCashAdvEpaymentOrder := func(orderId string, itemFee float64, discounts domainModel.DiscountList) *domainModel.Order {
		order := createOrder(orderId, itemFee-discounts.Total(), discounts)
		order.Options.DriverMoneyFlow = domainModel.FlowCashAdvancementEpayment
		return order
	}

	t.Run("should return bad request error when has validation error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		req := RefundOrderRequest{
			ItemRefundAmount: 0,
			VoidRiderWage:    false,
			RequestID:        "request-1",
			RequestedBy:      "<EMAIL>",
			Remark:           "คนขับจบงานไม่ได้้ส่งจริง",
		}
		order := createEpaymentOrder(orderID, 50.0, nil)
		order.TripID = tripID
		trip, err := domainModel.NewEndedTrip(tripID, *order)
		require.NoError(tt, err)
		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, NewValidationError("validation error"))
		deps.orderService.EXPECT().Get(gomock.Any(), orderID, gomock.Any()).Return(order, nil)
		deps.tripServices.EXPECT().GetTripByID(gomock.Any(), tripID).Return(trip, nil)
		gctx, recorder := makeReq(req, orderID)
		api.ValidateRefundOrder(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})

	t.Run("ValidateRefundOrder - should return internal error when has others error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		req := RefundOrderRequest{
			ItemRefundAmount: 0,
			VoidRiderWage:    false,
			RequestID:        "request-1",
			RequestedBy:      "<EMAIL>",
			Remark:           "คนขับจบงานไม่ได้้ส่งจริง",
		}
		order1 := createEpaymentOrder(orderID, 50.0, nil)
		order1.TripID = tripID
		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("something went wrong"))
		deps.orderService.EXPECT().Get(gomock.Any(), orderID, gomock.Any()).Return(order1, errors.New("something went wrong"))
		gctx, recorder := makeReq(req, orderID)
		api.ValidateRefundOrder(gctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code, recorder.Body.String())
	})

	t.Run("ValidateRefundOrder - should return validation refund success", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		req := RefundOrderRequest{
			ItemRefundAmount: 50.0,
			VoidRiderWage:    true,
			RequestID:        "request-1",
			RequestedBy:      "<EMAIL>",
			Remark:           "คนขับจบงานไม่ได้้ส่งจริง",
		}

		order := createCashAdvEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 10.0}})
		trip, err := domainModel.NewEndedTrip(tripID, *order)
		require.NoError(tt, err)
		trip.DriverWageSummary.TotalDriverWage = 50.0

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), order.OrderID, gomock.Any()).Return(order, nil)
		deps.tripServices.EXPECT().GetTripByID(gomock.Any(), order.TripID).Return(trip, nil)

		gctx, recorder := makeReq(req, orderID)
		api.ValidateRefundOrder(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("ValidateRefundOrder - should fully validation refund success with delay", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		req := RefundOrderRequest{
			ItemRefundAmount: 50.0,
			VoidRiderWage:    true,
			RequestID:        "request-1",
			RequestedBy:      "<EMAIL>",
			Remark:           "คนขับจบงานไม่ได้้ส่งจริง",
		}

		order := createCashAdvEpaymentOrder(orderID, 50.0, domainModel.DiscountList{{Discount: 10.0}})
		trip, err := domainModel.NewEndedTrip(tripID, *order)
		require.NoError(tt, err)
		trip.DriverWageSummary.TotalDriverWage = 50.0

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{
			AtomicOrderDBConfig: NewAtomicOrderDBConfig(OrderDBConfig{
				DelayRefundExecution: true,
				RefundEffectiveTime:  "21:30",
			}),
		}, &AtomicContingencyConfig{})
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.orderService.EXPECT().Get(gomock.Any(), order.OrderID, gomock.Any()).Return(order, nil)
		deps.tripServices.EXPECT().GetTripByID(gomock.Any(), order.TripID).Return(trip, nil)

		gctx, recorder := makeReq(req, orderID)
		api.ValidateRefundOrder(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})
}

type expectedTransactionInfo struct {
	OrderID  string
	TripID   string
	RefID    string
	Amount   types.Money
	Category domainModel.TransactionCategory
	Type     domainModel.TransactionType
	SubType  domainModel.TransactionSubType
}

type expectedTransactionItem struct {
	OrderID  string
	TripID   string
	RefID    string
	Amount   types.Money
	Category domainModel.TransactionCategory
	Type     domainModel.TransactionType
	SubType  domainModel.TransactionSubType
	Remark   string
}

func mapActualTransactionInfos(trans []domainModel.TransactionInfo) []expectedTransactionInfo {
	actualTrans := make([]expectedTransactionInfo, 0)
	for _, tran := range trans {
		actualTrans = append(actualTrans, expectedTransactionInfo{
			OrderID:  tran.OrderID,
			TripID:   tran.TripID,
			RefID:    tran.RefID,
			Amount:   tran.Amount,
			Category: tran.Category,
			Type:     tran.Type,
			SubType:  tran.SubType,
		})
	}
	return actualTrans
}

func mapActualTransactionItems(trans []domainModel.GroupTransactionItem) []expectedTransactionItem {
	actualTrans := make([]expectedTransactionItem, 0)
	for _, tran := range trans {
		actualTrans = append(actualTrans, expectedTransactionItem{
			OrderID:  tran.OrderID(),
			TripID:   tran.TripID(),
			RefID:    tran.RefID(),
			Amount:   tran.Amount(),
			Category: tran.Category(),
			Type:     tran.Type(),
			SubType:  tran.SubType(),
			Remark:   tran.Remark(),
		})
	}
	return actualTrans
}

func TestGetChargeBackAmount(t *testing.T) {
	type testcase struct {
		itemFeeSubTotal  types.Money
		itemFeeTotal     types.Money
		refundedAmount   types.Money
		itemRefundAmount types.Money

		expectedChargeItemFeeAmount    types.Money
		expectedChargeCouponAmount     types.Money
		expectedAdditionalChargeAmount types.Money
	}
	testcases := []testcase{
		{
			itemFeeSubTotal: 0,
			itemFeeTotal:    0,

			refundedAmount:                 0,
			itemRefundAmount:               10,
			expectedChargeItemFeeAmount:    0,
			expectedChargeCouponAmount:     0,
			expectedAdditionalChargeAmount: 10,
		},
		{
			itemFeeSubTotal: 0,
			itemFeeTotal:    0,

			refundedAmount:                 20,
			itemRefundAmount:               10,
			expectedChargeItemFeeAmount:    0,
			expectedChargeCouponAmount:     0,
			expectedAdditionalChargeAmount: 10,
		},

		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 0,
			itemRefundAmount:               10,
			expectedChargeItemFeeAmount:    10,
			expectedChargeCouponAmount:     0,
			expectedAdditionalChargeAmount: 0,
		},
		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 0,
			itemRefundAmount:               50,
			expectedChargeItemFeeAmount:    50,
			expectedChargeCouponAmount:     0,
			expectedAdditionalChargeAmount: 0,
		},
		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 0,
			itemRefundAmount:               60,
			expectedChargeItemFeeAmount:    50,
			expectedChargeCouponAmount:     10,
			expectedAdditionalChargeAmount: 0,
		},
		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 0,
			itemRefundAmount:               100,
			expectedChargeItemFeeAmount:    50,
			expectedChargeCouponAmount:     50,
			expectedAdditionalChargeAmount: 0,
		},
		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 0,
			itemRefundAmount:               130,
			expectedChargeItemFeeAmount:    50,
			expectedChargeCouponAmount:     50,
			expectedAdditionalChargeAmount: 30,
		},

		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 30,
			itemRefundAmount:               70,
			expectedChargeItemFeeAmount:    20,
			expectedChargeCouponAmount:     50,
			expectedAdditionalChargeAmount: 0,
		},
		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 30,
			itemRefundAmount:               100,
			expectedChargeItemFeeAmount:    20,
			expectedChargeCouponAmount:     50,
			expectedAdditionalChargeAmount: 30,
		},

		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 50,
			itemRefundAmount:               30,
			expectedChargeItemFeeAmount:    0,
			expectedChargeCouponAmount:     30,
			expectedAdditionalChargeAmount: 0,
		},
		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 50,
			itemRefundAmount:               50,
			expectedChargeItemFeeAmount:    0,
			expectedChargeCouponAmount:     50,
			expectedAdditionalChargeAmount: 0,
		},
		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 50,
			itemRefundAmount:               80,
			expectedChargeItemFeeAmount:    0,
			expectedChargeCouponAmount:     50,
			expectedAdditionalChargeAmount: 30,
		},

		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 100,
			itemRefundAmount:               30,
			expectedChargeItemFeeAmount:    0,
			expectedChargeCouponAmount:     0,
			expectedAdditionalChargeAmount: 30,
		},
		{
			itemFeeSubTotal: 100,
			itemFeeTotal:    50,

			refundedAmount:                 130,
			itemRefundAmount:               30,
			expectedChargeItemFeeAmount:    0,
			expectedChargeCouponAmount:     0,
			expectedAdditionalChargeAmount: 30,
		},
	}
	for i, tc := range testcases {
		t.Run(fmt.Sprintf("testcase #%v", i+1), func(tt *testing.T) {
			chargeItemFeeAmount, chargeCouponAmount, additionalChargeAmount := getChargeBackAmount(
				tc.itemFeeTotal,
				tc.itemFeeSubTotal,
				tc.refundedAmount,
				tc.itemRefundAmount,
			)
			require.Equal(tt, tc.expectedChargeItemFeeAmount, chargeItemFeeAmount)
			require.Equal(tt, tc.expectedChargeCouponAmount, chargeCouponAmount)
			require.Equal(tt, tc.expectedAdditionalChargeAmount, additionalChargeAmount)
		})
	}
}

func init() {
	binding.Validator = apiValidator.NewDefaultValidator()
}
