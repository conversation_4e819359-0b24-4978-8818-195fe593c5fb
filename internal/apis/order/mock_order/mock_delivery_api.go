// Code generated by MockGen. DO NOT EDIT.
// Source: ./delivery_api.go

// Package mock_order is a generated GoMock package.
package mock_order

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockCanceller is a mock of Canceller interface.
type MockCanceller struct {
	ctrl     *gomock.Controller
	recorder *MockCancellerMockRecorder
}

// MockCancellerMockRecorder is the mock recorder for MockCanceller.
type MockCancellerMockRecorder struct {
	mock *MockCanceller
}

// NewMockCanceller creates a new mock instance.
func NewMockCanceller(ctrl *gomock.Controller) *MockCanceller {
	mock := &MockCanceller{ctrl: ctrl}
	mock.recorder = &MockCancellerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCanceller) EXPECT() *MockCancellerMockRecorder {
	return m.recorder
}

// CancelOrder mocks base method.
func (m *MockCanceller) CancelOrder(ctx context.Context, orderID string, detail model.CancelDetail, statusValidator model.StatusValidator, serviceTypeValidator model.ServiceTypeValidator, hasCancelRateChanged *bool, isAllowToCancelSwitchFlow bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CancelOrder", ctx, orderID, detail, statusValidator, serviceTypeValidator, hasCancelRateChanged, isAllowToCancelSwitchFlow)
	ret0, _ := ret[0].(error)
	return ret0
}

// CancelOrder indicates an expected call of CancelOrder.
func (mr *MockCancellerMockRecorder) CancelOrder(ctx, orderID, detail, statusValidator, serviceTypeValidator, hasCancelRateChanged, isAllowToCancelSwitchFlow interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CancelOrder", reflect.TypeOf((*MockCanceller)(nil).CancelOrder), ctx, orderID, detail, statusValidator, serviceTypeValidator, hasCancelRateChanged, isAllowToCancelSwitchFlow)
}
