package order

import (
	"mime/multipart"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/csvutils"
)

type ListOrdersRequest struct {
	DriverID              string                      `form:"driverId"`
	OrderID               string                      `form:"orderId"`
	FraudStatus           string                      `form:"fraudStatus"`
	RestaurantID          string                      `form:"restaurantId"`
	BeginAt               time.Time                   `form:"beginAt"`
	EndAt                 time.Time                   `form:"endAt"`
	DeliveringPhotoStatus model.DeliveringPhotoStatus `form:"deliveringPhotoStatus"`
}

func (r *ListOrdersRequest) Query() map[string]interface{} {
	q := bson.M{}

	if r.DriverID != "" {
		q["driver"] = r.DriverID
	}
	if r.OrderID != "" {
		q["order_id"] = r.OrderID
	}
	if r.FraudStatus != "" {
		q["fraud_status"] = r.FraudStatus
	}
	if r.RestaurantID != "" {
		q["routes.0.id"] = r.RestaurantID
	}
	if !r.BeginAt.IsZero() || !r.EndAt.IsZero() {
		createAtQuery := bson.M{}
		if !r.BeginAt.IsZero() {
			createAtQuery["$gt"] = r.BeginAt
		}
		if !r.EndAt.IsZero() {
			createAtQuery["$lt"] = r.EndAt
		}
		q["created_at"] = createAtQuery
	}
	if r.DeliveringPhotoStatus != "" {
		if r.DeliveringPhotoStatus == "n/a" {
			q["delivering_photo_status"] = bson.M{"$exists": false}
		} else {
			q["delivering_photo_status"] = r.DeliveringPhotoStatus
		}
	}

	return q
}

type ReviewOrderPhotoRequest struct {
	DriverID string `form:"driverId"`
	OrderID  string `form:"orderId"`
}

func (r *ReviewOrderPhotoRequest) ReviewOrderQuery() map[string]interface{} {
	q := bson.M{
		"verified_rider_photo_urls.0": bson.M{"$exists": true},
	}
	if r.DriverID != "" {
		q["driver"] = r.DriverID
	}
	if r.OrderID != "" {
		q["order_id"] = r.OrderID
	}
	return q
}

type InternalCancelReasonRequest struct {
	Name                 string                     `json:"name" binding:"required"`
	Label                string                     `json:"label" binding:"required"`
	CompensationReason   string                     `json:"compensationReason"`
	BanDurationInMinute  int                        `json:"banDurationInMinute"`
	CancellationRateFree bool                       `json:"cancellationRateFree"`
	ShouldAutoClaim      bool                       `json:"shouldAutoClaim"`
	IsReassign           bool                       `json:"isReassign"`
	CancellationSource   string                     `json:"cancellationSource"`
	Message              model.CancelDisplayMessage `json:"message"`
	WMAMetric            string                     `json:"wmaMetric"`
	UseCase              string                     `json:"useCase" binding:"omitempty,internalCancelReasonUseCasePossibleValue"`
}

func (req *InternalCancelReasonRequest) Create() model.InternalCancelReason {
	return model.InternalCancelReason{
		ID:                   model.GenerateInternalCancelReasonId(),
		Name:                 req.Name,
		Label:                req.Label,
		BanDurationInMinute:  req.BanDurationInMinute,
		CompensationReason:   req.CompensationReason,
		CancellationRateFree: req.CancellationRateFree,
		ShouldAutoClaim:      req.ShouldAutoClaim,
		IsReassign:           req.IsReassign,
		CancellationSource:   req.CancellationSource,
		Message:              req.Message,
		WMAMetric:            req.WMAMetric,
		UseCase:              model.InternalCancelReasonUseCase(req.UseCase),
	}
}

func (req *InternalCancelReasonRequest) Update(internalCancelReason *model.InternalCancelReason) {
	internalCancelReason.Name = req.Name
	internalCancelReason.Label = req.Label
	internalCancelReason.BanDurationInMinute = req.BanDurationInMinute
	internalCancelReason.CompensationReason = req.CompensationReason
	internalCancelReason.CancellationRateFree = req.CancellationRateFree
	internalCancelReason.ShouldAutoClaim = req.ShouldAutoClaim
	internalCancelReason.IsReassign = req.IsReassign
	internalCancelReason.CancellationSource = req.CancellationSource
	internalCancelReason.Message = req.Message
	internalCancelReason.WMAMetric = req.WMAMetric
	internalCancelReason.UseCase = model.InternalCancelReasonUseCase(req.UseCase)
}

type CSBulkCancelOrderRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

func (r *CSBulkCancelOrderRequest) CancelRequests(requester string, maxRow int) (map[string]CSCancelOrderRequest, error) {
	file, err := r.File.Open()
	if err != nil {
		return nil, err
	}
	defer file.Close()

	bulkInfo := make(map[string]CSCancelOrderRequest)

	decoder, err := csvutils.NewCSVDecoder(file, csvutils.WithMaxRow(maxRow), csvutils.WithHeader())
	if err != nil {
		return nil, err
	}

	for decoder.Next() {
		var orderID string
		var request CSCancelOrderRequest
		if err := decoder.Decode(&orderID, &request.ID, &request.Remark, &request.ForceCancellationRateFree); err != nil {
			return nil, err
		}
		request.Requestor = requester
		bulkInfo[orderID] = request
	}

	return bulkInfo, nil
}

type UpdateOrderCancelReasonRequest struct {
	CSTicketID          string `json:"csTicketID"`
	CancellationSource  string `json:"cancellationSource"`
	Requestor           string `json:"requestor"`
	CancelReasonID      string `json:"cancelReasonID"`
	CancelReasonComment string `json:"cancelReasonComment"`
	CancelledBy         string `json:"cancelledBy"`
}
