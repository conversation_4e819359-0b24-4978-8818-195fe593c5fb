package order

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	formsubmitv1 "git.wndv.co/go/proto/lineman/form_service/v1"
	absintheAPI "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/mock_order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment/mock_payment"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery/mock_delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep/mock_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/mock_event_bus"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/bulkutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestAdminAPI_CSCancelOrder(t *testing.T) {
	t.Run("should cancel order with specific order ID", func(tt *testing.T) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/admin/order/order-A/cs-cancel", testutil.JSON(CSCancelOrderRequest{
			ID:        "id-restaurant-is-close",
			Requestor: "admin-user",
			Remark:    "cancellation-remark",
		}))

		ctx.Params = gin.Params{{Key: "orderID", Value: "order-A"}}

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		internalCancelReasonID := model.GenerateInternalCancelReasonId()

		api.bcpOrdersRepo.EXPECT().
			Find(gomock.Any(), gomock.Any()).
			Return([]model.BCPOrder{}, nil)

		api.internalCancalReasonRepository.EXPECT().
			FindById(gomock.Any(), "id-restaurant-is-close", gomock.Any()).
			Return(&model.InternalCancelReason{
				ID:                 internalCancelReasonID,
				CompensationReason: FoodCancelNoCompensation,
				ShouldAutoClaim:    true,
				CancellationSource: model.SourceDriver,
			}, nil)

		api.canceller.EXPECT().CancelOrder(gomock.Any(), "order-A", model.CancelDetail{
			Reason:                    FoodCancelNoCompensation,
			Remark:                    "cancellation-remark",
			CancelledBy:               "CS",
			Source:                    model.SourceDriver,
			Requestor:                 "admin-user",
			Label:                     "",
			BanDurationInMinute:       0,
			CancelledWithQuota:        false,
			CancellationRateFree:      false,
			ShouldAutoClaim:           true,
			IsReassign:                false,
			ForceCancellationRateFree: false,
			CancellationSource:        model.SourceDriver,
			CancelReasonIDForFood:     string(internalCancelReasonID),
		}, gomock.Any(), gomock.Any(), nil, true)

		api.admapi.CSCancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("should use bcp cancel reason as remark", func(t *testing.T) {
		expectedReason := "reasonBcp"

		ctx, recorder := testutil.TestRequestContext("POST", "/v1/admin/order/order-A/cs-cancel", testutil.JSON(CSCancelOrderRequest{
			ID:        "id-restaurant-is-close",
			Requestor: "admin-user",
			Remark:    "cancellation-remark",
		}))

		orderID := "order-A"
		ctx.Params = gin.Params{{Key: "orderID", Value: orderID}}

		api, done := newTestAdminAPI(t, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		internalCancelReasonID := model.GenerateInternalCancelReasonId()

		api.bcpOrdersRepo.EXPECT().
			Find(gomock.Any(), gomock.Any()).
			Return([]model.BCPOrder{
				{
					Status:       model.BCPOrderStatusCanceled,
					CancelReason: expectedReason,
				},
			}, nil)

		api.internalCancalReasonRepository.EXPECT().
			FindById(gomock.Any(), "id-restaurant-is-close", gomock.Any()).
			Return(&model.InternalCancelReason{
				ID:                 internalCancelReasonID,
				CompensationReason: FoodCancelNoCompensation,
				ShouldAutoClaim:    true,
				CancellationSource: model.SourceDriver,
			}, nil)

		api.canceller.EXPECT().CancelOrder(gomock.Any(), orderID, model.CancelDetail{
			Reason:                    FoodCancelNoCompensation,
			Remark:                    expectedReason,
			CancelledBy:               "CS",
			Source:                    model.SourceDriver,
			Requestor:                 "admin-user",
			Label:                     "",
			BanDurationInMinute:       0,
			CancelledWithQuota:        false,
			CancellationRateFree:      false,
			ShouldAutoClaim:           true,
			IsReassign:                false,
			ForceCancellationRateFree: false,
			CancellationSource:        model.SourceDriver,
			CancelReasonIDForFood:     string(internalCancelReasonID),
		}, gomock.Any(), gomock.Any(), nil, true)

		api.admapi.CSCancelOrder(ctx)

		require.Equal(t, http.StatusOK, recorder.Code, recorder.Body.String())
	})
}

func TestAdminAPI_CSBulkCancelOrder(t *testing.T) {
	t.Run("should return success when everything is work properly", func(tt *testing.T) {
		content := "order-A,icr-ID,remark-A,false\norder-B,icr-ID,remark-B,true"

		req := testutil.NewContextWithRecorder()
		req.FakeAdminCtxAuthorized("fake-id", "fake-token", "<EMAIL>")
		req.SetPOST("/v1/admin/bulk/orders/cs-cancel")
		req.Body().MultipartForm().
			File("file", "file.csv", content).
			String("requester", "admin-user").Build()

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		internalCancelReasonID := model.GenerateInternalCancelReasonId()

		api.internalCancalReasonRepository.EXPECT().
			FindByIDs(gomock.Any(), types.NewStringSet("icr-ID"), gomock.Any()).
			Return(map[string]model.InternalCancelReason{"icr-ID": {
				ID:                 internalCancelReasonID,
				CompensationReason: FoodCancelNoCompensation,
				ShouldAutoClaim:    true,
				CancellationSource: model.SourceDriver,
			}}, nil)

		api.canceller.EXPECT().CancelOrder(gomock.Any(), "order-A", model.CancelDetail{
			Reason:                    FoodCancelNoCompensation,
			Remark:                    "remark-A",
			CancelledBy:               "CS",
			Source:                    model.SourceDriver,
			Requestor:                 "<EMAIL>",
			Label:                     "",
			BanDurationInMinute:       0,
			CancelledWithQuota:        false,
			CancellationRateFree:      false,
			ShouldAutoClaim:           true,
			IsReassign:                false,
			ForceCancellationRateFree: false,
			CancellationSource:        model.SourceDriver,
			CancelReasonIDForFood:     string(internalCancelReasonID),
		}, gomock.Any(), gomock.Any(), nil, true)

		api.canceller.EXPECT().CancelOrder(gomock.Any(), "order-B", model.CancelDetail{
			Reason:                    FoodCancelNoCompensation,
			Remark:                    "remark-B",
			CancelledBy:               "CS",
			Source:                    model.SourceDriver,
			Requestor:                 "<EMAIL>",
			Label:                     "",
			BanDurationInMinute:       0,
			CancelledWithQuota:        false,
			CancellationRateFree:      false,
			ShouldAutoClaim:           true,
			IsReassign:                false,
			ForceCancellationRateFree: true,
			CancellationSource:        model.SourceDriver,
			CancelReasonIDForFood:     string(internalCancelReasonID),
		}, gomock.Any(), gomock.Any(), nil, true)

		api.admapi.CSBulkCancelOrder(req.GinCtx())

		require.Equal(tt, http.StatusOK, req.ResponseRecorder.Code, req.ResponseRecorder.Body.String())

		var res bulkutil.BulkResp
		testutil.DecodeJSON(tt, req.ResponseRecorder.Body, &res)
		require.Len(tt, res.Fail, 0)
		require.Len(tt, res.Success, 2)
		require.Contains(tt, res.Success, bulkutil.BulkRespInfo{ID: "order-A"})
		require.Contains(tt, res.Success, bulkutil.BulkRespInfo{ID: "order-B"})
	})

	t.Run("should return failed when cannot cancel order", func(tt *testing.T) {
		content := "order-A,icr-ID,remark-A,true"

		req := testutil.NewContextWithRecorder()
		req.FakeAdminCtxAuthorized("fake-id", "fake-token", "<EMAIL>")
		req.SetPOST("/v1/admin/bulk/orders/cs-cancel")
		req.Body().MultipartForm().
			File("file", "file.csv", content).Build()

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.internalCancalReasonRepository.EXPECT().
			FindByIDs(gomock.Any(), types.NewStringSet("icr-ID"), gomock.Any()).
			Return(map[string]model.InternalCancelReason{"icr-ID": {
				CompensationReason: FoodCancelNoCompensation,
				ShouldAutoClaim:    true,
				CancellationSource: model.SourceDriver,
			}}, nil)

		api.canceller.EXPECT().CancelOrder(gomock.Any(), "order-A", model.CancelDetail{
			Reason:                    FoodCancelNoCompensation,
			Remark:                    "remark-A",
			CancelledBy:               "CS",
			Source:                    model.SourceDriver,
			Requestor:                 "<EMAIL>",
			Label:                     "",
			BanDurationInMinute:       0,
			CancelledWithQuota:        false,
			CancellationRateFree:      false,
			ShouldAutoClaim:           true,
			IsReassign:                false,
			ForceCancellationRateFree: true,
			CancellationSource:        model.SourceDriver,
		}, gomock.Any(), gomock.Any(), nil, true).Return(errors.New("cannot cancel order"))

		api.admapi.CSBulkCancelOrder(req.GinCtx())

		require.Equal(tt, http.StatusOK, req.ResponseRecorder.Code, req.ResponseRecorder.Body.String())

		var res bulkutil.BulkResp
		testutil.DecodeJSON(tt, req.ResponseRecorder.Body, &res)
		require.Len(tt, res.Fail, 1)
		require.Len(tt, res.Success, 0)
		require.Contains(tt, res.Fail, bulkutil.BulkRespInfo{ID: "order-A", Message: "cancel order error, err=cannot cancel order"})
	})
}

func TestAdminAPI_ForceReassign(t *testing.T) {
	t.Run("should set flag cancellation free & force cancellation free for the cancellation", func(tt *testing.T) {
		req := testutil.NewContextWithRecorder()
		req.FakeAdminCtxAuthorized("fake-id", "fake-token", "<EMAIL>")
		req.SetPOST("/v1/admin/order/order-A/force-reassign")
		req.SetGinParams(gin.Params{{Key: "orderID", Value: "order-A"}})

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.internalCancalReasonRepository.EXPECT().
			FindByLabel(gomock.Any(), "LM System Error / Failure").
			Return(&model.InternalCancelReason{
				Label:                "LM System Error / Failure",
				BanDurationInMinute:  0,
				CancellationRateFree: true,
				ShouldAutoClaim:      false,
				IsReassign:           true,
			}, nil)

		api.canceller.EXPECT().CancelOrder(gomock.Any(), "order-A", model.CancelDetail{
			CancelledBy:               "CS",
			Source:                    model.SourceDriver,
			Reason:                    "LM System Error / Failure",
			Remark:                    fmt.Sprintf("ยกเลิกโดย <EMAIL> เพื่อทำการ Reassign ใหม่ เนื่องจากระบบมีปัญหา"),
			Requestor:                 "<EMAIL>",
			BanDurationInMinute:       0,
			Label:                     "LM System Error / Failure",
			CancellationRateFree:      true,
			ShouldAutoClaim:           false,
			IsReassign:                true,
			ForceCancellationRateFree: true,
		}, gomock.Any(), gomock.Any(), nil, false)

		api.admapi.ForceReassign(req.GinCtx())

		require.Equal(tt, http.StatusOK, req.ResponseRecorder.Code, req.ResponseRecorder.Body.String())
	})

	t.Run("should return error when cannot cancel the order", func(tt *testing.T) {
		req := testutil.NewContextWithRecorder()
		req.FakeAdminCtxAuthorized("fake-id", "fake-token", "<EMAIL>")
		req.SetPOST("/v1/admin/order/order-A/force-reassign")
		req.SetGinParams(gin.Params{{Key: "orderID", Value: "order-A"}})

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.internalCancalReasonRepository.EXPECT().
			FindByLabel(gomock.Any(), "LM System Error / Failure").
			Return(&model.InternalCancelReason{
				Label:                "LM System Error / Failure",
				BanDurationInMinute:  0,
				CancellationRateFree: true,
				ShouldAutoClaim:      false,
				IsReassign:           true,
			}, nil)

		api.canceller.EXPECT().CancelOrder(gomock.Any(), "order-A", model.CancelDetail{
			CancelledBy:               "CS",
			Source:                    model.SourceDriver,
			Reason:                    "LM System Error / Failure",
			Remark:                    fmt.Sprintf("ยกเลิกโดย <EMAIL> เพื่อทำการ Reassign ใหม่ เนื่องจากระบบมีปัญหา"),
			Requestor:                 "<EMAIL>",
			BanDurationInMinute:       0,
			Label:                     "LM System Error / Failure",
			CancellationRateFree:      true,
			ShouldAutoClaim:           false,
			IsReassign:                true,
			ForceCancellationRateFree: true,
		}, gomock.Any(), gomock.Any(), nil, false).Return(errors.New("some error"))

		api.admapi.ForceReassign(req.GinCtx())

		require.Len(tt, req.GinCtx().Errors, 1)
		require.Error(tt, req.GinCtx().Errors[0])
	})
}

func setOrderStatus(o model.Order, s model.Status) *model.Order {
	o.Status = s
	return &o
}

func TestAdminAPI_CanReassign(t *testing.T) {
	// the order must compat with reassign criteria
	stop := model.StopInfoCollector{
		StopInfo: &model.StopInfoFood{
			PriceScheme: model.PriceSchemeRMS,
		},
	}

	quote, err := model.NewQuote(
		"quote-A",
		"user-A",
		model.ServiceFood,
		[]model.Stop{{Info: stop}, {}},
		string(model.PriceSchemeRMS),
		false,
		model.NewDeliveryFee(70.0, 30.0, 1000),
		model.NewDeliveryFee(70.0, 30.0, 1000),
		[]model.RegionCode{"AYUTTHAYA"},
		0,
	)
	require.NoError(t, err)

	// create an order from quote
	order := model.NewOrder(*quote, "order-A")
	order.ExpireAt = time.Now().Add(time.Hour)
	order.Driver = "driver-A"

	t.Run("should return true when the order can be reassigned", func(tt *testing.T) {
		req := testutil.NewContextWithRecorder()
		req.FakeAdminCtxAuthorized("fake-id", "fake-token", "<EMAIL>")
		req.SetPOST("/v1/admin/order/order-A/can-reassign")
		req.SetGinParams(gin.Params{{Key: "orderID", Value: "order-A"}})

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.internalCancalReasonRepository.EXPECT().
			FindByLabel(gomock.Any(), "LM System Error / Failure").
			Return(&model.InternalCancelReason{
				Label:                "LM System Error / Failure",
				BanDurationInMinute:  0,
				CancellationRateFree: true,
				ShouldAutoClaim:      false,
				IsReassign:           true,
			}, nil)

		// override the order with specific status
		order := setOrderStatus(*order, model.StatusRestaurantAccepted)
		api.repo.EXPECT().
			Get(gomock.Any(), "order-A").
			Return(order, nil)

		api.admapi.CanReassign(req.GinCtx())

		require.Equal(tt, http.StatusOK, req.ResponseRecorder.Code)

		var resp gin.H
		testutil.DecodeJSON(tt, req.ResponseRecorder.Body, &resp)
		require.True(tt, resp["canReassign"].(bool))
	})

	t.Run("should return false when the order can not be reassigned", func(tt *testing.T) {
		req := testutil.NewContextWithRecorder()
		req.FakeAdminCtxAuthorized("fake-id", "fake-token", "<EMAIL>")
		req.SetPOST("/v1/admin/order/order-A/can-reassign")
		req.SetGinParams(gin.Params{{Key: "orderID", Value: "order-A"}})

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.internalCancalReasonRepository.EXPECT().
			FindByLabel(gomock.Any(), "LM System Error / Failure").
			Return(&model.InternalCancelReason{
				Label:                "LM System Error / Failure",
				BanDurationInMinute:  0,
				CancellationRateFree: true,
				ShouldAutoClaim:      false,
				IsReassign:           true,
			}, nil)

		// override the order with specific status
		order := setOrderStatus(*order, model.StatusCompleted)
		api.repo.EXPECT().
			Get(gomock.Any(), "order-A").
			Return(order, nil)

		api.admapi.CanReassign(req.GinCtx())

		require.Equal(tt, http.StatusOK, req.ResponseRecorder.Code)

		var resp gin.H
		testutil.DecodeJSON(tt, req.ResponseRecorder.Body, &resp)
		require.False(tt, resp["canReassign"].(bool))
	})
}

func TestAdminAPI_RMSCancelOrder(t *testing.T) {
	t.Run("should cancel order with specific order ID", func(tt *testing.T) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/admin/order/order-A/cancel", testutil.JSON(CSCancelOrderRequest{
			ID:        "id-restaurant-is-close",
			Requestor: "admin-user",
			Remark:    "cancellation-remark",
		}))

		ctx.Params = gin.Params{{Key: "orderID", Value: "order-A"}}

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.canceller.EXPECT().CancelOrder(gomock.Any(), "order-A", model.CancelDetail{
			CancelledBy:        "CS",
			Source:             model.SourceRMS,
			CancellationSource: model.SourceRMS,
		}, gomock.Any(), gomock.Any(), nil, false)

		api.admapi.RMSCancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})
}

type testAdminAPI struct {
	admapi *AdminAPI

	repo                           *mock_repository.MockOrderRepository
	assigner                       *mock_service.MockAssigner
	aslog                          *mock_repository.MockAssignmentLogRepository
	notifier                       *mock_service.MockNotifier
	driverRepository               *mock_repository.MockDriverRepository
	driverSvc                      *mock_service.MockDriverServiceInterface
	bansvc                         *mock_service.MockBanService
	delivery                       *mock_delivery.MockDelivery
	repSvc                         *mock_rep.MockREPService
	bus                            *mock_event_bus.MockEventBus
	driverTransactionService       *mock_payment.MockDriverTransactionService
	config                         *OrderAPIConfig
	tranRepo                       *mock_repository.MockTransactionRepository
	internalCancalReasonRepository *mock_repository.MockInternalCancelReasonRepository
	auditLogRepository             *mock_repository.MockAuditLogRepository
	shiftService                   *mock_service.MockShiftServices
	distributor                    *mock_order.MockOrderDistributor
	txnHelper                      *mock_transaction.MockTxnHelper
	canceller                      *mock_order.MockCanceller
	tripSvc                        *mock_service.MockTripServices
	tripRepo                       *mock_repository.MockTripRepository
	cancelReasonConfig             *service.CancelReasonCfg
	onTopFareService               *mock_service.MockOnTopFareService
	deliveryFeeService             *mock_service.MockDeliveryFeeService
	bcpOrdersRepo                  *mock_repository.MockBCPOrderRepository
}

func newTestAdminAPI(t *testing.T, config OrderAPIConfig, cancelReasonConfig *service.AtomicCancelReasonConfig) (*testAdminAPI, func()) {
	if config.AtomicOrderDBConfig == nil {
		config.AtomicOrderDBConfig = NewAtomicOrderDBConfig(OrderDBConfig{})
	}

	if cancelReasonConfig == nil {
		cancelReasonConfig = service.NewAtomicCancelReasonConfig(service.CancelReasonCfg{})
	}

	ctrl := gomock.NewController(t)
	repo := mock_repository.NewMockOrderRepository(ctrl)
	tripRepo := mock_repository.NewMockTripRepository(ctrl)
	tripServices := mock_service.NewMockTripServices(ctrl)
	delivery := mock_delivery.NewMockDelivery(ctrl)
	assigner := mock_service.NewMockAssigner(ctrl)
	aslog := mock_repository.NewMockAssignmentLogRepository(ctrl)
	notifier := mock_service.NewMockNotifier(ctrl)
	driverRepository := mock_repository.NewMockDriverRepository(ctrl)
	bansvc := mock_service.NewMockBanService(ctrl)
	driverSvc := mock_service.NewMockDriverServiceInterface(ctrl)
	repSvc := mock_rep.NewMockREPService(ctrl)
	bus := mock_event_bus.NewMockEventBus(gomock.NewController(t))
	driverTransactionService := mock_payment.NewMockDriverTransactionService(gomock.NewController(t))
	deliveryFeeService := mock_service.NewMockDeliveryFeeService(ctrl)
	transRepo := mock_repository.NewMockTransactionRepository(ctrl)
	internalCancelReasonRepository := mock_repository.NewMockInternalCancelReasonRepository(ctrl)
	auditLogRepository := mock_repository.NewMockAuditLogRepository(ctrl)
	shiftServices := mock_service.NewMockShiftServices(ctrl)
	distributor := mock_order.NewMockOrderDistributor(ctrl)
	txnHelper := mock_transaction.NewMockTxnHelper(ctrl)
	canceller := mock_order.NewMockCanceller(ctrl)
	onTopFareService := mock_service.NewMockOnTopFareService(ctrl)
	bcpOrderRepo := mock_repository.NewMockBCPOrderRepository(ctrl)

	admapi := NewAdminAPI(
		repo,
		tripRepo,
		tripServices,
		driverRepository,
		driverTransactionService,
		config,
		deliveryFeeService,
		transRepo,
		internalCancelReasonRepository,
		auditLogRepository,
		canceller,
		cancelReasonConfig,
		onTopFareService,
		bcpOrderRepo,
	)

	adminApi := &testAdminAPI{
		admapi:                         admapi,
		repo:                           repo,
		assigner:                       assigner,
		aslog:                          aslog,
		notifier:                       notifier,
		driverRepository:               driverRepository,
		bansvc:                         bansvc,
		driverSvc:                      driverSvc,
		delivery:                       delivery,
		repSvc:                         repSvc,
		bus:                            bus,
		driverTransactionService:       driverTransactionService,
		config:                         &config,
		tranRepo:                       transRepo,
		internalCancalReasonRepository: internalCancelReasonRepository,
		auditLogRepository:             auditLogRepository,
		shiftService:                   shiftServices,
		distributor:                    distributor,
		txnHelper:                      txnHelper,
		canceller:                      canceller,
		tripSvc:                        tripServices,
		tripRepo:                       tripRepo,
		cancelReasonConfig:             &cancelReasonConfig.Config,
		onTopFareService:               onTopFareService,
		deliveryFeeService:             deliveryFeeService,
		bcpOrdersRepo:                  bcpOrderRepo,
	}

	timeutils.Freeze()
	return adminApi, func() {
		timeutils.Unfreeze()
		ctrl.Finish()
	}
}

func TestGetOrderDetail(t *testing.T) {
	const (
		orderID  = "LMF-123456"
		driverID = "<driver_id>"
		rqMail   = "<EMAIL>"
	)

	createOrder := func(driverID string) *model.Order {
		nq, err := model.NewQuote(
			"quote_1",
			"user-1",
			model.ServiceFood,
			[]model.Stop{{Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{PriceScheme: model.PriceSchemeRMS}}}, {}},
			string(model.PriceSchemeRMS),
			false,
			model.NewDeliveryFee(70.0, 30.0, 1000),
			model.NewDeliveryFee(70.0, 30.0, 1000),
			[]model.RegionCode{"AYUTTHAYA"},
			0,
		)
		require.NoError(t, err)
		ord := model.NewOrder(*nq, orderID)

		ord.ExpireAt = time.Now().Add(time.Hour)

		ord.CancelDetail.Requestor = rqMail

		if driverID != "" {
			ord.Driver = driverID
		}
		return ord
	}
	req := func(q string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("GET", "/v1/admin/order/"+orderID+"/requestor", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("success with email in response", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		gctx, recorder := req(orderID)
		od := createOrder(driverID)

		api.repo.EXPECT().
			Get(gomock.Any(), orderID).
			Return(od, nil)

		api.admapi.GetOrderRequestor(gctx)

		actual := struct {
			Requestor string `json:"requestor"`
		}{}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, rqMail, actual.Requestor)
	})
}

func TestAdminAPI_BulkOrderFraudStatus(t *testing.T) {
	createOrder := func(orderId string, fraudStatus model.FraudStatus, orderStatus model.Status) *model.Order {
		nq, err := model.NewQuote(
			"quote_1",
			"user-1",
			model.ServiceFood,
			[]model.Stop{{Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{PriceScheme: model.PriceSchemeRMS}}}, {}},
			string(model.PriceSchemeRMS),
			false,
			model.NewDeliveryFee(70.0, 30.0, 1000),
			model.NewDeliveryFee(70.0, 30.0, 1000),
			[]model.RegionCode{"AYUTTHAYA"},
			0,
		)
		require.NoError(t, err)
		ord := model.NewOrder(*nq, orderId)

		ord.ExpireAt = time.Now().Add(time.Hour)
		ord.FraudStatus = fraudStatus
		ord.Status = orderStatus
		return ord
	}

	requestedByName := "chika"

	makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/bulk/orders/fraud_status")
		gctx.Body().MultipartForm().File("file", "file.csv", content).
			String("requestedBy", requestedByName).Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	expectAddWageTransaction := func(api *testAdminAPI) {
		api.driverTransactionService.EXPECT().AddDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.DriverTransaction{}, nil, nil)
	}

	expectGetToVoidTransaction := func(api *testAdminAPI, dt *model.DriverTransaction, tripWage []model.Transaction, tripId string, orderId string) {
		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver1", gomock.Any()).Return(*dt, nil)
		api.driverTransactionService.EXPECT().GetWageTransactionFromTrip(gomock.Any(), tripId).Return(tripWage, nil)
		api.driverTransactionService.EXPECT().GetTransactionByOrderID(gomock.Any(), orderId).Return([]model.Transaction{}, nil)
	}

	expectRecalculateTripWage := func(api *testAdminAPI) {
		api.tripSvc.EXPECT().CalculateTripRoutes(gomock.Any(), gomock.Any()).Return([]model.MapRoute{}, nil)
		api.tripSvc.EXPECT().RecalculateTripDeliveryFee(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
	}

	bulkThenAssert := func(api *testAdminAPI, tt *testing.T, gctx *gin.Context, recorder *httptest.ResponseRecorder, success int, fail int) {
		api.admapi.BulkOrderFraudStatus(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res BulkUpdateFraudStatusResp
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Len(tt, res.Successes, success)
		require.Len(tt, res.Failures, fail)
	}

	t.Run("api disabled", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: false}, &service.AtomicCancelReasonConfig{})
		defer finish()
		gctx, recorder := makeReq("content")

		api.admapi.BulkOrderFraudStatus(gctx)
		require.Equal(tt, http.StatusForbidden, recorder.Code)
	})

	t.Run("all success", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		content := `OrderId,Status
test-1,FRAUD
test-2,NORMAL
`
		o1 := createOrder("test-1", model.FraudStatusNormal, model.StatusCompleted)
		o2 := createOrder("test-2", model.FraudStatusFraud, model.StatusCompleted)
		gctx, recorder := makeReq(content)

		api.repo.EXPECT().
			Get(gomock.Any(), "test-1").
			Return(o1, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), "test-2").
			Return(o2, nil)

		transaction := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"
		transaction2 := model.NewDriverTransaction("driver2")
		o2.Driver = "driver2"
		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver1", gomock.Any()).Return(*transaction, nil)

		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver2", gomock.Any()).Return(*transaction2, nil)

		t := model.Transaction{}
		api.driverTransactionService.EXPECT().
			GetTransactionByOrderID(gomock.Any(), "test-1").Return([]model.Transaction{t}, nil)

		api.driverTransactionService.EXPECT().
			GetTransactionByOrderID(gomock.Any(), "test-2").Return([]model.Transaction{t}, nil)

		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)

		api.driverTransactionService.EXPECT().
			AddVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil, false)

		expectAddWageTransaction(api)
		expectAddWageTransaction(api)

		api.repo.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).Return(nil).Times(2)

		api.admapi.BulkOrderFraudStatus(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		var res BulkUpdateFraudStatusResp
		testutil.DecodeJSON(tt, recorder.Body, &res)
		require.Len(tt, res.Successes, 2)
		require.Len(tt, res.Failures, 0)
	})

	t.Run("partial pass because invalid request fraud status", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		content := `OrderId,Status
test-1,FRAUD
test-2,INVALID
`
		o1 := createOrder("test-1", model.FraudStatusNormal, model.StatusCompleted)
		gctx, recorder := makeReq(content)

		api.repo.EXPECT().
			Get(gomock.Any(), "test-1").
			Return(o1, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), "test-2").
			Return(o1, nil)

		transaction := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver1", gomock.Any()).Return(*transaction, nil)

		t := model.Transaction{}
		api.driverTransactionService.EXPECT().
			GetTransactionByOrderID(gomock.Any(), "test-1").Return([]model.Transaction{t}, nil)

		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)

		expectAddWageTransaction(api)

		api.repo.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 1)
	})

	t.Run("partial pass because order status is invalid", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		content := `OrderId,Status
test-1,FRAUD
test-2,FRAUD
`
		o1 := createOrder("test-1", model.FraudStatusNormal, model.StatusCompleted)
		o2 := createOrder("test-2", model.FraudStatusNormal, model.StatusDriverArrived)
		gctx, recorder := makeReq(content)

		api.repo.EXPECT().
			Get(gomock.Any(), "test-1").
			Return(o1, nil)
		api.repo.EXPECT().
			Get(gomock.Any(), "test-2").
			Return(o2, nil)

		transaction := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver1", gomock.Any()).Return(*transaction, nil)

		t := model.Transaction{}
		api.driverTransactionService.EXPECT().
			GetTransactionByOrderID(gomock.Any(), "test-1").Return([]model.Transaction{t}, nil)

		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)

		expectAddWageTransaction(api)

		api.repo.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 1)
	})

	t.Run("partial pass because order not found", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		content := `OrderId,Status
test-1,FRAUD
test-2,FRAUD
`
		o1 := createOrder("test-1", model.FraudStatusNormal, model.StatusCompleted)
		gctx, recorder := makeReq(content)

		api.repo.EXPECT().
			Get(gomock.Any(), "test-1").
			Return(o1, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), "test-2").
			Return(nil, errors.New("not found"))

		transaction := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver1", gomock.Any()).Return(*transaction, nil)

		t := model.Transaction{}
		api.driverTransactionService.EXPECT().
			GetTransactionByOrderID(gomock.Any(), "test-1").Return([]model.Transaction{t}, nil)

		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)

		expectAddWageTransaction(api)

		api.repo.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 1)
	})

	t.Run("exceeds limit", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 1}, &service.AtomicCancelReasonConfig{})
		defer finish()

		content := `OrderId,Status
test-1,FRAUD
test-2,NORMAL
`
		gctx, recorder := makeReq(content)

		api.admapi.BulkOrderFraudStatus(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("reject pending withdraw when wallet is negative", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		content := `OrderId,Status
test-1,FRAUD
`
		o1 := createOrder("test-1", model.FraudStatusNormal, model.StatusCompleted)
		gctx, recorder := makeReq(content)

		api.repo.EXPECT().
			Get(gomock.Any(), "test-1").
			Return(o1, nil)

		transaction := model.NewDriverTransaction("driver1")
		transaction.WalletBalance = -50
		o1.Driver = "driver1"
		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver1", gomock.Any()).Return(*transaction, nil)

		t := model.Transaction{}
		api.driverTransactionService.EXPECT().
			GetTransactionByOrderID(gomock.Any(), "test-1").Return([]model.Transaction{t}, nil)

		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)

		txn := *model.NewTransaction(
			"tran-1",
			model.UserTransactionChannel,
			model.PurchaseTransactionAction,
			model.PendingTransactionStatus,
			*model.NewWithdrawWalletTransactionInfo("driver-1", 200),
		)
		pendingWithdrawTxns := []model.Transaction{
			txn,
		}

		api.tranRepo.EXPECT().
			Find(gomock.Any(), gomock.Any(), 0, 0).
			Return(pendingWithdrawTxns, nil)

		api.tranRepo.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			Return(nil)

		api.driverTransactionService.EXPECT().ReturnWalletBalanceToDriver(gomock.Any(), gomock.Any(), requestedByName).Return(nil)

		api.driverTransactionService.EXPECT().AddDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(model.DriverTransaction{
				WalletBalance: -1,
			}, nil, nil)

		api.repo.EXPECT().
			UpdateOrder(gomock.Any(), gomock.Any()).
			Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 0)
	})

	t.Run("fail when trip status not completed", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-1"
		tripId := "trip-1"

		content := fmt.Sprintf(`OrderId,Status
%s,FRAUD
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusNormal, model.StatusCompleted)
		o1.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusDriveTo,
			HeadTo: 1,
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		bulkThenAssert(api, tt, gctx, recorder, 0, 1)
	})

	t.Run("should mark fraud fail when trip fraud_status FRAUD (so) [order a]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-a"
		tripId := "trip-a"

		content := fmt.Sprintf(`OrderId,Status
%s,FRAUD
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusFraud, model.StatusCompleted)
		o1.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		bulkThenAssert(api, tt, gctx, recorder, 0, 1)
	})

	t.Run("should mark fraud success when trip fraud_status FRAUD (mo) [order i,j]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-i"
		bundledOrderId := "test-j"

		tripId := "trip-ij"

		content := fmt.Sprintf(`OrderId,Status
%s,FRAUD
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusNormal, model.StatusCompleted)
		o2 := createOrder(bundledOrderId, model.FraudStatusNormal, model.StatusCompleted)
		o1.TripID = tripId
		o2.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
			Orders: model.TripOrders{
				{
					OrderID: orderId,
					IsFraud: false,
				},
				{
					OrderID: bundledOrderId,
					IsFraud: false,
				},
			},
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		dt := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		var tripWage []model.Transaction
		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver1", gomock.Any()).Return(*dt, nil)
		api.driverTransactionService.EXPECT().GetWageTransactionFromTrip(gomock.Any(), tripId).Return(tripWage, nil)
		api.driverTransactionService.EXPECT().GetTransactionByOrderID(gomock.Any(), orderId).Return([]model.Transaction{}, nil)
		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)
		expectAddWageTransaction(api)

		expectRecalculateTripWage(api)

		api.repo.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		api.tripRepo.EXPECT().SetFraudData(gomock.Any(), gomock.Any()).Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 0)
	})

	t.Run("should mark fraud success when trip fraud_status NORMAL (so) [order b]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-b"
		tripId := "trip-b"

		content := fmt.Sprintf(`OrderId,Status
%s,FRAUD
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusNormal, model.StatusCompleted)
		o1.TripID = tripId

		gctx, recorder := makeReq(content)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
			Orders: model.TripOrders{
				{
					OrderID: orderId,
					Status:  model.StatusCompleted,
				},
			},
		}, nil)

		dt := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		var tripWage []model.Transaction
		expectGetToVoidTransaction(api, dt, tripWage, tripId, orderId)
		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)
		expectAddWageTransaction(api)

		api.repo.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		api.tripRepo.EXPECT().SetFraudData(gomock.Any(), gomock.Any()).Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 0)
	})

	t.Run("should mark fraud success when trip fraud_status EMPTY (so) [order c]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-c"
		tripId := "trip-c"

		content := fmt.Sprintf(`OrderId,Status
%s,FRAUD
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusNormal, model.StatusCompleted)
		o1.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
			Orders: model.TripOrders{
				{
					OrderID: orderId,
					Status:  model.StatusCompleted,
				},
			},
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		dt := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		var tripWage []model.Transaction
		expectGetToVoidTransaction(api, dt, tripWage, tripId, orderId)
		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)
		expectAddWageTransaction(api)

		api.repo.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		api.tripRepo.EXPECT().SetFraudData(gomock.Any(), gomock.Any()).Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 0)
	})

	t.Run("should mark fraud success when trip fraud_status EMPTY (mo) [order d,e]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-d"
		bundledOrderId := "test-e"

		tripId := "trip-de"

		content := fmt.Sprintf(`OrderId,Status
%s,FRAUD
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusNormal, model.StatusCompleted)
		o2 := createOrder(bundledOrderId, model.FraudStatusNormal, model.StatusCompleted)
		o1.TripID = tripId
		o2.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
			Orders: model.TripOrders{
				{
					OrderID: orderId,
				},
				{
					OrderID: bundledOrderId,
				},
			},
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		dt := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		var tripWage []model.Transaction
		api.driverTransactionService.EXPECT().
			GetDriverTransaction(gomock.Any(), "driver1", gomock.Any()).Return(*dt, nil)
		api.driverTransactionService.EXPECT().GetWageTransactionFromTrip(gomock.Any(), tripId).Return(tripWage, nil)
		api.driverTransactionService.EXPECT().GetTransactionByOrderID(gomock.Any(), orderId).Return([]model.Transaction{}, nil)
		api.driverTransactionService.EXPECT().
			DeductVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil)

		expectRecalculateTripWage(api)
		expectAddWageTransaction(api)

		api.repo.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		api.tripRepo.EXPECT().SetFraudData(gomock.Any(), gomock.Any()).Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 0)
	})

	t.Run("should mark normal success when trip fraud_status FRAUD (so) [order f]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-f"
		tripId := "trip-f"

		content := fmt.Sprintf(`OrderId,Status
%s,NORMAL
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusFraud, model.StatusCompleted)
		o1.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
			Orders: model.TripOrders{
				{
					OrderID: orderId,
					IsFraud: true,
					Status:  model.StatusCompleted,
				},
			},
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		dt := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		var tripWage []model.Transaction
		expectGetToVoidTransaction(api, dt, tripWage, tripId, orderId)

		api.driverTransactionService.EXPECT().
			AddVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil, false)

		expectRecalculateTripWage(api)
		expectAddWageTransaction(api)

		api.repo.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		api.tripRepo.EXPECT().SetFraudData(gomock.Any(), gomock.Any()).Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 0)
	})

	t.Run("should mark normal success when trip fraud_status FRAUD (mo) [order g,h]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-g"
		bundledOrderId := "test-h"
		tripId := "trip-gh"

		content := fmt.Sprintf(`OrderId,Status
%s,NORMAL
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusFraud, model.StatusCompleted)
		o2 := createOrder(orderId, model.FraudStatusNormal, model.StatusCompleted)
		o1.TripID = tripId
		o2.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
			Orders: model.TripOrders{
				{
					OrderID: orderId,
					IsFraud: true,
					Status:  model.StatusCompleted,
				},
				{
					OrderID: bundledOrderId,
					IsFraud: false,
					Status:  model.StatusCompleted,
				},
			},
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		dt := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		var tripWage []model.Transaction
		expectGetToVoidTransaction(api, dt, tripWage, tripId, orderId)

		api.driverTransactionService.EXPECT().
			AddVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil, false)

		expectRecalculateTripWage(api)
		expectAddWageTransaction(api)

		api.repo.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		api.tripRepo.EXPECT().SetFraudData(gomock.Any(), gomock.Any()).Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 0)
	})

	t.Run("should mark normal fail when trip fraud_status EMPTY (so) [order k]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-k"
		tripId := "trip-k"

		content := fmt.Sprintf(`OrderId,Status
%s,NORMAL
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusFraud, model.StatusCompleted)
		o1.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
			Orders: model.TripOrders{
				{
					OrderID: orderId,
				},
			},
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		o1.Driver = "driver1"

		bulkThenAssert(api, tt, gctx, recorder, 0, 1)
	})

	t.Run("should mark normal success when trip fraud_status EMPTY (mo) [order l,m]", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: true, UpdateOrderFraudMaxSize: 50}, &service.AtomicCancelReasonConfig{})
		defer finish()

		orderId := "test-l"
		bundledOrderId := "test-m"
		tripId := "trip-lm"

		content := fmt.Sprintf(`OrderId,Status
%s,NORMAL
`, orderId)

		o1 := createOrder(orderId, model.FraudStatusFraud, model.StatusCompleted)
		o2 := createOrder(orderId, model.FraudStatusNormal, model.StatusCompleted)
		o1.TripID = tripId
		o2.TripID = tripId

		gctx, recorder := makeReq(content)

		api.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: tripId,
			Status: model.TripStatusCompleted,
			HeadTo: 1,
			Orders: model.TripOrders{
				{
					OrderID: orderId,
					IsFraud: true,
					Status:  model.StatusCompleted,
				},
				{
					OrderID: bundledOrderId,
					IsFraud: false,
					Status:  model.StatusCompleted,
				},
			},
		}, nil)

		api.repo.EXPECT().
			Get(gomock.Any(), orderId).
			Return(o1, nil)

		dt := model.NewDriverTransaction("driver1")
		o1.Driver = "driver1"

		var tripWage []model.Transaction
		expectGetToVoidTransaction(api, dt, tripWage, tripId, orderId)

		api.driverTransactionService.EXPECT().
			AddVoidFraudTransaction(gomock.Any(), gomock.Any()).Return([]model.TransactionInfo{}, nil, false)

		expectRecalculateTripWage(api)
		expectAddWageTransaction(api)

		api.repo.EXPECT().UpdateOrder(gomock.Any(), gomock.Any()).Return(nil)
		api.tripRepo.EXPECT().SetFraudData(gomock.Any(), gomock.Any()).Return(nil)

		bulkThenAssert(api, tt, gctx, recorder, 1, 0)
	})
}

func TestAdminAPI_ReviewOrderPhotoList(t *testing.T) {
	makeReq := func() (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/review-order-photo")
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("should return success", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: false}, &service.AtomicCancelReasonConfig{})
		defer finish()
		gctx, recorder := makeReq()

		od := model.NewOrder(model.Quote{}, "order-id")
		api.repo.EXPECT().
			FindAndSort(gctx, gomock.Any(), 0, 0, gomock.Any(), gomock.Any()).Return([]model.Order{*od}, nil)
		api.repo.EXPECT().
			Count(gctx, gomock.Any(), gomock.Any()).Return(1, nil)

		api.admapi.ReviewOrderPhotoList(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestAdminAPI_ReviewOrderPhotoDetail(t *testing.T) {
	orderID := "LMF-0"
	userID := "user-id"
	driverID := "LMD-01"
	driverAvatarUrl := "https://this-is-testing-avatar-url.com/image.jpg"
	uploadTime := time.Date(2020, time.January, 30, 1, 1, 0, 0, time.Local)
	req := func() (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("GET", "/review-order-photo/LMF-0", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("should return success", func(tt *testing.T) {
		adminapi, finish := newTestAdminAPI(tt, OrderAPIConfig{UpdateOrderFraudEnabled: false}, &service.AtomicCancelReasonConfig{})
		defer finish()
		gctx, recorder := req()

		od := model.NewOrder(model.Quote{}, "LMF-0")
		od.UserID = userID
		od.Driver = driverID
		od.VerifiedRiderPhotoUrls = []model.VerifiedRiderPhotoUrl{
			{
				PhotoUrl:  "url",
				CreatedAt: uploadTime,
			},
		}

		driverModel := &model.Driver{
			DriverID: driverID,
			BaseDriver: model.BaseDriver{
				AvatarURL: driverAvatarUrl,
			},
		}

		adminapi.repo.EXPECT().
			Get(gomock.Any(), orderID, gomock.Any()).
			Return(od, nil)

		adminapi.driverRepository.EXPECT().
			GetProfile(gomock.Any(), driverID).
			Return(driverModel, nil)

		adminapi.admapi.ReviewOrderPhotoDetail(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
		actual := struct {
			OrderID   string
			UserID    string
			PhotoUrl  string
			CreatedAt time.Time
		}{}
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, orderID, actual.OrderID)
		require.Equal(tt, userID, actual.UserID)
	})
}

func TestAdminAPi_InternalCancelReason(t *testing.T) {
	t.Parallel()

	t.Run("should get list internal cancel reason success", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		ctx, rec := testutil.TestRequestContext("GET", "v1/admin/internal-cancel-reasons", nil)
		expectedDate := time.Date(2021, 8, 26, 0, 0, 0, 0, time.UTC)
		testAdminApi.internalCancalReasonRepository.EXPECT().FindSorted(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.InternalCancelReason{
			{
				ID:                  "mock-id-0",
				Name:                "mock-name-0",
				CompensationReason:  FoodCancelUserCannotContact,
				BanDurationInMinute: 0,
				CreatedAt:           expectedDate,
				UpdatedAt:           expectedDate,
				CancellationSource:  model.SourceRMS,
				WMAMetric:           "mock-wma-metric",
			},
			{
				ID:                  "mock-id-1",
				Name:                "mock-name-1",
				CompensationReason:  FoodCancelDuplicateOrder,
				BanDurationInMinute: 1,
				CreatedAt:           expectedDate,
				UpdatedAt:           expectedDate,
			},
		}, nil)

		testAdminApi.admapi.GetInternalCancelReasons(ctx)

		actual := struct {
			Data  ListInternalCancelReasonRes `json:"data"`
			Count int                         `json:"countTotal"`
		}{}
		testutil.DecodeJSON(tt, rec.Body, &actual)
		require.Equal(tt, 200, rec.Code)
		require.Equal(tt, 2, actual.Count)
		first := actual.Data[0]
		require.Equal(tt, "mock-id-0", first.ID)
		require.Equal(tt, "mock-name-0", first.Name)
		require.Equal(tt, FoodCancelUserCannotContact, first.CompensationReason)
		require.Equal(tt, 0, first.BanDurationInMinute)
		require.Equal(tt, expectedDate, first.CreatedAt)
		require.Equal(tt, expectedDate, first.UpdatedAt)
		require.Equal(tt, model.SourceRMS, first.CancellationSource)
		require.Equal(tt, "mock-wma-metric", first.WMAMetric)
		second := actual.Data[1]
		require.Equal(tt, "mock-id-1", second.ID)
		require.Equal(tt, "mock-name-1", second.Name)
		require.Equal(tt, FoodCancelDuplicateOrder, second.CompensationReason)
		require.Equal(tt, 1, second.BanDurationInMinute)
		require.Equal(tt, expectedDate, second.CreatedAt)
		require.Equal(tt, expectedDate, second.UpdatedAt)
	})

	t.Run("should return 500 when get list internal cancel reason and error", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		ctx, rec := testutil.TestRequestContext("GET", "v1/admin/internal-cancel-reasons", nil)
		mockError := errors.New("database error")
		api.internalCancalReasonRepository.EXPECT().FindSorted(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.InternalCancelReason{}, mockError)

		api.admapi.GetInternalCancelReasons(ctx)
		require.Equal(tt, 500, rec.Code)
	})

	t.Run("should get internal cancel reason by id success", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		ctx, rec := testutil.TestRequestContext("GET", "v1/admin/internal-cancel-reasons/mock-id", nil)
		expectedDate := time.Date(2021, 8, 26, 0, 0, 0, 0, time.UTC)
		testAdminApi.internalCancalReasonRepository.EXPECT().FindById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.InternalCancelReason{
			ID:                  "mock-id-0",
			Name:                "mock-name-0",
			CompensationReason:  FoodCancelNoCompensation,
			BanDurationInMinute: 0,
			CreatedAt:           expectedDate,
			UpdatedAt:           expectedDate,
			CancellationSource:  model.SourceRMS,
			WMAMetric:           "mock-wma-metric",
		}, nil)

		testAdminApi.admapi.GetInternalCancelReason(ctx)

		var actual InternalCancelReasonRes
		testutil.DecodeJSON(tt, rec.Body, &actual)
		require.Equal(tt, 200, rec.Code)
		require.Equal(tt, "mock-id-0", actual.ID)
		require.Equal(tt, "mock-name-0", actual.Name)
		require.Equal(tt, FoodCancelNoCompensation, actual.CompensationReason)
		require.Equal(tt, 0, actual.BanDurationInMinute)
		require.Equal(tt, expectedDate, actual.CreatedAt)
		require.Equal(tt, expectedDate, actual.UpdatedAt)
		require.Equal(tt, model.SourceRMS, actual.CancellationSource)
		require.Equal(tt, "mock-wma-metric", actual.WMAMetric)
	})

	t.Run("should return 500 when get internal cancel reason and  error", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		ctx, rec := testutil.TestRequestContext("GET", "v1/admin/internal-cancel-reasons/mock-id", nil)
		mockError := errors.New("database error")
		testAdminApi.internalCancalReasonRepository.EXPECT().FindById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.InternalCancelReason{}, mockError)

		testAdminApi.admapi.GetInternalCancelReason(ctx)

		require.Equal(tt, 500, rec.Code)
	})

	t.Run("should return 404 when get internal cancel reason and error not found", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		ctx, rec := testutil.TestRequestContext("GET", "v1/admin/internal-cancel-reasons/mock-id", nil)
		mockError := repository.ErrNotFound
		testAdminApi.internalCancalReasonRepository.EXPECT().FindById(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.InternalCancelReason{}, mockError)

		testAdminApi.admapi.GetInternalCancelReason(ctx)

		require.Equal(tt, 404, rec.Code)
	})

	t.Run("create internal cancel reason should success", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelUserCannotContact,
			UseCase:            "WMA_SELF_CANCEL",
		}
		ctx, rec := testutil.TestRequestContext("POST", "v1/admin/internal-cancel-reasons", testutil.JSON(mockBody))
		testAdminApi.internalCancalReasonRepository.EXPECT().Create(gomock.Any(), gomock.Any()).Do(func(ctx context.Context, actual *model.InternalCancelReason) {
			require.NotNil(tt, actual)
			require.Equal(tt, actual.Name, mockBody.Name)
			require.Equal(tt, actual.Label, mockBody.Label)
			require.Equal(tt, string(actual.UseCase), mockBody.UseCase)
			require.Equal(tt, actual.CompensationReason, mockBody.CompensationReason)
		}).Return(nil)
		testAdminApi.auditLogRepository.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		testAdminApi.admapi.CreateInternalCancelReason(ctx)
		require.Equal(tt, 201, rec.Code)
	})

	t.Run("should return 400 when create internal cancel reason with unknown use case", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelUserCannotContact,
			UseCase:            "SomeThingUnknown",
		}
		ctx, rec := testutil.TestRequestContext("POST", "v1/admin/internal-cancel-reasons", testutil.JSON(mockBody))

		testAdminApi.admapi.CreateInternalCancelReason(ctx)
		require.Equal(tt, http.StatusBadRequest, rec.Code)
	})

	t.Run("should return 500 when create internal cancel reason and error", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelUserCannotContact,
		}
		ctx, rec := testutil.TestRequestContext("POST", "v1/admin/internal-cancel-reasons", testutil.JSON(mockBody))
		mockError := errors.New("database error")
		testAdminApi.internalCancalReasonRepository.EXPECT().Create(gomock.Any(), gomock.Any()).Return(mockError)

		testAdminApi.admapi.CreateInternalCancelReason(ctx)

		require.Equal(tt, 500, rec.Code)
	})

	t.Run("create internal cancel reason with wma metric should success ", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{
			Config: service.CancelReasonCfg{
				IsCancelReasonWithWMAMetricEnabled: true,
				WMAMetricListForCancelReason:       types.NewStringSet("mock-wma-metric"),
			},
		})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelUserCannotContact,
			CancellationSource: model.SourceRMS,
			WMAMetric:          "mock-wma-metric",
		}
		ctx, rec := testutil.TestRequestContext("POST", "v1/admin/internal-cancel-reasons", testutil.JSON(mockBody))
		testAdminApi.internalCancalReasonRepository.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
		testAdminApi.auditLogRepository.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		testAdminApi.admapi.CreateInternalCancelReason(ctx)
		require.Equal(tt, 201, rec.Code)
	})

	t.Run("should return 400 when create internal cancel reason with wma metric not exists", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{
			Config: service.CancelReasonCfg{
				IsCancelReasonWithWMAMetricEnabled: true,
				WMAMetricListForCancelReason:       types.NewStringSet("mock-wma-metric"),
			},
		})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelUserCannotContact,
			CancellationSource: model.SourceRMS,
			WMAMetric:          "mock-not-exists",
		}
		ctx, rec := testutil.TestRequestContext("POST", "v1/admin/internal-cancel-reasons", testutil.JSON(mockBody))

		testAdminApi.admapi.CreateInternalCancelReason(ctx)
		require.Equal(tt, 400, rec.Code)
	})

	t.Run("update internal cancel reason should success", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelDriverCannotFulfill,
		}
		ctx, rec := testutil.TestRequestContext("PUT", "v1/admin/internal-cancel-reasons/mock-id", testutil.JSON(mockBody))
		testAdminApi.internalCancalReasonRepository.EXPECT().FindById(gomock.Any(), gomock.Any()).Return(&model.InternalCancelReason{}, nil)
		testAdminApi.internalCancalReasonRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		testAdminApi.auditLogRepository.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		testAdminApi.admapi.UpdateInternalCancelReason(ctx)

		require.Equal(tt, 204, rec.Code)
	})

	t.Run("should return 500 when update internal cancel reason and error", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelDriverCannotFulfill,
		}
		ctx, rec := testutil.TestRequestContext("PUT", "v1/admin/internal-cancel-reasons/mock-id", testutil.JSON(mockBody))
		mockError := errors.New("database error")
		testAdminApi.internalCancalReasonRepository.EXPECT().FindById(gomock.Any(), gomock.Any()).Return(&model.InternalCancelReason{}, nil)
		testAdminApi.internalCancalReasonRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(mockError)

		testAdminApi.admapi.UpdateInternalCancelReason(ctx)

		require.Equal(tt, 500, rec.Code)
	})

	t.Run("should return 404 when update internal cancel reason and not found", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelDriverAccident,
		}
		ctx, rec := testutil.TestRequestContext("PUT", "v1/admin/internal-cancel-reasons/mock-id", testutil.JSON(mockBody))
		mockError := repository.ErrNotFound
		testAdminApi.internalCancalReasonRepository.EXPECT().FindById(gomock.Any(), gomock.Any()).Return(&model.InternalCancelReason{}, mockError)

		testAdminApi.admapi.UpdateInternalCancelReason(ctx)

		require.Equal(tt, 404, rec.Code)
	})

	t.Run("update internal cancel reason should success with wma metric ", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{
			Config: service.CancelReasonCfg{
				IsCancelReasonWithWMAMetricEnabled: true,
				WMAMetricListForCancelReason:       types.NewStringSet("mock-wma-metric"),
			},
		})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelDriverCannotFulfill,
			CancellationSource: model.SourceRMS,
			WMAMetric:          "mock-wma-metric",
		}
		ctx, rec := testutil.TestRequestContext("PUT", "v1/admin/internal-cancel-reasons/mock-id", testutil.JSON(mockBody))
		testAdminApi.internalCancalReasonRepository.EXPECT().FindById(gomock.Any(), gomock.Any()).Return(&model.InternalCancelReason{}, nil)
		testAdminApi.internalCancalReasonRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		testAdminApi.auditLogRepository.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		testAdminApi.admapi.UpdateInternalCancelReason(ctx)

		require.Equal(tt, 204, rec.Code)
	})

	t.Run("should return 400 when update internal cancel reason with wma metric not exists", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{
			Config: service.CancelReasonCfg{
				IsCancelReasonWithWMAMetricEnabled: true,
				WMAMetricListForCancelReason:       types.NewStringSet("mock-wma-metric"),
			},
		})
		defer finish()
		mockBody := InternalCancelReasonRequest{
			Name:               "mock-name",
			Label:              "mock-label",
			CompensationReason: FoodCancelUserCannotContact,
			CancellationSource: model.SourceRMS,
			WMAMetric:          "mock-not-exists",
		}
		ctx, rec := testutil.TestRequestContext("POST", "v1/admin/internal-cancel-reasons", testutil.JSON(mockBody))

		testAdminApi.admapi.UpdateInternalCancelReason(ctx)
		require.Equal(tt, 400, rec.Code)
	})

	t.Run("should delete internal cancel reason success", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		ctx, rec := testutil.TestRequestContext("DELETE", "v1/admin/internal-cancel-reasons/mock-id", nil)
		testAdminApi.internalCancalReasonRepository.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(nil)
		testAdminApi.auditLogRepository.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(nil)

		testAdminApi.admapi.DeleteInternalCancelReason(ctx)

		require.Equal(tt, 204, rec.Code)
	})

	t.Run("should return 500 when delete internal cancel reason and error", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		ctx, rec := testutil.TestRequestContext("DELETE", "v1/admin/internal-cancel-reasons/mock-id", nil)
		mockError := errors.New("database error")
		testAdminApi.internalCancalReasonRepository.EXPECT().Delete(gomock.Any(), gomock.Any()).Return(mockError)

		testAdminApi.admapi.DeleteInternalCancelReason(ctx)

		require.Equal(tt, 500, rec.Code)
	})

	t.Run("should get list internal cancel reason success", func(tt *testing.T) {
		testAdminApi, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		ctx, rec := testutil.TestRequestContext("GET", "v1/admin/internal-cancel-reasons", nil)
		expectedDate := time.Date(2021, 8, 26, 0, 0, 0, 0, time.UTC)
		testAdminApi.internalCancalReasonRepository.EXPECT().FindSorted(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.InternalCancelReason{
			{
				ID:                  "mock-id-0",
				Name:                "mock-name-0",
				CompensationReason:  FoodCancelUserCannotContact,
				BanDurationInMinute: 0,
				CreatedAt:           expectedDate,
				UpdatedAt:           expectedDate,
			},
			{
				ID:                  "mock-id-1",
				Name:                "mock-name-1",
				CompensationReason:  FoodCancelDuplicateOrder,
				BanDurationInMinute: 1,
				CreatedAt:           expectedDate,
				UpdatedAt:           expectedDate,
			},
		}, nil)

		testAdminApi.admapi.GetInternalCancelReasons(ctx)

		actual := struct {
			Data  ListInternalCancelReasonRes `json:"data"`
			Count int                         `json:"countTotal"`
		}{}
		testutil.DecodeJSON(tt, rec.Body, &actual)
		require.Equal(tt, 200, rec.Code)
		require.Equal(tt, 2, actual.Count)
		first := actual.Data[0]
		require.Equal(tt, "mock-id-0", first.ID)
		require.Equal(tt, "mock-name-0", first.Name)
		require.Equal(tt, FoodCancelUserCannotContact, first.CompensationReason)
		require.Equal(tt, 0, first.BanDurationInMinute)
		require.Equal(tt, expectedDate, first.CreatedAt)
		require.Equal(tt, expectedDate, first.UpdatedAt)
		second := actual.Data[1]
		require.Equal(tt, "mock-id-1", second.ID)
		require.Equal(tt, "mock-name-1", second.Name)
		require.Equal(tt, FoodCancelDuplicateOrder, second.CompensationReason)
		require.Equal(tt, 1, second.BanDurationInMinute)
		require.Equal(tt, expectedDate, second.CreatedAt)
		require.Equal(tt, expectedDate, second.UpdatedAt)
	})
}

func TestValidateFoodReason(t *testing.T) {
	testCases := []struct {
		foodReason string
		expected   error
	}{
		{FoodCancelNoCompensation, nil},
		{FoodCancelDriverAccident, nil},
		{FoodCancelDriverCannotFulfill, nil},
		{FoodCancelDuplicateOrder, nil},
		{FoodCancelUserCannotContact, nil},
		{"INVALID_FOOD_REASON", fmt.Errorf(`invalid food cancel reason "INVALID_FOOD_REASON"`)},
	}

	for _, tc := range testCases {
		actual := ValidateFoodReason(tc.foodReason)
		require.Equal(t, tc.expected, actual)
	}
}

func TestAdminAPI_OrderList(t *testing.T) {
	type Req struct {
		DriverID              string                      `form:"driverId"`
		OrderID               string                      `form:"orderId"`
		FraudStatus           string                      `form:"fraudStatus"`
		RestaurantID          string                      `form:"restaurantId"`
		BeginAt               time.Time                   `form:"beginAt"`
		EndAt                 time.Time                   `form:"endAt"`
		DeliveringPhotoStatus model.DeliveringPhotoStatus `form:"deliveringPhotoStatus"`
	}

	t.Run("should return success", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		makeReq := func() (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPOST("/orders")
			gctx.Body().JSON(Req{
				DriverID: "MOCKED_DRIVER",
			}).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}
		gctx, recorder := makeReq()

		od := model.NewOrder(model.Quote{}, "order-id")
		api.repo.EXPECT().
			Find(gctx, gomock.Any(), 0, 0, gomock.Any()).Return([]model.Order{*od}, nil)
		api.repo.EXPECT().
			Count(gctx, gomock.Any(), gomock.Any()).Return(1, nil)

		api.admapi.ListOrders(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should return correctly with query", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		makeQueryReq := func() (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPOST("/orders")
			gctx.Body().JSON(Req{
				DriverID:              "driver-id",
				OrderID:               "order-id",
				RestaurantID:          "res-id",
				BeginAt:               time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC),
				EndAt:                 time.Date(2021, 1, 2, 1, 1, 1, 1, time.UTC),
				DeliveringPhotoStatus: model.DeliveringPhotoStatusPassed,
			}).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}
		gctx, recorder := makeQueryReq()

		od := model.NewOrder(model.Quote{}, "order-id")
		api.repo.EXPECT().
			Find(gctx, gomock.Any(), 0, 0, gomock.Any()).Return([]model.Order{*od}, nil)
		api.repo.EXPECT().
			Count(gctx, gomock.Any(), gomock.Any()).Return(1, nil)

		api.admapi.ListOrders(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should return error if no filter", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		makeQueryReq := func() (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPOST("/orders")
			gctx.Body().JSON(Req{
				DriverID:              "",
				OrderID:               "",
				RestaurantID:          "",
				BeginAt:               time.Time{},
				EndAt:                 time.Time{},
				DeliveringPhotoStatus: "",
			}).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}
		gctx, recorder := makeQueryReq()

		od := model.NewOrder(model.Quote{}, "order-id")
		api.repo.EXPECT().
			Find(gctx, gomock.Any(), 0, 0, gomock.Any()).Return([]model.Order{*od}, nil).Times(0)
		api.repo.EXPECT().
			Count(gctx, gomock.Any(), gomock.Any()).Return(1, nil).Times(0)

		api.admapi.ListOrders(gctx)
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should return error if literally no filter", func(tt *testing.T) {
		api, finish := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer finish()
		makeQueryReq := func() (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPOST("/orders")
			gctx.Body().JSON(Req{}).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}
		gctx, recorder := makeQueryReq()

		od := model.NewOrder(model.Quote{}, "order-id")
		api.repo.EXPECT().
			Find(gctx, gomock.Any(), 0, 0, gomock.Any()).Return([]model.Order{*od}, nil).Times(0)
		api.repo.EXPECT().
			Count(gctx, gomock.Any(), gomock.Any()).Return(1, nil).Times(0)

		api.admapi.ListOrders(gctx)
		assert.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestAdminAPI_GetCommissionRate(t *testing.T) {
	t.Parallel()

	type TestCase struct {
		name                          string
		commissionRateSettingsEnabled bool
		service                       string
		expected                      float64
	}

	commissionRateSettings := map[string]float64{
		"food":      0.11,
		"mart":      0.12,
		"messenger": 0.13,
		"bike":      0.14,
	}
	commission := 0.30

	testCases := []TestCase{
		{
			name:                          "when commission rate settings are disabled, should fallback to commission",
			commissionRateSettingsEnabled: false,
			service:                       "food",
			expected:                      0.30,
		},
		{
			name:                          "when commission rate settings are disabled, should fallback to commission",
			commissionRateSettingsEnabled: false,
			service:                       "mart",
			expected:                      0.30,
		},
		{
			name:                          "when commission rate settings are disabled, should fallback to commission",
			commissionRateSettingsEnabled: false,
			service:                       "messenger",
			expected:                      0.30,
		},
		{
			name:                          "when commission rate settings are disabled, should fallback to commission",
			commissionRateSettingsEnabled: false,
			service:                       "bike",
			expected:                      0.30,
		},
		{
			name:                          "when commission rate settings are disabled, should fallback to commission",
			commissionRateSettingsEnabled: false,
			service:                       "unknown",
			expected:                      0.30,
		},
		{
			name:                          "when commission rate settings are enabled, should return from settings",
			commissionRateSettingsEnabled: true,
			service:                       "food",
			expected:                      0.11,
		},
		{
			name:                          "when commission rate settings are enabled, should return from settings",
			commissionRateSettingsEnabled: true,
			service:                       "mart",
			expected:                      0.12,
		},
		{
			name:                          "when commission rate settings are enabled, should return from settings",
			commissionRateSettingsEnabled: true,
			service:                       "messenger",
			expected:                      0.13,
		},
		{
			name:                          "when commission rate settings are enabled, should return from settings",
			commissionRateSettingsEnabled: true,
			service:                       "bike",
			expected:                      0.14,
		},
		{
			name:                          "when commission rate settings are enabled, but with unknown service, should fallback to default commission",
			commissionRateSettingsEnabled: false,
			service:                       "unknown",
			expected:                      0.30,
		},
	}

	for _, tc := range testCases {
		_tc := tc
		t.Run(_tc.name, func(t *testing.T) {
			t.Parallel()
			cfg := OrderAPIConfig{
				AtomicOrderDBConfig: &AtomicOrderDBConfig{
					Config: OrderDBConfig{
						Commisson:                     commission,
						CommissionRateSettings:        commissionRateSettings,
						CommissionRateSettingsEnabled: _tc.commissionRateSettingsEnabled,
					},
				},
			}
			actual := GetCommissionRate(cfg, _tc.service)
			require.Equal(t, _tc.expected, actual)
		})
	}
}

func TestAdminAPI_UpdateCancelReason(t *testing.T) {
	t.Run("Should response 400 if no request body", func(tt *testing.T) {
		mockOrderID := "ORDER_ID"

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		url := fmt.Sprintf("/v1/admin/orders/%s/update-cancel-reason", mockOrderID)
		gctx, recorder := testutil.TestRequestContext("PUT", url, nil)

		gctx.Params = gin.Params{{Key: "orderID", Value: mockOrderID}}
		api.admapi.UpdateOrderCancelReason(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)

		respBodyBytes := recorder.Body.Bytes()
		var respBody absintheAPI.Error
		err := json.Unmarshal(respBodyBytes, &respBody)
		require.NoError(tt, err)
		require.Equal(tt, "Invalid Body", respBody.Message)
	})
	t.Run("Should response 404 if order is not found", func(tt *testing.T) {
		ctx := context.Background()
		mockOrderID := "ORDER_ID"

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		// Mock that the order is not found in the database
		api.repo.EXPECT().Get(ctx, mockOrderID).Return(nil, repository.ErrNotFound)

		url := fmt.Sprintf("/v1/admin/orders/%s/update-cancel-reason", mockOrderID)
		gctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(UpdateOrderCancelReasonRequest{}))

		gctx.Params = gin.Params{{Key: "orderID", Value: mockOrderID}}
		api.admapi.UpdateOrderCancelReason(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)

		respBodyBytes := recorder.Body.Bytes()
		var respBody absintheAPI.Error
		err := json.Unmarshal(respBodyBytes, &respBody)
		require.NoError(tt, err)
		require.Equal(tt, "order id is not found", respBody.Message)
	})
	t.Run("Should response 500 if get order response unknown result", func(tt *testing.T) {
		ctx := context.Background()
		mockOrderID := "ORDER_ID"

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		// Mock that the order is not found in the database
		mockedError := errors.New("mocked error")
		api.repo.EXPECT().Get(ctx, mockOrderID).Return(nil, mockedError)

		url := fmt.Sprintf("/v1/admin/orders/%s/update-cancel-reason", mockOrderID)
		gctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(UpdateOrderCancelReasonRequest{}))

		gctx.Params = gin.Params{{Key: "orderID", Value: mockOrderID}}
		api.admapi.UpdateOrderCancelReason(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)

		respBodyBytes := recorder.Body.Bytes()
		var respBody absintheAPI.Error
		err := json.Unmarshal(respBodyBytes, &respBody)
		require.NoError(tt, err)
		require.Equal(tt, mockedError.Error(), respBody.Message)
	})
	t.Run("Should response 400 if order status is not CANCELLED", func(tt *testing.T) {
		ctx := context.Background()
		mockOrderID := "ORDER_ID"

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.repo.EXPECT().Get(ctx, mockOrderID).Return(&model.Order{
			OrderID: "Test",
			Status:  model.StatusCompleted,
		}, nil)

		url := fmt.Sprintf("/v1/admin/orders/%s/update-cancel-reason", mockOrderID)
		gctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(UpdateOrderCancelReasonRequest{}))

		gctx.Params = gin.Params{{Key: "orderID", Value: mockOrderID}}
		api.admapi.UpdateOrderCancelReason(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)

		respBodyBytes := recorder.Body.Bytes()
		var respBody absintheAPI.Error
		err := json.Unmarshal(respBodyBytes, &respBody)
		require.NoError(tt, err)
		require.Equal(tt, "order you request can't be updated the cancel reason detail", respBody.Message)
	})
	t.Run("Should response 404 if the Internal Cancel Reason is not found", func(tt *testing.T) {
		ctx := context.Background()
		mockOrderID := "ORDER_ID"
		mockCancelReasonID := "TEST_INTERNAL_CANCEL_REASON_ID"

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.repo.EXPECT().Get(ctx, mockOrderID).Return(&model.Order{
			OrderID: "Test",
			Status:  model.StatusCanceled,
		}, nil)
		api.internalCancalReasonRepository.EXPECT().FindById(ctx, mockCancelReasonID).Return(nil, repository.ErrNotFound)

		url := fmt.Sprintf("/v1/admin/orders/%s/update-cancel-reason", mockOrderID)
		gctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(UpdateOrderCancelReasonRequest{
			CancelReasonID: mockCancelReasonID,
		}))

		gctx.Params = gin.Params{{Key: "orderID", Value: mockOrderID}}
		api.admapi.UpdateOrderCancelReason(gctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)

		respBodyBytes := recorder.Body.Bytes()
		var respBody absintheAPI.Error
		err := json.Unmarshal(respBodyBytes, &respBody)
		require.NoError(tt, err)
		require.Equal(tt, "internal cancel reason not found", respBody.Message)
	})
	t.Run("Should response 500 if get the Internal Cancel Reason return error", func(tt *testing.T) {
		ctx := context.Background()
		mockOrderID := "ORDER_ID"
		mockCancelReasonID := "TEST_INTERNAL_CANCEL_REASON_ID"

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.repo.EXPECT().Get(ctx, mockOrderID).Return(&model.Order{
			OrderID: "Test",
			Status:  model.StatusCanceled,
		}, nil)

		mockedError := errors.New("mock error")
		api.internalCancalReasonRepository.EXPECT().FindById(ctx, mockCancelReasonID).Return(nil, mockedError)

		url := fmt.Sprintf("/v1/admin/orders/%s/update-cancel-reason", mockOrderID)
		gctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(UpdateOrderCancelReasonRequest{
			CancelReasonID: mockCancelReasonID,
		}))

		gctx.Params = gin.Params{{Key: "orderID", Value: mockOrderID}}
		api.admapi.UpdateOrderCancelReason(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)

		respBodyBytes := recorder.Body.Bytes()
		var respBody absintheAPI.Error
		err := json.Unmarshal(respBodyBytes, &respBody)
		require.NoError(tt, err)
		require.Equal(tt, mockedError.Error(), respBody.Message)
	})
	t.Run("Should response 500 if update order cancel return error", func(tt *testing.T) {
		ctx := context.Background()
		mockOrderID := "ORDER_ID"
		mockCancelReasonID := model.GenerateInternalCancelReasonId()

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.repo.EXPECT().Get(ctx, mockOrderID).Return(&model.Order{
			OrderID: "Test",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source: "Test",
			},
		}, nil)

		api.internalCancalReasonRepository.EXPECT().FindById(ctx, mockCancelReasonID.String()).Return(&model.InternalCancelReason{
			ID:                   mockCancelReasonID,
			Name:                 "Test-A",
			Label:                "Label-A",
			CompensationReason:   "Compensation-A",
			BanDurationInMinute:  10,
			ShouldAutoClaim:      true,
			IsReassign:           true,
			CancellationRateFree: true,
		}, nil)

		mockedError := errors.New("mocked error")
		api.repo.EXPECT().SetCancelDetailAndCancelDetailLog(ctx, mockOrderID, gomock.Any(), gomock.Any()).Do(func(ctx context.Context, orderID string, order model.Order, opts ...repository.Option) {
			require.Equal(tt, "Test-A", order.CancelDetail.Name)
			require.Equal(tt, "Label-A", order.CancelDetail.Label)
			require.Equal(tt, 10, order.CancelDetail.BanDurationInMinute)
			require.True(tt, order.CancelDetail.ShouldAutoClaim)
			require.True(tt, order.CancelDetail.IsReassign)
			require.True(tt, order.CancelDetail.CancellationRateFree)
			require.Equal(tt, "WMA", order.CancelDetail.Source)
			require.Equal(tt, "<EMAIL>", order.CancelDetail.Requestor)
			require.Equal(tt, "Comment", order.CancelDetail.Remark)

			require.Len(tt, order.CancelDetailLog, 1)
			require.Equal(tt, model.CancelDetailLog{
				CancelDetail: model.CancelDetail{
					Source: "Test",
				},
				LogAt: timeutils.Now().UTC(),
			}, order.CancelDetailLog[0])
		}).Return(mockedError)

		url := fmt.Sprintf("/v1/admin/orders/%s/update-cancel-reason", mockOrderID)
		gctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(UpdateOrderCancelReasonRequest{
			CancelReasonID:      mockCancelReasonID.String(),
			CancellationSource:  "WMA",
			Requestor:           "<EMAIL>",
			CancelReasonComment: "Comment",
		}))

		gctx.Params = gin.Params{{Key: "orderID", Value: mockOrderID}}
		api.admapi.UpdateOrderCancelReason(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)

		respBodyBytes := recorder.Body.Bytes()
		var respBody absintheAPI.Error
		err := json.Unmarshal(respBodyBytes, &respBody)
		require.NoError(tt, err)
		require.Equal(tt, mockedError.Error(), respBody.Message)
	})
	t.Run("Should response 200 update order cancel detail success", func(tt *testing.T) {
		ctx := context.Background()
		mockOrderID := "ORDER_ID"
		mockCancelReasonID := model.GenerateInternalCancelReasonId()

		api, done := newTestAdminAPI(tt, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		api.repo.EXPECT().Get(ctx, mockOrderID).Return(&model.Order{
			OrderID: "Test",
			Status:  model.StatusCanceled,
			CancelDetail: model.CancelDetail{
				Source: "Test",
			},
		}, nil)

		api.internalCancalReasonRepository.EXPECT().FindById(ctx, mockCancelReasonID.String()).Return(&model.InternalCancelReason{
			ID:                   mockCancelReasonID,
			Name:                 "Test-A",
			Label:                "Label-A",
			CompensationReason:   "Compensation-A",
			BanDurationInMinute:  10,
			ShouldAutoClaim:      true,
			IsReassign:           true,
			CancellationRateFree: true,
		}, nil)

		api.repo.EXPECT().SetCancelDetailAndCancelDetailLog(ctx, mockOrderID, gomock.Any(), gomock.Any()).Do(func(ctx context.Context, orderID string, order model.Order, opts ...repository.Option) {
			require.Equal(tt, "Test-A", order.CancelDetail.Name)
			require.Equal(tt, "Label-A", order.CancelDetail.Label)
			require.Equal(tt, 10, order.CancelDetail.BanDurationInMinute)
			require.True(tt, order.CancelDetail.ShouldAutoClaim)
			require.True(tt, order.CancelDetail.IsReassign)
			require.True(tt, order.CancelDetail.CancellationRateFree)
			require.Equal(tt, "WMA", order.CancelDetail.Source)
			require.Equal(tt, "<EMAIL>", order.CancelDetail.Requestor)
			require.Equal(tt, "Comment", order.CancelDetail.Remark)

			require.Len(tt, order.CancelDetailLog, 1)
			require.Equal(tt, model.CancelDetailLog{
				CancelDetail: model.CancelDetail{
					Source: "Test",
				},
				LogAt: timeutils.Now().UTC(),
			}, order.CancelDetailLog[0])
		})

		url := fmt.Sprintf("/v1/admin/orders/%s/update-cancel-reason", mockOrderID)
		gctx, recorder := testutil.TestRequestContext("PUT", url, testutil.JSON(UpdateOrderCancelReasonRequest{
			CancelReasonID:      mockCancelReasonID.String(),
			CancellationSource:  "WMA",
			Requestor:           "<EMAIL>",
			CancelReasonComment: "Comment",
		}))

		gctx.Params = gin.Params{{Key: "orderID", Value: mockOrderID}}
		api.admapi.UpdateOrderCancelReason(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestAdminAPI_CompensateOrderDistance(t *testing.T) {
	t.Parallel()

	mockOrderID := "ORDER_ID"
	mockFormID := "FORM_ID"

	mockedOrder := &model.Order{
		Status: model.StatusCompleted,
		Region: "BKK",
	}

	makeReq := func(dto *DistanceCompensatoryRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx := testutil.NewContextWithRecorder()
		ctx.SetPOST("/v1/admin/compensate/order-distance")
		ctx.SetBody(testutil.JSON(dto))
		return ctx.GinCtx(), ctx.ResponseRecorder
	}

	t.Run("should return 400 when req is invalid", func(t *testing.T) {
		t.Parallel()

		adminAPI, done := newTestAdminAPI(t, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		ctx, recorder := makeReq(nil)
		adminAPI.admapi.CompensateOrderDistance(ctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should return 500 when unable to get pickup on-top", func(t *testing.T) {
		t.Parallel()

		adminAPI, done := newTestAdminAPI(t, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		dto := &DistanceCompensatoryRequest{
			OrderId:                      mockOrderID,
			NewDistance:                  10,
			Requester:                    "<EMAIL>",
			IsNotAllowZeroCompensateFare: true,
			FormId:                       mockFormID,
			FormSubtype:                  formsubmitv1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM,
		}
		ctx, recorder := makeReq(dto)

		adminAPI.repo.EXPECT().Get(ctx, mockOrderID).Return(mockedOrder, nil)
		adminAPI.onTopFareService.EXPECT().GetOnTopFareForPickupClaimForm(ctx, gomock.Any()).Return(nil, errors.New("unable to get pickup on-top "))

		adminAPI.admapi.CompensateOrderDistance(ctx)

		require.Equal(t, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return 400 when order has no pickup on-top", func(t *testing.T) {
		t.Parallel()

		adminAPI, done := newTestAdminAPI(t, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()

		dto := &DistanceCompensatoryRequest{
			OrderId:                      mockOrderID,
			NewDistance:                  10,
			Requester:                    "<EMAIL>",
			IsNotAllowZeroCompensateFare: true,
			FormId:                       mockFormID,
			FormSubtype:                  formsubmitv1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM,
		}
		ctx, recorder := makeReq(dto)

		adminAPI.repo.EXPECT().Get(ctx, mockOrderID).Return(mockedOrder, nil)
		adminAPI.onTopFareService.EXPECT().GetOnTopFareForPickupClaimForm(ctx, gomock.Any()).Return([]model.OnTopFare{}, nil)

		adminAPI.admapi.CompensateOrderDistance(ctx)

		middlewares.ErrorMiddleware()(ctx)

		var res map[string]string
		testutil.DecodeJSON(t, recorder.Body, &res)

		require.Equal(t, http.StatusBadRequest, recorder.Code, "should return bad request")
		require.Contains(t, res["error"], "zero", "should contain 'zero' in error")
		require.Equal(t, res["error"], "INVALID_REQUEST: empty pick-up distance on-top (zero)")
	})

	t.Run("should return 201 when claim FORM_SUBTYPE_FAIR_DISPUTE_CLAIM success", func(t *testing.T) {
		t.Parallel()

		adminAPI, done := newTestAdminAPI(t, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()
		mockedOrder := &model.Order{
			OrderID: "mockOrder",
			Status:  model.StatusCompleted,
			Region:  "BKK",
			TripID:  "mockTrip",
			Quote: model.Quote{
				Routes: []model.Stop{
					{Location: model.Location{Lat: 1.4, Lng: 1.4}},
					{Location: model.Location{Lat: 1.5, Lng: 1.5}},
				},
			},
		}
		dto := &DistanceCompensatoryRequest{
			OrderId:                      mockOrderID,
			NewDistance:                  10,
			Requester:                    "<EMAIL>",
			IsNotAllowZeroCompensateFare: true,
			FormId:                       mockFormID,
			FormSubtype:                  formsubmitv1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM,
		}
		ctx, recorder := makeReq(dto)
		trip := model.Trip{
			TripID: "trip-C",
			Routes: model.TripRoutes{
				{StopOrders: model.TripStopOrders{{OrderID: "mockOrder"}}, Location: model.Location{Lat: 1.4, Lng: 1.4}},
				{StopOrders: model.TripStopOrders{{OrderID: "mockOrder"}}, Location: model.Location{Lat: 1.5, Lng: 1.5}, Action: model.TripActionDropOff, Distance: types.Distance(99)},
			},
			Orders: model.TripOrders{
				{OrderID: "mockOrder"},
				{OrderID: "mockOrder2"},
			},
			Status: model.TripStatusCompleted,
		}
		adminAPI.repo.EXPECT().Get(ctx, mockOrderID).Return(mockedOrder, nil)
		adminAPI.deliveryFeeService.EXPECT().GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DeliveryFeeSetting{}, model.SettingDeliveryFeePriceScheme{}, nil)
		adminAPI.tripRepo.EXPECT().GetTripByTripID(ctx, mockedOrder.TripID).Return(trip, nil)
		adminAPI.tripSvc.EXPECT().RecalculateTripDeliveryFee(ctx, gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, trip *model.Trip, opt service.RecalOptFn) error {
			trip.DriverWageSummary.TotalDriverWage = 100
			return nil
		})
		adminAPI.driverTransactionService.EXPECT().ProcessDriverTransaction(ctx, gomock.Any(), model.SystemTransactionChannel,
			model.WalletTopUpTransactionAction,
			model.SuccessTransactionStatus,
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil)
		adminAPI.repo.EXPECT().SetDistanceCompensatoryInfo(ctx, gomock.Any(), gomock.Any()).Return(nil)

		adminAPI.admapi.CompensateOrderDistance(ctx)

		middlewares.ErrorMiddleware()(ctx)

		var res map[string]string
		testutil.DecodeJSON(t, recorder.Body, &res)

		require.Equal(t, http.StatusCreated, recorder.Code, "should return StatusCreated")
	})

	t.Run("should return 400 when order has distance 0 on Routes.Distance", func(t *testing.T) {
		t.Parallel()

		adminAPI, done := newTestAdminAPI(t, OrderAPIConfig{}, &service.AtomicCancelReasonConfig{})
		defer done()
		mockedOrder := &model.Order{
			OrderID: "mockOrder",
			Status:  model.StatusCompleted,
			Region:  "BKK",
			TripID:  "mockTrip",
			Quote: model.Quote{
				Routes: []model.Stop{
					{Location: model.Location{Lat: 1.4, Lng: 1.4}},
					{Location: model.Location{Lat: 1.5, Lng: 1.5}},
				},
			},
		}
		dto := &DistanceCompensatoryRequest{
			OrderId:                      mockOrderID,
			NewDistance:                  10,
			Requester:                    "<EMAIL>",
			IsNotAllowZeroCompensateFare: true,
			FormId:                       mockFormID,
			FormSubtype:                  formsubmitv1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM,
		}
		ctx, recorder := makeReq(dto)
		trip := model.Trip{
			TripID: "trip-C",
			Routes: model.TripRoutes{
				{StopOrders: model.TripStopOrders{{OrderID: "mockOrder"}}, Location: model.Location{Lat: 1.4, Lng: 1.4}},
				{StopOrders: model.TripStopOrders{{OrderID: "mockOrder"}}, Location: model.Location{Lat: 1.5, Lng: 1.5}, Action: model.TripActionDropOff, Distance: types.Distance(0)},
			},
			Orders: model.TripOrders{
				{OrderID: "mockOrder"},
				{OrderID: "mockOrder2"},
			},
			Status: model.TripStatusCompleted,
		}
		adminAPI.repo.EXPECT().Get(ctx, mockOrderID).Return(mockedOrder, nil)
		adminAPI.deliveryFeeService.EXPECT().GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DeliveryFeeSetting{}, model.SettingDeliveryFeePriceScheme{}, nil)
		adminAPI.tripRepo.EXPECT().GetTripByTripID(ctx, mockedOrder.TripID).Return(trip, nil)
		adminAPI.tripSvc.EXPECT().RecalculateTripDeliveryFee(ctx, gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, trip *model.Trip, opt service.RecalOptFn) error {
			trip.DriverWageSummary.TotalDriverWage = 100
			return nil
		}).Times(0)
		adminAPI.driverTransactionService.EXPECT().ProcessDriverTransaction(ctx, gomock.Any(), model.SystemTransactionChannel,
			model.WalletTopUpTransactionAction,
			model.SuccessTransactionStatus,
			gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, []model.Transaction{}, nil).Times(0)
		adminAPI.repo.EXPECT().SetDistanceCompensatoryInfo(ctx, gomock.Any(), gomock.Any()).Return(nil).Times(0)

		adminAPI.admapi.CompensateOrderDistance(ctx)

		middlewares.ErrorMiddleware()(ctx)

		var res map[string]string
		testutil.DecodeJSON(t, recorder.Body, &res)

		require.Equal(t, http.StatusBadRequest, recorder.Code, "should return StatusCreated")
		require.Contains(t, res["error"], "zero", "should contain 'zero' in error")
		require.Equal(t, res["error"], "INVALID_REQUEST: compensate fare is not changed (zero)")
	})
}
