package distribution

//go:generate mockgen -source=assigning_state.go -destination=./mock_assigning_state/mock_assigning_state.go -package=mock_assigning_state

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"

	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
)

// AssigningState keep the state of each assigning round in case optimize plan has multiple candidates
type AssigningState struct {
	DriverQueue      DriverQueueMinimal    `json:"driverQueue"`
	AssigningSetting AssigningSettingState `json:"assigningSettingState"`
}

func (as *AssigningState) IsUsable(curDistCfgRevision string) bool {
	return as != nil && !as.DriverQueue.IsEmpty() && curDistCfgRevision != "" && curDistCfgRevision == as.AssigningSetting.DistCfgRevision
}

func NewAssigningState(driverQueue DriverQueueMinimal, assigningSetting AssigningSettingState) AssigningState {
	return AssigningState{
		DriverQueue:      driverQueue,
		AssigningSetting: assigningSetting,
	}
}

func AssigningStateKey(orderID string, deliveringRound int) string {
	return fmt.Sprintf("assigning_state:%s-%d", orderID, deliveringRound)
}

type AssigningStateManager interface {
	GetAndRemoveAssigningState(ctx context.Context, orderID string, deliveringRound int) (*AssigningState, error)
	RemoveAssigningState(ctx context.Context, orderID string, deliveringRound int) error
	SetAssigningState(ctx context.Context, assigningState AssigningState, orderID string, deliveringRound int, ttl time.Duration) error
}

func ProvideAssigningStateManager(redisClient datastore.RedisClient) AssigningStateManager {
	return &AssigningStateManagerImpl{
		RedisClient: redisClient,
	}
}

type AssigningStateManagerImpl struct {
	RedisClient datastore.RedisClient
}

func (a AssigningStateManagerImpl) GetAndRemoveAssigningState(ctx context.Context, orderID string, deliveringRound int) (*AssigningState, error) {
	key := AssigningStateKey(orderID, deliveringRound)
	res, err := a.RedisClient.Get(ctx, key).Result()
	if err != nil {
		if errors.Is(err, redis.Nil) { // key doesn't exist
			return nil, nil
		} else {
			return nil, err
		}
	}

	if res == "" {
		return nil, nil
	}

	_, err = a.RedisClient.Del(ctx, key).Result()
	if err != nil {
		return nil, err
	}

	var assigningState AssigningState
	if err := json.Unmarshal([]byte(res), &assigningState); err != nil {
		return nil, err
	}

	return &assigningState, nil
}

func (a AssigningStateManagerImpl) RemoveAssigningState(ctx context.Context, orderID string, deliveringRound int) error {
	key := AssigningStateKey(orderID, deliveringRound)
	_, err := a.RedisClient.Del(ctx, key).Result()
	return err
}

func (a AssigningStateManagerImpl) SetAssigningState(ctx context.Context, assigningState AssigningState, orderID string, deliveringRound int, ttl time.Duration) error {
	if assigningState.DriverQueue.IsEmpty() {
		return a.RemoveAssigningState(ctx, orderID, deliveringRound)
	}

	key := AssigningStateKey(orderID, deliveringRound)
	value, err := json.Marshal(assigningState)
	if err != nil {
		return err
	}
	if _, err := a.RedisClient.Set(ctx, key, value, ttl).Result(); err != nil {
		return err
	}
	return nil
}
