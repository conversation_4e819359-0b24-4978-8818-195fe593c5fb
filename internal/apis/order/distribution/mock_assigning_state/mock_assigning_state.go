// Code generated by MockGen. DO NOT EDIT.
// Source: assigning_state.go

// Package mock_assigning_state is a generated GoMock package.
package mock_assigning_state

import (
	context "context"
	reflect "reflect"
	time "time"

	distribution "git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	gomock "github.com/golang/mock/gomock"
)

// MockAssigningStateManager is a mock of AssigningStateManager interface.
type MockAssigningStateManager struct {
	ctrl     *gomock.Controller
	recorder *MockAssigningStateManagerMockRecorder
}

// MockAssigningStateManagerMockRecorder is the mock recorder for MockAssigningStateManager.
type MockAssigningStateManagerMockRecorder struct {
	mock *MockAssigningStateManager
}

// NewMockAssigningStateManager creates a new mock instance.
func NewMockAssigningStateManager(ctrl *gomock.Controller) *MockAssigningStateManager {
	mock := &MockAssigningStateManager{ctrl: ctrl}
	mock.recorder = &MockAssigningStateManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssigningStateManager) EXPECT() *MockAssigningStateManagerMockRecorder {
	return m.recorder
}

// GetAndRemoveAssigningState mocks base method.
func (m *MockAssigningStateManager) GetAndRemoveAssigningState(ctx context.Context, orderID string, deliveringRound int) (*distribution.AssigningState, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAndRemoveAssigningState", ctx, orderID, deliveringRound)
	ret0, _ := ret[0].(*distribution.AssigningState)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAndRemoveAssigningState indicates an expected call of GetAndRemoveAssigningState.
func (mr *MockAssigningStateManagerMockRecorder) GetAndRemoveAssigningState(ctx, orderID, deliveringRound interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAndRemoveAssigningState", reflect.TypeOf((*MockAssigningStateManager)(nil).GetAndRemoveAssigningState), ctx, orderID, deliveringRound)
}

// RemoveAssigningState mocks base method.
func (m *MockAssigningStateManager) RemoveAssigningState(ctx context.Context, orderID string, deliveringRound int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveAssigningState", ctx, orderID, deliveringRound)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveAssigningState indicates an expected call of RemoveAssigningState.
func (mr *MockAssigningStateManagerMockRecorder) RemoveAssigningState(ctx, orderID, deliveringRound interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveAssigningState", reflect.TypeOf((*MockAssigningStateManager)(nil).RemoveAssigningState), ctx, orderID, deliveringRound)
}

// SetAssigningState mocks base method.
func (m *MockAssigningStateManager) SetAssigningState(ctx context.Context, assigningState distribution.AssigningState, orderID string, deliveringRound int, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAssigningState", ctx, assigningState, orderID, deliveringRound, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAssigningState indicates an expected call of SetAssigningState.
func (mr *MockAssigningStateManagerMockRecorder) SetAssigningState(ctx, assigningState, orderID, deliveringRound, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAssigningState", reflect.TypeOf((*MockAssigningStateManager)(nil).SetAssigningState), ctx, assigningState, orderID, deliveringRound, ttl)
}
