package distribution_test

import (
	"testing"
	"time"

	"github.com/golang/mock/gomock"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestAutoAssign_RedistributionEventMessage(t *testing.T) {
	t.Parallel()

	withNotifyInBackgroundConfig := func(cfg dispatcherconfig.AutoAssignDbConfig) dispatcherconfig.AutoAssignDbConfig {
		// override config
		return cfg
	}

	t.Run("single - redistribute with kafka", func(tt *testing.T) {
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: true}))
		defer finishFn()
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
		_, wg, o, expireAt, businessLocation, regions := createStandingData()
		ctx, wg := safe.CreateCtxWithWaitGroup()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{}, model.StrategyNameFleetPool)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, time.Duration(0), true)

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), o.OrderID, types.Ptr(0)).Return(nil)

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 0.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER", "0", model.RegionsString(regions))
	})

	t.Run("batch - redistribute with kafka", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("ord-1"),
			createPredictedOrder("ord-2"),
			createPredictedOrder("ord-3"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)
		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(false)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameDirectSearch,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)

		// redistribute from defer in ProcessBatch
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, nil).Return(nil)
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, allOrderIDs, zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 3, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 0, zone.ZoneCode)
	})
}
