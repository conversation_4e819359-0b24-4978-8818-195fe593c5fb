package distribution_test

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/uber/h3-go"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetorder"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestAutoAssign_Distribute(t *testing.T) {
	t.Parallel()

	t.Run("happy path", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableDistributeAnyOrdersToTester: false, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mocks.mockOrderDistributionEventManager = mock_service.NewMockOrderDistributionEventManager(ctrl)
		autoAssignOrderDistributor.AutoAssignOrderDistributorDeps.OrderDistributionEventManager = mocks.mockOrderDistributionEventManager

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createTestDriver("T", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameFleetPool,
		)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "T", "3", "U"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		firstEvent := mocks.mockOrderDistributionEventManager.EXPECT().PublishSearchingOrAssigningDriver(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
			require.Equal(tt, o.OrderID, order.OrderID)
			require.Nil(tt, order.ActualAssigningAt)
			require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
			return nil
		})
		mocks.mockOrderDistributionEventManager.EXPECT().PublishSearchingOrAssigningDriver(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
			require.Equal(tt, o.OrderID, order.OrderID)
			require.NotNil(tt, order.ActualAssigningAt)
			require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
			return nil
		}).After(firstEvent).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 5, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("happy path with ENABLE_DISTRIBUTE_ANY_ORDERS_TO_TESTER = true", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableDistributeAnyOrdersToTester: true, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createTestDriver("T", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameDirectSearch,
		)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "T", "3", "U"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("T"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		expectNoAssignedState(ctx, mocks, "T", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "T", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "T")
		expectUnlockDriver(ctx, mocks, "T")
		expectNotify(tt, ctx, mocks, o.OrderID, "T", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "T"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 5, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("happy path with prediction - food", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameFleetPool,
		)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("happy path with prediction - bike", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.BikeB2BEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createBikeStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameDirectSearch,
		)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriveTo + "_0"
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("happy path with rain", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableDistributeAnyOrdersToTester: false, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), gomock.Any()).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, true)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createTestDriver("T", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameFleetPool,
		)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "T", "3", "U"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversWithOpt(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.AssignmentLogOpt{AutoAssigned: true, HasRained: true, SearchRiderStrategy: model.StrategyNameFleetPool, Region: model.DistributeRegions(regions).DefaultRegion()})
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotifyWithRain(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 5, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("assign to rider with auto accept disable", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = false
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameDirectSearch,
		)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: "3"}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("assign to rider with auto accept disable but order got cancelled", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = false
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameFleetPool,
		)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: "3"}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusCanceled}}, nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_ORDER_CANCELED", "1", model.RegionsString(regions))
	})
	t.Run("assign to rider with auto accept disable but order got cancelled after assigning (batch assignment enabled)", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(
			tt,
			dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true},
		)
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.BatchAssignmentEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = false
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameDirectSearch,
		)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: "3"}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)

		mocks.mockAssignmentRepository.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, assignment *model.Assignment, _ ...repository.Option) error {
			assignment.AssignmentID = "ass-1"
			return nil
		})

		expectAssignToDriversQueueBatchAssignmentEnabled(ctx, mocks, 1, "ass-1", o.OrderID, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotifyWithAssignmentID(tt, ctx, mocks, "ass-1", []string{o.OrderID}, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockAssignmentRepository.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(&model.Assignment{IsDetailIrretrievable: true}, nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusCanceled
			return []model.Order{o}, nil
		})
		expectUnassignToDriver(ctx, mocks, o.OrderID, "3")

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_ORDER_CANCELED", "1", model.RegionsString(regions))
	})
	t.Run("assign to rider but unable to accept (batch assignment enabled)", func(tt *testing.T) {
		tt.Parallel()

		testCases := []struct {
			name                string
			givenAssignment     *model.Assignment
			expectUnassignOrder bool
			expectForceOffline  bool
			searchRiderStrategy model.SearchRiderStrategy
		}{
			{
				name: "assignment with detail irretrievable should be unassigned",
				givenAssignment: &model.Assignment{
					AssignmentID:          "ass-1",
					IsDetailIrretrievable: true,
				},
				expectUnassignOrder: true,
				expectForceOffline:  false,
				searchRiderStrategy: model.StrategyNameDirectSearch,
			},
			{
				name: "assignment without ack should be unassigned",
				givenAssignment: &model.Assignment{
					AssignmentID: "ass-1",
					Ack:          nil,
				},
				expectUnassignOrder: true,
				expectForceOffline:  false,
				searchRiderStrategy: model.StrategyNameFleetPool,
			},
			{
				name: "assignment with ack app visible should not be unassigned, but forced offline",
				givenAssignment: &model.Assignment{
					AssignmentID: "ass-1",
					Ack:          &model.AssignmentAck{IsAppVisible: true},
				},
				expectUnassignOrder: false,
				expectForceOffline:  true,
				searchRiderStrategy: model.StrategyNameDirectSearch,
			},
			{
				name: "assignment with ack app not visible should not be unassigned, but forced offline",
				givenAssignment: &model.Assignment{
					AssignmentID: "ass-1",
					Ack:          &model.AssignmentAck{IsAppVisible: false},
				},
				expectUnassignOrder: false,
				expectForceOffline:  true,
				searchRiderStrategy: model.StrategyNameFleetPool,
			},
		}

		for _, tc := range testCases {
			tt.Run(tc.name, func(tt *testing.T) {
				tt.Parallel()
				autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(
					tt,
					dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true},
				)
				distCfg.PredictionServiceEnabled = true
				distCfg.AssignmentType = prediction.AssignmentTypeSingle
				distCfg.NotifyViaSocketIOEnabled = true
				distCfg.BatchAssignmentEnabled = true
				autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = false
				autoAssignOrderDistributor.OrderConfig.ForceOfflineEnabled = true
				autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0

				defer finishFn()

				ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

				mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
				mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
				expectEvaluateRainSituation(ctx, mocks, true, false)
				expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
				initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
					[]service.DriverWithLocation{
						createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
					}, tc.searchRiderStrategy)
				expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
				expectGetAssignedDrivers(ctx, mocks, o.OrderID)
				initDriverStatistics(ctx, mocks, []string{"3"})
				setDriverTransaction(ctx, mocks, createDriverTransaction("3"))

				mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
					order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
					return nil
				})
				mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
						return riders, nil
					})
				mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
					assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
					assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
					return nil
				})
				mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
					assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
					assert.Empty(tt, setting.MOType)
					return prediction.RouteResponse{RiderID: "3"}, nil, nil
				})
				mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
				mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)

				expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)

				mocks.mockAssignmentRepository.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, assignment *model.Assignment, _ ...repository.Option) error {
					assignment.AssignmentID = "ass-1"
					return nil
				})

				expectAssignToDriversQueueBatchAssignmentEnabled(ctx, mocks, 1, "ass-1", o.OrderID, "3", 1, tc.searchRiderStrategy, model.DistributeRegions(regions).DefaultRegion())
				expectLockDriver(ctx, mocks, "3")
				expectUnlockDriver(ctx, mocks, "3")
				expectNotifyWithAssignmentID(tt, ctx, mocks, "ass-1", []string{o.OrderID}, "3", service.WithFirebase, service.WithSocketIO)
				expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "3")
				expectEventPublish(ctx, mocks)
				expectZoneCall(mocks)
				expectOnTopFareCall(mocks, []model.OnTopFare{})
				mocks.mockAssignmentRepository.EXPECT().FindByID(gomock.Any(), gomock.Any()).Return(tc.givenAssignment, nil)
				mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
					o.Status = model.StatusAssigningDriver
					o.ExpireAt = time.Now().Add(24 * time.Hour)
					return []model.Order{o}, nil
				})

				if tc.expectUnassignOrder {
					expectUnassignToDriver(ctx, mocks, o.OrderID, "3")
				}

				if tc.expectForceOffline {
					expectForceOffline(ctx, mocks, "3", o.OrderID, tc.givenAssignment.AssignmentID)
				}

				mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
				mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
				mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
				mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
				mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

				stubDefaultBehavior(mocks)

				_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
					NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
				})

				autoAssignOrderDistributor.Stop()
				wg.Wait()

				assertFetchedMetric(tt, mocks, 1, model.RegionsString(regions))
				assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "2", model.RegionsString(regions))
			})
		}
	})
	t.Run("assign to rider with auto accept enable", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		expectedDriver := "3"
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, _ interface{}, setting service.ValidatePlanRouteSetting) (model.RiderFilterName, error) {
				require.Nil(tt, setting.FirstDistanceLimitByServices)
				return "", nil
			})

		expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver).Return(nil)
		expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		acceptOrderFnCalled := false
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			assert.Equal(tt, "3", driverID)
			acceptOrderFnCalled = true
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "3", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		assert.True(tt, acceptOrderFnCalled)
	})
	t.Run("assign to rider with auto accept fails (should not lose AR)", func(tt *testing.T) {
		tt.Parallel()

		expectedDriver := "3"

		expectMock := func(tt *testing.T, autoAssignOrderDistributor *distribution.AutoAssignOrderDistributor, mocks *mocks, distCfg model.AutoAssignDistribution) (context.Context, *sync.WaitGroup, model.Order, time.Time, model.Location, []model.RegionCode) {
			ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
			mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
			expectEvaluateRainSituation(ctx, mocks, true, false)
			expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
			initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
				[]service.DriverWithLocation{
					createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
					createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
				}, model.StrategyNameFleetPool)
			expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
			expectGetAssignedDrivers(ctx, mocks, o.OrderID)
			initDriverStatistics(ctx, mocks, []string{expectedDriver, "U"})

			mocks.mockPredictionService.EXPECT().Predict(ctx, gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
				order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
				return nil
			})
			mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
					return riders, nil
				})
			mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
				assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
				assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
				return nil
			})
			mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
				assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
				assert.Empty(tt, setting.MOType)
				return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
			})
			mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
			setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
			setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
			expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)

			return ctx, wg, o, expireAt, businessLocation, regions
		}

		tt.Run("lock fails", func(ttt *testing.T) {
			ttt.Parallel()
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(ttt, order.ContingencyConfig{})
			distCfg.PredictionServiceEnabled = true
			distCfg.AssignmentType = prediction.AssignmentTypeSingle
			distCfg.NotifyViaSocketIOEnabled = true
			autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
			autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
			defer finishFn()

			ctx, wg, o, expireAt, businessLocation, regions := expectMock(ttt, autoAssignOrderDistributor, mocks, distCfg)

			expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
			expectEventPublish(ctx, mocks)
			expectZoneCall(mocks)
			expectOnTopFareCall(mocks, []model.OnTopFare{})
			mocks.mockStatisticService.EXPECT().UpdateNotified(gomock.Any(), o.OrderID, expectedDriver).Return(nil)

			mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(errors.New("an error occurred"))
			mocks.mockAssigmentLogRepo.EXPECT().UnassignToDriver(gomock.Any(), o.OrderID, o.DeliveringRound, expectedDriver, gomock.Any())

			acceptOrderFnCalled := false
			*autoAssignOrderDistributor.AcceptOrderFn = func(
				ctx context.Context,
				order *model.Order,
				orderID,
				driverID string,
				syncDelivery bool,
			) (*model.AcceptedOrderInfo, error) {
				assert.Equal(ttt, "3", driverID)
				acceptOrderFnCalled = true
				return &model.AcceptedOrderInfo{Order: &o}, nil
			}

			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())

			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertFetchedMetric(ttt, mocks, 2, model.RegionsString(regions))
			assertStopMetric(ttt, mocks, "FAIL_NO_DRIVER_MATCHED", "2", model.RegionsString(regions))
			assert.False(ttt, acceptOrderFnCalled)
		})

		tt.Run("lock fails with batch assignment enabled", func(ttt *testing.T) {
			ttt.Parallel()
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(
				ttt,
				dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true},
			)
			distCfg.PredictionServiceEnabled = true
			distCfg.AssignmentType = prediction.AssignmentTypeSingle
			distCfg.NotifyViaSocketIOEnabled = true
			distCfg.BatchAssignmentEnabled = true
			autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
			autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
			defer finishFn()

			ctx, wg, o, expireAt, businessLocation, regions := expectMock(ttt, autoAssignOrderDistributor, mocks, distCfg)

			mocks.mockAssignmentRepository.EXPECT().Create(gomock.Any(), gomock.Any())
			expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
			expectEventPublish(ctx, mocks)
			expectZoneCall(mocks)
			expectOnTopFareCall(mocks, []model.OnTopFare{})
			mocks.mockStatisticService.EXPECT().UpdateNotified(gomock.Any(), o.OrderID, expectedDriver).Return(nil)

			mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(errors.New("an error occurred"))
			mocks.mockAssigmentLogRepo.EXPECT().UnassignToDriver(gomock.Any(), o.OrderID, o.DeliveringRound, expectedDriver, gomock.Any())

			acceptOrderFnCalled := false
			*autoAssignOrderDistributor.AcceptOrderFn = func(
				ctx context.Context,
				order *model.Order,
				orderID,
				driverID string,
				syncDelivery bool,
			) (*model.AcceptedOrderInfo, error) {
				assert.Equal(ttt, "3", driverID)
				acceptOrderFnCalled = true
				return &model.AcceptedOrderInfo{Order: &o}, nil
			}

			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())

			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertFetchedMetric(ttt, mocks, 2, model.RegionsString(regions))
			assertStopMetric(ttt, mocks, "FAIL_NO_DRIVER_MATCHED", "2", model.RegionsString(regions))
			assert.False(ttt, acceptOrderFnCalled)
		})

		tt.Run("order status not ASSIGNING", func(ttt *testing.T) {
			ttt.Parallel()
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(ttt, order.ContingencyConfig{})
			distCfg.PredictionServiceEnabled = true
			distCfg.AssignmentType = prediction.AssignmentTypeSingle
			distCfg.NotifyViaSocketIOEnabled = true
			autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
			autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
			defer finishFn()

			ctx, wg, o, expireAt, businessLocation, regions := expectMock(ttt, autoAssignOrderDistributor, mocks, distCfg)
			o.Status = model.StatusCanceled

			expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
			expectEventPublish(ctx, mocks)
			expectZoneCall(mocks)
			expectOnTopFareCall(mocks, []model.OnTopFare{})
			mocks.mockStatisticService.EXPECT().UpdateNotified(gomock.Any(), o.OrderID, expectedDriver).Return(nil)

			mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
			mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver)
			mocks.mockAssigmentLogRepo.EXPECT().UnassignToDriver(gomock.Any(), o.OrderID, o.DeliveringRound, expectedDriver, gomock.Any())

			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertFetchedMetric(ttt, mocks, 2, model.RegionsString(regions))
			assertStopMetric(ttt, mocks, "FAIL_ORDER_CANCELED", "1", model.RegionsString(regions))
		})

		tt.Run("sync delivery fails", func(ttt *testing.T) {
			ttt.Parallel()
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(ttt, order.ContingencyConfig{})
			distCfg.PredictionServiceEnabled = true
			distCfg.AssignmentType = prediction.AssignmentTypeSingle
			distCfg.NotifyViaSocketIOEnabled = true
			autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
			autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
			defer finishFn()

			ctx, wg, o, expireAt, businessLocation, regions := expectMock(ttt, autoAssignOrderDistributor, mocks, distCfg)

			expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
			expectEventPublish(ctx, mocks)
			expectZoneCall(mocks)
			expectOnTopFareCall(mocks, []model.OnTopFare{})
			mocks.mockStatisticService.EXPECT().UpdateNotified(gomock.Any(), o.OrderID, expectedDriver).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetAsDistributed(gomock.Any(), o.OrderID)

			mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
			mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver)

			acceptOrderFnCalled := false
			*autoAssignOrderDistributor.AcceptOrderFn = func(
				ctx context.Context,
				order *model.Order,
				orderID,
				driverID string,
				syncDelivery bool,
			) (*model.AcceptedOrderInfo, error) {
				assert.Equal(ttt, "3", driverID)
				acceptOrderFnCalled = true
				return nil, errors.New("mock error")
			}

			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertFetchedMetric(ttt, mocks, 2, model.RegionsString(regions))
			assertStopMetric(ttt, mocks, "FAIL_NO_DRIVER_MATCHED", "2", model.RegionsString(regions))
			assert.True(ttt, acceptOrderFnCalled)
		})
	})
	t.Run("assign to rider with auto accept enable, skip driver when cannot acquire lock driver", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectedDriver := "3"

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
		setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(errors.New("something went wrong"))
		mocks.mockAssigmentLogRepo.EXPECT().UnassignToDriver(gomock.Any(), o.OrderID, o.DeliveringRound, expectedDriver, gomock.Any())
		mocks.mockStatisticService.EXPECT().UpdateNotified(gomock.Any(), o.OrderID, expectedDriver).Return(nil)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: "2"}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 2, o.OrderID, "2", 600, nil, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), "2").Return(nil)

		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), "2").Return(nil)
		expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "2", service.WithFirebase, service.WithSocketIO)

		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		acceptOrderFnCalled := false
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			assert.Equal(tt, "2", driverID)
			acceptOrderFnCalled = true
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "2", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "2", model.RegionsString(regions))
		assert.True(tt, acceptOrderFnCalled)
	})
	t.Run("assign to rider with auto accept enable with advanced restaurant blacklist", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.PredictionRestaurantBlacklistTimeSlots = model.RestaurantBlacklistTimeSlots{{
			BlacklistOption: model.BlacklistMO,
			Time: model.StartEndTime{
				Begin: "00:00:00",
				End:   "23:59:59",
			},
			IDs: []string{"restaurant-1"},
		}}
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		defer finishFn()

		restaurantID := "restaurant-1"
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = restaurantID

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectedDriver := "3"

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(ctx, gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeMultiple, setting.AssignmentType)
			require.True(tt, setting.RestaurantBlacklistSets.HasAnyWithOption(model.BlacklistMO, restaurantID))
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)

		expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), gomock.Any()).Return(nil)
		expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		acceptOrderFnCalled := false
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			assert.Equal(tt, "3", driverID)
			acceptOrderFnCalled = true
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "3", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		assert.True(tt, acceptOrderFnCalled)
	})
	t.Run("not perform prediction service on blacklist", func(tt *testing.T) {
		tt.Parallel()

		nowAllowID := "NOT_ALLOW_ID"
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true

		autoAssignOrderDistributor.Config.AtomicBackToBackConfig.Config.BackToBackNotAllowIDs = []string{nowAllowID}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = nowAllowID

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not perform prediction service on restaurant not in whitelist", func(tt *testing.T) {
		tt.Parallel()

		nowAllowID := "NOT_ALLOW_ID"
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"someotherid"}

		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = nowAllowID

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)

		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		mocks.mockAssigmentLogRepo.EXPECT().AssignedDrivers(gomock.Any(), o.OrderID, gomock.Any()).Return(nil, nil)
		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not perform prediction service on mart", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true

		defer finishFn()

		ctx, wg, od, expireAt, businessLocation, regions := createStandingData()
		od.ServiceType = model.ServiceMart

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), od.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), od.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, od.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectGetAssignedDrivers(ctx, mocks, od.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectLockAndUnlockAssigningOrder(ctx, mocks, od.OrderID, gomock.Any(), true)
		expectNoAssignedState(ctx, mocks, "3", od.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, od.OrderID, 0, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, od.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{od.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			od.Status = model.StatusDriverMatched
			od.Driver = "3"

			return []model.Order{od}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), od.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), od.OrderID).Return(nil).AnyTimes()
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &od, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("principal model not distribute cash collection order to ban withdraw and driver who not enough money in contingency mode", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{ContingencyModeEnabled: true, LimitMinusCredit: -1000})
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Quote.Options.DriverMoneyFlow = model.FlowCashCollection
		o.Quote.RevenuePrincipalModel = true

		driver1ID := "driver_1"
		driver2ID := "driver_2"
		driver3ID := "driver_3"
		driverBannedID := "driver_banned"
		driverNotEnoughMoneyID := "driver_not_enough_money"
		driverFraudID := "driver_fraud"

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver(driver1ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver(driver2ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(driver3ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 5),
				createDriverWithLocation(createDriver(driverBannedID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 2),
				createDriverWithLocation(createDriver(driverNotEnoughMoneyID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver(driverFraudID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{driver1ID, driver2ID, driver3ID, driverBannedID, driverNotEnoughMoneyID, driverFraudID})

		driver1Trans := createDriverTransaction(driver1ID)
		driver2Trans := createDriverTransaction(driver2ID)
		driver3Trans := createDriverTransaction(driver3ID)
		driverFraudTrans := createDriverTransaction(driverFraudID)
		driverBannedTrans := createBanWithdrawDriverTransaction(driverBannedID)
		driverNotEnoughMoneyTrans := createDriverTransaction(driverNotEnoughMoneyID)
		driverNotEnoughMoneyTrans.WalletBalance = types.NewMoney(0)
		driverNotEnoughMoneyTrans.PurchaseCreditBalance = types.NewMoney(-1100)

		setDriverTransaction(ctx, mocks, driver1Trans)
		setDriverTransaction(ctx, mocks, driver2Trans)
		setDriverTransaction(ctx, mocks, driver3Trans)
		setDriverTransaction(ctx, mocks, driverBannedTrans)
		setDriverTransaction(ctx, mocks, driverNotEnoughMoneyTrans)
		setDriverTransaction(ctx, mocks, driverFraudTrans)

		// call in checkBanWithdraw
		expectedGetDriverTransaction(ctx, mocks, driverNotEnoughMoneyID, driverNotEnoughMoneyTrans)

		// call in calculateCreditWalletIsEnough
		expectedGetDriverTransaction(ctx, mocks, driverFraudID, driverFraudTrans)
		expectedGetDriverTransaction(ctx, mocks, driver1ID, driver1Trans)
		expectedGetDriverTransaction(ctx, mocks, driver2ID, driver2Trans)
		expectedGetDriverTransaction(ctx, mocks, driver3ID, driver3Trans)

		expectNoAssignedState(ctx, mocks, driver3ID, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, driver3ID, 5, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, driver3ID)
		expectUnlockDriver(ctx, mocks, driver3ID)
		expectNotify(tt, ctx, mocks, o.OrderID, driver3ID, service.WithFirebase)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = driver3ID

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 6, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("principal model not distribute order to driver who not enough money with disable negative balance feature", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{ContingencyModeEnabled: false})
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Quote.RevenuePrincipalModel = true
		o.PayAtStop = 1
		o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCreditCard
		o.Routes[o.GetPayAtStop()].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCreditCard
		o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee.RawBaseFee = 20
		o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee.BaseFee = 20
		o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee.OnTopFare = 10

		driver1ID := "driver_1"
		driver2ID := "driver_2"
		driver3ID := "driver_3"
		driverNotEnoughMoneyID := "driver_not_enough_money"
		driverFraudID := "driver_fraud"

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver(driver1ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver(driver2ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(driver3ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 5),
				createDriverWithLocation(createDriver(driverNotEnoughMoneyID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver(driverFraudID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{driver1ID, driver2ID, driver3ID, driverNotEnoughMoneyID, driverFraudID})

		driver1Trans := createDriverTransaction(driver1ID)
		driver1Trans.WalletBalance = types.NewMoney(0)
		driver1Trans.PurchaseCreditBalance = types.NewMoney(50)
		driver2Trans := createDriverTransaction(driver2ID)
		driver2Trans.WalletBalance = types.NewMoney(0)
		driver2Trans.PurchaseCreditBalance = types.NewMoney(50)
		driver3Trans := createDriverTransaction(driver3ID)
		driver3Trans.WalletBalance = types.NewMoney(0)
		driver3Trans.PurchaseCreditBalance = types.NewMoney(50)
		driverFraudTrans := createDriverTransaction(driverFraudID)
		driverNotEnoughMoneyTrans := createDriverTransaction(driverNotEnoughMoneyID)
		driverNotEnoughMoneyTrans.WalletBalance = types.NewMoney(0)
		driverNotEnoughMoneyTrans.PurchaseCreditBalance = types.NewMoney(-47.12)

		expectedGetDriverTransaction(ctx, mocks, driverFraudID, driverFraudTrans)
		expectedGetDriverTransaction(ctx, mocks, driverNotEnoughMoneyID, driverNotEnoughMoneyTrans)
		expectedGetDriverTransaction(ctx, mocks, driver1ID, driver1Trans)
		expectedGetDriverTransaction(ctx, mocks, driver2ID, driver2Trans)
		expectedGetDriverTransaction(ctx, mocks, driver3ID, driver3Trans)

		expectNoAssignedState(ctx, mocks, driver3ID, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, driver3ID, 5, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, driver3ID)
		expectUnlockDriver(ctx, mocks, driver3ID)
		expectNotify(tt, ctx, mocks, o.OrderID, driver3ID, service.WithFirebase)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = driver3ID

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 5, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("agent model not distribute order to driver who not enough money", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{ContingencyModeEnabled: false})
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Quote.RevenuePrincipalModel = false
		o.PayAtStop = 1
		o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee.PaymentMethod = model.PaymentMethodCreditCard
		o.Routes[o.GetPayAtStop()].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCreditCard
		o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee.Commission = 20
		o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee.WithholdingTax = 20

		driver1ID := "driver_1"
		driver2ID := "driver_2"
		driver3ID := "driver_3"
		driverNotEnoughMoneyID := "driver_not_enough_money"
		driverFraudID := "driver_fraud"

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver(driver1ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver(driver2ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(driver3ID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 5),
				createDriverWithLocation(createDriver(driverNotEnoughMoneyID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver(driverFraudID, model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{driver1ID, driver2ID, driver3ID, driverNotEnoughMoneyID, driverFraudID})

		driver1Trans := createDriverTransaction(driver1ID)
		driver1Trans.WalletBalance = types.NewMoney(0)
		driver1Trans.PurchaseCreditBalance = types.NewMoney(50)
		driver2Trans := createDriverTransaction(driver2ID)
		driver2Trans.WalletBalance = types.NewMoney(0)
		driver2Trans.PurchaseCreditBalance = types.NewMoney(50)
		driver3Trans := createDriverTransaction(driver3ID)
		driver3Trans.WalletBalance = types.NewMoney(0)
		driver3Trans.PurchaseCreditBalance = types.NewMoney(50)
		driverFraudTrans := createDriverTransaction(driverFraudID)
		driverNotEnoughMoneyTrans := createDriverTransaction(driverNotEnoughMoneyID)
		driverNotEnoughMoneyTrans.WalletBalance = types.NewMoney(0)
		driverNotEnoughMoneyTrans.PurchaseCreditBalance = types.NewMoney(-47.12)

		expectedGetDriverTransaction(ctx, mocks, driverFraudID, driverFraudTrans)
		expectedGetDriverTransaction(ctx, mocks, driverNotEnoughMoneyID, driverNotEnoughMoneyTrans)
		expectedGetDriverTransaction(ctx, mocks, driver1ID, driver1Trans)
		expectedGetDriverTransaction(ctx, mocks, driver2ID, driver2Trans)
		expectedGetDriverTransaction(ctx, mocks, driver3ID, driver3Trans)

		expectNoAssignedState(ctx, mocks, driver3ID, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, driver3ID, 5, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, driver3ID)
		expectUnlockDriver(ctx, mocks, driver3ID)
		expectNotify(tt, ctx, mocks, o.OrderID, driver3ID, service.WithFirebase)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = driver3ID

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 5, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("no driver", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		defer finishFn()
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 0.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER", "0", model.RegionsString(regions))
	})
	t.Run("redistribute non prediction once", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		defer finishFn()
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 1
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectZoneCall(mocks)
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 0.0, model.RegionsString(regions))
		assertStopMetrics(tt, mocks, "FAIL_NO_DRIVER", "0", model.RegionsString(regions), 1)
	})
	t.Run("first driver does not accept", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 100),
			}, model.StrategyNameFleetPool)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID, "3", "11")
		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID, "3", "11")
		initDriverStatistics(ctx, mocks, []string{"1", "2"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "2", 600, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "2")
		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)

		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		expectNotify(tt, ctx, mocks, o.OrderID, "1", service.WithFirebase)
		expectNoAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 2, o.OrderID, 0, "1", 1000, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "1")
		expectUnlockDriver(ctx, mocks, "1")

		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverToRestaurant
			o.Driver = "1"
			return []model.Order{o}, nil
		})
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "2", model.RegionsString(regions))
	})

	t.Run("first driver does not accept cash advancement e-payment", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Quote.Routes[0].PriceSummary.ItemFee.PaymentMethod = model.PaymentMethodCreditCard
		o.Quote.Options.DriverMoneyFlow = model.FlowCashAdvancementEpayment
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 100),
			}, model.StrategyNameFleetPool)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID, "3", "11")
		initDriverStatistics(ctx, mocks, []string{"1", "2"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "2", 600, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "2")
		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")

		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		expectNotify(tt, ctx, mocks, o.OrderID, "1", service.WithFirebase)
		expectNoAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID)
		expectAssignToDrivers(ctx, mocks, 2, o.OrderID, 0, "1", 1000, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "1")
		expectUnlockDriver(ctx, mocks, "1")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverToRestaurant
			o.Driver = "1"
			return []model.Order{o}, nil
		})

		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "2", model.RegionsString(regions))
	})
	t.Run("should stop distribution when search drivers error", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().
			GetExDrivers(gomock.Any(), gomock.Any()).
			Return(types.NewStringSet(), nil)
		mocks.mockLocationManager.EXPECT().
			SearchDriversLimit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(service.SearchResult{Results: nil}, errors.New("search driver error"))

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
	t.Run("no one accept", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		defer finishFn()
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 100),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 100, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "3")
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)

		expectAssignToDrivers(ctx, mocks, 2, o.OrderID, 0, "2", 600, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "2")
		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase)
		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		expectAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "3", model.RegionsString(regions))
	})

	t.Run("can distribute after clearing order in reassigning feature", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		expectDistribute := func(deliveringRound int) {
			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
			initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
				[]service.DriverWithLocation{
					createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
					createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
					createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
					createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
				}, model.StrategyNameFleetPool)
			mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
			expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
			expectGetAssignedDrivers(ctx, mocks, o.OrderID)
			initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
			setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
			setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
			setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
			setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

			expectEvaluateRainSituation(ctx, mocks, true, false)
			expectAssignmentLogCreated(ctx, mocks, o.OrderID, deliveringRound, model.DistributeRegions(regions).DefaultRegion())
			expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
			expectAssignToDrivers(ctx, mocks, 1, o.OrderID, deliveringRound, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())

			if deliveringRound == 0 {
				expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
			} else {
				expectNotifyWithoutSettingIsDistributed(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
			}

			expectEventPublish(ctx, mocks)
			expectZoneCall(mocks)
			expectOnTopFareCall(mocks, []model.OnTopFare{})
			expectLockDriver(ctx, mocks, "3")
			expectUnlockDriver(ctx, mocks, "3")
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
				o.Status = model.StatusDriverMatched
				o.Driver = "3"
				o.TripID = "mock-trip-id"
				return []model.Order{o}, nil
			})

			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})
			autoAssignOrderDistributor.Stop()
			wg.Wait()
			assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		}

		// delivering round 0
		expectDistribute(0)

		// delivering round 1
		o.ClearOrderForReassign(time.Minute*10, 0, 0.03)
		//  assert trip id is empty after clear
		require.Equal(tt, "", o.TripID)
		expectDistribute(1)
	})
	t.Run("unassign when order cancel", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})

		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectUnassignToDriver(ctx, mocks, o.OrderID, "3")
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectFindOneActive(ctx, mocks, o.OrderID, "3", false)
		expectEventPublish(ctx, mocks)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusCanceled
			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, fmt.Sprintf("FAIL_ORDER_%s", o.Status), "1", model.RegionsString(regions))
	})
	t.Run("do not unassign when order cancel because driver already tried to accept", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectFindOneActive(ctx, mocks, o.OrderID, "3", true)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusCanceled
			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, fmt.Sprintf("FAIL_ORDER_%s", o.Status), "1", model.RegionsString(regions))
	})
	t.Run("do not skip distributing to driver silent banned when silent ban disabled", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableSilentBan: false, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.ServiceType = model.ServiceMessenger

		silentBannedDriver := createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1)
		silentBannedDriver.Driver.ServiceTypesSilentBanned = []model.Service{model.ServiceMessenger}

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createTestDriver("T", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				silentBannedDriver,
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"2", "T", "3", "U"})

		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("T"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("skip distributing to driver silent banned when silent ban enabled", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableSilentBan: true, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.ServiceType = model.ServiceMessenger

		silentBannedDriver := createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1)
		silentBannedDriver.Driver.ServiceTypesSilentBanned = []model.Service{model.ServiceMessenger}

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createTestDriver("T", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				silentBannedDriver,
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"2", "T", "3", "U"})

		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("T"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "2", 600, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "2"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("do not skip distributing to driver silent banned when silent ban enabled on messenger but order is of type food", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableSilentBan: true, DisableThrottledDispatch: true, DisableRedistributeByDispatcher: true})
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		silentBannedDriver := createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1)
		silentBannedDriver.Driver.ServiceTypesSilentBanned = []model.Service{model.ServiceMessenger}

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createTestDriver("T", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				silentBannedDriver,
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"2", "T", "3", "U"})

		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("check for candidates successfully with 1 candidate required", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, _, o, _, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Driver = "3"

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)

		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1"})

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		candidatesEnough := autoAssignOrderDistributor.DistributorCheckIfCandidatesEnough(ctx, &o, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		}, 1)

		require.True(tt, candidatesEnough)
	})
	t.Run("check for candidates failed with 3 candidates required but only found 2", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, _, o, _, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				Drivers: []model.Record{
					{
						Round: "R1",
					},
				},
			},
		}, nil)

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		candidatesEnough := autoAssignOrderDistributor.DistributorCheckIfCandidatesEnough(ctx, &o, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		}, 3)

		require.False(tt, candidatesEnough)
	})
	t.Run("distribute to dedicate zone riders", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.RedistributionState = &model.RedistributionState{}
		oDistributed := o
		oDistributed.IsDistributed = true

		zoneMap := make(map[string]model.DedicatedZone)
		inZoneA := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneA.Label] = inZoneA

		inZoneB := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 0,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneB.Label] = inZoneB

		outZone := model.DedicatedZone{
			Label: "outZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {1, 0}, {1, 1}, {0, 1}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[outZone.Label] = outZone
		mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(zoneMap, nil).AnyTimes()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, o.DeliveringRound, model.DistributeRegions(regions).DefaultRegion())

		expectPredictCall(ctx, mocks, tt)
		dediA := createDriverWithLocation(createDriver("dediA", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1)
		dediA.Driver.DedicatedZones = []string{inZoneA.Label}
		dediA.Driver.ServiceTypes = []model.Service{model.ServiceMessenger}
		dediAB := createDriverWithLocation(createDriver("dediAB", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)
		dediAB.Driver.DedicatedZones = []string{inZoneA.Label, inZoneB.Label}
		normalA := createDriverWithLocation(createDriver("normalA", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 2000)
		normalA.Driver.DedicatedZones = []string{inZoneA.Label}
		outZoneRider := createDriverWithLocation(createDriver("outZone", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)
		outZoneRider.Driver.DedicatedZones = []string{outZone.Label}
		noZone := createDriverWithLocation(createDriver("noZone", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)

		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{dediA, dediAB, normalA, outZoneRider, noZone}, model.StrategyNameFleetPool)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"dediA", "dediAB", "normalA", "outZone", "noZone"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("dediA"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("dediAB"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("normalA"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("outZone"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("noZone"))

		expectOptimizeCall(tt, ctx, mocks, []string{"dediA", "dediAB"})
		expectNoAssignedState(ctx, mocks, "dediA", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectRouteCallWithValidation(ctx, mocks, tt, "dediA")
		expectAssignToDriversQueueDedicatedZonePrioritized(ctx, mocks, 1, o.OrderID, "dediA", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectNotify(tt, ctx, mocks, o.OrderID, "dediA", service.WithFirebase)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "dediA")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectLockDriver(ctx, mocks, "dediA")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil).Times(2)
		expectUnlockDriver(ctx, mocks, "dediA")
		expectEventPublish(ctx, mocks)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{oDistributed}, nil)

		expectNoAssignedState(ctx, mocks, "dediAB", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectRouteCallWithValidation(ctx, mocks, tt, "dediAB")
		expectAssignToDriversQueueDedicatedZonePrioritized(ctx, mocks, 2, o.OrderID, "dediAB", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectNotifyWithoutSettingIsDistributed(tt, ctx, mocks, o.OrderID, "dediAB", service.WithFirebase)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "dediAB")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectLockDriver(ctx, mocks, "dediAB")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{oDistributed}, nil).Times(2)
		expectUnlockDriver(ctx, mocks, "dediAB")
		expectEventPublish(ctx, mocks)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{oDistributed}, nil)

		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID)
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 5, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "3", model.RegionsString(regions))
	})
	t.Run("try to assign mo when enable optimization top-n", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.OptimizationTopN = 3
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.OrderID = "2"
		routeResp := prediction.RouteResponse{
			RiderID: "3",
			AssignedOrders: []prediction.AssignedOrder{
				{OrderID: "1"},
				{OrderID: o.OrderID},
			},
			PlanRoutes: testutil.GeneratePlanRoutes("P1 P2 D1 D2"),
		}

		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		driver1 := createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1)
		driver2 := createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 2)
		driver3 := createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 3)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				driver1,
				driver2,
				driver3,
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "2", "3"})

		// notifyBestDriver
		mocks.mockAssigmentLogRepo.EXPECT().AssignedDrivers(gomock.Any(), o.OrderID, gomock.Any()).Return(newDriverRecords(), nil)
		expectNoAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)

		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), driver2, gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeMultiple, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: "2", AssignedOrders: []prediction.AssignedOrder{{OrderID: "1"}, {OrderID: o.OrderID}}, PlanRoutes: testutil.GeneratePlanRoutes("P1 D1 P2 D2")}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), driver3, gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeMultiple, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return routeResp, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			1,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: "3", Distance: 3}},
			AssignmentLogOptMatcher(model.AssignmentLogOpt{AutoAssigned: true, AllowQueueing: true, AssignToQueue: true, PlanRoute: model.PlanRoute{
				RouteResponse: routeResp,
			}, SearchRiderStrategy: model.StrategyNameFleetPool, Region: model.DistributeRegions(regions).DefaultRegion()}),
		).Return(nil, nil)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusAssigningDriver
			return []model.Order{o}, nil
		})

		expectNoAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeMultiple, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: "1"}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			2,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: "1", Distance: 1}},
			gomock.Any()).Do(func(_ context.Context, _ int, _ string, _ int, _ []repository.DriverDistance, opt model.AssignmentLogOpt) {
			require.Equal(tt, model.StrategyNameFleetPool, opt.SearchRiderStrategy)
		}).Return(nil, nil)
		expectLockDriver(ctx, mocks, "1")
		expectUnlockDriver(ctx, mocks, "1")
		expectNotifyWithoutSettingIsDistributed(tt, ctx, mocks, o.OrderID, "1", service.WithFirebase)

		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "1"

			return []model.Order{o}, nil
		})
		expectEventPublish(ctx, mocks)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "2", model.RegionsString(regions))
	})
	t.Run("try to assign online when order has preferNotBundled=true", func(tt *testing.T) {
		tt.Parallel()

		preferNotBundledMOAggressiveLevel := "L1"
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.PreferNotBundledMOAggressiveLevel = "L1"
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.PreferNotBundledTopN = 5
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.OptimizationTopN = 3
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.OrderID = "0"
		o.Metadata[model.MetadataPreferNotBundled] = ""

		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		assignedDriver1ID := "assignedDriver1"
		onlineDriver1ID := "onlineDriver1"
		assignedDriver2ID := "assignedDriver2"
		assignedDriver1 := createDriverWithLocation(createDriver(assignedDriver1ID, model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1)
		onlineDriver1 := createDriverWithLocation(createDriver(onlineDriver1ID, model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 2)
		assignedDriver2 := createDriverWithLocation(createDriver(assignedDriver2ID, model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 3)

		onlineDriver1Route := prediction.RouteResponse{
			RiderID: onlineDriver1ID,
			AssignedOrders: []prediction.AssignedOrder{
				{OrderID: o.OrderID},
			},
		}
		assignedDriver1Route := prediction.RouteResponse{
			RiderID: assignedDriver1ID,
			AssignedOrders: []prediction.AssignedOrder{
				{OrderID: "1"},
				{OrderID: o.OrderID},
			},
			PlanRoutes: testutil.GeneratePlanRoutes("P1 P0 D1 D0"),
		}
		assignedDriver2Route := prediction.RouteResponse{
			RiderID: assignedDriver2ID,
			AssignedOrders: []prediction.AssignedOrder{
				{OrderID: "2"},
				{OrderID: o.OrderID},
			},
			PlanRoutes: testutil.GeneratePlanRoutes("P2 P0 D2 D0"),
		}

		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				assignedDriver1,
				onlineDriver1,
				assignedDriver2,
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{assignedDriver1ID, onlineDriver1ID, assignedDriver2ID})
		setDriverTransaction(ctx, mocks, createDriverTransaction(assignedDriver1ID))
		setDriverTransaction(ctx, mocks, createDriverTransaction(onlineDriver1ID))
		setDriverTransaction(ctx, mocks, createDriverTransaction(assignedDriver2ID))

		expectPredictCall(ctx, mocks, tt)
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) ([]service.DriverWithLocation, error) {
				require.Equal(tt, preferNotBundledMOAggressiveLevel, cfg.Level)
				expected := []string{assignedDriver1ID, onlineDriver1ID, assignedDriver2ID}
				assert.Len(tt, riders, len(expected))

				actual := make([]string, len(expected))
				for i := 0; i < len(expected); i++ {
					actual[i] = riders[i].Driver.DriverID
				}

				assert.ElementsMatch(tt, actual, expected)
				return riders, nil
			})

		// notifyBestDriver
		// MO Assignment type
		mocks.mockAssigmentLogRepo.EXPECT().AssignedDrivers(gomock.Any(), o.OrderID, gomock.Any()).Return(newDriverRecords(), nil)
		expectNoAssignedState(ctx, mocks, assignedDriver1ID, o.OrderID, distCfg.AcceptingDurationInSecond)

		expectNoAssignedState(ctx, mocks, onlineDriver1ID, o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), onlineDriver1, gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeMultiple, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return onlineDriver1Route, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
		expectLockDriver(ctx, mocks, onlineDriver1ID)
		expectUnlockDriver(ctx, mocks, onlineDriver1ID)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			1,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: onlineDriver1ID, Distance: onlineDriver1.DistanceMeter}},
			AssignmentLogOptMatcher(model.AssignmentLogOpt{AutoAssigned: true, AllowQueueing: true, PlanRoute: model.PlanRoute{
				RouteResponse: onlineDriver1Route,
			}, SearchRiderStrategy: model.StrategyNameFleetPool, Region: model.DistributeRegions(regions).DefaultRegion(), MOAggressiveLevel: "L1"}),
		).Return(nil, nil)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, onlineDriver1ID)
		expectNotify(tt, ctx, mocks, o.OrderID, onlineDriver1ID, service.WithFirebase)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusAssigningDriver
			return []model.Order{o}, nil
		})
		mocks.mockAssigmentLogRepo.EXPECT().AssignedDrivers(gomock.Any(), o.OrderID, gomock.Any()).Return(newDriverRecords(onlineDriver1ID), nil)
		expectNoAssignedState(ctx, mocks, assignedDriver2ID, o.OrderID, distCfg.AcceptingDurationInSecond)

		// Any Assignment type
		expectNoAssignedState(ctx, mocks, assignedDriver1ID, o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), assignedDriver1, gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeMultiple, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return assignedDriver1Route, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
		expectLockDriver(ctx, mocks, assignedDriver1ID)
		expectUnlockDriver(ctx, mocks, assignedDriver1ID)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			2,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: assignedDriver1ID, Distance: assignedDriver1.DistanceMeter}},
			AssignmentLogOptMatcher(model.AssignmentLogOpt{AutoAssigned: true, AllowQueueing: true, AssignToQueue: true, PlanRoute: model.PlanRoute{
				RouteResponse: assignedDriver1Route,
			}, SearchRiderStrategy: model.StrategyNameFleetPool, Region: model.DistributeRegions(regions).DefaultRegion(), MOAggressiveLevel: "L1"}),
		).Return(nil, nil)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, assignedDriver1ID)
		expectNotifyWithoutSettingIsDistributed(tt, ctx, mocks, o.OrderID, assignedDriver1ID, service.WithFirebase)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusAssigningDriver
			return []model.Order{o}, nil
		})

		expectNoAssignedState(ctx, mocks, assignedDriver2ID, o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), assignedDriver2, gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeMultiple, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return assignedDriver2Route, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(service.ValidateRouteErrRes{}, nil)
		expectLockDriver(ctx, mocks, assignedDriver2ID)
		expectUnlockDriver(ctx, mocks, assignedDriver2ID)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			3,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: assignedDriver2ID, Distance: assignedDriver2.DistanceMeter}},
			AssignmentLogOptMatcher(model.AssignmentLogOpt{AutoAssigned: true, AllowQueueing: true, AssignToQueue: true, PlanRoute: model.PlanRoute{
				RouteResponse: assignedDriver2Route,
			}, SearchRiderStrategy: model.StrategyNameFleetPool, Region: model.DistributeRegions(regions).DefaultRegion(), MOAggressiveLevel: "L1"}),
		).Return(nil, nil)
		expectNotifyWithoutSettingIsDistributed(tt, ctx, mocks, o.OrderID, assignedDriver2ID, service.WithFirebase)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = assignedDriver2ID
			return []model.Order{o}, nil
		})
		expectEventPublish(ctx, mocks)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "3", model.RegionsString(regions))
	})
	t.Run("set order as distributed", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		oDistributed := o
		oDistributed.IsDistributed = true

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 100),
			}, model.StrategyNameFleetPool)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID, "3", "11")
		initDriverStatistics(ctx, mocks, []string{"1", "2"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "2", 600, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "2")
		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{oDistributed}, nil)

		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		expectNotifyWithoutSettingIsDistributed(tt, ctx, mocks, o.OrderID, "1", service.WithFirebase)
		expectNoAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 2, o.OrderID, 0, "1", 1000, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "1")
		expectUnlockDriver(ctx, mocks, "1")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverToRestaurant
			o.Driver = "1"

			return []model.Order{o}, nil
		})
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "2", model.RegionsString(regions))
	})
	t.Run("calculate pickup-distance on top fare for each driver", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.PayAtStop = 1
		o.Routes[0].ID = "resid"
		o.CreatedAt = time.Now()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{
			{
				Scheme: model.PickupDistanceScheme,
				Region: string(o.DistributeRegions[0]),
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{model.Days(strings.ToUpper(o.CreatedAt.Format("Mon")))},
						Time:   []model.StartEndTime{{Begin: "00:00", End: "23:59"}},
						PickupDistancePrices: []model.OntopPickupDistancePrice{
							{From: 2000, Amount: 30},
						},
						ServiceTypes: model.Services{model.ServiceFood},
					},
				},
				Status: model.StatusActive,
			},
		})
		driverLocation := &model.DriverLocation{Location: model.Location{Lat: 1, Lng: 1}}
		mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), "3").Return(driverLocation, nil)
		mocks.mapService.EXPECT().FindFastestRoute(gomock.Any(), mapservice.Location{Lat: driverLocation.Location.Lat, Lng: driverLocation.Location.Lng}, mapservice.Location{Lat: o.Routes[0].Location.Lat, Lng: o.Routes[0].Location.Lng}, false).Return(&model.MapRoute{Distance: 2300, Duration: 30}, nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ord model.Order) error {
			require.True(tt, ord.HasPickupDistanceOnTopFare())
			for _, scheme := range ord.PriceSummary().DeliveryFee.OnTopScheme {
				if scheme.Scheme == model.PickupDistanceScheme {
					require.Equal(tt, 30.0, scheme.Amount)
				}
			}
			return nil
		})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("calculate pickup-distance on top fare for each driver but not add to ontop scheme if its amount is zero", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.PayAtStop = 1
		o.Routes[0].ID = "resid"
		o.CreatedAt = time.Now()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{
			{
				Scheme: model.PickupDistanceScheme,
				Region: string(o.DistributeRegions[0]),
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{model.Days(strings.ToUpper(o.CreatedAt.Format("Mon")))},
						Time:   []model.StartEndTime{{Begin: "00:00", End: "23:59"}},
						PickupDistancePrices: []model.OntopPickupDistancePrice{
							{From: 2000, Amount: 30},
						},
						ServiceTypes: model.Services{model.ServiceFood},
					},
				},
				Status: model.StatusActive,
			},
		})
		driverLocation := &model.DriverLocation{Location: model.Location{Lat: 1, Lng: 1}}
		mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), "3").Return(driverLocation, nil)
		mocks.mapService.EXPECT().FindFastestRoute(gomock.Any(), mapservice.Location{Lat: driverLocation.Location.Lat, Lng: driverLocation.Location.Lng}, mapservice.Location{Lat: o.Routes[0].Location.Lat, Lng: o.Routes[0].Location.Lng}, false).Return(&model.MapRoute{Distance: 500, Duration: 30}, nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ord model.Order) error {
			require.False(tt, ord.HasPickupDistanceOnTopFare())
			return nil
		})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("calculate single installment on top fare for each driver", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.PayAtStop = 1
		o.Routes[0].ID = "resid"
		o.CreatedAt = time.Now()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		otf := []model.OnTopFare{
			{
				Scheme:           model.InstallmentOnTopScheme,
				Region:           string(o.DistributeRegions[0]),
				DriverIDs:        []string{"3"},
				EnableActiveTime: false,
				Status:           model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{model.Monday, model.Tuesday, model.Wednesday, model.Thursday, model.Friday, model.Saturday, model.Sunday},
						Time:   []model.StartEndTime{{Begin: "00:00", End: "23:59"}},
						InstallmentPrice: model.OntopInstallmentPrice{
							MaxOrders: 10,
							Amount:    50,
						},
						ServiceTypes: model.Services{model.ServiceFood},
					},
				},
			},
		}
		expectOnTopFareCall(mocks, otf)
		driverLocation := &model.DriverLocation{Location: model.Location{Lat: 1, Lng: 1}}
		mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), "3").Return(driverLocation, nil)
		mocks.mapService.EXPECT().FindFastestRoute(gomock.Any(), mapservice.Location{Lat: driverLocation.Location.Lat, Lng: driverLocation.Location.Lng}, mapservice.Location{Lat: o.Routes[0].Location.Lat, Lng: o.Routes[0].Location.Lng}, false).Return(&model.MapRoute{Distance: 2300, Duration: 30}, nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ord model.Order) error {
			for _, scheme := range ord.PriceSummary().DeliveryFee.OnTopScheme {
				if scheme.Scheme == model.InstallmentOnTopScheme {
					require.Equal(tt, 50.0, scheme.Amount)
				}
			}
			return nil
		})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("calculate multiple installments on top fare for each driver", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.PayAtStop = 1
		o.Routes[0].ID = "resid"
		o.CreatedAt = time.Now()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		otf := []model.OnTopFare{
			{
				ID:               "scheme_1",
				Scheme:           model.InstallmentOnTopScheme,
				Region:           string(o.DistributeRegions[0]),
				DriverIDs:        []string{"3"},
				EnableActiveTime: false,
				Status:           model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{model.Monday, model.Tuesday, model.Wednesday, model.Thursday, model.Friday, model.Saturday, model.Sunday},
						Time:   []model.StartEndTime{{Begin: "00:00", End: "23:59"}},
						InstallmentPrice: model.OntopInstallmentPrice{
							MaxOrders: 10,
							Amount:    50,
						},
						ServiceTypes: model.Services{model.ServiceFood},
					},
				},
			},
			{
				ID:               "scheme_2",
				Scheme:           model.InstallmentOnTopScheme,
				Region:           string(o.DistributeRegions[0]),
				DriverIDs:        []string{"3", "4"},
				EnableActiveTime: false,
				Status:           model.StatusActive,
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{model.Monday, model.Tuesday, model.Wednesday, model.Thursday, model.Friday, model.Saturday, model.Sunday},
						Time:   []model.StartEndTime{{Begin: "00:00", End: "23:59"}},
						InstallmentPrice: model.OntopInstallmentPrice{
							MaxOrders: 5,
							Amount:    200,
						},
						ServiceTypes: model.Services{model.ServiceFood},
					},
				},
			},
		}
		expectOnTopFareCall(mocks, otf)
		driverLocation := &model.DriverLocation{Location: model.Location{Lat: 1, Lng: 1}}
		mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), "3").Return(driverLocation, nil)
		mocks.mapService.EXPECT().FindFastestRoute(gomock.Any(), mapservice.Location{Lat: driverLocation.Location.Lat, Lng: driverLocation.Location.Lng}, mapservice.Location{Lat: o.Routes[0].Location.Lat, Lng: o.Routes[0].Location.Lng}, false).Return(&model.MapRoute{Distance: 2300, Duration: 30}, nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ord model.Order) error {
			for _, scheme := range ord.PriceSummary().DeliveryFee.OnTopScheme {
				if scheme.Scheme == model.InstallmentOnTopScheme {
					if scheme.ID == "scheme_1" {
						require.Equal(tt, 50.0, scheme.Amount)
					} else if scheme.ID == "scheme_2" {
						require.Equal(tt, 200.0, scheme.Amount)
					}
				}
			}
			return nil
		})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("calculate single installment on top fare for each driver", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.PayAtStop = 1
		o.Routes[0].ID = "resid"
		o.CreatedAt = time.Now()
		o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee.OnTopScheme = []model.OnTopScheme{{
			ID:     "installment-1",
			Scheme: model.InstallmentOnTopScheme,
			Name:   "installment-1-from-other-driver",
			Amount: 10.0,
		}}

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ord model.Order) error {
			require.Equal(tt, 0, len(ord.PriceSummary().DeliveryFee.OnTopScheme))
			return nil
		})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("calculate pickup-distance on top fare for each driver but not add to ontop scheme if its amount is zero", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.PayAtStop = 1
		o.Routes[0].ID = "resid"
		o.CreatedAt = time.Now()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, 1, o.OrderID, "3", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{
			{
				Scheme: model.PickupDistanceScheme,
				Region: string(o.DistributeRegions[0]),
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive,
						Days:   []model.Days{model.Days(strings.ToUpper(o.CreatedAt.Format("Mon")))},
						Time:   []model.StartEndTime{{Begin: "00:00", End: "23:59"}},
						PickupDistancePrices: []model.OntopPickupDistancePrice{
							{From: 2000, Amount: 30},
						},
						ServiceTypes: model.Services{model.ServiceFood},
					},
				},
				Status: model.StatusActive,
			},
		})
		driverLocation := &model.DriverLocation{Location: model.Location{Lat: 1, Lng: 1}}
		mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), "3").Return(driverLocation, nil)
		mocks.mapService.EXPECT().FindFastestRoute(gomock.Any(), mapservice.Location{Lat: driverLocation.Location.Lat, Lng: driverLocation.Location.Lng}, mapservice.Location{Lat: o.Routes[0].Location.Lat, Lng: o.Routes[0].Location.Lng}, false).Return(&model.MapRoute{Distance: 500, Duration: 30}, nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ord model.Order) error {
			require.False(tt, ord.HasPickupDistanceOnTopFare())
			return nil
		})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("distribute to priority riders by recommended area", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.RedistributionState = &model.RedistributionState{}
		oDistributed := o
		oDistributed.IsDistributed = true

		zoneMap := make(map[string]model.DedicatedZone)
		inZoneA := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneA.Label] = inZoneA

		inZoneB := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 0,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneB.Label] = inZoneB

		outZone := model.DedicatedZone{
			Label: "outZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {1, 0}, {1, 1}, {0, 1}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[outZone.Label] = outZone
		mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(zoneMap, nil).AnyTimes()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, o.DeliveringRound, model.DistributeRegions(regions).DefaultRegion())

		expectPredictCall(ctx, mocks, tt)
		dediA := createDriverWithLocation(createDriver("dediA", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1)
		dediA.Driver.DedicatedZones = []string{inZoneA.Label}
		dediA.Driver.ServiceTypes = []model.Service{model.ServiceMessenger}
		priorityA := createDriverWithLocation(createDriver("priorityA", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 2000)
		h3ID := h3.ToString(h3.FromGeo(h3.GeoCoord{Latitude: priorityA.Location.Lat, Longitude: priorityA.Location.Lng}, 7))
		recommendedH3Area := model.RecommendedH3{H3ID: h3ID, Lat: priorityA.Location.Lat, Lng: priorityA.Location.Lng}
		priorityA.Driver.H3Recommendation = model.H3Recommendation{RecommendationID: "r-1", Areas: []model.RecommendedH3{recommendedH3Area}, H3AreasIndex: map[string]model.RecommendedH3{recommendedH3Area.H3ID: recommendedH3Area}, ExpiredAt: time.Now().Add(15 * time.Minute)}
		outZoneRider := createDriverWithLocation(createDriver("outZone", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)
		outZoneRider.Driver.DedicatedZones = []string{outZone.Label}
		noZone := createDriverWithLocation(createDriver("noZone", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)

		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{dediA, priorityA, outZoneRider, noZone}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"dediA", "priorityA", "outZone", "noZone"})

		setDriverTransaction(ctx, mocks, createDriverTransaction("dediA"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("priorityA"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("outZone"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("noZone"))
		expectOptimizeCall(tt, ctx, mocks, []string{"dediA"})
		expectNoAssignedState(ctx, mocks, "dediA", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectRouteCallWithValidation(ctx, mocks, tt, "dediA")

		expectAssignToDriversQueueDedicatedZonePrioritized(ctx, mocks, 1, o.OrderID, "dediA", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectNotify(tt, ctx, mocks, o.OrderID, "dediA", service.WithFirebase)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "dediA")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectLockDriver(ctx, mocks, "dediA")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil).Times(2)
		expectUnlockDriver(ctx, mocks, "dediA")
		expectEventPublish(ctx, mocks)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{oDistributed}, nil)

		dediAButFailOptimize := createDriverWithLocation(createDriver("dediAButFailOptimize", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1)
		dediAButFailOptimize.Driver.DedicatedZones = []string{inZoneA.Label}

		//// start redistribution process
		//initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
		//	[]service.DriverWithLocation{
		//		dediAButFailOptimize,
		//		dediA,
		//		outZoneRider,
		//		priorityA,
		//		noZone,
		//	}, model.StrategyNameDirectSearch)
		//expectGetAssignedDrivers(ctx, mocks, o.OrderID, "dediA")
		//initDriverStatistics(ctx, mocks, []string{"dediAButFailOptimize", "outZone", "priorityA", "noZone"})
		//setDriverTransaction(ctx, mocks, createDriverTransaction("dediAButFailOptimize"))
		//
		//mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Len(1), gomock.Any()).
		//	DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, _ interface{}) ([]service.DriverWithLocation, error) {
		//		assert.Equal(tt, dediAButFailOptimize.Driver.DriverID, riders[0].Driver.DriverID)
		//
		//		return []service.DriverWithLocation{}, nil
		//	})
		//
		//expectOptimizeCall(tt, ctx, mocks, []string{"priorityA"})
		//mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverOrderRecommendedState("priorityA")).Return("").Times(2)
		//mocks.mockAssigmentLogRepo.EXPECT().AssignedDrivers(gomock.Any(), o.OrderID, gomock.Any()).Return(newDriverRecords("dediA"), nil)
		//expectNoAssignedState(ctx, mocks, "priorityA", o.OrderID, distCfg.AcceptingDurationInSecond)
		//expectRouteCallWithValidation(ctx, mocks, tt, "priorityA")
		//expectOnTopFareCall(mocks, []model.OnTopFare{})
		//setDriverTransaction(ctx, mocks, createDriverTransaction("priorityA"))
		//setDriverTransaction(ctx, mocks, createDriverTransaction("priorityA"))
		//expectAssignToDriversQueueFromRecommendedAreaPrioritized(ctx, mocks, 3, o.OrderID, "priorityA", 2000, h3ID, model.StrategyNameDirectSearch)
		//expectNotifyWithoutSettingIsDistributed(tt, ctx, mocks, o.OrderID, "priorityA", service.WithFirebase)
		//expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "priorityA")
		//expectLockDriver(ctx, mocks, "priorityA")
		//mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverOrderRecommendedState("priorityA"), "r-1", gomock.Any()).Return(true)
		//expectEvaluateRainSituation(ctx, mocks, true, false)
		//mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{oDistributed}, nil).Times(2)
		//expectUnlockDriver(ctx, mocks, "priorityA")
		//expectEventPublish(ctx, mocks)
		//expectEvaluateRainSituation(ctx, mocks, true, false)
		//mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{oDistributed}, nil)
		//
		//// start redistribution process 2nd time
		//initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
		//	[]service.DriverWithLocation{
		//		dediA,
		//		priorityA,
		//		noZone,
		//	}, model.StrategyNameDirectSearch)
		//expectGetAssignedDrivers(ctx, mocks, o.OrderID, "dediA", "priorityA")
		//initDriverStatistics(ctx, mocks, []string{"noZone"})
		//
		//expectOptimizeCall(tt, ctx, mocks, []string{"noZone"})
		//mocks.mockAssigmentLogRepo.EXPECT().AssignedDrivers(gomock.Any(), o.OrderID, gomock.Any()).Return(newDriverRecords("dediA", "priorityA"), nil)
		//expectNoAssignedState(ctx, mocks, "noZone", o.OrderID, distCfg.AcceptingDurationInSecond)
		//expectRouteCallWithValidation(ctx, mocks, tt, "noZone")
		//expectOnTopFareCall(mocks, []model.OnTopFare{})
		//setDriverTransaction(ctx, mocks, createDriverTransaction("noZone"))
		//setDriverTransaction(ctx, mocks, createDriverTransaction("noZone"))
		//expectAssignToDriversQueue(ctx, mocks, 5, o.OrderID, "noZone", 1, model.StrategyNameDirectSearch)
		//expectNotifyWithoutSettingIsDistributed(tt, ctx, mocks, o.OrderID, "noZone", service.WithFirebase)
		//expectLockDriver(ctx, mocks, "noZone")
		//expectUnlockDriver(ctx, mocks, "noZone")
		//expectEventPublish(ctx, mocks)
		//mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
		//	o.Status = model.StatusDriverMatched
		//	o.Driver = "noZone"
		//	return []model.Order{o}, nil
		//})

		mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).MinTimes(1)
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
		//mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "2", model.RegionsString(regions))
	})
	t.Run("distribute to priority riders by recommended area (auto accept case)", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.FullyAutoAcceptEnabled = true
		distCfg.FullyAutoAcceptEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		oDistributed := o
		oDistributed.IsDistributed = true

		zoneMap := make(map[string]model.DedicatedZone)
		inZoneA := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneA.Label] = inZoneA

		inZoneB := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 0,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneB.Label] = inZoneB

		outZone := model.DedicatedZone{
			Label: "outZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {1, 0}, {1, 1}, {0, 1}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[outZone.Label] = outZone

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(zoneMap, nil).AnyTimes()
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, o.DeliveringRound, model.DistributeRegions(regions).DefaultRegion())
		expectPredictCall(ctx, mocks, tt)
		priorityA := createDriverWithLocation(createDriver("priorityA", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)
		h3ID := h3.ToString(h3.FromGeo(h3.GeoCoord{Latitude: priorityA.Location.Lat, Longitude: priorityA.Location.Lng}, 7))
		recommendedH3Area := model.RecommendedH3{H3ID: h3ID, Lat: priorityA.Location.Lat, Lng: priorityA.Location.Lng}
		priorityA.Driver.H3Recommendation = model.H3Recommendation{RecommendationID: "r-1", Areas: []model.RecommendedH3{recommendedH3Area}, H3AreasIndex: map[string]model.RecommendedH3{recommendedH3Area.H3ID: recommendedH3Area}, ExpiredAt: time.Now().Add(15 * time.Minute)}
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				priorityA,
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"priorityA"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("priorityA"))
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverOrderRecommendedState("priorityA")).Return("")
		expectOptimizeCall(tt, ctx, mocks, []string{"priorityA"})
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState("priorityA"), gomock.Any(), gomock.Any()).Return(true)
		expectRouteCallWithValidation(ctx, mocks, tt, "priorityA")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverOrderRecommendedState("priorityA")).Return("")
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().
			GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).
			Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(
			gomock.Any(),
			1,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: "priorityA", Distance: 1}},
			AssignmentLogOptMatcher(model.AssignmentLogOpt{
				AutoAssigned:                             true,
				AllowQueueing:                            true,
				AssignToQueue:                            true,
				IsAutoAccept:                             true,
				IsFullyAutoAccept:                        true,
				FromRecommendedArea:                      true,
				DriverRecommendedOrderAssignedH3Location: h3ID,
				PlanRoute:                                model.PlanRoute{RouteResponse: prediction.RouteResponse{RiderID: "priorityA"}},
				SearchRiderStrategy:                      model.StrategyNameDirectSearch,
				Region:                                   model.DistributeRegions(regions).DefaultRegion(),
			}),
		).Return(nil, nil)
		expectEventPublish(ctx, mocks)
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), "priorityA").Return(nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), "priorityA").Return(nil)
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		var k *string
		expectFullyAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "priorityA", k, service.WithFirebase)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverOrderRecommendedState("priorityA"), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState("priorityA"))
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "priorityA", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 1, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("do not distribute to priority riders by recommended area in the case that recommendation is expired", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.RedistributionState = &model.RedistributionState{}
		oDistributed := o
		oDistributed.IsDistributed = true

		zoneMap := make(map[string]model.DedicatedZone)
		inZoneA := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneA.Label] = inZoneA

		inZoneB := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 0,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneB.Label] = inZoneB

		outZone := model.DedicatedZone{
			Label: "outZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {1, 0}, {1, 1}, {0, 1}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[outZone.Label] = outZone
		mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(zoneMap, nil).AnyTimes()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, o.DeliveringRound, model.DistributeRegions(regions).DefaultRegion())

		expectPredictCall(ctx, mocks, tt)
		dediA := createDriverWithLocation(createDriver("dediA", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1)
		dediA.Driver.DedicatedZones = []string{inZoneA.Label}
		dediA.Driver.ServiceTypes = []model.Service{model.ServiceMessenger}
		priorityA := createDriverWithLocation(createDriver("priorityA", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 2000)
		h3ID := h3.ToString(h3.FromGeo(h3.GeoCoord{Latitude: priorityA.Location.Lat, Longitude: priorityA.Location.Lng}, 7))
		recommendedH3Area := model.RecommendedH3{H3ID: h3ID, Lat: priorityA.Location.Lat, Lng: priorityA.Location.Lng}
		priorityA.Driver.H3Recommendation = model.H3Recommendation{RecommendationID: "r-1", Areas: []model.RecommendedH3{recommendedH3Area}, H3AreasIndex: map[string]model.RecommendedH3{recommendedH3Area.H3ID: recommendedH3Area}, ExpiredAt: time.Now().Add(-15 * time.Minute)}
		outZoneRider := createDriverWithLocation(createDriver("outZone", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)
		outZoneRider.Driver.DedicatedZones = []string{outZone.Label}
		noZone := createDriverWithLocation(createDriver("noZone", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)

		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{dediA, priorityA, outZoneRider, noZone}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"dediA", "priorityA", "outZone", "noZone"})

		setDriverTransaction(ctx, mocks, createDriverTransaction("dediA"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("priorityA"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("outZone"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("noZone"))

		expectOptimizeCall(tt, ctx, mocks, []string{"dediA"})
		expectNoAssignedState(ctx, mocks, "dediA", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectRouteCallWithValidation(ctx, mocks, tt, "dediA")
		expectAssignToDriversQueueDedicatedZonePrioritized(ctx, mocks, 1, o.OrderID, "dediA", 1, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectNotify(tt, ctx, mocks, o.OrderID, "dediA", service.WithFirebase)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "dediA")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectLockDriver(ctx, mocks, "dediA")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil).Times(2)
		expectUnlockDriver(ctx, mocks, "dediA")
		expectEventPublish(ctx, mocks)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{oDistributed}, nil)

		mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "2", model.RegionsString(regions))
	})
	t.Run("should send 1 as default value to optimize when osrmPhase is not 1 or 2", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		distCfg.DalianOSRMPhase = 999
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		expectedDriver := "3"
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		// Search Strategy here has been mocked, In the real case, it should not be NoSearch strategy
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameNoSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) ([]service.DriverWithLocation, error) {
				expectDalianOSRMPhaseInt64 := int64(1)
				assert.Equal(tt, expectDalianOSRMPhaseInt64, cfg.DalianOSRMPhase)
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, _ interface{}, setting service.ValidatePlanRouteSetting) (model.RiderFilterName, error) {
				require.Nil(tt, setting.FirstDistanceLimitByServices)
				return "", nil
			})

		expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameNoSearch, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver).Return(nil)
		expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		acceptOrderFnCalled := false
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			assert.Equal(tt, "3", driverID)
			acceptOrderFnCalled = true
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "3", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		assert.True(tt, acceptOrderFnCalled)
	})
	t.Run("should send osrm phase to dalian api", func(tt *testing.T) {
		tt.Parallel()

		expectedResult := []int64{1, 2}
		for _, v := range expectedResult {
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
			distCfg.PredictionServiceEnabled = true
			distCfg.AssignmentType = prediction.AssignmentTypeSingle
			distCfg.NotifyViaSocketIOEnabled = true
			autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
			distCfg.DalianOSRMPhase = v
			defer finishFn()

			ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

			expectedDriver := "3"
			mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
			expectEvaluateRainSituation(ctx, mocks, true, false)
			expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
			initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
				[]service.DriverWithLocation{
					createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
					createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
					createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
					createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
				}, model.StrategyNameFleetPool)
			expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
			expectGetAssignedDrivers(ctx, mocks, o.OrderID)
			initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})
			setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
			setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
			setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
			setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

			mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
				order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
				return nil
			})
			mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) ([]service.DriverWithLocation, error) {
					assert.Equal(tt, v, cfg.DalianOSRMPhase)
					return riders, nil
				})
			mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
				assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
				assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
				return nil
			})
			mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
				assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
				assert.Empty(tt, setting.MOType)
				return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
			})
			mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, _ interface{}, setting service.ValidatePlanRouteSetting) (model.RiderFilterName, error) {
					require.Nil(tt, setting.FirstDistanceLimitByServices)
					return "", nil
				})

			expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
			expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, nil, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
			mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
			mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver).Return(nil)
			expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "3", service.WithFirebase, service.WithSocketIO)
			expectEventPublish(ctx, mocks)
			expectZoneCall(mocks)
			expectOnTopFareCall(mocks, []model.OnTopFare{})
			acceptOrderFnCalled := false
			*autoAssignOrderDistributor.AcceptOrderFn = func(
				ctx context.Context,
				order *model.Order,
				orderID,
				driverID string,
				syncDelivery bool,
			) (*model.AcceptedOrderInfo, error) {
				assert.Equal(tt, "3", driverID)
				acceptOrderFnCalled = true
				return &model.AcceptedOrderInfo{Order: &o}, nil
			}
			mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "3", gomock.Any()).Return(nil)

			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
			assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
			assert.True(tt, acceptOrderFnCalled)
		}
	})
	t.Run("should send mo_aggressive_level from baseline if it is in control group", func(tt *testing.T) {
		tt.Parallel()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(&model.SwitchbackExperiments{
			ExperimentName: "1",
			ExperimentID:   "1",
			GroupID:        "1",
			GroupName:      "1",
			IsControlGroup: true,
			Region:         string(o.DistributeRegions.DefaultRegion()),
			Params: model.SwitchbackExperimentsParams{
				MOAggressiveLevel: autoAssignOrderDistributor.Config.MultipleOrderAggressiveLevel,
			},
		}, nil)
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		defer finishFn()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		expectedDriver := "3"
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) ([]service.DriverWithLocation, error) {
				assert.Equal(tt, autoAssignOrderDistributor.Config.MultipleOrderAggressiveLevel, cfg.Level)
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Equal(tt, autoAssignOrderDistributor.Config.MultipleOrderAggressiveLevel, setting.Level)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, _ interface{}, setting service.ValidatePlanRouteSetting) (model.RiderFilterName, error) {
				require.Nil(tt, setting.FirstDistanceLimitByServices)
				return "", nil
			})

		expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, &model.SwitchbackExperiments{
			ExperimentName: "1",
			ExperimentID:   "1",
			GroupID:        "1",
			GroupName:      "1",
			IsControlGroup: true,
			Region:         string(o.DistributeRegions.DefaultRegion()),
			Params: model.SwitchbackExperimentsParams{
				MOAggressiveLevel: autoAssignOrderDistributor.Config.MultipleOrderAggressiveLevel,
			},
		}, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver).Return(nil)
		expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		acceptOrderFnCalled := false
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			assert.Equal(tt, "3", driverID)
			acceptOrderFnCalled = true
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "3", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		assert.True(tt, acceptOrderFnCalled)
	})
	t.Run("should send mo_aggressive_level from EP if it is in test group", func(tt *testing.T) {
		tt.Parallel()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(&model.SwitchbackExperiments{
			ExperimentName: "1",
			ExperimentID:   "1",
			GroupID:        "1",
			GroupName:      "1",
			IsControlGroup: false,
			Region:         string(o.DistributeRegions.DefaultRegion()),
			Params: model.SwitchbackExperimentsParams{
				MOAggressiveLevel: "L8",
			},
		}, nil)
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		defer finishFn()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		expectedDriver := "3"
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) ([]service.DriverWithLocation, error) {
				assert.Equal(tt, "L8", cfg.Level)
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Equal(tt, "L8", setting.Level)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, _ interface{}, setting service.ValidatePlanRouteSetting) (model.RiderFilterName, error) {
				require.Nil(tt, setting.FirstDistanceLimitByServices)
				return "", nil
			})

		expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, &model.SwitchbackExperiments{
			ExperimentName: "1",
			ExperimentID:   "1",
			GroupID:        "1",
			GroupName:      "1",
			IsControlGroup: false,
			Region:         string(o.DistributeRegions.DefaultRegion()),
			Params: model.SwitchbackExperimentsParams{
				MOAggressiveLevel: "L8",
			},
		}, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver).Return(nil)
		expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		acceptOrderFnCalled := false
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			assert.Equal(tt, "3", driverID)
			acceptOrderFnCalled = true
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "3", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		assert.True(tt, acceptOrderFnCalled)
	})
	t.Run("should send rush_mode from baseline if it is in control group", func(tt *testing.T) {
		tt.Parallel()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(&model.SwitchbackExperiments{
			ExperimentName: "1",
			ExperimentID:   "1",
			GroupID:        "1",
			GroupName:      "1",
			IsControlGroup: true,
			Region:         string(o.DistributeRegions.DefaultRegion()),
			Params: model.SwitchbackExperimentsParams{
				RushMode: autoAssignOrderDistributor.Config.RushMode,
			},
		}, nil)
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		defer finishFn()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		expectedDriver := "3"
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameDirectSearch)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) ([]service.DriverWithLocation, error) {
				assert.Equal(tt, autoAssignOrderDistributor.Config.RushMode, cfg.RushMode)
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Equal(tt, autoAssignOrderDistributor.Config.RushMode, setting.RushMode)
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, _ interface{}, setting service.ValidatePlanRouteSetting) (model.RiderFilterName, error) {
				require.Nil(tt, setting.FirstDistanceLimitByServices)
				return "", nil
			})

		expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, &model.SwitchbackExperiments{
			ExperimentName: "1",
			ExperimentID:   "1",
			GroupID:        "1",
			GroupName:      "1",
			IsControlGroup: true,
			Region:         string(o.DistributeRegions.DefaultRegion()),
			Params: model.SwitchbackExperimentsParams{
				RushMode: autoAssignOrderDistributor.Config.RushMode,
			},
		}, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver).Return(nil)
		expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		acceptOrderFnCalled := false
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			assert.Equal(tt, "3", driverID)
			acceptOrderFnCalled = true
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "3", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		assert.True(tt, acceptOrderFnCalled)
	})
	t.Run("should send rush_mode from EP if it is in test group", func(tt *testing.T) {
		tt.Parallel()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(&model.SwitchbackExperiments{
			ExperimentName: "1",
			ExperimentID:   "1",
			GroupID:        "1",
			GroupName:      "1",
			IsControlGroup: false,
			Region:         string(o.DistributeRegions.DefaultRegion()),
			Params: model.SwitchbackExperimentsParams{
				RushMode: "HARD",
			},
		}, nil)
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		defer finishFn()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		expectedDriver := "3"
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver(expectedDriver, model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", expectedDriver, "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction(expectedDriver))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		mocks.mockPredictionService.EXPECT().Predict(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, order *model.Order, _ interface{}) error {
			order.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
			return nil
		})
		mocks.mockPredictionService.EXPECT().Optimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) ([]service.DriverWithLocation, error) {
				assert.Equal(tt, "HARD", string(cfg.RushMode))
				return riders, nil
			})
		mocks.mockOrderRepository.EXPECT().SetPrediction(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, id string, prediction model.PredictionFeatures) error {
			assert.Equal(tt, 5, prediction.EstimatedCookingTimeSecond)
			assert.Equal(tt, 10, prediction.EstimatedUserWaitingTimeSecond)
			return nil
		})
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
			assert.Equal(tt, prediction.AssignmentTypeSingle, setting.AssignmentType)
			assert.Equal(tt, "HARD", string(setting.RushMode))
			assert.Empty(tt, setting.MOType)
			return prediction.RouteResponse{RiderID: expectedDriver}, nil, nil
		})
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, _ interface{}, setting service.ValidatePlanRouteSetting) (model.RiderFilterName, error) {
				require.Nil(tt, setting.FirstDistanceLimitByServices)
				return "", nil
			})

		expectNoAssignedState(ctx, mocks, expectedDriver, o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueueAutoAccept(ctx, mocks, 1, o.OrderID, expectedDriver, 1, &model.SwitchbackExperiments{
			ExperimentName: "1",
			ExperimentID:   "1",
			GroupID:        "1",
			GroupName:      "1",
			IsControlGroup: false,
			Region:         string(o.DistributeRegions.DefaultRegion()),
			Params: model.SwitchbackExperimentsParams{
				RushMode: "HARD",
			},
		}, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), expectedDriver).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{o}, nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), expectedDriver).Return(nil)
		expectAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		acceptOrderFnCalled := false
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			assert.Equal(tt, "3", driverID)
			acceptOrderFnCalled = true
			return &model.AcceptedOrderInfo{Order: &o}, nil
		}
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "3", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		assert.True(tt, acceptOrderFnCalled)
	})
	t.Run("should send fullyAutoAcceptCountdownUntil correctly if fullyAutoAcceptWithReassining has enabled", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.AutoAcceptEnabled = true
		autoAssignOrderDistributor.Config.AtomicAutoAcceptConfig.Config.FullyAutoAcceptEnabled = true
		distCfg.FullyAutoAcceptEnabled = true
		autoAssignOrderDistributor.OrderConfig.AtomicOrderDBConfig.Config.EnableAcknowledgeFullyAutoAcceptWithReassigning = true
		fullyAutoAcceptWithReassignCountdownUntil := time.Now().Add(time.Second * 30)
		fullyAutoAcceptWithReassignCountdownUntilFormat := fullyAutoAcceptWithReassignCountdownUntil.Format(time.RFC3339)
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		oDistributed := o
		oDistributed.IsDistributed = true

		zoneMap := make(map[string]model.DedicatedZone)
		inZoneA := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneA.Label] = inZoneA

		inZoneB := model.DedicatedZone{
			Label: "inZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {3, 0}, {3, 3}, {0, 3}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 0,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[inZoneB.Label] = inZoneB

		outZone := model.DedicatedZone{
			Label: "outZone",
			Geometry: model.DedicatedZoneGeometry{
				Coordinates: model.DedicatedZoneCoordinates{{{{0, 0}, {1, 0}, {1, 1}, {0, 1}}}},
			},
			SearchRadiusKM:               1,
			DistanceToDestinationLimitKM: 1,
			ServiceTypes:                 []model.Service{model.ServiceFood},
		}
		zoneMap[outZone.Label] = outZone

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(zoneMap, nil).AnyTimes()
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, o.DeliveringRound, model.DistributeRegions(regions).DefaultRegion())
		expectPredictCall(ctx, mocks, tt)
		priorityA := createDriverWithLocation(createDriver("priorityA", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 1)
		h3ID := h3.ToString(h3.FromGeo(h3.GeoCoord{Latitude: priorityA.Location.Lat, Longitude: priorityA.Location.Lng}, 7))
		recommendedH3Area := model.RecommendedH3{H3ID: h3ID, Lat: priorityA.Location.Lat, Lng: priorityA.Location.Lng}
		priorityA.Driver.H3Recommendation = model.H3Recommendation{RecommendationID: "r-1", Areas: []model.RecommendedH3{recommendedH3Area}, H3AreasIndex: map[string]model.RecommendedH3{recommendedH3Area.H3ID: recommendedH3Area}, ExpiredAt: time.Now().Add(15 * time.Minute)}
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				priorityA,
			}, model.StrategyNameFleetPool)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"priorityA"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("priorityA"))
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverOrderRecommendedState("priorityA")).Return("")
		expectOptimizeCall(tt, ctx, mocks, []string{"priorityA"})
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState("priorityA"), gomock.Any(), gomock.Any()).Return(true)
		expectRouteCallWithValidation(ctx, mocks, tt, "priorityA")
		expectEvaluateRainSituation(ctx, mocks, true, false)
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverOrderRecommendedState("priorityA")).Return("")
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().
			GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).
			Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(
			gomock.Any(),
			1,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: "priorityA", Distance: 1}},
			AssignmentLogOptMatcher(model.AssignmentLogOpt{
				AutoAssigned:                             true,
				AllowQueueing:                            true,
				AssignToQueue:                            true,
				IsAutoAccept:                             true,
				IsFullyAutoAccept:                        true,
				FromRecommendedArea:                      true,
				DriverRecommendedOrderAssignedH3Location: h3ID,
				PlanRoute:                                model.PlanRoute{RouteResponse: prediction.RouteResponse{RiderID: "priorityA"}},
				SearchRiderStrategy:                      model.StrategyNameFleetPool,
				Region:                                   model.DistributeRegions(regions).DefaultRegion(),
			}),
		).Return(nil, nil)
		expectEventPublish(ctx, mocks)
		mocks.mockDriverService.EXPECT().TryLockDriver(gomock.Any(), "priorityA").Return(nil)
		mocks.mockDriverService.EXPECT().UnlockDriver(gomock.Any(), "priorityA").Return(nil)
		*autoAssignOrderDistributor.AcceptOrderFn = func(
			ctx context.Context,
			order *model.Order,
			orderID,
			driverID string,
			syncDelivery bool,
		) (*model.AcceptedOrderInfo, error) {
			return &model.AcceptedOrderInfo{Order: &o, FullyAutoAcceptWithReAssigningCountdownUntil: &fullyAutoAcceptWithReassignCountdownUntil}, nil
		}
		expectFullyAutoAcceptNotify(tt, ctx, mocks, o.OrderID, o.TripID, "priorityA", types.NewString(fullyAutoAcceptWithReassignCountdownUntilFormat), service.WithFirebase)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverOrderRecommendedState("priorityA"), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState("priorityA"))
		mocks.mockDriverRepository.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), "priorityA", gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 1, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})

	t.Run("happy path with prediction - food - enabled assign API", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		lastPredictionDisruption := time.Now()
		driver3 := createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false})
		driver3.LastPredictionDisruption = lastPredictionDisruption

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(driver3, model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameFleetPool,
		)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
				Drivers: nil,
			},
		}, nil)

		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCall(ctx, mocks, tt, "3")

		expectZoneCall(mocks)

		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState("3")).Return("")
		mocks.mockAssigningStateManager.EXPECT().SetAssigningState(gomock.Any(), gomock.Any(), o.OrderID, 0, gomock.Any()).Return(nil)
		mocks.mockFleetOrderClient.EXPECT().AssignOrder(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, req fleetorder.AssignOrderRequest) (fleetorder.AssignOrderResponse, error) {
			require.Equal(tt, driver3.DriverID, req.DriverID)
			require.Equal(tt, driver3.LastPredictionDisruption, req.LastPredictionDisruption)
			require.Equal(tt, []string{o.OrderID}, req.OrderIDs)
			require.Equal(tt, driver3.DriverID, req.PlanRoute.RiderID)
			require.Equal(tt, o.DistributeRegions.DefaultRegion().String(), req.Region)
			require.NotZero(tt, req.DistributionStartedAt)
			require.Equal(tt, true, req.AssignmentLogOpts.IsUsingPredictionService)
			require.Equal(tt, string(model.SingleDistribution), req.DistributionLogMetadata.DistributionType)
			require.Equal(tt, string(distribution.AnyAssignmentType), req.AssignmentConstraint.AssignmentType)
			require.NotZero(tt, req.AssignmentConstraint.B2BDistanceSetting)
			return fleetorder.AssignOrderResponse{}, nil
		})

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(true)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
	})
	t.Run("happy path with prediction - food - enabled assign API - error from API", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameFleetPool,
		)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
				Drivers: nil,
			},
		}, nil)

		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectZoneCall(mocks)
		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState("3")).Return("")

		expectRouteCall(ctx, mocks, tt, "1")
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState("1")).Return("")
		mocks.mockAssigningStateManager.EXPECT().SetAssigningState(gomock.Any(), gomock.Any(), o.OrderID, 0, gomock.Any()).Return(nil)
		mocks.mockFleetOrderClient.EXPECT().AssignOrder(gomock.Any(), gomock.Any()).Return(fleetorder.AssignOrderResponse{}, errors.New("something went wrong"))

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), o.OrderID, types.Ptr(0))

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(true)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
	})

}

// this testcases can be removed if EnabledAsyncAssigning and EnabledRevisedRedistributionState are enabled nationwide and be removed from the code
func TestAutoAssign_DistributeWithNotifyBestDriverInBackground(t *testing.T) {
	t.Parallel()

	withNotifyInBackgroundConfig := func(cfg dispatcherconfig.AutoAssignDbConfig) dispatcherconfig.AutoAssignDbConfig {
		// override config
		return cfg
	}

	t.Run("[single distribute] match rider", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: true}))
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mocks.mockOrderDistributionEventManager = mock_service.NewMockOrderDistributionEventManager(ctrl)
		autoAssignOrderDistributor.AutoAssignOrderDistributorDeps.OrderDistributionEventManager = mocks.mockOrderDistributionEventManager

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createTestDriver("T", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameFleetPool,
		)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "T", "3", "U"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "3", 1, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		firstEvent := mocks.mockOrderDistributionEventManager.EXPECT().PublishSearchingOrAssigningDriver(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
			require.Equal(tt, o.OrderID, order.OrderID)
			require.Nil(tt, order.ActualAssigningAt)
			require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
			return nil
		})
		mocks.mockOrderDistributionEventManager.EXPECT().PublishSearchingOrAssigningDriver(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
			require.Equal(tt, o.OrderID, order.OrderID)
			require.NotNil(tt, order.ActualAssigningAt)
			require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
			return nil
		}).After(firstEvent).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 5, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})

	t.Run("[single distribute] no driver", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: true}))
		defer finishFn()
		autoAssignOrderDistributor.Config.AtomicAutoAssignDbConfig.Config.RedistributionLimit = 0
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{}, model.StrategyNameFleetPool)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, time.Duration(0), true)
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), o.OrderID, types.Ptr(0))

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 0.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER", "0", model.RegionsString(regions))
	})

	t.Run("[single distribute] no one accept", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: true}))
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 100),
			}, model.StrategyNameFleetPool)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID, "3", "11")
		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID, "3", "11")
		initDriverStatistics(ctx, mocks, []string{"1", "2"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase)
		expectAssignToDrivers(ctx, mocks, 1, o.OrderID, 0, "2", 600, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "2")
		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)

		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		expectNotify(tt, ctx, mocks, o.OrderID, "1", service.WithFirebase)
		expectNoAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDrivers(ctx, mocks, 2, o.OrderID, 0, "1", 1000, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "1")
		expectLockDriver(ctx, mocks, "1")
		expectUnlockDriver(ctx, mocks, "1")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), o.OrderID, types.Ptr(2))

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "3", model.RegionsString(regions))
	})

	t.Run("[single distribute] force assign mp2 and rider accept", func(tt *testing.T) {
		tt.Parallel()
		lockDuration := 10 * time.Minute
		driverID := "driv-1"
		lockedUntil := time.Now().Add(lockDuration)
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{
			EnableMultiplePickup:     true,
			DisableThrottledDeferred: true,
			DisableThrottledDispatch: true,
			DisableDeferredDispatch:  true,
		}))
		distCfg.PredictionServiceEnabled = true
		distCfg.MOType = prediction.MOTypeMOS1
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		mp1OrderID := "mp-first-order"
		currentTrip := model.Trip{
			TripID: "current-trip",
			Orders: []model.TripOrder{
				{
					OrderID: mp1OrderID,
					Status:  model.StatusDriverMatched,
				},
			},
		}
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Options.MpOrderIDs = []string{mp1OrderID}

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNoSearch).Return(nil)
		expectZoneCall(mocks)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectPredictCall(ctx, mocks, tt)

		mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(&model.Order{
			Status: model.StatusDriverMatched,
			Driver: driverID,
		}, nil)

		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{
			driverID: {
				DriverID:                driverID,
				Status:                  model.StatusAssigned,
				CurrentOrder:            mp1OrderID,
				CurrentTrip:             currentTrip.TripID,
				LockedFromQueueingUntil: &lockedUntil,
				Options: model.Options{
					AutoAccept: true,
				},
				Region: "AYUTTHAYA",
			},
		}, nil).Times(2)
		mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&model.DriverLocation{
			DriverID: driverID,
			Location: model.Location{
				Lat: 13,
				Lng: 100,
			},
			DistanceMeter: 1,
		}, nil)
		initDriverStatistics(ctx, mocks, []string{driverID})
		setDriverTransaction(ctx, mocks, createDriverTransaction(driverID))

		expectNoAssignedState(ctx, mocks, driverID, o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockTripRepository.EXPECT().GetMany(gomock.Any(), []string{currentTrip.TripID}).Return([]model.Trip{currentTrip}, nil)
		mocks.mockPredictionService.EXPECT().IsTripDistanceReduced(gomock.Any(), []string{o.OrderID}, []model.Trip{currentTrip}, gomock.Any(), gomock.Any()).Return(false, nil)
		mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil)

		expectAssignMpToDriversWithOpt(
			ctx,
			mocks,
			1,
			mp1OrderID,
			o.OrderID,
			o,
			0,
			driverID,
			1,
			model.AssignmentLogOpt{
				AutoAssigned:        true,
				IsMultiplePickup:    true,
				AllowQueueing:       true,
				AssignToQueue:       true,
				SearchRiderStrategy: model.StrategyNameNoSearch,
				PlanRoute: model.PlanRoute{
					RouteResponse: prediction.RouteResponse{
						RiderID: driverID,
						AssignedOrders: []prediction.AssignedOrder{
							{
								OrderID:   mp1OrderID,
								OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
							},
							{
								OrderID:   o.OrderID,
								OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
							},
						},
						PlanRoutes: []prediction.PlanRoute{
							{
								OrderID:    mp1OrderID,
								ActionType: prediction.PickupAction,
							},
							{
								OrderID:    o.OrderID,
								ActionType: prediction.PickupAction,
							},
							{
								OrderID:    mp1OrderID,
								ActionType: prediction.DropOffAction,
							},
							{
								OrderID:    o.OrderID,
								ActionType: prediction.DropOffAction,
							},
						},
					},
				},
				Region: model.DistributeRegions(regions).DefaultRegion(),
			})

		expectLockDriver(ctx, mocks, driverID)
		expectUnlockDriver(ctx, mocks, driverID)
		expectNotify(tt, ctx, mocks, o.OrderID, driverID, service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})

	t.Run("[single distribute] force assign mp2 and rider doesn't accept", func(tt *testing.T) {
		tt.Parallel()
		lockDuration := 10 * time.Minute
		driverID := "driv-1"
		lockedUntil := time.Now().Add(lockDuration)

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{
			EnableMultiplePickup:     true,
			DisableThrottledDeferred: true,
			DisableThrottledDispatch: true,
			DisableDeferredDispatch:  true,
		}))
		distCfg.PredictionServiceEnabled = true
		distCfg.MOType = prediction.MOTypeMOS1
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		mp1OrderID := "mp-first-order"
		currentTrip := model.Trip{
			TripID: "current-trip",
			Orders: []model.TripOrder{
				{
					OrderID: mp1OrderID,
					Status:  model.StatusDriverMatched,
				},
			},
		}
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Options.MpOrderIDs = []string{mp1OrderID}
		mp2OrderID := o.OrderID

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNoSearch).Return(nil)
		expectZoneCall(mocks)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		expectPredictCall(ctx, mocks, tt)

		mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(&model.Order{
			Status: model.StatusDriverMatched,
			Driver: driverID,
		}, nil)

		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{
			driverID: {
				DriverID:                driverID,
				Status:                  model.StatusAssigned,
				CurrentOrder:            mp1OrderID,
				CurrentTrip:             currentTrip.TripID,
				LockedFromQueueingUntil: &lockedUntil,
				Options: model.Options{
					AutoAccept: true,
				},
				Region: "AYUTTHAYA",
			},
		}, nil).Times(2)
		mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&model.DriverLocation{
			DriverID: driverID,
			Location: model.Location{
				Lat: 13,
				Lng: 100,
			},
			DistanceMeter: 1,
		}, nil)
		initDriverStatistics(ctx, mocks, []string{driverID})
		setDriverTransaction(ctx, mocks, createDriverTransaction(driverID))

		expectNoAssignedState(ctx, mocks, driverID, o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockTripRepository.EXPECT().GetMany(gomock.Any(), []string{currentTrip.TripID}).Return([]model.Trip{currentTrip}, nil)
		mocks.mockPredictionService.EXPECT().IsTripDistanceReduced(gomock.Any(), []string{o.OrderID}, []model.Trip{currentTrip}, gomock.Any(), gomock.Any()).Return(false, nil)
		mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil)

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{mp2OrderID, mp1OrderID}, gomock.Any()).
			Return([]model.Order{
				{OrderID: mp2OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
				{OrderID: mp1OrderID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
			}, nil)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusAssigningDriver
			o.Driver = driverID

			return []model.Order{o}, nil
		})
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			return []model.Order{o}, nil
		}).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			1,
			mp2OrderID,
			0,
			[]repository.DriverDistance{{DriverID: driverID, Distance: 1}},
			AssignmentLogOptMatcher(model.AssignmentLogOpt{
				AutoAssigned:        true,
				IsMultiplePickup:    true,
				AllowQueueing:       true,
				AssignToQueue:       true,
				SearchRiderStrategy: model.StrategyNameNoSearch,
				PlanRoute: model.PlanRoute{
					RouteResponse: prediction.RouteResponse{
						RiderID: driverID,
						AssignedOrders: []prediction.AssignedOrder{
							{
								OrderID:   mp1OrderID,
								OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
							},
							{
								OrderID:   o.OrderID,
								OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
							},
						},
						PlanRoutes: []prediction.PlanRoute{
							{
								OrderID:    mp1OrderID,
								ActionType: prediction.PickupAction,
							},
							{
								OrderID:    o.OrderID,
								ActionType: prediction.PickupAction,
							},
							{
								OrderID:    mp1OrderID,
								ActionType: prediction.DropOffAction,
							},
							{
								OrderID:    o.OrderID,
								ActionType: prediction.DropOffAction,
							},
						},
					},
				},
				Region: model.DistributeRegions(regions).DefaultRegion(),
			}),
		).Return(nil, nil)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, driverID)

		expectLockDriver(ctx, mocks, driverID)
		expectUnlockDriver(ctx, mocks, driverID)
		expectNotify(tt, ctx, mocks, o.OrderID, driverID, service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		// unlock driver
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(&model.Order{
			Status: model.StatusDriverMatched,
			Driver: driverID,
		}, nil)
		mocks.mockDriverRepository.EXPECT().UnlockForQueueing(gomock.Any(), driverID).Return(nil)

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), o.OrderID, types.Ptr(1))
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "2", model.RegionsString(regions))
	})

	t.Run("[redistribution] match rider", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: true}))
		defer finishFn()

		ctx, wg, o, _, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID))
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 100),
			}, model.StrategyNameFleetPool)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
				Drivers: []model.Record{
					{
						DriverID: "3",
						Round:    "R1",
					},
					{
						DriverID: "11",
						Round:    "R1",
					},
				},
			},
		}, nil)
		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID, "3", "11")
		initDriverStatistics(ctx, mocks, []string{"1"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			2,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: "2", Distance: 600}},
			gomock.Any(),
		).Return(nil, nil)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "2")
		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)

		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		expectNotify(tt, ctx, mocks, o.OrderID, "1", service.WithFirebase)
		expectNoAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			3,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: "1", Distance: 1000}},
			gomock.Any(),
		).Return(nil, nil)
		expectLockDriver(ctx, mocks, "1")
		expectUnlockDriver(ctx, mocks, "1")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "1"

			return []model.Order{o}, nil
		})

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunRedistributionTask(ctx, &o, businessLocation, model.RedistributionState{}, types.Ptr(1), false, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "3", model.RegionsString(regions))
	})

	t.Run("[redistribution] no one accept", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: true}))
		defer finishFn()

		ctx, wg, o, _, businessLocation, regions := createStandingData()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID))
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 100),
			}, model.StrategyNameFleetPool)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID, "3", "11")
		expectGetAssignedDriversInNotify(ctx, mocks, o.OrderID, "3", "11")
		initDriverStatistics(ctx, mocks, []string{"1"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			1,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: "2", Distance: 600}},
			gomock.Any(),
		).Return(nil, nil)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "2")
		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)

		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		expectNotify(tt, ctx, mocks, o.OrderID, "1", service.WithFirebase)
		expectNoAssignedState(ctx, mocks, "1", o.OrderID, distCfg.AcceptingDurationInSecond)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.Order{{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)}}, nil).Times(2)
		mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
			2,
			o.OrderID,
			0,
			[]repository.DriverDistance{{DriverID: "1", Distance: 1000}},
			gomock.Any(),
		).Return(nil, nil)
		expectUpdateDriverNotAcceptAutoAssignOrder(ctx, mocks, o.OrderID, "1")
		expectLockDriver(ctx, mocks, "1")
		expectUnlockDriver(ctx, mocks, "1")
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return([]model.Order{o}, nil)

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectEventPublish(ctx, mocks)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunRedistributionTask(ctx, &o, businessLocation, model.RedistributionState{}, types.Ptr(1), false, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3.0, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "3", model.RegionsString(regions))
	})

	t.Run("[redistribution] still able to throttle", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: false, BatchDistributeTimeoutInSeconds: 60}))
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		defer finishFn()

		ctx, wg, o, _, businessLocation, _ := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = &model.PredictionFeatures{EstimatedCookingTimeSecond: 5, EstimatedUserWaitingTimeSecond: 10}
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): timeutil.BangkokNow(),
		}
		o.ExpireAt = timeutil.BangkokNow().Add(1 * time.Hour)

		expectZoneCall(mocks)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID))

		zoneID := primitive.NewObjectID()
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID}, ZoneCode: "BKK-THROTTLE"}, nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetThrottledOrder(gomock.Any(), o.OrderID, gomock.Any(), gomock.Any()).Return(nil)

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunRedistributionTask(ctx, &o, businessLocation, model.RedistributionState{}, types.Ptr(1), false, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})

	t.Run("bike orders should be redistributed when found that delivery location is changed while distributing", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.BikeB2BEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createBikeStandingData()
		o.Routes[0].Location = model.Location{
			Lat: 1,
			Lng: 2,
		}
		o.Routes[1].Location = model.Location{
			Lat: 3,
			Lng: 4,
		}

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameDirectSearch,
		)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")
		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)

		mocks.mockOrderRepository.EXPECT().
			GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).
			DoAndReturn(func(ctx context.Context, orderIDs []string, opts ...repository.Option) ([]model.Order, error) {
				orderWithLocationChanged := o
				orderWithLocationChanged.LastDeliveryLocationUpdatedAt = types.NewTime(time.Now())
				return []model.Order{orderWithLocationChanged}, nil
			})

		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
	})

	t.Run("messenger orders should be redistributed when found that delivery location is changed while distributing", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.BikeB2BEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.ServiceType = model.ServiceMessenger

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			model.StrategyNameDirectSearch,
		)
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)

		mocks.mockOrderRepository.EXPECT().
			GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).
			DoAndReturn(func(ctx context.Context, orderIDs []string, opts ...repository.Option) ([]model.Order, error) {
				orderDeliveryLocationChanged := o
				orderDeliveryLocationChanged.LastDeliveryLocationUpdatedAt = types.NewTime(time.Now())
				return []model.Order{orderDeliveryLocationChanged}, nil
			})

		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
	})

	t.Run("[single distribute] match rider - enabled assign API without previous state", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: true}))
		defer finishFn()
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle

		ctx, wg, o, _, businessLocation, regions := createStandingData()

		mocks.mockAssigningStateManager.EXPECT().GetAndRemoveAssigningState(gomock.Any(), o.OrderID, 0).Return(nil, nil)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID))
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), o.OrderID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 100),
			}, model.StrategyNameFleetPool)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
				Drivers: []model.Record{
					{
						DriverID: "3",
						Round:    "R1",
					},
					{
						DriverID: "11",
						Round:    "R1",
					},
				},
			},
		}, nil)
		initDriverStatistics(ctx, mocks, []string{"1"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		expectZoneCall(mocks)
		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "2"})

		expectRouteCall(ctx, mocks, tt, "2")
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState("2")).Return("")
		mocks.mockAssigningStateManager.EXPECT().SetAssigningState(gomock.Any(), gomock.Any(), o.OrderID, 0, gomock.Any()).Return(nil)
		mocks.mockFleetOrderClient.EXPECT().AssignOrder(gomock.Any(), gomock.Any()).Return(fleetorder.AssignOrderResponse{}, nil)

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(true)
		_ = autoAssignOrderDistributor.RunRedistributionTask(ctx, &o, businessLocation, model.RedistributionState{}, types.Ptr(1), false, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3.0, model.RegionsString(regions))
	})

	t.Run("[single distribute] match rider - enabled assign API with previous state", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, withNotifyInBackgroundConfig(dispatcherconfig.AutoAssignDbConfig{DisableThrottledDispatch: true}))
		defer finishFn()
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle

		ctx, wg, o, _, businessLocation, _ := createStandingData()

		assigningStateSetting := distribution.AssigningSettingState{
			DistCfgRevision:          "revision",
			IsUsingPredictionService: true,
		}
		assigningState := distribution.NewAssigningState(distribution.DriverQueueMinimal{
			CurIdx: 0,
			DriverAssignments: []distribution.DriverAssignmentMinimal{
				{
					AssignmentType: "",
					Driver: service.DriverWithLocationMinimal{
						DriverID: "1",
					},
				},
				{
					AssignmentType: "",
					Driver: service.DriverWithLocationMinimal{
						DriverID: "2",
					},
				},
			},
		}, assigningStateSetting)
		mocks.mockAssigningStateManager.EXPECT().GetAndRemoveAssigningState(gomock.Any(), o.OrderID, 0).Return(types.Ptr(assigningState), nil)
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{"1", "2"}).Return(map[string]*model.DriverMinimal{
			"1": types.Ptr(createDriver("1", model.StatusOnline, model.Options{AutoAccept: true})),
			"2": types.Ptr(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true})),
		}, nil)

		// prepare
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
				Drivers: []model.Record{},
			},
		}, nil)
		expectZoneCall(mocks)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(o.OrderID))
		expectPredictCall(ctx, mocks, tt)

		expectRouteCall(ctx, mocks, tt, "1")
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState("1")).Return("")
		mocks.mockAssigningStateManager.EXPECT().SetAssigningState(gomock.Any(), gomock.Any(), o.OrderID, 0, gomock.Any()).Return(nil)
		mocks.mockFleetOrderClient.EXPECT().AssignOrder(gomock.Any(), gomock.Any()).Return(fleetorder.AssignOrderResponse{}, nil)

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(true)
		_ = autoAssignOrderDistributor.RunRedistributionTask(ctx, &o, businessLocation, model.RedistributionState{}, types.Ptr(1), false, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			Revision:                    assigningStateSetting.DistCfgRevision,
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
}
