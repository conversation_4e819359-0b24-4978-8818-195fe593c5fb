package distribution

import (
	"context"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/getsentry/sentry-go"
	"github.com/kelseyhightower/envconfig"
	"github.com/twpayne/go-geom"
	"github.com/uber/h3-go"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/exp/maps"

	exp "git.wndv.co/dalian-dev/experiment-sdk"
	"git.wndv.co/go/logx/v2"
	userPb "git.wndv.co/go/proto/lineman/auth/v1"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	v1 "git.wndv.co/lineman/delivery-service/types/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	orderapi "git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/orderassigner"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/experimentplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetorder"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mongotxn"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/sim"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/sets"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var (
	ErrDriverLock               = errors.New("driver is locked")
	ErrPredictionService        = errors.New("prediction service error")
	ErrAssignmentLimit          = errors.New("assignment is over the limit")
	ErrOrderCantBeRedistributed = errors.New("order can't be redistributed") // this error is very specific just to specify that order hasn't been accepted but the distribution should not continue
	ErrOrderReachBatchTimeout   = errors.New("order reach batch timeout")
)

type ErrValidationDistribution struct {
	err error
}

func NewErrValidationDistribution(err error) *ErrValidationDistribution {
	return &ErrValidationDistribution{err: err}
}

func (e ErrValidationDistribution) Error() string {
	return e.err.Error()
}

type AutoAssignOrderDistributor struct {
	AutoAssignOrderDistributorDeps

	runningTasks sync.WaitGroup
}

var _ orderapi.OrderDistributor = (*AutoAssignOrderDistributor)(nil)

type DriverLocationSearchable interface {
	SearchDriversLimit(ctx context.Context, loc model.Location, toM float64, limit int, service model.Service, region model.RegionCode, orderID string) (service.SearchResult, error)
	SearchDriversInMultiPolygon(ctx context.Context, batchZoneName string, coordinates [][][]geom.Coord, toM float64) (service.SearchResult, error)
	GetDriverWithLocationByIDs(ctx context.Context, driverIDs []string) ([]service.DriverWithLocation, error)
}

type DriverLocationSearchableFunc func(ctx context.Context, loc model.Location, toM float64, limit int, orderID string) ([]service.DriverWithLocation, error)

func (f DriverLocationSearchableFunc) SearchDriversLimit(ctx context.Context, loc model.Location, toM float64, limit int, orderID string) ([]service.DriverWithLocation, error) {
	return f(ctx, loc, toM, limit, orderID)
}

func (f DriverLocationSearchableFunc) SearchDriversInMultiPolygon(ctx context.Context, coordinates [][][]geom.Coord, toM float64) ([]service.DriverWithLocation, error) {
	return nil, errors.New("not implemented")
}

type AutoAssignOrderDistributorDeps struct {
	OrderRepository                   repository.OrderRepository
	DriverRepository                  repository.DriverRepository
	LocationManager                   DriverLocationSearchable
	Notifier                          service.Notifier
	AssignmentLogRepo                 repository.AssignmentLogRepository
	DriverTransaction                 payment.DriverTransactionService
	StatisticRepo                     repository.DriverStatisticRepository
	StatisticService                  service.StatisticService
	DriverOrderInfoRepo               repository.DriverOrderInfoRepository
	Locker                            locker.Locker
	Environment                       sim.Environment
	OrderConfig                       orderapi.OrderAPIConfig
	ContingencyCfg                    *orderapi.AtomicContingencyConfig
	AreaDistCfg                       model.AreaDistributionConfig
	DistributionConfig                *dispatcherconfig.AtomicDistributionConfig
	PredictionService                 service.PredictionService
	IncentiveRepo                     incentive.IncentiveRepository
	TxnHelper                         transaction.TxnHelper
	DriverService                     service.DriverServiceInterface
	MapService                        mapservice.MapService
	DedicatedZoneRepo                 repository.DedicatedZoneRepository
	DriverLocationRepository          repository.DriverLocationRepository
	TripRepository                    repository.TripRepository
	OnTopFareService                  service.OnTopFareService
	ThrottledDispatchDetailRepository repository.ThrottledDispatchDetailRepository
	ThrottledOrderRepository          repository.ThrottledOrderRepository
	Dispatcher                        dispatcher.Dispatcher
	DeferredOrderRepository           repository.DeferredOrderRepository
	AssignmentRepo                    repository.AssignmentRepository
	RainSituationService              service.RainSituationService
	ServiceAreaRepository             repository.ServiceAreaRepository
	ZoneRepository                    repository.ZoneRepository
	Delivery                          delivery.Delivery
	ServicePreferenceService          service.ServicePreferenceService
	DistributionLogManager            service.DistributionLogManager
	OrderDistributionEventManager     service.OrderDistributionEventManager
	DistributionService               service.DistributionService
	FeatureFlagService                featureflag.Service
	FleetOrderClient                  fleetorder.FleetOrderClient
	IllegalDriverRepository           repository.IllegalDriverRepository
	ThrottledOrderDBConfig            config.ThrottledOrderDBConfig

	Config AutoAssignConfig

	fetchedMetric                              metric.Histogram
	stopMetric                                 metric.Counter
	lockingDriverMetric                        metric.Histogram
	batchRiderCountMetric                      metric.Histogram
	batchOrderCountMetric                      metric.Histogram
	batchRedistributionCountMetric             metric.Counter
	batchBundlingOrderCardinalityCountMetric   metric.Counter
	batchPartialFailAssignOrderCountMetric     metric.Counter
	batchOrderFallbackCountMetric              metric.Counter
	deferredOrderCountMetric                   metric.Counter
	deferredOrderTransitionToSingleCountMetric metric.Counter
	deferredOrderRedistributionCountMetric     metric.Counter
	deferredOrderAcceptCountMetric             metric.Counter
	noRedistributionStateOrderCountMetric      metric.Counter
	batchOptimizeMappedOrderDalianMetric       metric.Histogram
	batchOptimizeRiderToOrderDalianMetric      metric.Histogram
	Rep                                        rep.REPService
	Bus                                        domain.EventBus

	AcceptOrderFn      *orderassigner.AcceptOrderFunction
	AcceptAssignmentFn *orderassigner.AcceptAssignmentFunction

	UserClient  userPb.UserServiceClient
	RedisClient datastore.RedisClient

	WorkerContext         safe.WorkerContext
	OrderHeartbeatService service.OrderHeartbeatService

	DistributionExperimentPlatformClient experimentplatform.DistributionExperimentPlatformClient
	metricsRegistry                      metric.MetricsRegistry
	AssigningStateManager                AssigningStateManager
}

type AutoAssignConfig struct {
	model.AutoAssignDistribution
	EnableRequireBox                 bool `envconfig:"ENABLE_REQUIRE_BOX" default:"false"`
	EnableCashCollection             bool `envconfig:"ENABLE_CASH_COLLECTION" default:"true"`
	EnableMockRider                  bool `envconfig:"ENABLE_MOCK_RIDER" default:"false"`
	LimitDriverFetch                 int  `envconfig:"DISTRIBUTION_LIMIT_DRIVER" default:"1000"`
	DebugScorer                      bool `envconfig:"DEBUG_SCORER" default:"false"`
	AtomicBackToBackConfig           *config.AtomicBackToBackConfig
	AtomicAutoAcceptConfig           *dispatcherconfig.AtomicAutoAcceptConfig
	AtomicAutoAssignDbConfig         *dispatcherconfig.AtomicAutoAssignDbConfig
	AtomicSupplyPositioningConfig    *config.AtomicSupplyPositioningConfig
	AtomicDedicatedPriorityScorerCfg *config.AtomicDedicatedPriorityScorerConfig
	IsLINEUserIDUsed                 bool `envconfig:"IS_LINE_USER_ID_USED" default:"false"`
}

func (aac *AutoAssignConfig) GetRedistributionLimit(order *model.Order) int {
	cfg := aac.AtomicAutoAssignDbConfig.Get()
	if order.Options.CanDefer {
		return cfg.DeferredOrderRedistributionLimit
	} else if order.Options.SwitchFlow && order.ServiceType == model.ServiceFood && order.DisabledRecreate {
		return cfg.SwitchFlowRedistributionLimit
	}
	return cfg.RedistributionLimit
}

func NewDefaultAutoAssignConfig() *AutoAssignConfig {
	cfg := AutoAssignConfig{}
	envconfig.MustProcess("", &cfg)

	return &cfg
}

func ProvideAutoAssignConfig(
	backCfg *config.AtomicBackToBackConfig,
	autoAcceptCfg *dispatcherconfig.AtomicAutoAcceptConfig,
	autoAssignCfg *dispatcherconfig.AtomicAutoAssignDbConfig,
	supplyPositioningCfg *config.AtomicSupplyPositioningConfig,
	dedicatedPriorityScorerConfig *config.AtomicDedicatedPriorityScorerConfig,
) *AutoAssignConfig {
	cfg := NewDefaultAutoAssignConfig()
	cfg.AtomicBackToBackConfig = backCfg
	cfg.AtomicAutoAcceptConfig = autoAcceptCfg
	cfg.AtomicAutoAssignDbConfig = autoAssignCfg
	cfg.AtomicSupplyPositioningConfig = supplyPositioningCfg
	cfg.AtomicDedicatedPriorityScorerCfg = dedicatedPriorityScorerConfig

	return cfg
}

// ProvideAutoAssignOrderDistributor create auto assign distributor with required dependencies
func ProvideAutoAssignOrderDistributor(
	deps AutoAssignOrderDistributorDeps,
) (*AutoAssignOrderDistributor, func()) {
	distributor := &AutoAssignOrderDistributor{
		AutoAssignOrderDistributorDeps: deps,
	}

	return distributor, func() { distributor.Stop() }
}

// ProvideAutoAssignOrderDistributor create auto assign distributor with required dependencies
func ProvideAutoAssignOrderDistributorDeps(
	locationManager service.LocationManager,
	orderRepository repository.OrderRepository,
	driverRepository repository.DriverRepository,
	notifier service.Notifier,
	assignmentLogRepo repository.AssignmentLogRepository,
	driverTransaction payment.DriverTransactionService,
	statisticRepo repository.DriverStatisticRepository,
	statisticService service.StatisticService,
	driverOrderInfoRepo repository.DriverOrderInfoRepository,
	incentiveRepo incentive.IncentiveRepository,
	locker locker.Locker,
	orderConfig orderapi.OrderAPIConfig,
	meter metric.Meter,
	repSvc rep.REPService,
	contingencyCfg *orderapi.AtomicContingencyConfig,
	bus domain.EventBus,
	distributionConfig *dispatcherconfig.AtomicDistributionConfig,
	predictionService service.PredictionService,
	txnHelper transaction.TxnHelper,
	autoAssignConfig *AutoAssignConfig,
	acceptor *orderapi.Acceptor,
	driverService service.DriverServiceInterface,
	mapService mapservice.MapService,
	dedicatedZoneRepo repository.DedicatedZoneRepository,
	driverLocationRepository repository.DriverLocationRepository,
	tripRepository repository.TripRepository,
	onTopFareService service.OnTopFareService,
	throttledDispatchDetailRepository repository.ThrottledDispatchDetailRepository,
	throttledOrderRepository repository.ThrottledOrderRepository,
	dispatcher dispatcher.Dispatcher,
	deferredOrderRepository repository.DeferredOrderRepository,
	assignmentRepo repository.AssignmentRepository,
	rainSituationService service.RainSituationService,
	serviceAreaRepository repository.ServiceAreaRepository,
	zoneRepository repository.ZoneRepository,
	userClient userPb.UserServiceClient,
	redisClient datastore.RedisClient,
	delivery delivery.Delivery,
	workerContext safe.WorkerContext,
	servicePreferenceService service.ServicePreferenceService,
	distributionLogManager service.DistributionLogManager,
	orderHeartbeatService service.OrderHeartbeatService,
	orderDistributionEventManager service.OrderDistributionEventManager,
	distributionExperimentPlatformClient experimentplatform.DistributionExperimentPlatformClient,
	distributionService service.DistributionService,
	featureFlagService featureflag.Service,
	metricsRegistry metric.MetricsRegistry,
	fleetOrderClient fleetorder.FleetOrderClient,
	illegalDriverRepo repository.IllegalDriverRepository,
	assigningStateManager AssigningStateManager,
	throttledOrderDBConfig config.ThrottledOrderDBConfig,
) AutoAssignOrderDistributorDeps {
	deps := AutoAssignOrderDistributorDeps{
		OrderRepository:                          orderRepository,
		DriverRepository:                         driverRepository,
		LocationManager:                          locationManager,
		Notifier:                                 notifier,
		AssignmentLogRepo:                        assignmentLogRepo,
		DriverTransaction:                        driverTransaction,
		StatisticRepo:                            statisticRepo,
		StatisticService:                         statisticService,
		Locker:                                   locker,
		DriverOrderInfoRepo:                      driverOrderInfoRepo,
		OrderConfig:                              orderConfig,
		Environment:                              sim.NewRealTimeEnvironment(),
		Config:                                   *autoAssignConfig,
		ContingencyCfg:                           contingencyCfg,
		DistributionConfig:                       distributionConfig,
		PredictionService:                        predictionService,
		IncentiveRepo:                            incentiveRepo,
		fetchedMetric:                            meter.GetHistogram("autoassign_fetched_drivers", "Number of fetched drivers before auto-assigned", []float64{1, 5, 10, 20, 50, 70, 100, 200, 500}, "region"),
		stopMetric:                               meter.GetCounter("autoassign_orders", "Number of orders stop distributed by auto-assignment", "status", "round", "region"),
		lockingDriverMetric:                      meter.GetHistogram("autoassign_locking_driver_duration_ms", "Locking duration that system has been used to lock the driver", metric.DefaultHistogramBucket),
		batchOrderCountMetric:                    meter.GetHistogram("autoassign_batch_orders", "Number of orders in a batch", []float64{1, 5, 10, 15, 20, 30, 40, 50, 75, 100, 150, 200, 300, 400}, "zone"),
		batchRiderCountMetric:                    meter.GetHistogram("autoassign_batch_riders", "Number of riders in a batch", []float64{1, 5, 10, 20, 50, 70, 100, 200, 300, 500, 700, 1000, 1500, 2000, 2500, 3000, 4000}, "zone"),
		batchRedistributionCountMetric:           meter.GetCounter("autoassign_batch_redistribution", "in batch distribution, how many redistribution", "zone"),
		batchBundlingOrderCardinalityCountMetric: meter.GetCounter("autoassign_batch_bundling_order", "in each batch, how many orders are assigned alone, how many are assigned as part of a pair, and so on..", "cardinality", "zone"),
		batchOrderFallbackCountMetric:            meter.GetCounter("autoassign_batch_order_fallback", "Number of fallback order from batch", "zone"),
		batchPartialFailAssignOrderCountMetric:   meter.GetCounter("autoassign_batch_partial_fail_assignment", "how many orders are redistribute after the rider has accepted at least one previous order of the same assignment", "zone"),
		deferredOrderCountMetric:                 meter.GetCounter("autoassign_deferred_orders", "how many orders are deferred", "region"),
		deferredOrderTransitionToSingleCountMetric: meter.GetCounter("autoassign_deferred_order_transition_to_single", "how many deferred orders are transitioned to single optimize", "region"),
		deferredOrderRedistributionCountMetric:     meter.GetCounter("autoassign_deferred_order_redistribute_count", "how many time deferred order got distribute", "region"),
		deferredOrderAcceptCountMetric:             meter.GetCounter("autoassign_deferred_order_accept_count", "how many deferred orders are accepted by rider", "region"),
		noRedistributionStateOrderCountMetric:      meter.GetCounter("autoassign_no_redistribution_state_order_count", "how many orders without redistribution state set in Mongo", "region"),
		batchOptimizeMappedOrderDalianMetric:       meter.GetHistogram("autoassign_batch_optimize_mapped_order_dalian", "Number of percentage of mapped orders from dalian", []float64{2, 4, 6, 8, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100}, "zone", "round"),
		batchOptimizeRiderToOrderDalianMetric:      meter.GetHistogram("autoassign_batch_optimize_rider_to_order_dalian", "Number of percentage of rider to order that request to dalian", []float64{1, 5, 10, 20, 50, 70, 100, 200, 300, 500, 700, 1000, 1500, 2000, 2500, 3000, 4000}, "zone", "round"),
		Rep:                                        repSvc,
		Bus:                                        bus,
		TxnHelper:                                  txnHelper,
		AcceptOrderFn:                              new(orderassigner.AcceptOrderFunction),
		AcceptAssignmentFn:                         new(orderassigner.AcceptAssignmentFunction),
		DriverService:                              driverService,
		MapService:                                 mapService,
		DedicatedZoneRepo:                          dedicatedZoneRepo,
		DriverLocationRepository:                   driverLocationRepository,
		TripRepository:                             tripRepository,
		OnTopFareService:                           onTopFareService,
		ThrottledOrderRepository:                   throttledOrderRepository,
		ThrottledDispatchDetailRepository:          throttledDispatchDetailRepository,
		Dispatcher:                                 dispatcher,
		DeferredOrderRepository:                    deferredOrderRepository,
		AssignmentRepo:                             assignmentRepo,
		RainSituationService:                       rainSituationService,
		ServiceAreaRepository:                      serviceAreaRepository,
		ZoneRepository:                             zoneRepository,
		UserClient:                                 userClient,
		RedisClient:                                redisClient,
		WorkerContext:                              workerContext,
		OrderHeartbeatService:                      orderHeartbeatService,
		Delivery:                                   delivery,
		ServicePreferenceService:                   servicePreferenceService,
		DistributionLogManager:                     distributionLogManager,
		OrderDistributionEventManager:              orderDistributionEventManager,
		DistributionExperimentPlatformClient:       distributionExperimentPlatformClient,
		DistributionService:                        distributionService,
		FeatureFlagService:                         featureFlagService,
		metricsRegistry:                            metricsRegistry,
		FleetOrderClient:                           fleetOrderClient,
		IllegalDriverRepository:                    illegalDriverRepo,
		AssigningStateManager:                      assigningStateManager,
		ThrottledOrderDBConfig:                     throttledOrderDBConfig,
	}

	*deps.AcceptOrderFn = func(
		ctx context.Context,
		order *model.Order,
		orderID,
		driverID string,
		syncDelivery bool,
	) (*model.AcceptedOrderInfo, error) {
		return acceptor.AcceptOrder(ctx, order, orderID, driverID, syncDelivery, orderapi.UpdateOrderState, nil, "auto_accepted")
	}
	*deps.AcceptAssignmentFn = func(
		ctx context.Context,
		assignmentID model.AssignmentID,
		driverID string,
		syncDelivery bool,
	) (orderapi.AcceptAssignmentResult, error) {
		return acceptor.AcceptAssignment(ctx, assignmentID, driverID, syncDelivery, orderapi.UpdateOrderState, nil, "auto_accepted")
	}
	return deps
}

func (ds *AutoAssignOrderDistributor) ValidateDistribution(ctx context.Context, region string) (*model.ServiceArea, error) {
	serviceArea, err := ds.ServiceAreaRepository.GetByRegion(ctx, region)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			return nil, NewErrValidationDistribution(fmt.Errorf("%v service area region %s", err, region))
		}
		return nil, fmt.Errorf("%v service area region %s", err, region)
	}

	if serviceArea.DistributionLogic != model.DistributionLogicAutoAssign {
		return nil, NewErrValidationDistribution(fmt.Errorf("service area region %s is not auto assign", region))
	}

	return serviceArea, nil
}

func (distributor *AutoAssignOrderDistributor) Distribute(ctx context.Context, order *model.Order, expireAt time.Time, loc model.Location) (wait func(), err error) {
	region := order.DistributeRegions.DefaultRegion().String()

	serviceArea, err := distributor.ValidateDistribution(ctx, region)
	if err != nil {
		return nil, err
	}

	areaDistCfg := model.AreaDistributionConfig{
		NegativeBalanceGroupsConfig: serviceArea.NegativeBalanceGroups,
		TierNegativeBalanceConfig:   serviceArea.TierNegativeBalanceConfig,
		Revision:                    serviceArea.UpdatedAt.String(),
	}

	return distributor.Stop, distributor.RunTask(ctx, order, expireAt, loc, order.DistributeRegions, &serviceArea.Distribution, areaDistCfg)
}

// Distribute job to the most appropriate driver
func (distributor *AutoAssignOrderDistributor) RunTask(
	ctx context.Context,
	order *model.Order,
	expireAt time.Time,
	loc model.Location,
	regions []model.RegionCode,
	distCfg *model.AutoAssignDistribution,
	areaDistCfg model.AreaDistributionConfig,
) error {
	cfg := distributor.loadConfig(distCfg)
	memoizer := newSelectorMemoizer()

	searchRadiusByDistrict := evaluateSearchRadiusByDistrictInKm(
		ctx,
		distributor.AutoAssignOrderDistributorDeps.ZoneRepository,
		order,
		distributor.OrderConfig.AtomicOrderDBConfig,
		timeutil.BangkokNow(),
	)
	snapshotCfg := NewSnapshotCfg(
		distributor.Config.AtomicAutoAssignDbConfig.Get(),
	)
	task := autoAssignTask{
		AutoAssignOrderDistributorDeps: distributor.AutoAssignOrderDistributorDeps,

		distributionType: AutoAssignDistribution,

		order:                   order,
		expireAt:                expireAt,
		businessLocation:        loc,
		scorers:                 cfg.createScorers(distributor.AutoAssignOrderDistributorDeps, order, regions, searchRadiusByDistrict, snapshotCfg),
		driverSelector:          cfg.createDriverSelector(ctx, distributor.AutoAssignOrderDistributorDeps, order, areaDistCfg, memoizer),
		selectorMemoizer:        memoizer,
		regions:                 regions,
		getNotifyMsg:            service.EventOrderAssigned,
		notifyViaSocketIO:       cfg.NotifyViaSocketIOEnabled,
		debugLogger:             NewDistributionDebugger(distributor.Config.AtomicAutoAssignDbConfig.Get().EnableAutoAssignConsoleLog, order.DistributeRegions.DefaultRegion(), order.OrderID, model.DistributionLogicAutoAssign),
		lockedOrderIDs:          types.NewStringSet(),
		acceptDurationBuffer:    cfg.AtomicAutoAssignDbConfig.Get().AcceptDurationBufferInSeconds,
		stopMetricWithRegion:    orderassigner.NewStopMetricWithRegions(distributor.stopMetric, regions),
		distributionLogMetadata: model.NewSingleDistributionLogMetadata(order.DistributeRegions.DefaultRegion().String(), distributor.Config.AtomicAutoAssignDbConfig.Get().DistributionLogSamplingRatio),
		searchRadiusByDistrict:  searchRadiusByDistrict,
		snapshotCfg:             snapshotCfg,
		assignmentLogMemoize:    NewAssignmentLogMemoize(distributor.AutoAssignOrderDistributorDeps.AssignmentLogRepo),
		distributionStartedAt:   time.Now(),
		distCfgRevision:         areaDistCfg.Revision,
	}
	task.Config = cfg
	task.AreaDistCfg = areaDistCfg
	opt := model.AssignmentLogOpt{
		AutoAssigned:        true,
		SearchRiderStrategy: model.StrategyNameNone,
		Region:              order.DistributeRegions.DefaultRegion(),
	}

	assignmentLog, err := task.AssignmentLogRepo.AssignToDrivers(ctx, 1, task.order.OrderID, task.order.DeliveringRound, []repository.DriverDistance{}, opt)
	if err != nil {
		logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: cannot create empty assignment log")
		return nil
	}

	if task.isExpired() {
		logx.Info().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: order was expired, stop assigning")
		return nil
	}

	task.prepare(ctx, &opt)
	if assignmentLog != nil {
		task.assignmentLogMemoize.Set(*assignmentLog)
	}

	task.currentRound = task.calculateCurrentRound(ctx, task.order.OrderID, withCachedAssignmentLog(task.assignmentLogMemoize.GetOrLoad(ctx, task.order.OrderID)))

	withTimeoutCtx, cancel := context.WithTimeout(ctx, task.Config.AtomicAutoAssignDbConfig.Get().InsertDeferredOrderTimeout)
	defer cancel()

	if task.tryToDeferThrottleDispatch(withTimeoutCtx) {
		return nil
	}

	if task.tryDeferredDispatch(withTimeoutCtx, false) {
		return nil
	}

	if err := task.OrderDistributionEventManager.PublishSearchingOrAssigningDriver(ctx, *order, timeutil.BangkokNow()); err != nil {
		logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: cannot publish searching/assigning event")
	}

	if !order.IsDeferred && order.ActualAssigningAt == nil &&
		(order.ServiceType == model.ServiceFood || order.ServiceType == model.ServiceMart) {
		if err := task.OrderRepository.SetActualAssigningAtFromNil(withTimeoutCtx, order.OrderID, order.CreatedAt); err == nil {
			tmp := order.CreatedAt
			order.ActualAssigningAt = &tmp

			if err := task.Delivery.UpdateActualAssigning(ctx, order.OrderID, v1.UpdateActualAssigningRequest{
				ActualAssigningAt: tmp,
			}); err != nil {
				logx.Error().Str(logutil.OrderID, order.OrderID).Err(err).Msgf("calling update-actual-assigning failed for order: %s", order.OrderID)
			}
		} else {
			logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: cannot update actual_assigning_at")
		}
	}

	distributor.runningTasks.Add(1)
	bgCtx := distributor.WorkerContext.NewContextWithSameWaitGroup(ctx)
	distributor.Environment.RunGo(func() {
		defer task.debugLogger.Log()
		defer distributor.WorkerContext.ContextDone(bgCtx)

		task.Process(bgCtx, &distributor.runningTasks, opt)
	})

	return nil
}

func (distributor *AutoAssignOrderDistributor) RedistributeV2(
	ctx context.Context,
	order *model.Order,
	loc model.Location,
	ridersTriedAssigning *int,
	isRedistributionRequired bool,
) (wait func(), err error) {
	region := order.DistributeRegions.DefaultRegion().String()

	serviceArea, err := distributor.ValidateDistribution(ctx, region)
	if err != nil {
		return nil, err
	}

	areaDistCfg := model.AreaDistributionConfig{
		NegativeBalanceGroupsConfig: serviceArea.NegativeBalanceGroups,
		TierNegativeBalanceConfig:   serviceArea.TierNegativeBalanceConfig,
		Revision:                    serviceArea.UpdatedAt.String(),
	}

	return distributor.Stop, distributor.RunRedistributionTask(ctx, order, loc, *order.RedistributionState, ridersTriedAssigning, isRedistributionRequired, &serviceArea.Distribution, areaDistCfg)
}

func (distributor *AutoAssignOrderDistributor) Redistribute(
	ctx context.Context,
	order *model.Order,
	loc model.Location,
	redistributionState model.RedistributionState,
	ridersTriedAssigning *int,
) (wait func(), err error) {
	region := order.DistributeRegions.DefaultRegion().String()

	serviceArea, err := distributor.ValidateDistribution(ctx, region)
	if err != nil {
		return nil, err
	}

	areaDistCfg := model.AreaDistributionConfig{
		NegativeBalanceGroupsConfig: serviceArea.NegativeBalanceGroups,
		TierNegativeBalanceConfig:   serviceArea.TierNegativeBalanceConfig,
		Revision:                    serviceArea.UpdatedAt.String(),
	}

	// Backward compatible, since redistribution never save state in order before.
	if order.RedistributionState == nil {
		distributor.noRedistributionStateOrderCountMetric.Inc(region)
		logx.Warn().Str("order_id", order.OrderID).Msgf("Order do not have redistribution state save in DB")
		order.RedistributionState = &redistributionState
	}

	return distributor.Stop, distributor.RunRedistributionTask(ctx, order, loc, redistributionState, ridersTriedAssigning, false, &serviceArea.Distribution, areaDistCfg)
}

func (distributor *AutoAssignOrderDistributor) RunRedistributionTask(
	ctx context.Context,
	order *model.Order,
	loc model.Location,
	redistributionState model.RedistributionState,
	ridersTriedAssigning *int,
	isRedistributionRequired bool,
	distCfg *model.AutoAssignDistribution,
	areaDistCfg model.AreaDistributionConfig,
) error {
	cfg := distributor.loadConfig(distCfg)
	memoizer := newSelectorMemoizer()
	regions := order.DistributeRegions

	searchRadiusByDistrict := evaluateSearchRadiusByDistrictInKm(
		ctx,
		distributor.AutoAssignOrderDistributorDeps.ZoneRepository,
		order,
		distributor.OrderConfig.AtomicOrderDBConfig,
		timeutil.BangkokNow(),
	)
	snapshotCfg := NewSnapshotCfg(
		distributor.Config.AtomicAutoAssignDbConfig.Get(),
	)
	task := autoAssignTask{
		isUsingPredictionService:       redistributionState.IsUsingPredictionService,
		AutoAssignOrderDistributorDeps: distributor.AutoAssignOrderDistributorDeps,
		distributionType:               AutoAssignDistribution,
		order:                          order,
		expireAt:                       order.ExpireAt,
		businessLocation:               loc,
		scorers:                        cfg.createScorers(distributor.AutoAssignOrderDistributorDeps, order, regions, searchRadiusByDistrict, snapshotCfg),
		driverSelector:                 cfg.createDriverSelector(ctx, distributor.AutoAssignOrderDistributorDeps, order, areaDistCfg, memoizer),
		selectorMemoizer:               memoizer,
		regions:                        regions,
		getNotifyMsg:                   service.EventOrderAssigned,
		notifyViaSocketIO:              cfg.NotifyViaSocketIOEnabled,
		debugLogger:                    NewDistributionDebugger(distributor.Config.AtomicAutoAssignDbConfig.Get().EnableAutoAssignConsoleLog, order.DistributeRegions.DefaultRegion(), order.OrderID, model.DistributionLogicAutoAssign),
		lockedOrderIDs:                 types.NewStringSet(),
		stopMetricWithRegion:           orderassigner.NewStopMetricWithRegions(distributor.stopMetric, regions),
		consecutiveRoundsWithNoRiders:  redistributionState.ConsecutiveRoundsWithNoRiders,
		distributionLogMetadata:        model.NewSingleDistributionLogMetadata(order.DistributeRegions.DefaultRegion().String(), distributor.Config.AtomicAutoAssignDbConfig.Get().DistributionLogSamplingRatio),
		searchRadiusByDistrict:         searchRadiusByDistrict,
		snapshotCfg:                    snapshotCfg,
		distCfgRevision:                areaDistCfg.Revision,
		assignmentLogMemoize:           NewAssignmentLogMemoize(distributor.AutoAssignOrderDistributorDeps.AssignmentLogRepo),
		distributionStartedAt:          time.Now(),
	}
	task.Config = cfg
	task.AreaDistCfg = areaDistCfg

	opt := redistributionState.Opt
	task.prepare(ctx, &opt)

	task.currentRound = task.calculateCurrentRound(ctx, order.OrderID, withCachedAssignmentLog(task.assignmentLogMemoize.GetOrLoad(ctx, order.OrderID)))

	distributor.runningTasks.Add(1)
	bgCtx := distributor.WorkerContext.NewContextWithSameWaitGroup(ctx)
	elapsedTime := time.Now().Sub(redistributionState.LastDistributeAt)
	distributor.Environment.RunGoWithCtx(bgCtx, func() {
		defer task.debugLogger.Log()
		defer distributor.WorkerContext.ContextDone(bgCtx)

		if task.enabledAssignAPI && !isRedistributionRequired {
			assigningState, err := task.AssigningStateManager.GetAndRemoveAssigningState(bgCtx, order.OrderID, order.DeliveringRound)
			if err != nil {
				logx.Error().Str(logutil.OrderID, order.OrderID).Err(err).Msgf("failed to get and remove assigning state")
			} else if assigningState.IsUsable(areaDistCfg.Revision) {
				if ridersTriedAssigning != nil && *ridersTriedAssigning > 0 {
					assigningState.AssigningSetting.RidersTriedAssigning = *ridersTriedAssigning // forward riders tried assigning data to assigning API
				}
				task.ContinueAssign(bgCtx, &distributor.runningTasks, opt, *assigningState)
				return
			}
		} else if task.enabledAssignAPI && isRedistributionRequired {
			err := task.AssigningStateManager.RemoveAssigningState(bgCtx, order.OrderID, order.DeliveringRound)
			if err != nil {
				logx.Error().Str(logutil.OrderID, order.OrderID).Err(err).Msgf("failed to remove assigning state")
			}
		}

		task.applyConsecutiveRoundsWithNoRiders(redistributionState, ridersTriedAssigning)
		task.ProcessRedistribution(
			bgCtx,
			&distributor.runningTasks,
			redistributionState.RedistributionCount,
			elapsedTime,
			opt,
		)
	})

	return nil
}

func (distributor *AutoAssignOrderDistributor) BatchDistribute(ctx context.Context, throttledOrders []model.ThrottledOrder, zoneID primitive.ObjectID) (wait func(), err error) {
	zone, err := distributor.ZoneRepository.FindById(ctx, zoneID.Hex(), repository.WithReadSecondaryPreferred)
	if err != nil {
		return nil, err
	}
	region := zone.Region

	orderIDSet := types.NewStringSet()
	for _, ttOrder := range throttledOrders {
		orderIDSet.Add(ttOrder.OrderID)
	}
	orderIDs := orderIDSet.GetElements()

	serviceArea, err := distributor.ValidateDistribution(ctx, region)
	if err != nil {
		return nil, err
	}

	areaDistCfg := model.AreaDistributionConfig{
		NegativeBalanceGroupsConfig: serviceArea.NegativeBalanceGroups,
		TierNegativeBalanceConfig:   serviceArea.TierNegativeBalanceConfig,
	}
	var wg sync.WaitGroup

	return wg.Wait, distributor.RunBatchTask(ctx, orderIDs, zone, &serviceArea.Distribution, areaDistCfg, &wg)
}

func (distributor *AutoAssignOrderDistributor) RunBatchTask(ctx context.Context, orderIDs []string, zone model.Zone, distCfg *model.AutoAssignDistribution, areaDistCfg model.AreaDistributionConfig, wg *sync.WaitGroup) error {
	cfg := distributor.loadConfig(distCfg)

	throttledZone, err := distributor.ThrottledDispatchDetailRepository.FindByZoneID(ctx, zone.ID)
	if err != nil {
		logx.Error().Err(err).Str(logutil.ZoneCode, zone.ZoneCode).Msgf("autoassign: cannot run batch task, find throttle failed")
		return nil
	}

	snapshotCfg := NewSnapshotCfg(
		distributor.Config.AtomicAutoAssignDbConfig.Get(),
	)
	regions := []model.RegionCode{model.RegionCode(zone.Region)}
	task := autoAssignTask{
		AutoAssignOrderDistributorDeps: distributor.AutoAssignOrderDistributorDeps,
		distributionType:               AutoAssignDistribution,
		expireAt:                       timeutil.TheEndOfTime(),
		regions:                        regions,
		batchOrderIDs:                  orderIDs,
		batchZone:                      zone,
		throttlingZoneDetail:           &throttledZone,
		getNotifyMsg:                   service.EventOrderAssigned,
		notifyViaSocketIO:              cfg.NotifyViaSocketIOEnabled,
		debugLogger:                    &NoneDebuggerLog{},
		lockedOrderIDs:                 types.NewStringSet(),
		acceptDurationBuffer:           cfg.AtomicAutoAssignDbConfig.Get().AcceptDurationBufferInSeconds,
		stopMetricWithRegion:           orderassigner.NewStopMetricWithRegions(distributor.stopMetric, regions),
		distributionLogMetadata:        model.NewBatchDistributionLogMetadata(zone.Region, zone.DisplayName, distributor.Config.AtomicAutoAssignDbConfig.Get().DistributionLogSamplingRatio),
		snapshotCfg:                    snapshotCfg,
		throttlingMOLevel:              throttledZone.MultipleOrderAggressiveLevel,
		assignmentLogMemoize:           NewAssignmentLogMemoize(distributor.AutoAssignOrderDistributorDeps.AssignmentLogRepo),
		distributionStartedAt:          time.Now(),
	}
	task.Config = cfg
	task.AreaDistCfg = areaDistCfg

	wg.Add(1)

	bgCtx := distributor.WorkerContext.NewContextWithSameWaitGroup(ctx)
	distributor.Environment.RunGo(func() {
		defer task.debugLogger.Log()
		defer distributor.WorkerContext.ContextDone(bgCtx)

		task.ProcessBatch(bgCtx, wg)
	})

	return nil
}

func (distributor *AutoAssignOrderDistributor) CheckIfCandidatesEnough(ctx context.Context, orderID string, candidatesRequired int) (bool, error) {
	if candidatesRequired <= 0 {
		return true, nil
	}

	order, err := distributor.OrderRepository.Get(ctx, orderID)
	if err != nil {
		return false, err
	}

	region := order.DistributeRegions.DefaultRegion().String()
	serviceArea, err := distributor.ValidateDistribution(ctx, region)
	if err != nil {
		return false, err
	}

	areaDistCfg := model.AreaDistributionConfig{
		NegativeBalanceGroupsConfig: serviceArea.NegativeBalanceGroups,
	}

	return distributor.DistributorCheckIfCandidatesEnough(ctx, order, order.Routes[0].Location, order.DistributeRegions, &serviceArea.Distribution, areaDistCfg, candidatesRequired), nil
}

func (distributor *AutoAssignOrderDistributor) DistributorCheckIfCandidatesEnough(ctx context.Context, order *model.Order, loc model.Location, regions []model.RegionCode, distCfg *model.AutoAssignDistribution, areaDistCfg model.AreaDistributionConfig, candidatesRequired int) bool {
	cfg := distributor.loadConfig(distCfg)

	searchRadiusByDistrict := evaluateSearchRadiusByDistrictInKm(
		ctx,
		distributor.AutoAssignOrderDistributorDeps.ZoneRepository,
		order,
		distributor.OrderConfig.AtomicOrderDBConfig,
		timeutil.BangkokNow(),
	)
	snapshotCfg := NewSnapshotCfg(
		distributor.Config.AtomicAutoAssignDbConfig.Get(),
	)
	task := autoAssignTask{
		AutoAssignOrderDistributorDeps: distributor.AutoAssignOrderDistributorDeps,

		distributionType: AutoAssignDistribution,

		order:                     order,
		expireAt:                  order.ExpireAt,
		businessLocation:          loc,
		scorers:                   cfg.createScorers(distributor.AutoAssignOrderDistributorDeps, order, regions, searchRadiusByDistrict, snapshotCfg),
		driverSelector:            cfg.createDriverSelector(ctx, distributor.AutoAssignOrderDistributorDeps, order, areaDistCfg, newSelectorMemoizer()),
		regions:                   regions,
		getNotifyMsg:              service.EventOrderAssigned,
		notifyViaSocketIO:         cfg.NotifyViaSocketIOEnabled,
		withNotifiedExceptCurrent: true,
		debugLogger:               NewDistributionDebugger(distributor.Config.AtomicAutoAssignDbConfig.Get().EnableAutoAssignConsoleLog, order.DistributeRegions.DefaultRegion(), order.OrderID, model.DistributionLogicAutoAssign),
		lockedOrderIDs:            types.NewStringSet(),
		acceptDurationBuffer:      cfg.AtomicAutoAssignDbConfig.Get().AcceptDurationBufferInSeconds,
		stopMetricWithRegion:      orderassigner.NewStopMetricWithRegions(distributor.stopMetric, regions),
		searchRadiusByDistrict:    searchRadiusByDistrict,
		snapshotCfg:               snapshotCfg,
		assignmentLogMemoize:      NewAssignmentLogMemoize(distributor.AutoAssignOrderDistributorDeps.AssignmentLogRepo),
		distributionStartedAt:     time.Now(),
	}

	task.Config = cfg
	task.AreaDistCfg = areaDistCfg

	return task.CheckIfCandidatesEnough(ctx, candidatesRequired, model.AssignmentLogOpt{AutoAssigned: true})
}

func (distributor *AutoAssignOrderDistributor) loadConfig(cfg *model.AutoAssignDistribution) AutoAssignConfig {
	merged := distributor.Config
	merged.apply(cfg)

	return merged
}

func (distributor *AutoAssignOrderDistributor) Stop() {
	distributor.runningTasks.Wait()
}

func (cfg *AutoAssignConfig) apply(distCfg *model.AutoAssignDistribution) {
	cfg.AutoAssignDistribution = *distCfg
}

func (cfg *AutoAssignConfig) createPreFilterDrivers(regions []model.RegionCode) driverSelector {
	sl := []driverSelector{
		isOnlineOrAssigned,
		isNotOfflineLater,
		isNotLockedFromQueueing,
		isNotLockedForAcknowledgement,
		inRegion(regions),
	}
	return and(sl...)
}

func getMP1FromOrder(ctx context.Context, orderRepo repository.OrderRepository, order *model.Order) (*model.Order, error) {
	if !order.IsMp() {
		return nil, nil
	}
	mp1OrderID, err := order.MP1OrderID()
	if err != nil {
		return nil, err
	}
	if order.OrderID == mp1OrderID { // for optimize
		return order, nil
	}
	mp1, err := orderRepo.Get(ctx, mp1OrderID)
	if err != nil && !errors.Is(err, repository.ErrNotFound) {
		return nil, err
	}
	return mp1, nil
}

// CAUTION: The function is used concurrently, beware of mutating shared variables.
func (cfg *AutoAssignConfig) createBatchScorers(deps AutoAssignOrderDistributorDeps, order *model.Order, batchOrderSet sets.Of[string], snapshotCfg SnapshotCfg) []scorer {
	ctx := context.Background()
	scorers := make([]scorer, 0)

	scorers = append(scorers, newRegionScorer(model.RegionSetFromList(order.DistributeRegions)))
	if isTestOrder(order) {
		scorers = append(scorers, &testerScorer{})
	}
	if cfg.EnableRequireBox {
		switch order.Options.BoxOption {
		case model.BoxOptionRequireBox:
			scorers = append(scorers, &requireBoxScorer{})
		case model.BoxOptionExcludeBox:
			scorers = append(scorers, &excludeBoxScorer{})
		}
	}
	if !cfg.AtomicAutoAssignDbConfig.Get().DisableDedicatedZones {
		zoneMap, err := deps.DedicatedZoneRepo.FindAllInCache(ctx)
		if err != nil {
			logx.Error().Err(err).Str(logutil.TripID, order.TripID).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: creating dedicated zone scorer failed because findAllInCache")
		} else {
			var overrideOrder *model.Order
			mp1, err := getMP1FromOrder(ctx, deps.OrderRepository, order)
			if err != nil {
				logx.Warn().Err(err).Str(logutil.TripID, order.TripID).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: can't get mp1 in createBatchScorers")
			} else if mp1 != nil && order.OrderID != mp1.OrderID {
				// override only if both mp are in the same batch
				if batchOrderSet.Has(mp1.OrderID) {
					overrideOrder = mp1
				}
			}
			// empty set for applyOnlineRequirementServices since it can only be applied to single optimize
			scorers = append(scorers, newDedicatedZoneScorer(deps.MapService, zoneMap, mp1, overrideOrder, types.NewStringSet(), snapshotCfg.skipDedicatedRoundForRushOrders))
		}
	}

	return scorers
}

func (cfg *AutoAssignConfig) createScorers(deps AutoAssignOrderDistributorDeps, order *model.Order, regions []model.RegionCode, searchRadiusByDistrictInKm float64, snapShotCfg SnapshotCfg) []scorer {
	ctx := context.Background()
	scorers := make([]scorer, 2, 10)

	scorers[0] = &statusScorer{}
	scorers[1] = newRegionScorer(model.RegionSetFromList(regions))
	if isTestOrder(order) {
		scorers = append(scorers, &testerScorer{})
	}

	if cfg.EnableRequireBox {
		switch order.Options.BoxOption {
		case model.BoxOptionRequireBox:
			scorers = append(scorers, &requireBoxScorer{})
		case model.BoxOptionExcludeBox:
			scorers = append(scorers, &excludeBoxScorer{})
		}
	}
	if cfg.DistanceScoreWeight > 0 {
		scorers = append(scorers, newDistanceScorer(cfg.MinRadiusInKm, cfg.MaxRadiusInKM(order.ServiceType, searchRadiusByDistrictInKm), cfg.GroupDistInKm, cfg.DistanceScoreWeight))
	}
	scorers = append(scorers, newAutoAcceptScorer(cfg.AutoAcceptScore))
	if cfg.NewbieScoreWeight > 0 {
		scorers = append(scorers, newNewbieScorer(cfg.NewbieMaxDays, cfg.NewbieScoreWeight))
	}
	if cfg.AcceptanceScoreWeight > 0 {
		coldStartRate := cfg.AtomicAutoAssignDbConfig.Get().AcceptanceScoreColdStartRateFood
		switch order.ServiceType {
		case model.ServiceMessenger:
			coldStartRate = cfg.AtomicAutoAssignDbConfig.Get().AcceptanceScoreColdStartRateMessenger
		case model.ServiceMart:
			coldStartRate = cfg.AtomicAutoAssignDbConfig.Get().AcceptanceScoreColdStartRateMart
		case model.ServiceBike:
			coldStartRate = cfg.AtomicAutoAssignDbConfig.Get().AcceptanceScoreColdStartRateBike
		}
		scorers = append(scorers, newStatisticScorer(deps.DriverOrderInfoRepo, cfg.AcceptancePositiveRate, cfg.AcceptanceMinOrders, cfg.AcceptanceScoreWeight, cfg.AtomicAutoAssignDbConfig.Get().AcceptanceScoreWindowDay, coldStartRate, deps.Environment))
	}
	if cfg.IncentiveScoreWeight > 0 {
		scorers = append(scorers, newIncentiveScorer(deps.IncentiveRepo, deps.StatisticRepo, cfg.IncentiveScoreA, cfg.IncentiveScoreWeight, deps.Environment))
	}
	if cfg.IdleScoreWeight > 0 {
		scorers = append(scorers, newIdleScorer(deps.StatisticRepo, cfg.MinIdleInMinute, cfg.MaxIdleInMinute, cfg.IdleScoreWeight, deps.Environment))
	}
	if cfg.PriorityDedicatedRidersEnabled {
		scorers = append(scorers, newDedicatedPriorityScorer(cfg.PriorityDedicatedRidersAllowOrderServiceTypes, cfg.PriorityDedicatedRidersDistance, cfg.AtomicAutoAssignDbConfig.Get().DisableDedicatedZones, cfg.AtomicDedicatedPriorityScorerCfg.Get().DedicatedPriorityBaseConfigMap))
	}
	if cfg.BoxTypeScoreEnabled {
		scorers = append(scorers, newBoxTypeScorer(cfg.BoxTypeScoreWeight, cfg.BoxTypeScoreSettings, cfg.BoxTypeScoreServiceTypes))
	}
	if !cfg.AtomicAutoAssignDbConfig.Get().DisableDedicatedZones {
		zoneMap, err := deps.DedicatedZoneRepo.FindAllInCache(ctx)
		if err != nil {
			logx.Error().Err(err).Str(logutil.TripID, order.TripID).Str(logutil.OrderID, order.OrderID).Msg("autoassign: creating dedicated zone scorer failed because findAllInCache")
		} else {
			mp1, err := getMP1FromOrder(ctx, deps.OrderRepository, order)
			if err != nil {
				logx.Warn().Err(err).Str(logutil.TripID, order.TripID).Str(logutil.OrderID, order.OrderID).Msg("autoassign: can't get mp1 in createScorers")
			}
			scorers = append(scorers, newDedicatedZoneScorer(deps.MapService, zoneMap, mp1, nil, snapShotCfg.applyOnlineRequirementForDedicatedServices, snapShotCfg.skipDedicatedRoundForRushOrders))
		}
	}

	return scorers
}

// CAUTION: The function is used concurrently, beware of mutating shared variables.
func (cfg *AutoAssignConfig) createBatchDriverSelector(ctx context.Context, deps AutoAssignOrderDistributorDeps, order *model.Order, areaDistCfg model.AreaDistributionConfig, memoizer *selectorMemoizer, illegalDriverIDs types.StringSet) driverSelector {
	sl := cfg.createDriverSelector(ctx, deps, order, areaDistCfg, memoizer)
	batchSl := []driverSelector{sl}
	batchSl = append(batchSl, nonIllegalDriver(illegalDriverIDs, order.OrderID, order.DeliveringRound, deps.IllegalDriverRepository, cfg.AtomicAutoAssignDbConfig.Get()))
	return and(batchSl...)
}

// CAUTION: The function is used concurrently, beware of mutating shared variables.
func (cfg *AutoAssignConfig) createDriverSelector(ctx context.Context, deps AutoAssignOrderDistributorDeps, order *model.Order, areaDistCfg model.AreaDistributionConfig, memoizer *selectorMemoizer) driverSelector {
	sl := make([]driverSelector, 0, 3)

	sl = append(sl, deprioritize(cfg.SmartDistributionDeprioritizationRatio/100.0, cfg.SmartDistributionBlacklistZoneCodes, cfg.AtomicSupplyPositioningConfig, deps.ZoneRepository, order, memoizer))

	if deps.ContingencyCfg.Get().ContingencyModeEnabled || order.IsCashCollection() {
		sl = append(sl, checkBanWithdraw(deps.DriverTransaction, memoizer))
	}

	if !isTestOrder(order) && !cfg.AtomicAutoAssignDbConfig.Get().EnableDistributeAnyOrdersToTester {
		sl = append(sl, isNotTesterSelector)
	}

	if cfg.EnableMockRider {
		hasMock, mockDriverID := order.GetMockDriver()
		if hasMock {
			driv, err := deps.DriverRepository.GetProfile(ctx, mockDriverID)
			if err == nil && driv.IsTester() {
				sl = append(sl, isMockDriver(mockDriverID))
			}
		}
	}

	if order.IsNonDalianRush() {
		sl = append(sl, isOnlineOnly)
	}

	sl = append(sl, calculateCreditWalletIsEnough(deps.DriverTransaction, order, deps.Rep, true, deps.ContingencyCfg.Get(),
		areaDistCfg.NegativeBalanceGroupsConfig, areaDistCfg.TierNegativeBalanceConfig, deps.Config.EnableCashCollection, memoizer))

	sl = append(sl, calculateCashEnough(deps.DriverTransaction, order, memoizer))

	sl = append(sl, nonExDriver(order.OrderID, deps.OrderRepository))

	if userPhones := getUserPhoneNumberFromOrder(order); userPhones.Count() != 0 {
		sl = append(sl, hasDistinctPhoneNumberFromUser(userPhones, memoizer))
	} else {
		logx.Error().Str(logutil.TripID, order.TripID).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: cannot get phone number for user %s", order.UserID)
	}

	if cfg.AtomicAutoAssignDbConfig != nil && cfg.AtomicAutoAssignDbConfig.Get().EnableUidSelector {
		sl = append(sl, hasDistinctUidFromUser(deps.RedisClient, deps.UserClient, order.UserID, memoizer, cfg.IsLINEUserIDUsed))
	}

	disableDedicatedZone := false
	if cfg.AtomicAutoAssignDbConfig != nil {
		disableDedicatedZone = cfg.AtomicAutoAssignDbConfig.Get().DisableDedicatedZones
	}
	if cfg.AtomicAutoAssignDbConfig != nil && cfg.AtomicAutoAssignDbConfig.Get().EnableDedicateServiceType {
		sl = append(sl, matchDriverServiceTypes(order.ServiceType, disableDedicatedZone))
	}

	sl = append(sl, checkIfRiderOptedOut(order.ServiceType, deps.ServicePreferenceService, ctx, cfg.ServicePreference))

	if cfg.AtomicAutoAssignDbConfig != nil && cfg.AtomicAutoAssignDbConfig.Get().EnableSilentBan {
		sl = append(sl, checkSilentBanned(order.ServiceType))
	}

	sl = append(sl, isNotOfflineLater)
	sl = append(sl, isNotLockedForAcknowledgement)
	sl = append(sl, checkIfRiderEligibleForMp1(ctx, order, deps.OrderRepository))

	return and(sl...)
}

type AutoAssignDistributionType int

const (
	AutoAssignDistribution AutoAssignDistributionType = iota
)

type SnapshotCfg struct {
	// configs that are set before creating task
	applyOnlineRequirementForDedicatedServices types.StringSet
	skipDedicatedRoundForRushOrders            bool

	// configs that are set in prepare
}

func NewSnapshotCfg(autoAssignDbCfg dispatcherconfig.AutoAssignDbConfig) SnapshotCfg {
	services := types.NewStringSet()
	if autoAssignDbCfg.ApplyOnlineRequirementForDedicatedServices.IsInitialized() {
		services = autoAssignDbCfg.ApplyOnlineRequirementForDedicatedServices
	}
	return SnapshotCfg{
		applyOnlineRequirementForDedicatedServices: services,
		skipDedicatedRoundForRushOrders:            autoAssignDbCfg.SkipDedicatedRoundForRushOrders,
	}
}

type autoAssignTask struct {
	AutoAssignOrderDistributorDeps

	distributionType AutoAssignDistributionType

	batchOrderIDs  []string
	batchZone      model.Zone
	lockedOrderIDs types.StringSet

	order                         *model.Order
	expireAt                      time.Time
	businessLocation              model.Location
	scorers                       []scorer
	driverSelector                driverSelector
	selectorMemoizer              *selectorMemoizer
	currentRound                  int
	getNotifyMsg                  service.AutoAssignedEventFn
	regions                       []model.RegionCode
	isUsingPredictionService      bool
	notifyViaSocketIO             bool
	withNotifiedExceptCurrent     bool
	restaurantBlacklistSets       model.RestaurantBlacklistStringSets
	acceptDurationBuffer          int64
	consecutiveRoundsWithNoRiders int
	useBikePriority               bool
	distributionStartedAt         time.Time
	// TODO: LMF-14279 remove when enabled nationwide
	enabledAssignAPI bool

	// Throttling
	isInThrottlingZone   bool
	throttlingZoneDetail *model.ThrottledDispatchDetailWithZoneCode
	throttlingMOLevel    string

	// isForSaversExperiment tells whether the current task is a shadow task
	isForSaversExperiment bool

	// total number of notified driver on the current distribution process
	debugLogger             DebuggerLog
	orderZoneCacher         *orderZoneCacher
	stopMetricWithRegion    orderassigner.StopMetricWithRegions
	distributionLogMetadata model.DistributionLogMetadata
	predictionModelVersions model.PredictionModelVersions

	// switchback experiments
	switchbackExperiments *model.SwitchbackExperiments

	// searchRadiusByDistrict only supports single distribution
	searchRadiusByDistrict float64

	// snapshotCfg is the config captured at the start of distribution process and kept consistent within one distribution
	snapshotCfg SnapshotCfg

	assignmentLogMemoize AssignmentLogMemoizer

	distCfgRevision string
}

func (task *autoAssignTask) multipleOrderAggressiveLevel() string {
	overrideAggressiveLevel := task.Config.AtomicAutoAssignDbConfig.Get().PreferNotBundledMOAggressiveLevel
	if task.order != nil && task.order.IsPreferNotBundled() && overrideAggressiveLevel != "" {
		return overrideAggressiveLevel
	}
	if task.throttlingMOLevel != "" {
		return task.throttlingMOLevel
	}
	return task.Config.MultipleOrderAggressiveLevel
}

func (task *autoAssignTask) multipleOrderAggressiveLevelWithSwitchbackExperiments() string {
	if task.switchbackExperiments != nil && task.switchbackExperiments.Params.MOAggressiveLevel != "" {
		return task.switchbackExperiments.Params.MOAggressiveLevel
	}
	return task.multipleOrderAggressiveLevel()
}

func (task *autoAssignTask) bikeBiasLevelWithSwitchbackExperiments() string {
	if task.switchbackExperiments != nil && task.switchbackExperiments.Params.BikeBiasLevel != "" {
		return task.switchbackExperiments.Params.BikeBiasLevel
	}
	return task.Config.BikeBiasLevel
}

func (task *autoAssignTask) parseBiasLevel(levelStr string) int {
	if !strings.HasPrefix(levelStr, "L") {
		return 0
	}

	level, err := strconv.Atoi(strings.TrimPrefix(levelStr, "L"))
	if err != nil {
		return 0
	}

	return level
}

func (task *autoAssignTask) serviceBiasWeightsWithSwitchbackExperiments() *prediction.ServiceBiasWeights {
	if task.useBikePriority {
		return nil
	}

	cfg := task.Config.AtomicAutoAssignDbConfig.Get()

	foodLevelStr := "L0"
	martLevelStr := "L0"
	bikeLevelStr := task.bikeBiasLevelWithSwitchbackExperiments()

	weights := prediction.ServiceBiasWeights{
		Food: 1,
		Mart: 1,
		Bike: 1,
	}

	foodLevel := task.parseBiasLevel(foodLevelStr)
	martLevel := task.parseBiasLevel(martLevelStr)
	bikeLevel := task.parseBiasLevel(bikeLevelStr)

	if foodLevel < len(cfg.FoodServiceBiasWeights) {
		weights.Food = cfg.FoodServiceBiasWeights[foodLevel]
	}

	if martLevel < len(cfg.MartServiceBiasWeights) {
		weights.Mart = cfg.MartServiceBiasWeights[martLevel]
	}

	if bikeLevel < len(cfg.BikeServiceBiasWeights) {
		weights.Bike = cfg.BikeServiceBiasWeights[bikeLevel]
	}

	return &weights
}

func (task *autoAssignTask) fallbackDalianOSRMPhase() int64 {
	if task.Config.DalianOSRMPhase == 1 || task.Config.DalianOSRMPhase == 2 {
		return task.Config.DalianOSRMPhase
	}

	return 1
}

func (task *autoAssignTask) batchAssignmentEnabled() bool {
	batchAssignmentEnabled := task.Config.AtomicAutoAssignDbConfig.Get().EnableBatchAssignment
	return batchAssignmentEnabled && task.Config.AutoAssignDistribution.BatchAssignmentEnabled
}

func (task *autoAssignTask) dalianOptimizeOSRMEnabled() bool {
	dalianOptimizeOSRMEnabled := !task.Config.AtomicAutoAssignDbConfig.Get().DisableDalianOptimizeOSRM
	return dalianOptimizeOSRMEnabled && task.Config.DalianOptimizeOSRMEnabled
}

func (task *autoAssignTask) isExpired() bool {
	return task.Environment.Now().After(task.expireAt)
}

// acceptDurationWithBufferInSeconds will be redundant in this transition phase
// please also consider to make the changes in assigner package
func (task *autoAssignTask) acceptDurationWithBufferInSeconds() int64 {
	return task.Config.AcceptingDurationInSecond + task.acceptDurationBuffer
}

func (task *autoAssignTask) rushMode() prediction.RushMode {
	return task.Config.RushMode
}

// rushModeWithSwitchbackExperiments will be redundant in this transition phase
// please also consider to make the changes in assigner package
func (task *autoAssignTask) rushModeWithSwitchbackExperiments() prediction.RushMode {
	if task.switchbackExperiments != nil && task.switchbackExperiments.Params.RushMode != "" {
		return task.switchbackExperiments.Params.RushMode
	}
	return task.rushMode()
}

func (task *autoAssignTask) applySwitchbackExperiments(region string) {
	switchbackExperiments, err := task.DistributionExperimentPlatformClient.GetSwitchbackExperimentsWithConditions("", &exp.Condition{Name: "region", Value: region})
	if err != nil {
		logx.Error().Err(err).Msgf("[applySwitchbackExperiments] failed to get switchback experiment from EP for region %v", region)
		return
	}
	if switchbackExperiments != nil {
		task.switchbackExperiments = switchbackExperiments
		logx.Info().Msgf("autoassign: [applySwitchbackExperiments] testing an experiment %s", switchbackExperiments.ToString())
	}
}

func (task *autoAssignTask) modelVersionWithSwitchbackExperiments(name model.PredictionModelName) string {
	if task.switchbackExperiments != nil {
		if predictionModel, ok := task.switchbackExperiments.Params.PredictionModels[name]; ok {
			return predictionModel.Hash
		}
	}
	if name == model.PredictModel {
		return task.predictionModelVersions.PredictVersion
	} else if name == model.OptimizeModel {
		return task.predictionModelVersions.OptimizeVersion
	} else if name == model.BatchOptimizeModel {
		return task.predictionModelVersions.BatchOptimizeVersion
	} else if name == model.RouteModel {
		return task.predictionModelVersions.RouteVersion
	}
	return ""
}

// CAUTION: The function is used concurrently, beware of mutating shared variables.
func withoutNotifiedDrivers(ctx context.Context, drivers []service.DriverWithLocation, order *model.Order, notifiedDrivers []model.Record) ([]service.DriverWithLocation, *model.RidersFilterData) {
	filterData := model.NewRiderFilterData()

	if len(notifiedDrivers) == 0 {
		return drivers, filterData
	}

	result := make([]service.DriverWithLocation, 0, len(drivers))

	for _, dl := range drivers {
		var seq int
		var rec *model.Record

		for i, r := range notifiedDrivers {
			if dl.Driver.DriverID == r.DriverID {
				copyR := r
				rec = &copyR
				seq = i
				break
			}
		}

		if rec != nil {
			filterData.Add(model.WithoutNotifiedRiders, dl.Driver.DriverID)
			logx.Info().Str(logutil.TripID, order.TripID).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: driverID=%s time=%v seq=%v round=%v driver was already notified.",
				dl.Driver.DriverID, rec.PushAt, seq, rec.Round)
			continue
		}

		result = append(result, dl)
	}
	return result, filterData
}

func (task *autoAssignTask) newNotifiedSet(ctx context.Context, orderID string) types.StringSet {
	set := types.NewStringSet()

	notifiedDrivers, err := task.AssignmentLogRepo.AssignedDrivers(ctx, orderID, repository.WithReadPrimary)
	if err != nil {
		if err != mongodb.ErrDataNotFound {
			logx.Error().Str(logutil.OrderID, orderID).Err(err).Msgf("autoassign: orderID=%s, cannot get assignment log with error %v.", orderID, err)
		}
		notifiedDrivers = []model.Record{}
	}

	for _, record := range notifiedDrivers {
		set.Add(record.DriverID)
	}
	return set
}

// CAUTION: The function is used concurrently, beware of mutating shared variables.
func withoutCurrentDriver(_ context.Context, drivers []service.DriverWithLocation, order *model.Order, isForSaversExperiment bool) ([]service.DriverWithLocation, *model.RidersFilterData) {
	filterData := model.NewRiderFilterData()
	result := make([]service.DriverWithLocation, 0, len(drivers))
	for _, dl := range drivers {
		if dl.Driver.DriverID == order.Driver {
			filterData.Add(model.WithoutCurrentRider, dl.Driver.DriverID)
			if !isForSaversExperiment {
				logx.Info().Str(logutil.TripID, order.TripID).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: driverID=%s current driver shouldn't be notified.", dl.Driver.DriverID)
			}
			continue
		}
		result = append(result, dl)
	}
	return result, filterData
}

func (task *autoAssignTask) searchDrivers(ctx context.Context, location model.Location) (service.SearchResult, error) {
	preFilterData := model.NewRiderFilterData()
	defer task.DistributionLogManager.CapturePreFilterEvent(ctx, preFilterData, task.distributionLogMetadata)

	bikeService := false
	if task.order != nil && task.order.ServiceType == model.ServiceBike {
		bikeService = true
	}
	searchStartedAt := timeutil.BangkokNow()
	searchResult, err := task.LocationManager.SearchDriversLimit(ctx, location, task.Config.MaxRadiusInMeter(task.order.ServiceType, task.searchRadiusByDistrict), task.Config.LimitDriverFetch, task.order.ServiceType, task.order.Region, task.order.OrderID)
	if err != nil {
		return searchResult, err
	}

	result := searchResult.Results
	logx.Info().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("[searchDrivers] isBike: %v, found driver %d with limit %d", bikeService, len(result), task.Config.LimitDriverFetch)

	task.DistributionLogManager.CaptureSearchEvent(ctx, result, []model.Order{*task.order}, searchStartedAt, task.distributionLogMetadata)
	task.logFetchedDrivers(result)

	var filterAndSortDriversFilterData *model.RidersFilterData
	result, filterAndSortDriversFilterData = filterAndSortDrivers(
		ctx,
		task.AutoAssignOrderDistributorDeps,
		task.order,
		result,
		task.scorers,
		task.driverSelector,
		task.Config,
		task.debugLogger,
		newSearchAndFilterOption(task),
		task.getOrLoadNotifiedDrivers(ctx, task.order.OrderID),
	)
	preFilterData.AddExisting(filterAndSortDriversFilterData)

	logx.Info().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("[searchDrivers] isBike: %v, driver count after filter %d", bikeService, len(result))

	// if the task is not using prediction service or there is no drivers from the scorer, return the drivers immediately
	if !task.isUsingPredictionService || len(result) == 0 {
		return service.SearchResult{
			Results:  result,
			Strategy: searchResult.Strategy,
		}, nil
	}

	whitelisted, whitelistDriversFilterData := whitelistDrivers(
		ctx,
		result,
		task.order,
		task.Config,
		task.debugLogger,
	)
	preFilterData.AddExisting(whitelistDriversFilterData)

	// after this will be dedicated zone filter all the way to optimize. shouldn't have more filters after this one,
	// since this will split the candidates in to two groups
	if task.orderZoneCacher == nil {
		task.orderZoneCacher = newOrderZoneCacher(task.MapService)
	}
	zoneMap, err := task.DedicatedZoneRepo.FindAllInCache(ctx)
	if err != nil {
		logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("dedicated zone distribution failed because findAllInCache of returns")
	}

	dedicatedFilterData := model.NewRiderFilterData()
	var dedicated []service.DriverWithLocation
	for _, w := range whitelisted {
		if !task.isDriverInDedicatedZoneWithPriorityRound(w, zoneMap) {
			dedicatedFilterData.Add(model.NotDedicated, w.Driver.DriverID)
			continue
		}
		dedicated = append(dedicated, w)
	}
	task.DistributionLogManager.CapturePreOptimizeFilterEvent(ctx, dedicatedFilterData, model.DedicatedRound, task.distributionLogMetadata)

	ridersAlreadyOptimized := types.NewStringSet()

	var candidates []service.DriverWithLocation
	if len(dedicated) != 0 {
		for _, d := range dedicated {
			ridersAlreadyOptimized.Add(d.Driver.DriverID)
		}
		candidates, _ = task.callOptimize(ctx, dedicated, model.DedicatedRound) // if there's a dedicated rider that can't get past optimize, this will be call unnecessarily every time
		if len(candidates) != 0 {
			logx.Info().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("[searchDrivers] isBike: %v, dedicated driver count after optimize %d", bikeService, len(candidates))
			return service.SearchResult{
				Results:  candidates,
				Strategy: searchResult.Strategy,
			}, nil
		}
	}

	spFilterData := model.NewRiderFilterData()
	var prioritizedByRecommendedArea []service.DriverWithLocation
	for _, w := range whitelisted {
		if ridersAlreadyOptimized.Has(w.Driver.DriverID) {
			spFilterData.Add(model.AlreadyOptimized, w.Driver.DriverID)
			continue
		}
		if _, should := task.shouldAssignByRecommendedArea(ctx, w); !should {
			spFilterData.Add(model.NotSupplyPositioned, w.Driver.DriverID)
			continue
		}
		prioritizedByRecommendedArea = append(prioritizedByRecommendedArea, w)
	}
	task.DistributionLogManager.CapturePreOptimizeFilterEvent(ctx, spFilterData, model.SupplyPositioningRound, task.distributionLogMetadata)

	if len(prioritizedByRecommendedArea) != 0 {
		for _, d := range prioritizedByRecommendedArea {
			ridersAlreadyOptimized.Add(d.Driver.DriverID)
		}
		candidates, _ = task.callOptimize(ctx, prioritizedByRecommendedArea, model.SupplyPositioningRound)
		if len(candidates) != 0 {
			logx.Info().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("[searchDrivers] isBike: %v, prioritizedByRecommendedArea driver count after optimize %d", bikeService, len(candidates))
			return service.SearchResult{
				Results:  candidates,
				Strategy: searchResult.Strategy,
			}, nil
		}
	}

	sharedFilterData := model.NewRiderFilterData()
	var shared []service.DriverWithLocation
	for _, w := range whitelisted {
		if ridersAlreadyOptimized.Has(w.Driver.DriverID) {
			sharedFilterData.Add(model.AlreadyOptimized, w.Driver.DriverID)
			continue
		}
		if task.isDriverWrongZone(w, zoneMap) {
			sharedFilterData.Add(model.DedicatedZoneUnmatched, w.Driver.DriverID)
			continue
		}
		shared = append(shared, w)
	}
	task.DistributionLogManager.CapturePreOptimizeFilterEvent(ctx, sharedFilterData, model.SharedRound, task.distributionLogMetadata)

	candidates, err = task.callOptimize(ctx, shared, model.SharedRound)
	if err != nil {
		task.isUsingPredictionService = false
		return service.SearchResult{
			Results:  result,
			Strategy: searchResult.Strategy,
		}, ErrPredictionService
	}
	logx.Info().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("[searchDrivers] isBike: %v, candidates count after optimize %d", bikeService, len(candidates))
	return service.SearchResult{
		Results:  candidates,
		Strategy: searchResult.Strategy,
	}, nil
}

type searchAndFilterOption struct {
	withNotifiedExceptCurrent bool
	isForSaversExperiment     bool
	isUsingPredictionService  bool
}

func newSearchAndFilterOption(task *autoAssignTask) searchAndFilterOption {
	return searchAndFilterOption{
		withNotifiedExceptCurrent: task.withNotifiedExceptCurrent,
		isForSaversExperiment:     task.isForSaversExperiment,
		isUsingPredictionService:  task.isUsingPredictionService,
	}
}

// filterAndSortDrivers filter and sort for eligible drivers for given order
// CAUTION: The function is used concurrently, beware of mutating shared variables.
func filterAndSortDrivers(
	ctx context.Context,
	deps AutoAssignOrderDistributorDeps,
	order *model.Order,
	drivers []service.DriverWithLocation,
	scorers []scorer,
	drvSelector driverSelector,
	autoAssignConfig AutoAssignConfig,
	debugLogger DebuggerLog,
	searchAndFilterOpt searchAndFilterOption,
	notifiedDrivers []model.Record,
) ([]service.DriverWithLocation, *model.RidersFilterData) {
	filterData := model.NewRiderFilterData()
	if searchAndFilterOpt.withNotifiedExceptCurrent {
		// In the case where we want to search for possible candidates,
		// the behaviour should be the same as reassigning an order where
		// notified drivers (except one that already accepts) can accept the order
		var withoutCurrentDriverFilterData *model.RidersFilterData
		drivers, withoutCurrentDriverFilterData = withoutCurrentDriver(ctx, drivers, order, searchAndFilterOpt.isForSaversExperiment)
		filterData.AddExisting(withoutCurrentDriverFilterData)
	} else {
		var withoutNotifiedDriversData *model.RidersFilterData
		drivers, withoutNotifiedDriversData = withoutNotifiedDrivers(ctx, drivers, order, notifiedDrivers)
		filterData.AddExisting(withoutNotifiedDriversData)
	}

	scores, scorerFilterData := calcScoreThenSort(ctx, scorers, order, drivers, searchAndFilterOpt.isUsingPredictionService, autoAssignConfig.DebugScorer, searchAndFilterOpt.isForSaversExperiment)
	filterData.AddExisting(scorerFilterData)

	drivers = make([]service.DriverWithLocation, 0, len(scores))
	for _, das := range scores {
		if passed, filterName, msg := drvSelector(ctx, das.DriverWithLocation.Driver); !passed {
			filterData.Add(filterName, das.DriverWithLocation.Driver.DriverID)
			if !searchAndFilterOpt.isForSaversExperiment {
				logx.Info().Str(logutil.TripID, order.TripID).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: driverID=%s, the driver was not selected by the selector, description=%s", das.DriverWithLocation.Driver.DriverID, msg)
			}
			continue
		}
		drivers = append(drivers, *das.DriverWithLocation)
	}
	debugLogger.AppendLog(AUTO_ASSIGN_ALL_FOUNDED_DRIVERS, drivers)
	return drivers, filterData
}

// CAUTION: The function is used concurrently, beware of mutating shared variables.
func whitelistDrivers(
	_ context.Context,
	drivers []service.DriverWithLocation,
	order *model.Order,
	autoAssignConfig AutoAssignConfig,
	debugLogger DebuggerLog,
) ([]service.DriverWithLocation, *model.RidersFilterData) {
	filterData := model.NewRiderFilterData()
	whitelist := types.NewStringSet(autoAssignConfig.PredictionWhitelist...)
	blacklist := types.NewStringSet(autoAssignConfig.PredictionBlacklist...)

	whitelisted := make([]service.DriverWithLocation, 0, len(drivers))
	for _, r := range drivers {
		isDriverWhitelisted := order.IsDalianMP || r.Driver.Status == model.StatusOnline || (!blacklist.Has(r.Driver.DriverID) && (whitelist.Has(r.Driver.DriverID) || whitelist.Count() == 0))
		if !isDriverWhitelisted {
			filterData.Add(model.RiderNotWhitelisted, r.Driver.DriverID)
			continue
		}

		whitelisted = append(whitelisted, r)
	}
	debugLogger.AppendLog(AUTO_ASSIGN_WHITELISTED_DRIVERS, whitelisted)
	return whitelisted, filterData
}

type ineligibleDriversWithOtherMPDriversCalculator struct {
	mpIDToOtherDriverID map[string]string
	otherMPDriversSet   types.StringSet
}

func newIneligibleDriversWithOtherMPDriversCalculator(driverMPs []repository.DriverMP) ineligibleDriversWithOtherMPDriversCalculator {
	mpIDToOtherDriverID := repository.DriverMPs(driverMPs).AsMapMPIDToDriverID()

	return ineligibleDriversWithOtherMPDriversCalculator{
		mpIDToOtherDriverID: mpIDToOtherDriverID,
		otherMPDriversSet:   types.NewStringSet(maps.Values(mpIDToOtherDriverID)...),
	}
}

// Calculate CAUTION: The function is used concurrently, beware of mutating shared variables.
func (c ineligibleDriversWithOtherMPDriversCalculator) Calculate(currentIneligibleDrivers types.StringSet, order *model.Order) (types.StringSet, *model.RidersFilterData) {
	filterData := model.NewRiderFilterData()
	result := c.otherMPDriversSet.Clone()

	if order.Options.MpID != "" {
		otherMPDriver := c.mpIDToOtherDriverID[order.Options.MpID]
		if otherMPDriver != "" {
			result.Remove(otherMPDriver)
		}
	}

	additionalIneligibleDrivers := result.Minus(currentIneligibleDrivers)
	filterData.AddMany(model.OtherMPRider, additionalIneligibleDrivers.GetElements())

	result.Add(currentIneligibleDrivers.GetElements()...) // add to result to prevent mutation
	return result, filterData
}

func (task *autoAssignTask) isDriverWrongZone(d service.DriverWithLocation, zoneMap map[string]model.DedicatedZone) bool {
	// check config at this level so everything behaves like there's no dedicated when feature disabled
	if task.Config.AtomicAutoAssignDbConfig.Get().DisableDedicatedZones {
		return false
	}
	if len(d.Driver.DedicatedZones) == 0 {
		return false
	}
	for _, zLabel := range d.Driver.DedicatedZones {
		zone, found := zoneMap[zLabel]
		if !found {
			logx.Warn().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: driverID=%s driver has dedicated zone %s but this dedicated zone doesn't exist", d.Driver.DriverID, zLabel)
			continue
		}
		if task.orderZoneCacher.validateOrderZone(task.order, zone) {
			// if order is in at least one same zone as the rider then rider can accept
			return false
		}
	}
	return true
}

func (task *autoAssignTask) isDriverInDedicatedZoneWithPriorityRound(d service.DriverWithLocation, zoneMap map[string]model.DedicatedZone) bool {
	// check config at this level so everything behaves like there's no dedicated when feature disabled
	if task.Config.AtomicAutoAssignDbConfig.Get().DisableDedicatedZones {
		return false
	}
	isSkipDedicatedRoundForRushOrder := task.snapshotCfg.skipDedicatedRoundForRushOrders && task.order.IsDalianRush()
	if isSkipDedicatedRoundForRushOrder || task.order.IsSkipDedicatedRound {
		return false
	}
	for _, zLabel := range d.Driver.DedicatedZones {
		zone, found := zoneMap[zLabel]
		if !found {
			logx.Warn().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: driverID=%s driver has dedicated zone %s but this dedicated zone doesn't exist", d.Driver.DriverID, zLabel)
			continue
		}
		if zone.DisablePriorityRound {
			continue
		}
		if task.orderZoneCacher.validateOrderZone(task.order, zone) &&
			d.DistanceMeter/1000 <= zone.SearchRadiusInKM() &&
			(!task.snapshotCfg.applyOnlineRequirementForDedicatedServices.Has(string(task.order.ServiceType)) || d.Driver.Status == model.StatusOnline) {
			return true
		}
	}
	return false
}

// shouldAssignByRecommendedArea will be redundant in this transition phase
// please also consider to make the changes in assigner package
func (task *autoAssignTask) shouldAssignByRecommendedArea(ctx context.Context, d service.DriverWithLocation) (string, bool) {
	// TODO: remove this after supply positioning supports other service types
	if task.order != nil && task.order.ServiceType != model.ServiceFood {
		return "", false
	}
	if len(d.Driver.H3Recommendation.Areas) == 0 {
		return "", false
	}
	if d.Driver.H3Recommendation.ExpiredAt.Before(time.Now().In(d.Driver.H3Recommendation.ExpiredAt.Location())) {
		return "", false
	}
	if task.Locker.GetState(ctx, locker.DriverOrderRecommendedState(d.Driver.DriverID)) == d.Driver.H3Recommendation.RecommendationID {
		return "", false
	}

	driverH3 := h3.ToString(h3.FromGeo(h3.GeoCoord{Latitude: d.Location.Lat, Longitude: d.Location.Lng}, h3.Resolution(h3.FromString(d.Driver.H3Recommendation.Areas[0].H3ID))))
	_, isInArea := d.Driver.H3Recommendation.H3AreasIndex[driverH3]

	if !isInArea {
		return "", false
	}

	return driverH3, true
}

func (task *autoAssignTask) callOptimize(ctx context.Context, drivers []service.DriverWithLocation, optimizationRound model.OptimizationRound) ([]service.DriverWithLocation, error) {
	cfg := service.NewOptimizeSetting(
		task.Config.AssignmentType,
		task.Config.MOType,
		task.Config.MaxOrdersPerRider,
		task.multipleOrderAggressiveLevelWithSwitchbackExperiments(),
		task.Config.OptimizationCandidateNumber,
		task.restaurantBlacklistSets,
		task.Config.BypassIdleTime,
		make(map[string]types.StringSet),
		task.Config.MaxRadiusInMeter(task.order.ServiceType, task.searchRadiusByDistrict),
		task.Config.MaxRadiusInMeterByServices(task.searchRadiusByDistrict),
		task.Config.MaxRadiusInMeterByServices(task.searchRadiusByDistrict),
		task.modelVersionWithSwitchbackExperiments(model.OptimizeModel),
		task.dalianOptimizeOSRMEnabled(),
		task.fallbackDalianOSRMPhase(),
		task.isForSaversExperiment,
		task.Config.DalianDrivingDurationInSeconds,
		task.rushModeWithSwitchbackExperiments(),
		task.Config.SmartDistributionGoodnessBiasLevel,
		task.distributionLogMetadata,
		optimizationRound,
		task.Config.BikeB2BEnabled,
		task.Config.BikeCrossServiceEnabled,
		task.Config.MartMOB2BEnabled,
		task.Config.MartCrossServiceEnabled,
		task.Config.ServicePreference,
		task.useBikePriority,
		task.serviceBiasWeightsWithSwitchbackExperiments(),
	)
	candidates, err := task.PredictionService.Optimize(ctx, task.order, drivers, cfg)
	if err != nil {
		if !task.isForSaversExperiment {
			logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msg("autoassign: optimization error")
		}
	} else {
		task.debugLogger.AppendLog(AUTO_ASSIGN_OPTIMIZED_DRIVERS, candidates)
	}
	return candidates, err
}

func (task *autoAssignTask) routeSetting(firstDistanceLimitByServices map[model.Service]float64, b2bDistanceLimitByServices map[model.Service]float64, ordersTrip map[string]*string) service.RouteSetting {
	return service.NewRouteSetting(
		task.Config.AssignmentType,
		task.Config.MOType,
		task.Config.MaxOrdersPerRider,
		task.multipleOrderAggressiveLevelWithSwitchbackExperiments(),
		task.restaurantBlacklistSets,
		task.Config.BypassIdleTime,
		firstDistanceLimitByServices,
		b2bDistanceLimitByServices,
		task.Config.DistanceFromZoneLimit,
		task.modelVersionWithSwitchbackExperiments(model.RouteModel),
		task.Config.DalianOSRMEnabled,
		task.isForSaversExperiment,
		task.rushModeWithSwitchbackExperiments(),
		task.Config.SmartDistributionGoodnessBiasLevel,
		task.Config.BikeB2BEnabled,
		task.Config.BikeCrossServiceEnabled,
		task.Config.MartMOB2BEnabled,
		task.Config.MartCrossServiceEnabled,
		task.Config.ServicePreference,
		ordersTrip,
	)
}

func (task *autoAssignTask) validatePlanRouteSetting(firstDistanceLimitByServices map[model.Service]float64, b2bDistanceLimitByServices map[model.Service]float64) service.ValidatePlanRouteSetting {
	return service.NewValidatePlanRouteSetting(
		firstDistanceLimitByServices,
		b2bDistanceLimitByServices,
		task.Config.DistanceFromZoneLimit,
		task.restaurantBlacklistSets,
		task.Config.MartCrossServiceEnabled,
	)
}

// prepare is used for both auto assign distribution and candidates searching
// This function must NOT contain any update queries that are specific to each distribution
// since it's possible that the function might just be used for searching candidates
func (task *autoAssignTask) prepare(ctx context.Context, opt *model.AssignmentLogOpt) {
	task.applySwitchbackExperiments(task.order.DistributeRegions.DefaultRegion().String())
	task.predictionModelVersions = task.Config.GetPredictionModelVersions(time.Now())
	task.restaurantBlacklistSets = task.Config.PredictionRestaurantBlacklistTimeSlots.ToRestaurantStringSets(timeutil.BangkokNow())
	task.useBikePriority = task.Config.BikePriorityTimeSlots.IsEligible(
		timeutil.BangkokNow(),
		task.order.DistributeRegions.DefaultRegion().String(),
		task.Config.AtomicAutoAssignDbConfig.Get().EligibleRegionsForBikePriority,
	)

	skipShopAllowList := task.order.IsDalianMP && !task.Config.EnableBlacklistWhitelistForMPATR
	task.isUsingPredictionService = isPredictionServiceUsable(task.order, task.Config, task.DistributionConfig, task.restaurantBlacklistSets, skipShopAllowList)
	flagCtx := featureflag.NewFeatureFlagContext().WithRegion(task.order.DistributeRegions.DefaultRegion().String())
	task.enabledAssignAPI = task.FeatureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsEnabledAssignAPIOnDistribution.Name, flagCtx.FeatureOption())

	if task.Config.AtomicAutoAssignDbConfig.Get().EnableMultiplePickup && task.order.IsLockRequired() {
		if !task.IsMPMaxLoad3() {
			task.driverSelector = and(task.driverSelector, isOnlineOnly)
		}
		opt.LockDuration = task.order.Options.LockDuration
	}

	if task.isUsingPredictionService && task.order.Prediction == nil {
		task.predictOrderCookingAndWaitingTime(ctx)
	}

	originalDetail, err := task.ThrottledDispatchDetailRepository.FindOneFromOrder(ctx, task.order, repository.WithReadSecondaryPreferred)
	if err != nil && err != repository.ErrNotFound {
		logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: error throttled_dispatch_detail not found")
	}
	if originalDetail != nil && err == nil {
		task.throttlingMOLevel = originalDetail.MultipleOrderAggressiveLevel
	}

	opt.IsRushOrder = task.order.IsNonDalianRush()
	if !task.Config.AtomicAutoAssignDbConfig.Get().DisableThrottledDispatch &&
		len(task.order.Routes) != 0 &&
		len(task.order.DistributeRegions) != 0 &&
		(task.order.ServiceType == model.ServiceFood || task.order.ServiceType == model.ServiceMart) &&
		!task.order.ProcessedBySingleDistribution {

		var detail *model.ThrottledDispatchDetailWithZoneCode
		if originalDetail != nil && err == nil {
			detail = originalDetail
		}

		detail, err = task.getThrottledDispatchDetail(ctx, task.order, detail)
		if err != nil && err != repository.ErrNotFound {
			logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: error throttled_dispatch_detail not found")
		}
		if detail != nil && err == nil {
			task.isInThrottlingZone = true
			task.throttlingZoneDetail = detail
			if detail.MultipleOrderAggressiveLevel != "" && detail.MultipleOrderAggressiveLevel != task.throttlingMOLevel {
				task.throttlingMOLevel = detail.MultipleOrderAggressiveLevel
			}
		}
	}
}

func (task *autoAssignTask) Process(ctx context.Context, runningTasks *sync.WaitGroup, opt model.AssignmentLogOpt) {
	if runningTasks != nil {
		defer runningTasks.Done()
	}

	aaDbConfig := task.Config.AtomicAutoAssignDbConfig.Get()
	if aaDbConfig.AcquireLockOrderOnStartEnabled {
		if !task.LockOrder(ctx, task.order.OrderID, aaDbConfig.AcquireLockOrderOnStartTTL) {
			return
		}
		defer task.UnlockOrder(ctx, task.order.OrderID)
	}

	if err := task.startHeartbeat(ctx, task.order.OrderID); err != nil {
		logx.Error().Err(err).Context(ctx).Str(logutil.OrderID, task.order.OrderID).Msg("hearthbeat for order failed")
	}
	assignableOrders := types.NewStringSet()
	defer func(orderID string) {
		// assign function will be responsible for stopping heartbeat of assignable order
		if !assignableOrders.Has(orderID) {
			task.stopHeartbeat(ctx, orderID)
		}
	}(task.order.OrderID)

	distributionTime := time.Now()
	_ = task.updateRedistributionState(ctx, &model.RedistributionState{
		IsUsingPredictionService:      task.isUsingPredictionService,
		CurrentRound:                  task.currentRound,
		RedistributionCount:           0,
		LastDistributeAt:              distributionTime,
		Opt:                           opt,
		ConsecutiveRoundsWithNoRiders: 0,
		CurrentDistributionLogic:      model.RedistributionStateDistributionLogicSingle,
	})

	// force assign multiple pickup order
	if task.Config.AtomicAutoAssignDbConfig.Get().EnableMultiplePickup && task.order.IsMultiplePickup() && task.tryToAssignMpOrder(ctx, opt) {
		assignableOrders.Add(task.order.OrderID)
		return
	}

	if task.tryToThrottleDispatch(ctx, distributionTime, opt) {
		return
	}

	task.updateProcessedBySingleDistributeAndSearchStrategy(ctx, task.order, opt.SearchRiderStrategy)
	// Lock order to prevent order to distribute twice for deferred dispatch
	if task.order.IsDeferred {
		// this condition will be removed if IdempotentDistributionEnabled was enabled nationwide
		if !task.lockedOrderIDs.Has(task.order.OrderID) {
			if !task.LockOrder(ctx, task.order.OrderID, timeutil.Abs(timeutil.BangkokNow(), task.order.ExpireAt)) {
				return
			}
			defer task.UnlockOrder(ctx, task.order.OrderID)
		}
		task.updateManyActualDeferDuration(ctx, []model.Order{*task.order})
		task.updateThrottleDeferActualAssigningAt(ctx, task.order)
	}

	if err := task.OrderDistributionEventManager.PublishSearchingOrAssigningDriver(ctx, *task.order, timeutil.BangkokNow()); err != nil {
		logx.Error().Err(err).Context(ctx).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msg("autoassign: cannot publish searching/assigning event")
	}

	result, err := task.searchDrivers(ctx, task.businessLocation)
	// if the error is not from prediction service, end the process
	if err != nil && !errors.Is(err, ErrPredictionService) {
		logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign: cannot get drivers")
		return
	}

	opt.SearchRiderStrategy = result.Strategy

	task.UnlockOrder(ctx, task.order.OrderID)
	task.notifyBestDriverInBackground(ctx, task.newDriverQueue(result.Results), opt, task.order, task.currentAssigningSetting())
	assignableOrders.Add(task.order.OrderID)
	return
}

func (task *autoAssignTask) updateConsecutiveRoundsWithNoRiders(ridersTriedAssigning int) {
	if ridersTriedAssigning == 0 {
		task.consecutiveRoundsWithNoRiders += 1
	} else {
		task.consecutiveRoundsWithNoRiders = 0
	}
}

func (task *autoAssignTask) currentAssigningSetting() AssigningSettingState {
	return AssigningSettingState{
		DistCfgRevision:          task.distCfgRevision,
		CurrentRound:             task.currentRound,
		IsUsingPredictionService: task.isUsingPredictionService,
		SwitchbackExperiments:    task.switchbackExperiments,
		PredictionModelVersions:  task.predictionModelVersions,
		UseBikePriority:          task.useBikePriority,
		SearchRadiusByDistrict:   task.searchRadiusByDistrict,
		DistributionLogMetadata:  task.distributionLogMetadata,
	}
}

// fetchDriverQueue restore DriverQueue from temporary state - DriverQueueMinimal on redis
func (task *autoAssignTask) fetchDriverQueue(ctx context.Context, driverQueueMinimal DriverQueueMinimal) (*DriverQueue, error) {
	driversMapByID, err := task.DriverRepository.GetMinimalProfilesByID(ctx, driverQueueMinimal.DriverIDs())
	if err != nil {
		return nil, err
	}

	driverAssignment := make([]DriverAssignment, 0, len(driverQueueMinimal.DriverAssignments))
	for _, assignment := range driverQueueMinimal.DriverAssignments {
		reloadedDriver, exists := driversMapByID[assignment.Driver.DriverID]
		if !exists {
			return nil, errors.New("driver assignment not found")
		}
		driver := service.DriverWithLocation{
			Driver:                   *reloadedDriver,
			Location:                 assignment.Driver.Location,
			DistanceMeter:            assignment.Driver.DistanceMeter,
			IsPrioritized:            assignment.Driver.IsPrioritized,
			IsDedicatedZone:          assignment.Driver.IsDedicatedZone,
			OverrideIdleTime:         assignment.Driver.OverrideIdleTime,
			PredictedFulFillmentRate: assignment.Driver.PredictedFulFillmentRate,
			TripID:                   assignment.Driver.TripID,
		}
		driverAssignment = append(driverAssignment, NewDriverAssignment(driver, assignment.AssignmentType))
	}
	res := DriverQueue{
		CurIdx:            driverQueueMinimal.CurIdx,
		DriverAssignments: driverAssignment,
	}
	return &res, nil
}

func (task *autoAssignTask) ContinueAssign(
	ctx context.Context,
	runningTasks *sync.WaitGroup,
	opt model.AssignmentLogOpt,
	assigningState AssigningState,
) {
	if runningTasks != nil {
		defer runningTasks.Done()
	}

	if !task.LockOrder(ctx, task.order.OrderID, timeutil.Abs(timeutil.BangkokNow(), task.order.ExpireAt)) {
		return
	}
	defer task.UnlockOrder(ctx, task.order.OrderID)

	if err := task.startHeartbeat(ctx, task.order.OrderID); err != nil {
		logx.Error().Context(ctx).Str(logutil.OrderID, task.order.OrderID).Err(err).Msg("hearthbeat for order failed")
	}
	assignableOrders := types.NewStringSet()
	defer func(orderID string) {
		// assign function will be responsible for stopping assignable heartbeat
		if !assignableOrders.Has(orderID) {
			task.stopHeartbeat(ctx, orderID)
		}
	}(task.order.OrderID)

	assigningSetting := assigningState.AssigningSetting
	driverQueue, err := task.fetchDriverQueue(ctx, assigningState.DriverQueue)
	if err != nil {
		logx.Error().Context(ctx).Str(logutil.OrderID, task.order.OrderID).Err(err).Msg("hearthbeat for order failed")

		task.UnlockOrder(ctx, task.order.OrderID)
		err = task.redistribute(ctx, task.order.OrderID, types.NewInt(0))
	}

	if err == nil { // having driver in queue or fetching error but success redistribution, adding order id to stop heartbeat
		assignableOrders.Add(task.order.OrderID)
	}

	task.notifyBestDriverInBackground(ctx, driverQueue, opt, task.order, assigningSetting)
}

func (task *autoAssignTask) ProcessRedistribution(
	ctx context.Context,
	runningTasks *sync.WaitGroup,
	redistributionCount int,
	lastDistributionTimeElapsed time.Duration,
	opt model.AssignmentLogOpt,
) {
	if runningTasks != nil {
		defer runningTasks.Done()
	}

	if redistributionCount > task.Config.GetRedistributionLimit(task.order) {
		return
	}

	if !task.LockOrder(ctx, task.order.OrderID, timeutil.Abs(timeutil.BangkokNow(), task.order.ExpireAt)) {
		return
	}
	defer task.UnlockOrder(ctx, task.order.OrderID)

	if err := task.startHeartbeat(ctx, task.order.OrderID); err != nil {
		logx.Error().Context(ctx).Str(logutil.OrderID, task.order.OrderID).Err(err).Msg("hearthbeat for order failed")
	}
	assignableOrders := types.NewStringSet()
	defer func(orderID string) {
		// assign function will be responsible for stopping assignable heartbeat
		if !assignableOrders.Has(orderID) {
			task.stopHeartbeat(ctx, orderID)
		}
	}(task.order.OrderID)

	if task.tryToThrottleDispatch(ctx, time.Now(), opt) {
		return
	}

	_, err, _, _, assignable := task.redistributeOrder(ctx, redistributionCount, lastDistributionTimeElapsed, opt)
	if assignable {
		assignableOrders.Add(task.order.OrderID)
	}
	if err != nil {
		logx.Error().Context(ctx).Str(logutil.OrderID, task.order.OrderID).Err(err).Msgf("ProcessRedistribution: redistribute failed with error")
		return
	}

	return
}

func (task *autoAssignTask) redistributeOrder(
	ctx context.Context,
	redistributionCount int,
	lastDistributionTimeElapsed time.Duration,
	opt model.AssignmentLogOpt,
) (bool, error, time.Duration, int, bool) {
	redistributionDelay := task.getRedistributionDelay(redistributionCount+1, task.isOrderFallbackFromBatchOptimize())
	if lastDistributionTimeElapsed < redistributionDelay {
		delayTime := redistributionDelay - lastDistributionTimeElapsed
		logx.Info().Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign redistribute order: orderID=%v delaying redistribution due to all riders are filtered out for %v seconds (redistribution delay = %v), consecutive rounds with no riders: %v", task.order.OrderID, delayTime.Seconds(), redistributionDelay.Seconds(), task.consecutiveRoundsWithNoRiders)
		task.Environment.Sleep(delayTime)
	}

	if err := task.validateIfAssignableAndRecord(ctx, *task.order, task.currentRound+1); err != nil {
		logx.Error().Str(logutil.OrderID, task.order.OrderID).Err(err).Msg("autoassign redistribute order: validation assignable with error")
		return false, nil, 0, 0, false
	}

	if task.isExpired() {
		return false, nil, 0, 0, false
	}

	logx.Info().Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign redistribute order: orderID=%v start redistribution process, round=%v", task.order.OrderID, redistributionCount)

	if task.order.IsDeferred {
		task.deferredOrderRedistributionCountMetric.Inc(task.order.DistributeRegions.DefaultRegion().String())
		task.updateThrottleDeferActualAssigningAt(ctx, task.order)
	}

	if err := task.OrderDistributionEventManager.PublishSearchingOrAssigningDriver(ctx, *task.order, timeutil.BangkokNow()); err != nil {
		logx.Error().Context(ctx).Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msg("auto_assign redistribute order: cannot publish searching/assigning event")
	}

	distributionTimeStarted := time.Now()
	_ = task.updateRedistributionState(ctx, &model.RedistributionState{
		IsUsingPredictionService:      task.isUsingPredictionService,
		CurrentRound:                  task.currentRound,
		RedistributionCount:           redistributionCount + 1,
		LastDistributeAt:              distributionTimeStarted,
		Opt:                           opt,
		ConsecutiveRoundsWithNoRiders: task.consecutiveRoundsWithNoRiders,
		CurrentDistributionLogic:      model.RedistributionStateDistributionLogicSingle,
	})

	task.selectorMemoizer.resetDriverTransactions()
	result, err := task.searchDrivers(ctx, task.businessLocation)
	// Update the search strategy to the new one.
	opt.SearchRiderStrategy = result.Strategy
	// if the error is not from prediction service, end the process
	if err != nil && !errors.Is(err, ErrPredictionService) {
		logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msg("autoassign redistribute cannot get drivers")
		return false, err, 0, 0, false
	}
	task.debugLogger.AppendLog(fmt.Sprintf(AUTO_ASSIGN_FORMAT_REDISTRIBUTE_DRIVER, redistributionCount), result.Results)

	task.UnlockOrder(ctx, task.order.OrderID)
	task.notifyBestDriverInBackground(ctx, task.newDriverQueue(result.Results), opt, task.order, task.currentAssigningSetting())
	return false, nil, 0, 0, true
}

func (task *autoAssignTask) getRedistributionDelay(redistributionCount int, isOrderFallbackFromBatchOptimize bool) time.Duration {
	interval := task.Config.RedistributionDelay
	if interval <= 0 {
		return 0
	}

	cfg := task.Config.AtomicAutoAssignDbConfig.Get()

	var exponentialRate, maxIntervalInSeconds float64
	if task.order.MatchRestaurantFirst() {
		if isOrderFallbackFromBatchOptimize {
			exponentialRate = cfg.FallbackOrderMatchRestaurantFirstRedistributionDelayExponentialRate
			maxIntervalInSeconds = cfg.FallbackOrderMatchRestaurantFirstMaxRedistributionDelayInSeconds
		} else {
			exponentialRate = cfg.MatchRestaurantFirstRedistributionDelayExponentialRate
			maxIntervalInSeconds = cfg.MatchRestaurantFirstMaxRedistributionDelayInSeconds
		}
	} else {
		if isOrderFallbackFromBatchOptimize {
			exponentialRate = cfg.FallbackOrderMatchRiderFirstRedistributionDelayExponentialRate
			maxIntervalInSeconds = cfg.FallbackOrderMatchRiderFirstMaxRedistributionDelayInSeconds
		} else {
			exponentialRate = cfg.MatchRiderFirstRedistributionDelayExponentialRate
			maxIntervalInSeconds = cfg.MatchRiderFirstMaxRedistributionDelayInSeconds
		}
	}

	if exponentialRate <= 1 {
		return interval
	}

	maxInterval := time.Duration(maxIntervalInSeconds) * time.Second
	maxExponent := math.Floor((math.Log(float64(maxInterval) / float64(interval))) / math.Log(exponentialRate))

	exponent := float64(task.consecutiveRoundsWithNoRiders)
	if cfg.EnableFallbackOrderRedistributionDelay && isOrderFallbackFromBatchOptimize {
		exponent = float64(redistributionCount)
	}

	if exponent > maxExponent {
		return maxInterval
	}

	redistributionDelay := time.Duration(float64(interval) * math.Pow(exponentialRate, exponent))
	if redistributionDelay > maxInterval {
		return maxInterval
	}
	return redistributionDelay
}

func (task *autoAssignTask) runSaversShadowExperiment(distributionTimeElapsed time.Duration, opt model.AssignmentLogOpt) {
	ctx := context.Background()
	distributionConfig := task.DistributionConfig.Get()
	if !distributionConfig.EnableSaversExperiment ||
		distributionConfig.SaversExperimentSampling == 0 ||
		task.order.ServiceType != model.ServiceFood ||
		task.isUsingPredictionService == false ||
		task.order.IsLockRequired() ||
		task.order.IsMultiplePickup() ||
		task.order.IsNonDalianRush() ||
		task.isExpired() {
		return
	}

	order, err := task.OrderRepository.Get(ctx, task.order.OrderID)
	if err != nil {
		return
	}

	if order.Driver == "" {
		return
	}

	rand.Seed(time.Now().UnixNano())
	randomValue := rand.Intn(100)
	if float64(randomValue) > (distributionConfig.SaversExperimentSampling*100 - 1) {
		return
	}

	safe.GoFunc(func() {
		experimentStartedAt := timeutil.BangkokNow()
		defer func() {
			logx.Info().Str(logutil.OrderID, order.OrderID).Msgf("[SaversExperiment] Ran savers experiment on order %v from %v to %v", order.OrderID, experimentStartedAt, timeutil.BangkokNow())
		}()

		expireAt := task.expireAt
		businessLocation := task.businessLocation
		regions := task.regions
		notifyViaSocketIO := task.notifyViaSocketIO
		acceptDurationBuffer := task.acceptDurationBuffer
		config := task.Config
		areaDistCfg := task.AreaDistCfg
		deps := task.AutoAssignOrderDistributorDeps
		searchRadiusByDistrict := task.searchRadiusByDistrict
		snapshotCfg := task.snapshotCfg

		// Use shadowTask instead of task to prevent side effect
		shadowTask := autoAssignTask{
			AutoAssignOrderDistributorDeps: deps,

			distributionType: AutoAssignDistribution,

			order:                     order,
			expireAt:                  expireAt,
			businessLocation:          businessLocation,
			scorers:                   config.createScorers(deps, order, regions, searchRadiusByDistrict, snapshotCfg),
			driverSelector:            config.createDriverSelector(ctx, deps, order, areaDistCfg, newSelectorMemoizer()),
			regions:                   regions,
			getNotifyMsg:              service.EventOrderAssigned,
			notifyViaSocketIO:         notifyViaSocketIO,
			debugLogger:               NewDistributionDebugger(false, order.DistributeRegions.DefaultRegion(), order.OrderID, model.DistributionLogicAutoAssign),
			lockedOrderIDs:            types.NewStringSet(),
			isForSaversExperiment:     true,
			withNotifiedExceptCurrent: true,
			acceptDurationBuffer:      acceptDurationBuffer,
			stopMetricWithRegion:      orderassigner.NewStopMetricWithRegions(deps.stopMetric, regions),
			searchRadiusByDistrict:    searchRadiusByDistrict,
			snapshotCfg:               snapshotCfg,
			assignmentLogMemoize:      NewAssignmentLogMemoize(deps.AssignmentLogRepo),
		}

		shadowTask.Config = config
		shadowTask.AreaDistCfg = areaDistCfg
		shadowTask.applySwitchbackExperiments(order.DistributeRegions.DefaultRegion().String())
		shadowTask.isUsingPredictionService = true
		shadowTask.restaurantBlacklistSets = config.PredictionRestaurantBlacklistTimeSlots.ToRestaurantStringSets(timeutil.BangkokNow())
		shadowTask.useBikePriority = task.Config.BikePriorityTimeSlots.IsEligible(
			timeutil.BangkokNow(),
			shadowTask.order.DistributeRegions.DefaultRegion().String(),
			shadowTask.Config.AtomicAutoAssignDbConfig.Get().EligibleRegionsForBikePriority,
		)

		if distributionTimeElapsed < distributionConfig.SaversExperimentFirstRoundDelay {
			delayTime := distributionConfig.SaversExperimentFirstRoundDelay - distributionTimeElapsed
			shadowTask.Environment.Sleep(delayTime)
		}

		var saversExperimentRecords []model.SaversExperimentRecord
		for redistributionCount := 0; redistributionCount < distributionConfig.SaversExperimentRedistributionMaxRetries; redistributionCount++ {
			if redistributionCount != 0 && distributionTimeElapsed < distributionConfig.SaversExperimentRedistributionDelay {
				delayTime := distributionConfig.SaversExperimentRedistributionDelay - distributionTimeElapsed
				shadowTask.Environment.Sleep(delayTime)
			}

			distributionTimeStarted := time.Now()
			drivers, err := shadowTask.searchDrivers(ctx, businessLocation)
			if err != nil {
				drivers.Results = []service.DriverWithLocation{}
				shadowTask.isUsingPredictionService = true
			}

			experimentRecord := model.SaversExperimentRecord{
				Round: redistributionCount + 1,
			}

			requiredTopN := distributionConfig.SaversExperimentTopN
			for _, driver := range drivers.Results {
				if requiredTopN <= 0 {
					break
				}
				if passed, _, _ := shadowTask.checkRequirement(ctx, &driver.Driver, AnyAssignmentType, opt, task.order, task.isUsingPredictionService); !passed {
					continue
				}
				if state := shadowTask.Locker.GetState(ctx, locker.DriverAutoAssignState(driver.Driver.DriverID)); state != "" && state != order.OrderID {
					continue
				}
				res, _, err := shadowTask.PredictionService.Route(ctx, []model.Order{*shadowTask.order}, driver, shadowTask.routeSetting(nil, shadowTask.Config.MaxRadiusInMeterByServices(shadowTask.searchRadiusByDistrict), nil))
				if err != nil {
					continue
				}
				if _, err := shadowTask.PredictionService.ValidatePlanRoute(ctx, res, []string{shadowTask.order.OrderID}, driver, shadowTask.validatePlanRouteSetting(nil, shadowTask.Config.MaxRadiusInMeterByServices(shadowTask.searchRadiusByDistrict))); err != nil {
					continue
				}
				isMO := service.IsMO(res, order.OrderID)
				experimentRecord.CandidatesByRank = append(experimentRecord.CandidatesByRank, driver.Driver.DriverID)
				experimentRecord.IsMOByRank = append(experimentRecord.IsMOByRank, isMO)
				requiredTopN--
			}

			experimentRecord.RecordedAt = timeutil.BangkokNow()
			saversExperimentRecords = append(saversExperimentRecords, experimentRecord)
			distributionTimeElapsed = time.Now().Sub(distributionTimeStarted)
		}

		if err := shadowTask.OrderRepository.SetSaversExperimentRecords(ctx, order.OrderID, order.DeliveringRound, saversExperimentRecords); err != nil {
			logx.Info().Str(logutil.OrderID, order.OrderID).Err(err).Msgf("[SaversExperiment] Unable to save experiment of %v result", order.OrderID)
		}
	})
}

func (task *autoAssignTask) ProcessBatch(ctx context.Context, runningTasks *sync.WaitGroup) {
	if runningTasks != nil {
		defer runningTasks.Done()
	}

	heartbeatCtx := task.WorkerContext.NewContext()
	if err := task.batchStartHeartbeat(ctx, task.batchOrderIDs); err != nil {
		logx.Error().Context(ctx).Err(err).Msg("hearthbeat for batch assignment failed")
	}
	collector, m := metric.GetCollector(), metric.NewAPILatency("process_batch")
	defer func() {
		task.WorkerContext.ContextDone(heartbeatCtx)
		if err := collector.Save(m); err != nil {
			logx.Error().Context(ctx).Err(err).Interface(logutil.OrderIDs, task.batchOrderIDs).Msgf("batch_auto_assign: cannot collect metric process_batch")
		}
	}()

	// all of unassigned orders must be redistributed and stop hearth beat.
	assignableOrders := types.NewStringSet()
	defer func(allOrders types.StringSet) {
		unassignedOrders := allOrders.Minus(assignableOrders).GetElements()

		task.batchRedistribute(ctx, unassignedOrders)
		task.batchStopHeartbeat(ctx, unassignedOrders)
	}(types.NewStringSet(task.batchOrderIDs...))

	// prepare
	task.isUsingPredictionService = true
	task.applySwitchbackExperiments(task.batchZone.Region)
	task.predictionModelVersions = task.Config.GetPredictionModelVersions(time.Now())
	task.restaurantBlacklistSets = task.Config.PredictionRestaurantBlacklistTimeSlots.ToRestaurantStringSets(timeutil.BangkokNow())
	task.useBikePriority = task.Config.BikePriorityTimeSlots.IsEligible(timeutil.BangkokNow(), task.batchZone.Region, task.Config.AtomicAutoAssignDbConfig.Get().EligibleRegionsForBikePriority)
	flagCtx := featureflag.NewFeatureFlagContext().WithRegion(task.batchZone.Region)
	enabledAssignAPI := task.FeatureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsEnabledAssignAPIOnDistribution.Name, flagCtx.FeatureOption())

	rawOrders, err := task.OrderRepository.GetActiveAssigningOrders(ctx, task.batchOrderIDs, task.Environment.Now())
	if err != nil {
		logx.Error().Err(err).Interface(logutil.OrderIDs, task.batchOrderIDs).Msgf("batch_auto_assign: can't get orders")
		return
	}
	m.Lap("get_active_assigning_orders")

	orders := task.preFilterOrders(ctx, rawOrders)
	task.batchOrderCountMetric.Observe(float64(len(orders)), task.batchZone.ZoneCode)
	if len(orders) == 0 {
		return
	}

	task.updateManyActualDeferDuration(ctx, orders)
	m.Lap("update_many_mp_actual_defer_duration")

	task.updateManyThrottleDeferActualAssigningAt(ctx, orders)
	m.Lap("update_many_throttle_defer_actual_assigning_at")

	offset := 0.0
	if task.Config.AtomicAutoAssignDbConfig.Get().EnableThrottlePolygonOffset {
		offset = task.Config.MaxRadiusInMeter(orders[0].ServiceType, task.searchRadiusByDistrict)
		if task.throttlingZoneDetail.EnabledSearchRadiusOffset && task.throttlingZoneDetail.GetSearchRadiusOffsetInMeter() < offset && task.throttlingZoneDetail.GetSearchRadiusOffsetInMeter() >= 0 {
			offset = task.throttlingZoneDetail.GetSearchRadiusOffsetInMeter()
		}
	}

	searchStartedAt := timeutil.BangkokNow()
	searchResult, err := task.LocationManager.SearchDriversInMultiPolygon(ctx, task.batchZone.ID.Hex(), task.batchZone.Geometry.Coordinates, offset)
	if err != nil {
		logx.Error().Err(err).Interface(logutil.OrderIDs, task.batchOrderIDs).Msgf("batch_auto_assign: can't search drivers")
		return
	}
	m.Lap("search_drivers_in_multiple_polygon")

	rawDrivers := searchResult.Results
	task.logFetchedDrivers(rawDrivers)

	driversOfOtherMPs, err := task.OrderRepository.GetDriversOfOtherMPs(ctx, orders)
	if err != nil {
		logx.Error().Err(err).Interface(logutil.OrderIDs, task.batchOrderIDs).Msgf("batch_auto_assign: can't get driverIDs of other MP orders")
	} else {
		driverIDToDriverMP := repository.DriverMPs(driversOfOtherMPs).AsMapDriverIDToDriverMP()
		for _, rd := range rawDrivers {
			delete(driverIDToDriverMP, rd.Driver.DriverID)
		}
		if len(driverIDToDriverMP) > 0 {
			tmp, err := task.LocationManager.GetDriverWithLocationByIDs(ctx, maps.Keys(driverIDToDriverMP))
			if err != nil {
				logx.Error().Err(err).Interface(logutil.OrderIDs, task.batchOrderIDs).Msgf("batch_auto_assign: can't get drivers of other MP orders")
			} else {
				rawDrivers = append(rawDrivers, tmp...)
			}
		}
		driversOfOtherMPs = maps.Values(driverIDToDriverMP)
		logx.Infof(ctx, "batch_auto_assign: included drivers of other MPs: %+v", driversOfOtherMPs)
	}
	m.Lap("fetch_other_mp_drivers")
	task.DistributionLogManager.CaptureSearchEvent(ctx, rawDrivers, orders, searchStartedAt, task.distributionLogMetadata)

	allRegions := task.unionDistributeRegions(orders)
	drivers := task.preFilterDrivers(ctx, rawDrivers, allRegions)
	m.Lap("pre_filter_drivers")
	task.batchRiderCountMetric.Observe(float64(len(drivers)), task.batchZone.ZoneCode)
	if len(drivers) == 0 {
		return
	}

	ineligibleDriversToOrder := task.ineligibleDrivers(ctx, drivers, orders, driversOfOtherMPs)
	m.Lap("ineligible_drivers")

	ordersAlreadyAssigned := types.NewStringSet()
	ridersAlreadyAssigned := types.NewStringSet()

	task.markDedicatedZone(drivers)
	prioritizedOrders, prioritizedDrivers, dZones := task.filterForDedicatedZoneRound(ctx, orders, drivers)
	m.Lap("filter_for_dedicated_zone_round")

	if len(prioritizedOrders) != 0 && len(prioritizedDrivers) != 0 {
		dZoneRadiusByServices := make(map[model.Service]float64)
		for _, dZone := range dZones {
			radius := dZone.SearchRadiusInMeters()
			for _, serviceType := range dZone.ServiceTypes {
				dZoneRadiusByServices[serviceType] = max(dZoneRadiusByServices[serviceType], radius)
			}
		}
		radiusByServices := task.Config.MaxRadiusInMeterByServices(task.searchRadiusByDistrict)
		for serviceType, radius := range dZoneRadiusByServices {
			radiusByServices[serviceType] = radius
		}
		batchAssignSetting := batchAssignSetting{
			firstDistanceLimit:           radiusByServices[prioritizedOrders[0].ServiceType],
			firstDistanceLimitByServices: radiusByServices,
			b2bDistanceLimitByServices:   radiusByServices,
			region:                       task.batchZone.Region,
		}

		prioritizedOptimizedPlan, err := task.callBatchOptimize(ctx, prioritizedOrders, prioritizedDrivers, ineligibleDriversToOrder, batchAssignSetting, model.DedicatedRound)
		if err == nil {
			m.Lap("call_batch_optimize_dedicated")

			// Assign API will be responsible for lock/unlock
			var assignableRiders []service.DriverWithLocation
			assignableRiderIDSet := types.NewStringSet()
			if enabledAssignAPI {
				assignableRiders, assignableRiderIDSet = driversFromBatchOptimizePlan(prioritizedDrivers, prioritizedOptimizedPlan)
			} else {
				// After this code block, if orders/riders are not assignable, they must be un-locked
				assignableRiders, assignableRiderIDSet = task.tryLockingDrivers(ctx, prioritizedDrivers, prioritizedOptimizedPlan)
			}
			prioritizedOrderIDSet := prioritizedOptimizedPlan.AssignedOrdersByDrivers(assignableRiderIDSet)
			assignableOrders.Add(prioritizedOrderIDSet.GetElements()...)
			m.Lap("try_locking_drivers_dedicated")

			task.markPrioritized(assignableRiders)

			bgCtx := task.AutoAssignOrderDistributorDeps.WorkerContext.NewContextWithSameWaitGroup(ctx)
			task.Environment.RunGoWithCtx(bgCtx, func() {
				defer task.AutoAssignOrderDistributorDeps.WorkerContext.ContextDone(bgCtx)

				task.batchAssign(
					bgCtx,
					assignableRiders,
					prioritizedOptimizedPlan,
					prioritizedOrders,
					searchResult.Strategy,
					batchAssignSetting,
					enabledAssignAPI,
				)
			})

			ordersAlreadyAssigned.Add(prioritizedOrderIDSet.GetElements()...)
			ridersAlreadyAssigned.Add(assignableRiderIDSet.GetElements()...)
		}
	}

	spOrders, spDrivers := task.filterForSupplyPositioningRound(ctx, orders, drivers, ordersAlreadyAssigned, ridersAlreadyAssigned)
	m.Lap("filter_for_supply_positioning_round")

	radiusByServices := task.Config.MaxRadiusInMeterByServices(task.searchRadiusByDistrict)
	var firstDistanceLimit float64
	if len(spOrders) != 0 {
		firstDistanceLimit = radiusByServices[spOrders[0].ServiceType]
	}
	batchAssignSetting := batchAssignSetting{
		firstDistanceLimit:           firstDistanceLimit,
		firstDistanceLimitByServices: radiusByServices,
		b2bDistanceLimitByServices:   radiusByServices,
		region:                       task.batchZone.Region,
	}

	if len(spDrivers) > 0 {
		spPlan, err := task.callBatchOptimize(ctx, spOrders, spDrivers, ineligibleDriversToOrder, batchAssignSetting, model.SupplyPositioningRound)
		if err == nil {
			m.Lap("call_batch_optimize_supply_positioning")

			// Assign API will be responsible for lock/unlock
			var assignableRiders []service.DriverWithLocation
			assignableRiderIDSet := types.NewStringSet()
			if enabledAssignAPI {
				assignableRiders, assignableRiderIDSet = driversFromBatchOptimizePlan(spDrivers, spPlan)
			} else {
				// After this code block, if orders/riders are not assignable, they must be un-locked
				assignableRiders, assignableRiderIDSet = task.tryLockingDrivers(ctx, spDrivers, spPlan)
			}
			spOrderIDSet := spPlan.AssignedOrdersByDrivers(assignableRiderIDSet)
			assignableOrders.Add(spOrderIDSet.GetElements()...)
			m.Lap("try_locking_drivers_supply_positioning")

			bgCtx := task.AutoAssignOrderDistributorDeps.WorkerContext.NewContextWithSameWaitGroup(ctx)
			task.Environment.RunGoWithCtx(bgCtx, func() {
				defer task.AutoAssignOrderDistributorDeps.WorkerContext.ContextDone(bgCtx)

				task.batchAssign(
					bgCtx,
					assignableRiders,
					spPlan, spOrders,
					searchResult.Strategy,
					batchAssignSetting,
					enabledAssignAPI,
				)
			})

			ordersAlreadyAssigned.Add(spOrderIDSet.GetElements()...)
			ridersAlreadyAssigned.Add(assignableRiderIDSet.GetElements()...)
		}
	}

	sharedOrders, sharedDrivers := task.filterForSharedRound(ctx, orders, drivers, ordersAlreadyAssigned, ridersAlreadyAssigned)
	m.Lap("filter_for_supply_positioning_round")

	// To re-check whether there are riders left in the shared round
	if len(sharedDrivers) == 0 {
		return
	}

	optimizedPlan, err := task.callBatchOptimize(ctx, sharedOrders, sharedDrivers, ineligibleDriversToOrder, batchAssignSetting, model.SharedRound)
	if err != nil {
		return
	}
	m.Lap("call_batch_optimize")

	// Assign API will be responsible for lock/unlock
	var assignableRiders []service.DriverWithLocation
	assignableRiderIDSet := types.NewStringSet()
	if enabledAssignAPI {
		assignableRiders, assignableRiderIDSet = driversFromBatchOptimizePlan(sharedDrivers, optimizedPlan)
	} else {
		assignableRiders, assignableRiderIDSet = task.tryLockingDrivers(ctx, sharedDrivers, optimizedPlan)
	}
	m.Lap("try_locking_drivers")

	reachAssign := types.NewBool(false) // flip to true when reach batchAssign. this is to unlock drivers if never reach assign
	defer func() {
		if !*reachAssign && !enabledAssignAPI {
			for _, d := range assignableRiders {
				task.Locker.RemoveState(ctx, locker.DriverAutoAssignState(d.Driver.DriverID))
			}
		}
	}()

	task.updateDeferUntil(ctx, optimizedPlan, sharedOrders)
	m.Lap("update_defer_until")

	*reachAssign = true // DON'T move this (see variable declaration)
	assignableOrders.Add(optimizedPlan.AssignedOrdersByDrivers(assignableRiderIDSet).GetElements()...)

	bgCtx := task.AutoAssignOrderDistributorDeps.WorkerContext.NewContextWithSameWaitGroup(ctx)
	task.Environment.RunGoWithCtx(bgCtx, func() {
		defer task.AutoAssignOrderDistributorDeps.WorkerContext.ContextDone(bgCtx)

		task.batchAssign(
			bgCtx,
			assignableRiders,
			optimizedPlan,
			sharedOrders,
			searchResult.Strategy,
			batchAssignSetting,
			enabledAssignAPI,
		)
	})

	return
}

func driversFromBatchOptimizePlan(allDrivers []service.DriverWithLocation, optimizedPlan prediction.BatchOptimizeResponse) ([]service.DriverWithLocation, types.StringSet) {
	res := []service.DriverWithLocation{}
	set := types.NewStringSet()
	for _, d := range allDrivers {
		if _, ok := optimizedPlan.RiderOrders[d.Driver.DriverID]; ok {
			res = append(res, d)
			set.Add(d.Driver.DriverID)
		}
	}
	return res, set
}

// updateManyActualDeferDuration
func (task *autoAssignTask) updateManyActualDeferDuration(ctx context.Context, orders []model.Order) {
	bgCtx := safe.NewContextWithSameWaitGroup(ctx)
	for i := range orders {
		o := orders[i]
		if !o.CanDefer() {
			continue
		}
		actualDeferDuration := timeutil.BangkokNow().Sub(o.CreatedAt)
		if actualDeferDuration < o.DeferDuration {
			orders[i].DeferDuration = actualDeferDuration
			task.Environment.RunGoWithCtx(bgCtx, func() {
				task.OrderRepository.SetDeferDuration(bgCtx, o.OrderID, actualDeferDuration)
			})
		}
	}
}

func (task *autoAssignTask) filterForDedicatedZoneRound(ctx context.Context, orders []model.Order, drivers []service.DriverWithLocation) ([]model.Order, []service.DriverWithLocation, []model.DedicatedZone) {
	if task.Config.AtomicAutoAssignDbConfig.Get().DisableDedicatedZones || task.Config.AtomicAutoAssignDbConfig.Get().DisableBatchDedicatedZones {
		return []model.Order{}, []service.DriverWithLocation{}, []model.DedicatedZone{}
	}

	filterData := model.NewRiderFilterData()
	defer task.DistributionLogManager.CapturePreOptimizeFilterEvent(ctx, filterData, model.DedicatedRound, task.distributionLogMetadata)

	zoneMap, err := task.DedicatedZoneRepo.FindAllInCache(ctx)
	if err != nil {
		logx.Error().Err(err).Interface(logutil.OrderIDs, task.batchOrderIDs).Msg("batch_auto_assign: dedicated zone distribution failed because findAllInCache")
		return []model.Order{}, []service.DriverWithLocation{}, []model.DedicatedZone{}
	}

	dedicatedZonesLabels := types.NewStringSet()
	for _, d := range drivers {
		dedicatedZonesLabels.Add(d.Driver.DedicatedZones...)
	}

	// For optimization purpose, if an order matches with a zone that has no dedicated riders, they will be skipped.
	var dedicatedZones []model.DedicatedZone
	for _, zLabel := range dedicatedZonesLabels.GetElements() {
		zone, found := zoneMap[zLabel]
		if !found {
			logx.Warn().Interface(logutil.OrderIDs, task.batchOrderIDs).Msgf("batch_auto_assign: driver has dedicated zone %s but this dedicated zone doesn't exist", zLabel)
			continue
		}
		if zone.DisablePriorityRound {
			continue
		}
		dedicatedZones = append(dedicatedZones, zone)
	}

	mp1MapByMpID := make(map[string]model.Order)
	for _, order := range orders {
		if order.IsMp1() {
			mp1MapByMpID[order.Options.MpID] = order
		}
	}

	var applicableDedicatedZones []model.DedicatedZone
	var zOrders []model.Order

	applicableDedicatedZoneLabels := types.NewStringSet()
	for i := range orders {
		o := orders[i]

		ozc := newOrderZoneCacher(task.MapService)
		matchAtLeastOneZone := false
		for _, dZone := range dedicatedZones {
			if matchAtLeastOneZone && applicableDedicatedZoneLabels.Has(dZone.Label) {
				continue
			}

			overrideOrder := o
			mp1, exists := mp1MapByMpID[o.Options.MpID]
			if o.IsMp() && exists {
				overrideOrder = mp1
			}

			isSkipDedicatedRoundForRushOrder := task.snapshotCfg.skipDedicatedRoundForRushOrders && overrideOrder.IsDalianRush()
			if isSkipDedicatedRoundForRushOrder || overrideOrder.IsSkipDedicatedRound {
				continue
			}

			if ozc.validateOrderZone(&overrideOrder, dZone) {
				if !applicableDedicatedZoneLabels.Has(dZone.Label) {
					applicableDedicatedZones = append(applicableDedicatedZones, dZone)
					applicableDedicatedZoneLabels.Add(dZone.Label)
				}
				matchAtLeastOneZone = true
			}
		}
		if matchAtLeastOneZone {
			zOrders = append(zOrders, o)
		}
	}

	// Ineligible drivers function will mark drivers that do not match any order zones as ineligible drivers
	// By that assumption, we can filter for all riders with dedicated zones.
	// Even if they do not match with any order zones of any orders, they will be marked as ineligible for all orders.
	var zDrivers []service.DriverWithLocation
	for i := range drivers {
		d := drivers[i]
		matchAtLeastOneApplicableZone := false
		for _, zone := range d.Driver.DedicatedZones {
			if applicableDedicatedZoneLabels.Has(zone) {
				zDrivers = append(zDrivers, d)
				matchAtLeastOneApplicableZone = true
				break
			}
		}
		if !matchAtLeastOneApplicableZone {
			filterData.Add(model.NotDedicated, d.Driver.DriverID)
			continue
		}

	}

	return zOrders, zDrivers, applicableDedicatedZones
}

// TODO: When other service types of orders other than food enter batch, they must be filtered
// from supply positioning rounds until it can support other types of orders
func (task *autoAssignTask) filterForSupplyPositioningRound(ctx context.Context, orders []model.Order, drivers []service.DriverWithLocation, ordersAlreadyAssigned types.StringSet, ridersAlreadyAssigned types.StringSet) ([]model.Order, []service.DriverWithLocation) {
	filterData := model.NewRiderFilterData()
	defer task.DistributionLogManager.CapturePreOptimizeFilterEvent(ctx, filterData, model.SupplyPositioningRound, task.distributionLogMetadata)

	remainingOrders := []model.Order{}
	for i := range orders {
		if !ordersAlreadyAssigned.Has(orders[i].OrderID) {
			remainingOrders = append(remainingOrders, orders[i])
		}
	}

	remainingRiders := []service.DriverWithLocation{}
	for i := range drivers {
		if ridersAlreadyAssigned.Has(drivers[i].Driver.DriverID) {
			filterData.Add(model.AlreadyAssigned, drivers[i].Driver.DriverID)
			continue
		}
		remainingRiders = append(remainingRiders, drivers[i])
	}

	var supplyPositioningDrivers []service.DriverWithLocation
	for i := range remainingRiders {
		if len(remainingRiders[i].Driver.DedicatedZones) != 0 {
			filterData.Add(model.NotSupplyPositioned, remainingRiders[i].Driver.DriverID)
			continue
		}
		if _, ok := task.shouldAssignByRecommendedArea(ctx, remainingRiders[i]); !ok {
			filterData.Add(model.NotSupplyPositioned, remainingRiders[i].Driver.DriverID)
			continue
		}
		supplyPositioningDrivers = append(supplyPositioningDrivers, remainingRiders[i])
	}

	return remainingOrders, supplyPositioningDrivers
}

func (task *autoAssignTask) filterForSharedRound(ctx context.Context, orders []model.Order, drivers []service.DriverWithLocation, ordersAlreadyAssigned types.StringSet, ridersAlreadyAssigned types.StringSet) ([]model.Order, []service.DriverWithLocation) {
	filterData := model.NewRiderFilterData()
	defer task.DistributionLogManager.CapturePreOptimizeFilterEvent(ctx, filterData, model.SharedRound, task.distributionLogMetadata)

	remainingOrders := []model.Order{}
	for i := range orders {
		if !ordersAlreadyAssigned.Has(orders[i].OrderID) {
			remainingOrders = append(remainingOrders, orders[i])
		}
	}

	remainingRiders := []service.DriverWithLocation{}
	for i := range drivers {
		if ridersAlreadyAssigned.Has(drivers[i].Driver.DriverID) {
			filterData.Add(model.AlreadyAssigned, drivers[i].Driver.DriverID)
			continue
		}
		remainingRiders = append(remainingRiders, drivers[i])
	}

	return remainingOrders, remainingRiders
}

func (task *autoAssignTask) tryLockingDrivers(ctx context.Context, drivers []service.DriverWithLocation, optimizedPlan prediction.BatchOptimizeResponse) ([]service.DriverWithLocation, types.StringSet) {
	filterData := model.NewRiderFilterData()
	lockedRiders := []service.DriverWithLocation{}
	lockedRiderIDSet := types.NewStringSet()

	var wg sync.WaitGroup
	var mu sync.Mutex
	for _, d := range drivers {
		d := d
		wg.Add(1)
		safe.GoFuncWithCtx(ctx, func() {
			defer wg.Done()
			if o, ok := optimizedPlan.RiderOrders[d.Driver.DriverID]; ok && len(o) > 0 {
				if ok := task.Locker.SetState(ctx, locker.DriverAutoAssignState(d.Driver.DriverID), "batch", time.Duration(task.acceptDurationWithBufferInSeconds()*task.Config.MaxOrdersPerRider)*time.Second); !ok {
					logx.Warn().Str(logutil.DriverID, d.Driver.DriverID).Msgf("batch_auto_assign: driverID=%s driver is being assigned another order.", d.Driver.DriverID)
					filterData.Add(model.DriverIsBeingAssignedAnotherOrder, d.Driver.DriverID)
					return
				}
				mu.Lock()
				defer mu.Unlock()
				lockedRiderIDSet.Add(d.Driver.DriverID)
				lockedRiders = append(lockedRiders, d)
			}
		})
	}
	wg.Wait()

	task.DistributionLogManager.CapturePostFilterEvent(ctx, filterData, task.distributionLogMetadata)

	return lockedRiders, lockedRiderIDSet
}

func (task *autoAssignTask) getThrottledDispatchDetailFromMP1Order(ctx context.Context, order *model.Order, originalDetail *model.ThrottledDispatchDetailWithZoneCode) (*model.ThrottledDispatchDetailWithZoneCode, error) {
	mp1OrderID, err := order.MP1OrderID()
	if err != nil {
		logx.Error().Str(logutil.OrderID, order.OrderID).Err(err).Msgf("cannot get mp1 order id from orderID: %v", mp1OrderID)
		return originalDetail, nil
	}
	mp1Order, err := task.OrderRepository.Get(ctx, mp1OrderID)
	if err != nil && !errors.Is(err, repository.ErrNotFound) {
		logx.Error().Str(logutil.OrderID, order.OrderID).Err(err).Msgf("cannot get mp1 order id from orderID: %v", mp1OrderID)
		return nil, err
	}
	if isBatchAssigningMP(mp1Order) {
		return task.ThrottledDispatchDetailRepository.FindOneFromOrder(ctx, mp1Order, repository.WithReadSecondaryPreferred)
	}
	return originalDetail, nil
}

func (task *autoAssignTask) getThrottledDispatchDetail(ctx context.Context, order *model.Order, originalDetail *model.ThrottledDispatchDetailWithZoneCode) (*model.ThrottledDispatchDetailWithZoneCode, error) {
	if order.IsMp1() || !order.IsMp() {
		return originalDetail, nil
	}
	if !order.IsDalianMP {
		return nil, nil
	}

	return task.getThrottledDispatchDetailFromMP1Order(ctx, order, originalDetail)
}

func (task *autoAssignTask) updateProcessedBySingleDistributeAndSearchStrategy(ctx context.Context, order *model.Order, searchRiderStrategy model.SearchRiderStrategy) {
	err := task.OrderRepository.SetProcessedBySingleDistribution(ctx, order.OrderID, true, searchRiderStrategy)
	if err != nil {
		logx.Error().Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msg("can't set processed by single distribute")
		return
	}
	task.order.ProcessedBySingleDistribution = true
}

func (task *autoAssignTask) updateManyThrottleDeferActualAssigningAt(ctx context.Context, orders []model.Order) {
	for i := range orders {
		o := &orders[i]
		if o.ShouldUpdateThrottleDeferAssigningAt(o.EstimatedCookingFinishedTime()) { // For optimization purpose
			safe.GoFunc(func() {
				task.updateThrottleDeferActualAssigningAt(ctx, o)

				if err := task.OrderDistributionEventManager.PublishSearchingOrAssigningDriver(ctx, *o, timeutil.BangkokNow()); err != nil {
					logx.Error().Err(err).Str(logutil.TripID, o.TripID).Str(logutil.OrderID, o.OrderID).Msg("batch_auto_assign: cannot publish searching/assigning driver event")
				}
			})
		} else {
			if err := task.OrderDistributionEventManager.PublishSearchingOrAssigningDriver(ctx, *o, timeutil.BangkokNow()); err != nil {
				logx.Error().Context(ctx).Err(err).Str(logutil.TripID, o.TripID).Str(logutil.OrderID, o.OrderID).Msg("batch_auto_assign: cannot publish searching/assigning driver event orderID")
			}
		}
	}
}

func (task *autoAssignTask) shouldUpdateMPThrottleDeferAssigningAt(ctx context.Context, o *model.Order) (bool, error) {
	if !o.IsHasAnotherMPOrder() {
		return true, nil
	}

	otherMP, err := task.OrderRepository.GetOtherActiveMP(ctx, *o)
	if err != nil && !errors.Is(err, repository.ErrNotFound) {
		return false, err
	}
	if otherMP == nil {
		return true, nil
	}
	if !isBatchAssigningDeferredMP(o) || !isBatchAssigningDeferredMP(otherMP) {
		return true, nil
	}

	// specific case for MP: if both MP are batch-assigning, time.now must be greater than EstimatedCookingFinishedTime and maximum T1
	if o.Prediction.DeferUntil == nil || otherMP.Prediction.DeferUntil == nil {
		return false, nil
	}
	actualAssigning := timeutil.MaxTime(*o.Prediction.DeferUntil, *otherMP.Prediction.DeferUntil, o.EstimatedCookingFinishedTime())
	return time.Now().After(actualAssigning), nil
}

func (task *autoAssignTask) updateThrottleDeferActualAssigningAt(ctx context.Context, o *model.Order) {
	estimatedCookingFinishedTime := o.EstimatedCookingFinishedTime()
	shouldUpdate := o.ShouldUpdateThrottleDeferAssigningAt(estimatedCookingFinishedTime)
	if !shouldUpdate {
		return
	}

	if o.IsDalianMP {
		shouldUpdateMP, err := task.shouldUpdateMPThrottleDeferAssigningAt(ctx, o)
		if err != nil {
			logx.Error().Str(logutil.OrderID, o.OrderID).Err(err).Msgf("cannot update actual_assigning_at for mp order: %s", o.OrderID)
			return
		}
		if !shouldUpdateMP {
			return
		}
	}

	actualAssigningAt := timeutil.MaxTime(time.Now(), estimatedCookingFinishedTime)
	if err := task.OrderRepository.SetActualAssigningAtFromNil(ctx, o.OrderID, actualAssigningAt); err != nil {
		logx.Error().Str(logutil.OrderID, o.OrderID).Err(err).Msgf("cannot update actual_assigning_at for order: %s", o.OrderID)
		return
	}
	o.ActualAssigningAt = &actualAssigningAt
	if err := task.Delivery.UpdateActualAssigning(ctx, o.OrderID, v1.UpdateActualAssigningRequest{
		ActualAssigningAt: actualAssigningAt,
	}); err != nil {
		logx.Error().Str(logutil.OrderID, o.OrderID).Err(err).Msgf("calling update-actual-assigning failed for order: %s", o.OrderID)
	}
}

func (task *autoAssignTask) updateDeferUntil(ctx context.Context, optimizedPlan prediction.BatchOptimizeResponse, orders []model.Order) {
	ordersMap := make(map[string]model.Order) // must become readonly after write for concurrency
	for _, order := range orders {
		ordersMap[order.OrderID] = order
	}
	mpT1Map := calculateMpT1(ordersMap, optimizedPlan)

	wg := sync.WaitGroup{}
	for oId := range optimizedPlan.DeferOrders {
		orderId := oId
		order, ok := ordersMap[orderId]
		if !ok {
			logx.Warn().Str(logutil.OrderID, orderId).Msgf("batch_auto_assign: some order aren't found when update defer until for orderID: %v", orderId)
			continue
		}
		if !order.CanDefer() {
			continue
		}
		originalT1 := optimizedPlan.DeferOrders[orderId].T1
		originalT2 := optimizedPlan.DeferOrders[orderId].T2
		t1 := originalT1
		mpT1, hasMpT1 := mpT1Map[order.Options.MpID]
		if hasMpT1 {
			t1 = mpT1
		}

		var deferDuration time.Duration
		duration := time.Unix(t1, 0).Sub(order.CreatedAt)
		if order.DeferDuration < duration {
			deferDuration = duration
		}

		wg.Add(1)
		safe.GoFuncWithCtx(ctx, func() {
			defer wg.Done()
			err := task.OrderRepository.SetPredictionDeferTime(ctx, orderId, time.Unix(originalT1, 0), time.Unix(mpT1, 0), time.Unix(originalT2, 0), deferDuration)
			if err != nil {
				logx.Error().Str(logutil.OrderID, orderId).Err(err).Msgf("update prediction defer time failed for order: %s; err: %v", orderId, err)
			}
		})
	}
	wg.Wait()
}

func calculateMpT1(ordersMap map[string]model.Order, optimizedPlan prediction.BatchOptimizeResponse) map[string]int64 {
	mpT1Map := make(map[string]int64)
	mpOrdersGroup := make(map[string][]model.Order)
	isAllMpGroupDefer := make(map[string]bool)
	for orderID := range optimizedPlan.DeferOrders {
		order, ok := ordersMap[orderID]
		if !ok {
			logx.Warn().Str(logutil.OrderID, orderID).Msgf("batch_auto_assign: some order aren't found when calculate mp_t1 for orderID: %v", orderID)
			continue
		}
		if order.Options.MpID != "" {
			mpID := order.Options.MpID
			mpOrdersGroup[mpID] = append(mpOrdersGroup[mpID], order)
			if order.CanDefer() {
				if _, ok := isAllMpGroupDefer[mpID]; !ok {
					isAllMpGroupDefer[mpID] = true
				}
			} else {
				isAllMpGroupDefer[mpID] = false
			}
		}
	}

	for _, ords := range mpOrdersGroup {
		if len(ords) == 1 {
			continue
		}
		for _, order := range ords {
			mpID := order.Options.MpID
			optimizedOrder, ok := optimizedPlan.DeferOrders[order.OrderID]
			if !ok {
				logx.Warn().Str(logutil.OrderID, order.OrderID).Msgf("batch_auto_assign: some optimized order aren't found when calculate mp_t1 for orderID: %v", order.OrderID)
				continue
			}
			if _, ok := mpT1Map[mpID]; !ok {
				mpT1Map[mpID] = optimizedOrder.T1
				continue
			}
			// override t1 to be the maximum if all mp is (can)defer order
			// override t1 to be the minimum if there has some normal mp
			if (isAllMpGroupDefer[mpID] && mpT1Map[mpID] < optimizedOrder.T1) ||
				(!isAllMpGroupDefer[mpID] && mpT1Map[mpID] > optimizedOrder.T1) {
				mpT1Map[mpID] = optimizedOrder.T1
			}
		}
	}

	return mpT1Map
}

type batchAssignSetting struct {
	firstDistanceLimit           float64 // TODO deprecated, please remove it
	firstDistanceLimitByServices map[model.Service]float64
	b2bDistanceLimitByServices   map[model.Service]float64
	region                       string
}

func (task *autoAssignTask) batchAssign(
	ctx context.Context,
	drivers []service.DriverWithLocation,
	optimizedPlan prediction.BatchOptimizeResponse,
	orders []model.Order,
	searchRiderStrategy model.SearchRiderStrategy,
	setting batchAssignSetting,
	enabledAssignAPI bool,
) {
	orderIDs := make([]string, 0)
	ordersMap := make(map[string]model.Order) // must become readonly after write for concurrency
	for _, order := range orders {
		ordersMap[order.OrderID] = order
		orderIDs = append(orderIDs, order.OrderID)
	}
	defer task.batchStopHeartbeat(ctx, orderIDs)

	notifyOpts := []service.NotifyOption{service.WithFirebase}
	if task.notifyViaSocketIO {
		notifyOpts = append(notifyOpts, service.WithSocketIO)
	}

	isBatchAssignmentEnabled := task.batchAssignmentEnabled()
	for _, d := range drivers {
		d := d

		bgCtx := task.AutoAssignOrderDistributorDeps.WorkerContext.NewContextWithSameWaitGroup(ctx)
		task.Environment.RunGoWithCtx(bgCtx, func() {
			defer task.AutoAssignOrderDistributorDeps.WorkerContext.ContextDone(bgCtx)
			// Assign API will be responsible for lock/unlock
			if !enabledAssignAPI {
				defer task.Locker.RemoveState(bgCtx, locker.DriverAutoAssignState(d.Driver.DriverID))
			}
			orderIDs, ok := optimizedPlan.RiderOrders[d.Driver.DriverID] // read-only
			if !ok {
				return
			}
			newOrderIDs := types.NewStringSet()
			newOrders := []model.Order{}
			for _, orderID := range orderIDs {
				// ordersMap is read-only
				if order, ok := ordersMap[orderID]; ok && order.Status == model.StatusAssigningDriver { // status condition is probably redundant but for the purpose of decoupling
					newOrderIDs.Add(orderID)
					newOrders = append(newOrders, order)
				}
			}
			filterData := model.NewRiderFilterData()
			defer task.DistributionLogManager.CapturePostFilterWithTargetOrdersEvent(bgCtx, filterData, newOrders, task.distributionLogMetadata)

			if !enabledAssignAPI {
				for _, newOrder := range newOrders {
					if isRiderEligible, _, err := checkIfRiderEligibleForMp1(bgCtx, &newOrder, task.OrderRepository)(bgCtx, d.Driver); !isRiderEligible {
						logx.Error().
							Str(logutil.OrderID, newOrder.OrderID).
							Str(logutil.DriverID, d.Driver.DriverID).
							Str("error", err).
							Msgf("batch assign: can't assign %v to driver %v", newOrder.OrderID, d.Driver.DriverID)
						task.batchRedistribute(bgCtx, newOrderIDs.GetElements())
						filterData.Add(model.Mp1ATRCannotBeAssigned, d.Driver.DriverID)
						return
					}
				}
			}
			routeSetting := task.routeSetting(setting.firstDistanceLimitByServices, setting.b2bDistanceLimitByServices, optimizedPlan.OrdersTrip)
			routeRes, routeFilterData, err := task.PredictionService.Route(bgCtx, newOrders, d, routeSetting)
			if err != nil {
				if errors.Is(err, prediction.NoLegalPathFoundError) {
					task.insertIllegalDriver(bgCtx, timeutil.TheEndOfTime(), model.NoLegalPathFound, err, newOrders, newOrderIDs.GetElements(), d.Driver)
				}
				task.batchRedistribute(bgCtx, newOrderIDs.GetElements())
				filterData.AddExisting(routeFilterData)
				return
			}
			task.batchBundlingOrderCardinalityCountMetric.Add(float64(len(newOrders)), strconv.Itoa(len(newOrders)), task.batchZone.ZoneCode)

			if !isBatchAssignmentEnabled {
				dr := orderassigner.NewDeltaRoutePlan(routeRes, newOrderIDs)
				defer func() {
					if len(dr.RemainingOrderIDs()) != len(newOrders) {
						task.batchPartialFailAssignOrderCountMetric.Add(float64(len(dr.RemainingOrderIDs())), task.batchZone.ZoneCode)
					}
					task.batchRedistribute(bgCtx, dr.RemainingOrderIDs())
				}()
				for dr.CurrentNewOrderID() != "" {
					planRoute := dr.CurrentRoutePlan()
					order := ordersMap[dr.CurrentNewOrderID()] // read-only
					orderPtr := &order

					if _, err := task.validateIfDriverIsEligible(bgCtx, &d, []model.Order{order}); err != nil {
						logx.Error().
							Interface(logutil.OrderIDs, orderIDs).
							Str(logutil.DriverID, d.Driver.DriverID).
							Err(err).
							Msgf("batch assign: %v while trying to assign orders %s", err.Error(), orderIDs)
						return
					}

					if validateErrRes, err := task.PredictionService.ValidatePlanRoute(bgCtx, planRoute.RouteResponse, []string{orderPtr.OrderID}, d, task.validatePlanRouteSetting(setting.firstDistanceLimitByServices, setting.b2bDistanceLimitByServices)); err != nil {
						logx.Error().Str(logutil.DriverID, d.Driver.DriverID).Str(logutil.OrderID, order.OrderID).Err(err).Msgf("batch assign: validate plan route fail for driver %s while trying to assign order %s", d.Driver.DriverID, order.OrderID)
						task.insertIllegalDriver(bgCtx, validateErrRes.IllegalUntil, validateErrRes.Reason, err, []model.Order{order}, []string{orderPtr.OrderID}, d.Driver)
						return
					}

					orderPickupDistanceMap := make(map[string]float64, len(newOrders))
					table, err := task.MapService.FindDistances(bgCtx, []mapservice.Location{mapservice.Location(d.Location)}, mapservice.Location(order.Routes[0].Location))
					if err != nil || len(table.Distances) == 0 {
						logx.Error().Str(logutil.DriverID, d.Driver.DriverID).Str(logutil.OrderID, order.OrderID).Err(err).Msgf("batch assign: can't find distance for driver %s while trying to assign order %s", d.Driver.DriverID, order.OrderID)
						return
					}
					orderPickupDistanceMap[order.OrderID] = table.Distances[0]

					currentRound := task.calculateCurrentRound(bgCtx, order.OrderID, withCachedAssignmentLog(task.assignmentLogMemoize.GetOrLoad(bgCtx, order.OrderID))) + 1
					opt := orderassigner.NewAssignmentLogOpt(model.RegionCode(setting.region), true)
					assigner := task.newOrderAssigner(task.isUsingPredictionService)
					if assignmentResult := assigner.AssignDriver(
						bgCtx,
						&d,
						planRoute,
						opt,
						notifyOpts,
						[]**model.Order{&orderPtr},
						[]string{order.OrderID},
						map[string]int{order.OrderID: currentRound},
						false,
						orderPickupDistanceMap,
						searchRiderStrategy,
						nil,
					); len(assignmentResult.SuccessfullyAssignedOrders) != 1 {
						logx.Warn().Context(bgCtx).Str(logutil.DriverID, d.Driver.DriverID).Str(logutil.OrderID, order.OrderID).Msg("batch assign: assigning order unsuccessful to rider")
						return
					}
					dr.DoneWithCurrentOrder()
					task.Locker.RemoveState(bgCtx, locker.OrderAutoAssignState(order.OrderID))
				}
			} else {
				if !enabledAssignAPI {
					if filterName, err := task.validateIfDriverIsEligible(bgCtx, &d, newOrders); err != nil {
						logx.Error().
							Interface(logutil.OrderIDs, orderIDs).
							Str(logutil.DriverID, d.Driver.DriverID).
							Err(err).
							Msgf("batch assign: %v while trying to assign orders %s", err.Error(), orderIDs)
						task.batchRedistribute(bgCtx, newOrderIDs.GetElements())
						filterData.Add(filterName, d.Driver.DriverID)
						return
					}

					validateErrRes, err := task.PredictionService.ValidatePlanRoute(bgCtx, routeRes, newOrderIDs.GetElements(), d, task.validatePlanRouteSetting(setting.firstDistanceLimitByServices, setting.b2bDistanceLimitByServices))
					if task.Config.AtomicAutoAssignDbConfig.Get().EnableFixManipulateMultipleTrips {
						if err != nil && validateErrRes.Reason == model.ManipulateMultipleTripsNotAllowed {
							fixedRouteRes, fixedOrderIDs, fixedOrders, fixedValidateErrRes, fixedErr := task.FixManipulateMultipleTrips(bgCtx, d, routeRes, newOrders, routeSetting, task.validatePlanRouteSetting(setting.firstDistanceLimitByServices, setting.b2bDistanceLimitByServices))
							if fixedErr == nil { // if fix success
								notAssignedOrderIDs := newOrderIDs.Minus(fixedOrderIDs)
								if notAssignedOrderIDs.Count() > 0 {
									var notAssignedOrders []model.Order
									for i, o := range newOrders {
										if notAssignedOrderIDs.Has(o.OrderID) {
											notAssignedOrders = append(notAssignedOrders, newOrders[i])
										}
									}
									task.insertIllegalDriver(bgCtx, validateErrRes.IllegalUntil, validateErrRes.Reason, err, notAssignedOrders, notAssignedOrderIDs.GetElements(), d.Driver)

									fd := model.NewRiderFilterData()
									fd.Add(validateErrRes.Reason, d.Driver.DriverID)
									task.DistributionLogManager.CapturePostFilterWithTargetOrdersEvent(bgCtx, fd, notAssignedOrders, task.distributionLogMetadata)

									task.batchRedistribute(bgCtx, notAssignedOrderIDs.GetElements())

									task.batchPartialFailAssignOrderCountMetric.Add(float64(len(notAssignedOrders)), task.batchZone.ZoneCode)
								}

								routeRes, newOrderIDs, newOrders, validateErrRes, err = fixedRouteRes, fixedOrderIDs, fixedOrders, fixedValidateErrRes, nil
							} else { // just log
								log := logx.Error().
									Err(fixedErr).
									Str(logutil.DriverID, d.Driver.DriverID)
								var e errorWithOrderIDs
								if errors.As(fixedErr, &e) {
									log = log.Interface(logutil.OrderIDs, e.OrderIDs())
								}
								log.Msgf("batch assign: fix manipulate multiple trips fail for driver %s while trying to assign orders %s", d.Driver.DriverID, e.OrderIDs())
							}
						}
					}
					if err != nil {
						logx.Error().
							Time("illegal_until", validateErrRes.IllegalUntil).
							Interface(logutil.OrderIDs, orderIDs).
							Str(logutil.DriverID, d.Driver.DriverID).
							Err(err).
							Msgf("batch assign: validate plan route fail for driver %s while trying to assign orders %s", d.Driver.DriverID, orderIDs)
						task.insertIllegalDriver(bgCtx, validateErrRes.IllegalUntil, validateErrRes.Reason, err, newOrders, newOrderIDs.GetElements(), d.Driver)
						task.batchRedistribute(bgCtx, newOrderIDs.GetElements())
						filterData.Add(validateErrRes.Reason, d.Driver.DriverID)
						return
					}
				} else {
					if task.Config.AtomicAutoAssignDbConfig.Get().EnableFixManipulateMultipleTrips {
						dalianTrips := model.NewDalianTrips(routeRes.PlanRoutes)
						isManipulateMultipleTrips := dalianTrips.IsManipulatingMultipleTrips(newOrderIDs.GetElements())
						if isManipulateMultipleTrips {
							validateErrRes := service.NewValidateRouteErrRes(model.ManipulateMultipleTripsNotAllowed, timeutil.TheEndOfTime())
							fixedRouteRes, fixedOrderIDs, fixedOrders, fixedValidateErrRes, fixedErr := task.FixManipulateMultipleTrips(bgCtx, d, routeRes, newOrders, routeSetting, task.validatePlanRouteSetting(setting.firstDistanceLimitByServices, setting.b2bDistanceLimitByServices))
							if fixedErr == nil { // if fix success
								notAssignedOrderIDs := newOrderIDs.Minus(fixedOrderIDs)
								if notAssignedOrderIDs.Count() > 0 {
									var notAssignedOrders []model.Order
									for i, o := range newOrders {
										if notAssignedOrderIDs.Has(o.OrderID) {
											notAssignedOrders = append(notAssignedOrders, newOrders[i])
										}
									}
									task.insertIllegalDriver(bgCtx, validateErrRes.IllegalUntil, validateErrRes.Reason, err, notAssignedOrders, notAssignedOrderIDs.GetElements(), d.Driver)

									fd := model.NewRiderFilterData()
									fd.Add(validateErrRes.Reason, d.Driver.DriverID)
									task.DistributionLogManager.CapturePostFilterWithTargetOrdersEvent(bgCtx, fd, notAssignedOrders, task.distributionLogMetadata)

									task.batchRedistribute(bgCtx, notAssignedOrderIDs.GetElements())

									task.batchPartialFailAssignOrderCountMetric.Add(float64(len(notAssignedOrders)), task.batchZone.ZoneCode)
								}

								routeRes, newOrderIDs, newOrders, validateErrRes, err = fixedRouteRes, fixedOrderIDs, fixedOrders, fixedValidateErrRes, nil
							} else { // just log
								log := logx.Error().
									Err(fixedErr).
									Str(logutil.DriverID, d.Driver.DriverID)
								var e errorWithOrderIDs
								if errors.As(fixedErr, &e) {
									log = log.Interface(logutil.OrderIDs, e.OrderIDs())
								}
								log.Msgf("batch assign: fix manipulate multiple trips fail for driver %s while trying to assign orders %s", d.Driver.DriverID, e.OrderIDs())
							}
						}
					}
					for _, id := range newOrderIDs.GetElements() {
						task.UnlockOrder(bgCtx, id)
					}
					_, err := task.FleetOrderClient.AssignOrder(bgCtx, fleetorder.AssignOrderRequest{
						DriverID:                 d.Driver.DriverID,
						LastPredictionDisruption: d.Driver.LastPredictionDisruption,
						OrderIDs:                 newOrderIDs.GetElements(),
						PlanRoute:                fleetorder.PlanRouteToRequest(routeRes),
						Region:                   task.batchZone.Region,
						ZoneCode:                 task.batchZone.ZoneCode,
						DistributionStartedAt:    task.distributionStartedAt,
						AssignmentLogOpts: fleetorder.AssignmentLogOpts{
							IsUsingPredictionService: task.isUsingPredictionService,
							SearchRiderStrategy:      searchRiderStrategy.String(),
							SwitchbackExperiments:    task.switchbackExperiments,
							PredictionModelVersions:  task.predictionModelVersions,
							UseBikePriority:          task.useBikePriority,
							IsPrioritized:            d.IsPrioritized,
							IsDedicatedZone:          d.IsDedicatedZone,
							SearchRadiusByDistrict:   task.searchRadiusByDistrict,
							MOAggressiveLevel:        task.multipleOrderAggressiveLevelWithSwitchbackExperiments(),
						},
						DistributionLogMetadata: fleetorder.ToDistributionLogMetadataReq(task.distributionLogMetadata),
						AssignmentConstraint: fleetorder.AssignmentConstraintReq{
							AssignmentType:       string(AnyAssignmentType),
							FirstDistanceSetting: setting.firstDistanceLimitByServices,
							B2BDistanceSetting:   setting.b2bDistanceLimitByServices,
						},
					})
					if err != nil {
						logx.Error().Context(bgCtx).Err(err).Interface(logutil.OrderIDs, newOrderIDs.GetElements()).Msgf("batch_auto_assign: failed on calling assign order API")
						task.batchRedistribute(bgCtx, newOrderIDs.GetElements())
					}
					return
				}

				orderPickupDistanceMap := make(map[string]float64, len(newOrders))
				for _, order := range newOrders {
					table, err := task.MapService.FindDistances(bgCtx, []mapservice.Location{mapservice.Location(d.Location)}, mapservice.Location(order.Routes[0].Location))
					if err != nil || len(table.Distances) == 0 {
						logx.Error().Str(logutil.OrderID, order.OrderID).Str(logutil.DriverID, d.Driver.DriverID).Err(err).Msgf("batch assign: can't find distance for driver %s while trying to assign order %s", d.Driver.DriverID, order.OrderID)
						task.batchRedistribute(bgCtx, newOrderIDs.GetElements())
						filterData.Add(model.DistanceNotFound, d.Driver.DriverID)
						return
					}
					orderPickupDistanceMap[order.OrderID] = table.Distances[0]
				}

				currentRoundMap := make(map[string]int, len(newOrders))
				for _, o := range newOrders {
					round := task.calculateCurrentRound(bgCtx, o.OrderID, withCachedAssignmentLog(task.assignmentLogMemoize.GetOrLoad(bgCtx, o.OrderID))) + 1
					currentRoundMap[o.OrderID] = round
				}

				var orderPtrs []**model.Order
				for _, o := range newOrders {
					order := ordersMap[o.OrderID] // read-only
					orderPtr := &order
					orderPtrs = append(orderPtrs, &orderPtr)
				}

				opt := orderassigner.NewAssignmentLogOpt(model.RegionCode(setting.region), true)
				assigner := task.newOrderAssigner(task.isUsingPredictionService)
				assignmentResult := assigner.AssignDriver(
					bgCtx,
					&d,
					model.PlanRoute{RouteResponse: routeRes},
					opt,
					notifyOpts,
					orderPtrs,
					newOrderIDs.GetElements(),
					currentRoundMap,
					true,
					orderPickupDistanceMap,
					searchRiderStrategy,
					nil,
				)
				for _, successfullyAssignedOrderID := range assignmentResult.SuccessfullyAssignedOrders {
					task.Locker.RemoveState(bgCtx, locker.OrderAutoAssignState(successfullyAssignedOrderID))
				}

				if len(assignmentResult.RedistributableOrders) != 0 {
					task.batchRedistribute(bgCtx, assignmentResult.RedistributableOrders)
				}

				if len(assignmentResult.SuccessfullyAssignedOrders) != len(newOrders) {
					unsuccessfulOrders := newOrderIDs.Minus(types.NewStringSet(assignmentResult.SuccessfullyAssignedOrders...))
					task.batchPartialFailAssignOrderCountMetric.Add(float64(unsuccessfulOrders.Count()), task.batchZone.ZoneCode)
					logx.Warn().Str(logutil.DriverID, d.Driver.DriverID).Interface(logutil.OrderIDs, unsuccessfulOrders.GetElements()).Msgf("batch assign: assigning orders %s unsuccessful to rider %s", unsuccessfulOrders.GetElements(), d.Driver.DriverID)
					return
				}
			}
		})
	}
}

func (task *autoAssignTask) newOrderAssigner(isUsingPredictionService bool) *orderassigner.OrderAssignerTask {
	return &orderassigner.OrderAssignerTask{
		OrderAssignerTaskDeps: orderassigner.NewOrderAssignerDeps(
			task.Config.AtomicAutoAcceptConfig,
			task.Config.AtomicAutoAssignDbConfig,
			task.DistributionConfig,
			task.AssignmentLogRepo,
			task.IllegalDriverRepository,
			task.Locker,
			task.DriverRepository,
			task.OrderRepository,
			task.ServiceAreaRepository,
			task.RainSituationService,
			task.OnTopFareService,
			task.DriverLocationRepository,
			task.MapService,
			task.OrderConfig,
			task.AssignmentRepo,
			task.StatisticService,
			task.Bus,
			task.DriverService,
			task.Notifier,
			task.TxnHelper,
			task.AcceptOrderFn,
			task.AcceptAssignmentFn,
			task.Environment,
			task.deferredOrderAcceptCountMetric,
			task.lockingDriverMetric,
			task.DistributionLogManager,
			task.ServicePreferenceService,
			task.PredictionService,
			task.metricsRegistry,
			nil, // no need post filter metric when not use assigning API
		),
		OrderAssignerTaskState: orderassigner.NewOrderAssignerState(
			task.order,
			task.Config.AutoAssignDistribution,
			isUsingPredictionService,
			task.acceptDurationBuffer,
			task.switchbackExperiments,
			task.predictionModelVersions,
			task.expireAt,
			task.useBikePriority,
			task.stopMetricWithRegion,
			task.searchRadiusByDistrict,
			task.multipleOrderAggressiveLevelWithSwitchbackExperiments(),
			task.distributionStartedAt,
		),
	}
}

// validateIfDriverIsEligible pulls latest driver profile to check if eligible and set the driver object back to driver with location
func (task *autoAssignTask) validateIfDriverIsEligible(ctx context.Context, d *service.DriverWithLocation, orders []model.Order) (model.RiderFilterName, error) {
	task.incrementMetrics(ctx, "validate", d.Driver.Region.String())

	profile, err := task.getDriverProfile(ctx, d.Driver.DriverID)
	if err != nil {
		return model.RiderProfileNotFound, err
	}

	if profile.LastPredictionDisruption.After(d.Driver.LastPredictionDisruption) && !profile.LastPredictionDisruption.Equal(d.Driver.LastPredictionDisruption) {
		return model.LastPredictionDisruptionChanged, errors.New("the driver must be offline later but the status is assigned")
	}
	d.Driver = *profile

	if filterName, err := task.validateDriverStatus(ctx, d); err != nil {
		return filterName, err
	}

	if err := task.validateOrderServiceTypes(ctx, d, orders); err != nil {
		return model.RiderOptedOut, err
	}

	return "", nil
}

func (task *autoAssignTask) getDriverProfile(ctx context.Context, driverID string) (*model.DriverMinimal, error) {
	profiles, err := task.DriverRepository.GetMinimalProfilesByID(ctx, []string{driverID})
	if err != nil {
		task.logAndIncrementError(ctx, driverID, "", "get_profile", fmt.Sprintf("failed to fetch driver %s profile", driverID), err)
		return nil, fmt.Errorf("failed to fetch driver profile: %w", err)
	}

	profile, exists := profiles[driverID]
	if !exists {
		task.logAndIncrementError(ctx, driverID, "", "get_profile", fmt.Sprintf("driver %s profile not found", driverID), nil)
		return nil, fmt.Errorf("driver profile not found: %s", driverID)
	}

	return profile, nil
}

func (task *autoAssignTask) validateDriverStatus(ctx context.Context, d *service.DriverWithLocation) (model.RiderFilterName, error) {
	isValid, profile, msg := isOnlineOrAssigned(ctx, d.Driver)
	if !isValid {
		task.logAndIncrementError(ctx, d.Driver.DriverID, d.Driver.Region.String(), "validate_online_or_assigned_status",
			fmt.Sprintf("driver %s is not eligible to assign order. msg: %s", d.Driver.DriverID, msg), nil)
		return profile, fmt.Errorf("driver is not eligible to assign order (status=%s). msg: %s", d.Driver.Status, msg)
	}

	if d.Driver.OfflineLater {
		task.logAndIncrementError(ctx, d.Driver.DriverID, d.Driver.Region.String(), "validate_status_offline_later",
			fmt.Sprintf("driver %s  enabled offline-later mode", d.Driver.DriverID), nil)
		return model.OfflineLater, fmt.Errorf("driver enabled offline-later mode")
	}

	return "", nil
}

func (task *autoAssignTask) validateOrderServiceTypes(ctx context.Context, d *service.DriverWithLocation, orders []model.Order) error {
	if err := task.validateOptedOutOrderServiceTypes(ctx, &d.Driver, orders); err != nil {
		task.logAndIncrementError(ctx, d.Driver.DriverID, d.Driver.Region.String(), "validate_opted_out",
			fmt.Sprintf("driver %s opted out for this order's service type", d.Driver.DriverID), err)
		return fmt.Errorf("driver opted out of service type: %w", err)
	}
	return nil
}

func (task *autoAssignTask) logAndIncrementError(ctx context.Context, driverID, region, errorCode, msg string, err error) {
	logx.Error().Err(err).
		Str(logutil.DriverID, driverID).
		Str(logutil.Region, region).
		Msgf("validateIfDriverIsEligible: %s", msg)
	task.incrementMetrics(ctx, errorCode, region)
}

func (task *autoAssignTask) incrementMetrics(ctx context.Context, errorCode, region string) {
	task.metricsRegistry.IncrValidateAutoAssignDriverIsEligibleCounter(ctx, errorCode, region)
}

func (task *autoAssignTask) validateOptedOutOrderServiceTypes(ctx context.Context, driver *model.DriverMinimal, orders []model.Order) error {
	optedOutSet := task.driverOptedOutServicesSet(ctx, driver)

	for _, o := range orders {
		if optedOutSet.Has(o.ServiceType) {
			return fmt.Errorf("batch assign: driver %s opted out for this order's service type", driver.DriverID)
		}
	}
	return nil
}

type calculateCurrentRoundOption struct {
	cachedAssignmentLog *model.AssignmentLogRecord
}

func withCachedAssignmentLog(cached *model.AssignmentLogRecord) calculateCurrentRoundOptionFn {
	return func(opt *calculateCurrentRoundOption) {
		opt.cachedAssignmentLog = cached
	}
}

type calculateCurrentRoundOptionFn func(opt *calculateCurrentRoundOption)

func (task *autoAssignTask) calculateCurrentRound(ctx context.Context, orderID string, opts ...calculateCurrentRoundOptionFn) int {
	var opt calculateCurrentRoundOption
	for _, fn := range opts {
		fn(&opt)
	}

	var records []model.Record
	if opt.cachedAssignmentLog != nil {
		records = opt.cachedAssignmentLog.Drivers
	} else {
		v, err := task.AssignmentLogRepo.AssignedDrivers(ctx, orderID)
		if err != nil {
			logx.Error().Err(err).Str(logutil.OrderID, orderID).Msgf("batch assign: couldn't find records to calculate current round for %s", orderID)
		}
		records = v
	}
	maxRound, err := model.CalculateMaxRound(records)
	if err != nil {
		logx.Error().Err(err).Str(logutil.OrderID, orderID).Msgf("batch assign: couldn't calculate max round for %s", orderID)
	}
	return maxRound
}

func (task *autoAssignTask) applyConsecutiveRoundsWithNoRiders(redistributionState model.RedistributionState, ridersTriedAssigning *int) {
	if ridersTriedAssigning != nil {
		if *ridersTriedAssigning == 0 {
			task.consecutiveRoundsWithNoRiders = redistributionState.ConsecutiveRoundsWithNoRiders + 1
		} else if *ridersTriedAssigning > 0 {
			task.consecutiveRoundsWithNoRiders = 0
		}
	}
}

func (task *autoAssignTask) batchRedistribute(ctx context.Context, orderIDs []string) {
	if len(orderIDs) == 0 {
		return
	}

	task.batchRedistributionCountMetric.Add(float64(len(orderIDs)), task.batchZone.ZoneCode)

	bgCtx := safe.NewContextWithSameWaitGroup(ctx)
	for _, o := range orderIDs {
		if task.lockedOrderIDs.Has(o) {
			task.Locker.RemoveState(bgCtx, locker.OrderAutoAssignState(o))
		}
	}

	for _, orderID := range orderIDs {
		orderID := orderID

		bgCtx := task.AutoAssignOrderDistributorDeps.WorkerContext.NewContextWithSameWaitGroup(ctx)
		task.Environment.RunGoWithCtx(bgCtx, func() {
			defer task.AutoAssignOrderDistributorDeps.WorkerContext.ContextDone(bgCtx)
			_ = task.redistribute(bgCtx, orderID, nil)
		})
	}
}

func (task *autoAssignTask) redistribute(ctx context.Context, orderID string, ridersTriedAssigning *int) error {
	if err := task.DistributionService.PublishRedistributeOrderEvent(ctx, orderID, ridersTriedAssigning); err != nil {
		logx.Error().Err(err).Context(ctx).Str(logutil.OrderID, orderID).Msg("redistribute: can't publish redistribute event")
		return err
	}

	return nil
}

func (task *autoAssignTask) ineligibleDrivers(ctx context.Context, drivers []service.DriverWithLocation, orders []model.Order, driverOtherMPs []repository.DriverMP) map[string]types.StringSet {
	allDriversSet := driverWithLocToStringSet(drivers)
	ineligibleDriversWithOtherMPDrivers := newIneligibleDriversWithOtherMPDriversCalculator(driverOtherMPs)
	ineligibleDriversToOrder := make(map[string]types.StringSet)

	var wg sync.WaitGroup
	var mu sync.Mutex
	memoizer := newSelectorMemoizer()
	driverIDs := make([]string, len(drivers))
	for i, d := range drivers {
		driverIDs[i] = d.Driver.DriverID
	}
	driverTransactions, err := task.AutoAssignOrderDistributorDeps.DriverTransaction.GetMany(ctx, driverIDs)
	if err != nil {
		logx.Warn().Interface(logutil.DriverIDs, driverIDs).Err(err).Msgf("ineligibleDrivers: attempt to pre-populate driver transaction memoization failed (GetMany)")
	} else {
		for _, t := range driverTransactions {
			memoizer.driverTransMem.valueTable[t.DriverID] = t
		}
	}

	batchOrderSet := sets.New[string]()
	for i := range orders {
		batchOrderSet.Add(orders[i].OrderID)
	}

	err = task.assignmentLogMemoize.GetMany(ctx, batchOrderSet.GetElements())
	if err != nil {
		logx.Error().Err(err).Msgf("autoassign: ineligibleDrivers cannot get assignment log with error %v.", err)
	}

	for i := 0; i < len(orders); i++ {
		ordersIdx := i
		wg.Add(1)
		safe.GoFuncWithCtx(ctx, func() {
			defer wg.Done()

			order := &orders[ordersIdx]

			filterData := model.NewRiderFilterData()
			scorers := task.Config.createBatchScorers(task.AutoAssignOrderDistributorDeps, order, batchOrderSet, task.snapshotCfg)
			drvSelector := task.Config.createBatchDriverSelector(ctx, task.AutoAssignOrderDistributorDeps, order, task.AreaDistCfg, memoizer, task.getOrLoadIllegalDriversIDs(ctx, order.OrderID))
			offsetDrivers := []service.DriverWithLocation{}
			indexOffset := 0
			if len(drivers) != 0 && len(orders) != 0 {
				indexOffset = (ordersIdx * (len(drivers) / len(orders))) % len(drivers)
			}
			offsetDrivers = append(offsetDrivers, drivers[indexOffset:]...)
			offsetDrivers = append(offsetDrivers, drivers[0:indexOffset]...)

			eligibleDrivers, filterAndSortDriversFilterData := filterAndSortDrivers(
				ctx,
				task.AutoAssignOrderDistributorDeps,
				order,
				offsetDrivers,
				scorers,
				drvSelector,
				task.Config,
				task.debugLogger,
				newSearchAndFilterOption(task),
				task.getOrLoadNotifiedDrivers(ctx, order.OrderID),
			)
			filterData.AddExisting(filterAndSortDriversFilterData)

			whitelistedDrivers, whitelistFilterData := whitelistDrivers(
				ctx,
				eligibleDrivers,
				order,
				task.Config,
				task.debugLogger,
			)
			filterData.AddExisting(whitelistFilterData)
			whitelistedDriversSet := driverWithLocToStringSet(whitelistedDrivers)
			ineligibleDrivers := allDriversSet.Minus(whitelistedDriversSet)

			var mpDriversFilterData *model.RidersFilterData
			ineligibleDrivers, mpDriversFilterData = ineligibleDriversWithOtherMPDrivers.Calculate(ineligibleDrivers, order)
			filterData.AddExisting(mpDriversFilterData)

			task.DistributionLogManager.CapturePreFilterWithTargetOrdersEvent(ctx, filterData, []model.Order{*order}, task.distributionLogMetadata)

			mu.Lock()
			defer mu.Unlock()
			ineligibleDriversToOrder[order.OrderID] = ineligibleDrivers
		})
	}
	wg.Wait()

	return ineligibleDriversToOrder
}

func (task *autoAssignTask) preFilterDrivers(ctx context.Context, drivers []service.DriverWithLocation, regions []model.RegionCode) []service.DriverWithLocation {
	filterData := model.NewRiderFilterData()
	drvSelector := task.Config.createPreFilterDrivers(regions)
	result := make([]service.DriverWithLocation, 0, len(drivers))

	var mu sync.Mutex
	var wg sync.WaitGroup

	for i := range drivers {
		d := drivers[i]
		wg.Add(1)
		safe.GoFuncWithCtx(ctx, func() {
			defer wg.Done()
			if passed, filterName, msg := drvSelector(ctx, d.Driver); !passed {
				filterData.Add(filterName, d.Driver.DriverID)
				logx.Info().Str(logutil.DriverID, d.Driver.DriverID).Msgf("batch_auto_assign: driverID=%s, the driver was not selected by the selector, description=%s", d.Driver.DriverID, msg)
				return
			}

			if v := task.Locker.GetState(ctx, locker.DriverAutoAssignState(d.Driver.DriverID)); v != "" {
				filterData.Add(model.DriverIsBeingAssignedAnotherOrder, d.Driver.DriverID)
				logx.Warn().Str(logutil.DriverID, d.Driver.DriverID).Msgf("batch_auto_assign: driverID=%s driver is being assigned another order.", d.Driver.DriverID)
				return
			}
			mu.Lock()
			defer mu.Unlock()
			result = append(result, d)
		})
	}
	wg.Wait()
	task.DistributionLogManager.CapturePreFilterEvent(ctx, filterData, task.distributionLogMetadata)

	return result
}

func (task *autoAssignTask) preFilterOrders(ctx context.Context, orders []model.Order) []model.Order {
	result := make([]model.Order, 0, len(orders))

	var wg sync.WaitGroup
	var mu sync.Mutex

	for i := range orders {
		ordersIdx := i
		wg.Add(1)
		safe.GoFuncWithCtx(ctx, func() {
			defer wg.Done()

			order := orders[ordersIdx]
			if order.Prediction == nil {
				return
			}

			// FRAGILE: cannot have filters after lock otherwise it won't get unlocked
			if ok := task.Locker.SetState(ctx, locker.OrderAutoAssignState(order.OrderID), "batch", time.Duration(task.acceptDurationWithBufferInSeconds()*task.Config.MaxOrdersPerRider)*time.Second); !ok {
				logx.Warn().Str(logutil.OrderID, order.OrderID).Msgf("batch_auto_assign: orderID=%s order is being assigned in another process", order.OrderID)
				return
			}

			task.lockedOrderIDs.Add(order.OrderID)

			mu.Lock()
			defer mu.Unlock()
			result = append(result, order)
		})
	}
	wg.Wait()

	return result
}

func (task *autoAssignTask) unionDistributeRegions(orders []model.Order) []model.RegionCode {
	regionSet := model.RegionSetFromList([]model.RegionCode{})
	for _, o := range orders {
		for _, region := range o.DistributeRegions {
			regionSet.Add(region)
		}
	}
	return regionSet.Elements()
}

func (task *autoAssignTask) callBatchOptimize(ctx context.Context, orders []model.Order, drivers []service.DriverWithLocation, ineligibleDriversToOrder map[string]types.StringSet, setting batchAssignSetting, optimizationRound model.OptimizationRound) (prediction.BatchOptimizeResponse, error) {
	cfg := service.NewOptimizeSetting(
		task.Config.AssignmentType,
		task.Config.MOType,
		task.Config.MaxOrdersPerRider,
		task.multipleOrderAggressiveLevelWithSwitchbackExperiments(),
		task.Config.OptimizationCandidateNumber,
		task.restaurantBlacklistSets,
		task.Config.BypassIdleTime,
		ineligibleDriversToOrder,
		setting.firstDistanceLimit,
		setting.firstDistanceLimitByServices,
		setting.b2bDistanceLimitByServices,
		task.modelVersionWithSwitchbackExperiments(model.BatchOptimizeModel),
		task.dalianOptimizeOSRMEnabled(),
		task.fallbackDalianOSRMPhase(),
		task.isForSaversExperiment,
		task.Config.DalianDrivingDurationInSeconds,
		task.rushModeWithSwitchbackExperiments(),
		task.Config.SmartDistributionGoodnessBiasLevel,
		task.distributionLogMetadata,
		optimizationRound,
		task.Config.BikeB2BEnabled,
		task.Config.BikeCrossServiceEnabled,
		task.Config.MartMOB2BEnabled,
		task.Config.MartCrossServiceEnabled,
		task.Config.ServicePreference,
		task.useBikePriority,
		task.serviceBiasWeightsWithSwitchbackExperiments(),
	)
	optimizedPlan, err := task.PredictionService.BatchOptimize(ctx, orders, drivers, cfg)
	if err != nil {
		driverIDs := make([]string, 0, len(drivers))
		for _, d := range drivers {
			driverIDs = append(driverIDs, d.Driver.DriverID)
		}
		orderIDs := make([]string, 0, len(orders))
		for _, o := range orders {
			orderIDs = append(orderIDs, o.OrderID)
		}
		logx.Error().Err(err).Interface(logutil.DriverIDs, driverIDs).Interface(logutil.OrderIDs, orderIDs).Msgf("autoassign: call batch optimization err=%v", err)
	}
	task.logBatchOptimizeMappedOrder(orders, drivers, optimizedPlan, optimizationRound)
	return optimizedPlan, err
}

func driverWithLocToStringSet(drivers []service.DriverWithLocation) types.StringSet {
	res := types.NewStringSet()
	for _, d := range drivers {
		res.Add(d.Driver.DriverID)
	}
	return res
}

func (task *autoAssignTask) newDriverQueue(drivers []service.DriverWithLocation) *DriverQueue {
	driverQueue := &DriverQueue{
		CurIdx:            0,
		DriverAssignments: make([]DriverAssignment, 0),
	}

	if len(drivers) == 0 {
		return driverQueue
	}

	notBundledTopN := task.Config.AtomicAutoAssignDbConfig.Get().PreferNotBundledTopN
	if task.order.IsPreferNotBundled() && notBundledTopN > 0 {
		if len(drivers) < notBundledTopN {
			notBundledTopN = len(drivers)
		}
		driverQueue.AddMany(drivers[:notBundledTopN], OnlineAssignmentType)
	}

	optimizationTopN := int(task.Config.OptimizationTopN)
	if optimizationTopN > 0 {
		if len(drivers) < optimizationTopN {
			optimizationTopN = len(drivers)
		}
		driverQueue.AddMany(drivers[:optimizationTopN], MOAssignmentType)
	}

	driverQueue.AddMany(drivers, AnyAssignmentType)
	return driverQueue
}

func (task *autoAssignTask) LockOrder(ctx context.Context, orderID string, ttl time.Duration) bool {
	ok := task.Locker.SetState(
		ctx,
		locker.OrderAutoAssignState(orderID),
		"auto-assign",
		ttl,
	)
	if !ok {
		logx.Warn().Str(logutil.OrderID, task.order.OrderID).Msgf("auto_assign: order is being assigned in another process")
		return false
	}

	task.lockedOrderIDs.Add(orderID)
	return true
}

func (task *autoAssignTask) UnlockOrder(ctx context.Context, orderID string) {
	if !task.lockedOrderIDs.Has(orderID) {
		return
	}
	task.Locker.RemoveState(
		ctx,
		locker.OrderAutoAssignState(orderID),
	)
	task.lockedOrderIDs.Remove(orderID)
}

func (task *autoAssignTask) SetIsTriedForceAssignMP(ctx context.Context, order *model.Order) error {
	if err := task.OrderRepository.SetIsTriedForceAssignMP(ctx, order.OrderID, true); err != nil {
		return err
	}
	order.IsTriedForceAssignMP = types.Ptr(true)
	return nil
}

func (task *autoAssignTask) tryToAssignMpOrder(ctx context.Context, opt model.AssignmentLogOpt) bool {
	if task.order.IsTriedForceAssignMP != nil && *task.order.IsTriedForceAssignMP {
		return false
	}

	if err := task.SetIsTriedForceAssignMP(ctx, task.order); err != nil {
		logx.Error().Err(err).Context(ctx).Str(logutil.OrderID, task.order.OrderID).Msg("autoassign mp: set IsTriedForceAssignMP error")
		return false
	}

	if len(task.order.Options.MpOrderIDs) > 1 {
		logx.Error().Context(ctx).Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign mp: orderID=%s, more than 2 multiple pickup orders are not supported", task.order.OrderID)
		return false
	}

	orderToBundleID := task.order.Options.MpOrderIDs[0]
	orderToBundleWith, err := task.OrderRepository.Get(ctx, orderToBundleID)
	if err != nil {
		logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Err(err).Msgf("autoassign mp: orderID=%s, get order failed, err=%v", orderToBundleID, err)
		return false
	}

	if orderToBundleWith.DeliveringRound != 0 {
		logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Str(logutil.BundleOrderID, orderToBundleID).Err(err).Msgf("autoassign mp: orderID=%s orderToBundleID=%s, order reassigned is not allowed", task.order.OrderID, orderToBundleID)
		return false
	}

	if orderToBundleWith.Status != model.StatusDriverMatched &&
		orderToBundleWith.Status != model.StatusRestaurantAccepted &&
		orderToBundleWith.Status != model.StatusDriverToRestaurant &&
		orderToBundleWith.Status != model.StatusDriverArrivedRestaurant {
		logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Str(logutil.BundleOrderID, orderToBundleID).Err(err).Msgf("autoassign mp: orderID=%s orderToBundleID=%s, status %v is not allowed", task.order.OrderID, orderToBundleID, orderToBundleWith.Status)
		return false
	}

	driversByID, err := task.DriverRepository.GetMinimalProfilesByID(ctx, []string{orderToBundleWith.Driver})
	if err != nil {
		logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Str(logutil.BundleOrderID, orderToBundleID).Err(err).Msgf("autoassign mp: orderID=%s orderToBundleID=%s, get minimal driver %s failed, err=%v", task.order.OrderID, orderToBundleID, orderToBundleWith.Driver, err)
		return false
	}

	driv, ok := driversByID[orderToBundleWith.Driver]
	if !ok {
		logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Str(logutil.BundleOrderID, orderToBundleID).Err(err).Msgf("autoassign mp: orderID=%s orderToBundleID=%s, minimal driver %s not found", task.order.OrderID, orderToBundleID, orderToBundleWith.Driver)
		return false
	}

	searchStartedAt := timeutil.BangkokNow()
	drivWithLocation, err := task.DriverLocationRepository.GetDriverLocation(ctx, orderToBundleWith.Driver)
	if err != nil {
		logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Str(logutil.BundleOrderID, orderToBundleID).Err(err).Msgf("autoassign mp: orderID=%s orderToBundleID=%s, get driver %s location failed, err=%v", task.order.OrderID, orderToBundleID, orderToBundleWith.Driver, err)
		return false
	}

	drivLocation := service.DriverWithLocation{
		Driver:        *driv,
		Location:      drivWithLocation.Location,
		DistanceMeter: drivWithLocation.DistanceMeter,
	}

	if err := task.OrderDistributionEventManager.PublishSearchingOrAssigningDriver(ctx, *task.order, timeutil.BangkokNow()); err != nil {
		logx.Error().Context(ctx).Err(err).Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Msg("auto_assign mp: cannot publish searching/assigning event")
	}

	filterData := model.NewRiderFilterData()
	task.DistributionLogManager.CaptureSearchEvent(ctx, []service.DriverWithLocation{drivLocation}, []model.Order{*task.order}, searchStartedAt, task.distributionLogMetadata)
	defer func() {
		task.DistributionLogManager.CapturePreFilterEvent(ctx, filterData, task.distributionLogMetadata)
		task.distributionLogMetadata.RenewID(task.Config.AtomicAutoAssignDbConfig.Get().DistributionLogSamplingRatio)
	}()

	isUnlockRequired := true
	defer func(isUnlockRequired *bool) {
		if isUnlockRequired != nil && *isUnlockRequired {
			if unlockErr := task.DriverRepository.UnlockForQueueing(ctx, orderToBundleWith.Driver); unlockErr != nil {
				logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Str(logutil.BundleOrderID, orderToBundleID).Err(err).Msgf("autoassign mp: unable to unlock driver %v for qeueuing, err=%v", orderToBundleWith.Driver, unlockErr)
			}
		}
	}(&isUnlockRequired)

	// driver selected still must pass the scorer and selector and will fall back to other riders if failed
	scores, scorerFilterData := calcScoreThenSort(ctx, task.scorers, task.order, []service.DriverWithLocation{drivLocation}, true, task.Config.DebugScorer, task.isForSaversExperiment)
	if len(scores) == 0 {
		filterData.AddExisting(scorerFilterData)
		logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Str(logutil.BundleOrderID, orderToBundleID).Err(err).Msgf("autoassign mp: orderID=%s orderToBundleID=%s, driver %s was filtered out by scorer", task.order.OrderID, orderToBundleID, orderToBundleWith.Driver)
		return false
	}

	if passed, filterName, msg := task.driverSelector(ctx, *driv); !passed {
		filterData.Add(filterName, driv.DriverID)
		logx.Error().Str(logutil.TripID, task.order.TripID).Str(logutil.OrderID, task.order.OrderID).Str(logutil.BundleOrderID, orderToBundleID).Err(err).Msgf("autoassign mp: orderID=%s orderToBundleID=%s driverID=%s, the driver was not selected by the selector, description=%s", task.order.OrderID, orderToBundleID, driv.DriverID, msg)
		return false
	}

	// option multiple pickup is set to true only in this function
	// when fallbacks to other riders, the option should not be set
	opt.IsMultiplePickup = true

	// Set the search strategy to no search since there is no tactical search in assigning to the same rider as the first order.
	opt.SearchRiderStrategy = model.StrategyNameNoSearch

	isUnlockRequired = false // move responsibility to notifyBestDriver
	task.UnlockOrder(ctx, task.order.OrderID)
	task.notifyBestDriverInBackground(ctx, task.newDriverQueue([]service.DriverWithLocation{drivLocation}), opt, task.order, task.currentAssigningSetting())
	task.updateProcessedBySingleDistributeAndSearchStrategy(ctx, task.order, opt.SearchRiderStrategy)
	return true
}

func (task *autoAssignTask) tryDeferredDispatch(ctx context.Context, isFallback bool) bool {
	if !isFallback && task.isInThrottlingZone {
		return false
	}

	if task.order.DeliveringRound != 0 || !task.order.CanDefer() {
		return false
	}

	if task.order.Options.MpID != "" {
		return false
	}

	exists, err := task.DeferredOrderRepository.IsUnprocessedExist(ctx, task.order.OrderID)
	if err != nil {
		logx.Error().Str(logutil.OrderID, task.order.OrderID).Msgf("can't find unprocessed deferred order %v", task.order.OrderID)
		return false
	} else if exists {
		return true
	}

	isDeferPossible, deferDurationMinute := canDefer(task.order, task.Config, task.isUsingPredictionService)
	if !isDeferPossible {
		return false
	} else if task.FeatureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsEnabledDeferStandaloneTransitionToSingle.Name) {
		safe.SentryErrorMessage("order will be not deferred, even though order is deferred (from deferred dispatched). since, we already removed defer standalone",
			safe.WithOrderID(task.order.OrderID), safe.WithInfo("region", task.order.DistributeRegions.DefaultRegion().String()))
		task.deferredOrderTransitionToSingleCountMetric.Inc(task.order.DistributeRegions.DefaultRegion().String())
		return false
	}

	if _, err := task.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		if err := task.DeferredOrderRepository.InsertOrder(sessCtx, *task.order, model.DeferredOrderOption{
			Region:                      task.order.DistributeRegions.DefaultRegion(),
			DeferDurationMinute:         deferDurationMinute,
			DrivingDurationConfigMinute: int(task.Config.DeferredDispatchDrivingDuration.Minutes()),
			BufferDurationConfigMinute:  int(task.Config.DeferredDispatchBufferDuration.Minutes()),
		}); err != nil {
			logx.Error().Err(err).Str(logutil.OrderID, task.order.OrderID).Msgf("error inserting order into deferred_orders")
			return nil, err
		}

		expireAt := task.order.ExpireAt
		deferDuration := time.Duration(deferDurationMinute) * time.Minute
		if err := task.OrderRepository.SetDeferredOrder(sessCtx, task.order.OrderID, expireAt.Add(deferDuration), deferDuration); err != nil {
			logx.Error().Err(err).Str(logutil.OrderID, task.order.OrderID).Msgf("error updating order after deferred")
			return nil, err
		}
		return nil, nil
	}, transaction.WithLabel("autoAssignTask.tryDeferredDispatch")); err != nil {
		return false
	}

	task.deferredOrderCountMetric.Inc(task.order.DistributeRegions.DefaultRegion().String())
	logx.Info().Str(logutil.OrderID, task.order.OrderID).Msgf("deferred dispatch: put order %s to defer", task.order.OrderID)
	return true
}

func (task *autoAssignTask) tryToDeferThrottleDispatch(ctx context.Context) bool {
	if !task.isInThrottlingZone || task.throttlingZoneDetail == nil {
		return false
	}

	cfg := task.Config.AtomicAutoAssignDbConfig.Get()
	if cfg.DisableThrottledDispatch || !task.isUsingPredictionService || task.order.Prediction == nil {
		return false
	}

	if !task.order.CanDefer() ||
		!task.Config.DeferredDispatchFeatureEnabled ||
		cfg.DisableDeferredDispatch {
		return false
	}

	if cfg.DisableThrottledDeferred || task.order.IsPreferNotBundled() || task.order.IsNonDalianRush() {
		return task.tryDeferredDispatch(ctx, true)
	}

	detail := task.throttlingZoneDetail
	now := task.Environment.Now()

	var otherMP *model.Order
	if task.order.IsDalianMP && task.order.Options.MpID != "" && task.order.IsHasAnotherMPOrder() {
		ord, err := task.OrderRepository.GetOtherActiveMP(ctx, *task.order)
		if err != nil && !errors.Is(err, repository.ErrNotFound) {
			logx.Error().Str(logutil.OrderID, task.order.OrderID).Msgf("throttled dispatch: unable to get other active mp")
			return false
		}
		if ord != nil {
			otherMP = ord
		}
	}

	expireAt := task.order.CalculateDeferExpiredAt()
	batchTimeoutAt := calculateDeferOrderBatchTimeoutAt(
		task.order,
		cfg.DeferBatchDistributeTimeoutInSeconds,
		cfg.DeferBatchDistributeFallbackBeforeExpireInSeconds,
		cfg.EnableFallbackBeforeExpireByRegion,
		cfg.EligibleRegionsForFallbackBeforeExpire,
	)
	isBatchAssigningBothMP := isBatchAssigningDeferredMP(task.order) && isBatchAssigningDeferredMP(otherMP)

	if isBatchAssigningBothMP {
		otherMPExpiredAt := otherMP.CalculateDeferExpiredAt()
		shouldOverrideExpiredAt := !otherMPExpiredAt.IsZero() && expireAt.Before(otherMPExpiredAt)
		if shouldOverrideExpiredAt {
			expireAt = otherMPExpiredAt
		}

		otherMPTimeoutAt := calculateDeferOrderBatchTimeoutAt(
			otherMP,
			cfg.DeferBatchDistributeTimeoutInSeconds,
			cfg.DeferBatchDistributeFallbackBeforeExpireInSeconds,
			cfg.EnableFallbackBeforeExpireByRegion,
			cfg.EligibleRegionsForFallbackBeforeExpire,
		)
		shouldOverrideBatchTimeout := !otherMPTimeoutAt.IsZero() && batchTimeoutAt.Before(otherMPTimeoutAt)
		if shouldOverrideBatchTimeout {
			batchTimeoutAt = otherMPTimeoutAt
		}
	}

	reachTimeout := !now.Before(batchTimeoutAt)
	if reachTimeout {
		task.batchOrderFallbackCountMetric.Inc(detail.ZoneCode)
		logx.Warn().
			Str(logutil.OrderID, task.order.OrderID).
			Msgf("throttled dispatch: skip throttle defer order delivering_round %v on zone: %v due to exceed defer batch timeout at %v",
				task.order.DeliveringRound, detail.ZoneCode, batchTimeoutAt)
		return false
	}

	var shouldPickupAt time.Time
	var isStaticDefer bool
	if task.order.IsDalianMP {
		shouldPickupAt, isStaticDefer = calculateMPDalianShouldPickupAt(*task.order, otherMP, now, batchTimeoutAt, time.Duration(cfg.RestaurantAcceptDurationInSeconds)*time.Second)
	} else {
		shouldPickupAt = calculateShouldPickupAt(now, *task.order, batchTimeoutAt)
	}

	// Be cautious on adding any queries in this function block!!
	// There is a possibility that SetThrottlingDeferredOrder is executed successfully but not InsertDeferredOrder causing data out-sync
	var deferDuration time.Duration
	if isStaticDefer {
		deferDuration = shouldPickupAt.Sub(task.order.CreatedAt)
	}
	if err := task.OrderRepository.SetThrottlingDeferredOrder(ctx, task.order.OrderID, expireAt, deferDuration); err != nil {
		logx.Error().Err(err).Msgf("error updating throttled deferred order; order: %s; error: %v", task.order.OrderID, err)
		return false
	}

	if task.order.IsDalianMP && task.order.Options.MpID != "" && otherMP != nil {
		return task.insertAndUpdateThrottledDeferredOrderWithOtherMp(ctx, otherMP, detail, shouldPickupAt) == nil
	}

	if err := task.ThrottledOrderRepository.InsertDeferredOrder(ctx, *task.order, detail.ZoneID, shouldPickupAt); err != nil {
		logx.Error().Err(err).Msgf("error inserting defer order into throttled_orders; order: %s; error: %v", task.order.OrderID, err)
		return false
	}

	logx.Info().Str(logutil.OrderID, task.order.OrderID).Msgf("throttled dispatch: order %s delivering_round %v has been throttled deferred in zone %v for round %v", task.order.OrderID, task.order.DeliveringRound, detail.ZoneCode, task.order.ThrottledRound+1)
	return true
}

func (task *autoAssignTask) insertAndUpdateThrottledDeferredOrderWithOtherMp(ctx context.Context, otherMP *model.Order, detail *model.ThrottledDispatchDetailWithZoneCode, shouldPickupAt time.Time) error {
	if task.ThrottledOrderDBConfig.IsUseSecondaryDB {
		ctx = mongotxn.WithSecondaryDB(ctx)
	}

	_, err := task.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		if task.order.IsDalianMP && task.order.IsMp1() && otherMP != nil {
			// wake mp2 up together with mp1 in mp1 zone
			if err := task.ThrottledOrderRepository.UpdateMpShouldPickupAtAndZoneID(sessCtx, task.order.Options.MpID, shouldPickupAt, detail.ZoneID); err != nil {
				logx.Error().Str(logutil.OrderID, task.order.OrderID).Str(logutil.ZoneCode, detail.ZoneCode).Err(err).Msgf("error update mp should pickup at and zone id; order: %s; error: %v", task.order.OrderID, err)
				return nil, err
			}
		} else if task.order.IsDalianMP && task.order.Options.MpID != "" && otherMP != nil {
			// if throttled_orders has the same mp that wasn't processed in throttled then override shouldPickupAt to be the same
			if err := task.ThrottledOrderRepository.UpdateMpShouldPickupAt(sessCtx, task.order.Options.MpID, shouldPickupAt); err != nil {
				logx.Error().Str(logutil.OrderID, task.order.OrderID).Str(logutil.ZoneCode, detail.ZoneCode).Err(err).Msgf("error update mp should pickup at; order: %s; error: %v", task.order.OrderID, err)
				return nil, err
			}
		}
		if err := task.ThrottledOrderRepository.InsertDeferredOrder(sessCtx, *task.order, detail.ZoneID, shouldPickupAt); err != nil {
			logx.Error().Str(logutil.OrderID, task.order.OrderID).Str(logutil.ZoneCode, detail.ZoneCode).Err(err).Msgf("error inserting defer order into throttled_orders; order: %s; error: %v", task.order.OrderID, err)
			return nil, err
		}
		return nil, nil
	}, transaction.WithLabel("autoAssignTask.insertAndUpdateThrottledDeferredOrderWithOtherMp"))
	if err == nil {
		logx.Info().Str(logutil.OrderID, task.order.OrderID).Msgf("throttled dispatch: order delivering_round %d has been throttled deferred in zone %s for round %d", task.order.DeliveringRound, detail.ZoneCode, task.order.ThrottledRound+1)
	}
	return err
}

// isMPEligibleToBundle rough check if the mp order will be eligible to be bundle
func isMPEligibleToBundle(order *model.Order) bool {
	if order == nil {
		return false
	}
	if order.Prediction == nil || order.Options.MpID == "" {
		return false
	}
	if order.Status == model.StatusCompleted || order.Status == model.StatusCanceled || order.Status == model.StatusExpired {
		return false
	}
	return true
}

// isBatchAssigningMP use in throttling manipulation process
func isBatchAssigningMP(order *model.Order) bool {
	return order != nil && order.Prediction != nil && order.IsDalianMP && !order.ProcessedBySingleDistribution && order.Status == model.StatusAssigningDriver
}

func isBatchAssigningDeferredMP(order *model.Order) bool {
	return order != nil && isBatchAssigningMP(order) && order.CanDefer()
}

func calculateOrderBatchTimeoutAt(
	order *model.Order,
	timeoutDuration float64,
	fallbackBeforeExpireDuration float64,
	enableFallbackBeforeExpireByRegion bool,
	fallbackBeforeExpireEligibleRegions types.StringSet,
) (time.Time, bool) {
	if order == nil {
		return time.Time{}, false
	}

	assignedAt, ok := order.History[string(model.StatusAssigningDriver)]
	if !ok || assignedAt.IsZero() {
		return order.CreatedAt, false
	}

	batchTimeoutAt := assignedAt.Add(time.Duration(timeoutDuration) * time.Second)

	isInEligibleRegion := !fallbackBeforeExpireEligibleRegions.IsInitialized() || fallbackBeforeExpireEligibleRegions.Has(order.Region.String())

	if fallbackBeforeExpireDuration != 0 && (!enableFallbackBeforeExpireByRegion || isInEligibleRegion) { // intended to use only when transitioning from old logic TODO remove this
		batchTimeoutAt = order.ExpireAt.Add(-time.Duration(fallbackBeforeExpireDuration) * time.Second)
	}

	return batchTimeoutAt, true
}

func calculateDeferOrderBatchTimeoutAt(
	order *model.Order,
	timeoutDuration float64,
	fallbackBeforeExpireDuration float64,
	enableFallbackBeforeExpireByRegion bool,
	fallbackBeforeExpireEligibleRegions types.StringSet,
) time.Time {
	if order == nil {
		return time.Time{}
	}
	if order.Prediction == nil {
		return order.CreatedAt
	}

	isInEligibleRegion := !fallbackBeforeExpireEligibleRegions.IsInitialized() || fallbackBeforeExpireEligibleRegions.Has(order.Region.String())

	if fallbackBeforeExpireDuration == 0 || (enableFallbackBeforeExpireByRegion && !isInEligibleRegion) { // intended to use only when transitioning from old logic TODO remove this
		// timeout = order.created_at + predict cooking time + throttling defer timeout config
		predictCookingDuration := order.ModifiedPredictedCookingTime()
		timeoutConfigDuration := time.Duration(timeoutDuration) * time.Second
		batchTimeoutAt := order.CreatedAt.Add(predictCookingDuration - timeoutConfigDuration)
		return batchTimeoutAt
	}
	return order.CalculateDeferExpiredAtNoExtend().
		Add(-time.Duration(fallbackBeforeExpireDuration) * time.Second)
}

func calculateShouldPickupAt(now time.Time, order model.Order, batchTimeoutAt time.Time) time.Time {
	var shouldPickupAt time.Time
	if order.Prediction.DeferUntil != nil && now.Before(*order.Prediction.DeferUntil) {
		shouldPickupAt = *order.Prediction.DeferUntil
	} else {
		shouldPickupAt = now
	}
	return timeutil.MinTime(batchTimeoutAt, shouldPickupAt)
}

func calculateMPDalianShouldPickupAt(order model.Order, otherMP *model.Order, now time.Time, batchTimeoutAt time.Time, restaurantAcceptDuration time.Duration) (time.Time, bool) {
	defaultShouldPickupAt := calculateShouldPickupAt(now, order, batchTimeoutAt)
	if !order.IsDalianMP {
		return defaultShouldPickupAt, false
	}

	isFirstThrottle := otherMP == nil && order.ThrottledRound == 0

	var mpShouldPickupAt time.Time
	var shouldStaticDeferMP bool
	if isFirstThrottle && !order.HasReassigned() { // do static defer only the first round of first MP
		mpShouldPickupAt = now
		mpDeferUntil := order.MpDeferUntilWithUserPreference()
		if mpDeferUntil != nil && mpDeferUntil.After(mpShouldPickupAt) {
			mpShouldPickupAt = *mpDeferUntil
			shouldStaticDeferMP = true
		}
		restaurantAcceptUntil := now.Add(restaurantAcceptDuration)
		if order.Options.IsMpAlongTheRoute && len(order.Options.MpOrderIDs) != 0 && restaurantAcceptUntil.After(mpShouldPickupAt) {
			// mp2 should wait for mp1 for max(mp defer until, restaurant accept until) to prevent driver from taking a detour
			mpShouldPickupAt = restaurantAcceptUntil
			shouldStaticDeferMP = true
		}
	} else {
		mpShouldPickupAt = timeutil.MinTime(batchTimeoutAt, calculateSyncedShouldPickupAt(now, order, otherMP))
	}

	if mpShouldPickupAt.IsZero() {
		mpShouldPickupAt = defaultShouldPickupAt
	}

	return mpShouldPickupAt, shouldStaticDeferMP
}

// calculateSyncedShouldPickupAt return the time to sync other mp if it can, or else return zero
func calculateSyncedShouldPickupAt(now time.Time, order model.Order, otherActiveMP *model.Order) time.Time {
	if !order.IsDalianMP || order.Options.MpID == "" {
		return time.Time{}
	}
	if otherActiveMP == nil {
		return time.Time{}
	}

	isOtherMPMatched := otherActiveMP.Driver != ""
	if isOtherMPMatched && isMPEligibleToBundle(otherActiveMP) { // other mp was matched
		return now
	} else if isBatchAssigningMP(otherActiveMP) { // able to sync batching round
		alreadySynced := order.Prediction.MpGroupDeferUntil != nil && otherActiveMP.Prediction.MpGroupDeferUntil != nil && order.Prediction.MpGroupDeferUntil.Equal(*otherActiveMP.Prediction.MpGroupDeferUntil)
		if alreadySynced && now.Before(*order.Prediction.MpGroupDeferUntil) {
			return *order.Prediction.MpGroupDeferUntil
		} else {
			return now
		}
	}

	return time.Time{}
}

func (task *autoAssignTask) isOrderFallbackFromBatchOptimize() bool {
	if task.order == nil || !task.order.IsThrottled {
		return false
	}

	cfg := task.Config.AtomicAutoAssignDbConfig.Get()

	var timeout time.Time
	if task.order.IsDeferred {
		timeout = calculateDeferOrderBatchTimeoutAt(
			task.order,
			cfg.DeferBatchDistributeTimeoutInSeconds,
			cfg.DeferBatchDistributeFallbackBeforeExpireInSeconds,
			cfg.EnableFallbackBeforeExpireByRegion,
			cfg.EligibleRegionsForFallbackBeforeExpire,
		)
	} else {
		timeout, _ = calculateOrderBatchTimeoutAt(
			task.order,
			cfg.BatchDistributeTimeoutInSeconds,
			cfg.BatchDistributeFallbackBeforeExpireInSeconds,
			cfg.EnableFallbackBeforeExpireByRegion,
			cfg.EligibleRegionsForFallbackBeforeExpire,
		)
	}

	return !timeout.IsZero() && !task.Environment.Now().Before(timeout)
}

func (task *autoAssignTask) tryToThrottleDispatch(ctx context.Context, distributionTime time.Time, opt model.AssignmentLogOpt) bool {
	if !task.isInThrottlingZone || task.throttlingZoneDetail == nil {
		return false
	}

	cfg := task.Config.AtomicAutoAssignDbConfig.Get()
	if cfg.DisableThrottledDispatch ||
		task.order.IsPreferNotBundled() ||
		task.order.IsNonDalianRush() ||
		task.order.IsLockRequired() ||
		!task.isUsingPredictionService ||
		(task.order.ServiceType != model.ServiceFood && task.order.ServiceType != model.ServiceMart) ||
		task.order.Prediction == nil {
		return false
	}

	if task.order.IsDeferred ||
		task.order.CanDefer() {
		return false
	}

	detail := task.throttlingZoneDetail

	batchTimeoutAt, ok := calculateOrderBatchTimeoutAt(
		task.order,
		cfg.BatchDistributeTimeoutInSeconds,
		cfg.BatchDistributeFallbackBeforeExpireInSeconds,
		cfg.EnableFallbackBeforeExpireByRegion,
		cfg.EligibleRegionsForFallbackBeforeExpire,
	)
	if !ok {
		return false
	}

	if !task.Environment.Now().Before(batchTimeoutAt) {
		task.batchOrderFallbackCountMetric.Inc(detail.ZoneCode)
		logx.Warn().Str(logutil.OrderID, task.order.OrderID).
			Str(logutil.ZoneCode, detail.ZoneCode).
			Msgf("throttled dispatch: skip to throttle order %v delivering_round %v on zone: %v due to exceed batch timeout at %v",
				task.order.OrderID, task.order.DeliveringRound, detail.ZoneCode, batchTimeoutAt)
		return false
	}

	task.order.RedistributionState = &model.RedistributionState{
		IsUsingPredictionService:      task.isUsingPredictionService,
		CurrentRound:                  task.currentRound,
		RedistributionCount:           0,
		LastDistributeAt:              distributionTime,
		Opt:                           opt,
		ConsecutiveRoundsWithNoRiders: 0,
		CurrentDistributionLogic:      model.RedistributionStateDistributionLogicBatch,
	}

	// Be cautious on adding any queries in this function block!!
	// There is a possibility that SetThrottledOrder is executed successfully but not InsertOrder causing data out-sync
	if err := task.OrderRepository.SetThrottledOrder(ctx, task.order.OrderID, detail.ZoneID.Hex(), task.order.RedistributionState); err != nil {
		logx.Error().Err(err).Str(logutil.OrderID, task.order.OrderID).Msgf("error updating throttled orders; order: %s; error: %v", task.order.OrderID, err)
		return false
	}

	if task.order.Options.MpID != "" && task.order.IsHasAnotherMPOrder() {
		return task.insertAndUpdateThrottledOrderWithOtherMp(ctx, detail) == nil
	}

	shouldPickupAt := task.Environment.Now()
	if err := task.ThrottledOrderRepository.InsertOrder(ctx, *task.order, detail.ZoneID, shouldPickupAt); err != nil {
		logx.Error().Err(err).Msgf("error inserting order into throttled_orders; order: %s; error: %v", task.order.OrderID, err)
		return false
	}
	logx.Info().Str(logutil.OrderID, task.order.OrderID).Msgf("throttled dispatch: order %s delivering_round %v has been throttled in zone %v for round %v", task.order.OrderID, task.order.DeliveringRound, detail.ZoneCode, task.order.ThrottledRound+1)

	return true
}

func (task *autoAssignTask) insertAndUpdateThrottledOrderWithOtherMp(ctx context.Context, detail *model.ThrottledDispatchDetailWithZoneCode) error {
	if task.ThrottledOrderDBConfig.IsUseSecondaryDB {
		ctx = mongotxn.WithSecondaryDB(ctx)
	}

	_, err := task.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		shouldPickupAt := task.Environment.Now()
		if task.order.Options.MpID != "" && task.order.IsHasAnotherMPOrder() {
			if err := task.ThrottledOrderRepository.UpdateMpShouldPickupAt(sessCtx, task.order.Options.MpID, shouldPickupAt); err != nil {
				logx.Error().Str(logutil.OrderID, task.order.OrderID).Str(logutil.ZoneCode, detail.ZoneCode).Err(err).Msgf("error update mp should pickup at; order: %s; error: %v", task.order.OrderID, err)
				return nil, err
			}
		}

		if err := task.ThrottledOrderRepository.InsertOrder(sessCtx, *task.order, detail.ZoneID, shouldPickupAt); err != nil {
			logx.Error().Str(logutil.OrderID, task.order.OrderID).Str(logutil.ZoneCode, detail.ZoneCode).Err(err).Msgf("error inserting order into throttled_orders; order: %s; error: %v", task.order.OrderID, err)
			return nil, err
		}
		return nil, nil
	}, transaction.WithLabel("autoAssignTask.insertAndUpdateThrottledOrderWithOtherMp"))
	if err == nil {
		logx.Info().Str(logutil.OrderID, task.order.OrderID).Msgf("throttled dispatch: order delivering_round %d has been throttled in zone %s for round %d", task.order.DeliveringRound, detail.ZoneCode, task.order.ThrottledRound+1)
	}
	return err
}

func (task *autoAssignTask) predictOrderCookingAndWaitingTime(ctx context.Context) {
	setting := service.NewPredictSetting(
		task.modelVersionWithSwitchbackExperiments(model.PredictModel),
		task.Config.SmartDistributionGoodnessBiasLevel,
	)
	if err := task.PredictionService.Predict(ctx, task.order, setting); err != nil {
		task.isUsingPredictionService = false
		return
	}

	if err := task.OrderRepository.SetPrediction(ctx, task.order.OrderID, *task.order.Prediction); err != nil {
		task.isUsingPredictionService = false
		logx.Error().Str(logutil.OrderID, task.order.OrderID).Err(err).Msgf("autoassign: orderID=%s, cannot update order prediction: %v", task.order.OrderID, err)
	}
}

func (task *autoAssignTask) shouldNotifyNextDriver() bool {
	return true
}

// CAUTION: The function is used concurrently, beware of mutating shared variables.
func calcScoreThenSort(
	ctx context.Context,
	scorers []scorer,
	order *model.Order,
	driverWithLocations []service.DriverWithLocation,
	allowQueueing bool,
	shouldDebugScorer bool,
	isForSaversExperiment bool,
) ([]driverAndScore, *model.RidersFilterData) {
	filterData := model.NewRiderFilterData()
	driverAndScores := createDriverAndScores(driverWithLocations)

	collector, m := metric.GetCollector(), metric.NewAPILatency("autoassign_driverscore")
	defer func() {
		if err := collector.Save(m); err != nil {
			logx.Warn().Err(err).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: cannot collect autoassign_driverscore metric: %v", err)
		}
	}()

	for _, scorer := range scorers {
		if len(driverAndScores) == 0 {
			break
		}
		if scorer.Name() == "status" && allowQueueing {
			continue
		}
		resultDriverAndScores := make([]driverAndScore, 0, len(driverAndScores))
		scorer.Init(ctx, driverAndScores)
		for _, driverAndScore := range driverAndScores {
			scored, filterName := driverAndScore.Add(order, scorer)
			if !scored {
				filterData.Add(filterName, driverAndScore.DriverWithLocation.Driver.DriverID)
				continue
			}
			resultDriverAndScores = append(resultDriverAndScores, driverAndScore)
		}
		driverAndScores = resultDriverAndScores
		m.Lap(scorer.Name())
	}
	sort.Sort(driverAndScoreByTotalScore(driverAndScores))
	m.Lap("sort")

	if shouldDebugScorer && !isForSaversExperiment {
		sortedDrivers := ""
		for i, sd := range driverAndScores {
			if i >= 20 {
				break
			}

			fmtSd := fmt.Sprintf("%s %v %v", sd.DriverWithLocation.Driver.DriverID, sd.TotalScore, sd.Scores)

			sortedDrivers = sortedDrivers + fmtSd + ", "
		}

		logx.Info().Str(logutil.OrderID, order.OrderID).Str(logutil.DriverIDs, sortedDrivers).Msgf("autoassign: orderID=%s sortedDrivers=[%s] sorted drivers...", order.OrderID, sortedDrivers)
	}

	return driverAndScores, filterData
}

func createDriverAndScores(driverWithLocations []service.DriverWithLocation) []driverAndScore {
	driverAndScores := make([]driverAndScore, len(driverWithLocations))

	for i := range driverWithLocations {
		dl := &driverWithLocations[i]
		driverAndScores[i] = driverAndScore{DriverWithLocation: dl}
	}

	return driverAndScores
}

func (task *autoAssignTask) CheckIfCandidatesEnough(ctx context.Context, candidatesRequired int, opt model.AssignmentLogOpt) bool {
	if candidatesRequired <= 0 {
		return true
	}

	task.prepare(ctx, &opt)

	drivers, err := task.searchDrivers(ctx, task.businessLocation)
	// if the error is not from prediction service, end the process
	if err != nil && !errors.Is(err, ErrPredictionService) {
		logx.Error().Str(logutil.OrderID, task.order.OrderID).Msgf("autoassign check if candidates enough: orderID=%v cannot get drivers: %v", task.order.OrderID, err)
		return false
	}

	var passedRequirementDrivers []service.DriverWithLocation
	for _, driver := range drivers.Results {
		if passed, _, msg := task.checkRequirement(ctx, &driver.Driver, AnyAssignmentType, opt, task.order, task.isUsingPredictionService); !passed {
			logx.Info().Str(logutil.OrderID, task.order.OrderID).Str(logutil.DriverID, driver.Driver.DriverID).Str("disqualifications", msg).Msgf("autoassign check if candidates enough: orderID=%s driverID=%s, the driver does not match the criteria, description=%s", task.order.OrderID, driver.Driver.DriverID, msg)
			continue
		}
		passedRequirementDrivers = append(passedRequirementDrivers, driver)
	}

	return len(passedRequirementDrivers) >= candidatesRequired
}

// validateIfAssignableAndRecord will be redundant in this transition phase
// please also consider to make the changes in assigner package
func (task *autoAssignTask) validateIfAssignableAndRecord(_ context.Context, order model.Order, currentRound int) error {
	if order.Status != model.StatusAssigningDriver {
		task.recordStopRound("FAIL_ORDER_"+string(order.Status), currentRound)
		return fmt.Errorf("autoassign: order %s status is not ASSIGNING_DRIVER but %v, stop assigning", order.OrderID, order.Status)
	}

	if order.Expired() {
		task.recordStopRound("FAIL_ORDER_EXPIRED", currentRound)
		return fmt.Errorf("autoassign: order %s status is going to be EXPIRED, stop assigning", order.OrderID)
	}

	return nil
}

func (task *autoAssignTask) lockAssigningOrder(ctx context.Context, orderID string, ttl time.Duration) bool {
	ok := task.Locker.SetState(
		ctx,
		locker.AssigningOrderState(orderID),
		"auto-assign",
		ttl,
	)
	if !ok {
		logx.Warn().Str(logutil.OrderID, orderID).Msgf("auto_assign: [notifyBestDriverInBackground] orderID=%s order is being assigned in another process", orderID)
		return false
	}
	return true
}

func (task *autoAssignTask) unlockAssigningOrder(ctx context.Context, orderID string) {
	task.Locker.RemoveState(ctx, locker.AssigningOrderState(orderID))
}

// notifyBestDriverInBackground is the wrapped notifyBestDriver
// and will be moved into the new Assign API
func (task *autoAssignTask) notifyBestDriverInBackground(
	ctx context.Context,
	driverQueue *DriverQueue,
	opt model.AssignmentLogOpt,
	order *model.Order,
	assigningSetting AssigningSettingState,
) {
	bgCtx := task.AutoAssignOrderDistributorDeps.WorkerContext.NewContextWithSameWaitGroup(ctx)
	orderID := order.OrderID

	task.Environment.RunGoWithCtx(bgCtx, func() {
		defer task.AutoAssignOrderDistributorDeps.WorkerContext.ContextDone(bgCtx)
		defer task.stopHeartbeat(bgCtx, orderID)

		lockedOrderIDs := types.NewStringSet()
		if !task.enabledAssignAPI {
			ttl := time.Duration(task.acceptDurationWithBufferInSeconds()*int64(driverQueue.Size())) * time.Second
			if ok := task.lockAssigningOrder(bgCtx, orderID, ttl); !ok {
				return
			}
			lockedOrderIDs.Add(orderID)
			defer func() {
				if lockedOrderIDs.Has(orderID) {
					task.unlockAssigningOrder(bgCtx, orderID)
				}
			}()
		}

		done, ridersTriedAssigning, err := task.notifyBestDriver(bgCtx, driverQueue, opt, order, assigningSetting)
		// no need to unlock since driver is automatically unlocked after accepting mp successfully
		if !task.enabledAssignAPI && opt.IsMultiplePickup && !(done && err == nil) {
			orderToBundleID := order.Options.MpOrderIDs[0]
			orderToBundleWith, err := task.OrderRepository.Get(bgCtx, orderToBundleID)
			if err != nil {
				logx.Error().Err(err).Str(logutil.OrderID, order.OrderID).Msgf("autoassign mp: orderID=%s, get order failed, err=%v", orderToBundleID, err)
				return
			}
			if unlockErr := task.DriverRepository.UnlockForQueueing(bgCtx, orderToBundleWith.Driver); unlockErr != nil {
				logx.Error().Err(err).Str(logutil.OrderID, order.OrderID).Msgf("autoassign mp: unable to unlock driver %v for queueing, err=%v", orderToBundleWith.Driver, unlockErr)
			}
		}
		if err != nil {
			logx.Error().Err(err).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: orderID=%v stop distribution process, err=%v", orderID, err)
			return
		}
		if done || task.distributionType != AutoAssignDistribution {
			return
		}

		if !task.enabledAssignAPI {
			task.unlockAssigningOrder(bgCtx, orderID)
			lockedOrderIDs.Remove(orderID)
		}
		_ = task.redistribute(bgCtx, order.OrderID, &ridersTriedAssigning)
	})
	return
}

type AssigningSettingState struct {
	DistCfgRevision          string                        `json:"distCfgRevision"`
	CurrentRound             int                           `json:"currentRound"`
	RidersTriedAssigning     int                           `json:"ridersTriedAssigning"`
	IsUsingPredictionService bool                          `json:"isUsingPredictionService"`
	SwitchbackExperiments    *model.SwitchbackExperiments  `json:"switchbackExperiments,omitempty"`
	PredictionModelVersions  model.PredictionModelVersions `json:"predictionModelVersions"`
	UseBikePriority          bool                          `json:"useBikePriority"`
	SearchRadiusByDistrict   float64                       `json:"searchRadiusByDistrict"`
	DistributionLogMetadata  model.DistributionLogMetadata `json:"distributionLogMetadata"`
}

// notifyBestDriver assigns an order to the best driver. The return parameters mean the following:
// false, nil means the order can still be redistributed
// true, nil means the order has been successfully assigned
// true, err means the order cannot be redistributed for some reasons
func (task *autoAssignTask) notifyBestDriver(
	ctx context.Context,
	drivers *DriverQueue,
	opt model.AssignmentLogOpt,
	order *model.Order,
	assigningSetting AssigningSettingState,
) (bool, int, error) {
	if drivers == nil || !drivers.HasNext() {
		task.recordStopRound("FAIL_NO_DRIVER", assigningSetting.CurrentRound)
		logx.Info().Str(logutil.OrderID, order.OrderID).Msgf("autoassign: orderID=%s no drivers", order.OrderID)
		return false, 0, nil
	}

	notifyOpts := []service.NotifyOption{service.WithFirebase}
	if task.notifyViaSocketIO {
		notifyOpts = append(notifyOpts, service.WithSocketIO)
	}

	ridersTriedAssigning := 0
	for assigningSetting.CurrentRound += 1; true; assigningSetting.CurrentRound++ {
		dl, assignmentType, planRoute := task.nextDriver(ctx, drivers, opt, order, assigningSetting, task.enabledAssignAPI)

		if task.enabledAssignAPI {
			if dl == nil {
				logx.Error().Str(logutil.OrderID, order.OrderID).Msgf("autoassign: orderID=%s round=%v no matched driver", order.OrderID, assigningSetting.CurrentRound)
				break
			}
			ttl := time.Duration(task.Config.AtomicAutoAssignDbConfig.Get().AssigningStateTTLInSecond) * time.Second
			if err := task.AssigningStateManager.SetAssigningState(ctx, NewAssigningState(drivers.Compact().ToMinimal(), assigningSetting), order.OrderID, order.DeliveringRound, ttl); err != nil {
				logx.Error().Err(err).Str(logutil.OrderID, order.OrderID).Msgf("autoassign: orderID=%s on setAssigningState", order.OrderID)
			}

			_, err := task.FleetOrderClient.AssignOrder(ctx, fleetorder.AssignOrderRequest{
				DriverID:                 dl.Driver.DriverID,
				LastPredictionDisruption: dl.Driver.LastPredictionDisruption,
				OrderIDs:                 []string{order.OrderID},
				PlanRoute:                fleetorder.PlanRouteToRequest(planRoute.RouteResponse),
				Region:                   order.DistributeRegions.DefaultRegion().String(),
				DistributionStartedAt:    task.distributionStartedAt,
				AssignmentLogOpts: fleetorder.AssignmentLogOpts{
					IsUsingPredictionService: assigningSetting.IsUsingPredictionService,
					IsMultiplePickup:         opt.IsMultiplePickup,
					LockDuration:             opt.LockDuration,
					SearchRiderStrategy:      opt.SearchRiderStrategy.String(),
					SwitchbackExperiments:    assigningSetting.SwitchbackExperiments,
					PredictionModelVersions:  assigningSetting.PredictionModelVersions,
					UseBikePriority:          assigningSetting.UseBikePriority,
					IsPrioritized:            dl.IsPrioritized,
					IsDedicatedZone:          dl.IsDedicatedZone,
					SearchRadiusByDistrict:   assigningSetting.SearchRadiusByDistrict,
				},
				DistributionLogMetadata: fleetorder.ToDistributionLogMetadataReq(task.distributionLogMetadata),
				AssignmentConstraint: fleetorder.AssignmentConstraintReq{
					AssignmentType:     string(assignmentType),
					B2BDistanceSetting: task.Config.MaxRadiusInMeterByServices(task.searchRadiusByDistrict),
				},
				RidersTriedAssigning: assigningSetting.RidersTriedAssigning,
			})
			if err != nil {
				logx.Error().Str(logutil.OrderID, order.OrderID).Msgf("autoassign: orderID=%s round=%v no matched driver", order.OrderID, assigningSetting.CurrentRound)
				continue
			}

			return true, assigningSetting.RidersTriedAssigning, nil
		}

		if dl == nil {
			logx.Error().Str(logutil.OrderID, order.OrderID).Msgf("autoassign: orderID=%s round=%v no matched driver", order.OrderID, assigningSetting.CurrentRound)
			break
		}
		ridersTriedAssigning += 1
		isBatchAssignmentEnabled := task.batchAssignmentEnabled()
		assigner := task.newOrderAssigner(assigningSetting.IsUsingPredictionService)
		assignmentResult := assigner.AssignDriver(
			ctx,
			dl,
			planRoute,
			&opt,
			notifyOpts,
			[]**model.Order{&order},
			[]string{order.OrderID},
			map[string]int{order.OrderID: assigningSetting.CurrentRound},
			isBatchAssignmentEnabled,
			map[string]float64{order.OrderID: dl.DistanceMeter},
			opt.SearchRiderStrategy,
			nil,
		)
		if len(assignmentResult.RedistributableOrders) == 0 {
			if len(assignmentResult.SuccessfullyAssignedOrders) == 1 {
				task.Locker.RemoveState(ctx, locker.DriverAutoAssignState(dl.Driver.DriverID))
				return true, ridersTriedAssigning, nil
			}
			return true, ridersTriedAssigning, ErrOrderCantBeRedistributed
		}

		if assignmentResult.IsRedistributionRequired {
			return false, ridersTriedAssigning, nil
		}

		if task.WorkerContext.Done() {
			logx.Info().Str(logutil.OrderID, task.order.OrderID).Msgf("notifyBestDriver: orderID=%s, the process stopped working due to receiving a SIGKILL signal", task.order.OrderID)
			break
		}
	}

	task.recordStopRound("FAIL_NO_DRIVER_MATCHED", assigningSetting.CurrentRound)

	return false, ridersTriedAssigning, nil
}

func (task *autoAssignTask) validateIfDriverEligibleForTakingMp(ctx context.Context, driv model.DriverMinimal, planRoute model.PlanRoute, assignedTrips []model.Trip, allOrders []model.Order) error {
	allOrdersMap := make(map[string]model.Order)
	for _, o := range allOrders {
		allOrdersMap[o.OrderID] = o
	}

	for _, mpOrder := range task.order.Options.MpOrderIDs {
		if _, ok := allOrdersMap[mpOrder]; !ok {
			return fmt.Errorf("autoassign mp: driver %s is ineligible for taking mp order since he/she is not holding mp order", driv.DriverID)
		}
	}

	dalianTrips := model.NewDalianTrips(planRoute.RouteResponse.PlanRoutes)
	reduced, err := task.PredictionService.IsTripDistanceReduced(ctx, []string{task.order.OrderID}, assignedTrips, dalianTrips, allOrdersMap)
	if err != nil {
		orderIDs := make([]string, 0, len(allOrders))
		for _, o := range allOrders {
			orderIDs = append(orderIDs, o.OrderID)
		}
		logx.Warn().Err(err).Interface(logutil.OrderIDs, orderIDs).Str(logutil.BundleOrderID, task.order.OrderID).Str(logutil.DriverID, driv.DriverID).Msgf("autoassign mp: driver %s, skip validate total trip distance due to err: %v, assigned trip: %v, dalian trips %v", driv.DriverID, err.Error(), assignedTrips, dalianTrips)
	} else if reduced {
		return fmt.Errorf("autoassign mp: driver %s is ineligible for taking mp order since total trip distance will be reduced", driv.DriverID)
	}

	return nil
}

func (task *autoAssignTask) nextDriver(ctx context.Context, drivers *DriverQueue, opt model.AssignmentLogOpt, order *model.Order, assigningSetting AssigningSettingState, enabledAssignAPI bool) (*service.DriverWithLocation, AssignmentType, model.PlanRoute) {
	if drivers == nil || !drivers.HasNext() {
		return nil, AnyAssignmentType, model.PlanRoute{}
	}

	filterData := model.NewRiderFilterData()
	defer task.DistributionLogManager.CapturePostFilterEvent(ctx, filterData, assigningSetting.DistributionLogMetadata)

	notifiedSet := types.NewStringSet()
	if !task.enabledAssignAPI {
		notifiedSet = task.newNotifiedSet(ctx, order.OrderID)
	}
	for drivers.HasNext() {
		d, assignmentType := drivers.Next()
		if d == nil {
			break
		}
		if task.enabledAssignAPI {
			if !task.ensureIdle(ctx, &d.Driver, order.OrderID, enabledAssignAPI) {
				filterData.Add(model.DriverIsBeingAssignedAnotherOrder, d.Driver.DriverID)
				continue
			}

			var planRoute model.PlanRoute
			// when the record has option multiple pickup, we try to generate plan route using the options mp order ids
			if opt.IsMultiplePickup {
				generatedPlanRoute, err := task.ValidateAndGenerateMultiplePickupPlanRoute(ctx, d.Driver, order)
				if err != nil {
					logx.Error().Str(logutil.OrderID, order.OrderID).Str(logutil.DriverID, d.Driver.DriverID).Err(err).Msgf("autoassign: validate and generate MP plan-route error; %v", err)
					filterData.Add(model.RiderIneligibleForAssigningMp2, d.Driver.DriverID)
					continue
				}
				planRoute = generatedPlanRoute
			} else if assigningSetting.IsUsingPredictionService {
				res, routeFilterData, err := task.PredictionService.Route(ctx, []model.Order{*order}, *d, task.routeSetting(nil, task.Config.MaxRadiusInMeterByServices(task.searchRadiusByDistrict), map[string]*string{task.order.OrderID: d.TripID}))
				if err != nil {
					logx.Error().Str(logutil.OrderID, order.OrderID).Str(logutil.DriverID, d.Driver.DriverID).Err(err).Msgf("autoassign: orderID=%s driverID=%s, skip assigning this driver because route fail; error: %v", order.OrderID, d.Driver.DriverID, err)
					filterData.AddExisting(routeFilterData)
					continue
				}
				planRoute = model.PlanRoute{RouteResponse: res}

				return d, assignmentType, planRoute
			}

			return d, assignmentType, planRoute
		}

		if notifiedSet.Has(d.Driver.DriverID) {
			filterData.Add(model.WithoutNotifiedRiders, d.Driver.DriverID)
			continue
		}
		if !task.ensureIdle(ctx, &d.Driver, order.OrderID, enabledAssignAPI) {
			filterData.Add(model.DriverIsBeingAssignedAnotherOrder, d.Driver.DriverID)
			continue
		}
		if passed, filterName, msg := task.checkRequirement(ctx, &d.Driver, assignmentType, opt, order, assigningSetting.IsUsingPredictionService); !passed {
			task.clearWaitForAcceptingState(ctx, &d.Driver)
			filterData.Add(filterName, d.Driver.DriverID)
			logx.Info().Str(logutil.OrderID, task.order.OrderID).Str(logutil.DriverID, d.Driver.DriverID).Msgf("autoassign: orderID=%s driverID=%s, the driver does not match the criteria, description=%s", task.order.OrderID, d.Driver.DriverID, msg)
			continue
		}

		var planRoute model.PlanRoute
		// when the record has option multiple pickup, we try to generate plan route using the options mp order ids
		if opt.IsMultiplePickup {
			generatedPlanRoute, err := task.ValidateAndGenerateMultiplePickupPlanRoute(ctx, d.Driver, order)
			if err != nil {
				logx.Error().Str(logutil.OrderID, task.order.OrderID).Str(logutil.DriverID, d.Driver.DriverID).Err(err).Msgf("autoassign: validate and generate MP plan-route error; %v", err)
				task.clearWaitForAcceptingState(ctx, &d.Driver)
				filterData.Add(model.RiderIneligibleForAssigningMp2, d.Driver.DriverID)
				continue
			}
			planRoute = generatedPlanRoute
		} else if task.isUsingPredictionService {
			var validateErrRes service.ValidateRouteErrRes
			res, routeFilterData, err := task.PredictionService.Route(ctx, []model.Order{*task.order}, *d, task.routeSetting(nil, task.Config.MaxRadiusInMeterByServices(task.searchRadiusByDistrict), map[string]*string{task.order.OrderID: d.TripID}))
			if err == nil {
				validateErrRes, err = task.PredictionService.ValidatePlanRoute(ctx, res, []string{task.order.OrderID}, *d, task.validatePlanRouteSetting(nil, task.Config.MaxRadiusInMeterByServices(task.searchRadiusByDistrict)))
			}
			if err != nil {
				logx.Error().Str(logutil.OrderID, task.order.OrderID).Str(logutil.DriverID, d.Driver.DriverID).Err(err).Msgf("autoassign: orderID=%s driverID=%s, skip assigning this driver because route fail; error: %v", task.order.OrderID, d.Driver.DriverID, err)
				task.clearWaitForAcceptingState(ctx, &d.Driver)
				filterData.AddExisting(routeFilterData)
				if validateErrRes.Reason != "" {
					filterData.Add(validateErrRes.Reason, d.Driver.DriverID)
				}
				continue
			}
			planRoute = model.PlanRoute{RouteResponse: res}
		}

		if assignmentType == MOAssignmentType && !service.IsMO(planRoute.RouteResponse, order.OrderID) {
			task.clearWaitForAcceptingState(ctx, &d.Driver)
			filterData.Add(model.PlanRoutesUnmatchedWithAssignmentTypeMO, d.Driver.DriverID)
			logx.Info().Str(logutil.OrderID, order.OrderID).Str(logutil.DriverID, d.Driver.DriverID).Msgf("autoassign: orderID=%s driverID=%s, the driver does not match the assignment type: %s", order.OrderID, d.Driver.DriverID, assignmentType)
			continue
		}

		return d, assignmentType, planRoute
	}
	return nil, AnyAssignmentType, model.PlanRoute{}
}

func (task *autoAssignTask) ValidateAndGenerateMultiplePickupPlanRoute(ctx context.Context, d model.DriverMinimal, order *model.Order) (model.PlanRoute, error) {
	if d.TemporaryBanHistory != nil || d.BanLater {
		return model.PlanRoute{}, fmt.Errorf("autoassign mp: driver %s is ineligible for taking mp order since he/she will be banned later", d.DriverID)
	}

	if d.CurrentTrip == "" {
		return model.PlanRoute{}, fmt.Errorf("autoassign mp: driver %s is ineligible for taking mp order since he/she does not have current trip", d.DriverID)
	}

	if task.Config.MaxOrdersPerRider < 3 && len(d.QueueingTrips) != 0 {
		return model.PlanRoute{}, fmt.Errorf("autoassign mp: driver %s is ineligible for taking mp order since he/she has queueing trip", d.DriverID)
	}

	if len(d.QueueingTrips) >= 2 {
		return model.PlanRoute{}, fmt.Errorf("autoassign mp: driver %s is ineligible for taking mp order since he/she already holds 2 queueing orders", d.DriverID)
	}

	// Finding driver's trips and orders
	var opts []model.NewMultiplePickupPlanRouteOpt
	tripIDs := d.AssignedTripIDs()
	trips, err := task.TripRepository.GetMany(ctx, tripIDs)
	if err != nil {
		return model.PlanRoute{}, fmt.Errorf("autoassign: orderID=%s driverID=%s, cannot find trips for mp validation, error=%v", order.OrderID, d.DriverID, err)
	}
	tripsMap := make(map[string]model.Trip)
	allOrderIDs := []string{order.OrderID}
	for _, trip := range trips {
		tripsMap[trip.TripID] = trip
		allOrderIDs = append(allOrderIDs, trip.GetOngoingOrderIDs()...)
	}
	allOrders, err := task.OrderRepository.GetMany(ctx, allOrderIDs)
	if err != nil {
		return model.PlanRoute{}, fmt.Errorf("autoassign: orderID=%s driverID=%s, cannot find orders for mp validation, error=%v", order.OrderID, d.DriverID, err)
	}

	// To check if plan route need to have additional b2b order.
	if task.Config.MaxOrdersPerRider > 2 {
		for _, trip := range trips {
			tripOrdersSet := types.NewStringSet(trip.GetOngoingOrderIDs()...)
			if tripOrdersSet.HasAll(order.Options.MpOrderIDs...) {
				// To prevent MP1 already Bundle with random order, Due to Lock expire.
				if len(trip.GetOngoingOrderIDs()) > 1 {
					return model.PlanRoute{}, fmt.Errorf("autoassign mp: driver %s is ineligible for taking mp order since driver already have random order bundled with mp", d.DriverID)
				}

				// To check if MP order is in current trip or queuing trip.
				if trip.TripID == d.CurrentTrip {
					queueingTrips := make([]model.Trip, 0)
					for _, qTripID := range d.QueueingTrips {
						qTrip := tripsMap[qTripID]
						queueingTrips = append(queueingTrips, qTrip)
					}
					opts = append(opts, model.WithTripAfterMP(queueingTrips))
				} else {
					currentTrip := tripsMap[d.CurrentTrip]
					opts = append(opts, model.WithTripBeforeMP([]model.Trip{currentTrip}))
				}
				break
			}
		}
	}

	planRoute := model.NewMultiplePickupPlanRoute(d.DriverID, order.OrderID, task.order.Options.MpOrderIDs, opts...)
	if err = task.validateIfDriverEligibleForTakingMp(ctx, d, planRoute, trips, allOrders); err != nil {
		return model.PlanRoute{}, err
	}

	return planRoute, nil
}

func (task *autoAssignTask) ensureIdle(ctx context.Context, d *model.DriverMinimal, orderID string, enabledAssignAPI bool) bool {
	if task.acceptDurationWithBufferInSeconds() == 0 {
		return true
	}

	if enabledAssignAPI {
		// TODO[LMF-16138] please revise locker, which service should be owner of driver locker during accepting order
		// after enable AssignOrderAPI the locker owner is fleet-order(acquire & release) but
		// fleet-distribute has to access the locker the optimize rider searching(pre-filter) some driver during accept duration
		// the locker will be share resource between both services, it would be good to avoid datasource sharing between multiple services
		if v := task.Locker.GetState(ctx, locker.DriverAutoAssignState(d.DriverID)); v != "" {
			logx.Warn().Str(logutil.OrderID, orderID).Str(logutil.DriverID, d.DriverID).Msgf("autoassign: orderID=%s driverID=%s driver is being assigned another order.",
				orderID, d.DriverID)
			return false
		}
		return true
	}

	if ok := task.Locker.SetState(ctx, locker.DriverAutoAssignState(d.DriverID), orderID, time.Duration(task.acceptDurationWithBufferInSeconds())*time.Second); !ok {
		logx.Warn().Str(logutil.OrderID, orderID).Str(logutil.DriverID, d.DriverID).Msgf("autoassign: orderID=%s driverID=%s driver is being assigned another order.",
			orderID, d.DriverID)
		return false
	}

	return true
}

func (task *autoAssignTask) checkRequirement(ctx context.Context, d *model.DriverMinimal, assignmentType AssignmentType, opt model.AssignmentLogOpt, order *model.Order, isUsingPredictionService bool) (passed bool, filterName model.RiderFilterName, description string) {
	driversByID, err := task.DriverRepository.GetMinimalProfilesByID(ctx, []string{d.DriverID})
	if err != nil {
		return false, model.RiderProfileNotFound, "cannot reload the driver's information"
	}

	reloadedD, ok := driversByID[d.DriverID]
	if !ok {
		return false, model.RiderProfileNotFound, "the driver's information is not found"
	}

	constraint := &orderassigner.AssignmentConstraint{
		AssignmentType:             orderassigner.AssignmentType(assignmentType),
		AssignmentLogOpt:           &opt,
		IsUsingPredictionService:   isUsingPredictionService,
		LastPredictionDisruption:   d.LastPredictionDisruption,
		IsMPMaxLoad3:               task.IsMPMaxLoad3,
		DriverOptedOutServicesSet:  task.driverOptedOutServicesSet,
		CheckIfRiderEligibleForMp1: orderassigner.CheckIfRiderEligibleForMp1(task.OrderRepository),
	}

	if ok, filterName, desc := orderassigner.CheckRequirement(ctx, reloadedD, []string{order.OrderID}, constraint); !ok {
		return ok, filterName, desc
	}

	if ok, filterName, desc := orderassigner.CheckOrderAndRiderRequirement(ctx, reloadedD, []*model.Order{order}, constraint); !ok {
		return ok, filterName, desc
	}

	return true, "", ""
}

func (task *autoAssignTask) driverOptedOutServicesSet(ctx context.Context, driver *model.DriverMinimal) sets.Of[model.Service] {
	// default to no opt-out allowed services (nil/empty slice)
	optOutAllowed, isMergeFoodMartEnabled, _ := task.ServicePreferenceService.OptOutAllowedServicesFromPreferenceWithWhitelist(ctx, task.Config.ServicePreference, driver.DriverID)
	return service.DriverMinimalOptOutSetWithMerge(driver, optOutAllowed, isMergeFoodMartEnabled)
}

func DriverLockKey(driverID string) string {
	return "lock_driver:" + driverID
}

func (task *autoAssignTask) clearWaitForAcceptingState(ctx context.Context, d *model.DriverMinimal) {
	task.Locker.RemoveState(ctx, locker.DriverAutoAssignState(d.DriverID))
}

// recordStopRound will be redundant in this transition phase
// please also consider to make the changes in assigner package
func (task *autoAssignTask) recordStopRound(status string, round int) {
	task.stopMetricWithRegion.RecordStopRound(status, round)
}

func (task *autoAssignTask) logFetchedDrivers(drivers []service.DriverWithLocation) {
	task.fetchedMetric.Observe(float64(len(drivers)), model.RegionsString(task.regions))
}

func (task *autoAssignTask) logBatchOptimizeMappedOrder(orders []model.Order, drivers []service.DriverWithLocation, optimizedPlan prediction.BatchOptimizeResponse, optimizationRound model.OptimizationRound) {
	mappedOrders := types.NewStringSet()
	for _, o := range optimizedPlan.RiderOrders {
		if len(o) > 0 {
			mappedOrders.Add(o...)
		}
	}

	mappedPercentage := 0.0
	if len(orders) > 0 && mappedOrders.Count() > 0 {
		mappedPercentage = (float64(mappedOrders.Count()) / float64(len(orders))) * 100
	}

	riderToOrderPercentage := 0.0
	if len(orders) > 0 && len(drivers) > 0 {
		riderToOrderPercentage = (float64(len(drivers)) / float64(len(orders))) * 100
	}

	// Mapped order refers to the number of orders matched and returned from Dalian based on all the orders sent to them (it might be low, due to ineligible rider to orders )
	// rider to orders refers to the ratio of riders to orders, used to monitor in the same timeframe to figure out why the match percentage is low, even when there are plenty of riders.
	task.batchOptimizeMappedOrderDalianMetric.Observe(mappedPercentage, task.batchZone.ZoneCode, string(optimizationRound))
	task.batchOptimizeRiderToOrderDalianMetric.Observe(riderToOrderPercentage, task.batchZone.ZoneCode, string(optimizationRound))
}

func (task *autoAssignTask) IsMPMaxLoad3() bool {
	return orderassigner.IsMPMaxLoad3(task.Config.AtomicAutoAssignDbConfig.Get(), task.Config.MOType, task.Config.MaxOrdersPerRider)
}

func (task *autoAssignTask) markDedicatedZone(drivers []service.DriverWithLocation) {
	for i := range drivers {
		if len(drivers[i].Driver.DedicatedZones) != 0 {
			drivers[i].IsDedicatedZone = true
		}
	}
}

func (task *autoAssignTask) markPrioritized(drivers []service.DriverWithLocation) {
	for i := range drivers {
		drivers[i].IsPrioritized = true
	}
}

func (task *autoAssignTask) batchStartHeartbeat(
	ctx context.Context,
	orderIDs []string,
) error {
	if len(orderIDs) < 1 {
		return nil
	}

	for _, oID := range orderIDs {
		err := task.startHeartbeat(ctx, oID)
		if err != nil {
			return err
		}
	}
	return nil
}

func (task *autoAssignTask) batchStopHeartbeat(
	ctx context.Context,
	orderIDs []string,
) {
	if len(orderIDs) < 1 {
		return
	}

	err := task.OrderHeartbeatService.DoneBatch(ctx, orderIDs)
	if err != nil {
		logx.Error().Err(err).Interface(logutil.OrderIDs, orderIDs).Msg("batchStopHeartbeat failed with error")
		return
	}
	return
}

func (task *autoAssignTask) startHeartbeat(
	ctx context.Context,
	orderID string,
) error {
	err := task.OrderHeartbeatService.New(ctx, orderID)
	if err != nil {
		return err
	}

	return nil
}

func (task *autoAssignTask) stopHeartbeat(
	ctx context.Context,
	orderID string,
) {
	err := task.OrderHeartbeatService.Done(ctx, orderID)
	if err != nil {
		logx.Error().Err(err).Str(logutil.OrderID, orderID).Msg("stopHeartbeat failed with error")
		return
	}
	return
}

func (task *autoAssignTask) updateRedistributionState(ctx context.Context, state *model.RedistributionState) error {
	task.order.RedistributionState = state
	err := task.OrderRepository.SetRedistributionState(ctx, task.order.OrderID, task.order.RedistributionState)
	if err != nil {
		logx.Errorf(ctx, err, "auto_assign_redistribution: save redistribution state to order %s failed", task.order.OrderID)
		safe.SentryErrorMessage("AutoAssignTask Set redistribution state failed", func(contexts map[string]sentry.Context) {
			contexts["failed_context"] = map[string]interface{}{
				"order_id": task.order.OrderID,
				"error":    err.Error(),
			}
		})
		return err
	}

	return nil
}

func (task *autoAssignTask) getOrLoadNotifiedDrivers(ctx context.Context, orderID string) []model.Record {
	assignmentLog := task.assignmentLogMemoize.GetOrLoad(ctx, orderID)
	if assignmentLog != nil {
		return assignmentLog.GetDrivers()
	}
	return []model.Record{}
}

func (task *autoAssignTask) getOrLoadIllegalDriversIDs(ctx context.Context, orderID string) types.StringSet {
	assignmentLog := task.assignmentLogMemoize.GetOrLoad(ctx, orderID)
	if assignmentLog != nil {
		return assignmentLog.GetIllegalDriverIDs()
	}
	return types.NewStringSet()
}

func (task *autoAssignTask) insertIllegalDriver(ctx context.Context, illegalUntil time.Time, riderFilterName model.RiderFilterName, validateErr error, newOrders []model.Order, newOrderIDs []string, d model.DriverMinimal) {
	insertIllegalDriver := orderassigner.InsertIllegalDriver(task.Config.AtomicAutoAssignDbConfig.Get(), task.AssignmentLogRepo, task.IllegalDriverRepository)
	insertIllegalDriver(ctx, illegalUntil, riderFilterName, validateErr, newOrders, newOrderIDs, d)
}

func (task *autoAssignTask) FixManipulateMultipleTrips(ctx context.Context, d service.DriverWithLocation, routeRes prediction.RouteResponse, orders []model.Order, routeSetting service.RouteSetting, validatePlanRouteSetting service.ValidatePlanRouteSetting) (prediction.RouteResponse, types.StringSet, []model.Order, service.ValidateRouteErrRes, error) {
	if len(orders) <= 1 {
		return prediction.RouteResponse{}, types.StringSet{}, nil, service.ValidateRouteErrRes{}, fmt.Errorf("not enough orders to fix: got %d orders", len(orders))
	}

	orderIDs := types.NewStringSet(fp.MapSlice(model.OrderToOrderID, orders)...)
	var selectedOrderID string
	for _, pr := range routeRes.PlanRoutes {
		if pr.ActionType == prediction.PickupAction && orderIDs.Has(pr.OrderID) {
			selectedOrderID = pr.OrderID
			break
		}
	}
	if selectedOrderID == "" {
		return prediction.RouteResponse{}, types.StringSet{}, nil, service.ValidateRouteErrRes{}, fmt.Errorf("no new pickup orders")
	}

	var selectedOrder model.Order
	for i := range orders {
		if orders[i].OrderID == selectedOrderID {
			selectedOrder = orders[i]
			break
		}
	}

	newOrders, newOrderIDs := []model.Order{selectedOrder}, []string{selectedOrderID}
	newRouteRes, _, newRouteErr := task.PredictionService.Route(ctx, newOrders, d, routeSetting)
	if newRouteErr != nil {
		err := errorWithOrderIDs{error: fmt.Errorf("call route failed: %w", newRouteErr), orderIDs: newOrderIDs}
		return prediction.RouteResponse{}, types.StringSet{}, nil, service.ValidateRouteErrRes{}, err
	}

	newValidateErrRes, newErr := task.PredictionService.ValidatePlanRoute(ctx, newRouteRes, newOrderIDs, d, validatePlanRouteSetting)
	if newErr != nil {
		err := errorWithOrderIDs{error: fmt.Errorf("validate plan route failed: %w", newErr), orderIDs: newOrderIDs}
		return prediction.RouteResponse{}, types.StringSet{}, nil, service.ValidateRouteErrRes{}, err
	}
	return newRouteRes, types.NewStringSet(newOrderIDs...), newOrders, newValidateErrRes, nil
}

type driverAndScore struct {
	DriverWithLocation *service.DriverWithLocation
	TotalScore         float64
	Scores             []namedScore
}

type driverAndScoreByTotalScore []driverAndScore

func (a driverAndScoreByTotalScore) Len() int           { return len(a) }
func (a driverAndScoreByTotalScore) Less(i, j int) bool { return a[i].TotalScore > a[j].TotalScore }
func (a driverAndScoreByTotalScore) Swap(i, j int)      { a[i], a[j] = a[j], a[i] }

type namedScore struct {
	Name  string
	Value float64
}

func (driverAndScore *driverAndScore) Add(order *model.Order, scorer scorer) (bool, model.RiderFilterName) {
	score, filterName := scorer.Calculate(order, driverAndScore.DriverWithLocation)
	if score == FilteredOut {
		logx.Debug().Str(logutil.OrderID, order.OrderID).Str(logutil.DriverID, driverAndScore.DriverWithLocation.Driver.DriverID).Msgf("autoassign: orderID=%s driverID=%s scorer=%s driver was filtered out.",
			order.OrderID, driverAndScore.DriverWithLocation.Driver.DriverID, scorer.Name())
		return false, filterName
	}

	driverAndScore.TotalScore += score
	driverAndScore.Scores = append(driverAndScore.Scores, namedScore{scorer.Name(), score})

	return true, ""
}

func isPredictionServiceUsable(order *model.Order, autoAssignConfig AutoAssignConfig, distributionConfig *dispatcherconfig.AtomicDistributionConfig, restaurantBlacklistSets model.RestaurantBlacklistStringSets, skipShopAllowList bool) bool {
	if !isAllowPrediction(order.ServiceType, autoAssignConfig, distributionConfig) {
		return false
	}
	if (order.ServiceType == model.ServiceFood || order.ServiceType == model.ServiceMart) && !isAllowShop(order) {
		return false
	}
	if (order.ServiceType == model.ServiceFood || order.ServiceType == model.ServiceMart) && !skipShopAllowList && !isShopInAllowList(order, autoAssignConfig, restaurantBlacklistSets) {
		return false
	}
	if !isAllowOrder(order) {
		return false
	}
	return true
}

func isAllowPrediction(orderServiceType model.Service, config AutoAssignConfig, distributionConfig *dispatcherconfig.AtomicDistributionConfig) bool {
	if !config.PredictionServiceEnabled || distributionConfig.Get().PredictionServiceDisabled {
		return false
	}
	if orderServiceType == model.ServiceBike && !config.BikeB2BEnabled {
		return false
	}
	if orderServiceType == model.ServiceMart && (config.AtomicBackToBackConfig.Get().ShutdownMartMOB2B || !config.MartMOB2BEnabled) {
		return false
	}
	return true
}

func isAllowShop(order *model.Order) bool {
	foodInfo := model.CastStopInfoFood(&order.Routes[0].Info.StopInfo)
	return foodInfo != nil && foodInfo.RestaurantType == model.PriceSchemeRMS
}

func isShopInAllowList(order *model.Order, config AutoAssignConfig, restaurantBlacklistSets model.RestaurantBlacklistStringSets) bool {
	whitelist := types.NewStringSet(config.PredictionRestaurantWhitelist...)
	id := order.Routes[0].ID
	return !isShopInBlackList(order, config) && !restaurantBlacklistSets.HasAnyWithOption(model.BlacklistB2B, id) && (whitelist.Has(id) || whitelist.Count() == 0)
}

func isShopInBlackList(order *model.Order, config AutoAssignConfig) bool {
	cfg := config.AtomicBackToBackConfig.Get()
	var trimIds []string
	for _, id := range cfg.BackToBackNotAllowIDs {
		s := strings.ReplaceAll(id, " ", "")
		trimIds = append(trimIds, s)
	}

	if stringutil.IsStringInList(trimIds, order.Routes[0].ID) {
		logx.Warn().Str(logutil.OrderID, order.OrderID).Msgf("can't apply b2b from blacklist id: %s", order.Routes[0].ID)
		return true
	}
	return false
}

func isAllowOrder(order *model.Order) bool {
	if order.ServiceType != model.ServiceFood && order.ServiceType != model.ServiceBike && order.ServiceType != model.ServiceMart {
		return false
	}
	if (order.ServiceType == model.ServiceFood || order.ServiceType == model.ServiceMart) && !order.IsHalfHalf() && order.IsRiderInvolveCashPayment() {
		return false
	}
	return true
}

func canDefer(order *model.Order, autoAssignConfig AutoAssignConfig, isPredictionServiceUsable bool) (bool, int) {
	if !isPredictionServiceUsable || order.IsLockRequired() || order.IsMultiplePickup() || order.Options.IsPickPack ||
		(order.ServiceType != model.ServiceFood && order.ServiceType != model.ServiceMart) {
		return false, 0
	}

	if !autoAssignConfig.DeferredDispatchFeatureEnabled ||
		autoAssignConfig.AtomicAutoAssignDbConfig.Get().DisableDeferredDispatch ||
		(order.ServiceType == model.ServiceMart && !autoAssignConfig.DeferredDispatchFeatureEnabledMart) ||
		order.IsDeferred {
		return false, 0
	}

	if autoAssignConfig.DeferredDispatchBlacklistTimeSlots.IsIn(timeutil.BangkokNow()) {
		return false, 0
	}

	if len(autoAssignConfig.DeferredDispatchRestaurantBlacklist) > 0 {
		blacklist := types.NewStringSet(autoAssignConfig.DeferredDispatchRestaurantBlacklist...)
		if blacklist.Has(order.Routes[0].ID) {
			return false, 0
		}
	} else if len(autoAssignConfig.DeferredDispatchRestaurantWhitelist) > 0 {
		whitelist := types.NewStringSet(autoAssignConfig.DeferredDispatchRestaurantWhitelist...)
		if !whitelist.Has(order.Routes[0].ID) {
			return false, 0
		}
	}

	cookingDurationMinute := int((time.Duration(order.Prediction.EstimatedCookingTimeSecond) * time.Second).Minutes())
	cfgDeferDurationMinute := int(autoAssignConfig.DeferredDispatchDrivingDuration.Minutes())
	cfgBufferDurationMinute := int(autoAssignConfig.DeferredDispatchBufferDuration.Minutes())
	deferredDurationMinute := cookingDurationMinute - (cfgDeferDurationMinute + cfgBufferDurationMinute)

	if autoAssignConfig.AtomicAutoAssignDbConfig.Get().DisableDeferredDispatchDrivingAndBufferDurationForMart && order.ServiceType == model.ServiceMart {
		if deferredDurationMinute < 0 {
			deferredDurationMinute = 0
		}
		return true, deferredDurationMinute
	}

	if deferredDurationMinute <= 0 {
		return false, 0
	}

	modifiedCookingDurationMinute := int(order.ModifiedPredictedCookingTime().Minutes())
	modifiedDeferredDurationMinute := modifiedCookingDurationMinute - (cfgDeferDurationMinute + cfgBufferDurationMinute)
	return true, max(modifiedDeferredDurationMinute, 0)
}

// evaluateSearchRadiusByDistrictInKm only supports single distribution since it evaluates radius from the order's pickup district
func evaluateSearchRadiusByDistrictInKm(ctx context.Context, zoneRepo repository.ZoneRepository, o *model.Order, cfg *orderapi.AtomicOrderDBConfig, t time.Time) float64 {
	if o == nil || o.ServiceType != model.ServiceBike {
		return 0
	}

	parsedDates, parsedSearchRadius, err := cfg.GetParsedBikeDistrictZonesExperimentConfigs()
	if err != nil {
		logx.Error().Msgf("[BikeDistrictZonesExperiment] orderID=%v,err=%v", o.OrderID, err)
		return 0
	}

	isDateMatched := false
	for _, parsedDate := range parsedDates {
		yearToEvaluate, monthToEvaluate, dayToEvaluate := t.Date()
		eligibleYear, eligibleMonth, eligibleDay := parsedDate.Date()

		if yearToEvaluate == eligibleYear &&
			monthToEvaluate == eligibleMonth &&
			dayToEvaluate == eligibleDay {
			isDateMatched = true
			break
		}
	}

	if !isDateMatched {
		return 0
	}

	matchedZoneCodes, err := zoneRepo.FindZoneCodesByLocation(ctx, o.Routes[0].Location.Lat, o.Routes[0].Location.Lng, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().Msgf("[BikeDistrictZonesExperiment] orderID=%v,err=unable to find zone codes by location: %v", o.OrderID, err)
		return 0
	}

	matchedZoneCodeSet := types.NewStringSet(matchedZoneCodes...)
	for eligibleZone, searchRadii := range parsedSearchRadius {
		if !matchedZoneCodeSet.Has(eligibleZone) || len(searchRadii) != 5 {
			continue
		}
		if model.IsInStartEndTimeCondition(t, []model.StartEndTime{{Begin: "00:00:00", End: "05:59:59"}}) {
			return searchRadii[0]
		} else if model.IsInStartEndTimeCondition(t, []model.StartEndTime{{Begin: "06:00:00", End: "09:59:59"}}) {
			return searchRadii[1]
		} else if model.IsInStartEndTimeCondition(t, []model.StartEndTime{{Begin: "10:00:00", End: "15:59:59"}}) {
			return searchRadii[2]
		} else if model.IsInStartEndTimeCondition(t, []model.StartEndTime{{Begin: "16:00:00", End: "19:59:59"}}) {
			return searchRadii[3]
		} else if model.IsInStartEndTimeCondition(t, []model.StartEndTime{{Begin: "20:00:00", End: "23:59:59"}}) {
			return searchRadii[4]
		}
	}

	return 0
}

type errorWithOrderIDs struct {
	error
	orderIDs []string
}

func (e errorWithOrderIDs) OrderIDs() []string {
	return e.orderIDs
}
