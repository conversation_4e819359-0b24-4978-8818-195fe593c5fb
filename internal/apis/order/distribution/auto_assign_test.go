package distribution_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/absinthe/crypt"
	v1 "git.wndv.co/lineman/delivery-service/types/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep/mock_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/cleanup"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestProvideAutoAssignOrderDistributor(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	autoAssignOrderDistributor, mocks, _, cleanUpFn := createAutoAssignOrderDistributorWithContingencyCfg(t, order.ContingencyConfig{})
	defer cleanUpFn()

	wgCtx := safe.ProvideWorkerContext(nil, cleanup.ProvideCleanupPriority())

	underTest, _ := distribution.ProvideAutoAssignOrderDistributor(
		distribution.ProvideAutoAssignOrderDistributorDeps(
			mocks.mockLocationManager,
			mocks.mockOrderRepository,
			mocks.mockDriverRepository,
			mocks.mockNotifier,
			mocks.mockAssigmentLogRepo,
			mocks.mockDriverTransaction,
			mocks.mockStatisticRepo,
			mocks.mockStatisticService,
			mocks.mockDriverOrderInfoRepo,
			mocks.mockIncentiveRepo,
			mocks.mockLocker,
			order.OrderAPIConfig{},
			testmetric.NewStubMeter(),
			mock_rep.NewMockREPService(ctrl),
			order.NewAtomicContingencyConfig(order.ContingencyConfig{}),
			mocks.mockEventBus,
			&dispatcherconfig.AtomicDistributionConfig{},
			mocks.mockPredictionService,
			mocks.mockTxnHelper,
			&distribution.AutoAssignConfig{},
			nil,
			mocks.mockDriverService,
			mocks.mapService,
			mocks.dedicatedZoneRepo,
			mocks.mockDriverLocationRepository,
			mocks.mockTripRepository,
			mocks.mockOnTopFareService,
			mocks.mockThrottledDispatchDetailRepository,
			mocks.mockThrottledOrderRepository,
			mocks.mockDispatcher,
			mocks.mockDeferredOrderRepository,
			mocks.mockAssignmentRepository,
			mocks.mockRainSituationService,
			mocks.mockServiceAreaRepository,
			mocks.mockZoneRepository,
			mocks.mockUserClient,
			mocks.mockRedisClient,
			mocks.mockDelivery,
			wgCtx,
			mocks.mockServicePreferenceService,
			mocks.mockDistributionLogManager,
			mocks.mockHearthBeatService,
			mocks.mockOrderDistributionEventManager,
			mocks.mockDistributionExperimentPlatformClient,
			mocks.mockDistributionService,
			mocks.mockFeatureFlagService,
			mocks.mockMetricsRegistry,
			mocks.mockFleetOrderClient,
			mocks.mockIllegalDriverRepo,
			mocks.mockAssigningStateManager,
			config.ThrottledOrderDBConfig{},
		),
	)

	t.Run("inject dependencies correctly", func(tt *testing.T) {
		tt.Parallel()

		require.Equal(tt, mocks.mockLocationManager, underTest.LocationManager)
		require.Equal(tt, mocks.mockOrderRepository, underTest.OrderRepository)
		require.Equal(tt, mocks.mockNotifier, underTest.Notifier)
		require.Equal(tt, mocks.mockAssigmentLogRepo, underTest.AssignmentLogRepo)
		require.Equal(tt, mocks.mockStatisticRepo, underTest.StatisticRepo)
		require.Equal(tt, mocks.mockLocker, underTest.Locker)
	})

	t.Run("default config", func(tt *testing.T) {
		tt.Parallel()

		cfg := autoAssignOrderDistributor.Config
		require.Equal(tt, float64(10), cfg.MaxRadiusInKm)
		require.Equal(tt, float64(1), cfg.DistanceScoreWeight)
		require.Equal(tt, int64(15), cfg.AcceptingDurationInSecond)
		require.Equal(tt, 0.5, cfg.AutoAcceptScore)
		require.Equal(tt, float64(5), cfg.NewbieMaxDays)
		require.Equal(tt, 1.0, cfg.NewbieScoreWeight)
	})

	t.Run("contingency config", func(tt *testing.T) {
		tt.Parallel()

		cfg := underTest.ContingencyCfg.Get()
		require.Equal(tt, false, cfg.ContingencyModeEnabled)
		require.Equal(tt, 0.0, cfg.LimitMinusCredit)
	})
}

func TestAutoAssignTask_checkRequirement(t *testing.T) {
	t.Parallel()
	t.Run("service type uses fresh rider data", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mocks.mockServicePreferenceService = mock_service.NewMockServicePreferenceService(ctrl)
		autoAssignOrderDistributor.AutoAssignOrderDistributorDeps.ServicePreferenceService = mocks.mockServicePreferenceService
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"
		oldDriver := &model.DriverMinimal{
			DriverID: driverID,
			Status:   model.StatusOnline,
		}
		newDriver := &model.DriverMinimal{
			DriverID:       driverID,
			Status:         model.StatusOnline,
			ServicesOptOut: []model.Service{model.ServiceFood},
		}
		*(task.Order()) = &model.Order{
			Quote: model.Quote{ServiceType: model.ServiceFood},
		}
		mocks.mockServicePreferenceService.EXPECT().OptOutAllowedServicesFromPreferenceWithWhitelist(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Service{model.ServiceFood}, false, nil)
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: newDriver}, nil)
		actualPassed, actualFilterName, _ := task.CheckRequirement(ctx, oldDriver, distribution.AnyAssignmentType, model.AssignmentLogOpt{}, *(task.Order()), true)
		assert.False(t, actualPassed)
		assert.EqualValues(t, model.RiderOptedOut, actualFilterName)
	})
	t.Run("if merge food mart, force allow food if only opt out food", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mocks.mockServicePreferenceService = mock_service.NewMockServicePreferenceService(ctrl)
		autoAssignOrderDistributor.AutoAssignOrderDistributorDeps.ServicePreferenceService = mocks.mockServicePreferenceService
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"
		oldDriver := &model.DriverMinimal{
			DriverID: driverID,
			Status:   model.StatusOnline,
		}
		newDriver := &model.DriverMinimal{
			DriverID:       driverID,
			Status:         model.StatusOnline,
			ServicesOptOut: []model.Service{model.ServiceFood},
		}
		*(task.Order()) = &model.Order{
			Quote: model.Quote{ServiceType: model.ServiceFood},
		}
		mocks.mockServicePreferenceService.EXPECT().OptOutAllowedServicesFromPreferenceWithWhitelist(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Service{model.ServiceFood, model.ServiceMart}, true, nil)
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: newDriver}, nil)
		actualPassed, _, _ := task.CheckRequirement(ctx, oldDriver, distribution.AnyAssignmentType, model.AssignmentLogOpt{}, *(task.Order()), true)
		assert.True(t, actualPassed)
	})
	t.Run("mp1 eligible uses fresh rider data", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"
		oldDriver := &model.DriverMinimal{
			DriverID: driverID,
			Status:   model.StatusOnline,
		}
		newDriver := &model.DriverMinimal{
			DriverID: "LMD2",
			Status:   model.StatusOnline,
		}
		*(task.Order()) = &model.Order{
			Quote: model.Quote{Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockOrderRepository.EXPECT().GetOtherActiveMP(gomock.Any(), gomock.Any()).Return(&model.Order{Driver: "LMD2"}, nil)
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: newDriver}, nil)
		actualPassed, actualFilterName, _ := task.CheckRequirement(ctx, oldDriver, distribution.AnyAssignmentType, model.AssignmentLogOpt{}, *(task.Order()), true)
		assert.False(t, actualPassed)
		assert.EqualValues(t, model.Mp1ATRCannotBeAssigned, actualFilterName)
	})
}

func TestAutoAssign_BatchDistribute(t *testing.T) {
	t.Parallel()
	t.Run("should throttle order correctly", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{BatchDistributeTimeoutInSeconds: 60})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, _, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): timeutil.BangkokNow(),
		}
		o.ExpireAt = time.Date(2023, 05, 25, 10, 0, 0, 0, timeutil.BangkokLocation())

		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		expectZoneCall(mocks)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.AssignmentLogRecord{{
			OrderID: o.OrderID,
		}}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		expectPredictCall(ctx, mocks, tt)

		zoneID := primitive.NewObjectID()
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID}, ZoneCode: "BKK-THROTTLE"}, nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetThrottledOrder(gomock.Any(), o.OrderID, gomock.Any(), gomock.Any()).Return(nil)

		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), gomock.Any()).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
	})
	t.Run("happy path without batch assigning", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("ord-1"),
			createPredictedOrder("ord-2"),
			createPredictedOrder("ord-3"),
			createPredictedOrder("ord-4"),
			createPredictedOrder("ord-5"),
			createPredictedOrder("ord-6"),
			createMinimalOrder("ord-without-prediction"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)
		validOrders := allOrders[:6]
		validOrderIDs := model.OrderIDsFromList(validOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
		for _, id := range validOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("4", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("5", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("6", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		validDrivers := drivers[:len(drivers)-1]
		for _, d := range validDrivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range validOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {validOrderIDs[0], validOrderIDs[1]},
					drivers[1].Driver.DriverID: {validOrderIDs[2]},
					drivers[2].Driver.DriverID: {validOrderIDs[3]},
				},
			}, nil,
		)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID)).Return()
		// redistribute for unassignable
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[4])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[4], gomock.Any())
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[5])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[5], gomock.Any())
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[6], gomock.Any())

		// test scope doesn't include assigning process.
		// batchAssign will unlock assignable orders, drivers and also redistribute all of unassigned orders
		assignableOrders := []string{validOrderIDs[0], validOrderIDs[1], validOrderIDs[2], validOrderIDs[3]}
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong")).AnyTimes()
		for _, id := range assignableOrders {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 6, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 5, zone.ZoneCode)
	})
	t.Run("happy path without batch assigning - with priority round for dedicated zone riders", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		zoneCoordinates := model.ZoneCoordinates{
			{
				{
					{
						100.56928074072022,
						14.353136925751102,
					},
					{
						100.56928074072022,
						14.351432311661526,
					},
					{
						100.57554638097992,
						14.351432311661526,
					},
					{
						100.57554638097992,
						14.353136925751102,
					},
					{
						100.56928074072022,
						14.353136925751102,
					},
				},
			},
		}
		zone.Geometry = model.ZoneGeometry{
			Type:        "MultiPolygon",
			Coordinates: zoneCoordinates,
		}

		orderInDedicatedZone := createPredictedOrder("ord-1")
		orderInDedicatedZone.Routes[0].Location = model.Location{
			Lat: 14.352398954170852,
			Lng: 100.57224189947391,
		}

		allOrders := []model.Order{
			orderInDedicatedZone,
			createPredictedOrder("ord-2"),
		}
		validOrders := allOrders[:2]
		validOrderIDs := model.OrderIDsFromList(validOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
		for _, id := range validOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		dedicatedZoneDriver := createDriver("2", model.StatusOnline, model.Options{AutoAccept: false})
		dedicatedZoneDriver.DedicatedZones = []string{"d-zone"}
		dedicatedZoneDriverWithLocation := createDriverWithLocation(dedicatedZoneDriver, model.Location{Lat: 1.002, Lng: 2}, 1000)

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			dedicatedZoneDriverWithLocation,
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range validOrderIDs {
			mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(map[string]model.DedicatedZone{
				"d-zone": {
					Label:        "d-zone",
					ServiceTypes: []model.Service{model.ServiceFood},
					Geometry: model.DedicatedZoneGeometry{
						Type:        "MultiPolygon",
						Coordinates: model.DedicatedZoneCoordinates(zoneCoordinates),
					},
				},
			}, nil).AnyTimes()
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		// Priority round
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[1].Driver.DriverID: {validOrderIDs[0]},
				},
			}, nil,
		)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(validOrderIDs[0])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), validOrderIDs[0], gomock.Any())

		// Normal round
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {validOrderIDs[1]},
				},
			}, nil,
		)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(validOrderIDs[1])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), validOrderIDs[1], gomock.Any())

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 2, zone.ZoneCode)
	})
	t.Run("happy path with batch assigning - SP round", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		zoneCoordinates := model.ZoneCoordinates{
			{
				{
					{
						100.56928074072022,
						14.353136925751102,
					},
					{
						100.56928074072022,
						14.351432311661526,
					},
					{
						100.57554638097992,
						14.351432311661526,
					},
					{
						100.57554638097992,
						14.353136925751102,
					},
					{
						100.56928074072022,
						14.353136925751102,
					},
				},
			},
		}
		zone.Geometry = model.ZoneGeometry{
			Type:        "MultiPolygon",
			Coordinates: zoneCoordinates,
		}

		orderInDedicatedZone := createPredictedOrder("ord-1")
		orderInDedicatedZone.Routes[0].Location = model.Location{
			Lat: 14.352398954170852,
		}

		allOrders := []model.Order{
			orderInDedicatedZone,
			createPredictedOrder("ord-2"),
		}
		validOrders := allOrders[:2]
		validOrderIDs := model.OrderIDsFromList(validOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
		for _, id := range validOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		spDriver := createDriver("2", model.StatusOnline, model.Options{AutoAccept: false})
		spDriver.H3Recommendation.Areas = []model.RecommendedH3{
			{
				H3ID: "mock-h3-id",
			},
		}
		H3AreasMap := make(map[string]model.RecommendedH3)
		H3AreasMap["8065fffffffffff"] = model.RecommendedH3{
			Name: "area-a",
		}
		spDriver.H3Recommendation.H3AreasIndex = H3AreasMap
		mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverOrderRecommendedState(spDriver.DriverID)).Return("mock-h3-id")
		spDriver.H3Recommendation.ExpiredAt = timeutil.BangkokNow().Add(time.Minute)
		spDriverWithLocation := createDriverWithLocation(spDriver, model.Location{Lat: 13.744, Lng: 100.606}, 1000)

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			spDriverWithLocation,
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range validOrderIDs {
			mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(map[string]model.DedicatedZone{
				"d-zone": {
					Label:        "d-zone",
					ServiceTypes: []model.Service{model.ServiceFood},
					Geometry: model.DedicatedZoneGeometry{
						Type:        "MultiPolygon",
						Coordinates: model.DedicatedZoneCoordinates(zoneCoordinates),
					},
				},
			}, nil).AnyTimes()
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		// SP round
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[1].Driver.DriverID: {validOrderIDs[0]},
				},
			}, nil,
		)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(validOrderIDs[0])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), validOrderIDs[0], gomock.Any())

		// Normal round
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {validOrderIDs[1]},
				},
			}, nil,
		)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(validOrderIDs[1])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), validOrderIDs[1], gomock.Any())

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 2, zone.ZoneCode)
	})
	t.Run("happy path with batch assigning - one by one", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
			createPredictedOrder("3"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
			expectEvaluateRainSituation(ctx, mocks, true, false)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
					drivers[1].Driver.DriverID: {allOrderIDs[2]},
				},
			}, nil,
		)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[0]}, {OrderID: allOrderIDs[1]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.DropOffAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[0].Driver, allOrders[0], false, model.StrategyNameFleetPool)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[0].Driver, allOrders[1], false, model.StrategyNameFleetPool)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[1], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[2]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[1].Driver, allOrders[2], false, model.StrategyNameFleetPool)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any()).AnyTimes()

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 3, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("happy path with batch assigning - all at once", func(tt *testing.T) {
		tt.Skip()
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		distCfg.BatchAssignmentEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
			createPredictedOrder("3"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameDirectSearch,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		for _, d := range drivers {
			mocks.mockDriverTransaction.EXPECT().GetDriverTransaction(gomock.Any(), d.Driver.DriverID, gomock.Any()).Return(createDriverTransaction(d.Driver.DriverID), nil).Times(len(allOrderIDs))
		}

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
					drivers[1].Driver.DriverID: {allOrderIDs[2]},
				},
			}, nil,
		)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[0]}, {OrderID: allOrderIDs[1]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.DropOffAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil)
		assertAssignMultipleOrderInBatch(tt, ctx, mocks, drivers[0].Driver, []model.Order{allOrders[0], allOrders[1]}, model.StrategyNameDirectSearch)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[1], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[2]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil)
		assertAssignMultipleOrderInBatch(tt, ctx, mocks, drivers[1].Driver, []model.Order{allOrders[2]}, model.StrategyNameDirectSearch)

		for _, d := range drivers {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return()
		}
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 3, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("should do nothing when fetched orders is empty", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, allOrderIDs, zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 0, zone.ZoneCode)
	})
	t.Run("should redistribute all orders when doesn't have any compatible riders", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("ord-1"),
			createPredictedOrder("ord-2"),
			createPredictedOrder("ord-3"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameDirectSearch,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)

		// redistribute from defer in ProcessBatch
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, allOrderIDs, zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 3, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 0, zone.ZoneCode)
	})
	t.Run("should evaluate rain location correctly", func(tt *testing.T) {
		tt.Parallel()

		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
			createPredictedOrder("3"),
		}
		allOrders[0].Routes[allOrders[0].GetPayAtStop()].Location = model.Location{Lat: 1.000, Lng: 1.000}
		allOrders[1].Routes[allOrders[1].GetPayAtStop()].Location = model.Location{Lat: 1.001, Lng: 1.001}
		allOrders[2].Routes[allOrders[2].GetPayAtStop()].Location = model.Location{Lat: 1.002, Lng: 1.002}

		serviceArea := &model.ServiceArea{}

		driverLocation := model.Location{Lat: 1.002, Lng: 2}

		type OrderDetail struct {
			Order                        model.Order
			PreviousDropOffLocationOrder *model.Order
		}

		type TestCase struct {
			Title               string
			PlanRoutes          []prediction.PlanRoute
			OrderDetails        []OrderDetail
			SearchRiderStrategy model.SearchRiderStrategy
		}

		testCases := []TestCase{
			{
				Title: "[p1d1][p2d2][p3d3]",
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[2].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[2].OrderID,
						ActionType: prediction.DropOffAction,
					},
				},
				OrderDetails: []OrderDetail{
					{
						Order: allOrders[0],
					},
					{
						Order:                        allOrders[1],
						PreviousDropOffLocationOrder: &allOrders[0],
					},
					{
						Order:                        allOrders[2],
						PreviousDropOffLocationOrder: &allOrders[1],
					},
				},
				SearchRiderStrategy: model.StrategyNameDirectSearch,
			},
			{
				Title: "[p1p2d1d2]",
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.DropOffAction,
					},
				},
				OrderDetails: []OrderDetail{
					{
						Order: allOrders[0],
					},
					{
						Order: allOrders[1],
					},
				},
				SearchRiderStrategy: model.StrategyNameFleetPool,
			},
			{
				Title: "[p1p2d1d2][p3d3]",
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[2].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[2].OrderID,
						ActionType: prediction.DropOffAction,
					},
				},
				OrderDetails: []OrderDetail{
					{
						Order: allOrders[0],
					},
					{
						Order: allOrders[1],
					},
					{
						Order:                        allOrders[2],
						PreviousDropOffLocationOrder: &allOrders[1],
					},
				},
				SearchRiderStrategy: model.StrategyNameFleetPool,
			},
		}

		for _, tc := range testCases {
			_tc := tc

			tt.Run(_tc.Title, func(ttt *testing.T) {
				ttt.Parallel()

				orderIdsSet := types.NewStringSet()
				for _, od := range _tc.OrderDetails {
					orderIdsSet.Add(od.Order.OrderID)
				}
				orderIds := orderIdsSet.PopToSlice()

				allOrders := []model.Order{}
				allOrderIDs := []string{}
				for _, od := range _tc.OrderDetails {
					allOrders = append(allOrders, od.Order)
					allOrderIDs = append(allOrderIDs, od.Order.OrderID)
				}

				autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(ttt, order.ContingencyConfig{})
				distCfg.PredictionServiceEnabled = true
				distCfg.NotifyViaSocketIOEnabled = true
				distCfg.AssignmentType = prediction.AssignmentTypeMultiple
				distCfg.MOType = prediction.MOTypeMOS4
				defer finishFn()

				ctx, wg := safe.CreateCtxWithWaitGroup()
				zone := createMinimalZone()

				mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
					ZoneCode: zone.ZoneCode,
					ThrottledDispatchDetail: model.ThrottledDispatchDetail{
						ZoneID:                    zone.ID,
						EnabledSearchRadiusOffset: false,
						SearchRadiusOffsetKM:      0,
					},
				}, nil)

				mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
				for _, id := range allOrderIDs {
					mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
				}

				drivers := []service.DriverWithLocation{
					createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), driverLocation, 1000),
				}
				mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
					Results:  drivers,
					Strategy: _tc.SearchRiderStrategy,
				}, nil)
				mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
				for _, d := range drivers {
					mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
					mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
				}

				for _, id := range allOrderIDs {
					expectZoneCall(mocks)
					mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
				}
				expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
				trans := make([]model.DriverTransaction, 0)
				for _, d := range drivers {
					trans = append(trans, createDriverTransaction(d.Driver.DriverID))
				}
				mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

				mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					prediction.BatchOptimizeResponse{
						RiderOrders: map[string][]string{
							drivers[0].Driver.DriverID: orderIds,
						},
					}, nil,
				)

				assignedOrders := make([]prediction.AssignedOrder, 0)
				for _, id := range orderIds {
					assignedOrders = append(assignedOrders, prediction.AssignedOrder{OrderID: id})
				}

				mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
					AssignedOrders: assignedOrders,
					PlanRoutes:     _tc.PlanRoutes,
				}, nil, nil)

				for _, od := range _tc.OrderDetails {
					assertAssignOrderInBatch(ttt, ctx, mocks, drivers[0].Driver, od.Order, true, _tc.SearchRiderStrategy)
					mocks.mockServiceAreaRepository.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(serviceArea, nil)
					if od.PreviousDropOffLocationOrder != nil {
						mocks.mockRainSituationService.EXPECT().IsRainingByRegionAndLocation(gomock.Any(), string(drivers[0].Driver.Region), od.PreviousDropOffLocationOrder.GetDropOff().Location).Return(true)
						mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), od.PreviousDropOffLocationOrder.OrderID, gomock.Any()).Return(od.PreviousDropOffLocationOrder, nil)
					} else {
						mocks.mockRainSituationService.EXPECT().IsRainingByRegionAndLocation(gomock.Any(), string(drivers[0].Driver.Region), drivers[0].Location).Return(true)
					}
				}

				for _, d := range drivers {
					mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return()
				}
				for _, id := range allOrderIDs {
					mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
				}

				mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
				mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
				mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
				mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
				mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any()).AnyTimes()

				stubDefaultBehavior(mocks)

				_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
				wg.Wait()
				AssertBatchOrderMetric(ttt, mocks, float64(len(orderIds)), zone.ZoneCode)
				AssertBatchRiderMetric(ttt, mocks, 1, zone.ZoneCode)
			})
		}
	})
	t.Run("happy path with batch assigning - assignable order with until defer", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
			createPredictedOrder("3"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
			expectEvaluateRainSituation(ctx, mocks, true, false)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		expectedDeferUntil := timeutil.TheEndOfTime()
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
					drivers[1].Driver.DriverID: {allOrderIDs[2]},
				},
				DeferOrders: map[string]prediction.BatchOptimizeDeferOrder{
					allOrderIDs[0]: {
						DeferUntil: expectedDeferUntil.Unix(),
						T1:         expectedDeferUntil.Unix(),
					},
				},
			}, nil,
		)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[0]}, {OrderID: allOrderIDs[1]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.DropOffAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[0].Driver, allOrders[0], false, model.StrategyNameFleetPool)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[0].Driver, allOrders[1], false, model.StrategyNameFleetPool)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[1], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[2]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[1].Driver, allOrders[2], false, model.StrategyNameFleetPool)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any()).AnyTimes()

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 3, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("happy path with batch assigning - with only defer", func(tt *testing.T) {
		tt.Parallel()
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mocks.mockOrderDistributionEventManager = mock_service.NewMockOrderDistributionEventManager(ctrl)
		autoAssignOrderDistributor.AutoAssignOrderDistributorDeps.OrderDistributionEventManager = mocks.mockOrderDistributionEventManager
		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
		}
		allOrders[0].Options.SwitchFlow = true
		allOrders[0].Options.CanDefer = true
		allOrders[1].Options.SwitchFlow = true
		allOrders[1].Options.CanDefer = true
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameDirectSearch,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		o1ExpectedDeferUntil := timeutil.TheEndOfTime()
		o2ExpectedDeferUntil := timeutil.TheEndOfTime()
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{},
				DeferOrders: map[string]prediction.BatchOptimizeDeferOrder{
					allOrderIDs[0]: {
						DeferUntil: o1ExpectedDeferUntil.Unix(),
						T1:         o1ExpectedDeferUntil.Unix(),
					},
					allOrderIDs[1]: {
						DeferUntil: o2ExpectedDeferUntil.Unix(),
						T1:         o2ExpectedDeferUntil.Unix(),
					},
				},
			}, nil,
		)
		mocks.mockOrderRepository.EXPECT().SetPredictionDeferTime(gomock.Any(), allOrderIDs[0], gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetPredictionDeferTime(gomock.Any(), allOrderIDs[1], gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderDistributionEventManager.EXPECT().PublishSearchingOrAssigningDriver(gomock.Any(), matchOrderID("1"), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
			require.Equal(tt, "1", order.OrderID)
			require.Nil(tt, order.ActualAssigningAt)
			require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
			return nil
		})
		mocks.mockOrderDistributionEventManager.EXPECT().PublishSearchingOrAssigningDriver(gomock.Any(), matchOrderID("2"), gomock.Any()).DoAndReturn(func(_ any, order model.Order, eventTime time.Time) error {
			require.Equal(tt, "2", order.OrderID)
			require.Nil(tt, order.ActualAssigningAt)
			require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
			return nil
		})

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("happy path with batch assigning - with passed cooking time", func(tt *testing.T) {
		tt.Parallel()
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		createdAt := time.Now().Add(-15 * time.Minute)
		estimatedCookingDuration := 10 * time.Minute
		expectedCookingFinishedTime := createdAt.Add(estimatedCookingDuration)
		expectedDeferUntil := createdAt.Add(11 * time.Minute)
		expectedDeferUntilUnix := expectedDeferUntil.Unix()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)
		for i := range allOrders {
			allOrders[i].IsDeferred = true
			allOrders[i].IsThrottled = true
			allOrders[i].Options.SwitchFlow = true
			allOrders[i].Options.CanDefer = true
			allOrders[i].CreatedAt = createdAt
			allOrders[i].Prediction.DeferUntil = &expectedDeferUntil
			allOrders[i].Prediction.EstimatedCookingTimeSecond = int(estimatedCookingDuration.Seconds())
		}

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(false)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{},
				DeferOrders: map[string]prediction.BatchOptimizeDeferOrder{
					allOrderIDs[0]: {
						DeferUntil: expectedDeferUntil.Unix(),
						T1:         expectedDeferUntil.Unix(),
					},
					allOrderIDs[1]: {
						DeferUntil: expectedDeferUntil.Unix(),
						T1:         expectedDeferUntil.Unix(),
					},
				},
			}, nil,
		)
		mocks.mockOrderRepository.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), allOrderIDs[0], gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, actualAssigningAt time.Time) error {
			require.True(tt, !actualAssigningAt.Before(expectedCookingFinishedTime))
			return nil
		})
		mocks.mockOrderRepository.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), allOrderIDs[1], gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, actualAssigningAt time.Time) error {
			require.True(tt, !actualAssigningAt.Before(expectedCookingFinishedTime))
			return nil
		})
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), allOrderIDs[0], gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, req v1.UpdateActualAssigningRequest) error {
			require.True(tt, !req.ActualAssigningAt.Before(expectedCookingFinishedTime))
			return nil
		})
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), allOrderIDs[1], gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, req v1.UpdateActualAssigningRequest) error {
			require.True(tt, !req.ActualAssigningAt.Before(expectedCookingFinishedTime))
			return nil
		})
		mocks.mockOrderRepository.EXPECT().SetPredictionDeferTime(gomock.Any(), allOrderIDs[0], time.Unix(expectedDeferUntilUnix, 0), time.Unix(0, 0), gomock.Any(), time.Unix(expectedDeferUntilUnix, 0).Sub(allOrders[0].CreatedAt)).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetPredictionDeferTime(gomock.Any(), allOrderIDs[1], time.Unix(expectedDeferUntilUnix, 0), time.Unix(0, 0), gomock.Any(), time.Unix(expectedDeferUntilUnix, 0).Sub(allOrders[1].CreatedAt)).Return(nil)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("happy path with batch assigning - with checking T2 has saved to orders collection", func(tt *testing.T) {
		tt.Parallel()
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		createdAt := time.Now().Add(-15 * time.Minute)
		estimatedCookingDuration := 10 * time.Minute
		expectedCookingFinishedTime := createdAt.Add(estimatedCookingDuration)
		expectedDeferUntil := createdAt.Add(11 * time.Minute)
		expectedSecondDeferUntilUnix := createdAt.Add(22 * time.Minute).Unix()
		expectedDeferUntilUnix := expectedDeferUntil.Unix()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)
		for i := range allOrders {
			allOrders[i].IsDeferred = true
			allOrders[i].IsThrottled = true
			allOrders[i].Options.SwitchFlow = true
			allOrders[i].Options.CanDefer = true
			allOrders[i].CreatedAt = createdAt
			allOrders[i].Prediction.DeferUntil = &expectedDeferUntil
			allOrders[i].Prediction.EstimatedCookingTimeSecond = int(estimatedCookingDuration.Seconds())
		}

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)
		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(false)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{},
				DeferOrders: map[string]prediction.BatchOptimizeDeferOrder{
					allOrderIDs[0]: {
						DeferUntil: expectedDeferUntil.Unix(),
						T1:         expectedDeferUntil.Unix(),
						T2:         expectedSecondDeferUntilUnix,
					},
					allOrderIDs[1]: {
						DeferUntil: expectedDeferUntil.Unix(),
						T1:         expectedDeferUntil.Unix(),
						T2:         expectedSecondDeferUntilUnix,
					},
				},
			}, nil,
		)
		mocks.mockOrderRepository.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), allOrderIDs[0], gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, actualAssigningAt time.Time) error {
			require.True(tt, !actualAssigningAt.Before(expectedCookingFinishedTime))
			return nil
		})
		mocks.mockOrderRepository.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), allOrderIDs[1], gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, actualAssigningAt time.Time) error {
			require.True(tt, !actualAssigningAt.Before(expectedCookingFinishedTime))
			return nil
		})
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), allOrderIDs[0], gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, req v1.UpdateActualAssigningRequest) error {
			require.True(tt, !req.ActualAssigningAt.Before(expectedCookingFinishedTime))
			return nil
		})
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), allOrderIDs[1], gomock.Any()).DoAndReturn(func(_ interface{}, _ interface{}, req v1.UpdateActualAssigningRequest) error {
			require.True(tt, !req.ActualAssigningAt.Before(expectedCookingFinishedTime))
			return nil
		})
		mocks.mockOrderRepository.EXPECT().SetPredictionDeferTime(gomock.Any(), allOrderIDs[0], time.Unix(expectedDeferUntilUnix, 0), time.Unix(0, 0), time.Unix(expectedSecondDeferUntilUnix, 0), time.Unix(expectedDeferUntilUnix, 0).Sub(allOrders[0].CreatedAt)).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetPredictionDeferTime(gomock.Any(), allOrderIDs[1], time.Unix(expectedDeferUntilUnix, 0), time.Unix(0, 0), time.Unix(expectedSecondDeferUntilUnix, 0), time.Unix(expectedDeferUntilUnix, 0).Sub(allOrders[1].CreatedAt)).Return(nil)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("happy path with batch assigning - assignable order with until defer less than now", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
			createPredictedOrder("3"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
			expectEvaluateRainSituation(ctx, mocks, true, false)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		expectedDeferUntil := time.Unix(1696930624, 0)
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
					drivers[1].Driver.DriverID: {allOrderIDs[2]},
				},
				DeferOrders: map[string]prediction.BatchOptimizeDeferOrder{
					allOrderIDs[0]: {
						DeferUntil: expectedDeferUntil.Unix(),
					},
				},
			}, nil,
		)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[0]}, {OrderID: allOrderIDs[1]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.DropOffAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[0].Driver, allOrders[0], false, model.StrategyNameFleetPool)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[0].Driver, allOrders[1], false, model.StrategyNameFleetPool)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[1], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[2]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatch(tt, ctx, mocks, drivers[1].Driver, allOrders[2], false, model.StrategyNameFleetPool)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any()).AnyTimes()

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 3, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("should collect illegal when route, validate plan route has an error", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		distCfg.BatchAssignmentEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
			createPredictedOrder("3"),
			createPredictedOrder("4"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameDirectSearch,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
					drivers[1].Driver.DriverID: {allOrderIDs[2], allOrderIDs[3]},
				},
			}, nil,
		)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()

		// validate plan route failed
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[0]}, {OrderID: allOrderIDs[1]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.DropOffAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{drivers[0].Driver.DriverID}).
			Return(map[string]*model.DriverMinimal{
				drivers[0].Driver.DriverID: &drivers[0].Driver,
			}, nil)

		reason := model.B2BDistanceLimitExceeded
		illegalUntil := timeutil.TheEndOfTime()
		mocks.mockPredictionService.EXPECT().ValidatePlanRoute(gomock.Any(), gomock.Any(), gomock.InAnyOrder([]string{allOrderIDs[0], allOrderIDs[1]}), gomock.Any(), gomock.Any()).Return(service.NewValidateRouteErrRes(reason, illegalUntil), errors.New("something went wrong"))
		mocks.mockAssigmentLogRepo.EXPECT().InsertIllegalDriver(gomock.Any(), allOrderIDs[0], 0, gomock.Any())
		mocks.mockAssigmentLogRepo.EXPECT().InsertIllegalDriver(gomock.Any(), allOrderIDs[1], 0, gomock.Any())
		mocks.mockIllegalDriverRepo.EXPECT().AddIllegalDriver(gomock.Any(), allOrderIDs[0], 0, allOrders[0].ExpireAt, drivers[0].Driver.DriverID, reason, illegalUntil)
		mocks.mockIllegalDriverRepo.EXPECT().AddIllegalDriver(gomock.Any(), allOrderIDs[1], 0, allOrders[1].ExpireAt, drivers[0].Driver.DriverID, reason, illegalUntil)

		// route failed
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[1], gomock.Any()).Return(prediction.RouteResponse{}, nil, prediction.NoLegalPathFoundError)
		mocks.mockAssigmentLogRepo.EXPECT().InsertIllegalDriver(gomock.Any(), allOrderIDs[2], 0, gomock.Any())
		mocks.mockAssigmentLogRepo.EXPECT().InsertIllegalDriver(gomock.Any(), allOrderIDs[3], 0, gomock.Any())
		mocks.mockIllegalDriverRepo.EXPECT().AddIllegalDriver(gomock.Any(), allOrderIDs[2], 0, allOrders[2].ExpireAt, drivers[1].Driver.DriverID, model.NoLegalPathFound, timeutil.TheEndOfTime())
		mocks.mockIllegalDriverRepo.EXPECT().AddIllegalDriver(gomock.Any(), allOrderIDs[3], 0, allOrders[3].ExpireAt, drivers[1].Driver.DriverID, model.NoLegalPathFound, timeutil.TheEndOfTime())

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any()).Times(4)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any()).AnyTimes()

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 4, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("happy path without batch assigning - with priority round for dedicated zone riders x MP", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		zoneCoordinates := model.ZoneCoordinates{
			{
				{
					{
						100.56928074072022,
						14.353136925751102,
					},
					{
						100.56928074072022,
						14.351432311661526,
					},
					{
						100.57554638097992,
						14.351432311661526,
					},
					{
						100.57554638097992,
						14.353136925751102,
					},
					{
						100.56928074072022,
						14.353136925751102,
					},
				},
			},
		}
		zone.Geometry = model.ZoneGeometry{
			Type:        "MultiPolygon",
			Coordinates: zoneCoordinates,
		}

		mp1InDedicatedZone := createPredictedOrder("ord-1")
		mp1InDedicatedZone.Routes[0].Location = model.Location{
			Lat: 14.352398954170852,
			Lng: 100.57224189947391,
		}
		mp1InDedicatedZone.IsDalianMP = true
		mp1InDedicatedZone.Options.MpID = "mp-1"

		mp2 := createPredictedOrder("ord-2")
		mp2.IsDalianMP = true
		mp2.Options.MpID = "mp-1"
		mp2.Options.MpOrderIDs = []string{"ord-1"}

		allOrders := []model.Order{
			mp1InDedicatedZone,
			mp2,
			createPredictedOrder("ord-3"),
		}
		validOrders := allOrders
		validOrderIDs := model.OrderIDsFromList(validOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
		for _, id := range validOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		dedicatedZoneDriver := createDriver("2", model.StatusOnline, model.Options{AutoAccept: false})
		dedicatedZoneDriver.DedicatedZones = []string{"d-zone"}
		dedicatedZoneDriverWithLocation := createDriverWithLocation(dedicatedZoneDriver, model.Location{Lat: 1.002, Lng: 2}, 1000)

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			dedicatedZoneDriverWithLocation,
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameDirectSearch,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), mp1InDedicatedZone.OrderID).Return(&mp1InDedicatedZone, nil) // call for mp2
		for _, id := range validOrderIDs {
			mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(map[string]model.DedicatedZone{
				"d-zone": {
					Label:        "d-zone",
					ServiceTypes: []model.Service{model.ServiceFood},
					Geometry: model.DedicatedZoneGeometry{
						Type:        "MultiPolygon",
						Coordinates: model.DedicatedZoneCoordinates(zoneCoordinates),
					},
				},
			}, nil).AnyTimes()
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		// Priority round
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[1].Driver.DriverID: {validOrderIDs[0], validOrderIDs[1]},
				},
			}, nil,
		)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(validOrderIDs[0])).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(validOrderIDs[1])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), validOrderIDs[0], gomock.Any())
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), validOrderIDs[1], gomock.Any())

		// Normal round
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {validOrderIDs[2]},
				},
			}, nil,
		)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(validOrderIDs[2])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), validOrderIDs[2], gomock.Any())

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 3, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 2, zone.ZoneCode)
	})
	t.Run("happy path without batch assigning - without priority round for dedicated zone riders x MP (MP1 assigned)", func(tt *testing.T) {
		tt.Parallel()

		driverNormalID := "driver-normal"
		driverDedicatedID := "driver-dedicated"

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		zoneCoordinates := model.ZoneCoordinates{
			{
				{
					{
						100.56928074072022,
						14.353136925751102,
					},
					{
						100.56928074072022,
						14.351432311661526,
					},
					{
						100.57554638097992,
						14.351432311661526,
					},
					{
						100.57554638097992,
						14.353136925751102,
					},
					{
						100.56928074072022,
						14.353136925751102,
					},
				},
			},
		}
		zone.Geometry = model.ZoneGeometry{
			Type:        "MultiPolygon",
			Coordinates: zoneCoordinates,
		}

		mp1InDedicatedZone := createPredictedOrder("ord-1")
		mp1InDedicatedZone.Routes[0].Location = model.Location{
			Lat: 14.352398954170852,
			Lng: 100.57224189947391,
		}
		mp1InDedicatedZone.IsDalianMP = true
		mp1InDedicatedZone.Options.MpID = "mp-1"
		mp1InDedicatedZone.Status = model.StatusDriverMatched
		mp1InDedicatedZone.Driver = driverDedicatedID

		mp2 := createPredictedOrder("ord-2")
		mp2.IsDalianMP = true
		mp2.Options.MpID = "mp-1"
		mp2.Options.MpOrderIDs = []string{"ord-1"}

		allOrders := []model.Order{
			mp2,
			createPredictedOrder("ord-3"),
		}
		validOrders := allOrders
		validOrderIDs := model.OrderIDsFromList(validOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
		for _, id := range validOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		normalDrivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver(driverNormalID, model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		dedicatedZoneDriver := createDriver(driverDedicatedID, model.StatusOnline, model.Options{AutoAccept: false})
		dedicatedZoneDriver.DedicatedZones = []string{"d-zone"}
		mpDrivers := []service.DriverWithLocation{
			createDriverWithLocation(dedicatedZoneDriver, model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		drivers := append(normalDrivers, mpDrivers...)
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  normalDrivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return([]repository.DriverMP{{DriverID: driverDedicatedID, MPID: "mp-1"}}, nil)
		mocks.mockLocationManager.EXPECT().GetDriverWithLocationByIDs(gomock.Any(), []string{driverDedicatedID}).Return(mpDrivers, nil)

		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), mp1InDedicatedZone.OrderID).Return(&mp1InDedicatedZone, nil) // call for mp2
		for _, id := range validOrderIDs {
			mocks.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(map[string]model.DedicatedZone{
				"d-zone": {
					Label:        "d-zone",
					ServiceTypes: []model.Service{model.ServiceFood},
					Geometry: model.DedicatedZoneGeometry{
						Type:        "MultiPolygon",
						Coordinates: model.DedicatedZoneCoordinates(zoneCoordinates),
					},
				},
			}, nil).AnyTimes()
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		// Normal round
		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[1].Driver.DriverID: {validOrderIDs[0]},
				},
				DeferOrders: map[string]prediction.BatchOptimizeDeferOrder{
					validOrderIDs[1]: {},
				},
			}, nil,
		)

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))

		for _, orderID := range validOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(orderID)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), orderID, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 2, zone.ZoneCode)
	})
	t.Run("should not evaluate rain location because a driver is full-time", func(tt *testing.T) {
		tt.Parallel()

		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
			createPredictedOrder("3"),
		}
		allOrders[0].Routes[allOrders[0].GetPayAtStop()].Location = model.Location{Lat: 1.000, Lng: 1.000}
		allOrders[1].Routes[allOrders[1].GetPayAtStop()].Location = model.Location{Lat: 1.001, Lng: 1.001}
		allOrders[2].Routes[allOrders[2].GetPayAtStop()].Location = model.Location{Lat: 1.002, Lng: 1.002}

		driverLocation := model.Location{Lat: 1.002, Lng: 2}

		type OrderDetail struct {
			Order                        model.Order
			PreviousDropOffLocationOrder *model.Order
		}

		type TestCase struct {
			Title                string
			PlanRoutes           []prediction.PlanRoute
			OrderDetails         []OrderDetail
			SearchResultStrategy model.SearchRiderStrategy
		}

		testCases := []TestCase{
			{
				Title: "[p1d1][p2d2][p3d3]",
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[2].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[2].OrderID,
						ActionType: prediction.DropOffAction,
					},
				},
				OrderDetails: []OrderDetail{
					{
						Order: allOrders[0],
					},
					{
						Order:                        allOrders[1],
						PreviousDropOffLocationOrder: &allOrders[0],
					},
					{
						Order:                        allOrders[2],
						PreviousDropOffLocationOrder: &allOrders[1],
					},
				},
				SearchResultStrategy: model.StrategyNameFleetPool,
			},
			{
				Title: "[p1p2d1d2]",
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.DropOffAction,
					},
				},
				OrderDetails: []OrderDetail{
					{
						Order: allOrders[0],
					},
					{
						Order: allOrders[1],
					},
				},
				SearchResultStrategy: model.StrategyNameDirectSearch,
			},
			{
				Title: "[p1p2d1d2][p3d3]",
				PlanRoutes: []prediction.PlanRoute{
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[0].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[1].OrderID,
						ActionType: prediction.DropOffAction,
					},
					{
						OrderID:    allOrders[2].OrderID,
						ActionType: prediction.PickupAction,
					},
					{
						OrderID:    allOrders[2].OrderID,
						ActionType: prediction.DropOffAction,
					},
				},
				OrderDetails: []OrderDetail{
					{
						Order: allOrders[0],
					},
					{
						Order: allOrders[1],
					},
					{
						Order:                        allOrders[2],
						PreviousDropOffLocationOrder: &allOrders[1],
					},
				},
				SearchResultStrategy: model.StrategyNameFleetPool,
			},
		}

		for _, tc := range testCases {
			_tc := tc

			tt.Run(_tc.Title, func(ttt *testing.T) {
				ttt.Parallel()

				orderIdsSet := types.NewStringSet()
				for _, od := range _tc.OrderDetails {
					orderIdsSet.Add(od.Order.OrderID)
				}
				orderIds := orderIdsSet.PopToSlice()

				allOrders := make([]model.Order, 0)
				allOrderIDs := make([]string, 0)
				for _, od := range _tc.OrderDetails {
					allOrders = append(allOrders, od.Order)
					allOrderIDs = append(allOrderIDs, od.Order.OrderID)
				}

				autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(ttt, order.ContingencyConfig{})
				distCfg.PredictionServiceEnabled = true
				distCfg.NotifyViaSocketIOEnabled = true
				distCfg.AssignmentType = prediction.AssignmentTypeMultiple
				distCfg.MOType = prediction.MOTypeMOS4
				defer finishFn()

				ctx, wg := safe.CreateCtxWithWaitGroup()
				zone := createMinimalZone()

				mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
					ZoneCode: zone.ZoneCode,
					ThrottledDispatchDetail: model.ThrottledDispatchDetail{
						ZoneID:                    zone.ID,
						EnabledSearchRadiusOffset: false,
						SearchRadiusOffsetKM:      0,
					},
				}, nil)

				mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
				for _, id := range allOrderIDs {
					mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
				}

				driverWithLoc := createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), driverLocation, 1000)
				driverWithLoc.Driver.DriverType = crypt.NewLazyEncryptedString(string(model.DriverTypeFullTime))
				drivers := []service.DriverWithLocation{
					driverWithLoc,
				}
				mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
					Results:  drivers,
					Strategy: _tc.SearchResultStrategy,
				}, nil)
				mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
				for _, d := range drivers {
					mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
					mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
				}

				for _, id := range allOrderIDs {
					expectZoneCall(mocks)
					mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
				}
				expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
				trans := make([]model.DriverTransaction, 0)
				for _, d := range drivers {
					trans = append(trans, createDriverTransaction(d.Driver.DriverID))
				}
				mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

				mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
					prediction.BatchOptimizeResponse{
						RiderOrders: map[string][]string{
							drivers[0].Driver.DriverID: orderIds,
						},
					}, nil,
				)

				assignedOrders := make([]prediction.AssignedOrder, 0)
				for _, id := range orderIds {
					assignedOrders = append(assignedOrders, prediction.AssignedOrder{OrderID: id})
				}

				mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
					AssignedOrders: assignedOrders,
					PlanRoutes:     _tc.PlanRoutes,
				}, nil, nil)

				for _, od := range _tc.OrderDetails {
					assertAssignOrderInBatch(ttt, ctx, mocks, drivers[0].Driver, od.Order, false, _tc.SearchResultStrategy)
				}

				for _, d := range drivers {
					mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return()
				}
				for _, id := range allOrderIDs {
					mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
				}

				mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
				mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
				mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
				mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
				mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any()).AnyTimes()

				stubDefaultBehavior(mocks)

				_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
				wg.Wait()
				AssertBatchOrderMetric(ttt, mocks, float64(len(orderIds)), zone.ZoneCode)
				AssertBatchRiderMetric(ttt, mocks, 1, zone.ZoneCode)
			})
		}
	})
	t.Run("should send 1 when osrm phase is not 1 or 2", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		distCfg.DalianOSRMPhase = 999
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("ord-1"),
			createPredictedOrder("ord-2"),
			createPredictedOrder("ord-3"),
			createPredictedOrder("ord-4"),
			createPredictedOrder("ord-5"),
			createPredictedOrder("ord-6"),
			createMinimalOrder("ord-without-prediction"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)
		validOrders := allOrders[:6]
		validOrderIDs := model.OrderIDsFromList(validOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
		for _, id := range validOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("4", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("5", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("6", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		validDrivers := drivers[:len(drivers)-1]
		for _, d := range validDrivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range validOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) (prediction.BatchOptimizeResponse, error) {
				expectDalianOSRMPhaseInt64 := int64(1)
				assert.Equal(tt, expectDalianOSRMPhaseInt64, cfg.DalianOSRMPhase)
				return prediction.BatchOptimizeResponse{
					RiderOrders: map[string][]string{
						drivers[0].Driver.DriverID: {validOrderIDs[0], validOrderIDs[1]},
						drivers[1].Driver.DriverID: {validOrderIDs[2]},
						drivers[2].Driver.DriverID: {validOrderIDs[3]},
					},
				}, nil
			})

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID)).Return()
		// redistribute for unassignable

		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[4])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[4], gomock.Any())
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[5])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[5], gomock.Any())
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[6], gomock.Any())

		// test scope doesn't include assigning process.
		// batchAssign will unlock assignable orders, drivers and also redistribute all of unassigned orders
		assignableOrders := []string{validOrderIDs[0], validOrderIDs[1], validOrderIDs[2], validOrderIDs[3]}
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong")).AnyTimes()
		for _, id := range assignableOrders {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 6, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 5, zone.ZoneCode)
	})
	t.Run("should send osrm phase to dalian api", func(tt *testing.T) {
		tt.Parallel()

		expectedResult := []int64{1, 2}
		for _, v := range expectedResult {
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
			distCfg.PredictionServiceEnabled = true
			distCfg.NotifyViaSocketIOEnabled = true
			distCfg.AssignmentType = prediction.AssignmentTypeMultiple
			distCfg.MOType = prediction.MOTypeMOS4
			distCfg.DalianOSRMPhase = v
			defer finishFn()

			ctx, wg := safe.CreateCtxWithWaitGroup()
			zone := createMinimalZone()
			allOrders := []model.Order{
				createPredictedOrder("ord-1"),
				createPredictedOrder("ord-2"),
				createPredictedOrder("ord-3"),
				createPredictedOrder("ord-4"),
				createPredictedOrder("ord-5"),
				createPredictedOrder("ord-6"),
				createMinimalOrder("ord-without-prediction"),
			}
			allOrderIDs := model.OrderIDsFromList(allOrders)
			validOrders := allOrders[:6]
			validOrderIDs := model.OrderIDsFromList(validOrders)

			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
				ZoneCode: zone.ZoneCode,
				ThrottledDispatchDetail: model.ThrottledDispatchDetail{
					ZoneID:                    zone.ID,
					EnabledSearchRadiusOffset: false,
					SearchRadiusOffsetKM:      0,
				},
			}, nil)

			mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
			for _, id := range validOrderIDs {
				mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
			}

			drivers := []service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("4", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("5", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("6", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			}
			mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
				Results:  drivers,
				Strategy: model.StrategyNameFleetPool,
			}, nil)
			mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
			// prefilter
			validDrivers := drivers[:len(drivers)-1]
			for _, d := range validDrivers {
				mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
			}

			// called in scorer
			for _, id := range validOrderIDs {
				expectZoneCall(mocks)
				mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
			}
			expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
			trans := make([]model.DriverTransaction, 0)
			for _, d := range drivers {
				trans = append(trans, createDriverTransaction(d.Driver.DriverID))
			}
			mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

			mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) (prediction.BatchOptimizeResponse, error) {
					assert.Equal(tt, v, cfg.DalianOSRMPhase)
					return prediction.BatchOptimizeResponse{
						RiderOrders: map[string][]string{
							drivers[0].Driver.DriverID: {validOrderIDs[0], validOrderIDs[1]},
							drivers[1].Driver.DriverID: {validOrderIDs[2]},
							drivers[2].Driver.DriverID: {validOrderIDs[3]},
						},
					}, nil
				})

			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID)).Return()
			// redistribute for unassignable

			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[4])).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[4], gomock.Any())
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[5])).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[5], gomock.Any())
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[6], gomock.Any())

			// test scope doesn't include assigning process.
			// batchAssign will unlock assignable orders, drivers and also redistribute all of unassigned orders
			assignableOrders := []string{validOrderIDs[0], validOrderIDs[1], validOrderIDs[2], validOrderIDs[3]}
			mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(prediction.RouteResponse{}, nil, errors.New("something went wrong")).AnyTimes()
			for _, id := range assignableOrders {
				mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
				mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
			}

			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
			wg.Wait()

			AssertBatchOrderMetric(tt, mocks, 6, zone.ZoneCode)
			AssertBatchRiderMetric(tt, mocks, 5, zone.ZoneCode)
		}
	})
	t.Run("should send mo_aggressive_level from EP if it is in test group", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(&model.SwitchbackExperiments{
			Params: model.SwitchbackExperimentsParams{
				MOAggressiveLevel: "L8",
			},
		}, nil)
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("ord-1"),
			createPredictedOrder("ord-2"),
			createPredictedOrder("ord-3"),
			createPredictedOrder("ord-4"),
			createPredictedOrder("ord-5"),
			createPredictedOrder("ord-6"),
			createMinimalOrder("ord-without-prediction"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)
		validOrders := allOrders[:6]
		validOrderIDs := model.OrderIDsFromList(validOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
		for _, id := range validOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("4", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("5", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("6", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		validDrivers := drivers[:len(drivers)-1]
		for _, d := range validDrivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range validOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) (prediction.BatchOptimizeResponse, error) {
				assert.Equal(tt, "L8", cfg.Level)
				return prediction.BatchOptimizeResponse{
					RiderOrders: map[string][]string{
						drivers[0].Driver.DriverID: {validOrderIDs[0], validOrderIDs[1]},
						drivers[1].Driver.DriverID: {validOrderIDs[2]},
						drivers[2].Driver.DriverID: {validOrderIDs[3]},
					},
				}, nil
			})

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID)).Return()
		// redistribute for unassignable
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[4])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[4], gomock.Any())
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[5])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[5], gomock.Any())
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[6], gomock.Any())

		// test scope doesn't include assigning process.
		// batchAssign will unlock assignable orders, drivers and also redistribute all of unassigned orders
		assignableOrders := []string{validOrderIDs[0], validOrderIDs[1], validOrderIDs[2], validOrderIDs[3]}
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
				assert.Equal(tt, "L8", setting.Level)
				return prediction.RouteResponse{}, nil, errors.New("something went wrong")
			}).AnyTimes()
		for _, id := range assignableOrders {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 6, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 5, zone.ZoneCode)
	})
	t.Run("should send rush_mode from EP if it is in test group", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(tt, order.ContingencyConfig{})
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(&model.SwitchbackExperiments{
			Params: model.SwitchbackExperimentsParams{
				RushMode: "HARD",
			},
		}, nil)
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("ord-1"),
			createPredictedOrder("ord-2"),
			createPredictedOrder("ord-3"),
			createPredictedOrder("ord-4"),
			createPredictedOrder("ord-5"),
			createPredictedOrder("ord-6"),
			createMinimalOrder("ord-without-prediction"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)
		validOrders := allOrders[:6]
		validOrderIDs := model.OrderIDsFromList(validOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), model.OrderIDsFromList(allOrders), gomock.Any()).Return(validOrders, nil)
		for _, id := range validOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("4", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("5", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("6", model.StatusOffline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		validDrivers := drivers[:len(drivers)-1]
		for _, d := range validDrivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range validOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, validOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, riders []service.DriverWithLocation, cfg service.OptimizeSetting) (prediction.BatchOptimizeResponse, error) {
				assert.Equal(tt, "HARD", string(cfg.RushMode))
				return prediction.BatchOptimizeResponse{
					RiderOrders: map[string][]string{
						drivers[0].Driver.DriverID: {validOrderIDs[0], validOrderIDs[1]},
						drivers[1].Driver.DriverID: {validOrderIDs[2]},
						drivers[2].Driver.DriverID: {validOrderIDs[3]},
					},
				}, nil
			})

		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[1].Driver.DriverID)).Return()
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[2].Driver.DriverID)).Return()
		// redistribute for unassignable

		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[4])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[4], gomock.Any())
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(allOrderIDs[5])).Return()
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[5], gomock.Any())
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[6], gomock.Any())

		// test scope doesn't include assigning process.
		// batchAssign will unlock assignable orders, drivers and also redistribute all of unassigned orders
		assignableOrders := []string{validOrderIDs[0], validOrderIDs[1], validOrderIDs[2], validOrderIDs[3]}
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ interface{}, _ interface{}, _ interface{}, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
				assert.Equal(tt, "HARD", string(setting.RushMode))
				return prediction.RouteResponse{}, nil, errors.New("something went wrong")
			}).AnyTimes()
		for _, id := range assignableOrders {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), id, gomock.Any())
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()

		AssertBatchOrderMetric(tt, mocks, 6, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 5, zone.ZoneCode)
	})

	t.Run("batch assigning - enabled assign API - happy path", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		distCfg.BatchAssignmentEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
			createPredictedOrder("3"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)
		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(true).MaxTimes(1)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
					drivers[1].Driver.DriverID: {allOrderIDs[2]},
				},
			}, nil,
		)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[0]}, {OrderID: allOrderIDs[1]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.DropOffAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatchWithAssignAPI(tt, mocks, drivers[0].Driver, []string{allOrderIDs[0], allOrderIDs[1]}, nil)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[1], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[2]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[2],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatchWithAssignAPI(tt, mocks, drivers[1].Driver, []string{allOrderIDs[2]}, nil)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 3, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
	t.Run("batch assigning - enabled assign API - error from API", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		distCfg.BatchAssignmentEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)
		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(true).MaxTimes(1)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
				},
			}, nil,
		)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[0]}, {OrderID: allOrderIDs[1]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.DropOffAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)
		assertAssignOrderInBatchWithAssignAPI(tt, mocks, drivers[0].Driver, []string{allOrderIDs[0], allOrderIDs[1]}, errors.New("something want wront"))
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[0], nil).Return(nil)
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[1], nil).Return(nil)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})

	t.Run("batch assigning - enabled assign API - error from route", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		distCfg.BatchAssignmentEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
		}
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)
		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(true).MaxTimes(1)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameFleetPool,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
				},
			}, nil,
		)

		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).
			Return(prediction.RouteResponse{}, nil, errors.New("something went wrong"))
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[0], nil).Return(nil)
		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), allOrderIDs[1], nil).Return(nil)

		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})

	t.Run("food/mart orders should be redistributed when found that delivery location is changed while distributing", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableBatchAssignment: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeMultiple
		distCfg.MOType = prediction.MOTypeMOS4
		distCfg.BatchAssignmentEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		zone := createMinimalZone()
		allOrders := []model.Order{
			createPredictedOrder("1"),
			createPredictedOrder("2"),
		}
		allOrders[1].ServiceType = model.ServiceMart
		allOrderIDs := model.OrderIDsFromList(allOrders)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindByZoneID(gomock.Any(), zone.ID).Return(model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode: zone.ZoneCode,
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{
				ZoneID:                    zone.ID,
				EnabledSearchRadiusOffset: false,
				SearchRadiusOffsetKM:      0,
			},
		}, nil)
		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(false)

		mocks.mockOrderRepository.EXPECT().GetActiveAssigningOrders(gomock.Any(), allOrderIDs, gomock.Any()).Return(allOrders, nil)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.OrderAutoAssignState(id), gomock.Any(), gomock.Any()).Return(true)
		}

		drivers := []service.DriverWithLocation{
			createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
			createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
		}
		mocks.mockLocationManager.EXPECT().SearchDriversInMultiPolygon(gomock.Any(), gomock.Any(), zone.Geometry.Coordinates, gomock.Any()).Return(service.SearchResult{
			Results:  drivers,
			Strategy: model.StrategyNameDirectSearch,
		}, nil)
		mocks.mockOrderRepository.EXPECT().GetDriversOfOtherMPs(gomock.Any(), gomock.Any()).Return(nil, nil)
		// prefilter
		for _, d := range drivers {
			mocks.mockLocker.EXPECT().GetState(gomock.Any(), locker.DriverAutoAssignState(d.Driver.DriverID)).Return("")
		}

		// called in scorer
		for _, id := range allOrderIDs {
			expectZoneCall(mocks)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), id).Return(types.NewStringSet(), nil)
		}
		expectGetAssignedDriversInBatch(ctx, mocks, allOrderIDs)
		trans := make([]model.DriverTransaction, 0)
		for _, d := range drivers {
			trans = append(trans, createDriverTransaction(d.Driver.DriverID))
		}
		mocks.mockDriverTransaction.EXPECT().GetMany(gomock.Any(), gomock.Any()).Return(trans, nil).AnyTimes()

		mocks.mockPredictionService.EXPECT().BatchOptimize(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			prediction.BatchOptimizeResponse{
				RiderOrders: map[string][]string{
					drivers[0].Driver.DriverID: {allOrderIDs[0], allOrderIDs[1]},
				},
			}, nil,
		)
		mocks.mockLocker.EXPECT().SetState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID), gomock.Any(), gomock.Any()).Return(true)
		mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.DriverAutoAssignState(drivers[0].Driver.DriverID)).Return()

		// validate plan route failed
		mocks.mockPredictionService.EXPECT().Route(gomock.Any(), gomock.Any(), drivers[0], gomock.Any()).Return(prediction.RouteResponse{
			AssignedOrders: []prediction.AssignedOrder{{OrderID: allOrderIDs[0]}, {OrderID: allOrderIDs[1]}},
			PlanRoutes: []prediction.PlanRoute{
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.PickupAction,
				},
				{
					OrderID:    allOrderIDs[0],
					ActionType: prediction.DropOffAction,
				},
				{
					OrderID:    allOrderIDs[1],
					ActionType: prediction.DropOffAction,
				},
			},
		}, nil, nil)

		assertPrepareAssignmentLogOpt(ctx, mocks, drivers[0].Driver, allOrders)

		mocks.mockOrderRepository.EXPECT().
			GetMany(gomock.Any(), gomock.InAnyOrder([]string{allOrders[0].OrderID, allOrders[1].OrderID}), gomock.Any()).
			DoAndReturn(func(ctx context.Context, orderIDs []string, opts ...repository.Option) ([]model.Order, error) {
				martOrderDeliveryLocationChanged := allOrders[1]
				martOrderDeliveryLocationChanged.LastDeliveryLocationUpdatedAt = types.NewTime(time.Now())
				return []model.Order{allOrders[0], martOrderDeliveryLocationChanged}, nil
			})

		mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		for _, id := range allOrderIDs {
			mocks.mockLocker.EXPECT().RemoveState(gomock.Any(), locker.OrderAutoAssignState(id)).Return()
		}

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().DoneBatch(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any()).AnyTimes()
		_ = autoAssignOrderDistributor.RunBatchTask(ctx, model.OrderIDsFromList(allOrders), zone, &distCfg, model.AreaDistributionConfig{}, wg)
		wg.Wait()
		AssertBatchOrderMetric(tt, mocks, 2, zone.ZoneCode)
		AssertBatchRiderMetric(tt, mocks, 3, zone.ZoneCode)
	})
}

func TestAutoAssign_ThrottledDeferredWithPrediction(t *testing.T) {
	t.Parallel()
	t.Run("should throttle defer order correctly", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			BatchDistributeTimeoutInSeconds:      60,
			DeferBatchDistributeTimeoutInSeconds: 60,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.DeferredDispatchFeatureEnabled = true
		defer finishFn()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mocks.mockOrderDistributionEventManager = mock_service.NewMockOrderDistributionEventManager(ctrl)
		autoAssignOrderDistributor.AutoAssignOrderDistributorDeps.OrderDistributionEventManager = mocks.mockOrderDistributionEventManager

		now := timeutil.BangkokNow()
		expectedDeferUntil := now.Add(5 * time.Minute)
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = &model.PredictionFeatures{
			EstimatedCookingTimeSecond: 2400, // 40 minutes
			DeferUntil:                 &expectedDeferUntil,
		}
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): now,
		}
		o.ExpireAt = time.Date(2023, 05, 25, 10, 0, 0, 0, timeutil.BangkokLocation())
		o.Options.CanDefer = true
		o.Options.SwitchFlow = true
		expectedExpireAt := o.ExpireAt.Add(2400 * time.Second)
		o.CreatedAt = now

		expectZoneCall(mocks)
		expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		zoneID := primitive.NewObjectID()
		mocks.mockThrottledDispatchDetailRepository.EXPECT().
			FindOneFromOrder(ctx, gomock.Any(), gomock.Any()).
			Return(&model.ThrottledDispatchDetailWithZoneCode{
				ZoneCode:                "BKK-THROTTLE",
				ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID},
			}, nil)

		mocks.mockOrderRepository.EXPECT().SetThrottlingDeferredOrder(gomock.Any(), o.OrderID, expectedExpireAt, time.Duration(0)).Return(nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertDeferredOrder(gomock.Any(), gomock.Any(), zoneID, expectedDeferUntil).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
	t.Run("should throttle defer order correctly if throttle round is greater or equal to 1", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			BatchDistributeTimeoutInSeconds:      60,
			DeferBatchDistributeTimeoutInSeconds: 60,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.DeferredDispatchFeatureEnabled = true
		defer finishFn()

		now := timeutil.BangkokNow()
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		expectedDeferUntil := now.Add(5 * time.Minute)
		o.Prediction = &model.PredictionFeatures{
			EstimatedCookingTimeSecond: 2400, // 40 minutes
			DeferUntil:                 &expectedDeferUntil,
		}
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): now,
		}
		o.ExpireAt = time.Date(2023, 05, 25, 10, 40, 0, 0, timeutil.BangkokLocation())
		o.Options.CanDefer = true
		o.Options.SwitchFlow = true
		o.CreatedAt = now
		o.IsDeferred = true
		o.IsThrottled = true
		o.ThrottledRound = 1

		expectZoneCall(mocks)
		expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		zoneID := primitive.NewObjectID()
		mocks.mockThrottledDispatchDetailRepository.EXPECT().
			FindOneFromOrder(ctx, gomock.Any(), gomock.Any()).
			Return(&model.ThrottledDispatchDetailWithZoneCode{
				ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID}, ZoneCode: "BKK-THROTTLE",
			},
				nil,
			)
		mocks.mockOrderRepository.EXPECT().SetThrottlingDeferredOrder(gomock.Any(), o.OrderID, o.ExpireAt, time.Duration(0)).Return(nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertDeferredOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
	t.Run("should throttle defer order correctly if throttle round is greater or equal to 5", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			BatchDistributeTimeoutInSeconds:      60,
			DeferBatchDistributeTimeoutInSeconds: 60,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.DeferredDispatchFeatureEnabled = true
		defer finishFn()

		now := timeutil.BangkokNow()
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		expectedDeferUntil := now.Add(5 * time.Minute)
		o.Prediction = &model.PredictionFeatures{
			EstimatedCookingTimeSecond: 2400, // 40 minutes
			DeferUntil:                 &expectedDeferUntil,
		}
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): now,
		}
		o.ExpireAt = time.Date(2023, 05, 25, 10, 40, 0, 0, timeutil.BangkokLocation())
		o.Options.CanDefer = true
		o.Options.SwitchFlow = true
		o.CreatedAt = now
		o.IsDeferred = true
		o.IsThrottled = true
		o.ThrottledRound = 5

		expectZoneCall(mocks)
		expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		zoneID := primitive.NewObjectID()
		mocks.mockThrottledDispatchDetailRepository.EXPECT().
			FindOneFromOrder(ctx, gomock.Any(), gomock.Any()).
			Return(&model.ThrottledDispatchDetailWithZoneCode{
				ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID}, ZoneCode: "BKK-THROTTLE",
			},
				nil,
			)
		mocks.mockOrderRepository.EXPECT().SetThrottlingDeferredOrder(gomock.Any(), o.OrderID, o.ExpireAt, time.Duration(0)).Return(nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertDeferredOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
	t.Run("should fallback to defer if throttle defer db config is disable", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			DisableThrottledDispatch: false,
			DisableThrottledDeferred: true,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantBlacklist = []string{"otherresid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		now := timeutil.BangkokNow()
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		expectedDeferUntil := now.Add(5 * time.Minute)
		o.Routes[0].ID = "resid"
		o.Prediction = &model.PredictionFeatures{
			EstimatedCookingTimeSecond: 2400, // 40 minutes
			DeferUntil:                 &expectedDeferUntil,
		}
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): now,
		}
		o.ExpireAt = time.Date(2023, 05, 25, 10, 0, 0, 0, timeutil.BangkokLocation())
		o.Options.CanDefer = true
		o.Options.SwitchFlow = true
		o.CreatedAt = now
		expectedExpireAt := time.Date(2023, 05, 25, 10, 25, 0, 0, timeutil.BangkokLocation())
		expectedDeferDuration := 25 * time.Minute

		expectZoneCall(mocks)
		expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		zoneID := primitive.NewObjectID()
		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledDeferStandaloneTransitionToSingle.Name, gomock.Any()).Return(false).MinTimes(1)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID}, ZoneCode: "BKK-THROTTLE"}, nil)
		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockDeferredOrderRepository.EXPECT().IsUnprocessedExist(gomock.Any(), gomock.Any()).Return(false, nil)
		mocks.mockDeferredOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), model.DeferredOrderOption{
			Region:                      o.DistributeRegions.DefaultRegion(),
			DrivingDurationConfigMinute: 10,
			BufferDurationConfigMinute:  5,
			DeferDurationMinute:         25,
		})
		mocks.mockOrderRepository.EXPECT().SetDeferredOrder(gomock.Any(), o.OrderID, expectedExpireAt, expectedDeferDuration).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
	t.Run("should fallback to single optimize if throttle defer order timeout", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			BatchDistributeTimeoutInSeconds:      60,
			DeferBatchDistributeTimeoutInSeconds: 60,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.DeferredDispatchFeatureEnabled = true
		defer finishFn()

		now := timeutil.BangkokNow()
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		expectedDeferUntil := now.Add(5 * time.Minute)
		o.Routes[0].ID = "resid"
		o.Prediction = &model.PredictionFeatures{
			EstimatedCookingTimeSecond: 2400, // 40 minutes
			DeferUntil:                 &expectedDeferUntil,
		}
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): timeutil.BangkokNow(),
		}
		o.ExpireAt = time.Date(2023, 05, 25, 10, 40, 0, 0, timeutil.BangkokLocation())
		o.IsDeferred = true
		o.Options.CanDefer = true
		o.Options.SwitchFlow = true
		o.CreatedAt = timeutil.BangkokNow().Add(-2500 * time.Second)
		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())
		expectLockOrder(ctx, mocks, o.OrderID, true)

		zoneID := primitive.NewObjectID()

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID}, ZoneCode: "BKK-THROTTLE"}, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
}

func TestAutoAssign_DalianMP(t *testing.T) {
	t.Parallel()
	mpID := "MP-1"

	newNormalMP1 := func() model.Order {
		o := createMinimalOrder("LMF-1")
		o.Routes[0].ID = "resid"
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): timeutil.BangkokNow(),
		}
		o.IsDalianMP = true
		o.Options.MpID = mpID
		o.Options.LockDuration = time.Duration(1000)
		return o
	}
	newDeferMP1 := func() model.Order {
		o := createMinimalOrder("LMF-1")
		o.Routes[0].ID = "resid"
		o.Prediction = &model.PredictionFeatures{
			EstimatedCookingTimeSecond:     2400, // 40 minutes
			EstimatedUserWaitingTimeSecond: 2500,
		}
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): timeutil.BangkokNow(),
		}
		o.Options.CanDefer = true
		o.Options.SwitchFlow = true
		o.IsDalianMP = true
		o.Options.MpID = "MP-1"
		o.Options.LockDuration = time.Duration(1000)
		mpDeferUntil := timeutil.TheEndOfTime()
		o.Options.MpDeferUntil = &mpDeferUntil
		o.CreatedAt = timeutil.BangkokNow()
		return o
	}
	newNormalMP2 := func() model.Order {
		o := createMinimalOrder("LMF-2")
		o.Routes[0].ID = "resid"
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): timeutil.BangkokNow(),
		}
		o.IsDalianMP = true
		o.Options.MpID = mpID
		o.Options.MpOrderIDs = []string{"LMF-1"}
		return o
	}
	newDeferMP2 := func() model.Order {
		o := createMinimalOrder("LMF-2")
		now := timeutil.BangkokNow()
		o.Routes[0].ID = "resid"
		o.Prediction = &model.PredictionFeatures{
			EstimatedCookingTimeSecond:     2400, // 40 minutes
			EstimatedUserWaitingTimeSecond: 2500,
		}
		o.History = map[string]time.Time{
			string(model.StatusAssigningDriver): now,
		}
		o.Options.CanDefer = true
		o.Options.SwitchFlow = true
		o.IsDalianMP = true
		o.Options.MpID = "MP-1"
		o.Options.MpOrderIDs = []string{"LMF-1"}
		mpDeferUntil := timeutil.TheEndOfTime()
		o.Options.MpDeferUntil = &mpDeferUntil
		o.CreatedAt = now
		return o
	}

	t.Run("should throttle normal-MP1 order correctly", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{BatchDistributeTimeoutInSeconds: 60})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		o := newNormalMP1()

		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		expectZoneCall(mocks)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, o.DistributeRegions.DefaultRegion())
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectPredictCall(ctx, mocks, tt)

		zoneID := primitive.NewObjectID()
		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID}, ZoneCode: "BKK-THROTTLE"}, nil)
		mocks.mockThrottledOrderRepository.EXPECT().UpdateMpShouldPickupAt(gomock.Any(), mpID, gomock.Any()).Return(nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetThrottledOrder(gomock.Any(), o.OrderID, gomock.Any(), gomock.Any()).Return(nil)

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, o.ExpireAt, o.Routes[0].Location, o.DistributeRegions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})

	t.Run("should throttle defer-MP1 order correctly", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			BatchDistributeTimeoutInSeconds:      60,
			DeferBatchDistributeTimeoutInSeconds: 60,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.DeferredDispatchFeatureEnabled = true
		defer finishFn()

		// ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		ctx, wg := safe.CreateCtxWithWaitGroup()
		o := newDeferMP1()
		expectedExpireAt := o.ExpireAt.Add(2400 * time.Second)
		expectedDeferDuration := o.Options.MpDeferUntil.Sub(o.CreatedAt)

		expectZoneCall(mocks)
		expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o.OrderID, 0, o.DistributeRegions.DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		zoneID := primitive.NewObjectID()
		mocks.mockThrottledDispatchDetailRepository.EXPECT().
			FindOneFromOrder(ctx, gomock.Any(), gomock.Any()).
			Return(&model.ThrottledDispatchDetailWithZoneCode{
				ZoneCode:                "BKK-THROTTLE",
				ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID},
			}, nil)

		mocks.mockOrderRepository.EXPECT().GetOtherActiveMP(gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound)
		mocks.mockThrottledOrderRepository.EXPECT().InsertDeferredOrder(gomock.Any(), gomock.Any(), zoneID, *o.Options.MpDeferUntil).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetThrottlingDeferredOrder(gomock.Any(), o.OrderID, expectedExpireAt, expectedDeferDuration).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, o.ExpireAt, o.Routes[0].Location, o.DistributeRegions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})

	t.Run("should throttle defer-MP1 order correctly with user preference", func(tt *testing.T) {
		tt.Parallel()

		tests := []struct {
			name                  string
			skipMpDefer           bool
			shouldSetMpDeferUntil bool
		}{
			{
				name:                  "MpDeferUntil not NULL & SkipMpDefer = FALSE",
				shouldSetMpDeferUntil: true,
				skipMpDefer:           false,
			},
			{
				name:                  "MpDeferUntil is NULL & SkipMpDefer = FALSE",
				shouldSetMpDeferUntil: false,
				skipMpDefer:           false,
			},
			{
				name:                  "MpDeferUntil is NULL & SkipMpDefer = FALSE",
				shouldSetMpDeferUntil: false,
				skipMpDefer:           false,
			},
			{
				name:                  "MpDeferUntil not NULL & SkipMpDefer = TRUE",
				shouldSetMpDeferUntil: true,
				skipMpDefer:           true,
			},
		}

		for _, test := range tests {
			tt.Run(test.name, func(ttt *testing.T) {
				autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(ttt, dispatcherconfig.AutoAssignDbConfig{
					BatchDistributeTimeoutInSeconds:      60,
					DeferBatchDistributeTimeoutInSeconds: 60,
				})
				distCfg.PredictionServiceEnabled = true
				distCfg.AssignmentType = prediction.AssignmentTypeSingle
				distCfg.NotifyViaSocketIOEnabled = true
				distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
				distCfg.PredictionWhitelist = []string{"2", "3", "U"}
				distCfg.PredictionBlacklist = []string{"1", "2"}
				distCfg.DeferredDispatchFeatureEnabled = true
				defer finishFn()

				ctx := context.Background()
				o := newDeferMP1()

				expectedDeferDuration := o.Options.MpDeferUntil.Sub(o.CreatedAt)
				if !test.shouldSetMpDeferUntil {
					o.Options.MpDeferUntil = nil
					expectedDeferDuration = 0
				}
				if test.skipMpDefer {
					o.Options.SkipMpDefer = true
					expectedDeferDuration = 0
				}

				expectedExpireAt := o.ExpireAt.Add(2400 * time.Second)

				expectZoneCall(mocks)
				expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o.OrderID, 0, o.DistributeRegions.DefaultRegion())
				mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

				zoneID := primitive.NewObjectID()
				mocks.mockThrottledDispatchDetailRepository.EXPECT().
					FindOneFromOrder(ctx, gomock.Any(), gomock.Any()).
					Return(&model.ThrottledDispatchDetailWithZoneCode{
						ZoneCode:                "BKK-THROTTLE",
						ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID},
					}, nil)

				mocks.mockOrderRepository.EXPECT().GetOtherActiveMP(gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound)
				mocks.mockThrottledOrderRepository.EXPECT().
					InsertDeferredOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).
					DoAndReturn(func(_ context.Context, _ model.Order, _ primitive.ObjectID, shouldPickupAt time.Time) error {
						if test.shouldSetMpDeferUntil && !test.skipMpDefer {
							require.Equal(ttt, *o.Options.MpDeferUntil, shouldPickupAt)
						} else {
							require.GreaterOrEqual(ttt, time.Now(), shouldPickupAt)
						}
						return nil
					})
				mocks.mockOrderRepository.EXPECT().SetThrottlingDeferredOrder(gomock.Any(), o.OrderID, expectedExpireAt, expectedDeferDuration).Return(nil)

				mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

				stubDefaultBehavior(mocks)

				_ = autoAssignOrderDistributor.RunTask(ctx, &o, o.ExpireAt, o.Routes[0].Location, o.DistributeRegions, &distCfg, model.AreaDistributionConfig{
					NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
				})
				autoAssignOrderDistributor.Stop()
			})
		}

	})

	t.Run("should throttle normal-MP2 when MP1 already exists correctly", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{BatchDistributeTimeoutInSeconds: 60})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		o := newNormalMP2()

		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		expectZoneCall(mocks)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0])
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, o.DistributeRegions.DefaultRegion())
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectPredictCall(ctx, mocks, tt)

		zoneID := primitive.NewObjectID()
		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(nil, nil)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID},
			ZoneCode:                "BKK-THROTTLE",
		}, nil)
		mocks.mockThrottledOrderRepository.EXPECT().UpdateMpShouldPickupAt(gomock.Any(), mpID, gomock.Any()).Return(nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetThrottledOrder(gomock.Any(), o.OrderID, gomock.Any(), gomock.Any()).Return(nil)

		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, o.ExpireAt, o.Routes[0].Location, o.DistributeRegions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})

	t.Run("should throttle defer-MP2 when normal-MP1 order correctly", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			BatchDistributeTimeoutInSeconds:      60,
			DeferBatchDistributeTimeoutInSeconds: 60,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.DeferredDispatchFeatureEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		o1 := newNormalMP1()
		o2 := newDeferMP2()
		expectedExpireAt := o2.ExpireAt.Add(time.Duration(o2.Prediction.EstimatedCookingTimeSecond) * time.Second)

		expectZoneCall(mocks)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o2.Options.MpOrderIDs[0])
		expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o2.OrderID, 0, o2.DistributeRegions.DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		zoneID := primitive.NewObjectID()
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o1.OrderID).Return(nil, nil)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode:                "BKK-THROTTLE",
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID},
		}, nil)

		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockOrderRepository.EXPECT().GetOtherActiveMP(gomock.Any(), gomock.Any()).Return(&o1, nil)
		mocks.mockThrottledOrderRepository.EXPECT().UpdateMpShouldPickupAt(gomock.Any(), mpID, gomock.Any()).Return(nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertDeferredOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetThrottlingDeferredOrder(gomock.Any(), o2.OrderID, expectedExpireAt, time.Duration(0)).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o2, o2.ExpireAt, o2.Routes[0].Location, o2.DistributeRegions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})

	t.Run("should throttle defer-MP2 when defer-MP1 order correctly (doesn't have mpGroupDeferUntil)", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			BatchDistributeTimeoutInSeconds:      60,
			DeferBatchDistributeTimeoutInSeconds: 60,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.DeferredDispatchFeatureEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		o1 := newDeferMP1()
		o2 := newDeferMP2()
		o1.Prediction.EstimatedCookingTimeSecond = 2600
		o2.Prediction.EstimatedCookingTimeSecond = 2400
		expectedExpireAt := o1.ExpireAt.Add(time.Duration(o1.Prediction.EstimatedCookingTimeSecond) * time.Second)

		expectZoneCall(mocks)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o2.Options.MpOrderIDs[0])
		expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o2.OrderID, 0, o2.DistributeRegions.DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		zoneID := primitive.NewObjectID()
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o1.OrderID).Return(nil, nil)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode:                "BKK-THROTTLE",
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID},
		}, nil)

		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockOrderRepository.EXPECT().GetOtherActiveMP(gomock.Any(), gomock.Any()).Return(&o1, nil)
		mocks.mockThrottledOrderRepository.EXPECT().UpdateMpShouldPickupAt(gomock.Any(), mpID, gomock.Any()).Return(nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertDeferredOrder(gomock.Any(), gomock.Any(), zoneID, gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetThrottlingDeferredOrder(gomock.Any(), o2.OrderID, expectedExpireAt, time.Duration(0)).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o2, o2.ExpireAt, o2.Routes[0].Location, o2.DistributeRegions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})

	t.Run("should throttle defer-MP2 when defer-MP1 order correctly (have mpGroupDeferUntil)", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
			BatchDistributeTimeoutInSeconds:      60,
			DeferBatchDistributeTimeoutInSeconds: 60,
		})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		distCfg.DeferredDispatchFeatureEnabled = true
		defer finishFn()

		ctx, wg := safe.CreateCtxWithWaitGroup()
		o1 := newDeferMP1()
		o2 := newDeferMP2()
		mpGroupDeferUntil := o1.CreatedAt.Add(10 * time.Minute)
		o1.Prediction.EstimatedCookingTimeSecond = 2600
		o2.Prediction.EstimatedCookingTimeSecond = 2400
		o1.Prediction.MpGroupDeferUntil = &mpGroupDeferUntil
		o2.Prediction.MpGroupDeferUntil = &mpGroupDeferUntil
		expectedExpireAt := o1.ExpireAt.Add(time.Duration(o1.Prediction.EstimatedCookingTimeSecond) * time.Second)

		expectZoneCall(mocks)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o2.Options.MpOrderIDs[0])
		expectAssignmentLogCreatedWithoutActualAssigningAt(ctx, mocks, o2.OrderID, 0, o2.DistributeRegions.DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		zoneID := primitive.NewObjectID()
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o1.OrderID).Return(nil, nil)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.ThrottledDispatchDetailWithZoneCode{
			ZoneCode:                "BKK-THROTTLE",
			ThrottledDispatchDetail: model.ThrottledDispatchDetail{ZoneID: zoneID},
		}, nil)

		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockOrderRepository.EXPECT().GetOtherActiveMP(gomock.Any(), gomock.Any()).Return(&o1, nil)
		mocks.mockThrottledOrderRepository.EXPECT().UpdateMpShouldPickupAt(gomock.Any(), mpID, mpGroupDeferUntil).Return(nil)
		mocks.mockThrottledOrderRepository.EXPECT().InsertDeferredOrder(gomock.Any(), gomock.Any(), zoneID, mpGroupDeferUntil).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetThrottlingDeferredOrder(gomock.Any(), o2.OrderID, expectedExpireAt, time.Duration(0)).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o2, o2.ExpireAt, o2.Routes[0].Location, o2.DistributeRegions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})
		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
}

func TestAutoAssign_DistributeMultiplePickupOrder(t *testing.T) {
	t.Parallel()
	const (
		lockDuration = 10 * time.Minute
		driverID     = "driver-key"
	)

	modifyDistCfgForFallbackTests := func(distCfg model.AutoAssignDistribution) model.AutoAssignDistribution {
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		return distCfg
	}
	expectNormalFlow := func(
		ctx context.Context,
		tt *testing.T,
		autoAssignOrderDistributor *distribution.AutoAssignOrderDistributor,
		mocks *mocks,
		distCfg model.AutoAssignDistribution,
		o model.Order,
		businessLocation model.Location,
		regions []model.RegionCode,
		round int,
		searchRiderStrategy model.SearchRiderStrategy,
	) {
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.002, Lng: 2}, 1000),
				createDriverWithLocation(createDriver("2", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.001, Lng: 2}, 600),
				createDriverWithLocation(createDriver("3", model.StatusAssigned, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createFraudDriver("U", model.StatusOnline, model.Options{AutoAccept: false}), model.Location{Lat: 1.000, Lng: 2}, 0),
			},
			searchRiderStrategy,
		)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)
		initDriverStatistics(ctx, mocks, []string{"1", "2", "3", "U"})
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("U"))
		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

		// no predict call since already predicted
		expectOptimizeCall(tt, ctx, mocks, []string{"1", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "3")

		expectNoAssignedState(ctx, mocks, "3", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversQueue(ctx, mocks, round, o.OrderID, "3", 1, searchRiderStrategy, model.DistributeRegions(regions).DefaultRegion())
		expectLockDriver(ctx, mocks, "3")
		expectUnlockDriver(ctx, mocks, "3")
		expectNotify(tt, ctx, mocks, o.OrderID, "3", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "3"

			return []model.Order{o}, nil
		})
	}

	t.Run("happy flow: first order should lock mp state", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableMultiplePickup: true, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Options.LockDuration = lockDuration

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectZoneCall(mocks)
		expectOnTopFareCall(mocks, []model.OnTopFare{})
		initDriverLocations(ctx, mocks, model.Location{Lat: businessLocation.Lat, Lng: businessLocation.Lng}, distCfg.MaxRadiusInKm,
			[]service.DriverWithLocation{
				createDriverWithLocation(createDriver("1", model.StatusAssigned, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 2),
				createDriverWithLocation(createDriver("2", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 1),
				createDriverWithLocation(createDriver("3", model.StatusOnline, model.Options{AutoAccept: true}), model.Location{Lat: 1.000, Lng: 2}, 3),
			}, model.StrategyNameFleetPool)
		expectGetAssignedDrivers(ctx, mocks, o.OrderID)

		initDriverStatistics(ctx, mocks, []string{"1", "2", "3"})

		expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)
		setDriverTransaction(ctx, mocks, createDriverTransaction("1"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("2"))
		setDriverTransaction(ctx, mocks, createDriverTransaction("3"))

		expectPredictCall(ctx, mocks, tt)
		expectOptimizeCall(tt, ctx, mocks, []string{"2", "3"})
		expectRouteCallWithValidation(ctx, mocks, tt, "2")

		expectNoAssignedState(ctx, mocks, "2", o.OrderID, distCfg.AcceptingDurationInSecond)
		expectAssignToDriversWithOpt(ctx, mocks, 1, o.OrderID, 0, "2", 1, model.AssignmentLogOpt{
			AutoAssigned:        true,
			AllowQueueing:       true,
			LockDuration:        lockDuration,
			PlanRoute:           model.PlanRoute{RouteResponse: prediction.RouteResponse{RiderID: "2"}},
			SearchRiderStrategy: model.StrategyNameFleetPool,
			Region:              model.DistributeRegions(regions).DefaultRegion(),
		})

		expectLockDriver(ctx, mocks, "2")
		expectUnlockDriver(ctx, mocks, "2")
		expectNotify(tt, ctx, mocks, o.OrderID, "2", service.WithFirebase, service.WithSocketIO)
		expectEventPublish(ctx, mocks)
		mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
			o.Status = model.StatusDriverMatched
			o.Driver = "2"

			return []model.Order{o}, nil
		})

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 3, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("happy flow: second order should bundle with first", func(tt *testing.T) {
		tt.Parallel()
		testFunc := func(tt *testing.T, lockedUntil *time.Time) {
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
				EnableMultiplePickup:     true,
				DisableThrottledDeferred: true,
				DisableThrottledDispatch: true,
				DisableDeferredDispatch:  true,
			})
			distCfg.PredictionServiceEnabled = true
			distCfg.MOType = prediction.MOTypeMOS1
			distCfg.AssignmentType = prediction.AssignmentTypeMultiple
			distCfg.NotifyViaSocketIOEnabled = true
			defer finishFn()

			mp1OrderID := "mp-first-order"
			currentTrip := model.Trip{
				TripID: "current-trip",
				Orders: []model.TripOrder{
					{
						OrderID: mp1OrderID,
						Status:  model.StatusDriverMatched,
					},
				},
			}
			ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
			o.Options.MpOrderIDs = []string{mp1OrderID}

			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
			mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNoSearch).Return(nil)
			expectGetAssignedDrivers(ctx, mocks, o.OrderID)
			expectZoneCall(mocks)
			expectEvaluateRainSituation(ctx, mocks, true, false)
			expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
			expectPredictCall(ctx, mocks, tt)

			mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
			mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(&model.Order{
				Status: model.StatusDriverMatched,
				Driver: driverID,
			}, nil)

			mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{
				driverID: {
					DriverID:                driverID,
					Status:                  model.StatusAssigned,
					CurrentOrder:            mp1OrderID,
					CurrentTrip:             currentTrip.TripID,
					LockedFromQueueingUntil: lockedUntil,
					Options: model.Options{
						AutoAccept: true,
					},
					Region: "AYUTTHAYA",
				},
			}, nil).Times(2)
			mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&model.DriverLocation{
				DriverID: driverID,
				Location: model.Location{
					Lat: 13,
					Lng: 100,
				},
				DistanceMeter: 1,
			}, nil)
			initDriverStatistics(ctx, mocks, []string{driverID})
			setDriverTransaction(ctx, mocks, createDriverTransaction(driverID))

			expectNoAssignedState(ctx, mocks, driverID, o.OrderID, distCfg.AcceptingDurationInSecond)
			mocks.mockTripRepository.EXPECT().GetMany(gomock.Any(), []string{currentTrip.TripID}).Return([]model.Trip{currentTrip}, nil)
			mocks.mockPredictionService.EXPECT().IsTripDistanceReduced(gomock.Any(), []string{o.OrderID}, []model.Trip{currentTrip}, gomock.Any(), gomock.Any()).Return(false, nil)
			mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil)
			expectAssignMpToDriversWithOpt(
				ctx,
				mocks,
				1,
				mp1OrderID,
				o.OrderID,
				o,
				0,
				driverID,
				1,
				model.AssignmentLogOpt{
					AutoAssigned:        true,
					IsMultiplePickup:    true,
					AllowQueueing:       true,
					AssignToQueue:       true,
					SearchRiderStrategy: model.StrategyNameNoSearch,
					PlanRoute: model.PlanRoute{
						RouteResponse: prediction.RouteResponse{
							RiderID: driverID,
							AssignedOrders: []prediction.AssignedOrder{
								{
									OrderID:   mp1OrderID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
								{
									OrderID:   o.OrderID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
							},
							PlanRoutes: []prediction.PlanRoute{
								{
									OrderID:    mp1OrderID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    o.OrderID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    mp1OrderID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    o.OrderID,
									ActionType: prediction.DropOffAction,
								},
							},
						},
					},
					Region: model.DistributeRegions(regions).DefaultRegion(),
				})

			expectLockDriver(ctx, mocks, driverID)
			expectUnlockDriver(ctx, mocks, driverID)
			expectNotify(tt, ctx, mocks, o.OrderID, driverID, service.WithFirebase, service.WithSocketIO)
			expectEventPublish(ctx, mocks)

			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		}
		tt.Run("with lock", func(tt *testing.T) {
			tt.Parallel()
			lockedUntil := time.Now().Add(lockDuration)
			testFunc(tt, &lockedUntil)
		})
		tt.Run("without lock", func(tt *testing.T) {
			tt.Parallel()
			testFunc(tt, nil)
		})
	})
	t.Run("happy flow: second order should bundle with first b2b with random ([random], [mp1, mp2])", func(tt *testing.T) {
		tt.Parallel()
		testFunc := func(tt *testing.T, lockedUntil *time.Time) {
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
				EnableMultiplePickup:     true,
				DisableThrottledDeferred: true,
				DisableThrottledDispatch: true,
				DisableDeferredDispatch:  true,
			})
			distCfg.PredictionServiceEnabled = true
			distCfg.MOType = prediction.MOTypeMOS5
			distCfg.AssignmentType = prediction.AssignmentTypeMultiple
			distCfg.NotifyViaSocketIOEnabled = true
			defer finishFn()

			randomOrder1ID := "random-order-1"
			mp1OrderID := "mp-first-order"
			currentTrip := model.Trip{
				TripID: "current-trip",
				Orders: []model.TripOrder{
					{
						OrderID: randomOrder1ID,
						Status:  model.StatusDriverMatched,
					},
				},
			}
			mpTrip := model.Trip{
				TripID: "mp-trip",
				Orders: []model.TripOrder{
					{
						OrderID: mp1OrderID,
						Status:  model.StatusDriverMatched,
					},
				},
			}
			ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
			o.Options.MpOrderIDs = []string{mp1OrderID}

			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
			mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNoSearch).Return(nil)
			expectGetAssignedDrivers(ctx, mocks, o.OrderID)
			expectZoneCall(mocks)
			expectEvaluateRainSituation(ctx, mocks, true, false)
			expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
			expectPredictCall(ctx, mocks, tt)

			mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
			mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(&model.Order{
				Status: model.StatusDriverMatched,
				Driver: driverID,
			}, nil)

			mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), randomOrder1ID, gomock.Any()).Return(&model.Order{
				Status: model.StatusDriverMatched,
				Driver: driverID,
			}, nil)

			mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{
				driverID: {
					DriverID:                driverID,
					Status:                  model.StatusAssigned,
					CurrentOrder:            randomOrder1ID,
					CurrentTrip:             currentTrip.TripID,
					QueueingTrips:           []string{mpTrip.TripID},
					LockedFromQueueingUntil: lockedUntil,
					Options: model.Options{
						AutoAccept: true,
					},
					Region: "AYUTTHAYA",
				},
			}, nil).Times(2)
			mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&model.DriverLocation{
				DriverID: driverID,
				Location: model.Location{
					Lat: 13,
					Lng: 100,
				},
				DistanceMeter: 1,
			}, nil)
			initDriverStatistics(ctx, mocks, []string{driverID})
			setDriverTransaction(ctx, mocks, createDriverTransaction(driverID))
			expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

			expectNoAssignedState(ctx, mocks, driverID, o.OrderID, distCfg.AcceptingDurationInSecond)
			mocks.mockTripRepository.EXPECT().GetMany(gomock.Any(), []string{currentTrip.TripID, mpTrip.TripID}).Return([]model.Trip{currentTrip, mpTrip}, nil)
			mocks.mockPredictionService.EXPECT().IsTripDistanceReduced(gomock.Any(), []string{o.OrderID}, []model.Trip{currentTrip, mpTrip}, gomock.Any(), gomock.Any()).Return(false, nil)
			mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil)

			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID, randomOrder1ID, mp1OrderID}, gomock.Any()).
				Return([]model.Order{
					{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
					{OrderID: randomOrder1ID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
					{OrderID: mp1OrderID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
				}, nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
				o.Status = model.StatusAssigningDriver
				o.Driver = driverID

				return []model.Order{o}, nil
			})
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
				o.Status = model.StatusDriverMatched
				o.Driver = driverID

				return []model.Order{o}, nil
			}).Times(2)
			mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
				1,
				o.OrderID,
				0,
				[]repository.DriverDistance{{DriverID: driverID, Distance: 1}},
				AssignmentLogOptMatcher(model.AssignmentLogOpt{
					AutoAssigned:     true,
					IsMultiplePickup: true,
					AllowQueueing:    true,
					AssignToQueue:    true,
					PlanRoute: model.PlanRoute{
						RouteResponse: prediction.RouteResponse{
							RiderID: driverID,
							AssignedOrders: []prediction.AssignedOrder{
								{
									OrderID:   randomOrder1ID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagB2B},
								},
								{
									OrderID:   mp1OrderID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
								{
									OrderID:   o.OrderID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
							},
							PlanRoutes: []prediction.PlanRoute{
								{
									OrderID:    randomOrder1ID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    randomOrder1ID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    mp1OrderID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    o.OrderID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    mp1OrderID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    o.OrderID,
									ActionType: prediction.DropOffAction,
								},
							},
						},
					},
					SearchRiderStrategy: model.StrategyNameNoSearch,
					Region:              model.DistributeRegions(regions).DefaultRegion(),
				}),
			).Return(nil, nil)

			expectLockDriver(ctx, mocks, driverID)
			expectUnlockDriver(ctx, mocks, driverID)
			expectNotify(tt, ctx, mocks, o.OrderID, driverID, service.WithFirebase, service.WithSocketIO)
			expectEventPublish(ctx, mocks)

			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

			stubDefaultBehavior(mocks)

			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		}
		tt.Run("with lock", func(tt *testing.T) {
			tt.Parallel()
			lockedUntil := time.Now().Add(lockDuration)
			testFunc(tt, &lockedUntil)
		})
		tt.Run("without lock", func(tt *testing.T) {
			tt.Parallel()
			testFunc(tt, nil)
		})
	})
	t.Run("happy flow: second order should bundle with mp1 and be able to compatible with maxload = 4 by mp1 and mp2 are in current trip ([mp1, mp2], [random, random])", func(tt *testing.T) {
		tt.Parallel()
		testFunc := func(tt *testing.T, lockedUntil *time.Time) {
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
				EnableMultiplePickup:     true,
				DisableThrottledDeferred: true,
				DisableThrottledDispatch: true,
				DisableDeferredDispatch:  true,
			})
			distCfg.PredictionServiceEnabled = true
			distCfg.MOType = prediction.MOTypeMOS5
			distCfg.AssignmentType = prediction.AssignmentTypeMultiple
			distCfg.NotifyViaSocketIOEnabled = true
			defer finishFn()

			randomOrder1ID := "random-order-1"
			randomOrder2ID := "random-order-2"
			mp1OrderID := "mp-first-order"
			currentTrip := model.Trip{
				TripID: "current-trip",
				Orders: []model.TripOrder{
					{
						OrderID: mp1OrderID,
						Status:  model.StatusDriverMatched,
					},
				},
			}
			mpTrip := model.Trip{
				TripID: "mp-trip",
				Orders: []model.TripOrder{
					{
						OrderID: randomOrder1ID,
						Status:  model.StatusDriverMatched,
					},
					{
						OrderID: randomOrder2ID,
						Status:  model.StatusDriverMatched,
					},
				},
				Routes: []model.TripRoute{
					{
						StopOrders: []model.TripStopOrder{
							{
								OrderID: randomOrder1ID,
								Done:    false,
							},
						},
						Action: model.TripActionPickUp,
					},
					{
						StopOrders: []model.TripStopOrder{
							{
								OrderID: randomOrder2ID,
								Done:    false,
							},
						},
						Action: model.TripActionPickUp,
					},
					{
						StopOrders: []model.TripStopOrder{
							{
								OrderID: randomOrder1ID,
								Done:    false,
							},
						},
						Action: model.TripActionDropOff,
					},
					{
						StopOrders: []model.TripStopOrder{
							{
								OrderID: randomOrder2ID,
								Done:    false,
							},
						},
						Action: model.TripActionDropOff,
					},
				},
			}
			ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
			o.Options.MpOrderIDs = []string{mp1OrderID}

			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
			mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNoSearch).Return(nil)
			expectGetAssignedDrivers(ctx, mocks, o.OrderID)
			expectZoneCall(mocks)
			expectEvaluateRainSituation(ctx, mocks, true, false)
			expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
			expectPredictCall(ctx, mocks, tt)

			mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
			mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(&model.Order{
				Status: model.StatusDriverMatched,
				Driver: driverID,
			}, nil)

			mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{
				driverID: {
					DriverID:                driverID,
					Status:                  model.StatusAssigned,
					CurrentOrder:            randomOrder1ID,
					CurrentTrip:             currentTrip.TripID,
					QueueingTrips:           []string{mpTrip.TripID},
					LockedFromQueueingUntil: lockedUntil,
					Options: model.Options{
						AutoAccept: true,
					},
					Region: "AYUTTHAYA",
				},
			}, nil).Times(2)
			mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), gomock.Any(), gomock.Any()).Return(false).MinTimes(1)
			mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&model.DriverLocation{
				DriverID: driverID,
				Location: model.Location{
					Lat: 13,
					Lng: 100,
				},
				DistanceMeter: 1,
			}, nil)
			initDriverStatistics(ctx, mocks, []string{driverID})
			setDriverTransaction(ctx, mocks, createDriverTransaction(driverID))
			expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

			expectNoAssignedState(ctx, mocks, driverID, o.OrderID, distCfg.AcceptingDurationInSecond)
			mocks.mockTripRepository.EXPECT().GetMany(gomock.Any(), []string{currentTrip.TripID, mpTrip.TripID}).Return([]model.Trip{currentTrip, mpTrip}, nil)
			mocks.mockPredictionService.EXPECT().IsTripDistanceReduced(gomock.Any(), []string{o.OrderID}, []model.Trip{currentTrip, mpTrip}, gomock.Any(), gomock.Any()).Return(false, nil)
			mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID, mp1OrderID, randomOrder1ID, randomOrder2ID}, gomock.Any()).
				Return([]model.Order{
					{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
					{OrderID: randomOrder1ID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
					{OrderID: mp1OrderID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
				}, nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
				o.Status = model.StatusAssigningDriver
				o.Driver = driverID

				return []model.Order{o}, nil
			})
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
				o.Status = model.StatusDriverMatched
				o.Driver = driverID

				return []model.Order{o}, nil
			}).Times(2)
			mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
				1,
				o.OrderID,
				0,
				[]repository.DriverDistance{{DriverID: driverID, Distance: 1}},
				AssignmentLogOptMatcher(model.AssignmentLogOpt{
					AutoAssigned:     true,
					IsMultiplePickup: true,
					AllowQueueing:    true,
					AssignToQueue:    true,
					PlanRoute: model.PlanRoute{
						RouteResponse: prediction.RouteResponse{
							RiderID: driverID,
							AssignedOrders: []prediction.AssignedOrder{
								{
									OrderID:   mp1OrderID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
								{
									OrderID:   o.OrderID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
								{
									OrderID:   randomOrder1ID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
								{
									OrderID:   randomOrder2ID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
							},
							PlanRoutes: []prediction.PlanRoute{

								{
									OrderID:    mp1OrderID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    o.OrderID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    mp1OrderID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    o.OrderID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    randomOrder1ID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    randomOrder2ID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    randomOrder1ID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    randomOrder2ID,
									ActionType: prediction.DropOffAction,
								},
							},
						},
					},
					SearchRiderStrategy: model.StrategyNameNoSearch,
					Region:              model.DistributeRegions(regions).DefaultRegion(),
				}),
			).Return(nil, nil)

			expectLockDriver(ctx, mocks, driverID)
			expectUnlockDriver(ctx, mocks, driverID)
			expectNotify(tt, ctx, mocks, o.OrderID, driverID, service.WithFirebase, service.WithSocketIO)
			expectEventPublish(ctx, mocks)

			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		}
		tt.Run("with lock", func(tt *testing.T) {
			tt.Parallel()
			lockedUntil := time.Now().Add(lockDuration)
			testFunc(tt, &lockedUntil)
		})
		tt.Run("without lock", func(tt *testing.T) {
			tt.Parallel()
			testFunc(tt, nil)
		})
	})
	t.Run("happy flow: second order should bundle with mp1 and be able to compatible with maxload = 4 by mp1 and mp2 are in queueing trip ([random, random], [mp1, mp2])", func(tt *testing.T) {
		tt.Parallel()
		testFunc := func(tt *testing.T, lockedUntil *time.Time) {
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
				EnableMultiplePickup:     true,
				DisableThrottledDeferred: true,
				DisableThrottledDispatch: true,
				DisableDeferredDispatch:  true,
			})
			distCfg.PredictionServiceEnabled = true
			distCfg.MOType = prediction.MOTypeMOS5
			distCfg.AssignmentType = prediction.AssignmentTypeMultiple
			distCfg.NotifyViaSocketIOEnabled = true
			defer finishFn()

			randomOrder1ID := "random-order-1"
			randomOrder2ID := "random-order-2"
			mp1OrderID := "mp-first-order"
			currentTrip := model.Trip{
				TripID: "current-trip",
				Orders: []model.TripOrder{
					{
						OrderID: randomOrder1ID,
						Status:  model.StatusDriverMatched,
					},
					{
						OrderID: randomOrder2ID,
						Status:  model.StatusDriverMatched,
					},
				},
				Routes: []model.TripRoute{
					{
						StopOrders: []model.TripStopOrder{
							{
								OrderID: randomOrder1ID,
								Done:    false,
							},
						},
						Action: model.TripActionPickUp,
					},
					{
						StopOrders: []model.TripStopOrder{
							{
								OrderID: randomOrder2ID,
								Done:    false,
							},
						},
						Action: model.TripActionPickUp,
					},
					{
						StopOrders: []model.TripStopOrder{
							{
								OrderID: randomOrder1ID,
								Done:    false,
							},
						},
						Action: model.TripActionDropOff,
					},
					{
						StopOrders: []model.TripStopOrder{
							{
								OrderID: randomOrder2ID,
								Done:    false,
							},
						},
						Action: model.TripActionDropOff,
					},
				},
			}
			mpTrip := model.Trip{
				TripID: "mp-trip",
				Orders: []model.TripOrder{
					{
						OrderID: mp1OrderID,
						Status:  model.StatusDriverMatched,
					},
				},
			}
			ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
			o.Options.MpOrderIDs = []string{mp1OrderID}

			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
			mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNoSearch).Return(nil)
			expectGetAssignedDrivers(ctx, mocks, o.OrderID)
			expectZoneCall(mocks)
			expectEvaluateRainSituation(ctx, mocks, true, false)
			expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
			expectPredictCall(ctx, mocks, tt)

			mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
			mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(&model.Order{
				Status: model.StatusDriverMatched,
				Driver: driverID,
			}, nil)

			mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), randomOrder2ID, gomock.Any()).Return(&model.Order{
				Status: model.StatusDriverMatched,
				Driver: driverID,
			}, nil)

			mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{
				driverID: {
					DriverID:                driverID,
					Status:                  model.StatusAssigned,
					CurrentOrder:            randomOrder1ID,
					CurrentTrip:             currentTrip.TripID,
					QueueingTrips:           []string{mpTrip.TripID},
					LockedFromQueueingUntil: lockedUntil,
					Options: model.Options{
						AutoAccept: true,
					},
					Region: "AYUTTHAYA",
				},
			}, nil).Times(2)
			mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), gomock.Any(), gomock.Any()).Return(false).MinTimes(1)
			mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&model.DriverLocation{
				DriverID: driverID,
				Location: model.Location{
					Lat: 13,
					Lng: 100,
				},
				DistanceMeter: 1,
			}, nil)
			initDriverStatistics(ctx, mocks, []string{driverID})
			setDriverTransaction(ctx, mocks, createDriverTransaction(driverID))
			expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

			expectNoAssignedState(ctx, mocks, driverID, o.OrderID, distCfg.AcceptingDurationInSecond)
			mocks.mockTripRepository.EXPECT().GetMany(gomock.Any(), []string{currentTrip.TripID, mpTrip.TripID}).Return([]model.Trip{currentTrip, mpTrip}, nil)
			mocks.mockPredictionService.EXPECT().IsTripDistanceReduced(gomock.Any(), []string{o.OrderID}, []model.Trip{currentTrip, mpTrip}, gomock.Any(), gomock.Any()).Return(false, nil)
			mocks.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID, randomOrder1ID, randomOrder2ID, mp1OrderID}, gomock.Any()).
				Return([]model.Order{
					{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
					{OrderID: randomOrder1ID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
					{OrderID: mp1OrderID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
				}, nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, gomock.Any()).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
				o.Status = model.StatusAssigningDriver
				o.Driver = driverID

				return []model.Order{o}, nil
			})
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID}, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).DoAndReturn(func(_, _, _ interface{}) ([]model.Order, error) {
				o.Status = model.StatusDriverMatched
				o.Driver = driverID

				return []model.Order{o}, nil
			}).Times(2)
			mocks.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(),
				1,
				o.OrderID,
				0,
				[]repository.DriverDistance{{DriverID: driverID, Distance: 1}},
				AssignmentLogOptMatcher(model.AssignmentLogOpt{
					AutoAssigned:     true,
					IsMultiplePickup: true,
					AllowQueueing:    true,
					AssignToQueue:    true,
					PlanRoute: model.PlanRoute{
						RouteResponse: prediction.RouteResponse{
							RiderID: driverID,
							AssignedOrders: []prediction.AssignedOrder{
								{
									OrderID:   randomOrder1ID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
								{
									OrderID:   randomOrder2ID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
								{
									OrderID:   mp1OrderID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
								{
									OrderID:   o.OrderID,
									OrderFlag: []prediction.OrderFlag{prediction.OrderFlagMO},
								},
							},
							PlanRoutes: []prediction.PlanRoute{
								{
									OrderID:    randomOrder1ID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    randomOrder2ID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    randomOrder1ID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    randomOrder2ID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    mp1OrderID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    o.OrderID,
									ActionType: prediction.PickupAction,
								},
								{
									OrderID:    mp1OrderID,
									ActionType: prediction.DropOffAction,
								},
								{
									OrderID:    o.OrderID,
									ActionType: prediction.DropOffAction,
								},
							},
						},
					},
					SearchRiderStrategy: model.StrategyNameNoSearch,
					Region:              model.DistributeRegions(regions).DefaultRegion(),
				}),
			).Return(nil, nil)

			expectLockDriver(ctx, mocks, driverID)
			expectUnlockDriver(ctx, mocks, driverID)
			expectNotify(tt, ctx, mocks, o.OrderID, driverID, service.WithFirebase, service.WithSocketIO)
			expectEventPublish(ctx, mocks)

			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
		}
		tt.Run("with lock", func(tt *testing.T) {
			tt.Parallel()
			lockedUntil := time.Now().Add(lockDuration)
			testFunc(tt, &lockedUntil)
		})
		tt.Run("without lock", func(tt *testing.T) {
			tt.Parallel()
			testFunc(tt, nil)
		})
	})
	t.Run("happy flow: should not assign mp2 (mp hack) to same rider, if mp1 has already bundled with random order", func(tt *testing.T) {
		tt.Parallel()
		testFunc := func(tt *testing.T, lockedUntil *time.Time) {
			autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{
				EnableMultiplePickup:     true,
				DisableThrottledDeferred: true,
				DisableThrottledDispatch: true,
				DisableDeferredDispatch:  true,
			})
			distCfg.PredictionServiceEnabled = true
			distCfg.MOType = prediction.MOTypeMOS5
			distCfg.AssignmentType = prediction.AssignmentTypeMultiple
			distCfg.NotifyViaSocketIOEnabled = true
			defer finishFn()

			randomOrder1ID := "random-order-1"
			mp1OrderID := "mp-first-order"
			currentTrip := model.Trip{
				TripID: "current-trip",
				Orders: []model.TripOrder{
					{
						OrderID: randomOrder1ID,
						Status:  model.StatusDriverMatched,
					},
					{
						OrderID: mp1OrderID,
						Status:  model.StatusDriverMatched,
					},
				},
			}
			mpTrip := model.Trip{
				TripID: "mp-trip",
				Orders: []model.TripOrder{},
			}
			ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
			o.Options.MpOrderIDs = []string{mp1OrderID}

			mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
			mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
			mocks.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNoSearch).Return(nil)
			expectGetAssignedDrivers(ctx, mocks, o.OrderID)
			expectZoneCall(mocks)
			expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
			expectPredictCall(ctx, mocks, tt)

			mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
			mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), o.Options.MpOrderIDs[0]).Return(&model.Order{
				Status: model.StatusDriverMatched,
				Driver: driverID,
			}, nil).Times(2)

			mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{
				driverID: {
					DriverID:                driverID,
					Status:                  model.StatusAssigned,
					CurrentOrder:            randomOrder1ID,
					CurrentTrip:             currentTrip.TripID,
					QueueingTrips:           []string{mpTrip.TripID},
					LockedFromQueueingUntil: lockedUntil,
					Options: model.Options{
						AutoAccept: true,
					},
					Region: "AYUTTHAYA",
				},
			}, nil).Times(2)
			mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&model.DriverLocation{
				DriverID: driverID,
				Location: model.Location{
					Lat: 13,
					Lng: 100,
				},
				DistanceMeter: 1,
			}, nil)
			initDriverStatistics(ctx, mocks, []string{driverID})
			setDriverTransaction(ctx, mocks, createDriverTransaction(driverID))
			expectLockAndUnlockAssigningOrder(ctx, mocks, o.OrderID, gomock.Any(), true)

			expectNoAssignedState(ctx, mocks, driverID, o.OrderID, distCfg.AcceptingDurationInSecond)
			mocks.mockTripRepository.EXPECT().GetMany(gomock.Any(), []string{currentTrip.TripID, mpTrip.TripID}).Return([]model.Trip{currentTrip, mpTrip}, nil)
			mocks.mockOrderRepository.EXPECT().GetMany(gomock.Any(), []string{o.OrderID, randomOrder1ID, mp1OrderID}, gomock.Any()).
				Return([]model.Order{
					{OrderID: o.OrderID, Status: model.StatusAssigningDriver, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
					{OrderID: randomOrder1ID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
					{OrderID: mp1OrderID, Status: model.StatusDriverMatched, DeliveringRound: 0, ExpireAt: time.Now().Add(1 * time.Hour)},
				}, nil)

			mocks.mockDriverRepository.EXPECT().UnlockForQueueing(gomock.Any(), driverID).Return(nil)
			mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), gomock.Any(), gomock.Any()).Return(false).MinTimes(1)
			mocks.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any())
			expectEventPublish(ctx, mocks)

			mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
			mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)
			_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})

			autoAssignOrderDistributor.Stop()
			wg.Wait()

			assertStopMetric(tt, mocks, "FAIL_NO_DRIVER_MATCHED", "1", model.RegionsString(regions))
		}
		tt.Run("with lock", func(tt *testing.T) {
			tt.Parallel()
			lockedUntil := time.Now().Add(lockDuration)
			testFunc(tt, &lockedUntil)
		})
		tt.Run("without lock", func(tt *testing.T) {
			tt.Parallel()
			testFunc(tt, nil)
		})
	})
	t.Run("next status flow: order is already DRIVER_TO_DESTINATION", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableMultiplePickup: true, DisableThrottledDispatch: true})
		distCfg = modifyDistCfgForFallbackTests(distCfg)
		defer finishFn()

		mpOrderID := "mp-first-order"
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Options.MpOrderIDs = []string{mpOrderID}

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectPredictCall(ctx, mocks, tt)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), mpOrderID).Return(&model.Order{
			OrderID: mpOrderID,
			Status:  model.StatusDriverToDestination,
			Driver:  driverID,
		}, nil)
		expectNormalFlow(ctx, tt, autoAssignOrderDistributor, mocks, distCfg, o, businessLocation, regions, 1, model.StrategyNameFleetPool)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("order not allowed: switch-flow", func(tt *testing.T) {
		tt.Parallel()
		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableMultiplePickup: true, DisableThrottledDispatch: true})
		distCfg = modifyDistCfgForFallbackTests(distCfg)
		defer finishFn()

		mpOrderID := "mp-first-order"
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Options.MpOrderIDs = []string{mpOrderID}

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectPredictCall(ctx, mocks, tt)

		mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), mpOrderID).Return(&model.Order{
			OrderID: mpOrderID,
			Status:  model.StatusDriverToDestination,
			Driver:  driverID,
			Quote: model.Quote{
				Options: model.OrderOptions{
					SwitchFlow: true,
				},
			},
		}, nil)
		expectNormalFlow(ctx, tt, autoAssignOrderDistributor, mocks, distCfg, o, businessLocation, regions, 1, model.StrategyNameDirectSearch)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("order not allowed: reassigned", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableMultiplePickup: true, DisableThrottledDispatch: true})
		distCfg = modifyDistCfgForFallbackTests(distCfg)
		defer finishFn()

		mpOrderID := "mp-first-order"
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Options.MpOrderIDs = []string{mpOrderID}

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectPredictCall(ctx, mocks, tt)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), mpOrderID).Return(&model.Order{
			OrderID:         mpOrderID,
			Status:          model.StatusDriverToDestination,
			Driver:          driverID,
			DeliveringRound: 1,
		}, nil)
		expectNormalFlow(ctx, tt, autoAssignOrderDistributor, mocks, distCfg, o, businessLocation, regions, 1, model.StrategyNameFleetPool)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("order not allowed: not enough cash", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{EnableMultiplePickup: true, DisableThrottledDispatch: true})
		distCfg = modifyDistCfgForFallbackTests(distCfg)
		defer finishFn()

		mpOrderID := "mp-first-order"
		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Routes[1].PriceSummary.ItemFee.SubTotal = 100
		o.PayAtStop = 1
		o.Options.MpOrderIDs = []string{mpOrderID}

		expectEvaluateRainSituation(ctx, mocks, true, false)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectPredictCall(ctx, mocks, tt)

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetIsTriedForceAssignMP(gomock.Any(), o.OrderID, true).Return(nil)
		mocks.mockOrderRepository.EXPECT().Get(gomock.Any(), mpOrderID).Return(&model.Order{
			OrderID: mpOrderID,
			Status:  model.StatusDriverMatched,
			Driver:  driverID,
		}, nil)
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{
			driverID: {
				DriverID:     driverID,
				Status:       model.StatusAssigned,
				CurrentOrder: mpOrderID,
				Options: model.Options{
					AutoAccept: true,
				},
				Region: "AYUTTHAYA",
			},
		}, nil)
		mocks.mockDriverLocationRepository.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&model.DriverLocation{
			DriverID: driverID,
			Location: model.Location{
				Lat: 13,
				Lng: 100,
			},
			DistanceMeter: 1,
		}, nil)
		initDriverStatistics(ctx, mocks, []string{driverID})
		mocks.mockDriverTransaction.EXPECT().GetDriverTransaction(gomock.Any(), driverID, gomock.Any()).Return(model.DriverTransaction{
			DriverID: driverID,
			Cash:     50,
		}, nil).AnyTimes()
		mocks.mockDriverRepository.EXPECT().UnlockForQueueing(gomock.Any(), driverID).Return(nil)

		expectNormalFlow(ctx, tt, autoAssignOrderDistributor, mocks, distCfg, o, businessLocation, regions, 1, model.StrategyNameFleetPool)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
}

func TestAutoAssign_DeferredOrder(t *testing.T) {
	t.Parallel()
	t.Run("not defer order if DisableDeferredDispatch is true", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: true, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order if DeferredDispatchFeatureEnabled is false", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = false
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order if order cannot be deferred on food side", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = false

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order if order already been deferred", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = &model.PredictionFeatures{}
		o.IsDeferred = true
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())
		expectLockOrder(ctx, mocks, o.OrderID, true)

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order with restaurant not included in whitelist", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"otherid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order with restaurant included in blacklist", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantBlacklist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order with restaurant included in blacklist and whitelist", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchRestaurantBlacklist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("defer order with empty white and black lists", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true
		o.ExpireAt = time.Date(2023, 05, 25, 10, 0, 0, 0, timeutil.BangkokLocation())
		expectedExpireAt := time.Date(2023, 05, 25, 10, 20, 0, 0, timeutil.BangkokLocation())
		expectedDeferDuration := 20 * time.Minute

		expectZoneCall(mocks)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		cookingTime := int((35 * time.Minute).Seconds())
		expectPredictCall(ctx, mocks, tt, predictCallWithCookingTime(cookingTime))

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New(""))
		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockDeferredOrderRepository.EXPECT().IsUnprocessedExist(gomock.Any(), gomock.Any()).Return(false, nil)
		mocks.mockDeferredOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), model.DeferredOrderOption{
			Region:                      o.DistributeRegions.DefaultRegion(),
			DeferDurationMinute:         20,
			DrivingDurationConfigMinute: 10,
			BufferDurationConfigMinute:  5,
		})
		mocks.mockOrderRepository.EXPECT().SetDeferredOrder(gomock.Any(), o.OrderID, expectedExpireAt, expectedDeferDuration).Return(nil)

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledDeferStandaloneTransitionToSingle.Name, gomock.Any()).Return(false).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
	t.Run("defer order with predicted cooking time more than defer config 1", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true
		o.ExpireAt = time.Date(2023, 05, 25, 10, 0, 0, 0, timeutil.BangkokLocation())
		expectedExpireAt := time.Date(2023, 05, 25, 10, 20, 0, 0, timeutil.BangkokLocation())
		expectedDeferDuration := 20 * time.Minute

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		expectZoneCall(mocks)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		cookingTime := int((35 * time.Minute).Seconds())
		expectPredictCall(ctx, mocks, tt, predictCallWithCookingTime(cookingTime))

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledDeferStandaloneTransitionToSingle.Name, gomock.Any()).Return(false).MinTimes(1)
		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockDeferredOrderRepository.EXPECT().IsUnprocessedExist(gomock.Any(), gomock.Any()).Return(false, nil)
		mocks.mockDeferredOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), model.DeferredOrderOption{
			Region:                      o.DistributeRegions.DefaultRegion(),
			DeferDurationMinute:         20,
			DrivingDurationConfigMinute: 10,
			BufferDurationConfigMinute:  5,
		})
		mocks.mockOrderRepository.EXPECT().SetDeferredOrder(gomock.Any(), o.OrderID, expectedExpireAt, expectedDeferDuration).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
	t.Run("defer order with predicted cooking time more than defer config 2", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true
		o.ExpireAt = time.Date(2023, 05, 25, 10, 0, 0, 0, timeutil.BangkokLocation())
		expectedExpireAt := time.Date(2023, 05, 25, 10, 40, 0, 0, timeutil.BangkokLocation())
		expectedDeferDuration := 40 * time.Minute

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		expectZoneCall(mocks)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)

		cookingTime := int((55 * time.Minute).Seconds())
		expectPredictCall(ctx, mocks, tt, predictCallWithCookingTime(cookingTime))

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledDeferStandaloneTransitionToSingle.Name, gomock.Any()).Return(false).MinTimes(1)
		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockDeferredOrderRepository.EXPECT().IsUnprocessedExist(gomock.Any(), gomock.Any()).Return(false, nil)
		mocks.mockDeferredOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), model.DeferredOrderOption{
			Region:                      o.DistributeRegions.DefaultRegion(),
			DeferDurationMinute:         40,
			DrivingDurationConfigMinute: 10,
			BufferDurationConfigMinute:  5,
		})
		mocks.mockOrderRepository.EXPECT().SetDeferredOrder(gomock.Any(), o.OrderID, expectedExpireAt, expectedDeferDuration).Return(nil)

		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
	t.Run("not defer order with predicted cooking time equal to defer config", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 9, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order with predicted cooking time less than defer config 1", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 9, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order with if insert to defer failed", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameFleetPool, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledDeferStandaloneTransitionToSingle.Name, gomock.Any()).Return(false).MinTimes(1)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockDeferredOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), model.DeferredOrderOption{
			Region:                      o.DistributeRegions.DefaultRegion(),
			DeferDurationMinute:         25,
			DrivingDurationConfigMinute: 10,
			BufferDurationConfigMinute:  5,
		}).Return(errors.New("error inserting order into deferred_orders"))

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order if update order failed", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = nil
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		expectDeferToSkip(tt, mocks, ctx, distCfg, businessLocation, o, 40, model.StrategyNameDirectSearch, model.DistributeRegions(regions).DefaultRegion())

		mocks.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledDeferStandaloneTransitionToSingle.Name, gomock.Any()).Return(false).MinTimes(1)
		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockTxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any())
		mocks.mockDeferredOrderRepository.EXPECT().InsertOrder(gomock.Any(), gomock.Any(), model.DeferredOrderOption{
			Region:                      o.DistributeRegions.DefaultRegion(),
			DeferDurationMinute:         25,
			DrivingDurationConfigMinute: 10,
			BufferDurationConfigMinute:  5,
		}).Return(nil)
		mocks.mockOrderRepository.EXPECT().SetDeferredOrder(gomock.Any(), o.OrderID, gomock.Any(), gomock.Any()).Return(errors.New("error updating order after deferred"))

		mocks.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()

		assertFetchedMetric(tt, mocks, 4, model.RegionsString(regions))
		assertStopMetric(tt, mocks, "ACCEPTED", "1", model.RegionsString(regions))
	})
	t.Run("not defer order and not distribute if order is locked", func(tt *testing.T) {
		tt.Parallel()

		autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(tt, dispatcherconfig.AutoAssignDbConfig{DisableDeferredDispatch: false, DisableThrottledDispatch: true})
		distCfg.PredictionServiceEnabled = true
		distCfg.AssignmentType = prediction.AssignmentTypeSingle
		distCfg.NotifyViaSocketIOEnabled = true
		distCfg.PredictionRestaurantWhitelist = []string{"resid", "otherid"}
		distCfg.PredictionWhitelist = []string{"2", "3", "U"}
		distCfg.PredictionBlacklist = []string{"1", "2"}
		// deferred configuration
		distCfg.DeferredDispatchFeatureEnabled = true
		distCfg.DeferredDispatchRestaurantWhitelist = []string{"resid"}
		distCfg.DeferredDispatchDrivingDuration = 10 * time.Minute
		distCfg.DeferredDispatchBufferDuration = 5 * time.Minute
		defer finishFn()

		ctx, wg, o, expireAt, businessLocation, regions := createStandingData()
		o.Routes[0].ID = "resid"
		o.Prediction = &model.PredictionFeatures{}
		o.IsDeferred = true
		o.Options.SwitchFlow = true
		o.Options.CanDefer = true

		mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		mocks.mockDeferredOrderRepository.EXPECT().IsUnprocessedExist(gomock.Any(), gomock.Any()).Return(false, nil)
		expectZoneCall(mocks)
		mocks.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), []string{o.OrderID}, gomock.Any()).Return([]model.AssignmentLogRecord{
			{
				OrderID: o.OrderID,
			},
		}, nil)
		expectAssignmentLogCreated(ctx, mocks, o.OrderID, 0, model.DistributeRegions(regions).DefaultRegion())
		expectLockOrder(ctx, mocks, o.OrderID, false)
		mocks.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		mocks.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil)
		mocks.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), o.OrderID, gomock.Any(), model.StrategyNameNone).Return(nil)
		mocks.mockHearthBeatService.EXPECT().New(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockHearthBeatService.EXPECT().Done(gomock.Any(), o.OrderID).Return(nil).MinTimes(1)
		mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil)

		stubDefaultBehavior(mocks)

		_ = autoAssignOrderDistributor.RunTask(ctx, &o, expireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
			NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
		})

		autoAssignOrderDistributor.Stop()
		wg.Wait()
	})
}

func TestAutoAssignTask_validateIfDriverIsEligible(t *testing.T) {
	t.Run("eligible driver", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"

		driver := &model.DriverMinimal{
			DriverID: driverID,
			Status:   model.StatusOnline,
		}
		or := model.Order{
			Quote: model.Quote{Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: driver}, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any(), gomock.Any()).Times(1)
		actualFilterName, err := task.ValidateIfDriverIsEligible(ctx, &service.DriverWithLocation{Driver: *driver}, []model.Order{or})
		assert.NoError(t, err)
		assert.EqualValues(t, model.RiderFilterName(""), actualFilterName)
	})

	t.Run("driver not found", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"

		driver := &model.DriverMinimal{
			DriverID: driverID,
			Status:   model.StatusOnline,
		}
		or := model.Order{
			Quote: model.Quote{Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{}, errors.New("driver not found"))
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		actualFilterName, err := task.ValidateIfDriverIsEligible(ctx, &service.DriverWithLocation{Driver: *driver}, []model.Order{or})
		assert.Error(t, err)
		assert.EqualValues(t, model.RiderProfileNotFound, actualFilterName)
	})

	t.Run("driver status Offline not eligible", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"

		driver := &model.DriverMinimal{
			DriverID: driverID,
			Status:   model.StatusOffline,
		}
		or := model.Order{
			Quote: model.Quote{Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: driver}, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		actualFilterName, err := task.ValidateIfDriverIsEligible(ctx, &service.DriverWithLocation{Driver: *driver}, []model.Order{or})
		assert.Error(t, err)
		assert.EqualValues(t, model.RiderNotOnlineOrAssigned, actualFilterName)
	})

	t.Run("driver status Deactivated not eligible", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"

		driver := &model.DriverMinimal{
			DriverID: driverID,
			Status:   model.StatusDeactivated,
		}
		or := model.Order{
			Quote: model.Quote{Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: driver}, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		actualFilterName, err := task.ValidateIfDriverIsEligible(ctx, &service.DriverWithLocation{Driver: *driver}, []model.Order{or})
		assert.Error(t, err)
		assert.EqualValues(t, model.RiderNotOnlineOrAssigned, actualFilterName)
	})

	t.Run("driver status Banned not eligible", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"

		driver := &model.DriverMinimal{
			DriverID: driverID,
			Status:   model.StatusBanned,
		}
		or := model.Order{
			Quote: model.Quote{Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: driver}, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		actualFilterName, err := task.ValidateIfDriverIsEligible(ctx, &service.DriverWithLocation{Driver: *driver}, []model.Order{or})
		assert.Error(t, err)
		assert.EqualValues(t, model.RiderNotOnlineOrAssigned, actualFilterName)
	})

	t.Run("driver enabled offline-later mode", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"

		driver := &model.DriverMinimal{
			DriverID:     driverID,
			Status:       model.StatusOnline,
			OfflineLater: true,
		}
		or := model.Order{
			Quote: model.Quote{Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: driver}, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		actualFilterName, err := task.ValidateIfDriverIsEligible(ctx, &service.DriverWithLocation{Driver: *driver}, []model.Order{or})
		assert.Error(t, err)
		assert.EqualValues(t, model.OfflineLater, actualFilterName)
	})
	t.Run("has prediction disruption changed", func(t *testing.T) {
		t.Parallel()
		t0 := time.Now()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}

		ctx := context.TODO()
		driverID := "LMD1"

		oldDriver := &model.DriverMinimal{
			DriverID:                 driverID,
			Status:                   model.StatusOnline,
			LastPredictionDisruption: t0,
		}
		newDriver := &model.DriverMinimal{
			DriverID:                 driverID,
			Status:                   model.StatusOnline,
			LastPredictionDisruption: t0.Add(10 * time.Second),
		}
		or := model.Order{
			Quote: model.Quote{Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: newDriver}, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any(), gomock.Any())
		actualFilterName, err := task.ValidateIfDriverIsEligible(ctx, &service.DriverWithLocation{Driver: *oldDriver}, []model.Order{or})
		assert.Error(t, err)
		assert.EqualValues(t, model.LastPredictionDisruptionChanged, actualFilterName)
	})

	t.Run("driver opted out service type", func(t *testing.T) {
		t.Parallel()
		autoAssignOrderDistributor, mocks, _, finishFn := createAutoAssignOrderDistributorWithAutoAssignCfg(t, dispatcherconfig.AutoAssignDbConfig{})
		defer finishFn()
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		mocks.mockServicePreferenceService = mock_service.NewMockServicePreferenceService(ctrl)
		autoAssignOrderDistributor.AutoAssignOrderDistributorDeps.ServicePreferenceService = mocks.mockServicePreferenceService
		task := &distribution.AutoAssignTask{AutoAssignOrderDistributorDeps: autoAssignOrderDistributor.AutoAssignOrderDistributorDeps}
		ctx := context.TODO()
		driverID := "LMD1"

		driver := &model.DriverMinimal{
			DriverID:       driverID,
			Status:         model.StatusOnline,
			ServicesOptOut: []model.Service{model.ServiceFood},
		}
		or := model.Order{
			Quote: model.Quote{ServiceType: model.ServiceFood, Options: model.OrderOptions{MpID: "0", IsMpAlongTheRoute: true}},
		}
		mocks.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), []string{driverID}).Return(map[string]*model.DriverMinimal{driverID: driver}, nil)
		mocks.mockMetricsRegistry.EXPECT().IncrValidateAutoAssignDriverIsEligibleCounter(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		mocks.mockServicePreferenceService.EXPECT().OptOutAllowedServicesFromPreferenceWithWhitelist(gomock.Any(), gomock.Any(), driverID).Return([]model.Service{model.ServiceFood}, true, nil)
		actualFilterName, err := task.ValidateIfDriverIsEligible(ctx, &service.DriverWithLocation{Driver: *driver}, []model.Order{or})
		assert.Error(t, err)
		assert.EqualValues(t, model.RiderOptedOut, actualFilterName)
	})
}
