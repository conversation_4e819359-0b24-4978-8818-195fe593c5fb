package distribution_test

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"os"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/aybabtme/uniplot/barchart"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/account"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/geo"
	"git.wndv.co/lineman/fleet-distribution/internal/httplog"
	"git.wndv.co/lineman/fleet-distribution/internal/sim"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestAutoAssignSimulation(t *testing.T) {
	rand.Seed(1)

	t.Run("random", func(tt *testing.T) {
		runAutoAssignSimulation(tt, func(simulator *sim.Simulator, state *inMemState, distributor *distribution.AutoAssignOrderDistributor, distCfg model.AutoAssignDistribution) {
			generateDrivers(simulator, state, 100, 0.09, 0.09)
			generateOrders(simulator, distributor, state, distCfg, 100, 5, 0.09, 0.09)
		})
	})

	t.Run("from http log", func(tt *testing.T) {
		tt.Skip()
		runAutoAssignSimulation(tt, func(simulator *sim.Simulator, state *inMemState, distributor *distribution.AutoAssignOrderDistributor, distCfg model.AutoAssignDistribution) {
			dist := true
			generateActionsFromHttpLog(simulator, state, distributor, distCfg, "testdata/delivery.csv", dist)
			generateActionsFromHttpLog(simulator, state, distributor, distCfg, "testdata/driver.csv", dist)
		})
	})
}

const StatusInActive model.DriverStatus = "INACTIVE"

type generator func(simulator *sim.Simulator, state *inMemState, distributor *distribution.AutoAssignOrderDistributor, distCfg model.AutoAssignDistribution)

func runAutoAssignSimulation(t *testing.T, g generator) {
	autoAssignOrderDistributor, mocks, distCfg, finishFn := createAutoAssignOrderDistributorWithContingencyCfg(t, order.ContingencyConfig{})
	mocks.mockDistributionExperimentPlatformClient.EXPECT().GetSwitchbackExperimentsWithConditions(gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	mocks.mockThrottledDispatchDetailRepository.EXPECT().FindOneFromOrder(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
	defer finishFn()

	env := sim.NewSimulator(time.Unix(0, 0), 100*time.Millisecond)

	autoAssignOrderDistributor.Environment = env
	autoAssignOrderDistributor.Config.EnableCashCollection = false
	distCfg.MaxRadiusInKm = 10
	distCfg.AcceptingDurationInSecond = 15

	distCfg.DistanceScoreWeight = 1
	distCfg.AcceptanceScoreWeight = 1
	distCfg.IncentiveScoreWeight = 1
	distCfg.IdleScoreWeight = 1

	state := NewInMemState(env)
	state.init(mocks)

	g(env, state, autoAssignOrderDistributor, distCfg)

	env.Run()

	fmt.Printf("logLastTimestamp=%v\n", state.logLastTimestamp)
	printReport(state)
}

func generateDrivers(env sim.Environment, state *inMemState, size int, latFactor float64, lngFactor float64) {
	i := 1
	for i <= size {
		d := createDriverWithLocation(
			createDriver(strconv.Itoa(i), randomDriverStatus(1, 0, 0), model.Options{AutoAccept: randomBool()}),
			model.Location{Lat: 1 + latFactor*rand.Float64(), Lng: 2 + lngFactor*rand.Float64()}, 0)

		env.Schedule(func() {
			state.updateDriver(d.Driver.DriverID, func(_ *service.DriverWithLocation) *service.DriverWithLocation {
				return &d
			})
		}, env.Now().Add(time.Duration(i)*500*time.Millisecond))
		i++
	}
}

func randomDriverStatus(onlineRatio float64, offlineRatio float64, assignedRatio float64) model.DriverStatus {
	r := rand.Float64()
	if r > 1-onlineRatio {
		return model.StatusOnline
	} else if r > 1-offlineRatio {
		return model.StatusOffline
	} else if r > 1-assignedRatio {
		return model.StatusAssigned
	}
	return model.StatusBanned
}

func randomBool() bool {
	return rand.Float64() > 0.4
}

func generateOrders(env sim.Environment, distributor *distribution.AutoAssignOrderDistributor, state *inMemState, distCfg model.AutoAssignDistribution, ordersPerHour float64, hour int, latFactor float64, lngFactor float64) {
	i := 1
	size := int(ordersPerHour) * hour
	nanoPerOrder := time.Duration(1000 * 1000 * 1000 * 60 * 60 / ordersPerHour)
	for i <= size {
		ctx := context.Background()

		ts := env.Now().Add(time.Duration(i) * nanoPerOrder)
		o := model.Order{
			OrderID:  "LM" + strconv.Itoa(i),
			Status:   model.StatusAssigningDriver,
			History:  make(map[string]time.Time),
			ExpireAt: ts.Add(5 * time.Minute),
			Region:   model.RegionCode("BKK"),
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				CreatedAt:   ts,
				Routes: []model.Stop{
					{Location: model.Location{Lat: 1 + latFactor*rand.Float64(), Lng: 2 + lngFactor*rand.Float64()}},
					{Location: model.Location{Lat: 1 + latFactor*rand.Float64(), Lng: 2 + lngFactor*rand.Float64()}},
				},
				DistributeRegions: model.DistributeRegions{
					"AYUTTHAYA",
				},
			},
		}
		state.updateOrder(o.OrderID, func(_ *model.Order) *model.Order {
			return &o
		})
		businessLocation := o.Routes[0].Location
		regions := []model.RegionCode{"AYUTTHAYA"}

		env.Schedule(func() {
			err := distributor.RunTask(ctx, &o, o.ExpireAt, businessLocation, regions, &distCfg, model.AreaDistributionConfig{
				NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
			})
			if err != nil {
				panic(err)
			}
		}, ts)
		i++
	}
}

func generateActionsFromHttpLog(env sim.Environment, state *inMemState, distributor *distribution.AutoAssignOrderDistributor, distCfg model.AutoAssignDistribution, httpLogFilename string, dist bool) {
	hl, err := httplog.NewLogFile(httpLogFilename)
	if err != nil {
		panic(err)
	}
	defer func() {
		if err := hl.Close(); err != nil {
			panic(err)
		}
	}()

	ap := httplog.NewApiParser()
	for {
		row, err := hl.Read()
		if err == io.EOF {
			break
		}
		if err != nil {
			panic(err)
		}

		action := ap.Parse(row.Uri)
		switch action.Api {
		case "account":
			generateAccountAction(env, state, action, row)
		case "order":
			generateOrderAction(env, state, distributor, distCfg, action, row, dist)
		case "driverOrder":
			generateDriverOrderAction(env, state, action, row, dist)
		default:
		}
		state.updateLogTimestamp(row.Timestamp)
	}
}

func generateAccountAction(env sim.Environment, state *inMemState, action httplog.ApiAction, row httplog.Row) {
	v, ok := row.Headers["X-DRIVER-ID"]
	if !ok {
		v, ok = row.Headers["x-driver-id"]
		if !ok {
			logrus.Warnf("action=%v no X-DRIVER-ID header", action)
			return
		}
	}
	driverID := v.(string)

	switch action.Verb {
	case "status":
		if row.Method != "POST" {
			return
		}
		env.Schedule(func() {
			req := parseUpdateDriverStatusReq(row.Body)
			updateDriverState(state, driverID, func(d *service.DriverWithLocation) {
				if d.Driver.Status == model.StatusOnline {
					d.Driver.Status = req.Status
				}
			})
		}, row.Timestamp)

	case "location":
		env.Schedule(func() {
			req := parseUpdateDriverLocationRequest(row.Body)
			updateDriverState(state, driverID, func(d *service.DriverWithLocation) {
				d.Location = model.Location{Lat: req.Lat, Lng: req.Lng}
			})
		}, row.Timestamp)
	case "balance", "profile", "order-state", "device-token", "order-history", "refresh", "credit", "transactions":
	default:
		logrus.Warnf("action=%v driverID=%s Cannot generate account action.", action, driverID)
	}
}

func updateDriverState(state *inMemState, driverID string, updateFn func(*service.DriverWithLocation)) {
	state.updateDriver(driverID, func(existingDriver *service.DriverWithLocation) *service.DriverWithLocation {
		if existingDriver == nil {
			existingDriver = createNewDriver(driverID)
			state.updateActivityTime(driverID)
		}

		updateFn(existingDriver)

		return existingDriver
	})
}

func createNewDriver(driverID string) *service.DriverWithLocation {
	boxType := []model.BoxType{model.BoxTypeLM, model.BoxTypeStandard, model.BoxTypeNonStandard, model.BoxTypeNone}[rand.Int()%4]
	dl := createDriverWithLocation(createDriver(driverID, model.StatusOnline, model.Options{BoxType: boxType}),
		model.Location{Lat: 1, Lng: 2}, 0)

	return &dl
}

func parseUpdateDriverStatusReq(jsonString string) account.UpdateDriverStatusRequest {
	req := account.UpdateDriverStatusRequest{}
	if err := json.Unmarshal([]byte(jsonString), &req); err != nil {
		panic(err)
	}

	return req
}

func parseUpdateDriverLocationRequest(jsonString string) account.UpdateDriverLocationRequest {
	req := account.UpdateDriverLocationRequest{}
	if err := json.Unmarshal([]byte(jsonString), &req); err != nil {
		panic(err)
	}

	return req
}

func generateOrderAction(env sim.Environment, state *inMemState, distributor *distribution.AutoAssignOrderDistributor, distCfg model.AutoAssignDistribution, action httplog.ApiAction, row httplog.Row, dist bool) {
	switch action.Verb {
	case "quote":
		env.Schedule(func() {
			q := parseQuote(row.Body, row.Region)
			state.updateQuote(q.QuoteID, func(_ *model.Quote) *model.Quote {
				return q
			})
		}, row.Timestamp)
	case "create":
		env.Schedule(func() {
			o := createOrderFromQuote(row.Body, state, row.Region, env)
			if o == nil {
				return
			}
			state.updateOrder(o.OrderID, func(_ *model.Order) *model.Order {
				return o
			})
			if dist {
				logrus.Debugf("order=%s distribute order", o.OrderID)
				_ = distributor.RunTask(context.Background(), o, o.ExpireAt, o.Routes[0].Location, []model.RegionCode{o.Region}, &distCfg, model.AreaDistributionConfig{
					NegativeBalanceGroupsConfig: []model.NegativeBalanceGroupConfig{},
				})
			}
		}, row.Timestamp)
	case "cancel":
		env.Schedule(func() {
			state.cancelOrder(action.ID)
		}, row.Timestamp)
	case "confirm-price", "status":
	default:
		logrus.Warnf("action=%v Cannot generate order action.", action)
	}
}

func generateDriverOrderAction(env sim.Environment, state *inMemState, action httplog.ApiAction, row httplog.Row, dist bool) {
	switch action.Verb {
	case "accept":
		driverID := row.Headers["x-driver-id"].(string)
		env.Schedule(func() {
			state.updateDriver(driverID, func(existingDriver *service.DriverWithLocation) *service.DriverWithLocation {
				if existingDriver.Driver.Status == StatusInActive {
					existingDriver.Driver.Status = model.StatusOnline
				}
				return existingDriver
			})
			state.updateActivityTime(driverID)
			o, _ := state.getOrder(action.ID)
			if o == nil {
				return
			}
			if !dist {
				if o.Status == model.StatusAssigningDriver {
					state.updateOrder(action.ID, func(existingOrder *model.Order) *model.Order {
						existingOrder.Status = model.StatusDriverMatched
						existingOrder.Driver = driverID

						return existingOrder
					})
				}
				state.setAcceptedOrder(o.OrderID, driverID)
			}
		}, row.Timestamp)
	default:
		logrus.Warnf("action=%v Cannot generate order action.", action)
	}
}

func parseQuote(jsonString string, region string) *model.Quote {
	req := order.QuoteRequest{}
	if err := json.Unmarshal([]byte(jsonString), &req); err != nil {
		panic(err)
	}

	fee := model.NewDeliveryFee(50, 9, 10)
	q, err := req.Quote([]model.RegionCode{model.RegionCode(region)}, fee, fee)
	if err != nil {
		panic(err)
	}
	return q
}

func createOrderFromQuote(jsonString string, state *inMemState, region string, env sim.Environment) *model.Order {
	req := order.CreateOrderRequest{}
	if err := json.Unmarshal([]byte(jsonString), &req); err != nil {
		panic(err)
	}

	q := state.getQuote(req.QuoteID)
	if q == nil {
		logrus.Warnf("quote=%s order=%s quote dees not exist", req.QuoteID, req.QuoteID)
		return nil
	}

	o := model.Order{
		OrderID:  req.OrderID,
		Quote:    *q,
		Region:   model.RegionCode(region),
		Status:   model.StatusAssigningDriver,
		History:  make(map[string]time.Time),
		ExpireAt: env.Now().Add(5 * time.Minute),
	}
	o.CreatedAt = env.Now()

	return &o
}

type inMemState struct {
	env                     sim.Environment
	cookingDuration         time.Duration
	driverAverageSpeedInKmh float64
	incentiveStep           int64
	acceptChance            float64
	maxOnlineDuration       time.Duration

	logLastTimestamp           time.Time
	drivers                    map[string]*service.DriverWithLocation
	driversLock                sync.RWMutex
	quotes                     map[string]*model.Quote
	quotesLock                 sync.RWMutex
	ordersByID                 map[string]*model.Order
	ordersLock                 sync.RWMutex
	assignmentLogsByOrderId    map[string]*model.AssignmentLogRecord
	assignmentLogsLock         sync.RWMutex
	driverStatistics           map[string]*model.DriverStatistic
	driverStatisticsLock       sync.RWMutex
	expiredValues              map[string]*expiredValue
	expiredValuesLock          sync.RWMutex
	driverRedisLockValues      map[string]bool
	driverRedisLockValuesLock  sync.RWMutex
	acceptedOrders             map[string]*acceptedOrder
	acceptedOrdersLock         sync.RWMutex
	driverLatestActivities     map[string]time.Time
	driverLatestActivitiesLock sync.RWMutex
}

type expiredValue struct {
	value     string
	expiredAt time.Time
}

type acceptedOrder struct {
	order          *model.Order
	driver         string
	driverLocation model.Location
	acceptedAt     time.Time
	finishedAt     time.Time
}

func NewInMemState(env sim.Environment) *inMemState {
	return &inMemState{
		env:                     env,
		cookingDuration:         6 * time.Minute,
		driverAverageSpeedInKmh: 50,
		incentiveStep:           10,
		acceptChance:            0.9,
		maxOnlineDuration:       10 * time.Minute,

		drivers:                 make(map[string]*service.DriverWithLocation),
		quotes:                  make(map[string]*model.Quote),
		ordersByID:              make(map[string]*model.Order),
		assignmentLogsByOrderId: make(map[string]*model.AssignmentLogRecord),
		driverStatistics:        make(map[string]*model.DriverStatistic),
		expiredValues:           make(map[string]*expiredValue),
		driverRedisLockValues:   make(map[string]bool),
		acceptedOrders:          make(map[string]*acceptedOrder),
		driverLatestActivities:  make(map[string]time.Time),
	}
}

func (s *inMemState) init(m *mocks) {
	rand.Seed(1)

	m.mockDelivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	m.mockFeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledAssignAPIOnDistribution.Name, gomock.Any()).Return(false).AnyTimes()
	m.mockOrderRepository.EXPECT().GetExDrivers(gomock.Any(), gomock.Any()).Return(types.NewStringSet(), nil).AnyTimes()
	m.mockOrderRepository.EXPECT().SetProcessedBySingleDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	m.mockLocationManager.EXPECT().SearchDriversLimit(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, loc model.Location, toM float64, limit int, srv model.Service, region model.RegionCode, orderID string) ([]service.DriverWithLocation, error) {
			return s.searchDrivers(loc)
		}).AnyTimes()

	m.mockDriverRepository.EXPECT().GetMinimalProfilesByID(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, driverIDs []string) (map[string]*model.DriverMinimal, error) {
			return s.getMinimalProfilesByID(ctx, driverIDs)
		}).AnyTimes()

	m.mockNotifier.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, driverIDs []string, data map[string]string, opts ...service.NotifyOption) error {
			return s.notify()
		}).AnyTimes()

	m.mockOrderRepository.EXPECT().SetAsDistributed(gomock.Any(), gomock.Any()).AnyTimes()

	m.mockServiceAreaRepository.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(s.getDriverServiceArea(), nil).AnyTimes()
	m.mockRainSituationService.EXPECT().IsRainingByRegionAndLocation(gomock.Any(), gomock.Any(), gomock.Any()).Return(true).AnyTimes()

	m.mockAssigmentLogRepo.EXPECT().AssignToDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, r int, orderID string, deliveringRound int, driverDists []repository.DriverDistance, option ...model.AssignmentLogOpt) (*model.AssignmentLogRecord, error) {
			return nil, s.assignToDrivers(r, orderID, deliveringRound, driverDists)
		}).AnyTimes()

	m.mockAssigmentLogRepo.EXPECT().UpdateDriverNotAcceptAutoAssignedOrder(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, orderID, driverID string) error {
			return s.updateDriverNotAcceptAutoAssignedOrder(orderID, driverID)
		}).AnyTimes()

	m.mockAssigmentLogRepo.EXPECT().GetManyAssignedDrivers(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, orderIDs []string, opts ...repository.Option) (map[string]model.AssignmentLogRecord, error) {
			return nil, nil
		}).AnyTimes()
	m.mockAssigmentLogRepo.EXPECT().AssignedDrivers(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, orderID string, opts ...repository.Option) ([]model.Record, error) {
			return s.assignedDrivers()
		}).AnyTimes()

	m.mockLocker.EXPECT().Lock(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, lockKey string, opts ...locker.Option) (bool, error) {
			if s.driverRedisLock(lockKey) {
				return true, nil
			} else {
				return false, errors.New("fake error to skip retry")
			}
		}).AnyTimes()

	m.mockLocker.EXPECT().Unlock(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, lockKey string) error {
			s.driverRedisUnlock(lockKey)
			return nil
		}).AnyTimes()

	m.mockOrderRepository.EXPECT().GetMany(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, ordids []string, opts ...repository.Option) ([]model.Order, error) {
			order, err := s.getOrder(ordids[0])
			if err != nil {
				return nil, err
			}
			return []model.Order{*order}, nil
		}).AnyTimes()

	m.mockDriverOrderInfoRepo.EXPECT().GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DriverOrderInfo{}, nil).AnyTimes()
	m.mockStatisticRepo.EXPECT().FindByDriverIDs(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, driverIDs []string) ([]model.DriverStatistic, error) {
			return s.findByDriverIDs(driverIDs)
		}).AnyTimes()

	m.mockIncentiveRepo.EXPECT().GetActiveIncentiveByRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return(nil, nil).AnyTimes()

	m.mockStatisticService.EXPECT().UpdateNotified(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, orderID, driverID string) error {
			return s.updateNotified(driverID)
		}).AnyTimes()

	m.mockLocker.EXPECT().SetState(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, stateKey, value string, ttl time.Duration) bool {
			return s.setExpiredValue(stateKey, value, ttl)
		}).AnyTimes()

	m.mockLocker.EXPECT().RemoveState(gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, stateKey string) bool {
			return s.removeExpiredValue(stateKey)
		}).AnyTimes()

	m.mockDriverTransaction.EXPECT().GetDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
		DoAndReturn(func(ctx context.Context, driverID string, option repository.Option) (model.DriverTransaction, error) {
			return s.getDriverTransaction()
		}).AnyTimes()

	m.mockEventBus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	m.dedicatedZoneRepo.EXPECT().FindAllInCache(gomock.Any()).Return(nil, nil).AnyTimes()

	m.mockOnTopFareService.EXPECT().GetOnTopFareAtDistribution(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{}, nil).AnyTimes()

	m.mockOrderRepository.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	m.mockOrderRepository.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	m.mockOrderRepository.EXPECT().SetRedistributionState(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	m.mockDistributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()

	m.mockHearthBeatService.EXPECT().New(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	m.mockHearthBeatService.EXPECT().Done(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
}

type driverUpdater func(existingDriver *service.DriverWithLocation) *service.DriverWithLocation
type orderUpdater func(existingOrder *model.Order) *model.Order
type quoteUpdater func(existingQuote *model.Quote) *model.Quote

func (s *inMemState) updateDriver(id string, updater driverUpdater) *service.DriverWithLocation {
	s.driversLock.Lock()
	defer s.driversLock.Unlock()
	var result *service.DriverWithLocation
	d := s.drivers[id]
	result = updater(d)
	if result == nil {
		delete(s.drivers, id)
	} else {
		s.drivers[id] = result
	}
	return result
}

func (s *inMemState) getDriver(id string) *service.DriverWithLocation {
	s.driversLock.RLock()
	defer s.driversLock.RUnlock()

	return s.drivers[id]
}

func (s *inMemState) updateOrder(id string, updater orderUpdater) *model.Order {
	s.ordersLock.Lock()
	defer s.ordersLock.Unlock()
	var result *model.Order
	o := s.ordersByID[id]
	result = updater(o)
	if result == nil {
		delete(s.ordersByID, id)
	} else {
		s.ordersByID[id] = result
	}
	return result
}

func (s *inMemState) updateQuote(id string, updater quoteUpdater) *model.Quote {
	s.quotesLock.Lock()
	defer s.quotesLock.Unlock()
	var result *model.Quote
	q := s.quotes[id]
	result = updater(q)
	if result == nil {
		delete(s.ordersByID, id)
	} else {
		s.quotes[id] = result
	}
	return result
}

func (s *inMemState) searchDrivers(location model.Location) ([]service.DriverWithLocation, error) {
	s.driversLock.RLock()
	defer s.driversLock.RUnlock()

	matched := make([]service.DriverWithLocation, 0, len(s.drivers))

	for _, d := range s.drivers {
		cd := cloneDriver(*d)
		cd.DistanceMeter = geo.DistanceInKm(location.Lat, location.Lng, cd.Location.Lat, cd.Location.Lng)

		matched = append(matched, *cd)
	}

	return matched, nil
}

func cloneDriver(d service.DriverWithLocation) *service.DriverWithLocation {
	return &d
}

func (s *inMemState) getMinimalProfilesByID(_ context.Context, ds []string) (map[string]*model.DriverMinimal, error) {
	s.driversLock.RLock()
	defer s.driversLock.RUnlock()

	m := make(map[string]*model.DriverMinimal)

	for _, driverID := range ds {
		for _, d := range s.drivers {
			dm := d.Driver
			if dm.DriverID == driverID {
				m[d.Driver.DriverID] = &dm
			}
		}
	}

	return m, nil
}

func (s *inMemState) assignToDrivers(r int, orderID string, deliveringRond int, driverDists []repository.DriverDistance) error {
	s.assignmentLogsLock.Lock()
	defer s.assignmentLogsLock.Unlock()
	al, ok := s.assignmentLogsByOrderId[orderID]
	if !ok {
		al = &model.AssignmentLogRecord{OrderID: orderID, DeliveringRound: deliveringRond}
	}
	for _, driverDist := range driverDists {
		al.Drivers = append(al.Drivers, model.Record{
			DriverID:        driverDist.DriverID,
			DistanceInMeter: driverDist.Distance,
			PushAt:          s.env.Now(),
			Round:           "R" + strconv.Itoa(r),
		})
	}
	s.assignmentLogsByOrderId[orderID] = al

	if len(driverDists) == 0 {
		return nil
	}
	if rand.Float64() < 1-s.acceptChance {
		return nil
	}
	driverID := driverDists[0].DriverID
	d := s.getDriver(driverID)
	if d.Driver.Status != model.StatusOnline {
		logrus.Warnf("envtime=%v driverID=%s status=%s assigned to not online driver.", s.env.Now(), driverID, d.Driver.Status)
		return nil
	}

	s.updateOrder(orderID, func(o *model.Order) *model.Order {
		o.AssignedRound = "R" + strconv.Itoa(r)
		o.SetStatus(model.StatusDriverMatched)
		o.Driver = driverID
		return o
	})

	s.updateDriver(driverID, func(d *service.DriverWithLocation) *service.DriverWithLocation {
		d.Driver.Status = model.StatusAssigned
		return d
	})

	s.removeExpiredValue(locker.DriverAutoAssignState(driverID))

	s.setAcceptedOrder(orderID, driverID)

	s.env.Schedule(func() {
		o, _ := s.getOrder(orderID)

		s.updateDriver(driverID, func(d *service.DriverWithLocation) *service.DriverWithLocation {
			if d.Driver.Status == model.StatusAssigned {
				d.Driver.Status = model.StatusOnline
			}
			d.Location = o.Routes[1].Location
			return d
		})

		s.completeOrder(orderID)

		err := s.updateDone(driverID)
		if err != nil {
			panic(err)
		}
	}, s.env.Now().Add(s.getJobDuration(orderID, d.Location)))

	return nil
}

func (s *inMemState) updateDriverNotAcceptAutoAssignedOrder(orderID, driverID string) error {
	s.assignmentLogsLock.Lock()
	defer s.assignmentLogsLock.Unlock()
	al, ok := s.assignmentLogsByOrderId[orderID]
	if !ok {
		al = &model.AssignmentLogRecord{OrderID: orderID}
	}
	for i, driver := range al.Drivers {
		if driver.DriverID == driverID {
			al.Drivers[i].IsNotAcceptAutoAssignedOrder = true
		}
	}
	s.assignmentLogsByOrderId[orderID] = al
	return nil
}

func (s *inMemState) assignedDrivers() ([]model.Record, error) {
	return []model.Record{}, mongodb.ErrDataNotFound
}

func (s *inMemState) notify() error {
	return nil
}

func (s *inMemState) getOrder(ordid string) (*model.Order, error) {
	s.ordersLock.RLock()
	defer s.ordersLock.RUnlock()
	o := s.ordersByID[ordid]
	if o != nil {
		ro := *o
		return &ro, nil
	} else {
		return nil, fmt.Errorf("not found")
	}
}

func (s *inMemState) cancelOrder(id string) {
	o := s.updateOrder(id, func(o *model.Order) *model.Order {
		if o == nil {
			logrus.Warnf("orderid=%s cannot cancel non existing order.", id)
			return nil
		}
		o.SetStatus(model.StatusCanceled)
		return o
	})

	if o != nil && o.Driver != "" {
		s.updateDriver(o.Driver, func(existingDriver *service.DriverWithLocation) *service.DriverWithLocation {
			if existingDriver != nil && existingDriver.Driver.Status == model.StatusAssigned {
				existingDriver.Driver.Status = model.StatusOnline
			}

			return existingDriver
		})
	}
}

func (s *inMemState) getQuote(id string) *model.Quote {
	s.quotesLock.RLock()
	defer s.quotesLock.RUnlock()

	return s.quotes[id]
}

func (s *inMemState) findByDriverIDs(driverIDs []string) ([]model.DriverStatistic, error) {
	s.driverStatisticsLock.RLock()
	defer s.driverStatisticsLock.RUnlock()

	result := make([]model.DriverStatistic, 0, len(driverIDs))

	for _, driverID := range driverIDs {
		ds, ok := s.driverStatistics[driverID]
		if ok {
			result = append(result, *ds)
		}
	}

	return result, nil
}

func (s *inMemState) updateNotified(driverID string) error {
	s.driverStatisticsLock.Lock()
	defer s.driverStatisticsLock.Unlock()

	ds, ok := s.driverStatistics[driverID]
	if !ok {
		ds = model.NewDriverStatistic(driverID)
		s.driverStatistics[driverID] = ds
	}
	ds.AddNotified(s.env.Now())

	return nil
}

func (s *inMemState) updateDone(driverID string) error {
	s.driverStatisticsLock.Lock()
	defer s.driverStatisticsLock.Unlock()

	ds, ok := s.driverStatistics[driverID]
	if ok {
		ds.AddDone(s.env.Now(), true)
		total := ds.DailyStatistic[timeutil.ToYYYYMMDD(s.env.Now())].Done
		nextIncentive := total + (s.incentiveStep - (total % s.incentiveStep))
		ds.UpdateDailyNextIncentive(s.env.Now(), nextIncentive)
	}

	return nil
}

func (s *inMemState) setExpiredValue(key string, value string, ttl time.Duration) bool {
	s.expiredValuesLock.Lock()
	defer s.expiredValuesLock.Unlock()

	ev, ok := s.expiredValues[key]
	if ok && s.env.Now().Before(ev.expiredAt) {
		return false
	}

	s.expiredValues[key] = &expiredValue{
		value:     value,
		expiredAt: s.env.Now().Add(ttl),
	}

	return true
}

func (s *inMemState) removeExpiredValue(key string) bool {
	s.expiredValuesLock.Lock()
	defer s.expiredValuesLock.Unlock()

	delete(s.expiredValues, key)

	return true
}

func (s *inMemState) driverRedisLock(lockKey string) bool {
	s.driverRedisLockValuesLock.Lock()
	defer s.driverRedisLockValuesLock.Unlock()

	lv, ok := s.driverRedisLockValues[lockKey]
	if ok && lv {
		return false
	}

	s.driverRedisLockValues[lockKey] = true
	return true
}

func (s *inMemState) driverRedisUnlock(lockKey string) bool {
	s.driverRedisLockValuesLock.Lock()
	defer s.driverRedisLockValuesLock.Unlock()

	lv, ok := s.driverRedisLockValues[lockKey]
	if ok && lv {
		return false
	}

	delete(s.driverRedisLockValues, lockKey)
	return true
}

func (s *inMemState) setAcceptedOrder(orderID string, driverID string) {
	s.acceptedOrdersLock.Lock()
	defer s.acceptedOrdersLock.Unlock()
	o, _ := s.getOrder(orderID)

	s.driversLock.RLock()
	defer s.driversLock.RUnlock()
	d := s.drivers[driverID]

	s.acceptedOrders[orderID] = &acceptedOrder{
		order:          o,
		driver:         d.Driver.DriverID,
		driverLocation: d.Location,
		acceptedAt:     s.env.Now(),
	}
}

func (s *inMemState) completeOrder(orderID string) {
	s.acceptedOrdersLock.Lock()
	defer s.acceptedOrdersLock.Unlock()

	ao := s.acceptedOrders[orderID]
	ao.finishedAt = s.env.Now()

	s.acceptedOrders[orderID] = ao
}

func (s *inMemState) getDriverTransaction() (model.DriverTransaction, error) {
	return model.DriverTransaction{}, nil
}

func (s *inMemState) updateActivityTime(driverID string) {
	s.driverLatestActivitiesLock.Lock()
	s.driverLatestActivities[driverID] = s.env.Now()
	s.driverLatestActivitiesLock.Unlock()

	if s.maxOnlineDuration <= 0 {
		return
	}
	s.env.Schedule(func() {
		s.trySetInActive(driverID)
	}, s.env.Now().Add(s.maxOnlineDuration))
}

func (s *inMemState) trySetInActive(driverID string) {
	if s.env.Now().After(s.logLastTimestamp) {
		return
	}

	s.updateDriver(driverID, func(existingDriver *service.DriverWithLocation) *service.DriverWithLocation {
		if existingDriver == nil {
			return existingDriver
		}

		s.driverLatestActivitiesLock.RLock()
		defer s.driverLatestActivitiesLock.RUnlock()

		t, ok := s.driverLatestActivities[driverID]
		if !ok || s.env.Now().Sub(t) >= s.maxOnlineDuration {
			existingDriver.Driver.Status = StatusInActive
		}

		return existingDriver
	})
}

func (s *inMemState) updateLogTimestamp(timestamp time.Time) {
	if timestamp.After(s.logLastTimestamp) {
		s.logLastTimestamp = timestamp
	}
}

func (s *inMemState) getJobDuration(orderID string, driverLocation model.Location) time.Duration {
	o, _ := s.getOrder(orderID)

	businessLocation := o.Routes[0].Location
	customerLocation := o.Routes[1].Location

	distance1 := geo.DistanceInKm(driverLocation.Lat, driverLocation.Lng, businessLocation.Lat, businessLocation.Lng)
	distance2 := geo.DistanceInKm(businessLocation.Lat, businessLocation.Lng, customerLocation.Lat, customerLocation.Lng)

	speedPerNanoS := s.driverAverageSpeedInKmh / (1000 * 1000 * 1000 * 60 * 60)
	t1 := time.Duration(distance1 / speedPerNanoS)
	t2 := time.Duration(distance2 / speedPerNanoS)

	if t1 < s.cookingDuration {
		t1 = s.cookingDuration
	}

	return t1 + t2
}

func (s *inMemState) getDriverServiceArea() *model.ServiceArea {
	srv := &model.ServiceArea{}
	return srv
}

func printReport(state *inMemState) {
	orderStatusLabels := newBarLabels([]string{"ASSIGNING_DRIVER", "DRIVER_MATCHED"})
	printBar("Number of orders by state", createOrderStatusBarData(state.ordersByID, orderStatusLabels), orderStatusLabels)

	driverStatusLabels := newBarLabels([]string{"OFFLINE", "ONLINE", "ASSIGNED", "BANNED"})
	printBar("Number of drivers by status", createDriverStatusBarData(state.drivers, driverStatusLabels), driverStatusLabels)

	numberOfAssignedLabels := newBarLabels([]string{"no assigned order"})
	printBar("Number of drivers by number of assigned orders", createAssignedOrderNumbersBarData(state.assignmentLogsByOrderId, numberOfAssignedLabels, len(state.drivers)), numberOfAssignedLabels)

	numberOfOrdersLabels := newBarLabels([]string{"no order"})
	printBar("Number of drivers by number of accepted orders", createOrderNumbersBarData(state.ordersByID, numberOfOrdersLabels, len(state.drivers)), numberOfOrdersLabels)

	numberOfAcceptedOrdersLabels := newBarLabels([]string{"R1", "R2"})
	printBar("Number of orders by accepted round", createOrderAcceptedRoundBarData(state.ordersByID, numberOfAcceptedOrdersLabels), numberOfAcceptedOrdersLabels)

	printTimeBar("Number of created orders by time", createOrderHistData(state.ordersByID, selectAllOrders))
	printTimeBar("Number of not accepted orders by time", createOrderHistData(state.ordersByID, selectAssigningOrders))
	printTimeBar("Number of accepted orders by time", createOrderHistData(state.ordersByID, selectMatchedOrders))
	printTimeBar("Number of cancelled orders by time", createOrderHistData(state.ordersByID, selectCancelledOrders))
	printTimeBar("Number of inactive drivers by time", createInActiveDriverHistData(state.driverLatestActivities))

	printDriverDistanceHistogram(state.acceptedOrders)
	printOrderDurationHistogram(state.acceptedOrders)
}

func createDriverStatusBarData(driversByID map[string]*service.DriverWithLocation, driverStateLabelResolver *barLabels) [][2]int {
	data := make([][2]int, 0, len(driversByID))

	for _, d := range driversByID {
		k := driverStateLabelResolver.getOrCreateKey(string(d.Driver.Status))
		data = append(data, [2]int{k, 1})
	}

	return data
}

type barLabels struct {
	labelToKey map[string]int
	keyToLabel map[float64]string
}

func newBarLabels(labels []string) *barLabels {
	bls := barLabels{
		labelToKey: make(map[string]int),
		keyToLabel: make(map[float64]string),
	}
	for _, l := range labels {
		bls.getOrCreateKey(l)
	}
	return &bls
}

func (bls *barLabels) getLabel(value float64) string {
	l, ok := bls.keyToLabel[value]
	if !ok {
		return fmt.Sprintf("%v", value)
	}
	return l
}

func (bls *barLabels) getOrCreateKey(label string) int {
	k, ok := bls.labelToKey[label]
	if !ok {
		k = len(bls.labelToKey) + 1
		bls.labelToKey[label] = k
		bls.keyToLabel[float64(k)] = label
	}
	return k
}

func (bls *barLabels) countKey() int {
	return len(bls.keyToLabel)
}

func createOrderStatusBarData(ordersByID map[string]*model.Order, bls *barLabels) [][2]int {
	data := make([][2]int, 0, len(ordersByID))

	for _, o := range ordersByID {
		k := bls.getOrCreateKey(string(o.Status))
		data = append(data, [2]int{k, 1})
	}

	return data
}

func createOrderNumbersBarData(ordersByID map[string]*model.Order, bls *barLabels, allDrivers int) [][2]int {
	max := 0
	ordersByDriverID := make(map[string]int)
	for _, o := range ordersByID {
		if o.Status != model.StatusDriverMatched {
			continue
		}
		orders, ok := ordersByDriverID[o.Driver]
		if !ok {
			orders = 0
		}
		orders = orders + 1
		ordersByDriverID[o.Driver] = orders
		if orders > max {
			max = orders
		}
	}
	for i := 1; i <= max; i++ {
		bls.getOrCreateKey(strconv.Itoa(i) + " orders")
	}

	data := make([][2]int, 0, len(ordersByDriverID))
	data = append(data, [2]int{bls.getOrCreateKey("no order"), allDrivers - len(ordersByDriverID)})
	for _, n := range ordersByDriverID {
		k := bls.getOrCreateKey(strconv.Itoa(n) + " orders")
		data = append(data, [2]int{k, 1})
	}

	return data
}

func createAssignedOrderNumbersBarData(assignmentLogsByOrderID map[string]*model.AssignmentLogRecord, bls *barLabels, allDrivers int) [][2]int {
	max := 0
	ordersByDriverID := make(map[string]int)
	for _, al := range assignmentLogsByOrderID {
		for _, r := range al.Drivers {
			orders, ok := ordersByDriverID[r.DriverID]
			if !ok {
				orders = 0
			}
			orders = orders + 1
			ordersByDriverID[r.DriverID] = orders
			if orders > max {
				max = orders
			}
		}
	}
	for i := 1; i <= max; i++ {
		bls.getOrCreateKey(strconv.Itoa(i) + " assigned orders")
	}

	data := make([][2]int, 0, len(ordersByDriverID))
	data = append(data, [2]int{bls.getOrCreateKey("no assigned order"), allDrivers - len(ordersByDriverID)})
	for _, n := range ordersByDriverID {
		k := bls.getOrCreateKey(strconv.Itoa(n) + " assigned orders")
		data = append(data, [2]int{k, 1})
	}

	return data
}

func createOrderAcceptedRoundBarData(ordersByID map[string]*model.Order, bls *barLabels) [][2]int {
	data := make([][2]int, 0, len(ordersByID))

	for _, o := range ordersByID {
		if o.Status != model.StatusDriverMatched {
			continue
		}
		k := bls.getOrCreateKey(o.AssignedRound)
		data = append(data, [2]int{k, 1})
	}

	return data
}

func createOrderHistData(ordersByID map[string]*model.Order, selector func(*model.Order) float64) []float64 {
	data := make([]float64, 0, len(ordersByID))
	for _, o := range ordersByID {
		v := selector(o)
		if v < 0 {
			continue
		}
		data = append(data, v)
	}

	return data
}

func selectAllOrders(o *model.Order) float64 {
	return float64(o.CreatedAt.Unix())
}

func selectAssigningOrders(o *model.Order) float64 {
	if o.Status != model.StatusAssigningDriver {
		return -1
	}
	return selectAllOrders(o)
}

func selectMatchedOrders(o *model.Order) float64 {
	if o.Status != model.StatusDriverMatched {
		return -1
	}
	return selectAllOrders(o)
}

func selectCancelledOrders(o *model.Order) float64 {
	if o.Status != model.StatusCanceled {
		return -1
	}
	return selectAllOrders(o)
}

func printBar(name string, data [][2]int, bls *barLabels) {
	numberOfData := 0
	for _, v := range data {
		numberOfData += v[1]
	}
	keys := bls.countKey()
	if keys < 2 {
		keys = 2
	}
	for i := 1; i <= keys; i++ {
		data = append(data, [2]int{i, 0})
	}

	bc := barchart.BarChartXYs(data)
	fmt.Printf("%s (%v)\n==========\n", name, numberOfData)
	err := barchart.Fprintf(
		os.Stdout,
		bc,
		bc.MaxX,
		barchart.Linear(50),
		func(v float64) string {
			l := bls.getLabel(v)
			return l
		}, func(v float64) string {
			return fmt.Sprintf("%v (%v%%)", v, v*100/float64(numberOfData))
		})
	if err != nil {
		panic(err)
	}
	fmt.Println()
}

func unixTimeHourFormatter(v float64) string {
	t := time.Unix(int64(v), 0)

	return t.Format("02 15") + ":00:00"
}

func printTimeBar(name string, timeKeys []float64) {
	min := 0.0
	max := 0.0
	for _, v := range timeKeys {
		if min > v || min == 0 {
			min = v
		}
		if max < v {
			max = v
		}
	}
	barData := make([][2]int, 0, len(timeKeys))

	secondsInHour := float64(60) * 60
	bls := newBarLabels([]string{})
	for i := min; i <= max; i += secondsInHour {
		k := bls.getOrCreateKey(unixTimeHourFormatter(i))
		barData = append(barData, [2]int{k, 0})
	}
	for _, v := range timeKeys {
		k := bls.getOrCreateKey(unixTimeHourFormatter(v))
		barData = append(barData, [2]int{k, 1})
	}
	for i := 0; len(bls.labelToKey) < 2; i++ {
		k := bls.getOrCreateKey("filled " + strconv.Itoa(i))
		barData = append(barData, [2]int{k, 0})
	}

	printBar(name, barData, bls)
}

func createInActiveDriverHistData(driverLatestActivities map[string]time.Time) []float64 {
	data := make([]float64, 0, len(driverLatestActivities))
	for _, t := range driverLatestActivities {
		data = append(data, float64(t.Unix()))
	}

	return data
}

func printDriverDistanceHistogram(acceptedOrders map[string]*acceptedOrder) {
	distsInMeter := make([]int, 0, len(acceptedOrders))
	minDist := 1000000
	maxDist := 0
	for _, ao := range acceptedOrders {
		bl := ao.order.Routes[0].Location
		dl := ao.driverLocation

		dist := int(geo.DistanceInKm(bl.Lat, bl.Lng, dl.Lat, dl.Lng) * 1000)

		if dist > maxDist {
			maxDist = dist
		}
		if dist < minDist {
			minDist = dist
		}

		distsInMeter = append(distsInMeter, dist)
	}

	span := 100
	bls := newBarLabels([]string{})
	i := (minDist / span) * span
	for i < maxDist {
		bls.getOrCreateKey(strconv.Itoa(i) + "-" + strconv.Itoa(i+span))
		i = i + span
	}
	barData := make([][2]int, 0)

	for _, dist := range distsInMeter {
		g := (dist / span) * span
		k := bls.getOrCreateKey(strconv.Itoa(g) + "-" + strconv.Itoa(g+span))

		barData = append(barData, [2]int{k, 1})
	}

	printBar("Driver distance histogram (m)", barData, bls)
}

func printOrderDurationHistogram(acceptedOrders map[string]*acceptedOrder) {
	durationsInSecond := make([]int, 0, len(acceptedOrders))
	minDuration := 10000000000
	maxDuration := 0
	for _, ao := range acceptedOrders {
		d := int(ao.finishedAt.Sub(ao.acceptedAt).Seconds())

		if d < 0 {
			continue
		}
		if d > maxDuration {
			maxDuration = d
		}
		if d < minDuration {
			minDuration = d
		}

		durationsInSecond = append(durationsInSecond, d)
	}
	span := 30
	bls := newBarLabels([]string{})
	i := (minDuration / span) * span
	for i < maxDuration {
		bls.getOrCreateKey(formatDuration(i) + "-" + formatDuration(i+span))
		i = i + span
	}
	barData := make([][2]int, 0)

	for _, d := range durationsInSecond {
		g := (d / span) * span
		k := bls.getOrCreateKey(formatDuration(g) + "-" + formatDuration(g+span))

		barData = append(barData, [2]int{k, 1})
	}

	printBar("Order duration histogram (s)", barData, bls)
}

func formatDuration(i int) string {
	d := time.Duration(i) * time.Second

	return d.String()
}
