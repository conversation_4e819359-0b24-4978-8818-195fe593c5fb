package order

import "git.wndv.co/lineman/absinthe/api"

type UpdateMultipleOrderStatusRes struct {
	Errors []UpdateOrderStatusError `json:"errors,omitempty"`
}

type UpdateOrderStatusError struct {
	OrderID string `json:"orderId"`
	Error   string `json:"error"`
	Title   string `json:"title"`
	Detail  string `json:"detail"`
}

func (res *UpdateMultipleOrderStatusRes) Error(orderID string, err error) {
	updateError := UpdateOrderStatusError{
		OrderID: orderID,
		Error:   err.Error(),
	}

	if apiError, ok := err.(*api.Error); ok {
		updateError.Title = getStringFromMap(apiError.Info, "title")
		updateError.Detail = getStringFromMap(apiError.Info, "detail")
	}

	res.Errors = append(res.Errors, updateError)
}

func getStringFromMap(info map[string]interface{}, key string) string {
	s, ok := info[key].(string)
	if !ok {
		return ""
	}
	return s
}
