package order

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/gin-gonic/gin"
	legacyMock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/mock/gomock"

	"git.wndv.co/lineman/absinthe/api"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/delivery-service/pkg/client"
	v1 "git.wndv.co/lineman/delivery-service/types/v1"
	v2 "git.wndv.co/lineman/delivery-service/types/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/mock_order"
	"git.wndv.co/lineman/fleet-distribution/internal/aws"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery/mock_delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher/mock_dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker/mock_locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/mock_mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep/mock_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/mock_event_bus"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/fraud/mock_fraud"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func init() {
	gin.SetMode(gin.ReleaseMode)
}

func TestOrderAPI_GetOrderDetail(t *testing.T) {
	expectedSubdistrict := "ตำบล ในเมือง"
	addressWithSubdistrict := fmt.Sprintf("99/99 ถนน %s อำเภอ", expectedSubdistrict)
	ord := model.Order{
		OrderID: "testorder",
		Quote: model.Quote{
			Routes: []model.Stop{
				{
					Address: addressWithSubdistrict,
				},
				{
					Address: addressWithSubdistrict,
				},
			},
		},
	}

	autoAssignedOrd := ord
	autoAssignedOrd.Distribution = model.DistributionLogicAutoAssign

	drv := model.Driver{
		DriverID: "testdriver",
	}
	dt := model.DriverTransaction{DriverID: "driver-1", PurchaseCreditBalance: 100}

	otherdrv := drv
	otherdrv.DriverID = "otherdriver"

	ordAssigned := ord
	ordAssigned.Driver = "testdriver"

	ordCancel := ord
	ordCancel.Driver = "testdriver"
	ordCancel.Status = model.StatusCanceled

	t.Run("doesn't return subdistrict field if disabled via config", func(tt *testing.T) {
		api, ctxRec := newGetOrderDetailTest(tt, ord, drv, dt, false)
		api.cfg.SubdistrictEnabled = false

		api.GetOrderDetail(ctxRec.GinCtx())

		res := getOrderDetailResponse(ctxRec, tt)
		require.NotContains(tt, res.Routes[0], "subdistrict")
		require.Contains(tt, res.Routes[0], "address")
		require.Nil(tt, res.Captcha)
	})

	t.Run("returns subdistrict if enabled via config", func(tt *testing.T) {
		api, ctxRec := newGetOrderDetailTest(tt, ord, drv, dt, false)
		api.cfg.SubdistrictEnabled = true

		api.GetOrderDetail(ctxRec.GinCtx())

		var res OrderDetailRes
		ctxRec.DecodeJSONResponse(&res)
		ctxRec.AssertResponseCode(tt, 200)
		require.Equal(tt, expectedSubdistrict, res.Routes[0].Subdistrict)
	})

	t.Run("get auto-assigned order detail should not return captcha", func(tt *testing.T) {
		api, ctxRec := newGetOrderDetailTest(tt, autoAssignedOrd, drv, dt, true)

		api.GetOrderDetail(ctxRec.GinCtx())

		res := getOrderDetailResponse(ctxRec, tt)
		require.Nil(tt, res.Captcha)
	})

	t.Run("return success when assigned and match driver id", func(tt *testing.T) {
		odapi, ctxRec := newGetOrderDetailTest(tt, ordAssigned, drv, dt, false)
		odapi.cfg.EnableHandleMatchDriverOrderDetail = true
		odapi.GetOrderDetail(ctxRec.GinCtx())

		AssertSuccess(ctxRec, tt)
	})

	t.Run("return error when assigned and NOT match driver id", func(tt *testing.T) {
		odapi, ctxRec := newErrorGetOrderDetailTest(tt, ordAssigned, otherdrv)
		odapi.cfg.EnableHandleMatchDriverOrderDetail = true
		odapi.GetOrderDetail(ctxRec.GinCtx())

		AssertFail(ctxRec, tt, "ORDER_ALREADY_TAKEN")
	})

	t.Run("not return error when assigned but disable config", func(tt *testing.T) {
		odapi, ctxRec := newGetOrderDetailTest(tt, ordAssigned, otherdrv, dt, false)
		odapi.cfg.EnableHandleMatchDriverOrderDetail = false
		odapi.GetOrderDetail(ctxRec.GinCtx())

		AssertSuccess(ctxRec, tt)
	})

	t.Run("return success when not assigned", func(tt *testing.T) {
		odapi, ctxRec := newGetOrderDetailTest(tt, ord, drv, dt, false)
		odapi.cfg.EnableHandleMatchDriverOrderDetail = true
		odapi.GetOrderDetail(ctxRec.GinCtx())

		AssertSuccess(ctxRec, tt)
	})

	t.Run("return success when StatusCanceled and match driver id", func(tt *testing.T) {
		odapi, ctxRec := newGetOrderDetailTest(tt, ordCancel, drv, dt, false)
		odapi.cfg.EnableHandleMatchDriverOrderDetail = true
		odapi.GetOrderDetail(ctxRec.GinCtx())

		AssertSuccess(ctxRec, tt)
	})

	t.Run("return error when StatusCanceled and NOT match driver id", func(tt *testing.T) {
		odapi, ctxRec := newErrorGetOrderDetailTest(tt, ordCancel, otherdrv)
		odapi.cfg.EnableHandleMatchDriverOrderDetail = true
		odapi.GetOrderDetail(ctxRec.GinCtx())

		AssertFail(ctxRec, tt, "ORDER_CANCELED")
	})

	t.Run("return tipAmount on order with tip", func(tt *testing.T) {
		ord.TipAmount = types.NewMoney(500.0)
		api, ctxRec := newGetOrderDetailTest(tt, ord, drv, dt, false)
		api.GetOrderDetail(ctxRec.GinCtx())
		res := getOrderDetailResponse(ctxRec, tt)
		require.Equal(tt, res.TipAmount, types.NewMoney(500.0))
	})

	t.Run("return tipAmount and OnGoingTipAmount on order with tip", func(tt *testing.T) {
		ord.TipAmount = types.NewMoney(300.0)
		ord.Tips = []model.TipRecord{
			{
				ID:          "TIP_1_ID",
				Amount:      200,
				OrderStatus: string(model.StatusDriverToDestination),
				CreatedAt:   timeutil.BangkokNow().Add(time.Minute * -20),
			},
			{
				ID:          "TIP_2_ID",
				Amount:      100,
				OrderStatus: string(model.StatusCompleted),
				CreatedAt:   timeutil.BangkokNow(),
			},
		}
		api, ctxRec := newGetOrderDetailTest(tt, ord, drv, dt, false)
		api.GetOrderDetail(ctxRec.GinCtx())
		res := getOrderDetailResponse(ctxRec, tt)
		require.Equal(tt, res.TipAmount, types.NewMoney(100.0))
		require.Equal(tt, res.OnGoingTipAmount, types.NewMoney(200.0))
	})
}

func AssertSuccess(ctxRec *testutil.GinContextWithRecorder, tt *testing.T) {
	var res OrderDetailRes
	ctxRec.DecodeJSONResponse(&res)
	ctxRec.AssertResponseCode(tt, 200)
}

func AssertFail(ctxRec *testutil.GinContextWithRecorder, tt *testing.T, message string) {
	lastErr := ctxRec.GinCtx().Errors.Last().Err
	require.Error(tt, lastErr)
	apiErr := lastErr.(*api.Error)
	require.Equal(tt, message, apiErr.Code)
}

func newErrorGetOrderDetailTest(t *testing.T, ord model.Order, drv model.Driver) (*OrderAPI, *testutil.GinContextWithRecorder) {
	api, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
	api.cfg.SubdistrictEnabled = false

	deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)
	deps.DriverRepository.EXPECT().FindDriverID(gomock.Any(), drv.DriverID, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&drv, nil)

	ctxRec := testutil.NewContextWithRecorder()
	ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
	driver.SetDriverIDToContext(ctxRec.GinCtx(), drv.DriverID)

	return api, ctxRec
}

func newGetOrderDetailTest(t *testing.T, ord model.Order, drv model.Driver, dt model.DriverTransaction, isAutoAssign bool) (*OrderAPI, *testutil.GinContextWithRecorder) {
	api, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
	api.cfg.SubdistrictEnabled = false

	deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)
	deps.DriverRepository.EXPECT().FindDriverID(gomock.Any(), drv.DriverID, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&drv, nil)
	deps.DriverTransactionRepo.EXPECT().FindByID(gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).Return(&dt, nil)
	deps.ServiceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
	deps.CoinCashConversionRepo.EXPECT().GetConversionRateMinimalFromRegionViaRedisCache(gomock.Any(), gomock.Any()).Return(&model.CoinCashConversionRateMinimal{}, nil)

	ctxRec := testutil.NewContextWithRecorder()
	ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
	driver.SetDriverIDToContext(ctxRec.GinCtx(), drv.DriverID)

	return api, ctxRec
}

type OrderResForTest struct {
	Routes           []map[string]interface{} `json:"routes"`
	Captcha          map[string]interface{}   `json:"captcha"`
	TipAmount        types.Money              `json:"tipAmount"`
	OnGoingTipAmount types.Money              `json:"onGoingTipAmount"`
}

func createGetOrderDetailParams(orderID string) gin.Params {
	return gin.Params{
		{
			Key:   "orderID",
			Value: orderID,
		},
	}
}

func getOrderDetailResponse(ctxRec *testutil.GinContextWithRecorder, t *testing.T) OrderResForTest {
	var res OrderResForTest
	ctxRec.DecodeJSONResponse(&res)
	ctxRec.AssertResponseCode(t, 200)

	return res
}

func TestOrderAPI_findOrder(t *testing.T) {
	t.Run("found order, returns order", func(tt *testing.T) {
		api, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		deps.OrderRepository.EXPECT().
			Get(gomock.Any(), "LMF-12354").
			Return(&model.Order{
				OrderID: "LMF-12354",
				Quote: model.Quote{
					UserID: "<user_id>",
					Routes: []model.Stop{
						{},
					},
				},
			}, nil)

		order, err := FindOrder(context.Background(), api.OrderRepository, "LMF-12354")
		require.NoError(tt, err)
		require.NotNil(tt, order)
		require.Equal(tt, "<user_id>", order.UserID, "ensure that order get it from repository")
		require.NotEqual(tt, 0, order.Routes[0].EstimatedDeliveryTime)
	})

	t.Run("order not found", func(tt *testing.T) {
		api, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		deps.OrderRepository.EXPECT().
			Get(gomock.Any(), "LMF-12354").
			Return(nil, repository.ErrNotFound)

		_, err := FindOrder(context.Background(), api.OrderRepository, "LMF-12354")
		require.Error(tt, err)
		require.Equal(tt, repository.ErrNotFound, err)
	})

	t.Run("order id empty, returns error", func(tt *testing.T) {
		_, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		_, err := FindOrder(context.Background(), deps.OrderRepository, "")
		require.Error(tt, err)
		require.Equal(tt, repository.ErrInvalidOrderID, err)
	})
}

func TestValidateDriverCreditBalance(t *testing.T) {
	tcs := []struct {
		isCashCollectionEnabled bool
		isFlowCashCollection    bool
		wallet                  float64
		credit                  float64
		sub                     float64
		coupon                  float64
		foodFee                 float64
		isError                 bool
		transfer                float64
		totalAdditionalPrice    float64
	}{
		{isCashCollectionEnabled: true, isFlowCashCollection: true, wallet: 100, credit: 500, foodFee: 300},
		{isCashCollectionEnabled: true, isFlowCashCollection: true, credit: 210, foodFee: 500, isError: true},
		{isCashCollectionEnabled: true, credit: 210, foodFee: 500},
		{isCashCollectionEnabled: true, credit: 5, foodFee: 500, isError: true},
		{isFlowCashCollection: true, credit: 210, foodFee: 500},
		{isFlowCashCollection: true, credit: 5, foodFee: 500, isError: true},
		{credit: 5, foodFee: 500, isError: true},
		{credit: 10, foodFee: 500},
		{isCashCollectionEnabled: true, credit: 5, isError: true, totalAdditionalPrice: 40},
		{credit: 10, isError: true, totalAdditionalPrice: 40},
		{credit: 14, totalAdditionalPrice: 40},
	}
	for _, tc := range tcs {
		driverTrans := *model.NewDriverTransaction("driverID")
		addCredit := model.NewPurchaseCreditTransactionInfo("driver-1", types.NewMoney(tc.credit))
		AddWallet := model.NewIncentiveTransactionInfo("driver-1", types.NewMoney(tc.wallet), []string{})

		discounts := model.DiscountList{}
		if tc.coupon > 0 {
			discounts = append(discounts, model.Discount{Type: model.DiscountTypeCoupon, Category: "", Code: "", Discount: tc.coupon})
		}

		if tc.sub > 0 {
			discounts = append(discounts, model.Discount{Type: model.DiscountTypeSubsidize, Category: "", Code: "", Discount: tc.sub})
		}

		order := model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								SubTotal:  40,
								Discounts: discounts,
								AdditionalServiceFee: model.AdditionalServiceSummary{
									Total: tc.totalAdditionalPrice,
								},
							},
							ItemFee: model.ItemFeeSummary{Total: tc.foodFee},
						},
					},
				},
			},
		}

		if tc.isFlowCashCollection {
			order.Options.DriverMoneyFlow = model.FlowCashCollection
		}

		order.SetCommission(0.15, 0.0)
		order.SetWithholdingTax(0.03)
		_, _ = driverTrans.AddTransaction([]model.TransactionInfo{*addCredit, *AddWallet})
		transfer, err := ValidateDriverCredit(tc.isCashCollectionEnabled, ContingencyConfig{}, driverTrans, &order,
			model.NegativeBalanceGroupConfig{}, model.NewTierNegativeBalanceConfig(), "")

		if tc.isCashCollectionEnabled {
			if tc.isError {
				require.Error(t, err)
				require.True(t, transfer == types.Money(0.0))
			} else if tc.isFlowCashCollection {
				require.Equal(t, types.Money(tc.transfer), transfer)
				require.NoError(t, err)
			} else {
				require.True(t, transfer == types.Money(0.0))
				require.NoError(t, err)
			}
		} else {
			require.True(t, transfer == types.Money(0.0))
			if tc.isError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		}
	}
}

func TestValidateDriverCreditBalanceContingency(t *testing.T) {
	tcs := []struct {
		isFlowCashCollection bool
		limitMinusCredit     float64
		deliveryFee          float64
		userDeliveryFee      float64
		wallet               float64
		credit               float64
		sub                  float64
		coupon               float64
		foodFee              float64
		isError              bool
		transfer             float64
	}{
		// not enough credit but can transfer from wallet
		// cash flat rate
		{limitMinusCredit: -200, deliveryFee: 42.5, userDeliveryFee: 50, wallet: 100, credit: 5, foodFee: 120, transfer: 46.27},
		// cash gp
		{limitMinusCredit: -200, deliveryFee: 42.5, wallet: 100, sub: 50, foodFee: 120, transfer: 1.27},
		// cash collection
		{isFlowCashCollection: true, limitMinusCredit: -200, deliveryFee: 42.5, wallet: 100, sub: 50, foodFee: 120, transfer: 121.27},
		// cash advancement
		{limitMinusCredit: -200, deliveryFee: 42.5, wallet: 100, sub: 50, coupon: 10, foodFee: 110, transfer: 1.27},
		// e-payment gp
		{limitMinusCredit: -200, deliveryFee: 42.5, wallet: 100, sub: 50, foodFee: 120, transfer: 1.27},

		// not enough credit and wallet but not break limit
		// cash flat rate
		{limitMinusCredit: -200, deliveryFee: 42.5, userDeliveryFee: 50, credit: -100, foodFee: 120, transfer: 42.50},
		// cash gp
		{limitMinusCredit: -200, deliveryFee: 42.5, credit: -100, sub: 50, foodFee: 120, transfer: 42.50},
		// cash gp10
		{limitMinusCredit: -200, deliveryFee: 42.5, userDeliveryFee: 10, credit: -100, sub: 50, foodFee: 120, transfer: 42.50},
		// cash collection gp10
		{isFlowCashCollection: true, limitMinusCredit: -200, deliveryFee: 42.5, userDeliveryFee: 10, credit: -100, sub: 50, foodFee: 120, transfer: 42.50},
		// e-payment gp
		{limitMinusCredit: -200, deliveryFee: 42.5, credit: -100, sub: 50, foodFee: 120, transfer: 42.50},

		// not enough credit and wallet and equal limit
		// cash flat rate
		{limitMinusCredit: -108.77, deliveryFee: 42.5, userDeliveryFee: 50, credit: -100, foodFee: 120, transfer: 42.50},
		// cash gp
		{limitMinusCredit: -58.77, deliveryFee: 42.5, credit: -100, sub: 50, foodFee: 120, transfer: 42.50},
		// cash gp10
		{limitMinusCredit: -68.77, deliveryFee: 42.5, userDeliveryFee: 10, credit: -100, sub: 50, foodFee: 120, transfer: 42.50},
		// cash collection gp10
		{isFlowCashCollection: true, limitMinusCredit: -188.77, deliveryFee: 42.5, userDeliveryFee: 10, credit: -100, sub: 50, foodFee: 120, transfer: 42.50},
		// e-payment gp
		{limitMinusCredit: -58.77, deliveryFee: 42.5, credit: -100, sub: 50, foodFee: 120, transfer: 42.50},

		// break contingency limit
		// cash flat rate
		{limitMinusCredit: -50.0, deliveryFee: 42.5, credit: -100, sub: 50, foodFee: 120, isError: true},
		// e-payment gp
		{limitMinusCredit: -50.0, deliveryFee: 42.5, credit: -100, sub: 50, foodFee: 120, isError: true},
		// cash advancement
		{limitMinusCredit: -50.0, deliveryFee: 42.5, credit: -100, sub: 50, coupon: 10, foodFee: 110, isError: true},
		// cash gp
		{limitMinusCredit: -50.0, deliveryFee: 42.5, userDeliveryFee: 10, credit: -100, sub: 50, foodFee: 120, isError: true},
		// cash gp10
		{isFlowCashCollection: true, limitMinusCredit: -50.0, deliveryFee: 42.5, credit: -100, sub: 50, foodFee: 120, isError: true},
		// cash collection gp10
		{isFlowCashCollection: true, limitMinusCredit: -50.0, deliveryFee: 42.5, userDeliveryFee: 10, credit: -100, sub: 50, foodFee: 120, isError: true},
	}
	for _, tc := range tcs {
		driverTrans := *model.NewDriverTransaction("driverID")
		var addCredit *model.TransactionInfo
		if tc.credit >= 0 {
			addCredit = model.NewPurchaseCreditTransactionInfo("driver-1", types.NewMoney(tc.credit))
		} else {
			addCredit = model.NewCommissionCreditTransactionInfo("driver-1", "", "", types.NewMoney(tc.credit).Abs(), "")
		}
		AddWallet := model.NewIncentiveTransactionInfo("driver-1", types.NewMoney(tc.wallet), []string{})

		discounts := model.DiscountList{}

		if tc.sub > 0 {
			discounts = append(discounts, model.Discount{Type: model.DiscountTypeSubsidize, Category: "", Code: "", Discount: tc.sub})
		}

		order := model.Order{
			Quote: model.Quote{
				RevenuePrincipalModel: true,
				ServiceType:           model.ServiceFood,
				Routes: []model.Stop{
					{},
					{
						CollectPayment: tc.isFlowCashCollection,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								SubTotal:        tc.deliveryFee,
								UserDeliveryFee: tc.userDeliveryFee,
								Discounts:       discounts,
							},
							ItemFee: model.ItemFeeSummary{Total: tc.foodFee},
						},
					},
				},
			},
		}

		if tc.coupon > 0 {
			order.Quote.Routes[1].PriceSummary.ItemFee.Discounts = model.DiscountList{model.Discount{Type: model.DiscountTypeCoupon, Category: "", Code: "", Discount: tc.coupon}}
		}

		if tc.isFlowCashCollection {
			order.Options.DriverMoneyFlow = model.FlowCashCollection
		}

		order.SetWithholdingTax(0.03)
		_, _ = driverTrans.AddTransaction([]model.TransactionInfo{*addCredit, *AddWallet})
		transfer, err := ValidateDriverCredit(true, ContingencyConfig{
			ContingencyModeEnabled: true,
			LimitMinusCredit:       tc.limitMinusCredit,
		}, driverTrans, &order, model.NegativeBalanceGroupConfig{}, model.NewTierNegativeBalanceConfig(), "")

		if tc.isError {
			require.Error(t, err)
			require.True(t, transfer == types.Money(0.0))
		} else {
			require.Equal(t, types.Money(tc.transfer), transfer)
			require.NoError(t, err)
		}
	}
}

func TestValidateDriverNegativeCreditBalance(t *testing.T) {
	tcs := []struct {
		name                 string
		isFlowCashCollection bool
		limitMinusCredit     float64
		deliveryFee          float64
		userDeliveryFee      float64
		wallet               float64
		credit               float64
		sub                  float64
		coupon               float64
		foodFee              float64
		isError              bool
		transfer             float64
		byTierEnabled        bool
		tierLimitMinusCredit float64
	}{
		{"cash flat rate - not enough credit but can transfer from wallet", false, -200, 42.5, 50, 100, 5, 0, 0, 120, false, 46.27, false, 0},
		{"cash gp - not enough credit but can transfer from wallet", false, -200, 42.5, 0, 100, 0, 50, 0, 120, false, 1.27, false, 0},
		{"cash collection - not enough credit but can transfer from wallet", true, -200, 42.5, 0, 100, 0, 50, 0, 120, false, 121.27, false, 0},
		{"cash advancement - not enough credit but can transfer from wallet", false, -200, 42.5, 0, 100, 0, 50, 10, 110, false, 1.27, false, 0},
		{"e-payment gp - not enough credit but can transfer from wallet", false, -200, 42.5, 0, 100, 0, 50, 0, 120, false, 1.27, false, 0},

		{"cash flat rate - not enough credit but not break limit", false, -200, 42.5, 50, 0, -100, 0, 0, 120, false, 42.50, false, 0},
		{"cash gp - not enough credit but not break limit", false, -200, 42.5, 0, 0, -100, 50, 0, 120, false, 42.50, false, 0},
		{"cash gp10 - not enough credit but not break limit", false, -200, 42.5, 10, 0, -100, 50, 0, 120, false, 42.50, false, 0},
		{"cash collection gp10 - not enough credit but not break limit", true, -200, 42.5, 10, 0, -100, 50, 0, 120, false, 42.50, false, 0},
		{"cash gp - not enough credit but not break limit", false, -200, 42.5, 0, 0, -100, 50, 0, 120, false, 42.50, false, 0},

		{"cash flat rate - not enough credit and wallet and equal limit", false, -108.77, 42.5, 50, 0, -100, 0, 0, 120, false, 42.50, false, 0},
		{"cash gp - not enough credit and wallet and equal limit", false, -58.77, 42.5, 0, 0, -100, 50, 0, 120, false, 42.50, false, 0},
		{"cash gp10 - not enough credit and wallet and equal limit", false, -68.77, 42.5, 10, 0, -100, 50, 0, 120, false, 42.50, false, 0},
		{"cash collection gp10 - not enough credit and wallet and equal limit", true, -188.77, 42.5, 10, 0, -100, 50, 0, 120, false, 42.50, false, 0},
		{"cash gp - not enough credit and wallet and equal limit", false, -58.77, 42.5, 0, 0, -100, 50, 0, 120, false, 42.50, false, 0},

		{"cash flat rate - break limit", false, -50.0, 42.5, 0, 0, -100, 50, 0, 120, true, 0, false, 0},
		{"e-payment gp - break limit", false, -50.0, 42.5, 0, 0, -100, 50, 0, 120, true, 0, false, 0},
		{"cash advancement - break limit", false, -50.0, 42.5, 0, 0, -100, 50, 10, 110, true, 0, false, 0},
		{"cash gp - break limit", false, -50.0, 42.5, 10, 0, -100, 50, 0, 120, true, 0, false, 0},
		{"cash gp10 - break limit", true, -50.0, 42.5, 0, 0, -100, 50, 0, 120, true, 0, false, 0},
		{"cash collection gp10 - break limit", true, -50.0, 42.5, 10, 0, -100, 50, 0, 120, true, 0, false, 0},

		{"tier negative credit - cash flat rate - break limit", false, -50.0, 42.5, 0, 0, -200, 50, 0, 120, true, 0, true, -100.0},
	}
	for _, tc := range tcs {
		t.Run(tc.name, func(tt *testing.T) {
			driverTrans := *model.NewDriverTransaction("driverID")
			var addCredit *model.TransactionInfo
			if tc.credit >= 0 {
				addCredit = model.NewPurchaseCreditTransactionInfo("driver-1", types.NewMoney(tc.credit))
			} else {
				addCredit = model.NewCommissionCreditTransactionInfo("driver-1", "", "", types.NewMoney(tc.credit).Abs(), "")
			}
			AddWallet := model.NewIncentiveTransactionInfo("driver-1", types.NewMoney(tc.wallet), []string{})

			discounts := model.DiscountList{}

			if tc.sub > 0 {
				discounts = append(discounts, model.Discount{Type: model.DiscountTypeSubsidize, Category: "", Code: "", Discount: tc.sub})
			}

			order := model.Order{
				Quote: model.Quote{
					RevenuePrincipalModel: true,
					ServiceType:           model.ServiceFood,
					Routes: []model.Stop{
						{},
						{
							CollectPayment: tc.isFlowCashCollection,
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{
									SubTotal:        tc.deliveryFee,
									UserDeliveryFee: tc.userDeliveryFee,
									Discounts:       discounts,
								},
								ItemFee: model.ItemFeeSummary{Total: tc.foodFee},
							},
						},
					},
				},
			}

			if tc.coupon > 0 {
				order.Quote.Routes[1].PriceSummary.ItemFee.Discounts = model.DiscountList{model.Discount{Type: model.DiscountTypeCoupon, Category: "", Code: "", Discount: tc.coupon}}
			}

			if tc.isFlowCashCollection {
				order.Options.DriverMoneyFlow = model.FlowCashCollection
			}

			order.SetWithholdingTax(0.03)
			_, _ = driverTrans.AddTransaction([]model.TransactionInfo{*addCredit, *AddWallet})
			transfer, err := ValidateDriverCredit(true, ContingencyConfig{
				ContingencyModeEnabled: false,
			}, driverTrans, &order, model.NegativeBalanceGroupConfig{
				Enabled:              true,
				MinimumCreditBalance: tc.limitMinusCredit,
				Name:                 "fakeGroup",
			}, model.TierNegativeBalanceConfig{
				Enabled: tc.byTierEnabled,
				NegativeBalanceByTier: model.DriverTierMap{
					"STAR": tc.tierLimitMinusCredit,
				},
			}, "")

			if tc.isError {
				require.Error(tt, err)
				require.True(tt, transfer == types.Money(0.0))
			} else {
				require.Equal(tt, types.Money(tc.transfer), transfer)
				require.NoError(tt, err)
			}
		})
	}
}

func TestOrderAPI_RejectAssignmentHandler(t *testing.T) {
	createGinContext := func(assignmentID string, driverID string, body RejectOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/assignment/"+assignmentID+"/reject", testutil.JSON(body))
		ctx.Params = gin.Params{
			{
				Key:   "assignmentID",
				Value: assignmentID,
			},
		}
		driver.SetDriverIDToContext(ctx, driverID)
		return ctx, recorder
	}

	body := RejectOrderRequest{Reason: "HEAVY_RAIN"}
	driverID := "<driver_id_1>"
	assignmentID := "ass"

	t.Run("simple reject", func(tt *testing.T) {
		tt.Parallel()

		ctx, _ := createGinContext(assignmentID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		var driverProfile model.Driver
		deps.DriverRepository.EXPECT().
			GetProfile(ctx, driverID).
			Return(&driverProfile, nil)

		assignment := model.Assignment{
			AssignmentID: model.AssignmentID(assignmentID),
			DriverID:     driverID,
		}
		deps.AssignmentRepo.EXPECT().
			FindByID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&assignment, nil)

		holder := testutil.ParameterHolder{}
		deps.AssignmentRejectionRepo.EXPECT().Create(ctx, &holder).Return(nil)

		ordapi.RejectAssignmentHandler(ctx)

		ar := holder.Values[0].(*model.AssignmentRejection)
		require.Equal(tt, driverID, ar.DriverID)
		require.Equal(tt, assignmentID, ar.AssignmentID)
		require.Equal(tt, body.Reason, ar.Reason)
	})
}

func TestOrderAPI_RejectOrderHandler(t *testing.T) {
	createGinContext := func(orderID string, driverID string, body RejectOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/order/"+orderID+"/reject", testutil.JSON(body))
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		driver.SetDriverIDToContext(ctx, driverID)
		return ctx, recorder
	}

	body := RejectOrderRequest{Reason: "HEAVY_RAIN"}
	driverID := "<driver_id_1>"
	orderID := "LMF-123151"

	t.Run("simple reject", func(tt *testing.T) {
		tt.Parallel()

		ctx, _ := createGinContext(orderID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		var driverProfile model.Driver
		deps.DriverRepository.EXPECT().
			GetProfile(ctx, driverID).
			Return(&driverProfile, nil)

		order := model.NewOrder(model.Quote{}, orderID)
		deps.OrderRepository.EXPECT().
			Get(gomock.Any(), orderID, gomock.Any()).
			Return(order, nil)

		holder := testutil.ParameterHolder{}
		deps.AssignmentRejectionRepo.EXPECT().Create(ctx, &holder).Return(nil)

		ordapi.RejectOrderHandler(ctx)

		ar := holder.Values[0].(*model.AssignmentRejection)
		require.Equal(tt, driverID, ar.DriverID)
		require.Equal(tt, orderID, ar.OrderID)
		require.Equal(tt, body.Reason, ar.Reason)
	})

	t.Run("no reason", func(tt *testing.T) {
		tt.Parallel()

		ctx, _ := createGinContext(orderID, driverID, RejectOrderRequest{})

		ordapi, _ := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		ordapi.RejectOrderHandler(ctx)

		lastErr := ctx.Errors.Last().Err
		require.Error(tt, lastErr)
	})

	t.Run("error getting driver", func(tt *testing.T) {
		tt.Parallel()

		ctx, _ := createGinContext(orderID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		deps.DriverRepository.EXPECT().
			GetProfile(ctx, driverID).
			Return(nil, repository.ErrNotFound)

		ordapi.RejectOrderHandler(ctx)

		lastErr := ctx.Errors.Last().Err
		require.Error(tt, lastErr)
	})

	t.Run("error getting order", func(tt *testing.T) {
		tt.Parallel()

		ctx, _ := createGinContext(orderID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		var driverProfile model.Driver
		deps.DriverRepository.EXPECT().
			GetProfile(ctx, driverID).
			Return(&driverProfile, nil)

		deps.OrderRepository.EXPECT().
			Get(gomock.Any(), orderID, gomock.Any()).
			Return(nil, repository.ErrNotFound)

		ordapi.RejectOrderHandler(ctx)

		lastErr := ctx.Errors.Last().Err
		require.Error(tt, lastErr)
	})

	t.Run("error creating rejection", func(tt *testing.T) {
		tt.Parallel()

		ctx, _ := createGinContext(orderID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		var driverProfile model.Driver
		deps.DriverRepository.EXPECT().
			GetProfile(ctx, driverID).
			Return(&driverProfile, nil)

		order := model.NewOrder(model.Quote{}, orderID)
		deps.OrderRepository.EXPECT().
			Get(gomock.Any(), orderID, gomock.Any()).
			Return(order, nil)

		deps.AssignmentRejectionRepo.EXPECT().Create(ctx, gomock.Any()).Return(errors.New("error saving"))

		ordapi.RejectOrderHandler(ctx)

		lastErr := ctx.Errors.Last().Err
		require.Error(tt, lastErr)
	})
}

func TestRatingAPI_CreateRestaurantRating(t *testing.T) {
	t.Run("Should create restaurant rating success", func(tt *testing.T) {
		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		rr := model.RatingRestaurant{}

		ratingInfo := []Radio{
			{"option-id", 3},
			{"option-id", 2},
		}
		req := CreateRestaurantRatingRequest{
			RadioInfo: ratingInfo,
		}
		quote := model.Quote{Routes: []model.Stop{{ID: "restaurant-id"}}}
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(&model.Order{Quote: quote}, nil)
		deps.RatingRestaurantRepo.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(&rr, nil)
		deps.RatingRestaurantRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
			Return(nil)
		deps.DriverRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).
			Return(true)

		ctx, recorder := testutil.TestRequestContext("POST", "v1/order/LMF-31566140/rating", testutil.JSON(&req))

		ordapi.CreateRestaurantRating(ctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("Create rating restaurant error", func(tt *testing.T) {
		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		rr := model.RatingRestaurant{}

		ratingInfo := []Radio{
			{"option-id", 3},
			{"option-id", 2},
		}
		req := CreateRestaurantRatingRequest{
			RadioInfo: ratingInfo,
		}
		quote := model.Quote{Routes: []model.Stop{{ID: "restaurant-id"}}}
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(&model.Order{Quote: quote}, nil)
		deps.RatingRestaurantRepo.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(&rr, nil)
		deps.RatingRestaurantRepo.EXPECT().Create(gomock.Any(), gomock.Any()).
			Return(errors.New("no reachable servers"))
		deps.DriverRepository.EXPECT().IsExists(gomock.Any(), gomock.Any()).
			Return(true)

		ctx, recorder := testutil.TestRequestContext("POST", "v1/order/LMF-31566140/rating", testutil.JSON(&req))

		ordapi.CreateRestaurantRating(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestOrderAPI_CancelOrder(t *testing.T) {
	createGinContext := func(orderID string, driverID string, body CancelOrderReq) (*gin.Context, *httptest.ResponseRecorder) {
		var b bytes.Buffer
		w := multipart.NewWriter(&b)
		defer w.Close()

		var fw io.Writer
		fw, _ = w.CreateFormField("reason")
		fw.Write([]byte(body.Reason))
		fw, _ = w.CreateFormFile("photos", "photo1")
		fw.Write([]byte("photo1"))
		fw, _ = w.CreateFormFile("photos", "photo2")
		fw.Write([]byte("photo2"))

		ctx, recorder := testutil.TestRequestContext("PUT", "", &b)
		ctx.Request.Header.Set("Content-Type", w.FormDataContentType())
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		driver.SetDriverIDToContext(ctx, driverID)
		return ctx, recorder
	}

	body := CancelOrderReq{Reason: "cancel reason"}
	driverID := "<driver_id_1>"
	orderID := "LMF-484848"

	t.Run("should cancel order success", func(tt *testing.T) {
		tt.Parallel()

		ctx, record := createGinContext(orderID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: true,
		})

		cancelReasonID := primitive.NewObjectID()
		cancelReason := model.CancelReason{ID: cancelReasonID, Name: "cancel", CancelByRider: model.CancelByRider{IsAllowed: true, IsPhotoRequired: false, Count: 10}, ShouldAutoClaim: true}

		order := model.Order{OrderID: orderID, Status: model.StatusDriverMatched, Quote: model.Quote{ServiceType: "food"}, History: map[string]time.Time{}, Driver: driverID}
		order.Quote.Routes = []model.Stop{{Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{PriceScheme: model.PriceSchemeRMS}}}, {}}
		deps.CancelReasonRepo.EXPECT().GetByReasonName(gomock.Any(), gomock.Any(), gomock.Any()).Return(cancelReason, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil).AnyTimes()
		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(1, nil)
		deps.TxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some error so it won't reassign"))
		deps.Delivery.EXPECT().CSCancelOrder(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, req v1.CSCancelOrderRequest) error {
			require.Equal(tt, orderID, req.OrderID)
			require.Equal(tt, cancelReasonID.Hex(), req.CancelReasonID)
			require.Equal(tt, cancelReason.Name, req.Reason)
			require.Equal(tt, "LINEMAN_RIDER", req.Source)
			require.Equal(tt, 0.0, req.PenaltyForUser)
			require.Equal(tt, "RIDER", req.Requestor)
			require.Equal(tt, cancelReason.ShouldAutoClaim, req.ShouldAutoClaim)
			return nil
		})
		deps.OrderRepository.EXPECT().UpdateAndUpsertCanceledRevision(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.ServiceOptInReminderService.EXPECT().TryToMarkForReminder(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, d *model.Driver) error {
			require.Equal(tt, driverID, d.DriverID)
			return nil
		})
		deps.RepService.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil).Times(3)
		deps.Bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.NotifierService.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: driverID, CurrentOrder: orderID, Status: model.StatusAssigned}, nil)
		deps.VosSvc.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&file.File{}, nil).Times(2)
		deps.DriverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&model.LocationWithUpdatedAt{}, nil)
		stubLockDriver(tt, deps.DriverService)

		wg := safe.CreateWaitGroupOnGctx(ctx)
		ordapi.CancelOrder(ctx)
		wg.Wait()

		assert.Equal(tt, http.StatusOK, record.Code)
	})

	t.Run("should cancel order and ban offline later", func(tt *testing.T) {
		tt.Parallel()

		ctx, record := createGinContext(orderID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: true,
		})

		cancelReason := model.CancelReason{Name: "cancel", CancelByRider: model.CancelByRider{IsAllowed: true, IsPhotoRequired: false, Count: 10}, ShouldAutoClaim: true}

		order := model.Order{OrderID: orderID, Status: model.StatusDriverMatched, Quote: model.Quote{ServiceType: "food"}, History: map[string]time.Time{}, Driver: driverID}
		order.Quote.Routes = []model.Stop{{Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{PriceScheme: model.PriceSchemeRMS}}}, {}}
		deps.CancelReasonRepo.EXPECT().GetByReasonName(gomock.Any(), gomock.Any(), gomock.Any()).Return(cancelReason, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil).AnyTimes()
		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(1, nil)
		deps.TxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some error so it won't reassign"))
		deps.Delivery.EXPECT().CSCancelOrder(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, req v1.CSCancelOrderRequest) error {
			require.Equal(tt, orderID, req.OrderID)
			require.Equal(tt, cancelReason.Name, req.Reason)
			require.Equal(tt, "LINEMAN_RIDER", req.Source)
			require.Equal(tt, 0.0, req.PenaltyForUser)
			require.Equal(tt, "RIDER", req.Requestor)
			require.Equal(tt, cancelReason.ShouldAutoClaim, req.ShouldAutoClaim)
			return nil
		})
		deps.OrderRepository.EXPECT().UpdateAndUpsertCanceledRevision(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.ServiceOptInReminderService.EXPECT().TryToMarkForReminder(gomock.Any(), gomock.Any()).Return(nil)
		deps.RepService.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil).Times(3)
		deps.Bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.NotifierService.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{
			CurrentOrder: orderID,
			Status:       model.StatusAssigned,
			OfflineLater: true,
		}, nil)

		serviceArea := &model.ServiceArea{}
		serviceArea.SetOfflineLaterBreakDuration(10 * time.Minute)
		deps.ServiceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(serviceArea, nil)
		deps.BanService.EXPECT().Ban(gomock.Any(), gomock.Any(), gomock.Any())
		deps.VosSvc.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&file.File{}, nil).Times(2)
		deps.DriverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&model.LocationWithUpdatedAt{}, nil)
		stubLockDriver(tt, deps.DriverService)

		wg := safe.CreateWaitGroupOnGctx(ctx)
		ordapi.CancelOrder(ctx)
		wg.Wait()

		assert.Equal(tt, http.StatusOK, record.Code)
	})

	t.Run("should cancel order success and reassign order", func(tt *testing.T) {
		tt.Parallel()

		gctx, record := createGinContext(orderID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: true,
		})

		cancelReason := model.CancelReason{Name: "cancel", CancelByRider: model.CancelByRider{IsAllowed: true, IsPhotoRequired: false, Count: 10}, ShouldAutoClaim: true, IsReassign: true}

		orderWithStatus := func(status model.Status) *model.Order {
			return &model.Order{OrderID: orderID, Status: status, Quote: model.Quote{ServiceType: "food"}, History: map[string]time.Time{}, Driver: driverID}
		}

		order := orderWithStatus(model.StatusRestaurantAccepted)
		order.Quote.Routes = []model.Stop{{Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{PriceScheme: model.PriceSchemeRMS}}}, {}}
		deps.CancelReasonRepo.EXPECT().GetByReasonName(gomock.Any(), gomock.Any(), gomock.Any()).Return(cancelReason, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(order, nil).Times(3)
		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(1, nil)
		deps.TxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.AssignmentLogRepo.EXPECT().SoftDelete(gomock.Any(), orderID, 0).Return(nil)
		deps.OrderRepository.EXPECT().UpsertCancelRevision(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&model.LocationWithUpdatedAt{}, nil)
		deps.OrderRepository.EXPECT().UpdateOrder(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, order *model.Order, opts ...repository.Option) error {
			require.Empty(tt, order.Driver)
			require.Equal(tt, model.StatusAssigningDriver, order.Status)
			require.Equal(tt, 1, order.DeliveringRound)
			return nil
		})
		deps.Delivery.EXPECT().RejectOrder(gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.FeatureFlagService.EXPECT().IsEnabledWithDefaultFalse(gomock.Any(), featureflag.IsEnabledReassignViaDispatcherAPI.Name, gomock.Any()).Return(false)
		deps.Distributor.EXPECT().Distribute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(func() {}, nil)
		deps.ServiceOptInReminderService.EXPECT().TryToMarkForReminder(gomock.Any(), gomock.Any()).Return(nil)
		deps.RepService.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil)
		deps.Bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.NotifierService.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{CurrentOrder: orderID, Status: model.StatusAssigned}, nil)
		deps.VosSvc.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&file.File{}, nil).Times(2)
		stubLockDriver(tt, deps.DriverService)

		wg := safe.CreateWaitGroupOnGctx(gctx)
		ordapi.CancelOrder(gctx)
		wg.Wait()

		assert.Equal(tt, http.StatusOK, record.Code)
	})

	t.Run("disallow self-cancel for reason of 'HALF_HALF_WRONG_PAYMENT_METHOD' if order is half-half", func(tt *testing.T) {
		tt.Parallel()

		reason := "HALF_HALF_WRONG_PAYMENT_METHOD"
		ctx, _ := createGinContext(orderID, driverID, CancelOrderReq{Reason: reason})

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: true,
		})

		order := model.Order{Status: model.StatusDriverMatched, Quote: model.Quote{ServiceType: "food", SpecialEvent: []string{"HalfHalf"}, StoreAcceptHalfHalf: true}, History: map[string]time.Time{}, Driver: driverID}
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(false, nil)
		stubLockDriver(tt, deps.DriverService)

		ordapi.CancelOrder(ctx)
		assert.NotNil(tt, ctx.Errors.Last())
	})
	t.Run("disallow self-cancel for reason of 'HALF_HALF_WRONG_PAYEMNT_METHOD' if StoreAcceptHalfHalf is false", func(tt *testing.T) {
		tt.Parallel()

		reason := "HALF_HALF_WRONG_PAYMENT_METHOD"
		ctx, _ := createGinContext(orderID, driverID, CancelOrderReq{Reason: reason})

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: true,
		})

		order := model.Order{Status: model.StatusDriverMatched, Quote: model.Quote{ServiceType: "food"}, History: map[string]time.Time{}, Driver: driverID}
		deps.CancelReasonRepo.EXPECT().GetByReasonName(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.CancelReason{Name: reason, CancelByRider: model.CancelByRider{IsAllowed: true, IsPhotoRequired: false, Count: 10}}, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
		stubLockDriver(tt, deps.DriverService)

		ordapi.CancelOrder(ctx)
		assert.NotNil(tt, ctx.Errors.Last())
	})
	t.Run("allow self-cancel for reason of 'HALF_HALF_WRONG_PAYEMNT_METHOD' if the conditions are met", func(tt *testing.T) {
		tt.Parallel()

		reason := "HALF_HALF_WRONG_PAYMENT_METHOD"
		ctx, record := createGinContext(orderID, driverID, CancelOrderReq{Reason: reason})

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: true,
		})

		order := model.Order{Status: model.StatusDriverMatched, Quote: model.Quote{ServiceType: "food", StoreAcceptHalfHalf: true}, History: map[string]time.Time{}, Driver: driverID}
		order.Quote.Routes = []model.Stop{{Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{PriceScheme: model.PriceSchemeRMS}}}, {}}
		deps.CancelReasonRepo.EXPECT().GetByReasonName(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.CancelReason{Name: reason, CancelByRider: model.CancelByRider{IsAllowed: true, IsPhotoRequired: false, Count: 10}}, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&order, nil).AnyTimes()
		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(1, nil)
		deps.Delivery.EXPECT().CSCancelOrder(gomock.Any(), gomock.Any()).Return(nil)
		deps.TxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some error so it won't reassign"))
		deps.OrderRepository.EXPECT().UpdateAndUpsertCanceledRevision(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.ServiceOptInReminderService.EXPECT().TryToMarkForReminder(gomock.Any(), gomock.Any()).Return(nil)
		deps.RepService.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil).Times(3)
		deps.Bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.NotifierService.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{CurrentOrder: orderID, Status: model.StatusAssigned}, nil)
		deps.VosSvc.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&file.File{}, nil).Times(2)
		deps.DriverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&model.LocationWithUpdatedAt{}, nil)
		stubLockDriver(tt, deps.DriverService)

		ordapi.CancelOrder(ctx)
		assert.Nil(tt, ctx.Errors.Last())
		assert.Equal(tt, http.StatusOK, record.Code)
	})
	t.Run("disallow self-cancel if driver hold wechat order", func(tt *testing.T) {
		tt.Parallel()

		reason := "HALF_HALF_WRONG_PAYMENT_METHOD"
		ctx, record := createGinContext(orderID, driverID, CancelOrderReq{Reason: reason})

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: true,
		})

		order := model.Order{
			Status:  model.StatusDriverMatched,
			Quote:   model.Quote{ServiceType: "food", Options: model.OrderOptions{IsWechatUser: true}, StoreAcceptHalfHalf: true},
			History: map[string]time.Time{},
			Driver:  driverID,
		}
		cancelReason := model.CancelReason{
			Name:            "cancel",
			CancelByRider:   model.CancelByRider{IsAllowed: true, IsPhotoRequired: false, Count: 10},
			ShouldAutoClaim: true,
		}

		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
		deps.CancelReasonRepo.EXPECT().GetByReasonName(gomock.Any(), gomock.Any(), gomock.Any()).Return(cancelReason, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(true, nil)
		stubLockDriver(tt, deps.DriverService)

		ordapi.CancelOrder(ctx)
		assert.Equal(tt, http.StatusBadRequest, record.Code)
	})
	t.Run("disallow self-cancel if reason will ban and driver hold wechat order", func(tt *testing.T) {
		tt.Parallel()

		reason := "HALF_HALF_WRONG_PAYMENT_METHOD"
		ctx, record := createGinContext(orderID, driverID, CancelOrderReq{Reason: reason})

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: true,
		})

		order := model.Order{
			Status:  model.StatusDriverMatched,
			Quote:   model.Quote{ServiceType: "food", StoreAcceptHalfHalf: true},
			History: map[string]time.Time{},
			Driver:  driverID,
		}
		cancelReason := model.CancelReason{
			Name:            "cancel",
			CancelByRider:   model.CancelByRider{IsAllowed: true, IsPhotoRequired: false, Count: 10, BanDurationInMinute: 60},
			ShouldAutoClaim: true,
		}

		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
		deps.CancelReasonRepo.EXPECT().GetByReasonName(gomock.Any(), gomock.Any(), gomock.Any()).Return(cancelReason, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(true, nil)
		stubLockDriver(tt, deps.DriverService)

		ordapi.CancelOrder(ctx)
		assert.Equal(tt, http.StatusBadRequest, record.Code)
	})

	t.Run("should cancel order success without upload photo", func(tt *testing.T) {
		tt.Parallel()

		ctx, record := createGinContext(orderID, driverID, body)

		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: false,
		})

		order := model.Order{Status: model.StatusDriverMatched, Quote: model.Quote{ServiceType: "food"}, History: map[string]time.Time{}, Driver: driverID}
		order.Quote.Routes = []model.Stop{{Info: model.StopInfoCollector{StopInfo: &model.StopInfoFood{PriceScheme: model.PriceSchemeRMS}}}, {}}
		deps.CancelReasonRepo.EXPECT().GetByReasonName(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.CancelReason{Name: "cancel", CancelByRider: model.CancelByRider{IsAllowed: true, IsPhotoRequired: false, Count: 10}}, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).Return(false, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).Return(&order, nil).AnyTimes()
		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(1, nil)
		deps.Delivery.EXPECT().CSCancelOrder(gomock.Any(), gomock.Any()).Return(nil)
		deps.TxnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some error so it won't reassign"))
		deps.OrderRepository.EXPECT().UpdateAndUpsertCanceledRevision(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.ServiceOptInReminderService.EXPECT().TryToMarkForReminder(gomock.Any(), gomock.Any()).Return(nil)
		deps.RepService.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil).Times(3)
		deps.Bus.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.NotifierService.EXPECT().Notify(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.DriverRepository.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{CurrentOrder: orderID, Status: model.StatusAssigned}, nil)
		deps.VosSvc.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&file.File{}, nil).Times(0)
		deps.DriverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&model.LocationWithUpdatedAt{}, nil)
		stubLockDriver(tt, deps.DriverService)

		ordapi.CancelOrder(ctx)

		assert.Equal(tt, http.StatusOK, record.Code)
	})
}

func stubLockDriver(t *testing.T, locker *mock_service.MockDriverServiceInterface) {
	t.Helper()
	locker.EXPECT().TryLockDriver(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	locker.EXPECT().UnlockDriver(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
}

func stubLockDriverWithRetryDelayAndRetryAttempts(t *testing.T, locker *mock_service.MockDriverServiceInterface) {
	t.Helper()
	locker.EXPECT().TryLockDriver(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
	locker.EXPECT().UnlockDriver(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
}

func TestOrderAPI_ListCancelReasons(t *testing.T) {
	driverID := "<driver_id_1>"
	orderID := "LMF-123151"
	reasons := func() []model.CancelReason {
		return []model.CancelReason{
			{
				Name:                      "foo",
				ServiceType:               model.ServiceFood,
				IsPhotoRequiredForZendesk: false,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
			},
		}
	}

	createGinContext := func(orderID string, driverID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("GET", "/v1/orders/cancel-reasons/food", nil)
		ctx.Params = gin.Params{
			{
				Key:   "serviceType",
				Value: "food",
			},
		}
		driver.SetDriverIDToContext(ctx, driverID)
		return ctx, recorder
	}

	testListCancelAPI := func(AllowDriverSelfCancel bool) (*OrderAPI, *orderDeps) {
		return newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, OrderAPIConfig{
			EnableToStoreCancelOrderPhoto: false,
			AtomicOrderDBConfig: &AtomicOrderDBConfig{
				Config: OrderDBConfig{
					AllowDriverSelfCancel: AllowDriverSelfCancel,
				},
			},
		})
	}

	orderListCancel := func(specialEvent []string) *model.Order {
		return &model.Order{
			Status: model.StatusDriverMatched,
			Quote: model.Quote{
				ServiceType:  "food",
				SpecialEvent: specialEvent,
			},
			History: map[string]time.Time{},
			Driver:  driverID,
		}
	}

	t.Run("target `ZENDESK` and call food if order is half half and allow self cancel", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := orderListCancel([]string{"HalfHalf"})

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons(), nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.Delivery.EXPECT().GovCampaignStatus(gomock.Any(), gomock.Any()).
			Return(&client.GovCampaignStatusResponse{Cancelable: false}, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "foo", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		t.Logf("%+v", res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `LM_DRIVER` and not call food if order is not half half and allow self cancel", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := orderListCancel([]string{})

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons(), nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "foo", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "LM_DRIVER", res[0].Target)
	})

	t.Run("target `ZENDESK` if order is half half and not allow by config", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(false)
		order := orderListCancel([]string{"HalfHalf"})

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons(), nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "foo", *order).Return(false)

		ordapi.ListCancelReasons(ctx)
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `ZENDESK` if food return Cancelable is false", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := orderListCancel([]string{"HalfHalf"})

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons(), nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.Delivery.EXPECT().GovCampaignStatus(gomock.Any(), gomock.Any()).
			Return(&client.GovCampaignStatusResponse{Cancelable: false}, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "foo", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `ZENDESK` if food return Cancelable is false and allow by config", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := orderListCancel([]string{"HalfHalf"})

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons(), nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.Delivery.EXPECT().GovCampaignStatus(gomock.Any(), gomock.Any()).
			Return(&client.GovCampaignStatusResponse{Cancelable: false}, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "foo", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `LM_DRIVER` if food return Cancelable is true and allow by config", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := orderListCancel([]string{"HalfHalf"})

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons(), nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.Delivery.EXPECT().GovCampaignStatus(gomock.Any(), gomock.Any()).
			Return(&client.GovCampaignStatusResponse{Cancelable: true}, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "foo", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)

		t.Logf("%+v", res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "LM_DRIVER", res[0].Target)
	})

	t.Run("target `ZENDESK` if food return Cancelable is true and allow by config but quota is exceed", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := orderListCancel([]string{"HalfHalf"})

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons(), nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.Delivery.EXPECT().GovCampaignStatus(gomock.Any(), gomock.Any()).
			Return(&client.GovCampaignStatusResponse{
				Cancelable: true,
			}, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any()).
			Return(5, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "foo", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `ZENDESK` if order is reach basket size", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Status: model.StatusDriverMatched,
			Quote: model.Quote{
				ServiceType: "food",
				Routes: []model.Stop{
					{},
					{
						ItemsPrice: 300,
					},
				},
			},
			History: map[string]time.Time{},
			Driver:  driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:                      "bar",
				ServiceType:               model.ServiceFood,
				IsPhotoRequiredForZendesk: false,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				MaxBasketSize: 50,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `ZENDESK` if order is food with invalid status", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Status: "INVALID",
			Quote: model.Quote{
				ServiceType: "food",
				Routes: []model.Stop{
					{},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				AllowOrderStatus: []string{"VALID"},
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `ZENDESK` if order is mart with invalid status", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Status: "INVALID",
			Quote: model.Quote{
				ServiceType: "mart",
				Routes: []model.Stop{
					{},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceMart,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				AllowOrderStatus: []string{"VALID"},
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `ZENDESK` if order is messenger with invalid status", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Status: "INVALID",
			Quote: model.Quote{
				ServiceType: "messenger",
				Routes: []model.Stop{
					{},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceMessenger,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				AllowOrderStatus: []string{"VALID"},
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `LM_DRIVER` if normal order has distance greather than or equal max_distance_meter", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 7001},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "LM_DRIVER", res[0].Target)
	})

	t.Run("target `LM_DRIVER` if normal order has distance equal max_distance_meter", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 7000},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "LM_DRIVER", res[0].Target)
	})

	t.Run("target `ZENDESK` if normal order has distance less than max_distance_meter", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 6999},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				AllowOrderStatus: []string{"VALID"},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `LM_DRIVER` if B2B order has distance greather than max_distance_meter", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 7001},
					{},
				},
			},
			AssignedToQueue: true,
			Driver:          driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "LM_DRIVER", res[0].Target)
	})

	t.Run("target `LM_DRIVER` if B2B order has distance equal max_distance_meter", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 7000},
					{},
				},
			},
			AssignedToQueue: true,
			Driver:          driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "LM_DRIVER", res[0].Target)
	})

	t.Run("target `ZENDESK` if B2B order has distance less than max_distance_meter", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 6999},
					{},
				},
			},
			AssignedToQueue: true,
			Driver:          driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `LM_DRIVER` if normal order and use orderId from query params", func(t *testing.T) {
		// Given
		ctx, record := testutil.TestRequestContext("GET", "/v1/orders/cancel-reasons/food?orderId=LMF-query", nil)
		ctx.Params = gin.Params{
			{
				Key:   "serviceType",
				Value: "food",
			},
		}
		driver.SetDriverIDToContext(ctx, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 7000},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), "LMF-query").
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "LM_DRIVER", res[0].Target)
	})

	t.Run("target `ZENDESK` if wechat order", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 7000},
					{},
				},
				Options: model.OrderOptions{
					IsWechatUser: true,
				},
			},
			AssignedToQueue: true,
			Driver:          driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(true, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `ZENDESK` if reason will ban and driver hold wechat order even if cancel normal order", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 7000},
					{},
				},
			},
			AssignedToQueue: true,
			Driver:          driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					BanDurationInMinute: 60,
					IsAllowed:           true,
					Count:               5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(true, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `LM_DRIVER` if reason will ban and but driver doesn't hold wechat order", func(t *testing.T) {
		// Given
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{Distance: 7000},
					{},
				},
			},
			AssignedToQueue: true,
			Driver:          driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceFood,
				CancelByRider: model.CancelByRider{
					BanDurationInMinute: 60,
					IsAllowed:           true,
					Count:               5,
				},
				MaxDistanceMeter: 7000,
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		// When
		ordapi.ListCancelReasons(ctx)

		// Then
		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "LM_DRIVER", res[0].Target)
	})

	t.Run("target `ZENDESK` if order is bike with invalid status", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Status: "INVALID",
			Quote: model.Quote{
				ServiceType: "bike",
				Routes: []model.Stop{
					{},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceBike,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				AllowOrderStatus: []string{"VALID"},
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `LM_DRIVER` if order is bike with valid status", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := &model.Order{
			Status: "VALID",
			Quote: model.Quote{
				ServiceType: "bike",
				Routes: []model.Stop{
					{},
					{},
				},
			},
			Driver: driverID,
		}
		reasons := []model.CancelReason{
			{
				Name:        "bar",
				ServiceType: model.ServiceBike,
				CancelByRider: model.CancelByRider{
					IsAllowed: true,
					Count:     5,
				},
				AllowOrderStatus: []string{"VALID"},
			},
		}

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons, nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "bar", *order).Return(false)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})

	t.Run("target `ZENDESK` if fraud cash collection is detected", func(t *testing.T) {
		ctx, record := createGinContext(orderID, driverID)
		ordapi, deps := testListCancelAPI(true)
		order := orderListCancel([]string{})

		deps.CancelReasonRepo.EXPECT().GetByService(gomock.Any(), gomock.Any()).
			Return(reasons(), nil)
		deps.DriverRepository.EXPECT().CurrentStatus(gomock.Any(), gomock.Any()).
			Return(repository.DriverCurrentState{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any()).
			Return(order, nil)
		deps.TripSvc.EXPECT().HasOngoingWechatOrder(gomock.Any(), gomock.Any()).
			Return(false, nil)

		deps.OrderRepository.EXPECT().CountCancelOrderByCancelDetail(
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any()).
			Return(1, nil)

		deps.FraudService.EXPECT().IsFraudCashCollection(ctx.Request.Context(), "foo", *order).Return(true)

		ordapi.ListCancelReasons(ctx)

		var res CancelReasonsResponse
		testutil.DecodeJSON(t, record.Body, &res)
		require.Equal(t, http.StatusOK, record.Code)
		require.Equal(t, "ZENDESK", res[0].Target)
	})
}

func newUploadPhotoRequest(
	content []byte,
) (*gin.Context, *httptest.ResponseRecorder, error) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	var fw io.Writer

	orderID := "order_ID"

	fw, _ = w.CreateFormFile("photo", "image.jpg")
	_, _ = fw.Write(content)
	w.Close()

	path := fmt.Sprintf("/v1/order/%s/upload-photo", orderID)
	gctx, recorder := testutil.TestRequestContext("POST", path, &b)
	gctx.Request.Header.Set("Content-Type", w.FormDataContentType())

	return gctx, recorder, nil
}

type nopCloser struct {
	io.Reader
}

func (nopCloser) Close() error { return nil }

func TestOrderAPI_SaveCompareFaceSimilarity(t *testing.T) {
	t.Run("should save compare face similarity success", func(tt *testing.T) {
		cfg := OrderAPIConfig{SaveCompareFaceSimilarityEnabled: true}
		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, cfg)

		order := model.Order{
			OrderID: "order_id",
		}
		deps.DriverRepository.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.Driver{BaseDriver: model.BaseDriver{CitizenIDCardPhotoURL: "url"}}, nil)
		deps.VosSvc.EXPECT().GetObjectInternal(gomock.Any(), gomock.Any()).
			Return(&s3.GetObjectOutput{Body: nopCloser{bytes.NewBufferString("body data")}}, nil)
		deps.AwsService.EXPECT().CompareFaces(gomock.Any()).Return(aws.CompareFacesOutput{Similarity: 89.0, Confidence: 99.0}, nil)
		deps.OrderRepository.EXPECT().SetVerifiedRiderPhotoCompareFace(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		ordapi.saveCompareFaceSimilarity(context.Background(), &order, "verifiedPhotoId", nil)
	})

	t.Run("should do nothing when Get driver profile error", func(tt *testing.T) {
		cfg := OrderAPIConfig{SaveCompareFaceSimilarityEnabled: true}
		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, cfg)

		order := model.Order{
			OrderID: "order_id",
		}
		deps.DriverRepository.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.Driver{}, errors.New("error"))
		deps.VosSvc.EXPECT().GetObjectInternal(gomock.Any(), gomock.Any()).
			Return(&s3.GetObjectOutput{Body: nopCloser{bytes.NewBufferString("body data")}}, nil).Times(0)
		deps.AwsService.EXPECT().CompareFaces(gomock.Any()).Return(aws.CompareFacesOutput{Similarity: 89.0, Confidence: 99.0}, nil).Times(0)
		deps.OrderRepository.EXPECT().SetVerifiedRiderPhotoCompareFace(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(0)

		ordapi.saveCompareFaceSimilarity(context.Background(), &order, "verifiedPhotoId", nil)
	})

	t.Run("should do nothing when GetObjectInternal error", func(tt *testing.T) {
		cfg := OrderAPIConfig{SaveCompareFaceSimilarityEnabled: true}
		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, cfg)

		order := model.Order{
			OrderID: "order_id",
		}
		deps.DriverRepository.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.Driver{}, nil)
		deps.VosSvc.EXPECT().GetObjectInternal(gomock.Any(), gomock.Any()).
			Return(&s3.GetObjectOutput{Body: nopCloser{bytes.NewBufferString("body data")}}, errors.New("error"))
		deps.AwsService.EXPECT().CompareFaces(gomock.Any()).Return(aws.CompareFacesOutput{Similarity: 89.0, Confidence: 99.0}, nil).Times(0)
		deps.OrderRepository.EXPECT().SetVerifiedRiderPhotoCompareFace(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(0)

		ordapi.saveCompareFaceSimilarity(context.Background(), &order, "verifiedPhotoId", nil)
	})

	t.Run("should do nothing when CompareFaces error", func(tt *testing.T) {
		cfg := OrderAPIConfig{SaveCompareFaceSimilarityEnabled: true}
		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, cfg)

		order := model.Order{
			OrderID: "order_id",
		}
		deps.DriverRepository.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.Driver{}, nil)
		deps.VosSvc.EXPECT().GetObjectInternal(gomock.Any(), gomock.Any()).
			Return(&s3.GetObjectOutput{Body: nopCloser{bytes.NewBufferString("body data")}}, nil)
		deps.AwsService.EXPECT().CompareFaces(gomock.Any()).Return(aws.CompareFacesOutput{Similarity: 89.0, Confidence: 99.0}, errors.New("error"))
		deps.OrderRepository.EXPECT().SetVerifiedRiderPhotoCompareFace(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(0)

		ordapi.saveCompareFaceSimilarity(context.Background(), &order, "verifiedPhotoId", nil)
	})

	t.Run("should do nothing when SetVerifiedRiderPhotoCompareFace error", func(tt *testing.T) {
		cfg := OrderAPIConfig{SaveCompareFaceSimilarityEnabled: true}
		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, cfg)

		order := model.Order{
			OrderID: "order_id",
		}
		deps.DriverRepository.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.Driver{}, nil)
		deps.VosSvc.EXPECT().GetObjectInternal(gomock.Any(), gomock.Any()).
			Return(&s3.GetObjectOutput{Body: nopCloser{bytes.NewBufferString("body data")}}, nil)
		deps.AwsService.EXPECT().CompareFaces(gomock.Any()).Return(aws.CompareFacesOutput{Similarity: 89.0, Confidence: 99.0}, nil)
		deps.OrderRepository.EXPECT().SetVerifiedRiderPhotoCompareFace(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error"))

		ordapi.saveCompareFaceSimilarity(context.Background(), &order, "verifiedPhotoId", nil)
	})
}

func TestOrderAPI_UploadPhoto(t *testing.T) {
	t.Run("should upload photo success", func(tt *testing.T) {
		b64 := "dummy_base64"
		gctx, recorder, _ := newUploadPhotoRequest([]byte(b64))
		ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})

		order := model.Order{
			OrderID: "order_id",
		}

		deps.VosSvc.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&file.File{}, nil)
		deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
		deps.OrderRepository.EXPECT().UpdateVerifiedRiderPhoto(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		ordapi.UploadPhoto(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})
}

func TestOrderAPI_UploadPhotoWithWaitGroup(t *testing.T) {
	type testTable struct {
		Name                         string
		FileContent                  []byte
		MaximumByteInMB              float64
		IsCallFaceRecognitionService bool
	}

	byte6MBs := make([]byte, 6_000_000)
	for index := range byte6MBs {
		byte6MBs[index] = 'a'
	}
	testTables := []testTable{
		{
			Name:                         "should upload photo success",
			FileContent:                  []byte("dummy_base64"),
			MaximumByteInMB:              5,
			IsCallFaceRecognitionService: true,
		},
		{
			Name:                         "should upload photo fail (file size > 5MB)",
			FileContent:                  byte6MBs,
			MaximumByteInMB:              5,
			IsCallFaceRecognitionService: false,
		},
		{
			Name:                         "should upload photo success (file size > 5MB)",
			FileContent:                  byte6MBs,
			MaximumByteInMB:              7,
			IsCallFaceRecognitionService: true,
		},
	}

	for _, item := range testTables {
		t.Run(item.Name, func(tt *testing.T) {
			gctx, recorder, _ := newUploadPhotoRequest(item.FileContent)
			safedGctx := safe.CreateWaitGroupOnGctx(gctx)
			configs := OrderAPIConfig{
				SaveCompareFaceSimilarityEnabled: true,
				AWSFaceRecognitionMaxSizeInMB:    item.MaximumByteInMB,
			}
			ordapi, deps := newTestOrderAPI(tt, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{}, configs)

			order := model.Order{
				OrderID: "order_id",
			}
			driver := model.Driver{}
			citizenIdObj := s3.GetObjectOutput{Body: io.NopCloser(bytes.NewReader(item.FileContent))}
			silimarityVal := float64(100)
			vosUploadFunc := func(ctx context.Context, prefixName string, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
				io.ReadAll(content)
				return &file.File{}, nil
			}
			deps.VosSvc.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(vosUploadFunc)
			deps.OrderRepository.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
			deps.OrderRepository.EXPECT().UpdateVerifiedRiderPhoto(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			if item.IsCallFaceRecognitionService {
				deps.DriverRepository.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(&driver, nil)
				deps.VosSvc.EXPECT().GetObjectInternal(gomock.Any(), gomock.Any()).Return(&citizenIdObj, nil)
				compareFaceFunc := func(p aws.CompareParam) (float64, error) {
					require.Equal(tt, item.FileContent, p.SourceImage)
					return silimarityVal, nil
				}
				deps.AwsService.EXPECT().CompareFaces(gomock.Any()).DoAndReturn(compareFaceFunc)
				deps.OrderRepository.EXPECT().SetVerifiedRiderPhotoCompareFace(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			}

			ordapi.UploadPhoto(gctx)
			safedGctx.Wait()
			require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
		})
	}
}

func TestOrderAPI_GetQRPaymentDetail(t *testing.T) {
	t.Run("get_waiting_for_payment_qr_status", func(tt *testing.T) {
		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{}, {Pauses: model.PauseSet{model.PauseQRPayment: true}},
				},
			},
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)
		qrPaymentRes := v1.QRPaymentDetailResponse{
			QRCode:    "RES_RAW_QR_CODE",
			Amount:    65535,
			CreatedAt: timeutils.Now(),
			ExpiredAt: timeutils.Now().Add(time.Minute * 15),
			Status:    v1.QRPaymentWaitingForPaymentStatus,
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(&qrPaymentRes, nil)

		ordApi.GetQRPaymentDetail(ctxRec.GinCtx())

		var actualResponse QRPaymentDetailResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)
		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		require.Equal(tt, qrPaymentRes.QRCode, actualResponse.QRCode)
		require.Equal(tt, qrPaymentRes.Amount, *actualResponse.Amount)
		require.Equal(tt, qrPaymentRes.ExpiredAt.Local(), actualResponse.ExpiredAt.Local())
		require.Equal(tt, string(qrPaymentRes.Status), actualResponse.Status)
	})

	t.Run("get_paid_qr_status", func(tt *testing.T) {
		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)
		qrPaymentRes := v1.QRPaymentDetailResponse{
			QRCode:    "RES_RAW_QR_CODE",
			Amount:    65535,
			CreatedAt: timeutils.Now(),
			ExpiredAt: timeutils.Now().Add(time.Minute * 15),
			Status:    v1.QRPaymentPaidStatus,
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(&qrPaymentRes, nil)
		deps.OrderRepository.EXPECT().UnsetPauseFlag(gomock.Any(), ord.OrderID, 1, model.PauseQRPayment).Return(nil)

		ordApi.GetQRPaymentDetail(ctxRec.GinCtx())

		var actualResponse QRPaymentDetailResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)
		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		require.Equal(tt, qrPaymentRes.QRCode, actualResponse.QRCode)
		require.Equal(tt, qrPaymentRes.Amount, *actualResponse.Amount)
		require.Equal(tt, qrPaymentRes.ExpiredAt.Local(), actualResponse.ExpiredAt.Local())
		require.Equal(tt, string(qrPaymentRes.Status), actualResponse.Status)
	})

	t.Run("invalid_request_error", func(tt *testing.T) {
		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{}, {Pauses: model.PauseSet{model.PauseQRPayment: true}},
				},
			},
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)
		resApiError := &api.Error{
			Code:    api.ERRCODE_INVALID_REQUEST,
			Message: "order not allowed to generate qr",
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(nil, resApiError)

		ordApi.GetQRPaymentDetail(ctxRec.GinCtx())

		var actualResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)
		require.Equal(tt, http.StatusBadRequest, ctxRec.ResponseRecorder.Code)
		require.Equal(tt, api.ERRCODE_INVALID_REQUEST, actualResponse.Code)
	})

	t.Run("convert_to_cash_error", func(tt *testing.T) {
		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				PayAtStop:   1,
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{},
					{
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
							},
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
							},
							ExperimentalDeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
							},
						},
						Pauses: model.PauseSet{
							model.PauseQRPayment: true,
						},
						CollectPayment: false,
					},
				},
			},
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)
		resApiError := &api.Error{
			Code:    "QR_PROMPTPAY_ALREADY_CONVERTED_TO_CASH",
			Message: "qr promptpay already convert to cash",
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(nil, resApiError)
		deps.OrderRepository.EXPECT().UnsetPauseFlag(gomock.Any(), ord.OrderID, 1, model.PauseQRPayment).Return(nil)
		ordApi.GetQRPaymentDetail(ctxRec.GinCtx())

		var actualResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)
		require.Equal(tt, http.StatusBadRequest, ctxRec.ResponseRecorder.Code)
		require.Equal(tt, "QR_PROMPTPAY_ALREADY_CONVERTED_TO_CASH", actualResponse.Code)
	})

	t.Run("custom_error", func(tt *testing.T) {
		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{}, {Pauses: model.PauseSet{model.PauseQRPayment: true}},
				},
			},
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)
		resApiError := &api.Error{
			Code:    "CUSTOM_ERROR_CODE",
			Message: "custom error message",
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(nil, resApiError)

		ordApi.GetQRPaymentDetail(ctxRec.GinCtx())

		var actualResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)
		require.Equal(tt, http.StatusInternalServerError, ctxRec.ResponseRecorder.Code)
		require.Equal(tt, "CUSTOM_ERROR_CODE", actualResponse.Code)
		require.Equal(tt, "custom error message", actualResponse.Message)
	})

	t.Run("convert_to_cash_error_from_2nd_call", func(tt *testing.T) {
		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				PayAtStop:   1,
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{},
					{
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodCash,
							},
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
							},
							ExperimentalDeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
							},
						},
						Pauses:         model.PauseSet{},
						CollectPayment: false,
					},
				},
			},
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		ordApi.GetQRPaymentDetail(ctxRec.GinCtx())

		var actualResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)
		require.Equal(tt, http.StatusBadRequest, ctxRec.ResponseRecorder.Code)
		require.Equal(tt, "QR_PROMPTPAY_ALREADY_CONVERTED_TO_CASH", actualResponse.Code)
	})
}

func TestOrderAPI_ValidateConvertQRPaymentToCash(t *testing.T) {
	t.Parallel()

	getFloatPtr := func(val float64) *float64 {
		return &val
	}

	getBaseOrder := func() model.Order {
		return model.Order{
			Status:  model.StatusDriverArrived,
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				UserID:      "REQ_USER_ID",
				ServiceType: model.ServiceFood,
				PayAtStop:   1,
				Routes: []model.Stop{
					{}, {
						Pauses: model.PauseSet{model.PauseQRPayment: true},
						PriceSummary: model.PriceSummary{
							ItemFee: model.ItemFeeSummary{
								Total:    100.0,
								SubTotal: 100.0,
							},
							DeliveryFee: model.DeliveryFeeSummary{
								UserDeliveryFee: 20,
								Total:           20,
							},
						},
					},
				},
				RevenuePrincipalModel: true,
			},
		}
	}

	getMartOrder := func() model.Order {
		return model.Order{
			Status:  model.StatusDriverArrived,
			OrderID: "MART_REQ_ORDER_ID",
			Driver:  "MART_REQ_DRIVER_ID",
			Quote: model.Quote{
				UserID:      "MART_REQ_USER_ID",
				ServiceType: model.ServiceMart,
				PayAtStop:   1,
				Routes: []model.Stop{
					{}, {
						Pauses: model.PauseSet{model.PauseQRPayment: true},
						PriceSummary: model.PriceSummary{
							ItemFee: model.ItemFeeSummary{
								Total:    100.0,
								SubTotal: 100.0,
							},
							DeliveryFee: model.DeliveryFeeSummary{
								UserDeliveryFee: 20,
								Total:           20,
							},
						},
					},
				},
				RevenuePrincipalModel: true,
			},
		}
	}

	t.Run("qr_payment_to_cash", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{
			OriginalPrice: 120,
			NewPrice:      120,
		}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		var actualResponse ValidateConvertQRToCashResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)

		expectedResponse := ValidateConvertQRToCashResponse{
			DriverLabel: `หากชำระด้วยเงินสด คุณจะถูกตัดค่าอาหารผ่านเครดิต
กรุณาเก็บเงินสดค่าอาหารจากลูกค้า`,
			CustomerOriginalPrice: nil,
			CustomerNewPrice:      120.0,
			DriverCreditDeduction: getFloatPtr(120.0),
		}
		require.Equal(tt, expectedResponse, actualResponse)
	})

	t.Run("mart_qr_payment_to_cash", func(tt *testing.T) {
		tt.Parallel()
		ord := getMartOrder()

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "MART_REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{
			OriginalPrice: 120,
			NewPrice:      120,
		}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		var actualResponse ValidateConvertQRToCashResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)

		expectedResponse := ValidateConvertQRToCashResponse{
			DriverLabel: `หากชำระด้วยเงินสด คุณจะถูกตัดค่าสินค้าผ่านเครดิต
กรุณาเก็บเงินสดค่าสินค้าจากลูกค้า`,
			CustomerOriginalPrice: nil,
			CustomerNewPrice:      120.0,
			DriverCreditDeduction: getFloatPtr(120.0),
		}
		require.Equal(tt, expectedResponse, actualResponse)
	})

	t.Run("qr_payment_to_cash_with_coupon_and_price_changed", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()
		ord.Routes[1].PriceSummary.ItemFee = model.ItemFeeSummary{
			Total:    80,
			SubTotal: 100,
			Discounts: []model.Discount{
				{
					Type:     model.DiscountTypeCouponAdvance,
					Category: "COUPON",
					Discount: 20,
					Code:     "XXX",
				},
			},
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{
			OriginalPrice: 100,
			NewPrice:      120,
		}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		var actualResponse ValidateConvertQRToCashResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)

		expectedResponse := ValidateConvertQRToCashResponse{
			DriverLabel: `หากชำระด้วยเงินสด คุณจะถูกตัดค่าอาหารผ่านเครดิต
กรุณาเก็บเงินสดค่าอาหารจากลูกค้า`,
			CustomerOriginalPrice: getFloatPtr(100.0),
			CustomerNewPrice:      120.0,
			DriverCreditDeduction: getFloatPtr(120.0),
		}
		require.Equal(tt, expectedResponse, actualResponse)
	})

	t.Run("qr_payment_to_cash_(advance_e_payment)", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()
		ord.Options.DriverMoneyFlow = model.FlowCashAdvancementEpayment

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{
			OriginalPrice: 120,
			NewPrice:      120,
		}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		var actualResponse ValidateConvertQRToCashResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)

		expectedResponse := ValidateConvertQRToCashResponse{
			DriverLabel:           "กรุณาเก็บเงินสดค่าอาหารจากลูกค้า",
			CustomerOriginalPrice: nil,
			CustomerNewPrice:      120.0,
		}
		require.Equal(tt, expectedResponse, actualResponse)
	})

	t.Run("qr_payment_to_cash_(advance_e_payment)_coupon", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()
		ord.Routes[1].PriceSummary.ItemFee = model.ItemFeeSummary{
			Total:    80,
			SubTotal: 100,
			Discounts: []model.Discount{
				{
					Type:     model.DiscountTypeCouponAdvance,
					Category: "COUPON",
					Discount: 20,
					Code:     "XXX",
				},
			},
		}
		ord.Options.DriverMoneyFlow = model.FlowCashAdvancementEpayment

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{
			OriginalPrice: 100,
			NewPrice:      100,
		}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		var actualResponse ValidateConvertQRToCashResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)

		expectedResponse := ValidateConvertQRToCashResponse{
			DriverLabel:           "กรุณาเก็บเงินสดค่าอาหารจากลูกค้า\nคุณจะได้รับส่วนต่างที่คุณจ่ายร้านคืนผ่านวอลเล็ต",
			CustomerOriginalPrice: nil,
			CustomerNewPrice:      100.0,
		}
		require.Equal(tt, expectedResponse, actualResponse)
	})

	t.Run("qr_payment_to_cash_(advance_e_payment)_coupon_with_price_changed", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()
		ord.Routes[1].PriceSummary.ItemFee = model.ItemFeeSummary{
			Total:    80,
			SubTotal: 100,
			Discounts: []model.Discount{
				{
					Type:     model.DiscountTypeCouponAdvance,
					Category: "COUPON",
					Discount: 20,
					Code:     "XXX",
				},
			},
		}
		ord.Options.DriverMoneyFlow = model.FlowCashAdvancementEpayment

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{
			OriginalPrice: 100,
			NewPrice:      120,
		}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		var actualResponse ValidateConvertQRToCashResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)

		expectedResponse := ValidateConvertQRToCashResponse{
			DriverLabel:           "กรุณาเก็บเงินสดค่าอาหารจากลูกค้า",
			CustomerOriginalPrice: getFloatPtr(100.0),
			CustomerNewPrice:      120.0,
		}
		require.Equal(tt, expectedResponse, actualResponse)
	})

	t.Run("qr_payment_to_cash_(advance_e_payment)_lineman_wallet_with_price_changed", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()
		ord.Routes[1].PriceSummary.ItemFee = model.ItemFeeSummary{
			Total:    100,
			SubTotal: 100,
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{
			OriginalPrice:         120,
			NewPrice:              120,
			OriginalLinemanCredit: 50,
		}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		var actualResponse ValidateConvertQRToCashResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)

		expectedResponse := ValidateConvertQRToCashResponse{
			DriverLabel: `หากชำระด้วยเงินสด คุณจะถูกตัดค่าอาหารผ่านเครดิต
กรุณาเก็บเงินสดค่าอาหารจากลูกค้า`,
			CustomerOriginalPrice: getFloatPtr(70.0),
			CustomerNewPrice:      120.0,
			DriverCreditDeduction: getFloatPtr(120.0),
		}
		require.Equal(tt, expectedResponse, actualResponse)
	})

	t.Run("qr_payment_to_cash_(advance_e_payment)_coupon_and_lineman_wallet_with_price_changed", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()
		ord.Routes[1].PriceSummary.ItemFee = model.ItemFeeSummary{
			Total:    80,
			SubTotal: 100,
			Discounts: []model.Discount{
				{
					Type:     model.DiscountTypeCouponAdvance,
					Category: "COUPON",
					Discount: 20,
					Code:     "XXX",
				},
			},
		}
		ord.Options.DriverMoneyFlow = model.FlowCashAdvancementEpayment

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{
			OriginalPrice:         100,
			NewPrice:              100,
			OriginalLinemanCredit: 50,
		}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
		var actualResponse ValidateConvertQRToCashResponse
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)

		expectedResponse := ValidateConvertQRToCashResponse{
			DriverLabel: `กรุณาเก็บเงินสดค่าอาหารจากลูกค้า
คุณจะได้รับส่วนต่างที่คุณจ่ายร้านคืนผ่านวอลเล็ต`,
			CustomerOriginalPrice: getFloatPtr(50.0),
			CustomerNewPrice:      100.0,
		}
		require.Equal(tt, expectedResponse, actualResponse)
	})

	t.Run("validate_from_pure_qr_order_failed", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		qrPaymentRes := v1.ChangeQRPaymentMethodToCashResponse{}
		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&qrPaymentRes, api.NewAPIError(api.ERRCODE_INVALID_REQUEST, "order already finished"))

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusInternalServerError, ctxRec.ResponseRecorder.Code)
		var actualErrorResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualErrorResponse)
		require.Equal(tt, api.ERRCODE_INVALID_REQUEST, actualErrorResponse.Code)
		require.Equal(tt, "order already finished", actualErrorResponse.Message)
	})

	t.Run("invalid_order_status", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()
		ord.Status = model.StatusDriverArrivedRestaurant

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusBadRequest, ctxRec.ResponseRecorder.Code)
		var actualErrorResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualErrorResponse)
		require.Equal(tt, api.ERRCODE_INVALID_REQUEST, actualErrorResponse.Code)
		require.Equal(tt, "unable to convert QR Payment while not in ARRIVED status", actualErrorResponse.Message)
	})

	t.Run("invalid_driver_id", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "EX_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusBadRequest, ctxRec.ResponseRecorder.Code)
		var actualErrorResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualErrorResponse)
		require.Equal(tt, api.ERRCODE_INVALID_REQUEST, actualErrorResponse.Code)
		require.Equal(tt, "invalid driver id", actualErrorResponse.Message)
	})

	t.Run("got_error_qr_paid_from_delivery", func(tt *testing.T) {
		tt.Parallel()
		ord := getBaseOrder()

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		deps.Delivery.EXPECT().ValidateConvertQRPaymentToCash(gomock.Any(), &v1.ChangeQRPaymentMethodToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&v1.ChangeQRPaymentMethodToCashResponse{}, &api.Error{
			Code:    "QR_PROMPTPAY_ALREADY_PAID",
			Message: "the order already paid via qr promptpay by user",
		})

		deps.OrderRepository.EXPECT().
			UnsetPauseFlag(gomock.Any(), "REQ_ORDER_ID", 1, model.Pause("WAITING_QR_PAYMENT")).
			Return(nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusConflict, ctxRec.ResponseRecorder.Code)

		var actualResponse *api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)
		require.Equal(tt, v1.ErrCodeQRPromptpayAlreadyPaid, actualResponse.Code)
	})
}

func TestOrderAPI_ConvertQRPaymentToCash(t *testing.T) {
	t.Parallel()

	t.Run("confirm_convert_from_pure_qr_order", func(tt *testing.T) {
		tt.Parallel()
		ord := model.Order{
			Driver: "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				UserID:      "REQ_USER_ID",
			},
			OrderID: "REQ_ORDER_ID",
			Status:  model.StatusDriverArrived,
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		deps.Delivery.EXPECT().ConfirmConvertQRPaymentToCashV2(gomock.Any(), &v2.ConfirmConvertQRPaymentToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&v2.ConfirmConvertQRPaymentToCashResponse{}, nil)

		deps.OrderRepository.EXPECT().SetDriverAction(gomock.Any(), "REQ_ORDER_ID", model.DriverAction("CHANGED_PAYMENT_METHOD_QR_TO_CASH"), gomock.Any()).
			DoAndReturn(func(ctx context.Context, orderID string, action model.DriverAction, actionAt time.Time) error {
				require.True(tt, timeutil.Abs(time.Now(), actionAt) < time.Second)
				return nil
			})

		ordApi.ConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
	})

	t.Run("confirm_convert_from_pure_qr_order_but_failed", func(tt *testing.T) {
		tt.Parallel()
		ord := model.Order{
			Driver: "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				UserID:      "REQ_USER_ID",
			},
			OrderID: "REQ_ORDER_ID",
			Status:  model.StatusDriverArrived,
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		deps.Delivery.EXPECT().ConfirmConvertQRPaymentToCashV2(gomock.Any(), &v2.ConfirmConvertQRPaymentToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&v2.ConfirmConvertQRPaymentToCashResponse{}, api.NewAPIError(api.ERRCODE_INVALID_REQUEST, "order already finished"))

		ordApi.ConvertQRPaymentToCash(ctxRec.GinCtx())
		require.Equal(tt, http.StatusInternalServerError, ctxRec.ResponseRecorder.Code)
		var actualErrorResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualErrorResponse)
		require.Equal(tt, api.ERRCODE_INVALID_REQUEST, actualErrorResponse.Code)
		require.Equal(tt, "order already finished", actualErrorResponse.Message)
	})

	t.Run("invalid_order_status", func(tt *testing.T) {
		tt.Parallel()
		ord := model.Order{
			Driver: "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				UserID:      "REQ_USER_ID",
			},
			OrderID: "REQ_ORDER_ID",
			Status:  model.StatusDriverArrivedRestaurant,
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		ordApi.ValidateConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusBadRequest, ctxRec.ResponseRecorder.Code)
		var actualErrorResponse api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualErrorResponse)
		require.Equal(tt, api.ERRCODE_INVALID_REQUEST, actualErrorResponse.Code)
		require.Equal(tt, "unable to convert QR Payment while not in ARRIVED status", actualErrorResponse.Message)
	})

	t.Run("got_error_qr_paid_from_delivery", func(tt *testing.T) {
		tt.Parallel()
		ord := model.Order{
			Driver: "REQ_DRIVER_ID",
			Quote: model.Quote{
				UserID:      "REQ_USER_ID",
				ServiceType: model.ServiceFood,
				PayAtStop:   1,
			},
			OrderID: "REQ_ORDER_ID",
			Status:  model.StatusDriverArrived,
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{CDNEndpoint: "https://cdn-dev.net"}, ContingencyConfig{})
		deps.OrderRepository.EXPECT().Get(gomock.Any(), ord.OrderID).Return(&ord, nil)

		deps.Delivery.EXPECT().ConfirmConvertQRPaymentToCashV2(gomock.Any(), &v2.ConfirmConvertQRPaymentToCashRequest{
			OrderID: ord.OrderID,
			UserID:  ord.UserID,
		}).Return(&v2.ConfirmConvertQRPaymentToCashResponse{}, &api.Error{
			Code:    "QR_PROMPTPAY_ALREADY_PAID",
			Message: "the order already paid via qr promptpay by user",
		})

		deps.OrderRepository.EXPECT().
			UnsetPauseFlag(gomock.Any(), "REQ_ORDER_ID", 1, model.Pause("WAITING_QR_PAYMENT")).
			Return(nil)

		ordApi.ConvertQRPaymentToCash(ctxRec.GinCtx())

		require.Equal(tt, http.StatusConflict, ctxRec.ResponseRecorder.Code)

		var actualResponse *api.Error
		testutil.DecodeJSON(tt, ctxRec.ResponseRecorder.Body, &actualResponse)
		require.Equal(tt, v1.ErrCodeQRPromptpayAlreadyPaid, actualResponse.Code)
	})
}

func TestOrderAPI_SetCellPhoneContact(t *testing.T) {
	t.Parallel()

	t.Run("should return 200 when set correctly", func(tt *testing.T) {
		tt.Parallel()
		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{}, ContingencyConfig{})

		deps.OrderRepository.EXPECT().SetCellPhoneContact(gomock.Any(), "REQ_ORDER_ID", "REQ_DRIVER_ID", gomock.Any()).
			DoAndReturn(func(ctx context.Context, orderID string, driverID string, now time.Time, opts ...interface{}) error {
				return nil
			})

		ordApi.SetCellPhoneContact(ctxRec.GinCtx())

		require.Equal(tt, http.StatusOK, ctxRec.ResponseRecorder.Code)
	})

	t.Run("should return 400 when unable to set", func(tt *testing.T) {
		tt.Parallel()
		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
		}

		ctxRec := testutil.NewContextWithRecorder()
		ctxRec.GinCtx().Params = createGetOrderDetailParams(ord.OrderID)
		driver.SetDriverIDToContext(ctxRec.GinCtx(), "REQ_DRIVER_ID")

		ordApi, deps := newTestOrderAPI(t, file.VOSConfig{}, ContingencyConfig{})

		deps.OrderRepository.EXPECT().SetCellPhoneContact(gomock.Any(), "REQ_ORDER_ID", "REQ_DRIVER_ID", gomock.Any()).
			DoAndReturn(func(ctx context.Context, orderID string, driverID string, now time.Time, opts ...interface{}) error {
				return mongodb.ErrDataNotFound
			})

		ordApi.SetCellPhoneContact(ctxRec.GinCtx())

		require.Equal(tt, http.StatusInternalServerError, ctxRec.ResponseRecorder.Code)
	})
}

type orderDeps struct {
	OrderRepository             *mock_repository.MockOrderRepository
	AssignmentLogRepo           *mock_repository.MockAssignmentLogRepository
	DriverRepository            *mock_repository.MockDriverRepository
	AssignmentRepo              *mock_repository.MockAssignmentRepository
	AssignmentRejectionRepo     *mock_repository.MockAssignmentRejectionRepository
	DriverTransactionRepo       *mock_repository.MockDriverTransactionRepository
	RatingRestaurantRepo        *mock_repository.MockRatingRestaurantRepository
	CancelReasonRepo            *mock_repository.MockCancelReasonRepository
	ServiceAreaRepo             *mock_repository.MockServiceAreaRepository
	CoinCashConversionRepo      *mock_repository.MockCoinCashConversionRateRepository
	BanService                  *mock_service.MockBanService
	DriverService               *mock_service.MockDriverServiceInterface
	NotifierService             *mock_service.MockNotifier
	AssignerService             *mock_service.MockAssigner
	RepService                  *mock_rep.MockREPService
	Bus                         *mock_event_bus.MockEventBus
	VosSvc                      *mock_service.MockVOSService
	ShiftService                *mock_service.MockShiftServices
	AwsService                  *mock_service.MockAwsService
	Distributor                 *mock_order.MockOrderDistributor
	TxnHelper                   *mock_transaction.MockTxnHelper
	TripSvc                     *mock_service.MockTripServices
	TripRepo                    *mock_repository.MockTripRepository
	CookingTimeDelayRepo        *mock_repository.MockCookingTimeDelayRepository
	OnTopFareService            *mock_service.MockOnTopFareService
	DriverLocationRepo          *mock_repository.MockDriverLocationRepository
	MapService                  *mock_mapservice.MockMapService
	ThrottledOrderRepo          *mock_repository.MockThrottledOrderRepository
	Delivery                    *mock_delivery.MockDelivery
	Locker                      *mock_locker.MockLocker
	FraudService                *mock_fraud.MockService
	FeatureFlagService          *mock_featureflag.MockService
	ServiceOptInReminderService *mock_service.MockServiceOptInReminderService
	Dispatcher                  *mock_dispatcher.MockDispatcher
}

func newTestOrderAPI(t *testing.T, vosCfg file.VOSConfig, contingencyCfg ContingencyConfig, cfg ...OrderAPIConfig) (*OrderAPI, *orderDeps) {
	legacyCtrl := legacyMock.NewController(t)
	ctrl := gomock.NewController(t)
	deps := &orderDeps{
		OrderRepository:             mock_repository.NewMockOrderRepository(legacyCtrl),
		AssignmentLogRepo:           mock_repository.NewMockAssignmentLogRepository(legacyCtrl),
		DriverRepository:            mock_repository.NewMockDriverRepository(legacyCtrl),
		AssignmentRepo:              mock_repository.NewMockAssignmentRepository(legacyCtrl),
		AssignmentRejectionRepo:     mock_repository.NewMockAssignmentRejectionRepository(legacyCtrl),
		DriverTransactionRepo:       mock_repository.NewMockDriverTransactionRepository(legacyCtrl),
		RatingRestaurantRepo:        mock_repository.NewMockRatingRestaurantRepository(legacyCtrl),
		CancelReasonRepo:            mock_repository.NewMockCancelReasonRepository(legacyCtrl),
		ServiceAreaRepo:             mock_repository.NewMockServiceAreaRepository(legacyCtrl),
		CoinCashConversionRepo:      mock_repository.NewMockCoinCashConversionRateRepository(legacyCtrl),
		BanService:                  mock_service.NewMockBanService(legacyCtrl),
		DriverService:               mock_service.NewMockDriverServiceInterface(legacyCtrl),
		NotifierService:             mock_service.NewMockNotifier(legacyCtrl),
		AssignerService:             mock_service.NewMockAssigner(legacyCtrl),
		RepService:                  mock_rep.NewMockREPService(legacyCtrl),
		Bus:                         mock_event_bus.NewMockEventBus(legacyCtrl),
		VosSvc:                      mock_service.NewMockVOSService(legacyCtrl),
		ShiftService:                mock_service.NewMockShiftServices(legacyCtrl),
		AwsService:                  mock_service.NewMockAwsService(legacyCtrl),
		Distributor:                 mock_order.NewMockOrderDistributor(legacyCtrl),
		TxnHelper:                   mock_transaction.NewMockTxnHelper(legacyCtrl),
		TripSvc:                     mock_service.NewMockTripServices(legacyCtrl),
		CookingTimeDelayRepo:        mock_repository.NewMockCookingTimeDelayRepository(legacyCtrl),
		OnTopFareService:            mock_service.NewMockOnTopFareService(legacyCtrl),
		DriverLocationRepo:          mock_repository.NewMockDriverLocationRepository(legacyCtrl),
		MapService:                  mock_mapservice.NewMockMapService(legacyCtrl),
		ThrottledOrderRepo:          mock_repository.NewMockThrottledOrderRepository(legacyCtrl),
		Locker:                      mock_locker.NewMockLocker(legacyCtrl),
		Delivery:                    mock_delivery.NewMockDelivery(legacyCtrl),
		FraudService:                mock_fraud.NewMockService(ctrl),
		FeatureFlagService:          mock_featureflag.NewMockService(legacyCtrl),
		ServiceOptInReminderService: mock_service.NewMockServiceOptInReminderService(legacyCtrl),
		Dispatcher:                  mock_dispatcher.NewMockDispatcher(legacyCtrl),
	}
	c := OrderAPIConfig{}
	if len(cfg) > 0 {
		c = cfg[0]
	}
	if c.AtomicOrderDBConfig == nil {
		c.AtomicOrderDBConfig = NewAtomicOrderDBConfig(OrderDBConfig{})
	}
	canceller := ProvideCanceller(
		deps.OrderRepository,
		deps.AssignerService,
		deps.AssignmentLogRepo,
		deps.NotifierService,
		deps.DriverRepository,
		deps.BanService,
		deps.RepService,
		deps.Bus,
		deps.DriverService,
		deps.ShiftService,
		c,
		config.NewAtomicCancellationRateConfig(config.CancellationRateConfig{}),
		deps.Distributor,
		deps.TxnHelper,
		deps.TripSvc,
		deps.TripRepo,
		deps.CookingTimeDelayRepo,
		deps.OnTopFareService,
		deps.DriverLocationRepo,
		deps.MapService,
		deps.Locker,
		deps.ServiceAreaRepo,
		deps.ThrottledOrderRepo,
		deps.Delivery,
		testmetric.NewStubMeter(),
		deps.ServiceOptInReminderService,
		deps.Dispatcher,
		deps.FeatureFlagService,
	)
	deps.CookingTimeDelayRepo.EXPECT().RemoveOrder(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	ordapi := ProvideOrderAPI(
		deps.OrderRepository,
		deps.AssignmentLogRepo,
		deps.DriverRepository,
		deps.AssignmentRepo,
		deps.AssignmentRejectionRepo,
		c,
		deps.DriverTransactionRepo,
		deps.RatingRestaurantRepo,
		deps.CancelReasonRepo,
		deps.ServiceAreaRepo,
		deps.CoinCashConversionRepo,
		deps.DriverService,
		deps.BanService,
		deps.NotifierService,
		deps.AssignerService,
		deps.RepService,
		deps.Bus,
		deps.VosSvc,
		deps.Delivery,
		vosCfg,
		NewAtomicContingencyConfig(contingencyCfg),
		deps.ShiftService,
		deps.AwsService,
		deps.Distributor,
		canceller,
		deps.TripSvc,
		deps.FraudService,
		deps.FeatureFlagService,
	)

	t.Cleanup(func() {
		legacyCtrl.Finish()
		ctrl.Finish()
	})

	return ordapi, deps
}
