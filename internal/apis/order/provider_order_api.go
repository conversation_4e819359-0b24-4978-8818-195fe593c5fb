package order

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/api"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	lmdelivery "git.wndv.co/lineman/delivery-service/pkg/client"
	v1 "git.wndv.co/lineman/delivery-service/types/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiErr "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	repApi "git.wndv.co/lineman/fleet-distribution/internal/apis/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/geo"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

var ErrAutoConfirmPrice = errors.New("something wrong on confirm price")

const (
	RemarkCompleteOriginDelivery      = "Order has completed from delivery service."
	RemarkCompleteWithoutSyncDelivery = "order นี้ทำการ force complete ที่ฝั่ง driver เนื่องจาก user/driver order not sync"
)

func (fapi *FoodProviderImpl) UpdatePrice(ctx *gin.Context, order *domainModel.Order, orderID, driverID string) {
	backCompatRestaurantType(order)
	var req UpdatePriceRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		ctx.Error(err)
		return
	}
	if err := req.Validate(); err != nil {
		ctx.Error(apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	orderRepo := fapi.OrderService

	if order.Driver != driverID {
		ctx.Error(apiErr.ErrDriverInvalid())
		return
	}

	if err := ValidateOrderStatus(order.Status); err != nil {
		ctx.Error(err)
		return
	}

	stopRestaurant := domainModel.CastStopInfoFood(&order.Routes[0].Info.StopInfo)

	backCompatRestaurantType(order)
	if len(order.Routes) != 0 && !order.Routes[0].Pauses[domainModel.PauseConfirmPrice] {
		ctx.Error(apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.New("cannot update price for RMS order")))
		return
	}

	if !order.RevampedStatus && order.Status != domainModel.StatusDriverArrivedRestaurant && order.Status != domainModel.StatusWaitingUserConfirmPrice {
		ctx.Error(ErrOrderStatusNotAllowed())
		return
	}

	updatePriceRequest := &lmdelivery.UpdatePriceRequest{
		OrderID:                order.OrderID,
		HelpBuyAmount:          fmt.Sprintf("%.02f", req.TotalPrice),
		HelpBuyAmountCurrency:  "THB",
		PreparationTimeMinutes: req.EstimatedTime / 60,
	}

	updatePriceRes, err := fapi.Delivery.UpdatePrice(ctx, updatePriceRequest)
	if err != nil {
		ctx.Error(err)
		return
	}

	totalDiscount := 0.0
	itemDiscounts := make([]domainModel.Discount, len(updatePriceRes.Discounts))
	for i, d := range updatePriceRes.Discounts {
		if d.Type != string(domainModel.DiscountTypeCouponAdvance) {
			ctx.Error(fmt.Errorf("unknow discount type %s from delivery response", d.Type))
			return
		}

		itemDiscounts[i] = domainModel.Discount{
			Type:     domainModel.DiscountTypeCouponAdvance,
			Category: d.Category,
			Code:     d.Code,
			Discount: d.DiscountPrice,
		}
		totalDiscount += d.DiscountPrice
	}

	order.SetItemDiscounts(itemDiscounts)
	order.SetItemFee(req.TotalPrice - totalDiscount)
	order.SetItemFeeBeforeDiscount(&req.TotalPrice)
	stopRestaurant.EstimatedCookingTime = domainModel.DurationSecond(req.EstimatedTime)
	order.Summary(GetCommissionRate(fapi.Cfg, order.ServiceType.String()), fapi.Cfg.AtomicOrderDBConfig.Get().WithHoldingTax)

	if order.Status == domainModel.StatusDriverArrivedRestaurant {
		backCompatRestaurantType(order)
		transitionOrderStatus(order)
	} else if order.Status == domainModel.StatusArrivedAt && order.HeadTo == 0 {
		order.MarkUpdatePrice()
	}

	if updatePriceRes.OrderStatus == string(domainModel.StatusUserConfirmedPrice) {
		if err := TransitionToConfirmPrice(order); err != nil {
			safe.SentryErrorMessage("has error when try to auto confirm price", safe.WithOrderID(order.OrderID))
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, ErrAutoConfirmPrice))
			return
		}
		event := service.EventOrderUpdated(orderID)
		if err := fapi.Notifier.Notify(ctx, []string{order.Driver}, event, service.WithFirebase, service.WithSocketIO); err != nil {
			logrus.Errorf("Failed to notify driver. driver=%s event=%v err=%s", order.Driver, event, err)
		}
	}

	err = orderRepo.UpdateOrder(ctx, order)
	if err != nil {
		switch err {
		case mongodb.ErrDataNotFound:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, repository.ErrNotFound))
			return
		default:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, err))
			return
		}
	}

	apiutil.OK(ctx, gin.H{})
}

func (fapi *FoodProviderImpl) UpdateOrderStatus(ctx *gin.Context, order *domainModel.Order, orderID, driverID string) {
	driverLocation := GetDriverLocation(ctx)

	res, err := fapi.UpdateOrderStatusV2(ctx, order, driverID, driverLocation, nil, nil)
	if err != nil {
		ctx.Error(err)
		return
	}

	if res != nil {
		// TODO remove after client for trip 100% rollout
		if order.TripID != "" {
			if trip, err := fapi.TripServices.GetTripByID(ctx, order.TripID); err == nil {
				res.Info.TransferredAmount = trip.DriverWageSummary.TransferAmount.Float64()
				res.Info.OutstandingAmount = trip.DriverWageSummary.Outstanding.Float64()
				if trip.DriverWageSummary.TransferAmount.Equal(0) && trip.DriverWageSummary.Outstanding.Equal(0) {
					res.Info.Code = CreditEnoughCashUpdateStatusCode
				} else {
					res.Info.Code = TransferredWalletUpdateStatusCode
				}
			}
		}

		apiutil.OK(ctx, res)
		return
	}

	apiutil.OK(ctx, gin.H{})
}

func (fapi *FoodProviderImpl) UpdateOrderStatusV2(ctx context.Context, order *domainModel.Order, driverID string, driverLocation *domainModel.Location, pods []lmdelivery.ProofOfDeliveryImage, tripMtx *sync.Mutex) (*UpdateStatusRes, error) {
	backCompatRestaurantType(order)
	if driverLocation != nil {
		ctx = geo.WrapContext(ctx, driverLocation.Lat, driverLocation.Lng, timeutils.Now())
	}
	if order.Driver != driverID {
		return nil, apiErr.ErrDriverInvalid()
	}

	if err := ValidateOrderStatus(order.Status); err != nil {
		return nil, err
	}

	headTo := order.RevampHeadTo()
	err := allowToTransition(order.Status, order.Routes[headTo].Pauses)
	if err != nil {
		switch err {
		case domainModel.ErrMustWaitCheckout:
			return nil, ErrMustWaitForCheckout()
		case domainModel.ErrMustWaitConfirmPrice:
			return nil, ErrMustWaitForConfirmPrice()
		case domainModel.ErrMustUploadDeliveringPhoto:
			if !fapi.Cfg.AtomicOrderDBConfig.Get().DisableVOSPhoto {
				return nil, ErrMustUploadDeliveringPhoto()
			}

			// VOS service was disabled, so ignore the order and remove pause delivering photo
			delete(order.Routes[headTo].Pauses, domainModel.PauseDeliveringPhoto)
		default:
			return nil, ErrOrderStatusNotAllowed()
		}
	}

	res, err := fapi.nextStatus(ctx, order, driverLocation, pods, tripMtx)
	if err != nil {
		e, ok := err.(*api.Error)
		if ok && e.Code == errKTBPaymentErrorCode {
			fapi.getHalfHalfError(e)
			return nil, e
		}

		if ok && e.Code == errQRPaymentNotResolved {
			logrus.Errorf("[errorQRPaymentNotResolved] on order id [%s]", order.OrderID)
			if err := fapi.OrderService.SetPauseFlag(ctx, order.OrderID, headTo, model.PauseQRPayment); err != nil {
				logrus.Errorf("[setPauseFlagFailed] unable to set pause flag on order id [%s]: %v", order.OrderID, err)
			}
			fapi.getQRPaymentNotResolved(e)
			return nil, e
		}

		if ok && strings.Contains(e.Error(), "waiting for ready to pick") {
			fapi.getDarkstoreError(e)
			return nil, e
		}

		if errors.Is(err, domainModel.ErrDeliveringPhotoIsRequired) {
			return nil, ErrDeliveringPhotoIsRequired(err)
		}

		return nil, err
	}

	if fapi.Cfg.WaitForDbSyncDuration > 0 {
		time.Sleep(fapi.Cfg.WaitForDbSyncDuration)
	}

	return res, nil
}

func (fapi *FoodProviderImpl) getHalfHalfError(ex *api.Error) {
	if fapi.Cfg.AtomicOrderDBConfig != nil {
		cfg := fapi.Cfg.AtomicOrderDBConfig.Get()
		info := make(map[string]any)
		info["title"] = cfg.HalfHalfPaymentErrorTitle
		info["detail"] = cfg.HalfHalfPaymentErrorDetail
		ex.Info = info
	}
}

func (fapi *FoodProviderImpl) getQRPaymentNotResolved(ex *api.Error) {
	info := make(map[string]any)
	info["title"] = fapi.Cfg.QRPaymentNotResolvedErrTitle
	info["detail"] = fapi.Cfg.QRPaymentNotResolvedErrBody
	ex.Info = info
}

func (fapi *FoodProviderImpl) getDarkstoreError(ex *api.Error) {
	ex.Code = "STORE_CHECKOUT_ERROR"
	info := make(map[string]interface{})
	info["title"] = fapi.Cfg.DarkstoreCheckoutErrorTitle
	info["detail"] = fapi.Cfg.DarkstoreCheckoutErrorDescription
	ex.Info = info
}

func shouldVerifyPhoto(chance int) bool {
	chancePercent := float64(chance) / 100
	return 1-chancePercent < rand.Float64()
}

func (fapi *FoodProviderImpl) recordReviewDriverPhoto(verify bool, region string) {
	fapi.ReviewDriverPhotoMetric.Inc(strconv.FormatBool(verify), region)
}

func (fapi *FoodProviderImpl) calculateShouldVerify(ctx context.Context, order *domainModel.Order) error {
	for _, preventService := range fapi.Cfg.PreventVerifyDriverPhotoService {
		if order.ServiceType == preventService {
			return nil
		}
	}

	serviceArea, err := fapi.ServiceAreaRepo.GetByRegion(ctx, order.Region.String())
	if err != nil {
		logrus.Errorf("calculateShouldVerify: ServiceAreaRepo.GetByRegion err: %v", err.Error())
		return err
	}

	driver, err := fapi.DriverRepository.GetProfile(ctx, order.Driver)
	if err != nil {
		logrus.Errorf("calculateShouldVerify: DriverRepository.GetProfile err: %v", err.Error())
		return err
	}

	year, month, day := time.Now().In(timeutil.BangkokLocation()).Date()
	if driver.LatestRandomAt == nil {
		driver.LatestRandomAt = &time.Time{}
	}
	y, m, d := driver.LatestRandomAt.In(timeutil.BangkokLocation()).Date()
	recentlyDoRandom := year == y && month == m && day == d

	if !recentlyDoRandom {
		bkkTime := timeutil.BangkokNow()
		orderCfg := fapi.ProviderDeps.Cfg.AtomicOrderDBConfig.Get()
		isTierWhitelist := orderCfg.ForcePhotoVerifyDriverTiers.Has(string(driver.DriverTier))
		isDriverIDWhitelist := orderCfg.ForcePhotoVerifyDriverIDs.Has(driver.DriverID)
		isDriverShouldVerifyPhoto := (isTierWhitelist || isDriverIDWhitelist) || shouldVerifyPhoto(serviceArea.VerifyDriverPhotoRate)
		if isDriverShouldVerifyPhoto {
			order.ShouldVerify = true
			driver.LatestRandomAt = &bkkTime
			err = fapi.DriverRepository.Update(ctx, driver)
			if err != nil {
				logrus.Errorf("calculateShouldVerify: isDriverShouldVerifyPhoto DriverRepository.Update err: %v", err.Error())
				return err
			}
			err = fapi.OrderService.UpdateOrder(ctx, order)
			if err != nil {
				return err
			}
			fapi.recordReviewDriverPhoto(true, driver.Region.String())
		} else {
			driver.LatestRandomAt = &bkkTime
			err = fapi.DriverRepository.Update(ctx, driver)
			if err != nil {
				logrus.Errorf("calculateShouldVerify: DriverRepository.Update err: %v", err.Error())
				return err
			}
			fapi.recordReviewDriverPhoto(false, driver.Region.String())
		}
	}

	return nil
}

func (fapi *FoodProviderImpl) updateOrderProofOfDeliveryStatus(ctx context.Context, order *domainModel.Order, driverLocation *model.Location) error {
	headTo := order.RevampHeadTo()
	if order.ServiceType.IsRevampedStatus() {
		headTo = order.HeadTo
	}

	if headTo >= len(order.Routes) {
		logx.Error().Msgf("[updateOrderProofOfDeliveryStatus] headTo [%v] >= len(order.Route) [%v]", headTo, len(order.Routes))
		return nil
	}

	if !order.Routes[headTo].Pauses[model.PauseDeliveringPhoto] {
		return nil
	}

	if order.Routes[headTo].IsRain {
		// already checked rain status for this order so skip the process
		return nil
	}

	if driverLocation == nil {
		logx.Error().Msgf("unable to get driver's location [%s] of order [%s]", order.Driver, order.OrderID)
		return nil
	}

	if !fapi.RainSituationService.IsCheckProofOfDeliveryEnabled(ctx) {
		return nil
	}

	// Driver's region should be the same as Order's region
	isRaining := fapi.RainSituationService.IsRainingByRegionAndLocation(ctx, order.Region.String(), *driverLocation)
	if isRaining {
		delete(order.Routes[headTo].Pauses, model.PauseDeliveringPhoto)
		order.Routes[headTo].IsRain = true
	}
	return nil
}

func (fapi *FoodProviderImpl) changeTripByOrder(ctx context.Context, order domainModel.Order, tripMtx *sync.Mutex) (domainModel.Trip, error) {
	if tripMtx != nil {
		tripMtx.Lock()
		defer tripMtx.Unlock()
	}
	var lc domainModel.Location

	if len(order.Routes) > order.GetPayAtStop() {
		lc = order.Routes[order.GetPayAtStop()].Location
	}

	input := service.TripOrderEvent{
		TripID:              order.TripID,
		OrderID:             order.OrderID,
		OrderStatus:         order.Status,
		OrderLocation:       lc,
		OrderRevampedStatus: order.RevampedStatus,
		OrderHeadTo:         order.HeadTo,
	}

	trip, err := fapi.TripServices.OnOrderChanged(ctx, input)
	if err != nil {
		return domainModel.Trip{}, err
	}

	return trip, err
}

func (fapi *FoodProviderImpl) nextStatus(ctx context.Context, order *domainModel.Order, driverLocation *domainModel.Location, pods []lmdelivery.ProofOfDeliveryImage, tripMtx *sync.Mutex) (*UpdateStatusRes, error) {
	previousStatus := order.Status

	switch order.Status {
	case domainModel.StatusAssigningDriver, domainModel.StatusDriverMatched, domainModel.StatusDriverToRestaurant, domainModel.StatusDriverArrivedRestaurant,
		domainModel.StatusWaitingUserConfirmPrice, domainModel.StatusUserConfirmedPrice, domainModel.StatusDriverToDestination:

		backCompatRestaurantType(order)

		_ = transitionOrderStatus(order)

		if err := fapi.TripServices.SyncTripOrderStatus(ctx, order, pods); err != nil {
			// LMF-4460 added this check to handle food/mart service return error with `info.status` `WAITING_USER_RESOLVE_FOOD_FEE`
			// which food and mart will return error if delivery status (after transition) is `ARRIVED` and order status still `WAITING_USER_RESOLVE_FOOD_FEE`
			// Note: (Step 4) Incoming status: `DRIVER_TO_DESTINATION` --> transit to --> `ARRIVED`.
			if previousStatus == domainModel.StatusDriverToDestination && isFoodFeeNotResolveError(err) {
				return nil, ErrFoodFeeNotResolveInStep4()
			}
			logrus.Errorf("nextStatus: UpdateStatus err: %v", err.Error())
			return nil, err
		}

		if order.Status == domainModel.StatusDriverToDestination {
			fapi.updateOrderProofOfDeliveryStatus(ctx, order, driverLocation)
		}

		if err := fapi.OrderService.UpdateOrder(ctx, order); err != nil {
			logrus.Errorf("nextStatus: UpdateOrder err: %v", err.Error())
			return nil, err
		}

		if _, err := fapi.changeTripByOrder(ctx, *order, tripMtx); err != nil {
			logrus.Errorf("nextStatus: changeTripByOrder err: %v", err)
			return nil, err
		}

		if err := repApi.PublishRepEventOrderUpdated(fapi.Rep, order); err != nil {
			logrus.Errorf("nextStatus: PublishRepEventOrderUpdated err: %v", err.Error())
			return nil, err
		}

		if order.Status == domainModel.StatusDriverToDestination {
			if fapi.Cfg.EnableAddWithdrawalQuotaCashAdvance && (order.IsCashAdvance() || order.IsCashAdvancementEpayment()) {
				if err := fapi.DriverService.AddWithdrawalQuota(ctx, order.Driver); err != nil {
					logrus.Errorf("nextStatus: AddWithdrawalQuota err: %v", err.Error())
				}
			}

			if err := fapi.calculateShouldVerify(ctx, order); err != nil {
				logrus.Errorf("nextStatus: calculateShouldVerify err: %v", err.Error())
				return nil, err
			}

			trip, err := fapi.TripRepo.GetTripByTripID(ctx, order.TripID)
			if err != nil {
				logrus.Warnf("nextStatus cannot get trip to try to update leave_prev_stop_at for orderID: %s; error: %v", order.OrderID, err)
			} else {
				fapi.DriverService.TryUpdateLeavePrevStopAt(ctx, &trip)
			}
		}
	case domainModel.StatusRestaurantAccepted, domainModel.StatusReady:
		backCompatRestaurantType(order)
		_ = transitionOrderStatus(order)
		if err := fapi.OrderService.UpdateOrder(ctx, order); err != nil {
			return nil, err
		}

		if _, err := fapi.changeTripByOrder(ctx, *order, tripMtx); err != nil {
			logrus.Errorf("nextStatus: changeTripByOrder err: %v", err)
			return nil, err
		}
		trip, err := fapi.TripRepo.GetTripByTripID(ctx, order.TripID)
		if err != nil {
			logrus.Warnf("nextStatus cannot get trip to try to update leave_prev_stop_at for orderID: %s; error: %v", order.OrderID, err)
		} else {
			fapi.DriverService.TryUpdateLeavePrevStopAt(ctx, &trip)
		}

		if err := repApi.PublishRepEventOrderUpdated(fapi.Rep, order); err != nil {
			return nil, err
		}

	case domainModel.StatusDriveTo:
		backCompatRestaurantType(order)
		currentHeadTo := order.HeadTo
		_ = transitionOrderStatus(order)

		if err := fapi.TripServices.SyncTripOrderStatus(ctx, order, pods); err != nil {
			return nil, err
		}

		if err := fapi.OrderService.UpdateOrder(ctx, order); err != nil {
			return nil, err
		}

		if _, err := fapi.changeTripByOrder(ctx, *order, tripMtx); err != nil {
			logrus.Errorf("nextStatus: changeTripByOrder err: %v", err)
			return nil, err
		}

		if err := repApi.PublishRepEventOrderUpdated(fapi.Rep, order); err != nil {
			return nil, err
		}
		if currentHeadTo == 1 {
			err := fapi.calculateShouldVerify(ctx, order)
			if err != nil {
				return nil, err
			}
		}
	case domainModel.StatusDriverArrived:
		backCompatRestaurantType(order)
		if driverLocation != nil && order.IsHalfHalf() && fapi.IsDriverTooFarToCompleteHalfHalfOrder(order, driverLocation) {
			return nil, ErrCompleteHalfHalfOrderTooFar()
		}
		_ = transitionOrderStatus(order)

		if err := fapi.TripServices.SyncTripOrderStatus(ctx, order, pods); err != nil {
			if isFoodFeeNotResolveError(err) {
				return nil, ErrFoodFeeNotResolve()
			}
			return nil, err
		}

		return fapi.nextStatus(ctx, order, driverLocation, nil, tripMtx)
	case domainModel.StatusArrivedAt:
		backCompatRestaurantType(order)
		currentHeadTo := order.HeadTo
		if currentHeadTo == 1 && driverLocation != nil && order.IsHalfHalf() && fapi.IsDriverTooFarToCompleteHalfHalfOrder(order, driverLocation) {
			return nil, ErrCompleteHalfHalfOrderTooFar()
		}

		_ = transitionOrderStatus(order)
		if err := fapi.TripServices.SyncTripOrderStatus(ctx, order, pods); err != nil {
			if isFoodFeeNotResolveError(err) {
				return nil, ErrFoodFeeNotResolve()
			}
			return nil, err
		}

		if currentHeadTo != len(order.Routes)-1 {
			if order.Status == domainModel.StatusDriveTo && order.ServiceType != domainModel.ServiceBike {
				var isCheckRainStatusForPoD bool
				switch currentHeadTo {
				case 0:
					isCheckRainStatusForPoD = order.Routes[0].DeliveryStatus == domainModel.DeliveryStatusPickupDone &&
						order.Routes[1].DeliveryStatus == domainModel.DeliveryStatusOnTheWay
				case 1:
					isCheckRainStatusForPoD = order.Routes[1].DeliveryStatus == domainModel.DeliveryStatusDropOffDone &&
						order.Routes[2].DeliveryStatus == domainModel.DeliveryStatusOnTheWay
				}
				if isCheckRainStatusForPoD {
					fapi.updateOrderProofOfDeliveryStatus(ctx, order, driverLocation)
				}
			}

			if err := fapi.OrderService.UpdateOrder(ctx, order); err != nil {
				return nil, err
			}

			if _, err := fapi.changeTripByOrder(ctx, *order, tripMtx); err != nil {
				logrus.Errorf("nextStatus: changeTripByOrder err: %v", err)
				return nil, err
			}

			if err := repApi.PublishRepEventOrderUpdated(fapi.Rep, order); err != nil {
				return nil, err
			}
		} else if currentHeadTo == len(order.Routes)-1 {
			return fapi.nextStatus(ctx, order, driverLocation, nil, tripMtx)
		}
	case domainModel.StatusDropOffDone:
		res, completedOrder, err := fapi.completeOrder(ctx, order, true, fapi.StatusTransitioner(), driverLocation, pods)
		if err != nil {
			logrus.Errorf("nextStatus: completeOrder err: %v", err)
			return nil, err
		}

		if _, err := fapi.changeTripByOrder(ctx, *completedOrder.order, tripMtx); err != nil {
			logrus.Errorf("nextStatus: changeTripByOrder err: %v", err)
			return nil, err
		}

		return res, nil
	case domainModel.StatusOnGoing:
		if allDropOffDone(order) {
			res, _, err := fapi.completeOrder(ctx, order, true, fapi.StatusTransitioner(), driverLocation, pods)
			if err != nil {
				return nil, err
			}
			return res, nil
		}
		orderBeforeTransition := *order
		_ = transitionOrderStatus(order)

		// Note: always updateStatus on stop which is first stop index of transitionedStatus
		if transitionedStopIndex := getFirstTransitionedStopIndex(orderBeforeTransition.Routes, order.Routes); transitionedStopIndex != -1 {
			if err := UpdateStatus(ctx, fapi.Delivery, order, lmdelivery.DeliveryStatus{
				OrderID:                            order.OrderID,
				StopIndex:                          transitionedStopIndex,
				Status:                             string(order.Routes[transitionedStopIndex].DeliveryStatus),
				PodImages:                          pods,
				IsSkipIncompleteQRPromptpayPayment: order.IsSkipIncompleteQRPromptpayPayment,
			}); err != nil {
				return nil, err
			}
		}

		if err := fapi.OrderService.UpdateOrder(ctx, order); err != nil {
			return nil, err
		}

		payloadOrder := repApi.ToPayloadOrder(order)
		if err := fapi.Rep.Publish(rep.EventOrderUpdated, &payloadOrder); err != nil {
			return nil, err
		}
	default:

		return nil, ErrOrderStatusNotAllowed()
	}

	if order.Status == domainModel.StatusDriverToDestination {
		if err := fapi.CookingTimeDelayRepo.RemoveOrder(ctx, order.OrderID); err != nil {
			logrus.Errorf("couldn't remove order's delay-checking timer error: %v orderID: %v", err, order.OrderID)
		}
		if order.IsLockRequired() {
			if err := fapi.DriverRepository.UnlockForQueueing(ctx, order.Driver); err != nil {
				logrus.Errorf("unable to unlock driver %v for qeueuing, err=%v", order.Driver, err)
			}
		}
	}

	return nil, nil
}

// Due to the race condition that often arises when driver completes the order, a transaction is needed here.
// completeOrder is divided into a total of 3 sections where section 1 and 2 are part of the transaction.
//
// Section 1 is the section where db operations are executed. If there are any transient transaction errors,
// the txn helper will automatically abort the transaction and retry the whole transaction.
//
// Section 2 is the section where external service is called. The reason why it is put in the last part of the transaction
// is because the transaction should be aborted (no retry) if there is an error returned from calling the external service to
// update status of the order.
//
// Section 3 is the section where other external services are called but are not important. If any operations fail, it
// will just log the error and continue with the operation. This section MUST NOT contain any DB operations.

type CompleteVal struct {
	transferAmount types.Money
	outstanding    types.Money
	order          *domainModel.Order
	driv           *domainModel.Driver
}

func (fapi *FoodProviderImpl) completeOrder(ctx context.Context, order *domainModel.Order, syncDelivery bool, transitioner StatusTransitioner, driverLocation *domainModel.Location, pods []lmdelivery.ProofOfDeliveryImage) (*UpdateStatusRes, *CompleteVal, error) {
	if order.TripID != "" {
		res, completeVal, err := fapi.completeOrderInTrip(ctx, order, syncDelivery, transitioner, driverLocation, pods)
		if err != nil {
			switch err {
			case domainModel.ErrTransitStatus:
				return nil, &CompleteVal{}, ErrOrderStatusNotAllowed()
			default:
				return nil, &CompleteVal{}, err
			}
		}
		if fapi.featureflagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsPublishMissionLogOrderCompletedEnabled.Name) {
			fapi.MissionLogEventService.PublishMissionLogOrderEvent(ctx, order)
		}

		if order.IsDeferred {
			fapi.DeferredOrderCompletedCountMetric.Inc(order.DistributeRegions.DefaultRegion().String())
		}
		return res, completeVal, nil
	}

	// NOTE DEPRECATED, most order should already have TripID
	val, err := fapi.txnHelper.WithTxn(ctx, func(ctx context.Context) (interface{}, error) {
		// SECTION 1
		o := *order
		cOrder := &o
		driverTrans, err := fapi.DriverTransactionService.GetDriverTransaction(ctx, cOrder.Driver, repository.WithReadPrimary)
		if err != nil {
			return CompleteVal{
				transferAmount: 0,
				outstanding:    0,
				order:          cOrder,
			}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, fmt.Errorf("GetDriverTransaction: %v", err))
		}

		// TODO refactor this after removing agent model
		_, transferAmount, outstanding := domainModel.ValidateDriverBalanceEnough(driverTrans, cOrder)

		var txnInfos []domainModel.TransactionInfo
		txnInfos, rewardTxns, err := cOrder.Complete(driverTrans, transitioner)
		if err != nil {
			switch err {
			case domainModel.ErrTransitStatus:
				return CompleteVal{
					transferAmount: 0,
					outstanding:    0,
					order:          cOrder,
				}, ErrOrderStatusNotAllowed()
			default:
				return CompleteVal{
					transferAmount: 0,
					outstanding:    0,
					order:          cOrder,
				}, err
			}
		}

		if len(rewardTxns) > 0 {
			err = fapi.RewardTransactionProcessor.ProcessRewardTransaction(ctx, order.OrderID, service.ToRewardTransactionBuilder(rewardTxns))
			if err != nil {
				return nil, ErrServiceInternal(fmt.Sprintf("cannot process reward txn to driver %s: %v", order.Driver, err))
			}
		}

		_, _, err = fapi.DriverTransactionService.AddDriverTransaction(
			ctx,
			driverTrans,
			domainModel.SystemTransactionChannel,
			domainModel.CommissionDeductionTransactionAction,
			domainModel.SuccessTransactionStatus,
			txnInfos,
		)
		if err != nil {
			return CompleteVal{
				transferAmount: 0,
				outstanding:    0,
				order:          cOrder,
			}, err
		}
		if err := fapi.OrderService.UpdateAndUpsertRevision(ctx, cOrder); err != nil {
			return nil, err
		}

		driv, err := fapi.DriverRepository.GetProfile(ctx, cOrder.Driver)
		if err != nil {
			logrus.WithFields(logrus.Fields{"error": err.Error()}).Error("updatestatus: GetProfile, driver not found")
			return nil, err
		}

		earning, err := fapi.DriverRepository.GetTodayEarning(ctx, cOrder.Driver)
		if err != nil {
			return nil, ErrServiceInternal(fmt.Sprintf("cannot get today earning for driver id (%s): %v", order.Driver, err))
		}

		priceSummary := order.PriceSummary()
		df, cms, wht := CalculateNewEarning(priceSummary)
		newEarning := domainModel.NewDriverEarning(
			order.Driver,
			types.NewMoney(df).Add(earning.Fee),
			types.NewMoney(cms).Add(earning.Commission),
			types.NewMoney(wht).Add(earning.Tax),
			earning.Tip,
		)
		if err := fapi.DriverRepository.SetTodayEarning(ctx, *newEarning); err != nil {
			logrus.WithField("method", "UpdateOrderStatus").
				Errorf("cannot driver earning for order id (%s) and driver id (%s): %v", order.OrderID, order.Driver, err)
			return nil, err
		}

		if err := fapi.DriverService.AddCompletedOrderQuota(ctx, driv); err != nil {
			logrus.Errorf("cannot add completed quota on order %s from driver %s :: %v", order.OrderID, order.Driver, err.Error())
			return nil, err
		}
		if err := fapi.DriverRepository.SetCompletedOrderTime(ctx, driv.DriverID); err != nil {
			logrus.Errorf("cannot update completed order time for driver %s :: %v", order.Driver, err.Error())
		}
		if order.OrderID == driv.CurrentOrder {
			if len(driv.QueueingOrders) == 0 {
				driv.CompleteTrip()
				if driv.BanLater {
					err := fapi.BanService.BanAndSaveHistory(ctx, driv, domainModel.NewBanInfo(driv.Reason, driv.BannedUntil))
					if err != nil {
						if err == repository.ErrDriverNotFound {
							return nil, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, repository.ErrDriverNotFound)
						}

						return nil, err
					}
				} else if driv.TemporaryBanHistory != nil {
					if err := fapi.BanService.BanWithHistory(ctx, driv, driv.TemporaryBanHistory); err != nil {
						return nil, err
					}
				} else {
					driv, err = fapi.DriverService.UnassignOrder(ctx, cOrder.Driver)
					if err != nil {
						logrus.Errorf("cannot unassigned order %s from driver %s :: %v", order.OrderID, order.Driver, err.Error())
						return nil, err
					}
					if driv.OfflineLater {
						if err := fapi.DriverService.OfflineDriver(ctx, driv); err != nil {
							logrus.Errorf("cannot offline driver after order complete (offline-later). orderID: %s, driverID: %s, error: %v", order.OrderID, driv.DriverID, err)
						}
					}
					now := time.Now()
					payloadDriver := repApi.ToRepPayload(driv.DriverID, string(driv.Status), string(driv.Region), &now)
					if err := fapi.Rep.Publish(rep.EventDriverUpdateStatus, &payloadDriver); err != nil {
						logrus.Errorf("cannot publish to rep (event driver update status) for driver %s, err: %v", order.Driver, err)
					}
				}
			} else {
				if len(driv.QueueingTrips) == 0 {
					if err := fapi.DriverService.DequeueOrder(ctx, *driv); err != nil {
						return nil, err
					}
				} else {
					if err := fapi.DriverService.DequeueTrip(ctx, *driv); err != nil {
						return nil, err
					}
				}
			}
		} else if driv.IsOrderInQueue(order.OrderID) { // order is in queue
			if err := fapi.DriverService.UnassignQueueOrder(ctx, *driv, order.OrderID); err != nil {
				return nil, err
			}
		}

		// SECTION 2
		if syncDelivery {
			if err := fapi.doSyncDeliveryCompletedTrip(ctx, cOrder, pods); err != nil {
				return nil, err
			}
		}

		return CompleteVal{
			transferAmount: transferAmount,
			outstanding:    outstanding,
			order:          cOrder,
			driv:           driv,
		}, nil
	}, transaction.WithLabel("FoodProviderImpl.completeOrder"))
	if err != nil {
		return nil, &CompleteVal{}, err
	}

	retVal := val.(CompleteVal)
	order = retVal.order
	driv := retVal.driv

	// SECTION 3
	if !driv.IsTester() {
		goRoutineCtx := safe.NewContextWithSameWaitGroup(ctx)
		func(ctx context.Context, fapi *FoodProviderImpl, driv *domainModel.Driver, order *domainModel.Order) {
			safe.GoFuncWithCtx(ctx, func() {
				BanWithdraw(ctx, driv, order, fapi.FraudAdvisor, fapi.OrderService, fapi.DriverTransactionRepo, fapi.Banhistsvc)
			})
		}(goRoutineCtx, fapi, driv, order)
	}

	if driv.DriverUOBRefID() == "" {
		goRoutineCtx := safe.NewContextWithSameWaitGroup(ctx)
		safe.GoFuncWithCtx(goRoutineCtx, func() {
			if err := fapi.DriverService.AssignUobRefToDriver(ctx, driv); err != nil {
				logrus.Warnf("completeOrder, cannot assign uobRefID to driver %s", driv.DriverID)
			}
			return
		})
	}

	if err := UpdateDoneStatistic(order.OrderID, order.Driver, fapi.TaskExecutor, fapi.StatisticService); err != nil {
		logrus.Errorf("cannot update done statistic for driver %s, err: %v", order.Driver, err)
	}

	if err := repApi.PublishRepEventOrderCompleted(fapi.Rep, order); err != nil {
		return nil, &CompleteVal{}, err
	}

	if driverLocation == nil {
		logrus.Warning("cannot get driver location when complete order")
	}

	PublishEventOrderComplete(ctx, order, driverLocation, fapi.Bus)

	if err := fapi.serviceOptInReminder.TryToMarkForReminder(ctx, driv); err != nil {
		safe.SentryErrorMessage("TryToMarkForReminder fails",
			safe.WithError(err), safe.WithDriverID(driv.DriverID))
	}

	if order.IsDeferred {
		fapi.DeferredOrderCompletedCountMetric.Inc(order.DistributeRegions.DefaultRegion().String())
	}

	return NewUpdateStatusResponse(order.Status, retVal.transferAmount, retVal.outstanding), &retVal, nil
}

type CompletedInfo struct {
	order  *domainModel.Order
	driver *domainModel.Driver
}

func (fapi *FoodProviderImpl) doSyncDeliveryCompletedTrip(ctx context.Context, order *domainModel.Order, pods []lmdelivery.ProofOfDeliveryImage) error {
	discounts := []lmdelivery.DiscountRes{}
	for _, d := range append(order.PriceSummary().DeliveryFee.Discounts, order.PriceSummary().ItemFee.Discounts...) {
		discounts = append(discounts, lmdelivery.DiscountRes{
			DiscountPrice: d.Discount,
			Code:          d.Code,
			Type:          string(d.Type),
			Category:      d.Category,
		})
	}

	return UpdateStatus(ctx, fapi.Delivery, order, lmdelivery.DeliveryStatus{
		OrderID:   order.OrderID,
		Status:    string(domainModel.StatusCompleted),
		PodImages: pods,
		TripSummary: &lmdelivery.TripSummary{
			Duration: order.GetTotalDurationInSeconds(),
			TripPriceSummary: lmdelivery.TripPriceSummary{
				Total:           order.PriceSummary().Total,
				StartingFee:     order.PriceSummary().DeliveryFee.StartingFee,
				DistanceUnitFee: order.PriceSummary().DeliveryFee.DistanceUnitFee,
				Discounts:       discounts,
			},
		},
		IsSkipIncompleteQRPromptpayPayment: order.IsSkipIncompleteQRPromptpayPayment,
	})
}

func (fapi *FoodProviderImpl) completeOrderInTrip(ctx context.Context, order *domainModel.Order, syncDelivery bool, transitioner StatusTransitioner, driverLocation *domainModel.Location, pods []lmdelivery.ProofOfDeliveryImage) (*UpdateStatusRes, *CompleteVal, error) {
	syncOutside := fapi.Cfg.AtomicOrderDBConfig.Get().EnableSyncDeliveryOutsideTransactionWhenCompleteOrderInTrip

	o := *order
	syncOrder := &o

	if err := transitioner(syncOrder, domainModel.StatusCompleted); err != nil {
		return nil, nil, domainModel.ErrTransitStatus
	}

	if syncDelivery && syncOutside {
		if err := fapi.doSyncDeliveryCompletedTrip(ctx, syncOrder, pods); err != nil {
			return nil, nil, err
		}
	}

	res, err := fapi.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		if err := fapi.OrderService.UpdateAndUpsertRevision(sessCtx, syncOrder); err != nil {
			return nil, err
		}

		driv, err := fapi.DriverRepository.GetProfile(sessCtx, syncOrder.Driver)
		if err != nil {
			logrus.WithFields(logrus.Fields{"error": err.Error()}).Error("updatestatus: GetProfile, driver not found")
			return nil, err
		}

		if err := fapi.DriverService.AddCompletedOrderQuota(sessCtx, driv); err != nil {
			logrus.Errorf("cannot add completed quota on order %s from driver %s :: %v", order.OrderID, order.Driver, err.Error())
			return nil, err
		}
		if err := fapi.DriverRepository.SetCompletedOrderTime(sessCtx, driv.DriverID); err != nil {
			logrus.Errorf("cannot update completed order time for driver %s :: %v", order.Driver, err.Error())
		}

		if syncDelivery && !syncOutside {
			if err := fapi.doSyncDeliveryCompletedTrip(sessCtx, syncOrder, pods); err != nil {
				return nil, err
			}
		}

		return CompletedInfo{
			order:  syncOrder,
			driver: driv,
		}, nil
	}, transaction.WithLabel("FoodProviderImpl.completeOrderInTrip"))
	if err != nil {
		if syncDelivery && syncOutside {
			logx.Error().Err(err).Msgf("cannot complete order but already sync delivery, driverID=%v, orderID=%v", order.Driver, order.OrderID)
		}
		return nil, nil, err
	}

	resVal, ok := res.(CompletedInfo)
	if !ok {
		return nil, nil, errors.New("error casting to completedInfo")
	}
	cOrder := resVal.order
	driv := resVal.driver

	if !driv.IsTester() {
		func(ctx context.Context, fapi *FoodProviderImpl, driv *domainModel.Driver, order *domainModel.Order) {
			safe.GoFunc(func() {
				BanWithdraw(ctx, driv, order, fapi.FraudAdvisor, fapi.OrderService, fapi.DriverTransactionRepo, fapi.Banhistsvc)
			})
		}(context.Background(), fapi, driv, cOrder)
	}

	if driv.DriverUOBRefID() == "" {
		// It was using `ginContext.Copy()` to copy out current context and use only for goroutine
		ctx := safe.NewContextWithSameWaitGroup(ctx)
		safe.GoFuncWithCtx(ctx, func() {
			if err := fapi.DriverService.AssignUobRefToDriver(ctx, driv); err != nil {
				logrus.Warnf("completeOrder, cannot assign uobRefID to driver %s", driv.DriverID)
			}
		})
	}

	if err := UpdateDoneStatistic(order.OrderID, order.Driver, fapi.TaskExecutor, fapi.StatisticService); err != nil {
		logrus.Errorf("cannot update done statistic for driver %s, err: %v", order.Driver, err)
	}

	if err := repApi.PublishRepEventOrderCompleted(fapi.Rep, order); err != nil {
		return nil, &CompleteVal{
			order: cOrder,
		}, err
	}

	if driverLocation == nil {
		logrus.Warning("cannot get driver location when complete order")
	}

	PublishEventOrderComplete(ctx, cOrder, driverLocation, fapi.Bus)

	return NewUpdateStatusResponse(cOrder.Status, 0, 0), &CompleteVal{
		order: cOrder,
	}, nil
}

func (fapi *FoodProviderImpl) IsDriverTooFarToCompleteHalfHalfOrder(order *domainModel.Order, driverLocation *domainModel.Location) bool {
	halfHalfCfg := fapi.Cfg.AtomicOrderDBConfig.Get()

	if !order.IsHalfHalf() || halfHalfCfg.AllowCompleteOrderTooFar || order.IgnoreDriverTooFarValidation {
		return false
	}

	destinationLocation := order.Routes[order.GetPayAtStop()].Location
	distance := geo.DistanceInKm(driverLocation.Lat, driverLocation.Lng, destinationLocation.Lat, destinationLocation.Lng)
	return distance > halfHalfCfg.CompleteOrderDistanceInKm
}

func GetDriverLocation(ctx *gin.Context) *domainModel.Location {
	driverLocation := &domainModel.Location{}
	if err := ctx.ShouldBindJSON(driverLocation); err != nil {
		logrus.Errorf("bind json driver location err: %v", err)
		return nil
	}
	return driverLocation
}

func CalculateNewEarning(priceSummary domainModel.PriceSummary) (float64, float64, float64) {
	df := priceSummary.DeliveryFee.SubTotal + priceSummary.DeliveryFee.OnTopFare
	cms := priceSummary.DeliveryFee.Commission + priceSummary.DeliveryFee.OnTopCommissionFare
	wht := priceSummary.DeliveryFee.WithholdingTax + priceSummary.DeliveryFee.OnTopWithholdingTax
	return df, cms, wht
}

func (fapi *FoodProviderImpl) AcceptOrderHandler(ctx *gin.Context, order *domainModel.Order, orderID, driverID string, deviceID string) {
	backCompatRestaurantType(order)
	acceptedOrderInfo, err := fapi.Acceptor.AcceptOrder(ctx, order, orderID, driverID, true, UpdateOrderState, TestCaptchaFromBody(ctx), deviceID)
	if err != nil {
		_ = ctx.Error(err)
		return
	}
	apiutil.OK(
		ctx,
		NewAcceptOrderRes(
			*acceptedOrderInfo.Order,
			acceptedOrderInfo.MoneyTransferWallet,
		),
	)
}

func (fapi *FoodProviderImpl) AcceptAssignmentHandler(gCtx *gin.Context, assignmentID model.AssignmentID, driverID string, deviceID string) {
	// TODO: Check and remove WithoutCancel if it's not needed
	ctx := context.WithoutCancel(gCtx.Request.Context())
	result, err := fapi.Acceptor.AcceptAssignment(ctx, assignmentID, driverID, true, UpdateOrderState, TestCaptchaFromBody(gCtx), deviceID)
	if err != nil {
		_ = gCtx.Error(err)
		return
	}

	drv, err := fapi.DriverRepository.FindDriverID(ctx, driverID, repository.WithReadSecondaryPreferred)
	if err != nil {
		_ = gCtx.Error(err)
		return
	}

	apiutil.OK(
		gCtx,
		AcceptAssignmentRes{
			TripID:         result.TripID,
			PartialSuccess: len(result.UnacceptedOrderIDs) > 0,
			DriverType:     domainModel.DriverType(drv.DriverType.String()),
		},
	)
}

func UpdateOrderState(order *domainModel.Order) error {
	backCompatRestaurantType(order)
	if len(order.Routes) != 0 && (order.Autostart || order.ServiceType.IsRevampedStatus()) {
		_ = transitionOrderStatus(order)
	}
	if order.ServiceType.IsRevampedStatus() && order.RevampedStatus {
		_ = transitionOrderStatus(order)
	}

	return nil
}

func isFoodFeeNotResolveError(err error) bool {
	if terr, ok := err.(*api.Error); ok && terr.Code == api.ERRCODE_INVALID_REQUEST {
		if s, ok := terr.Info["status"]; ok && s == "WAITING_USER_RESOLVE_FOOD_FEE" {
			return true
		}
	}

	return false
}

func (fapi *FoodProviderImpl) CheckOrderStatusBeforeAdminCompleteOrder(order *domainModel.Order) error {
	if order.Status == domainModel.StatusCanceled ||
		order.Status == domainModel.StatusExpired ||
		order.Status == domainModel.StatusCompleted {
		return apiutil.NewFromString(api.ERRCODE_INVALID_REQUEST, "ออเดอร์สิ้นสุดไปแล้ว")
	}
	return nil
}

func (fapi *FoodProviderImpl) PerformAdminCompleteOrder(ctx *gin.Context, order *domainModel.Order, allowComplete bool, userProcessPayment bool, comment string, admin string, remarks ...string) error {
	if !allowComplete {
		if err := fapi.Delivery.CSCompleteOrder(ctx, v1.CSCompleteOrderRequest{
			OrderID:        order.OrderID,
			ProcessPayment: userProcessPayment,
			Comment:        fmt.Sprintf("[จบงานโดย Rider Admin] %s", comment),
			CreatedBy:      admin,
		}); err != nil {
			logrus.Warnf("CSCompleteOrder: error complete order %v", err)

			if ape, ok := err.(*api.Error); ok {
				e := apiutil.NewFromString(api.ERRCODE_INVALID_REQUEST, fapi.Cfg.ForceCompleteErrorMessage)
				e.Info["message"] = ape.Code
				return e
			} else {
				return apiutil.NewFromString(api.ERRCODE_INTERNAL_ERROR, fapi.Cfg.ForceCompleteErrorMessage)
			}
		}
	}
	order.AddRemarks(fmt.Sprintf("จบ Order นี้ จากระบบ Rider Admin โดย %s", admin), fmt.Sprintf("[จบงานโดย Rider Admin] %s", comment))
	order.AddRemarks(remarks...)

	_, completedOrder, err := fapi.completeOrder(ctx, order, false, fapi.AdminStatusTransitioner(), nil, nil)
	if err != nil {
		return err
	}

	_, err = fapi.TripServices.OnForceCompletedOrder(ctx, service.TripOrderForceCompletedEvent{
		TripID:              completedOrder.order.TripID,
		OrderID:             completedOrder.order.OrderID,
		OrderStatus:         completedOrder.order.Status,
		OrderHeadTo:         completedOrder.order.HeadTo,
		OrderRevampedStatus: completedOrder.order.RevampedStatus,
	})
	if err != nil {
		return err
	}

	return nil
}

func (fapi *FoodProviderImpl) AdminCompleteOrder(ctx *gin.Context, order *domainModel.Order, orderID string, adminEmail string) {
	var req AdminCompleteOrderReq
	if err := ctx.ShouldBindJSON(&req); err != nil {
		_ = ctx.Error(err)
		return
	}

	if err := fapi.CheckOrderStatusBeforeAdminCompleteOrder(order); err != nil {
		_ = ctx.Error(err)
		return
	}

	remarks := []string{}
	if req.AllowComplete {
		remarks = append(remarks, RemarkCompleteWithoutSyncDelivery)
	}

	if err := fapi.PerformAdminCompleteOrder(ctx, order, req.AllowComplete, req.UserProcessPayment, req.Comment, adminEmail, remarks...); err != nil {
		_ = ctx.Error(err)
		return
	}

	apiutil.OK(ctx, gin.H{})
}

// CronCompleteOrder was used by cron job force completed
func (fapi *FoodProviderImpl) CronCompleteOrder(ctx context.Context, order *domainModel.Order) error {
	tripID := order.TripID
	if tripID == "" {
		// backward compatibility, just go complete only order.
		_, _, err := fapi.completeOrder(ctx, order, false, fapi.AdminStatusTransitioner(), nil, nil)
		if err != nil {
			return err
		}
		return nil
	}

	trip, err := fapi.TripServices.GetTripByID(ctx, tripID)
	if err != nil {
		return fmt.Errorf("[cron force complete order] trip svc unable to get trip by id: %v", err)
	}

	_, completedOrder, err := fapi.completeOrder(ctx, order, false, fapi.AdminStatusTransitioner(), nil, nil)
	if err != nil {
		return err
	}

	if trip.Status == domainModel.TripStatusCompleted {
		logrus.Warnf("[cron force complete order] trip already completed, so ignore calling to trip. OnForceCompletedOrder. trip id: %v", trip.TripID)
		return nil
	}

	trip, err = fapi.TripServices.OnForceCompletedOrder(ctx, service.TripOrderForceCompletedEvent{
		TripID:              completedOrder.order.TripID,
		OrderID:             completedOrder.order.OrderID,
		OrderStatus:         completedOrder.order.Status,
		OrderHeadTo:         completedOrder.order.HeadTo,
		OrderRevampedStatus: completedOrder.order.RevampedStatus,
	})
	if err != nil {
		return fmt.Errorf("[cron force complete order] trip svc unable to react on force complete order: %v", err)
	}

	return nil
}

func allDropOffDone(o *domainModel.Order) bool {
	return o.Routes[len(o.Routes)-1].DeliveryStatus == domainModel.DeliveryStatusDropOffDone
}

func getFirstTransitionedStopIndex(beforeRoutes, afterRoutes []domainModel.Stop) int {
	for i, br := range beforeRoutes {
		if afterRoutes[i].DeliveryStatus != br.DeliveryStatus {
			return i
		}
	}
	return -1
}
