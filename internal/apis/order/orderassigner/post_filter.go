package orderassigner

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/sets"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type AssignmentType string

const (
	OnlineAssignmentType AssignmentType = "Online"
	MOAssignmentType     AssignmentType = "MO"
	AnyAssignmentType    AssignmentType = ""
)

type DistanceLimitSetting map[model.Service]float64

func (dls DistanceLimitSetting) ServiceDistanceLimit(s model.Service) (float64, bool) {
	if limit, found := dls[s]; found {
		return limit, true
	}

	return 0, false
}

// AssignmentConstraint rider constraint setting, to do post validation of candidate rider before assign orders to the rider
// mostly use for backward compatibility supporting during breakdown fleet microservice.
// TODO[LMF-16135] after breaking down each constraint, should be revisited and try to remove
type AssignmentConstraint struct {
	AssignmentType           AssignmentType
	AssignmentLogOpt         *model.AssignmentLogOpt
	IsUsingPredictionService bool
	LastPredictionDisruption time.Time
	DistributionLogMetadata  model.DistributionLogMetadata // use to log event - post filter e.g. batch/single

	// --- config to create route setting ---
	FirstDistanceSetting DistanceLimitSetting
	B2BDistanceSetting   DistanceLimitSetting
	// --- end ---

	IsMPMaxLoad3               func() bool
	DriverOptedOutServicesSet  func(ctx context.Context, driver *model.DriverMinimal) sets.Of[model.Service]
	CheckIfRiderEligibleForMp1 func(ctx context.Context, driver *model.DriverMinimal, order *model.Order) (bool, model.RiderFilterName, string)
	GetNotifiedDrivers         func(orderID string) sets.Of[string]
}

func CheckRequirement(_ context.Context, driver *model.DriverMinimal, newOrderIDs []string, constraint *AssignmentConstraint) (passed bool, filterName model.RiderFilterName, description string) {
	if constraint.AssignmentType == OnlineAssignmentType && driver.Status != model.StatusOnline {
		return false, model.RiderStatusUnmatchedWithAssignmentTypeOnline, fmt.Sprintf("the driver's status: %s is inappropriate with assignment type: %s", driver.Status, constraint.AssignmentType)
	}

	if constraint.AssignmentType == MOAssignmentType && driver.Status != model.StatusAssigned {
		return false, model.RiderStatusUnmatchedWithAssignmentTypeMO, fmt.Sprintf("the driver's status: %s is inappropriate with assignment type: %s", driver.Status, constraint.AssignmentType)
	}

	if constraint.AssignmentLogOpt.IsRushOrder && driver.Status != model.StatusOnline {
		return false, model.RiderNotOnlineWhenAssigningRush, "trying to assign rush order but the driver's isn't online"
	}

	if constraint.AssignmentLogOpt.LockDuration > 0 && (!constraint.IsMPMaxLoad3() && driver.Status != model.StatusOnline) {
		return false, model.RiderNotOnlineWhenAssigningMp1, fmt.Sprintf("the driver's status is inappropriate for locking (status=%s)", driver.Status)
	}

	if !constraint.AssignmentLogOpt.IsMultiplePickup &&
		driver.Status != model.StatusOnline &&
		!(constraint.IsUsingPredictionService && driver.Status == model.StatusAssigned) {
		return false, model.RiderNotOnline, fmt.Sprintf("the driver's status is inappropriate (status=%s)", driver.Status)
	}

	if constraint.AssignmentLogOpt.IsMultiplePickup && driver.Status != model.StatusAssigned {
		return false, model.RiderNotAssignedWhenAssigningMp2, fmt.Sprintf("the driver's status is inappropriate for multiple pickup (status=%s)", driver.Status)
	}

	if driver.IsAcknowledgementRequired {
		if driver.Status == model.StatusAssigned {
			return false, model.RiderLockedForAcknowledgement, "the driver must acknowledge before getting distributed with new orders"
		} else {
			safe.SentryErrorMessage("while distributing, found rider marked acknowledgement-required but status isn't ASSIGNED. will distribute anyway", safe.WithDriverID(driver.DriverID), safe.WithOrderIDs(newOrderIDs))
		}
	}

	if driver.OfflineLater {
		if driver.Status == model.StatusAssigned {
			return false, model.OfflineLater, "the driver must be offline later but the status is assigned"
		} else {
			safe.SentryErrorMessage("while distributing, found rider marked offline-later but status isn't ASSIGNED. will distribute anyway", safe.WithDriverID(driver.DriverID), safe.WithOrderIDs(newOrderIDs))
		}
	}

	if driver.LastPredictionDisruption != constraint.LastPredictionDisruption {
		return false, model.LastPredictionDisruptionChanged, "the driver last prediction disruption has been changed"
	}

	if !constraint.AssignmentLogOpt.IsMultiplePickup && driver.IsLockedFromQueueing() {
		if driver.Status == model.StatusAssigned {
			return false, model.RiderLockedFromQueueing, "the driver is being locked for queueing"
		} else {
			safe.SentryErrorMessage("while distributing, found rider locked for queueing but status isn't ASSIGNED. will distribute anyway", safe.WithDriverID(driver.DriverID), safe.WithOrderIDs(newOrderIDs))
		}
	}

	if constraint.AssignmentLogOpt.IsMultiplePickup && !driver.IsLockedFromQueueing() {
		if len(newOrderIDs) > 0 {
			logx.Info().Str(logutil.OrderID, newOrderIDs[0]).Str(logutil.DriverID, driver.DriverID).Msgf("trying to assign MP order %s to unlocked rider %s", newOrderIDs[0], driver.DriverID)
		}
	}

	// lookup notified set from assignment log quite expensive, move to last step might reduce usage.
	// TODO[LMF-16139] logic check notified set should be move to fleet-distribution as they known which driver was assigned
	if constraint.GetNotifiedDrivers != nil && constraint.GetNotifiedDrivers(newOrderIDs[0]).Has(driver.DriverID) {
		return false, model.WithoutNotifiedRiders, "the driver already got assigned the order"
	}

	return true, "", ""
}

func CheckOrderAndRiderRequirement(ctx context.Context, driver *model.DriverMinimal, newOrders []*model.Order, constraint *AssignmentConstraint) (passed bool, filterName model.RiderFilterName, description string) {
	serviceOptOut := constraint.DriverOptedOutServicesSet(ctx, driver)
	for _, freshOrder := range newOrders {
		if freshOrder != nil && serviceOptOut.Has(freshOrder.ServiceType) {
			return false, model.RiderOptedOut, "the driver opted out for this order's service type"
		}

		if isRiderEligible, _, _ := constraint.CheckIfRiderEligibleForMp1(ctx, driver, freshOrder); !isRiderEligible {
			return false, model.Mp1ATRCannotBeAssigned, "MP1 ATR cannot be assigned to a rider holding MP2"
		}
	}

	return true, "", ""
}

func IsMPMaxLoad3(config dispatcherconfig.AutoAssignDbConfig, moType prediction.MOType, maxOrdersPerRider int64) bool {
	return config.EnableMultiplePickup &&
		!config.DisableMPMaxLoadThree &&
		service.GetMaxOrdersPerTrip(moType) > 2 &&
		maxOrdersPerRider > 2
}

func DriverOptedOutServicesSet(srv OptOutAllowedLoader, pref model.ServicePreference) func(context.Context, *model.DriverMinimal) sets.Of[model.Service] {
	return func(ctx context.Context, driver *model.DriverMinimal) sets.Of[model.Service] {
		// default to no opt-out allowed services (nil/empty slice)
		optOutAllowed, isMergeFoodMartEnabled, _ := srv.OptOutAllowedServicesFromPreferenceWithWhitelist(ctx, pref, driver.DriverID)
		return service.DriverMinimalOptOutSetWithMerge(driver, optOutAllowed, isMergeFoodMartEnabled)
	}
}

func CheckIfRiderEligibleForMp1(orderRepository repository.OrderRepository) func(context.Context, *model.DriverMinimal, *model.Order) (bool, model.RiderFilterName, string) {
	return func(ctx context.Context, dm *model.DriverMinimal, ord *model.Order) (bool, model.RiderFilterName, string) {
		errorGettingOtherActiveMP := false

		var otherMP *model.Order
		if ord.IsMP1ATR() {
			if ord.IsHasAnotherMPOrder() {
				var err error
				otherMP, err = orderRepository.GetOtherActiveMP(ctx, *ord)
				if err != nil && !errors.Is(err, repository.ErrNotFound) {
					errorGettingOtherActiveMP = true
					logrus.Errorf("unable to get other active mp to validate if rider is eilgible for MP1 ATR due to %v", err)
				}
			}
		}

		return !errorGettingOtherActiveMP && (otherMP == nil || otherMP.Driver == "" || otherMP.Driver != dm.DriverID), model.Mp1ATRCannotBeAssigned, "MP1 ATR cannot be assigned to a rider holding MP2"
	}
}

func getNotifiedDrivers(ctx context.Context, alogRepository repository.AssignmentLogRepository, orderID string) sets.Of[string] {
	notifiedDrivers, err := alogRepository.AssignedDrivers(ctx, orderID, repository.WithReadPrimary)
	if err != nil {
		if !errors.Is(err, mongodb.ErrDataNotFound) {
			logx.Error().Str(logutil.OrderID, orderID).Err(err).Msgf("autoassign: orderID=%s, cannot get assignment log with error %v.", orderID, err)
		}
	}

	if len(notifiedDrivers) > 0 {
		drivers := sets.New[string]()
		for _, driver := range notifiedDrivers {
			drivers.Add(driver.DriverID)
		}
		return drivers
	}

	return sets.New[string]()
}

func CheckPlanRoute(ctx context.Context, dl *service.DriverWithLocation, newOrders []*model.Order,
	planRoute model.PlanRoute, constraint *AssignmentConstraint, validator PlanRouteValidator,
	setting service.ValidatePlanRouteSetting) (bool, service.ValidateRouteErrRes, string) {

	// support TopN MO and expected single order assign
	if constraint.AssignmentType == MOAssignmentType && len(newOrders) == 1 && !service.IsMO(planRoute.RouteResponse, newOrders[0].OrderID) {
		logx.Info().Str(logutil.OrderID, newOrders[0].OrderID).Str(logutil.DriverID, dl.Driver.DriverID).Msgf("the driver does not match the assignment type: MO")
		return false, service.NewValidateRouteErrRes(model.PlanRoutesUnmatchedWithAssignmentTypeMO, timeutil.TheEndOfTime()), "the driver does not match the assignment type: MO"
	}

	// check planroute
	var newOrderIDs []string
	for _, o := range newOrders {
		newOrderIDs = append(newOrderIDs, (*o).OrderID)
	}
	// TODO[LMF-16135] can be tuning to reduce load database, method should not reload driver and new orders as caller should provide fresh data when calling
	if filterOut, err := validator.ValidatePlanRoute(ctx, planRoute.RouteResponse, newOrderIDs, *dl, setting); err != nil {
		return false, filterOut, err.Error()
	}

	return true, service.ValidateRouteErrRes{}, ""
}

func InsertIllegalDriver(config dispatcherconfig.AutoAssignDbConfig, assignmentLogRepository repository.AssignmentLogRepository, illegalDriverRepository repository.IllegalDriverRepository) func(context.Context, time.Time, model.RiderFilterName, error, []model.Order, []string, model.DriverMinimal) {
	return func(ctx context.Context, illegalUntil time.Time, riderFilterName model.RiderFilterName, validateErr error, newOrders []model.Order, newOrderIDs []string, d model.DriverMinimal) {
		// For backward compatibility, only supports cases where driver is permanently illegal to assigning orders
		if !config.DisableIllegalDriverFilter && illegalUntil == timeutil.TheEndOfTime() {
			reason := validateErr.Error()
			for _, order := range newOrders {
				record := model.NewIllegalDriverRecord(d.DriverID, reason)
				if err := assignmentLogRepository.InsertIllegalDriver(ctx, order.OrderID, order.DeliveringRound, record); err != nil {
					logx.Error().Interface(logutil.OrderIDs, newOrderIDs).Str(logutil.DriverID, d.DriverID).Err(err).Msgf("batch assign: can't insert illegal driver for orderId: %v, driver: %v due to %v", order.OrderID, d.DriverID, err)
				}
			}
		}
		if !config.DisableIllegalDriverFilterByRedis {
			for _, order := range newOrders {
				if err := illegalDriverRepository.AddIllegalDriver(ctx, order.OrderID, order.DeliveringRound, order.ExpireAt, d.DriverID, riderFilterName, illegalUntil); err != nil {
					logx.Error().Interface(logutil.OrderIDs, newOrderIDs).Str(logutil.DriverID, d.DriverID).Err(err).Msgf("batch assign: can't insert temporary illegal driver to redis for orderId: %v, driver: %v due to %v", order.OrderID, d.DriverID, err)
				}
			}
		}
	}
}
