package orderassigner

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiErr "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	orderapi "git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/domain"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/sim"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type OrderAssignerHandler struct {
	taskDeps OrderAssignerTaskDeps

	AssignmentLogRepo                      repository.AssignmentLogRepository
	DriverRepository                       repository.DriverRepository
	MapService                             mapservice.MapService
	Locker                                 locker.Locker
	AtomicAutoAssignDbConfig               *dispatcherconfig.AtomicAutoAssignDbConfig
	ServiceAreaRepository                  repository.ServiceAreaRepository
	OrderRepository                        repository.OrderRepository
	LocationManager                        service.LocationManager
	FeatureFlagService                     featureflag.Service
	DistributionService                    service.DistributionService
	Dispatcher                             dispatcher.Dispatcher
	PredictionService                      service.PredictionService
	AssignerTaskBuilder                    AssignerTaskBuilderFn
	WorkerContext                          safe.WorkerContext
	DistributionLogManager                 service.DistributionLogManager
	StopMetric                             metric.Counter
	BatchRedistributionCountMetric         metric.Counter
	BatchPartialFailAssignOrderCountMetric metric.Counter
}

type AssignerTaskBuilderFn func(deps OrderAssignerTaskDeps, state OrderAssignerTaskState) OrderAssigner

func (task *OrderAssignerHandler) AssignHandler(gctx *gin.Context) {
	var req AssignOrderReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(ErrValidateAssigningOrderFailedCode, err))
		return
	}

	if len(req.OrderIDs) == 0 {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(ErrValidateAssigningOrderFailedCode, errors.New("orderIds is empty")))
		return
	}

	ctx := gctx.Request.Context()
	constraint := req.GetAssignmentConstraint()
	serviceArea, err := task.ServiceAreaRepository.GetByRegion(ctx, req.Region)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	// TODO can read secondary with filter status = ASSIGNING_DRIVER
	orders, err := task.OrderRepository.GetMany(ctx, req.OrderIDs)
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}
	if len(orders) != len(req.OrderIDs) {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(ErrValidateAssigningOrderFailedCode, errors.New("some order is missing")))
		return
	}
	ordersMap := make(map[string]model.Order) // must become readonly after write for concurrency
	for _, order := range orders {
		ordersMap[order.OrderID] = order
	}
	newOrders := []model.Order{}
	for _, orderID := range req.OrderIDs {
		// ordersMap is read-only
		order, ok := ordersMap[orderID]
		if !ok {
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(ErrValidateAssigningOrderFailedCode, errors.New("some order is missing")))
			return
		}
		if order.Status != model.StatusAssigningDriver {
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(ErrValidateAssigningOrderFailedCode, errors.New("some order is not status assigning")))
			return
		}
		newOrders = append(newOrders, order)
	}
	// TODO can be removed? might never reach
	if len(newOrders) == 0 {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(ErrValidateAssigningOrderFailedCode, errors.New("order state doesn't valid")))
		return
	}

	currentRoundMap := task.calculateCurrentRound(ctx, newOrders)

	drivers, err := task.LocationManager.GetDriverWithLocationByIDs(ctx, []string{req.DriverID})
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}
	if len(drivers) != 1 {
		apiutil.ErrBadRequest(gctx, apiutil.NewFromError(ErrValidateAssigningOrderFailedCode, errors.New("driver not found")))
		return
	}
	d := drivers[0]

	if err := task.assign(ctx, req, newOrders, d, serviceArea.Distribution, currentRoundMap, constraint); err != nil {
		apiutil.ErrInternalError(gctx, apiErr.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, gin.H{})
	return
}

func (task *OrderAssignerHandler) assign(
	ctx context.Context,
	req AssignOrderReq,
	newOrders []model.Order,
	d service.DriverWithLocation,
	distCfg model.AutoAssignDistribution,
	currentRoundMap map[string]int,
	constraint AssignmentConstraint,
) error {
	d.IsPrioritized = req.AssignmentLogOpts.IsPrioritized
	d.IsDedicatedZone = req.AssignmentLogOpts.IsDedicatedZone
	planRoute := req.PlanRoute.ToModel()
	region, zoneCode := req.Region, req.ZoneCode
	notifyOpts := []service.NotifyOption{service.WithFirebase}
	if distCfg.NotifyViaSocketIOEnabled {
		notifyOpts = append(notifyOpts, service.WithSocketIO)
	}
	newOrderIDs := model.OrderIDSetFromList(newOrders)
	var orderPtrs []**model.Order
	for _, order := range newOrders {
		orderPtr := &order
		orderPtrs = append(orderPtrs, &orderPtr)
	}
	acceptDurationBufferInSeconds := task.AtomicAutoAssignDbConfig.Get().AcceptDurationBufferInSeconds
	lockTTL := time.Duration(distCfg.AcceptingDurationInSecond+acceptDurationBufferInSeconds) * time.Second

	_, lockOrderIDs, err := task.lockDriverAndOrder(ctx, d.Driver.DriverID, newOrderIDs.GetElements(), lockTTL, constraint.DistributionLogMetadata)
	if err != nil {
		return err
	}

	bgCtx := task.WorkerContext.NewContextWithSameWaitGroup(ctx)
	safe.GoFuncWithCtx(bgCtx, func() {
		defer task.WorkerContext.ContextDone(bgCtx)
		defer task.unlockAssigningDriver(bgCtx, d.Driver.DriverID)

		orderPickupDistanceMap := make(map[string]float64, len(newOrders))
		for _, order := range newOrders {
			if req.AssignmentLogOpts.IsMultiplePickup { // Interim handle MP Hack
				orderPickupDistanceMap[order.OrderID] = 0
				continue
			}
			table, err := task.MapService.FindDistances(bgCtx, []mapservice.Location{mapservice.Location(d.Location)}, mapservice.Location(order.Routes[0].Location))
			if err != nil || len(table.Distances) == 0 {
				logrus.Errorf("assign: can't find distance for driver %s while trying to assign order %s", d.Driver.DriverID, order.OrderID)
				task.batchRedistribute(bgCtx, newOrderIDs.GetElements(), lockOrderIDs, region, zoneCode, req.IsFromBatchDistribution(), req.RidersTriedAssigning, false)
				return
			}
			orderPickupDistanceMap[order.OrderID] = table.Distances[0]
		}

		opt := NewAssignmentLogOpt(model.RegionCode(region), req.IsFromBatchDistribution())
		opt.IsMultiplePickup = req.AssignmentLogOpts.IsMultiplePickup
		opt.LockDuration = req.AssignmentLogOpts.LockDuration
		opt.IsEnabledAssignAPI = true

		var order *model.Order
		if req.IsFromSingleDistribution() && len(orderPtrs) == 1 {
			order = *orderPtrs[0]
		}

		assigner := task.AssignerTaskBuilder(
			task.taskDeps,
			NewOrderAssignerState(
				order,
				distCfg,
				req.AssignmentLogOpts.IsUsingPredictionService,
				acceptDurationBufferInSeconds,
				req.AssignmentLogOpts.SwitchbackExperiments,
				req.AssignmentLogOpts.PredictionModelVersions,
				timeutil.TheEndOfTime(),
				req.AssignmentLogOpts.UseBikePriority,
				NewStopMetricWithRegions(task.StopMetric, []model.RegionCode{model.RegionCode(region)}),
				req.AssignmentLogOpts.SearchRadiusByDistrict,
				req.AssignmentLogOpts.MOAggressiveLevel,
				req.DistributionStartedAt,
			),
		)
		isBatchAssignmentEnabled := task.AtomicAutoAssignDbConfig.Get().EnableBatchAssignment && distCfg.BatchAssignmentEnabled
		assignmentResult := assigner.AssignDriver(
			bgCtx,
			&d,
			model.PlanRoute{RouteResponse: planRoute},
			opt,
			notifyOpts,
			orderPtrs,
			newOrderIDs.GetElements(),
			currentRoundMap,
			isBatchAssignmentEnabled,
			orderPickupDistanceMap,
			model.SearchRiderStrategy(req.AssignmentLogOpts.SearchRiderStrategy),
			&constraint,
		)
		done := len(assignmentResult.RedistributableOrders) == 0
		if opt.IsMultiplePickup && !(done && err == nil) && len(orderPtrs) == 1 && req.IsFromSingleDistribution() { // interim handle MP hack
			order := *orderPtrs[0]
			if len(order.Options.MpOrderIDs) != 0 {
				orderToBundleID := order.Options.MpOrderIDs[0]
				orderToBundleWith, err := task.OrderRepository.Get(bgCtx, orderToBundleID)
				if err != nil {
					logx.Error().Err(err).Str(logutil.OrderID, order.OrderID).Msgf("assign mp: orderID=%s, get order failed, err=%v", orderToBundleID, err)
					return
				}
				if unlockErr := task.DriverRepository.UnlockForQueueing(bgCtx, orderToBundleWith.Driver); unlockErr != nil {
					logx.Error().Err(err).Str(logutil.OrderID, order.OrderID).Msgf("assign mp: unable to unlock driver %v for queueing, err=%v", orderToBundleWith.Driver, unlockErr)
				}
			}
		}
		for _, successfullyAssignedOrderID := range assignmentResult.SuccessfullyAssignedOrders {
			task.Locker.RemoveState(bgCtx, locker.AssigningOrderState(successfullyAssignedOrderID))
		}

		if len(assignmentResult.RedistributableOrders) != 0 {
			ridersTriedAssigning := req.RidersTriedAssigning
			if assignmentResult.IsTriedAssigned { // TODO: recheck
				ridersTriedAssigning += 1
			}
			task.batchRedistribute(bgCtx, assignmentResult.RedistributableOrders, lockOrderIDs, region, zoneCode, req.IsFromBatchDistribution(), ridersTriedAssigning, assignmentResult.IsRedistributionRequired)
		}

		if len(assignmentResult.SuccessfullyAssignedOrders) != len(newOrders) {
			unsuccessfulOrders := newOrderIDs.Minus(types.NewStringSet(assignmentResult.SuccessfullyAssignedOrders...))
			//TODO: interim solution will be revised metrics in LMF-15697
			if req.IsFromBatchDistribution() {
				task.BatchPartialFailAssignOrderCountMetric.Add(float64(unsuccessfulOrders.Count()), zoneCode)
			}
			logrus.Warnf("assign: assigning orders %s unsuccessful to rider %s", unsuccessfulOrders.GetElements(), d.Driver.DriverID)
			return
		}
	})

	return nil
}

func (task *OrderAssignerHandler) calculateCurrentRound(ctx context.Context, orders []model.Order) map[string]int {
	currentRoundMap := make(map[string]int, len(orders))
	for _, o := range orders {
		records, err := task.AssignmentLogRepo.AssignedDrivers(ctx, o.OrderID)
		if err != nil {
			logrus.Errorf("failed finding assigned driver on calculate current round err: %v", err)
		}
		round, err := model.CalculateMaxRound(records)
		if err != nil {
			logrus.Errorf("failed on calculate max round err: %v", err)
		}
		currentRoundMap[o.OrderID] = round + 1
	}
	return currentRoundMap
}

func (task *OrderAssignerHandler) lockDriverAndOrder(ctx context.Context, driverID string, orderIDs []string, ttl time.Duration, distributionLogMetadata model.DistributionLogMetadata) (types.StringSet, types.StringSet, error) {
	lockDriverIDs := types.NewStringSet()
	lockOrderIDs := types.NewStringSet()
	filterData := model.NewRiderFilterData()
	defer task.DistributionLogManager.CapturePostFilterEvent(ctx, filterData, distributionLogMetadata)

	for _, orderID := range orderIDs {
		ok := task.Locker.SetState(ctx, locker.AssigningOrderState(orderID), "auto-assign", ttl)
		if !ok {
			logrus.Warnf("auto_assign: [notifyBestDriverInBackground] orderID=%s order is being assigned in another process", orderID)
			return types.NewStringSet(), types.NewStringSet(), fmt.Errorf("can't acquire lock on orderID: %s", orderID)
		}
		lockOrderIDs.Add(orderID)
	}

	if ok := task.Locker.SetState(ctx, locker.DriverAutoAssignState(driverID), "assign-order", ttl); !ok {
		logrus.Warnf("batch_auto_assign: driverID=%s driver is being assigned another order.", driverID)
		filterData.Add(model.DriverIsBeingAssignedAnotherOrder, driverID)
		task.unlockAssigningOrders(ctx, lockOrderIDs.GetElements(), lockOrderIDs)
		return types.NewStringSet(), types.NewStringSet(), fmt.Errorf("can't acquire lock on driverID: %s", driverID)
	}
	lockDriverIDs.Add(driverID)

	return lockDriverIDs, lockOrderIDs, nil
}

func (task *OrderAssignerHandler) unlockAssigningOrders(ctx context.Context, orderIDs []string, lockedOrderIDs types.StringSet) {
	for _, o := range orderIDs {
		if lockedOrderIDs.Has(o) {
			task.Locker.RemoveState(ctx, locker.AssigningOrderState(o))
		}
	}
}

func (task *OrderAssignerHandler) unlockAssigningDriver(ctx context.Context, driverID string) {
	if driverID == "" {
		return
	}
	task.Locker.RemoveState(ctx, locker.DriverAutoAssignState(driverID))
}

func (task *OrderAssignerHandler) batchRedistribute(ctx context.Context, orderIDs []string, lockedOrderIDs types.StringSet, region string, zoneCode string, isFromBatchDistribution bool, ridersTriedAssigning int, isRedistributionRequired bool) {
	if len(orderIDs) == 0 {
		return
	}

	if isFromBatchDistribution { //TODO: interim solution will be revised metrics in LMF-15697
		task.BatchRedistributionCountMetric.Add(float64(len(orderIDs)), zoneCode)
	}

	bgCtx := safe.NewContextWithSameWaitGroup(ctx)
	task.unlockAssigningOrders(bgCtx, orderIDs, lockedOrderIDs)

	for _, orderID := range orderIDs {
		orderID := orderID
		safe.GoFuncWithCtx(bgCtx, func() {
			if err := task.DistributionService.DistributeOrder(ctx, service.NewRedistributeOrderReq(orderID, &ridersTriedAssigning, isRedistributionRequired)); err != nil {
				logrus.Errorf("batch_auto_assign: can't publish redistribute event orderID:%s error:%v", orderID, err)
			}
		})
	}
}

func ProvideOrderAssignerHandler(
	atomicAutoAcceptConfig *dispatcherconfig.AtomicAutoAcceptConfig,
	atomicAutoAssignDbConfig *dispatcherconfig.AtomicAutoAssignDbConfig,
	distributionConfig *dispatcherconfig.AtomicDistributionConfig,
	assignmentLogRepo repository.AssignmentLogRepository,
	IllegalDriverRepo repository.IllegalDriverRepository,
	locker locker.Locker,
	driverRepository repository.DriverRepository,
	orderRepository repository.OrderRepository,
	serviceAreaRepository repository.ServiceAreaRepository,
	rainSituationService service.RainSituationService,
	onTopFareService service.OnTopFareService,
	driverLocationRepository repository.DriverLocationRepository,
	mapService mapservice.MapService,
	orderConfig orderapi.OrderAPIConfig,
	assignmentRepo repository.AssignmentRepository,
	statisticService service.StatisticService,
	bus domain.EventBus,
	driverService service.DriverServiceInterface,
	notifier service.Notifier,
	txnHelper transaction.TxnHelper,
	meter metric.Meter,
	locationManager service.LocationManager,
	featureFlagService featureflag.Service,
	distributionService service.DistributionService,
	dispatcher dispatcher.Dispatcher,
	distributionLogManager service.DistributionLogManager,
	servicePreferenceService service.ServicePreferenceService,
	predictionService service.PredictionService,
	acceptor *orderapi.Acceptor,
	workerContext safe.WorkerContext,
	metricsRegistry metric.MetricsRegistry,
) *OrderAssignerHandler {
	taskDeps := NewOrderAssignerDeps(
		atomicAutoAcceptConfig,
		atomicAutoAssignDbConfig,
		distributionConfig,
		assignmentLogRepo,
		IllegalDriverRepo,
		locker,
		driverRepository,
		orderRepository,
		serviceAreaRepository,
		rainSituationService,
		onTopFareService,
		driverLocationRepository,
		mapService,
		orderConfig,
		assignmentRepo,
		statisticService,
		bus,
		driverService,
		notifier,
		txnHelper,
		new(AcceptOrderFunction),
		new(AcceptAssignmentFunction),
		sim.NewRealTimeEnvironment(),
		meter.GetCounter("autoassign_deferred_order_accept_count", "how many deferred orders are accepted by rider", "region"),
		meter.GetHistogram("autoassign_locking_driver_duration_ms", "Locking duration that system has been used to lock the driver", metric.DefaultHistogramBucket),
		distributionLogManager,
		servicePreferenceService,
		predictionService,
		metricsRegistry,
		meter.GetCounter("autoassign_post_filter_count", "how many candidate rider was filter out before assigning orders", "filter_name", "region"),
	)
	*taskDeps.AcceptOrderFn = func(
		ctx context.Context,
		order *model.Order,
		orderID,
		driverID string,
		syncDelivery bool,
	) (*model.AcceptedOrderInfo, error) {
		return acceptor.AcceptOrder(ctx, order, orderID, driverID, syncDelivery, orderapi.UpdateOrderState, nil, "auto_accepted")
	}
	*taskDeps.AcceptAssignmentFn = func(
		ctx context.Context,
		assignmentID model.AssignmentID,
		driverID string,
		syncDelivery bool,
	) (orderapi.AcceptAssignmentResult, error) {
		return acceptor.AcceptAssignment(ctx, assignmentID, driverID, syncDelivery, orderapi.UpdateOrderState, nil, "auto_accepted")
	}
	return &OrderAssignerHandler{
		taskDeps:                               taskDeps,
		AssignmentLogRepo:                      assignmentLogRepo,
		DriverRepository:                       driverRepository,
		MapService:                             mapService,
		Locker:                                 locker,
		AtomicAutoAssignDbConfig:               atomicAutoAssignDbConfig,
		ServiceAreaRepository:                  serviceAreaRepository,
		OrderRepository:                        orderRepository,
		LocationManager:                        locationManager,
		FeatureFlagService:                     featureFlagService,
		DistributionService:                    distributionService,
		Dispatcher:                             dispatcher,
		AssignerTaskBuilder:                    NewOrderAssignerTask,
		WorkerContext:                          workerContext,
		DistributionLogManager:                 distributionLogManager,
		StopMetric:                             meter.GetCounter("autoassign_orders", "Number of orders stop distributed by auto-assignment", "status", "round", "region"),
		BatchRedistributionCountMetric:         meter.GetCounter("autoassign_batch_redistribution", "in batch distribution, how many redistribution", "zone"),
		BatchPartialFailAssignOrderCountMetric: meter.GetCounter("autoassign_batch_partial_fail_assignment", "how many orders are redistribute after the rider has accepted at least one previous order of the same assignment", "zone"),
	}
}
