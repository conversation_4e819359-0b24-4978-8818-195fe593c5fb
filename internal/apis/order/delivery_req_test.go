package order

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/go-playground/validator.v9"

	translationProto "git.wndv.co/go/proto/lineman/shared/common/v1"
	customvalidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

var validate = customvalidator.NewDefaultValidator().ValidateStruct

func TestQuoteRequest_Quote(t *testing.T) {
	t.Run("create correct quote", func(tt *testing.T) {
		req := QuoteRequest{
			QuoteID:     "quote-1",
			UserID:      "user-1",
			ServiceType: model.ServiceFood,
			Routes: []model.Stop{
				{
					ID:      "stop-1",
					Name:    "stop-name-1",
					Address: "address-1",
					Phones:  []string{"012345678"},
					Location: model.Location{
						Lat: 2.03319032,
						Lng: 2.03482783,
					},
					Memo:     "memo",
					MemoType: "someBadMemoType", // Should be defaulted to MemoTypeText
					PickingItems: []model.Item{
						{
							Name:     "item-1",
							Price:    100.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
						{
							Name:     "item-2",
							Price:    150.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
					},
					CollectPayment:           true,
					ItemsPrice:               250.0,
					ItemsPriceBeforeDiscount: types.NewFloat64(20.0),
				},
				{
					ID:      "stop-2",
					Name:    "stop-name-2",
					Address: "address-2",
					Phones:  []string{"012345678"},
					Location: model.Location{
						Lat: 3.03319032,
						Lng: 3.03482783,
					},
					Memo:     "memo",
					MemoType: model.MemoTypeHandToUser,
					PickingItems: []model.Item{
						{
							Name:     "item-1",
							Price:    100.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
						{
							Name:     "item-2",
							Price:    150.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
					},
					CollectPayment:           true,
					ItemsPrice:               250.0,
					ItemsPriceBeforeDiscount: types.NewFloat64(20.0),
					Distance:                 200,
				},
			},
			PaymentMethod: model.PaymentMethodCash,
			Discounts: []DiscountReq{
				{
					Type:     model.DiscountTypeCoupon,
					Category: "category-coupon",
					Code:     "code-coupon",
					Discount: 30.0,
				},
				{
					Type:     model.DiscountTypeSubsidize,
					Category: "category-subsidize",
					Code:     "code-subsidize",
					Discount: 20.0,
				},
				{
					Type:     model.DiscountTypeCouponAdvance,
					Category: "category-advance",
					Code:     "code-advance",
					Discount: 10.0,
				},
			},
			Options: OptionsReq{
				RequireBox:        true,
				UserPreferredLang: translationProto.TranslationLanguage_TRANSLATION_LANGUAGE_ENGLISH,
			},
		}

		timeutils.Freeze()
		defer timeutils.Unfreeze()
		now := timeutil.BangkokNow().UTC()
		deliveryFee := model.NewDeliveryFee(40.0, 30.0, 200)
		req.Routes[1].PriceSummary = *model.NewPriceSummary(req.Routes[0].ItemsPrice, req.Routes[0].ItemsPriceBeforeDiscount, deliveryFee, deliveryFee, req.PaymentMethod)
		actual, err := req.Quote([]model.RegionCode{"AYUTTHAYA"}, deliveryFee, deliveryFee)

		require.NoError(tt, err)
		require.Equal(tt, "quote-1", actual.QuoteID)
		require.Equal(tt, "user-1", actual.UserID)
		require.Equal(tt, model.ServiceFood, actual.ServiceType)

		actualPriceSummary := actual.Routes[1].PriceSummary
		actualPriceSummary.ExperimentalDeliveryFee = model.DeliveryFeeSummary{}
		actual.Routes[0].PriceSummary = model.PriceSummary{}
		actual.Routes[1].PriceSummary = model.PriceSummary{}
		require.Equal(tt, model.Stop{
			ID:      "stop-1",
			Name:    "stop-name-1",
			Address: "address-1",
			Phones:  []string{"012345678"},
			Location: model.Location{
				Lat: 2.03319032,
				Lng: 2.03482783,
			},
			Memo:     "memo",
			MemoType: model.MemoTypeText,
			PickingItems: []model.Item{
				{
					Name:     "item-1",
					Price:    100.0,
					Quantity: 2,
					Memo:     "memo-1",
				},
				{
					Name:     "item-2",
					Price:    150.0,
					Quantity: 2,
					Memo:     "memo-1",
				},
			},
			DeliveryStatus: model.DeliveryStatusInit,
			DeliveryHistory: map[model.DeliveryStatus]time.Time{
				model.DeliveryStatusInit: now,
			},
			CollectPayment:           true,
			ItemsPrice:               260.0,
			ItemsPriceBeforeDiscount: types.NewFloat64(20.0),
		}, actual.Routes[0])
		require.Equal(tt, model.Stop{
			ID:      "stop-2",
			Name:    "stop-name-2",
			Address: "address-2",
			Phones:  []string{"012345678"},
			Location: model.Location{
				Lat: 3.03319032,
				Lng: 3.03482783,
			},
			Memo:     "memo",
			MemoType: model.MemoTypeHandToUser,
			PickingItems: []model.Item{
				{
					Name:     "item-1",
					Price:    100.0,
					Quantity: 2,
					Memo:     "memo-1",
				},
				{
					Name:     "item-2",
					Price:    150.0,
					Quantity: 2,
					Memo:     "memo-1",
				},
			},
			DeliveryStatus: model.DeliveryStatusInit,
			DeliveryHistory: map[model.DeliveryStatus]time.Time{
				model.DeliveryStatusInit: now,
			},
			CollectPayment:           true,
			ItemsPrice:               250.0,
			ItemsPriceBeforeDiscount: types.NewFloat64(20.0),
			Distance:                 200,
		}, actual.Routes[1])
		require.Equal(tt, model.PriceSummary{
			DeliveryFee: model.DeliveryFeeSummary{
				PaymentMethod: model.PaymentMethodCash,
				OnTopScheme:   []model.OnTopScheme{},
				Discounts: []model.Discount{
					{
						Type:     model.DiscountTypeCoupon,
						Category: "category-coupon",
						Code:     "code-coupon",
						Discount: 30.0,
					},
					{
						Type:     model.DiscountTypeSubsidize,
						Category: "category-subsidize",
						Code:     "code-subsidize",
						Discount: 20.0,
					},
				},
				ExtraCharges:  model.ExtraChargeList{},
				RawBaseFee:    40.0,
				BaseFee:       40.0,
				RoadFee:       30.0,
				AdditionalFee: map[string]float64{},
				SubTotal:      70.0,
				Total:         20.0,
			},
			ItemFee: model.ItemFeeSummary{
				PaymentMethod: model.PaymentMethodCash,
				Discounts: []model.Discount{
					{
						Type:     model.DiscountTypeCouponAdvance,
						Category: "category-advance",
						Code:     "code-advance",
						Discount: 10.0,
					},
				},
				ItemFee:                    250.0,
				ItemFeeBeforeDiscount:      types.NewFloat64(20.0),
				QuoteItemFeeBeforeDiscount: 260.0,
				SubTotal:                   260.0,
				Total:                      250.0,
			},
			Total: 270,
		}, actualPriceSummary)
		require.Equal(tt, model.OrderOptions{
			RequireBox:        true,
			BoxOption:         model.BoxOptionRequireBox,
			UserPreferredLang: translationProto.TranslationLanguage_TRANSLATION_LANGUAGE_ENGLISH.String(),
		}, actual.Options)
	})

	t.Run("item fee discount should empty if no discount match", func(tt *testing.T) {
		req := QuoteRequest{
			QuoteID:     "quote-1",
			UserID:      "user-1",
			ServiceType: model.ServiceFood,
			Routes: []model.Stop{
				{
					ID:      "stop-1",
					Name:    "stop-name-1",
					Address: "address-1",
					Phones:  []string{"012345678"},
					Location: model.Location{
						Lat: 2.03319032,
						Lng: 2.03482783,
					},
					Memo:     "stop-1-memo",
					MemoType: "",
					PickingItems: []model.Item{
						{
							Name:     "item-1",
							Price:    100.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
						{
							Name:     "item-2",
							Price:    150.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
					},
					CollectPayment: true,
					ItemsPrice:     250.0,
				},
				{
					ID:      "stop-2",
					Name:    "stop-name-2",
					Address: "address-2",
					Phones:  []string{"012345678"},
					Location: model.Location{
						Lat: 3.03319032,
						Lng: 3.03482783,
					},
					Memo:     "stop-2-memo",
					MemoType: model.MemoTypeLeaveAtPlace,
					PickingItems: []model.Item{
						{
							Name:     "item-1",
							Price:    100.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
						{
							Name:     "item-2",
							Price:    150.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
					},
					CollectPayment: true,
					ItemsPrice:     250.0,
					Distance:       200,
				},
			},
			PaymentMethod: model.PaymentMethodCash,
			Discounts: []DiscountReq{
				{
					Type:     model.DiscountTypeCoupon,
					Category: "category-coupon",
					Code:     "code-coupon",
					Discount: 30.0,
				},
				{
					Type:     model.DiscountTypeSubsidize,
					Category: "category-subsidize",
					Code:     "code-subsidize",
					Discount: 20.0,
				},
			},
			Options: OptionsReq{RequireBox: true},
		}

		actual, err := req.Quote([]model.RegionCode{"AYUTTHAYA"}, model.NewDeliveryFee(40.0, 30.0, 1000), model.NewDeliveryFee(40.0, 30.0, 1000))

		require.NoError(tt, err)
		require.Empty(tt, actual.Routes[1].PriceSummary.ItemFee.Discounts)

		require.Equal(tt, "stop-1-memo", actual.Routes[0].Memo)
		require.Equal(tt, model.MemoTypeText, actual.Routes[0].MemoType)
		require.Equal(tt, "stop-2-memo", actual.Routes[1].Memo)
		require.Equal(tt, model.MemoTypeLeaveAtPlace, actual.Routes[1].MemoType)
	})

	t.Run("delivery discount should empty if no discount match", func(tt *testing.T) {
		req := QuoteRequest{
			QuoteID:     "quote-1",
			UserID:      "user-1",
			ServiceType: model.ServiceFood,
			Routes: []model.Stop{
				{
					ID:      "stop-1",
					Name:    "stop-name-1",
					Address: "address-1",
					Phones:  []string{"012345678"},
					Location: model.Location{
						Lat: 2.03319032,
						Lng: 2.03482783,
					},
					Memo: "memo-1",
					PickingItems: []model.Item{
						{
							Name:     "item-1",
							Price:    100.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
						{
							Name:     "item-2",
							Price:    150.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
					},
					CollectPayment: true,
					ItemsPrice:     250.0,
				},
				{
					ID:      "stop-2",
					Name:    "stop-name-2",
					Address: "address-2",
					Phones:  []string{"012345678"},
					Location: model.Location{
						Lat: 3.03319032,
						Lng: 3.03482783,
					},
					Memo:     "memo-2",
					MemoType: model.MemoTypeHandToUser,
					PickingItems: []model.Item{
						{
							Name:     "item-1",
							Price:    100.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
						{
							Name:     "item-2",
							Price:    150.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
					},
					CollectPayment: true,
					ItemsPrice:     250.0,
					Distance:       200,
				},
			},
			PaymentMethod: model.PaymentMethodCash,
			Discounts: []DiscountReq{
				{
					Type:     model.DiscountTypeCouponAdvance,
					Category: "category-advance",
					Code:     "code-advance",
					Discount: 10.0,
				},
			},
			Options: OptionsReq{RequireBox: true},
		}

		actual, err := req.Quote([]model.RegionCode{"AYUTTHAYA"}, model.NewDeliveryFee(40.0, 30.0, 200), model.NewDeliveryFee(40.0, 30.0, 200))

		require.NoError(tt, err)
		require.Equal(tt, "memo-1", actual.Routes[0].Memo)
		require.Equal(tt, model.MemoTypeText, actual.Routes[0].MemoType)
		require.Equal(tt, "memo-2", actual.Routes[1].Memo)
		require.Equal(tt, model.MemoTypeHandToUser, actual.Routes[1].MemoType)
		require.Empty(tt, actual.Routes[1].PriceSummary.DeliveryFee.Discounts)
	})

	t.Run("require box types conversion", func(tt *testing.T) {
		req := QuoteRequest{
			QuoteID:     "quote-1",
			UserID:      "user-1",
			ServiceType: model.ServiceFood,
			Routes: []model.Stop{
				{
					ID:      "stop-1",
					Name:    "stop-name-1",
					Address: "address-1",
					Phones:  []string{"012345678"},
					Location: model.Location{
						Lat: 2.03319032,
						Lng: 2.03482783,
					},
					Memo: "memo",
					PickingItems: []model.Item{
						{
							Name:     "item-1",
							Price:    100.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
						{
							Name:     "item-2",
							Price:    150.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
					},
					CollectPayment: true,
					ItemsPrice:     250.0,
				},
				{
					ID:      "stop-2",
					Name:    "stop-name-2",
					Address: "address-2",
					Phones:  []string{"012345678"},
					Location: model.Location{
						Lat: 3.03319032,
						Lng: 3.03482783,
					},
					Memo: "memo",
					PickingItems: []model.Item{
						{
							Name:     "item-1",
							Price:    100.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
						{
							Name:     "item-2",
							Price:    150.0,
							Quantity: 2,
							Memo:     "memo-1",
						},
					},
					CollectPayment: true,
					ItemsPrice:     250.0,
					Distance:       200,
				},
			},
			PaymentMethod: model.PaymentMethodCash,
			Discounts: []DiscountReq{
				{
					Type:     model.DiscountTypeCouponAdvance,
					Category: "category-advance",
					Code:     "code-advance",
					Discount: 10.0,
				},
			},
			Options: OptionsReq{BoxOption: model.BoxOptionRequireBox, RequireBoxTypes: []model.BoxType{model.BoxTypeLM, model.BoxTypeFoldable}},
		}

		actual, err := req.Quote([]model.RegionCode{"AYUTTHAYA"}, model.NewDeliveryFee(40.0, 30.0, 200), model.NewDeliveryFee(40.0, 30.0, 200))

		require.NoError(tt, err)
		require.Empty(tt, actual.Routes[1].PriceSummary.DeliveryFee.Discounts)
		require.Equal(tt, []model.BoxType{model.BoxTypeLM, model.BoxTypeFoldable}, actual.Options.RequireBoxTypes)
		require.Equal(tt, model.BoxOptionRequireBox, actual.Options.BoxOption)
	})
}

func TestUpdatePriceRequest_Validate(t *testing.T) {
	t.Parallel()

	t.Run("TotalPrice <= 0", func(tt *testing.T) {
		tt.Parallel()
		req := &UpdatePriceRequest{}
		require.Error(tt, req.Validate())
	})

	t.Run("EstimatedTime <= 0", func(tt *testing.T) {
		tt.Parallel()
		req := &UpdatePriceRequest{TotalPrice: 200}
		require.Error(tt, req.Validate())
	})

	t.Run("TotalPrice > 0 && EstimatedTime > 0", func(tt *testing.T) {
		tt.Parallel()
		req := &UpdatePriceRequest{TotalPrice: 200, EstimatedTime: 300}
		require.NoError(tt, req.Validate())
	})
}

func Test_QuoteRequest_Pauses(t *testing.T) {
	t.Run("does not accept unknown pause", func(t *testing.T) {
		var actual QuoteRequest
		err := bind(t, `
			{
				"routes": [
					 { "pauses": ["CONFIRM_PRICE", "badvalue", "CHECKOUT"] }
				]
			}
		`, &actual)
		assertValidationFailExists(t, err, "pause-validator")
	})
	t.Run("binds correctly", func(t *testing.T) {
		var actual QuoteRequest
		err := bind(t, `
			{
				"routes": [
					 { "pauses": ["CONFIRM_PRICE", "CHECKOUT"] }
				]
			}
		`, &actual)
		assertNoValidationFail(t, err, "pause-validator")
		assert.True(t, actual.Routes[0].Pauses[model.PauseConfirmPrice])
		assert.True(t, actual.Routes[0].Pauses[model.PauseCheckout])
	})
	t.Run("is not required", func(t *testing.T) {
		var actual QuoteRequest
		err := bind(t, `
			{
				"routes": [{}]
			}
		`, &actual)
		assertNoValidationFail(t, err, "pause-validator")
		assert.False(t, actual.Routes[0].Pauses[model.PauseConfirmPrice])
		assert.False(t, actual.Routes[0].Pauses[model.PauseCheckout])
	})
}

func TestUserOnTops_Schemes(t *testing.T) {
	t.Run("should create on-top scheme correctly", func(t *testing.T) {
		uot := UserOnTops{
			{
				Title:  "ON-TOP-1",
				Amount: 50,
			},
			{
				Title:  "ON-TOP-2",
				Amount: 20,
			},
			{
				Title:  "ON-TOP-3", // this should be filter out
				Amount: 0,
			},
		}

		require.Equal(t, []model.OnTopScheme{
			{
				Scheme:           model.UserPriceInterventionOnTopScheme,
				Name:             "ON-TOP-1",
				Amount:           50,
				BundleAmount:     50,
				IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
			},
			{
				Scheme:           model.UserPriceInterventionOnTopScheme,
				Name:             "ON-TOP-2",
				Amount:           20,
				BundleAmount:     20,
				IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
			},
		}, uot.Schemes())
	})
}

func bind(t *testing.T, rawJSON string, st interface{}) error {
	in, err := json.Marshal(json.RawMessage(rawJSON))
	require.NoError(t, err)
	err = json.Unmarshal(in, st)
	require.NoError(t, err)
	err = validate(st)
	return err
}

func assertValidationFailExists(t *testing.T, err error, tag string) {
	if !hasValidationFail(err, tag) {
		t.Errorf("no validation error of tag: %s! error: [%v]", tag, err)
	}
}

func assertNoValidationFail(t *testing.T, err error, tag string) {
	if hasValidationFail(err, tag) {
		t.Errorf("expects no validation fail with tag: %s, but it exists! error: [%v]", tag, err)
	}
}

func hasValidationFail(err error, tag string) bool {
	if err == nil {
		return false
	}
	validationErrors, ok := err.(validator.ValidationErrors)
	if !ok {
		return false
	}
	for _, e := range validationErrors {
		if e.Tag() == tag {
			return true
		}
	}
	return false
}
