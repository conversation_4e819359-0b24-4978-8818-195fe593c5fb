package order

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/api"
	v1 "git.wndv.co/lineman/delivery-service/types/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestFoodMartQRPromptpayPaymentInfo(t *testing.T) {
	t.Run("get_waiting_for_payment_qr_status", func(tt *testing.T) {
		ctx := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		qrPaymentRes := v1.QRPaymentDetailResponse{
			QRCode:    "RES_RAW_QR_CODE",
			Amount:    65535,
			CreatedAt: timeutils.Now(),
			ExpiredAt: timeutils.Now().Add(time.Minute * 15),
			Status:    v1.QRPaymentWaitingForPaymentStatus,
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(&qrPaymentRes, nil)

		qrPromptpayPaymentInfo := NewQRPromptpayPaymentInfo(ordApi, &model.Order{})
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctx)
		require.NoError(tt, err)
		err = qrPromptpayPaymentInfo.UpdateOrderDetail(ctx)
		require.NoError(tt, err)
		actualResponse, err := qrPromptpayPaymentInfo.GetAccountResponse(ctx)
		require.NoError(tt, err)
		require.Equal(tt, qrPaymentRes.QRCode, actualResponse.QRCode)
		require.Equal(tt, qrPaymentRes.Amount, *actualResponse.Amount)
		require.Equal(tt, qrPaymentRes.ExpiredAt.Local(), actualResponse.ExpiredAt.Local())
		require.Equal(tt, string(qrPaymentRes.Status), actualResponse.Status)
	})

	t.Run("get_paid_qr_status", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		qrPaymentRes := v1.QRPaymentDetailResponse{
			QRCode:    "RES_RAW_QR_CODE",
			Amount:    65535,
			CreatedAt: timeutils.Now(),
			ExpiredAt: timeutils.Now().Add(time.Minute * 15),
			Status:    v1.QRPaymentPaidStatus,
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(&qrPaymentRes, nil)
		deps.OrderRepository.EXPECT().UnsetPauseFlag(gomock.Any(), "REQ_ORDER_ID", 1, model.PauseQRPayment).Return(nil)

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}
		qrPromptpayPaymentInfo := NewQRPromptpayPaymentInfo(ordApi, &ord)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.NoError(tt, err)
		err = qrPromptpayPaymentInfo.UpdateOrderDetail(ctxRec)
		require.NoError(tt, err)
		actualResponse, err := qrPromptpayPaymentInfo.GetAccountResponse(ctxRec)
		require.NoError(tt, err)
		require.Equal(tt, qrPaymentRes.QRCode, actualResponse.QRCode)
		require.Equal(tt, qrPaymentRes.Amount, *actualResponse.Amount)
		require.Equal(tt, qrPaymentRes.ExpiredAt.Local(), actualResponse.ExpiredAt.Local())
		require.Equal(tt, string(qrPaymentRes.Status), actualResponse.Status)
	})

	t.Run("convert_to_cash_error", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				PayAtStop:   1,
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{},
					{
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
							},
							ItemFee: model.ItemFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
							},
							ExperimentalDeliveryFee: model.DeliveryFeeSummary{
								PaymentMethod: model.PaymentMethodQRPromptPay,
							},
						},
						Pauses: model.PauseSet{
							model.PauseQRPayment: true,
						},
						CollectPayment: false,
					},
				},
			},
		}

		resApiError := &api.Error{
			Code:    "QR_PROMPTPAY_ALREADY_CONVERTED_TO_CASH",
			Message: "qr promptpay already convert to cash",
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(nil, resApiError)
		deps.OrderRepository.EXPECT().UnsetPauseFlag(gomock.Any(), ord.OrderID, 1, model.PauseQRPayment).Return(nil)

		qrPromptpayPaymentInfo := NewQRPromptpayPaymentInfo(ordApi, &ord)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.ErrorContains(tt, err, "QR_PROMPTPAY_ALREADY_CONVERTED_TO_CASH")
	})
}

func TestBikeQRPromptpayPaymentInfo(t *testing.T) {
	t.Parallel()

	t.Run("qr pending", func(tt *testing.T) {
		ctx := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		qrPaymentRes := v1.QRPaymentDetailResponse{
			QRCode:    "RES_RAW_QR_CODE",
			Amount:    65535,
			CreatedAt: timeutils.Now(),
			ExpiredAt: timeutils.Now().Add(time.Minute * 15),
			Status:    v1.QRPaymentWaitingForPaymentStatus,
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(&qrPaymentRes, nil)

		qrPromptpayPaymentInfo := NewBikeQRPromptpayPaymentInfo(ordApi, &model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}, false, 0)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctx)
		require.NoError(tt, err)
		err = qrPromptpayPaymentInfo.UpdateOrderDetail(ctx)
		require.NoError(tt, err)
		actualResponse, err := qrPromptpayPaymentInfo.GetAccountResponse(ctx)
		require.NoError(tt, err)
		require.Equal(tt, qrPaymentRes.QRCode, actualResponse.QRCode)
		require.Equal(tt, qrPaymentRes.Amount, *actualResponse.Amount)
		require.Equal(tt, qrPaymentRes.ExpiredAt.Local(), actualResponse.ExpiredAt.Local())
		require.Equal(tt, string(qrPaymentRes.Status), actualResponse.Status)
	})

	t.Run("qr paid", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		qrPaymentRes := v1.QRPaymentDetailResponse{
			QRCode:    "RES_RAW_QR_CODE",
			Amount:    65535,
			CreatedAt: timeutils.Now(),
			ExpiredAt: timeutils.Now().Add(time.Minute * 15),
			Status:    v1.QRPaymentPaidStatus,
		}
		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(&qrPaymentRes, nil)
		deps.OrderRepository.EXPECT().UnsetPauseFlag(gomock.Any(), "REQ_ORDER_ID", 1, model.PauseQRPayment).Return(nil)

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}
		qrPromptpayPaymentInfo := NewQRPromptpayPaymentInfo(ordApi, &ord)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.NoError(tt, err)
		err = qrPromptpayPaymentInfo.UpdateOrderDetail(ctxRec)
		require.NoError(tt, err)
		actualResponse, err := qrPromptpayPaymentInfo.GetAccountResponse(ctxRec)
		require.NoError(tt, err)
		require.Equal(tt, qrPaymentRes.QRCode, actualResponse.QRCode)
		require.Equal(tt, qrPaymentRes.Amount, *actualResponse.Amount)
		require.Equal(tt, qrPaymentRes.ExpiredAt.Local(), actualResponse.ExpiredAt.Local())
		require.Equal(tt, string(qrPaymentRes.Status), actualResponse.Status)
	})

	t.Run("qr paid with error QR_PROMPTPAY_ALREADY_PAID should not error", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(nil, &api.Error{
			Code:      "QR_PROMPTPAY_ALREADY_PAID",
			Message:   "Error",
			Detail:    "",
			Info:      map[string]any{},
			Timestamp: api.Timestamp{},
		})
		deps.OrderRepository.EXPECT().UnsetPauseFlag(gomock.Any(), "REQ_ORDER_ID", 1, model.PauseQRPayment).Return(nil)

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}
		qrPromptpayPaymentInfo := NewBikeQRPromptpayPaymentInfo(ordApi, &ord, false, 0)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.NoError(tt, err)
		err = qrPromptpayPaymentInfo.UpdateOrderDetail(ctxRec)
		require.NoError(tt, err)
		actualResponse, err := qrPromptpayPaymentInfo.GetAccountResponse(ctxRec)
		require.NoError(tt, err)
		require.Zero(tt, actualResponse.QRCode)
		require.Nil(tt, actualResponse.Amount)
		require.Zero(tt, actualResponse.ExpiredAt)
		require.Zero(tt, actualResponse.CreatedAt)
		require.Equal(tt, string(v1.QRPaymentPaidStatus), actualResponse.Status)
	})

	t.Run("qr paid with error QR_PROMPTPAY_OUTSTANDING_AMOUNT_IS_ZERO should not error", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(nil, &api.Error{
			Code:      "QR_PROMPTPAY_OUTSTANDING_AMOUNT_IS_ZERO",
			Message:   "Error",
			Detail:    "",
			Info:      map[string]any{},
			Timestamp: api.Timestamp{},
		})
		deps.OrderRepository.EXPECT().UnsetPauseFlag(gomock.Any(), "REQ_ORDER_ID", 1, model.PauseQRPayment).Return(nil)

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}
		qrPromptpayPaymentInfo := NewBikeQRPromptpayPaymentInfo(ordApi, &ord, false, 0)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.NoError(tt, err)
		err = qrPromptpayPaymentInfo.UpdateOrderDetail(ctxRec)
		require.NoError(tt, err)
		actualResponse, err := qrPromptpayPaymentInfo.GetAccountResponse(ctxRec)
		require.NoError(tt, err)
		require.Zero(tt, actualResponse.QRCode)
		require.Nil(tt, actualResponse.Amount)
		require.Zero(tt, actualResponse.ExpiredAt)
		require.Zero(tt, actualResponse.CreatedAt)
		require.Equal(tt, string(v1.QRPaymentPaidStatus), actualResponse.Status)
	})

	t.Run("qr with error QR_PROMPTPAY_ALREADY_CONVERTED_TO_CASH throw error", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		deps.Delivery.EXPECT().GetQRPaymentDetail(gomock.Any(), gomock.Any()).Return(nil, &api.Error{
			Code:      "QR_PROMPTPAY_ALREADY_CONVERTED_TO_CASH",
			Message:   "Error",
			Detail:    "",
			Info:      map[string]any{},
			Timestamp: api.Timestamp{},
		})
		deps.OrderRepository.EXPECT().UnsetPauseFlag(gomock.Any(), "REQ_ORDER_ID", 1, model.PauseQRPayment).Return(nil)

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}
		qrPromptpayPaymentInfo := NewBikeQRPromptpayPaymentInfo(ordApi, &ord, false, 0)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.Error(tt, err)
	})

	t.Run("qr paid with error QR_PROMPTPAY_PRICE_NOT_CONFIRMED once", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		deps.Delivery.EXPECT().
			GetQRPaymentDetail(gomock.Any(), gomock.Any()).
			Return(nil, &api.Error{
				Code:      "QR_PROMPTPAY_PRICE_NOT_CONFIRMED",
				Message:   "Error",
				Detail:    "",
				Info:      map[string]any{},
				Timestamp: api.Timestamp{},
			})

		qrPaymentRes := v1.QRPaymentDetailResponse{
			QRCode:    "RES_RAW_QR_CODE",
			Amount:    65535,
			CreatedAt: timeutils.Now(),
			ExpiredAt: timeutils.Now().Add(time.Minute * 15),
			Status:    v1.QRPaymentWaitingForPaymentStatus,
		}
		deps.Delivery.EXPECT().
			GetQRPaymentDetail(gomock.Any(), gomock.Any()).
			Return(&qrPaymentRes, nil).
			Times(1)

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Status:  "ARRIVED_AT",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}

		qrPromptpayPaymentInfo := NewBikeQRPromptpayPaymentInfo(ordApi, &ord, true, 3)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.NoError(tt, err)
		err = qrPromptpayPaymentInfo.UpdateOrderDetail(ctxRec)
		require.NoError(tt, err)
		actualResponse, err := qrPromptpayPaymentInfo.GetAccountResponse(ctxRec)
		require.NoError(tt, err)
		require.Equal(tt, "RES_RAW_QR_CODE", actualResponse.QRCode)
		require.Equal(tt, &qrPaymentRes.Amount, actualResponse.Amount)
		require.Equal(tt, qrPaymentRes.ExpiredAt, actualResponse.ExpiredAt)
		require.Equal(tt, qrPaymentRes.CreatedAt, actualResponse.CreatedAt)
		require.Equal(tt, string(v1.QRPaymentWaitingForPaymentStatus), actualResponse.Status)
	})

	t.Run("qr paid with error QR_PROMPTPAY_PRICE_NOT_CONFIRMED 4 times", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		deps.Delivery.EXPECT().
			GetQRPaymentDetail(gomock.Any(), gomock.Any()).
			Return(nil, &api.Error{
				Code:      "QR_PROMPTPAY_PRICE_NOT_CONFIRMED",
				Message:   "Error",
				Detail:    "",
				Info:      map[string]any{},
				Timestamp: api.Timestamp{},
			}).Times(4)

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Status:  "ARRIVED_AT",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}

		qrPromptpayPaymentInfo := NewBikeQRPromptpayPaymentInfo(ordApi, &ord, true, 3)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.ErrorContains(tt, err, "QR_PROMPTPAY_PRICE_NOT_CONFIRMED")
	})

	t.Run("qr paid with error QR_PROMPTPAY_PRICE_NOT_CONFIRMED, not retry cause flag disabled", func(tt *testing.T) {
		ctxRec := context.TODO()
		ordApi, deps := newTestOrderAPI(tt, file.VOSConfig{}, ContingencyConfig{})

		deps.Delivery.EXPECT().
			GetQRPaymentDetail(gomock.Any(), gomock.Any()).
			Return(nil, &api.Error{
				Code:      "QR_PROMPTPAY_PRICE_NOT_CONFIRMED",
				Message:   "Error",
				Detail:    "",
				Info:      map[string]any{},
				Timestamp: api.Timestamp{},
			}).Times(1)

		ord := model.Order{
			OrderID: "REQ_ORDER_ID",
			Driver:  "REQ_DRIVER_ID",
			Status:  "ARRIVED_AT",
			Quote: model.Quote{
				ServiceType: model.ServiceBike,
				Routes: []model.Stop{
					{}, {
						Pauses: map[model.Pause]bool{
							model.PauseQRPayment: true,
						},
					},
				},
			},
		}

		qrPromptpayPaymentInfo := NewBikeQRPromptpayPaymentInfo(ordApi, &ord, false, 0)
		err := qrPromptpayPaymentInfo.GetPaymentDetail(ctxRec)
		require.ErrorContains(tt, err, "QR_PROMPTPAY_PRICE_NOT_CONFIRMED")
	})
}
