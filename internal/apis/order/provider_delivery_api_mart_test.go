package order

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestMart_CreateOrder(t *testing.T) {
	t.Parallel()

	req := func(req *CreateOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/order/create", testutil.JSON(req))
		return ctx, recorder
	}

	t.Run("create order from quote with mart POD", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)
		dirs := []domainModel.RestaurantDirection{{
			RestaurantID: resID,
			Parking:      "parking",
			Direction:    "direction",
			Memo:         "memo",
		}}

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		fp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		mockOrderDistEventMgr := mock_service.NewMockOrderDistributionEventManager(ctrl)
		fp.OrderDistributionEventManager = mockOrderDistEventMgr
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				ServiceType: domainModel.ServiceMart,
				Routes: []domainModel.Stop{
					{

						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								RawBaseFee: 60,
								BaseFee:    60,
								RoadFee:    55,
								SubTotal:   55,
								Total:      55,
							},
							Total: 0,
						},
						Memo: "test memo",
					},
				},
				DistributeRegions:     []domainModel.RegionCode{"AYUTTHAYA"},
				RevenuePrincipalModel: true,
			}, nil)

		deps.orderService.EXPECT().GetOtherActiveMP(gomock.Any(), gomock.Any()).Return(nil, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{RequiredPhotoOfJacketAndBoxes: []string{string(domainModel.ServiceMart)}}, nil)

		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				fromStopInfo, valid := order.Routes[0].Info.StopInfo.(*domainModel.StopInfoFood)
				require.True(tt, valid)
				require.Equal(tt, dirs[0], fromStopInfo.RestaurantDirection)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.Equal(tt, order.DistributeRegions[0], order.Region)
				require.False(tt, order.RevampedStatus)
				require.True(tt, order.IsRequireDeliveringPhotoURL)
				require.True(tt, order.Routes[1].Pauses[domainModel.PauseDeliveringPhoto])
				order.ExpireAt = expireAt
				return nil
			})

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		mockOrderDistEventMgr.EXPECT().PublishCreated(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(_ any, order domainModel.Order, eventTime time.Time) error {
			require.Equal(tt, ordid, order.OrderID)
			require.InDelta(tt, time.Now().UnixNano(), eventTime.UnixNano(), 5*float64(time.Minute))
			return nil
		})

		deps.distributionService.EXPECT().
			PublishDistributeOrderEvent(gomock.Any(), gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), fp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid, Remarks: dirs})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		fp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(tt, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)
		require.Equal(tt, ordid, resp.OrderID)
		require.Equal(tt, 60.0, resp.DeliveryFee.BaseFee)
	})
}
