package order

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"golang.org/x/sync/errgroup"

	exp "git.wndv.co/dalian-dev/experiment-sdk"
	"git.wndv.co/go/logx/v2"
	formServicePb "git.wndv.co/go/proto/lineman/form_service/v1"
	polygonv2 "git.wndv.co/go/proto/lineman/polygon/v2"
	transportationV1 "git.wndv.co/go/proto/lineman/transportation_price_intervention/v1"
	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	repApi "git.wndv.co/lineman/fleet-distribution/internal/apis/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uwterror"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/form"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/alertutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var (
	ErrRequiredCollectPayment   = errors.New("collect payment is required at one stop")
	ErrReCreateDeferredOrders   = errors.New("deferred orders cannot be re-created")
	ErrReCreateSwitchFlowOrders = errors.New("switch-flow orders cannot be re-created")
)

func (fapi *FoodProviderImpl) CalculateDeliveryPrice(gctx *gin.Context) {
	req, err := NewCalculateDeliveryFeeReq(gctx)
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		default:
			apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		}
		return
	}
	ctx := gctx.Request.Context()
	ds, err := fapi.AreaService.GetDistributeRegion(ctx, req.ServiceType, req.Region, service.NewLocation(req.Routes[0].Lat, req.Routes[0].Lng))
	if err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}
	distRegions := domainModel.DistributeRegions(ds)

	var (
		distance    types.Distance
		duration    float64
		calculator  domainModel.DeliveryFeeCalculator
		priceScheme domainModel.SettingDeliveryFeePriceScheme
	)

	g, ctx := errgroup.WithContext(ctx)

	g.Go(func() error {
		defer safe.Recover()
		for i := 0; i < len(req.Routes)-1; i++ {
			tmpDistance, tmpDuration, _, err := DistanceDurationBetweenRoutes(ctx, fapi.MapService, req.Routes[i], req.Routes[i+1], req.UseGoogleMap)
			if err != nil {
				return err
			}
			duration += tmpDuration
			distance += tmpDistance
		}
		return nil
	})

	g.Go(func() error {
		defer safe.Recover()

		tmpCalculator, tmpPriceScheme, err := fapi.DeliveryFeeSvc.GetDeliveryFeeCalculator(ctx, req.ServiceType, distRegions.DefaultRegion().String(), req.Routes[0], service.ToPriceSchemeKey(req.PriceScheme))
		calculator = tmpCalculator
		priceScheme = tmpPriceScheme
		return err
	})

	if err := g.Wait(); err != nil {
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	deliveryFee := calculator.Calculate(distance, false, priceScheme)
	deliveryPrices := make([]DeliveryPrice, len(req.Routes[1:]))
	extraCharge := make([]model.ExtraCharge, 0)
	for i, location := range req.Routes[1:] {
		if i == 0 {
			// NOTE: currently, set all roadFee, distance and duration into first delivery location
			deliveryPrices[i] = NewDeliveryPrice(location, deliveryFee.IncrementFee(), deliveryFee.Distance(), domainModel.DurationSecond(duration))
		} else {
			// TODO: set each roadFee, distance and duration for each location when calculator supports multiple location calculation
			deliveryPrices[i] = NewDeliveryPrice(location, 0, types.Distance(0), domainModel.DurationSecond(0))
		}
	}

	if len(req.Routes) >= 3 {
		amount := calMessengerStopPrice(len(req.Routes), priceScheme.MessengerFixPricePerStop())
		extraCharge = append(extraCharge, model.ExtraCharge{
			Name:   "STOP_PRICE",
			Type:   model.ExtraChargeTypeFixed,
			Amount: amount,
		})
	} else {
		extraCharge = req.ExtraCharges
	}

	var totalRoadFee float64

	for i := range deliveryPrices {
		totalRoadFee += deliveryPrices[i].RoadFee.Float64()
	}

	deliveryFeeSummary := model.DeliveryFeeSummary{
		RawBaseFee:   deliveryFee.BaseFee().Float64(),
		RoadFee:      totalRoadFee,
		ExtraCharges: extraCharge,
		AdditionalServiceFee: model.AdditionalServiceSummary{
			Total: req.AdditionalServiceDetail.Total,
		},
	}

	deliveryFeeSummary = deliveryFeeSummary.Sum()

	apiutil.OK(gctx, NewCalculateDeliveryFeeRes(req.Routes[0], deliveryFee.BaseFee(), extraCharge, deliveryFeeSummary, deliveryPrices...))
}

func (fapi *FoodProviderImpl) CreateOrder2(ctx *gin.Context) {
	var req CreateOrder2Request
	if err := ctx.ShouldBindJSON(&req); err != nil {
		InvalidJSONRequest(ctx)
		return
	}

	var header DeliveryHeader
	if err := ctx.ShouldBindHeader(&header); err != nil {
		apiutil.ErrBadRequest(ctx, &api.Error{
			Message:   err.Error(),
			Timestamp: api.Timestamp(time.Now()),
		})
		logrus.Warning("bind header err: ", err)
		return
	}

	order, err := fapi.getOrderInfoByIdempotencyLockKey(ctx.Request.Context(), req.QuoteID)
	if err != nil {
		logrus.Errorf("createOrder2 getOrderInfoByIdempotencyLockKey error : %v", err)
	}

	if order != nil {
		ctx.JSON(http.StatusCreated, orderToCreateOrderResponse(*order))
		return
	}

	quote, err := fapi.quote(ctx.Request.Context(), &req.QuoteRequest, header.Region)
	if err != nil {
		switch err {
		case repository.ErrInvalidDeliveryFee:
			apiutil.ErrBadRequest(ctx, &api.Error{
				Code:      "INVALID_DELIVERY_FEE",
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		case domainModel.ErrInvalidCouponType:
			apiutil.ErrBadRequest(ctx, &api.Error{
				Code:      "INVALID_COUPON_TYPE",
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		default:
			apiutil.ErrBadRequest(ctx, &api.Error{
				Code:      "INVALID_REGION",
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		}

		logrus.Error("unknown quote error: ", err)
		return
	}

	order, err = fapi.createOrderFromQuote(ctx, *quote, createOrderFromQuoteArgs{
		orderID:              req.OrderID,
		remarks:              req.Remarks,
		pointOfInterests:     req.PointOfInterests,
		restaurantAcceptedAt: req.RestaurantAcceptedAt,
	})
	if err != nil {
		apiutil.ErrBadRequest(ctx, &api.Error{Message: err.Error(), Timestamp: api.Timestamp(time.Now())})
	}

	ctx.JSON(http.StatusCreated, orderToCreateOrderResponse(*order))
}

func (fapi *FoodProviderImpl) CanDefer(gctx *gin.Context) {
	// TODO: Check and remove WithoutCancel if it's not needed
	ctx := context.WithoutCancel(gctx.Request.Context())
	var req CanDeferRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		InvalidJSONRequest(gctx)
		return
	}

	var header DeliveryHeader
	if err := gctx.ShouldBindHeader(&header); err != nil {
		apiutil.ErrBadRequest(gctx, &api.Error{
			Message:   err.Error(),
			Timestamp: api.Timestamp(time.Now()),
		})
		logrus.Warning("bind header err: ", err)
		return
	}

	matchRestaurantFirstMRThreshold := fapi.Cfg.AtomicOrderDBConfig.Get().MatchRestaurantFirstMRThreshold
	if req.ServiceType != model.ServiceFood {
		apiutil.OK(gctx, CanDeferResponse{
			CanDefer:                               false,
			MatchRestaurantFirstMatchRateThreshold: matchRestaurantFirstMRThreshold,
		})
		return
	}

	if len(req.Routes) != 0 {
		from := req.Routes[0]
		ds, err := fapi.AreaService.GetDistributeRegion(ctx, req.ServiceType, header.Region, service.NewLocation(from.Location.Lat, from.Location.Lng))
		if err != nil {
			InvalidRegion(gctx, errors.WithMessagef(err, "fail to get distribution region by area %s", header.Region))
			return
		}

		mrThresholdInThrottleZone, shouldOverride, err := fapi.DistributionConfigValidator.GetMatchRestaurantFirstMRThresholdInZone(ctx, from.Location, ds)
		if err == nil && shouldOverride {
			matchRestaurantFirstMRThreshold = mrThresholdInThrottleZone
		}
	}

	if req.MatchRate != nil && *req.MatchRate < matchRestaurantFirstMRThreshold {
		apiutil.OK(gctx, CanDeferResponse{
			CanDefer:                               false,
			MatchRestaurantFirstMatchRateThreshold: matchRestaurantFirstMRThreshold,
		})
		return
	}

	quote, err := fapi.quote(ctx, &req.QuoteRequest, header.Region)
	if err != nil {
		switch err {
		case repository.ErrInvalidDeliveryFee:
			apiutil.ErrBadRequest(gctx, &api.Error{
				Code:      "INVALID_DELIVERY_FEE",
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		case domainModel.ErrInvalidCouponType:
			apiutil.ErrBadRequest(gctx, &api.Error{
				Code:      "INVALID_COUPON_TYPE",
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		default:
			InvalidRegion(gctx, err)
		}

		logrus.Error("unknown quote error: ", err)
		return
	}

	order, err := fapi.newOrderFromQuote(ctx, *quote, req.OrderID, []domainModel.RestaurantDirection{}, req.PointOfInterests, nil)
	if err != nil {
		apiutil.ErrBadRequest(gctx, &api.Error{Message: err.Error(), Timestamp: api.Timestamp(time.Now())})
		return
	}

	region := order.DistributeRegions.DefaultRegion().String()
	serviceArea, err := fapi.ServiceAreaRepo.GetByRegion(ctx, region)
	if err != nil {
		apiutil.ErrBadRequest(gctx, &api.Error{Message: err.Error(), Timestamp: api.Timestamp(time.Now())})
		return
	}

	isEnableBlacklistWhitelistForMPATR := serviceArea.Distribution.EnableBlacklistWhitelistForMPATR

	isDeferPossible, err := fapi.DistributionConfigValidator.CanDefer(ctx, order)
	if err != nil {
		apiutil.ErrBadRequest(gctx, &api.Error{Message: err.Error(), Timestamp: api.Timestamp(time.Now())})
		return
	}

	if !isEnableBlacklistWhitelistForMPATR {
		isDeferPossible = order.IsDalianMP || isDeferPossible
	}

	apiutil.OK(gctx, CanDeferResponse{
		CanDefer:                               isDeferPossible,
		MatchRestaurantFirstMatchRateThreshold: matchRestaurantFirstMRThreshold,
	})
}

func (fapi *FoodProviderImpl) newOrderWithPredictionFeaturesFromQuote(ctx context.Context, quote *domainModel.Quote, pois []domainModel.PointOfInterest) (*domainModel.Order, error) {
	order, err := fapi.newOrderFromQuote(ctx, *quote, quote.QuoteID, []domainModel.RestaurantDirection{}, pois, nil)
	if err != nil {
		return nil, err
	}

	if order.ServiceType != domainModel.ServiceFood && order.ServiceType != domainModel.ServiceMart {
		return nil, nil
	}

	region := order.DistributeRegions.DefaultRegion().String()
	serviceArea, err := fapi.ServiceAreaRepo.GetByRegion(ctx, region)
	if err != nil {
		return nil, err
	}

	switchbackExperiments, err := fapi.DistributionExperimentPlatformClient.GetSwitchbackExperimentsWithConditions("", &exp.Condition{Name: "region", Value: region})
	if err != nil {
		logrus.Errorf("[newOrderWithPredictionFeaturesFromQuote] failed to get switchback experiment from EP for region %v", region)
	}

	if err := fapi.PredictionService.Predict(ctx, order, service.NewPredictSetting(
		serviceArea.Distribution.GetModelVersionWithSwitchbackExperiments(time.Now(), domainModel.PredictModel, switchbackExperiments),
		serviceArea.Distribution.SmartDistributionGoodnessBiasLevel,
	)); err != nil {
		return nil, err
	}

	return order, nil
}

func (fapi *FoodProviderImpl) getMatchRateThreshold(ctx context.Context, serviceType domainModel.Service, distributionRegions []model.RegionCode, location domainModel.Location) (float64, error) {
	switch serviceType {
	case domainModel.ServiceFood:
		threshold, shouldOverride, err := fapi.DistributionConfigValidator.GetMatchRestaurantFirstMRThresholdInZone(ctx, location, distributionRegions)
		if err == nil && shouldOverride {
			return threshold, nil
		}

		return fapi.Cfg.AtomicOrderDBConfig.Get().MatchRestaurantFirstMRThreshold, nil
	case domainModel.ServiceMart:
		return fapi.Cfg.AtomicOrderDBConfig.Get().MatchRestaurantFirstMRThresholdMart, nil
	default:
		return 0, fmt.Errorf("unsupported service type, %v", serviceType)
	}
}

func (fapi *FoodProviderImpl) setIfDeferrable(ctx context.Context, quote *domainModel.Quote, order *domainModel.Order, matchRate float64) error {
	threshold, err := fapi.getMatchRateThreshold(ctx, quote.ServiceType, quote.DistributeRegions, quote.Routes[0].Location)
	if err != nil {
		return err
	}

	if matchRate < threshold {
		if !(quote.ServiceType == model.ServiceMart && quote.StoreType == model.StoreTypeSP) {
			quote.Options.SwitchFlow = false
		}

		return nil
	}

	canDefer, err := fapi.DistributionConfigValidator.CanDefer(ctx, order)
	if err != nil {
		return err
	}

	serviceArea, err := fapi.ServiceAreaRepo.GetByRegion(ctx, order.DistributeRegions.DefaultRegion().String())
	if err != nil {
		return err
	}

	if !serviceArea.Distribution.EnableBlacklistWhitelistForMPATR {
		canDefer = order.IsDalianMP || canDefer
	}

	if canDefer {
		quote.Options.SwitchFlow = true
		quote.Options.CanDefer = true
	}

	return nil
}

func (fapi *FoodProviderImpl) QuoteOrder(gctx *gin.Context) {
	// TODO: Check and remove WithoutCancel if it's not needed
	ctx := context.WithoutCancel(gctx.Request.Context())
	var req QuoteRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		InvalidJSONRequest(gctx)
		return
	}

	var header DeliveryHeader
	if err := gctx.ShouldBindHeader(&header); err != nil {
		apiutil.ErrBadRequest(gctx, &api.Error{
			Message:   err.Error(),
			Timestamp: api.Timestamp(time.Now()),
		})
		logrus.Warning("bind header err: ", err)
		return
	}

	previousQuote, err := fapi.QuoteService.Find(ctx, req.QuoteID)
	if err != nil && !errors.Is(err, mongodb.ErrDataNotFound) {
		logx.Error().Context(ctx).Err(err).Str(logutil.QuoteID, req.QuoteID).Msg("find previous quote error")
	}

	quote, err := fapi.quote(ctx, &req, header.Region)
	if err != nil {
		switch err {
		case repository.ErrInvalidDeliveryFee:
			apiutil.ErrBadRequest(gctx, &api.Error{
				Code:      "INVALID_DELIVERY_FEE",
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		case domainModel.ErrInvalidCouponType:
			apiutil.ErrBadRequest(gctx, &api.Error{
				Code:      "INVALID_COUPON_TYPE",
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		default:
			apiutil.ErrBadRequest(gctx, &api.Error{
				Code:      "INVALID_REGION",
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		}

		logrus.Error("unknown quote error: ", err)
		return
	}

	order, err := fapi.newOrderWithPredictionFeaturesFromQuote(ctx, quote, req.PointOfInterests)
	if err != nil {
		logx.Error().Err(err).Msgf("cannot create order during quote: %v", quote.QuoteID)
	}

	matchRate, matchRateErr := fapi.MatchRateService.GetMatchRate(ctx, quote.ServiceType, quote.Routes[0].Location)
	if matchRateErr != nil {
		logx.Warnf(ctx, matchRateErr, "cannot get match rate for quote: %v", quote.QuoteID)
	}
	quote.MatchRate = matchRate

	isQuoteMP := quote.Options.MpID != ""
	isQuoteEvaluateCanDefer := req.Options.IsQuoteEvaluateCanDefer && (quote.ServiceType != model.ServiceMart || req.Options.CanDefer)

	if isQuoteEvaluateCanDefer && matchRateErr == nil && order != nil {
		if err := fapi.setIfDeferrable(ctx, quote, order, matchRate); err != nil {
			logx.Warnf(ctx, err, "cannot evaluate defer for quote: %v", quote.QuoteID)
		}

		if isQuoteMP && quote.Options.CanDefer {
			quote.Options.LockDuration = 0
		}

		if isQuoteMP && !quote.Options.CanDefer {
			quote.Options.MpDeferUntil = nil
		}
	}

	if isQuoteEvaluateCanDefer && matchRateErr != nil {
		quote.Options.CanDefer = false

		if isQuoteMP && !quote.Options.CanDefer {
			quote.Options.SwitchFlow = false
		}
	}

	if !isQuoteEvaluateCanDefer {
		quote.Options.CanDefer = req.Options.CanDefer
	}

	var completionTime *CompletionTime
	if order != nil && order.Prediction != nil && (order.ServiceType == model.ServiceFood || order.ServiceType == model.ServiceMart) {
		ae, aeError := fapi.UWTErrorService.GetAbsoluteError(ctx, order.DistributeRegions.DefaultRegion().String(), order.ServiceType, order.GetDeliveryMode(), uwterror.ErrorSourcePredict)
		if aeError != nil {
			logx.Warnf(ctx, aeError, "cannot get absolute error for: %v", order.QuoteID)
		}

		serviceType := order.ServiceType.String()

		if aeError == nil {
			uwtMin, uwtMax := estimateUserWaitingTime(time.Duration(order.Prediction.EstimatedUserWaitingTimeSecond)*time.Second, EstimateUserWaitingTimeSetting{
				MeanAbsoluteError: ae.Avg,
				P85AbsoluteError:  ae.P85,
				UWTLowerLimit:     fapi.Cfg.AtomicOrderDBConfig.Get().EstimateCompletionTimeUWTLowerLimit[serviceType],
				LowerLimit:        fapi.Cfg.AtomicOrderDBConfig.Get().EstimateCompletionTimeLowerLimit[serviceType],
				UpperLimit:        fapi.Cfg.AtomicOrderDBConfig.Get().EstimateCompletionTimeUpperLimit[serviceType],
				AcceptableRange:   fapi.Cfg.AtomicOrderDBConfig.Get().EstimateCompletionTimeAcceptableRange[serviceType],
			})

			completionTime = &CompletionTime{
				Min: order.UserPlacedTime.Add(uwtMin),
				Max: order.UserPlacedTime.Add(uwtMax),
			}
		}
	}

	quote.EvaluateAutoStart()

	if err := fapi.QuoteService.CreateOrUpdateQuote(ctx, quote); err != nil {
		apiutil.ErrBadRequest(gctx, &api.Error{
			Code:      "INVALID_REGION",
			Message:   err.Error(),
			Timestamp: api.TimestampNow(),
		})
		return
	}

	isDeliveryFeeChanged := quote.IsDeliveryFeeChanged(previousQuote)

	apiutil.OK(gctx, quoteToResponse(quote, req.ExtraCharges, matchRate, completionTime, isDeliveryFeeChanged))
}

func (fapi *FoodProviderImpl) getOnTopFareByRestaurantID(ctx context.Context, restaurant domainModel.Stop, serviceType domainModel.Service) ([]domainModel.OnTopFare, error) {
	onTopRes, err := fapi.OnTopFareRepo.GetOnTopFareByRestaurantID(ctx, restaurant.ID, serviceType.String())
	if err != nil {
		return nil, err
	}

	otr := make([]domainModel.OnTopFare, 0)
	if len(onTopRes) > 0 {
		for _, item := range onTopRes {
			if len(item.Restaurants) > 0 {
				for _, rt := range item.Restaurants {
					if rt.RestaurantId == restaurant.ID {
						otr = append(otr, item)
					}
				}
			} else {
				otr = append(otr, item)
			}
		}
	}
	return otr, nil
}

func (fapi *FoodProviderImpl) GetOnTopScheme(ctx context.Context, q domainModel.Quote, regions domainModel.DistributeRegions, time time.Time) ([]domainModel.OnTopScheme, []domainModel.OnTopScheme) {
	var ots []domainModel.OnTopFare
	var onTopScheme []domainModel.OnTopScheme
	var experimentalOnTopScheme []domainModel.OnTopScheme
	restaurant := q.Routes[0]

	if q.ServiceType == domainModel.ServiceFood || q.ServiceType == domainModel.ServiceMart {
		restaurantOntops, err := fapi.getOnTopFareByRestaurantID(ctx, restaurant, q.ServiceType)
		if err != nil {
			logrus.Error("makeOnTopFare GetOnTopFareByRestaurantID error: ", err)
			return []domainModel.OnTopScheme{}, []domainModel.OnTopScheme{}
		}
		ots = append(ots, restaurantOntops...)
	}

	onTopLoc, err := fapi.OnTopFareRepo.GetOnTopFareByLocation(ctx, restaurant.Location, q.ServiceType.String(), regions.DefaultRegion())
	if err != nil {
		logrus.Error("makeOnTopFare GetOnTopFareByLocation error: ", err)
		return []domainModel.OnTopScheme{}, []domainModel.OnTopScheme{}
	}

	ots = append(ots, onTopLoc...)

	var totalDistance types.Distance
	var experimentalTotalDistance types.Distance
	for i := 1; i < len(q.Routes); i++ {
		totalDistance += q.Routes[i].Distance
		experimentalTotalDistance += q.Routes[i].ExperimentalDistance
	}

	for _, ot := range ots {
		if (q.ServiceType == domainModel.ServiceMessenger && (!ot.IsDistanceScheme() && !ot.IsFlatRateScheme())) ||
			ot.IsPickupDistanceScheme() || ot.IsInstallmentScheme() {
			continue
		}

		amount, bundleAmount, experimentalAmount, experimentalBundleAmount, c, bc := ot.CalculateOnTopAmount(time, q, totalDistance, experimentalTotalDistance, false)
		if amount == 0.0 && bundleAmount == 0.0 && c == 0 && bc == 0 {
			continue
		}

		onTopScheme = append(onTopScheme, domainModel.OnTopScheme{
			ID:               ot.ID,
			Amount:           amount,
			Scheme:           ot.Scheme,
			BundleAmount:     bundleAmount,
			Name:             ot.Name,
			Coin:             c,
			BundleCoin:       bc,
			IncentiveSources: ot.IncentiveSources,
		})
		experimentalOnTopScheme = append(experimentalOnTopScheme, domainModel.OnTopScheme{
			ID:               ot.ID,
			Amount:           experimentalAmount,
			Scheme:           ot.Scheme,
			BundleAmount:     experimentalBundleAmount,
			Name:             ot.Name,
			IncentiveSources: ot.GetIncentiveSources(),
		})
	}

	return onTopScheme, experimentalOnTopScheme
}

func (fapi *FoodProviderImpl) quote(ctx context.Context, req *QuoteRequest, region string) (*domainModel.Quote, error) {
	if err := req.Validate(); err != nil {
		return nil, err
	}
	from := req.Routes[0]
	ds, err := fapi.AreaService.GetDistributeRegion(ctx, req.ServiceType, region, service.NewLocation(from.Location.Lat, from.Location.Lng))
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to get distribution region by area %s", region)
	}

	distRegions := domainModel.DistributeRegions(ds)
	calculator, priceScheme, err := fapi.DeliveryFeeSvc.GetDeliveryFeeCalculator(ctx, req.ServiceType, distRegions.DefaultRegion().String(), req.Routes[0].Location, service.ToPriceSchemeKey(req.PriceScheme))
	if err != nil {
		return nil, errors.WithMessagef(err, "fail to get delivery fee region : %s", distRegions.DefaultRegion().String())
	}

	distance := types.Distance(0)
	experimentalDistance := types.Distance(0)
	isExperimentalSuccess := fapi.Cfg.AtomicOrderDBConfig.Get().EnableExperimentalOsrm
	for i := 0; i < len(req.Routes)-1; i++ {
		dist, duration, waypoints, err := DistanceDurationBetweenRoutes(ctx, fapi.MapService, req.Routes[i].Location, req.Routes[i+1].Location, req.UseGoogleMap)
		if err != nil {
			return nil, err
		}

		var experimentalDist types.Distance
		var experimentalDuration float64
		var experimentalWaypoints []domainModel.MapWaypoint
		if isExperimentalSuccess {
			experimentalDist, experimentalDuration, experimentalWaypoints, err = DistanceDurationBetweenRoutes(ctx, fapi.ExperimentalMapService, req.Routes[i].Location, req.Routes[i+1].Location, req.UseGoogleMap)
			if err != nil {
				logrus.Errorf("[ExperimentalOsrm] unable to calculate experimental distance, quote id: %s, err: %v", req.QuoteID, err)
				isExperimentalSuccess = false
			}
		}

		req.Routes[i+1].Distance = dist
		if waypoints != nil && len(waypoints) == 2 {
			req.Routes[i+1].SourceSnappingDistance = types.Distance(waypoints[0].Distance)
			req.Routes[i+1].TargetSnappingDistance = types.Distance(waypoints[1].Distance)
		}
		req.Routes[i+1].EstimatedDeliveryTime = domainModel.DurationSecond(duration)
		req.Routes[i+1].ExperimentalDistance = experimentalDist
		req.Routes[i+1].ExperimentalEstimatedDeliveryTime = domainModel.DurationSecond(experimentalDuration)
		if experimentalWaypoints != nil && len(experimentalWaypoints) == 2 {
			req.Routes[i+1].ExperimentalSourceSnappingDistance = types.Distance(experimentalWaypoints[0].Distance)
			req.Routes[i+1].ExperimentalTargetSnappingDistance = types.Distance(experimentalWaypoints[1].Distance)
		}

		distance += dist
		experimentalDistance += experimentalDist
	}

	if req.PayAtStop == -1 || req.PayAtStop >= len(req.Routes) || (req.Options.RoundTrip && req.PayAtStop == len(req.Routes)-1) {
		return nil, ErrRequiredCollectPayment
	}

	deliveryFee := calculator.Calculate(distance, !req.RevenueAgentModel, priceScheme)

	var experimentalDeliveryFee model.DeliveryFee
	if isExperimentalSuccess {
		experimentalDeliveryFee = calculator.Calculate(experimentalDistance, !req.RevenueAgentModel, priceScheme)
	}

	req.Routes[req.PayAtStop].CollectPayment = true
	req.Routes[req.PayAtStop].PriceSummary = *domainModel.NewPriceSummary(from.ItemsPrice, from.ItemsPriceBeforeDiscount, deliveryFee, experimentalDeliveryFee, req.PaymentMethod)

	newCharges := make(ExtraChargeListReq, 0)
	for _, ec := range req.ExtraCharges.ExtraCharges() {
		if ec.Type == model.ExtraChargeTypeFixed && ec.Name == "ROUND_TRIP" {
			amount := calMessengerStopPrice(len(req.Routes), priceScheme.MessengerFixPricePerStop())
			newCharges = append(newCharges, ExtraChargeReq{
				Name:   "STOP_PRICE",
				Type:   model.ExtraChargeTypeFixed,
				Amount: amount,
			})
		} else {
			newCharges = append(newCharges, ExtraChargeReq{
				Name:   ec.Name,
				Type:   ec.Type,
				Amount: ec.Amount,
			})
		}
	}
	req.ExtraCharges = newCharges

	srvArea, err := fapi.ServiceAreaRepo.GetByRegion(ctx, distRegions.DefaultRegion().String())
	if err != nil {
		return nil, err
	}

	if srvArea.Distribution.OverrideOrderMPDeferUntil {
		req.Options.MpDeferUntil = nil
	}

	q, err := req.Quote(distRegions, deliveryFee, experimentalDeliveryFee)
	if err != nil {
		return nil, err
	}

	q.UserRegion = region
	q.SpecialEvent = req.SpecialEvent
	q.StoreAcceptHalfHalf = req.StoreAcceptHalfHalf
	q.RestaurantChainID = req.RestaurantChainID
	q.MapProvider = MapProvider(req)
	q.Routes[q.PayAtStop].PriceSummary.DeliveryFee.PriceSchemeRefId = priceScheme.Key()
	q.Routes[q.PayAtStop].PriceSummary.ExperimentalDeliveryFee.PriceSchemeRefId = priceScheme.Key()
	q.IsExperimentalSuccess = isExperimentalSuccess

	if !fapi.Cfg.DisabledPreStampDistributionZoneIDs {
		zones, err := fapi.ThrottledDispatchDetailRepo.FindManyFromLocation(ctx, req.Routes[0].Location, distRegions, repository.WithReadSecondaryPreferred)
		if err != nil {
			logrus.Errorf("new quote id: %v, FindManyFromLocation err: %v", req.QuoteID, err.Error())
		} else {
			zoneIDs := make([]primitive.ObjectID, 0, len(zones))
			for _, zone := range zones {
				zoneIDs = append(zoneIDs, zone.ZoneID)
			}
			q.DistributionZoneIDs = zoneIDs
		}
	}

	onTopSchemes, experimentalOnTopSchemes := fapi.GetOnTopScheme(ctx, *q, distRegions, timeutil.BangkokNow())
	if onTopSchemes != nil {
		q.Routes[q.PayAtStop].PriceSummary.DeliveryFee.OnTopScheme = onTopSchemes
		q.Routes[q.PayAtStop].PriceSummary.ExperimentalDeliveryFee.OnTopScheme = experimentalOnTopSchemes
	}
	q.SetUserPriceInterventionSchemes(req.UserOnTops.Schemes())

	fapi.setDriverPriceInterventionOnTopIfPossible(ctx, req, q, from)
	fapi.setPointsInEachRoute(ctx, q)

	q.SetAdditionalService(req.AdditionalServiceFee.ToAdditionalService())

	q.IsEnabledAgentModelCommissionAndTaxV2 = fapi.OrderServiceV2.IsEnabledAgentModelCommissionAndTaxV2(ctx, q.ServiceType)

	if keywords := fapi.Cfg.AtomicOrderDBConfig.Get().MartInvalidItemGroups; q.ServiceType == model.ServiceMart && len(keywords) > 0 {
		q.Routes[0].PickingItems = martItemsWithDefaultGroup(q.Routes[0].PickingItems, keywords)
		q.Routes[q.PayAtStop].DeliveryItems = martItemsWithDefaultGroup(q.Routes[q.PayAtStop].DeliveryItems, keywords)
	}

	if q.ServiceType == model.ServiceMart && q.StoreType == domainModel.StoreTypeRMS {
		q.Routes[0].PickingItems = martRMSItemsWithoutDirtyFields(q.Routes[0].PickingItems)
		q.Routes[q.PayAtStop].DeliveryItems = martRMSItemsWithoutDirtyFields(q.Routes[q.PayAtStop].DeliveryItems)
	}

	q.Summary(GetCommissionRate(fapi.Cfg, req.ServiceType.String()), fapi.Cfg.AtomicOrderDBConfig.Get().WithHoldingTax)

	return q, nil
}

func (fapi *FoodProviderImpl) setDriverPriceInterventionOnTopIfPossible(ctx context.Context, req *QuoteRequest, q *domainModel.Quote, from domainModel.Stop) {
	if fapi.featureflagService.IsEnabledWithDefaultTrue(ctx, featureflag.IsQuotePIPOnTopEnabled.Name) &&
		q.ServiceType == model.ServiceBike {
		pipReq := toGetEstimatedUserFareOnTopRequest(from, req.Routes[q.PayAtStop])
		response, err := fapi.priceInterventionClient.GetEstimatedUserFareOnTopRequest(ctx, pipReq)
		if err != nil {
			logx.Error().Err(err).Str("quote_id", q.QuoteID).Msg("failed to get estimated user fare ontop")
			return
		}
		q.SetDriverPriceInterventionOnTop(response)
	}
}

func (fapi *FoodProviderImpl) setPointsInEachRoute(ctx context.Context, q *domainModel.Quote) {
	if q.ServiceType != domainModel.ServiceBike {
		return
	}
	if fapi.Cfg.AtomicOrderDBConfig.Get().DisableSavePoiPoints {
		return
	}
	for i := range q.Routes {
		pointID := q.Routes[i].PoiPointID
		if pointID == "" {
			continue
		}
		poiResp, err := fapi.userPolygonServiceClient.GetPOIByPointID(ctx, &polygonv2.GetPOIByPointIDRequest{PointId: pointID})
		if err != nil {
			logx.Error().Err(err).
				Str("quote_id", q.QuoteID).
				Str("point_id", pointID).
				Msg("unable to get poi by point id")
			continue
		}
		q.Routes[i].Point = polygon.ConvertToPoint(poiResp.GetPoi(), pointID)
	}
}

func toGetEstimatedUserFareOnTopRequest(from domainModel.Stop, to domainModel.Stop) *transportationV1.GetEstimatedUserFareOntopRequest {
	return &transportationV1.GetEstimatedUserFareOntopRequest{
		PickupLocation:       &transportationV1.Location{Lat: utils.Float64ToProtoDouble(from.Location.Lat), Lng: utils.Float64ToProtoDouble(from.Location.Lng)},
		DestinationLocations: []*transportationV1.Location{{Lat: utils.Float64ToProtoDouble(to.Location.Lat), Lng: utils.Float64ToProtoDouble(to.Location.Lng)}},
		VehicleCategoryFares: []*transportationV1.VehicleCategoryFare{
			{
				VehicleCategory: transportationV1.VehicleCategory_VEHICLE_CATEGORY_2WHEEL,
				BasePrice:       utils.Float64ToProtoDouble(to.PriceSummary.DeliveryFee.BaseFee),
			},
		},
		Eta:      utils.Float64ToProtoDouble(float64(to.EstimatedDeliveryTime)),
		Distance: utils.Float64ToProtoDouble(to.Distance.Float64()),
	}
}

func calMessengerStopPrice(routesNum int, price float64) float64 {
	return float64(routesNum-2) * price
}

func quoteToResponse(q *domainModel.Quote, ecs ExtraChargeListReq, matchRate float64, completionTime *CompletionTime, isDeliveryFeeChanged bool) *QuoteResponse {
	extraCharges := make([]model.ExtraCharge, 0)
	for _, e := range ecs {
		extraCharges = append(extraCharges, model.ExtraCharge{
			Name:   e.Name,
			Type:   e.Type,
			Amount: e.Amount,
		})
	}

	return &QuoteResponse{
		QuoteID:                 q.QuoteID,
		Distance:                q.Distance,
		DeliveryFee:             q.Routes[q.PayAtStop].PriceSummary.DeliveryFee,
		ExtraCharges:            extraCharges,
		EstimateDurationSec:     q.Routes[q.PayAtStop].EstimatedDeliveryTime,
		CanDefer:                q.Options.CanDefer,
		SwitchFlow:              q.Options.SwitchFlow,
		MatchRate:               matchRate,
		EstimatedCompletionTime: completionTime,
		IsDeliveryFeeChanged:    isDeliveryFeeChanged,
	}
}

func (fapi *FoodProviderImpl) setRestaurantDirection(o *domainModel.Order, dirs []domainModel.RestaurantDirection) error {
	// TODO: this is for backward compatibility, remove code in this block when order's stop info is migrated
	if len(o.Routes) < 1 {
		return nil
	}

	for i, route := range o.Routes {
		for _, dir := range dirs {
			if dir.RestaurantID == route.ID {
				info := domainModel.CastStopInfoFood(&o.Routes[i].Info.StopInfo)
				info.RestaurantDirection = dir
			}
		}
	}
	return nil
}

func (fapi *FoodProviderImpl) CreateOrder(ctx *gin.Context) {
	var req CreateOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		InvalidJSONRequest(ctx)
		return
	}

	reqCtx := ctx.Request.Context()

	order, err := fapi.getOrderInfoByIdempotencyLockKey(reqCtx, req.QuoteID)
	if err != nil {
		logrus.Errorf("createOrder getOrderInfoByIdempotencyLockKey error : %v", err)
	}

	if order != nil {
		ctx.JSON(http.StatusCreated, orderToCreateOrderResponse(*order))
		return
	}

	q, err := fapi.QuoteService.Find(reqCtx, req.QuoteID, repository.WithReadPrimary)
	if err != nil {
		ResolveFindErr(err)(ctx)
		return
	}

	if q.ServiceType == model.ServiceFood ||
		q.ServiceType == model.ServiceMart {
		if len(req.Routes) == len(q.Routes) {
			for index, reqRoute := range req.Routes {
				if reqRoute.Pauses != nil {
					q.Routes[index].Pauses = reqRoute.Pauses
				}
			}
		}
	}

	if len(req.Discounts) > 0 {
		q.SetDiscount(req.Discounts.Discounts())
		q.ExperimentalSetDiscount(req.Discounts.Discounts())
		q.Summary(GetCommissionRate(fapi.Cfg, q.ServiceType.String()), fapi.Cfg.AtomicOrderDBConfig.Get().WithHoldingTax)
	}

	if q.ServiceType == model.ServiceBike && q.IsZeroOutstandingDeliveryFee() && q.IsQRPaymentDeliveryFee() {
		q.OmitQRPaymentPause()

		q.Routes[q.PayAtStop].PriceSummary.DeliveryFee.QRPromptPayInfo = model.QRPromptPayInfo{
			Status:          model.QRPromptPayStatusResolvedByUser,
			IsUserResolveQR: true,
		}
	}

	order, err = fapi.createOrderFromQuote(reqCtx, *q, createOrderFromQuoteArgs{
		orderID:              req.OrderID,
		remarks:              req.Remarks,
		pointOfInterests:     req.PointOfInterests,
		restaurantAcceptedAt: req.RestaurantAcceptedAt,
		tip:                  &req.Tip,
	})
	if err != nil {
		apiutil.ErrBadRequest(ctx, &api.Error{Message: err.Error(), Timestamp: api.Timestamp(time.Now())})
		return
	}

	ctx.JSON(http.StatusCreated, orderToCreateOrderResponse(*order))
}

func (fapi *FoodProviderImpl) setRequireDeliveringPhoto(_ context.Context, svc *domainModel.ServiceArea, order *domainModel.Order) {
	if svc.IsRequiredPhotoOfJacketAndBoxes(order.ServiceType) && !fapi.Cfg.AtomicOrderDBConfig.Get().DisableVOSPhoto {
		order.IsRequireDeliveringPhotoURL = true
		for i := 1; i < len(order.Routes); i++ { // We don't want to set pause in routes[0].
			order.Routes[i].AddPauses(domainModel.PauseDeliveringPhoto)
		}
	}
}

func isUserRequiredDoNotDisturb(whiteListUsers []string, order domainModel.Order) bool {
	if order.ServiceType == model.ServiceFood {
		if order.IsOneTripOrder() {
			whitelistUsersSet := types.NewStringSet(whiteListUsers...)
			if whitelistUsersSet.Has(order.UserID) {
				return true
			}
		}
	}
	return false
}

type createOrderFromQuoteArgs struct {
	orderID              string
	remarks              []domainModel.RestaurantDirection
	pointOfInterests     []domainModel.PointOfInterest
	restaurantAcceptedAt *time.Time
	tip                  *model.TipRecord
}

func (fapi *FoodProviderImpl) createOrderFromQuote(ctx context.Context, q domainModel.Quote, args createOrderFromQuoteArgs) (*domainModel.Order, error) {
	// lock an order by idempotencyKey in period time based on config
	ok, err := fapi.Locker.Lock(ctx, locker.CreateOrderByIdempotencyLockKey(q.QuoteID), locker.Option(func(conf locker.Config) locker.Config {
		conf.Expiration = fapi.Cfg.CreateOrderIdempotencyExpirationDuration
		return conf
	}))
	if err == nil {
		if !ok {
			return nil, fmt.Errorf("order was locked by idempotencyKey %s", q.QuoteID)
		}
	} else {
		// in case of cannot perform locking we wont break the next action
		safe.SentryErrorMessage(fmt.Sprintf("creareOrder(idempotence) cannot perform locking err: %v", err), safe.WithInfo("quoteId", q.QuoteID))
	}

	order, err := fapi.newOrderFromQuote(ctx, q, args.orderID, args.remarks, args.pointOfInterests, args.tip)
	if err != nil {
		return order, err
	}

	if args.restaurantAcceptedAt != nil && !args.restaurantAcceptedAt.IsZero() {
		order.History[string(model.StatusRestaurantAccepted)] = *args.restaurantAcceptedAt
	}

	hasAnotherMPOrder := true
	hasNoAnotherMPOrder := false
	otherMPOrder, err := fapi.OrderService.GetOtherActiveMP(ctx, *order)
	if err != nil {
		if errors.Is(err, repository.ErrNotFound) {
			order.HasAnotherMPOrder = &hasNoAnotherMPOrder
		} else {
			logrus.Infof("couldn't GetOtherActiveMP: %v\n", err)
		}
	}
	if err == nil && otherMPOrder != nil {
		order.HasAnotherMPOrder = &hasAnotherMPOrder
	}

	unlockIdempotencyKey := func() {
		// unlock the key if create an order failed to let user create an order again without locking
		unlockErr := fapi.Locker.Unlock(ctx, locker.CreateOrderByIdempotencyLockKey(q.QuoteID))
		if unlockErr != nil {
			// in case of an error is happening, we should manual remove the key from redis to prevent breaking creating an order
			safe.SentryErrorMessage(fmt.Sprintf("creareOrder(idempotence) cannot perform unlocking err: %v", unlockErr), safe.WithInfo("quoteId", q.QuoteID))
		}
	}

	if order.ShouldTranslateMemo() {
		if fapi.featureflagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsOrderMemoTranslationAsyncEnabled.Name) {
			order.TranslationInfo.ShouldTranslateMemo = true

			newCtx := context.WithoutCancel(ctx)
			safe.GoFuncWithCtx(newCtx, func() {
				translatedMemo, translationStatus := fapi.autoTranslateMemoV2(newCtx, *order)
				if err := fapi.OrderService.SetMemoTranslation(
					newCtx,
					order.OrderID,
					order.GetPayAtStop(),
					translatedMemo,
					translationStatus,
				); err != nil {
					logx.Error().Err(err).Context(newCtx).Str(logutil.OrderID, order.OrderID).Str(logutil.Method, "SetMemoTranslation").Msg("cannot update order memo translation")
				}
			})
		} else {
			fapi.autoTranslateMemo(ctx, order)
		}
	}

	if q.IsSwitchFlow() {
		logrus.Infof("switch flow quote: %v\n", q.QuoteID)
		if err := fapi.createSwitchFlowOrder(ctx, order); err != nil {
			unlockIdempotencyKey()
			if errors.Is(err, ErrReCreateSwitchFlowOrders) {
				logrus.Infof("skip to distribute switch-flow order becuase recreate flow was disabled")
				return order, nil
			} else {
				logrus.Errorf("createOrder switch flow error : %v", err)
				return order, err
			}
		}
	} else {
		if err := fapi.OrderService.CreateOrder(ctx, order); err != nil {
			unlockIdempotencyKey()
			logrus.Errorf("createOrder error : %v", err)
			return order, err
		}
	}

	if otherMPOrder != nil {
		goRoutineCtx := safe.NewContextWithSameWaitGroup(ctx)
		safe.GoFuncWithCtx(goRoutineCtx, func() {
			if err := fapi.OrderService.SetHasAnotherMPOrder(ctx, otherMPOrder.OrderID, order.HasAnotherMPOrder); err != nil {
				logrus.Errorf("cannot update HasAnotherMPOrder: %v", err)
			}
		})
	}

	if err := fapi.OrderDistributionEventManager.PublishCreated(ctx, *order, timeutil.BangkokNow()); err != nil {
		logrus.Errorf("cannot publish order created distribution event: %v", err)
	}

	if err := fapi.DistributionService.PublishDistributeOrderEvent(ctx, order.OrderID); err != nil {
		logx.Error().Err(err).Str(logutil.OrderID, order.OrderID).Msg("cannot publish distribution event")
		return order, err
	}

	if err := repApi.PublishRepEventOrderCreated(fapi.Rep, order); err != nil {
		return order, err
	}

	PublishEventOrderCreate(ctx, order, fapi.Bus)

	goRoutineCtx := safe.NewContextWithSameWaitGroup(ctx)
	safe.GoFuncWithCtx(goRoutineCtx, func() {
		jsonBytes, err := json.Marshal(order)
		if err == nil {
			if ok := fapi.Locker.SetState(goRoutineCtx, locker.GetOrderInfoByIdempotencyLockKey(q.QuoteID), string(jsonBytes), fapi.Cfg.CreateOrderIdempotencyExpirationDuration); !ok {
				logrus.Errorf("createOrder setState GetOrderInfoByIdempotencyLockKey quoteID: %s", q.QuoteID)
			}
		}
	})

	return order, nil
}

func (fapi *FoodProviderImpl) newOrderFromQuote(ctx context.Context, q domainModel.Quote, orderID string, remarks []domainModel.RestaurantDirection, pointOfInterests []domainModel.PointOfInterest, tip *model.TipRecord) (*domainModel.Order, error) {
	order := domainModel.NewOrder(q, orderID)
	if err := fapi.setRestaurantDirection(order, remarks); err != nil {
		logrus.Errorf("createOrder setRestaurantDirection error : %v", err)
		return order, err
	}

	order.PointOfInterests = pointOfInterests

	if len(order.DistributeRegions) == 0 {
		return order, errors.New("distribute regions is empty")
	}

	order.Region = order.DistributeRegions[0]
	reg := order.DistributeRegions[0]
	svc, err := fapi.ServiceAreaRepo.GetByRegion(ctx, reg.String())
	if err != nil {
		err = errors.WithMessagef(err, "fail to get service area by region: %s", reg.String())
		return order, err
	}

	if svc.WhitelistUsersEnabled {
		if isUserRequiredDoNotDisturb(svc.WhitelistUsers, *order) {
			order.Routes[1].DoNotDisturb = true
		}
	}

	if !order.IsRushOrder() && !order.IsNoRushOrder() {
		if types.NewStringSet(svc.Distribution.RushExperimentWhitelist...).Has(order.UserID) {
			order.Metadata[domainModel.MetadataDeliveryMode] = domainModel.OrderDeliveryModeRush
			logrus.WithField("prioritize_by_dalian", order.IsRushPrioritizedByDalian).Infof("[DeliveryModeExperiment] order %v is overriden as rush order since user %v is in whitelist", order.OrderID, order.UserID)
		} else if types.NewStringSet(svc.Distribution.NoRushExperimentWhitelist...).Has(order.UserID) {
			order.Metadata[domainModel.MetadataDeliveryMode] = domainModel.OrderDeliveryModeNoRush
			logrus.Infof("[DeliveryModeExperiment] order %v is overriden as no-rush order since user %v is in whitelist", order.OrderID, order.UserID)
		}
	}

	if order.IsRushOrder() && svc.Distribution.RushDalianEnabled {
		order.IsRushPrioritizedByDalian = true
	}

	fapi.setRequireDeliveringPhoto(ctx, svc, order)

	if fapi.Cfg.EnableRevampedStatus || order.ServiceType.IsRevampedStatus() {
		order.RevampedStatus = true
	}

	if tip != nil && tip.Amount > 0 {
		if tip.ID == "" {
			logx.Warn().Str("method", "newOrderFromQuote").
				Msgf("received an empty tip id for an order [%s]", order.OrderID)
		}
		order.AddTip(tip.ID, tip.Amount)
	}

	hasMPID := order.Options.MpID != ""
	if order.Options.CanDefer {
		if hasMPID {
			order.IsDalianMP = true
		}
	} else {
		canDalianMP, err := fapi.DistributionConfigValidator.CanDalianMP(ctx, order)
		if err != nil {
			logrus.Errorf("createOrder determining candefer error : %v, orderID: %s", err, order.OrderID)
			return order, err
		}
		if hasMPID {
			order.IsDalianMP = canDalianMP
		}
	}

	return order, nil
}

// Auto translate english memo to thai memo
func (fapi *FoodProviderImpl) autoTranslateMemo(ctx context.Context, order *domainModel.Order) {
	if order.ServiceType != domainModel.ServiceFood {
		return
	}

	if order.IsEnglishMemo() {
		originalMemo := order.Routes[order.GetPayAtStop()].Memo
		translatedMemo := fapi.translationService.TranslateENToTH(ctx, originalMemo)

		if strings.TrimSpace(translatedMemo) != strings.TrimSpace(originalMemo) {
			order.Routes[order.GetPayAtStop()].MemoTH = translatedMemo
		}
	}
}

// Auto translate english memo to thai memo V2
func (fapi *FoodProviderImpl) autoTranslateMemoV2(ctx context.Context, order domainModel.Order) (string, model.TranslationStatus) {
	originalMemo := strings.TrimSpace(order.Routes[order.GetPayAtStop()].Memo)

	translatedMemo := strings.TrimSpace(fapi.translationService.TranslateENToTH(ctx, originalMemo))
	if translatedMemo == originalMemo {
		return "", model.TranslationFail
	}

	return translatedMemo, model.TranslationSuccess
}

func orderToCreateOrderResponse(o domainModel.Order) *CreateOrderResponse {
	summary := o.PriceSummary()
	return &CreateOrderResponse{
		OrderID:           o.OrderID,
		Distance:          o.Distance,
		DeliveryFee:       summary.DeliveryFee,
		ActualAssigningAt: o.ActualAssigningAt,
	}
}

func (fapi *FoodProviderImpl) ConfirmPrice(ctx *gin.Context) {
	orderID := OrderIDFromGinContext(ctx)
	order, err := FindOrder(ctx.Request.Context(), fapi.OrderService, orderID)
	if err != nil {
		ResolveFindErr(err)(ctx)
		return
	}
	backCompatRestaurantType(order)

	if order.Status == domainModel.StatusCanceled {
		apiutil.ErrBadRequest(ctx, ErrOrderCanceled())
		return
	}

	if order.Status == domainModel.StatusCompleted {
		apiutil.ErrBadRequest(ctx, ErrOrderCompleted())
		return
	}

	if err := TransitionToConfirmPrice(order); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err := fapi.OrderService.UpdateOrder(ctx, order); err != nil {
		apiutil.ErrInternalError(ctx, &api.Error{
			Code:      api.ERRCODE_INTERNAL_ERROR,
			Message:   err.Error(),
			Timestamp: api.TimestampNow(),
		})
		return
	}

	evt := service.EventOrderUpdated(orderID)
	if err := fapi.Notifier.Notify(ctx, []string{order.Driver}, evt, service.WithFirebase, service.WithSocketIO); err != nil {
		logrus.Errorf("Failed to notify driver. driver=%s event=%v err=%s", order.Driver, evt, err)
	}

	apiutil.OK(ctx, gin.H{})
}

func TransitionToConfirmPrice(order *model.Order) error {
	if !order.RevampedStatus && order.Status != domainModel.StatusWaitingUserConfirmPrice {
		return domainModel.ErrStatusNotAllowed
	}

	if order.RevampedStatus && len(order.Routes) != 0 && !order.Routes[0].Pauses[domainModel.PauseConfirmPrice] {
		return domainModel.ErrStatusNotAllowed
	}

	if order.RevampedStatus {
		delete(order.Routes[0].Pauses, domainModel.PauseConfirmPrice)
		return nil
	}

	_ = transitionOrderStatus(order)

	return nil
}

func (fapi *FoodProviderImpl) CancelOrder(ctx *gin.Context) {
	req := domainModel.CancelDetail{
		CancelledBy:        "LINEMAN",
		Source:             "USER",
		CancellationSource: model.SourceUSER,
	}
	orderID := ctx.Param("orderID")
	if err := fapi.Canceller.CancelOrder(ctx, orderID, req, NoopStatusValidator, NoopServiceValidator, nil, false); err != nil {
		_ = ctx.Error(err)
		return
	}
}

func (fapi *FoodProviderImpl) SystemCancelOrder(ctx *gin.Context) {
	req := domainModel.CancelDetail{
		CancelledBy:        "DELIVERY-SERVICE",
		Source:             "SYSTEM",
		CancellationSource: model.SourceOther,
	}
	orderID := ctx.Param("orderID")
	if err := fapi.Canceller.CancelOrder(ctx, orderID, req, SystemCancelStatusValidator, NoopServiceValidator, nil, false); err != nil {
		_ = ctx.Error(err)
		return
	}
}

func (fapi *FoodProviderImpl) RestaurantAccepted(gctx *gin.Context) {
	// NOTE: fixing LMF-12311, async flow got canceled by request context
	ctx := context.WithoutCancel(gctx.Request.Context())
	orderID := OrderIDFromGinContext(gctx)
	var req RestaurantAcceptedOrderRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		InvalidJSONRequest(gctx)
		return
	}

	bgCtx, wg := safe.CreateWaitGroupOnContext(ctx)
	ord, err := FindOrder(ctx, fapi.OrderService, orderID)
	if err != nil {
		switch err {
		case repository.ErrInvalidOrderID:
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
			return
		case repository.ErrNotFound:
			apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err))
			return
		default:
			apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, err))
			return
		}
	}
	if err := fapi.DriverService.TryLockDriver(ctx, ord.Driver,
		service.WithRetryAttempts(fapi.Cfg.RetryAttemptsRestaurantAccepted), service.WithRetryDelay(fapi.Cfg.RetryDelayRestaurantAccepted)); err != nil {
		logrus.Warnf("lock driver failed, RestaurantAccepted, driverId: %v, orderId: %v, err: %v", ord.Driver, ord.OrderID, err.Error())
		apiutil.ErrLocked(gctx, apiutil.NewFromError(apiutil.ErrCodeResourceLocked, err))
		return
	}
	defer safe.GoFunc(func() {
		defer fapi.DriverService.UnlockDriver(ctx, ord.Driver)
		wg.Wait()
	})

	res, err := fapi.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		order, err := FindOrder(sessCtx, fapi.OrderService, orderID)
		if err != nil {
			switch err {
			case repository.ErrInvalidOrderID:
				return domainModel.Order{}, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err)
			case repository.ErrNotFound:
				return domainModel.Order{}, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, err)
			default:
				return domainModel.Order{}, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, err)
			}
		}

		if order.IsSwitchFlow() {
			return domainModel.Order{}, ErrRestaurantAcceptedSwitchFlow
		}

		if order.Status == domainModel.StatusCanceled {
			return domainModel.Order{}, ErrOrderCanceled()
		}

		if order.Status == domainModel.StatusCompleted {
			return domainModel.Order{}, ErrOrderCompleted()
		}

		stopRestaurant := domainModel.CastStopInfoFood(&order.Routes[0].Info.StopInfo)

		backCompatRestaurantType(order)
		if len(order.Routes) != 0 && order.Autostart {
			return domainModel.Order{}, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.New("cannot update price for RMS order"))
		}

		if order.Status != domainModel.StatusDriverMatched {
			return domainModel.Order{}, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, domainModel.ErrStatusNotAllowed)
		}

		backCompatRestaurantType(order)
		transitionOrderStatus(order)
		stopRestaurant.EstimatedCookingTime = domainModel.DurationSecond(req.EstimatedCookingTimeInSecond)

		err = fapi.OrderService.UpdateOrder(sessCtx, order)
		if err != nil {
			switch err {
			case mongodb.ErrDataNotFound:
				return domainModel.Order{}, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, repository.ErrNotFound)
			default:
				// please note this intention to return pure mongo error without wrap error back to TxnHelper
				// since it's require mongo driver to retry this block of code if write conflict error occured (wit TransientTransactionError label)
				return domainModel.Order{}, err
			}
		}

		return *order, nil
	}, transaction.WithLabel("FoodProviderImpl.RestaurantAccepted"))
	if err != nil {
		if errors.Is(err, ErrRestaurantAcceptedSwitchFlow) {
			apiutil.OK(gctx, gin.H{})
			return
		}
		var apiErr *api.Error
		if errors.As(err, &apiErr) {
			switch {
			case apiErr.Code == "ORDER_CANCELED":
				apiutil.ErrBadRequest(gctx, apiErr)
			case apiErr.Code == "ORDER_COMPLETED":
				apiutil.ErrBadRequest(gctx, apiErr)
			case apiErr.Code == api.ERRCODE_INVALID_REQUEST:
				apiutil.ErrBadRequest(gctx, apiErr)
			case apiErr.Code == api.ERRCODE_SERVICE_INTERNAL_ERROR:
				apiutil.ErrInternalError(gctx, apiErr)
			case apiErr.Code == api.ERRCODE_NOT_FOUND:
				apiutil.ErrNotFound(gctx, apiErr)
			default:
				apiutil.ErrInternalError(gctx, apiErr)
			}
			return
		}
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, err))
		return
	}

	order, ok := res.(model.Order)
	if !ok {
		apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, fmt.Errorf("cast order type error"))
		return
	}

	if order.TripID != "" {
		trip, err := fapi.TripServices.OnOrderChanged(ctx, service.TripOrderEvent{
			TripID:              order.TripID,
			OrderID:             order.OrderID,
			OrderStatus:         order.Status,
			OrderLocation:       order.Routes[order.GetPayAtStop()].Location,
			OrderHeadTo:         order.HeadTo,
			OrderRevampedStatus: order.RevampedStatus,
		})
		if err != nil {
			apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, err)
			return
		}
		wg.Add(1)
		safe.GoFuncWithCtx(bgCtx, func() {
			defer wg.Done()
			fapi.autoTransitionOrder(bgCtx, &order, trip)

			evt := service.EventOrderUpdated(orderID)
			if order.Driver != "" {
				if err := fapi.Notifier.Notify(ctx, []string{order.Driver}, evt, service.WithFirebase, service.WithSocketIO); err != nil {
					logrus.Errorf("Failed to notify driver. driver=%s event=%v err=%s", order.Driver, evt, err)
				}
			}
		})
	}

	apiutil.OK(gctx, gin.H{})
}

// autoTransitionOrder auto transition order status to the farthest status that in current trip route
func (fapi *FoodProviderImpl) autoTransitionOrder(ctx context.Context, order *domainModel.Order, trip domainModel.Trip) {
	shouldTransitToDriveToRest, shouldTransitToArrivedAtRest, err := calculateShouldAutoTransition(trip, order.OrderID)
	if err != nil {
		safe.SentryErrorMessage(fmt.Sprintf("autoTransitionOrder, calculateShouldAutoTransition err:%v", err.Error()), safe.WithOrderID(order.OrderID))
		return
	}

	if shouldTransitToArrivedAtRest {
		// TODO: remove this log after push log to prometheus
		logrus.Infof("auto transition orderID %v from %v to %v",
			order.OrderID,
			domainModel.StatusRestaurantAccepted,
			domainModel.StatusDriverToRestaurant)
		_, err := fapi.UpdateOrderStatusV2(ctx, order, order.Driver, &domainModel.Location{}, nil, nil)
		if err != nil {
			fapi.autoTransitionError(err,
				order.OrderID,
				domainModel.StatusRestaurantAccepted,
				domainModel.StatusDriverToRestaurant)
		}
		// TODO: remove this log after push log to prometheus
		logrus.Infof("auto transition orderID %v from %v to %v",
			order.OrderID,
			domainModel.StatusDriverToRestaurant,
			domainModel.StatusDriverArrivedRestaurant)
		_, err = fapi.UpdateOrderStatusV2(ctx, order, order.Driver, &domainModel.Location{}, nil, nil)
		if err != nil {
			fapi.autoTransitionError(err,
				order.OrderID,
				domainModel.StatusDriverToRestaurant,
				domainModel.StatusDriverArrivedRestaurant)
		}
	} else if shouldTransitToDriveToRest {
		// TODO: remove this log after push log to prometheus
		logrus.Infof("auto transition orderID %v from %v to %v",
			order.OrderID,
			domainModel.StatusRestaurantAccepted,
			domainModel.StatusDriverToRestaurant)
		_, err := fapi.UpdateOrderStatusV2(ctx, order, order.Driver, &domainModel.Location{}, nil, nil)
		if err != nil {
			fapi.autoTransitionError(err,
				order.OrderID,
				domainModel.StatusRestaurantAccepted,
				domainModel.StatusDriverToRestaurant)
		}
	}
}

func calculateShouldAutoTransition(trip model.Trip, orderID string) (shouldTransitToDriveToRest bool, shouldTransitToArrivedAtRest bool, err error) {
	idx, err := trip.FindRouteIdx(orderID, 0)
	if err != nil {
		return false, false, err
	}
	currentRouteOrders := types.NewStringSet(trip.Routes[idx].StopOrders.OrderIDs()...)
	var hasDriveToRest bool
	for _, o := range trip.Orders {
		if !currentRouteOrders.Has(o.OrderID) {
			continue
		}
		if (o.Status == model.StatusDriverToDestination) || (o.Status == model.StatusDriveTo && o.HeadTo == 1) ||
			(o.Status == model.StatusDriverArrivedRestaurant) || (o.Status == model.StatusArrivedAt && o.HeadTo == 0) {
			return false, true, nil
		}
		if (o.Status == model.StatusDriverToRestaurant) || (o.Status == model.StatusDriveTo && o.HeadTo == 0) {
			hasDriveToRest = true
		}
	}
	return hasDriveToRest, false, nil
}

func (fapi *FoodProviderImpl) autoTransitionError(e error, orderID string, from, to domainModel.Status) {
	msg := fmt.Sprintf("autoTransitionOrder orderID %v from %v to %v err: %v", orderID, from, to, e.Error())
	logrus.Errorf("RestaurantAccepted: autoTransitionOrder err: %v", msg)
	safe.SentryErrorMessage(msg, safe.WithOrderID(orderID))
}

func (fapi *FoodProviderImpl) Continue(ctx *gin.Context) {
	orderID := OrderIDFromGinContext(ctx)
	pos, err := strconv.Atoi(ctx.Param("pos"))
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.New("':pos' in path must be integer")))
		return
	}

	orderPtr, err := domainModel.NewOrderID(orderID)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if orderPtr.ServiceType() != domainModel.ServiceBike {
		err = fapi.tryLockingOrderWithRetry(ctx, orderID, retry.Delay(100*time.Millisecond), retry.Attempts(5))
		if err != nil {
			switch errors.Cause(err) {
			case middlewares.ErrOrderLocked:
				apiutil.ErrLocked(ctx, apiutil.NewFromString(apiutil.ErrCodeResourceLocked, "resource is locked"))
			default:
				apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
			}
			return
		}
		defer func() {
			if err := fapi.Locker.Unlock(ctx, getLockOrderKey(orderID)); err != nil {
				logrus.Errorf("unable to unlock %v in continue API due to %v", orderID, err)
			}
		}()
	}

	var req ContinueRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		InvalidJSONRequest(ctx)
		return
	}

	order, err := FindOrder(ctx.Request.Context(), fapi.OrderService, orderID, repository.WithReadPrimary)
	if err != nil {
		ResolveFindErr(err)(ctx)
		return
	}

	if pos > len(order.Routes)-1 {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.Errorf("no stop with index %v", pos)))
		return
	}

	if order.Status == domainModel.StatusCanceled {
		apiutil.ErrBadRequest(ctx, ErrOrderCanceled())
		return
	}

	if order.Status == domainModel.StatusCompleted && order.ServiceType != domainModel.ServiceBike {
		apiutil.ErrBadRequest(ctx, ErrOrderCompleted())
		return
	}

	if !order.Routes[pos].Pauses[req.Pause] {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.Errorf("stop(index: %v) does not support this pause", pos)))
		return
	}

	evt := service.EventOrderUpdated(orderID)

	switch req.Pause {
	case domainModel.PauseCheckout:
		delete(order.Routes[pos].Pauses, domainModel.PauseCheckout)
	case domainModel.PauseConfirmPrice:
		if !order.RevampedStatus {
			if order.Status != domainModel.StatusWaitingUserConfirmPrice {
				apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, domainModel.ErrStatusNotAllowed))
				return
			}
			backCompatRestaurantType(order)
			transitionOrderStatus(order)
		}
		delete(order.Routes[pos].Pauses, domainModel.PauseConfirmPrice)
	case domainModel.PauseQRPayment:
		evt = getEvent(order, orderID, req.Reason)
		delete(order.Routes[pos].Pauses, domainModel.PauseQRPayment)
		if req.Reason != "" {
			order.Routes[pos].ContinueReason = req.Reason
		}
	default:
		apiutil.ErrNotImplemented(ctx, &api.Error{Code: api.ERRCODE_NOT_IMPLEMENT, Message: "Not implement"})
		return
	}

	if req.Pause == domainModel.PauseQRPayment && order.ServiceType == domainModel.ServiceBike {
		if err := fapi.continueQRBikePause(ctx, order); err != nil {
			logx.Error().Context(ctx).Err(err).Str(logutil.OrderID, order.OrderID).Str(logutil.DriverID, order.Driver).Str(logutil.Tag, logutil.TagQRBike).Msg("unable to remove pause flag")
			alertutil.Alert(alertutil.ErrQRBikeContinuePayment, err, safe.WithOrderID(order.OrderID))
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
			return
		}
		logx.Info().Context(ctx).Str(logutil.OrderID, order.OrderID).Str(logutil.DriverID, order.Driver).Str(logutil.Tag, logutil.TagQRBike).Msg("pause flag removed")
	} else {
		err = fapi.OrderService.UpdateOrder(ctx, order)
		if err != nil {
			switch {
			case errors.Is(err, mongodb.ErrDataNotFound):
				apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, repository.ErrNotFound))
				return
			default:
				apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, err))
				return
			}
		}
	}

	if order.Driver != "" && len(evt) > 0 {
		if err := fapi.Notifier.Notify(ctx, []string{order.Driver}, evt, service.WithFirebase, service.WithSocketIO); err != nil {
			logrus.Errorf("Failed to notify driver. driver=%s event=%v err=%s", order.Driver, evt, err)
		}
	}

	apiutil.OK(ctx, gin.H{})
}

func (fapi *FoodProviderImpl) continueQRBikePause(ctx context.Context, order *model.Order) error {
	qrInfo := order.PriceSummary().DeliveryFee.QRPromptPayInfo
	shouldProcessFee := order.Status == model.StatusCompleted && qrInfo.Status == model.QRPromptPayStatusWaitingForPayment
	shouldUpdateForm := qrInfo.Status == model.QRPromptPayStatusWaitingForPayment

	_, err := fapi.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		var err error
		qrInfo.IsUserResolveQR = true
		if qrInfo.Status == model.QRPromptPayStatusWaitingForPayment {
			qrInfo.Status = domainModel.QRPromptPayStatusResolvedByUser
		}
		if err = fapi.OrderService.SetQRPromptPayInfo(sessCtx, *order, qrInfo); err != nil {
			return nil, errors.Wrap(err, "unable to set QRPromptPayInfo")
		}

		if !shouldProcessFee {
			return nil, nil
		}

		var trip domainModel.Trip
		if trip, err = fapi.TripRepo.GetTripByTripID(sessCtx, order.TripID); err != nil {
			return nil, errors.Wrap(err, "unable to get trip by trip ID from order")
		}

		if err = fapi.TripServices.ProcessPendingFee(sessCtx, trip, false); err != nil {
			return nil, errors.Wrap(err, "unable to process fee")
		}

		return nil, nil
	}, transaction.WithLabel("FoodProviderImpl.continueQRBikePause"))
	if err != nil {
		return err
	}
	if shouldProcessFee &&
		shouldUpdateForm &&
		qrInfo.FormID != "" {
		// For the Bike service (Agent Model), We would like to also automatically update the form status
		// to APPROVED if rider already submit the form also.
		updatedFormErr := fapi.formService.UpdateFormStatus(ctx, qrInfo.FormID, formServicePb.FormStatus_FORM_STATUS_APPROVED)
		if updatedFormErr != nil &&
			!errors.Is(updatedFormErr, form.ErrOrderNoFormID) {
			// We only want to detect the error by using sentry but not block the main flow.
			// So n returning the error here
			logx.Error().
				Context(ctx).
				Str(logutil.OrderID, order.OrderID).
				Msg("error update form status for user self resolve QR Payment")
			safe.SentryError(updatedFormErr,
				safe.WithOrderID(order.OrderID),
				safe.WithInfo(logutil.SentryErrorCode, "CONTINUE_UPDATE_FORM_1"),
			)
			return nil
		}
	}
	return nil
}

func getEvent(order *domainModel.Order, orderID string, reason domainModel.ContinueReason) map[string]string {
	if reason == domainModel.ContinueReasonSlipUploaded {
		return service.EventSlipUploaded(orderID, order.ServiceType)
	}

	if order.ServiceType == domainModel.ServiceFood && len(order.Tips) > 0 {
		return service.EventQRPaidWithTip(orderID)
	}

	return service.EventQRPaid(orderID, order.ServiceType)
}

// TODO backward compat. remove after this code has been in production for at least 3 months
func backCompatRestaurantType(o *domainModel.Order) {
	if len(o.Routes) < 1 {
		return
	}
	stopRestaurant := domainModel.CastStopInfoFood(&o.Routes[0].Info.StopInfo)
	if stopRestaurant.PriceScheme == "" {
		return
	}
	if stopRestaurant.PriceScheme != domainModel.PriceSchemeRMS {
		o.Autostart = true
		if o.Routes[0].Pauses == nil {
			o.Routes[0].Pauses = make(map[domainModel.Pause]bool)
		}
		if !o.RevampedStatus {
			o.Routes[0].Pauses[domainModel.PauseConfirmPrice] = true
		}
	}
	if o.IsSwitchFlow() || o.HasReassigned() {
		o.Autostart = true
	}
	o.PriceScheme = string(stopRestaurant.PriceScheme)
}

// END TODO

// RatingDriver rating driver by given order.
func (fapi *FoodProviderImpl) RatingDriver(gctx *gin.Context) {
	ctx := gctx.Request.Context()

	var req RatingDriverRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		InvalidJSONRequest(gctx)
		return
	}

	order, err := FindOrder(ctx, fapi.OrderService, req.OrderID)
	if err != nil {
		ResolveFindErr(err)(gctx)
		return
	}

	err = fapi.DriverService.RatingDriver(gctx, req.Score, req.Comment, req.Tags, order)
	if err == service.ErrRatingInvalidOrderStatus {
		apiutil.ErrBadRequest(gctx, ErrOrderStatusNotAllowed())
	} else if err != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, err))
	}

	apiutil.OK(gctx, apiutil.EmptyBody())
}

// TipDriver tip driver by given order.
func (f *FoodProviderImpl) TipDriver(gctx *gin.Context) {
	ctx := gctx.Request.Context()

	var req TipDriverRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		InvalidJSONRequest(gctx)
		return
	}

	rs, err := f.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		return f.doTipDriver(sessCtx, req)
	}, transaction.WithLabel("FoodProviderImpl.TipDriver"))
	if err != nil {
		var apiErr *api.Error
		if errors.As(err, &apiErr) {
			switch apiErr.Code {
			case AlreadyTip().Code,
				ErrOrderStatusNotAllowed().Code,
				api.ERRCODE_INVALID_REQUEST:
				apiutil.ErrBadRequest(gctx, apiErr)
			default:
				apiutil.ErrInternalError(gctx, apiErr)
			}
			return
		}
		ResolveFindErr(err)(gctx)
		return
	}

	order := rs.(*TipOrderResponse).Order

	if shouldOnlyUpdateOrder(order) {
		apiutil.OK(gctx, rs)
		return
	}

	if updateTodayEarningsErr := f.updateTodayEarnings(ctx, order.Driver, req); updateTodayEarningsErr != nil {
		apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_SERVICE_INTERNAL_ERROR, updateTodayEarningsErr))
		return
	}

	if notifyErr := f.Notifier.Notify(ctx, []string{order.Driver}, service.EventTipDriver(order.TripID, order.OrderID, f.Cfg.TipDriverTitle, f.Cfg.TipDriverBody)); notifyErr != nil {
		logx.Error().Err(notifyErr).
			Str("method", "TipDriver").
			Msg("unable to notify tip driver")
	}

	apiutil.OK(gctx, rs)
	return
}

// we won't pay any tip amount unless the order is completed so just record to `orders` document
// only update order because order revision may not be created yet
func shouldOnlyUpdateOrder(order *model.Order) bool {
	return order.Status != domainModel.StatusCompleted
}

func (f *FoodProviderImpl) doTipDriver(ctx context.Context, req TipDriverRequest) (*TipOrderResponse, error) {
	order, findErr := FindOrder(ctx, f.OrderService, req.OrderID)
	if findErr != nil {
		return nil, findErr
	}

	if !order.IsReadyForTip() {
		return nil, ErrOrderStatusNotAllowed()
	}

	if order.Tips.IsTip(req.TipID) {
		return nil, AlreadyTip()
	}

	if len(order.Tips) >= 2 {
		logx.Warn().Str("method", "TipDriver").
			Msgf("[TipDriver-SuspiciousTipSize] order [%s] already have [%v] tips for driver [%s]", order.OrderID, len(order.Tips), order.Driver)
	}

	orderUpdater := model.NewOrderUpdater().
		AddTipAmount(req.Amount).
		AddTipRecord(model.TipRecord{
			ID:          req.TipID,
			OrderStatus: string(order.Status),
			CreatedAt:   timeutil.BangkokNow(),
			Amount:      req.Amount,
		})

	if shouldOnlyUpdateOrder(order) {
		// we won't pay any tip amount unless the order is completed so just record to `orders` document
		// only update order because order revision may not be created yet
		if err := f.OrderService.UpdateOrderByField(ctx, req.OrderID, orderUpdater); err != nil {
			return nil, err
		}
		return &TipOrderResponse{
			DriverID:    order.Driver,
			OrderStatus: string(order.Status),
			Order:       order,
		}, nil
	}

	if _, _, err := f.DriverTransactionService.ProcessDriverTransaction(ctx,
		order.Driver,
		domainModel.SystemTransactionChannel,
		domainModel.WalletTopUpTransactionAction,
		domainModel.SuccessTransactionStatus,
		service.TransactionInfos(*domainModel.NewDriverTipWithTipID(order.Driver, order.OrderID, req.Amount, req.TipID)),
		service.WithTransactionOptions(func(tx *domainModel.Transaction) {
			tx.AddRemark(domainModel.GetTransactionBalanceLog(tx.Info.CreditBalance, tx.Info.WalletBalance, tx.Info.InstallmentAmount), "SYSTEM")
		}),
	); err != nil {
		logx.Error().Err(err).
			Str("method", "TipDriver").
			Msg("fail to process tip transaction")
		return nil, err
	}

	if updateErr := f.updateOrderAndRevision(ctx, order, orderUpdater); updateErr != nil {
		return nil, updateErr
	}

	return &TipOrderResponse{
		DriverID:    order.Driver,
		OrderStatus: string(order.Status),
		Order:       order,
	}, nil
}

func (f *FoodProviderImpl) updateTodayEarnings(ctx context.Context, driverID string, req TipDriverRequest) error {
	earning, err := f.DriverRepository.GetTodayEarning(ctx, driverID)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "TipDriver").
			Msg("Cannot get today earning")
		return err
	}
	t := earning.Tip + req.Amount
	newEarning := domainModel.NewDriverEarning(
		driverID,
		earning.Fee,
		earning.Commission,
		earning.Tax,
		t,
	)
	if setErr := f.DriverRepository.SetTodayEarning(ctx, *newEarning); setErr != nil {
		logx.Error().Err(setErr).
			Str("method", "TipDriver").
			Msg("fail to set tip earning")
		return setErr
	}

	return nil
}

func (f *FoodProviderImpl) updateOrderAndRevision(ctx context.Context, srcOrder *model.Order, orderUpdater model.OrderUpdater) error {
	if err := f.OrderService.UpdateOrderByField(ctx, srcOrder.OrderID, orderUpdater); err != nil {
		logx.Error().Err(err).Str("method", "updateOrderAndRevision").
			Msgf("unable to update order [%s] of driver [%s]", srcOrder.OrderID, srcOrder.Driver)
		return err
	}

	if err := f.OrderService.UpdateOrderRevisionByField(ctx, srcOrder.OrderID, srcOrder.DeliveringRound, orderUpdater); err != nil {
		// only log
		logx.Error().Err(err).Str("method", "updateOrderAndRevision").
			Msgf("unable to update order revision [%s] of driver [%s]", srcOrder.OrderID, srcOrder.Driver)
		return err
	}
	return nil
}

// EditOrder edit items inside an order. This api will re-calculate price summary and send push notification to driver
// if this order already assigned to driver.
func (fapi *FoodProviderImpl) EditOrder(ctx *gin.Context) {
	var req EditOrderRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(ctx)
		return
	}

	order, err := FindOrder(ctx.Request.Context(), fapi.OrderService, req.OrderID)
	if err != nil {
		ResolveFindErr(err)(ctx)
		return
	}

	if fapi.featureflagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsVerifyOrderTimestampEnabled.Name) {
		if !order.VerifyOwnerChangedAt(req.UpdatedAt) {
			apiutil.ErrBadRequest(ctx, apiutil.NewFromString("RACE_CONDITION_ERROR", "updated_at must be later than the previous request"))
			return
		}
	}

	items := req.Items

	if keywords := fapi.Cfg.AtomicOrderDBConfig.Get().MartInvalidItemGroups; order.ServiceType == domainModel.ServiceMart && len(keywords) > 0 {
		items = martItemsWithDefaultGroup(items, keywords)
	}

	if order.ServiceType == model.ServiceMart && order.StoreType == domainModel.StoreTypeRMS {
		items = martRMSItemsWithoutDirtyFields(items)
	}

	pickingItemsChangeDetail := domainModel.Items(order.Routes[0].PickingItems).CompareWithNewItems(items)
	order.SetItems(items)
	order.SetItemFee(req.GetItemPrice())
	order.SetItemFeeBeforeDiscount(req.ItemsPriceBeforeDiscount)
	order.SetItemDiscounts(req.Discounts.Discounts().ItemFeeDiscounts())

	if req.UserDeliveryFee != nil && req.UserDeliveryFeeBeforeDiscount != nil {
		order.SetUserDeliveryFee(*req.UserDeliveryFee)
		order.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee.UserDeliveryFeeBeforeDiscount = *req.UserDeliveryFeeBeforeDiscount
	}

	order.SetOwnerChangedAt(req.UpdatedAt)
	order.Summary(GetCommissionRate(fapi.Cfg, order.ServiceType.String()), fapi.Cfg.AtomicOrderDBConfig.Get().WithHoldingTax)
	if (order.ServiceType == domainModel.ServiceFood || order.ServiceType == domainModel.ServiceMart) && order.Prediction != nil {
		region := order.DistributeRegions.DefaultRegion().String()
		serviceArea, err := fapi.ServiceAreaRepo.GetByRegion(ctx, region)
		if err != nil {
			ResolveFindErr(err)(ctx)
			return
		}
		switchbackExperiments, err := fapi.DistributionExperimentPlatformClient.GetSwitchbackExperimentsWithConditions("", &exp.Condition{Name: "region", Value: region})
		if err != nil {
			logrus.Errorf("[EditOrder] failed to get switchback experiment from EP for region %v", region)
		}
		setting := service.NewPredictSetting(
			serviceArea.Distribution.GetModelVersionWithSwitchbackExperiments(time.Now(), domainModel.PredictModel, switchbackExperiments),
			serviceArea.Distribution.SmartDistributionGoodnessBiasLevel,
		)
		_ = fapi.PredictionService.Predict(ctx, order, setting) // Predict should alert anyway
	}

	if err := fapi.OrderService.UpdateOrder(ctx.Request.Context(), order); err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if len(order.Driver) != 0 {
		evt := service.EventOrderItemsChanged(order, &pickingItemsChangeDetail)
		if err := fapi.Notifier.Notify(ctx.Request.Context(), []string{order.Driver}, evt, service.WithFirebase, service.WithSocketIO); err != nil {
			logrus.Errorf("Failed to notify driver. driver=%s event=%v err=%s", order.Driver, evt, err)
		}
	}

	apiutil.OK(ctx, apiutil.EmptyBody())
}

// UpdateDeliveryLocation update order delivery location.
func (fapi *FoodProviderImpl) UpdateDeliveryLocation(ctx *gin.Context) {
	var req UpdateDeliveryLocationRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(ctx)
		return
	}

	req = req.BackwardCompatMemo()

	var header DeliveryHeader
	if err := ctx.ShouldBindHeader(&header); err != nil {
		apiutil.ErrBadRequest(ctx, &api.Error{
			Message:   err.Error(),
			Timestamp: api.Timestamp(time.Now()),
		})
		return
	}

	// should not retry for too long since this could cause deadlock when this API is called and rider is trying to accept the order
	err := fapi.tryLockingOrderWithRetry(ctx, req.OrderID, retry.Delay(100*time.Millisecond), retry.Attempts(2))
	if err != nil {
		switch errors.Cause(err) {
		case middlewares.ErrOrderLocked:
			apiutil.ErrLocked(ctx, apiutil.NewFromString(apiutil.ErrCodeResourceLocked, "resource is locked"))
		default:
			apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		}
		return
	}
	defer func() {
		if err := fapi.Locker.Unlock(ctx, getLockOrderKey(req.OrderID)); err != nil {
			logrus.Errorf("unable to unlock %v in UpdateDeliveryLocation API due to %v", req.OrderID, err)
		}
	}()

	order, err := FindOrder(ctx.Request.Context(), fapi.OrderService, req.OrderID)
	if err != nil {
		ResolveFindErr(err)(ctx)
		return
	}

	if len(order.Routes) < 2 {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, &api.Error{
			Message:   "order has no delivery location",
			Timestamp: api.Timestamp(time.Now()),
		}))
		return
	}

	stop := order.GetChangeDeliveryLocationStop()
	originalDistance := order.Routes[stop].Distance

	if req.Name != "" {
		order.Routes[stop].Name = req.Name
	}
	if req.Phone != "" {
		if len(order.Routes[stop].Phones) == 0 {
			order.Routes[stop].Phones = []string{
				req.Phone,
			}
		} else {
			order.Routes[stop].Phones[0] = req.Phone
		}
	}
	if req.Address != "" {
		order.Routes[stop].Address = req.Address
	}

	order.Routes[stop].Memo = req.GetMemo()
	order.Routes[stop].MemoType = req.MemoType

	order.Routes[stop].Location = domainModel.Location{
		Lat: req.Location.Lat,
		Lng: req.Location.Lng,
	}

	order.UserRegion = header.Region
	ds, err := fapi.AreaService.GetDistributeRegion(ctx, order.ServiceType, order.UserRegion, service.NewLocation(order.Routes[0].Location.Lat, order.Routes[0].Location.Lng))
	if err != nil {
		ResolveFindErr(errors.WithMessagef(err, "fail to get distribution region by area %s", order.UserRegion))(ctx)
		return
	}

	distance := types.Distance(0)
	for i := 0; i < len(order.Routes)-1; i++ {
		dist, duration, _, err := DistanceDurationBetweenRoutes(ctx, fapi.MapService, order.Routes[i].Location, order.Routes[i+1].Location, req.UseGoogleMap)
		if err != nil {
			ResolveFindErr(err)(ctx)
			return
		}
		order.Routes[i+1].Distance = dist
		order.Routes[i+1].EstimatedDeliveryTime = domainModel.DurationSecond(duration)

		distance += dist
	}

	distRegions := domainModel.DistributeRegions(ds)

	backCompatRestaurantType(order)
	calculator, priceScheme, err := fapi.DeliveryFeeSvc.GetDeliveryFeeCalculator(ctx, order.ServiceType, distRegions.DefaultRegion().String(), order.Routes[0].Location, domainModel.SchemeKeyFromPriceScheme(order.Quote.PriceScheme))
	if err != nil {
		ResolveFindErr(errors.WithMessagef(err, "fail to get delivery fee region : %s", distRegions.DefaultRegion().String()))(ctx)
		return
	}

	deliveryFee := calculator.Calculate(distance, !req.RevenueAgentModel, priceScheme)
	order.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee.RawBaseFee = deliveryFee.BaseFee().Float64()
	order.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee.RoadFee = deliveryFee.IncrementFee().Float64()
	order.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee.PriceSchemeRefId = priceScheme.Key()

	if order.ServiceType == model.ServiceMessenger && len(order.Routes) >= 3 {
		order.Distance = deliveryFee.Distance()
	} else {
		order.Distance = distance
	}

	order.SetUserPriceInterventionSchemes(req.UserOnTops.Schemes())

	order.IsExperimentalSuccess = false
	order.SetDeliveryDiscounts(req.Discounts.Discounts())
	order.RevenuePrincipalModel = !req.RevenueAgentModel
	order.SetUserDeliveryFee(req.UserDeliveryFee)
	order.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee.UserDeliveryFeeBeforeDiscount = req.UserDeliveryFeeBeforeDiscount
	order.Summary(GetCommissionRate(fapi.Cfg, order.ServiceType.String()), fapi.Cfg.AtomicOrderDBConfig.Get().WithHoldingTax)
	order.IgnoreDriverTooFarValidation = true
	if (order.ServiceType == domainModel.ServiceFood || order.ServiceType == domainModel.ServiceMart) && order.Prediction != nil {
		serviceArea, err := fapi.ServiceAreaRepo.GetByRegion(ctx, order.DistributeRegions.DefaultRegion().String())
		if err != nil {
			ResolveFindErr(err)(ctx)
			return
		}
		switchbackExperiments, err := fapi.DistributionExperimentPlatformClient.GetSwitchbackExperimentsWithConditions("", &exp.Condition{Name: "region", Value: string(serviceArea.Region)})
		if err != nil {
			logrus.Errorf("[UpdateDeliveryLocation] failed to get switchback experiment from EP for region %v", string(serviceArea.Region))
		}
		setting := service.NewPredictSetting(
			serviceArea.Distribution.GetModelVersionWithSwitchbackExperiments(time.Now(), domainModel.PredictModel, switchbackExperiments),
			serviceArea.Distribution.SmartDistributionGoodnessBiasLevel,
		)
		_ = fapi.PredictionService.Predict(ctx, order, setting) // Predict should alert anyway
	}
	order.LastDeliveryLocationUpdatedAt = types.NewTime(time.Now())

	if err := fapi.OrderService.UpdateOrder(ctx.Request.Context(), order); err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	if len(order.Driver) != 0 {
		evt := service.EventOrderItemsChanged(order, nil)
		if err := fapi.Notifier.Notify(ctx.Request.Context(), []string{order.Driver}, evt, service.WithFirebase, service.WithSocketIO); err != nil {
			logrus.Errorf("Failed to notify driver. driver=%s event=%v err=%s", order.Driver, evt, err)
		}
	}

	// Note: Memo and MemoType are not updated in trips.
	// They are updated to orders collection, and will be merged in GetTripDetails API.
	_, err = fapi.TripServices.OnUpdateDeliveryLocation(ctx, service.TripOrderUpdateLocationEvent{
		TripID:                order.TripID,
		OrderID:               order.OrderID,
		OrderStopChangedIndex: stop,
		OrderLocation:         order.Routes[stop].Location,
	})
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	logx.Info().Msgf("[UpdateDeliveryLocation] order %v delivery location is updated, original_distance:%v, new_distance:%v", order.OrderID, originalDistance, order.Routes[stop].Distance)
	apiutil.OK(ctx, apiutil.EmptyBody())
}

func (fapi *FoodProviderImpl) ChangePaymentMethod(gCtx *gin.Context) {
	orderID := OrderIDFromGinContext(gCtx)
	ctx := gCtx.Request.Context()
	order, err := FindOrder(ctx, fapi.OrderService, orderID)
	if err != nil {
		ResolveFindErr(err)(gCtx)
		return
	}

	oldMethod := order.Routes[order.GetPayAtStop()].PriceSummary.DeliveryFee.PaymentMethod

	req, err := NewPaymentMethodRequest(gCtx)
	if err != nil {
		apiutil.InvalidJSONRequest(gCtx)
		return
	}

	if req.PayAtStop != nil {
		if err := order.SetPayAtStop(*req.PayAtStop); err != nil {
			apiutil.ErrBadRequest(gCtx, &api.Error{
				Code:      api.ERRCODE_INVALID_REQUEST,
				Message:   err.Error(),
				Timestamp: api.TimestampNow(),
			})
		}
	}

	if req.DriverMoneyFlow != nil {
		order.Options.DriverMoneyFlow = *req.DriverMoneyFlow
	}
	if err := order.ChangeDeliveryFeePaymentMethod(req.PaymentMethod, req.ItemFeePaymentMethod, req.DeliveryFeePaymentMethod); err != nil {
		apiutil.ErrBadRequest(gCtx, &api.Error{
			Code:      api.ERRCODE_INVALID_REQUEST,
			Message:   err.Error(),
			Timestamp: api.TimestampNow(),
		})
		return
	}

	if err := fapi.OrderService.UpdateOrder(ctx, order); err != nil {
		apiutil.ErrInternalError(gCtx, &api.Error{
			Code:      api.ERRCODE_INTERNAL_ERROR,
			Message:   err.Error(),
			Timestamp: api.TimestampNow(),
		})
		return
	}

	var evt map[string]string

	if oldMethod.HasConvertQRPaymentToCash(req.PaymentMethod) {
		evt = service.EventChangePaymentMethodFromQRtoCash(order.OrderID, order.ServiceType)
	}

	if oldMethod.HasConvertCashToQRPayment(req.PaymentMethod) {
		evt = service.EventChangePaymentMethodFromCashtoQR(order.OrderID)
	}

	if evt != nil && order.Driver != "" {
		if err := fapi.Notifier.Notify(ctx, []string{order.Driver}, evt, service.WithFirebase, service.WithSocketIO); err != nil {
			logrus.Errorf("Failed to notify driver. driver=%s event=%v err=%s", order.Driver, evt, err)
		}
	}

	apiutil.OK(gCtx, apiutil.EmptyBody())
}

func (fapi *FoodProviderImpl) UnlockMP(gCtx *gin.Context) {
	orderID := OrderIDFromGinContext(gCtx)
	_, err := fapi.txnHelper.WithTxn(gCtx, func(ctx context.Context) (interface{}, error) {
		order, err := FindOrder(ctx, fapi.OrderService, orderID)
		if err != nil {
			return nil, err
		}

		if order.Status == domainModel.StatusCanceled || order.Status == domainModel.StatusCompleted || order.Status == domainModel.StatusExpired {
			logrus.Warnf("food tried to unlock MP of %s order; orderID: %s", order.Status, order.OrderID)
			return nil, nil
		}

		if order.IsDalianMP && order.Options.MpID != "" {
			if err := fapi.ThrottledOrderRepository.UpdateMpShouldPickupAt(ctx, order.Options.MpID, timeutil.BangkokNow()); err != nil {
				return nil, err
			}
		} else {
			if order.Driver == "" {
				logrus.Warnf("food tried to unlock MP of order with no driver; orderID: %s", order.OrderID)
				return nil, nil
			}
			err = fapi.DriverRepository.UnlockForQueueing(ctx, order.Driver)
			if err != nil {
				return nil, err
			}
		}
		return nil, nil
	}, transaction.WithLabel("FoodProviderImpl.UnlockMP"))
	if err != nil {
		ResolveFindErr(err)(gCtx)
		return
	}

	apiutil.OK(gCtx, apiutil.EmptyBody())
}

// GetOrderStatus get status from order
func (fapi *FoodProviderImpl) GetOrderStatus(ctx *gin.Context) {
	orderID := OrderIDFromGinContext(ctx)
	order, err := FindOrder(ctx.Request.Context(), fapi.OrderService, orderID)
	if err != nil {
		ResolveFindErr(err)(ctx)
		return
	}
	apiutil.OK(ctx, &GetOrderStatusResponse{
		Status: string(order.Status),
		Time:   order.GetCurrentStatusTime(),
	})
}

func (fapi *FoodProviderImpl) GetOrderDetail(ctx *gin.Context) {
	orderID := OrderIDFromGinContext(ctx)
	order, err := FindOrder(ctx.Request.Context(), fapi.OrderService, orderID)
	if err != nil {
		ResolveFindErr(err)(ctx)
		return
	}
	apiutil.OK(ctx, NewGetOrderDetailResponse(order))
}

func (fapi *FoodProviderImpl) RefundOrder(gctx *gin.Context) {
	var req RefundOrderRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	orderID := OrderIDFromGinContext(gctx)
	ctx := gctx.Request.Context()

	_, err := fapi.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		order, err := FindOrder(sessCtx, fapi.OrderService, orderID)
		if err != nil {
			return nil, err
		}
		trip, err := fapi.TripServices.GetTripByID(sessCtx, order.TripID)
		if err != nil {
			return nil, err
		}

		itemRefundAmount, voidRiderWage := types.NewMoney(req.ItemRefundAmount), req.VoidRiderWage
		additionalLimit := fapi.Cfg.AtomicOrderDBConfig.Get().RefundAdditionalLimit
		if err := validateRefundOrder(*order, trip, itemRefundAmount, voidRiderWage, req.RequestID, types.NewMoney(additionalLimit)); err != nil {
			return nil, err
		}

		txnInfos := make([]domainModel.TransactionInfo, 0)
		tripRefundRecord := trip.NewRefundRecord(orderID)
		if itemRefundAmount.GT(0) {
			infos, err := fapi.refundItemFee(sessCtx, order, itemRefundAmount, &tripRefundRecord)
			if err != nil {
				return nil, err
			}
			txnInfos = append(txnInfos, infos...)
		}
		if voidRiderWage {
			infos, err := fapi.refundDriverWage(sessCtx, order, &trip, &tripRefundRecord)
			if err != nil {
				return nil, err
			}
			txnInfos = append(txnInfos, infos...)
		}

		order.AddRefundRecord(NewRefundRecordFromReq(req))
		trip.AddRefundRecord(tripRefundRecord)

		effectiveTime := timeutil.ParseEffectiveHour(fapi.Cfg.AtomicOrderDBConfig.Get().RefundEffectiveTime)
		if fapi.Cfg.AtomicOrderDBConfig.Get().DelayRefundExecution && !effectiveTime.IsZero() {
			groupTxns, err := newPenaltyGroupTxnsFromTxnInfos(req, txnInfos, effectiveTime.Add(24*time.Hour))
			if err != nil {
				return nil, err
			}
			if err := fapi.GroupTransactionRepo.CreateMany(ctx, groupTxns); err != nil {
				return nil, err
			}
		} else {
			if _, _, err := fapi.DriverTransactionService.ProcessDriverTransaction(
				sessCtx,
				order.Driver,
				model.AdminTransactionChannel,
				model.DeductChargeTransactionAction,
				model.SuccessTransactionStatus,
				service.TransactionInfos(txnInfos...),
				service.WithTransactionOptions(model.WithNewTransactionRefID(), model.WithRemarks(req.Remark, req.RequestedBy), model.WithRequestedBy(req.RequestedBy)),
			); err != nil {
				return nil, err
			}
		}

		if err := fapi.OrderService.UpdateOrder(sessCtx, order); err != nil {
			return nil, err
		}
		if err := fapi.TripRepo.SetRefundData(sessCtx, trip); err != nil {
			return nil, err
		}
		return nil, nil
	}, transaction.WithLabel("FoodProviderImpl.RefundOrder"))
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		case ValidationError:
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, t))
			return
		default:
			switch err {
			case repository.ErrInvalidOrderID:
				apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, t))
				return
			case repository.ErrNotFound:
				apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, t))
				return
			default:
				apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, t))
				return
			}
		}
	}

	apiutil.OK(gctx, apiutil.EmptyBody())
	return
}

func (fapi *FoodProviderImpl) ValidateRefundOrder(gctx *gin.Context) {
	var req RefundOrderRequest
	if err := gctx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gctx)
		return
	}

	orderID := OrderIDFromGinContext(gctx)
	ctx := gctx.Request.Context()

	_, err := fapi.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		order, err := FindOrder(sessCtx, fapi.OrderService, orderID)
		if err != nil {
			return nil, err
		}
		trip, err := fapi.TripServices.GetTripByID(sessCtx, order.TripID)
		if err != nil {
			return nil, err
		}

		itemRefundAmount, voidRiderWage := types.NewMoney(req.ItemRefundAmount), req.VoidRiderWage
		additionalLimit := fapi.Cfg.AtomicOrderDBConfig.Get().RefundAdditionalLimit
		if err := validateRefundOrder(*order, trip, itemRefundAmount, voidRiderWage, req.RequestID, types.NewMoney(additionalLimit)); err != nil {
			return nil, err
		}

		return nil, nil
	}, transaction.WithLabel("FoodProviderImpl.ValidateRefundOrder"))
	if err != nil {
		switch t := err.(type) {
		case *api.Error:
			apiutil.ErrBadRequest(gctx, t)
		case ValidationError:
			apiutil.ErrBadRequest(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, t))
			return
		default:
			switch err {
			case repository.ErrInvalidOrderID:
				apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, t))
				return
			case repository.ErrNotFound:
				apiutil.ErrNotFound(gctx, apiutil.NewFromError(api.ERRCODE_NOT_FOUND, t))
				return
			default:
				apiutil.ErrInternalError(gctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, t))
				return
			}
		}
	}

	apiutil.OK(gctx, apiutil.EmptyBody())
	return
}

func (fapi *FoodProviderImpl) GetFareMetadata(gctx *gin.Context) {
	req, err := NewGetFareMetadataReq(gctx)
	if err != nil {
		logx.Error().Msgf("GetFareMetadata NewGetFareMetadataReq err: %v", err)
		apiutil.ErrBadRequest(gctx, apiErrors.ErrInvalidRequest(err))
		return
	}
	ctx := gctx.Request.Context()
	ds, err := fapi.AreaService.GetDistributeRegion(ctx, req.ServiceType, req.Region, service.NewLocation(req.Routes[0].Lat, req.Routes[0].Lng))
	if err != nil {
		logx.Error().Msgf("GetFareMetadata GetDistributeRegion err: %v", err)
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	_, priceScheme, err := fapi.DeliveryFeeSvc.GetDeliveryFeeCalculator(ctx, req.ServiceType, domainModel.DistributeRegions(ds).DefaultRegion().String(), req.Routes[0], model.PriceSchemeKeyDefault)
	if err != nil {
		logx.Error().Msgf("GetFareMetadata GetDeliveryFeeCalculator err: %v", err)
		apiutil.ErrInternalError(gctx, apiErrors.ErrInternal(err))
		return
	}

	apiutil.OK(gctx, NewFareMetadataRes(priceScheme))
	return
}

func newPenaltyGroupTxnsFromTxnInfos(req RefundOrderRequest, txnInfos []domainModel.TransactionInfo, effectiveTime time.Time) ([]model.GroupTransaction, error) {
	transRefId := crypt.EncryptedString(utils.GenerateUUID())

	var creditCategoryGroupTxn *model.GroupTransaction
	var walletCategoryGroupTxn *model.GroupTransaction

	for _, txnInfo := range txnInfos {
		item := *model.NewPenaltyChargeGroupTransactionItem(txnInfo, transRefId, req.Remark)
		if txnInfo.Category == domainModel.CreditTransactionCategory {
			if creditCategoryGroupTxn == nil {
				creditCategoryGroupTxn = model.NewGroupTransaction(
					utils.GenerateUUID(),
					req.RequestedBy,
					string(domainModel.CreditTransactionCategory),
					model.DeductChargeTransactionAction,
					effectiveTime,
				)
			}
			creditCategoryGroupTxn.AddItem(item)
		}

		if txnInfo.Category == domainModel.WalletTransactionCategory {
			if walletCategoryGroupTxn == nil {
				walletCategoryGroupTxn = model.NewGroupTransaction(
					utils.GenerateUUID(),
					req.RequestedBy,
					string(domainModel.WalletTransactionCategory),
					model.DeductChargeTransactionAction,
					effectiveTime,
				)
			}
			walletCategoryGroupTxn.AddItem(item)
		}
	}

	var groupTxns []model.GroupTransaction
	if creditCategoryGroupTxn != nil {
		if err := creditCategoryGroupTxn.Approve(req.RequestedBy); err != nil {
			return nil, err
		}
		groupTxns = append(groupTxns, *creditCategoryGroupTxn)
	}
	if walletCategoryGroupTxn != nil {
		if err := walletCategoryGroupTxn.Approve(req.RequestedBy); err != nil {
			return nil, err
		}
		groupTxns = append(groupTxns, *walletCategoryGroupTxn)
	}

	return groupTxns, nil
}

func getMaximumRefundLimit(o model.Order, refundAdditionalLimit types.Money) types.Money {
	return types.NewMoney(o.Routes[o.GetPayAtStop()].PriceSummary.ItemFee.SubTotal).Add(refundAdditionalLimit)
}

func validateRefundOrder(order model.Order, trip model.Trip, itemRefundAmount types.Money, voidRiderWage bool, requestID string, refundAdditionalLimit types.Money) error {
	if itemRefundAmount.Equal(0) && !voidRiderWage {
		return NewValidationError("no action")
	}
	if itemRefundAmount.LT(0) {
		return NewValidationError("itemRefundAmount must greater than or equal 0")
	}
	if order.Status != model.StatusCompleted {
		return NewValidationError("order must be COMPLETED")
	}
	if order.ServiceType != model.ServiceFood {
		return NewValidationError("order must be food service")
	}
	if order.HasRefundRequestID(requestID) {
		return NewValidationError("duplicate request")
	}
	if voidRiderWage && order.FraudStatus == model.FraudStatusFraud {
		return ErrIncompatibleWithFraud()
	}
	if voidRiderWage && order.IsVoidedWage {
		return NewValidationError("order was already void wage")
	}

	if itemRefundAmount.GT(0) && !order.IsItemFeeEPayment() {
		return NewValidationError("item fee must be paid by e-payment")
	}
	if voidRiderWage && !order.IsDeliveryFeeEPayment() {
		return NewValidationError("delivery fee must be paid by e-payment")
	}

	destPriceSummary := order.Routes[order.GetPayAtStop()].PriceSummary
	totalRefundItemFee := itemRefundAmount.Add(order.TotalRefundItemFee()).Float64()
	maximumRefundAmount := getMaximumRefundLimit(order, refundAdditionalLimit)

	if totalRefundItemFee > maximumRefundAmount.Float64() {
		return NewValidationError(fmt.Sprintf("ItemRefundAmount is over than maximum %v", maximumRefundAmount))
	}
	if order.IsCashAdvancementEpayment() && (totalRefundItemFee > destPriceSummary.ItemFee.Total && totalRefundItemFee != destPriceSummary.ItemFee.SubTotal) {
		return NewValidationError("not support to partial refund coupon")
	}

	if trip.Status != model.TripStatusCompleted {
		return NewValidationError("trip must be COMPLETED")
	}
	for _, tripOrder := range trip.Orders {
		if voidRiderWage && tripOrder.IsFraud {
			return ErrIncompatibleWithFraud()
		}
		if voidRiderWage && tripOrder.OrderID == order.OrderID && tripOrder.IsVoidedWage {
			return NewValidationError("order was already void wage")
		}
		if tripOrder.OrderID == order.OrderID && tripOrder.Status != model.StatusCompleted {
			return NewValidationError("trip order wasn't completed")
		}
	}

	return nil
}

func diffWithBoundary(lower, upper, minimum, maximum types.Money) types.Money {
	result := upper.Sub(lower)
	if result > maximum {
		result = maximum
	} else if result < minimum {
		result = 0
	}
	return result
}

func getChargeBackAmount(itemFeeTotal, itemFeeSubTotal, refundedAmount, itemRefundAmount types.Money) (types.Money, types.Money, types.Money) {
	if itemRefundAmount <= 0 {
		return 0, 0, 0
	}

	totalRefundAmount := itemRefundAmount.Add(refundedAmount)
	chargeItemFeeAmount := diffWithBoundary(
		types.NewMoney(math.Max(0, refundedAmount.Float64())),
		types.NewMoney(math.Min(itemFeeTotal.Float64(), totalRefundAmount.Float64())),
		0,
		itemFeeTotal,
	)
	chargeCouponAmount := diffWithBoundary(
		types.NewMoney(math.Max(itemFeeTotal.Float64(), refundedAmount.Float64())),
		types.NewMoney(math.Min(itemFeeSubTotal.Float64(), totalRefundAmount.Float64())),
		0,
		itemFeeSubTotal.Sub(itemFeeTotal),
	)
	additionalChargeAmount := diffWithBoundary(
		types.NewMoney(math.Max(itemFeeSubTotal.Float64(), refundedAmount.Float64())),
		totalRefundAmount,
		0,
		math.MaxFloat64,
	)

	return chargeItemFeeAmount, chargeCouponAmount, additionalChargeAmount
}

func (fapi *FoodProviderImpl) refundItemFee(ctx context.Context, order *model.Order, itemRefundAmount types.Money, refundRecord *model.TripRefundRecord) ([]model.TransactionInfo, error) {
	if itemRefundAmount.LT(0) {
		return nil, errors.New("total refund amount must be greater than or equal zero")
	}

	txnInfos := make([]model.TransactionInfo, 0)
	// if cash advancement e-payment should separate charge item fee and coupon
	if order.IsCashAdvancementEpayment() {
		destPriceSummary := order.Routes[order.GetPayAtStop()].PriceSummary
		chargeItemFeeAmount, chargeCouponAmount, additionalChargeAmount := getChargeBackAmount(
			types.NewMoney(destPriceSummary.ItemFee.Total),
			types.NewMoney(destPriceSummary.ItemFee.SubTotal),
			order.TotalRefundItemFee(),
			itemRefundAmount,
		)
		if chargeCouponAmount.NE(0.0) && chargeCouponAmount.NE(types.NewMoney(destPriceSummary.ItemFee.TotalDiscount())) {
			return nil, errors.New("not support to partial refund coupon")
		}
		oTrans, err := fapi.TransactionRepo.FindByOrderID(ctx, order.OrderID)
		if err != nil {
			return nil, err
		}
		var foundItemFeeTran, foundCouponTran bool
		for _, tran := range oTrans {
			if tran.Info.Type == model.ItemFeeTransactionType && chargeItemFeeAmount.GT(0) {
				if foundItemFeeTran {
					return nil, errors.New("not support with multiple item fee transaction")
				}
				chargeTran := domainModel.NewPenaltyChargeItemFeeTransactionInfo(order.Driver, order.OrderID, order.TripID, chargeItemFeeAmount)
				chargeTran.RefID = tran.TransactionID
				txnInfos = append(txnInfos, *chargeTran)
				foundItemFeeTran = true
			} else if tran.Info.Type == model.CouponTransactionType && chargeCouponAmount.GT(0) {
				if foundCouponTran {
					return nil, errors.New("not support with multiple coupon transaction")
				}
				chargeTran := domainModel.NewPenaltyChargeCouponTransactionInfo(order.Driver, order.OrderID, order.TripID, chargeCouponAmount)
				chargeTran.RefID = tran.TransactionID
				txnInfos = append(txnInfos, *chargeTran)
				foundCouponTran = true
			}
		}
		if additionalChargeAmount.GT(0) {
			chargeTran := domainModel.NewPenaltyChargeItemFeeTransactionInfo(order.Driver, order.OrderID, order.TripID, additionalChargeAmount)
			txnInfos = append(txnInfos, *chargeTran)
		}
	} else if itemRefundAmount.GT(0) { // e-payment
		chargeTran := domainModel.NewPenaltyChargeItemFeeTransactionInfo(order.Driver, order.OrderID, order.TripID, itemRefundAmount)
		txnInfos = append(txnInfos, *chargeTran)
	}

	refundRecord.ItemRefundAmount = itemRefundAmount
	return txnInfos, nil
}

func (fapi *FoodProviderImpl) refundDriverWage(ctx context.Context, order *model.Order, trip *model.Trip, refundRecord *model.TripRefundRecord) ([]model.TransactionInfo, error) {
	txnInfos := make([]model.TransactionInfo, 0)
	oTrans, err := fapi.TransactionRepo.FindOnTopsByOrderID(ctx, order.OrderID)
	if err != nil {
		return nil, err
	}
	for _, tran := range oTrans {
		chargeTran := domainModel.NewPenaltyChargeOnTopTransactionInfo(order.Driver, order.OrderID, order.TripID, tran.Info.Amount)
		chargeTran.RefID = tran.TransactionID
		txnInfos = append(txnInfos, *chargeTran)
		refundRecord.ChargedOnTop = refundRecord.ChargedOnTop.Add(tran.Info.Amount)
	}

	lastWageTran, err := fapi.DriverTransactionService.GetWageTransactionFromTrip(ctx, order.TripID)
	if err != nil {
		return nil, err
	}
	if len(lastWageTran) != 1 {
		return nil, errors.New("should have one wage transaction")
	}

	refID := lastWageTran[0].TransactionID
	newDriverWage := types.Money(0)
	latestWage := trip.GetLatestWageFromRefund()
	chargeAmount := latestWage
	order.MarkVoidedWage()
	trip.MarkVoidedWage(order.OrderID)
	for _, o := range trip.Orders {
		if !o.IsVoidedWage {
			continue
		}
		if err := trip.Cancel(o.OrderID); err != nil {
			return nil, err
		}
	}

	if len(trip.GetActiveOrderIDs()) > 0 {
		if err := fapi.TripServices.RecalculateTripWage(ctx, trip, service.RecalAction(fmt.Sprintf("VOID_WAGE_%s", order.OrderID))); err != nil {
			return nil, err
		}
		newWageTran := trip.GetWageTransactionInfoNoFraudAndVoided()
		chargeAmount = latestWage.Sub(newWageTran.Amount)
		if chargeAmount.LT(0) {
			return nil, errors.New("new wage should less than the latest")
		}
		newDriverWage = trip.DriverWageSummary.TotalDriverWage
	}

	chargeTran := domainModel.NewPenaltyChargeWageTransactionInfo(order.Driver, order.OrderID, order.TripID, chargeAmount)
	chargeTran.RefID = refID
	txnInfos = append(txnInfos, *chargeTran)
	refundRecord.Wage = newDriverWage
	refundRecord.WageChange = chargeAmount.Neg()
	refundRecord.VoidRiderWage = true
	return txnInfos, nil
}

func (fapi *FoodProviderImpl) createSwitchFlowOrder(ctx context.Context, order *domainModel.Order) error {
	existingOrder, err := fapi.OrderService.Get(ctx, order.OrderID)
	if err == repository.ErrNotFound {
		cfg := fapi.Cfg.AtomicOrderDBConfig.Get()
		order.DisabledRecreate = cfg.DisableRecreateSwitchFlow && order.ServiceType == model.ServiceFood
		return fapi.OrderService.CreateOrder(ctx, order)
	}

	if err != nil {
		return err
	}

	if existingOrder.Options.CanDefer {
		return ErrReCreateDeferredOrders
	} else if existingOrder.Options.SwitchFlow && existingOrder.ServiceType == model.ServiceFood && existingOrder.DisabledRecreate {
		return ErrReCreateSwitchFlowOrders
	}

	now := time.Now().UTC()
	order.UpdatedAt = now
	order.ExpireAt = now.Add(order.GetExpiredDuration(fapi.Cfg.OrderExpirationDuration, fapi.Cfg.DeferredOrderExpirationDuration, fapi.Cfg.SwitchFlowOrderExpirationDuration))
	order.SetStatus(domainModel.StatusAssigningDriver)
	order.DeliveringRound = existingOrder.DeliveringRound
	order.IsDistributed = existingOrder.IsDistributed
	order.ActualAssigningAt = existingOrder.ActualAssigningAt
	return fapi.OrderService.UpdateOrder(ctx, order)
}

func (fapi *FoodProviderImpl) getOrderInfoByIdempotencyLockKey(ctx context.Context, quoteId string) (*domainModel.Order, error) {
	if val := fapi.Locker.GetState(ctx, locker.GetOrderInfoByIdempotencyLockKey(quoteId)); val != "" {
		var order *domainModel.Order
		if err := json.Unmarshal([]byte(val), &order); err != nil {
			return nil, err
		}

		return order, nil
	}

	return nil, nil
}

func (fapi *FoodProviderImpl) tryLockingOrderWithRetry(ctx context.Context, orderID string, opts ...retry.Option) error {
	defaultOptions := []retry.Option{
		retry.RetryIf(func(err error) bool {
			return err == middlewares.ErrOrderLocked
		}),
		retry.LastErrorOnly(true),
	}
	return retry.Do(
		func() error {
			ok, err := fapi.Locker.Lock(ctx, getLockOrderKey(orderID))
			if err != nil {
				return err
			}
			if !ok {
				return middlewares.ErrOrderLocked
			}
			return nil
		},
		append(defaultOptions, opts...)...,
	)
}

// TODO: Remove after LMF-14348 is done
// Image -> Force all items to not have an image, since mart cannot send us an image during the edit order.
// FullPrice -> They send to us but they're not sure what it is.
func martRMSItemsWithoutDirtyFields(items []domainModel.Item) []domainModel.Item {
	for i := 0; i < len(items); i++ {
		items[i].Image = ""
		items[i].FullPricePerItem = items[i].PricePerItem
	}

	return items
}

func martItemsWithDefaultGroup(items []domainModel.Item, invalidKeywordsFromConfig []string) []domainModel.Item {
	invalidKeywords := make([]string, 0, len(invalidKeywordsFromConfig))
	for _, kw := range invalidKeywordsFromConfig {
		if strings.TrimSpace(kw) == "" {
			continue
		}
		invalidKeywords = append(invalidKeywords, kw)
	}

	for i := 0; i < len(items); i++ {
		for _, group := range items[i].Groups {
			isGroupValid := true
			for _, kw := range invalidKeywords {
				if strings.Contains(strings.ToLower(group), strings.ToLower(kw)) {
					isGroupValid = false
					break
				}
			}

			if isGroupValid {
				items[i].DefaultGroup = group
				break
			}
		}

		if items[i].DefaultGroup == "" && len(items[i].Groups) > 0 {
			items[i].DefaultGroup = items[i].Groups[0]
		}

		if items[i].DefaultGroup == "" && len(items[i].Groups) == 0 {
			items[i].DefaultGroup = "อื่น ๆ"
		}
	}

	return items
}
