package order

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func TestCalculateShiftModel(t *testing.T) {

	t.Run("food - valid delivery fee price when match shift", func(t *testing.T) {
		cfg := OrderAPIConfig{
			AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{WithHoldingTax: 0.03}},
		}
		ord := orderForShipModel()
		ord.Shifts = []model.OrderShift{
			{
				ShiftId:       "xx",
				DiscountPrice: 3.5,
			},
		}
		expected := model.DeliveryFeeSummary{
			BaseFee:         46.5,
			SubTotal:        46.5,
			Total:           46.5,
			WithholdingTax:  1.4,
			ShiftPriceValue: 3.5,
		}

		driver := &model.Driver{
			Shifts: []string{"xx"},
			Region: model.RegionCode("dummy_region"),
		}

		o := calculateShiftModel(driver, ord, cfg)

		actual := o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee

		require.Equal(t, expected.BaseFee, actual.BaseFee)
		require.Equal(t, expected.SubTotal, actual.SubTotal)
		require.Equal(t, expected.Total, actual.Total)
		require.Equal(t, expected.WithholdingTax, actual.WithholdingTax)
		require.Equal(t, expected.ShiftPriceValue, actual.ShiftPriceValue)
	})

	t.Run("mart - valid delivery fee price when match shift", func(t *testing.T) {
		cfg := OrderAPIConfig{
			AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{WithHoldingTax: 0.03}},
		}
		ord := orderForShipModel()
		ord.ServiceType = model.ServiceMart
		ord.Shifts = []model.OrderShift{
			{
				ShiftId:       "xx",
				DiscountPrice: 3.5,
			},
		}
		expected := model.DeliveryFeeSummary{
			BaseFee:         46.5,
			SubTotal:        46.5,
			Total:           46.5,
			WithholdingTax:  1.4,
			ShiftPriceValue: 3.5,
		}

		driver := &model.Driver{
			Shifts: []string{"xx"},
			Region: model.RegionCode("dummy_region"),
		}

		o := calculateShiftModel(driver, ord, cfg)

		actual := o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee

		require.Equal(t, expected.BaseFee, actual.BaseFee)
		require.Equal(t, expected.SubTotal, actual.SubTotal)
		require.Equal(t, expected.Total, actual.Total)
		require.Equal(t, expected.WithholdingTax, actual.WithholdingTax)
		require.Equal(t, expected.ShiftPriceValue, actual.ShiftPriceValue)
	})

	t.Run("messenger - valid delivery fee price when match shift", func(t *testing.T) {
		cfg := OrderAPIConfig{
			AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{WithHoldingTax: 0.03}},
		}
		ord := orderForShipModel()
		ord.ServiceType = model.ServiceMessenger
		ord.Shifts = []model.OrderShift{
			{
				ShiftId:       "xx",
				DiscountPrice: 3.5,
			},
		}
		expected := model.DeliveryFeeSummary{
			BaseFee:         46.5,
			SubTotal:        46.5,
			Total:           46.5,
			WithholdingTax:  1.4,
			ShiftPriceValue: 3.5,
		}

		driver := &model.Driver{
			Shifts: []string{"xx"},
			Region: model.RegionCode("dummy_region"),
		}

		o := calculateShiftModel(driver, ord, cfg)

		actual := o.Routes[ord.GetPayAtStop()].PriceSummary.DeliveryFee

		require.Equal(t, expected.BaseFee, actual.BaseFee)
		require.Equal(t, expected.SubTotal, actual.SubTotal)
		require.Equal(t, expected.Total, actual.Total)
		require.Equal(t, expected.WithholdingTax, actual.WithholdingTax)
		require.Equal(t, expected.ShiftPriceValue, actual.ShiftPriceValue)
	})

	t.Run("valid delivery fee price when not match shift", func(t *testing.T) {
		cfg := OrderAPIConfig{
			AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{WithHoldingTax: 0.03}},
		}
		ord := orderForShipModel()
		ord.Shifts = []model.OrderShift{
			{
				ShiftId:       "xx",
				DiscountPrice: 3.5,
			},
			{
				ShiftId:       "yy",
				DiscountPrice: 3.5,
			},
		}
		expected := model.DeliveryFeeSummary{
			BaseFee:         50,
			SubTotal:        50,
			Total:           50,
			WithholdingTax:  1.5,
			ShiftPriceValue: 0,
		}

		driver := &model.Driver{
			Shifts: []string{"aa", "bb"},
			Region: model.RegionCode("dummy_region"),
		}

		o := calculateShiftModel(driver, ord, cfg)

		actual := o.Routes[1].PriceSummary.DeliveryFee

		require.Equal(t, expected.BaseFee, actual.BaseFee)
		require.Equal(t, expected.SubTotal, actual.SubTotal)
		require.Equal(t, expected.Total, actual.Total)
		require.Equal(t, expected.WithholdingTax, actual.WithholdingTax)
		require.Equal(t, expected.ShiftPriceValue, actual.ShiftPriceValue)
	})

	t.Run("valid delivery fee when no shift in order", func(t *testing.T) {
		cfg := OrderAPIConfig{
			AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{WithHoldingTax: 0.03}},
		}
		ord := orderForShipModel()
		ord.Shifts = []model.OrderShift{}
		expected := model.DeliveryFeeSummary{
			BaseFee:         50,
			SubTotal:        50,
			Total:           50,
			WithholdingTax:  1.5,
			ShiftPriceValue: 0,
		}

		driver := &model.Driver{
			Shifts: []string{"xx"},
			Region: model.RegionCode("dummy_region"),
		}

		o := calculateShiftModel(driver, ord, cfg)
		actual := o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee
		require.Equal(t, expected.BaseFee, actual.BaseFee)
		require.Equal(t, expected.SubTotal, actual.SubTotal)
		require.Equal(t, expected.Total, actual.Total)
		require.Equal(t, expected.WithholdingTax, actual.WithholdingTax)
		require.Equal(t, expected.ShiftPriceValue, actual.ShiftPriceValue)
	})

	t.Run("valid delivery fee when no shift in driver", func(t *testing.T) {
		cfg := OrderAPIConfig{
			AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{WithHoldingTax: 0.03}},
		}
		ord := orderForShipModel()
		ord.Shifts = []model.OrderShift{
			{
				ShiftId:       "xx",
				DiscountPrice: 3.5,
			},
		}
		expected := model.DeliveryFeeSummary{
			BaseFee:         50,
			SubTotal:        50,
			Total:           50,
			WithholdingTax:  1.5,
			ShiftPriceValue: 0,
		}

		driver := &model.Driver{
			Shifts: []string{},
			Region: model.RegionCode("dummy_region"),
		}

		o := calculateShiftModel(driver, ord, cfg)
		actual := o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee
		require.Equal(t, expected.BaseFee, actual.BaseFee)
		require.Equal(t, expected.SubTotal, actual.SubTotal)
		require.Equal(t, expected.Total, actual.Total)
		require.Equal(t, expected.WithholdingTax, actual.WithholdingTax)
		require.Equal(t, expected.ShiftPriceValue, actual.ShiftPriceValue)
	})

	t.Run("valid delivery fee when no shift in driver and in order", func(t *testing.T) {
		cfg := OrderAPIConfig{
			AtomicOrderDBConfig: &AtomicOrderDBConfig{Config: OrderDBConfig{WithHoldingTax: 0.03}},
		}
		ord := orderForShipModel()
		ord.Shifts = []model.OrderShift{}
		expected := model.DeliveryFeeSummary{
			BaseFee:         50,
			SubTotal:        50,
			Total:           50,
			WithholdingTax:  1.5,
			ShiftPriceValue: 0,
		}

		driver := &model.Driver{
			Shifts: []string{},
			Region: model.RegionCode("dummy_region"),
		}

		o := calculateShiftModel(driver, ord, cfg)
		actual := o.Routes[o.GetPayAtStop()].PriceSummary.DeliveryFee
		require.Equal(t, expected.BaseFee, actual.BaseFee)
		require.Equal(t, expected.SubTotal, actual.SubTotal)
		require.Equal(t, expected.Total, actual.Total)
		require.Equal(t, expected.WithholdingTax, actual.WithholdingTax)
		require.Equal(t, expected.ShiftPriceValue, actual.ShiftPriceValue)
	})
}

func orderForShipModel() *model.Order {
	return &model.Order{
		Quote: model.Quote{
			ServiceType: model.ServiceFood,
			Routes: []model.Stop{
				{
					PriceSummary: model.PriceSummary{
						DeliveryFee: model.DeliveryFeeSummary{
							RawBaseFee:          50,
							BaseFee:             50,
							SubTotal:            50,
							Total:               50,
							WithholdingTax:      1.5,
							OnTopCommissionFare: 10,
							OnTopWithholdingTax: 0.03,
						},
						Total: 50,
					},
				},
				{
					PriceSummary: model.PriceSummary{
						DeliveryFee: model.DeliveryFeeSummary{
							RawBaseFee:          50,
							BaseFee:             50,
							SubTotal:            50,
							Total:               50,
							WithholdingTax:      1.5,
							OnTopCommissionFare: 10,
							OnTopWithholdingTax: 0.03,
						},
						Total: 50,
					},
				},
			},
			RevenuePrincipalModel: true,
		},
	}
}

func Test_shouldReassignOrdersBehindWhenDelayed(t *testing.T) {
	t.Parallel()

	createDeps := func(enableCookingTimeDelayReassign bool) *AcceptorDeps {
		return &AcceptorDeps{Cfg: OrderAPIConfig{AtomicOrderDBConfig: NewAtomicOrderDBConfig(OrderDBConfig{DisableCookingTimeDelayReassign: !enableCookingTimeDelayReassign})}}
	}
	createServiceArea := func(enableCookingTimeDelayReassign bool) *model.ServiceArea {
		return &model.ServiceArea{CookingTimeDelayReassignEnabled: enableCookingTimeDelayReassign}
	}
	createOrder := func(serviceType model.Service, withPrediction bool) model.Order {
		o := model.Order{
			OrderID: "OrderID1",
			Quote:   model.Quote{ServiceType: serviceType},
		}
		if withPrediction {
			o.Prediction = &model.PredictionFeatures{}
		}
		return o
	}
	createRecord := func(lockDuration time.Duration, isMultiplePickup bool) model.Record {
		return model.Record{
			LockDuration:     lockDuration,
			IsMultiplePickup: isMultiplePickup,
		}
	}

	t.Run("order with prediction - when feature is enabled, should be added into cooking time delay repo", func(tt *testing.T) {
		tt.Parallel()
		o1 := createOrder(model.ServiceFood, true)
		o2 := createOrder(model.ServiceMart, true)
		deps := createDeps(true)
		serviceArea := createServiceArea(true)
		rec := createRecord(0, false)
		require.Equal(tt, true, shouldReassignOrdersBehindWhenDelayed(deps, o1, rec, serviceArea))
		require.Equal(tt, true, shouldReassignOrdersBehindWhenDelayed(deps, o2, rec, serviceArea))
	})

	t.Run("order with prediction - when feature is disabled (db config), should not be added into cooking time delay repo", func(tt *testing.T) {
		tt.Parallel()
		o1 := createOrder(model.ServiceFood, true)
		o2 := createOrder(model.ServiceMart, true)
		deps := createDeps(false)
		serviceArea := createServiceArea(true)
		rec := createRecord(0, false)
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o1, rec, serviceArea))
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o2, rec, serviceArea))
	})

	t.Run("order with prediction - when feature is disabled (service area), should not be added into cooking time delay repo", func(tt *testing.T) {
		tt.Parallel()
		o1 := createOrder(model.ServiceFood, true)
		o2 := createOrder(model.ServiceMart, true)
		deps := createDeps(true)
		serviceArea := createServiceArea(false)
		rec := createRecord(0, false)
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o1, rec, serviceArea))
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o2, rec, serviceArea))
	})

	t.Run("order without prediction - when feature is enabled, should not be added into cooking time delay repo", func(tt *testing.T) {
		tt.Parallel()
		o1 := createOrder(model.ServiceFood, false)
		o2 := createOrder(model.ServiceMart, false)
		deps := createDeps(true)
		serviceArea := createServiceArea(true)
		rec := createRecord(0, false)
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o1, rec, serviceArea))
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o2, rec, serviceArea))
	})

	t.Run("food with prediction (mp1) - when feature is enabled, should not be added into cooking time delay repo", func(tt *testing.T) {
		tt.Parallel()
		o := createOrder(model.ServiceFood, true)
		deps := createDeps(true)
		serviceArea := createServiceArea(true)
		rec := createRecord(10*time.Minute, false)
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o, rec, serviceArea))
	})

	t.Run("food with prediction (mp2) - when feature is enabled, should not be added into cooking time delay repo", func(tt *testing.T) {
		tt.Parallel()
		o := createOrder(model.ServiceFood, true)
		deps := createDeps(true)
		serviceArea := createServiceArea(true)
		rec := createRecord(0, true)
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o, rec, serviceArea))
	})

	t.Run("mart with prediction (pick pack) - when feature is enabled, should not be added into cooking time delay repo", func(tt *testing.T) {
		tt.Parallel()
		o := createOrder(model.ServiceMart, true)
		o.Options.IsPickPack = true
		deps := createDeps(true)
		serviceArea := createServiceArea(true)
		rec := createRecord(0, false)
		require.Equal(tt, false, shouldReassignOrdersBehindWhenDelayed(deps, o, rec, serviceArea))
	})
}
