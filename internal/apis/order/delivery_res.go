package order

import (
	"math"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

// QuoteResponse ...
type QuoteResponse struct {
	// QuoteID is quote id. It's returns as same as quote id in QuoteRequest.
	QuoteID string `json:"quoteId"`

	// Distance is distance each routing.
	Distance types.Distance `json:"distance"`

	DeliveryFee model.DeliveryFeeSummary `json:"deliveryFee"`

	ExtraCharges []model.ExtraCharge `json:"extraCharges,omitempty"`

	Total float64 `json:"total"` // DeliveryFee.Total + AdditionalServiceFee.Total

	EstimateDurationSec model.DurationSecond `json:"estimateDurationSec"`

	CanDefer bool `json:"canDefer"`

	SwitchFlow bool `json:"isSwitchFlow"`

	MatchRate float64 `json:"matchRate"`

	EstimatedCompletionTime *CompletionTime `json:"estimatedCompletionTime"`

	// Return true when a sub total amount has been changed compared with latest request.
	IsDeliveryFeeChanged bool `json:"isDeliveryFeeChanged"`
}

type CompletionTime struct {
	Min time.Time `json:"min"`
	Max time.Time `json:"max"`
}

type CanDeferResponse struct {
	CanDefer                               bool    `json:"canDefer"`
	MatchRestaurantFirstMatchRateThreshold float64 `json:"matchRestaurantFirstMatchRateThreshold"`
}

// CreateOrderResponse ...
type CreateOrderResponse struct {
	// OrderID is quote id. It's returns as same as quote id in QuoteRequest.
	OrderID string `json:"orderId"`

	// Distance is distance each routing.
	Distance types.Distance `json:"distance"`

	DeliveryFee model.DeliveryFeeSummary `json:"deliveryFee"`

	ActualAssigningAt *time.Time `json:"actualAssigningAt,omitempty"`
}

// GetOrderStatusResponse is response for GetOrderStatus api.
type GetOrderStatusResponse struct {
	// Status is current status of an order.
	Status string `json:"status"`

	// Time that status was changed.
	Time time.Time `json:"time"`
}

// GetOrderDetailResponse is response for GetOrderDetail api.
type GetOrderDetailResponse struct {
	OrderID string `json:"orderId"`

	ServiceType model.Service      `json:"serviceType"`
	PriceScheme string             `json:"priceScheme"`
	Region      model.RegionCode   `json:"region"`
	Distance    types.Distance     `json:"distance"`
	Options     model.OrderOptions `json:"options"`
	Status      model.Status       `json:"status"`
	HeadTo      int                `json:"headTo"`

	// Routes ...
	PayAtStop int                  `json:"payAtStop"`
	Routes    []StopRes            `json:"routes"`
	History   map[string]time.Time `json:"history"`

	CommissionRate      int         `json:"commissionRate"`
	Commission          types.Money `json:"commission"`
	WithholdingTax      types.Money `json:"withholdingTax"`
	OnTopWithholdingTax types.Money `json:"onTopWithholdingTax"`
	OnTopFare           types.Money `json:"onTopFare"`
	TotalTax            types.Money `json:"totalTax"`
	ExtraCharge         types.Money `json:"extraCharge"`

	SpecialEvent []string `json:"specialEvent"`

	RevenueAgentModel bool `json:"revenueAgentModel"`

	ExpireAt  time.Time `json:"expireAt"`
	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

func NewGetOrderDetailResponse(order *model.Order) *GetOrderDetailResponse {
	priceSummary := order.PriceSummary()
	res := &GetOrderDetailResponse{
		OrderID:             order.OrderID,
		ServiceType:         order.ServiceType,
		PriceScheme:         order.PriceScheme,
		Region:              order.Region,
		Distance:            order.Distance,
		Options:             order.Options,
		Status:              order.Status,
		HeadTo:              order.HeadTo,
		History:             order.History,
		ExtraCharge:         types.Money(priceSummary.DeliveryFee.ExtraCharges.Total()),
		CommissionRate:      int(priceSummary.DeliveryFee.CommissionRate * 100),
		Commission:          types.Money(priceSummary.DeliveryFee.Commission),
		WithholdingTax:      types.Money(priceSummary.DeliveryFee.WithholdingTax),
		OnTopWithholdingTax: types.Money(priceSummary.DeliveryFee.OnTopWithholdingTax),
		OnTopFare:           types.Money(priceSummary.DeliveryFee.OnTopFare),
		TotalTax:            types.Money(priceSummary.DeliveryFee.TotalTax()),
		SpecialEvent:        order.SpecialEvent,
		ExpireAt:            order.ExpireAt,
		CreatedAt:           order.CreatedAt,
		UpdatedAt:           order.UpdatedAt,
		RevenueAgentModel:   !order.RevenuePrincipalModel,
		PayAtStop:           order.GetPayAtStop(),
	}

	res.Routes = make([]StopRes, len(order.Routes))
	for i, r := range order.Routes {
		res.Routes[i] = *res.newStopRes(r)
	}

	return res
}

func (os *GetOrderDetailResponse) newStopRes(s model.Stop) *StopRes {
	return &StopRes{
		ID:                    s.ID,
		Name:                  s.Name,
		Address:               s.Address,
		Phones:                s.Phones,
		Location:              s.Location,
		Memo:                  s.Memo,
		MemoType:              s.MemoType,
		PickingItems:          s.PickingItems,
		DeliveryItems:         s.DeliveryItems,
		CollectPayment:        s.CollectPayment,
		ItemsPrice:            s.ItemsPrice,
		EstimatedDeliveryTime: s.EstimatedDeliveryTime,
		Distance:              s.Distance,
		DeliveryStatus:        s.DeliveryStatus,
		Info:                  s.Info,
	}
}

// TipOrderResponse  ...
type TipOrderResponse struct {
	DriverID    string       `json:"driverID"`
	OrderStatus string       `json:"orderStatus"`
	Order       *model.Order `json:"-"`
}

type CalculateDeliveryFeeRes struct {
	From               model.Location           `json:"from"`
	BaseFee            types.Money              `json:"baseFee"`
	ExtraCharges       []model.ExtraCharge      `json:"extraCharges"`
	DeliveryPrices     []DeliveryPrice          `json:"deliveryPrices"`
	DeliveryFeeSummary model.DeliveryFeeSummary `json:"deliveryFeeSummary"`
}

type FareMetadataRes struct {
	StartingFee      types.Money       `json:"startingFee"`
	DistanceUnitFees []DistanceUnitFee `json:"distanceUnitFees"`
}

type DistanceUnitFee struct {
	From     int32       `json:"fromInKm"`
	To       int32       `json:"toInKm"`
	FeePerKM types.Money `json:"feePerKM"`
}

func ToDistanceUnitFees(in model.RoadFeeTiers) []DistanceUnitFee {
	results := make([]DistanceUnitFee, 0, len(in))

	for i, tier := range in {
		from := tier.From() / types.KM
		to := int32(math.MaxInt32)

		if i+1 < len(in) {
			to = (in[i+1].From() / types.KM).Int32()
		}

		results = append(results, DistanceUnitFee{
			From:     from.Int32(),
			To:       to,
			FeePerKM: tier.FeePerKM(),
		})
	}
	return results
}

type DeliveryPrice struct {
	RoadFee       types.Money          `json:"roadFee"`
	Distance      types.Distance       `json:"distance"`
	EstimatedTime model.DurationSecond `json:"estimatedTime"`
	To            model.Location       `json:"to"`
}

func NewDeliveryPrice(to model.Location, roadFee types.Money, distance types.Distance, estimatedTime model.DurationSecond) DeliveryPrice {
	return DeliveryPrice{
		To:            to,
		Distance:      distance,
		EstimatedTime: estimatedTime,
		RoadFee:       roadFee,
	}
}

func NewFareMetadataRes(priceScheme model.SettingDeliveryFeePriceScheme) FareMetadataRes {
	distanceUnitFees := ToDistanceUnitFees(priceScheme.RoadFeeTiers())
	return FareMetadataRes{
		StartingFee:      priceScheme.BaseFee(),
		DistanceUnitFees: distanceUnitFees,
	}
}

type CancelOrderResponse struct {
	CancelDetail CancelDetail `json:"cancelDetail"`
}

type CancelDetail struct {
	CancelledBy        string `json:"cancelledBy"`
	Source             string `json:"source"`
	CancelledWithQuota bool   `json:"cancelledWithQuota"`
}

type ValidationError struct {
	msg string
}

func (e ValidationError) Error() string {
	return e.msg
}

func NewValidationError(msg string) ValidationError {
	return ValidationError{msg: msg}
}

func NewCalculateDeliveryFeeRes(from model.Location, baseFee types.Money, extraCharges []model.ExtraCharge, deliveryFeeSummary model.DeliveryFeeSummary, deliveryPrices ...DeliveryPrice) CalculateDeliveryFeeRes {
	return CalculateDeliveryFeeRes{
		From:               from,
		BaseFee:            baseFee,
		ExtraCharges:       extraCharges,
		DeliveryPrices:     deliveryPrices,
		DeliveryFeeSummary: deliveryFeeSummary,
	}
}
