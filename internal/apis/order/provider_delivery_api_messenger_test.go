package order

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model/mock_model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestMessengerDeliveryFeeAPI_CalculateDeliveryPrice(t *testing.T) {
	t.Parallel()

	makeReq := func(req CalculateDeliveryFeeReq) (*gin.Context, *httptest.ResponseRecorder) {
		url := "/v1/delivery/driver/price"

		gctx, recorder := testutil.TestRequestContext("POST", url, testutil.JSON(req))
		gctx.Request.Header.Add("X-Region", req.Region)

		return gctx, recorder
	}

	t.Run("Success calculating delivery fee should return 200", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"
		defaultScheme := domainModel.NewLegacyDeliveryFeeSettingScheme(nil, types.NewMoney(40))
		calculator := domainModel.NewDeliveryFeeSetting("<delivery-fee-setting-1>", domainModel.ServiceMessenger, domainModel.RegionCode(region), defaultScheme)

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: domainModel.ServiceMessenger,
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Cannot call area service should return 500", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"
		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{}, errors.New("mock area service error"))

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: domainModel.ServiceMessenger,
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("map service error should return 500", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"
		defaultScheme := domainModel.NewLegacyDeliveryFeeSettingScheme(nil, types.NewMoney(40))
		calculator := domainModel.NewDeliveryFeeSetting("<delivery-fee-setting-1>", domainModel.ServiceMessenger, domainModel.RegionCode(region), defaultScheme)

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil, errors.New("mock error"))

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: domainModel.ServiceMessenger,
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("Delivery fee calculator not found should return 500", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(domainModel.DeliveryFeeSetting{}, domainModel.SettingDeliveryFeePriceScheme{}, repository.ErrNotFound)

		gctx, recorder := makeReq(CalculateDeliveryFeeReq{
			ServiceType: domainModel.ServiceMessenger,
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.CalculateDeliveryPrice(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestMessengerQuoteHandlerInternal(t *testing.T) {
	t.Parallel()

	t.Run("create non-existing quote", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: domainModel.ServiceMessenger,
			QuoteID:     "LMFQ-1231541",
			Routes: []domainModel.Stop{
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: true,
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			DoAndReturn(func(ctx context.Context, serviceType domainModel.Service, area string, loc service.Location) ([]domainModel.RegionCode, error) {
				require.Equal(tt, q.Routes[0].Location.Lat, loc.Lat())
				require.Equal(tt, q.Routes[0].Location.Lng, loc.Lng())
				return []domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil
			})

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(30.0, 0.0, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), q.ServiceType).Return(false)

		_, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.NoError(tt, err)
	})

	t.Run("create quote with option round-trip (distance for last route will not be included in delivery fee calculation)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: domainModel.ServiceMessenger,
			QuoteID:     "LMFQ-1231541",
			Routes: []domainModel.Stop{
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: true,
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
			Options: OptionsReq{RoundTrip: true},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		gomock.InOrder(
			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 100.54702667857362, Lng: 13.743937518724506}, mapservice.Location{Lat: 100.5072724, Lng: 13.7487528}, gomock.Any()).
				Return(&domainModel.MapRoute{Distance: 5930}, nil, nil),
			deps.mapService.EXPECT().
				FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 100.5072724, Lng: 13.7487528}, mapservice.Location{Lat: 100.54702667857362, Lng: 13.743937518724506}, gomock.Any()).
				Return(&domainModel.MapRoute{Distance: 5930}, nil, nil),
		)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(30.0, 0.0, 5930))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), q.ServiceType).Return(false)

		resp, _ := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Equal(tt, types.Distance(5930), resp.Distance)
	})

	t.Run("create quote with require box", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: domainModel.ServiceMessenger,
			QuoteID:     "LMFQ-1231541",
			Routes: []domainModel.Stop{
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: true,
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
			Options: OptionsReq{
				RequireBox: true,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(30.0, 0.0, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), q.ServiceType).Return(false)

		_, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.NoError(tt, err)
	})

	t.Run("error on get distribute region should return error", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: domainModel.ServiceMessenger,
			QuoteID:     "LMFQ-1231541",
			Routes: []domainModel.Stop{
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: true,
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
		}

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{}, errors.New("errors"))

		_, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Error(tt, err)
	})

	t.Run("calculate distance from manmap and response back", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: domainModel.ServiceMessenger,
			QuoteID:     "LMFQ-1231541",
			Routes: []domainModel.Stop{
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: true,
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 100.54702667857362, Lng: 13.743937518724506}, mapservice.Location{Lat: 100.5072724, Lng: 13.7487528}, gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 5930}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(30.0, 0.0, 5930))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), q.ServiceType).Return(false)

		resp, _ := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Equal(tt, types.Distance(5930), resp.Distance)
	})

	t.Run("returns error if Routes has less than 2 stop", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: domainModel.ServiceMessenger,
			QuoteID:     "LMFQ-1231541",
			Routes: []domainModel.Stop{
				{
					CollectPayment: true,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
			},
		}

		testapi, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		_, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.Error(tt, err)
	})

	t.Run("returns error if Routes has less than 3 stops with option round-trip", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: domainModel.ServiceMessenger,
			QuoteID:     "LMFQ-1231541",
			Routes: []domainModel.Stop{
				{
					CollectPayment: true,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
			},
			Options: OptionsReq{RoundTrip: true},
		}

		testapi, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		_, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.EqualError(tt, err, "routes with round-trip option should have greater than or equal 3 stops")
	})

	t.Run("calculate base fee and send to quote to CreateOrUpdateQuote (with collect payment at route 0)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			expectedBase = 40.0
			expectedRoad = 10.0
		)

		qreq := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			UserID:      "U1231542",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					ID:   "125891ka",
					Name: "Jim Burger",
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					CollectPayment: true,
					Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
					Phones:         []string{"0891231234"},
					PickingItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					Memo: "4th floor, DedicatedZone Eden.",
				},
				{
					ID:   "",
					Name: "Uncle Tob",
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
					CollectPayment: false,
					Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
					DeliveryItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					ItemsPrice: 150,
					Memo:       "Near Tesco lotus",
				},
			},
			PaymentMethod: domainModel.PaymentMethodCash,
			DeliveryFee: domainModel.DeliveryFeeSummary{
				AdditionalFee: map[string]float64{},
				Discounts: []domainModel.Discount{
					{Type: domainModel.DiscountTypeCoupon, Code: "HELLO70", Discount: 70},
				},
			},
		}
		expectedRegion := "AYUTTHAYA"

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(expectedBase, expectedRoad, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), qreq.ServiceType).Return(false)

		q, err := api.quote(context.Background(), qreq, "AYUTTHAYA")
		require.NoError(tt, err)
		require.Equal(tt, qreq.QuoteID, q.QuoteID)
		require.Equal(tt, qreq.UserID, q.UserID)
		require.Equal(tt, qreq.ServiceType, q.ServiceType)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
		require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
		require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
		require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
		require.NotEqual(tt, 0, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
		require.NotEqual(tt, 0, q.Routes[0].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, types.Distance(1000), q.Distance)
	})

	t.Run("calculate base fee and send to quote to CreateOrUpdateQuote (with additional service)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			expectedBase          = 40.0
			expectedRoad          = 10.0
			expectedDeliveryFee   = expectedBase + expectedRoad
			expectedAdditionalFee = 40.0
			expectedTotal         = expectedDeliveryFee + expectedAdditionalFee
		)

		qreq := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			UserID:      "U1231542",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					ID:   "125891ka",
					Name: "Jim Burger",
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					CollectPayment: true,
					Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
					Phones:         []string{"0891231234"},
					PickingItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					Memo: "4th floor, DedicatedZone Eden.",
				},
				{
					ID:   "",
					Name: "Uncle Tob",
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
					CollectPayment: false,
					Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
					DeliveryItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					ItemsPrice: 150,
					Memo:       "Near Tesco lotus",
				},
			},
			AdditionalServiceFee: AdditionalServiceReq(AdditionalServiceRequest{
				Total: expectedAdditionalFee,
				AdditionalServiceItems: []AdditionalServiceItemRequest{
					{
						Topic:       "BAG",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_bag.jpg",
						Title:       "กระเป๋าส่งของ",
						Description: "ขอไรเดอร์ที่มีกระเป๋าหลัง",
						SortNumber:  0,
						Price:       0,
					}, {
						Topic:       "DOCUMENT_PROCESSING",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_document_processing.jpg",
						Title:       "เดินเอกสาร",
						Description: "ช่วยวางบิล, ทำเอกสาร, ขอลายเซ็น",
						SortNumber:  1,
						Price:       20.0,
					}, {
						Topic:       "POSTAL_AND_SHIPPING",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_shiping.jpg",
						Title:       "ฝากส่งไปรษณีย์หรือบริการขนส่ง",
						Description: "แพ็กพัสดุไว้ให้พร้อมก็พอแล้ว",
						SortNumber:  300,
						Price:       20.0,
					},
				},
			}),
			PaymentMethod: domainModel.PaymentMethodCash,
			DeliveryFee: domainModel.DeliveryFeeSummary{
				AdditionalFee: map[string]float64{},
			},
		}
		expectedRegion := "AYUTTHAYA"

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(expectedBase, expectedRoad, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), qreq.ServiceType).Return(false)

		q, err := api.quote(context.Background(), qreq, "AYUTTHAYA")
		require.NoError(tt, err)
		require.Equal(tt, qreq.QuoteID, q.QuoteID)
		require.Equal(tt, qreq.UserID, q.UserID)
		require.Equal(tt, qreq.ServiceType, q.ServiceType)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
		require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
		require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
		require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
		require.Equal(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
		require.Equal(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, types.Distance(1000), q.Distance)
	})

	t.Run("calculate base fee and send to quote to CreateOrUpdateQuote (with additional service and half discount)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			expectedBase          = 40.0
			expectedRoad          = 10.0
			expectedAdditionalFee = 40.0
			expectedDeliveryFee   = 50.0
			expectedDiscount      = 70.0
			expectedTotal         = 20.0
		)

		qreq := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			UserID:      "U1231542",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					ID:   "125891ka",
					Name: "Jim Burger",
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					CollectPayment: true,
					Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
					Phones:         []string{"0891231234"},
					PickingItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					Memo: "4th floor, DedicatedZone Eden.",
				},
				{
					ID:   "",
					Name: "Uncle Tob",
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
					CollectPayment: false,
					Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
					DeliveryItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					ItemsPrice: 150,
					Memo:       "Near Tesco lotus",
				},
			},
			AdditionalServiceFee: AdditionalServiceReq(AdditionalServiceRequest{
				Total: expectedAdditionalFee,
				AdditionalServiceItems: []AdditionalServiceItemRequest{
					{
						Topic:       "BAG",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_bag.jpg",
						Title:       "กระเป๋าส่งของ",
						Description: "ขอไรเดอร์ที่มีกระเป๋าหลัง",
						SortNumber:  0,
						Price:       0,
					}, {
						Topic:       "DOCUMENT_PROCESSING",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_document_processing.jpg",
						Title:       "เดินเอกสาร",
						Description: "ช่วยวางบิล, ทำเอกสาร, ขอลายเซ็น",
						SortNumber:  1,
						Price:       20.0,
					}, {
						Topic:       "POSTAL_AND_SHIPPING",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_shiping.jpg",
						Title:       "ฝากส่งไปรษณีย์หรือบริการขนส่ง",
						Description: "แพ็กพัสดุไว้ให้พร้อมก็พอแล้ว",
						SortNumber:  300,
						Price:       20.0,
					},
				},
			}),
			PaymentMethod: domainModel.PaymentMethodCash,
			Discounts: []DiscountReq{
				{Type: domainModel.DiscountTypeCoupon, Code: "HELLO70", Discount: expectedDiscount},
			},
		}
		expectedRegion := "AYUTTHAYA"

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(expectedBase, expectedRoad, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), qreq.ServiceType).Return(false)

		q, err := api.quote(context.Background(), qreq, "AYUTTHAYA")
		require.NoError(tt, err)
		require.Equal(tt, qreq.QuoteID, q.QuoteID)
		require.Equal(tt, qreq.UserID, q.UserID)
		require.Equal(tt, qreq.ServiceType, q.ServiceType)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
		require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
		require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
		require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
		require.Equal(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
		require.Equal(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, types.Distance(1000), q.Distance)
	})

	t.Run("calculate base fee and send to quote to CreateOrUpdateQuote (with additional service and fully discount)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			expectedBase          = 50.0
			expectedRoad          = 10.0
			expectedAdditionalFee = 40.0
			expectedDeliveryFee   = 60.0
			expectedDiscount      = 100.0
			expectedTotal         = 0.0
		)

		qreq := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			UserID:      "U1231542",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					ID:   "125891ka",
					Name: "Jim Burger",
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					CollectPayment: true,
					Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
					Phones:         []string{"0891231234"},
					PickingItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					Memo: "4th floor, DedicatedZone Eden.",
				},
				{
					ID:   "",
					Name: "Uncle Tob",
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
					CollectPayment: false,
					Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
					DeliveryItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					ItemsPrice: 150,
					Memo:       "Near Tesco lotus",
				},
			},
			AdditionalServiceFee: AdditionalServiceReq(AdditionalServiceRequest{
				Total: expectedAdditionalFee,
				AdditionalServiceItems: []AdditionalServiceItemRequest{
					{
						Topic:       "BAG",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_bag.jpg",
						Title:       "กระเป๋าส่งของ",
						Description: "ขอไรเดอร์ที่มีกระเป๋าหลัง",
						SortNumber:  0,
						Price:       0,
					}, {
						Topic:       "DOCUMENT_PROCESSING",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_document_processing.jpg",
						Title:       "เดินเอกสาร",
						Description: "ช่วยวางบิล, ทำเอกสาร, ขอลายเซ็น",
						SortNumber:  1,
						Price:       20.0,
					}, {
						Topic:       "POSTAL_AND_SHIPPING",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_shiping.jpg",
						Title:       "ฝากส่งไปรษณีย์หรือบริการขนส่ง",
						Description: "แพ็กพัสดุไว้ให้พร้อมก็พอแล้ว",
						SortNumber:  300,
						Price:       20.0,
					},
				},
			}),
			PaymentMethod: domainModel.PaymentMethodCash,
			Discounts: []DiscountReq{
				{Type: domainModel.DiscountTypeCoupon, Code: "HELLO70", Discount: expectedDiscount},
			},
		}
		expectedRegion := "AYUTTHAYA"

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(expectedBase, expectedRoad, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), qreq.ServiceType).Return(false)

		q, err := api.quote(context.Background(), qreq, "AYUTTHAYA")
		require.NoError(tt, err)
		require.Equal(tt, qreq.QuoteID, q.QuoteID)
		require.Equal(tt, qreq.UserID, q.UserID)
		require.Equal(tt, qreq.ServiceType, q.ServiceType)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
		require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
		require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
		require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
		require.Equal(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
		require.Equal(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, types.Distance(1000), q.Distance)
	})

	t.Run("calculate base fee and send to quote to CreateOrUpdateQuote (with additional service and delivery discount)", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		var (
			expectedBase          = 50.0
			expectedRoad          = 10.0
			expectedAdditionalFee = 40.0
			expectedDeliveryFee   = 60.0
			expectedDiscount      = 60.0
			expectedTotal         = 40.0
		)

		qreq := &QuoteRequest{
			QuoteID:     "LMFQ-1231541",
			UserID:      "U1231542",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					ID:   "125891ka",
					Name: "Jim Burger",
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
					CollectPayment: true,
					Address:        "49 ระหว่างอารีย์สัมพันธ์ 3 - 4 สามเสนใน พญาไท กรุงเทพมหานคร 10400",
					Phones:         []string{"0891231234"},
					PickingItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					Memo: "4th floor, DedicatedZone Eden.",
				},
				{
					ID:   "",
					Name: "Uncle Tob",
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
					CollectPayment: false,
					Address:        "Sukhumvit 52 Alley, Khwaeng Bang Chak, Khet Phra Khanong, Krung Thep Maha Nakhon 10260",
					DeliveryItems: []domainModel.Item{
						{
							Name:     "Cheeseburger",
							Quantity: 1,
							Price:    150,
							Memo:     "Without prickle",
						},
					},
					ItemsPrice: 150,
					Memo:       "Near Tesco lotus",
				},
			},
			AdditionalServiceFee: AdditionalServiceReq(AdditionalServiceRequest{
				Total: expectedAdditionalFee,
				AdditionalServiceItems: []AdditionalServiceItemRequest{
					{
						Topic:       "BAG",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_bag.jpg",
						Title:       "กระเป๋าส่งของ",
						Description: "ขอไรเดอร์ที่มีกระเป๋าหลัง",
						SortNumber:  0,
						Price:       0,
					}, {
						Topic:       "DOCUMENT_PROCESSING",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_document_processing.jpg",
						Title:       "เดินเอกสาร",
						Description: "ช่วยวางบิล, ทำเอกสาร, ขอลายเซ็น",
						SortNumber:  1,
						Price:       20.0,
					}, {
						Topic:       "POSTAL_AND_SHIPPING",
						ImageUrl:    "https://line-objects-dev.com/lineman/messenger/additional-service/additional_service_shiping.jpg",
						Title:       "ฝากส่งไปรษณีย์หรือบริการขนส่ง",
						Description: "แพ็กพัสดุไว้ให้พร้อมก็พอแล้ว",
						SortNumber:  300,
						Price:       20.0,
					},
				},
			}),
			PaymentMethod: domainModel.PaymentMethodCash,
			Discounts: []DiscountReq{
				{Type: domainModel.DiscountTypeCoupon, Code: "HELLO70", Discount: expectedDiscount},
			},
		}
		expectedRegion := "AYUTTHAYA"

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), expectedRegion).
			Return(&model.ServiceArea{}, nil)
		deps.throttledDispatchDetailRepo.EXPECT().FindManyFromLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]domainModel.ThrottledDispatchDetailWithZoneCode{}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1000}, nil, nil)

		calculator := mock_model.NewMockDeliveryFeeCalculator(ctrl)
		calculator.EXPECT().
			Calculate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(domainModel.NewDeliveryFee(expectedBase, expectedRoad, 1000))
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(calculator, priceScheme, nil)
		deps.onTopFareRepo.EXPECT().
			GetOnTopFareByLocation(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.OnTopFare{}, nil)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsQuotePIPOnTopEnabled.Name).Return(false)
		deps.orderServiceV2.EXPECT().IsEnabledAgentModelCommissionAndTaxV2(gomock.Any(), qreq.ServiceType).Return(false)

		q, err := api.quote(context.Background(), qreq, "AYUTTHAYA")
		require.NoError(tt, err)
		require.Equal(tt, qreq.QuoteID, q.QuoteID)
		require.Equal(tt, qreq.UserID, q.UserID)
		require.Equal(tt, qreq.ServiceType, q.ServiceType)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.DeliveryFee.PaymentMethod)
		require.Equal(tt, qreq.PaymentMethod, q.Routes[0].PriceSummary.ItemFee.PaymentMethod)
		require.Equal(tt, expectedBase, q.Routes[0].PriceSummary.DeliveryFee.BaseFee, "base fee should be the same as DeliveryFeeConfig")
		require.Equal(tt, expectedRoad, q.Routes[0].PriceSummary.DeliveryFee.RoadFee)
		require.Equal(tt, map[string]float64{}, q.Routes[0].PriceSummary.DeliveryFee.AdditionalFee)
		require.Equal(tt, expectedDeliveryFee, q.Routes[0].PriceSummary.DeliveryFee.SubTotal)
		require.Equal(tt, expectedTotal, q.Routes[0].PriceSummary.DeliveryFee.Total)
		require.Equal(tt, types.Distance(1000), q.Distance)
	})

	t.Run("return error collect payment is required at one stop", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := &QuoteRequest{
			ServiceType: domainModel.ServiceMessenger,
			QuoteID:     "LMFQ-1231541",
			Routes: []domainModel.Stop{
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.54702667857362,
						Lng: 13.743937518724506,
					},
				},
				{
					CollectPayment: false,
					Location: domainModel.Location{
						Lat: 100.5072724,
						Lng: 13.7487528,
					},
				},
			},
			PayAtStop: 3,
			DeliveryFee: domainModel.DeliveryFeeSummary{
				RawBaseFee: 30,
				RoadFee:    0,
			},
		}
		expectedRegion := "AYUTTHAYA"

		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(expectedRegion)}, nil)

		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 100.54702667857362, Lng: 13.743937518724506}, mapservice.Location{Lat: 100.5072724, Lng: 13.7487528}, gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 5930}, nil, nil)

		defaultScheme := domainModel.NewLegacyDeliveryFeeSettingScheme(nil, types.NewMoney(40))
		calculator := domainModel.NewDeliveryFeeSetting("<delivery-fee-setting-1>", domainModel.ServiceMessenger, domainModel.RegionCode(expectedRegion), defaultScheme)
		priceScheme := CreateValidPriceScheme()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, expectedRegion, gomock.Any(), gomock.Any()).
			Return(*calculator, priceScheme, nil)

		_, err := testapi.quote(context.Background(), q, "AYUTTHAYA")
		require.EqualError(tt, err, "collect payment is required at one stop")
	})
}

func TestMessengerDeliveryAPI_CreateOrder(t *testing.T) {
	t.Parallel()
	req := func(req *CreateOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/messenger/order/create", testutil.JSON(req))
		return ctx, recorder
	}
	t.Run("create order from quote", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid   = "LMFQ-1231451"
			ordid = "LMFQ-1231451"
			resID = "res-id"
		)

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				ServiceType: domainModel.ServiceMessenger,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []domainModel.RegionCode{"AYUTTHAYA"},
			}, nil)
		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.True(tt, order.RevampedStatus)
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())

		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), mp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, ordid, resp.OrderID)
		require.Equal(t, 60.0, resp.DeliveryFee.BaseFee)
	})

	t.Run("create order from quote when enabled distribution-service flag correctly", func(tt *testing.T) {
		tt.Parallel()

		tt.Run("successfully", func(tt *testing.T) {
			const (
				qid   = "LMFQ-1231451"
				ordid = "LMFQ-1231451"
				resID = "res-id"
			)

			expireAt := time.Now().Add(10 * time.Minute)

			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			deps.quoteService.EXPECT().
				Find(gomock.Any(), qid, gomock.Any()).
				Return(&domainModel.Quote{
					ServiceType: domainModel.ServiceMessenger,
					QuoteID:     qid,
					Distance:    types.Distance(1000),
					Routes: []domainModel.Stop{
						{
							ID: resID,
						},
						{
							CollectPayment: true,
							PriceSummary: domainModel.PriceSummary{
								DeliveryFee: domainModel.DeliveryFeeSummary{
									BaseFee:  60,
									RoadFee:  55,
									SubTotal: 55,
									Total:    55,
								},
								Total: 0,
							},
						},
					},
					PayAtStop:         1,
					DistributeRegions: []domainModel.RegionCode{"AYUTTHAYA"},
				}, nil)
			deps.distributionConfigValidator.EXPECT().
				CanDalianMP(gomock.Any(), gomock.Any()).
				Return(false, nil)
			deps.orderService.EXPECT().
				CreateOrder(gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
					require.Equal(tt, ordid, order.OrderID)
					require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
					require.True(tt, order.RevampedStatus)
					order.ExpireAt = expireAt
					return nil
				})
			deps.orderService.EXPECT().
				GetOtherActiveMP(gomock.Any(), gomock.Any()).
				Return(nil, nil)
			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
			deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), ordid).Return(nil)

			deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
			deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
			deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
			deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
			deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
			deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), mp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

			ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
			wg := safe.CreateWaitGroupOnGctx(ctx)
			mp.CreateOrder(ctx)
			wg.Wait()
			require.Equal(t, http.StatusCreated, recorder.Code)
			var resp CreateOrderResponse
			testutil.DecodeJSON(t, recorder.Body, &resp)
			require.Equal(t, ordid, resp.OrderID)
			require.Equal(t, 60.0, resp.DeliveryFee.BaseFee)
		})

		tt.Run("error and fallback", func(tt *testing.T) {
			const (
				qid   = "LMFQ-1231451"
				ordid = "LMF-1231451"
				resID = "res-id"
			)

			expireAt := time.Now().Add(10 * time.Minute)

			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()
			mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
			deps.quoteService.EXPECT().
				Find(gomock.Any(), qid, gomock.Any()).
				Return(&domainModel.Quote{
					ServiceType: domainModel.ServiceMessenger,
					QuoteID:     qid,
					Distance:    types.Distance(1000),
					Routes: []domainModel.Stop{
						{
							ID: resID,
						},
						{
							CollectPayment: true,
							PriceSummary: domainModel.PriceSummary{
								DeliveryFee: domainModel.DeliveryFeeSummary{
									BaseFee:  60,
									RoadFee:  55,
									SubTotal: 55,
									Total:    55,
								},
								Total: 0,
							},
						},
					},
					PayAtStop:         1,
					DistributeRegions: []domainModel.RegionCode{"AYUTTHAYA"},
				}, nil)
			deps.distributionConfigValidator.EXPECT().
				CanDalianMP(gomock.Any(), gomock.Any()).
				Return(false, nil)
			deps.orderService.EXPECT().
				CreateOrder(gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
					require.Equal(tt, ordid, order.OrderID)
					require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
					require.True(tt, order.RevampedStatus)
					order.ExpireAt = expireAt
					return nil
				})
			deps.orderService.EXPECT().
				GetOtherActiveMP(gomock.Any(), gomock.Any()).
				Return(nil, nil)
			deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
			deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), ordid).Return(errors.New("something went wrong"))
			deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any()).Times(0)
			deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any()).Times(0)
			deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any()).Times(0)
			deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
			deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
			deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), mp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true).Times(0)

			ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
			wg := safe.CreateWaitGroupOnGctx(ctx)
			mp.CreateOrder(ctx)
			wg.Wait()
			require.Equal(t, http.StatusBadRequest, recorder.Code)
		})
	})

	t.Run("create order from quote with idempotency", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid            = "LMFQ-1231451"
			ordid          = "LMFQ-1231451"
			resID          = "res-id"
			idempotencyKey = "A-B-C"
		)

		expireAt := time.Now().Add(10 * time.Minute)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				ServiceType: domainModel.ServiceMessenger,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []domainModel.RegionCode{"AYUTTHAYA"},
			}, nil)

		deps.distributionConfigValidator.EXPECT().
			CanDalianMP(gomock.Any(), gomock.Any()).
			Return(false, nil)
		deps.orderService.EXPECT().
			CreateOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order) error {
				require.Equal(tt, ordid, order.OrderID)
				require.Equal(tt, 60.0, order.Routes[1].PriceSummary.DeliveryFee.BaseFee)
				require.True(tt, order.RevampedStatus)
				order.ExpireAt = expireAt
				return nil
			})
		deps.orderService.EXPECT().
			GetOtherActiveMP(gomock.Any(), gomock.Any()).
			Return(nil, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&domainModel.ServiceArea{}, nil)
		deps.distributionService.EXPECT().PublishDistributeOrderEvent(gomock.Any(), gomock.Any())

		deps.repSvc.EXPECT().Publish(rep.EventOrderCreated, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(true, nil)
		deps.locker.EXPECT().SetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid), gomock.Any(), mp.Cfg.CreateOrderIdempotencyExpirationDuration).Return(true)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp api.Error
		testutil.DecodeJSON(t, recorder.Body, &resp)
	})

	t.Run("create order from quote with idempotency and should return previous data response from cache", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid            = "LMFQ-1231451"
			ordid          = "LMFQ-1231451"
			resID          = "res-id"
			idempotencyKey = "A-B-C"
		)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return(fmt.Sprintf(`{"orderId": "%s"}`, ordid))

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusCreated, recorder.Code)
		var resp CreateOrderResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, ordid, resp.OrderID)
	})

	t.Run("create order from quote with idempotency and should return an error order locked with the idempotencyKey", func(tt *testing.T) {
		tt.Parallel()

		const (
			qid            = "LMFQ-1231451"
			ordid          = "LMFQ-1231451"
			resID          = "res-id"
			idempotencyKey = "A-B-C"
		)

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.quoteService.EXPECT().
			Find(gomock.Any(), qid, gomock.Any()).
			Return(&domainModel.Quote{
				ServiceType: domainModel.ServiceMessenger,
				QuoteID:     qid,
				Distance:    types.Distance(1000),
				Routes: []domainModel.Stop{
					{
						ID: resID,
					},
					{
						CollectPayment: true,
						PriceSummary: domainModel.PriceSummary{
							DeliveryFee: domainModel.DeliveryFeeSummary{
								BaseFee:  60,
								RoadFee:  55,
								SubTotal: 55,
								Total:    55,
							},
							Total: 0,
						},
					},
				},
				PayAtStop:         1,
				DistributeRegions: []domainModel.RegionCode{"AYUTTHAYA"},
			}, nil)

		deps.locker.EXPECT().GetState(gomock.Any(), locker.GetOrderInfoByIdempotencyLockKey(qid)).Return("")
		deps.locker.EXPECT().Lock(gomock.Any(), locker.CreateOrderByIdempotencyLockKey(qid), gomock.Any()).Return(false, nil)

		ctx, recorder := req(&CreateOrderRequest{QuoteID: qid, OrderID: ordid})
		wg := safe.CreateWaitGroupOnGctx(ctx)
		mp.CreateOrder(ctx)
		wg.Wait()
		require.Equal(t, http.StatusBadRequest, recorder.Code)
		var resp api.Error
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, fmt.Sprintf("order was locked by idempotencyKey %s", qid), resp.Message)
	})
}

func TestMessengerDeliveryAPI_ConfirmPrice(t *testing.T) {
	t.Parallel()

	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/delivery/messenger/order/LMF-13151/confirm-price", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}
	t.Run("not implement", func(tt *testing.T) {
		tt.Skip() // TODO clean not implement API
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = "LMF-123151"

		ctx, recorder := req(orderID)

		dapi, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		dapi.ConfirmPrice(ctx)

		require.Equal(tt, http.StatusNotImplemented, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "NOT_IMPLEMENT", err.Code)
	})
}

func TestMessengerDeliveryAPI_CancelOrder(t *testing.T) {
	t.Parallel()

	const (
		orderID  = "LMF-123151"
		driverID = "<driver_id_1>"
	)

	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/messenger/order/"+orderID+"/cancel", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	createOrder := func(driverID string) *domainModel.Order {
		q, _ := domainModel.NewQuote(
			"quote_1",
			"user-1",
			domainModel.ServiceMessenger,
			[]domainModel.Stop{{Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}}}, {}},
			string(domainModel.PriceSchemeRMS),
			false,
			domainModel.NewDeliveryFee(70.0, 30.0, 1000),
			domainModel.NewDeliveryFee(70.0, 30.0, 1000),
			[]domainModel.RegionCode{"AYUTTHAYA"},
			0,
		)
		ord := domainModel.NewOrder(*q, orderID)

		ord.ExpireAt = time.Now().Add(time.Hour)

		if driverID != "" {
			ord.Driver = driverID
		}
		return ord
	}

	t.Run("cancel order and unassign driver", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some error so it won't reassign"))
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil).AnyTimes()

		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&model.LocationWithUpdatedAt{}, nil)

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.driverRepository.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(&domainModel.Driver{DriverID: driverID, BanLater: false, CurrentOrder: orderID, Status: domainModel.StatusAssigned}, nil)
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		stubLockDriver(tt, deps.driverService)
		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("do not unassign driver is empty", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder("")
		ord.Status = domainModel.StatusAssigningDriver

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some error so it won't reassign"))
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil).AnyTimes()

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any()).
			Return(nil)

		deps.assignmentLog.EXPECT().
			AssignedDrivers(gomock.Any(), orderID).
			Return([]domainModel.Record{
				{
					DriverID:     "driver-id-1",
					PushAt:       timeutil.BangkokNow().Add(-time.Hour),
					AcceptBefore: timeutil.BangkokNow().Add(-time.Hour).Add(15 * time.Second),
				},
				{
					DriverID:     "driver-id-2",
					PushAt:       timeutil.BangkokNow(),
					AcceptBefore: timeutil.BangkokNow().Add(15 * time.Second),
				},
			}, nil)

		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{"driver-id-2"}, service.EventOrderCanceled(orderID, false, false, false), gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

	t.Run("LMFS-458: cancel expired order", func(tt *testing.T) {
		tt.Skip("til confirm with po")
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil)
		stubLockDriver(tt, deps.driverService)

		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "ORDER_EXPIRED", err.Code)
	})

	t.Run("cancel order and Ban driver when BanLater is true", func(tt *testing.T) {
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(orderID)

		dapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		ord := createOrder(driverID)
		ord.Status = domainModel.StatusDriverMatched
		t := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		driver := &domainModel.Driver{DriverID: driverID, BanLater: true, BannedUntil: &t, CurrentOrder: orderID, Status: domainModel.StatusAssigned}

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some error so it won't reassign"))
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(ord, nil).AnyTimes()

		deps.driverService.EXPECT().GetDriverLocationWithUpdatedAt(gomock.Any(), gomock.Any()).Return(&model.LocationWithUpdatedAt{}, nil)

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(gomock.Any(), ord, gomock.Any())

		deps.driverRepository.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			Return(nil)
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderCanceled(orderID, true, true, false), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepository.EXPECT().
			FindDriverID(gomock.Any(), driverID, gomock.Any()).Return(driver, nil)
		deps.bansvc.EXPECT().
			Ban(gomock.Any(), driver, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventDriverUpdateStatus, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any()).Return(nil)
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())
		stubLockDriver(tt, deps.driverService)
		dapi.CancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, domainModel.StatusCanceled, ord.Status)
	})

}

func TestMessengerDeliveryAPI_SystemCancelOrder(t *testing.T) {
	t.Parallel()

	const expectedOrderID = "order-id"

	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/delivery/messenger/order/%s/system-cancel", orderID), nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("Should OK when cancel order while order status is ASSIGNING_DRIVER or EXPIRED", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, recorder := req(expectedOrderID)
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		ord := &domainModel.Order{
			Quote:   domainModel.Quote{ServiceType: domainModel.ServiceMessenger},
			History: map[string]time.Time{},
			Status:  domainModel.StatusAssigningDriver,
		}
		ord.Routes = []domainModel.Stop{{}, {}}
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("some error so it won't reassign"))
		deps.orderService.EXPECT().
			Get(gomock.Any(), gomock.Any()).
			Return(ord, nil).AnyTimes()

		deps.orderService.EXPECT().
			UpdateAndUpsertCanceledRevision(ctx, gomock.Any(), gomock.Any()).
			Return(nil)

		deps.assignmentLog.EXPECT().
			AssignedDrivers(ctx, expectedOrderID).
			Return([]domainModel.Record{
				{
					DriverID:     "driver-id-1",
					PushAt:       timeutil.BangkokNow().Add(-time.Hour),
					AcceptBefore: timeutil.BangkokNow().Add(-time.Hour).Add(15 * time.Second),
				},
				{
					DriverID:     "driver-id-2",
					PushAt:       timeutil.BangkokNow(),
					AcceptBefore: timeutil.BangkokNow().Add(15 * time.Second),
				},
			}, nil)

		deps.notifier.EXPECT().
			Notify(ctx, []string{"driver-id-2"}, service.EventOrderCanceled(expectedOrderID, false, false, false), gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(rep.EventOrderCancelled, gomock.Any())
		deps.repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, gomock.Any())
		deps.bus.EXPECT().Publish(gomock.Any(), BusTopicDriverEventOrder, gomock.Any())

		mp.SystemCancelOrder(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should error when cancel order while order status is not ASSIGNING_DRIVER or EXPIRED", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, _ := req(expectedOrderID)
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(ctx, expectedOrderID).
			Return(&domainModel.Order{
				History: map[string]time.Time{},
				Status:  domainModel.StatusDriverMatched,
			}, nil)

		mp.SystemCancelOrder(ctx)

		require.Contains(tt, ctx.Errors.Errors(), "ORDER_STATUS_NOT_ALLOWED: order status is not allowed")
	})
}

func TestMessengerDeliveryAPI_RestaurantAccept(t *testing.T) {
	t.Parallel()

	req := func(orderID string, req *RestaurantAcceptedOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/messenger/order/"+orderID+"/restaurant-accepted", testutil.JSON(req))
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("not Implemented", func(tt *testing.T) {
		tt.Skip() // TODO clean not implement API
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		const orderID = ""

		ctx, recorder := req(orderID, &RestaurantAcceptedOrderRequest{})

		dapi, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		dapi.RestaurantAccepted(ctx)

		require.Equal(tt, http.StatusNotImplemented, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "NOT_IMPLEMENT", err.Code)
	})
}

func TestMessengerDeliveryAPI_EditOrder(t *testing.T) {
	t.Parallel()

	req := func(req *EditOrderRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/messenger/order/edit", testutil.JSON(req))
		return ctx, recorder
	}

	t.Run("not Implemented", func(tt *testing.T) {
		tt.Skip() // TODO clean not implement API
		tt.Parallel()
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(&EditOrderRequest{})

		dapi, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		dapi.EditOrder(ctx)

		require.Equal(tt, http.StatusNotImplemented, recorder.Code)
		var err api.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "NOT_IMPLEMENT", err.Code)
	})
}

func TestMessengerDeliveryAPI_RatingDriver(t *testing.T) {
	t.Parallel()

	const (
		driverID = "<driver_id>"
		orderID  = "<order_id>"
	)

	var tags []string

	req := func(req *RatingDriverRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", "/v1/delivery/driver/rating", testutil.JSON(req))
		return ctx, recorder
	}

	t.Run("bad request when status is not expected", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		order := &domainModel.Order{
			Quote:  domainModel.Quote{ServiceType: domainModel.ServiceMessenger},
			Status: domainModel.StatusDriverMatched,
			Driver: driverID,
		}

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.driverService.EXPECT().
			RatingDriver(gomock.Any(), uint32(5), "", tags, order).
			Return(service.ErrRatingInvalidOrderStatus)
		ctx, recorder := req(&RatingDriverRequest{
			OrderID: orderID,
			Score:   5,
		})
		api.RatingDriver(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})
}

func TestMessengerDeliveryAPI_GetOrderStatus(t *testing.T) {
	t.Parallel()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	const (
		orderID = "<order_id>"
	)
	now := time.Now()

	req := func(orderID string) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("GET", "/v1/delivery/messenger/order/"+orderID+"/status", nil)
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

	deps.orderService.EXPECT().
		Get(gomock.Any(), orderID).
		Return(&domainModel.Order{
			Status: domainModel.StatusDriverMatched,
			History: map[string]time.Time{
				string(domainModel.StatusDriverMatched): now,
			},
		}, nil)

	ctx, recorder := req(orderID)
	api.GetOrderStatus(ctx)

	require.Equal(t, http.StatusOK, recorder.Code)
	var resp GetOrderStatusResponse
	testutil.DecodeJSON(t, recorder.Body, &resp)
	require.Equal(t, string(domainModel.StatusDriverMatched), resp.Status)
	require.True(t, resp.Time.Equal(now))
}

func TestMessengerDeliveryAPI_UpdateDeliveryLocation(t *testing.T) {
	t.Parallel()

	const (
		region   = "AYUTTHAYA"
		driverID = "<driver_id>"
		orderID  = "<order_id>"
	)

	req := func(req *UpdateDeliveryLocationRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("POST", "/v1/delivery/messenger/order/update-delivery-location", testutil.JSON(req))
		ctx.Request.Header.Add("X-Region", "AYUTTHAYA")
		return ctx, recorder
	}

	t.Run("should return error when binding json error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := testutil.TestRequestContext("POST", "/v1/delivery/messenger/order/update-delivery-location", strings.NewReader("{}"))
		ctx.Request.Header.Add("X-Region", "AYUTTHAYA")
		api, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})

	t.Run("should return error when binding header error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location: LocationReq{
				Lat: 13,
				Lng: 100,
			},
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})
		ctx.Request.Header.Del("X-Region")

		api, _ := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})

	t.Run("should return error when find order error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location: LocationReq{
				Lat: 13,
				Lng: 100,
			},
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(nil, repository.ErrNotFound)
		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusNotFound, recorder.Code, recorder.Body.String())
	})

	t.Run("should return error when order has no delivery location", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location: LocationReq{
				Lat: 13,
				Lng: 100,
			},
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}},
				},
			},
			PayAtStop: 0,
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code, recorder.Body.String())
	})

	t.Run("should return error when finding distributed region error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}},
				},
				{
					Name:     "current name",
					Phones:   []string{"0801111111"},
					Address:  "current address",
					Memo:     "current memo",
					Location: domainModel.Location{Lat: 13, Lng: 100},
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
				},
			},
			PayAtStop: 0,
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location:    location,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, repository.ErrNotFound)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code, recorder.Body.String())
	})

	t.Run("should return error when finding fastest route error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}},
				},
				{
					Name:     "current name",
					Phones:   []string{"0801111111"},
					Address:  "current address",
					Memo:     "current memo",
					Location: domainModel.Location{Lat: 13, Lng: 100},
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
				},
			},
			PayAtStop: 0,
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location:    location,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil, errors.New("osrm timeout"))
		//&mapservice.Route{Distance: 1.5, Duration: 100}
		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code, recorder.Body.String())
	})

	t.Run("should return error when finding delivery fee calculator error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}},
				},
				{
					Name:     "current name",
					Phones:   []string{"0801111111"},
					Address:  "current address",
					Memo:     "current memo",
					Location: domainModel.Location{Lat: 13, Lng: 100},
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
				},
			},
			PayAtStop: 0,
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location:    location,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()
		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(nil, domainModel.SettingDeliveryFeePriceScheme{}, repository.ErrNotFound)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code, recorder.Body.String())
	})

	t.Run("should return 500 when db fails", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}},
				},
				{
					Name:     "current name",
					Phones:   []string{"0801111111"},
					Address:  "current address",
					Memo:     "current memo",
					Location: domainModel.Location{Lat: 13, Lng: 100},
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
				},
			},
			PayAtStop: 0,
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location:    location,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.RestaurantTypeKeyRMS, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)

		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			require.Equal(tt, order.Routes[1].Name, "updated name")
			require.Equal(tt, order.Routes[1].Phones[0], "**********")
			require.Equal(tt, order.Routes[1].Address, "updated address")
			require.Equal(tt, order.Routes[1].Memo, "updated memo")
			require.Equal(tt, order.Routes[1].Location, domainModel.Location{
				Lat: location.Lat,
				Lng: location.Lng,
			})
			require.Equal(tt, order.Routes[1].Distance, types.Distance(1.5))
			require.Equal(tt, order.Routes[1].EstimatedDeliveryTime, domainModel.DurationSecond(100))
			require.Equal(tt, order.Distance, types.Distance(1.5))

			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.SubTotal, 40.0)
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.Total, 10.0)
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.Commission, 0.0)     // 0.15 * subTotal
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.WithholdingTax, 1.2) // 0.03 * (subTotal - commission)
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.Total, 165.0)

			return errors.New("update db error")
		})

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code, recorder.Body.String())
	})

	t.Run("should update delivery location and price summary", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							SubTotal:      155,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}},
				},
				{
					Name:     "current name",
					Phones:   []string{"0801111111"},
					Address:  "current address",
					Memo:     "current memo",
					Location: domainModel.Location{Lat: 13, Lng: 100},
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
				},
			},
			PayAtStop:             0,
			Distance:              393,
			CreatedAt:             time.Now(),
			UpdatedAt:             time.Now(),
			RevenuePrincipalModel: true,
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location:    location,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.PriceSchemeKeyDefault, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()

		deps.tripServices.EXPECT().OnUpdateDeliveryLocation(gomock.Any(), gomock.Any()).Return(model.Trip{}, nil)

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {
			require.Equal(tt, order.Routes[1].Name, "updated name")
			require.Equal(tt, order.Routes[1].Phones[0], "**********")
			require.Equal(tt, order.Routes[1].Address, "updated address")
			require.Equal(tt, order.Routes[1].Memo, "updated memo")
			require.Equal(tt, order.Routes[1].Location, domainModel.Location{
				Lat: location.Lat,
				Lng: location.Lng,
			})
			require.Equal(tt, order.Routes[1].Distance, types.Distance(1.5))
			require.Equal(tt, order.Routes[1].EstimatedDeliveryTime, domainModel.DurationSecond(100))
			require.Equal(tt, order.Distance, types.Distance(1.5))

			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.SubTotal, 40.0)
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.Total, 10.0)
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.Commission, 0.0)     // 0.15 * subTotal
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.WithholdingTax, 1.2) // 0.03 * (subTotal - commission)
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.Total, 165.0)

			return nil
		})
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderItemsChanged(order, nil), gomock.Any()).Return(nil)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})

	t.Run("for agent model order, should calculate commission but ignore withholding tax", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		q := domainModel.Quote{
			QuoteID:     "LMFQ-123151",
			UserID:      "<user_id_1>",
			ServiceType: domainModel.ServiceMessenger,
			Routes: []domainModel.Stop{
				{
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
					PriceSummary: domainModel.PriceSummary{
						DeliveryFee: domainModel.DeliveryFeeSummary{
							RawBaseFee:    55,
							RoadFee:       100,
							SubTotal:      155,
							Total:         155,
							AdditionalFee: map[string]float64{},
						},
						ItemFee: domainModel.ItemFeeSummary{
							ItemFee: 155,
						},
						Total: 155,
					},
					Info: domainModel.StopInfoCollector{StopInfo: &domainModel.StopInfoFood{PriceScheme: domainModel.PriceSchemeRMS}},
				},
				{
					Name:     "current name",
					Phones:   []string{"0801111111"},
					Address:  "current address",
					Memo:     "current memo",
					Location: domainModel.Location{Lat: 13, Lng: 100},
					PickingItems: []domainModel.Item{
						{
							Name:     "B",
							Price:    155,
							Quantity: 1,
						},
					},
				},
			},
			PayAtStop: 0,
			Distance:  393,
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		}
		order := domainModel.NewOrder(q, orderID)
		order.SetStatus(domainModel.StatusDriverMatched)
		order.Driver = driverID

		location := LocationReq{
			Lat: 13,
			Lng: 100,
		}

		ctx, recorder := req(&UpdateDeliveryLocationRequest{
			OrderID:     orderID,
			Name:        "updated name",
			Phone:       "**********",
			Address:     "updated address",
			AddressMemo: "updated memo",
			Location:    location,
			Discounts: []DiscountReq{
				{
					Type:     domainModel.DiscountTypeCoupon,
					Category: "",
					Code:     "code30",
					Discount: 30.0,
				},
			},
			RevenueAgentModel: true,
		})

		api, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		priceScheme := CreateValidPriceScheme()
		scheme := domainModel.NewDeliveryFeeSettingScheme(domainModel.PriceSchemeKeyDefault, priceScheme)
		cal := domainModel.NewDeliveryFeeSetting("setting-delivery-fee-id", domainModel.ServiceFood, "AYUTTHAYA", scheme)

		deps.locker.EXPECT().Lock(gomock.Any(), gomock.Any()).
			Return(true, nil).AnyTimes()
		deps.locker.EXPECT().Unlock(gomock.Any(), gomock.Any()).AnyTimes()

		deps.tripServices.EXPECT().OnUpdateDeliveryLocation(gomock.Any(), gomock.Any()).Return(model.Trip{}, nil)

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(cal, priceScheme, nil)

		deps.orderService.EXPECT().
			Get(gomock.Any(), orderID).
			Return(order, nil)
		deps.mapService.EXPECT().
			FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&domainModel.MapRoute{Distance: 1.5, Duration: 100}, nil, nil)
		mockRepositoryUpdate(tt, deps.orderService, func(ctx context.Context, order *domainModel.Order) error {

			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.Commission, 6.0)     // 0.15 * subTotal
			require.Equal(tt, order.Routes[order.PayAtStop].PriceSummary.DeliveryFee.WithholdingTax, 0.0) // 0.03 * (subTotal - commission)

			return nil
		})
		deps.notifier.EXPECT().
			Notify(gomock.Any(), []string{driverID}, service.EventOrderItemsChanged(order, nil), gomock.Any()).Return(nil)

		api.UpdateDeliveryLocation(ctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
	})
}

func TestMessengerDeliveryAPI_ChangePaymentMethod(t *testing.T) {
	t.Parallel()

	const expectedOrderID = "order-id"

	req := func(orderID string, req *PaymentMethodRequest) (*gin.Context, *httptest.ResponseRecorder) {
		ctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/delivery/messenger/order/%s/payment-method", orderID), testutil.JSON(req))
		ctx.Params = gin.Params{
			{
				Key:   "orderID",
				Value: orderID,
			},
		}
		return ctx, recorder
	}

	t.Run("should return error if order does not exist", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, recorder := req(expectedOrderID, &PaymentMethodRequest{
			PaymentMethod: domainModel.PaymentMethodCash,
		})
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(ctx.Request.Context(), expectedOrderID).
			Return(nil, repository.ErrNotFound)

		mp.ChangePaymentMethod(ctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("should return error if cash is changed to e-payment", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, recorder := req(expectedOrderID, &PaymentMethodRequest{
			PaymentMethod: domainModel.PaymentMethodRLP,
		})
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(ctx.Request.Context(), expectedOrderID).
			Return(&domainModel.Order{
				Quote: domainModel.Quote{
					ServiceType: domainModel.ServiceMessenger,
					Routes: []domainModel.Stop{
						{
							PriceSummary: domainModel.PriceSummary{
								DeliveryFee: domainModel.DeliveryFeeSummary{
									PaymentMethod: domainModel.PaymentMethodCash,
								},
							},
							CollectPayment: false,
						},
						{
							CollectPayment: false,
						},
					},
				},
				History: map[string]time.Time{},
				Status:  domainModel.StatusAssigningDriver,
			}, nil)

		mp.ChangePaymentMethod(ctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("should update order with new payment method", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		ctx, recorder := req(expectedOrderID, &PaymentMethodRequest{
			PaymentMethod: domainModel.PaymentMethodCash,
		})
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(ctx.Request.Context(), expectedOrderID).
			Return(&domainModel.Order{
				Quote: domainModel.Quote{
					ServiceType: domainModel.ServiceMessenger,
					Routes: []domainModel.Stop{
						{
							PriceSummary: domainModel.PriceSummary{
								DeliveryFee: domainModel.DeliveryFeeSummary{
									PaymentMethod: domainModel.PaymentMethodRLP,
								},
							},
							CollectPayment: false,
						},
						{
							CollectPayment: false,
						},
					},
				},
				History: map[string]time.Time{},
				Status:  domainModel.StatusAssigningDriver,
			}, nil)

		deps.orderService.EXPECT().
			UpdateOrder(ctx.Request.Context(), gomock.Any(), []repository.Option{}).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order, opts ...repository.Option) error {
				require.Equal(tt, order.Quote.Routes[0].PriceSummary.DeliveryFee, domainModel.DeliveryFeeSummary{
					PaymentMethod: domainModel.PaymentMethodCash,
				})
				require.Equal(tt, order.Quote.Routes[0].CollectPayment, true)

				return nil
			})

		mp.ChangePaymentMethod(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("should update order with new payAtStop if provided", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()

		newPayAtStop := 1
		ctx, recorder := req(expectedOrderID, &PaymentMethodRequest{
			PaymentMethod: domainModel.PaymentMethodCash,
			PayAtStop:     &newPayAtStop,
		})
		mp, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		deps.orderService.EXPECT().
			Get(ctx.Request.Context(), expectedOrderID).
			Return(&domainModel.Order{
				Quote: domainModel.Quote{
					ServiceType: domainModel.ServiceMessenger,
					Routes: []domainModel.Stop{
						{
							PriceSummary: domainModel.PriceSummary{
								DeliveryFee: domainModel.DeliveryFeeSummary{
									PaymentMethod: domainModel.PaymentMethodRLP,
								},
							},
							CollectPayment: false,
						},
						{
							CollectPayment: false,
						},
					},
				},
				History: map[string]time.Time{},
				Status:  domainModel.StatusAssigningDriver,
			}, nil)

		deps.orderService.EXPECT().
			UpdateOrder(ctx.Request.Context(), gomock.Any(), []repository.Option{}).
			DoAndReturn(func(ctx context.Context, order *domainModel.Order, opts ...repository.Option) error {
				require.Equal(tt, order.Quote.Routes[0].CollectPayment, false)
				require.Equal(tt, order.Quote.Routes[1].PriceSummary.DeliveryFee, domainModel.DeliveryFeeSummary{
					PaymentMethod: domainModel.PaymentMethodCash,
				})
				require.Equal(tt, order.Quote.Routes[1].CollectPayment, true)

				return nil
			})

		mp.ChangePaymentMethod(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestMessengerAPI_GetOnTopScheme(mainT *testing.T) {
	type testData struct {
		title                    string
		srcQuote                 domainModel.Quote
		requestAt                time.Time
		onTopFareQueryResult     []domainModel.OnTopFare
		expectedOnTopFareAmount  int
		expectedOnTopFareSumRate float64
	}

	regionCode := "BKK"
	testList := []testData{}
	// define & generate test data part
	{
		generateOnTopFareStatus := func(
			activeStatus domainModel.OnTopFareStatus,
			scheme domainModel.OnTopFareScheme,
			conditions ...domainModel.OntopCondition,
		) []domainModel.OnTopFare {
			baseCondition := []domainModel.OntopCondition{}
			return []domainModel.OnTopFare{
				{
					Scheme:     scheme,
					Status:     activeStatus,
					Region:     regionCode,
					Name:       fmt.Sprintf("%s_%d", regionCode, time.Now().Unix()),
					Conditions: append(baseCondition, conditions...),
				},
			}
		}

		generateOntopCondition := func(
			activeStatus domainModel.OnTopFareStatus,
			days []domainModel.Days,
			times []domainModel.StartEndTime,
			flatRate float64,
			distancePrices ...domainModel.OntopDistancePrice,
		) domainModel.OntopCondition {
			onTopCondition := domainModel.OntopCondition{
				Status:         activeStatus,
				Days:           days,
				Time:           times,
				FlatRateAmount: flatRate,
				ServiceTypes:   []domainModel.Service{domainModel.ServiceMessenger},
			}
			onTopCondition.DistancePrices = distancePrices
			if len(distancePrices) == 0 {
				onTopCondition.FlatRateAmount = flatRate
				onTopCondition.DistancePrices = nil
			}
			return onTopCondition
		}

		otf_matched_day := []domainModel.Days{
			domainModel.Sunday,
		}
		otf_another_matched_day := []domainModel.Days{
			domainModel.Monday,
		}

		otf_matched_time := []domainModel.StartEndTime{
			{
				Begin: "06:00",
				End:   "18:00",
			},
		}
		otf_another_matched_time := []domainModel.StartEndTime{
			{
				Begin: "18:01",
				End:   "20:00",
			},
		}

		otf_flat_gain := float64(10)
		otf_dist1_0_gain := float64(100)
		otf_dist1_1_gain := float64(1000)
		otf_dist1_2_gain := float64(10000)
		otf_dist1_3_gain := float64(100000)

		bkk_locale, _ := time.LoadLocation("Asia/Bangkok")
		q_time_matched := time.Date(2021, 3, 21, 11, 07, 0, 0, bkk_locale)        // it's Sunday
		q_another_time_matched := time.Date(2021, 3, 22, 19, 0, 0, 0, bkk_locale) // it's Monday

		messengerOrigin := domainModel.Stop{
			ID:       "res-id",
			Distance: types.Distance(10),
			Location: domainModel.Location{}, // origin location
		}
		messengerDestination := domainModel.Stop{
			ID:       "dest-id",
			Distance: types.Distance(100),
			Location: domainModel.Location{}, // destination location
		}
		messengerDestinationRoundTrip := domainModel.Stop{
			ID:       "dest-id",
			Distance: types.Distance(1000),
			Location: domainModel.Location{}, // origin location
		}

		messengerQuote := domainModel.Quote{
			ServiceType: domainModel.ServiceMessenger,
			Routes:      []domainModel.Stop{messengerOrigin, messengerDestination},
			Distance:    messengerDestination.Distance,
		}

		messengerQuoteRoundTrip := domainModel.Quote{
			ServiceType: domainModel.ServiceMessenger,
			Routes:      []domainModel.Stop{messengerOrigin, messengerDestination, messengerOrigin, messengerDestinationRoundTrip},
			Distance:    messengerDestination.Distance,
		}

		ontopDistancePrices := []domainModel.OntopDistancePrice{
			{
				From:   0,
				To:     100,
				Amount: types.Money(otf_dist1_0_gain),
			},
			{
				From:   101,
				To:     150,
				Amount: types.Money(otf_dist1_1_gain),
			},
			{
				From:   151,
				To:     500,
				Amount: types.Money(otf_dist1_2_gain),
			},
			{
				From:   501,
				To:     1500,
				Amount: types.Money(otf_dist1_3_gain),
			},
		}

		basketSizeOnTop := generateOntopCondition(domainModel.StatusActive, otf_matched_day, otf_matched_time, otf_flat_gain)
		basketSizeOnTop.BasketPrices = []domainModel.OntopBasketPrice{
			{
				From:   0,
				To:     101,
				Amount: types.Money(otf_dist1_1_gain),
			},
			{
				From:   102,
				To:     200,
				Amount: types.Money(otf_dist1_2_gain),
			},
		}

		testList = append(testList,
			testData{
				title:     "one trip flat rate",
				srcQuote:  messengerQuote,
				requestAt: q_time_matched,
				onTopFareQueryResult: generateOnTopFareStatus(
					domainModel.StatusActive,
					domainModel.FlatRateScheme,
					generateOntopCondition(domainModel.StatusActive, otf_matched_day, otf_matched_time, otf_flat_gain),
				),
				expectedOnTopFareAmount:  1,
				expectedOnTopFareSumRate: otf_flat_gain,
			},
			testData{
				title:     "one trip distance rate",
				srcQuote:  messengerQuote,
				requestAt: q_time_matched,
				onTopFareQueryResult: generateOnTopFareStatus(
					domainModel.StatusActive,
					domainModel.DistanceScheme,
					generateOntopCondition(domainModel.StatusActive, otf_matched_day, otf_matched_time, 0, ontopDistancePrices...),
				),
				expectedOnTopFareAmount:  1,
				expectedOnTopFareSumRate: otf_dist1_0_gain,
			},
			testData{
				title:     "round trip distance rate",
				srcQuote:  messengerQuoteRoundTrip,
				requestAt: q_time_matched,
				onTopFareQueryResult: generateOnTopFareStatus(
					domainModel.StatusActive,
					domainModel.DistanceScheme,
					generateOntopCondition(domainModel.StatusActive, otf_matched_day, otf_matched_time, 0, ontopDistancePrices...),
				),
				expectedOnTopFareAmount:  1,
				expectedOnTopFareSumRate: otf_dist1_3_gain,
			},
			testData{
				title:     "another one trip flat rate",
				srcQuote:  messengerQuote,
				requestAt: q_another_time_matched,
				onTopFareQueryResult: generateOnTopFareStatus(
					domainModel.StatusActive,
					domainModel.FlatRateScheme,
					generateOntopCondition(domainModel.StatusActive, otf_another_matched_day, otf_another_matched_time, otf_flat_gain),
				),
				expectedOnTopFareAmount:  1,
				expectedOnTopFareSumRate: otf_flat_gain,
			},
			testData{
				title:     "one trip flat rate: missing day and time",
				srcQuote:  messengerQuote,
				requestAt: q_time_matched,
				onTopFareQueryResult: generateOnTopFareStatus(
					domainModel.StatusActive,
					domainModel.FlatRateScheme,
					generateOntopCondition(domainModel.StatusActive, otf_another_matched_day, otf_another_matched_time, otf_flat_gain),
				),
				expectedOnTopFareAmount:  0,
				expectedOnTopFareSumRate: 0.0,
			},
			testData{
				title:     "one trip flat rate: inactive on top",
				srcQuote:  messengerQuote,
				requestAt: q_time_matched,
				onTopFareQueryResult: generateOnTopFareStatus(
					domainModel.StatusInactive,
					domainModel.FlatRateScheme,
					generateOntopCondition(domainModel.StatusActive, otf_another_matched_day, otf_another_matched_time, otf_flat_gain),
				),
				expectedOnTopFareAmount:  0,
				expectedOnTopFareSumRate: 0.0,
			},
			testData{
				title:     "one trip flat rate: inactive scheme",
				srcQuote:  messengerQuote,
				requestAt: q_time_matched,
				onTopFareQueryResult: generateOnTopFareStatus(
					domainModel.StatusActive,
					domainModel.FlatRateScheme,
					generateOntopCondition(domainModel.StatusInactive, otf_another_matched_day, otf_another_matched_time, otf_flat_gain),
				),
				expectedOnTopFareAmount:  0,
				expectedOnTopFareSumRate: 0.0,
			},
			testData{
				title:     "one trip flat rate: basket size",
				srcQuote:  messengerQuote,
				requestAt: q_time_matched,
				onTopFareQueryResult: generateOnTopFareStatus(
					domainModel.StatusActive,
					domainModel.BasketSizeScheme,
					basketSizeOnTop,
				),
				expectedOnTopFareAmount:  0,
				expectedOnTopFareSumRate: 0.0,
			},
		)
	}

	for _, item := range testList {
		mainT.Run(item.title, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			ctx := context.Background()
			testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

			deps.onTopFareRepo.EXPECT().
				GetOnTopFareByLocation(ctx, item.srcQuote.Routes[0].Location, string(domainModel.ServiceMessenger), gomock.Any()).
				Return(item.onTopFareQueryResult, nil).
				Times(1)

			scheme, _ := testapi.GetOnTopScheme(
				ctx,
				item.srcQuote,
				[]domainModel.RegionCode{domainModel.RegionCode(regionCode)},
				item.requestAt,
			)

			require.Equal(t, item.expectedOnTopFareAmount, len(scheme))
			{
				var sum float64
				for _, schemeItem := range scheme {
					sum += schemeItem.Amount
				}
				require.Equal(t, item.expectedOnTopFareSumRate, sum)
			}
		})
	}
}

func TestMessengerDeliveryFeeAPI_GetFareMetaData(t *testing.T) {
	t.Parallel()

	makeReq := func(req GetFareMetadataReq) (*gin.Context, *httptest.ResponseRecorder) {
		url := "/v1/delivery/fare-metadata"

		gctx, recorder := testutil.TestRequestContext("POST", url, testutil.JSON(req))
		gctx.Request.Header.Add("X-Region", req.Region)

		return gctx, recorder
	}

	t.Run("get fare meta data successfully", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		name := "price-scheme-01"
		rft := domainModel.RoadFeeTiers{model.NewRoadFeeTier(3000, 8.43), model.NewRoadFeeTier(6000, 12.62)}
		ps := domainModel.NewSettingDeliveryFeePriceScheme(name, 40, rft, []float64{1, 0.5, 0.33}, types.Money(10), 10)
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(nil, *ps, nil)

		gctx, recorder := makeReq(GetFareMetadataReq{
			ServiceType: domainModel.ServiceMessenger,
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.GetFareMetadata(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual FareMetadataRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, types.Money(8.43), actual.DistanceUnitFees[0].FeePerKM)
		require.Equal(tt, int32(3), actual.DistanceUnitFees[0].From)
		require.Equal(tt, int32(6), actual.DistanceUnitFees[0].To)
		require.Equal(tt, types.Money(12.62), actual.DistanceUnitFees[1].FeePerKM)
		require.Equal(tt, int32(6), actual.DistanceUnitFees[1].From)
		require.Equal(tt, int32(math.MaxInt32), actual.DistanceUnitFees[1].To)
	})

	t.Run("get fare meta data GetDistributeRegion error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{}, errors.New("error"))

		gctx, recorder := makeReq(GetFareMetadataReq{
			ServiceType: domainModel.ServiceMessenger,
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.GetFareMetadata(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("get fare meta data get delivery fee calculator error", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		testapi, deps := newTestFoodProvider(ctrl, OrderAPIConfig{}, &AtomicContingencyConfig{})

		region := "AYUTTHAYA"

		deps.areaSvc.EXPECT().
			GetDistributeRegion(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]domainModel.RegionCode{domainModel.RegionCode(region)}, nil)

		name := "price-scheme-01"
		rft := domainModel.RoadFeeTiers{model.NewRoadFeeTier(3000, 8.43), model.NewRoadFeeTier(6000, 12.62)}
		ps := domainModel.NewSettingDeliveryFeePriceScheme(name, 40, rft, []float64{1, 0.5, 0.33}, types.Money(10), 10)
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), domainModel.ServiceMessenger, region, gomock.Any(), gomock.Any()).
			Return(nil, *ps, errors.New("error"))

		gctx, recorder := makeReq(GetFareMetadataReq{
			ServiceType: domainModel.ServiceMessenger,
			Region:      region,
			Routes: []domainModel.Location{
				{Lat: 1, Lng: 1},
				{Lat: 2, Lng: 2},
			},
		})

		testapi.GetFareMetadata(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}
