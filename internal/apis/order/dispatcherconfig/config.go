package dispatcherconfig

import (
	"sync"
	"time"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type AutoAssignDbConfig struct {
	// This 2 configs below should move to this atomic config
	// BackToBackNotAllowIDs      []string `envconfig:"BACK_TO_BACK_NOT_ALLOW_IDS" default:""`
	// AutoAcceptEnabled          bool     `envconfig:"AUTO_ACCEPT_ENABLED" default:"false"`
	EnableAutoAssignConsoleLog bool `envconfig:"ENABLE_AUTO_ASSIGN_CONSOLE_LOG" default:"false"`
	EnableDedicateServiceType  bool `envconfig:"ENABLE_DEDICATE_SERVICE_TYPE" default:"true"`

	// EnableDistributeNormalOrderToTester - allow any orders to be distributed to tester
	EnableDistributeAnyOrdersToTester bool `envconfig:"ENABLE_DISTRIBUTE_ANY_ORDERS_TO_TESTER" default:"false"`

	// EnableSilentBan - skips distributing to driver if silent banned
	EnableSilentBan                   bool `envconfig:"ENABLE_SILENT_BAN" default:"true"`
	EnableMultiplePickup              bool `envconfig:"ENABLE_MULTIPLE_PICKUP" default:"true"`
	EnableUidSelector                 bool `envconfig:"ENABLE_UID_SELECTOR" default:"false"`
	EnableBatchAssignment             bool `envconfig:"ENABLE_BATCH_ASSIGNMENT" default:"false"`
	DisableThrottledDispatch          bool `envconfig:"DISABLE_THROTTLED_DISPATCH" default:"false"`
	DisableBatchDedicatedZones        bool `envconfig:"DISABLE_BATCH_DEDICATED_ZONES" default:"false"`
	DisableDedicatedZones             bool `envconfig:"DISABLE_DEDICATED_ZONES" default:"false"`
	DisableThrottledDeferred          bool `envconfig:"DISABLE_THROTTLED_DEFERRED" default:"false"`
	DisableIllegalDriverFilter        bool `envconfig:"DISABLE_ILLEGAL_DRIVER_FILTER" default:"false"`
	DisableDalianOptimizeOSRM         bool `envconfig:"DISABLE_DALIAN_OPTIMIZE_OSRM" default:"false"`
	DisableIllegalDriverFilterByRedis bool `envconfig:"DISABLE_ILLEGAL_DRIVER_FILTER_BY_REDIS" default:"false"`
	EnableFixManipulateMultipleTrips  bool `envconfig:"ENABLE_FIX_MANIPULATE_MULTIPLE_TRIPS" default:"false"`

	// PreferNotBundledTopN number of top online drivers that we prioritize
	PreferNotBundledTopN int `envconfig:"PREFER_NOT_BUNDLED_TOP_N" default:"5"`
	// PreferNotBundledMOAggressiveLevel used to override aggressive level
	PreferNotBundledMOAggressiveLevel string `envconfig:"PREFER_NOT_BUNDLED_MO_AGGRESSIVE_LEVEL" default:"L1"`

	DisableDeferredDispatch                           bool            `envconfig:"DISABLE_DEFERRED_DISPATCH" default:"false"`
	BatchDistributeTimeoutInSeconds                   float64         `envconfig:"BATCH_DISTRIBUTE_TIMEOUT_IN_SECONDS" default:"60"`
	DeferBatchDistributeTimeoutInSeconds              float64         `envconfig:"DEFER_BATCH_DISTRIBUTE_TIMEOUT_IN_SECONDS" default:"0"`
	BatchDistributeFallbackBeforeExpireInSeconds      float64         `envconfig:"BATCH_DISTRIBUTE_FALLBACK_BEFORE_EXPIRE_IN_SECONDS" default:"0"`
	DeferBatchDistributeFallbackBeforeExpireInSeconds float64         `envconfig:"DEFER_BATCH_DISTRIBUTE_FALLBACK_BEFORE_EXPIRE_IN_SECONDS" default:"0"`
	EnableFallbackBeforeExpireByRegion                bool            `envconfig:"ENABLE_FALLBACK_BEFORE_EXPIRE_BY_REGION" default:"false"`
	EligibleRegionsForFallbackBeforeExpire            types.StringSet `envconfig:"ELIGIBLE_REGIONS_FOR_FALLBACK_BEFORE_EXPIRE" default:"-"`
	EnableThrottlePolygonOffset                       bool            `envconfig:"ENABLE_THROTTLE_POLYGON_OFFSET" default:"false"`

	DisableMPMaxLoadThree         bool  `envconfig:"DISABLE_MP_MAX_LOAD_THREE" default:"false"`
	AcceptDurationBufferInSeconds int64 `envconfig:"ACCEPT_DURATION_BUFFER_IN_SECONDS" default:"0"`

	DisableRedistributeByDispatcher                                     bool            `envconfig:"DISABLE_REDISTRIBUTE_BY_DISPATCHER" default:"false"`
	RedistributionLimit                                                 int             `envconfig:"AUTO_ASSIGN_REDISTRIBUTION_LIMIT" default:"10"`
	DeferredOrderRedistributionLimit                                    int             `envconfig:"DEFERRED_ORDER_REDISTRIBUTION_LIMIT" default:"360"`
	SwitchFlowRedistributionLimit                                       int             `envconfig:"SWITCH_FLOW_ORDER_REDISTRIBUTION_LIMIT" default:"360"`
	EnableFallbackOrderRedistributionDelay                              bool            `envconfig:"ENABLE_FALLBACK_ORDER_REDISTRIBUTION_DELAY" default:"false"`
	MatchRiderFirstRedistributionDelayExponentialRate                   float64         `envconfig:"MATCH_RIDER_FIRST_REDISTRIBUTION_DELAY_EXPONENTIAL_RATE" default:"1.0"`
	FallbackOrderMatchRiderFirstRedistributionDelayExponentialRate      float64         `envconfig:"FALLBACK_ORDER_MATCH_RIDER_FIRST_REDISTRIBUTION_DELAY_EXPONENTIAL_RATE" default:"1.0"`
	MatchRiderFirstMaxRedistributionDelayInSeconds                      float64         `envconfig:"MATCH_RIDER_FIRST_MAX_REDISTRIBUTION_DELAY_IN_SECONDS" default:"120"`
	FallbackOrderMatchRiderFirstMaxRedistributionDelayInSeconds         float64         `envconfig:"FALLBACK_ORDER_MATCH_RIDER_FIRST_MAX_REDISTRIBUTION_DELAY_IN_SECONDS" default:"120"`
	MatchRestaurantFirstRedistributionDelayExponentialRate              float64         `envconfig:"MATCH_RESTAURANT_FIRST_REDISTRIBUTION_DELAY_EXPONENTIAL_RATE" default:"1.0"`
	FallbackOrderMatchRestaurantFirstRedistributionDelayExponentialRate float64         `envconfig:"FALLBACK_ORDER_MATCH_RESTAURANT_FIRST_REDISTRIBUTION_DELAY_EXPONENTIAL_RATE" default:"1.0"`
	MatchRestaurantFirstMaxRedistributionDelayInSeconds                 float64         `envconfig:"MATCH_RESTAURANT_FIRST_MAX_REDISTRIBUTION_DELAY_IN_SECONDS" default:"120"`
	FallbackOrderMatchRestaurantFirstMaxRedistributionDelayInSeconds    float64         `envconfig:"FALLBACK_ORDER_MATCH_RESTAURANT_FIRST_MAX_REDISTRIBUTION_DELAY_IN_SECONDS" default:"120"`
	DistributionLogSamplingRatio                                        float64         `envconfig:"DISTRIBUTION_LOG_SAMPLING_RATIO" default:"1.0"`
	RestaurantAcceptDurationInSeconds                                   float64         `envconfig:"RESTAURANT_ACCEPT_DURATION_IN_SECONDS" default:"300"`
	AcceptanceScoreWindowDay                                            int             `envconfig:"ACCEPTANCE_SCORE_WINDOW_DAY" default:"7"`
	AcceptanceScoreColdStartRateFood                                    float64         `envconfig:"ACCEPTANCE_SCORE_COLD_START_RATE_FOOD" default:"0.9"`
	AcceptanceScoreColdStartRateMessenger                               float64         `envconfig:"ACCEPTANCE_SCORE_COLD_START_RATE_MESSENGER" default:"0.9"`
	AcceptanceScoreColdStartRateMart                                    float64         `envconfig:"ACCEPTANCE_SCORE_COLD_START_RATE_MART" default:"0.9"`
	AcceptanceScoreColdStartRateBike                                    float64         `envconfig:"ACCEPTANCE_SCORE_COLD_START_RATE_BIKE" default:"0.9"`
	InsertDeferredOrderTimeout                                          time.Duration   `envconfig:"INSERT_DEFERRED_ORDER_TIMEOUT" default:"2s"`
	DisableDeferredDispatchDrivingAndBufferDurationForMart              bool            `envconfig:"DISABLE_DEFERRED_DISPATCH_DRIVING_AND_BUFFER_DURATION_FOR_MART" default:"false"`
	AcquireLockOrderOnStartEnabled                                      bool            `envconfig:"ACQUIRE_LOCK_ORDER_ON_START_ENABLED" default:"false"`
	AcquireLockOrderOnStartTTL                                          time.Duration   `envconfig:"ACQUIRE_LOCK_ORDER_ON_START_TTL" default:"3m"`
	EligibleRegionsForBikePriority                                      types.StringSet `envconfig:"ELIGIBLE_REGIONS_FOR_BIKE_PRIORITY" default:"-"`
	ApplyOnlineRequirementForDedicatedServices                          types.StringSet `envconfig:"APPLY_ONLINE_REQUIREMENT_FOR_DEDICATED_SERVICES" default:"-"`
	FoodServiceBiasWeights                                              []float64       `envconfig:"FOOD_SERVICE_BIAS_WEIGHTS" default:"1,1,1,1,1,1"`
	MartServiceBiasWeights                                              []float64       `envconfig:"MART_SERVICE_BIAS_WEIGHTS" default:"1,1,1,1,1,1"`
	BikeServiceBiasWeights                                              []float64       `envconfig:"BIKE_SERVICE_BIAS_WEIGHTS" default:"1,5.7,8.9,12,15.2,18.3"`
	SkipDedicatedRoundForRushOrders                                     bool            `envconfig:"SKIP_DEDICATED_ROUND_FOR_RUSH_ORDERS" default:"false"`
	AssigningStateTTLInSecond                                           float64         `envconfig:"ASSIGNING_STATE_TTL_IN_SECOND" default:"60"`
}

type AtomicAutoAssignDbConfig struct {
	lock   sync.RWMutex
	Config AutoAssignDbConfig
	config.Validatable[AutoAssignDbConfig]
}

func (cfg *AtomicAutoAssignDbConfig) Parse() {
	cfg.lock.Lock()
	defer cfg.lock.Unlock()
	envconfig.MustProcess("", &cfg.Config)
}

func (cfg *AtomicAutoAssignDbConfig) Get() AutoAssignDbConfig {
	cfg.lock.RLock()
	defer cfg.lock.RUnlock()
	return cfg.Config
}

func ProvideAutoAssignDbConfig(configUpdater *config.DBConfigUpdater) *AtomicAutoAssignDbConfig {
	var cfg AtomicAutoAssignDbConfig
	configUpdater.Register(&cfg)
	return &cfg
}

func NewAtomicAutoAssignDbConfig(cfg AutoAssignDbConfig) *AtomicAutoAssignDbConfig {
	return &AtomicAutoAssignDbConfig{
		lock:        sync.RWMutex{},
		Config:      cfg,
		Validatable: config.Validatable[AutoAssignDbConfig]{},
	}
}

type AutoAcceptConfig struct {
	AutoAcceptEnabled      bool `envconfig:"AUTO_ACCEPT_ENABLED" default:"true"`
	FullyAutoAcceptEnabled bool `envconfig:"FULLY_AUTO_ACCEPT_ENABLED" default:"true"`
}

type AtomicAutoAcceptConfig struct {
	lock   sync.RWMutex
	Config AutoAcceptConfig
	config.Validatable[AutoAcceptConfig]
}

func (cfg *AtomicAutoAcceptConfig) Parse() {
	cfg.lock.Lock()
	defer cfg.lock.Unlock()
	envconfig.MustProcess("", &cfg.Config)
}

func (cfg *AtomicAutoAcceptConfig) Get() AutoAcceptConfig {
	cfg.lock.RLock()
	defer cfg.lock.RUnlock()
	return cfg.Config
}

func ProvideAutoAcceptConfig(configUpdater *config.DBConfigUpdater) *AtomicAutoAcceptConfig {
	var cfg AtomicAutoAcceptConfig
	configUpdater.Register(&cfg)
	return &cfg
}

// DistributionConfig is configuration for order distribution feature.
type DistributionConfig struct {
	// EnableRequireBox is toggle if order required box
	EnableRequireBox bool `envconfig:"ENABLE_REQUIRE_BOX"`
	// LimitDriverFetch limits maximum number of drivers fetched for distribution
	LimitDriverFetch int `envconfig:"DISTRIBUTION_LIMIT_DRIVER" default:"1000"`
	// RedistributionMaxRetries limits redistribution max attempt
	RedistributionMaxRetries int `envconfig:"REDISTRIBUTION_MAX_RETRIES" default:"4"`
	// PredictionServiceDisabled to disable prediction service for driver distribution
	PredictionServiceDisabled bool `envconfig:"PREDICTION_SERVICE_DISABLED" default:"false"`
	// RetryAttemptsLockingDriver count of retry to acquire lock on driver
	RetryAttemptsLockingDriver uint `envconfig:"RETRY_ATTEMPTS_LOCKING_DRIVER" default:"8"`
	// RetryDelayLockingDriver delay to acquire lock on driver
	RetryDelayLockingDriver time.Duration `envconfig:"RETRY_DELAY_LOCKING_DRIVER" default:"280ms"`
	// RetryAttemptLockingDriverAlertThreshold threshold to alert when locking attempt reached the threshold
	RetryAttemptLockingDriverAlertThreshold uint `envconfig:"RETRY_ATTEMPTS_LOCKING_DRIVER_ALERT_THRESHOLD" default:"6"`
	// LastAcceptWithin limits the duration between last accept and now
	LastAcceptWithin time.Duration `envconfig:"LAST_ACCEPT_WITHIN" default:"0s"`

	// EnableSaversExperiment enables the shadow redistribution of saver-like orders
	EnableSaversExperiment                   bool          `envconfig:"ENABLE_SAVERS_EXPERIMENT" default:"false"`
	SaversExperimentSampling                 float64       `envconfig:"SAVERS_EXPERIMENT_SAMPLING" default:"0.1"`
	SaversExperimentFirstRoundDelay          time.Duration `envconfig:"SAVERS_EXPERIMENT_FIRST_ROUND_DELAY" default:"30s"`
	SaversExperimentRedistributionDelay      time.Duration `envconfig:"SAVERS_EXPERIMENT_REDISTRIBUTION_DELAY" default:"2m"`
	SaversExperimentRedistributionMaxRetries int           `envconfig:"SAVERS_EXPERIMENT_REDISTRIBUTION_MAX_RETRIES" default:"3"`
	SaversExperimentTopN                     int           `envconfig:"SAVERS_EXPERIMENT_TOP_N" default:"1"`
}

func ProvideDistributionConfig() (cfg DistributionConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func ProvideAtomicDistributionConfig(configUpdater *config.DBConfigUpdater) *AtomicDistributionConfig {
	var cfg AtomicDistributionConfig

	configUpdater.Register(&cfg)
	return &cfg
}

type AtomicDistributionConfig = config.AtomicWrapper[DistributionConfig]

func NewAtomicDistributionConfig(cfg DistributionConfig) *AtomicDistributionConfig {
	return config.NewAtomicWrapper(cfg)
}
