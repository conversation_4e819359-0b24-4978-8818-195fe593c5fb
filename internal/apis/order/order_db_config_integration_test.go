//go:build integration_test
// +build integration_test

package order_test

import (
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
)

func TestAtomicOrderDBConfig_Parse(t *testing.T) {
	type TestCase struct {
		name                                             string
		givenQRNormalSelfCompleteDurationByServiceName   string
		givenQRIncidentSelfCompleteDurationByServiceName string
		expectedQRNormalSelfCompleteDurationByService    map[string]time.Duration
		expectedQRIncidentSelfCompleteDurationByService  map[string]time.Duration
	}

	testCases := []TestCase{
		{
			name: "empty",
			givenQRNormalSelfCompleteDurationByServiceName:   "",
			givenQRIncidentSelfCompleteDurationByServiceName: "",
			expectedQRNormalSelfCompleteDurationByService:    map[string]time.Duration{},
			expectedQRIncidentSelfCompleteDurationByService:  map[string]time.Duration{},
		},
		{
			name: "default",
			givenQRNormalSelfCompleteDurationByServiceName:   "bike:0s",
			givenQRIncidentSelfCompleteDurationByServiceName: "food:5m,bike:0s",
			expectedQRNormalSelfCompleteDurationByService: map[string]time.Duration{
				"bike": 0 * time.Second,
			},
			expectedQRIncidentSelfCompleteDurationByService: map[string]time.Duration{
				"food": 5 * time.Minute,
				"bike": 0 * time.Second,
			},
		},
		{
			name: "default ignoring space and empty pairs",
			givenQRNormalSelfCompleteDurationByServiceName:   " bike : 0 s,,,",
			givenQRIncidentSelfCompleteDurationByServiceName: ",, , food: 5m,bike : 0 s,",
			expectedQRNormalSelfCompleteDurationByService: map[string]time.Duration{
				"bike": 0 * time.Second,
			},
			expectedQRIncidentSelfCompleteDurationByService: map[string]time.Duration{
				"food": 5 * time.Minute,
				"bike": 0 * time.Second,
			},
		},
		{
			name: "default phase 2",
			givenQRNormalSelfCompleteDurationByServiceName:   "bike:2m",
			givenQRIncidentSelfCompleteDurationByServiceName: "food:5m,bike:30s",
			expectedQRNormalSelfCompleteDurationByService: map[string]time.Duration{
				"bike": 2 * time.Minute,
			},
			expectedQRIncidentSelfCompleteDurationByService: map[string]time.Duration{
				"food": 5 * time.Minute,
				"bike": 30 * time.Second,
			},
		},
		{
			name: "invalid duration normal",
			givenQRNormalSelfCompleteDurationByServiceName:   "bike:2z",
			givenQRIncidentSelfCompleteDurationByServiceName: "food:5m,bike:30s",
			expectedQRNormalSelfCompleteDurationByService:    nil,
			expectedQRIncidentSelfCompleteDurationByService: map[string]time.Duration{
				"food": 5 * time.Minute,
				"bike": 30 * time.Second,
			},
		},
		{
			name: "invalid duration incident",
			givenQRNormalSelfCompleteDurationByServiceName:   "bike:2m",
			givenQRIncidentSelfCompleteDurationByServiceName: "food:5z,bike:30s",
			expectedQRNormalSelfCompleteDurationByService: map[string]time.Duration{
				"bike": 2 * time.Minute,
			},
			expectedQRIncidentSelfCompleteDurationByService: nil,
		},
	}

	container := ittest.NewContainer(t)
	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			os.Setenv("QR_NORMAL_SELF_COMPLETE_DURATION_BY_SERVICE", tc.givenQRNormalSelfCompleteDurationByServiceName)
			os.Setenv("QR_INCIDENT_SELF_COMPLETE_DURATION_BY_SERVICE", tc.givenQRIncidentSelfCompleteDurationByServiceName)

			container.ProviderDeps.Cfg.AtomicOrderDBConfig.Parse()
			cfg := container.ProviderDeps.Cfg.AtomicOrderDBConfig.Get()
			require.Equal(tt, tc.expectedQRNormalSelfCompleteDurationByService, map[string]time.Duration(cfg.QRNormalSelfCompleteDurationByService))
			require.Equal(tt, tc.expectedQRIncidentSelfCompleteDurationByService, map[string]time.Duration(cfg.QRIncidentSelfCompleteDurationByService))
		})
	}
}

func TestAtomicOrderDBConfig_GetParsedBikeDistrictZonesExperimentConfigs(t *testing.T) {
	isDateMatched := func(input time.Time, eligibleDates []time.Time) bool {
		day, month, year := input.Date()
		for _, eligibleDate := range eligibleDates {
			eligibleDay, eligibleMonth, eligibleYear := eligibleDate.Date()
			if day == eligibleDay && eligibleMonth == month && eligibleYear == year {
				return true
			}
		}
		return false
	}

	testCases := []struct {
		name                               string
		bikeDistrictExperimentDates        string
		bikeDistrictExperimentSearchRadius string
		expectedDateMatched                []time.Time
		expectedDateUnmatched              []time.Time
		expectedRadii                      map[string][]float64
		expectError                        bool
	}{
		{
			name:                               "should parse correctly",
			bikeDistrictExperimentDates:        "2025-01-30,2025-02-01,2025-02-03",
			bikeDistrictExperimentSearchRadius: "AYUTTHAYA-WAT_KUDI_DAO:4.5|5|5.5|6|6.5,AYUTTHAYA-WAT_MAHEYONG:7|7.5|8|8.5|9",
			expectedDateMatched: []time.Time{
				time.Date(2025, 1, 30, 1, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 1, 30, 23, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 2, 01, 1, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 2, 01, 23, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 2, 03, 1, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 2, 03, 23, 0, 0, 0, timeutil.BangkokLocation()),
			},
			expectedDateUnmatched: []time.Time{
				time.Date(2025, 1, 31, 1, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 1, 31, 23, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 2, 02, 1, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 2, 02, 23, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 2, 05, 1, 0, 0, 0, timeutil.BangkokLocation()),
				time.Date(2025, 2, 05, 23, 0, 0, 0, timeutil.BangkokLocation()),
			},
			expectedRadii: map[string][]float64{
				"AYUTTHAYA-WAT_KUDI_DAO": {4.5, 5, 5.5, 6, 6.5},
				"AYUTTHAYA-WAT_MAHEYONG": {7, 7.5, 8, 8.5, 9},
			},
		},
		{
			name:                               "should error on parsing dates #1",
			bikeDistrictExperimentDates:        "2025/01/30,2025-02-01,2025-02-03",
			bikeDistrictExperimentSearchRadius: "AYUTTHAYA-WAT_KUDI_DAO:4.5|5|5.5|6|6.5,AYUTTHAYA-WAT_MAHEYONG:7|7.5|8|8.5|9",
			expectError:                        true,
		},
		{
			name:                               "should error on parsing dates #2",
			bikeDistrictExperimentDates:        "20250130,2025-02-01,2025-02-03",
			bikeDistrictExperimentSearchRadius: "AYUTTHAYA-WAT_KUDI_DAO:4.5|5|5.5|6|6.5,AYUTTHAYA-WAT_MAHEYONG:7|7.5|8|8.5|9",
			expectError:                        true,
		},
		{
			name:                               "should error on parsing dates #3",
			bikeDistrictExperimentDates:        "2025-01-30|2025-02-01|2025-02-03",
			bikeDistrictExperimentSearchRadius: "AYUTTHAYA-WAT_KUDI_DAO:4.5|5|5.5|6|6.5,AYUTTHAYA-WAT_MAHEYONG:7|7.5|8|8.5|9",
			expectError:                        true,
		},
		{
			name:                               "should error on parsing dates #4",
			bikeDistrictExperimentDates:        " 2025/01/30 ",
			bikeDistrictExperimentSearchRadius: "AYUTTHAYA-WAT_KUDI_DAO:4.5|5|5.5|6|6.5,AYUTTHAYA-WAT_MAHEYONG:7|7.5|8|8.5|9",
			expectError:                        true,
		},
		{
			name:                               "should error on parsing search radii #1",
			bikeDistrictExperimentDates:        "2025-01-30,2025-02-01,2025-02-03",
			bikeDistrictExperimentSearchRadius: "AYUTTHAYA-WAT_KUDI_DAO:4.5|5|5.5|6,AYUTTHAYA-WAT_MAHEYONG:7|7.5|8|8.5|9",
			expectError:                        true,
		},
		{
			name:                               "should error on parsing search radii #2",
			bikeDistrictExperimentDates:        "2025-01-30,2025-02-01,2025-02-03",
			bikeDistrictExperimentSearchRadius: "AYUTTHAYA-WAT_KUDI_DAO:4.5| 5 |5.5|6|6.5,AYUTTHAYA-WAT_MAHEYONG:7|7.5|8|8.5|9",
			expectError:                        true,
		},
	}

	container := ittest.NewContainer(t)
	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			os.Setenv("BIKE_DISTRICT_EXPERIMENT_SEARCH_RADIUS", tc.bikeDistrictExperimentSearchRadius)
			os.Setenv("BIKE_DISTRICT_EXPERIMENT_DATES", tc.bikeDistrictExperimentDates)

			container.ProviderDeps.Cfg.AtomicOrderDBConfig.Parse()
			parsedDates, parsedSearchRadiusMap, err := container.ProviderDeps.Cfg.AtomicOrderDBConfig.GetParsedBikeDistrictZonesExperimentConfigs()
			if tc.expectError {
				require.Error(tt, err)
				return
			}

			require.Nil(tt, err)
			require.Len(tt, parsedDates, 3)
			require.Len(tt, parsedSearchRadiusMap, 2)

			for _, date := range tc.expectedDateMatched {
				require.True(tt, isDateMatched(date, parsedDates))
			}
			for _, date := range tc.expectedDateUnmatched {
				require.False(tt, isDateMatched(date, parsedDates))
			}
			for expectedZone, expectedZoneRadii := range tc.expectedRadii {
				zoneRadii, ok := parsedSearchRadiusMap[expectedZone]
				require.True(tt, ok)
				require.EqualValues(tt, expectedZoneRadii, zoneRadii)
			}
		})
	}
}
