package rep

import (
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep/mock_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func TestPublishRepEventOrderCreated(t *testing.T) {
	t.Parallel()
	t.Run("should publish EventOrderCreated for both delivery and fleet", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCreated, driver).Return(nil)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, delivery).Return(nil)

		err := PublishRepEventOrderCreated(repSvc, ord)
		assert.NoError(tt, err)
	})

	t.Run("should return error fleet event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, _ := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCreated, driver).Return(errors.New("mock error"))

		err := PublishRepEventOrderCreated(repSvc, ord)
		assert.Error(tt, err)
	})

	t.Run("should return error delivery event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCreated, driver).Return(nil)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderCreated, delivery).Return(errors.New("mock error"))

		err := PublishRepEventOrderCreated(repSvc, ord)
		assert.Error(tt, err)
	})
}

func TestPublishRepEventOrderCompleted(t *testing.T) {
	t.Parallel()
	t.Run("should publish EventOrderCompleted for both delivery and fleet", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCompleted, driver)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderCompleted, delivery)

		err := PublishRepEventOrderCompleted(repSvc, ord)
		assert.NoError(tt, err)
	})

	t.Run("should return error fleet event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, _ := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCompleted, driver).Return(errors.New("mock error"))

		err := PublishRepEventOrderCompleted(repSvc, ord)
		assert.Error(tt, err)
	})

	t.Run("should return error delivery event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCompleted, driver).Return(nil)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderCompleted, delivery).Return(errors.New("mock error"))

		err := PublishRepEventOrderCompleted(repSvc, ord)
		assert.Error(tt, err)
	})
}
func TestPublishRepEventOrderUpdated(t *testing.T) {
	t.Parallel()
	t.Run("should publish EventOrderUpdated for both delivery and fleet", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderUpdated, driver)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderUpdated, delivery)

		err := PublishRepEventOrderUpdated(repSvc, ord)
		assert.NoError(tt, err)
	})

	t.Run("should return error fleet event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, _ := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderUpdated, driver).Return(errors.New("mock error"))

		err := PublishRepEventOrderUpdated(repSvc, ord)
		assert.Error(tt, err)
	})

	t.Run("should return error delivery event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderUpdated, driver).Return(nil)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderUpdated, delivery).Return(errors.New("mock error"))

		err := PublishRepEventOrderUpdated(repSvc, ord)
		assert.Error(tt, err)
	})
}
func TestPublishRepEventOrderCancelled(t *testing.T) {
	t.Parallel()
	t.Run("should publish EventOrderCancelled for both delivery and fleet", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCancelled, driver)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, delivery)

		err := PublishRepEventOrderCancelled(repSvc, ord)
		assert.NoError(tt, err)
	})

	t.Run("should return error fleet event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, _ := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCancelled, driver).Return(errors.New("mock error"))

		err := PublishRepEventOrderCancelled(repSvc, ord)
		assert.Error(tt, err)
	})

	t.Run("should return error delivery event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderCancelled, driver).Return(nil)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderCancelled, delivery).Return(errors.New("mock error"))

		err := PublishRepEventOrderCancelled(repSvc, ord)
		assert.Error(tt, err)
	})
}
func TestPublishRepEventOrderAccepted(t *testing.T) {
	t.Parallel()
	t.Run("should publish EventOrderAccepted for both delivery and fleet", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderAccepted, driver)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderAccepted, delivery)

		err := PublishRepEventOrderAccepted(repSvc, ord)
		assert.NoError(tt, err)
	})

	t.Run("should return error fleet event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, _ := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderAccepted, driver).Return(errors.New("mock error"))

		err := PublishRepEventOrderAccepted(repSvc, ord)
		assert.Error(tt, err)
	})

	t.Run("should return error delivery event error", func(tt *testing.T) {
		repSvc := newMockRep(t)
		ord := newOrder()
		driver, delivery := newMockPayload()

		repSvc.EXPECT().Publish(rep.EventOrderAccepted, driver).Return(nil)
		repSvc.EXPECT().Publish(rep.DeliveryEventOrderAccepted, delivery).Return(errors.New("mock error"))

		err := PublishRepEventOrderAccepted(repSvc, ord)
		assert.Error(tt, err)
	})
}

func newOrder() *model.Order {
	ord := &model.Order{
		OrderID: "LMF-1",
		Quote:   model.Quote{QuoteID: "LMMQ-1"},
		Status:  model.StatusAssigningDriver,
		Driver:  "LMD1",
	}

	ord.UserID = "U1"
	ord.ServiceType = model.ServiceFood
	ord.Region = model.RegionCode("AYUTTHAYA")
	ord.Routes = []model.Stop{
		{Location: model.Location{Lat: 1.0, Lng: 1.0}},
		{Location: model.Location{Lat: 1.1, Lng: 1.1}},
	}

	return ord
}

func newMockPayload() (*rep.FleetPayload, *rep.DeliveryPayload) {
	order := newOrder()
	driver := ToPayloadOrder(order)
	delivery := ToDeliveryPayloadOrder(order)

	return &driver, &delivery
}

func newMockRep(t *testing.T) *mock_rep.MockREPService {
	ctrl := gomock.NewController(t)
	rep := mock_rep.NewMockREPService(ctrl)

	return rep
}
