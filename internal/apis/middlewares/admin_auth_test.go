package middlewares_test

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/icrowley/fake"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestAdminAuthMiddleware(t *testing.T) {
	t.Cleanup(timeutils.Unfreeze)

	adminId := fake.Characters()

	t.Run("return 200 if user has a token", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		record := httptest.NewRecorder()
		_, r, deps := newRouter(ctrl, record)

		r.PUT("/v1/admin/drivers/test", deps.builder.AdminAuth(), func(gc *gin.Context) {
			user, _ := auth.GetAdminUserFromGctx(gc)
			require.Equal(tt, adminId, user.GetId())
		})
		req := testutil.NewContextWithRecorder().SetPUT("/v1/admin/drivers/test").
			AdminAuthorized(adminId).GinCtx().Request

		r.ServeHTTP(record, req)

		require.Equal(tt, http.StatusOK, record.Code)
	})

	t.Run("return 401 if user doesn't has a token", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		record := httptest.NewRecorder()
		_, r, deps := newRouter(ctrl, record)

		r.PUT("/v1/admin/drivers/test", deps.builder.AdminAuth(), func(gc *gin.Context) {
			user, _ := auth.GetAdminUserFromGctx(gc)
			require.Equal(tt, nil, user)
		})
		req := testutil.NewContextWithRecorder().SetPUT("/v1/admin/drivers/test").GinCtx().Request
		r.ServeHTTP(record, req)

		require.Equal(tt, http.StatusUnauthorized, record.Code)
	})

	t.Run("return 200 if user has a token", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		record := httptest.NewRecorder()
		_, r, deps := newRouter(ctrl, record)

		r.PUT("/v1/admin/drivers/test", deps.builder.AdminAuth(), func(gc *gin.Context) {
			user, _ := auth.GetAdminUserFromGctx(gc)
			require.Equal(tt, adminId, user.GetId())
		})
		req := testutil.NewContextWithRecorder().SetPUT("/v1/admin/drivers/test").GinCtx().Request
		r.ServeHTTP(record, req)

		require.Equal(tt, http.StatusUnauthorized, record.Code)
	})
}

func TestMiddleware_AllowedAdminRoles(t *testing.T) {
	adminId := fake.Characters()
	t.Cleanup(timeutils.Unfreeze)

	t.Run("return 200 if a user has any permissions matching with allowed permissions", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		record := httptest.NewRecorder()
		_, r, deps := newRouter(ctrl, record)

		req := testutil.NewContextWithRecorder().SetGET("/fake_path").
			AdminAuthorized(adminId, "FakeRole", "ExportRole").GinCtx().Request

		r.GET("/fake_path", deps.builder.AdminAuth("FakeRole"), func(gc *gin.Context) {})
		r.ServeHTTP(record, req)

		require.Equal(tt, http.StatusOK, record.Code)
	})

	t.Run("return 403 when reach an api without allowed permissions", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		record := httptest.NewRecorder()
		_, r, deps := newRouter(ctrl, record)

		req := testutil.NewContextWithRecorder().SetGET("/fake_path").
			AdminAuthorized(adminId, "WrongRole").GinCtx().Request

		r.GET("/fake_path", deps.builder.AdminAuth("FakeRole"), func(gc *gin.Context) {})
		r.ServeHTTP(record, req)

		require.Equal(tt, http.StatusForbidden, record.Code)
	})

	t.Run("return 401 if user doesn't has a token", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		record := httptest.NewRecorder()
		gctx, r, deps := newRouter(ctrl, record)

		r.GET("/fake_path", deps.builder.AdminAuth("FakeRole"), func(gc *gin.Context) {})
		req := httptest.NewRequest("GET", "/fake_path", nil)
		r.ServeHTTP(record, req)

		val, _ := gctx.Get(driver.AdminIdKey)
		require.Equal(tt, nil, val)
		require.Equal(tt, http.StatusUnauthorized, record.Code)
	})

	t.Run("return 200 if user has the SUPER_USER permission", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		record := httptest.NewRecorder()
		gctx, r, deps := newRouter(ctrl, record)

		r.GET("/fake_path", deps.builder.AdminAuth("FakeRole"), func(gc *gin.Context) {})
		req := testutil.NewContextWithRecorder().SetGET("/fake_path").
			AdminAuthorized(adminId, "SUPER_USER").GinCtx().Request
		r.ServeHTTP(record, req)

		val, _ := gctx.Get(driver.AdminIdKey)
		require.Equal(tt, nil, val)
		require.Equal(tt, http.StatusOK, record.Code)
	})
}
