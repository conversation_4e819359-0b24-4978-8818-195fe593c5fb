package middlewares

import (
	"fmt"

	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

func (b *Builder) FeatureToggle(flag bool) gin.HandlerFunc {
	if !flag {
		return func(gctx *gin.Context) {
			apiutil.ErrStatusForbidden(gctx, apiutil.NewFromError("FORBIDDEN", fmt.Errorf("the api is disabled")))
			gctx.Abort()
			return
		}
	}
	return func(gctx *gin.Context) {
		gctx.Next()
	}
}

// ReportDeprecatedUsed is gin middleware to report sentry.Event when a deprecated API was called
//
// Parameters:
//   - shouldReport - report event if the evaluation is 'true'
func ReportDeprecatedUsed(shouldReport func() bool) gin.HandlerFunc {
	return func(gctx *gin.Context) {
		if shouldReport() {
			safe.SentryErrorMessage("unexpected calling, the API was deprecated", func(contexts map[string]sentry.Context) {
				contexts["deprecated-api"] = map[string]interface{}{
					"method": gctx.Request.Method,
					"path":   gctx.Request.RequestURI,
				}
			})
		}

		gctx.Next()
	}
}
