package account

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"

	form_servicev1 "git.wndv.co/go/proto/lineman/form_service/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func Test_NewOrderClaimListResponse(t *testing.T) {

	t.Run("isClaimed is true on order1", func(t *testing.T) {
		newStruct, err := structpb.NewStruct(map[string]any{
			"orderId": "order1",
		})
		require.NoError(t, err)
		form := &form_servicev1.ListFormResponse{
			Data: []*form_servicev1.Form{
				{
					FormSubtype: 5,
					Value:       newStruct,
				},
			},
		}

		trip := &model.Trip{
			Orders: []model.TripOrder{
				{
					OrderID: "order1",
				},
			},
		}

		r := NewOrderClaimListResponse(context.Background(), trip, form, availableClaimSubType, &model.Order{Region: "BKK"}, service.NewAtomicFormServiceConfig(service.FormServiceConfig{}), config.FormConfig{}, nil)
		require.Equal(t, true, r.ClaimSubTypes[0].Orders[0].IsClaimed)

	})

	t.Run("isClaimed is true on order1", func(t *testing.T) {
		newStruct, err := structpb.NewStruct(map[string]any{
			"orderId": "order2",
		})
		require.NoError(t, err)
		form := &form_servicev1.ListFormResponse{
			Data: []*form_servicev1.Form{
				{
					FormSubtype: 5,
					Value:       newStruct,
				},
			},
		}

		trip := &model.Trip{
			Orders: []model.TripOrder{
				{
					OrderID: "order1",
				},
				{
					OrderID: "order2",
				},
			},
		}

		r := NewOrderClaimListResponse(context.Background(), trip, form, availableClaimSubType, &model.Order{Region: ""}, service.NewAtomicFormServiceConfig(service.FormServiceConfig{}), config.FormConfig{}, nil)
		require.Equal(t, false, r.ClaimSubTypes[0].Orders[0].IsClaimed)
		require.Equal(t, true, r.ClaimSubTypes[0].Orders[1].IsClaimed)

	})

	t.Run("check claimable for a pickup distance claim form", func(tt *testing.T) {

		newStruct, err := structpb.NewStruct(map[string]any{
			"orderId": "order2",
		})
		require.NoError(tt, err)
		form := &form_servicev1.ListFormResponse{
			Data: []*form_servicev1.Form{
				{
					FormSubtype: 5,
					Value:       newStruct,
				},
			},
		}

		trip := &model.Trip{
			Orders: []model.TripOrder{
				{
					OrderID:     "order1",
					ServiceType: model.ServiceFood,
					Status:      model.StatusCompleted,
					CreatedAt:   timeutils.Now(),
				},
				{
					OrderID:     "order2",
					ServiceType: model.ServiceFood,
					Status:      model.StatusCompleted,
					CreatedAt:   timeutils.Now(),
				},
			},
			Routes: []model.TripRoute{
				{
					Action:     model.TripActionPickUp,
					StopOrders: model.TripStopOrders{{OrderID: "order1"}},
				},
				{
					Action:     model.TripActionPickUp,
					StopOrders: model.TripStopOrders{{OrderID: "order2"}},
				},
				{
					Action:     model.TripActionDropOff,
					StopOrders: model.TripStopOrders{{OrderID: "order1"}},
				},
				{
					Action:     model.TripActionDropOff,
					StopOrders: model.TripStopOrders{{OrderID: "order2"}},
				},
			},
			History: model.TripHistory{},
		}
		trip.History.InsertNewEntry("order1", model.StatusDriverArrivedRestaurant, model.TripStatusDriveTo)
		trip.History.InsertNewEntry("order1", model.StatusCompleted, model.TripStatusArrivedAt)
		trip.History.InsertNewEntry("order2", model.StatusCanceled, model.TripStatusCompleted)

		r := NewOrderClaimListResponse(context.Background(), trip, form, []ClaimSubType{
			{
				Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM],
				SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM.String(),
			},
		}, &model.Order{Region: "BKK"}, service.NewAtomicFormServiceConfig(service.FormServiceConfig{}), config.FormConfig{}, nil)

		require.Equal(tt, false, r.ClaimSubTypes[0].Orders[0].IsClaimed)
		require.Equal(tt, false, r.ClaimSubTypes[0].Orders[1].IsClaimed)
		require.Equal(tt, true, r.ClaimSubTypes[0].Orders[0].IsClaimable)
		require.Equal(tt, false, r.ClaimSubTypes[0].Orders[1].IsClaimable)
	})
}

func Test_getClaimSubType(t *testing.T) {
	t.Run("show all sub type", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType()
		require.Equal(t, len(availableClaimSubType), len(r))
	})

	t.Run("food claim sub type", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType().filterSubTypeByServiceTypeAndRegionWithConfig(model.ServiceFood, "BKK", false)
		require.Equal(t, len(availableClaimSubType)-1, len(r))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FOOD_CLAIM.String()))
	})

	t.Run("food claim sub type - region not BKK", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType().filterSubTypeByServiceTypeAndRegionWithConfig(model.ServiceFood, "TAK", false)
		require.Equal(t, len(availableClaimSubType)-2, len(r))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FOOD_CLAIM.String()))
	})

	t.Run("mart should show food claim sub type", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType().filterSubTypeByServiceTypeAndRegionWithConfig(model.ServiceMart, "BKK", false)
		require.Equal(t, len(availableClaimSubType)-2, len(r))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FOOD_CLAIM.String()))
	})

	t.Run("not show parking claim sub type", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType().filterSubTypeByServiceTypeAndRegionWithConfig(model.ServiceMart, "TAK", false)
		require.Equal(t, len(availableClaimSubType)-2, len(r))
		require.Equal(t, false, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_PARKING_CLAIM.String()))
	})

	t.Run("mart show food claim but not show parking claim", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType().filterSubTypeByServiceTypeAndRegionWithConfig(model.ServiceMart, "TAK", false)
		require.Equal(t, len(availableClaimSubType)-2, len(r))
		require.Equal(t, false, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_PARKING_CLAIM.String()))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FOOD_CLAIM.String()))
	})

	t.Run("messenger show rider compensation claim and fair_dispute claim", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType().filterSubTypeByServiceTypeAndRegionWithConfig(model.ServiceMessenger, "TAK", false)
		require.Equal(t, len(availableClaimSubType)-4, len(r))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION.String()))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM.String()))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM.String()))
	})

	t.Run("bike show rider compensation claim", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType().filterSubTypeByServiceTypeAndRegionWithConfig(model.ServiceBike, "TAK", false)
		require.Equal(t, len(availableClaimSubType)-4, len(r))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION.String()))
		require.Equal(t, false, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM.String()))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM.String()))
	})
	t.Run("bike show rider compensation claim and fair_dispute claim when fair_dispute config is enabled", func(t *testing.T) {
		acc := &AccountAPI{}
		r := acc.getClaimSubType().filterSubTypeByServiceTypeAndRegionWithConfig(model.ServiceBike, "TAK", true)
		require.Equal(t, len(availableClaimSubType)-3, len(r))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION.String()))
		require.Equal(t, true, findSubType(r, form_servicev1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM.String()))
	})
}

func findSubType(r []ClaimSubType, subType string) bool {
	for _, v := range r {
		if v.SubType == subType {
			return true
		}
	}
	return false
}
