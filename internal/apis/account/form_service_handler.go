package account

import (
	"context"
	"errors"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/polygonutil"
)

var (
	ErrInvalidFormSubtype error = errors.New("invalid form subtype")
)

func (accapi *AccountAPI) calculateCancellationMetadata(ctx context.Context, order *model.Order) error {
	if order.CancelDetail.CancellationMetadata == nil {
		order.CancelDetail.CancellationMetadata = &model.CancellationMetadata{}
	}

	var isUpdated bool
	if order.CancelDetail.CancellationMetadata.ActualDistanceBetweenDriverMatchedAndCancelled == nil {
		resultIsUpdated, err := accapi.calculateActualDistanceBetweenDriverMatchedAndCancelled(ctx, order)
		if err != nil {
			return err
		}

		// This operator is to make sure that the isUpdated will stay true once it has been set to true
		isUpdated = isUpdated || resultIsUpdated
	}

	if order.CancelDetail.CancellationMetadata.DisplacementBetweenArrivedAtAndCancelled == nil {
		resultIsUpdated := accapi.calculateDisplacementBetweenArrivedAtAndCancelled(ctx, order)

		// This operator is to make sure that the isUpdated will stay true once it has been set to true
		isUpdated = isUpdated || resultIsUpdated
	}

	// Update the order CancellationMetadata
	if isUpdated {
		err := accapi.OrderRepo.UpdateOrderCancellationMetadata(ctx, order)
		if err != nil {
			logx.Error().Err(err).Msg("Fail to update order CancellationMetadata")
			return err
		}
	}
	return nil
}

func (accapi *AccountAPI) calculateDisplacementBetweenArrivedAtAndCancelled(ctx context.Context, order *model.Order) bool {
	arrivedAtLocation, ok := order.HistoryLocation[model.NewArrivedAtOrderStatus(0).String()]
	if !ok {
		logx.Warn().Context(ctx).Msg("No ArrivedAt Location in the HistoryLocation. Skipping the DisplacementBetweenArrivedAtAndCancelled calculation")
		return false
	}

	cancelledLocation, ok := order.HistoryLocation[model.StatusCanceled.String()]
	if !ok {
		logx.Warn().Context(ctx).Msg("No CANCELLED Location in the HistoryLocation. Skipping the DisplacementBetweenArrivedAtAndCancelled calculation")
		return false
	}

	distanceInMeter := polygonutil.DistanceInMeter(
		model.Location{
			Lat: arrivedAtLocation.Lat,
			Lng: arrivedAtLocation.Lng,
		},
		model.Location{
			Lat: cancelledLocation.Lat,
			Lng: cancelledLocation.Lng,
		},
	)

	order.CancelDetail.CancellationMetadata.DisplacementBetweenArrivedAtAndCancelled = &distanceInMeter
	return true
}

func (accapi *AccountAPI) calculateActualDistanceBetweenDriverMatchedAndCancelled(ctx context.Context, order *model.Order) (bool, error) {
	driverMatchedLocation, ok := order.HistoryLocation[model.StatusDriverMatched.String()]
	if !ok {
		logx.Warn().Context(ctx).Msg("No DRIVER_MATCHED Location in the HistoryLocation. Skipping the ActualDistanceBetweenDriverMatchedAndCancelled calculation")
		return false, nil
	}
	cancelledLocation, ok := order.HistoryLocation[model.StatusCanceled.String()]
	if !ok {
		logx.Warn().Context(ctx).Msg("No CANCELED Location in the HistoryLocation. Skipping the ActualDistanceBetweenDriverMatchedAndCancelled calculation")
		return false, nil
	}

	// Call MapProxyGo service to get the distance.
	actualDistanceResponse, err := accapi.mapService.FindDistances(
		ctx,
		[]mapservice.Location{
			{
				Lat: driverMatchedLocation.Lat,
				Lng: driverMatchedLocation.Lng,
			},
		},
		mapservice.Location{
			Lat: cancelledLocation.Lat,
			Lng: cancelledLocation.Lng,
		},
	)
	if err != nil {
		logx.Error().Context(ctx).Err(err).Msg("Fail to call OSRM to calculate the distance")
		return false, err
	}

	if len(actualDistanceResponse.Distances) != 1 {
		logx.Error().Err(err).Msg("OSRM response unexpected number of results")
		return false, errors.New("OSRM response unexpected number of results")
	}

	order.CancelDetail.CancellationMetadata.ActualDistanceBetweenDriverMatchedAndCancelled = fp.ToPointer(actualDistanceResponse.Distances[0])
	return true, nil
}

func getLatestOrderByDriverID(ctx context.Context, orderRepo repository.OrderRepository, driverId string) (*model.Order, error) {
	query := persistence.NewOrderQuery()
	query.WithDriverID(driverId)

	orders, err := orderRepo.FindAndSort(ctx, query.Query(), 0, 1, repository.DefaultSort, repository.WithReadSecondaryPreferred)
	if err != nil {
		return &model.Order{}, err
	}

	if len(orders) > 0 {
		return &orders[0], nil
	}
	return nil, nil
}
