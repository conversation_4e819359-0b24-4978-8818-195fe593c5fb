package account

import (
	"encoding/json"
	"errors"
	"fmt"
	"mime/multipart"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"gopkg.in/go-playground/validator.v9"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/account/helpers"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	apiValidator "git.wndv.co/lineman/fleet-distribution/internal/apis/validator"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const (
	DriverImageName              = "avatar"
	DriverLicenseImageName       = "driver_license"
	CitizenIDImageName           = "citizen_id"
	VehicleImageName             = "vehicle"
	VehicleRegistrationImageName = "vehicle_registration"
	BookBankImageName            = "book_bank"
	LegislationImageName         = "legislation"
	LendingVehicleImageName      = "lending_vehicle"

	LimitFileSize = 1 * 1024 * 1024 // MB
)

var ErrUnsupportedGranularity = errors.New("unsupported granularity")
var ErrInvalidStatus = errors.New("invalid status")
var ErrInvalidServiceTypePreference = errors.New("invalid service type preference")
var ErrUpdateExceedQuota = errors.New("quota limit exceed")
var ErrImageOverSize = errors.New("file over size limit")

// LoginRequest login request
type LoginRequest struct {
	UID             string `json:"uid" form:"uid"`
	LineAccessToken string `json:"lineAccessToken" form:"lineAccessToken" binding:"required"`
}

type LoginUidRequest struct {
	UID string `json:"uid" form:"uid" binding:"required"`
}

// RefreshToken request token request
type RefreshTokenRequest struct {
	RefreshToken            string `json:"refreshToken" binding:"required"`
	ShouldRenewRefreshToken bool   `json:"shouldRenewRefreshToken"`
}

type Order struct {
	OrderId     string `json:"orderId"`
	OrderStatus string `json:"orderStatus"`
}

type SavedLocation struct {
	AccuracyInMeter    float64 `json:"accuracyInMeter"`
	Bearing            float64 `json:"bearing"`
	Lat                float64 `json:"lat"`
	Lng                float64 `json:"lng"`
	Orders             []Order `json:"orders"`
	SpeedInMeterPerSec float64 `json:"speedInMeterPerSec"`
	Timestamp          int64   `json:"timestamp"`
	TripId             string  `json:"tripId"`
	TripStatus         string  `json:"tripStatus"`
}

type UpdateDriverLocationHeader struct {
	ClientTimestamp int64 `header:"x-client-timestamp"`
}

// UpdateDriverLocationRequest is the request for update driver location api.
type UpdateDriverLocationRequest struct {
	Lat                float64         `json:"lat" binding:"required"`
	Lng                float64         `json:"lng" binding:"required"`
	Region             string          `json:"region"`
	AccuracyInMeter    float64         `json:"accuracyInMeter"`
	Orders             []Order         `json:"orders"`
	SavedLocations     []SavedLocation `json:"savedLocations"`
	SpeedInMeterPerSec float64         `json:"speedInMeterPerSec"`
	TripId             string          `json:"tripId"`
	TripStatus         string          `json:"tripStatus"`
}

// UpdateDeviceTokenRequest is the request for update device token, device ID, and advertise ID
type UpdateDeviceTokenRequest struct {
	DeviceToken string `json:"deviceToken" binding:"required"`
	DeviceID    string `json:"tsDeviceId"`
	AdvertiseID string `json:"advertiseId"`
}

// UpdateSocketIDRequest is the request for update socket ID
type UpdateSocketIDRequest struct {
	SocketID string `json:"socketId"`
}

// UpdateDriverStatusRequest is the request for update driver status api.
type UpdateDriverStatusRequest struct {
	// Status that want to update.
	Status model.DriverStatus `json:"status"`
}

type UpdateCashRequest struct {
	Amount float64 `json:"amount" binding:"gte=0"`
}

type UpdateServicePreferenceRequest struct {
	ServicesOptOut []model.Service `json:"servicesOptOut"`
}

func (req *UpdateServicePreferenceRequest) Validate() error {
	optedOutSvcs := types.NewStringSet(model.Services(req.ServicesOptOut).String()...)
	supportedSvcs := types.NewStringSet(model.Services(model.CurrentSupportServices).String()...)
	if !supportedSvcs.HasAll(optedOutSvcs.GetElements()...) || optedOutSvcs.HasAll(supportedSvcs.GetElements()...) {
		return ErrInvalidServiceTypePreference
	}
	return nil
}

func (req *UpdateDriverStatusRequest) Validate() error {
	switch req.Status {
	case model.StatusOffline, model.StatusOnline:
		return nil
	default:
		return ErrInvalidStatus
	}
}

type DriverCreditType string

const (
	WalletDriverCreditType DriverCreditType = "wallet"
)

type DriverCreditReq struct {
	Type   DriverCreditType `json:"type" binding:"required,oneof=wallet"`
	Amount float64          `json:"amount" binding:"required,gt=0"`
}

type DriverWalletType string

const (
	WithdrawDriverWalletType DriverWalletType = "withdraw"
)

type DriverWalletReq struct {
	Type    DriverWalletType `json:"type" binding:"required,oneof=withdraw"`
	Amount  float64          `json:"amount" binding:"required,numeric"`
	Payload interface{}      `json:"payload"`
}

type AddTermAndConditionRequest struct {
	// backward compatible
	TermService   string `json:"termService"`
	PrivacyPolicy string `json:"privacyPolicy"`

	TermServiceF   float64 `json:"termOfServiceVersion"`
	PrivacyPolicyF float64 `json:"privacyPolicyVersion"`
}

func (tcr AddTermAndConditionRequest) validate(termServiceVersion float64, privacyPolicyVersion float64) error {
	if termServiceVersion < 0 || privacyPolicyVersion < 0 {
		return errors.New("invalid version, term service version and privacy policy version must be >= 0")
	}

	return nil
}

func (tcr *AddTermAndConditionRequest) getVersion() (termServiceVersion float64, privacyPolicyVersion float64, err error) {
	// for backward compatible
	isOldVersion := tcr.TermService != "" && tcr.PrivacyPolicy != ""

	if isOldVersion {
		if termServiceVersion, err = strconv.ParseFloat(tcr.TermService, 64); err != nil {
			return 0, 0, err
		}
		if privacyPolicyVersion, err = strconv.ParseFloat(tcr.PrivacyPolicy, 64); err != nil {
			return 0, 0, err
		}
	} else {
		termServiceVersion = tcr.TermServiceF
		privacyPolicyVersion = tcr.PrivacyPolicyF
	}

	return termServiceVersion, privacyPolicyVersion, nil
}

type UpdateAutoAcceptMode struct {
	AutoAccept *bool `json:"autoAccept" binding:"required"`
}

type TransactionReqCategory string

const (
	CreditTransactionReqCategory TransactionReqCategory = "CREDIT"
	WalletTransactionReqCategory TransactionReqCategory = "WALLET"
)

var mapTransCategoryToModel = map[TransactionReqCategory]model.TransactionCategory{
	CreditTransactionReqCategory: model.CreditTransactionCategory,
	WalletTransactionReqCategory: model.WalletTransactionCategory,
}

func (a TransactionReqCategory) ToModel() model.TransactionCategory {
	return mapTransCategoryToModel[a]
}

type ListTransactionReq struct {
	Category      TransactionReqCategory `form:"category" binding:"required,oneof=CREDIT WALLET"`
	NextPageToken string                 `form:"nextPageToken"`
	InfoType      model.TransactionType  `form:"infoType"`
}

func (a ListTransactionReq) ToQuery(driverID string, transactionSchemes []model.TransactionScheme, withPagination bool) persistence.TransactionQuery {
	var (
		types    []model.TransactionType
		statuses []model.TransactionStatus
	)

	if a.InfoType != "" {
		types = []model.TransactionType{
			a.InfoType,
		}
		statuses = []model.TransactionStatus{}
	} else {
		switch a.Category.ToModel() {
		case model.CreditTransactionCategory:
			types = []model.TransactionType{
				model.PurchaseTransactionType,
				model.CommissionTransactionType,
				model.WithholdingTransactionType,
				model.ItemFeeTransactionType,
				model.VoidTransactionType,
				model.ExpiredTransactionType,
				model.VoidReturnCreditTransactionType,
				model.ChargeTransactionType,
				model.WithdrawTransactionType,
				model.UserDeliveryFeeType,
				model.WithdrawalFeeTransactionType,
				model.InstallmentDeductTransactionType,
				model.AddCreditRefinanceTransactionType,
				model.PenaltyChargeTransactionType,
				model.AddCreditInstallmentCancellationTransactionType,
			}

			types = appendTypesFromTransactionSchemes(transactionSchemes, types, model.CreditTransactionCategory)

			statuses = []model.TransactionStatus{
				model.SuccessTransactionStatus,
				model.FailTransactionStatus,
			}
		case model.WalletTransactionCategory:
			types = []model.TransactionType{
				model.IncentiveTransactionType,
				model.WithdrawTransactionType,
				model.CouponTransactionType,
				model.SubsidizeTransactionType,
				model.CompensationTransactionType,
				model.ClaimTransactionType,
				model.DeliveryFeeTransactionType,
				model.VoidTransactionType,
				model.NewRiderIncentiveTransactionType,
				model.CashAdvanceCouponTransactionType,
				model.RiderReferralIncentiveTransactionType,
				model.AddTransactionType,
				model.DeductVoidTransactionType,
				model.OtherIncentiveTransactionType,
				model.DeductVoidFraudTransactionType,
				model.AddVoidFraudTransactionType,
				model.OnTopTransactionType,
				model.DriverWageType,
				model.TipTransactionType,
				model.ItemFeeTransactionType,
				model.InstallmentDeductTransactionType,
				model.AdjustNormalOrderTripWageTransactionType,
				model.PenaltyChargeTransactionType,
				model.RefundInstallmentCancellationTransactionType,
				model.AdditionalServiceFeeTransactionType,
				model.GoodwillTransactionType,
			}

			types = appendTypesFromTransactionSchemes(transactionSchemes, types, model.WalletTransactionCategory)

			statuses = []model.TransactionStatus{
				model.SuccessTransactionStatus,
				model.RejectedTransactionStatus,
				model.PendingTransactionStatus,
				model.ProcessingTransactionStatus,
				model.FailTransactionStatus,
			}
		}
	}

	var nextPageToken primitive.ObjectID

	if withPagination {
		nextPageToken, _ = primitive.ObjectIDFromHex(a.NextPageToken)
	}

	query := persistence.TransactionQuery{
		DriverID:      driverID,
		Category:      a.Category.ToModel(),
		Types:         types,
		Statuses:      statuses,
		NextPageToken: nextPageToken,
	}

	return query
}

func appendTypesFromTransactionSchemes(transactionSchemes []model.TransactionScheme, types []model.TransactionType, category model.TransactionCategory) []model.TransactionType {
	for _, scheme := range transactionSchemes {
		if scheme.Category == category {
			types = append(types, scheme.Type)
		}
	}
	return types
}

func (a ListTransactionReq) ToTransactionSchemeQuery() repository.Query {
	return persistence.NewTransactionSchemeQuery().WithCategory(model.TransactionCategory(a.Category))
}

type SaveEventOrderTrackingReq struct {
	OrderID     string  `json:"orderId"`
	OrderStatus string  `json:"orderStatus"`
	Lat         float64 `json:"lat" binding:"required"`
	Lng         float64 `json:"lng" binding:"required"`
}

type WithDrawRequest struct {
	Account       string `json:"account" binding:"omitempty,integer"`
	BankName      string `json:"bankName" binding:"omitempty,bank"`
	AccountHolder string `json:"accountHolder" binding:"omitempty"`
}

func (d *DriverWalletReq) UnmarshalJSON(b []byte) error {
	req := struct {
		Type            DriverWalletType `json:"type" binding:"required,oneof=withdraw"`
		Amount          float64          `json:"amount" binding:"required,numeric"`
		DeviceSignature string           `json:"deviceSignature"`
	}{}
	if err := json.Unmarshal(b, &req); err != nil {
		return err
	}
	payloadReq := struct {
		Payload interface{} `json:"payload"`
	}{}

	switch req.Type {
	case WithdrawDriverWalletType:
		payloadReq.Payload = &WithDrawRequest{}
	}

	if err := json.Unmarshal(b, &payloadReq); err != nil {
		return err
	}

	d.Type = req.Type
	d.Amount = req.Amount
	d.Payload = payloadReq.Payload

	return nil
}

func NewDriverWalletRequest(gctx *gin.Context) (*DriverWalletReq, *api.Error) {
	var req DriverWalletReq
	if err := gctx.ShouldBindJSON(&req); err != nil {
		if validErrors, ok := err.(validator.ValidationErrors); ok {
			errs := apiErrors.NewMultipleError()
			for _, verr := range validErrors {
				errs.AddError(apiErrors.NewValidatorError(verr, apiValidator.GetTranslator(gctx.GetHeader("Accept-Language"))))
			}
			return nil, errs.NewAPIError()
		}

		return nil, apiErrors.ErrInvalidRequest(err)
	}
	return &req, nil
}

func (w *WithDrawRequest) WithdrawInfo() model.WithdrawInfo {
	if w.BankName == "" && w.Account == "" && w.AccountHolder == "" {
		return model.WithdrawInfo{}
	}
	return model.WithdrawInfo{
		Account:       crypt.EncryptedString(w.Account),
		BankName:      crypt.EncryptedString(w.BankName),
		AccountHolder: crypt.EncryptedString(w.AccountHolder),
	}
}

func (w *WithDrawRequest) RemoveUnexpectedCharacter() {
	trimSpace := strings.TrimSpace(w.AccountHolder)
	w.AccountHolder = strings.TrimFunc(trimSpace, isQuote)
}

func isQuote(r rune) bool {
	return r == '\n' || r == '\'' || r == '"'
}

func (w *WithDrawRequest) Validate() error {
	if w.AccountHolder != "" && !IsValidAccountHolder(w.AccountHolder) {
		return errors.New("invalid bank account holder. allow only thai and english characters")
	}
	return nil
}

var accountHolderRegx = regexp.MustCompile(`[.ก-๙a-zA-Z\s]+$`)

func IsValidAccountHolder(in string) bool {
	return accountHolderRegx.MatchString(in)
}

type Granularity string
type PerformanceType string

const (
	DAILY       Granularity = "daily"
	WEEKLY      Granularity = "weekly"
	MONTHLY     Granularity = "monthly"
	LAST8DAYAGO Granularity = "last_8_day_ago"

	AR   PerformanceType = "ar"
	CR   PerformanceType = "cr"
	ATTR PerformanceType = "attr"
)

type DriverPerformanceReq struct {
	Granularity Granularity       `form:"granularity" binding:"required"`
	Date        time.Time         `form:"date"`
	Types       []PerformanceType `form:"types"`
}

func (dpr *DriverPerformanceReq) GetTypes() []string {
	if len(dpr.Types) == 0 {
		return []string{string(AR), string(CR), string(ATTR)}
	}
	var types []string
	for _, performanceType := range dpr.Types {
		types = append(types, string(performanceType))
	}
	return types
}

func (dpr *DriverPerformanceReq) GetStartDate() time.Time {
	if dpr.Date.IsZero() {
		dpr.Date = timeutil.BangkokNow()
	}

	if dpr.Granularity == WEEKLY {
		offset := int(time.Monday - dpr.Date.Weekday())
		if offset > 0 {
			offset = -6
		}
		if offset != 0 {
			dpr.Date = dpr.Date.AddDate(0, 0, offset)
		}
	} else if dpr.Granularity == MONTHLY {
		if dpr.Date.Day() != 1 {
			dpr.Date = time.Date(dpr.Date.Year(), dpr.Date.Month(), 1, dpr.Date.Hour(), dpr.Date.Minute(), dpr.Date.Second(), dpr.Date.Nanosecond(), dpr.Date.Location())
		}
	}

	return time.Date(dpr.Date.Year(), dpr.Date.Month(), dpr.Date.Day(), 0, 0, 0, 0, timeutil.BangkokLocation())
}

func (dpr *DriverPerformanceReq) GetEndDate() (time.Time, error) {
	switch dpr.Granularity {
	case DAILY:
		return dpr.GetStartDate(), nil
	case WEEKLY:
		return dpr.GetStartDate().AddDate(0, 0, 6), nil
	case MONTHLY:
		return time.Date(dpr.GetStartDate().Year(), dpr.GetStartDate().Month()+1, 0, dpr.GetStartDate().Hour(), dpr.GetStartDate().Minute(), dpr.GetStartDate().Second(), dpr.GetStartDate().Nanosecond(), timeutil.BangkokLocation()), nil
	}

	return time.Time{}, ErrUnsupportedGranularity
}

func (dpr *DriverPerformanceReq) GetPreviousGranularityStartDate() (time.Time, error) {
	switch dpr.Granularity {
	case DAILY:
		return dpr.GetStartDate().AddDate(0, 0, -1), nil
	case WEEKLY:
		return dpr.GetStartDate().AddDate(0, 0, -7), nil
	case MONTHLY:
		return time.Date(dpr.GetStartDate().Year(), dpr.GetStartDate().Month()-1, 1, dpr.GetStartDate().Hour(), dpr.GetStartDate().Minute(), dpr.GetStartDate().Second(), dpr.GetStartDate().Nanosecond(), timeutil.BangkokLocation()), nil
	}

	return time.Time{}, ErrUnsupportedGranularity
}

func (dpr *DriverPerformanceReq) GetPreviousGranularityEndDate() (time.Time, error) {
	switch dpr.Granularity {
	case DAILY:
		return dpr.GetStartDate().AddDate(0, 0, -1), nil
	case WEEKLY:
		return dpr.GetStartDate().AddDate(0, 0, -1), nil
	case MONTHLY:
		return time.Date(dpr.GetStartDate().Year(), dpr.GetStartDate().Month(), 0, dpr.GetStartDate().Hour(), dpr.GetStartDate().Minute(), dpr.GetStartDate().Second(), dpr.GetStartDate().Nanosecond(), timeutil.BangkokLocation()), nil
	}

	return time.Time{}, ErrUnsupportedGranularity
}

func NewDriverPerformanceReq(ctx *gin.Context) (*DriverPerformanceReq, error) {
	var req DriverPerformanceReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		return nil, err
	}

	return &req, nil
}

//------------- From this point on, everything is duplicated from internal/apis/driver/registration_req.go -------------

type UpdateProfileReq struct {
	DriverID                 string
	Profile                  UpdateProfilePayload  `form:"profile" binding:"required"`
	DriverImage              *multipart.FileHeader `form:"driverImage"`
	DriverLicenseImage       *multipart.FileHeader `form:"driverLicenseImage"`
	CitizenIDImage           *multipart.FileHeader `form:"citizenIdImage"`
	VehicleImage             *multipart.FileHeader `form:"vehicleImage"`
	VehicleRegistrationImage *multipart.FileHeader `form:"vehicleRegistrationImage"`
	BookBankImage            *multipart.FileHeader `form:"bookBankImage"`
	LegislationImage         *multipart.FileHeader `form:"legislationImage"`
	LendingVehicleImage      *multipart.FileHeader `form:"lendingVehicleImage"`
	UpdateProfileHeader      UpdateProfileHeader
}

type UpdateProfileHeader struct {
	DeviceID string `header:"x-device-id"`
}

type UpdateProfilePayload struct {
	Title                  string               `json:"title" binding:"required,driver-title"`
	Firstname              string               `json:"firstname" binding:"required,alphabet-thai"`
	Lastname               string               `json:"lastname" binding:"required,alphabet-thai"`
	Phone                  string               `json:"phone" binding:"required,phone"`
	EmergencyPhone         string               `json:"emergencyPhone" binding:"omitempty,phone"`
	CitizenID              string               `json:"citizenId" binding:"required,citizen-id"`
	CitizenIDExpiredDate   *time.Time           `json:"citizenIDExpiredDate" binding:"omitempty"`
	Birthday               time.Time            `json:"birthday" binding:"required,birthday"`
	InterestingProvince    string               `json:"interestingProvince" binding:"required"`
	Address                AddressPayload       `json:"address" binding:"required"`
	Bank                   BankPayload          `json:"banking" binding:"required"`
	DriverLicense          DriverLicensePayload `json:"driverLicense" binding:"required"`
	Vehicle                VehiclePayload       `json:"vehicle" binding:"required"`
	AcceptedConsentVersion int64                `json:"acceptedConsentVersion" binding:"required"`
}

type AddressPayload struct {
	HouseNumber string `json:"houseNumber" binding:"omitempty"`
	Moo         string `json:"moo" binding:"omitempty"`
	Subdistrict string `json:"subdistrict" binding:"omitempty"`
	District    string `json:"district" binding:"omitempty"`
	Province    string `json:"province" binding:"omitempty"`
	Zipcode     string `json:"zipcode" binding:"omitempty,len=5,integer"`
}

func (a *AddressPayload) address() (*model.Address, apiErrors.MultipleError) {
	return &model.Address{
		HouseNumber: crypt.NewLazyEncryptedString(a.HouseNumber),
		Moo:         crypt.NewLazyEncryptedString(a.Moo),
		Subdistrict: crypt.NewLazyEncryptedString(a.Subdistrict),
		District:    crypt.NewLazyEncryptedString(a.District),
		Province:    crypt.NewLazyEncryptedString(a.Province),
		Zipcode:     crypt.NewLazyEncryptedString(a.Zipcode),
	}, nil
}

type BankPayload struct {
	Account       string `json:"account" binding:"omitempty,integer"`
	BankName      string `json:"bankName" binding:"omitempty,bank"`
	AccountHolder string `json:"accountHolder" binding:"omitempty"`
}

func (b *BankPayload) banking() (*model.BankingInfo, apiErrors.MultipleError) {
	return &model.BankingInfo{
		Account:       crypt.NewLazyEncryptedString(b.Account),
		BankName:      crypt.NewLazyEncryptedString(b.BankName),
		AccountHolder: crypt.NewLazyEncryptedString(b.AccountHolder),
	}, nil
}

type DriverLicensePayload struct {
	ID             string    `json:"id" binding:"omitempty"`
	ExpirationDate time.Time `json:"expirationDate" binding:"omitempty,gt"`
}

func (v *DriverLicensePayload) driverLicense() (*model.DriverLicenseInfo, apiErrors.MultipleError) {
	return &model.DriverLicenseInfo{
		ID:             crypt.NewLazyEncryptedString(v.ID),
		ExpirationDate: &v.ExpirationDate,
	}, nil
}

type VehiclePayload struct {
	RegistrationDate       time.Time `json:"registrationDate" binding:"omitempty,lt"`
	PlateNumber            string    `json:"plateNumber" binding:"omitempty"`
	LegislationExpiredDate time.Time `json:"legislationExpiredDate" binding:"omitempty"`
}

func (v *VehiclePayload) vehicle() (*model.VehicleInfo, apiErrors.MultipleError) {
	return &model.VehicleInfo{
		RegistrationDate:       &v.RegistrationDate,
		PlateNumber:            crypt.NewLazyEncryptedString(v.PlateNumber),
		LegislationExpiredDate: &v.LegislationExpiredDate,
	}, nil
}

func NewUpdateProfileReq(gctx *gin.Context) (*UpdateProfileReq, *api.Error) {
	var req UpdateProfileReq
	if err := gctx.ShouldBind(&req); err != nil {
		if validErrors, ok := err.(validator.ValidationErrors); ok {
			errs := apiErrors.NewMultipleError()
			for _, verr := range validErrors {
				errs.AddError(apiErrors.NewValidatorError(verr, apiValidator.GetTranslator(gctx.GetHeader("Accept-Language"))))
			}

			return nil, errs.NewAPIError()
		}
		return nil, apiErrors.ErrInvalidRequest(err)
	}

	if err := gctx.BindHeader(&req); err != nil {
		return nil, apiErrors.ErrInvalidRequest(err)
	}

	req.DriverID = driver.DriverIDFromGinContext(gctx)
	return &req, nil
}

func (ur *UpdateProfileReq) Update(profile *model.Driver) ([]ProfileFile, error) {
	if err := profile.SetProfileStatus(model.ProfileStatusPending); err != nil {
		return nil, errors.New("Not allow to update")
	}
	errs := apiErrors.NewMultipleError()
	rErrs := ur.updateProfile(profile)
	if rErrs != nil {
		errs.Merge(rErrs)
	}

	files, fErrs := ur.files()
	if fErrs != nil {
		errs.Merge(fErrs)
	}

	if errs.HasError() {
		return nil, errs
	} else {
		return files, nil
	}
}

type ProfileFile struct {
	File        *multipart.FileHeader
	ContentType string
	Name        string
}

func (r *UpdateProfileReq) updateProfile(profile *model.Driver) apiErrors.MultipleError {
	errs := apiErrors.NewMultipleError()

	profile.Title = crypt.NewLazyEncryptedString(r.Profile.Title)
	profile.Firstname = crypt.NewLazyEncryptedString(r.Profile.Firstname)
	profile.Lastname = crypt.NewLazyEncryptedString(strings.TrimSpace(r.Profile.Lastname))
	profile.Phone = crypt.NewLazyEncryptedString(r.Profile.Phone)
	profile.EmergencyPhone = crypt.NewLazyEncryptedString(r.Profile.EmergencyPhone)
	profile.StrongEmergencyPhone = crypt.NewStrongEncryptedString(r.Profile.EmergencyPhone)
	profile.Birthday = &r.Profile.Birthday
	profile.InterestingProvince = r.Profile.InterestingProvince
	profile.AcceptedConsentVersion = r.Profile.AcceptedConsentVersion
	profile.DeviceID = r.UpdateProfileHeader.DeviceID

	if r.Profile.CitizenIDExpiredDate != nil {
		profile.CitizenIDExpiredDate = r.Profile.CitizenIDExpiredDate
	}

	addErrIfExists := func(f func() apiErrors.MultipleError) {
		if e := f(); e != nil {
			errs.Merge(e)
		}
	}

	addErrIfExists(func() apiErrors.MultipleError {
		if r.Profile.CitizenID != profile.CitizenID.String() {
			someErrs := apiErrors.NewMultipleError()
			someErrs.AddError(apiErrors.NewFieldError("citizenId", "updating citizen id is not allowed"))
			return someErrs
		}
		profile.CitizenID = crypt.NewLazyEncryptedString(r.Profile.CitizenID)
		return nil
	})

	addErrIfExists(func() apiErrors.MultipleError {
		address, err := r.Profile.Address.address()
		profile.Address = *address
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		bank, err := r.Profile.Bank.banking()
		bank.PhotoURL = profile.Banking.PhotoURL
		bank.RefID = profile.DriverRefID()
		bank.CitiRefID = profile.DriverCitiRefID()
		bank.UOBRefID = profile.DriverUOBRefID()
		profile.Banking = *bank
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		dl, err := r.Profile.DriverLicense.driverLicense()
		dl.PhotoURL = profile.DriverLicense.PhotoURL
		profile.DriverLicense = *dl
		return err
	})
	addErrIfExists(func() apiErrors.MultipleError {
		v, err := r.Profile.Vehicle.vehicle()
		v.PhotoURL = profile.Vehicle.PhotoURL
		v.RegistrationPhotoURL = profile.Vehicle.RegistrationPhotoURL
		profile.Vehicle = *v
		return err
	})

	if errs.HasError() {
		return errs
	} else {
		return nil
	}
}

func (ur *UpdateProfileReq) files() ([]ProfileFile, apiErrors.MultipleError) {
	multipleError := apiErrors.NewMultipleError()
	profileFiles := []ProfileFile{}

	if ur.DriverImage != nil {
		if ur.DriverImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: ur.DriverImage, Name: DriverImageName})
	}

	if ur.DriverLicenseImage != nil {
		if ur.DriverLicenseImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverLicenseImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: ur.DriverLicenseImage, Name: DriverLicenseImageName})
	}

	if ur.CitizenIDImage != nil {
		if ur.CitizenIDImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("citizenIdImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: ur.CitizenIDImage, Name: CitizenIDImageName})
	}

	if ur.VehicleImage != nil {
		if ur.VehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("vehicleImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: ur.VehicleImage, Name: VehicleImageName})
	}

	if ur.BookBankImage != nil {
		if ur.BookBankImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("bookBankImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: ur.BookBankImage, Name: BookBankImageName})
	}

	if ur.VehicleRegistrationImage != nil {
		if ur.VehicleRegistrationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("vehicleRegistrationImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: ur.VehicleRegistrationImage, Name: VehicleRegistrationImageName})
	}

	if ur.LegislationImage != nil {
		if ur.LegislationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("legislationImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: ur.LegislationImage, Name: LegislationImageName})
	}

	if ur.LendingVehicleImage != nil {
		if ur.LendingVehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("lendingVehicleImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: ur.LendingVehicleImage, Name: LendingVehicleImageName})
	}

	if multipleError.HasError() {
		return nil, multipleError
	}

	return profileFiles, nil
}

type RequestUpdateProfileReq struct {
	LicenseId                string                `form:"licenseId"`
	ExpirationDate           time.Time             `form:"expirationDate"`
	CitizenIdExpiredDate     time.Time             `form:"citizenIdExpiredDate"`
	DriverImage              *multipart.FileHeader `form:"driverImage"`
	DriverLicenseImage       *multipart.FileHeader `form:"driverLicenseImage"`
	VehicleRegistrationImage *multipart.FileHeader `form:"vehicleRegistrationImage"`
	VehicleImage             *multipart.FileHeader `form:"vehicleImage"`
	VehicleRegistrationDate  time.Time             `form:"vehicleRegistrationDate"`
	VehiclePlateNumber       string                `form:"vehiclePlateNumber"`
	LegislationImage         *multipart.FileHeader `form:"legislationImage"`
	LendingVehicleImage      *multipart.FileHeader `form:"lendingVehicleImage"`
	CitizenIdImage           *multipart.FileHeader `form:"citizenIdImage"`
	EmergencyPhone           string                `form:"emergencyPhone"`
	LegislationExpiredDate   time.Time             `form:"legislationExpiredDate"`
}

func NewRequestUpdateProfileReq(gctx *gin.Context) (*RequestUpdateProfileReq, *api.Error) {
	var req RequestUpdateProfileReq
	if err := gctx.ShouldBind(&req); err != nil {
		return nil, apiErrors.ErrInvalidRequest(err)
	}

	return &req, nil
}

func (rp *RequestUpdateProfileReq) Update(profile *model.Driver, requestProfileUpdate *model.RequestUpdateProfile, quota int) ([]ProfileFile, error) {
	if err := profile.SetProfileStatus(model.ProfileStatusUpdatePending); err != nil {
		return nil, errors.New("not allow to update")
	}

	found, lastRequest := profile.GetLatestRequestUpdateProfile()
	if found {
		isQuotaExceed := lastRequest.CreatedAt.Add(time.Hour * time.Duration(24) * time.Duration(quota)).After(time.Now())
		if isQuotaExceed {
			return nil, ErrUpdateExceedQuota
		}
	}

	if profile.Phone.String() != "" && profile.Phone.String() == rp.EmergencyPhone {
		return nil, apiErrors.ErrInvalidEmergencyPhone()
	}

	errs := apiErrors.NewMultipleError()
	rp.updateProfile(requestProfileUpdate)

	files, fErrs := rp.files()
	if fErrs != nil {
		errs.Merge(fErrs)
	}

	if errs.HasError() {
		return nil, errs
	} else {
		return files, nil
	}
}

func (rp *RequestUpdateProfileReq) updateProfile(requestProfileUpdate *model.RequestUpdateProfile) {
	dl := &model.DriverLicenseInfo{}
	if rp.LicenseId != "" {
		dl.ID = crypt.NewLazyEncryptedString(rp.LicenseId)
	}
	if !rp.ExpirationDate.IsZero() {
		dl.ExpirationDate = &rp.ExpirationDate
	}
	requestProfileUpdate.DriverLicense = *dl

	vehicle := &model.VehicleInfo{}
	if rp.VehiclePlateNumber != "" {
		vehicle.PlateNumber = crypt.NewLazyEncryptedString(rp.VehiclePlateNumber)
	}
	if !rp.VehicleRegistrationDate.IsZero() {
		vehicle.RegistrationDate = &rp.VehicleRegistrationDate
	}
	if !rp.LegislationExpiredDate.IsZero() {
		vehicle.LegislationExpiredDate = &rp.LegislationExpiredDate
	}
	if rp.EmergencyPhone != "" {
		requestProfileUpdate.EmergencyPhone = crypt.NewLazyEncryptedString(rp.EmergencyPhone)
	}
	if !rp.CitizenIdExpiredDate.IsZero() {
		requestProfileUpdate.CitizenIdExpiredDate = &rp.CitizenIdExpiredDate
	}

	requestProfileUpdate.Vehicle = *vehicle
	requestProfileUpdate.Status = model.ProfileStatusUpdatePending
	now := time.Now().UTC()
	requestProfileUpdate.CreatedAt = now
	requestProfileUpdate.UpdatedAt = now
}

func (rp *RequestUpdateProfileReq) files() ([]ProfileFile, apiErrors.MultipleError) {
	multipleError := apiErrors.NewMultipleError()
	var profileFiles []ProfileFile

	if rp.DriverImage != nil {
		if rp.DriverImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: rp.DriverImage, Name: DriverImageName})
	}

	if rp.DriverLicenseImage != nil {
		if rp.DriverLicenseImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverLicenseImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: rp.DriverLicenseImage, Name: DriverLicenseImageName})
	}

	if rp.VehicleImage != nil {
		if rp.VehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("vehicleImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: rp.VehicleImage, Name: VehicleImageName})
	}

	if rp.VehicleRegistrationImage != nil {
		if rp.VehicleRegistrationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("vehicleRegistrationImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: rp.VehicleRegistrationImage, Name: VehicleRegistrationImageName})
	}

	if rp.LegislationImage != nil {
		if rp.LegislationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("legislationImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: rp.LegislationImage, Name: LegislationImageName})
	}

	if rp.LendingVehicleImage != nil {
		if rp.LendingVehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("lendingVehicleImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: rp.LendingVehicleImage, Name: LendingVehicleImageName})
	}

	if rp.CitizenIdImage != nil {
		if rp.CitizenIdImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("citizenIdImage", "file over size limit"))
		}
		profileFiles = append(profileFiles, ProfileFile{File: rp.CitizenIdImage, Name: CitizenIDImageName})
	}

	if multipleError.HasError() {
		return nil, multipleError
	}

	return profileFiles, nil
}

type GetOrderHistoryReq struct {
	ServiceType model.Service `form:"serviceType" binding:"omitempty"`
}

func (r GetOrderHistoryReq) ToQuery() GetOrderHistoryQuery {
	return GetOrderHistoryQuery{
		ServiceType: r.ServiceType,
	}
}

type GetOrderHistoryQuery struct {
	DriverID string

	Status []model.Status

	ServiceType model.Service
}

func (q *GetOrderHistoryQuery) WithStatus(status []model.Status) *GetOrderHistoryQuery {
	q.Status = append(q.Status, status...)
	return q
}

func (q *GetOrderHistoryQuery) WithDriverID(driverID string) *GetOrderHistoryQuery {
	q.DriverID = driverID
	return q
}

func (q GetOrderHistoryQuery) Query() bson.M {
	query := bson.M{}

	if q.DriverID != "" {
		query["driver"] = q.DriverID
	}

	if len(q.Status) > 0 {
		query["status"] = bson.M{
			"$in": q.Status,
		}
	}

	if q.ServiceType != "" {
		query["service_type"] = q.ServiceType
	}

	return query
}

type GetTripHistoryReq struct {
	ServiceType model.Service `form:"serviceType" binding:"omitempty"`
}

type UpdateDriverProfileReq struct {
	DriverID string

	// PersonalInfoRequest
	DriverImage      *multipart.FileHeader `form:"driverImage"`
	EmergencyPhone   string                `form:"emergencyPhone"`
	LinemanEquipment bool                  `form:"linemanEquipment"`

	// CitizenInfoRequest
	CitizenIDImage       *multipart.FileHeader `form:"citizenIdImage"`
	CitizenIDExpiredDate time.Time             `form:"citizenIdExpiredDate"`

	// DriverLicenseInfoRequest
	DriverLicenseID             string                `form:"driverLicenseId"`
	DriverLicenseExpirationDate time.Time             `form:"driverLicenseExpirationDate"`
	DriverLicenseImage          *multipart.FileHeader `form:"driverLicenseImage"`

	// VehicleInfoRequest
	VehicleRegistrationImage *multipart.FileHeader `form:"vehicleRegistrationImage"`
	VehicleImage             *multipart.FileHeader `form:"vehicleImage"`
	VehiclePlateNumber       string                `form:"vehiclePlateNumber"`
	LegislationImage         *multipart.FileHeader `form:"legislationImage"`
	LegislationExpiredDate   time.Time             `form:"legislationExpiredDate"`
	LendingVehicleImage      *multipart.FileHeader `form:"lendingVehicleImage"`
	VehicleRegistrationDate  time.Time             `form:"vehicleRegistrationDate"`

	// BankingInfoRequest
	BookBankImage   *multipart.FileHeader `form:"bookBankImage"`
	BankName        string                `form:"bankName"`
	BankAccount     string                `form:"bankAccount"`
	BankAccountName string                `form:"bankAccountName"`
}

func NewUpdateDriverProfileReq(gctx *gin.Context) (*UpdateDriverProfileReq, *api.Error) {
	var req UpdateDriverProfileReq
	if err := gctx.ShouldBind(&req); err != nil {
		return nil, apiErrors.ErrInvalidRequest(err)
	}

	if err := req.ImageSizeValidator(); err != nil {
		return nil, err
	}

	return &req, nil
}

type HideOptInReminderReq struct {
	Period string `json:"period"`
}

func (req HideOptInReminderReq) ValidatePeriod() (model.ServiceOptInReminderPausePeriod, error) {
	period, ok := model.NewServiceOptInReminderPausePeriodFromString(req.Period)
	if !ok {
		return "", fmt.Errorf("period %#v is not supported", req.Period)
	}
	return period, nil
}

func (rp *UpdateDriverProfileReq) ImageSizeValidator() *api.Error {
	multipleError := apiErrors.NewMultipleError()

	// Driver
	if rp.DriverImage != nil {
		if rp.DriverImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverImage", ErrImageOverSize.Error()))
		}
	}

	// Citizen
	if rp.CitizenIDImage != nil {
		if rp.CitizenIDImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("citizenIdImage", ErrImageOverSize.Error()))
		}
	}

	// DriverLicense
	if rp.DriverLicenseImage != nil {
		if rp.DriverLicenseImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("driverLicenseImage", ErrImageOverSize.Error()))
		}
	}

	// Vehicle
	if rp.VehicleImage != nil {
		if rp.VehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("vehicleImage", ErrImageOverSize.Error()))
		}
	}

	if rp.VehicleRegistrationImage != nil {
		if rp.VehicleRegistrationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("vehicleRegistrationImage", ErrImageOverSize.Error()))
		}
	}

	if rp.LegislationImage != nil {
		if rp.LegislationImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("legislationImage", ErrImageOverSize.Error()))
		}
	}

	if rp.LendingVehicleImage != nil {
		if rp.LendingVehicleImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("lendingVehicleImage", ErrImageOverSize.Error()))
		}
	}

	if rp.BookBankImage != nil {
		if rp.BookBankImage.Size > LimitFileSize {
			multipleError.AddError(apiErrors.NewFieldError("bookBankImage", ErrImageOverSize.Error()))
		}
	}

	if multipleError.HasError() {
		return multipleError.APIError()
	}
	return nil
}

func (rp *UpdateDriverProfileReq) Update(profile *model.Driver, updateDriverProfile *model.UpdateDriverProfile) []helpers.ProfileFile {
	rp.updateProfile(updateDriverProfile)
	files := rp.files()
	return files
}

func (rp *UpdateDriverProfileReq) updateProfile(updateDriverProfile *model.UpdateDriverProfile) {
	// Personal info
	if rp.EmergencyPhone != "" {
		updateDriverProfile.PersonalInfoRequest.EmergencyPhone = crypt.NewLazyEncryptedString(rp.EmergencyPhone)
		updateDriverProfile.PersonalInfoRequest.StrongEmergencyPhone = crypt.NewStrongEncryptedString(rp.EmergencyPhone)
	}

	updateDriverProfile.PersonalInfoRequest.LinemanEquipment = rp.LinemanEquipment

	// Citize Info
	if !rp.CitizenIDExpiredDate.IsZero() {
		updateDriverProfile.CitizenInfoRequest.CitizenIDExpiredDate = &rp.CitizenIDExpiredDate
	}

	// Driver info
	if rp.DriverLicenseID != "" {
		updateDriverProfile.DriverLicenseInfoRequest.DriverLicenseID = crypt.NewLazyEncryptedString(rp.DriverLicenseID)
	}
	if !rp.DriverLicenseExpirationDate.IsZero() {
		updateDriverProfile.DriverLicenseInfoRequest.DriverLicenseExpirationDate = &rp.DriverLicenseExpirationDate
	}

	// Vehicle info
	if rp.VehiclePlateNumber != "" {
		updateDriverProfile.VehicleInfoRequest.VehiclePlateNumber = crypt.NewLazyEncryptedString(rp.VehiclePlateNumber)
	}
	if !rp.VehicleRegistrationDate.IsZero() {
		updateDriverProfile.VehicleInfoRequest.VehicleRegistrationDate = &rp.VehicleRegistrationDate
	}
	if !rp.LegislationExpiredDate.IsZero() {
		updateDriverProfile.VehicleInfoRequest.LegislationExpiredDate = &rp.LegislationExpiredDate
	}

	// Bank info
	if rp.BankName != "" {
		updateDriverProfile.BankingInfoRequest.BankName = crypt.NewLazyEncryptedString(rp.BankName)
	}
	if rp.BankAccount != "" {
		updateDriverProfile.BankingInfoRequest.BankAccount = crypt.NewLazyEncryptedString(rp.BankAccount)
	}
	if rp.BankAccountName != "" {
		updateDriverProfile.BankingInfoRequest.BankAccountName = crypt.NewLazyEncryptedString(rp.BankAccountName)
	}
}

func (rp *UpdateDriverProfileReq) files() []helpers.ProfileFile {
	var profileFiles []helpers.ProfileFile

	// Driver
	if rp.DriverImage != nil {
		profileFiles = append(profileFiles, helpers.ProfileFile{File: rp.DriverImage, Name: DriverImageName})
	}

	// Citizen
	if rp.CitizenIDImage != nil {
		profileFiles = append(profileFiles, helpers.ProfileFile{File: rp.CitizenIDImage, Name: CitizenIDImageName})
	}

	// DriverLicense
	if rp.DriverLicenseImage != nil {
		profileFiles = append(profileFiles, helpers.ProfileFile{File: rp.DriverLicenseImage, Name: DriverLicenseImageName})
	}

	// Vehicle
	if rp.VehicleImage != nil {
		profileFiles = append(profileFiles, helpers.ProfileFile{File: rp.VehicleImage, Name: VehicleImageName})
	}

	if rp.VehicleRegistrationImage != nil {
		profileFiles = append(profileFiles, helpers.ProfileFile{File: rp.VehicleRegistrationImage, Name: VehicleRegistrationImageName})
	}

	if rp.LegislationImage != nil {
		profileFiles = append(profileFiles, helpers.ProfileFile{File: rp.LegislationImage, Name: LegislationImageName})
	}

	if rp.LendingVehicleImage != nil {
		profileFiles = append(profileFiles, helpers.ProfileFile{File: rp.LendingVehicleImage, Name: LendingVehicleImageName})
	}

	if rp.BookBankImage != nil {
		profileFiles = append(profileFiles, helpers.ProfileFile{File: rp.BookBankImage, Name: BookBankImageName})
	}

	return profileFiles
}

func parseBool(strVal string) (bool, error) {
	if strVal == "" {
		return false, nil
	}

	val, err := strconv.ParseBool(strVal)
	if err != nil {
		return false, err
	}

	return val, nil
}

type IncomeSummaryReq struct {
	Granularity Granularity `form:"granularity" binding:"required"`
	Date        time.Time   `form:"date"`
}

func (r IncomeSummaryReq) validate(now time.Time, maximumHistoricalViewMonth int) error {
	switch {
	case r.Granularity == DAILY || r.Granularity == WEEKLY || r.Granularity == MONTHLY:
		if r.Date.IsZero() {
			return errors.New("date is required")
		}

		maximum := now.AddDate(0, -maximumHistoricalViewMonth, 0)
		firstDay := timeutil.GetFirstDayOfMonthFromTime(timeutil.DateTruncate(maximum))

		if r.Date.Before(firstDay) {
			return fmt.Errorf("cannot select historical month less than %v months", maximumHistoricalViewMonth)
		}

	case r.Granularity == LAST8DAYAGO:

	default:
		return errors.New("invalid granularity")
	}

	return nil
}

type GetFormServiceV2Req struct {
	FormType    model.FormType         `json:"formType"`
	FormSubtype model.FormSubtype      `json:"formSubtype"`
	Value       map[string]interface{} `json:"value"`
}

type GetIncentivesQuery struct {
	// client use UTC timezone
	Date time.Time `form:"date" binding:"required"`
}

type GetProfileQuery struct {
	Lat           float64 `form:"lat" json:"lat"`
	Lng           float64 `form:"lng" json:"lng"`
	RequestUpdate bool    `form:"requestUpdate" json:"requestUpdate"`
}

type LoginResponse struct {
	DriverID            string `json:"driverId"`
	Token               string `json:"token"`
	ExpiresInSec        int64  `json:"expiresInSec"`
	RefreshToken        string `json:"refreshToken"`
	RefreshExpiresInSec int64  `json:"refreshExpiresInSec"`
}

type RefreshResponse struct {
	Token        string `json:"token"`
	ExpiresInSec int64  `json:"expiresInSec"`
	RefreshToken string `json:"refreshToken"`
}
