package account

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/icrowley/fake"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/structpb"
	"google.golang.org/protobuf/types/known/timestamppb"

	formServicePb "git.wndv.co/go/proto/lineman/form_service/v1"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"git.wndv.co/lineman/xgo/api"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestAccountAPI_GetFormService(t *testing.T) {
	currentTime := time.Date(2022, 12, 11, 10, 0, 0, 0, timeutil.BangkokLocation())
	timeutils.FreezeWithTime(currentTime.UnixMilli())
	t.Cleanup(func() {
		timeutils.Unfreeze()
	})

	makeReq := func(id string, formType string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("GET", "/v1/account/form-service/:template", nil)
		gctx.Params = gin.Params{
			gin.Param{Key: "template", Value: formType},
		}
		driver.SetDriverIDToContext(gctx, id)
		return gctx, recorder
	}
	motorcycleInsurancePolicyVOSID := "motor_vos"
	personalAccidentInsurancePolicyVOSID := "personal_vos"

	type TestData struct {
		name                  string
		latestDriverInsurance []model.DriverInsurance
		formDriverInsurance   []*formServicePb.Form
		expectedPrefilFunc    func() map[string]interface{}
		expectedHttpCode      int
		formID                string
	}

	newGRPCStruct := func(src map[string]interface{}) *structpb.Struct {
		res, _ := structpb.NewStruct(src)
		return res
	}

	mockedBirthDay := time.Now()
	getDefaultPrefillMap := func() map[string]interface{} {
		return map[string]interface{}{
			"driverId":    "DRIVER_ID",
			"tier":        "MEMBER",
			"firstName":   "AA",
			"lastName":    "BB",
			"citizenId":   "CC",
			"phoneNumber": "DD",
			"title":       "EE",
			"birthday":    mockedBirthDay.Format(time.DateOnly),
			"address": map[string]interface{}{
				"houseNumber": "FF",
				"moo":         "GG",
				"subdistrict": "HH",
				"district":    "II",
				"province":    "JJ",
				"zipcode":     "KK",
			},
			"type": "Motor",
		}
	}

	testSet := []TestData{
		{
			name:                  "prefill with existing APPROVED data",
			latestDriverInsurance: []model.DriverInsurance{},
			formDriverInsurance: []*formServicePb.Form{
				{
					RefId:     "DRIVER_ID",
					FormType:  formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION,
					CreatedAt: timestamppb.New(currentTime.AddDate(0, 1, 0)),
					IssuedAt:  timestamppb.New(currentTime.AddDate(0, 1, 0)),
					Status:    formServicePb.FormStatus_FORM_STATUS_APPROVED,
					Value: newGRPCStruct(map[string]interface{}{
						"type":      "Motor",
						"firstName": "A",
						"lastName":  "Z",
						"tier":      "PRO",
						"newVal1":   "1",
						"newVal2":   "a",
						"newVal3":   "A",
					}),
				},
			},
			expectedHttpCode: http.StatusOK,
			expectedPrefilFunc: func() map[string]interface{} {
				defaultPrefill := getDefaultPrefillMap()
				defaultPrefill["newVal1"] = "1"
				defaultPrefill["newVal2"] = "a"
				defaultPrefill["newVal3"] = "A"
				return defaultPrefill
			},
			formID: "888",
		},
		{
			name:                  "prefill with existing APPROVED data and some missing field",
			latestDriverInsurance: []model.DriverInsurance{},
			formDriverInsurance: []*formServicePb.Form{
				{
					RefId:     "DRIVER_ID",
					FormType:  formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION,
					CreatedAt: timestamppb.New(currentTime.AddDate(0, 1, 0)),
					IssuedAt:  timestamppb.New(currentTime.AddDate(0, 1, 0)),
					Status:    formServicePb.FormStatus_FORM_STATUS_APPROVED,
					Value: newGRPCStruct(map[string]interface{}{
						"type":        "Motor",
						"firstName":   "",
						"lastName":    nil,
						"title":       "",
						"birthday":    nil,
						"phoneNumber": "A",
						"newVal1":     "1",
						"newVal2":     nil,
						"newVal3":     "A",
					}),
				},
			},
			expectedHttpCode: http.StatusOK,
			expectedPrefilFunc: func() map[string]interface{} {
				defaultPrefill := getDefaultPrefillMap()
				defaultPrefill["newVal1"] = "1"
				defaultPrefill["newVal3"] = "A"
				return defaultPrefill
			},
			formID: "888",
		},
	}

	for _, item := range testSet {
		testData := item
		t.Run(testData.name, func(tt *testing.T) {
			tt.Parallel()

			driverID := "DRIVER_ID"
			ctx, recorder := makeReq(driverID, string(model.FormTypeInsuranceRegistration))
			accapi, deps, finish := newTestAccountAPI(
				tt,
				config.DriverPeriodCompletedTripsConfig{},
				config.PaymentConfig{},
				&order.AtomicContingencyConfig{},
				&AtomicDBConfig{},
			)

			d := model.Driver{
				DriverID: driverID,
				BaseDriver: model.BaseDriver{
					DriverTier: "MEMBER",
					Firstname:  crypt.NewLazyEncryptedString("AA"),
					Lastname:   crypt.NewLazyEncryptedString("BB"),
					CitizenID:  crypt.NewLazyEncryptedString("CC"),
					Phone:      crypt.NewLazyEncryptedString("DD"),
					Title:      crypt.NewLazyEncryptedString("EE"),
					Birthday:   &mockedBirthDay,
					Address: model.Address{
						HouseNumber: crypt.NewLazyEncryptedString("FF"),
						Moo:         crypt.NewLazyEncryptedString("GG"),
						Subdistrict: crypt.NewLazyEncryptedString("HH"),
						District:    crypt.NewLazyEncryptedString("II"),
						Province:    crypt.NewLazyEncryptedString("JJ"),
						Zipcode:     crypt.NewLazyEncryptedString("KK"),
					},
					Vehicle: model.VehicleInfo{
						RegistrationDate: nil,
					},
				},
			}

			defer finish()
			accapi.insuranceConfig = DriverInsuranceConfig{
				MotorcycleInsurancePolicyVosURL:       motorcycleInsurancePolicyVOSID,
				PersonalAccidentInsurancePolicyVosURL: personalAccidentInsurancePolicyVOSID,
				InsuranceRecordMonthDuration:          1,
			}

			deps.driverRepository.EXPECT().
				FindDriverID(ctx, gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
				Return(&d, nil)

			deps.formService.EXPECT().ListForm(gomock.Any(), gomock.Any()).
				Return(&formServicePb.ListFormResponse{
					Data: testData.formDriverInsurance,
				}, nil)

			expectedPrefil := testData.expectedPrefilFunc()
			expectedStruct, _ := structpb.NewStruct(expectedPrefil)
			deps.formService.EXPECT().CreateForm(gomock.Any(), &formServicePb.CreateFormRequest{
				FormType: formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION,
				Prefill:  expectedStruct,
			}).
				Return(&formServicePb.CreateFormResponse{
					Id: testData.formID,
				}, nil)

			accapi.GetFormService(ctx)
			var actual formServicePb.CreateFormResponse
			testutil.DecodeJSON(tt, recorder.Body, &actual)
			require.Equal(tt, testData.expectedHttpCode, recorder.Code)
		})
	}
}

func TestAccountAPI_GetFormServiceV2(t *testing.T) {
	timeutils.Freeze()
	t.Cleanup(func() {
		timeutils.Unfreeze()
	})

	makeReq := func(driverID string, req GetFormServiceV2Req) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("POST", "/v1/account/form-service/v2", testutil.JSON(req))
		driver.SetDriverIDToContext(gctx, driverID)
		return gctx, recorder
	}

	expectedDriver := model.Driver{
		DriverID: "DRIVER_ID",
		BaseDriver: model.BaseDriver{
			DriverTier: model.DriverTierBasic,
			Title:      crypt.NewLazyEncryptedString("TITLE"),
			Firstname:  crypt.NewLazyEncryptedString("FIRSTNAME"),
			Lastname:   crypt.NewLazyEncryptedString("LASTNAME"),
			Phone:      crypt.NewLazyEncryptedString("PHONE"),
			Address: model.Address{
				HouseNumber: crypt.NewLazyEncryptedString("HOUSE_NUMBER"),
				Moo:         crypt.NewLazyEncryptedString("MOO"),
				Subdistrict: crypt.NewLazyEncryptedString("SUBDISTRICT"),
				District:    crypt.NewLazyEncryptedString("DISTRICT"),
				Province:    crypt.NewLazyEncryptedString("PROVINCE"),
				Zipcode:     crypt.NewLazyEncryptedString("ZIPCODE"),
			},
		},
	}

	t.Run("purchase egs form", func(t *testing.T) {
		t.Parallel()
		expectedFormType := model.FormTypePurchaseEGS
		mockExpectedReq := func() GetFormServiceV2Req {
			return GetFormServiceV2Req{
				FormType: expectedFormType,
				Value: map[string]interface{}{
					"batchId":          "BATCH_ID",
					"templateId":       "TEMPLATE_ID",
					"productSku":       "PRODUCT_SKU",
					"productName":      "PRODUCT_NAME",
					"productImageUrls": []interface{}{"IMG_1", "IMG_2"},
					"batchGroupType":   "BATCH_GROUP_TYPE",
					"deliverBy":        "DELIVER_BY",
					"paymentTenor": map[string]interface{}{
						"type": "ONETIME",
						"dailyAmount": map[string]interface{}{
							"value": "100",
						},
						"netPrice": map[string]interface{}{
							"value": "1000",
						},
						"rewardAmount": "9",
						"rewardLabel": map[string]interface{}{
							"value": "1",
						},
						"rewardSku": "REWARD_SKU",
						"tenor":     10,
					},
				},
			}
		}

		t.Run("return 200 when success", func(tt *testing.T) {
			tt.Parallel()

			accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
			defer finish()

			gctx, recorder := makeReq(expectedDriver.DriverID, mockExpectedReq())
			deps.driverRepository.EXPECT().FindDriverID(gctx, expectedDriver.DriverID, gomock.Any()).Return(&expectedDriver, nil)
			deps.formService.EXPECT().CreateForm(gctx, gomock.Any()).DoAndReturn(
				func(ctx context.Context, in *formServicePb.CreateFormRequest, opts ...grpc.CallOption) (*formServicePb.CreateFormResponse, error) {
					require.Equal(tt, formServicePb.FormSubtype_FORM_SUBTYPE_NONE, in.FormSubtype)
					require.Equal(tt, formServicePb.FormType_FORM_TYPE_PURCHASE_EGS, in.FormType)
					prefill := in.Prefill.AsMap()

					// Require address fields
					address := prefill["address"].(map[string]interface{})
					require.Equal(tt, "DISTRICT", address["district"])
					require.Equal(tt, "HOUSE_NUMBER หมู่ MOO", address["houseNumberAndMoo"])
					require.Equal(tt, "PROVINCE", address["province"])
					require.Equal(tt, "SUBDISTRICT", address["subdistrict"])
					require.Equal(tt, "ZIPCODE", address["zipcode"])

					// Require batchGroupType, batchId, etc.
					require.Equal(tt, "BATCH_GROUP_TYPE", prefill["batchGroupType"])
					require.Equal(tt, "BATCH_ID", prefill["batchId"])
					require.Equal(tt, "DELIVER_BY", prefill["deliverBy"])
					require.Equal(tt, "DRIVER_ID", prefill["driverId"])
					require.Equal(tt, "FIRSTNAME", prefill["firstName"])
					require.Equal(tt, "LASTNAME", prefill["lastName"])

					// Require paymentTenor fields
					paymentTenor := prefill["paymentTenor"].(map[string]interface{})
					dailyAmount := paymentTenor["dailyAmount"].(map[string]interface{})
					require.Equal(tt, "100", dailyAmount["value"])

					netPrice := paymentTenor["netPrice"].(map[string]interface{})
					require.Equal(tt, "1000", netPrice["value"])

					require.Equal(tt, "9", paymentTenor["rewardAmount"])

					rewardLabel := paymentTenor["rewardLabel"].(map[string]interface{})
					require.Equal(tt, "1", rewardLabel["value"])

					require.Equal(tt, "REWARD_SKU", paymentTenor["rewardSku"])
					require.Equal(tt, 10.0, paymentTenor["tenor"])
					require.Equal(tt, "ONETIME", paymentTenor["type"])

					// Require other fields
					require.Equal(tt, "PHONE", prefill["phoneNumber"])
					require.Nil(tt, prefill["productBundleSkus"])
					require.ElementsMatch(tt, []string{"IMG_1", "IMG_2"}, prefill["productImageUrls"])
					require.Equal(tt, "PRODUCT_NAME", prefill["productName"])
					require.Equal(tt, "PRODUCT_SKU", prefill["productSku"])
					require.Equal(tt, "TEMPLATE_ID", prefill["templateId"])
					require.Equal(tt, "BASIC", prefill["tier"])
					require.Equal(tt, "TITLE", prefill["title"])

					return &formServicePb.CreateFormResponse{}, nil
				})

			accapi.GetFormServiceV2(gctx)

			require.Equal(tt, http.StatusOK, recorder.Code)
		})

		t.Run("return 500 when product name is empty", func(tt *testing.T) {
			tt.Parallel()

			accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
			defer finish()
			expectedReq := mockExpectedReq()
			delete(expectedReq.Value, "productName")

			gctx, recorder := makeReq(expectedDriver.DriverID, expectedReq)
			deps.driverRepository.EXPECT().FindDriverID(gctx, expectedDriver.DriverID, gomock.Any()).Return(&expectedDriver, nil)

			accapi.GetFormServiceV2(gctx)

			require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		})

		t.Run("return 500 when driver not found", func(tt *testing.T) {
			tt.Parallel()

			accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
			defer finish()
			expectedReq := mockExpectedReq()

			gctx, recorder := makeReq(expectedDriver.DriverID, expectedReq)
			deps.driverRepository.EXPECT().FindDriverID(gctx, expectedDriver.DriverID, gomock.Any()).Return(nil, errors.New("driver not found"))

			accapi.GetFormServiceV2(gctx)

			require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		})

		t.Run("return 500 when create form error", func(tt *testing.T) {
			tt.Parallel()

			accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
			defer finish()
			expectedReq := mockExpectedReq()

			gctx, recorder := makeReq(expectedDriver.DriverID, expectedReq)
			deps.driverRepository.EXPECT().FindDriverID(gctx, expectedDriver.DriverID, gomock.Any()).Return(&expectedDriver, nil)
			deps.formService.EXPECT().CreateForm(gctx, gomock.Any()).Return(nil, errors.New("create form error"))

			accapi.GetFormServiceV2(gctx)

			require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		})

		t.Run("return 500 when invalid form type", func(tt *testing.T) {
			tt.Parallel()

			accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
			defer finish()
			expectedReq := mockExpectedReq()
			expectedFormType := model.FormType("error")
			expectedReq.FormType = expectedFormType

			gctx, recorder := makeReq(expectedDriver.DriverID, expectedReq)

			deps.driverRepository.EXPECT().FindDriverID(gctx, expectedDriver.DriverID, gomock.Any()).Return(&expectedDriver, nil)

			accapi.GetFormServiceV2(gctx)

			require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		})
	})

	t.Run("purchase positive credit form", func(t *testing.T) {
		t.Parallel()
		expectedFormType := model.FormTypePurchaseEGS
		expectedFormSubType := model.FormSubtypePositiveCredit
		mockExpectedValue := func() map[string]interface{} {
			return map[string]interface{}{
				"batchId":          "BATCH_ID",
				"templateId":       "TEMPLATE_ID",
				"productSku":       "PRODUCT_SKU",
				"productName":      "PRODUCT_NAME",
				"productImageUrls": []interface{}{"IMG_1", "IMG_2"},
				"batchGroupType":   "BATCH_GROUP_TYPE_POSITIVE_CREDIT",
				"deliverBy":        "DELIVER_BY",
				"paymentTenor": map[string]interface{}{
					"type": "TENOR",
					"dailyAmount": map[string]interface{}{
						"value": "100",
					},
					"netPrice": map[string]interface{}{
						"value": "1000",
					},
					"rewardAmount": "9",
					"rewardLabel": map[string]interface{}{
						"value": "1",
					},
					"rewardSku": "REWARD_SKU",
					"tenor":     10,
				},
			}
		}

		t.Run("return 200 when success", func(tt *testing.T) {
			tt.Parallel()

			accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
			defer finish()
			value := mockExpectedValue()
			expectedReq := GetFormServiceV2Req{
				FormType:    expectedFormType,
				FormSubtype: expectedFormSubType,
				Value:       value,
			}

			gctx, recorder := makeReq(expectedDriver.DriverID, expectedReq)
			deps.driverRepository.EXPECT().FindDriverID(gctx, expectedDriver.DriverID, gomock.Any()).Return(&expectedDriver, nil)
			deps.formService.EXPECT().CreateForm(gctx, gomock.Any()).DoAndReturn(
				func(ctx context.Context, in *formServicePb.CreateFormRequest, opts ...grpc.CallOption) (*formServicePb.CreateFormResponse, error) {
					require.Equal(tt, formServicePb.FormType_FORM_TYPE_PURCHASE_EGS, in.FormType)
					require.Equal(tt, formServicePb.FormSubtype_FORM_SUBTYPE_POSITIVE_CREDIT, in.FormSubtype)
					prefill := in.Prefill.AsMap()

					// No address fields
					require.Equal(tt, nil, prefill["address"])

					// Require batchGroupType, batchId, etc.
					require.Equal(tt, "BATCH_GROUP_TYPE_POSITIVE_CREDIT", prefill["batchGroupType"])
					require.Equal(tt, "BATCH_ID", prefill["batchId"])
					require.Equal(tt, nil, prefill["deliverBy"])
					require.Equal(tt, "DRIVER_ID", prefill["driverId"])
					require.Equal(tt, "FIRSTNAME", prefill["firstName"])
					require.Equal(tt, "LASTNAME", prefill["lastName"])

					// Require paymentTenor fields
					paymentTenor := prefill["paymentTenor"].(map[string]interface{})
					dailyAmount := paymentTenor["dailyAmount"].(map[string]interface{})
					require.Equal(tt, "100", dailyAmount["value"])

					netPrice := paymentTenor["netPrice"].(map[string]interface{})
					require.Equal(tt, "1000", netPrice["value"])

					require.Equal(tt, "9", paymentTenor["rewardAmount"])

					rewardLabel := paymentTenor["rewardLabel"].(map[string]interface{})
					require.Equal(tt, "1", rewardLabel["value"])

					require.Equal(tt, "REWARD_SKU", paymentTenor["rewardSku"])
					require.Equal(tt, 10.0, paymentTenor["tenor"])
					require.Equal(tt, "TENOR", paymentTenor["type"])

					// Require other fields
					require.Equal(tt, "PHONE", prefill["phoneNumber"])
					require.Nil(tt, prefill["productBundleSkus"])
					require.ElementsMatch(tt, []string{"IMG_1", "IMG_2"}, prefill["productImageUrls"])
					require.Equal(tt, "PRODUCT_NAME", prefill["productName"])
					require.Equal(tt, "PRODUCT_SKU", prefill["productSku"])
					require.Equal(tt, "TEMPLATE_ID", prefill["templateId"])
					require.Equal(tt, "BASIC", prefill["tier"])
					require.Equal(tt, nil, prefill["title"])

					return &formServicePb.CreateFormResponse{}, nil
				})

			accapi.GetFormServiceV2(gctx)

			require.Equal(tt, http.StatusOK, recorder.Code)
		})

		t.Run("return 500 when batch group type is not positive credit", func(tt *testing.T) {
			tt.Parallel()

			accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
			defer finish()

			value := mockExpectedValue()
			value["batchGroupType"] = "INVALID"
			expectedReq := GetFormServiceV2Req{
				FormType:    expectedFormType,
				FormSubtype: expectedFormSubType,
				Value:       value,
			}

			gctx, recorder := makeReq(expectedDriver.DriverID, expectedReq)
			deps.driverRepository.EXPECT().FindDriverID(gctx, expectedDriver.DriverID, gomock.Any()).Return(&expectedDriver, nil)

			accapi.GetFormServiceV2(gctx)

			require.Equal(tt, http.StatusInternalServerError, recorder.Code)
			var apiErr api.Error
			testutil.DecodeJSON(tt, recorder.Body, &apiErr)
			require.Equal(tt, "batch group type is not positive credit", apiErr.Message)
		})

		t.Run("return 500 when paymentTenorType is ONE_TIME", func(tt *testing.T) {
			tt.Parallel()

			accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
			defer finish()

			value := mockExpectedValue()
			value["paymentTenor"].(map[string]interface{})["type"] = "ONE_TIME"
			expectedReq := GetFormServiceV2Req{
				FormType:    expectedFormType,
				FormSubtype: expectedFormSubType,
				Value:       value,
			}

			gctx, recorder := makeReq(expectedDriver.DriverID, expectedReq)
			deps.driverRepository.EXPECT().FindDriverID(gctx, expectedDriver.DriverID, gomock.Any()).Return(&expectedDriver, nil)

			accapi.GetFormServiceV2(gctx)

			require.Equal(tt, http.StatusInternalServerError, recorder.Code)
			var apiErr api.Error
			testutil.DecodeJSON(tt, recorder.Body, &apiErr)
			require.Equal(tt, "payment tenor type cannot be ONE_TIME", apiErr.Message)
		})
	})
}

func TestAccountAPI_GetFormServiceV2_PickupClaimForm(t *testing.T) {
	timeutils.Freeze()
	t.Cleanup(func() {
		timeutils.Unfreeze()
	})

	makeReq := func(formType string, driverID string, req GetFormServiceV2Req) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("POST", "/v1/account/form-service/v2", testutil.JSON(req))
		driver.SetDriverIDToContext(gctx, driverID)
		return gctx, recorder
	}

	generateMockDriverFunc := func(driverID string, modifiers ...func(*model.Driver)) model.Driver {
		basedDriver := model.Driver{
			DriverID: driverID,
			BaseDriver: model.BaseDriver{
				DriverTier: model.DriverTierBasic,
				Title:      crypt.NewLazyEncryptedString(fake.CharactersN(10)),
				Firstname:  crypt.NewLazyEncryptedString(fake.CharactersN(10)),
				Lastname:   crypt.NewLazyEncryptedString(fake.CharactersN(10)),
				Phone:      crypt.NewLazyEncryptedString(fake.CharactersN(10)),
				Address: model.Address{
					HouseNumber: crypt.NewLazyEncryptedString(fake.CharactersN(10)),
					Moo:         crypt.NewLazyEncryptedString(fake.CharactersN(10)),
					Subdistrict: crypt.NewLazyEncryptedString(fake.CharactersN(10)),
					District:    crypt.NewLazyEncryptedString(fake.CharactersN(10)),
					Province:    crypt.NewLazyEncryptedString(fake.CharactersN(10)),
					Zipcode:     crypt.NewLazyEncryptedString(fake.CharactersN(10)),
				},
				DriverType: crypt.NewLazyEncryptedString("FREELANCER"),
			},
			Region:         "BKK",
			DriverVendorID: "",
		}

		for _, modifier := range modifiers {
			modifier(&basedDriver)
		}

		return basedDriver
	}

	generateReqFunc := func(formType model.FormType, formSubType model.FormSubtype, orderID string, modifiers ...func(map[string]any)) GetFormServiceV2Req {
		baseReq := GetFormServiceV2Req{
			FormType:    formType,
			FormSubtype: formSubType,
			Value: map[string]interface{}{
				model.FormPrefillOrderId: orderID,
			},
		}

		for _, modifier := range modifiers {
			modifier(baseReq.Value)
		}

		return baseReq
	}

	generatedMockOrderFunc := func() func(
		driverID string,
		tripID string,
		serviceType model.Service,
		status model.Status,
		lastStatus model.Status,
		modifiers ...func(*model.Order)) model.Order {
		calledCounter := 0
		distanceList := []float64{
			1122.33, 2233.44, 3344.55, 4455.66, 5566.77, 6677.88, 7788.99,
		}
		locationList := []float64{
			10.123, 11.234, 12.345, 13.456, 14.567, 15.678, 16.789, 17.89, 18.901, 19.012,
		}
		historyLocationList := []float64{
			11.111, 22.222, 33.333, 44.444, 55.555, 66.666, 77.777, 88.888, 99.999,
		}

		return func(
			driverID string,
			tripID string,
			serviceType model.Service,
			status model.Status,
			lastStatus model.Status,
			modifiers ...func(*model.Order),
		) model.Order {
			calledCounter++

			basedOrder := model.Order{
				Driver:  driverID,
				TripID:  tripID,
				OrderID: fmt.Sprintf("MOCKED_ORDER_ID_%v", calledCounter),
				Status:  status,
				Quote: model.Quote{
					CreatedAt:   timeutil.BangkokNow(),
					ServiceType: serviceType,
					Routes: []model.Stop{
						{
							Distance: types.Distance(distanceList[calledCounter%len(distanceList)]),
							Location: model.Location{
								Lat: locationList[calledCounter%len(locationList)],
								Lng: locationList[(calledCounter+1)%len(locationList)],
							},
						},
						{
							Distance: types.Distance(distanceList[(calledCounter+1)%len(distanceList)]),
							Location: model.Location{
								Lat: locationList[(calledCounter+2)%len(locationList)],
								Lng: locationList[(calledCounter+3)%len(locationList)],
							},
						},
					},
				},
				Region: "BKK",
			}

			var orderFlow []model.Status
			switch serviceType {
			case model.ServiceFood:
				fallthrough
			case model.ServiceMart:
				orderFlow = []model.Status{
					model.StatusDriverMatched,
					model.StatusDriverToRestaurant,
					model.StatusDriverArrivedRestaurant,
					model.StatusDriverToDestination,
					model.StatusDriverArrived,
					model.StatusDropOffDone,
				}
			}

			history := map[string]time.Time{}
			historyLocation := map[string]model.LocationWithUpdatedAt{}
			for index, item := range orderFlow {
				setTime := timeutil.BangkokNow().Add(time.Minute * time.Duration(index))
				history[string(item)] = setTime
				historyLocation[string(item)] = model.LocationWithUpdatedAt{
					Lat:       historyLocationList[index%len(historyLocationList)],
					Lng:       historyLocationList[(index+1)%len(historyLocationList)],
					UpdatedAt: setTime,
				}
				if item == lastStatus {
					break
				}
			}
			setTime := timeutil.BangkokNow().Add(time.Minute * time.Duration(len(historyLocation)+1))
			history[string(status)] = setTime
			historyLocation[string(status)] = model.LocationWithUpdatedAt{
				Lat:       historyLocationList[len(historyLocation)%len(historyLocationList)],
				Lng:       historyLocationList[(len(historyLocation)+1)%len(historyLocationList)],
				UpdatedAt: setTime,
			}

			basedOrder.History = history
			basedOrder.HistoryLocation = historyLocation

			for _, modifier := range modifiers {
				modifier(&basedOrder)
			}

			return basedOrder
		}
	}()

	generatePrefillValueFromOrderFunc := func(srcDriver model.Driver, srcOrder model.Order, modifiers ...func(map[string]any)) map[string]any {
		basedValue := map[string]any{
			model.FormPrefillDriverId:      srcOrder.Driver,
			model.FormPrefillFirstName:     srcDriver.Firstname.String(),
			model.FormPrefillLastName:      srcDriver.Lastname.String(),
			model.FormPrefillOrderId:       srcOrder.OrderID,
			model.FormPrefillOrderStatus:   string(srcOrder.Status),
			model.FormPrefillWorkingRegion: string(srcOrder.Region),
			model.FormPrefillTripId:        srcOrder.TripID,
			model.FormPrefillServiceType:   string(srcOrder.ServiceType),
		}

		for _, modifier := range modifiers {
			modifier(basedValue)
		}

		return basedValue
	}

	t.Run("[P1D1] claim order 1 [M1P1]", func(tt *testing.T) {
		tt.Parallel()
		// M1 = DRIVER_MATCHED of a 1st order
		// P1 = 1st order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(12345.76)
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order1st,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "12.35" // rounding
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		basedTrip, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order1st.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order1st.OrderID, gomock.Any()).
			Return(&order1st, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(basedTrip, nil).Times(1)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2D1D2] order 1 cancelled before P1 claim order 2 [C1P2]", func(tt *testing.T) {
		tt.Parallel()
		// C1 = CANCELLED location of a 1st order
		// P2 = 2nd order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverMatched,
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(3344.55)
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order2nd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "3.34"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order2nd.Routes[0].Location.Lat,
					"lng": order2nd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusCanceled)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusCanceled)].Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusCanceled)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusCanceled)].Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.HistoryLocation[string(model.StatusCanceled)].Lat,
					order1st.HistoryLocation[string(model.StatusCanceled)].Lng,
					order2nd.Routes[0].Location.Lat,
					order2nd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		basedTrip, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, basedTrip.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, basedTrip.Cancel(order1st.OrderID))

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order2nd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order2nd.OrderID, gomock.Any()).
			Return(&order2nd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(basedTrip, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1D1][P2D2] order 1 cancelled after P1 claim order 2 [M2P2]", func(tt *testing.T) {
		tt.Parallel()
		// M2 = 2nd order's DRIVER_MATCHED location
		// P2 = 2nd order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mocked2ndTripID := "MOCKED_TRIP_ID_B"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToRestaurant,
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(121212)
				o.IsB2B = true
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order2nd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "121.21"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order2nd.Routes[0].Location.Lat,
					"lng": order2nd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order2nd.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order2nd.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order2nd.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order2nd.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order2nd.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					order2nd.HistoryLocation[string(model.StatusDriverMatched)].Lng,
					order2nd.Routes[0].Location.Lat,
					order2nd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, trip1st.Cancel(order1st.OrderID))

		trip2nd, err := model.DoNewTrip(mocked2ndTripID, model.TripStatusInit, order2nd)
		assert.NoError(tt, err)

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order2nd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order2nd.OrderID, gomock.Any()).
			Return(&order2nd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked2ndTripID, gomock.Any()).
			Return(trip2nd, nil).Times(1)

		deps.tripServices.EXPECT().GetOrdersByTrip(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
				assert.Equal(tt, trip2nd.TripID, trip.TripID)

				return []model.Order{
					order2nd,
				}, nil
			}).Times(1)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2P3D1D2D3] order 2 and 3 cancelled [M1P1]", func(tt *testing.T) {
		tt.Parallel()
		// M1 = 1nd order's DRIVER_MATCHED location
		// P1 = 1nd order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(9999)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToDestination,
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverMatched,
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order1st,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "10.00" // rounding
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)

		assert.NoError(tt, trip1st.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, trip1st.Cancel(order2nd.OrderID))
		assert.NoError(tt, trip1st.AddNewOrder(&order3rd, 1, 2))
		assert.NoError(tt, trip1st.Cancel(order3rd.OrderID))

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order1st.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order1st.OrderID, gomock.Any()).
			Return(&order1st, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2P3D1D2D3] order 1 cancelled after P1 claim order 2 [P1P2]", func(tt *testing.T) {
		tt.Parallel()
		// P1 = 1nd order's pickup point (orders.routes[0])
		// P2 = 2nd order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverArrivedRestaurant,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(9999)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(8888)
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(7777)
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order2nd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "8.89" // rounding
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order2nd.Routes[0].Location.Lat,
					"lng": order2nd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
					order2nd.Routes[0].Location.Lat,
					order2nd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)

		assert.NoError(tt, trip1st.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, trip1st.AddNewOrder(&order3rd, 2, 2))
		assert.NoError(tt, trip1st.Cancel(order1st.OrderID))

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order2nd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order2nd.OrderID, gomock.Any()).
			Return(&order2nd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2D1D2] order 1 claim order 1 [M1P1]", func(tt *testing.T) {
		tt.Parallel()
		// M1 = DRIVER_MATCHED location of a 1st order
		// P1 = 1st order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(1111.55)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(3344.55)
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order1st,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "1.11"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		basedTrip, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, basedTrip.AddNewOrder(&order2nd, 1, 1))

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order1st.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order1st.OrderID, gomock.Any()).
			Return(&order1st, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(basedTrip, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil).Times(0)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2D1D2] order 1 cancelled after P1 claim order 2 [P1P2]", func(tt *testing.T) {
		tt.Parallel()
		// P1 = 1st order's pickup point (orders.routes[0])
		// P2 = 2nd order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToDestination,
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(3344.55)
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order2nd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "3.34"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order2nd.Routes[0].Location.Lat,
					"lng": order2nd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
					order2nd.Routes[0].Location.Lat,
					order2nd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		basedTrip, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, basedTrip.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, basedTrip.Cancel(order1st.OrderID))

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order2nd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order2nd.OrderID, gomock.Any()).
			Return(&order2nd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(basedTrip, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2P3D1D2D3] order 1 canclled after P1, order 2 also cancelled [P1P3]", func(tt *testing.T) {
		tt.Parallel()
		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToDestination,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(9999)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverMatched,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(2222)
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(3333)
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order3rd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "3.33" // rounding
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order3rd.Routes[0].Location.Lat,
					"lng": order3rd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
					order3rd.Routes[0].Location.Lat,
					order3rd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)

		assert.NoError(tt, trip1st.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, trip1st.AddNewOrder(&order3rd, 2, 2))
		assert.NoError(tt, trip1st.Cancel(order1st.OrderID))
		assert.NoError(tt, trip1st.Cancel(order2nd.OrderID))

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order3rd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order3rd.OrderID, gomock.Any()).
			Return(&order3rd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil).Times(1)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1D1][P2P3D2D3] order 2 cancelled after P2 claim order 1 [M1P1]", func(tt *testing.T) {
		tt.Parallel()
		// M1 = 1st order's DRIVER_MATCHED location
		// P1 = 1st order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mocked2ndTripID := "MOCKED_TRIP_ID_B"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(100)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToDestination,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(121212)
				o.IsB2B = true
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(23123)
				o.IsB2B = true
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order1st,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "0.10"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)

		trip2nd, err := model.DoNewTrip(mocked2ndTripID, model.TripStatusInit, order2nd)
		assert.NoError(tt, err)
		assert.NoError(tt, trip2nd.AddNewOrder(&order3rd, 1, 1))
		assert.NoError(tt, trip2nd.Cancel(order2nd.OrderID))

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order1st.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order1st.OrderID, gomock.Any()).
			Return(&order1st, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.tripServices.EXPECT().GetOrdersByTrip(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
				assert.Equal(tt, trip2nd.TripID, trip.TripID)

				return []model.Order{
					order2nd,
				}, nil
			}).Times(0)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(0)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil).Times(0)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1D1][P2P3D2D3] order 2 cancelled after P2 claim order 3 [D1P3]", func(tt *testing.T) {
		tt.Parallel()
		// D1 = 1st order's drop-off point (orders.routes[1])
		// P3 = 3rd order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mocked2ndTripID := "MOCKED_TRIP_ID_B"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(100)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToDestination,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(121212)
				o.IsB2B = true
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(23123)
				o.IsB2B = false
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order3rd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "23.12"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order3rd.Routes[0].Location.Lat,
					"lng": order3rd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.Routes[1].Location.Lat,
					"lng": order1st.Routes[1].Location.Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[1].Location.Lat,
					"lng": order1st.Routes[1].Location.Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.Routes[1].Location.Lat,
					order1st.Routes[1].Location.Lng,
					order3rd.Routes[0].Location.Lat,
					order3rd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)

		trip2nd, err := model.DoNewTrip(mocked2ndTripID, model.TripStatusInit, order2nd)
		assert.NoError(tt, err)
		assert.NoError(tt, trip2nd.AddNewOrder(&order3rd, 1, 1))
		assert.NoError(tt, trip2nd.Cancel(order2nd.OrderID))

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order3rd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order3rd.OrderID, gomock.Any()).
			Return(&order3rd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked2ndTripID, gomock.Any()).
			Return(trip2nd, nil).Times(1)

		deps.tripServices.EXPECT().GetOrdersByTrip(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
				assert.Equal(tt, trip2nd.TripID, trip.TripID)
				return []model.Order{
					order2nd,
					order3rd,
				}, nil
			}).Times(1)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil).Times(1)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2D1D2][P3D3] order 1 cancelled after P1 claim order 3 [D2P3]", func(tt *testing.T) {
		tt.Parallel()
		// D2 = 2nd order's drop-off point (orders.routes[1])
		// P3 = 3rd order's pickup point (orders.routes[0])

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mocked2ndTripID := "MOCKED_TRIP_ID_B"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToDestination,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(100)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(121212)
				o.IsB2B = false
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(23123)
				o.IsB2B = true
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order3rd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "23.12"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order3rd.Routes[0].Location.Lat,
					"lng": order3rd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order2nd.Routes[1].Location.Lat,
					"lng": order2nd.Routes[1].Location.Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order2nd.Routes[1].Location.Lat,
					"lng": order2nd.Routes[1].Location.Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order2nd.Routes[1].Location.Lat,
					order2nd.Routes[1].Location.Lng,
					order3rd.Routes[0].Location.Lat,
					order3rd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, trip1st.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, trip1st.Cancel(order1st.OrderID))

		trip2nd, err := model.DoNewTrip(mocked2ndTripID, model.TripStatusInit, order3rd)
		assert.NoError(tt, err)

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order3rd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order3rd.OrderID, gomock.Any()).
			Return(&order3rd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked2ndTripID, gomock.Any()).
			Return(trip2nd, nil).Times(1)

		deps.tripServices.EXPECT().GetOrdersByTrip(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
				assert.Equal(tt, trip2nd.TripID, trip.TripID)
				return []model.Order{
					order3rd,
				}, nil
			}).Times(1)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order2nd.OrderID, mockedDriverID, gomock.Any()).
			Return(&order2nd, nil).Times(1)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2D1D2][P3D3] order 1 cancelled after P1 claim order 2 [P1P2]", func(tt *testing.T) {
		// P1 = 1st order's pickup point (orders.routes[0])
		// P2 = 2nd order's pickup point (orders.routes[0])
		tt.Parallel()

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mocked2ndTripID := "MOCKED_TRIP_ID_B"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToDestination,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(100)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(121212)
				o.IsB2B = false
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(23123)
				o.IsB2B = true
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order2nd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "121.21"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order2nd.Routes[0].Location.Lat,
					"lng": order2nd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
					order2nd.Routes[0].Location.Lat,
					order2nd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, trip1st.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, trip1st.Cancel(order1st.OrderID))

		trip2nd, err := model.DoNewTrip(mocked2ndTripID, model.TripStatusInit, order3rd)
		assert.NoError(tt, err)

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order2nd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order2nd.OrderID, gomock.Any()).
			Return(&order2nd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.tripServices.EXPECT().GetOrdersByTrip(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
				assert.Equal(tt, trip2nd.TripID, trip.TripID)
				return []model.Order{
					order3rd,
				}, nil
			}).Times(0)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(0)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil).Times(1)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2D1D2][P3D3] order 1 and 2 cancelled claim order 3 [M3P3]", func(tt *testing.T) {
		// M3 = 3rd order's matching point
		// P3 = 3rd order's pickup point (orders.routes[0])
		tt.Parallel()

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mocked2ndTripID := "MOCKED_TRIP_ID_B"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToDestination,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(100)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(121212)
				o.IsB2B = false
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(23123)
				o.IsB2B = true
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order3rd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "23.12"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order3rd.Routes[0].Location.Lat,
					"lng": order3rd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order3rd.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order3rd.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order3rd.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order3rd.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order3rd.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					order3rd.HistoryLocation[string(model.StatusDriverMatched)].Lng,
					order3rd.Routes[0].Location.Lat,
					order3rd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, trip1st.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, trip1st.Cancel(order1st.OrderID))
		assert.NoError(tt, trip1st.Cancel(order2nd.OrderID))

		trip2nd, err := model.DoNewTrip(mocked2ndTripID, model.TripStatusInit, order3rd)
		assert.NoError(tt, err)

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order3rd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order3rd.OrderID, gomock.Any()).
			Return(&order3rd, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked2ndTripID, gomock.Any()).
			Return(trip2nd, nil).Times(1)

		deps.tripServices.EXPECT().GetOrdersByTrip(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
				assert.Equal(tt, trip2nd.TripID, trip.TripID)
				return []model.Order{
					order3rd,
				}, nil
			}).Times(1)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order2nd.OrderID, mockedDriverID, gomock.Any()).
			Return(&order2nd, nil).Times(1)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2D1D2][P3D3] order 2 cancelled before P1 claim order 1 [M1P1]", func(tt *testing.T) {
		// M1 = 1st order's DRIVER_MATCHED point (orders.routes[0])
		// P1 = 1st order's pickup point (orders.routes[0])
		tt.Parallel()

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mocked2ndTripID := "MOCKED_TRIP_ID_B"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(100)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToRestaurant,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(121212)
				o.IsB2B = false
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(23123)
				o.IsB2B = true
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order1st,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "0.10"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[0].Location.Lat,
					"lng": order1st.Routes[0].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					"lng": order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lat,
					order1st.HistoryLocation[string(model.StatusDriverMatched)].Lng,
					order1st.Routes[0].Location.Lat,
					order1st.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, trip1st.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, trip1st.Cancel(order2nd.OrderID))

		trip2nd, err := model.DoNewTrip(mocked2ndTripID, model.TripStatusInit, order3rd)
		assert.NoError(tt, err)

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order1st.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order1st.OrderID, gomock.Any()).
			Return(&order1st, nil)

		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		deps.tripServices.EXPECT().GetOrdersByTrip(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
				assert.Equal(tt, trip2nd.TripID, trip.TripID)
				return []model.Order{
					order3rd,
				}, nil
			}).Times(0)

		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(0)

		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order1st.OrderID, mockedDriverID, gomock.Any()).
			Return(&order1st, nil).Times(0)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("[P1P2D1D2][P3D3] order 2 cancelled before P1 claim order 3 [D1P3]", func(tt *testing.T) {
		// D1 = 1st order's drop-off point (orders.routes[1])
		// P3 = 3rd order's pickup point (orders.routes[0])
		// tt.Parallel() // NOTE to prevent a flaky test for now

		accapi, deps, finish := newTestAccountAPI(tt, config.DriverPeriodCompletedTripsConfig{}, config.PaymentConfig{}, &order.AtomicContingencyConfig{}, &AtomicDBConfig{})
		defer finish()

		// Test Info
		formType := model.FormTypeDriversClaim
		formSubType := model.FormSubtypeFareDisputePickupClaim
		mocked1stTripID := "MOCKED_TRIP_ID_A"
		mocked2ndTripID := "MOCKED_TRIP_ID_B"
		mockedDriverID := "MOCKED_DRIVER_ID"

		// Test Object
		basedDriver := generateMockDriverFunc(mockedDriverID)

		order1st := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(100)
			},
		)

		order2nd := generatedMockOrderFunc(
			mockedDriverID,
			mocked1stTripID,
			model.ServiceFood,
			model.StatusCanceled,
			model.StatusDriverToRestaurant,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(121212)
				o.IsB2B = false
				o.QueuedAfterOrder = order1st.OrderID
			},
		)

		order3rd := generatedMockOrderFunc(
			mockedDriverID,
			mocked2ndTripID,
			model.ServiceFood,
			model.StatusCompleted,
			model.StatusDropOffDone,
			func(o *model.Order) {
				o.Routes[0].Distance = types.Distance(23123)
				o.IsB2B = true
				o.QueuedAfterOrder = order2nd.OrderID
			},
		)

		prefillValue := generatePrefillValueFromOrderFunc(
			basedDriver,
			order3rd,
			func(m map[string]any) {
				m[model.FormPrefillOrderDistanceFromRiderApp] = "23.12"
				m[model.FormPrefillPickupLocation] = map[string]interface{}{
					"lat": order1st.Routes[1].Location.Lat,
					"lng": order1st.Routes[1].Location.Lng,
				}
				m[model.FormPrefillDriverMatchedLocation] = map[string]interface{}{
					"lat": order3rd.Routes[0].Location.Lat,
					"lng": order3rd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillBeforePickupLocation] = map[string]interface{}{
					"lat": order3rd.Routes[0].Location.Lat,
					"lng": order3rd.Routes[0].Location.Lng,
				}
				m[model.FormPrefillGoogleDirectionClaimDistanceURL] = fmt.Sprintf(
					"https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler",
					order1st.Routes[1].Location.Lat,
					order1st.Routes[1].Location.Lng,
					order3rd.Routes[0].Location.Lat,
					order3rd.Routes[0].Location.Lng,
				)
				m[model.FormPrefillDriverType] = "FREELANCER"
				m[model.FormPrefillDriverVendorID] = ""
			},
		)

		generatedPrefill, err := structpb.NewStruct(prefillValue)
		assert.NoError(tt, err)

		trip1st, err := model.DoNewTrip(mocked1stTripID, model.TripStatusInit, order1st)
		assert.NoError(tt, err)
		assert.NoError(tt, trip1st.AddNewOrder(&order2nd, 1, 1))
		assert.NoError(tt, trip1st.Cancel(order2nd.OrderID))

		trip2nd, err := model.DoNewTrip(mocked2ndTripID, model.TripStatusInit, order3rd)
		assert.NoError(tt, err)

		// Expectation
		expectedReq := generateReqFunc(formType, formSubType, order3rd.OrderID)
		gctx, recorder := makeReq(string(formType), mockedDriverID, expectedReq)

		deps.driverRepository.EXPECT().FindDriverID(gctx,
			mockedDriverID,
			gomock.Any()).
			Return(&basedDriver, nil)

		deps.orderService.EXPECT().Get(gctx, order3rd.OrderID, gomock.Any()).
			Return(&order3rd, nil)

		// get a request order's trip
		deps.tripServices.EXPECT().GetTripByID(gctx, mocked2ndTripID, gomock.Any()).
			Return(trip2nd, nil).Times(1)

		// checking all order in a request order's trip
		deps.tripServices.EXPECT().GetOrdersByTrip(gctx, gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
				assert.Equal(tt, trip2nd.TripID, trip.TripID)
				return []model.Order{
					order3rd,
				}, nil
			}).Times(1)

		// get a previous B2B order info
		deps.orderService.EXPECT().GetRevisionByOrderIdAndDriverId(gctx, order2nd.OrderID, mockedDriverID, gomock.Any()).
			Return(&order2nd, nil).Times(1)

		// get trip from a previous B2B order
		deps.tripServices.EXPECT().GetTripByID(gctx, mocked1stTripID, gomock.Any()).
			Return(trip1st, nil).Times(1)

		// expectation call
		deps.formService.EXPECT().CreateForm(gctx, &formServicePb.CreateFormRequest{
			FormType:    model.ToServiceFormType(model.FormTypeDriversClaim),
			FormSubtype: model.ToServiceFormSubtype(model.FormSubtypeFareDisputePickupClaim),
			Prefill:     generatedPrefill,
		}).Return(&formServicePb.CreateFormResponse{}, nil)

		// Call
		accapi.GetFormServiceV2(gctx)

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}
