package account

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	form_servicev1 "git.wndv.co/go/proto/lineman/form_service/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type GetClaimableDetail func(co ClaimOrder, actualOrderData *model.Order, subType string) (bool, string)

var availableClaimSubType = []ClaimSubType{
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_FOOD_CLAIM],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_FOOD_CLAIM.String(), // filter out if not food
	},
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION.String(),
	},
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_LONG_WAITING_TIME_COMPENSATION],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_LONG_WAITING_TIME_COMPENSATION.String(),
	},
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_PARKING_CLAIM],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_PARKING_CLAIM.String(), // filter out if not bkk
	},
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM.String(),
	},
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM.String(),
	},
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM.String(),
	},
}

var availableNonOrderBaseClaimSubType = []ClaimSubType{
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_COVID_CLAIM],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_COVID_CLAIM.String(),
	},
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_ACCIDENT_CLAIM],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_ACCIDENT_CLAIM.String(),
	},
	{
		Title:   model.FormSubTypeTitle[form_servicev1.FormSubtype_FORM_SUBTYPE_CREDIT_TOPUP_CLAIM],
		SubType: form_servicev1.FormSubtype_FORM_SUBTYPE_CREDIT_TOPUP_CLAIM.String(),
	},
}

type ClaimOrder struct {
	OrderID          string          `json:"orderId"`
	Order            model.TripOrder `json:"-"`
	IsClaimed        bool            `json:"isClaimed"`
	IsClaimable      bool            `json:"isClaimable"`
	ClaimableMessage string          `json:"claimableMessage"`
}

type ClaimSubTypes []ClaimSubType

type ClaimSubType struct {
	Title   string       `json:"title"`
	SubType string       `json:"subType"`
	Orders  []ClaimOrder `json:"orders"`
}

type FormClaim struct {
	Title            string              `json:"title"`
	SubType          string              `json:"subType"`
	Status           string              `json:"status"`
	Message          string              `json:"message"`
	CreatedAt        time.Time           `json:"createdAt"`
	UpdatedAt        time.Time           `json:"updatedAt"`
	OrderID          string              `json:"orderId"`
	OrderServiceType string              `json:"orderServiceType"`
	TransactionInfo  *FormClaimTransInfo `json:"transactionInfo,omitempty"`
}

type FormClaimTransInfo struct {
	TransactionRefID string `json:"transactionRefId"`
	TransactionID    string `json:"transactionId"`
}

type OrderClaimListResponse struct {
	ClaimSubTypes []ClaimSubType `json:"claimSubTypes"`
	LastClaim     *FormClaim     `json:"lastClaim,omitempty"`
}

func NewOrderClaimListResponse(
	ctx context.Context,
	trip *model.Trip,
	form *form_servicev1.ListFormResponse,
	subType []ClaimSubType,
	order *model.Order,
	atomicFormConfig *service.AtomicFormServiceConfig,
	formConfig config.FormConfig,
	queriedOrderData map[string]model.Order,
) OrderClaimListResponse {

	var resp OrderClaimListResponse
	claims := make([]ClaimSubType, 0, len(subType))

	if trip == nil {
		logrus.Error("GetOrderClaimList error trip is nil")
		return resp
	}

	if len(trip.Orders) == 0 {
		logrus.Error("GetOrderClaimList orders is empty")
		return resp
	}

	orders := getClaimOrderByTrip(trip)
	for _, v := range subType {
		c := ClaimSubType{
			Title:   v.Title,
			SubType: v.SubType,
			Orders:  orders,
		}
		getOClaimAbleDetail := getOrderClaimableDetail(ctx, trip, order, atomicFormConfig, formConfig)
		c.Orders = assignIsClaimedToOrder(c.Orders, form, v.SubType, getOClaimAbleDetail, queriedOrderData)
		claims = append(claims, c)
	}

	resp.ClaimSubTypes = claims

	return resp
}

func (r *OrderClaimListResponse) setLastClaim(form *form_servicev1.ListFormResponse) {
	data := form.GetData()

	if len(data) == 0 {
		return
	}
	var lf *form_servicev1.Form
	for _, f := range data {
		if f.Status != form_servicev1.FormStatus_FORM_STATUS_INIT {
			lf = f
			break
		}
	}

	if lf == nil {
		return
	}

	lastClaim := getFormClaim(lf)
	r.LastClaim = &lastClaim
}

func assignIsClaimedToOrder(
	orders []ClaimOrder,
	form *form_servicev1.ListFormResponse,
	subType string,
	getOClaimableDetail GetClaimableDetail,
	queriedOrderData map[string]model.Order,
) []ClaimOrder {
	var ords []ClaimOrder
	for _, o := range orders {
		var actualOrderData *model.Order
		if orderData, ok := queriedOrderData[o.OrderID]; ok {
			actualOrderData = &orderData
		}
		isClaimed := isOrderClaimed(form, subType, o.OrderID)
		isAbleToClaim, claimableMessage := getOClaimableDetail(o, actualOrderData, subType)
		ords = append(ords, ClaimOrder{
			OrderID:          o.OrderID,
			IsClaimed:        isClaimed,
			IsClaimable:      isAbleToClaim,
			ClaimableMessage: claimableMessage,
		})
	}
	return ords
}

func getOrderClaimableDetail(ctx context.Context, trip *model.Trip, order *model.Order, atomicFormConfig *service.AtomicFormServiceConfig, formConfig config.FormConfig) GetClaimableDetail {
	return func(o ClaimOrder, actualOrderData *model.Order, subType string) (bool, string) {
		var formBuilder model.FormBuilder
		createdAt := timeutil.BangkokNow()
		if !o.Order.CreatedAt.IsZero() {
			createdAt = o.Order.CreatedAt
		}

		oHistory := make(map[string]time.Time)
		for _, t := range trip.History {
			if t.OrderID == o.OrderID {
				oHistory[string(t.OrderStatus)] = t.Timestamp
			}
		}

		orderFromTrip := &model.Order{
			OrderID: o.OrderID,
			Region:  order.Region,
			Quote:   model.Quote{CreatedAt: createdAt, ServiceType: o.Order.ServiceType},
			Status:  o.Order.Status,
			History: oHistory,
		}

		switch subType {
		case model.FormSubtypeFoodClaim.ToString():
			formBuilder = service.NewFoodClaimForm(nil, orderFromTrip, nil, nil, nil)
		case model.FormSubtypeRiderCompensation.ToString():
			orderToPassIn := orderFromTrip
			if formConfig.IsEnableRiderCompensationTimeAndDistanceLogicFeature &&
				o.Order.ServiceType == model.ServiceBike &&
				actualOrderData != nil {
				orderToPassIn = actualOrderData
			}
			formBuilder = service.NewRiderCompensationClaimForm(nil, orderToPassIn, nil, nil, nil, atomicFormConfig, formConfig)
		case model.FormSubtypeFairDisputeClaim.ToString():
			formBuilder = service.NewFairDisputeClaimForm(nil, orderFromTrip, nil)
		case model.FormSubtypeCreditTopupClaim.ToString():
			formBuilder = service.NewCreditTopupClaimForm(nil)
		case model.FormSubtypeLongWaitingTimeCompensation.ToString():
			formBuilder = service.NewLongWaitingTimeCompensationClaimForm(nil, orderFromTrip, nil)
		case model.FormSubtypeParkingClaim.ToString():
			formBuilder = service.NewParkingClaimForm(nil, orderFromTrip)
		case model.FormSubtypeFareDisputePickupClaim.ToString():
			formBuilder = service.NewFairDisputePickupClaimForm(nil, orderFromTrip, trip, nil, nil, nil)
		case model.FormSubtypeDriver2WQRPaymentClaim.ToString():
			formBuilder = service.New2WQRPaymentClaimForm(nil, order, trip, nil, nil, nil, formConfig)
		default:
			return false, "sub type not support to claim"
		}

		if _, err := formBuilder.Validate(ctx); err != nil {
			return false, err.Error()
		}
		return true, ""
	}
}

func (c ClaimSubTypes) filterSubTypeByServiceTypeAndRegionWithConfig(serviceType model.Service, region string, bikeFairDisputeClaimEnabled bool) ClaimSubTypes {
	var res []ClaimSubType
	switch serviceType {
	case model.ServiceFood:
		for _, v := range c {
			if !stringutil.IsStringInList([]string{
				form_servicev1.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM.String()}, v.SubType) {

				if v.SubType == form_servicev1.FormSubtype_FORM_SUBTYPE_PARKING_CLAIM.String() && region != "BKK" {
					continue
				}
				res = append(res, ClaimSubType{
					SubType: v.SubType,
					Title:   v.Title,
					Orders:  v.Orders,
				})
			}
		}

		return res
	case model.ServiceMart:
		for _, v := range c {
			if !stringutil.IsStringInList([]string{
				form_servicev1.FormSubtype_FORM_SUBTYPE_PARKING_CLAIM.String(),
				form_servicev1.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM.String()}, v.SubType) {
				res = append(res, ClaimSubType{
					SubType: v.SubType,
					Title:   v.Title,
					Orders:  v.Orders,
				})
			}
		}
		return res
	case model.ServiceMessenger:
		for _, v := range c {
			if stringutil.IsStringInList([]string{
				form_servicev1.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION.String(),
				form_servicev1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM.String(),
				form_servicev1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM.String(),
			}, v.SubType) {
				res = append(res, ClaimSubType{
					SubType: v.SubType,
					Title:   v.Title,
					Orders:  v.Orders,
				})
			}
		}
		return res
	case model.ServiceBike:
		for _, v := range c {
			st := []string{form_servicev1.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION.String(),
				form_servicev1.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM.String(),
				form_servicev1.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM.String()}
			if bikeFairDisputeClaimEnabled {
				st = append(st, form_servicev1.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM.String())
			}
			if stringutil.IsStringInList(st, v.SubType) {
				res = append(res, ClaimSubType{
					SubType: v.SubType,
					Title:   v.Title,
					Orders:  v.Orders,
				})
			}
		}
		return res
	default:
		logrus.Errorf("GetOrderClaimList serviceType invalid: %v", serviceType)
		return []ClaimSubType{}
	}

}

func (c ClaimSubTypes) filterSubTypeByConfig(blacklisting types.StringSet) ClaimSubTypes {
	if !blacklisting.IsInitialized() || blacklisting.Count() == 0 {
		return c
	}

	var res []ClaimSubType
	for _, v := range c {
		if !blacklisting.Has(v.SubType) {
			res = append(res, v)
		}
	}
	return res
}

func NewClaimListResponse(form *form_servicev1.ListFormResponse) []FormClaim {
	var res []FormClaim
	data := form.GetData()

	for _, v := range data {
		if v.Status != form_servicev1.FormStatus_FORM_STATUS_INIT {
			f := getFormClaim(v)
			res = append(res, f)
		}
	}

	return res
}

func getFormClaim(val *form_servicev1.Form) FormClaim {

	transRefId := val.GetValue().Fields["transactionRefId"].GetStringValue()
	var transInfo *FormClaimTransInfo
	if transRefId != "" {
		transInfo = &FormClaimTransInfo{
			TransactionRefID: transRefId,
			TransactionID:    val.GetValue().Fields["transactionId"].GetStringValue(),
		}
	}
	return FormClaim{
		Title:            model.FormSubTypeTitle[val.FormSubtype],
		SubType:          val.GetFormSubtype().String(),
		Status:           val.GetStatus().String(),
		Message:          val.GetValue().Fields["reason"].GetStringValue(),
		CreatedAt:        val.GetCreatedAt().AsTime(),
		UpdatedAt:        val.GetUpdatedAt().AsTime(),
		OrderID:          val.GetValue().Fields["orderId"].GetStringValue(),
		OrderServiceType: val.GetValue().Fields["serviceType"].GetStringValue(),
		TransactionInfo:  transInfo,
	}
}

func isOrderClaimed(form *form_servicev1.ListFormResponse, subType string, orderID string) bool {

	if form == nil {
		return false
	}
	r := make(map[string]bool)
	key := fmt.Sprintf("%s_%s", subType, orderID)

	for _, v := range form.GetData() {
		if v != nil && v.GetValue() != nil {
			name := v.GetFormSubtype().String()
			id := v.GetValue().Fields["orderId"].GetStringValue()
			s := fmt.Sprintf("%s_%s", name, id)
			if v.Status != form_servicev1.FormStatus_FORM_STATUS_INIT {
				r[s] = true
			}
		}
	}

	if _, ok := r[key]; ok {
		return true
	}
	return false
}

func getClaimOrderByTrip(trip *model.Trip) []ClaimOrder {
	var r []ClaimOrder

	for _, o := range trip.Orders {
		r = append(r, ClaimOrder{OrderID: o.OrderID, Order: o})
	}

	return r
}
