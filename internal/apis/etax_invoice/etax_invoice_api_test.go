package etax_invoice_test

import (
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	absintheapi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/etax_invoice"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/installment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestEtaxInvoiceAPI_ListEtaxInvoices(t *testing.T) {
	t.Parallel()

	makeReq := func(req installment.ListInstallmentRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("GET", "/v1/admin/etax-invoice", testutil.JSON(req))
	}

	existedEtaxInvoices := []model.EtaxInvoice{{
		InvoiceNo: "INVOICE_NO",
		Document: model.EtaxInvoiceDocument{
			PdfFileID: "PDF_FILE_ID",
		},
	}}

	presignedUrl := "PRESIGNED_URL"
	cfg := etax_invoice.Config{EtaxPDFPresignedUrlTTL: 15 * time.Minute}

	t.Run("should return 200 and e-tax invoices correctly", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestEtaxInvoiceAPI(t, cfg)
		defer finish()

		deps.eTaxInvoiceRepo.
			EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(existedEtaxInvoices, nil)

		deps.eTaxInvoiceRepo.
			EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(len(existedEtaxInvoices), nil)

		deps.vosService.
			EXPECT().GetCDNPreSignedUrl(gomock.Any(), gomock.Any(), 15*time.Minute).
			Return(presignedUrl, nil)

		gctx, recorder := makeReq(installment.ListInstallmentRequest{})

		api.ListEtaxInvoices(gctx)

		var actual ListEtaxInvoicesResponse

		testutil.DecodeJSON(t, recorder.Body, &actual)

		require.Equal(t, http.StatusOK, recorder.Code)
		require.Equal(t, len(existedEtaxInvoices), actual.CountTotal)
		require.Equal(t, existedEtaxInvoices[0].InvoiceNo, actual.Data[0].InvoiceNo)
		require.Equal(t, presignedUrl, *actual.Data[0].PDFUrl)
	})

	t.Run("should return error when find e-tax invoices error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestEtaxInvoiceAPI(t, cfg)
		defer finish()

		errMsg := "find error"

		deps.eTaxInvoiceRepo.
			EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, errors.New(errMsg))

		gctx, recorder := makeReq(installment.ListInstallmentRequest{})

		api.ListEtaxInvoices(gctx)

		var actual absintheapi.Error

		testutil.DecodeJSON(t, recorder.Body, &actual)

		require.Equal(t, http.StatusInternalServerError, recorder.Code)
		require.Equal(t, errMsg, actual.Message)
	})

	t.Run("should return error when count e-tax invoices error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestEtaxInvoiceAPI(t, cfg)
		defer finish()

		errMsg := "count error"

		deps.eTaxInvoiceRepo.
			EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(existedEtaxInvoices, nil)

		deps.eTaxInvoiceRepo.
			EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(0, errors.New(errMsg))

		gctx, recorder := makeReq(installment.ListInstallmentRequest{})

		api.ListEtaxInvoices(gctx)

		var actual absintheapi.Error

		testutil.DecodeJSON(t, recorder.Body, &actual)

		require.Equal(t, http.StatusInternalServerError, recorder.Code)
		require.Equal(t, errMsg, actual.Message)
	})

	t.Run("should return error when sign cdn url error", func(t *testing.T) {
		t.Parallel()
		api, deps, finish := newTestEtaxInvoiceAPI(t, cfg)
		defer finish()

		errMsg := "get cdn presigned url error"

		deps.eTaxInvoiceRepo.
			EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(existedEtaxInvoices, nil)

		deps.eTaxInvoiceRepo.
			EXPECT().CountWithQuery(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(len(existedEtaxInvoices), nil)

		deps.vosService.
			EXPECT().GetCDNPreSignedUrl(gomock.Any(), gomock.Any(), gomock.Any()).
			Return("", errors.New(errMsg))

		gctx, recorder := makeReq(installment.ListInstallmentRequest{})

		api.ListEtaxInvoices(gctx)

		var actual absintheapi.Error

		testutil.DecodeJSON(t, recorder.Body, &actual)

		require.Equal(t, http.StatusInternalServerError, recorder.Code)
		require.Equal(t, errMsg, actual.Message)
	})

}

type ListEtaxInvoicesResponse struct {
	CountTotal int                                `json:"countTotal"`
	Data       []etax_invoice.EtaxInvoiceResponse `json:"data"`
}

type etaxInvoiceAPIDeps struct {
	eTaxInvoiceRepo *mock_repository.MockEtaxInvoiceRepository
	vosService      *mock_service.MockVOSService
}

func newTestEtaxInvoiceAPI(t gomock.TestReporter, cfg etax_invoice.Config) (*etax_invoice.EtaxInvoiceAPI, *etaxInvoiceAPIDeps, func()) {
	ctrl := gomock.NewController(t)

	deps := &etaxInvoiceAPIDeps{
		eTaxInvoiceRepo: mock_repository.NewMockEtaxInvoiceRepository(ctrl),
		vosService:      mock_service.NewMockVOSService(ctrl),
	}
	return etax_invoice.ProvideEtaxInvoiceAPI(
		cfg,
		deps.eTaxInvoiceRepo,
		deps.vosService,
	), deps, func() { ctrl.Finish() }
}
