package etax_invoice

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type EtaxInvoiceResponse struct {
	ID                    string                  `json:"id"`
	InvoiceNo             string                  `json:"invoiceNo"`
	InstallmentID         string                  `json:"installmentId"`
	RelatedInstallmentID  string                  `json:"relatedInstallmentId"`
	TransactionID         string                  `json:"transactionId"`
	PDFUrl                *string                 `json:"pdfUrl"`
	DriverID              string                  `json:"driverId"`
	GeneratedAt           *time.Time              `json:"generatedAt"`
	SubmittedAt           *time.Time              `json:"submittedAt"`
	PublishedAt           *time.Time              `json:"publishedAt"`
	DocumentIssueDate     time.Time               `json:"documentIssueDate"`
	RelatedDocumentNumber string                  `json:"relatedDocumentNumber"`
	Type                  model.EtaxInvoiceType   `json:"type"`
	Status                model.EtaxInvoiceStatus `json:"status"`
	ProductType           model.ProductType       `json:"productType"`
	CreatedAt             time.Time               `json:"createdAt"`
	UpdatedAt             time.Time               `json:"updatedAt"`
	Remarks               []model.Remark          `json:"remarks"`
}

func NewEtaxInvoiceResponse(ei model.EtaxInvoice, pdfUrl *string) EtaxInvoiceResponse {
	res := EtaxInvoiceResponse{
		ID:                    ei.ID.Hex(),
		InvoiceNo:             ei.InvoiceNo,
		InstallmentID:         ei.InstallmentID,
		RelatedInstallmentID:  ei.RelatedInstallmentID,
		TransactionID:         ei.TransactionID,
		PDFUrl:                pdfUrl,
		DriverID:              ei.DriverID,
		DocumentIssueDate:     ei.DocumentIssueDate,
		RelatedDocumentNumber: ei.RelatedDocumentNum,
		Type:                  ei.Type,
		Status:                ei.EtaxInvoiceStatus,
		ProductType:           ei.ProductType,
		CreatedAt:             ei.CreatedAt,
		UpdatedAt:             ei.UpdatedAt,
		Remarks:               ei.Remarks,
	}

	if !ei.GeneratedAt.IsZero() {
		res.GeneratedAt = &ei.GeneratedAt
	}
	if !ei.SubmittedAt.IsZero() {
		res.SubmittedAt = &ei.SubmittedAt
	}
	if !ei.PublishedAt.IsZero() {
		res.PublishedAt = &ei.PublishedAt
	}

	return res
}
