package bulkcsv

import (
	"context"
	"fmt"
	"strings"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type bulkImportCSVDataRowsValidator struct {
}

func newValidator() *bulkImportCSVDataRowsValidator {
	return &bulkImportCSVDataRowsValidator{}
}

func (v *bulkImportCSVDataRowsValidator) validate(ctx context.Context, csvData BulkImportCSVData) error {
	merr := NewCSVMultipleError()
	for idx, row := range csvData.RawRows {
		rowIdx := idx + 1
		errorReasons, isValidRow := v.validateRow(ctx, row)
		if !isValidRow {
			reason := strings.Join(errorReasons, ",")
			merr.AddError(errors.NewCSVRowError(rowIdx, row.Region, reason))
			continue
		}

		merr.AddSuccess(SuccessRow{
			Row:    rowIdx,
			Region: row.Region,
		})
	}

	if merr.HasError() {
		return merr
	}

	return nil
}

func (v *bulkImportCSVDataRowsValidator) validateRow(ctx context.Context, d CoinConversionCSV) ([]string, bool) {
	var reasons []string
	if d.Region == "" {
		reasons = append(reasons, "region is required")
	}

	if len(d.Conditions.Conditions) > 5 {
		reasons = append(reasons, "conditions size limited to 5")
	}

	var days []model.Days
	var dayStrs []string

	for id, c := range d.Conditions.Conditions {
		if len(c.Days) == 0 {
			reasons = append(reasons, fmt.Sprintf("conditions#%v is empty days", id))
		}
		days = append(days, c.Days...)
	}

	for _, d := range days {
		dayStrs = append(dayStrs, string(d))
	}

	hasDup := types.NewStringSet(dayStrs...).Count() != len(dayStrs)
	if hasDup {
		reasons = append(reasons, "conditions days duplicate")
	}

	return reasons, len(reasons) == 0
}
