package bulkcoinconversioncsv

import (
	"github.com/kelseyhightower/envconfig"
)

type Config struct {
	BulkUploadCoinConversionCSVMaxSchemeSize int `envconfig:"BULK_UPLOAD_COIN_CONVERSION_CSV_MAX_SCHEME_SIZE"  default:"5000"`
	BulkUploadCoinConversionCSVWorkerPool    int `envconfig:"BULK_UPLOAD_COIN_CONVERSION_CSV_WORKER_POOL"  default:"10"`
}

func ProvideBulkCoinConversionConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
