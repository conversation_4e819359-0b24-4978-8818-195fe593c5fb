package bulkcoinconversioncsv

import (
	"sort"
	"sync"
)

type bulkImportCSVCreateRes struct {
	m         *sync.Mutex
	Successes []success `json:"successes"`
	Failures  []fail    `json:"failures"`
}

func (r *bulkImportCSVCreateRes) AddSuccess(row int, id string) {
	r.m.Lock()
	defer r.m.Unlock()
	s := success{Row: row, Id: id}
	r.Successes = sortAndInsert(r.<PERSON>, s)
}

func (r *bulkImportCSVCreateRes) AddFailure(row int, id string, reason string) {
	r.m.Lock()
	defer r.m.Unlock()
	f := fail{Row: row, Id: id, Error: reason}

	r.Failures = sortAndInsert(r.Failures, f)
}

type InsertAndSort interface {
	success | fail
	GetRow() int
}

func sortAndInsert[T InsertAndSort](ss []T, s T) []T {
	i := sort.Search(len(ss), func(i int) bool {
		return s.GetRow() < ss[i].GetRow()
	})
	a := new(T)
	ss = append(ss, *a)
	copy(ss[i+1:], ss[i:])
	ss[i] = s
	return ss
}

type bulkImportCSVUpdateRes struct {
	m         *sync.Mutex
	Successes []success `json:"successes"`
	Failures  []fail    `json:"failures"`
}

func (r *bulkImportCSVUpdateRes) AddSuccess(row int, id string) {
	r.m.Lock()
	defer r.m.Unlock()
	s := success{Row: row, Id: id}
	r.Successes = sortAndInsert(r.Successes, s)
}

func (r *bulkImportCSVUpdateRes) AddFailure(row int, id string, reason string) {
	r.m.Lock()
	defer r.m.Unlock()
	f := fail{Row: row, Id: id, Error: reason}
	r.Failures = sortAndInsert(r.Failures, f)
}

type success struct {
	Row int    `json:"row"`
	Id  string `json:"id"`
}

func (r success) GetRow() int {
	return r.Row
}

type fail struct {
	Row   int    `json:"row"`
	Id    string `json:"id"`
	Error string `json:"error"`
}

func (r fail) GetRow() int {
	return r.Row
}

type bulkResultResponse struct {
	Status       string `json:"status"`
	TotalSuccess int    `json:"totalSuccess"`
	TotalFailed  int    `json:"totalFailed"`
	Data         string `json:"data"`
}
