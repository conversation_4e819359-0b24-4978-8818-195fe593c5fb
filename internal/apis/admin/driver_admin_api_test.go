package admin

import (
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"net/url"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"github.com/icrowley/fake"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/go/unleash/test"
	absintheApi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/bulk"
	mock_bulk "git.wndv.co/lineman/fleet-distribution/internal/bulk/mock"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack/mock_slack"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/mock_event_bus"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_unleash"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/income/mock_income"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestDriverAdminAPI_GetDriversCSV(t *testing.T) {
	generateDriver := func(id string) model.Driver {
		now := time.Now()
		citiRefID := crypt.NewLazyEncryptedString(fake.DigitsN(10))
		return model.Driver{
			DriverID: id,
			BaseDriver: model.BaseDriver{
				Title:                 crypt.NewLazyEncryptedString(fake.CharactersN(10)),
				Firstname:             crypt.NewLazyEncryptedString("Somsak"),
				Lastname:              crypt.NewLazyEncryptedString("Somchai"),
				Phone:                 crypt.NewLazyEncryptedString("**********"),
				AvatarURL:             fake.CharactersN(20),
				CitizenID:             crypt.NewLazyEncryptedString(fake.DigitsN(13)),
				CitizenIDCardPhotoURL: fake.CharactersN(20),
				Birthday:              &now,
				CanExceedAgeLimit:     false,
				DriverType:            crypt.NewLazyEncryptedString(fake.CharactersN(10)),

				LineUID: crypt.NewLazyEncryptedString(fake.CharactersN(10)),

				Banking: model.BankingInfo{
					Account:   crypt.NewLazyEncryptedString(fake.DigitsN(10)),
					RefID:     citiRefID,
					CitiRefID: citiRefID.String(),
					UOBRefID:  fake.DigitsN(10),
					PhotoURL:  fmt.Sprintf("https://test1/%s", fake.CharactersN(20)),
				},

				CreatedAt: now.UTC(),
				UpdatedAt: now.UTC(),
			},
			Region: "BKK",
		}
	}

	makeReq := func(q string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/drivers%s", q), nil)
		gctx.Request.Header.Set("accept", "text/csv")
		return gctx, recorder
	}

	makeExpectedCSV := func(driver model.Driver) bytes.Buffer {
		b := &bytes.Buffer{}
		writer := csv.NewWriter(b)
		_ = writer.Write([]string{
			"driver_id",
			"first_name",
			"last_name",
			"nation_id",
			"creation_time",
			"criminal_check_status",
			"phone",
			"region",
		})

		_ = writer.Write([]string{
			driver.DriverID,
			driver.Firstname.String(),
			driver.Lastname.String(),
			driver.CitizenID.String(),
			driver.CreatedAt.In(timeutil.BangkokLocation()).String(),
			string(driver.GetCriminalCheckStatus()),
			driver.Phone.String(),
			driver.Region.String(),
		})
		writer.Flush()
		return *b
	}

	t.Run("return 200 with a csv file response", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		fakeDriveID := fake.Characters()
		fakeDriver := generateDriver(fakeDriveID)

		deps.driverRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), 0, 40000).Return([]model.Driver{fakeDriver}, nil)

		gctx, recorder := makeReq("?driverId=DRIVER_ID")
		api.ExportCSVDrivers(gctx)
		expectedCSV := makeExpectedCSV(fakeDriver)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, expectedCSV.String(), recorder.Body.String())
	})

	t.Run("return 401 if send a invalid request", func(tt *testing.T) {
		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := makeReq("?beginAt=something")
		api.ExportCSVDrivers(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("return 400 if request is empty", func(t *testing.T) {
		api, _, finish := newTestDriverAdminAPI(t)
		defer finish()

		gctx, recorder := makeReq("")
		api.ExportCSVDrivers(gctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(t, recorder.Body, &actual)
		require.Equal(t, "INVALID_REQUEST", actual.Code)
		require.Equal(t, "กรุณาเลือกอย่างน้อย 1 เงื่อนไขในการค้นหาข้อมูล", actual.Message)
	})
}

func TestDriverAdminAPI_ListDrivers(t *testing.T) {
	generateDriver := func(id string) model.Driver {
		now := time.Now()
		citiRefID := crypt.NewLazyEncryptedString(fake.DigitsN(10))
		return model.Driver{
			DriverID: id,
			BaseDriver: model.BaseDriver{
				Title:                 crypt.NewLazyEncryptedString(fake.CharactersN(10)),
				Firstname:             crypt.NewLazyEncryptedString("Somsak"),
				Lastname:              crypt.NewLazyEncryptedString("Somchai"),
				Phone:                 crypt.NewLazyEncryptedString("**********"),
				AvatarURL:             fake.CharactersN(20),
				CitizenID:             crypt.NewLazyEncryptedString(fake.DigitsN(13)),
				CitizenIDCardPhotoURL: fake.CharactersN(20),
				Birthday:              &now,
				CanExceedAgeLimit:     false,
				DriverType:            crypt.NewLazyEncryptedString(fake.CharactersN(10)),

				LineUID: crypt.NewLazyEncryptedString(fake.CharactersN(10)),

				Banking: model.BankingInfo{
					Account:   crypt.NewLazyEncryptedString(fake.DigitsN(10)),
					RefID:     citiRefID,
					CitiRefID: citiRefID.String(),
					UOBRefID:  fake.DigitsN(10),
					PhotoURL:  fmt.Sprintf("https://test1/%s", fake.CharactersN(20)),
				},

				CreatedAt: now.UTC(),
				UpdatedAt: now.UTC(),
			},
		}
	}

	makeReq := func(q string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/drivers%s", q), nil)
		gctx.Request.Header.Set("accept", "text/csv")
		return gctx, recorder
	}

	t.Run("return 200 with a json response", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		fakeDriveID := fake.Characters()
		fakeDriver := generateDriver(fakeDriveID)

		deps.driverRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), 0, 10, gomock.Any()).Return([]model.Driver{fakeDriver}, nil)

		gctx, recorder := makeReq("?driverId=DRIVER_ID")
		api.ListDrivers(gctx)

		type PaginationResponse struct {
			Data []model.Driver `json:"data"`
		}
		var actual PaginationResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)

		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, 1, len(actual.Data))
	})

	t.Run("return 401 if send a invalid request", func(tt *testing.T) {
		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := makeReq("?beginAt=something")
		api.ListDrivers(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("return 400 if request is empty", func(t *testing.T) {
		api, _, finish := newTestDriverAdminAPI(t)
		defer finish()

		gctx, recorder := makeReq("")
		api.ListDrivers(gctx)

		require.Equal(t, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(t, recorder.Body, &actual)
		require.Equal(t, "INVALID_REQUEST", actual.Code)
		require.Equal(t, "กรุณาเลือกอย่างน้อย 1 เงื่อนไขในการค้นหาข้อมูล", actual.Message)
	})
}

func TestDriverAdminAPI_BulkUpdateTier(t *testing.T) {
	t.Parallel()

	t.Run("bulk upload update tier success, len equal to batchSize", func(tt *testing.T) {
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/tier").
				Body().MultipartForm().
				String("createdBy", "admin").
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID,Tier,Negative Group
		LMDY0EBYC,BRONZE,
		LMD7YJR1P,BRONZE,
		LMDX4T05B,SILVER,`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		api.Cfg.BulkUpdateDriverTierBatchSize = 3
		defer finish()

		deps.driverRepo.EXPECT().UpdateManyByTier(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil).AnyTimes()

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkUpdateDriverTier(gctx)
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual *BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 3, len(actual.Successes))
		require.Equal(tt, 0, len(actual.Failures))
	})

	t.Run("bulk upload update tier success and failures, wrong TIER", func(tt *testing.T) {
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/tier")
			gctx.Body().MultipartForm().
				String("createdBy", "admin").
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID,Tier,Negative Group
		LMDY0EBYC,BRONZE,
		LMD7YJR1P,BRONZE,
		LMDX4T05B,INVALID,`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		api.Cfg.BulkUpdateDriverTierBatchSize = 3
		defer finish()

		deps.driverRepo.EXPECT().UpdateManyByTier(gomock.Any(), gomock.Any(), gomock.Any()).Times(1)
		deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil).AnyTimes()

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkUpdateDriverTier(gctx)
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual *BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)

		require.Equal(tt, 2, len(actual.Successes))
		require.Equal(tt, 1, len(actual.Failures))
	})

	t.Run("bulk upload update tier success and failures, wrong DriverID", func(tt *testing.T) {
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/tier")
			gctx.Body().MultipartForm().
				String("createdBy", "admin").
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID,Tier,Negative Group
		LMDY0EBYC,BRONZE,
		LMD7YJR1P,BRONZE,
		LMDX4T05B,SILVER,`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		api.Cfg.BulkUpdateDriverTierBatchSize = 3
		defer finish()

		deps.driverRepo.EXPECT().UpdateManyByTier(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)
		deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil).AnyTimes()

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkUpdateDriverTier(gctx)
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual *BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 3, len(actual.Successes))
		require.Equal(tt, 0, len(actual.Failures))
	})

	t.Run("bulk upload update tier success, len greater than batchSize", func(tt *testing.T) {
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/tier")
			gctx.Body().MultipartForm().
				String("createdBy", "admin").
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID,Tier,Negative group
		LMDY0EBYC,BRONZE,
		LMD7YJR1P,BRONZE,
		LMDX4T05B,SILVER,
		LMDLJK56K,GOLD,
		LMDUV6KJ7,MASTER,`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		api.Cfg.BulkUpdateDriverTierBatchSize = 3
		defer finish()

		deps.driverRepo.EXPECT().UpdateManyByTier(gomock.Any(), gomock.Any(), gomock.Any()).Times(4)

		deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil).AnyTimes()

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkUpdateDriverTier(gctx)
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual *BulkIDsResponse
		fmt.Println(recorder.Body)
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 5, len(actual.Successes))
		require.Equal(tt, 0, len(actual.Failures))
	})
}

func generateDriver(id string) model.Driver {
	return model.Driver{
		DriverID: id,
	}
}

func TestDriverAdminAPI_BulkUpdateDriverZones(t *testing.T) {
	t.Parallel()

	t.Run("bulk upload update dedicated_zone success", func(tt *testing.T) {
		// NOTE: Might be invalid test
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/dedicated-zones")
			form := gctx.Body().MultipartForm().
				String("createdBy", "admin").
				File("file", "file.csv", content)
			gctx.FakeAdminCtxAuthorized("admin", fake.CharactersN(20), "<EMAIL>")
			form.Build()

			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID,Dedicated Zone Labels
		LMD1,zone1
		LMD2,zone2
		LMD3,zone1|zone2
		LMD4,zone1|zone2
		LMD5,
		LMD6,`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPIWithAdminCfg(tt, Config{
			BulkUpdateDriverDedicatedZonesWorker:     10,
			BulkUpdateDriverDedicatedZonesBatchSize:  2,
			BulkUpdateDriverDedicatedZonesMaxCSVRows: 1000,
		}, &service.AtomicCancelReasonConfig{})
		defer finish()
		invalid := types.NewStringSet("LMD2", "LMD3")

		deps.dedicatedZoneRepo.EXPECT().CountByLabels(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, labels []string, _ ...interface{}) (int, error) {
			require.ElementsMatch(tt, []string{"zone1", "zone2"}, labels)
			return 2, nil
		})
		deps.driverRepo.EXPECT().UpdateManyDedicatedZoneLabels(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(4)

		deps.driverRepo.EXPECT().
			ValidateDriverIDs(gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, driverIDs []string, _ ...repository.Option) ([]string, []string, error) {
				var v []string
				var i []string

				for _, d := range driverIDs {
					if invalid.Has(d) {
						i = append(i, d)
					} else {
						v = append(v, d)
					}
				}

				return v, i, nil
			}).Times(4)

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkUpdateDriverDedicatedZones(gctx)
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual *BulkIDReasonResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 4, len(actual.Successes))
		require.Equal(tt, 2, len(actual.Failures))
	})

	t.Run("bulk upload with effectiveAt", func(tt *testing.T) {
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/dedicated-zones")
			form := gctx.Body().MultipartForm().
				String("createdBy", "admin").
				File("file", "file.csv", content)
			gctx.FakeAdminCtxAuthorized("admin", fake.CharactersN(20), "<EMAIL>")

			date := time.Date(2024, 2, 1, 0, 0, 0, 0, timeutils.BangkokLocation())
			form.String("effectiveAt", date.Format(time.RFC3339))
			form.Build()

			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID,Dedicated Zone Labels
		LMD1,zone1
		LMD2,zone2
		LMD3,zone1|zone2
		LMD4,zone1|zone2
		LMD5,
		LMD6,`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPIWithAdminCfg(tt, Config{
			BulkUpdateDriverDedicatedZonesWorker:     10,
			BulkUpdateDriverDedicatedZonesBatchSize:  2,
			BulkUpdateDriverDedicatedZonesMaxCSVRows: 1000,
		}, &service.AtomicCancelReasonConfig{})
		defer finish()

		deps.driverRepo.EXPECT().
			ValidateDriverIDs(gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{"LMD1", "LMD4", "LMD5", "LMD6"}, []string{"LMD2", "LMD3"}, nil)

		deps.bulkProcessInfoRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
		deps.dedicatedZoneRepo.EXPECT().CountByLabels(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, labels []string, _ ...interface{}) (int, error) {
			require.ElementsMatch(tt, []string{"zone1", "zone2"}, labels)
			return 2, nil
		})
		deps.slack.EXPECT().Notify(gomock.Any(), gomock.Any()).Return(nil)

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkUpdateDriverDedicatedZones(gctx)
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestDriverAdminAPI_BulkUpdateServiceTypes(t *testing.T) {
	t.Parallel()

	cfg := Config{
		BulkUpdateServiceTypeMaxCSVRows: 1000,
		BulkUpdateServiceTypeWorker:     10,
	}

	generateCSV := func(count int, svcs model.Services) string {
		driverIds := []string{}
		for id := 1; id <= count; id++ {
			s := strings.Join(svcs.String(), "|")
			driverIds = append(driverIds, fmt.Sprintf("LM%05d,%s", id, s))
		}
		return strings.Join(append([]string{"Driver ID, Service"}, driverIds...), "\n")
	}

	compareAuditLog := func(t *testing.T, expected model.AuditLog, actual model.AuditLog) {
		require.True(t,
			cmp.Equal(expected, actual, cmpopts.IgnoreFields(model.AuditLog{}, "Timestamp", "Before", "After")),
			cmp.Diff(expected, actual, cmpopts.IgnoreFields(model.AuditLog{}, "Timestamp", "Before", "After")),
		)
	}

	type TestCase struct {
		name                     string
		content                  string
		effectiveAt              time.Time
		expect                   func(t *testing.T, recorder *httptest.ResponseRecorder)
		stubUpdateServiceTypes   func(ctx context.Context, driverID string, services []model.Service) error
		stubIsExist              func(ctx context.Context, query repository.DriverQuery, selector []string, sort []string, skip, limit int, opts ...repository.Option) ([]model.Driver, error)
		stubFindDriverByID       func(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error)
		expectUpdateServiceTypes func() ([]model.Service, int)
		expectAuditLog           func(t *testing.T, ll []model.AuditLog)
	}

	testCases := []TestCase{
		{
			name:    "happy case: multiple service types",
			content: generateCSV(1, []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger, model.ServiceBike}),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 1, len(actual.Successes))
				require.Equal(t, 0, len(actual.Failures))
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger, model.ServiceBike}, 1
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 1)

				expected := model.AuditLog{
					Object: model.AuditObject{
						ObjectType: model.DriverObject,
						ID:         "LM00001",
					},
					Actor: model.AuditLogActor{
						ID: "<EMAIL>",
					},
					Event:  model.AuditEventBulkUpdateCSVDriverServiceType,
					Action: model.UpdateAction,
				}

				compareAuditLog(t, expected, ll[0])
			},
		},
		{
			name:    "happy case: multiple service types",
			content: generateCSV(5, []model.Service{model.ServiceFood, model.ServiceBike}),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 5, len(actual.Successes))
				require.Equal(t, 0, len(actual.Failures))
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood, model.ServiceBike}, 5
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 5)

				expected := model.AuditLog{
					Object: model.AuditObject{
						ObjectType: model.DriverObject,
						ID:         "LM00001",
					},
					Actor: model.AuditLogActor{
						ID: "<EMAIL>",
					},
					Event:  model.AuditEventBulkUpdateCSVDriverServiceType,
					Action: model.UpdateAction,
				}

				for i := 0; i < 5; i++ {
					expected.Object.ID = fmt.Sprintf("LM%05d", i+1)
					compareAuditLog(t, expected, ll[i])
				}
			},
		},
		{
			name:    "happy case: single service type",
			content: generateCSV(10, []model.Service{model.ServiceBike}),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 10, len(actual.Successes))
				require.Equal(t, 0, len(actual.Failures))
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceBike}, 10
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 10)

				expected := model.AuditLog{
					Object: model.AuditObject{
						ObjectType: model.DriverObject,
						ID:         "LM00001",
					},
					Actor: model.AuditLogActor{
						ID: "<EMAIL>",
					},
					Event:  model.AuditEventBulkUpdateCSVDriverServiceType,
					Action: model.UpdateAction,
				}

				for i := 0; i < 10; i++ {
					expected.Object.ID = fmt.Sprintf("LM%05d", i+1)
					compareAuditLog(t, expected, ll[i])
				}
			},
		},
		{
			name: "unhappy case (partial sucesss): one valid row (driver_id) and another one invalid row (driver_id)",
			content: `driver id, service
driver_a,food
driver_b,food`,
			stubUpdateServiceTypes: func(ctx context.Context, driverID string, services []model.Service) error {
				if driverID == "driver_b" {
					return errors.New("db error")
				}
				return nil
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 1, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_b", actual.Failures[0].ID)
				require.Equal(t, "db error", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood}, 2
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 1)

				expected := model.AuditLog{
					Object: model.AuditObject{
						ObjectType: model.DriverObject,
						ID:         "driver_a",
					},
					Actor: model.AuditLogActor{
						ID: "<EMAIL>",
					},
					Event:  model.AuditEventBulkUpdateCSVDriverServiceType,
					Action: model.UpdateAction,
				}

				compareAuditLog(t, expected, ll[0])
			},
		},
		{
			name: "unhappy case (partial sucesss): one valid row (service_type) and another one invalid row (service_type)",
			content: `driver id, service
driver_a,food
driver_b,invalid_service_type`,
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 1, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_b", actual.Failures[0].ID)
				require.Equal(t, "invalid service type for service:invalid_service_type", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood}, 1
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 1)

				expected := model.AuditLog{
					Object: model.AuditObject{
						ObjectType: model.DriverObject,
						ID:         "driver_a",
					},
					Actor: model.AuditLogActor{
						ID: "<EMAIL>",
					},
					Event:  model.AuditEventBulkUpdateCSVDriverServiceType,
					Action: model.UpdateAction,
				}

				compareAuditLog(t, expected, ll[0])
			},
		},
		{
			name: "unhappy case (partial sucesss): duplicate driver id",
			content: `driver id, service
driver_a,food
driver_a,food`,
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 1, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_a", actual.Failures[0].ID)
				require.Equal(t, "duplicate driver id:driver_a", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood}, 1
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 1)

				expected := model.AuditLog{
					Object: model.AuditObject{
						ObjectType: model.DriverObject,
						ID:         "driver_a",
					},
					Actor: model.AuditLogActor{
						ID: "<EMAIL>",
					},
					Event:  model.AuditEventBulkUpdateCSVDriverServiceType,
					Action: model.UpdateAction,
				}

				compareAuditLog(t, expected, ll[0])
			},
		},
		{
			name: "unhappy case (fail): db error",
			content: `driver id, service
driver_a,food`,
			stubUpdateServiceTypes: func(ctx context.Context, driverID string, serviceTypes []model.Service) error {
				return errors.New("db error")
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 0, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_a", actual.Failures[0].ID)
				require.Equal(t, "db error", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood}, 1
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name: "unhappy case (fail): driver id doesn't exists",
			content: `driver id, service
driver_a,food`,
			stubIsExist: func(ctx context.Context, query repository.DriverQuery, selector, sort []string, skip, limit int, opts ...repository.Option) ([]model.Driver, error) {
				return []model.Driver{}, nil
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 0, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_a", actual.Failures[0].ID)
				require.Equal(t, "driver id doesn't exists", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{}, 0
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name: "unhappy case (fail): empty service types",
			content: `driver id, service
driver_a,`,
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 0, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_a", actual.Failures[0].ID)
				require.Equal(t, "empty service type for driver id:driver_a", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{}, 0
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name: "unhappy case (fail): invalid service type",
			content: `driver id, service
driver_a,unknown`,
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 0, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_a", actual.Failures[0].ID)
				require.Equal(t, "invalid service type for service:unknown", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{}, 0
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name: "unhappy case (fail): invalid driver id and service type",
			content: `driver id, service
driver_a,unknown`,
			stubIsExist: func(ctx context.Context, query repository.DriverQuery, selector, sort []string, skip, limit int, opts ...repository.Option) ([]model.Driver, error) {
				return []model.Driver{}, nil
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 0, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_a", actual.Failures[0].ID)
				require.Equal(t, "driver id doesn't exists,invalid service type for service:unknown", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{}, 0
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name: "unhappy case (fail): space between each service type",
			content: `driver id, service
driver_a, food| mart| messenger`,
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 0, len(actual.Successes))
				require.Equal(t, 1, len(actual.Failures))
				require.Equal(t, "driver_a", actual.Failures[0].ID)
				require.Equal(t, "invalid service type for service: food,invalid service type for service: mart,invalid service type for service: messenger", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{}, 0
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name: "unhappy case (fail): column exceeded",
			content: `driver id, service, test
driver_a,food,abc`,
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusBadRequest, recorder.Code)

				var actual absintheApi.Error
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, "invalid file structure, the csv file must has only 2 columns (Driver ID, Service Types)", actual.Message)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{}, 0
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name: "unhappy case (fail): column missing",
			content: `driver id
driver_a`,
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusBadRequest, recorder.Code)

				var actual absintheApi.Error
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, "invalid file structure, the csv file must has only 2 columns (Driver ID, Service Types)", actual.Message)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{}, 0
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name:        "happy case: multiple service types with effective time",
			content:     generateCSV(5, []model.Service{model.ServiceFood, model.ServiceBike}),
			effectiveAt: time.Date(2024, 2, 1, 0, 0, 0, 0, timeutils.BangkokLocation()),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Equal(t, 5, len(actual.Successes))
				require.Equal(t, 0, len(actual.Failures))
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood, model.ServiceBike}, 5
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name:    "happy case: valid services type for full time driver",
			content: generateCSV(5, driver.ServiceTypesForFullTimeDriver),
			stubFindDriverByID: func(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
				if driverID == "LM00001" {
					return &model.Driver{
						DriverID: driverID,
						BaseDriver: model.BaseDriver{
							DriverType: crypt.NewLazyEncryptedString(
								string(model.DriverTypeFullTime),
							),
						},
						DriverVendorID: "vendor_a",
					}, nil
				}
				return &model.Driver{
					DriverID: driverID,
					BaseDriver: model.BaseDriver{
						DriverType: crypt.NewLazyEncryptedString(
							"normal",
						),
					},
				}, nil
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Len(t, actual.Successes, 5)
				require.Len(t, actual.Failures, 0)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return driver.ServiceTypesForFullTimeDriver, 5
			},
		},
		{
			name: "unhappy case (partial sucess): invalid service types for full time driver",
			content: generateCSV(5, []model.Service{model.ServiceFood, model.
				ServiceMart, model.ServiceMessenger, model.ServiceBike}),
			stubFindDriverByID: func(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
				if driverID == "LM00001" {
					return &model.Driver{
						DriverID: driverID,
						BaseDriver: model.BaseDriver{
							DriverType: crypt.NewLazyEncryptedString(
								string(model.DriverTypeFullTime),
							),
						},
					}, nil
				}
				return &model.Driver{
					DriverID: driverID,
					BaseDriver: model.BaseDriver{
						DriverType: crypt.NewLazyEncryptedString(
							"normal",
						),
					},
				}, nil
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Len(t, actual.Successes, 4)
				require.Len(t, actual.Failures, 1)

				require.Equal(t, "LM00001", actual.Failures[0].ID)
				require.Equal(t, "invalid service type(s) for a full-time driver", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger, model.ServiceBike}, 4
			},
		},
		{
			name:        "happy case: valid services type for full time driver with effective time",
			content:     generateCSV(5, driver.ServiceTypesForFullTimeDriver),
			effectiveAt: time.Date(2024, 2, 1, 0, 0, 0, 0, timeutils.BangkokLocation()),
			stubFindDriverByID: func(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
				if driverID == "LM00001" {
					return &model.Driver{
						DriverID: driverID,
						BaseDriver: model.BaseDriver{
							DriverType: crypt.NewLazyEncryptedString(
								string(model.DriverTypeFullTime),
							),
						},
						DriverVendorID: "vendor_a",
					}, nil
				}
				return &model.Driver{
					DriverID: driverID,
					BaseDriver: model.BaseDriver{
						DriverType: crypt.NewLazyEncryptedString(
							"normal",
						),
					},
				}, nil
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Len(t, actual.Successes, 5)
				require.Len(t, actual.Failures, 0)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return driver.ServiceTypesForFullTimeDriver, 5
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
		{
			name: "unhappy case (partial sucess): invalid service types for full time driver with effective time",
			content: generateCSV(5, []model.Service{model.ServiceFood, model.
				ServiceMart, model.ServiceMessenger, model.ServiceBike}),
			effectiveAt: time.Date(2024, 2, 1, 0, 0, 0, 0, timeutils.BangkokLocation()),
			stubFindDriverByID: func(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
				if driverID == "LM00001" {
					return &model.Driver{
						DriverID: driverID,
						BaseDriver: model.BaseDriver{
							DriverType: crypt.NewLazyEncryptedString(
								string(model.DriverTypeFullTime),
							),
						},
					}, nil
				}
				return &model.Driver{
					DriverID: driverID,
					BaseDriver: model.BaseDriver{
						DriverType: crypt.NewLazyEncryptedString(
							"normal",
						),
					},
				}, nil
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)

				var actual BulkIDReasonResponse
				testutil.DecodeJSON(t, recorder.Body, &actual)
				require.Len(t, actual.Successes, 4)
				require.Len(t, actual.Failures, 1)

				require.Equal(t, "LM00001", actual.Failures[0].ID)
				require.Equal(t, "invalid service type(s) for a full-time driver", actual.Failures[0].Reason)
			},
			expectUpdateServiceTypes: func() ([]model.Service, int) {
				return []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger, model.ServiceBike}, 4
			},
			expectAuditLog: func(t *testing.T, ll []model.AuditLog) {
				require.Len(t, ll, 0)
			},
		},
	}

	for _, tc := range testCases {
		_tc := tc
		t.Run(_tc.name, func(tt *testing.T) {
			makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
				gctx := testutil.NewContextWithRecorder()
				gctx.SetPUT("/bulk/drivers/service-types")
				gctx.FakeAdminCtxAuthorized("admin", fake.CharactersN(20), "<EMAIL>")

				form := gctx.Body().MultipartForm().
					File("file", "file.csv", content)

				if !_tc.effectiveAt.IsZero() {
					form.String("effectiveAt", _tc.effectiveAt.Format(time.RFC3339))
				}

				form.Build()

				return gctx.GinCtx(), gctx.ResponseRecorder
			}

			csv := _tc.content

			gctx, recorder := makeReq(csv)
			api, deps, finish := newTestDriverAdminAPIWithAdminCfg(tt, cfg, &service.AtomicCancelReasonConfig{})
			defer finish()

			safeAuditLog := types.NewSafeSlice[model.AuditLog]()

			defaultStubExists := func(ctx context.Context, query repository.DriverQuery, selector []string, sort []string, skip, limit int, opts ...repository.Option) ([]model.Driver, error) {
				mq, ok := query.(*persistence.MongoDriverQuery)
				if !ok {
					return nil, errors.New("query must be MongoGroupTransactionQuery")
				}
				driverIds := mq.DriverIds()
				drivers := make([]model.Driver, len(driverIds))
				for idx, v := range driverIds {
					drivers[idx] = model.Driver{DriverID: v}
				}
				return drivers, nil
			}
			if _tc.stubIsExist != nil {
				defaultStubExists = _tc.stubIsExist
			}
			deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(defaultStubExists).AnyTimes()

			stubFindDriverByID := func(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
				return &model.Driver{DriverID: driverID}, nil
			}
			if _tc.stubFindDriverByID != nil {
				stubFindDriverByID = _tc.stubFindDriverByID
			}

			deps.driverRepo.EXPECT().FindDriverIDs(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, driverIDs []string, opts ...repository.Option) ([]model.Driver, error) {
				drivers := make([]model.Driver, 0, len(driverIDs))
				for _, driverID := range driverIDs {
					drv, err := stubFindDriverByID(ctx, driverID)
					if err != nil {
						return nil, err
					}
					drivers = append(drivers, *drv)
				}
				return drivers, nil
			})

			if !_tc.effectiveAt.IsZero() {
				deps.bulkProcessInfoRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, info *model.BulkProcessInfo, opts ...repository.Option) error {
					require.NotZero(tt, info.ID)
					require.True(tt, _tc.effectiveAt.Equal(info.ProcessedAt))
					require.Equal(tt, "<EMAIL>", info.RequestedBy)
					return nil
				})
				deps.slack.EXPECT().Notify(gomock.Any(), gomock.Any()).Return(nil)
			} else if _tc.expectUpdateServiceTypes != nil {
				expectServiceTypes, expectedCalls := _tc.expectUpdateServiceTypes()
				stub := func(ctx context.Context, driverID string, services []model.Service) error {
					return nil
				}
				if _tc.stubUpdateServiceTypes != nil {
					stub = _tc.stubUpdateServiceTypes
				}

				deps.driverRepo.EXPECT().UpdateServiceTypes(gomock.Any(), gomock.Any(), expectServiceTypes).DoAndReturn(stub).Times(expectedCalls)

				deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(stubFindDriverByID)

				deps.auditLog.EXPECT().Insert(gomock.Any(), gomock.Any()).AnyTimes().DoAndReturn(func(ctx context.Context, al *model.AuditLog) error {
					// *caution* not concurrent safe here since we use wait group to wait for all goroutines to finish
					safeAuditLog.Add(*al)
					return nil
				})
			}

			wg := safe.CreateWaitGroupOnGctx(gctx)
			api.BulkUpdateServiceTypes(gctx)
			wg.Wait()

			_tc.expect(t, recorder)

			if _tc.expectAuditLog != nil {
				sortedAuditLog := safeAuditLog.Slice()

				// sort by object id for easy comparison
				sort.Slice(sortedAuditLog, func(i, j int) bool {
					return sortedAuditLog[i].Object.ID < sortedAuditLog[j].Object.ID
				})

				_tc.expectAuditLog(t, sortedAuditLog)
			}
		})

	}
}

func TestDriverAdminAPI_UnbanDriver(t *testing.T) {
	t.Parallel()

	b64 := "dummy_base64"
	gctx, recorder, _ := newTestUnbanRequest([]byte(b64), "testbot", "testbot", "driver-1")

	driver := &model.Driver{Status: model.StatusBanned}

	api, deps, finish := newTestDriverAdminAPI(t)
	deps.driverRepo.EXPECT().
		GetProfile(gomock.Any(), gomock.Any()).
		Return(driver, nil)
	deps.banSvc.EXPECT().Unban(gomock.Any(), driver, gomock.Any())

	api.UnbanDriver(gctx)
	require.Equal(t, http.StatusOK, recorder.Code, recorder.Body.String())
	finish()
}

func TestDriverAdminAPI_SetHaveBoxStatus(t *testing.T) {
	t.Parallel()

	req := func(req *SetHaveBoxRequst) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", "/v1/admin/bulk/drivers/setHaveBox", testutil.JSON(req))
	}

	t.Run("set haveBox to true", func(tt *testing.T) {
		tt.Parallel()

		driverList := []string{"X001", "X002"}
		driverReq := &SetHaveBoxRequst{
			DriverIDs: driverList,
			HaveBox:   true,
		}

		ctx, recorder := req(driverReq)
		api, deps, finish := newTestDriverAdminAPI(t)
		deps.driverRepo.EXPECT().
			MultipleUpdateHaveBox(gomock.Any(), driverList, true).
			Return(nil)

		api.SetHaveBoxStatus(ctx)
		require.Equal(t, http.StatusNoContent, recorder.Code, recorder.Body.String())
		finish()
	})

	t.Run("set haveBox failed", func(tt *testing.T) {
		tt.Parallel()

		driverList := []string{"X001", "X002"}
		driverReq := &SetHaveBoxRequst{
			DriverIDs: driverList,
			HaveBox:   true,
		}

		ctx, recorder := req(driverReq)
		api, deps, finish := newTestDriverAdminAPI(t)
		deps.driverRepo.EXPECT().
			MultipleUpdateHaveBox(gomock.Any(), driverList, true).
			Return(errors.New("mock error"))

		api.SetHaveBoxStatus(ctx)
		var err absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "mock error", err.Message)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)

		finish()
	})

	t.Run("set haveBox failed with empty driverIds", func(tt *testing.T) {
		tt.Parallel()

		var driverList []string
		driverReq := &SetHaveBoxRequst{
			DriverIDs: driverList,
			HaveBox:   true,
		}

		ctx, recorder := req(driverReq)
		api, _, finish := newTestDriverAdminAPI(t)

		api.SetHaveBoxStatus(ctx)
		var err absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &err)
		require.Equal(tt, "cannot unmarshaling json", err.Message)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)

		finish()
	})
}

func TestDriverAdminAPI_BulkAssignReviewerToDriver(t *testing.T) {
	t.Parallel()

	req := func(req *BulkAssignReviewerRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("PUT", "/v1/admin/bulk/drivers/assign-reviewer", testutil.JSON(req))
	}

	t.Run("assign reviewer to driver successfully", func(tt *testing.T) {
		tt.Parallel()

		driverList := []string{"X001", "X002"}
		driverReq := &BulkAssignReviewerRequest{
			DriverIDs: driverList,
			Reviewer:  "reviewer",
		}

		ctx, recorder := req(driverReq)
		api, deps, finish := newTestDriverAdminAPI(t)
		deps.driverRepo.EXPECT().
			FindDriverIDs(gomock.Any(), driverList).
			Return([]model.Driver{{DriverID: "LMDKNBNK1"}, {DriverID: "LMDKNBNK2"}}, nil)
		deps.driverRepo.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			Return(nil).Times(2)

		api.BulkAssignReviewer(ctx)
		require.Equal(t, http.StatusNoContent, recorder.Code, recorder.Body.String())
		finish()
	})

	t.Run("should return error when driver list size exceed limit", func(tt *testing.T) {
		tt.Parallel()

		driverList := make([]string, 101)
		for i := range driverList {
			driverList[i] = "fake"
		}

		driverReq := &BulkAssignReviewerRequest{
			DriverIDs: driverList,
			Reviewer:  "reviewer",
		}

		ctx, recorder := req(driverReq)
		api, _, finish := newTestDriverAdminAPI(t)

		api.BulkAssignReviewer(ctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		finish()
	})

	t.Run("should return error when error during find driver", func(tt *testing.T) {
		tt.Parallel()

		driverList := []string{"X001", "X002"}
		driverReq := &BulkAssignReviewerRequest{
			DriverIDs: driverList,
			Reviewer:  "reviewer",
		}

		ctx, recorder := req(driverReq)
		api, deps, finish := newTestDriverAdminAPI(t)
		deps.driverRepo.EXPECT().
			FindDriverIDs(gomock.Any(), driverList).
			Return(nil, errors.New("error"))

		api.BulkAssignReviewer(ctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
		finish()
	})

	t.Run("should return 200 when error during update driver", func(tt *testing.T) {
		tt.Parallel()

		driverList := []string{"X001", "X002"}
		driverReq := &BulkAssignReviewerRequest{
			DriverIDs: driverList,
			Reviewer:  "reviewer",
		}

		ctx, recorder := req(driverReq)
		api, deps, finish := newTestDriverAdminAPI(t)
		deps.driverRepo.EXPECT().
			FindDriverIDs(gomock.Any(), driverList).
			Return([]model.Driver{{DriverID: "LMDKNBNK1"}, {DriverID: "LMDKNBNK2"}}, nil)
		deps.driverRepo.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			Return(errors.New("error")).Times(2)

		api.BulkAssignReviewer(ctx)
		require.Equal(tt, http.StatusNoContent, recorder.Code)
		finish()
	})
}

func TestDriverAdminAPI_ReactivateDriver(t *testing.T) {
	t.Parallel()

	req := func(drvId string, req *ReactivateDriverReq) (*gin.Context, *httptest.ResponseRecorder) {
		url := fmt.Sprintf("/v1/admin/drivers/%s/reactivate", drvId)
		return testutil.TestRequestContext("PUT", url, testutil.JSON(req))
	}

	t.Run("should be reject driver status not deactivated", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(t)
		defer finish()
		drvId := "driver-1"
		reactivateDriverReq := &ReactivateDriverReq{
			Requester: "<EMAIL>",
		}
		ctx, recorder := req(drvId, reactivateDriverReq)
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver-1", Status: model.StatusOnline}, nil)

		api.ReactivateDriver(ctx)

		require.Equal(tt, 400, recorder.Code)
	})

	t.Run("should be reject when exists the driver in registration process", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(t)
		defer finish()
		drvId := "driver-1"
		reactivateDriverReq := &ReactivateDriverReq{
			Requester: "<EMAIL>",
		}
		ctx, recorder := req(drvId, reactivateDriverReq)
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver-1", Status: model.StatusDeactivated}, nil)
		deps.driverRegistrationRepo.EXPECT().IsExistsByLineUid(gomock.Any(), gomock.Any()).Return(true)

		api.ReactivateDriver(ctx)

		require.Equal(tt, 400, recorder.Code)
	})

	t.Run("should be reactivate driver success", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(t)
		defer finish()
		drvId := "driver-1"
		reactivateDriverReq := &ReactivateDriverReq{
			Requester: "<EMAIL>",
		}
		ctx, recorder := req(drvId, reactivateDriverReq)
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(&model.Driver{DriverID: "driver-1", Status: model.StatusDeactivated}, nil)
		deps.banSvc.EXPECT().BanAndSaveHistory(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.driverSvc.EXPECT().AssignUobRefToDriver(gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRegistrationRepo.EXPECT().IsExistsByLineUid(gomock.Any(), gomock.Any()).Return(false)

		api.ReactivateDriver(ctx)

		require.Equal(tt, 204, recorder.Code)
	})
}

func OriginalDriverRequestUpdateProfile() *model.Driver {
	expirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
	newExpirationDate := time.Date(2022, 1, 1, 1, 1, 1, 1, time.UTC)

	vehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
	newVerhicleRegistrationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
	legislationExpiredDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
	newLegislationExpiredDate := time.Date(2022, 1, 1, 1, 1, 1, 1, time.UTC)
	return &model.Driver{
		BaseDriver: model.BaseDriver{
			CitizenIDCardPhotoURL: "original-id-card-photo-url",
			CitizenIDExpiredDate:  &expirationDate,
			AvatarURL:             "original-avatar-photo-url",
			DriverLicense: model.DriverLicenseInfo{
				ID:             crypt.NewLazyEncryptedString("original-license-id"),
				ExpirationDate: &expirationDate,
				PhotoURL:       "original-license-photo-url",
			},
			Vehicle: model.VehicleInfo{
				RegistrationDate:       &vehicleRegistrationDate,
				RegistrationPhotoURL:   "original-vehicle-registration-url",
				PhotoURL:               "original-vehicle-photo-url",
				PlateNumber:            crypt.NewLazyEncryptedString("original-plate-number"),
				LegislationPhotoURL:    "original-vehicle-legislation-url",
				LendingVehiclePhotoURL: "original-vehicle-lending-vehicle-url",
				LegislationExpiredDate: &legislationExpiredDate,
			},
		},
		RequestUpdateProfile: []model.RequestUpdateProfile{
			{
				CitizenIdCardPhotoURL: "original-id-card-photo-url",
				CitizenIdExpiredDate:  &newExpirationDate,
				DriverLicense: model.DriverLicenseInfo{
					ID:             crypt.NewLazyEncryptedString("new-license-id"),
					ExpirationDate: &newExpirationDate,
					PhotoURL:       "new-license-photo-url",
				},
				AvatarURL: "new-avatar-photo-url",
				Status:    "UPDATE_PENDING",
				Vehicle: model.VehicleInfo{
					RegistrationDate:       &newVerhicleRegistrationDate,
					RegistrationPhotoURL:   "new-vehicle-registration-url",
					PhotoURL:               "new-vehicle-photo-url",
					PlateNumber:            crypt.NewLazyEncryptedString("new-plate-number"),
					LegislationPhotoURL:    "new-vehicle-legislation-url",
					LendingVehiclePhotoURL: "new-vehicle-lending-vehicle-url",
					LegislationExpiredDate: &newLegislationExpiredDate,
				},
			},
		},
	}
}

func TestDriverAdminAPI_ApproveRequestUpdateProfile(t *testing.T) {
	t.Parallel()
	req := func(req *ApproveRequestUpdateProfileRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("PUT", "driver-request-update-profile/driver-01/approve", testutil.JSON(req))
	}
	t.Run("Should be approve request update profile success", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()
		gctx, recorder := req(&ApproveRequestUpdateProfileRequest{
			Requester: "<EMAIL>",
		})
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(OriginalDriverRequestUpdateProfile(), nil)
		deps.driverRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		api.ApproveRequestUpdateProfile(gctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("Should be error when current request update not update pending", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()
		gctx, recorder := req(&ApproveRequestUpdateProfileRequest{
			Requester: "<EMAIL>",
		})
		driverProfile := OriginalDriverRequestUpdateProfile()
		driverProfile.RequestUpdateProfile[0].Status = model.ProfileStatusCompleted
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(driverProfile, nil)

		api.ApproveRequestUpdateProfile(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("Should be error with no pending update", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()
		gctx, recorder := req(&ApproveRequestUpdateProfileRequest{
			Requester: "<EMAIL>",
		})
		driverProfile := OriginalDriverRequestUpdateProfile()
		driverProfile.RequestUpdateProfile = []model.RequestUpdateProfile{}
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(driverProfile, nil)

		api.ApproveRequestUpdateProfile(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestDriverAdminAPI_RejectRequestUpdateProfile(t *testing.T) {
	t.Parallel()
	req := func(req *RejectRequestUpdateProfileRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("PUT", "driver-request-update-profile/driver-01/reject", testutil.JSON(req))
	}
	t.Run("Should be reject request update profile success", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()
		gctx, recorder := req(&RejectRequestUpdateProfileRequest{
			Requester: "<EMAIL>",
			Remark:    "รูปใบขับขี่ไม่ชัดเชน กรุณาอัพโหลดเข้ามาอีกครั้ง",
		})
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(OriginalDriverRequestUpdateProfile(), nil)
		deps.driverRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		api.RejectRequestUpdateProfile(gctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("Should be error when current request update not update pending", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()
		gctx, recorder := req(&RejectRequestUpdateProfileRequest{
			Requester: "<EMAIL>",
			Remark:    "รูปใบขับขี่ไม่ชัดเชน กรุณาอัพโหลดเข้ามาอีกครั้ง",
		})
		driverProfile := OriginalDriverRequestUpdateProfile()
		driverProfile.RequestUpdateProfile[0].Status = model.ProfileStatusCompleted
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(driverProfile, nil)

		api.RejectRequestUpdateProfile(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("Should be error with no pending update", func(tt *testing.T) {
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()
		gctx, recorder := req(&RejectRequestUpdateProfileRequest{
			Requester: "<EMAIL>",
			Remark:    "รูปใบขับขี่ไม่ชัดเชน กรุณาอัพโหลดเข้ามาอีกครั้ง",
		})
		driverProfile := OriginalDriverRequestUpdateProfile()
		driverProfile.RequestUpdateProfile = []model.RequestUpdateProfile{}
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(driverProfile, nil)

		api.RejectRequestUpdateProfile(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestDriverAdminAPI_GetARCRStatistic(t *testing.T) {
	t.Parallel()

	req := func(driverID string, date string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder().
			SetGET("/v1/admin/drivers/%v/statistic/arcr?date=%v", driverID, date).
			SetGinParams(gin.Params{
				gin.Param{Key: "driver_id", Value: driverID},
			})

		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("Should return 200 success", func(tt *testing.T) {
		// Given
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022")
		deps.driverOrderInfoRepo.EXPECT().GetDailyCounts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.DriverOrderInfo{
			DriverID: "driver-A",
			DailyCounts: []model.DailyCount{
				{
					Year:  2022,
					Month: 3,
					Day:   23,
				},
			},
		}, nil)

		// When
		api.GetARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("Should return 400 bad request when not supply date query param", func(tt *testing.T) {
		// Given
		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "")

		// When
		api.GetARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return 404 not found when driver_order_info not found for this driver", func(tt *testing.T) {
		// Given
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022")
		deps.driverOrderInfoRepo.EXPECT().GetDailyCounts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, repository.ErrNotFound)

		// When
		api.GetARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("Should return 404 not found when driver_order_info found but daily count donesn't have selected date for this driver", func(tt *testing.T) {
		// Given
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022")
		deps.driverOrderInfoRepo.EXPECT().GetDailyCounts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.DriverOrderInfo{
			DriverID: "driver-A",
			DailyCounts: []model.DailyCount{
				{
					Year:  2022,
					Month: 3,
					Day:   22,
				},
			},
		}, nil)

		// When
		api.GetARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusNotFound, recorder.Code)
	})

	t.Run("Should return 500 internal server error when driverOrderInfoRepo return unhandled error", func(tt *testing.T) {
		// Given
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022")
		deps.driverOrderInfoRepo.EXPECT().GetDailyCounts(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("error"))

		// When
		api.GetARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestDriverAdminAPI_UpdateARCRStatistic(t *testing.T) {
	t.Parallel()

	req := func(driverID string, date string, req interface{}, isAdmin bool) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder().SetPUT("/v1/admin/drivers/%v/statistic/arcr", driverID).SetGinParams(gin.Params{
			gin.Param{Key: "driver_id", Value: driverID},
		}).Body().JSON(req).Build()

		if isAdmin {
			gctx.FakeAdminCtxAuthorized("admin", fake.CharactersN(20), "<EMAIL>")
		}

		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	intPtr := func(i int) *int { return &i }

	t.Run("Should return 204 when success", func(tt *testing.T) {
		// Given
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022", UpdateARCRStatisticRequest{
			Date:                 "23/03/2022",
			Service:              "food",
			AutoAssignedAccepted: intPtr(100),
			AutoAssigned:         intPtr(100),
			CancelledNotFree:     intPtr(100),
			Accepted:             intPtr(100),
		}, true)

		deps.busSvc.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		// When

		api.UpdateARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("Should return 400 bad request when not supply driver_id", func(tt *testing.T) {
		// Given
		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("", "23/03/2022", UpdateARCRStatisticRequest{
			Date:                 "23/03/2022",
			Service:              "food",
			AutoAssignedAccepted: intPtr(100),
			AutoAssigned:         intPtr(100),
			CancelledNotFree:     intPtr(100),
			Accepted:             intPtr(100),
		}, true)

		// When
		api.UpdateARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return 400 bad request when not supply a correct service type", func(tt *testing.T) {
		// Given
		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022", UpdateARCRStatisticRequest{
			Date:                 "23/03/2022",
			Service:              "xxx",
			AutoAssignedAccepted: intPtr(100),
			AutoAssigned:         intPtr(100),
			CancelledNotFree:     intPtr(100),
			Accepted:             intPtr(100),
		}, true)

		// When
		api.UpdateARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return 400 bad request when date is invalid format", func(tt *testing.T) {
		// Given
		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022", UpdateARCRStatisticRequest{
			Date:                 "23-03-2022",
			Service:              "food",
			AutoAssignedAccepted: intPtr(100),
			AutoAssigned:         intPtr(100),
			CancelledNotFree:     intPtr(100),
			Accepted:             intPtr(100),
		}, true)

		// When
		api.UpdateARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return 400 bad request when invalid request format", func(tt *testing.T) {
		// Given
		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022", nil, true)

		// When
		api.UpdateARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
	})

	t.Run("Should return 401 unauthorized when request by non-admin user", func(tt *testing.T) {
		// Given
		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022", UpdateARCRStatisticRequest{
			Date:                 "23/03/2022",
			Service:              "food",
			AutoAssignedAccepted: intPtr(100),
			AutoAssigned:         intPtr(100),
			CancelledNotFree:     intPtr(100),
			Accepted:             intPtr(100),
		}, false)

		// When
		api.UpdateARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusUnauthorized, recorder.Code)
	})

	t.Run("Should return 500 internal server error when bus service broken", func(tt *testing.T) {
		// Given
		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		gctx, recorder := req("driver-A", "23/03/2022", UpdateARCRStatisticRequest{
			Date:                 "23/03/2022",
			Service:              "food",
			AutoAssignedAccepted: intPtr(100),
			AutoAssigned:         intPtr(100),
			CancelledNotFree:     intPtr(100),
			Accepted:             intPtr(100),
		}, true)

		deps.busSvc.EXPECT().Publish(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error"))

		// When
		api.UpdateARCRStatistic(gctx)

		// Then
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})
}

func TestDriverAdminAPI_BanUntilValidation_BulkBanDriver(t *testing.T) {
	t.Parallel()

	t.Run("bulk upload ban success", func(tt *testing.T) {
		dateTime := timeutil.BangkokNow().AddDate(0, 0, 1)
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/ban").AdminAuthorized("<EMAIL>", middlewares.RoleBanDriver)
			gctx.Body().MultipartForm().
				String("reason", "ban").
				String("type", string(model.BanTypePermanent)).
				String("category", "ban").
				String("createdBy", "admin").
				String("banAmount", "1").
				String("bannedUntil", dateTime.Format("2006-01-02T15:04:05Z")).
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID
LMDZ9YHH5
LMDMF0SPJ
LMDSAC9I3`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		drivers := []model.Driver{
			{
				DriverID: "LMDZ9YHH5",
			},
			{
				DriverID: "LMDMF0SPJ",
			},
			{
				DriverID: "LMDSAC9I3",
			},
		}

		deps.driverRepo.EXPECT().FindDriverIDs(gomock.Any(), gomock.Any(), gomock.Any()).Return(drivers, nil)
		deps.banSvc.EXPECT().BanWithMetadata(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(len(drivers))
		deps.shiftSvc.EXPECT().RemoveShiftByTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil).Times(len(drivers))

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkBanDriver(gctx)
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)

		var actual BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, len(actual.Successes), len(drivers))
		require.Equal(tt, len(actual.Failures), 0)
	})

	t.Run("bulk upload ban fails", func(tt *testing.T) {
		// add time is in the past
		dateTime := timeutil.BangkokNow().AddDate(0, 0, -1)
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/ban").AdminAuthorized("<EMAIL>", middlewares.RoleBanDriver)
			gctx.Body().MultipartForm().
				String("reason", "ban").
				String("type", "ban").
				String("category", "ban").
				String("createdBy", "admin").
				String("banAmount", "1").
				String("bannedUntil", dateTime.Format("2006-01-02T15:04:05Z")).
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID
	LMDZ9YHH5
	LMDMF0SPJ
	LMDSAC9I3`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		drivers := []model.Driver{
			{
				DriverID: "LMDZ9YHH5",
			},
			{
				DriverID: "LMDMF0SPJ",
			},
			{
				DriverID: "LMDSAC9I3",
			},
		}

		deps.driverRepo.EXPECT().FindDriverIDs(gomock.Any(), gomock.Any(), gomock.Any()).Return(drivers, nil)
		deps.banSvc.EXPECT().BanWithMetadata(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("time for banValidate until should be future")).Times(len(drivers))
		deps.banSvc.EXPECT().Ban(gomock.Any(), gomock.Any(), gomock.Any()).Times(0)
		deps.banHistRepo.EXPECT().Record(gomock.Any(), gomock.Any()).Times(0)

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkBanDriver(gctx)
		wg.Wait()

		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, len(actual.Successes), 0)
		require.Equal(tt, len(actual.Failures), len(drivers))
	})

	t.Run("bulk upload effective time ban success", func(tt *testing.T) {
		dateTime := timeutil.BangkokNow().AddDate(0, 0, 1)
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/ban").AdminAuthorized("<EMAIL>", middlewares.RoleBanDriver)
			gctx.Body().MultipartForm().
				String("reason", "ban").
				String("type", string(model.BanTypePermanent)).
				String("category", "ban").
				String("createdBy", "admin").
				String("banAmount", "1").
				String("bannedUntil", dateTime.Format("2006-01-02T15:04:05Z")).
				String("effectiveTime", dateTime.Format("2006-01-02T15:04:05Z")).
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID
LMDZ9YHH5
LMDMF0SPJ
LMDSAC9I3`

		gctx, recorder := makeReq(content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.banEffectiveTimeRepo.EXPECT().
			Create(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, src model.BanEffectiveTime, opts ...repository.Option) error {
				expected := model.BanEffectiveTime{
					BanType:    model.BanTypePermanent,
					BanReasons: nil,
					Detail: model.BanDriverDetail{
						CreatedBy:                 "admin",
						Type:                      string(model.BanTypePermanent),
						MessageToDriver:           "",
						BannedUntil:               src.Detail.BannedUntil,
						Category:                  "ban",
						Reason:                    "ban",
						Url:                       "",
						RetrainingTitle:           "",
						RetrainingMessageToDriver: "",
					},
					Status:          model.BanEffectiveWaiting,
					EffectDriverIDs: []string{"LMDZ9YHH5", "LMDMF0SPJ", "LMDSAC9I3"},
					Remarks:         nil,
					CreatedAt:       src.CreatedAt,
					EffectiveTime:   src.EffectiveTime,
				}
				require.Equal(tt, expected, src)
				return nil
			})
		api.BulkBanDriver(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code)
	})

	t.Run("bulk upload effective time ban fail time passed", func(tt *testing.T) {
		dateTime := time.Now().UTC().Add(time.Hour * -1)
		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/ban").AdminAuthorized("<EMAIL>", middlewares.RoleBanDriver)
			gctx.Body().MultipartForm().
				String("reason", "ban").
				String("type", string(model.BanTypePermanent)).
				String("category", "ban").
				String("createdBy", "admin").
				String("banAmount", "1").
				String("bannedUntil", dateTime.Format("2006-01-02T15:04:05Z")).
				String("effectiveTime", dateTime.Add(time.Hour*-1).Format("2006-01-02T15:04:05Z")).
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID
LMDZ9YHH5
LMDMF0SPJ
LMDSAC9I3`

		gctx, recorder := makeReq(content)

		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		api.BulkBanDriver(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var errRes absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &errRes)
		require.Equal(tt, "INVALID_REQUEST", errRes.Code)
		require.Contains(tt, errRes.Message, "time already passed")
	})

	t.Run("bulk upload effective time ban fail time passed after ", func(tt *testing.T) {
		dateTime := time.Date(2000, 1, 1, 1, 40, 0, 0, time.UTC)
		timeutils.FreezeWithTime(dateTime.Unix() * 1000)
		defer timeutils.Unfreeze()

		makeReq := func(content string) (*gin.Context, *httptest.ResponseRecorder) {
			gctx := testutil.NewContextWithRecorder()
			gctx.SetPUT("/bulk/drivers/ban").AdminAuthorized("<EMAIL>", middlewares.RoleBanDriver)
			gctx.Body().MultipartForm().
				String("reason", "ban").
				String("type", string(model.BanTypePermanent)).
				String("category", "ban").
				String("createdBy", "admin").
				String("banAmount", "1").
				String("bannedUntil", dateTime.Format("2006-01-02T15:04:05Z")).
				String("effectiveTime", dateTime.Add(time.Minute*2).Format("2006-01-02T15:04:05Z")).
				File("file", "file.csv", content).Build()
			return gctx.GinCtx(), gctx.ResponseRecorder
		}

		content := `Driver ID
LMDZ9YHH5
LMDMF0SPJ
LMDSAC9I3`

		gctx, recorder := makeReq(content)

		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		api.BulkBanDriver(gctx)
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var errRes absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &errRes)
		require.Equal(tt, "INVALID_REQUEST", errRes.Code)
		require.Contains(tt, errRes.Message, "time already passed")
	})
}

func TestDriverAdminAPI_BanDriverWithMetadata(t *testing.T) {
	t.Parallel()

	req := func(id string, req BanDriverWithMetadataRequest) (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("POST", fmt.Sprintf("/v1/admin/drivers/%s/ban-metadata", id), testutil.JSON(req))
	}

	t.Run("should return 200 and ban driver correctly", func(tt *testing.T) {
		tt.Parallel()

		t := time.Date(2023, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())
		gctx, recorder := req("driverID", BanDriverWithMetadataRequest{
			BanType:                   model.BanTypePermanent,
			BannedUntil:               &t,
			MessageToDriver:           "msg",
			Url:                       "url",
			Category:                  "category",
			Reason:                    "reason",
			RetrainingTitle:           "retrain",
			RetrainingMessageToDriver: "retrain",
		})

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(&model.Driver{}, nil)
		deps.banSvc.EXPECT().GetBanReasons(gomock.Any(), gomock.Any()).Return([]*model.BanReason{}, nil)
		deps.banSvc.EXPECT().BanWithMetadata(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.shiftSvc.EXPECT().RemoveShiftByTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{}, nil)

		api.BanDriverWithMetadata(gctx)

		require.Equal(tt, http.StatusNoContent, recorder.Code)
	})

	t.Run("should return 400 and if has on-going offline later ban", func(tt *testing.T) {
		tt.Parallel()

		t := time.Date(2023, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())
		gctx, recorder := req("driverID", BanDriverWithMetadataRequest{
			BanType:                   model.BanTypePermanent,
			BannedUntil:               &t,
			MessageToDriver:           "msg",
			Url:                       "url",
			Category:                  "category",
			Reason:                    "reason",
			RetrainingTitle:           "retrain",
			RetrainingMessageToDriver: "retrain",
		})

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		bannedUntil := time.Now().Add(30 * time.Minute)
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(&model.Driver{
			Status:      model.StatusBanned,
			Reason:      model.ReasonOfflineLater,
			BannedUntil: timeutil.NilIfZero(&bannedUntil),
		}, nil)

		api.BanDriverWithMetadata(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, absintheApi.ERRCODE_INVALID_REQUEST, actual.Code)
		require.Equal(tt, "driver has on-going offline later ban, please try again later or refresh your screen", actual.Message)
	})

	t.Run("should return 400 and if has on-going temporary ban", func(tt *testing.T) {
		tt.Parallel()

		t := time.Date(2023, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())
		gctx, recorder := req("driverID", BanDriverWithMetadataRequest{
			BanType:                   model.BanTypePermanent,
			BannedUntil:               &t,
			MessageToDriver:           "msg",
			Url:                       "url",
			Category:                  "category",
			Reason:                    "reason",
			RetrainingTitle:           "retrain",
			RetrainingMessageToDriver: "retrain",
		})

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		bannedUntil := time.Now().Add(24 * time.Hour)
		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(&model.Driver{
			Status:      model.StatusBanned,
			BannedUntil: timeutil.NilIfZero(&bannedUntil),
		}, nil)

		api.BanDriverWithMetadata(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, absintheApi.ERRCODE_INVALID_REQUEST, actual.Code)
		require.Equal(tt, "driver has on-going ban, please try again later or refresh your screen", actual.Message)
	})

	t.Run("should return 400 and if has on-going permanent ban", func(tt *testing.T) {
		tt.Parallel()

		t := time.Date(2023, 1, 1, 10, 0, 0, 0, timeutil.BangkokLocation())
		gctx, recorder := req("driverID", BanDriverWithMetadataRequest{
			BanType:                   model.BanTypePermanent,
			BannedUntil:               &t,
			MessageToDriver:           "msg",
			Url:                       "url",
			Category:                  "category",
			Reason:                    "reason",
			RetrainingTitle:           "retrain",
			RetrainingMessageToDriver: "retrain",
		})

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.driverRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any()).Return(&model.Driver{
			Status:      model.StatusBanned,
			BannedUntil: timeutil.NilIfZero(nil),
		}, nil)

		api.BanDriverWithMetadata(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, absintheApi.ERRCODE_INVALID_REQUEST, actual.Code)
		require.Equal(tt, "driver has on-going ban, please try again later or refresh your screen", actual.Message)
	})
}

func TestDriverAdminAPI_GetDriverRequestUpdateProfileSections(t *testing.T) {
	t.Parallel()
	type ListRequestUpdateProfileSectionRes struct {
		CountTotal int                                 `json:"countTotal"`
		Data       []DriverRequestUpdateProfileSection `json:"data"`
	}

	req := func() (*gin.Context, *httptest.ResponseRecorder) {
		return testutil.TestRequestContext("GET", "/v1/admin/driver-request-update-profile/sections", nil)
	}

	t.Run("should return 200 and driver request update profile sections", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := req()

		api, deps, finish := newTestDriverAdminAPI(
			tt,
			WithAtomicAdminConfig(&config.AtomicAdminConfig{
				Config: config.AdminConfig{
					DisabledUpdateDriverProfileSection: types.NewStringSet(),
				},
			}),
		)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().GetAll(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DriverRequestUpdateProfileSection{{SectionLabel: "Section1"}}, nil)

		api.GetDriverRequestUpdateProfileSections(gctx)

		var actual ListRequestUpdateProfileSectionRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, 1, len(actual.Data))
		require.Equal(tt, "Section1", actual.Data[0].Name)
	})

	t.Run("should return 500 when get driver request update profile sections error", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := req()

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().GetAll(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DriverRequestUpdateProfileSection{}, errors.New("error"))

		api.GetDriverRequestUpdateProfileSections(gctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return 500 if the admin config is nil", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := req()

		api, deps, finish := newTestDriverAdminAPI(
			tt,
		)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().GetAll(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DriverRequestUpdateProfileSection{{SectionLabel: "Section1"}}, nil)

		api.GetDriverRequestUpdateProfileSections(gctx)
		require.Equal(tt, http.StatusInternalServerError, recorder.Code)
	})

	t.Run("should return 200 without the excluded section", func(tt *testing.T) {
		tt.Parallel()

		gctx, recorder := req()

		sectionList := []model.DriverRequestUpdateProfileSection{
			{
				SectionLabel: "Section1",
				ID:           primitive.NewObjectID(),
			},
			{
				SectionLabel: "Section2",
				ID:           primitive.NewObjectID(),
			},
			{
				SectionLabel: "Section3",
				ID:           primitive.NewObjectID(),
			},
			{
				SectionLabel: "Section4",
				ID:           primitive.NewObjectID(),
			},
		}

		disableList := types.NewStringSet(sectionList[0].ID.Hex(), sectionList[2].ID.Hex())

		api, deps, finish := newTestDriverAdminAPI(
			tt,
			WithAtomicAdminConfig(&config.AtomicAdminConfig{
				Config: config.AdminConfig{
					DisabledUpdateDriverProfileSection: disableList,
				},
			}),
		)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().GetAll(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(sectionList, nil)

		api.GetDriverRequestUpdateProfileSections(gctx)

		var actual ListRequestUpdateProfileSectionRes
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, 2, len(actual.Data))
		require.Equal(tt, sectionList[1].ID.Hex(), actual.Data[0].ID)
		require.Equal(tt, sectionList[3].ID.Hex(), actual.Data[1].ID)
	})
}

func TestDriverAdminAPI_BulkRequestUpdateProfiles(t *testing.T) {
	t.Parallel()

	makeReq := func(sectionIds []string, content string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/driver-request-update-profile/upload").AdminAuthorized("<EMAIL>")
		sectionIDsJson, _ := json.Marshal(sectionIds)
		gctx.Body().MultipartForm().
			String("sectionIDs", string(sectionIDsJson)).
			File("file", "file.csv", content).Build()
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	content := `Driver ID
Driver_A`

	expectedDriverProfileStatus := map[string]model.ProfileStatus{
		"Driver_A": model.ProfileStatusRequestUpdate,
	}

	sectionID1, _ := primitive.ObjectIDFromHex("0123456789101112131415a1")
	sectionID2, _ := primitive.ObjectIDFromHex("0123456789101112131415a2")
	sectionIDs := []string{"0123456789101112131415a1", "0123456789101112131415a2"}

	sections := []model.DriverRequestUpdateProfileSection{
		{
			ID:           sectionID1,
			SectionLabel: "Section_1",
			Fields: []model.DriverRequestUpdateProfileField{
				{
					Field:    "Field_1",
					Label:    "Field_1",
					Required: true,
				},
			},
		},
		{
			ID:           sectionID2,
			SectionLabel: "Section_2",
			Fields: []model.DriverRequestUpdateProfileField{
				{
					Field:    "Field_1",
					Label:    "Field_1",
					Required: true,
				},
				{
					Field:    "Field_2",
					Label:    "Field_2",
					Required: true,
				},
			},
		},
	}

	t.Run("should return 200 and success ", func(tt *testing.T) {
		tt.Parallel()

		driver := model.DriverRequestUpdateProfile{
			DriverID:             "Driver_A",
			ProfileStatus:        model.ProfileStatusCompleted,
			RequestUpdateProfile: []model.RequestUpdateProfile{},
		}

		gctx, recorder := makeReq([]string{sectionIDs[0], sectionIDs[1]}, content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(sections, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(nil, nil)
		deps.driverRepo.EXPECT().GetProfileByIDForRequestUpdate(gomock.Any(), gomock.Any()).Return(driver, nil)
		deps.driverRepo.EXPECT().UpdateProfileStatus(gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, driverID string, profileStatus model.ProfileStatus) error {
				require.Equal(tt, expectedDriverProfileStatus[driverID], model.ProfileStatusRequestUpdate)
				return nil
			})

		deps.requestUpdateProfileRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil)

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkRequestUpdateProfiles(gctx)
		wg.Wait()

		var actual BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, len(actual.Successes), 1)
	})

	t.Run("should return 200 bulk upload correctly and change status delete to the old request", func(tt *testing.T) {
		tt.Parallel()

		// Status is not equal Complete, so it means it has the old request to update.
		// Should check the same section and set status to delete, if it duplicated with the old.
		driver := model.DriverRequestUpdateProfile{
			DriverID:             "Driver_A",
			ProfileStatus:        model.ProfileStatusRequestUpdate,
			RequestUpdateProfile: []model.RequestUpdateProfile{},
		}

		oldRequests := []model.RequestUpdateDriverProfile{
			{
				DriverID:  "Driver_A",
				SectionID: sectionID1.Hex(),
				Status:    model.RequestProfileStatusUpdatePending,
			},
		}
		gctx, recorder := makeReq([]string{sectionIDs[0]}, content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(sections, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.driverRepo.EXPECT().GetProfileByIDForRequestUpdate(gomock.Any(), gomock.Any()).Return(driver, nil)
		deps.requestUpdateProfileRepo.EXPECT().
			FindInProcessRequestByDriverIDAndSectionIDs(gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(oldRequests, nil)
		deps.requestUpdateProfileRepo.EXPECT().ReplaceAll(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, requestUpdateDriverProfiles []model.RequestUpdateDriverProfile) error {
			for index, requestUpdateDriverProfile := range requestUpdateDriverProfiles {
				require.Equal(tt, requestUpdateDriverProfile.DriverID, oldRequests[index].DriverID)
				require.Equal(tt, requestUpdateDriverProfile.Status, model.RequestProfileStatusCancel)
			}
			return nil
		})
		deps.requestUpdateProfileRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return(nil)

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkRequestUpdateProfiles(gctx)
		wg.Wait()

		var actual BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, len(actual.Successes), 1)
	})

	t.Run("should return 200 fail when Driver_A find in process request error", func(tt *testing.T) {
		tt.Parallel()

		driver := model.DriverRequestUpdateProfile{
			DriverID:             "Driver_A",
			ProfileStatus:        model.ProfileStatusUpdatePending,
			RequestUpdateProfile: []model.RequestUpdateProfile{},
		}

		gctx, recorder := makeReq([]string{sectionIDs[0]}, content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(sections, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(nil, nil)
		deps.driverRepo.EXPECT().GetProfileByIDForRequestUpdate(gomock.Any(), gomock.Any()).Return(driver, nil)
		deps.requestUpdateProfileRepo.EXPECT().
			FindInProcessRequestByDriverIDAndSectionIDs(gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(nil, errors.New("error"))

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkRequestUpdateProfiles(gctx)
		wg.Wait()

		var actual BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, len(actual.Successes), 0)
		require.Equal(tt, len(actual.Failures), 1)
		require.Equal(tt, actual.Failures[0], driver.DriverID)
	})

	t.Run("should return 200 fail when Driver_A not found", func(tt *testing.T) {
		tt.Parallel()

		driver := model.DriverRequestUpdateProfile{
			DriverID:             "Driver_A",
			ProfileStatus:        model.ProfileStatusUpdatePending,
			RequestUpdateProfile: []model.RequestUpdateProfile{},
		}

		gctx, recorder := makeReq([]string{sectionIDs[0]}, content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(sections, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(nil, nil)
		deps.driverRepo.EXPECT().GetProfileByIDForRequestUpdate(gomock.Any(), gomock.Any()).Return(model.DriverRequestUpdateProfile{}, errors.New("DRIVER NOT FOUND"))

		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkRequestUpdateProfiles(gctx)
		wg.Wait()

		var actual BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, len(actual.Successes), 0)
		require.Equal(tt, len(actual.Failures), 1)
		require.Equal(tt, actual.Failures[0], driver.DriverID)
	})

	// Old flow = driver can request to update
	t.Run("should return error when driver has already requested to update profile (old flow)", func(tt *testing.T) {
		tt.Parallel()

		// Status equal Complete then have no request to update
		driver := model.DriverRequestUpdateProfile{
			DriverID:      "Driver_A",
			ProfileStatus: model.ProfileStatusUpdatePending,
			RequestUpdateProfile: []model.RequestUpdateProfile{
				{
					EmergencyPhone: crypt.NewLazyEncryptedString("0800000000"),
				},
			},
		}

		gctx, recorder := makeReq([]string{sectionIDs[0]}, content)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.requestUpdateProfileSectionRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadSecondaryPreferred)).
			Return(sections, nil)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Times(1).Return(nil, nil)
		deps.driverRepo.EXPECT().GetProfileByIDForRequestUpdate(gomock.Any(), gomock.Any()).Return(driver, nil)
		wg := safe.CreateWaitGroupOnGctx(gctx)
		api.BulkRequestUpdateProfiles(gctx)
		wg.Wait()

		var actual BulkIDsResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, http.StatusOK, recorder.Code)
		require.Equal(tt, len(actual.Failures), 1)
	})
}

func TestDriverAdminAPI_BulkUpdateDriverARCR(t *testing.T) {
	req := func(content string, isAdmin bool) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/bulk/drivers/arcr")
		gctx.Body().MultipartForm().
			String("createdBy", "admin").
			File("file", "file.csv", content).Build()

		if isAdmin {
			gctx.FakeAdminCtxAuthorized("admin", fake.CharactersN(20), "<EMAIL>")
		}

		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	timeutils.Freeze()
	defer timeutils.Unfreeze()
	t.Run("bulk update driver arcr success", func(tt *testing.T) {
		// Given
		content := "Driver ID,Date Time,AR,CR\ndriver-A,2022-09-22,100,0\ndriver-B,2022-09-22,NO_CHANGE,0\ndriver-C,2022-09-22,100,NO_CHANGE"

		gctx, recorder := req(content, true)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.orderRepo.EXPECT().FindDriverOrderAutoAssignedCompleted(
			gomock.Any(),
			gomock.InAnyOrder([]string{"driver-A", "driver-B", "driver-C"}),
			gomock.Eq(timeutil.ConvertYYYYMMDDToDate("2022-09-22")),
			gomock.Eq(timeutil.ConvertYYYYMMDDToDate("2022-09-23")),
			gomock.Any(),
		).Return(model.CountDriverAutoAssignedRecords{
			{
				Driver:                     "driver-A",
				AutoAssignedCompletedCount: types.NewInt(1),
				Service:                    "food",
			},
			{
				Driver:                     "driver-B",
				AutoAssignedCompletedCount: types.NewInt(2),
				Service:                    "food",
			},
			{
				Driver:                     "driver-C",
				AutoAssignedCompletedCount: types.NewInt(3),
				Service:                    "mart",
			},
		}, nil)

		deps.busSvc.EXPECT().Publish(gomock.Any(), gomock.Eq(order.BusTopicDriverEventOrder), gomock.Any()).Times(12)

		// When
		api.BulkUpdateDriverARCR(gctx)

		// Then
		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual *BulkUpdateDriverARCRResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, len(actual.Successes), 3)
		require.Equal(tt, len(actual.Failures), 0)
	})

	t.Run("bulk update driver arcr by service type", func(tt *testing.T) {
		// Given
		content := "Driver ID,Date Time,AR,CR,Service\ndriver-A,2022-09-22,100,NO_CHANGE,food\ndriver-A,2022-09-22,NO_CHANGE,0,mart"

		gctx, recorder := req(content, true)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.orderRepo.EXPECT().FindDriverOrderAutoAssignedCompleted(
			gomock.Any(),
			gomock.InAnyOrder([]string{"driver-A"}),
			gomock.Eq(timeutil.ConvertYYYYMMDDToDate("2022-09-22")),
			gomock.Eq(timeutil.ConvertYYYYMMDDToDate("2022-09-23")),
			gomock.Any(),
		).Return(model.CountDriverAutoAssignedRecords{
			{
				Driver:                     "driver-A",
				AutoAssignedCompletedCount: types.NewInt(1),
				Service:                    "food",
			},
			{
				Driver:                     "driver-A",
				AutoAssignedCompletedCount: types.NewInt(1),
				Service:                    "mart",
			},
		}, nil)

		deps.busSvc.EXPECT().Publish(gomock.Any(), gomock.Eq(order.BusTopicDriverEventOrder), gomock.Any()).Times(2)

		// When
		api.BulkUpdateDriverARCR(gctx)

		// Then
		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual *BulkUpdateDriverARCRResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, len(actual.Successes), 2)
		require.Equal(tt, len(actual.Failures), 0)
	})

	t.Run("bulk update driver arcr by invalid service type", func(tt *testing.T) {
		// Given
		content := "Driver ID,Date Time,AR,CR,Service\ndriver-A,2022-09-22,100,NO_CHANGE,invalid"

		gctx, recorder := req(content, true)

		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		// When
		api.BulkUpdateDriverARCR(gctx)

		// Then
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, absintheApi.ERRCODE_INVALID_REQUEST, actual.Code)
		require.Equal(tt, "unable to parse row 1: invalid service type", actual.Message)
	})

	t.Run("failed unauthorized", func(tt *testing.T) {
		// Given
		content := "Driver ID,Date Time,AR,CR\ndriver-A,2022-09-22,100,0\ndriver-B,2022-09-22,NO_CHANGE,0\ndriver-C,2022-09-22,100,NO_CHANGE"

		gctx, recorder := req(content, false)

		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()

		// When
		api.BulkUpdateDriverARCR(gctx)

		// Then
		require.Equal(tt, http.StatusUnauthorized, recorder.Code)
	})

	t.Run("fail record on reason unable to find auto assigned count for driver", func(tt *testing.T) {
		// Given
		content := "Driver ID,Date Time,AR,CR\ndriver-A,2022-09-22,100,0\ndriver-B,2022-09-22,NO_CHANGE,0\ndriver-C,2022-09-22,100,NO_CHANGE"

		gctx, recorder := req(content, true)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.orderRepo.EXPECT().FindDriverOrderAutoAssignedCompleted(
			gomock.Any(),
			gomock.InAnyOrder([]string{"driver-A", "driver-B", "driver-C"}),
			gomock.Eq(timeutil.ConvertYYYYMMDDToDate("2022-09-22")),
			gomock.Eq(timeutil.ConvertYYYYMMDDToDate("2022-09-23")),
			gomock.Any(),
		).Return(model.CountDriverAutoAssignedRecords{
			{
				Driver:                     "driver-A",
				AutoAssignedCompletedCount: types.NewInt(1),
				Service:                    "food",
			},
			{
				Driver:                     "driver-B",
				AutoAssignedCompletedCount: types.NewInt(2),
				Service:                    "mart",
			},
		}, nil)

		deps.busSvc.EXPECT().Publish(gomock.Any(), gomock.Eq(order.BusTopicDriverEventOrder), gomock.Any()).Times(8)

		// When
		api.BulkUpdateDriverARCR(gctx)

		// Then
		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual *BulkUpdateDriverARCRResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, 2, len(actual.Successes))
		require.Equal(tt, 1, len(actual.Failures))
		require.Equal(tt, 3, actual.Failures[0].Row)
		require.Equal(tt, "unable to find auto assigned count for driver", actual.Failures[0].Reason)
		require.Equal(tt, "driver-C", actual.Failures[0].DriverId)
	})

	t.Run("fail exceed max csv records", func(tt *testing.T) {
		// Given
		content := "Driver ID,Date Time,AR,CR\ndriver-A,2022-09-22,100,0\ndriver-B,2022-09-22,NO_CHANGE,0\ndriver-C,2022-09-22,100,NO_CHANGE"

		gctx, recorder := req(content, true)

		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()
		api.Cfg.BulkUpdateDriverARCRMaxCSVRows = 0

		// When
		api.BulkUpdateDriverARCR(gctx)

		// Then
		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, absintheApi.ERRCODE_INVALID_REQUEST, actual.Code)
		require.Equal(tt, "over maximum rows which max row size is 0", actual.Message)
	})

	t.Run("bulk update driver arcr when driver has canceled orders", func(tt *testing.T) {
		content := `Driver ID,Date Time,AR,CR,Service
driver-A,2022-09-22,100,NO_CHANGE,food
driver-A,2022-09-22,100,0,mart`

		gctx, recorder := req(content, true)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.simpleUnleasher.SetEnabled(featureflag.ReviseBulkARCR.Name, true)
		deps.orderRepo.EXPECT().FindDriverOrderAutoAssigned(
			gomock.Any(),
			gomock.InAnyOrder([]string{"driver-A"}),
			gomock.Eq(timeutil.ConvertYYYYMMDDToDate("2022-09-22")),
			gomock.Eq(timeutil.ConvertYYYYMMDDToDate("2022-09-23")),
			gomock.Any(),
		).Return(model.CountDriverAutoAssignedRecords{
			{
				Driver:                     "driver-A",
				AutoAssignedCompletedCount: types.NewInt(0),
				AutoAssignedCanceledCount:  types.NewInt(1),
				Service:                    "food",
			},
			{
				Driver:                     "driver-A",
				AutoAssignedCompletedCount: types.NewInt(0),
				AutoAssignedCanceledCount:  types.NewInt(1),
				Service:                    "mart",
			},
		}, nil)

		parsePublishMsg := func(req model.StatsResetEventPayload) []byte {
			payload, err := json.Marshal(req)
			require.Nil(tt, err)
			e := event.DriverEventOrderModel{
				Event:     string(event.EventStatsReset),
				Payload:   payload,
				EventTime: timeutil.BangkokNow(),
			}
			msg, err := json.Marshal(e)
			require.Nil(tt, err)
			return msg
		}

		deps.busSvc.EXPECT().Publish(gomock.Any(), gomock.Eq(order.BusTopicDriverEventOrder), gomock.Eq(parsePublishMsg(model.StatsResetEventPayload{
			DriverID:                 "driver-A",
			Date:                     timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
			Service:                  "food",
			AutoAssignedAccepted:     types.NewInt(0),
			AutoAssignedAcceptedRain: types.NewInt(0),
			AutoAssigned:             types.NewInt(0),
			AutoAssignedRain:         types.NewInt(0),
			RequestedBy:              "<EMAIL>",
			Upsert:                   true,
		})))
		deps.busSvc.EXPECT().Publish(gomock.Any(), gomock.Eq(order.BusTopicDriverEventOrder), gomock.Eq(parsePublishMsg(model.StatsResetEventPayload{
			DriverID:                 "driver-A",
			Date:                     timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
			Service:                  "mart",
			AutoAssignedAccepted:     types.NewInt(0),
			AutoAssignedAcceptedRain: types.NewInt(0),
			AutoAssigned:             types.NewInt(0),
			AutoAssignedRain:         types.NewInt(0),
			CancelledNotFree:         types.NewInt(0),
			RequestedBy:              "<EMAIL>",
			Upsert:                   true,
		})))

		// When
		api.BulkUpdateDriverARCR(gctx)

		// Then
		require.Equal(tt, http.StatusOK, recorder.Code)
		var actual *BulkUpdateDriverARCRResponse
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, len(actual.Successes), 2)
		require.Equal(tt, len(actual.Failures), 0)
	})
}

func newTestUnbanRequest(
	content []byte,
	reason string,
	createdBy string,
	driverID string,
) (*gin.Context, *httptest.ResponseRecorder, error) {
	var b bytes.Buffer
	w := multipart.NewWriter(&b)
	var fw io.Writer

	fw, _ = w.CreateFormField("reason")
	_, _ = fw.Write([]byte(reason))

	fw, _ = w.CreateFormField("createdBy")
	_, _ = fw.Write([]byte(createdBy))
	fw, _ = w.CreateFormFile("unbanImageRef", "image.jpg")
	_, _ = fw.Write(content)
	w.Close()

	path := fmt.Sprintf("/v1/admin/drivers/%s/unban", driverID)
	gctx, recorder := testutil.TestRequestContext("POST", path, &b)
	gctx.Request.Header.Set("Content-Type", w.FormDataContentType())

	return gctx, recorder, nil
}

func TestDriverAdminAPI_BulkUpdateDriverDeprioritization(t *testing.T) {
	t.Parallel()

	header := "driver_id,is_deprioritized,service_types\n"
	withHeader := func(s string) string { return header + s }

	req := func(content string, isAdmin bool) (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/bulk/drivers/deprioritization")
		gctx.Body().MultipartForm().
			File("file", "file.csv", content).Build()

		if isAdmin {
			gctx.FakeAdminCtxAuthorized("admin", fake.CharactersN(20), "<EMAIL>")
		}

		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("fail exceed max csv records", func(tt *testing.T) {
		content := withHeader("LMD01,true,food\nLMD02,false,\nLMD03,false,\nLMD04,true,mart")

		gctx, recorder := req(content, true)

		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()
		api.Cfg.BulkUpdateDriverDeprioritizationMaxCSVRows = 2

		api.BulkUpdateDriverDeprioritization(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, absintheApi.ERRCODE_INVALID_REQUEST, actual.Code)
		require.Equal(tt, "exceed max row got [4], max [2]", actual.Message)
	})

	t.Run("fail missing column", func(tt *testing.T) {
		content := withHeader("LMD01,true,food\nLMD02,false\nLMD03,false,\nLMD04,true,mart")
		gctx, recorder := req(content, true)

		api, _, finish := newTestDriverAdminAPI(tt)
		defer finish()
		api.Cfg.BulkUpdateDriverDeprioritizationMaxCSVRows = 2

		api.BulkUpdateDriverDeprioritization(gctx)

		require.Equal(tt, http.StatusBadRequest, recorder.Code)
		var actual absintheApi.Error
		testutil.DecodeJSON(tt, recorder.Body, &actual)
		require.Equal(tt, absintheApi.ERRCODE_INVALID_REQUEST, actual.Code)
	})

	t.Run("bulk update deprioritization success", func(tt *testing.T) {
		content := withHeader("LMD01,true,mart|food\nLMD02,false,\nLMD03,false,\nLMD04,true,mart")
		gctx, recorder := req(content, true)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.driverRepo.EXPECT().
			ValidateDriverIDs(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]string{"LMD01", "LMD02", "LMD03", "LMD04"}, nil, nil)

		deps.driverRepo.EXPECT().
			BulkUpdateDeprioritization(gomock.Any(), gomock.Any(), map[string]model.Services{
				"LMD01": model.ServicesFromString("food", "mart"),
				"LMD04": model.ServicesFromString("mart"),
			}).
			DoAndReturn(func(_ context.Context, disables []string, enables map[string]model.Services) (int, error) {
				expectedDisables := types.NewSetFrom("LMD02", "LMD03")
				expectedEnables := types.NewSetFrom("LMD01", "LMD04")

				require.True(tt, expectedDisables.ContainsExact(disables...))
				require.True(tt, expectedEnables.ContainsExact(utils.CollectKeys(enables)...))

				return 4, nil
			})

		api.BulkUpdateDriverDeprioritization(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())

		var resp BulkUpdateDriverDeprioritizationResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)

		require.Equal(tt, resp.Count.Deprioritized, 2)
		require.Equal(tt, resp.Count.Normalized, 2)

		expectedSuccesses := types.NewSetFrom("LMD01", "LMD02", "LMD03", "LMD04")
		require.True(tt, expectedSuccesses.ContainsExact(resp.Successes...))
	})

	t.Run("bulk update deprioritization partial success (enable)", func(tt *testing.T) {
		content := withHeader("LMD01,true,bike|food\nLMD02,false,\nLMDxx,true,food\nLMD04,true,mart")
		gctx, recorder := req(content, true)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.driverRepo.EXPECT().
			ValidateDriverIDs(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]string{"LMD01", "LMD02", "LMD04"}, []string{"LMDxx"}, nil)

		deps.driverRepo.EXPECT().
			BulkUpdateDeprioritization(gomock.Any(), []string{"LMD02"}, map[string]model.Services{
				"LMD01": model.ServicesFromString("bike", "food"),
				"LMD04": model.ServicesFromString("mart"),
			}).
			DoAndReturn(func(_ context.Context, disables []string, enables map[string]model.Services) (int, error) {
				expectedDisables := types.NewSetFrom("LMD02")
				expectedEnables := types.NewSetFrom("LMD01", "LMD04")

				require.True(tt, expectedDisables.ContainsExact(disables...))
				require.True(tt, expectedEnables.ContainsExact(utils.CollectKeys(enables)...))

				return 3, nil
			})

		api.BulkUpdateDriverDeprioritization(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
		body := recorder.Body.String()

		var resp BulkUpdateDriverDeprioritizationResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)

		require.Equal(tt, resp.Count.Deprioritized, 2, body)
		require.Equal(tt, resp.Count.Normalized, 1, body)

		expectedSuccesses := types.NewSetFrom("LMD01", "LMD02", "LMD04")
		require.True(tt, expectedSuccesses.ContainsExact(resp.Successes...))
		require.Equal(tt, "LMDxx", resp.Failures[0].ID)
	})

	t.Run("bulk update deprioritization partial success (disable)", func(tt *testing.T) {
		content := withHeader("LMD01,true,bike|food\nLMDxx,false,\nLMD03,true,food\nLMD04,true,mart|bike\nLMD05,false,\nLMDyy,false,")
		gctx, recorder := req(content, true)

		api, deps, finish := newTestDriverAdminAPI(tt)
		defer finish()

		deps.driverRepo.EXPECT().
			ValidateDriverIDs(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]string{"LMD01", "LMD03", "LMD04", "LMD05"}, []string{"LMDxx", "LMDyy"}, nil)

		deps.driverRepo.EXPECT().
			BulkUpdateDeprioritization(gomock.Any(), []string{"LMD05"}, map[string]model.Services{
				"LMD01": model.ServicesFromString("bike", "food"),
				"LMD03": model.ServicesFromString("food"),
				"LMD04": model.ServicesFromString("bike", "mart"),
			}).
			DoAndReturn(func(_ context.Context, disables []string, enables map[string]model.Services) (int, error) {
				expectedDisables := types.NewSetFrom("LMD05")
				expectedEnables := types.NewSetFrom("LMD01", "LMD03", "LMD04")

				require.True(tt, expectedDisables.ContainsExact(disables...))
				require.True(tt, expectedEnables.ContainsExact(utils.CollectKeys(enables)...))

				return 4, nil
			})

		api.BulkUpdateDriverDeprioritization(gctx)
		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
		body := recorder.Body.String()

		var resp BulkUpdateDriverDeprioritizationResponse
		testutil.DecodeJSON(tt, recorder.Body, &resp)

		require.Equal(tt, resp.Count.Deprioritized, 3, body)
		require.Equal(tt, resp.Count.Normalized, 1, body)

		expectedSuccesses := types.NewSetFrom("LMD01", "LMD03", "LMD04", "LMD05")
		require.True(tt, expectedSuccesses.ContainsExact(resp.Successes...))

		expectedFailures := types.NewSetFrom("LMDxx", "LMDyy")
		failedIDs := make([]string, len(resp.Failures))
		for i := range resp.Failures {
			failedIDs[i] = resp.Failures[i].ID
		}
		require.True(tt, expectedFailures.ContainsExact(failedIDs...))
	})
}

type driverAdminApiDeps struct {
	banHistRepo                     *mock_repository.MockBanHistoryRepository
	banSvc                          *mock_service.MockBanService
	driverSvc                       *mock_service.MockDriverServiceInterface
	driverRepo                      *mock_repository.MockDriverRepository
	adminConfig                     Config
	slack                           *mock_slack.MockSlack
	driverOrderInfoRepo             *mock_repository.MockDriverOrderInfoRepository
	orderRepo                       *mock_repository.MockOrderRepository
	driverCancelRepo                *mock_repository.MockCancelReasonRepository
	vosSvc                          *mock_service.MockVOSService
	driverRegistrationRepo          *mock_repository.MockDriverRegistrationRepository
	assignmentLogRepo               *mock_repository.MockAssignmentLogRepository
	shiftSvc                        *mock_service.MockShiftServices
	busSvc                          *mock_event_bus.MockEventBus
	requestUpdateProfileSectionRepo *mock_repository.MockRequestUpdateProfileSectionRepository
	requestUpdateProfileRepo        *mock_repository.MockRequestUpdateProfileRepository
	txnHelper                       *mock_transaction.MockTxnHelper
	incomeSummarySvc                *mock_income.MockIncomeSummaryService
	dedicatedZoneRepo               *mock_repository.MockDedicatedZoneRepository
	banEffectiveTimeRepo            *mock_repository.MockBanEffectiveTimeRepository
	auditLog                        *mock_repository.MockAuditLogRepository
	serviceAreaRepo                 *mock_repository.MockServiceAreaRepository
	deviceManagerSvc                *mock_service.MockDeviceManager
	simpleUnleasher                 *test.SimpleUnleasher
	bulkProcessInfoRepo             *mock_repository.MockBulkProcessInfoRepository
	globalConfig                    config.GlobalConfig
	cancelReasonConfig              *service.CancelReasonCfg
	bulkProcessInfoService          *mock_bulk.MockProcessInfoService
	atomicAdminConfig               *config.AtomicAdminConfig
	bulkConfig                      *bulk.Config
}

type driverAPITestOps func(deps *driverAdminApiDeps)

func WithAtomicAdminConfig(cfg *config.AtomicAdminConfig) func(deps *driverAdminApiDeps) {
	return func(deps *driverAdminApiDeps) {
		deps.atomicAdminConfig = cfg
	}
}

func newTestDriverAdminAPI(t gomock.TestReporter, opts ...driverAPITestOps) (*DriverAdminAPI, *driverAdminApiDeps, func()) {
	ctrl := gomock.NewController(t)

	simpleUnleasher := test.ProvideSimpleUnleasher()
	unleashAdmin := mock_unleash.NewMockAdmin(ctrl)
	featureFlagSvc := featureflag.NewFeatureFlagService(simpleUnleasher, unleashAdmin)

	deps := &driverAdminApiDeps{
		banHistRepo:                     mock_repository.NewMockBanHistoryRepository(ctrl),
		banSvc:                          mock_service.NewMockBanService(ctrl),
		driverSvc:                       mock_service.NewMockDriverServiceInterface(ctrl),
		driverRepo:                      mock_repository.NewMockDriverRepository(ctrl),
		adminConfig:                     ProvideAdminConfig(),
		slack:                           mock_slack.NewMockSlack(ctrl),
		driverOrderInfoRepo:             mock_repository.NewMockDriverOrderInfoRepository(ctrl),
		orderRepo:                       mock_repository.NewMockOrderRepository(ctrl),
		driverCancelRepo:                mock_repository.NewMockCancelReasonRepository(ctrl),
		vosSvc:                          mock_service.NewMockVOSService(ctrl),
		driverRegistrationRepo:          mock_repository.NewMockDriverRegistrationRepository(ctrl),
		assignmentLogRepo:               mock_repository.NewMockAssignmentLogRepository(ctrl),
		shiftSvc:                        mock_service.NewMockShiftServices(ctrl),
		busSvc:                          mock_event_bus.NewMockEventBus(ctrl),
		requestUpdateProfileSectionRepo: mock_repository.NewMockRequestUpdateProfileSectionRepository(ctrl),
		requestUpdateProfileRepo:        mock_repository.NewMockRequestUpdateProfileRepository(ctrl),
		txnHelper:                       mock_transaction.NewMockTxnHelper(ctrl),
		incomeSummarySvc:                mock_income.NewMockIncomeSummaryService(ctrl),
		dedicatedZoneRepo:               mock_repository.NewMockDedicatedZoneRepository(ctrl),
		banEffectiveTimeRepo:            mock_repository.NewMockBanEffectiveTimeRepository(ctrl),
		auditLog:                        mock_repository.NewMockAuditLogRepository(ctrl),
		serviceAreaRepo:                 mock_repository.NewMockServiceAreaRepository(ctrl),
		simpleUnleasher:                 simpleUnleasher,
		bulkProcessInfoRepo:             mock_repository.NewMockBulkProcessInfoRepository(ctrl),
		globalConfig:                    config.GlobalConfig{},
		cancelReasonConfig:              &service.CancelReasonCfg{},
		bulkProcessInfoService:          mock_bulk.NewMockProcessInfoService(ctrl),
		bulkConfig:                      &bulk.Config{},
	}

	for _, opt := range opts {
		opt(deps)
	}

	return ProvideDriverOperationAdminAPI(deps.assignmentLogRepo, deps.banSvc, deps.banHistRepo, deps.driverSvc, deps.driverRepo, deps.driverCancelRepo, deps.driverOrderInfoRepo, deps.orderRepo, deps.driverRegistrationRepo, deps.adminConfig, deps.slack, deps.vosSvc, deps.shiftSvc, deps.busSvc,
			deps.requestUpdateProfileSectionRepo, deps.requestUpdateProfileRepo, deps.txnHelper, deps.incomeSummarySvc, deps.dedicatedZoneRepo, deps.banEffectiveTimeRepo, deps.auditLog, deps.serviceAreaRepo, featureFlagSvc, deps.bulkProcessInfoRepo, deps.globalConfig, &service.AtomicCancelReasonConfig{}, deps.atomicAdminConfig, deps.bulkConfig), deps, func() {
			ctrl.Finish()
		}
}

func newTestDriverAdminAPIWithAdminCfg(t gomock.TestReporter, cfg Config, cancelReasonConfig *service.AtomicCancelReasonConfig, opts ...driverAPITestOps) (*DriverAdminAPI, *driverAdminApiDeps, func()) {
	ctrl := gomock.NewController(t)

	simpleUnleasher := test.ProvideSimpleUnleasher()
	unleashAdmin := mock_unleash.NewMockAdmin(ctrl)
	featureFlagSvc := featureflag.NewFeatureFlagService(simpleUnleasher, unleashAdmin)

	if cancelReasonConfig == nil {
		cancelReasonConfig = service.NewAtomicCancelReasonConfig(service.CancelReasonCfg{})
	}

	deps := &driverAdminApiDeps{
		banHistRepo:                     mock_repository.NewMockBanHistoryRepository(ctrl),
		banSvc:                          mock_service.NewMockBanService(ctrl),
		driverSvc:                       mock_service.NewMockDriverServiceInterface(ctrl),
		driverRepo:                      mock_repository.NewMockDriverRepository(ctrl),
		adminConfig:                     cfg,
		slack:                           mock_slack.NewMockSlack(ctrl),
		driverOrderInfoRepo:             mock_repository.NewMockDriverOrderInfoRepository(ctrl),
		orderRepo:                       mock_repository.NewMockOrderRepository(ctrl),
		driverCancelRepo:                mock_repository.NewMockCancelReasonRepository(ctrl),
		vosSvc:                          mock_service.NewMockVOSService(ctrl),
		driverRegistrationRepo:          mock_repository.NewMockDriverRegistrationRepository(ctrl),
		assignmentLogRepo:               mock_repository.NewMockAssignmentLogRepository(ctrl),
		shiftSvc:                        mock_service.NewMockShiftServices(ctrl),
		busSvc:                          mock_event_bus.NewMockEventBus(ctrl),
		requestUpdateProfileSectionRepo: mock_repository.NewMockRequestUpdateProfileSectionRepository(ctrl),
		requestUpdateProfileRepo:        mock_repository.NewMockRequestUpdateProfileRepository(ctrl),
		txnHelper:                       mock_transaction.NewMockTxnHelper(ctrl),
		incomeSummarySvc:                mock_income.NewMockIncomeSummaryService(ctrl),
		dedicatedZoneRepo:               mock_repository.NewMockDedicatedZoneRepository(ctrl),
		banEffectiveTimeRepo:            mock_repository.NewMockBanEffectiveTimeRepository(ctrl),
		auditLog:                        mock_repository.NewMockAuditLogRepository(ctrl),
		serviceAreaRepo:                 mock_repository.NewMockServiceAreaRepository(ctrl),
		simpleUnleasher:                 simpleUnleasher,
		bulkProcessInfoRepo:             mock_repository.NewMockBulkProcessInfoRepository(ctrl),
		globalConfig:                    config.GlobalConfig{},
		cancelReasonConfig:              &cancelReasonConfig.Config,
		bulkProcessInfoService:          mock_bulk.NewMockProcessInfoService(ctrl),
		bulkConfig:                      &bulk.Config{},
	}

	for _, opt := range opts {
		opt(deps)
	}

	return ProvideDriverOperationAdminAPI(deps.assignmentLogRepo, deps.banSvc, deps.banHistRepo, deps.driverSvc, deps.driverRepo, deps.driverCancelRepo, deps.driverOrderInfoRepo, deps.orderRepo, deps.driverRegistrationRepo, deps.adminConfig, deps.slack, deps.vosSvc, deps.shiftSvc, deps.busSvc,
			deps.requestUpdateProfileSectionRepo, deps.requestUpdateProfileRepo, deps.txnHelper, deps.incomeSummarySvc, deps.dedicatedZoneRepo, deps.banEffectiveTimeRepo, deps.auditLog, deps.serviceAreaRepo, featureFlagSvc, deps.bulkProcessInfoRepo, deps.globalConfig, cancelReasonConfig, deps.atomicAdminConfig, deps.bulkConfig), deps, func() {
			ctrl.Finish()
		}
}

func TestDriverAdminAPI_UpdateDriverRequestUpdateProfileStatus(t *testing.T) {
	type Request struct {
		Status     string   `json:"status"`
		Message    string   `json:"message"`
		RequestIDs []string `json:"requestIds"`
	}

	type ExpectUpdateRequestItem struct {
		status          model.RequestProfileStatus
		message         string
		stringObjectIDs []string
	}

	req := func(driverID string, req interface{}) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/drivers/%s/request-update-profile", driverID), testutil.JSON(req))
		return gctx, recorder
	}

	type testData struct {
		name                string
		request             Request
		getReqUpdateItems   func() []model.RequestUpdateDriverProfile
		targetDriverInfo    model.Driver
		checkDriver         func(t *testing.T, driver *model.Driver)
		expectedRequestItem ExpectUpdateRequestItem
		inProgressItems     []model.RequestUpdateDriverProfile
	}

	testDriverID := "DRIVER_ID"
	timeNow := time.Now()
	timeNowFuture := time.Now().AddDate(3, 2, 1)
	validPrimitiveObjectIDs := []primitive.ObjectID{
		primitive.NewObjectID(), primitive.NewObjectID(), primitive.NewObjectID(),
	}
	generateDriver := func() model.Driver {
		return model.Driver{
			BaseDriver: model.BaseDriver{
				AvatarURL:            "OLD_AVATAR_URL",
				ProfileStatus:        model.ProfileStatusUpdatePending,
				Phone:                crypt.NewLazyEncryptedString("0000000000"),
				EmergencyPhone:       crypt.NewLazyEncryptedString("1111111111"),
				StrongEmergencyPhone: crypt.NewStrongEncryptedString("1111111111"),
				Vehicle: model.VehicleInfo{
					RegistrationPhotoURL:   "OLD_VEHICLE_REG_PHOTO",
					PhotoURL:               "OLD_VEHICLE_PHOTO",
					LegislationPhotoURL:    "OLD_LEGISLATION_PHOTO",
					LegislationExpiredDate: &timeNow,
				},
			},
		}
	}
	getRequestUpdateProfileItems := func() []model.RequestUpdateDriverProfile {
		return []model.RequestUpdateDriverProfile{
			{
				ID:       validPrimitiveObjectIDs[0],
				DriverID: testDriverID,
				Status:   model.RequestProfileStatusUpdatePending,
				Fields: []model.DriverRequestUpdateProfileField{
					{
						Field:    model.ProfileFieldAvatarPhoto,
						Required: true,
					},
					{
						Field:    model.ProfileFieldEmergencyPhoneNumber,
						Required: true,
					},
					{
						Field:    model.ProfileFieldStrongEmergencyPhoneNumber,
						Required: true,
					},
					{
						Field:    model.ProfileFieldLinemanEquipment,
						Required: true,
					},
				},
				UpdateDriverProfile: model.UpdateDriverProfile{
					PersonalInfoRequest: model.PersonalInfoRequest{
						AvatarURL:            "AVATAR_URL",
						EmergencyPhone:       crypt.NewLazyEncryptedString("0900000001"),
						StrongEmergencyPhone: crypt.NewStrongEncryptedString("0900000001"),
					},
				},
			},
			{
				ID:       validPrimitiveObjectIDs[1],
				DriverID: testDriverID,
				Status:   model.RequestProfileStatusUpdatePending,
				Fields: []model.DriverRequestUpdateProfileField{
					{
						Field:    model.ProfileFieldVehicleLegislationPhoto,
						Required: true,
					},
					{
						Field:    model.ProfileFieldVehicleLegislationExpirationDate,
						Required: true,
					},
				},
				UpdateDriverProfile: model.UpdateDriverProfile{
					VehicleInfoRequest: model.VehicleInfoRequest{
						LegislationPhotoURL:    "LEGISLATION_PHOTO",
						LegislationExpiredDate: &timeNowFuture,
					},
				},
			},
		}
	}

	testSet := []testData{
		{
			name: "1-req-in-progress-complete-1",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				return []model.RequestUpdateDriverProfile{
					getRequestUpdateProfileItems()[0],
				}
			},
			targetDriverInfo: generateDriver(),
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusCompleted, driver.ProfileStatus)
				require.Equal(t, "0900000001", driver.EmergencyPhone.String())
				require.Equal(t, "0900000001", driver.StrongEmergencyPhone.String())
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{validPrimitiveObjectIDs[0].Hex()},
			},
		},
		{
			name: "2-req-in-progress-complete-1",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				return []model.RequestUpdateDriverProfile{
					getRequestUpdateProfileItems()[0],
				}
			},
			targetDriverInfo: generateDriver(),
			inProgressItems:  []model.RequestUpdateDriverProfile{{Status: model.RequestProfileStatusUpdatePending}},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusUpdatePending, driver.ProfileStatus)
				require.Equal(t, "0900000001", driver.EmergencyPhone.String())
				require.Equal(t, "0900000001", driver.StrongEmergencyPhone.String())
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{validPrimitiveObjectIDs[0].Hex()},
			},
		},
		{
			name: "2-req-in-progress-complete-2",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				items := getRequestUpdateProfileItems()
				items[1].UpdateDriverProfile.VehicleInfoRequest = model.VehicleInfoRequest{
					VehicleRegistrationPhotoURL: "VEHICLE_REG_PHOTO",
					VehiclePhotoURL:             "VEHICLE_PHOTO",
					LegislationPhotoURL:         "LEGISLATION_PHOTO",
					LegislationExpiredDate:      &timeNowFuture,
				}
				return items
			},
			targetDriverInfo: generateDriver(),
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusCompleted, driver.ProfileStatus)
				require.Equal(t, "AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "0900000001", driver.EmergencyPhone.String())
				require.Equal(t, "0900000001", driver.StrongEmergencyPhone.String())

				require.Equal(t, "LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNowFuture.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{validPrimitiveObjectIDs[0].Hex(), validPrimitiveObjectIDs[1].Hex()},
			},
		},
		{
			name: "3-req-in-progress-complete-2-driver-empty",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				return getRequestUpdateProfileItems()
			},
			targetDriverInfo: model.Driver{
				BaseDriver: model.BaseDriver{
					ProfileStatus: model.ProfileStatusUpdatePending,
				},
			},
			inProgressItems: []model.RequestUpdateDriverProfile{{Status: model.RequestProfileStatusUpdatePending}},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusUpdatePending, driver.ProfileStatus)
				require.Equal(t, "AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "0900000001", driver.EmergencyPhone.String())
				require.Equal(t, "0900000001", driver.StrongEmergencyPhone.String())

				require.Equal(t, "LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNowFuture.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{validPrimitiveObjectIDs[0].Hex(), validPrimitiveObjectIDs[1].Hex()},
			},
		},
		{
			name: "2-req-in-progress-complete-2-blank-request-2",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				items := getRequestUpdateProfileItems()
				items[0].UpdateDriverProfile = model.UpdateDriverProfile{}
				items[1].UpdateDriverProfile = model.UpdateDriverProfile{}
				return items
			},
			targetDriverInfo: generateDriver(),
			inProgressItems:  []model.RequestUpdateDriverProfile{{Status: model.RequestProfileStatusUpdatePending}},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusUpdatePending, driver.ProfileStatus)
				require.Equal(t, "OLD_AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "1111111111", driver.EmergencyPhone.String())
				require.Equal(t, "1111111111", driver.StrongEmergencyPhone.String())

				require.Equal(t, "OLD_LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNow.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
			},
		},
		{
			name: "2-req-in-progress-complete-2-blank-request-1",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				items := getRequestUpdateProfileItems()
				items[0].UpdateDriverProfile = model.UpdateDriverProfile{}
				return items
			},
			targetDriverInfo: generateDriver(),
			inProgressItems:  []model.RequestUpdateDriverProfile{{Status: model.RequestProfileStatusUpdatePending}},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusUpdatePending, driver.ProfileStatus)
				require.Equal(t, "OLD_AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "1111111111", driver.EmergencyPhone.String())
				require.Equal(t, "1111111111", driver.StrongEmergencyPhone.String())

				require.Equal(t, "LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNowFuture.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{validPrimitiveObjectIDs[1].Hex()},
			},
		},
		{
			name: "2-request-in-progress-request-update-2",
			request: Request{
				Status:  "REQUESTED_UPDATE",
				Message: "please update your document again",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				return getRequestUpdateProfileItems()
			},
			targetDriverInfo: generateDriver(),
			inProgressItems:  []model.RequestUpdateDriverProfile{{Status: model.RequestProfileStatusRequestUpdate}},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusRequestUpdate, driver.ProfileStatus)
				require.Equal(t, "OLD_AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "1111111111", driver.EmergencyPhone.String())
				require.Equal(t, "1111111111", driver.StrongEmergencyPhone.String())

				require.Equal(t, "OLD_LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNow.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusRequestUpdate,
				message:         "please update your document again",
				stringObjectIDs: []string{validPrimitiveObjectIDs[0].Hex(), validPrimitiveObjectIDs[1].Hex()},
			},
		},
	}

	for index := range testSet {
		testData := testSet[index]
		t.Run(testData.name, func(tt *testing.T) {
			gctx, recorder := req(testDriverID, testData.request)
			api, deps, finish := newTestDriverAdminAPI(tt)
			defer finish()

			deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil, nil)

			deps.requestUpdateProfileRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(testData.getReqUpdateItems(), nil)

			deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), testDriverID).
				Return(&testData.targetDriverInfo, nil)

			deps.driverRepo.EXPECT().Update(gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, driver *model.Driver) error {
					testData.checkDriver(tt, driver)
					return nil
				})

			deps.requestUpdateProfileRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(testData.inProgressItems, nil)

			deps.requestUpdateProfileRepo.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, status model.RequestProfileStatus, message string, objectID ...primitive.ObjectID) error {
					require.Equal(tt, testData.expectedRequestItem.status, status)
					require.Equal(tt, testData.expectedRequestItem.message, message)
					require.Len(tt, objectID, len(testData.expectedRequestItem.stringObjectIDs))
					for index := range objectID {
						require.Equal(tt, testData.expectedRequestItem.stringObjectIDs[index], objectID[index].Hex())
					}
					return nil
				}).AnyTimes()

			api.UpdateDriverRequestUpdateProfileStatus(gctx)

			require.Equal(tt, http.StatusOK, recorder.Code)
		})
	}
}

func TestDriverAdminAPI_UpdateAllDriverRequestUpdateProfileStatus(t *testing.T) {
	t.Parallel()
	type Request struct {
		Status     string   `json:"status"`
		Message    string   `json:"message"`
		RequestIDs []string `json:"requestIds"`
	}

	type ExpectUpdateRequestItem struct {
		status          model.RequestProfileStatus
		message         string
		stringObjectIDs []string
	}

	req := func(driverID string, req interface{}) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/drivers/%s/request-update-profile/all", driverID), testutil.JSON(req))
		return gctx, recorder
	}

	type testData struct {
		name                string
		request             Request
		getReqUpdateItems   func() []model.RequestUpdateDriverProfile
		targetDriverInfo    model.Driver
		checkDriver         func(t *testing.T, driver *model.Driver)
		expectedRequestItem ExpectUpdateRequestItem
		inProgressItems     []model.RequestUpdateDriverProfile
	}

	testDriverID := "DRIVER_ID"
	timeNow := time.Now()
	timeNowFuture := time.Now().AddDate(3, 2, 1)
	validPrimitiveObjectIDs := []primitive.ObjectID{
		primitive.NewObjectID(), primitive.NewObjectID(), primitive.NewObjectID(),
	}
	generateDriver := func() model.Driver {
		return model.Driver{
			BaseDriver: model.BaseDriver{
				AvatarURL:            "OLD_AVATAR_URL",
				ProfileStatus:        model.ProfileStatusUpdatePending,
				Phone:                crypt.NewLazyEncryptedString("0000000000"),
				EmergencyPhone:       crypt.NewLazyEncryptedString("1111111111"),
				StrongEmergencyPhone: crypt.NewStrongEncryptedString("1111111111"),
				Vehicle: model.VehicleInfo{
					RegistrationPhotoURL:   "OLD_VEHICLE_REG_PHOTO",
					PhotoURL:               "OLD_VEHICLE_PHOTO",
					LegislationPhotoURL:    "OLD_LEGISLATION_PHOTO",
					LegislationExpiredDate: &timeNow,
				},
			},
		}
	}
	getRequestUpdateProfileItems := func() []model.RequestUpdateDriverProfile {
		return []model.RequestUpdateDriverProfile{
			{
				ID:       validPrimitiveObjectIDs[0],
				DriverID: testDriverID,
				Status:   model.RequestProfileStatusUpdatePending,
				Fields: []model.DriverRequestUpdateProfileField{
					{
						Field:    model.ProfileFieldAvatarPhoto,
						Required: true,
					},
					{
						Field:    model.ProfileFieldEmergencyPhoneNumber,
						Required: true,
					},
					{
						Field:    model.ProfileFieldStrongEmergencyPhoneNumber,
						Required: true,
					},
					{
						Field:    model.ProfileFieldLinemanEquipment,
						Required: true,
					},
				},
				UpdateDriverProfile: model.UpdateDriverProfile{
					PersonalInfoRequest: model.PersonalInfoRequest{
						AvatarURL:            "AVATAR_URL",
						EmergencyPhone:       crypt.NewLazyEncryptedString("0900000001"),
						StrongEmergencyPhone: crypt.NewStrongEncryptedString("0900000001"),
					},
				},
			},
			{
				ID:       validPrimitiveObjectIDs[1],
				DriverID: testDriverID,
				Status:   model.RequestProfileStatusUpdatePending,
				Fields: []model.DriverRequestUpdateProfileField{
					{
						Field:    model.ProfileFieldVehicleLegislationPhoto,
						Required: true,
					},
					{
						Field:    model.ProfileFieldVehicleLegislationExpirationDate,
						Required: true,
					},
				},
				UpdateDriverProfile: model.UpdateDriverProfile{
					VehicleInfoRequest: model.VehicleInfoRequest{
						LegislationPhotoURL:    "LEGISLATION_PHOTO",
						LegislationExpiredDate: &timeNowFuture,
					},
				},
			},
		}
	}

	testSet := []testData{
		{
			name: "1-req-in-progress-complete-1",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				return []model.RequestUpdateDriverProfile{
					getRequestUpdateProfileItems()[0],
				}
			},
			targetDriverInfo: generateDriver(),
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusCompleted, driver.ProfileStatus)
				require.Equal(t, "0900000001", driver.EmergencyPhone.String())
				require.Equal(t, "0900000001", driver.StrongEmergencyPhone.String())
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{validPrimitiveObjectIDs[0].Hex()},
			},
		},
		{
			name: "2-req-in-progress-complete-2",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				items := getRequestUpdateProfileItems()
				items[1].UpdateDriverProfile.VehicleInfoRequest = model.VehicleInfoRequest{
					VehicleRegistrationPhotoURL: "VEHICLE_REG_PHOTO",
					VehiclePhotoURL:             "VEHICLE_PHOTO",
					LegislationPhotoURL:         "LEGISLATION_PHOTO",
					LegislationExpiredDate:      &timeNowFuture,
				}
				return items
			},
			targetDriverInfo: generateDriver(),
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusCompleted, driver.ProfileStatus)
				require.Equal(t, "AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "0900000001", driver.EmergencyPhone.String())
				require.Equal(t, "0900000001", driver.StrongEmergencyPhone.String())

				require.Equal(t, "LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNowFuture.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{validPrimitiveObjectIDs[0].Hex(), validPrimitiveObjectIDs[1].Hex()},
			},
		},
		{
			name: "2-req-in-progress-complete-2-blank-request-2",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				items := getRequestUpdateProfileItems()
				items[0].UpdateDriverProfile = model.UpdateDriverProfile{}
				items[1].UpdateDriverProfile = model.UpdateDriverProfile{}
				return items
			},
			targetDriverInfo: generateDriver(),
			inProgressItems:  []model.RequestUpdateDriverProfile{{Status: model.RequestProfileStatusUpdatePending}},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusUpdatePending, driver.ProfileStatus)
				require.Equal(t, "OLD_AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "1111111111", driver.EmergencyPhone.String())
				require.Equal(t, "1111111111", driver.StrongEmergencyPhone.String())

				require.Equal(t, "OLD_LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNow.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
			},
		},
		{
			name: "2-req-in-progress-complete-2-blank-request-1",
			request: Request{
				Status: "COMPLETED",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				items := getRequestUpdateProfileItems()
				items[0].UpdateDriverProfile = model.UpdateDriverProfile{}
				return items
			},
			targetDriverInfo: generateDriver(),
			inProgressItems:  []model.RequestUpdateDriverProfile{{Status: model.RequestProfileStatusUpdatePending}},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusUpdatePending, driver.ProfileStatus)
				require.Equal(t, "OLD_AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "1111111111", driver.EmergencyPhone.String())
				require.Equal(t, "1111111111", driver.StrongEmergencyPhone.String())

				require.Equal(t, "LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNowFuture.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{validPrimitiveObjectIDs[1].Hex()},
			},
		},
		{
			name: "2-request-in-progress-request-update-2",
			request: Request{
				Status:  "REQUESTED_UPDATE",
				Message: "please update your document again",
				RequestIDs: []string{
					validPrimitiveObjectIDs[0].Hex(),
					validPrimitiveObjectIDs[1].Hex(),
				},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				return getRequestUpdateProfileItems()
			},
			targetDriverInfo: generateDriver(),
			inProgressItems:  []model.RequestUpdateDriverProfile{{Status: model.RequestProfileStatusRequestUpdate}},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusRequestUpdate, driver.ProfileStatus)
				require.Equal(t, "OLD_AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "1111111111", driver.EmergencyPhone.String())
				require.Equal(t, "1111111111", driver.StrongEmergencyPhone.String())

				require.Equal(t, "OLD_LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, timeNow.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusRequestUpdate,
				message:         "please update your document again",
				stringObjectIDs: []string{validPrimitiveObjectIDs[0].Hex(), validPrimitiveObjectIDs[1].Hex()},
			},
		},
	}

	for index := range testSet {
		testData := testSet[index]
		t.Run(testData.name, func(tt *testing.T) {
			tt.Parallel()
			gctx, recorder := req(testDriverID, testData.request)
			api, deps, finish := newTestDriverAdminAPI(tt)
			defer finish()

			deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil, nil)

			deps.requestUpdateProfileRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(testData.getReqUpdateItems(), nil).Times(2)

			deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), testDriverID).
				Return(&testData.targetDriverInfo, nil)

			deps.driverRepo.EXPECT().Update(gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, driver *model.Driver) error {
					testData.checkDriver(tt, driver)
					return nil
				})

			deps.requestUpdateProfileRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(testData.inProgressItems, nil)

			deps.requestUpdateProfileRepo.EXPECT().UpdateStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				DoAndReturn(func(ctx context.Context, status model.RequestProfileStatus, message string, objectID ...primitive.ObjectID) error {
					require.Equal(tt, testData.expectedRequestItem.status, status)
					require.Equal(tt, testData.expectedRequestItem.message, message)
					require.Len(tt, objectID, len(testData.expectedRequestItem.stringObjectIDs))
					for index := range objectID {
						require.Equal(tt, testData.expectedRequestItem.stringObjectIDs[index], objectID[index].Hex())
					}
					return nil
				}).AnyTimes()

			api.UpdateAllDriverRequestUpdateProfileStatus(gctx)

			require.Equal(tt, http.StatusOK, recorder.Code)
		})
	}
}

func TestDriverAdminAPI_UpdateAllDriverRequestUpdateProfileStatus_ForceComplete(t *testing.T) {
	type Request struct {
		Status     string   `json:"status"`
		Message    string   `json:"message"`
		RequestIDs []string `json:"requestIds"`
	}

	type ExpectUpdateRequestItem struct {
		status          model.RequestProfileStatus
		message         string
		stringObjectIDs []string
	}

	req := func(driverID string, req interface{}) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/admin/drivers/%s/request-update-profile/all", driverID), testutil.JSON(req))
		gctx.Set("REQUEST_USER_EMAIL", "<EMAIL>")
		gctx.Params = gin.Params{
			{
				Key:   "driver_id",
				Value: "DRIVER_ID",
			},
		}
		return gctx, recorder
	}

	type testData struct {
		name                string
		request             Request
		getReqUpdateItems   func() []model.RequestUpdateDriverProfile
		targetDriverInfo    model.Driver
		checkDriver         func(t *testing.T, driver *model.Driver)
		expectedRequestItem ExpectUpdateRequestItem
		inProgressItems     []model.RequestUpdateDriverProfile
	}

	testDriverID := "DRIVER_ID"
	timeNow := time.Now()
	generateDriver := func() model.Driver {
		return model.Driver{
			BaseDriver: model.BaseDriver{
				AvatarURL:            "OLD_AVATAR_URL",
				ProfileStatus:        model.ProfileStatusUpdatePending,
				Phone:                crypt.NewLazyEncryptedString("0000000000"),
				EmergencyPhone:       crypt.NewLazyEncryptedString("1111111111"),
				StrongEmergencyPhone: crypt.NewStrongEncryptedString("1111111111"),
				Vehicle: model.VehicleInfo{
					RegistrationPhotoURL:   "OLD_VEHICLE_REG_PHOTO",
					PhotoURL:               "OLD_VEHICLE_PHOTO",
					LegislationPhotoURL:    "OLD_LEGISLATION_PHOTO",
					LegislationExpiredDate: &timeNow,
				},
			},
		}
	}

	testSet := []testData{
		{
			name: "0-request-in-progress-update-completed",
			request: Request{
				Status:     "CANCELLED",
				Message:    "",
				RequestIDs: []string{},
			},
			getReqUpdateItems: func() []model.RequestUpdateDriverProfile {
				return []model.RequestUpdateDriverProfile{}
			},
			targetDriverInfo: generateDriver(),
			inProgressItems:  []model.RequestUpdateDriverProfile{},
			checkDriver: func(t *testing.T, driver *model.Driver) {
				require.Equal(t, model.ProfileStatusRequestUpdate, driver.ProfileStatus)
				require.Equal(t, "OLD_AVATAR_URL", driver.AvatarURL)
				require.Equal(t, "0000000000", driver.Phone.String())
				require.Equal(t, "1111111111", driver.EmergencyPhone.String())
				require.Equal(t, "1111111111", driver.StrongEmergencyPhone.String())

				require.Equal(t, "OLD_LEGISLATION_PHOTO", driver.Vehicle.LegislationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_REG_PHOTO", driver.Vehicle.RegistrationPhotoURL)
				require.Equal(t, "OLD_VEHICLE_PHOTO", driver.Vehicle.PhotoURL)
				require.Equal(t, timeNow.Unix(), driver.Vehicle.LegislationExpiredDate.Unix())
			},
			expectedRequestItem: ExpectUpdateRequestItem{
				status:          model.RequestProfileStatusComplete,
				message:         "",
				stringObjectIDs: []string{},
			},
		},
	}

	for index := range testSet {
		testData := testSet[index]
		t.Run(testData.name, func(tt *testing.T) {
			gctx, recorder := req(testDriverID, testData.request)
			api, deps, finish := newTestDriverAdminAPI(tt)
			defer finish()

			deps.requestUpdateProfileRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(testData.getReqUpdateItems(), nil).Times(1)

			deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(nil, nil)

			deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), testDriverID).
				Return(&testData.targetDriverInfo, nil)

			deps.requestUpdateProfileRepo.EXPECT().FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return([]model.RequestUpdateDriverProfile{}, nil)

			deps.driverRepo.EXPECT().UpdateProfileStatus(gomock.Any(), testDriverID, model.ProfileStatusCompleted).
				Return(nil)

			deps.driverRepo.EXPECT().AddRemark(gomock.Any(), testDriverID, gomock.Any()).DoAndReturn(func(ctx context.Context, driverID string, remark model.DriverRemark) error {
				expectedRemark := model.DriverRemark{
					Remark:    "Update Profile Status to COMPLETED",
					CreatedBy: "<EMAIL>",
					CreatedAt: remark.CreatedAt,
				}
				assert.Equal(tt, expectedRemark, remark)
				return nil
			})

			api.UpdateAllDriverRequestUpdateProfileStatus(gctx)

			require.Equal(tt, http.StatusOK, recorder.Code)
		})
	}

}

func TestDriverAdminAPI_GetIncomeSummary(t *testing.T) {
	t.Parallel()

	req := func(driverID string, queryParams url.Values) (*gin.Context, *httptest.ResponseRecorder) {
		params := gin.Params{
			gin.Param{Key: "driver_id", Value: driverID},
		}
		gctx := testutil.NewContextWithRecorder().
			SetGET("/v1/admin/drivers/%v/income-summary", driverID).
			SetGinParams(params)

		gctx.FakeAdminCtxAuthorized("admin", fake.CharactersN(20), "<EMAIL>")
		gctx.SetQuery(queryParams.Encode())
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("happy cases", func(t *testing.T) {
		t.Parallel()

		t.Run("get income summary daily success", func(t *testing.T) {
			t.Parallel()

			// Given
			params := url.Values{}
			params.Add("date", time.Now().Format(time.RFC3339))
			params.Add("granularity", "daily")
			gctx, recorder := req("driver_a", params)

			api, deps, finish := newTestDriverAdminAPI(t)
			defer finish()

			deps.incomeSummarySvc.EXPECT().Query(gomock.Any(), gomock.Any(), gomock.Any()).Return(
				income.StubIncomeSummaryDailySuccess(),
				nil,
			)

			// When
			api.GetIncomeSummary(gctx)

			// Then
			var actual IncomeSummaryResponse
			testutil.DecodeJSON(t, recorder.Body, &actual)
			require.Equal(t, http.StatusOK, recorder.Code)
			assert.Equal(t, types.Money(197), actual.Summary.TotalIncome)
			assert.Equal(t, types.Money(100), actual.Summary.TotalWage)
			assert.Equal(t, types.Money(3), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, types.Money(50), actual.Summary.TotalTipAndOthers)
			assert.Equal(t, types.Money(50), actual.Summary.TotalIncentive)
			assert.Equal(t, 5, actual.Summary.TotalTrip)
			assert.Equal(t, 5, actual.Summary.TotalOrder)
			require.Nil(t, actual.Overview)
		})

		t.Run("get income summary weekly success", func(t *testing.T) {
			t.Parallel()

			// Given
			params := url.Values{}
			params.Add("date", time.Now().Format(time.RFC3339))
			params.Add("granularity", "weekly")
			gctx, recorder := req("driver_a", params)

			api, deps, finish := newTestDriverAdminAPI(t)
			defer finish()

			deps.incomeSummarySvc.EXPECT().Query(gomock.Any(), gomock.Any(), gomock.Any()).Return(
				income.StubIncomeSummaryWeeklySuccess(),
				nil,
			)

			// When
			api.GetIncomeSummary(gctx)

			// Then
			var actual IncomeSummaryResponse
			testutil.DecodeJSON(t, recorder.Body, &actual)
			require.Equal(t, http.StatusOK, recorder.Code)
			assert.Equal(t, types.Money(197), actual.Summary.TotalIncome)
			assert.Equal(t, types.Money(100), actual.Summary.TotalWage)
			assert.Equal(t, types.Money(3), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, types.Money(50), actual.Summary.TotalTipAndOthers)
			assert.Equal(t, types.Money(50), actual.Summary.TotalIncentive)
			assert.Equal(t, 5, actual.Summary.TotalTrip)
			assert.Equal(t, 5, actual.Summary.TotalOrder)
			require.Len(t, actual.Overview.Incomes, 7)

			startDate := time.Date(2022, 12, 12, 0, 0, 0, 0, timeutil.BangkokLocation())
			for _, v := range actual.Overview.Incomes {
				assert.Equal(t, startDate, v.Date.In(timeutil.BangkokLocation()))
				assert.Equal(t, types.Money(1), v.TotalIncome)
				startDate = startDate.AddDate(0, 0, 1)
			}
		})

		t.Run("get income summary monthly success", func(t *testing.T) {
			t.Parallel()

			// Given
			params := url.Values{}
			params.Add("date", time.Now().Format(time.RFC3339))
			params.Add("granularity", "monthly")
			gctx, recorder := req("driver_a", params)

			api, deps, finish := newTestDriverAdminAPI(t)
			defer finish()

			deps.incomeSummarySvc.EXPECT().Query(gomock.Any(), gomock.Any(), gomock.Any()).Return(
				income.StubIncomeSummaryMonthlySuccess(),
				nil,
			)

			// When
			api.GetIncomeSummary(gctx)

			// Then
			var actual IncomeSummaryResponse
			testutil.DecodeJSON(t, recorder.Body, &actual)
			require.Equal(t, http.StatusOK, recorder.Code)
			assert.Equal(t, types.Money(197), actual.Summary.TotalIncome)
			assert.Equal(t, types.Money(100), actual.Summary.TotalWage)
			assert.Equal(t, types.Money(3), actual.Summary.TotalWithholdingTax)
			assert.Equal(t, types.Money(50), actual.Summary.TotalTipAndOthers)
			assert.Equal(t, types.Money(50), actual.Summary.TotalIncentive)
			assert.Equal(t, 5, actual.Summary.TotalTrip)
			assert.Equal(t, 5, actual.Summary.TotalOrder)
			require.Nil(t, actual.Overview)
		})
	})

	t.Run("fail cases", func(t *testing.T) {
		t.Parallel()

		t.Run("fail not send driver id", func(t *testing.T) {
			t.Parallel()

			// Given
			params := url.Values{}

			gctx, recorder := req("", params)

			api, _, finish := newTestDriverAdminAPI(t)
			defer finish()

			// When
			api.GetIncomeSummary(gctx)

			// Then
			var actual absintheApi.Error
			testutil.DecodeJSON(t, recorder.Body, &actual)
			require.Equal(t, http.StatusBadRequest, recorder.Code)
			assert.Equal(t, "INVALID_REQUEST", actual.Code)
			assert.Equal(t, "driver_id is required", actual.Message)
		})

		t.Run("fail not send granularity", func(t *testing.T) {
			t.Parallel()

			// Given
			params := url.Values{}

			gctx, recorder := req("driver_a", params)

			api, _, finish := newTestDriverAdminAPI(t)
			defer finish()

			// When
			api.GetIncomeSummary(gctx)

			// Then
			var actual absintheApi.Error
			testutil.DecodeJSON(t, recorder.Body, &actual)
			require.Equal(t, http.StatusBadRequest, recorder.Code)
			assert.Equal(t, "INVALID_REQUEST", actual.Code)
			assert.Equal(t, "cannot unmarshaling json", actual.Message)
		})

		t.Run("fail not send date", func(t *testing.T) {
			t.Parallel()

			// Given
			params := url.Values{}
			params.Add("granularity", "daily")

			gctx, recorder := req("driver_a", params)

			api, _, finish := newTestDriverAdminAPI(t)
			defer finish()

			// When
			api.GetIncomeSummary(gctx)

			// Then
			var actual absintheApi.Error
			testutil.DecodeJSON(t, recorder.Body, &actual)
			require.Equal(t, http.StatusBadRequest, recorder.Code)
			assert.Equal(t, "INVALID_REQUEST", actual.Code)
			assert.Equal(t, "date is required", actual.Message)
		})
	})
}

func TestDriverAdminAPI_GetBanEffectiveTimeList(t *testing.T) {
	t.Run("happy_flow", func(tt *testing.T) {
		tt.Parallel()

		ctx, recorder := testutil.TestRequestContext("GET", fmt.Sprintf("/v1/admin/bulk/ban/effective-time?page=%v&size=%v", 0, 0), nil)
		accapi, deps, finish := newTestDriverAdminAPI(t)
		defer finish()

		deps.banEffectiveTimeRepo.EXPECT().
			FindWithQueryAndSort(ctx, gomock.Any(), 0, 10, gomock.Any()).
			Return([]model.BanEffectiveTime{}, nil)

		deps.banEffectiveTimeRepo.EXPECT().
			CountWithQuery(ctx, gomock.Any()).
			Return(0, nil)

		accapi.GetBanEffectiveTimeList(ctx)

		assert.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestDriverAdminAPI_CancelBanEffectiveTime(t *testing.T) {
	t.Run("happy_flow", func(tt *testing.T) {
		tt.Parallel()

		batchID := primitive.NewObjectID()
		ctx, recorder := testutil.TestRequestContext("POST", fmt.Sprintf("/v1/bulk/ban/effective-time/%s/cancel", batchID.Hex()), nil)
		ctx.Params = gin.Params{{Key: "effId", Value: batchID.Hex()}}
		accapi, deps, finish := newTestDriverAdminAPI(t)
		defer finish()

		deps.banEffectiveTimeRepo.EXPECT().
			UpdateStatusWithExecutedTime(ctx, batchID, model.BanEffectiveCancelled, time.Time{}).
			Return(nil)

		accapi.CancelBanEffectiveTime(ctx)

		assert.Equal(tt, http.StatusOK, recorder.Code)
	})
}

func TestDriverAdminAPI_BulkGoodness(t *testing.T) {
	defaultTime := time.Date(2023, 1, 29, 19, 0, 0, 0, timeutil.BangkokLocation())
	futureTime := time.Date(2023, 1, 30, 19, 0, 0, 0, timeutil.BangkokLocation())
	eodFutureTime := timeutil.DateCeilingBKK(futureTime)

	shoudlNotCallToUpdateGoodnessFn := func(t *testing.T, driverID string, goodness model.Goodness) error {
		t.Error("should not call to update")
		return nil
	}

	type testFixture struct {
		name               string
		content            io.Reader
		overrideDefaultCfg func(cfg *Config)
		overrideTimeNow    func() time.Time
		expect             func(t *testing.T, recorder *httptest.ResponseRecorder)
		stubGoodness       func(t *testing.T, driverID string, actual model.Goodness) error
	}

	fixtures := []testFixture{
		{
			name: "happy flow - single ",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_A,Rider_L1,2023-01-30`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Failures))
			},
			stubGoodness: func(t *testing.T, driverID string, goodness model.Goodness) error {
				require.Equal(t, "DRIVER_A", driverID)
				require.Equal(t, model.Goodness{
					Level:          "Rider_L1",
					LevelExpiredAt: &eodFutureTime,
				}, goodness)
				return nil
			},
		},
		{
			name: "happy flow - multiple ",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_01,Rider_L1,2023-01-30
DRIVER_02,Rider_L1,2023-01-30
DRIVER_03,Rider_L1,2023-01-30
DRIVER_04,Rider_L1,2023-01-30
DRIVER_05,Rider_L1,2023-01-30
DRIVER_06,Rider_L1,2023-01-30
DRIVER_07,Rider_L1,2023-01-30
DRIVER_08,Rider_L1,2023-01-30
DRIVER_09,Rider_L1,2023-01-30
DRIVER_10,Rider_L1,2023-01-30
DRIVER_11,Rider_L1,2023-01-30
DRIVER_12,Rider_L1,2023-01-30
DRIVER_13,Rider_L1,2023-01-30
DRIVER_14,Rider_L1,2023-01-30
DRIVER_15,Rider_L1,2023-01-30
DRIVER_16,Rider_L1,2023-01-30
DRIVER_17,Rider_L1,2023-01-30
DRIVER_18,Rider_L1,2023-01-30
DRIVER_19,Rider_L1,2023-01-30
DRIVER_20,Rider_L1,2023-01-30
`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 20, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Failures))
				require.ElementsMatch(t, resp.BulkIDReasonResponse.Successes, []string{
					"DRIVER_01",
					"DRIVER_02",
					"DRIVER_03",
					"DRIVER_04",
					"DRIVER_05",
					"DRIVER_06",
					"DRIVER_07",
					"DRIVER_08",
					"DRIVER_09",
					"DRIVER_10",
					"DRIVER_11",
					"DRIVER_12",
					"DRIVER_13",
					"DRIVER_14",
					"DRIVER_15",
					"DRIVER_16",
					"DRIVER_17",
					"DRIVER_18",
					"DRIVER_19",
					"DRIVER_20",
				})
			},
			stubGoodness: func(t *testing.T, driverID string, goodness model.Goodness) error {
				require.Contains(t, []string{
					"DRIVER_01",
					"DRIVER_02",
					"DRIVER_03",
					"DRIVER_04",
					"DRIVER_05",
					"DRIVER_06",
					"DRIVER_07",
					"DRIVER_08",
					"DRIVER_09",
					"DRIVER_10",
					"DRIVER_11",
					"DRIVER_12",
					"DRIVER_13",
					"DRIVER_14",
					"DRIVER_15",
					"DRIVER_16",
					"DRIVER_17",
					"DRIVER_18",
					"DRIVER_19",
					"DRIVER_20",
				}, driverID)
				require.Equal(t, model.Goodness{
					Level:          "Rider_L1",
					LevelExpiredAt: &eodFutureTime,
				}, goodness)
				return nil
			},
		},
		{
			name: "happy flow - clear goodness",
			content: strings.NewReader(`driver_id,rider_level,expired_at
RIDER_A,,`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Failures))
			},
			stubGoodness: func(t *testing.T, driverID string, actual model.Goodness) error {
				require.Equal(t, "RIDER_A", driverID)
				require.Equal(t, model.Goodness{
					Level:          "",
					LevelExpiredAt: &time.Time{},
				}, actual)
				return nil
			},
		},
		{
			name: "happy flow - valid rider level and empty expired_at should default at 1 week ahead",
			content: strings.NewReader(`driver_id,rider_level,expired_at
RIDER_A,Rider_L1,`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Failures))
			},
			stubGoodness: func(t *testing.T, driverID string, actual model.Goodness) error {
				require.Equal(t, "RIDER_A", driverID)
				eod := timeutil.DateCeilingBKK(time.Date(2023, 2, 5, 0, 0, 0, 0, timeutil.BangkokLocation()))
				require.Equal(t, model.Goodness{
					Level:          "Rider_L1",
					LevelExpiredAt: &eod,
				}, actual)
				return nil
			},
		},
		{
			name: "unhappy flow - max row exceeded",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_A,Rider_L1,2023-01-30
DRIVER_B,Rider_L1,2023-01-30`),
			overrideDefaultCfg: func(cfg *Config) {
				cfg.BulkUpdateDriverGoodnessMaxCSVRows = 1
			},
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusBadRequest, recorder.Code)
				var resp absintheApi.Error
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, absintheApi.ERRCODE_INVALID_REQUEST, resp.Code)
				require.Equal(t, "exceed max row got [2], max [1]", resp.Message)
			},
			stubGoodness: shoudlNotCallToUpdateGoodnessFn,
		},
		{
			name: "unhappy flow - error driver id not exists",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_NOT_EXISTS,Rider_L1,2023-01-30`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Failures))
			},
			stubGoodness: shoudlNotCallToUpdateGoodnessFn,
		},
		{
			name: "unhappy flow - error unique driver id",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_A,Rider_L1,2023-01-30
DRIVER_A,Rider_L1,2023-01-30`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Failures))

				m := utils.SliceToMapKeepFirst(func(f Failure) string {
					return f.ID
				}, func(f Failure) Failure {
					return f
				}, resp.BulkIDReasonResponse.Failures)

				failDriver := m["DRIVER_A"]
				require.Equal(t, "DRIVER_A", failDriver.ID)
				require.ElementsMatch(t, strings.Split(failDriver.Reason, ","), []string{"duplicate driver id on row 2"})
			},
			stubGoodness: func(t *testing.T, driverID string, goodness model.Goodness) error {
				require.Equal(t, "DRIVER_A", driverID)
				require.Equal(t, model.Goodness{
					Level:          "Rider_L1",
					LevelExpiredAt: &eodFutureTime,
				}, goodness)
				return nil
			},
		},
		{
			name: "unhappy flow - error invalid expired_at",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_A,Rider_L1,2023-01-30
DRIVER_B,Rider_L1,invalid`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Failures))

				m := utils.SliceToMapKeepFirst(func(f Failure) string {
					return f.ID
				}, func(f Failure) Failure {
					return f
				}, resp.BulkIDReasonResponse.Failures)

				failDriver := m["DRIVER_B"]
				require.Equal(t, "DRIVER_B", failDriver.ID)
				require.ElementsMatch(t, strings.Split(failDriver.Reason, ","), []string{"invalid expired_at format: parsing time \"invalid\" as \"2006-01-02\": cannot parse \"invalid\" as \"2006\""})
			},
			stubGoodness: func(t *testing.T, driverID string, goodness model.Goodness) error {
				require.Equal(t, "DRIVER_A", driverID)
				require.Equal(t, model.Goodness{
					Level:          "Rider_L1",
					LevelExpiredAt: &eodFutureTime,
				}, goodness)
				return nil
			},
		},
		{
			name: "unhappy flow - multiple validation errors",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_A,RIDER_INVALID,invalid
DRIVER_NOT_EXISTS,RIDER_INVALID,invalid`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 2, len(resp.BulkIDReasonResponse.Failures))

				m := utils.SliceToMapKeepFirst(func(f Failure) string {
					return f.ID
				}, func(f Failure) Failure {
					return f
				}, resp.BulkIDReasonResponse.Failures)

				failDriver := m["DRIVER_A"]
				require.Equal(t, "DRIVER_A", failDriver.ID)
				require.ElementsMatch(t, strings.Split(failDriver.Reason, ","), []string{"invalid rider level", "invalid expired_at format: parsing time \"invalid\" as \"2006-01-02\": cannot parse \"invalid\" as \"2006\""})

				failDriver = m["DRIVER_NOT_EXISTS"]
				require.Equal(t, "DRIVER_NOT_EXISTS", failDriver.ID)
				require.ElementsMatch(t, strings.Split(failDriver.Reason, ","), []string{"driver id is not exists", "invalid rider level", "invalid expired_at format: parsing time \"invalid\" as \"2006-01-02\": cannot parse \"invalid\" as \"2006\""})
			},
			stubGoodness: shoudlNotCallToUpdateGoodnessFn,
		},
		{
			name: "unhappy flow - fail db",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_A,Rider_L1,2023-01-30
FAIL_DB,Rider_L1,2023-01-30
`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Failures))

				m := utils.SliceToMapKeepFirst(func(f Failure) string {
					return f.ID
				}, func(f Failure) Failure {
					return f
				}, resp.BulkIDReasonResponse.Failures)

				failDriver := m["FAIL_DB"]
				require.Equal(t, "FAIL_DB", failDriver.ID)
				require.ElementsMatch(t, strings.Split(failDriver.Reason, ","), []string{"unable to set goodness: fail db"})
			},
			stubGoodness: func(t *testing.T, driverID string, goodness model.Goodness) error {
				if driverID == "DRIVER_A" {
					require.Equal(t, "DRIVER_A", driverID)
					require.Equal(t, model.Goodness{
						Level:          "Rider_L1",
						LevelExpiredAt: &eodFutureTime,
					}, goodness)
					return nil
				}
				return errors.New("fail db")
			},
		},
		{
			name: "unhappy flow - expired_at is past",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_A,Rider_L1,2023-01-28`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Failures))

				m := utils.SliceToMapKeepFirst(func(f Failure) string {
					return f.ID
				}, func(f Failure) Failure {
					return f
				}, resp.BulkIDReasonResponse.Failures)

				failDriver := m["DRIVER_A"]
				require.Equal(t, "DRIVER_A", failDriver.ID)
				require.ElementsMatch(t, strings.Split(failDriver.Reason, ","), []string{"expired_at should be future"})
			},
			stubGoodness: shoudlNotCallToUpdateGoodnessFn,
		},
		{
			name: "unhappy flow - rider_level empty but valid expired_at should error",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_A,,2023-01-30`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Failures))

				m := utils.SliceToMapKeepFirst(func(f Failure) string {
					return f.ID
				}, func(f Failure) Failure {
					return f
				}, resp.BulkIDReasonResponse.Failures)

				failDriver := m["DRIVER_A"]
				require.Equal(t, "DRIVER_A", failDriver.ID)
				require.ElementsMatch(t, strings.Split(failDriver.Reason, ","), []string{"rider_level should not be empty when expired_at is not empty"})
			},
			stubGoodness: shoudlNotCallToUpdateGoodnessFn,
		},
		{
			name: "unhappy flow - driver is not exist but rider_level and expired_at are empty",
			content: strings.NewReader(`driver_id,rider_level,expired_at
DRIVER_NOT_EXISTS,,`),
			expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
				require.Equal(t, http.StatusOK, recorder.Code)
				var resp BulkUpdateDriverGoodnessResponse
				testutil.DecodeJSON(t, recorder.Body, &resp)
				require.Equal(t, 0, len(resp.BulkIDReasonResponse.Successes))
				require.Equal(t, 1, len(resp.BulkIDReasonResponse.Failures))

				m := utils.SliceToMapKeepFirst(func(f Failure) string {
					return f.ID
				}, func(f Failure) Failure {
					return f
				}, resp.BulkIDReasonResponse.Failures)

				failDriver := m["DRIVER_NOT_EXISTS"]
				require.Equal(t, "DRIVER_NOT_EXISTS", failDriver.ID)
				require.ElementsMatch(t, strings.Split(failDriver.Reason, ","), []string{"driver id is not exists"})
			},
			stubGoodness: shoudlNotCallToUpdateGoodnessFn,
		},
	}

	riderLevelFixtures := func() []testFixture {
		tfs := []testFixture{}
		allowedRiderLevel := []prediction.RiderLevel{
			"Rider_L1",
			"Rider_L2",
			"Rider_L3",
			"Rider_L4",
			"Rider_L5",
			"Rider_L6",
			"Rider_L7",
			"Rider_L8",
			"Rider_L9",
			"Rider_L10",
		}

		for _, v := range allowedRiderLevel {
			copy := v
			tf := testFixture{
				name: fmt.Sprintf("happy flow - rider level is %s", copy),
				content: strings.NewReader(fmt.Sprintf(`driver_id,rider_level,expired_at
DRIVER_A,%s,2023-01-30`, copy)),
				expect: func(t *testing.T, recorder *httptest.ResponseRecorder) {
					require.Equal(t, http.StatusOK, recorder.Code)
					var resp BulkUpdateDriverGoodnessResponse
					testutil.DecodeJSON(t, recorder.Body, &resp)
					require.Equal(t, 1, len(resp.BulkIDReasonResponse.Successes))
					require.Equal(t, 0, len(resp.BulkIDReasonResponse.Failures))
				},
				stubGoodness: func(t *testing.T, driverID string, goodness model.Goodness) error {
					require.Equal(t, "DRIVER_A", driverID)
					require.Equal(t, model.Goodness{
						Level:          copy,
						LevelExpiredAt: &eodFutureTime,
					}, goodness)
					return nil
				},
			}
			tfs = append(tfs, tf)
		}
		return tfs
	}

	fixtures = append(fixtures, riderLevelFixtures()...)

	for _, f := range fixtures {
		t.Run(f.name, func(t *testing.T) {
			makeReq := func(content interface{}) (*gin.Context, *httptest.ResponseRecorder) {
				gctx := testutil.NewContextWithRecorder()
				gctx.SetPUT("/bulk/drivers/goodness").
					Body().MultipartForm().
					String("createdBy", "admin").
					File("file", "file.csv", content).Build()
				return gctx.GinCtx(), gctx.ResponseRecorder
			}

			content := f.content

			gctx, recorder := makeReq(content)

			cfg := ProvideAdminConfig()
			if f.overrideDefaultCfg != nil {
				f.overrideDefaultCfg(&cfg)
			}

			api, deps, finish := newTestDriverAdminAPIWithAdminCfg(t, cfg, &service.AtomicCancelReasonConfig{})
			defer finish()

			defaultStubExists := func(ctx context.Context, query repository.DriverQuery, selector []string, sort []string, skip, limit int, opts ...repository.Option) ([]model.Driver, error) {
				mq, ok := query.(*persistence.MongoDriverQuery)
				if !ok {
					return nil, errors.New("query must be MongoGroupTransactionQuery")
				}
				driverIds := mq.DriverIds()
				drivers := make([]model.Driver, len(driverIds))
				for idx, v := range driverIds {
					if v == "DRIVER_NOT_EXISTS" {
						continue
					}
					drivers[idx] = model.Driver{DriverID: v}
				}
				return drivers, nil
			}

			deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(defaultStubExists).AnyTimes()

			deps.driverRepo.EXPECT().SetGoodness(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driverID string, goodness model.Goodness) error {
				return f.stubGoodness(t, driverID, goodness)
			}).AnyTimes()

			gctx = timeutil.NewGinContextWithTime(gctx, defaultTime)
			if f.overrideTimeNow != nil {
				gctx = timeutil.NewGinContextWithTime(gctx, f.overrideTimeNow())
			}

			// When
			api.BulkGoodness(gctx)

			// Then
			t.Log(recorder.Body)
			f.expect(t, recorder)
		})
	}
}

func TestDriverAdminAPI_GetFullTimeDriverVendorList(t *testing.T) {
	t.Parallel()

	req := func() (*gin.Context, *httptest.ResponseRecorder) {
		gctx := testutil.NewContextWithRecorder().
			SetGET("/v1/admin/full-time-driver-vendor-list")

		gctx.FakeAdminCtxAuthorized("admin", fake.CharactersN(20), "<EMAIL>")
		return gctx.GinCtx(), gctx.ResponseRecorder
	}

	t.Run("get success", func(t *testing.T) {
		t.Parallel()

		// Given
		gctx, recorder := req()

		api, _, finish := newTestDriverAdminAPI(t, WithAtomicAdminConfig(&config.AtomicAdminConfig{
			Config: config.AdminConfig{
				FullTimeDriverVendorList: []string{
					"VENDOR_A", "VENDOR_B", "VENDOR_C", "VENDOR_SPACE_BACK   ", "   VENDOR_SPACE_FRONT",
				},
			},
		}))
		defer finish()

		// When
		api.GetFullTimeDriverVendorList(gctx)

		// Then
		expected := FullTimeDriverVendorList{
			Vendors: []string{"VENDOR_A", "VENDOR_B", "VENDOR_C", "VENDOR_SPACE_BACK", "VENDOR_SPACE_FRONT"},
		}

		var actual FullTimeDriverVendorList
		testutil.DecodeJSON(t, recorder.Body, &actual)
		require.Equal(t, http.StatusOK, recorder.Code)
		require.Equal(t, expected, actual)
	})

	t.Run("get while a config not set", func(t *testing.T) {
		t.Parallel()

		// Given
		gctx, recorder := req()

		api, _, finish := newTestDriverAdminAPI(t, WithAtomicAdminConfig(&config.AtomicAdminConfig{
			Config: config.AdminConfig{
				FullTimeDriverVendorList: nil,
			},
		}))
		defer finish()

		// When
		api.GetFullTimeDriverVendorList(gctx)

		// Then
		expected := FullTimeDriverVendorList{
			Vendors: []string{},
		}

		var actual FullTimeDriverVendorList
		testutil.DecodeJSON(t, recorder.Body, &actual)
		require.Equal(t, http.StatusOK, recorder.Code)
		require.Equal(t, expected, actual)
	})
}
