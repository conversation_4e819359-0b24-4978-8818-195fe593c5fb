package admin

import (
	"strings"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson/primitive"

	absintheApi "git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

type BanMetadataAPI struct {
	banService service.BanService
}

func (m *BanMetadataAPI) Create(gctx *gin.Context) {
	var banMetadataRequest BanMetadataRequest
	err := gctx.ShouldBindJSON(&banMetadataRequest)
	if err != nil {
		_ = gctx.Error(apiutil.NewFromError(absintheApi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if banMetadataRequest.ID != "" {
		_ = gctx.Error(apiutil.NewFromString(absintheApi.ERRCODE_INVALID_REQUEST, "id must be empty"))
		return
	}

	banMetadata, err := banMetadataRequest.GetBanMetadata()
	if err != nil {
		_ = gctx.Error(apiutil.NewFromError(absintheApi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err = m.banService.VerifyMetadata(gctx, banMetadata); err != nil {
		_ = gctx.Error(apiutil.NewFromError(absintheApi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err = m.banService.CreateMetadata(gctx, banMetadata); err != nil {
		_ = gctx.Error(err)
		return
	}

	apiutil.NoContent(gctx)
}

func (m *BanMetadataAPI) List(gctx *gin.Context) {
	banMetadataList, err := m.banService.ListMetadata(gctx)
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	list := NewBanMetadataListResponse(banMetadataList)

	apiutil.OKList(gctx, list, len(list))
}

func (m *BanMetadataAPI) Update(gctx *gin.Context) {
	var banMetadataRequest BanMetadataRequest
	err := gctx.ShouldBindJSON(&banMetadataRequest)
	if err != nil {
		_ = gctx.Error(apiutil.NewFromError(absintheApi.ERRCODE_INVALID_REQUEST, err))
		apiutil.ErrInternalError(gctx, utils.ErrorStructResponse(err))
		return
	}

	banMetadata, err := banMetadataRequest.GetBanMetadata()
	if err != nil || banMetadata.ID == primitive.NilObjectID {
		_ = gctx.Error(apiutil.NewFromError(absintheApi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err = m.banService.VerifyMetadata(gctx, banMetadata); err != nil {
		_ = gctx.Error(apiutil.NewFromError(absintheApi.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if err = m.banService.UpdateMetadata(gctx, banMetadata); err != nil {
		_ = gctx.Error(err)
		return
	}

	apiutil.NoContent(gctx)
}

func (m *BanMetadataAPI) Delete(gctx *gin.Context) {
	err := m.banService.DeleteMetadata(gctx, strings.TrimSpace(gctx.Query("id")))
	if err != nil {
		_ = gctx.Error(err)
		return
	}

	apiutil.NoContent(gctx)
}

func ProvideBanMetadataAPI(banService service.BanService) *BanMetadataAPI {
	return &BanMetadataAPI{
		banService: banService,
	}
}
