package admin

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type DriverCancelReasonRes struct {
	ID                       string                     `json:"id"`
	Name                     string                     `json:"name"`
	Label                    string                     `json:"label"`
	ServiceType              model.Service              `json:"serviceType"`
	CancelByRider            cancelByRiderRes           `json:"cancelByRider"`
	IsPhotoRequireForZendesk bool                       `json:"isPhotoRequireForZendesk"`
	Photo                    cancelPhotoRes             `json:"photo"`
	WarningMessage           string                     `json:"warningMessage"`
	HintTitle                string                     `json:"hintTitle"`
	HintMessage              string                     `json:"hintMessage"`
	UpdatedAt                time.Time                  `json:"updatedAt"`
	Priority                 int                        `json:"priority,omitempty"`
	CancellationRateFree     bool                       `json:"cancellationRateFree"`
	ShouldAutoClaim          bool                       `json:"shouldAutoClaim"`
	MaxBasketSize            int                        `json:"maxBasketSize"`
	AllowOrderStatus         []string                   `json:"allowOrderStatus"`
	IsReassign               bool                       `json:"isReassign"`
	MaxDistanceMeter         types.Distance             `json:"maxDistanceMeter"`
	MaximumUploadPhoto       int                        `json:"maximumUploadPhoto"`
	Message                  model.CancelDisplayMessage `json:"message"`
	CancellationSource       string                     `json:"cancellationSource"`
	WMAMetric                string                     `json:"wmaMetric"`
}

type cancelByRiderRes struct {
	IsAllowed           bool `json:"isAllowed"`
	Count               int  `json:"count"`
	IntervalInDays      int  `json:"intervalInDays"`
	IsPhotoRequired     bool `json:"isPhotoRequired"`
	BanDurationInMinute int  `json:"banDurationInMinute"`
	MaximumUploadPhoto  int  `json:"maximumUploadPhoto"`
}

type cancelPhotoRes struct {
	Instruction      string `json:"instruction"`
	PreviewImageURL  string `json:"previewImageURL"`
	CallToActionText string `json:"callToActionText"`
}

func NewDriverCancelReasonRes(m *model.CancelReason) *DriverCancelReasonRes {
	return &DriverCancelReasonRes{
		ID:          m.ID.Hex(),
		Name:        m.Name,
		Label:       m.Label,
		ServiceType: m.ServiceType,
		CancelByRider: cancelByRiderRes{
			IsAllowed:           m.CancelByRider.IsAllowed,
			Count:               m.CancelByRider.Count,
			IntervalInDays:      m.CancelByRider.IntervalInDays,
			IsPhotoRequired:     m.CancelByRider.IsPhotoRequired,
			MaximumUploadPhoto:  m.CancelByRider.MaximumUploadPhoto,
			BanDurationInMinute: m.CancelByRider.BanDurationInMinute,
		},
		IsPhotoRequireForZendesk: m.IsPhotoRequiredForZendesk,
		Photo: cancelPhotoRes{
			Instruction:      m.Photo.Instruction,
			PreviewImageURL:  m.Photo.PreviewImageURL,
			CallToActionText: m.Photo.CallToActionText,
		},
		WarningMessage:       m.WarningMessage,
		HintTitle:            m.HintTitle,
		HintMessage:          m.HintMessage,
		UpdatedAt:            m.UpdatedAt,
		Priority:             m.Priority,
		CancellationRateFree: m.CancellationRateFree,
		ShouldAutoClaim:      m.ShouldAutoClaim,
		MaxBasketSize:        m.MaxBasketSize,
		AllowOrderStatus:     m.AllowOrderStatus,
		IsReassign:           m.IsReassign,
		MaxDistanceMeter:     m.MaxDistanceMeter,
		Message:              m.Message,
		CancellationSource:   m.CancellationSource,
		WMAMetric:            m.WMAMetric,
	}
}
