package admin

import (
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type BaseBanMetadata struct {
	ID              string   `json:"id"`
	Category        string   `json:"category"`
	SubCategory     string   `json:"subCategory"`
	MessageToDriver string   `json:"messageToDriver"`
	Financial       bool     `json:"financial"`
	Roles           []string `json:"roles"`
}

func (c *BaseBanMetadata) GetBanMetadata() (*model.BanMetadata, error) {
	var objectID primitive.ObjectID
	if c.ID == "" {
		objectID = primitive.NilObjectID
	} else {
		var err error
		objectID, err = primitive.ObjectIDFromHex(c.ID)
		if err != nil {
			return nil, err
		}
	}

	return &model.BanMetadata{
		ID:              objectID,
		Category:        c.Category,
		SubCategory:     c.SubCategory,
		MessageToDriver: c.MessageToDriver,
		Financial:       c.Financial,
		Roles:           c.Roles,
	}, nil
}

type BanMetadataRequest struct {
	BaseBanMetadata
}

type BanMetadataResponse struct {
	BaseBanMetadata
}

func NewBanMetadataResponse(b *model.BanMetadata) *BanMetadataResponse {
	return &BanMetadataResponse{
		BaseBanMetadata{
			ID:              b.ID.Hex(),
			Category:        b.Category,
			SubCategory:     b.SubCategory,
			MessageToDriver: b.MessageToDriver,
			Financial:       b.Financial,
			Roles:           b.Roles,
		},
	}
}

func NewBanMetadataListResponse(bl []model.BanMetadata) []BanMetadataResponse {
	result := make([]BanMetadataResponse, len(bl))

	for i := 0; i < len(bl); i++ {
		result[i] = *NewBanMetadataResponse(&bl[i])
	}

	return result
}
