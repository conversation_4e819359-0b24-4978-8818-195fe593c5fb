package admin

import (
	"bytes"
	"encoding/csv"
	stderr "errors"
	"fmt"
	"io"
	"mime/multipart"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/account"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/csvutils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type ValidateErr struct {
	reason string
}

func NewValidateError(reason string) ValidateErr {
	return ValidateErr{reason: reason}
}

func (e ValidateErr) Error() string {
	return e.reason
}

const (
	ErrMaximumRow                                 string = "over maximum rows"
	ErrDuplicateRow                               string = "duplicate rows"
	ErrCannotReadCSV                              string = "cannot read csv file"
	ErrInvalidServiceTypes                        string = "invalid service types"
	ErrInvalidDriverTierStructure                 string = "invalid file structure, the csv file must has only 2 columns (Driver ID, Tier)"
	ErrInvalidServiceTypesSilentBannedStructure   string = "invalid file structure, the csv file must has only 2 columns (Driver ID, Service Types)"
	ErrInvalidToggleOnRiderServicePreference      string = "invalid file structure, the csv file must has only 2 columns (Driver ID, Service Types)"
	ErrInvalidUpdateDriverDedicatedZonesStructure string = "invalid file structure, the csv file must has only 2 columns (Driver ID, Dedicated Zone Labels)"
	ErrInvalidUpdateDriverServiceTypes            string = "invalid file structure, the csv file must has only 2 columns (Driver ID, Service Types)"
	ErrInvalidUpdateIncentiveStructure            string = "invalid file structure, the csv file must has only 25 columns (Please download the template and fill in the data)"
)

type BanDriverWithMetadataRequest struct {
	ReasonsMetadata           []*model.BanReasonMetadata `json:"reasonsMetadata"`
	BannedUntil               *time.Time                 `json:"bannedUntil"`
	BanType                   model.BanType              `json:"banType"`
	Url                       string                     `json:"url"`
	MessageToDriver           string                     `json:"messageToDriver"`
	Category                  string                     `json:"category"`
	Reason                    string                     `json:"reason"`
	RetrainingTitle           string                     `json:"retrainingTitle"`
	RetrainingMessageToDriver string                     `json:"retrainingMessageToDriver"`
	CreatedBy                 string                     `json:"createdBy"`
}

// BanDriverRequest is request payload for ban driver api.
type BanDriverRequest struct {
	// Reason is detail why admin ban this driver.
	Reason string `json:"reason" form:"reason" binding:"required"`

	// Type is ban type. Permanent or Temporary.
	Type string `json:"type" form:"type" binding:"required"`

	// Category is kind of ban. For example, "Fraud related", "Driver behaviour", "Document expired".
	Category string `json:"category" form:"category" binding:"required"`

	// MessageToDriver ...
	MessageToDriver string `json:"messageToDriver" form:"messageToDriver"`

	// BanAmount how long to ban this driver.
	BanAmount int `json:"banAmount" form:"banAmount"`

	// BannedUntil tells that will ban driver until this day.
	BannedUntil time.Time `json:"bannedUntil" form:"bannedUntil"`

	// CreatedBy is who's ban.
	CreatedBy string `json:"createdBy" form:"createdBy" binding:"required"`

	MetadataId             string `json:"metadataId" form:"metadataId"`
	MetadataInternalReason string `json:"metadataInternalReason" form:"metadataInternalReason"`

	Url                       string `json:"url" form:"url"`
	RetrainingTitle           string `json:"retrainingTitle" form:"retrainingTitle"`
	RetrainingMessageToDriver string `json:"retrainingMessageToDriver" form:"retrainingMessageToDriver"`

	EffectiveTime time.Time `json:"effectiveTime" form:"effectiveTime"`
}

// UnbanDriverRequest is request payload for unban driver api.
type UnbanDriverRequest struct {
	Reason        string                `form:"reason" json:"reason" binding:"required"`
	CreatedBy     string                `form:"createdBy" json:"createdBy" binding:"required"`
	UnbanImageRef *multipart.FileHeader `form:"unbanImageRef"`
}

// ListDriverRequest is driver request query
type ListDriverRequest struct {
	DriverID         string               `form:"driverId"`
	CitizenID        string               `form:"citizenId"`
	Firstname        string               `form:"firstname"`
	Lastname         string               `form:"lastname"`
	Phone            string               `form:"phone"`
	Status           model.DriverStatus   `form:"status"`
	DriverType       string               `form:"driverType"`
	Trained          string               `form:"trained"`
	BoxTypes         []string             `form:"boxTypes"`
	BeginAt          time.Time            `form:"beginAt"`
	EndAt            time.Time            `form:"endAt"`
	Regions          []string             `form:"regions"`
	ProfileStatus    model.ProfileStatus  `form:"profileStatus"`
	AssignedReviewer string               `form:"assignedReviewer"`
	CitizenIDs       []string             `form:"citizenIds"`
	DriverRole       string               `form:"driverRole"`
	CriminalStatus   model.CriminalStatus `form:"criminalStatus"`
	ExcludeStatus    []string             `form:"excludeStatus"`
	LicensePlate     string               `form:"licensePlate"`
	EffectiveTime    time.Time            `form:"effectiveTime"`
}

type BulkBanDriverRequest struct {
	BanDriverRequest BanDriverRequest

	// File file contain list of driver id to Ban
	File *multipart.FileHeader `form:"file" binding:"required"`
}

type BulkUpdateDriverTierRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

type DriverTierAndNegativeGroup struct {
	DriverID      string
	Tier          model.TierDisplayName
	NegativeGroup string
}

func (req *BanDriverRequest) NewBanUntil() time.Time {
	now := timeutil.BangkokNow()
	until := now.Add(time.Hour * 24 * time.Duration(req.BanAmount))
	return until
}

func (req *BulkUpdateDriverTierRequest) GetTierDataRows(maxSize int) ([][]string, error) {
	file, err := req.File.Open()
	if err != nil {
		return nil, err
	}

	defer file.Close()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, NewValidateError(errors.Wrapf(err, ErrCannotReadCSV).Error())
	}
	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	if len(rows) > maxSize {
		return nil, NewValidateError(ErrMaximumRow)
	}

	for _, columns := range rows {
		if len(columns) < 2 {
			return nil, NewValidateError(ErrInvalidDriverTierStructure)
		}
	}

	return rows, nil
}

func (req *BulkBanDriverRequest) GetDriverIDs() ([]string, error) {
	file, err := req.File.Open()
	if err != nil {
		return nil, errors.New("cannot open csv file")
	}
	defer file.Close()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrapf(err, "cannot read csv file")
	}
	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	if len(rows) > 5000 {
		return nil, NewValidateError(ErrMaximumRow)
	}

	driverIds := make([]string, 0, len(rows))

	for _, columns := range rows {
		driverIds = append(driverIds, strings.TrimSpace(columns[0]))
	}
	return driverIds, nil
}

// BulkUnbanDriverRequest is request payload for bulk unban driver api.
type BulkUnbanDriverRequest struct {
	UnbanDriverRequest UnbanDriverRequest
	// File file contain list of driver id to Ban
	File *multipart.FileHeader `form:"file" binding:"required"`
}

func (req *BulkUnbanDriverRequest) GetDriverIDs() ([]string, error) {
	file, err := req.File.Open()
	if err != nil {
		return nil, errors.New("cannot open csv file")
	}
	defer file.Close()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrapf(err, "cannot read csv file")
	}
	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	if len(rows) > 5000 {
		return nil, NewValidateError(ErrMaximumRow)
	}

	driverIds := make([]string, 0, len(rows))

	for _, columns := range rows {
		driverIds = append(driverIds, strings.TrimSpace(columns[0]))
	}
	return driverIds, nil
}

func (r *ListDriverRequest) Query() repository.DriverQuery {
	boxTypes := transFormBoxTypeRequest(r.BoxTypes)

	q := persistence.BuildDriverQuery()
	if r.Status != "" {
		q = q.WithStatus(r.Status)
	}

	if r.CitizenID != "" {
		q = q.WithCitizenID(r.CitizenID)
	}

	if r.CitizenIDs != nil {
		q = q.WithCitizenIDs(r.CitizenIDs)
	}

	if r.Firstname != "" {
		q = q.WithFirstName(r.Firstname)
	}

	if r.Lastname != "" {
		q = q.WithLastName(r.Lastname)
	}

	if r.Phone != "" {
		q = q.WithPhone(r.Phone)
	}

	if r.Trained != "" {
		q = q.WithTrained(r.Trained)
	}

	if r.DriverID != "" {
		q = q.WithDriverID(r.DriverID)
	}

	if r.DriverType != "" {
		q = q.WithDriverType(r.DriverType)
	}

	if r.Status != "" {
		q = q.WithStatus(r.Status)
	}

	if len(boxTypes) != 0 {
		q = q.WithBoxTypes(boxTypes)
	}

	if !r.BeginAt.IsZero() {
		q = q.WithBeginAt(r.BeginAt)
	}

	if !r.EndAt.IsZero() {
		q = q.WithEndAt(r.EndAt)
	}

	if r.DriverID != "" {
		q = q.WithDriverID(r.DriverID)
	}

	if r.CitizenID != "" {
		q = q.WithCitizenID(r.CitizenID)
	}

	if len(r.Regions) != 0 {
		q = q.WithInRegions(r.Regions)
	}

	if r.ProfileStatus != "" {
		q = q.WithProfileStatus(r.ProfileStatus)
	}

	if r.AssignedReviewer != "" {
		q = q.WithAssignedReviewer(r.AssignedReviewer)
	}

	if r.DriverRole != "" {
		q = q.WithDriverRole(r.DriverRole)
	}

	if r.CriminalStatus != "" {
		q = q.WithCriminalStatus(r.CriminalStatus)
	}

	if r.ExcludeStatus != nil {
		q = q.WithoutStatus(r.ExcludeStatus)
	}

	if r.LicensePlate != "" {
		q = q.WithLicensePlate(r.LicensePlate)
	}

	return q
}

func transFormBoxTypeRequest(boxTypeReq []string) []string {
	var res []string
	var r string

	// Not select box type or select all box type
	if len(boxTypeReq) == 0 || (len(boxTypeReq) == 1 && boxTypeReq[0] == "") {
		return res
	}

	for _, v := range boxTypeReq {
		r = v
		if v == "UNKNOWN" {
			r = model.BoxTypeUnknown.String()
		}
		res = append(res, r)
	}
	return res
}

// SetHaveBoxRequst is driver list to set or unset haveBox
type SetHaveBoxRequst struct {
	DriverIDs []string `json:"driverIds" binding:"required,min=1"`
	HaveBox   bool     `json:"haveBox"`
}

func (req *BulkUpdateProfileRequest) GetDriverIDs() ([]string, error) {
	file, err := req.File.Open()
	if err != nil {
		return nil, errors.New("cannot open csv file")
	}
	defer file.Close()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, errors.Wrapf(err, "cannot read csv file")
	}
	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	if len(rows) > 5000 {
		return nil, NewValidateError(ErrMaximumRow)
	}

	driverIds := types.NewStringSet()

	for _, columns := range rows {
		driverIds.Add(strings.TrimSpace(columns[0]))
	}
	return driverIds.GetElements(), nil
}

type BulkAssignReviewerRequest struct {
	DriverIDs []string `json:"driverIds" binding:"required,min=1"`
	Reviewer  string   `json:"reviewer" binding:"required"`
}

type ListDriversByCitizenIdsRequest struct {
	DriverCitizenIds []string `json:"driverCitizenIds" binding:"required"`
}

type ReactivateDriverReq struct {
	Requester string `json:"requester" binding:"required"`
}

type ApproveRequestUpdateProfileRequest struct {
	Requester string `json:"requester" binding:"required"`
}

type RejectRequestUpdateProfileRequest struct {
	Remark    string `json:"remark" binding:"required"`
	Requester string `json:"requester" binding:"required"`
}

type UpdateARCRStatisticRequest struct {
	Date                     string        `json:"date" binding:"required" validate:"datetime=02/01/2006"`
	Service                  model.Service `json:"service"`
	AutoAssignedAccepted     *int          `json:"autoAssignedAccepted,omitempty"`
	AutoAssigned             *int          `json:"autoAssigned,omitempty"`
	CancelledNotFree         *int          `json:"cancelledNotFree,omitempty"`
	Accepted                 *int          `json:"accepted,omitempty"`
	AutoAssignedRain         *int          `json:"autoAssignedRain,omitempty"`
	AutoAssignedAcceptedRain *int          `json:"autoAssignedAcceptedRain,omitempty"`
}

type BulkUpdateProfileRequest struct {
	UpdateProfileRequest UpdateProfileRequest
	File                 *multipart.FileHeader `form:"file" binding:"required"`
}

type UpdateProfileRequest struct {
	// Section is topic for the request to update.
	SectionIDs      string `json:"sectionIDs" form:"sectionIDs" binding:"required"`
	MessageToDriver string `json:"messageToDriver" form:"messageToDriver"`
}

type RequestToUpdateRequestUpdateProfileStatus struct {
	Status          model.RequestProfileStatus `json:"status" form:"status"`
	MessageToDriver string                     `json:"message" form:"message"`
	RequestIDs      []string                   `json:"requestIds" form:"requestIds" binding:"required"`
}

type BulkUpdateDriverARCRRequest struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

func (req *BulkUpdateDriverARCRRequest) GetDataRows(maxSize int) (BulkUpdateDriverARCRRows, error) {
	file, err := req.File.Open()
	if err != nil {
		return nil, err
	}

	defer file.Close()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return nil, NewValidateError(errors.Wrapf(err, ErrCannotReadCSV).Error())
	}
	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	if len(rows) > maxSize {
		return nil, NewValidateError(fmt.Sprintf("%v which max row size is %v", ErrMaximumRow, maxSize))
	}

	var bulkUpdateDriverARCRRow []BulkUpdateDriverARCRRow
	for rowIdx, columns := range rows {
		var r BulkUpdateDriverARCRRow
		err := parseBulkUpdateDriverARCRRow(rowIdx, columns, &r)
		if err != nil {
			return nil, NewValidateError(fmt.Errorf("unable to parse row %v: %w", rowIdx+1, err).Error())
		}
		bulkUpdateDriverARCRRow = append(bulkUpdateDriverARCRRow, r)
	}
	return bulkUpdateDriverARCRRow, nil
}

type BulkUpdateDriverARCRRows []BulkUpdateDriverARCRRow

func (r BulkUpdateDriverARCRRows) groupByDate() map[time.Time]BulkUpdateDriverARCRRows {
	m := map[time.Time]BulkUpdateDriverARCRRows{}
	for _, v := range r {
		date := v.TargetDate
		rows, exist := m[date]
		if !exist {
			m[date] = BulkUpdateDriverARCRRows{v}
			continue
		}
		rows = append(rows, v)
		m[date] = rows
	}
	return m
}

func (r BulkUpdateDriverARCRRows) uniqueDrivers() []string {
	set := types.NewStringSet()
	for _, v := range r {
		set.Add(v.DriverId)
	}

	return set.GetElements()
}

type BulkUpdateDriverARCRRow struct {
	RowIdx     int
	DriverId   string
	TargetDate time.Time
	AR         *float64
	CR         *float64
	Service    model.Service
	Command    BulkUpdateARCRCommand
}

func parseBulkUpdateDriverARCRRow(rowIdx int, columns []string, row *BulkUpdateDriverARCRRow) error {
	if len(columns) < 4 {
		return NewValidateError("invalid file structure")
	}
	row.RowIdx = rowIdx
	row.DriverId = columns[0]

	t, err := time.Parse(time.DateOnly, columns[1])
	if err != nil {
		return NewValidateError("invalid date format, expect format YYYY-MM-DD")
	}

	if timeutil.IsTodayBKK(t) {
		return NewValidateError("invalid date, at least T-1 is allowed")
	}

	row.TargetDate = timeutil.DateTruncateBKK(t)

	v := columns[2]
	switch v {
	case "NO_CHANGE":
		row.AR = nil
	default:
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return NewValidateError(fmt.Sprintf("AR not in float64 format err: %v", err.Error()))
		}
		if f != 100 {
			return NewValidateError("AR now allowed only 100")
		}
		row.AR = types.NewFloat64(f)
	}

	v = columns[3]
	switch v {
	case "NO_CHANGE":
		row.CR = nil
	default:
		f, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return NewValidateError(fmt.Sprintf("CR not in float64 format err: %v", err.Error()))
		}
		if f != 0 {
			return NewValidateError("CR now allowed only 0")
		}
		row.CR = types.NewFloat64(f)
	}

	err = row.parseCommand()
	if err != nil {
		return NewValidateError(fmt.Sprintf("parse command err: %v", err.Error()))
	}

	// optional column, service type
	if len(columns) >= 5 {
		v = columns[4]
		sv := model.Service(v)
		switch sv {
		case model.ServiceFood, model.ServiceMart, model.ServiceMessenger, model.ServiceBike, "":
			row.Service = model.Service(v)
		default:
			return NewValidateError("invalid service type")
		}
	}

	return nil
}

func (r *BulkUpdateDriverARCRRow) parseCommand() error {
	if r.AR != nil && *r.AR == *types.NewFloat64(100) &&
		r.CR != nil && *r.CR == *types.NewFloat64(0) {
		r.Command = AR_100_CR_0
		return nil
	} else if r.AR != nil && *r.AR == *types.NewFloat64(100) && r.CR == nil {
		r.Command = AR_100
		return nil
	} else if r.CR != nil && *r.CR == *types.NewFloat64(0) && r.AR == nil {
		r.Command = CR_0
		return nil
	}

	return errors.New("current input doesn't support")
}

type BulkUpdateARCRCommand int

const (
	AR_100_CR_0 BulkUpdateARCRCommand = iota
	AR_100
	CR_0
)

type BulkUpdateServiceTypes struct {
	File        *multipart.FileHeader `form:"file" binding:"required"`
	EffectiveAt time.Time             `form:"effectiveAt"`
}

func NewBulkUpdateServiceTypes(ctx *gin.Context, maxRow int, resp *BulkIDReasonResponse, optimizedDriverIdsExistsFn optimizedDriverIdsExistsFn) ([]string, map[string]model.Services, time.Time, error) {
	m := make(map[string]model.Services)

	var req BulkUpdateServiceTypes
	if err := ctx.ShouldBind(&req); err != nil {
		return []string{}, m, time.Time{}, errors.New(err.Error())
	}

	file, err := req.File.Open()
	if err != nil {
		return []string{}, m, time.Time{}, errors.New(err.Error())
	}
	defer file.Close()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return []string{}, m, time.Time{}, errors.New(err.Error())
	}
	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	if len(rows) > maxRow {
		return []string{}, m, time.Time{}, NewValidateError(ErrMaximumRow)
	}

	driverIdsSet := types.NewStringSet()
	for _, columns := range rows {
		if len(columns) != 2 {
			return []string{}, m, time.Time{}, NewValidateError(ErrInvalidUpdateDriverServiceTypes)
		}
		id := strings.TrimSpace(columns[0])
		driverIdsSet.Add(id)
	}

	driverExistsMemoize, err := optimizedDriverIdsExistsFn(driverIdsSet)
	if err != nil {
		return []string{}, m, time.Time{}, NewValidateError(fmt.Sprintf("unable to create driver exists memoize: %v", err))
	}

	ids := types.NewStringSet()
	for _, columns := range rows {
		var errs []error

		id := strings.TrimSpace(columns[0])
		if !driverExistsMemoize(id) {
			err := fmt.Errorf("driver id doesn't exists")
			errs = append(errs, err)
		}

		if len(m[id]) > 0 {
			err := fmt.Errorf("duplicate driver id:%s", id)
			errs = append(errs, err)
		}

		svc := columns[1]
		var ss []model.Service
		if len(svc) == 0 {
			err := fmt.Errorf("empty service type for driver id:%s", id)
			errs = append(errs, err)
		} else {
			s := strings.Split(svc, "|")
			for _, item := range s {
				sv := model.Service(item)
				if !sv.IsValid() {
					err := fmt.Errorf("invalid service type for service:%s", item)
					errs = append(errs, err)
				}
				ss = append(ss, sv)
			}
		}

		if len(errs) > 0 {
			s := make([]string, len(errs))
			for idx, err := range errs {
				s[idx] = err.Error()
			}
			resp.AddFailure(id, strings.Join(s, ","))
			continue
		}

		m[id] = ss
		ids.Add(id)
	}

	return ids.GetElements(), m, req.EffectiveAt, nil
}

type BulkUpdateDriverDedicatedZones struct {
	File        *multipart.FileHeader `form:"file" binding:"required"`
	EffectiveAt time.Time             `form:"effectiveAt"`
}

// NewBulkUpdateDriverDedicatedZones return map[rawZoneLabels][]driverID
// Example rawZoneLabels is a string that may include "|" as a separator
func NewBulkUpdateDriverDedicatedZones(ctx *gin.Context, maxRow int) ([]string, map[string][]string, time.Time, error) {
	var req BulkUpdateDriverDedicatedZones
	if err := ctx.ShouldBind(&req); err != nil {
		return []string{}, nil, time.Time{}, errors.New(err.Error())
	}

	file, err := req.File.Open()
	if err != nil {
		return []string{}, nil, time.Time{}, errors.New(err.Error())
	}
	defer file.Close()

	reader := csv.NewReader(file)
	rows, err := reader.ReadAll()
	if err != nil {
		return []string{}, nil, time.Time{}, errors.New(err.Error())
	}

	if len(rows) > 0 {
		rows = rows[1:] // strip header
	}

	if len(rows) > maxRow {
		return []string{}, nil, time.Time{}, NewValidateError(ErrMaximumRow)
	}

	driverZoneLabelsMap := make(map[string][]string, len(rows))
	driverIDs := types.NewStringSet()
	for _, columns := range rows {
		if len(columns) != 2 {
			return []string{}, nil, time.Time{}, NewValidateError(ErrInvalidUpdateDriverDedicatedZonesStructure)
		}

		driverID := strings.TrimSpace(columns[0])

		if driverIDs.Has(driverID) {
			return []string{}, nil, time.Time{}, NewValidateError(ErrDuplicateRow)
		}
		driverIDs.Add(driverID)

		rawZoneLabels := strings.TrimSpace(columns[1])
		driverZoneLabelsMap[rawZoneLabels] = append(driverZoneLabelsMap[rawZoneLabels], driverID)
	}

	return driverIDs.GetElements(), driverZoneLabelsMap, req.EffectiveAt, nil
}

type IncomeSummaryReq struct {
	Granularity account.Granularity `form:"granularity" binding:"required"`
	Date        time.Time           `form:"date"`
}

func (r IncomeSummaryReq) validate() error {
	switch {
	case r.Granularity == account.DAILY || r.Granularity == account.WEEKLY || r.Granularity == account.MONTHLY:
		if r.Date.IsZero() {
			return errors.New("date is required")
		}
	default:
		return errors.New("invalid granularity")
	}

	return nil
}

type BulkUpdateDriverDeprioritization struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

func (r *BulkUpdateDriverDeprioritization) GetDriverDeprioritization(maxRow int) (
	types.Set[string], // Set of drivers who with deprioritize=false
	map[string]model.Services, // Map driver ID -> deprioritized services
	error,
) {
	file, err := r.File.Open()
	if err != nil {
		return nil, nil, err
	}

	defer file.Close()

	decoder, err := csvutils.NewCSVDecoder(file, csvutils.WithoutHeader(), csvutils.WithMaxRow(maxRow))
	if err != nil {
		return nil, nil, err
	}

	enables := make(map[string]model.Services)
	disables := types.NewSet[string]()
	seen := types.NewSet[string]()

	for decoder.Next() {
		var driverID string
		var isDeprioritized bool
		var serviceTypesString string

		if err := decoder.Decode(&driverID, &isDeprioritized, &serviceTypesString); err != nil {
			return nil, nil, err
		}

		if seen.Contains(driverID) {
			return nil, nil, fmt.Errorf("duplicate driver ID: %s", driverID)
		}

		seen.Add(driverID)

		// Note:
		// strings.Split("", "|") will return []string{""}
		slice := strings.Split(serviceTypesString, "|")
		if len(slice) < 1 {
			return nil, nil, errors.New("split service types but got empty")
		}

		// Trim whitespaces around services
		for i := range slice {
			slice[i] = strings.TrimSpace(slice[i])
		}

		// Collect disables
		if !isDeprioritized {
			// We also trim in case admin put some trailing whitespace(s) on the row
			if slice[0] != "" {
				return nil, nil, fmt.Errorf("false row should not have any service types: '%s'", driverID)
			}

			disables.Add(driverID)
			continue
		}

		// Collect enables
		// If service type empty, deprioritize for all services
		if len(slice) == 1 && slice[0] == "" {
			enables[driverID] = model.Services(model.CurrentSupportServicesSortedByAlphabet)
			continue
		}

		services := make(model.Services, len(slice))
		for i, serviceStr := range slice {
			service := model.Service(serviceStr)
			if !service.IsValid() {
				return nil, nil, fmt.Errorf("invalid service type: '%s'", service)
			}

			services[i] = service
		}

		// Eliminate duplicate services before sort
		services = types.NewSetFrom(services...).Slice()
		sort.Slice(services, func(i, j int) bool {
			return services[i] < services[j]
		})

		enables[driverID] = services
	}

	if len(disables)+len(enables) == 0 {
		return nil, nil, errors.New("empty csv files")
	}

	return disables, enables, nil
}

type BulkUpdateDriverGoodness struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

func (r *BulkUpdateDriverGoodness) ReadFile() (*bytes.Reader, error) {
	file, err := r.File.Open()
	if err != nil {
		return nil, err
	}
	defer file.Close()

	b, err := io.ReadAll(file)
	if err != nil {
		return nil, err
	}
	return bytes.NewReader(b), nil
}

func (r *BulkUpdateDriverGoodness) GetDriverGoodness(reader io.Reader, maxRow int) ([]BulkUpdateDriverGoodnessCSVRow, error) {
	decoder, err := csvutils.NewCSVDecoder(reader, csvutils.WithoutHeader(), csvutils.WithMaxRow(maxRow))
	if err != nil {
		return nil, err
	}

	result := []BulkUpdateDriverGoodnessCSVRow{}
	for decoder.Next() {
		var row BulkUpdateDriverGoodnessCSVRow
		if err := decoder.Decode(&row.DriverID, &row.RiderLevel, &row.ExpiredAt); err != nil {
			return nil, err
		}

		result = append(result, row)
	}

	return result, nil
}

type BulkUpdateDriverGoodnessCSVRow struct {
	DriverID      string
	RiderLevel    string
	ExpiredAt     string
	ExpiredAtTime time.Time
}

func (r *BulkUpdateDriverGoodnessCSVRow) validate(rowIndex int, existFn func(driverId string) bool, uniqueDriverID func(string) bool, now time.Time) (BulkUpdateDriverGoodnessCSVRow, error) {
	var errs []error
	if err := r.validDriverID(existFn); err != nil {
		errs = append(errs, err)
	}

	if !uniqueDriverID(r.DriverID) {
		errs = append(errs, fmt.Errorf("duplicate driver id on row %v", rowIndex+1))
	}

	if len(errs) == 0 && r.RiderLevel == "" && r.ExpiredAt == "" {
		return BulkUpdateDriverGoodnessCSVRow{
			DriverID:      r.DriverID,
			RiderLevel:    r.RiderLevel,
			ExpiredAt:     r.ExpiredAt,
			ExpiredAtTime: time.Time{},
		}, nil
	}

	_, validRiderLevel, err := r.validRiderLevel()
	if err != nil {
		errs = append(errs, err)
	}

	expiredAtTime, validExpiredAt, err := r.validExpiredAt(now)
	if err != nil {
		errs = append(errs, err)
	}

	if (validRiderLevel && r.RiderLevel == "") && (validExpiredAt && r.ExpiredAt != "") {
		errs = append(errs, errors.New("rider_level should not be empty when expired_at is not empty"))
	}

	if len(errs) > 0 {
		return BulkUpdateDriverGoodnessCSVRow{}, stderr.Join(errs...)
	}

	return BulkUpdateDriverGoodnessCSVRow{
		DriverID:      r.DriverID,
		RiderLevel:    r.RiderLevel,
		ExpiredAt:     r.ExpiredAt,
		ExpiredAtTime: expiredAtTime,
	}, nil
}

func (r *BulkUpdateDriverGoodnessCSVRow) validDriverID(existsFn func(string) bool) error {
	if !existsFn(r.DriverID) {
		return stderr.New("driver id is not exists")
	}

	return nil
}

func (r *BulkUpdateDriverGoodnessCSVRow) validRiderLevel() (prediction.RiderLevel, bool, error) {
	rl, err := prediction.FromStringRiderLevel(r.RiderLevel)
	if err != nil {
		return rl, false, stderr.New("invalid rider level")
	}

	return rl, true, nil
}

func (r *BulkUpdateDriverGoodnessCSVRow) validExpiredAt(now time.Time) (time.Time, bool, error) {
	if r.ExpiredAt == "" {
		return time.Time{}, true, nil
	}

	t, err := time.ParseInLocation(time.DateOnly, r.ExpiredAt, timeutil.BangkokLocation())
	if err != nil {
		return time.Time{}, false, fmt.Errorf("invalid expired_at format: %v", err)
	}

	eod := timeutil.DateCeiling(t)
	if eod.Before(now) {
		return time.Time{}, false, stderr.New("expired_at should be future")
	}

	return eod, true, nil
}

type reqBulkUpdateSupplyPos struct {
	File *multipart.FileHeader `form:"file" binding:"required"`
}

func (r *reqBulkUpdateSupplyPos) driverIDs(maxRows int) ([]string, error) {
	file, err := r.File.Open()
	if err != nil {
		return nil, errors.Wrap(err, "failed to open multipart file")
	}

	defer file.Close()

	decoder, err := csvutils.NewCSVDecoder(file, csvutils.WithoutHeader(), csvutils.WithMaxRow(maxRows))
	if err != nil {
		return nil, errors.Wrap(err, "failed to create csv reader")
	}

	c := 0
	var driverIDs []string
	for decoder.Next() {
		var driverID string
		err := decoder.Decode(&driverID)
		if err != nil {
			return nil, errors.Wrapf(err, "bad csv row #%d", c+1)
		}

		driverIDs = append(driverIDs, driverID)
		c++
	}

	return driverIDs, nil
}
