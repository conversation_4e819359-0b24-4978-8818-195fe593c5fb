package admin

import (
	"fmt"
	"mime/multipart"
	"reflect"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestBulkBanDriverRequest_GetDriverIDs(t *testing.T) {
	tests := []struct {
		name        string
		content     string
		expectedIDs []string
	}{
		{"simple", "Driver ID\nLMD1\nLMD2\n", []string{"LMD1", "LMD2"}},
		{"simple linefeed", "Driver ID\r\nLMD1\r\nLMD2\r\n", []string{"LMD1", "LMD2"}},
		{"simple 2", "Driver ID\nLMD1\nLMD2", []string{"LMD1", "LMD2"}},
		{"empty", "Driver ID\n", []string{}},
		{"empty 2", "Driver ID", []string{}},
		{"1 driver", "Driver ID\nLMD1", []string{"LMD1"}},
		{"driver with space", "Driver ID\n  LMD1  ", []string{"LMD1"}},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			req := &BulkBanDriverRequest{}
			req.File = testutil.MultipartFileHeader("file.csv", []byte(test.content))
			actualIDs, err := req.GetDriverIDs()
			assert.NoError(t, err)
			assert.ElementsMatch(t, test.expectedIDs, actualIDs)
		})
	}
}

func TestBulkUnbanDriverRequest_GetDriverIDs(t *testing.T) {
	tests := []struct {
		name        string
		content     string
		expectedIDs []string
	}{
		{"simple", "Driver ID\nLMD1\nLMD2\n", []string{"LMD1", "LMD2"}},
		{"simple linefeed", "Driver ID\r\nLMD1\r\nLMD2\r\n", []string{"LMD1", "LMD2"}},
		{"simple 2", "Driver ID\nLMD1\nLMD2", []string{"LMD1", "LMD2"}},
		{"empty", "Driver ID\n", []string{}},
		{"empty 2", "Driver ID", []string{}},
		{"1 driver", "Driver ID\nLMD1", []string{"LMD1"}},
		{"driver with space", "Driver ID\n  LMD1  ", []string{"LMD1"}},
	}
	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			req := &BulkUnbanDriverRequest{}
			req.File = testutil.MultipartFileHeader("file.csv", []byte(test.content))
			actualIDs, err := req.GetDriverIDs()
			assert.NoError(t, err)
			assert.ElementsMatch(t, test.expectedIDs, actualIDs)
		})
	}
}

func TestBulkDeprioritizeRequest(t *testing.T) {
	type expected struct {
		disables types.Set[string]
		enables  map[string]model.Services
	}

	type test struct {
		description string
		rows        string
		shouldErr   bool
		expected    expected
	}

	tests := []test{
		{
			description: "basic case",
			rows:        "LMD01,true,food\nLMD02,false,\nLMD03,true,food|mart",
			expected: expected{
				disables: types.NewSetFrom("LMD02"),
				enables: map[string]model.Services{
					"LMD01": model.ServicesFromString("food"),
					"LMD03": model.ServicesFromString("food", "mart"),
				},
			},
		},
		{
			description: "sort mart|food to [food,mart]",
			rows:        "LMD00,false,\nLMD01,true,mart|food\nLMD02,false,\nLMD03,true,food|mart",
			expected: expected{
				disables: types.NewSetFrom("LMD00", "LMD02"),
				enables: map[string]model.Services{
					"LMD01": model.ServicesFromString("food", "mart"),
					"LMD03": model.ServicesFromString("food", "mart"),
				},
			},
		},
		{
			description: "empty with true equals all services",
			rows:        "LMD00,false,\nLMD01,true,\nLMD02,false,\nLMD03,true,food|mart",
			expected: expected{
				disables: types.NewSetFrom("LMD00", "LMD02"),
				enables: map[string]model.Services{
					"LMD01": model.CurrentSupportServicesSortedByAlphabet,
					"LMD03": model.ServicesFromString("food", "mart"),
				},
			},
		},
		{
			description: "sort mart|bike|food to [bike,food,mart]",
			rows:        "LMD01,true,mart|food\nLMD02,false,\nLMD03,true,food|bike|mart",
			expected: expected{
				disables: types.NewSetFrom("LMD02"),
				enables: map[string]model.Services{
					"LMD01": model.ServicesFromString("food", "mart"),
					"LMD03": model.ServicesFromString("bike", "food", "mart"),
				},
			},
		},
		{
			description: "whitespaces are properly trimmed",
			rows:        "LMD01,true,mart  | food\nLMD02,false,	 \nLMD03,true,   food|  bike | mart",
			expected: expected{
				disables: types.NewSetFrom("LMD02"),
				enables: map[string]model.Services{
					"LMD01": model.ServicesFromString("food", "mart"),
					"LMD03": model.ServicesFromString("bike", "food", "mart"),
				},
			},
		},
		{
			description: "flag is false but has service types",
			rows:        "LMD01,true,mart\nLMD02,false,food\nLMD03,true,food|bike",
			shouldErr:   true,
		},
		{
			description: "invalid service type",
			rows:        "LMD01,true,mart\nLMD02,false,\nLMD03,true,food|bike|foo",
			shouldErr:   true,
		},
		{
			description: "duplicates",
			rows:        "LMD01,true,mart\nLMD02,false,\nLMD03,true,food|bike|mart\nLMD01,false,",
			shouldErr:   true,
		},
		{
			description: "duplicate enables",
			rows:        "LMD01,true,mart\nLMD02,false,\nLMD03,true,food|bike|mart\nLMD01,true,food",
			shouldErr:   true,
		},
		{
			description: "duplicate disables",
			rows:        "LMD01,true,mart\nLMD02,false,\nLMD03,true,food|bike|mart\nLMD02,false,",
			shouldErr:   true,
		},
		{
			description: "missing column",
			rows:        "GRAB-001,false,\nLMD_BAZ,false",
			shouldErr:   true,
		},
		{
			description: "empty file",
			rows:        "",
			shouldErr:   true,
		},
	}

	csvHeader := "driver_id,is_deprioritized,service_types\n"
	for i := range tests {
		testCase := &tests[i]
		t.Run(testCase.description, func(tt *testing.T) {
			req := BulkUpdateDriverDeprioritization{
				File: testutil.MultipartFileHeader("file.csv", []byte(csvHeader+testCase.rows)),
			}

			disables, enables, err := req.GetDriverDeprioritization(100)
			if testCase.shouldErr {
				require.Error(tt, err, "expecting error")
				return
			}

			require.NoError(tt, err)
			require.Equal(tt, testCase.expected.disables, disables)

			for driverID, services := range testCase.expected.enables {
				actual, ok := enables[driverID]
				require.True(tt, ok, "driverID missing:", driverID)
				require.Equal(tt, services, actual)
			}
		})
	}
}

func TestApproveRequestUpdateProfileRequest_ApproveRequestUpdateProfile(t *testing.T) {

	t.Run("Should update all correct after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2022, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "new-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("new-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "new-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "new-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "new-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, "new-vehicle-legislation-url", profile.Vehicle.LegislationPhotoURL)
		require.Equal(t, "new-vehicle-lending-vehicle-url", profile.Vehicle.LendingVehiclePhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("new-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only AvatarURL after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status:    model.ProfileStatusUpdatePending,
			AvatarURL: "new-avatar-photo-url",
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "new-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only LicenseId after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			DriverLicense: model.DriverLicenseInfo{
				ID: crypt.NewLazyEncryptedString("new-license-id"),
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("new-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only ExpirationDate after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		newExpirationDate := time.Date(2022, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			DriverLicense: model.DriverLicenseInfo{
				ExpirationDate: &newExpirationDate,
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &newExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only LicensePhotoURL after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			DriverLicense: model.DriverLicenseInfo{
				PhotoURL: "new-license-photo-url",
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "new-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only VehiclePhotoURL after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			Vehicle: model.VehicleInfo{
				PhotoURL: "new-vehicle-photo-url",
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "new-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only VehicleRegistrationPhotoURL after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			Vehicle: model.VehicleInfo{
				RegistrationPhotoURL: "new-vehicle-registration-url",
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "new-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only VehicleRegistrationDate after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			Vehicle: model.VehicleInfo{
				RegistrationDate: &expectedVehicleRegistrationDate,
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only VehiclePlatNumber after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			Vehicle: model.VehicleInfo{
				PlateNumber: crypt.NewLazyEncryptedString("new-plate-number"),
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("new-plate-number"), profile.Vehicle.PlateNumber)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only Legislation after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			Vehicle: model.VehicleInfo{
				LegislationPhotoURL: "new-legislation-url",
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, "new-legislation-url", profile.Vehicle.LegislationPhotoURL)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only LendingVehicle after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)
		expectedVehicleRegistrationDate := time.Date(2020, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			Vehicle: model.VehicleInfo{
				LendingVehiclePhotoURL: "new-lending-vehicle-url",
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, &expectedVehicleRegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, "new-lending-vehicle-url", profile.Vehicle.LendingVehiclePhotoURL)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only CitizenIDCardPhotoURL & CitizenIDExpiredDate after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2077, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status:                model.ProfileStatusUpdatePending,
			CitizenIdExpiredDate:  &expectedExpirationDate,
			CitizenIdCardPhotoURL: "new-citizen-id-card-photo-url",
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, profile.DriverLicense.ExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, profile.Vehicle.RegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, "original-vehicle-lending-vehicle-url", profile.Vehicle.LendingVehiclePhotoURL)
		require.Equal(t, &expectedExpirationDate, profile.CitizenIDExpiredDate)
		require.Equal(t, "new-citizen-id-card-photo-url", profile.CitizenIDCardPhotoURL)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})

	t.Run("Should update only LegislationPhotoURL & LegislationExpiredDate after approve", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2055, 1, 1, 1, 1, 1, 1, time.UTC)
		profile.RequestUpdateProfile = []model.RequestUpdateProfile{{
			Status: model.ProfileStatusUpdatePending,
			Vehicle: model.VehicleInfo{
				LegislationPhotoURL:    "new-legislation-photo-url",
				LegislationExpiredDate: &expectedExpirationDate,
			},
		}}

		err := profile.ApproveRequestUpdateProfile()

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, profile.DriverLicense.ExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, "original-vehicle-registration-url", profile.Vehicle.RegistrationPhotoURL)
		require.Equal(t, profile.Vehicle.RegistrationDate, profile.Vehicle.RegistrationDate)
		require.Equal(t, "original-vehicle-lending-vehicle-url", profile.Vehicle.LendingVehiclePhotoURL)
		require.Equal(t, &expectedExpirationDate, profile.Vehicle.LegislationExpiredDate)
		require.Equal(t, "new-legislation-photo-url", profile.Vehicle.LegislationPhotoURL)
		require.Equal(t, model.ProfileStatusCompleted, profile.ProfileStatus)
	})
}

func TestApproveRequestUpdateProfileRequest_RejectRequestUpdateProfile(t *testing.T) {

	t.Run("Should add correct request profile update status reject", func(t *testing.T) {
		profile := OriginalDriverRequestUpdateProfile()
		expectedExpirationDate := time.Date(2021, 1, 1, 1, 1, 1, 1, time.UTC)

		err := profile.RejectRequestUpdateProfile("รูปใบขับขี่ไม่ชัดเชน กรุณาอัพโหลดเข้ามาอีกครั้ง")

		require.NoError(t, err)
		require.Equal(t, "original-avatar-photo-url", profile.AvatarURL)
		require.Equal(t, &expectedExpirationDate, profile.DriverLicense.ExpirationDate)
		require.Equal(t, crypt.NewLazyEncryptedString("original-license-id"), profile.DriverLicense.ID)
		require.Equal(t, "original-license-photo-url", profile.DriverLicense.PhotoURL)
		require.Equal(t, "original-vehicle-photo-url", profile.Vehicle.PhotoURL)
		require.Equal(t, model.ProfileStatusRequestedReupdate, profile.ProfileStatus)
		require.Equal(t, model.ProfileStatusRequestedReupdate, profile.RequestUpdateProfile[len(profile.RequestUpdateProfile)-1].Status)
		require.Equal(t, "รูปใบขับขี่ไม่ชัดเชน กรุณาอัพโหลดเข้ามาอีกครั้ง", profile.RequestUpdateProfile[len(profile.RequestUpdateProfile)-1].Remark)
	})

}

func TestBulkUpdateDriverARCRRows_groupByDate(t *testing.T) {
	tests := []struct {
		name string
		r    BulkUpdateDriverARCRRows
		want map[time.Time]BulkUpdateDriverARCRRows
	}{
		{
			name: "empty",
			r:    BulkUpdateDriverARCRRows{},
			want: map[time.Time]BulkUpdateDriverARCRRows{},
		},
		{
			name: "group by date correctly with same date",
			r: BulkUpdateDriverARCRRows{
				{
					RowIdx:     1,
					DriverId:   "A",
					TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
					AR:         types.NewFloat64(100),
					CR:         types.NewFloat64(0),
					Command:    AR_100_CR_0,
				},
				{
					RowIdx:     2,
					DriverId:   "B",
					TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
					AR:         types.NewFloat64(100),
					CR:         nil,
					Command:    AR_100,
				},
				{
					RowIdx:     3,
					DriverId:   "C",
					TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
					AR:         nil,
					CR:         types.NewFloat64(0),
					Command:    CR_0,
				},
			},
			want: map[time.Time]BulkUpdateDriverARCRRows{
				timeutil.ConvertYYYYMMDDToDate("2022-09-22"): {
					{
						RowIdx:     1,
						DriverId:   "A",
						TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
						AR:         types.NewFloat64(100),
						CR:         types.NewFloat64(0),
						Command:    AR_100_CR_0,
					},
					{
						RowIdx:     2,
						DriverId:   "B",
						TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
						AR:         types.NewFloat64(100),
						CR:         nil,
						Command:    AR_100,
					},
					{
						RowIdx:     3,
						DriverId:   "C",
						TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
						AR:         nil,
						CR:         types.NewFloat64(0),
						Command:    CR_0,
					},
				},
			},
		},
		{
			name: "group by date correctly with different date",
			r: BulkUpdateDriverARCRRows{
				{
					RowIdx:     1,
					DriverId:   "A",
					TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
					AR:         types.NewFloat64(100),
					CR:         types.NewFloat64(0),
					Command:    AR_100_CR_0,
				},
				{
					RowIdx:     2,
					DriverId:   "B",
					TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-23"),
					AR:         types.NewFloat64(100),
					CR:         nil,
					Command:    AR_100,
				},
				{
					RowIdx:     3,
					DriverId:   "C",
					TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-24"),
					AR:         nil,
					CR:         types.NewFloat64(0),
					Command:    CR_0,
				},
			},
			want: map[time.Time]BulkUpdateDriverARCRRows{
				timeutil.ConvertYYYYMMDDToDate("2022-09-22"): {
					{
						RowIdx:     1,
						DriverId:   "A",
						TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
						AR:         types.NewFloat64(100),
						CR:         types.NewFloat64(0),
						Command:    AR_100_CR_0,
					},
				},
				timeutil.ConvertYYYYMMDDToDate("2022-09-23"): {
					{
						RowIdx:     2,
						DriverId:   "B",
						TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-23"),
						AR:         types.NewFloat64(100),
						CR:         nil,
						Command:    AR_100,
					},
				},
				timeutil.ConvertYYYYMMDDToDate("2022-09-24"): {
					{
						RowIdx:     3,
						DriverId:   "C",
						TargetDate: timeutil.ConvertYYYYMMDDToDate("2022-09-24"),
						AR:         nil,
						CR:         types.NewFloat64(0),
						Command:    CR_0,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.r.groupByDate(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BulkUpdateDriverARCRRows.groupByDate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBulkUpdateDriverARCRRows_uniqueDrivers(t *testing.T) {
	tests := []struct {
		name string
		r    BulkUpdateDriverARCRRows
		want []string
	}{
		{
			name: "empty",
			r:    BulkUpdateDriverARCRRows{},
			want: []string{},
		},
		{
			name: "unique drivers correctly with total different drivers",
			r: BulkUpdateDriverARCRRows{
				{DriverId: "A"},
				{DriverId: "B"},
				{DriverId: "C"},
			},
			want: []string{"A", "B", "C"},
		},
		{
			name: "unique drivers correctly with total same drivers",
			r: BulkUpdateDriverARCRRows{
				{DriverId: "A"},
				{DriverId: "A"},
				{DriverId: "A"},
			},
			want: []string{"A"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.r.uniqueDrivers(); !assert.ElementsMatch(t, got, tt.want) {
				t.Errorf("BulkUpdateDriverARCRRows.uniqueDrivers() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestBulkUpdateDriverARCRRow_parseCommand(t *testing.T) {
	type fields struct {
		RowIdx     int
		DriverId   string
		TargetDate time.Time
		AR         *float64
		CR         *float64
		Command    BulkUpdateARCRCommand
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "Ops would like to set AR 100 and CR 0",
			fields: fields{
				AR: types.NewFloat64(100),
				CR: types.NewFloat64(0),
			},
			wantErr: false,
		},
		{
			name: "Ops would like to set AR 100 and leave CR NO_CHANGED",
			fields: fields{
				AR: types.NewFloat64(100),
				CR: nil,
			},
			wantErr: false,
		},
		{
			name: "Ops would like to set CR 100 and leave AR NO_CHANGED",
			fields: fields{
				AR: nil,
				CR: types.NewFloat64(0),
			},
			wantErr: false,
		},
		{
			name: "Ops would like to set some unsupport AR/CR",
			fields: fields{
				AR: types.NewFloat64(100),
				CR: types.NewFloat64(100),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &BulkUpdateDriverARCRRow{
				RowIdx:     tt.fields.RowIdx,
				DriverId:   tt.fields.DriverId,
				TargetDate: tt.fields.TargetDate,
				AR:         tt.fields.AR,
				CR:         tt.fields.CR,
				Command:    tt.fields.Command,
			}
			if err := r.parseCommand(); (err != nil) != tt.wantErr {
				t.Errorf("BulkUpdateDriverARCRRow.parseCommand() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_parseBulkUpdateDriverARCRRow(t *testing.T) {
	type args struct {
		rowIdx  int
		columns []string
		row     *BulkUpdateDriverARCRRow
	}
	tests := []struct {
		name    string
		before  func()
		after   func()
		args    args
		wantErr bool
	}{
		{
			name: "Ops would like to set AR 100 CR 0",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "100.0", "0"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: false,
		},
		{
			name: "Ops would like to set AR 100 CR NO_CHANGE",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "100.0", "NO_CHANGE"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: false,
		},
		{
			name: "Ops would like to set AR NO_CHANGE CR 0",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "NO_CHANGE", "0"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: false,
		},
		{
			name: "invalid file structure",
			args: args{
				rowIdx:  1,
				columns: []string{""},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: true,
		},
		{
			name: "allow empty service type",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "100.0", "0", ""},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: false,
		},
		{
			name: "invalid date format",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022/09/22", "100.0", "0"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: true,
		},
		{
			name: "invalid AR float format",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "A", "0"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: true,
		},
		{
			name: "invalid AR not 100 pecent",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "99", "0"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: true,
		},
		{
			name: "invalid CR float format",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "100.0", "A"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: true,
		},
		{
			name: "invalid CR not 0 pecent",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "100", "80"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			wantErr: true,
		},
		{
			name: "invalid date submit date same as execution date same not allowed",
			args: args{
				rowIdx:  1,
				columns: []string{"driver-A", "2022-09-22", "100", "80"},
				row:     &BulkUpdateDriverARCRRow{},
			},
			before: func() {
				timeutils.Now = func() time.Time {
					return time.Date(2022, 9, 22, 8, 0, 0, 0, timeutil.BangkokLocation())
				}
			},
			after:   timeutils.Unfreeze,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.before != nil {
				tt.before()
			}

			if tt.after != nil {
				t.Cleanup(tt.after)
			}

			if err := parseBulkUpdateDriverARCRRow(tt.args.rowIdx, tt.args.columns, tt.args.row); (err != nil) != tt.wantErr {
				t.Errorf("parseBulkUpdateDriverARCRRow() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestBulkUpdateDriverARCRRequest_GetDataRows(t *testing.T) {
	type fields struct {
		File *multipart.FileHeader
	}
	type args struct {
		maxSize int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    BulkUpdateDriverARCRRows
		wantErr bool
	}{
		{
			name: "success case",
			fields: fields{
				File: testutil.MultipartFileHeader(
					"test.csv",
					[]byte("Driver ID,Date Time,AR,CR\ndriver-A,2022-09-22,100,0\ndriver-B,2022-09-22,NO_CHANGE,0\ndriver-C,2022-09-22,100,NO_CHANGE\n"),
				),
			},
			args: args{
				maxSize: 5000,
			},
			want: BulkUpdateDriverARCRRows{
				{
					RowIdx:     0,
					DriverId:   "driver-A",
					TargetDate: time.Date(2022, 9, 22, 0, 0, 0, 0, timeutil.BangkokLocation()),
					AR:         types.NewFloat64(100),
					CR:         types.NewFloat64(0),
					Command:    AR_100_CR_0,
					Service:    "",
				},
				{
					RowIdx:     1,
					DriverId:   "driver-B",
					TargetDate: time.Date(2022, 9, 22, 0, 0, 0, 0, timeutil.BangkokLocation()),
					AR:         nil,
					CR:         types.NewFloat64(0),
					Command:    CR_0,
					Service:    "",
				},
				{
					RowIdx:     2,
					DriverId:   "driver-C",
					TargetDate: time.Date(2022, 9, 22, 0, 0, 0, 0, timeutil.BangkokLocation()),
					AR:         types.NewFloat64(100),
					CR:         nil,
					Command:    AR_100,
					Service:    "",
				},
			},
			wantErr: false,
		},
		{
			name: "exceed max row size",
			fields: fields{
				File: testutil.MultipartFileHeader(
					"test.csv",
					[]byte("Driver ID,Date Time,AR,CR\ndriver-A,2022-09-22,100,0"),
				),
			},
			args: args{
				maxSize: 0,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "fail invalid AR not 100",
			fields: fields{
				File: testutil.MultipartFileHeader(
					"test.csv",
					[]byte("Driver ID,Date Time,AR,CR\ndriver-A,2022-09-22,99,0"),
				),
			},
			args: args{
				maxSize: 5000,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "fail invalid CR not 0",
			fields: fields{
				File: testutil.MultipartFileHeader(
					"test.csv",
					[]byte("Driver ID,Date Time,AR,CR\ndriver-A,2022-09-22,100,100"),
				),
			},
			args: args{
				maxSize: 5000,
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &BulkUpdateDriverARCRRequest{
				File: tt.fields.File,
			}
			got, err := req.GetDataRows(tt.args.maxSize)
			if (err != nil) != tt.wantErr {
				t.Errorf("BulkUpdateDriverARCRRequest.GetDataRows() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("BulkUpdateDriverARCRRequest.GetDataRows() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestNewBulkUpdateServiceTypes(t *testing.T) {

	makeReq := func(content string, effectiveAt string) *gin.Context {
		ctx := testutil.NewContextWithRecorder()

		ctx.SetPUT("/v1/admin/bulk/drivers/service-types")

		builder := ctx.Body().MultipartForm()
		builder.File("file", "file.csv", content)

		if effectiveAt != "" {
			builder.String("effectiveAt", effectiveAt)
		}
		builder.Build()

		return ctx.GinCtx()

	}

	validServicesTxt := "food|messenger|mart"

	var exists optimizedDriverIdsExistsFn = func(driverIds types.StringSet) (func(driverId string) bool, error) {
		return func(driverId string) bool {
			return true
		}, nil
	}
	t.Run("valid response without effective at", func(t *testing.T) {
		content := fmt.Sprintf("driverId,service\n1,%s\n2,%s", validServicesTxt, validServicesTxt)
		ctx := makeReq(content, "")
		resp := BulkIDReasonResponse{}

		driverIDs, services, effectiveAt, err := NewBulkUpdateServiceTypes(ctx, 10, &resp, exists)
		require.Nil(t, err)
		require.True(t, effectiveAt.IsZero())
		require.Equal(t, model.Services{model.ServiceFood, model.ServiceMessenger, model.ServiceMart}, services["1"])
		require.Equal(t, model.Services{model.ServiceFood, model.ServiceMessenger, model.ServiceMart}, services["2"])
		require.ElementsMatch(t, []string{"1", "2"}, driverIDs)
	})

	t.Run("valid response with effective at", func(t *testing.T) {
		content := fmt.Sprintf("driverId,service\n1,%s\n2,%s", validServicesTxt, validServicesTxt)
		ctx := makeReq(content, "2024-02-16T08:00:00.000Z")
		resp := BulkIDReasonResponse{}

		driverIDs, services, effectiveAt, err := NewBulkUpdateServiceTypes(ctx, 10, &resp, exists)
		require.Nil(t, err)
		require.Equal(t, effectiveAt, time.Date(2024, time.February, 16, 8, 0, 0, 0, time.UTC))
		require.Equal(t, model.Services{model.ServiceFood, model.ServiceMessenger, model.ServiceMart}, services["1"])
		require.Equal(t, model.Services{model.ServiceFood, model.ServiceMessenger, model.ServiceMart}, services["2"])
		require.ElementsMatch(t, []string{"1", "2"}, driverIDs)
	})

	t.Run("error max rows", func(t *testing.T) {
		content := fmt.Sprintf("driverId,service\n1,%s\n2,%s", validServicesTxt, validServicesTxt)
		ctx := makeReq(content, "")
		resp := BulkIDReasonResponse{}
		_, _, _, err := NewBulkUpdateServiceTypes(ctx, 1, &resp, exists)
		require.Equal(t, ValidateErr{ErrMaximumRow}, err)
	})

	t.Run("error invalid services type", func(t *testing.T) {
		content := "driverId,service\n1,food\n2,mart|invalid"
		ctx := makeReq(content, "")
		resp := BulkIDReasonResponse{}
		_, _, _, err := NewBulkUpdateServiceTypes(ctx, 10, &resp, exists)
		require.NoError(t, err)
		require.Equal(t, 1, len(resp.Failures))
		require.Equal(t, "2", resp.Failures[0].ID)
		require.Equal(t, "invalid service type for service:invalid", resp.Failures[0].Reason)
	})

}
