package admin

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type TopupCreditReportAPI struct {
	repo repository.TopupCreditReportRepository
	vos  service.VOSService
	cfg  Config
}

func (t *TopupCreditReportAPI) ExportCSV(ctx *gin.Context) {
	var req ExportTopupCreditReportRequest
	if err := ctx.ShouldBind(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}

	if req.BeginTransactionUpdatedAt.IsZero() || req.EndTransactionUpdatedAt.IsZero() {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(errors.New("both start and end date must be set")))
		return
	}

	if int(req.EndTransactionUpdatedAt.Sub(req.BeginTransactionUpdatedAt).Hours()/24) > t.cfg.TopupReportSearchDayLimit {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(errors.New("date range is more than limit")))
		return
	}

	count, err := t.repo.CountByQuery(ctx, req.toQuery(), repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrBadRequest(ctx, apiError.ErrInvalidRequest(err))
		return
	}

	logrus.Infof("TopupCreditReportAPI.ExportCSV topup credit report amount: %v", count)

	if count == 0 {
		apiutil.OK(ctx, gin.H{"links": []CSVLink{}})
		return
	}

	q := req.toQuery()
	q.Limit = t.cfg.TopupReportDownloadLimitPerFile

	linkResp := &LinkResp{}

	wg := &sync.WaitGroup{}
	wk, release := safe.NewWorker(t.cfg.TopupReportWorkerPoolLimit)
	defer release()
	// round up after division
	numFile := int(math.Ceil(float64(count) / float64(t.cfg.TopupReportDownloadLimitPerFile)))

	wg.Add(numFile)
	for i := 1; i <= numFile; i++ {
		topup, err := t.repo.GetByQuery(ctx, q, repository.WithReadSecondaryPreferred)
		if err != nil {
			apiutil.ErrInternalError(ctx, apiError.ErrInternal(err))
			return
		}
		t.saveToVos(ctx, wk, wg, linkResp, topup, i)
		q.LastID = topup[len(topup)-1].ID
	}
	wg.Wait()
	apiutil.OK(ctx, gin.H{"links": linkResp.links})
}

func (t *TopupCreditReportAPI) saveToVos(ctx context.Context, wk *safe.Worker, wg *sync.WaitGroup, links *LinkResp, topup []model.TopupCreditReport, r int) {
	wk.GoFuncWithPool(func() {
		defer wg.Done()

		b := WriteTopupReportToCSV(topup)
		n := timeutil.BangkokNow().Format("20060102150405")
		fileName := fmt.Sprintf("topup_report_%s_%s.csv", n, strconv.Itoa(r))
		prefix := timeutil.BangkokNow().Format(time.DateOnly) // YYYY-MM-DD
		filePath := fmt.Sprintf("%s/%s", prefix, fileName)
		f, err := t.vos.UploadTOVOSForInternal(ctx, filePath, " application/csv", bytes.NewReader(b), t.cfg.TopupReportDownloadFolder)
		if err != nil {
			logrus.Errorf("TopupCreditReportAPI.saveToVos file %v err %v", fileName, err)
			links.Add(CSVLink{
				FileName: fileName,
				Status:   "FAILED",
			})
			return
		}
		logrus.Infof("Save file %v to VOS successfully", fileName)
		links.Add(CSVLink{
			URL:      f.ID(),
			FileName: fileName,
			Status:   "COMPLETED",
		})
	})
}

func ProvideTopupCreditReportAPI(repo repository.TopupCreditReportRepository, vos service.VOSService, cfg Config) *TopupCreditReportAPI {
	return &TopupCreditReportAPI{repo: repo, vos: vos, cfg: cfg}
}
