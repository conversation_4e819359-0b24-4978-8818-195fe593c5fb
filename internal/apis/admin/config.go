package admin

import (
	"time"

	"github.com/kelseyhightower/envconfig"
)

type Config struct {
	MaxExportedCriminalCheckStatusCSVRows        int           `envconfig:"ADMIN_MAX_EXPORTED_CRIMINAL_CHECK_STATUS_ROWS"  default:"40000"`
	BulkBanUnBanWorker                           int           `envconfig:"BULK_BAN_UNBAN_WORKER"  default:"50"`
	BulkUpdateDriverTierMaxCSVRows               int           `envconfig:"BULK_UPDATE_DRIVER_TIER_MAX_CSV_ROWS"   default:"100000"`
	BulkUpdateDriverTierWorker                   int           `envconfig:"BULK_UPDATE_DRIVER_TIER_WORKER"    default:"2"`
	BulkUpdateDriverTierBatchSize                int           `envconfig:"BULK_UPDATE_DRIVER_TIER_BATCH_SIZE"   default:"1000"`
	BulkRequsetUpdateEnabled                     bool          `envconfig:"BULK_REQUEST_UPDATE_ENABLED"  default:"true"`
	BulkRequsetUpdateWorker                      int           `envconfig:"BULK_REQUEST_UPDATE_WORKER"  default:"10"`
	BulkUpdateDriverARCRMaxCSVRows               int           `envconfig:"BULK_UPDATE_DRIVER_ARCR_MAX_CSV_ROWS"   default:"5000"`
	BulkUpdateServiceTypeWorker                  int           `envconfig:"BULK_UPDATE_SERVICE_TYPE_WORKER"  default:"10"`
	BulkUpdateServiceTypeMaxCSVRows              int           `envconfig:"BULK_UPDATE_SERVICE_TYPE_MAX_CSV_ROWS"  default:"5000"`
	BulkUpdateServiceTypesSilentBannedWorker     int           `envconfig:"BULK_UPDATE_SERVICE_TYPES_SILENT_BANNED_WORKER"  default:"10"`
	BulkUpdateServiceTypesSilentBannedBatchSize  int           `envconfig:"BULK_UPDATE_SERVICE_TYPES_SILENT_BANNED_BATCH_SIZE"  default:"100"`
	BulkUpdateServiceTypesSilentBannedMaxCSVRows int           `envconfig:"BULK_UPDATE_SERVICE_TYPES_SILENT_BANNED_MAX_CSV_ROWS"  default:"5000"`
	BulkToggleOnRiderServicePreference           int           `envconfig:"BULK_TOGGLE_ON_RIDER_SERVICE_PREFERENCE_MAX_CSV_ROWS"  default:"50000"`
	BulkUpdateDriverDedicatedZonesWorker         int           `envconfig:"BULK_UPDATE_DRIVER_DEDICATED_ZONES_WORKER"  default:"10"`
	BulkUpdateDriverDedicatedZonesBatchSize      int           `envconfig:"BULK_UPDATE_DRIVER_DEDICATED_ZONES_BATCH_SIZE"  default:"100"`
	BulkUpdateDriverDedicatedZonesMaxCSVRows     int           `envconfig:"BULK_UPDATE_DRIVER_DEDICATED_ZONES_MAX_CSV_ROWS"  default:"5000"`
	BulkUpdateDriverDeprioritizationMaxCSVRows   int           `envconfig:"BULK_UPDATE_DRIVER_DEPRIORITIZATION_MAX_CSV_ROWS" default:"100000"`
	TopupReportDownloadFolder                    string        `envconfig:"TOPUP_REPORT_DOWNLOAD_FOLDER" default:"topup_report_download_folder"`
	TopupReportDownloadLimitPerFile              int           `envconfig:"TOPUP_REPORT_DOWNLOAD_LIMIT_PER_FILE" default:"50000"`
	TopupReportWorkerPoolLimit                   int           `envconfig:"TOPUP_REPORT_WORKER_POOL_LIMIT" default:"10"`
	BulkUpdateDriverGoodnessWorker               int           `envconfig:"BULK_UPDATE_DRIVER_GOODNESS_WORKER"  default:"10"`
	BulkUpdateDriverGoodnessBatchSize            int           `envconfig:"BULK_UPDATE_DRIVER_GOODNESS_BATCH_SIZE"  default:"100"`
	BulkUpdateDriverGoodnessMaxCSVRows           int           `envconfig:"BULK_UPDATE_DRIVER_GOODNESS_MAX_CSV_ROWS"  default:"5000"`
	BulkUploadIncentiveCSVMaxSchemeSize          int           `envconfig:"BULK_UPLOAD_INCENTIVE_CSV_MAX_SCHEME_SIZE"  default:"5000"`
	TopupReportSearchDayLimit                    int           `envconfig:"TOPUP_REPORT_SEARCH_DAY_LIMIT" default:"7"`
	BulkUpdateDriverDedicatedZonesDelayTime      time.Duration `envconfig:"BULK_UPDATE_DRIVER_DEDICATED_ZONES_DELAY_TIME" default:"50ms"`
}

func ProvideAdminConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
