package trip

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func Test_ToQuery(t *testing.T) {
	t.Run("set all field", func(t *testing.T) {
		r := ListReq{
			TripID:      "t1",
			DriverID:    "d1",
			OrderID:     "o1",
			Status:      model.TripStatusCompleted,
			ServiceType: model.ServiceFood,
			BeginAt:     time.Date(2022, 9, 20, 15, 0, 0, 0, time.UTC),
			EndAt:       time.Date(2022, 9, 21, 15, 0, 0, 0, time.UTC),
		}

		q := r.ToQuery()
		require.Equal(t, r.TripID, q.TripID)
		require.Equal(t, r.DriverID, q.DriverID)
		require.Equal(t, r.OrderID, q.OrderID)
		require.Equal(t, []model.TripStatus{r.Status}, q.Status)
		require.Equal(t, r.ServiceType, q.ServiceType)
		require.Equal(t, r.<PERSON>, q.GTECreatedAt)
		require.Equal(t, r.<PERSON>, q.LTECreatedAt)
	})

	t.Run("not set all field", func(t *testing.T) {
		r := ListReq{}
		q := r.ToQuery()
		require.Empty(t, q.TripID)
		require.Empty(t, q.DriverID)
		require.Empty(t, q.OrderID)
		require.Empty(t, q.Status)
		require.Empty(t, q.ServiceType)
		require.Equal(t, true, q.GTECreatedAt.IsZero())
		require.Equal(t, true, q.LTECreatedAt.IsZero())
	})

	t.Run("set some field", func(t *testing.T) {
		r := ListReq{
			TripID:      "t1",
			ServiceType: model.ServiceFood,
			EndAt:       time.Date(2022, 9, 21, 15, 0, 0, 0, time.UTC),
		}

		q := r.ToQuery()
		require.Equal(t, r.TripID, q.TripID)
		require.Equal(t, r.ServiceType, q.ServiceType)
		require.Equal(t, r.EndAt, q.LTECreatedAt)

		require.Equal(t, true, q.GTECreatedAt.IsZero())
		require.Empty(t, q.DriverID)
		require.Empty(t, q.OrderID)
		require.Empty(t, q.Status)
	})
}
