package trip

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type TripDetailRes struct {
	TripID string           `json:"tripId"`
	HeadTo int              `json:"headTo"`
	Status model.TripStatus `json:"status"`

	MoneySummary   MoneySummaryRes   `json:"moneySummary"`
	TransferDetail TransferDetailRes `json:"transferDetail"`
	Earning        EarningRes        `json:"earning"`

	Routes         []TripRouteRes         `json:"routes"`
	Orders         []TripOrderRes         `json:"orders"`
	InactiveOrders []TripInactiveOrderRes `json:"inactiveOrders"`

	CreatedAt    time.Time `json:"createdAt"`
	UpdatedAt    time.Time `json:"updatedAt"`
	IsQRIncident bool      `json:"isQRIncident"`
}

type TripDetailAdminRes struct {
	TripID                   string                               `json:"tripId"`
	HeadTo                   int                                  `json:"headTo"`
	Status                   model.TripStatus                     `json:"status"`
	History                  model.TripHistory                    `json:"history"`
	TotalDriverWage          types.Money                          `json:"totalDriverWage"`
	DriverWageHistory        []model.TripDriverWageRecord         `json:"driverWageHistory"`
	Routes                   []TripRouteAdminRes                  `json:"routes"`
	Orders                   []TripOrderAdminRes                  `json:"orders"`
	IsBundle                 bool                                 `json:"isBundle"`
	DistanceCompensatoryInfo TripDistanceCompensatoryInfoAdminRes `json:"distanceCompensatoryInfo,omitempty"`
}

type TripDistanceCompensatoryInfoAdminRes struct {
	Stop          int            `json:"stop"`
	NewBaseFee    types.Money    `json:"newBaseFee"`
	NewDistance   types.Distance `json:"newDistance"`
	Requester     string         `json:"requester"`
	CompensatedAt time.Time      `json:"compensatedAt"`
}

type TripOrderAdminRes struct {
	OrderID        string       `json:"orderId"`
	Status         model.Status `json:"status"` // order status
	CreatedAt      time.Time    `json:"createdAt"`
	UpdatedAt      time.Time    `json:"updatedAt"`
	RestaurantID   string       `json:"restaurantId"`
	RestaurantName string       `json:"restaurantName"`
}

type TripRouteAdminRes struct {
	Action     string         `json:"action"`
	Distance   types.Distance `json:"distance"`
	StopOrders []string       `json:"stopOrders"`
	Lat        float64        `json:"lat"`
	Lng        float64        `json:"lng"`
}

type TripHistoryAdminRes struct {
	Status string    `json:"status"`
	Time   time.Time `json:"time"`
}

type TripHistoryDetailRes struct {
	TripID string           `json:"tripId"`
	Status model.TripStatus `json:"status"`

	ActiveMoneySummary   MoneySummaryRes   `json:"activeMoneySummary"`
	InactiveMoneySummary MoneySummaryRes   `json:"inactiveMoneySummary"`
	TransferDetail       TransferDetailRes `json:"transferDetail"`
	Earning              EarningRes        `json:"earning"`

	Routes         []TripHistoryRouteRes  `json:"routes"`
	Orders         []TripHistoryOrderRes  `json:"orders"`
	InactiveOrders []TripInactiveOrderRes `json:"inactiveOrders"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`

	DriverType model.DriverType `json:"driverType"`
}

type TripRouteRes struct {
	ID                    string               `json:"id"`
	Action                model.TripAction     `json:"action"`
	Name                  string               `json:"name"`
	Address               string               `json:"address"`
	Subdistrict           string               `json:"subdistrict,omitempty"`
	Location              model.Location       `json:"location"`
	Phones                []string             `json:"phones"`
	Memo                  string               `json:"memo"`
	MemoTH                string               `json:"memoTH,omitempty"`
	MemoType              model.MemoType       `json:"memoType"`
	Distance              types.Distance       `json:"distance"`
	EstimatedDeliveryTime model.DurationSecond `json:"estimatedDeliveryTime"`
	StopOrders            []TripStopOrderRes   `json:"orders"`
	Hidden                bool                 `json:"hidden"` // hides map
	HideChat              bool                 `json:"hideChat"`
	DoNotDisturb          bool                 `json:"doNotDisturb"`
	CreatedAt             time.Time            `json:"createdAt"`
	UpdatedAt             time.Time            `json:"updatedAt"`
	CompletedAt           time.Time            `json:"completedAt,omitempty"`
	MoneySummary          MoneySummaryRes      `json:"moneySummary"`
	Point                 *model.Point         `json:"point,omitempty"`
	RestaurantReviewed    bool                 `json:"restaurantReviewed,omitempty"`
}

func (trr *TripRouteRes) hideAddress() {
	trr.Address = ""
	trr.Memo = ""
}

func (trr *TripRouteRes) hideContact() {
	trr.Name = ""
	trr.hidePhone()
}

func (trr *TripRouteRes) hidePhone() {
	trr.Phones = []string{}
}

func (trr *TripRouteRes) hideChat() {
	trr.HideChat = true
}

func (trr *TripRouteRes) hideDistance() {
	trr.Distance = 0
}

type TripHistoryRouteRes struct {
	ID          string             `json:"id"`
	Action      model.TripAction   `json:"action"`
	Name        string             `json:"name"`
	Address     string             `json:"address"`
	Subdistrict string             `json:"subdistrict,omitempty"`
	Location    model.Location     `json:"location"`
	Distance    types.Distance     `json:"distance"`
	Memo        string             `json:"memo"`
	StopOrders  []TripStopOrderRes `json:"orders"`
	CreatedAt   time.Time          `json:"createdAt"`
	UpdatedAt   time.Time          `json:"updatedAt"`
	CompletedAt time.Time          `json:"completedAt,omitempty"`
}

type TripStopOrderRes struct {
	OrderID string `json:"orderId"`
	StopID  int    `json:"stopId"`
	Done    bool   `json:"done"`

	CollectPayment bool                    `json:"collectPayment"`
	ItemsPrice     float64                 `json:"itemsPrice"`
	DeliveryStatus model.DeliveryStatus    `json:"deliveryStatus,omitempty"`
	Info           model.StopInfoCollector `json:"info,omitempty"`
}

type TripOrderRes struct {
	OrderID                     string                      `json:"orderId"`
	UserID                      string                      `json:"userId"`
	ServiceType                 model.Service               `json:"serviceType"`
	DeliveringRound             int                         `json:"deliveringRound"`
	CancelDetail                model.CancelDetail          `json:"cancelDetail"`
	TipAmount                   types.Money                 `json:"tipAmount"`
	OnGoingTipAmount            types.Money                 `json:"onGoingTipAmount"`
	History                     map[string]time.Time        `json:"history"`
	Commission                  types.Money                 `json:"commission"`
	CommissionRate              int                         `json:"commissionRate"`
	SpecialEvent                []string                    `json:"specialEvent"`
	RevenueAgentModel           bool                        `json:"revenueAgentModel"`
	IsAutoAssigned              bool                        `json:"isAutoAssigned"`
	Status                      model.Status                `json:"status"`
	HeadTo                      int                         `json:"headTo"`
	PayAtStop                   int                         `json:"payAtStop"`
	DeliveringPhotoStatus       model.DeliveringPhotoStatus `json:"deliveringPhotoStatus"`
	DeliveringPhotoURL          string                      `json:"deliveringPhotoUrl"`
	IsRequireDeliveringPhotoURL bool                        `json:"isRequireDeliveredPhotoURL"`
	ShouldVerify                bool                        `json:"shouldVerify"`
	NoteToDriver                string                      `json:"noteToDriver"`
	Options                     model.OrderOptions          `json:"options"`
	PriceSummary                order.PriceSummaryRes       `json:"priceSummary,omitempty"`
	TotalTax                    types.Money                 `json:"totalTax"`
	Items                       []model.Item                `json:"items"`
	CreatedAt                   time.Time                   `json:"createdAt"`
	UpdatedAt                   time.Time                   `json:"updatedAt"`
	ExpiredAt                   time.Time                   `json:"expiredAt"`
	Routes                      []order.StopRes             `json:"routes"`
	IsRain                      bool                        `json:"isRain"`
	HasPickupDistanceOnTop      bool                        `json:"hasPickupDistanceOnTop"`
}

type OrderStopRes struct {
	Pauses model.PauseSet `json:"pauses"`
}

type TripHistoryOrderRes struct {
	OrderID           string                `json:"orderId"`
	ServiceType       model.Service         `json:"serviceType"`
	TipAmount         types.Money           `json:"tipAmount"`
	OnGoingTipAmount  types.Money           `json:"onGoingTipAmount"`
	SpecialEvent      []string              `json:"specialEvent"`
	RevenueAgentModel bool                  `json:"revenueAgentModel"`
	Status            model.Status          `json:"status"`
	NoteToDriver      string                `json:"noteToDriver"`
	TotalTax          types.Money           `json:"totalTax"`
	Items             []model.Item          `json:"items"`
	Options           model.OrderOptions    `json:"options"`
	PriceSummary      order.PriceSummaryRes `json:"priceSummary,omitempty"`
	CreatedAt         time.Time             `json:"createdAt"`
	UpdatedAt         time.Time             `json:"updatedAt"`
	CompletedAt       time.Time             `json:"completedAt"`
}

type TripInactiveOrderRes struct {
	OrderID        string                  `json:"orderId"`
	UserID         string                  `json:"userId"`
	ServiceType    model.Service           `json:"serviceType"`
	CancelDetail   model.CancelDetail      `json:"cancelDetail"`
	PickUpName     string                  `json:"pickUpName"`
	PickUpAddress  string                  `json:"pickUpAddress"`
	PickupDistance types.Distance          `json:"pickupDistance"`
	PickUpInfo     model.StopInfoCollector `json:"pickUpInfo"`
	DropOffName    string                  `json:"dropOffName"`
	DropOffAddress string                  `json:"dropOffAddress"`
	Items          []model.Item            `json:"items"`
	Options        model.OrderOptions      `json:"options"`
	PriceSummary   order.PriceSummaryRes   `json:"priceSummary,omitempty"`
	Status         model.Status            `json:"status"`

	CreatedAt time.Time `json:"createdAt"`
	UpdatedAt time.Time `json:"updatedAt"`
}

type TransferDetailRes struct {
	TransferAmount types.Money `json:"transferAmount"`
	Outstanding    types.Money `json:"outstanding"`
}

type MoneySummaryRes struct {
	CashPay      types.Money `json:"cashPay"`
	CashReceive  types.Money `json:"cashReceive"`
	CreditDeduct types.Money `json:"creditDeduct"`
}

type EarningRes struct {
	WageWallet              types.Money `json:"wageWallet"`
	WageCash                types.Money `json:"wageCash"`
	TotalWage               types.Money `json:"totalWage"`
	OnTop                   types.Money `json:"onTop"`
	EGSOnTop                types.Money `json:"egsOnTop"`
	TotalOnTopFare          types.Money `json:"totalOnTopFare"`
	WithHoldingOnTopTax     types.Money `json:"withholdingOnTopTax"`
	WithHoldingOnTopTaxRate int         `json:"withholdingOnTopTaxRate"`
	Tax                     types.Money `json:"tax"`
	// Total Before Deduction
	TotalBeforeTax types.Money `json:"totalBeforeTax"`
	// Total After Deduction
	TotalAfterTax     types.Money `json:"totalAfterTax"`
	AdditionalService types.Money `json:"additionalService"`
	Coin              int         `json:"coin"`
	CommissionRate    int         `json:"commissionRate"`
	CommissionAmount  types.Money `json:"commissionAmount"`
	TotalTipAmount    types.Money `json:"totalTipAmount"`
}

type BulkCompleteTripRes struct {
	Successes []string `json:"successes"`
	Failures  []string `json:"failures"`
}

type AssignmentRes struct {
	TripRes     TripDetailRes    `json:"trip"`
	NewOrderIDs []string         `json:"newOrderIds"`
	DriverType  model.DriverType `json:"driverType"`
}

func NewTripDetailRes(trip model.Trip, ordersMap map[string]order.OrderDetailRes, orderApiCfg order.OrderAPIConfig, isQRIncident bool, isEmbarked *bool) TripDetailRes {
	routes := make([]TripRouteRes, len(trip.Routes))
	for i, route := range trip.Routes {
		routes[i] = NewTripRouteRes(
			trip,
			route,
			trip.IsPastRoute(i),
			trip.IsFutureRoute(i),
			i == (trip.HeadTo+1) && trip.Status == model.TripStatusArrivedAt,
			ordersMap,
			isEmbarked,
		)
	}

	tripOrders := make([]TripOrderRes, 0, len(trip.Orders))
	inactiveTripOrders := make([]TripInactiveOrderRes, 0, len(trip.Orders))
	for _, tripOrder := range trip.Orders {
		if ordersMap[tripOrder.OrderID].Status == model.StatusCanceled || ordersMap[tripOrder.OrderID].Status == model.StatusExpired {
			inactiveTripOrders = append(inactiveTripOrders, NewTripInactiveOrderRes(tripOrder, ordersMap[tripOrder.OrderID]))
		} else {
			tripOrders = append(tripOrders, NewTripOrderRes(tripOrder, ordersMap[tripOrder.OrderID]))
		}
	}

	if len(tripOrders) > 1 && orderApiCfg.RemoveZeroQuantityPickingItems {
		for i := range tripOrders {
			if tripOrders[i].Status == model.StatusDriverArrivedRestaurant {
				filteredItems := make([]model.Item, 0, len(tripOrders[i].Items))
				for _, item := range tripOrders[i].Items {
					if item.Quantity != 0 {
						filteredItems = append(filteredItems, item)
					}
				}
				tripOrders[i].Items = filteredItems
			}
		}
	}

	res := TripDetailRes{
		TripID:         trip.TripID,
		HeadTo:         trip.HeadTo,
		Status:         trip.Status,
		Routes:         routes,
		Orders:         tripOrders,
		InactiveOrders: inactiveTripOrders,
		MoneySummary:   NewActiveMoneySummaryRes(ordersMap),
		TransferDetail: NewTransferDetailRes(trip.DriverWageSummary),
		Earning:        NewEarningRes(trip, ordersMap),
		CreatedAt:      trip.CreatedAt,
		UpdatedAt:      trip.UpdatedAt,
		IsQRIncident:   isQRIncident,
	}

	return res
}

type TripAdminRes struct {
	TripID    string                `json:"tripId"`
	DriverID  string                `json:"driverId"`
	Orders    []OrderInTripAdminRes `json:"orders"`
	HeadTo    int                   `json:"headTo"`
	Status    model.TripStatus      `json:"status"`
	CreatedAt time.Time             `json:"createdAt"`
	UpdatedAt time.Time             `json:"updatedAt"`
}

type OrderInTripAdminRes struct {
	OrderID         string        `json:"orderId"`
	ServiceType     model.Service `json:"serviceType"`
	DeliveringRound int           `json:"deliveringRound"`
	Status          model.Status  `json:"status"`
	HeadTo          int           `json:"headTo"`
}

func NewTripForAdminRes(trip model.Trip) TripAdminRes {
	orders := make([]OrderInTripAdminRes, len(trip.Orders))
	for i, o := range trip.Orders {
		orders[i] = OrderInTripAdminRes{
			OrderID:         o.OrderID,
			ServiceType:     o.ServiceType,
			DeliveringRound: o.DeliveringRound,
			Status:          o.Status,
			HeadTo:          o.HeadTo,
		}
	}
	return TripAdminRes{
		TripID:    trip.TripID,
		DriverID:  trip.DriverID,
		Orders:    orders,
		HeadTo:    trip.HeadTo,
		Status:    trip.Status,
		CreatedAt: trip.CreatedAt,
		UpdatedAt: trip.UpdatedAt,
	}
}

func NewTripDetailForAdminRes(trip model.Trip, ords []model.Order) TripDetailAdminRes {
	isBundle := len(trip.Orders) > 1
	orders := make([]TripOrderAdminRes, len(trip.Orders))
	routes := make([]TripRouteAdminRes, len(trip.Routes))
	histories := make([]TripHistoryAdminRes, len(trip.History))

	for i, ord := range ords {
		orders[i] = NewTripOrderDetailForAdminRes(ord)
	}
	for i, r := range trip.Routes {
		routes[i] = NewTripRouteAdminRes(r)
	}
	for i, h := range trip.History {
		histories[i] = NewHistoryAdminRes(h)
	}

	var distanceCompensatoryInfoRes TripDistanceCompensatoryInfoAdminRes
	if trip.DistanceCompensatoryInfo != nil {
		distanceCompensatoryInfoRes = TripDistanceCompensatoryInfoAdminRes{
			Stop:          trip.DistanceCompensatoryInfo.Stop,
			NewBaseFee:    trip.DistanceCompensatoryInfo.NewBaseFee,
			NewDistance:   trip.DistanceCompensatoryInfo.NewDistance,
			Requester:     trip.DistanceCompensatoryInfo.Requester,
			CompensatedAt: trip.DistanceCompensatoryInfo.CompensatedAt,
		}
	}
	res := TripDetailAdminRes{
		TripID:                   trip.TripID,
		HeadTo:                   trip.HeadTo,
		Status:                   trip.Status,
		TotalDriverWage:          trip.DriverWageSummary.TotalDriverWage,
		DriverWageHistory:        trip.DriverWageHistory,
		History:                  trip.History,
		Routes:                   routes,
		Orders:                   orders,
		IsBundle:                 isBundle,
		DistanceCompensatoryInfo: distanceCompensatoryInfoRes,
	}

	return res
}

func NewTripOrderDetailForAdminRes(od model.Order) TripOrderAdminRes {
	order := TripOrderAdminRes{
		OrderID:        od.OrderID,
		Status:         od.Status,
		CreatedAt:      od.CreatedAt,
		UpdatedAt:      od.UpdatedAt,
		RestaurantID:   od.Quote.Routes[0].ID,
		RestaurantName: od.Quote.Routes[0].Name,
	}

	return order
}

func NewTripRouteAdminRes(r model.TripRoute) TripRouteAdminRes {
	sts := make([]string, len(r.StopOrders))

	for i, st := range r.StopOrders {
		sts[i] = st.OrderID
	}

	route := TripRouteAdminRes{
		Action:     string(r.Action),
		Distance:   r.Distance,
		StopOrders: sts,
		Lat:        r.Location.Lat,
		Lng:        r.Location.Lng,
	}
	return route
}

func NewHistoryAdminRes(e model.TripEntry) TripHistoryAdminRes {
	return TripHistoryAdminRes{
		Status: string(e.TripStatus),
		Time:   e.Timestamp,
	}
}

func NewTripHistoryDetailRes(trip model.Trip, ordersMap map[string]order.OrderDetailRes) TripHistoryDetailRes {
	routes := make([]TripHistoryRouteRes, len(trip.Routes))
	for i, route := range trip.Routes {
		routes[i] = NewTripHistoryRouteRes(route, ordersMap)
	}

	tripOrders := make([]TripHistoryOrderRes, 0, len(trip.Orders))
	inactiveTripOrders := make([]TripInactiveOrderRes, 0, len(trip.Orders))
	for _, tripOrder := range trip.Orders {
		if ordersMap[tripOrder.OrderID].Status == model.StatusCanceled || ordersMap[tripOrder.OrderID].Status == model.StatusExpired {
			inactiveTripOrder := NewTripInactiveOrderRes(tripOrder, ordersMap[tripOrder.OrderID])
			inactiveTripOrder.DropOffName = ""
			inactiveTripOrder.DropOffAddress = ""
			inactiveTripOrder.UserID = ""
			inactiveTripOrders = append(inactiveTripOrders, inactiveTripOrder)
		} else {
			tripOrders = append(tripOrders, NewTripHistoryOrderRes(tripOrder, ordersMap[tripOrder.OrderID]))
		}
	}

	res := TripHistoryDetailRes{
		TripID:               trip.TripID,
		Status:               trip.Status,
		Routes:               routes,
		Orders:               tripOrders,
		InactiveOrders:       inactiveTripOrders,
		ActiveMoneySummary:   NewActiveMoneySummaryRes(ordersMap),
		InactiveMoneySummary: NewInactiveMoneySummaryRes(ordersMap),
		TransferDetail:       NewTransferDetailRes(trip.DriverWageSummary),
		Earning:              NewEarningRes(trip, ordersMap),
		CreatedAt:            trip.CreatedAt,
		UpdatedAt:            trip.UpdatedAt,
		DriverType:           trip.DriverType,
	}

	return res
}

func NewTripRouteRes(trip model.Trip, tripRoute model.TripRoute, isPastRoute, isFutureRoute, isPreviousRouteArrived bool, ordersMap map[string]order.OrderDetailRes, isEmbarked *bool) TripRouteRes {
	stopOrders := make([]TripStopOrderRes, len(tripRoute.StopOrders))
	currentOrdersMap := make(map[string]order.OrderDetailRes)
	for i, stopOrder := range tripRoute.StopOrders {
		stopOrders[i] = NewTripStopOrderRes(stopOrder, ordersMap[stopOrder.OrderID])
		currentOrdersMap[stopOrder.OrderID] = ordersMap[stopOrder.OrderID]
	}

	order := ordersMap[tripRoute.StopOrders[0].OrderID]
	orderRoute := getRouteFromStopId(order, tripRoute.StopOrders[0].StopID)
	res := TripRouteRes{
		ID:                    tripRoute.ID,
		Action:                tripRoute.Action,
		Distance:              tripRoute.Distance,
		EstimatedDeliveryTime: tripRoute.EstimatedDeliveryTime,
		Location:              tripRoute.Location,

		Name:         orderRoute.Name,
		Phones:       orderRoute.Phones,
		Address:      orderRoute.Address,
		Subdistrict:  orderRoute.Subdistrict,
		Memo:         orderRoute.Memo,
		MemoTH:       orderRoute.MemoTH,
		MemoType:     orderRoute.MemoType,
		DoNotDisturb: orderRoute.DoNotDisturb,
		Point:        orderRoute.Point,

		StopOrders: stopOrders,

		CreatedAt:   tripRoute.CreatedAt,
		UpdatedAt:   tripRoute.UpdatedAt,
		CompletedAt: tripRoute.CompletedAt,

		MoneySummary:       NewActiveMoneySummaryRes(currentOrdersMap),
		RestaurantReviewed: tripRoute.RestaurantReviewed,
	}

	if tripRoute.Action == model.TripActionDropOff && order.ServiceType == model.ServiceFood && trip.ShowContactOnlyIfFoodEmbarked && isEmbarked != nil && !*isEmbarked {
		res.hidePhone()
		res.hideChat()
	}

	if trip.Status != model.TripStatusCompleted && order.ServiceType != model.ServiceMessenger && trip.IsMultipleOrders() {
		if isFutureRoute && tripRoute.Action == model.TripActionPickUp && !isPreviousRouteArrived {
			res.Hidden = true
			return res
		}

		if isFutureRoute && tripRoute.Action == model.TripActionDropOff && !isPreviousRouteArrived {
			res.Hidden = true
			return res
		}

		if isPastRoute && tripRoute.Action == model.TripActionDropOff {
			res.hideContact()
			res.hideAddress()
			res.hideDistance()
			res.Hidden = true
			return res
		}
	}

	return res
}

func NewTripHistoryRouteRes(tripRoute model.TripRoute, ordersMap map[string]order.OrderDetailRes) TripHistoryRouteRes {
	stopOrders := make([]TripStopOrderRes, len(tripRoute.StopOrders))
	for i, stopOrder := range tripRoute.StopOrders {
		stopOrders[i] = NewTripStopOrderRes(stopOrder, ordersMap[stopOrder.OrderID])
	}

	order := ordersMap[tripRoute.StopOrders[0].OrderID]
	orderRoute := getRouteFromStopId(order, tripRoute.StopOrders[0].StopID)
	res := TripHistoryRouteRes{
		ID:       tripRoute.ID,
		Action:   tripRoute.Action,
		Location: tripRoute.Location,
		Distance: tripRoute.Distance,

		Name:        orderRoute.Name,
		Address:     orderRoute.Address,
		Subdistrict: orderRoute.Subdistrict,
		Memo:        orderRoute.Memo,

		StopOrders: stopOrders,

		CreatedAt:   tripRoute.CreatedAt,
		UpdatedAt:   tripRoute.UpdatedAt,
		CompletedAt: tripRoute.CompletedAt,
	}

	if order.ServiceType.IsRevampedStatus() || (order.ServiceType == model.ServiceFood || order.ServiceType == model.ServiceMart) && tripRoute.StopOrders[0].StopID == order.PayAtStop {
		res.Name = ""
		res.Address = ""
		res.Subdistrict = ""
		res.Location = model.Location{}
	}

	return res
}

func NewTripStopOrderRes(stopOrder model.TripStopOrder, order order.OrderDetailRes) TripStopOrderRes {
	orderRoute := getRouteFromStopId(order, stopOrder.StopID)
	res := TripStopOrderRes{
		OrderID:        stopOrder.OrderID,
		StopID:         stopOrder.StopID,
		Done:           stopOrder.Done,
		CollectPayment: orderRoute.CollectPayment,
		Info:           orderRoute.Info,
		ItemsPrice:     orderRoute.ItemsPrice,
		DeliveryStatus: orderRoute.DeliveryStatus,
	}

	return res
}

func NewTripOrderRes(tripOrder model.TripOrder, order order.OrderDetailRes) TripOrderRes {
	pickUpRoute := getPickUpRoute(order)
	return TripOrderRes{
		OrderID:                     tripOrder.OrderID,
		UserID:                      order.UserID,
		ServiceType:                 tripOrder.ServiceType,
		DeliveringRound:             tripOrder.DeliveringRound,
		HeadTo:                      tripOrder.HeadTo,
		Status:                      order.Status,
		ShouldVerify:                order.ShouldVerify,
		PayAtStop:                   order.PayAtStop,
		DeliveringPhotoStatus:       order.DeliveringPhotoStatus,
		IsRequireDeliveringPhotoURL: order.IsRequireDeliveringPhotoURL,
		Options:                     order.Options,
		PriceSummary:                getPriceSummary(order),
		History:                     order.History,
		Commission:                  order.Commission,
		CommissionRate:              order.CommissionRate,
		TotalTax:                    order.TotalTax,
		SpecialEvent:                order.SpecialEvent,
		RevenueAgentModel:           order.RevenueAgentModel,
		IsAutoAssigned:              order.IsAutoAssigned,
		NoteToDriver:                getNoteToDriver(order),
		Items:                       pickUpRoute.PickingItems,
		CancelDetail:                order.CancelDetail,
		TipAmount:                   order.TipAmount,
		OnGoingTipAmount:            order.OnGoingTipAmount,
		CreatedAt:                   order.CreatedAt,
		UpdatedAt:                   order.UpdatedAt,
		ExpiredAt:                   order.ExpireAt,
		Routes:                      order.Routes,
		IsRain:                      order.IsRain,
		HasPickupDistanceOnTop:      order.HasPickupDistanceOnTop,
	}
}

func NewTripHistoryOrderRes(tripOrder model.TripOrder, order order.OrderDetailRes) TripHistoryOrderRes {
	pickUpRoute := getPickUpRoute(order)
	return TripHistoryOrderRes{
		OrderID:           tripOrder.OrderID,
		ServiceType:       tripOrder.ServiceType,
		Status:            order.Status,
		TotalTax:          order.TotalTax,
		SpecialEvent:      order.SpecialEvent,
		RevenueAgentModel: order.RevenueAgentModel,
		NoteToDriver:      order.NoteToDriver,
		Options:           order.Options,
		Items:             pickUpRoute.PickingItems,
		PriceSummary:      getPriceSummary(order),
		TipAmount:         order.TipAmount,
		OnGoingTipAmount:  order.OnGoingTipAmount,
		CreatedAt:         order.CreatedAt,
		UpdatedAt:         order.UpdatedAt,
	}
}

func NewTripInactiveOrderRes(tripOrder model.TripOrder, order order.OrderDetailRes) TripInactiveOrderRes {
	pickUpRoute := getPickUpRoute(order)
	dropOffRoute := getDropOffRoute(order)
	res := TripInactiveOrderRes{
		OrderID:        tripOrder.OrderID,
		UserID:         order.UserID,
		ServiceType:    order.ServiceType,
		CancelDetail:   order.CancelDetail,
		PickUpName:     pickUpRoute.Name,
		PickUpAddress:  pickUpRoute.Address,
		PickupDistance: pickUpRoute.Distance,
		PickUpInfo:     pickUpRoute.Info,
		DropOffName:    dropOffRoute.Name,
		DropOffAddress: dropOffRoute.Address,
		Options:        order.Options,
		Items:          pickUpRoute.PickingItems,
		PriceSummary:   getPriceSummary(order),
		Status:         order.Status,
		CreatedAt:      order.CreatedAt,
		UpdatedAt:      order.UpdatedAt,
	}

	if order.ServiceType.IsRevampedStatus() {
		res.PickUpName = ""
		res.PickUpAddress = ""
		res.DropOffName = ""
		res.DropOffAddress = ""
	}

	return res
}

func NewTransferDetailRes(summary model.TripDriverWageSummary) TransferDetailRes {
	return TransferDetailRes{
		Outstanding:    summary.Outstanding,
		TransferAmount: summary.TransferAmount,
	}
}

func NewActiveMoneySummaryRes(ordersMap map[string]order.OrderDetailRes) MoneySummaryRes {
	moneySummary := MoneySummaryRes{}
	for _, orderRes := range ordersMap {
		if orderRes.Status != model.StatusCanceled && orderRes.Status != model.StatusExpired {
			moneySummary.CashPay = moneySummary.CashPay.Add(calculateCashPay(orderRes))
			moneySummary.CreditDeduct = moneySummary.CreditDeduct.Add(calculateCreditDeduct(orderRes))
			moneySummary.CashReceive = moneySummary.CashReceive.Add(calculateCashReceive(orderRes))
		}
	}
	return moneySummary
}

func NewInactiveMoneySummaryRes(ordersMap map[string]order.OrderDetailRes) MoneySummaryRes {
	moneySummary := MoneySummaryRes{}
	for _, orderRes := range ordersMap {
		if orderRes.Status == model.StatusCanceled || orderRes.Status == model.StatusExpired {
			moneySummary.CashPay = moneySummary.CashPay.Add(calculateCashPay(orderRes))
			moneySummary.CreditDeduct = moneySummary.CreditDeduct.Add(calculateCreditDeduct(orderRes))
			moneySummary.CashReceive = moneySummary.CashReceive.Add(calculateCashReceive(orderRes))
		}
	}
	return moneySummary
}

func NewEarningRes(trip model.Trip, ordersMap map[string]order.OrderDetailRes) EarningRes {
	earningRes := EarningRes{
		WageWallet:              trip.DriverWageSummary.TotalDriverWage,
		Tax:                     trip.DriverWageSummary.WithHoldingTax,
		CommissionRate:          int(trip.DriverWageSummary.CommissionRate * 100),
		CommissionAmount:        trip.DriverWageSummary.TotalCommission(),
		WithHoldingOnTopTax:     trip.DriverWageSummary.WithHoldingOnTopTax,
		WithHoldingOnTopTaxRate: int(trip.DriverWageSummary.WithHoldingOnTopTaxRate * 100),
	}

	// Do not support MO for agent model
	if len(ordersMap) == 1 {
		for _, orderRes := range ordersMap {
			if orderRes.RevenueAgentModel {
				priceSummary := getPriceSummary(orderRes)
				if priceSummary.Earning != nil {
					earningRes.WageCash = types.NewMoney(priceSummary.Earning.Cash)
					earningRes.WageWallet = types.NewMoney(priceSummary.Earning.Wallet).Sub(types.NewMoney(priceSummary.Earning.TotalOnTopFare))

					// AdditionalService is only supported on agent model
					earningRes.AdditionalService = types.NewMoney(priceSummary.Earning.AdditionalService)
				}
			}
		}
	}

	for _, orderRes := range ordersMap {
		priceSummary := getPriceSummary(orderRes)
		if orderRes.Status != model.StatusCanceled && orderRes.Status != model.StatusExpired {
			if priceSummary.Earning != nil {
				earningRes.OnTop = earningRes.OnTop.Add(types.Money(priceSummary.Earning.OnTop))
				earningRes.EGSOnTop = earningRes.EGSOnTop.Add(types.Money(priceSummary.Earning.EGSOnTopFare))
				earningRes.TotalOnTopFare = earningRes.TotalOnTopFare.Add(types.Money(priceSummary.Earning.TotalOnTopFare))
			}
			earningRes.Coin += orderRes.Coin
			earningRes.Tax = earningRes.Tax.Add(orderRes.OnTopWithholdingTax)
			earningRes.CommissionAmount = earningRes.CommissionAmount.Add(orderRes.OnTopFareCommission)
			earningRes.TotalTipAmount = earningRes.TotalTipAmount.Add(orderRes.TipAmount)
			earningRes.TotalTipAmount = earningRes.TotalTipAmount.Add(orderRes.OnGoingTipAmount)
		}
	}

	earningRes.TotalWage = earningRes.WageWallet.Add(earningRes.WageCash).Sub(earningRes.AdditionalService)
	earningRes.TotalBeforeTax = earningRes.TotalWage.Add(earningRes.TotalOnTopFare).Add(earningRes.AdditionalService)
	if trip.IsRevenuePrincipalModel() {
		earningRes.TotalAfterTax = earningRes.TotalBeforeTax.Sub(earningRes.Tax)
	} else {
		earningRes.TotalAfterTax = earningRes.TotalBeforeTax.Sub(earningRes.CommissionAmount).Sub(earningRes.WithHoldingOnTopTax)
	}

	return earningRes
}

func NewBulkCompleteTripRes(successes []string, failures []string) BulkCompleteTripRes {
	return BulkCompleteTripRes{
		Successes: successes,
		Failures:  failures,
	}
}

func isHalfHalf(orderRes order.OrderDetailRes) bool {
	events := orderRes.SpecialEvent
	for _, e := range events {
		if e == model.SpecialEventHalfHalf {
			return true
		}
	}
	return false
}

func calculateCashPay(orderRes order.OrderDetailRes) types.Money {
	priceSummary := getPriceSummary(orderRes)

	if isHalfHalf(orderRes) {
		return types.NewMoney(0.0)
	}

	var cashPay float64
	if !orderRes.RevenueAgentModel {
		if priceSummary.DeliveryFee.PaymentMethod.IsCash() &&
			priceSummary.ItemFee.PaymentMethod.IsCash() {
			cashPay = priceSummary.ItemFee.SubTotal
		} else if orderRes.Options.DriverMoneyFlow == model.FlowCashAdvancementCoupon ||
			orderRes.Options.DriverMoneyFlow == model.FlowCashAdvancementEpayment {
			cashPay = priceSummary.ItemFee.SubTotal
		}
	}

	return types.NewMoney(cashPay)
}

func calculateCashReceive(orderRes order.OrderDetailRes) types.Money {
	priceSummary := getPriceSummary(orderRes)

	var cashReceive float64
	if priceSummary.DeliveryFee.PaymentMethod.IsCash() &&
		priceSummary.ItemFee.PaymentMethod.IsCash() {
		// For HALF-HALF order, priceSummary.Total is overwritten by earning.cash in order detail res
		cashReceive = priceSummary.Total
	} else if orderRes.Options.DriverMoneyFlow == model.FlowCashAdvancementCoupon ||
		orderRes.Options.DriverMoneyFlow == model.FlowCashCollection {
		cashReceive = priceSummary.Total
	} else if priceSummary.DeliveryFee.PaymentMethod.IsCash() &&
		priceSummary.ItemFee.PaymentMethod.IsEPayment() {
		cashReceive = priceSummary.DeliveryFee.UserDeliveryFee
	}

	return types.NewMoney(cashReceive)
}

func calculateCreditDeduct(orderRes order.OrderDetailRes) types.Money {
	priceSummary := getPriceSummary(orderRes)

	var creditDeduct float64
	if !orderRes.RevenueAgentModel {
		if orderRes.Options.DriverMoneyFlow == model.FlowCashCollection {
			creditDeduct = priceSummary.ItemFee.SubTotal
		}
	}

	return types.NewMoney(creditDeduct)
}

func getRouteFromStopId(orderRes order.OrderDetailRes, stopId int) order.StopRes {
	if stopId >= len(orderRes.Routes) {
		return order.StopRes{}
	}
	return orderRes.Routes[stopId]
}

func getPriceSummary(orderRes order.OrderDetailRes) order.PriceSummaryRes {
	if orderRes.PayAtStop >= len(orderRes.Routes) {
		return order.PriceSummaryRes{}
	}
	return orderRes.Routes[orderRes.PayAtStop].PriceSummary
}

func getPickUpRoute(orderRes order.OrderDetailRes) order.StopRes {
	if len(orderRes.Routes) < 1 {
		return order.StopRes{}
	}
	return orderRes.Routes[0]
}

func getDropOffRoute(orderRes order.OrderDetailRes) order.StopRes {
	if len(orderRes.Routes) < 2 {
		return order.StopRes{}
	}
	return orderRes.Routes[1]
}

func getNoteToDriver(orderRes order.OrderDetailRes) string {
	if orderRes.Options.IsWechatUser {
		return ""
	}
	return orderRes.NoteToDriver
}
