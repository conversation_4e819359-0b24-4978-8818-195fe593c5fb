//go:build integration_test
// +build integration_test

package province_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gopkg.in/guregu/null.v4"

	provincepkg "git.wndv.co/lineman/fleet-distribution/internal/apis/province"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
)

func TestProvinceAdminAPI_Create(t *testing.T) {
	t.Run("create province", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		provinceDBHelper := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "provinces")
		numRecords := provinceDBHelper.CountAll()

		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/province").
			Body().
			JSON(provincepkg.ProvinceAdminRequest{
				Name:                   "กรุง",
				RegionCode:             "BK",
				Label:                  "กรุงเทพ",
				RequestedBy:            "User1",
				RegistrationFlow:       "NORMAL",
				IsWhitelistAutoApprove: true,
				IsNormalAutoApprove:    true,
			}).
			Build()

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		assert.True(t, provinceDBHelper.CountAll() == numRecords+1)
		var province model.Province
		provinceDBHelper.LastRecord(&province)
		assert.Equal(t, "กรุง", province.Name)
		assert.Equal(t, "NORMAL", string(province.RegistrationFlow))
		assert.Equal(t, "BK", province.RegionCode)
		assert.Equal(t, "User1", province.RequestedBy)
		assert.Equal(t, true, province.IsWhitelistAutoApprove)
		assert.Equal(t, true, province.IsNormalAutoApprove)
	})

	t.Run("create province v2", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		provinceDBHelper := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "provinces")
		numRecords := provinceDBHelper.CountAll()

		gctx := testutil.NewContextWithRecorder()
		gctx.SetPOST("/v1/admin/province").
			Body().
			JSON(provincepkg.ProvinceAdminRequest{
				Name:                   "กรุง",
				RegionCode:             "BK",
				Label:                  "กรุงเทพ",
				RequestedBy:            "User1",
				RegistrationFlow:       "NORMAL",
				AdminApprovalFlow:      null.NewString("REVISE", true),
				Hidden:                 null.NewBool(true, true),
				IsWhitelistAutoApprove: true,
				IsNormalAutoApprove:    true,
			}).
			Build()

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		assert.True(t, provinceDBHelper.CountAll() == numRecords+1)
		var province model.Province
		provinceDBHelper.LastRecord(&province)
		assert.Equal(t, "REVISE", string(province.AdminApprovalFlow))
		assert.Equal(t, "BK", province.RegionCode)
		assert.Equal(t, true, province.Hidden)
	})
}

func TestProvinceAdminAPI_List(t *testing.T) {
	t.Run("list provinces", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		provinceDBHelper := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "provinces")
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/admin/province")

		ctn.GinEngineRouter.HandleContext(gctx.GinCtx())

		gctx.AssertResponseCode(t, 200)
		var resp []provincepkg.ProvinceAdminResponse
		gctx.DecodeJSONResponse(&resp)
		assert.Len(t, resp, provinceDBHelper.CountAll())
	})
}

func TestProvinceAdminAPI_Get(t *testing.T) {
	t.Run("get province", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		id := testdata.ObjectId(1)
		provinceDBHelper := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "provinces")
		var province model.Province
		provinceDBHelper.Get(id, &province)
		gctx := testutil.NewContextWithRecorder()
		gctx.SetGET("/v1/admin/province/%s", id)

		gctx.Send(ctn.GinEngineRouter)

		gctx.AssertResponseCode(t, 200)
		var resp provincepkg.ProvinceAdminResponse
		gctx.DecodeJSONResponse(&resp)
		assert.Equal(t, province.ID.Hex(), resp.ID)
		assert.Equal(t, province.RegionCode, resp.RegionCode)
		assert.Equal(t, string(province.RegistrationFlow), resp.RegistrationFlow)
		assert.Equal(t, string(province.AdminApprovalFlow), resp.AdminApprovalFlow)
		assert.Equal(t, province.Name, resp.Name)
		assert.Equal(t, province.Label, resp.Label)
		assert.Equal(t, province.IsWhitelistAutoApprove, resp.IsWhitelistAutoApprove)
		assert.Equal(t, province.IsNormalAutoApprove, resp.IsNormalAutoApprove)
		assert.Equal(t, province.Hidden, resp.Hidden)
	})
}

func TestProvinceAdminAPI_Delete(t *testing.T) {
	t.Run("delete province", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		id := testdata.ObjectId(1)
		provinceDBHelper := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "provinces")
		assert.True(t, provinceDBHelper.IsExist(id))
		gctx := testutil.NewContextWithRecorder()
		gctx.SetDELETE("/v1/admin/province/%s", id)

		gctx.Send(ctn.GinEngineRouter)

		gctx.AssertResponseCode(t, 200)
		assert.False(t, provinceDBHelper.IsExist(id))
	})
}

func TestProvinceAdminAPI_Update(t *testing.T) {
	t.Run("update province", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		region1 := testdata.ObjectId(1)
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/admin/province/%s", region1).
			Body().
			JSON(provincepkg.ProvinceAdminRequest{
				Name:                   "กรุง",
				RegionCode:             "BK",
				Label:                  "กรุงเทพ",
				RequestedBy:            "User1",
				RegistrationFlow:       "NORMAL",
				IsWhitelistAutoApprove: false,
				IsNormalAutoApprove:    false,
			}).
			Build()

		gctx.Send(ctn.GinEngineRouter)

		gctx.AssertResponseCode(t, 200)
		province, err := ctn.ProvinceRepository.Get(context.Background(), region1)
		assert.NoError(t, err)
		assert.Equal(t, "กรุง", province.Name)
		assert.Equal(t, "NORMAL", string(province.RegistrationFlow))
		assert.Equal(t, "BK", province.RegionCode)
		assert.Equal(t, "User1", province.RequestedBy)
		assert.Equal(t, false, province.IsWhitelistAutoApprove)
		assert.Equal(t, false, province.IsNormalAutoApprove)
	})

	t.Run("update province with all fields", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		region1 := testdata.ObjectId(1)
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/admin/province/%s", region1).
			Body().
			JSON(provincepkg.ProvinceAdminRequest{
				Name:                   "กรุง",
				RegionCode:             "BK",
				Label:                  "กรุงเทพ",
				RequestedBy:            "User1",
				RegistrationFlow:       "NORMAL",
				AdminApprovalFlow:      null.NewString("REVISE", true),
				Hidden:                 null.NewBool(true, true),
				IsWhitelistAutoApprove: false,
				IsNormalAutoApprove:    false,
			}).
			Build()

		gctx.Send(ctn.GinEngineRouter)

		gctx.AssertResponseCode(t, 200)
		province, err := ctn.ProvinceRepository.Get(context.Background(), region1)
		assert.NoError(t, err)
		assert.Equal(t, "กรุง", province.Name)
		assert.Equal(t, "NORMAL", string(province.RegistrationFlow))
		assert.Equal(t, "REVISE", string(province.AdminApprovalFlow))
		assert.Equal(t, "BK", province.RegionCode)
		assert.Equal(t, "User1", province.RequestedBy)
		assert.Equal(t, false, province.IsWhitelistAutoApprove)
		assert.Equal(t, false, province.IsNormalAutoApprove)
		assert.Equal(t, true, province.Hidden)
	})

	t.Run("update province with optional field on existing province must not affect", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		region1 := testdata.ObjectId(4)
		oriProvince, err := ctn.ProvinceRepository.Get(context.Background(), region1)
		require.NoError(t, err)
		gctx := testutil.NewContextWithRecorder()
		gctx.SetPUT("/v1/admin/province/%s", region1).
			Body().
			JSON(provincepkg.ProvinceAdminRequest{
				Name:                   "นนทบุรี2",
				RegionCode:             "NONTABURI2",
				Label:                  "นนทบุรี2",
				RequestedBy:            "User2",
				RegistrationFlow:       "REVISE",
				IsWhitelistAutoApprove: false,
				IsNormalAutoApprove:    false,
			}).
			Build()

		gctx.Send(ctn.GinEngineRouter)

		gctx.AssertResponseCode(t, 200)
		province, err := ctn.ProvinceRepository.Get(context.Background(), region1)
		assert.NoError(t, err)
		assert.Equal(t, "นนทบุรี2", province.Name)
		assert.Equal(t, "REVISE", string(province.RegistrationFlow))
		assert.Equal(t, "NONTABURI2", province.RegionCode)
		assert.Equal(t, "User2", province.RequestedBy)
		assert.Equal(t, false, province.IsWhitelistAutoApprove)
		assert.Equal(t, false, province.IsNormalAutoApprove)
		assert.Equal(t, oriProvince.AdminApprovalFlow, province.AdminApprovalFlow)
		assert.Equal(t, oriProvince.Hidden, province.Hidden)
	})
}
