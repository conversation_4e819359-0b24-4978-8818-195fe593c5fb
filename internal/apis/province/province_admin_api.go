package province

import (
	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

type ProvinceAdminAPI struct {
	provinceRepo repository.ProvinceRepository
}

func (p *ProvinceAdminAPI) Create(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	req, err := NewProvinceAdminRequest(gCtx)
	if err != nil {
		apiutil.InvalidJSONRequest(gCtx)
		return
	}

	provinceModel := req.ToProvince()
	err = p.provinceRepo.Create(ctx, &provinceModel)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, apiutil.EmptyBody())
}

func (p *ProvinceAdminAPI) List(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	pv, err := p.provinceRepo.ListAll(ctx, repository.ProvinceQuery{})
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}
	listProvince := make([]ProvinceAdminResponse, len(pv))
	for i, v := range pv {
		p := NewProvinceAdminResponse(v)
		listProvince[i] = p
	}
	apiutil.OK(gCtx, listProvince)
}

func (p *ProvinceAdminAPI) Get(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	id := gCtx.Param(ProvinceKeyID)
	pv, err := p.provinceRepo.Get(ctx, id)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, NewProvinceAdminResponse(pv))
}

func (p *ProvinceAdminAPI) Update(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	id := gCtx.Param(ProvinceKeyID)
	pv, err := p.provinceRepo.Get(ctx, id)
	if err != nil {
		apiutil.ErrNotFound(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	req, err := NewProvinceAdminRequest(gCtx)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	req.UpdateProvince(&pv)
	err = p.provinceRepo.Update(ctx, &pv)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}
	apiutil.OK(gCtx, apiutil.EmptyBody())
}

func (p *ProvinceAdminAPI) Delete(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	id := gCtx.Param(ProvinceKeyID)
	pv, err := p.provinceRepo.Get(ctx, id)
	if err != nil {
		apiutil.ErrNotFound(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	err = p.provinceRepo.Delete(ctx, &pv)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, apiutil.EmptyBody())
}

func ProvideProvinceAdminAPI(provinceRepo repository.ProvinceRepository) *ProvinceAdminAPI {
	return &ProvinceAdminAPI{provinceRepo: provinceRepo}
}
