package province

import "git.wndv.co/lineman/fleet-distribution/internal/domain/model"

type ProvinceAdminResponse struct {
	ID                     string `json:"id"`
	Name                   string `json:"name"`
	RegionCode             string `json:"regionCode"`
	Label                  string `json:"label"`
	RegistrationFlow       string `json:"registrationFlow"`
	AdminApprovalFlow      string `json:"adminApprovalFlow"`
	Hidden                 bool   `json:"hidden"`
	IsWhitelistAutoApprove bool   `json:"isWhitelistAutoApprove"`
	IsNormalAutoApprove    bool   `json:"isNormalAutoApprove"`
	Priority               int    `json:"priority"`
}

func NewProvinceAdminResponse(pv model.Province) ProvinceAdminResponse {
	return ProvinceAdminResponse{
		ID:                     pv.ID.Hex(),
		Name:                   pv.Name,
		RegionCode:             pv.RegionCode,
		Label:                  pv.Label,
		RegistrationFlow:       string(pv.RegistrationFlow),
		IsWhitelistAutoApprove: pv.IsWhitelistAutoApprove,
		IsNormalAutoApprove:    pv.IsNormalAutoApprove,
		Priority:               pv.Priority,
		AdminApprovalFlow:      string(pv.AdminApprovalFlow),
		Hidden:                 pv.Hidden,
	}
}
