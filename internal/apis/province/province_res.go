package province

import "git.wndv.co/lineman/fleet-distribution/internal/domain/model"

type FlowResponse struct {
	RegistrationFlow string `json:"registrationFlow" example:"NORMAL" enums:"NORMAL,REVISE"`
}

func NewFlowResponse(flow model.RegistrationFlow) FlowResponse {
	return FlowResponse{
		RegistrationFlow: string(flow),
	}
}

func DefaultFlowResponse() FlowResponse {
	return FlowResponse{
		RegistrationFlow: "NORMAL",
	}
}
