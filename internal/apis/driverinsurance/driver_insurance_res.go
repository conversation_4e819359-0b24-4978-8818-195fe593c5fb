package driverinsurance

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

type ConsolidateResponse struct {
	Count     int      `json:"count"`
	DriverIDs []string `json:"driverIDs"`
}

type BulkUpdatePolicyNumberResponse struct {
	MotorActiveWant      int `json:"motor_active_want"`
	MotorActiveSuccesses int `json:"motor_active_successes"`
	MotorRejectWant      int `json:"motor_reject_want"`
	MotorRejectSuccesses int `json:"motor_reject_successes"`
}

func NewConsolidateResponse(count int, driverIDs []string) ConsolidateResponse {
	return ConsolidateResponse{
		Count:     count,
		DriverIDs: driverIDs,
	}
}

func NewBulkUpdatePolicyNumber(motorList PolicyStatusDriverIDsList, activeMotorSuccess int, rejectMotorSuccess int) BulkUpdatePolicyNumberResponse {
	return BulkUpdatePolicyNumberResponse{
		MotorActiveWant:      len(motorList.Approve),
		MotorActiveSuccesses: activeMotorSuccess,
		MotorRejectWant:      len(motorList.Reject),
		MotorRejectSuccesses: rejectMotorSuccess,
	}
}

type ConsolidatedInsuranceInfoResponse struct {
	ID             string    `json:"id"`
	ProgramType    string    `json:"programType"`
	URL            string    `json:"url"`
	ConsolidatedBy string    `json:"consolidatedBy"`
	CreatedAt      time.Time `json:"createdAt"`
}

func ConvertConsolidatedInsuranceInfosToResponse(ctx context.Context, vosService service.VOSService, srcs []model.ConsolidatedInsuranceInfo) []ConsolidatedInsuranceInfoResponse {
	if vosService == nil {
		return []ConsolidatedInsuranceInfoResponse{}
	}
	responses := make([]ConsolidatedInsuranceInfoResponse, 0, len(srcs))
	for _, src := range srcs {
		responseItem := ConsolidatedInsuranceInfoResponse{
			ID:             src.ID.Hex(),
			ProgramType:    string(src.Program),
			ConsolidatedBy: src.ConsolidatedBy,
			CreatedAt:      src.CreatedAt,
		}
		if src.FileUrl != "" {
			presignedURL, err := vosService.GetPreSignedUrl(ctx, src.FileUrl, 15*time.Minute)
			if err != nil {
				logrus.Errorf("unable to get pre-signed url - err: %v", err)
				continue
			}
			responseItem.URL = presignedURL
		}
		responses = append(responses, responseItem)
	}
	return responses
}

type InsurancePolicyFileRes struct {
	Name       string     `json:"name"`
	ID         string     `json:"id"`
	Location   string     `json:"location"`
	LastUpdate *time.Time `json:"last_update"`
}
