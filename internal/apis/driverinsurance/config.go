package driverinsurance

import (
	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type Config struct {
	SponsorPersonalAccidentDriverTiers                 types.StringSet `envconfig:"INSURANCE_SPONSOR_PA_DRIVER_TIER" default:"PRO,PRO+"`
	SponsorMotorcycleDriverTiers                       types.StringSet `envconfig:"INSURANCE_SPONSOR_MOTOR_DRIVER_TIER" default:"PRO+"`
	UploadInsuranceConsolidateFileToVosInternalEnabled bool            `envconfig:"UPLOAD_RIDER_INSURANCE_CONSOLIDATE_FILE_TO_VOS_INTERNAL_ENABLED" default:"true"`
	InsurancePolicyVosPrefix                           string          `envconfig:"INSURANCE_POLICY_VOS_PREFIX" default:"driver_public"`
	InsurancePolicyVosPath                             string          `envconfig:"INSURANCE_POLICY_VOS_PATH" default:"driver-insurance"`
	PersonalAccidentInsurancePolicyFileName            string          `envconfig:"INSURANCE_POLICY_PA_FILE_NAME" default:"LMWN_PA_CHUBB.pdf"`
	MotorcycleInsurancePolicyFileName                  string          `envconfig:"INSURANCE_POLICY_MOTOR_FILE_NAME" default:"LMWN_Motor_{{MonthCycle}}{{Version}}.pdf"`
	CDNEndpoint                                        string          `envconfig:"VOS_CDN_ENDPOINT" required:"true"`
}

func ProvideInsuranceConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
