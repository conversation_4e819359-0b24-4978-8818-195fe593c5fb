package shift

import (
	"context"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

type ShiftAPI struct {
	shiftRepo       repository.ShiftRepository
	driverRepo      repository.DriverRepository
	txnHelper       transaction.TxnHelper
	config          Config
	svcAreaRepo     repository.ServiceAreaRepository
	shiftCancelRepo repository.ShiftCancelRepository
	attendanceSVC   service.Attendances
}

func (s *ShiftAPI) Delete(gCtx *gin.Context) {
	ctx := gCtx.Request.Context()
	id := gCtx.Param("id")
	shift, err := s.shiftRepo.FindByID(ctx, id)
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	err = s.shiftRepo.Delete(ctx, &shift)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, apiutil.EmptyBody())
}

func (s *ShiftAPI) Create(gCtx *gin.Context) {
	req, err := NewShiftRequest(gCtx)
	if err != nil {
		if v, ok := err.(*api.Error); ok {
			apiutil.ErrBadRequest(gCtx, v)
			return
		}
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	if err := req.Validate(); err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	shifts, err := req.toShifts(auth.GetAdminEmailFromGctx(gCtx))
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	err = s.shiftRepo.AnyStartEndDatetimeDuplicated(gCtx, shifts, []string{}, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError("START_END_DATETIME_DUPLICATED", err))
		return
	}

	err = s.shiftRepo.CreateAll(gCtx, shifts)
	if err != nil {
		logrus.Warnf("create shift err :: %v", err)
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.Created(gCtx, apiutil.EmptyBody())
}

func (s *ShiftAPI) Update(gCtx *gin.Context) {
	req, err := NewShiftUpdateRequest(gCtx)
	if err != nil {
		if v, ok := err.(*api.Error); ok {
			apiutil.ErrBadRequest(gCtx, v)
			return
		}
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	if err := req.Validate(); err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	shiftID := gCtx.Param("shift_id")
	oldShift, err := s.shiftRepo.FindByID(gCtx, shiftID)

	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	updateShift, err := req.updateShift(oldShift, auth.GetAdminEmailFromGctx(gCtx))
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	err = s.shiftRepo.AnyStartEndDatetimeDuplicated(gCtx, []model.Shift{updateShift}, []string{shiftID}, repository.WithReadSecondaryPreferred)
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiutil.NewFromError("START_END_DATETIME_DUPLICATED", err))
		return
	}

	err = s.shiftRepo.UpdateByID(gCtx, shiftID, &updateShift)
	if err != nil {
		logrus.Warnf("update shift id %v error : %v", shiftID, err)
		apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
		return
	}

	apiutil.OK(gCtx, NewShiftRes(updateShift))
}

func (s *ShiftAPI) updateActive(ctx context.Context, shiftID string, active bool) error {
	sh, err := s.shiftRepo.FindByID(ctx, shiftID)
	if err != nil {
		logrus.Errorf("find shift error: %v", err)
		return err
	}

	sh.Active = active
	err = s.shiftRepo.UpdateByID(ctx, shiftID, &sh)
	if err != nil {
		logrus.Errorf("update shift error: %v", err)
		return err
	}

	return nil
}

func (s *ShiftAPI) updateActiveInBackground(gCtx context.Context, shiftID string, active bool, resp *BulkActiveShiftsResp, wg *sync.WaitGroup) {
	safe.GoFunc(func() {
		defer wg.Done()
		err := s.updateActive(gCtx, shiftID, active)
		if err != nil {
			resp.AddFail(shiftID)
		} else {
			resp.AddSuccess(shiftID)
		}
	})
}

func (s *ShiftAPI) BulkActiveShifts(gCtx *gin.Context) {
	var req SetActiveRequest
	if err := gCtx.ShouldBindJSON(&req); err != nil {
		apiutil.InvalidJSONRequest(gCtx)
		return
	}

	reqCtx := gCtx.Request.Context()
	resp := NewBulkActiveShiftsResp()
	wg := &sync.WaitGroup{}
	wg.Add(len(req.ShiftIDs))
	for _, id := range req.ShiftIDs {
		s.updateActiveInBackground(reqCtx, id, req.Active, resp, wg)
	}
	wg.Wait()

	apiutil.OK(gCtx, resp)
}

func (s *ShiftAPI) GetAll(gCtx *gin.Context) {
	var req ListReq
	startDate := gCtx.Query("startDate")
	endDate := gCtx.Query("endDate")
	parsedStart := time.Time{}
	parsedEnd := time.Time{}

	if startDate != "" {
		if date, err := time.Parse(time.RFC3339, startDate); err == nil {
			parsedStart = date
		} else {
			apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
			return
		}
	}
	if endDate != "" {
		if date, err := time.Parse(time.RFC3339, endDate); err == nil {
			parsedEnd = date
		} else {
			apiutil.ErrInternalError(gCtx, apiError.ErrInternal(err))
			return
		}
	}

	if err := gCtx.ShouldBindQuery(&req); err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	query := req.ToQuery(parsedStart, parsedEnd)
	skip, size := utils.ParsePagination(gCtx)
	res, countTotal, err := s.shiftRepo.Find(gCtx, query, skip, size)
	if err != nil {
		apiutil.ErrInternalError(gCtx, apiError.ErrInvalidRequest(err))
		return
	}

	apiutil.OK(gCtx, GetAllRes{
		Data:  NewListShiftRes(res),
		Count: countTotal,
	})
}

func (s *ShiftAPI) Get(gCtx *gin.Context) {
	shiftID := gCtx.Param("shift_id")
	res, err := s.shiftRepo.FindByIdDeletedAtNotExist(gCtx, shiftID)
	if err != nil {
		apiutil.ErrBadRequest(gCtx, apiError.ErrInvalidRequest(err))
		return
	}
	apiutil.OK(gCtx, NewShiftRes(*res))
}

func ProvideShiftAPI(shiftRepo repository.ShiftRepository, driverRepo repository.DriverRepository, shiftCancelRepo repository.ShiftCancelRepository, txnHelper transaction.TxnHelper, config Config, svRepo repository.ServiceAreaRepository, atdSvc service.Attendances) *ShiftAPI {

	return &ShiftAPI{
		shiftRepo:       shiftRepo,
		driverRepo:      driverRepo,
		txnHelper:       txnHelper,
		config:          config,
		shiftCancelRepo: shiftCancelRepo,
		svcAreaRepo:     svRepo,
		attendanceSVC:   atdSvc,
	}
}
