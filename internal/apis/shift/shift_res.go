package shift

import (
	"sync"
	"time"

	"gopkg.in/guregu/null.v4"

	absintheUtils "git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type ShiftRes struct {
	ID                    string                 `json:"id"`
	Region                string                 `json:"region"`
	Label                 string                 `json:"label"`
	Active                bool                   `json:"active"`
	Quota                 int                    `json:"quota"`
	BookedAmount          int                    `json:"bookedAmount"`
	DriverIDs             []string               `json:"driverIds"`
	RestaurantIDs         []string               `json:"restaurantIds"`
	RestaurantIdEnabled   bool                   `json:"restaurantIdEnabled"`
	Distance              float64                `json:"distance"`
	DiscountPrice         float64                `json:"discountPrice"`
	MaxBasketPrice        float64                `json:"maxBasketPrice"`
	MaxBasketPriceEnabled bool                   `json:"maxBasketPriceEnabled"`
	Start                 time.Time              `json:"start"`
	End                   time.Time              `json:"end"`
	CreatedAt             time.Time              `json:"createdAt"`
	UpdatedAt             time.Time              `json:"updatedAt"`
	DeletedAt             *time.Time             `json:"deletedAt,omitempty"`
	CreatedBy             string                 `json:"createdBy"`
	Coordinates           model.ShiftCoordinates `json:"coordinates"`
	BreakQuota            float64                `json:"breakQuota"`
	BreakTime             *float64               `json:"breakTime,omitempty"`
}

type GetAllRes struct {
	Data  []ShiftRes `json:"data"`
	Count int        `json:"countTotal"`
}

type ListReq struct {
	Region              string    `form:"region"`
	Active              null.Bool `form:"active"`
	RestaurantIdEnabled null.Bool `form:"restaurantIdEnabled"`
	RestaurantIDs       string    `form:"restaurantIds"`
	BasketPriceEnabled  null.Bool `form:"basketPriceEnabled"`
	BasketPrice         float64   `form:"basketPrice"`
}

func (l *ListReq) ToQuery(s time.Time, e time.Time) repository.ShiftQuery {
	return repository.ShiftQuery{Region: l.Region, IsDeleted: null.BoolFrom(false), Active: l.Active,
		RestaurantIdEnabled: l.RestaurantIdEnabled, RestaurantIds: l.RestaurantIDs, BasketPriceEnabled: l.BasketPriceEnabled,
		BasketPrice: l.BasketPrice, StartDate: s, EndDate: e}
}

func NewShiftRes(s model.Shift) ShiftRes {
	loc := timeutil.BangkokLocation()
	return ShiftRes{
		ID:                    s.ID.Hex(),
		Region:                s.Region,
		Label:                 s.Label,
		Active:                s.Active,
		Quota:                 s.Quota,
		BookedAmount:          s.BookedAmount,
		DriverIDs:             s.DriverIDs,
		RestaurantIDs:         s.RestaurantIDs,
		RestaurantIdEnabled:   s.RestaurantIdEnabled,
		Distance:              s.Distance,
		DiscountPrice:         s.DiscountPrice,
		BreakQuota:            s.BreakQuota,
		MaxBasketPrice:        s.MaxBasketPrice,
		MaxBasketPriceEnabled: s.MaxBasketPriceEnabled,
		Start:                 s.Start.In(loc),
		End:                   s.End.In(loc),
		CreatedAt:             s.CreatedAt.In(loc),
		UpdatedAt:             s.UpdatedAt.In(loc),
		CreatedBy:             s.CreatedBy,
		Coordinates:           s.Geometry.Coordinates,
	}
}

func NewListShiftRes(shifts []model.Shift) []ShiftRes {
	var res []ShiftRes
	if len(shifts) == 0 {
		return []ShiftRes{}
	}
	for _, s := range shifts {
		res = append(res, NewShiftRes(s))
	}
	return res
}

type ShiftClientRes struct {
	ID             string            `json:"id"`
	Active         bool              `json:"active"`
	Start          time.Time         `json:"start"`
	End            time.Time         `json:"end"`
	Region         string            `json:"region"`
	Label          string            `json:"label"`
	Distance       float64           `json:"distance"`
	Quota          int               `json:"quota"`
	BreakQuota     float64           `json:"breakQuota"`
	BookAmount     int               `json:"bookAmount"`
	Status         model.ShiftStatus `json:"status"`
	AttendanceRate *float64          `json:"attendanceRate,omitempty"`
	BreakTime      *float64          `json:"breakTime,omitempty"`
}

func calculateShiftStatus(s model.Shift, d model.Driver) model.ShiftStatus {
	booked := absintheUtils.StrContains(d.DriverID, s.DriverIDs) &&
		absintheUtils.StrContains(s.ID.Hex(), d.Shifts)
	if booked {
		return model.StatusBooked
	} else {
		if s.BookedAmount < s.Quota {
			return model.StatusAvailable
		}
	}
	return model.StatusFull
}

func NewShiftClientRes(shift model.Shift, driver model.Driver) *ShiftClientRes {
	return &ShiftClientRes{
		ID:         shift.ID.Hex(),
		Active:     shift.Active,
		Start:      shift.Start,
		End:        shift.End,
		Region:     shift.Region,
		Label:      shift.Label,
		Distance:   shift.Distance,
		Quota:      shift.Quota,
		BookAmount: shift.BookedAmount,
		Status:     calculateShiftStatus(shift, driver),
	}
}

type Res []ShiftRes

func isShiftOverlapped(start time.Time, end time.Time, bookings []ShiftClientRes) bool {
	for _, book := range bookings {
		if (end.After(book.Start) || end.Equal(book.Start)) && (start.Before(book.End) || start.Equal((book.End))) {
			return true
		}
	}
	return false
}

func NewRes(shifts []model.Shift, driver model.Driver) []ShiftClientRes {
	res := make([]ShiftClientRes, len(shifts))
	for i, s := range shifts {
		res[i] = *NewShiftClientRes(s, driver)
	}

	bookings := make([]ShiftClientRes, 0)
	for _, item := range res {
		if item.Status == model.StatusBooked {
			bookings = append(bookings, item)
		}
	}

	for i, item := range res {
		if item.Status == model.StatusAvailable {
			if isShiftOverlapped(item.Start, item.End, bookings) {
				res[i].Status = model.StatusOverlapped
			}
		}
	}

	return res
}

func NewIncomingShiftClientRes(shift model.Shift) *ShiftClientRes {
	return &ShiftClientRes{
		ID:         shift.ID.Hex(),
		Active:     shift.Active,
		Start:      shift.Start,
		End:        shift.End,
		Region:     shift.Region,
		Label:      shift.Label,
		Distance:   shift.Distance,
		Quota:      shift.Quota,
		BreakQuota: shift.BreakQuota,
		BookAmount: shift.BookedAmount,
		Status:     model.StatusBooked,
	}
}

func NewIncomingRes(shifts []model.Shift) []ShiftClientRes {
	res := make([]ShiftClientRes, len(shifts))
	for i, s := range shifts {
		res[i] = *NewIncomingShiftClientRes(s)
	}
	return res
}

func AssignCurrentAttendanceRateAndBreakTime(scr []ShiftClientRes, sr service.ShiftAttendanceRate) []ShiftClientRes {

	if sr.ShiftID == "" {
		return scr
	}

	for k, v := range scr {
		if v.ID == sr.ShiftID {
			scr[k].AttendanceRate = &sr.Rate
			scr[k].BreakTime = &sr.BreakTime
		}
	}

	return scr
}

type CancelReason struct {
	ID    string `json:"id"`
	Name  string `json:"name"`
	Label string `json:"label"`
}

func NewCoordinatesRes(s model.Shift) CoordinatesRes {
	return CoordinatesRes{s.Geometry.Coordinates}
}

type CoordinatesRes struct {
	Coordinates model.ShiftCoordinates `json:"coordinates"`
}

func NewBulkActiveShiftsResp() *BulkActiveShiftsResp {
	return &BulkActiveShiftsResp{
		l:         new(sync.Mutex),
		Successes: []UpdateShiftActiveSuccess{},
		Failures:  []UpdateShiftActiveFail{},
	}
}

type BulkActiveShiftsResp struct {
	l         *sync.Mutex
	Successes []UpdateShiftActiveSuccess `json:"successes"`
	Failures  []UpdateShiftActiveFail    `json:"failures"`
}

func (resp *BulkActiveShiftsResp) AddSuccess(id string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Successes = append(resp.Successes, UpdateShiftActiveSuccess{ShiftID: id})
}

func (resp *BulkActiveShiftsResp) AddFail(id string) {
	resp.l.Lock()
	defer resp.l.Unlock()
	resp.Failures = append(resp.Failures, UpdateShiftActiveFail{ShiftID: id})
}

type UpdateShiftActiveSuccess struct {
	ShiftID string `json:"shiftID"`
}

type UpdateShiftActiveFail struct {
	ShiftID string `json:"shiftID"`
}
