package shift_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/shift"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

func TestAssignCurrentAttendanceRateAndBreakTime(t *testing.T) {
	t.Run("assign rate to valid shift", func(t *testing.T) {
		scr := []shift.ShiftClientRes{
			{
				ID: "s1",
			},
			{
				ID: "s2",
			},
		}

		res := shift.AssignCurrentAttendanceRateAndBreakTime(scr, service.ShiftAttendanceRate{
			ShiftID:   "s2",
			Rate:      10,
			BreakTime: 5,
		})

		require.Equal(t, 2, len(scr))
		require.Nil(t, res[0].AttendanceRate)
		require.Equal(t, *res[1].AttendanceRate, float64(10))
		require.Equal(t, *res[1].BreakTime, float64(5))
	})

	t.Run("not assign ar if no shift id", func(t *testing.T) {
		scr := []shift.ShiftClientRes{
			{
				ID: "s1",
			},
			{
				ID: "s2",
			},
		}

		res := shift.AssignCurrentAttendanceRateAndBreakTime(scr, service.ShiftAttendanceRate{})

		require.Equal(t, 2, len(scr))
		require.Nil(t, res[0].AttendanceRate)
		require.Nil(t, res[1].AttendanceRate)
	})
}
