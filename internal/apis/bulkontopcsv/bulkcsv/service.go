package bulkcsv

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkontopcsv/shared"
	apiError "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type MultipartFileReader interface {
	Open() (multipart.File, error)
}

type BulkOntopCSVService struct {
	bulkImportCSVDataRowsValidator *bulkImportCSVDataRowsValidator
	cfg                            Config
}

func NewBulkOnTopCSVService(cfg Config, adapter Adapter) *BulkOntopCSVService {
	svc := &BulkOntopCSVService{
		cfg:                            cfg,
		bulkImportCSVDataRowsValidator: newValidator(adapter),
	}

	return svc
}

func (svc *BulkOntopCSVService) ParseCSVFile(ctx context.Context, f MultipartFileReader, schemeType shared.SchemeType) (BulkImportCSVData, error) {
	file, err := f.Open()
	if err != nil {
		err = fmt.Errorf("open file error: %w", err)
		return BulkImportCSVData{}, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}
	defer file.Close()

	data, err := parseCSVFile(file, schemeType)
	if err != nil {
		return BulkImportCSVData{}, err
	}

	groupByID(&data)

	return data, nil
}

func (svc *BulkOntopCSVService) ParseDeleteCSVFile(ctx context.Context, f MultipartFileReader) (BulkDeleteCSVData, error) {
	file, err := f.Open()
	if err != nil {
		return BulkDeleteCSVData{}, fmt.Errorf("open file error: %v", err)
	}
	defer file.Close()

	data, err := parseBulkDeleteCSVFile(file)
	if err != nil {
		if errors.Is(err, io.EOF) {
			return BulkDeleteCSVData{}, ErrEmptyFileOrHeader()
		}
		return BulkDeleteCSVData{}, fmt.Errorf("parse csv file error: %w", err)
	}

	return data, nil
}

type getOnTopFareFn func(ctx context.Context, ids []string) ([]model.OnTopFare, error)

func (svc *BulkOntopCSVService) FetchOnTopFareFromDB(ctx context.Context, getOnTopFareFn getOnTopFareFn, ids []string) (OnTopFareModel, error) {
	ontops, err := getOnTopFareFn(ctx, ids)
	if err != nil {
		err = fmt.Errorf("get on top fare from db error: %v", err)
		err := apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
		return OnTopFareModel{}, err
	}

	idMap := make(map[string]model.OnTopFare, len(ontops))
	for _, v := range ontops {
		idMap[v.ID] = v
	}

	return OnTopFareModel{
		IdMap: idMap,
	}, nil
}

func (svc *BulkOntopCSVService) CheckExist(ctx context.Context, ids []string, m map[string]model.OnTopFare) error {
	merr := NewCSVMultipleError()
	for idx, id := range ids {
		rowIndex := idx + 1
		_, exist := m[id]
		if !exist {
			merr.AddError(apiError.NewCSVRowError(
				rowIndex,
				id,
				fmt.Sprintf("[id: %v] record not found in database", id),
			))
			continue
		}
		merr.AddSuccess(SuccessRow{
			Row: rowIndex,
			Id:  id,
		})
	}
	if merr.HasError() {
		return merr
	}

	return nil
}

func (svc *BulkOntopCSVService) ValidateBulkImportCSVData(ctx context.Context, data BulkImportCSVData) error {
	size := data.SchemeSize

	if size == 0 {
		return ErrNoDataRows()
	}

	maxScheme := svc.cfg.BulkUploadCSVMaxSchemeSize
	if size > svc.cfg.BulkUploadCSVMaxSchemeSize {
		return ErrExceededMaxScheme(size, maxScheme)
	}

	err := svc.bulkImportCSVDataRowsValidator.validate(ctx, data)
	if err != nil {
		var merr *CSVMultipleError
		if errors.As(err, &merr) {
			return ErrRowValidationError(merr)
		}
		return apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err)
	}

	return nil
}

func (svc *BulkOntopCSVService) ValidateBulkDeleteCSVData(ctx context.Context, data BulkDeleteCSVData) error {
	size := len(data.IDs)
	if size == 0 {
		return ErrNoDataRows()
	}

	maxScheme := svc.cfg.BulkUploadCSVMaxSchemeSize
	if size > svc.cfg.BulkUploadCSVMaxSchemeSize {
		return ErrExceededMaxScheme(size, maxScheme)
	}

	return nil
}
