package bulkcsv

import (
	"bytes"
	"context"
	"embed"
	"errors"
	"mime/multipart"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkontopcsv/shared"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

var (
	_ multipart.File      = (*testDummyMultiPartFile)(nil)
	_ MultipartFileReader = (*testMultiPartFileReader)(nil)

	invalidCSV          string
	validFlatRateFile   string
	validBasketSizeFile string
	validDistanceFile   string

	//go:embed testdata/*
	testdata embed.FS
)

func init() {
	b, _ := testdata.ReadFile("testdata/bulk_flat_rate_invalid.csv")
	invalidCSV = string(b)

	b, _ = testdata.ReadFile("testdata/bulk_flat_rate_valid.csv")
	validFlatRateFile = string(b)

	b, _ = testdata.ReadFile("testdata/bulk_basket_size_valid.csv")
	validBasketSizeFile = string(b)

	b, _ = testdata.ReadFile("testdata/bulk_distance_valid.csv")
	validDistanceFile = string(b)
}

type testMultiPartFileReader struct {
	f                 *testDummyMultiPartFile
	needOpenFileError bool
}

func (t *testMultiPartFileReader) Open() (multipart.File, error) {
	if t.needOpenFileError {
		return nil, errors.New("something wrong when open file")
	}
	return t.f, nil
}

type testDummyMultiPartFile struct {
	reader *bytes.Reader
}

func newTestDummyMultiPartFile(s string) *testDummyMultiPartFile {
	r := bytes.NewReader([]byte(s))
	return &testDummyMultiPartFile{
		reader: r,
	}
}

// Read implements multipart.File
func (t *testDummyMultiPartFile) Read(p []byte) (n int, err error) {
	return t.reader.Read(p)
}

// ReadAt implements multipart.File
func (t *testDummyMultiPartFile) ReadAt(p []byte, off int64) (n int, err error) {
	return t.reader.ReadAt(p, off)
}

// Seek implements multipart.File
func (t *testDummyMultiPartFile) Seek(offset int64, whence int) (int64, error) {
	return t.reader.Seek(offset, whence)
}

// Close implements multipart.File
func (t *testDummyMultiPartFile) Close() error {
	return nil
}

func newDummyFile(content string, needOpenFileError bool) *testMultiPartFileReader {
	dummy := &testMultiPartFileReader{f: newTestDummyMultiPartFile(content), needOpenFileError: needOpenFileError}
	return dummy
}

func assertFlatRateCSVSuccess(t *testing.T, data BulkImportCSVData) {
	require.Len(t, data.RawRows, 1)
	require.Equal(t, 1, data.SchemeSize)
	require.Contains(t, data.UniqueIDMap, "1")
	require.Equal(t, []string{"1"}, data.UniqueIDs)
	require.Equal(t, "1", data.RawRows[0].Id)
	require.Equal(t, "Test Bulk Upload", data.RawRows[0].Name)
	require.Equal(t, "BKK", data.RawRows[0].Region)
}

func assertBasketSizeCSVSuccess(t *testing.T, data BulkImportCSVData) {
	require.Len(t, data.RawRows, 1)
	require.Equal(t, 1, data.SchemeSize)
	require.Contains(t, data.UniqueIDMap, "1")
	require.Equal(t, []string{"1"}, data.UniqueIDs)
	require.Equal(t, "1", data.RawRows[0].Id)
	require.Equal(t, "Test Bulk Basket Upload", data.RawRows[0].Name)
	require.Equal(t, "BKK", data.RawRows[0].Region)
}

func assertDistanceCSVSuccess(t *testing.T, data BulkImportCSVData) {
	require.Len(t, data.RawRows, 1)
	require.Equal(t, 1, data.SchemeSize)
	require.Contains(t, data.UniqueIDMap, "1")
	require.Equal(t, []string{"1"}, data.UniqueIDs)
	require.Equal(t, "1", data.RawRows[0].Id)
	require.Equal(t, "Test Bulk Distance Upload", data.RawRows[0].Name)
	require.Equal(t, "BKK", data.RawRows[0].Region)
}

var _ Adapter = (*stubAdapterTest)(nil)

type stubAdapterTest struct {
}

func newStubAdapterTest() *stubAdapterTest {
	return &stubAdapterTest{}
}

// GetIncentiveSources implements Adapter
func (dep *stubAdapterTest) GetIncentiveSources(ctx context.Context) ([]string, error) {
	return []string{"A", "B", "C"}, nil
}

// GetZones implements Adapter
func (dep *stubAdapterTest) GetZones(ctx context.Context, region string) ([]string, error) {
	return []string{"Zone A", "Zone B", "Zone C"}, nil
}

func newDefaultBulkOnTopCSVServiceForTest() *BulkOntopCSVService {
	cfg := Config{
		BulkUploadCSVMaxSchemeSize: 1,
	}
	return NewBulkOnTopCSVService(cfg, newStubAdapterTest())
}

func newBulkOnTopCSVServiceForTest(cfg Config, adapter Adapter) *BulkOntopCSVService {
	return NewBulkOnTopCSVService(cfg, adapter)
}

func TestBulkOntopCSVService_ParseCSVFile(t *testing.T) {

	var parseCSVFileTests = []struct {
		name                    string
		content                 string
		maxScheme               int
		schemeType              shared.SchemeType
		needOpenFileError       bool
		isErr                   bool
		assertError             func(t *testing.T, err error)
		assertBulkImportCSVData func(t *testing.T, data BulkImportCSVData)
	}{
		{
			name:              "open file error should error",
			content:           ``,
			needOpenFileError: true,
			isErr:             true,
			assertError: func(t *testing.T, err error) {
				assert.Error(t, err, "open file error: something wrong when open file")
			}},
		{
			name:    "empty file should error",
			content: ``,
			isErr:   true,
			assertError: func(t *testing.T, err error) {
				var apiErr *api.Error
				require.ErrorAs(t, err, &apiErr)
				assert.Equal(t, "EMPTY_FILE_OR_HEADER", apiErr.Code)
				assert.Equal(t, "file empty", apiErr.Message)
			}},
		{
			name: "invalid csv file format should error",
			content: `a,b,c
	1,2`,
			isErr: true,
			assertError: func(t *testing.T, err error) {
				assert.Error(t, err, "wrong number of fields")
			}},
		{
			name: "unknown scheme type should error",
			content: `id,name,region
		1,ABC,BKK`,
			maxScheme:  1,
			schemeType: shared.UNKNOWN,
			isErr:      true,
			assertError: func(t *testing.T, err error) {
				assert.Error(t, err, "parse csv file error: unable to add condition on row 1: scheme not supported when add condition")
			},
			assertBulkImportCSVData: assertFlatRateCSVSuccess,
		},
		{
			name:                    "correct flat rate file should no error",
			content:                 validFlatRateFile,
			maxScheme:               1,
			schemeType:              shared.FLAT_RATE,
			isErr:                   false,
			assertBulkImportCSVData: assertFlatRateCSVSuccess,
		},
		{
			name:                    "correct basket size file should no error",
			content:                 validBasketSizeFile,
			maxScheme:               1,
			schemeType:              shared.BASKET_SIZE,
			isErr:                   false,
			assertBulkImportCSVData: assertBasketSizeCSVSuccess,
		},
		{
			name:                    "correct distance file should no error",
			content:                 validDistanceFile,
			maxScheme:               1,
			schemeType:              shared.DISTANCE,
			isErr:                   false,
			assertBulkImportCSVData: assertDistanceCSVSuccess,
		},
	}

	for _, tt := range parseCSVFileTests {
		t.Run(tt.name, func(t *testing.T) {
			svc := NewBulkOnTopCSVService(Config{
				BulkUploadCSVMaxSchemeSize: tt.maxScheme,
			}, newStubAdapterTest())
			data, err := svc.ParseCSVFile(context.Background(), newDummyFile(tt.content, tt.needOpenFileError), tt.schemeType)
			if !tt.isErr {
				require.NoError(t, err)
				tt.assertBulkImportCSVData(t, data)
			} else {
				require.Error(t, err)
				tt.assertError(t, err)
			}
		})
	}
}

var fetchOnTopFareTests = []struct {
	name                 string
	getOnTopFareFn       getOnTopFareFn
	ids                  []string
	isErr                bool
	assertError          func(t *testing.T, err error)
	assertOnTopFareModel func(t *testing.T, m OnTopFareModel)
}{
	{
		name: "success",
		getOnTopFareFn: func(ctx context.Context, ids []string) ([]model.OnTopFare, error) {
			return []model.OnTopFare{{
				ID:   "ID_A",
				Name: "A",
			}}, nil
		},
		ids:   []string{"ID_A"},
		isErr: false,
		assertOnTopFareModel: func(t *testing.T, m OnTopFareModel) {
			a := m.IdMap["ID_A"]
			assert.Equal(t, "ID_A", a.ID)
			assert.Equal(t, "A", a.Name)
		},
	},
	{
		name: "db error",
		getOnTopFareFn: func(ctx context.Context, ids []string) ([]model.OnTopFare, error) {
			return nil, errors.New("db error")
		},
		ids:   []string{"ID_A"},
		isErr: true,
		assertError: func(t *testing.T, err error) {
			assert.Error(t, err, "get on top fare from db error: db error")
		},
	},
}

func TestBulkOntopCSVService_FetchOnTopFareFromDB(t *testing.T) {
	for _, tt := range fetchOnTopFareTests {
		t.Run(tt.name, func(t *testing.T) {
			svc := newDefaultBulkOnTopCSVServiceForTest()
			data, err := svc.FetchOnTopFareFromDB(context.Background(), tt.getOnTopFareFn, tt.ids)
			if !tt.isErr {
				require.NoError(t, err)
				tt.assertOnTopFareModel(t, data)
			} else {
				require.Error(t, err)
				tt.assertError(t, err)
			}
		})
	}
}

var checkExistsTests = []struct {
	name        string
	groupsRow   []string
	idMap       map[string]model.OnTopFare
	isErr       bool
	assertError func(t *testing.T, err error)
}{
	{
		name:      "every records found should no error",
		groupsRow: []string{"A", "B"},
		idMap: map[string]model.OnTopFare{
			"A": {},
			"B": {},
		},
		isErr: false,
	},
	{
		name:      "if some records not found should error",
		groupsRow: []string{"A", "B", "C", "D"},
		idMap: map[string]model.OnTopFare{
			"A": {},
			"C": {},
		},
		isErr: true,
		assertError: func(t *testing.T, err error) {
			var merr *CSVMultipleError
			require.ErrorAs(t, err, &merr)
			assert.Equal(t, 2, len(merr.MultipleError))

			rowErr := merr.MultipleError[0]
			assert.EqualError(t, rowErr, "row 2: [id: B] record not found in database")
			rowErr = merr.MultipleError[1]
			assert.EqualError(t, rowErr, "row 4: [id: D] record not found in database")
		},
	},
}

func TestBulkOntopCSVService_CheckExist(t *testing.T) {
	for _, tt := range checkExistsTests {
		t.Run(tt.name, func(t *testing.T) {
			svc := newDefaultBulkOnTopCSVServiceForTest()
			err := svc.CheckExist(context.Background(), tt.groupsRow, tt.idMap)
			if !tt.isErr {
				require.NoError(t, err)
			} else {
				require.Error(t, err)
				tt.assertError(t, err)
			}
		})
	}
}

func TestBulkOntopCSVService_ValidateBulkImportCSVData(t *testing.T) {
	type fields struct {
		adapter Adapter
		cfg     Config
	}
	type args struct {
		data BulkImportCSVData
	}
	tests := []struct {
		name        string
		fields      fields
		args        args
		wantErr     bool
		assertError func(t *testing.T, err error)
	}{
		{
			name: "fill all required fields should no error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{
				data: BulkImportCSVData{
					SchemeSize: 1,
					RawRows: []BulkImportCSVDataRow{
						{
							FixedColumns: FixedColumns{
								Id:     "Id",
								Name:   "Name",
								Region: "Region",
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "empty",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{
				data: BulkImportCSVData{
					SchemeSize: 0,
				},
			},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "NO_DATA_ROWS: please add data rows")
			},
		},
		{
			name: "exceed scheme should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 0,
				},
			},
			args: args{
				data: BulkImportCSVData{
					SchemeSize: 1,
				},
			},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "EXCEEDED_MAX_SCHEME: scheme exceeded the limit: actual=1, limit=0")
			},
		},
		{
			name: "validate fail should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: id is required\nrow 1: name is required\nrow 1: region is required\n")
			},
		},
		{
			name: "if incentive sources supplied but not valid value should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:               "Id",
						Name:             "Name",
						Region:           "Region",
						IncentiveSources: IncentiveSourcesColumn{IncentiveSources: []string{"D", "E", "F"}},
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: incentive_sources value D is not valid,incentive_sources value E is not valid,incentive_sources value F is not valid\n")
			},
		},
		{
			name: "if effective area type is zone and zone supplied but not valid value should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                "Id",
						Name:              "Name",
						Region:            "Region",
						EffectiveAreaType: EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_ZONE},
						EffectiveAreaZone: "Zone E",
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: effective_area.zone value Zone E is not valid\n")
			},
		},
		{
			name: "if effective area type is zone and zone empty should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                "Id",
						Name:              "Name",
						Region:            "Region",
						EffectiveAreaType: EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_ZONE},
						EffectiveAreaZone: "",
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: effective_area.zone is required when effective_area.type is ZONE\n")
			},
		},
		{
			name: "if effective area type is RESTAURANTS_CSV and restaurants csv empty should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                          "Id",
						Name:                        "Name",
						Region:                      "Region",
						EffectiveAreaType:           EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_RESTAURANTS_CSV},
						EffectiveAreaRestaurantsCSV: EffectiveAreaRestaurantsCSVColumn{EffectiveAreaRestaurantCSV: []EffectiveAreaRestaurantCSV{}},
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: effective_area.restaurants_csv is required when effective_area.type is RESTAURANTS_CSV\n")
			},
		},
		{
			name: "if effective area type is COORDINATES and coordinates empty should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                       "Id",
						Name:                     "Name",
						Region:                   "Region",
						EffectiveAreaType:        EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_COORDINATES},
						EffectiveAreaCoordinates: CoordinatesColumn{Coordinates: model.OntopCoordinates{}},
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: effective_area.coordinates is required when effective_area.type is COORDINATES\n")
			},
		},
		{
			name: "if effective area type is RESTAURANTS and restaurants empty should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                       "Id",
						Name:                     "Name",
						Region:                   "Region",
						EffectiveAreaType:        EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_RESTAURANTS},
						EffectiveAreaRestaurants: EffectiveAreaRestaurantsColumn{Restaurants: []string{}},
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: effective_area.restaurants is required when effective_area.type is RESTAURANTS\n")
			},
		},
		{
			name: "should cache zone if same region",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 2,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                "Id 1",
						Name:              "Name 1",
						Region:            "Region 1",
						EffectiveAreaType: EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_ZONE},
						EffectiveAreaZone: "Zone E",
					}},
					{FixedColumns: FixedColumns{
						Id:                "Id 2",
						Name:              "Name 2",
						Region:            "Region 1",
						EffectiveAreaType: EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_ZONE},
						EffectiveAreaZone: "Zone F",
					}},
				},
				SchemeSize: 2,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: effective_area.zone value Zone E is not valid\nrow 2: effective_area.zone value Zone F is not valid\n")
			},
		},
		{
			name: "should cache incentive sources",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 2,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:               "Id 1",
						Name:             "Name 1",
						Region:           "Region 1",
						IncentiveSources: IncentiveSourcesColumn{IncentiveSources: []string{"D", "E", "F"}},
					}},
					{FixedColumns: FixedColumns{
						Id:               "Id 2",
						Name:             "Name 2",
						Region:           "Region 2",
						IncentiveSources: IncentiveSourcesColumn{IncentiveSources: []string{"D", "E", "F"}},
					}},
				},
				SchemeSize: 2,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: incentive_sources value D is not valid,incentive_sources value E is not valid,incentive_sources value F is not valid\nrow 2: incentive_sources value D is not valid,incentive_sources value E is not valid,incentive_sources value F is not valid\n")
			},
		},
		{
			name: "flag whole region with non-empty coordinates should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                "Id",
						Name:              "Name",
						Region:            "Region",
						EffectiveAreaType: EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_REGION},
						EffectiveAreaCoordinates: CoordinatesColumn{Coordinates: model.OntopCoordinates{
							{
								{
									{100.12939453125, 16.193574826697834},
									{99.86572265625, 15.347761924346937},
									{101.6455078125, 14.796127603627053},
									{100.12939453125, 16.193574826697834},
								},
							},
						},
						},
						IsWholeRegion: true,
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: effective_area.coordinates must be empty\n")
			},
		},
		{
			name: "flag whole region with invalid effective area type should error",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                       "Id",
						Name:                     "Name",
						Region:                   "Region",
						EffectiveAreaType:        EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_ZONE},
						EffectiveAreaCoordinates: CoordinatesColumn{},
						IsWholeRegion:            true,
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: true,
			assertError: func(t *testing.T, err error) {
				require.EqualError(t, err, "row 1: to enable whole region, area type must be REGION\nrow 1: effective_area.zone is required when effective_area.type is ZONE\n")
			},
		},
		{
			name: "flag whole region success",
			fields: fields{
				adapter: newStubAdapterTest(),
				cfg: Config{
					BulkUploadCSVMaxSchemeSize: 1,
				},
			},
			args: args{data: BulkImportCSVData{
				RawRows: []BulkImportCSVDataRow{
					{FixedColumns: FixedColumns{
						Id:                       "Id",
						Name:                     "Name",
						Region:                   "Region",
						EffectiveAreaType:        EffectiveAreaTypeColumn{EffectiveAreaType: EFFECTIVE_AREA_REGION},
						EffectiveAreaCoordinates: CoordinatesColumn{Coordinates: model.OntopCoordinates{}},
						IsWholeRegion:            true,
					}},
				},
				SchemeSize: 1,
			}},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			svc := newBulkOnTopCSVServiceForTest(tt.fields.cfg, tt.fields.adapter)
			if err := svc.ValidateBulkImportCSVData(context.Background(), tt.args.data); (err != nil) != tt.wantErr {
				t.Errorf("BulkOntopCSVService.Validate() error = %v, wantErr %v", err, tt.wantErr)
			} else {
				if err != nil {
					tt.assertError(t, err)
				}
			}
		})
	}
}
