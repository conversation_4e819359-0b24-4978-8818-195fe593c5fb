package bulkcsv

import (
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type OnTopFareStatusColumn struct {
	Status model.OnTopFareStatus
}

func (f OnTopFareStatusColumn) MarshalCSV() ([]byte, error) {
	return []byte(f.Status), nil
}

func (f *OnTopFareStatusColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}
	v, exist := model.OnTopFareStatusLookup[s]
	if !exist {
		return fmt.Errorf("invalid value")
	}
	*f = OnTopFareStatusColumn{
		Status: v,
	}
	return nil
}

type IncentiveSourcesColumn struct {
	IncentiveSources []string
}

func (f IncentiveSourcesColumn) MarshalCSV() ([]byte, error) {
	return []byte(strings.Join(f.IncentiveSources, ",")), nil
}

func (f *IncentiveSourcesColumn) UnmarshalCSV(data []byte) error {
	if len(string(data)) == 0 {
		return nil
	}

	ss := strings.Split(string(data), ",")

	*f = IncentiveSourcesColumn{IncentiveSources: ss}
	return nil
}

type BangkokDateTimeColumn struct {
	Time time.Time
}

const format = time.DateTime

func (t BangkokDateTimeColumn) MarshalCSV() ([]byte, error) {
	if t.Time.IsZero() {
		return []byte{}, nil
	}
	var b [len(format)]byte
	return t.Time.In(timeutil.BangkokLocation()).AppendFormat(b[:0], format), nil
}

func (t *BangkokDateTimeColumn) UnmarshalCSV(data []byte) error {
	tt, err := time.ParseInLocation(format, string(data), timeutil.BangkokLocation())
	if err != nil {
		return err
	}
	*t = BangkokDateTimeColumn{Time: tt}
	return nil
}

type ServiceTypesColumn struct {
	Services []model.Service
}

func (f ServiceTypesColumn) MarshalCSV() ([]byte, error) {
	ss := []string{}
	for _, v := range f.Services {
		ss = append(ss, strings.ToUpper(v.String()))
	}
	return []byte(strings.Join(ss, ",")), nil
}

func (f *ServiceTypesColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}

	ss := strings.Split(s, ",")

	serviceTypes := []model.Service{}
	for _, s := range ss {
		v, exists := model.ServicesTypeLookup[s]
		if !exists {
			return fmt.Errorf("value %s is invalid", s)
		}
		serviceTypes = append(serviceTypes, v)
	}

	*f = ServiceTypesColumn{Services: serviceTypes}
	return nil
}

type DaysColumn struct {
	Days []model.Days
}

func (f DaysColumn) MarshalCSV() ([]byte, error) {
	ss := []string{}
	for _, v := range f.Days {
		ss = append(ss, string(v))
	}
	return []byte(strings.Join(ss, ",")), nil
}

func (f *DaysColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}

	ss := strings.Split(s, ",")
	days := []model.Days{}
	for _, s := range ss {
		v, exists := model.DaysLookup[s]
		if !exists {
			return fmt.Errorf("value %s is invalid", s)
		}
		days = append(days, v)
	}

	*f = DaysColumn{Days: days}
	return nil
}

type MoneyColumn struct {
	Money types.Money
}

func (f MoneyColumn) MarshalCSV() ([]byte, error) {
	return []byte(f.Money.String()), nil
}

func (f *MoneyColumn) UnmarshalCSV(data []byte) error {
	v, err := strconv.ParseFloat(string(data), 64)
	if err != nil {
		return fmt.Errorf("parse float err: %v", err)
	}
	*f = MoneyColumn{Money: types.NewMoney(v)}
	return nil
}

type IntColumn int

func (c IntColumn) MarshalCSV() ([]byte, error) {
	return []byte(strconv.Itoa(int(c))), nil
}

func (c *IntColumn) UnmarshalCSV(data []byte) error {
	v, err := strconv.ParseInt(string(data), 10, 64)
	if err != nil {
		return fmt.Errorf("parse int err: %v", err)
	}
	*c = IntColumn(v)
	return nil
}

type EffectiveAreaRestaurantsCSVColumn struct {
	EffectiveAreaRestaurantCSV []EffectiveAreaRestaurantCSV
}

func (f EffectiveAreaRestaurantsCSVColumn) MarshalCSV() ([]byte, error) {
	s := []string{}
	for _, v := range f.EffectiveAreaRestaurantCSV {
		line := fmt.Sprintf("%v,%v,%v,%v,%v", v.RestaurantId, v.RestaurantName, v.Description, v.Lat, v.Lng)
		s = append(s, line)
	}

	return []byte(strings.Join(s, "\n")), nil
}

func (f *EffectiveAreaRestaurantsCSVColumn) UnmarshalCSV(data []byte) error {
	ss := strings.Split(string(data), "\n")
	ee := []EffectiveAreaRestaurantCSV{}
	for _, v := range ss {
		vv := strings.Split(v, ",")
		if len(vv) != 5 {
			return fmt.Errorf("invalid format expect %v comma separators but got %v comma separators", 5, len(vv))
		}
		lat, err := strconv.ParseFloat(vv[3], 64)
		if err != nil {
			return fmt.Errorf("lat is invalid format")
		}
		lng, err := strconv.ParseFloat(vv[4], 64)
		if err != nil {
			return fmt.Errorf("lng is invalid format")
		}
		e := EffectiveAreaRestaurantCSV{
			RestaurantId:   vv[0],
			RestaurantName: vv[1],
			Description:    vv[2],
			Lat:            lat,
			Lng:            lng,
		}
		ee = append(ee, e)
	}

	*f = EffectiveAreaRestaurantsCSVColumn{EffectiveAreaRestaurantCSV: ee}
	return nil
}

type EffectiveAreaRestaurantsColumn struct {
	Restaurants []string
}

func (f EffectiveAreaRestaurantsColumn) MarshalCSV() ([]byte, error) {
	return []byte(strings.Join(f.Restaurants, ",")), nil
}

func (f *EffectiveAreaRestaurantsColumn) UnmarshalCSV(data []byte) error {
	if len(data) == 0 {
		return nil
	}
	*f = EffectiveAreaRestaurantsColumn{Restaurants: strings.Split(string(data), ",")}
	return nil
}

type DriverIDsColumn struct {
	DriverIDs []string
}

func (f DriverIDsColumn) MarshalCSV() ([]byte, error) {
	return []byte(strings.Join(f.DriverIDs, ",")), nil
}

func (f *DriverIDsColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("driverIDs column cannot be empty")
	}
	*f = DriverIDsColumn{
		DriverIDs: strings.Split(string(data), ","),
	}
	return nil
}

type TimeRangeColumn struct {
	TimeRanges []model.StartEndTime
}

const timeRangeFormat = "15:04:05"

func (f TimeRangeColumn) MarshalCSV() ([]byte, error) {
	s := []string{}
	for _, v := range f.TimeRanges {
		line := fmt.Sprintf("%v|%v", v.Begin, v.End)
		s = append(s, line)
	}
	return []byte(strings.Join(s, ",")), nil
}

func (f *TimeRangeColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}

	ss := strings.Split(s, ",")
	trs := []model.StartEndTime{}
	for _, v := range ss {
		vv := strings.Split(v, "|")
		if len(vv) != 2 {
			return fmt.Errorf("invalid format expect %v pipe separators but got %v pipe separators", 2, len(vv))
		}
		start, err := time.ParseInLocation(timeRangeFormat, vv[0], timeutil.BangkokLocation())
		if err != nil {
			return fmt.Errorf("start time is invalid format: %v", err)
		}
		end, err := time.ParseInLocation(timeRangeFormat, vv[1], timeutil.BangkokLocation())
		if err != nil {
			return fmt.Errorf("end time is invalid format: %v", err)
		}
		trs = append(trs, model.StartEndTime{
			Begin: start.Format(timeRangeFormat),
			End:   end.Format(timeRangeFormat),
		})
	}
	*f = TimeRangeColumn{
		TimeRanges: trs,
	}
	return nil
}

type FlatRateTypeColumn struct {
	Type model.FlatRateType
}

func (f FlatRateTypeColumn) MarshalCSV() ([]byte, error) {
	return []byte(f.Type), nil
}

func (f *FlatRateTypeColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}

	v, exist := model.FlatRateTypeLookup[s]
	if !exist {
		return fmt.Errorf("invalid value")
	}
	*f = FlatRateTypeColumn{
		Type: v,
	}
	return nil
}

type AllowPaymentTypeColumn struct {
	AllowPaymentType AllowPaymentType
}

func (f AllowPaymentTypeColumn) MarshalCSV() ([]byte, error) {
	ss := []string{}
	if f.AllowPaymentType.Cash {
		ss = append(ss, "CASH")
	}
	if f.AllowPaymentType.CashCollection {
		ss = append(ss, "CASH_COLLECTION")
	}
	if f.AllowPaymentType.CashAdvanceCoupon {
		ss = append(ss, "CASH_ADVANCE_COUPON")
	}
	if f.AllowPaymentType.CashAdvanceEPayment {
		ss = append(ss, "CASH_ADVANCE_EPAYMENT")
	}
	if f.AllowPaymentType.CashAdvanceQRPayment {
		ss = append(ss, "CASH_ADVANCE_QR_PAYMENT")
	}
	if f.AllowPaymentType.RLP {
		ss = append(ss, "RLP")
	}
	if f.AllowPaymentType.CreditCard {
		ss = append(ss, "CREDIT_CARD")
	}
	if f.AllowPaymentType.LinemanWallet {
		ss = append(ss, "LINEMAN_WALLET")
	}
	if f.AllowPaymentType.QRPromptPay {
		ss = append(ss, "QR_PROMPTPAY")
	}
	if f.AllowPaymentType.WechatPay {
		ss = append(ss, "WECHAT_PAY")
	}
	if f.AllowPaymentType.TRUEMoney {
		ss = append(ss, "TRUE_MONEY")
	}
	return []byte(strings.Join(ss, ",")), nil
}

func (f *AllowPaymentTypeColumn) UnmarshalCSV(data []byte) error {
	ss := strings.Split(string(data), ",")

	for _, v := range ss {
		switch v {
		case "CASH":
			f.AllowPaymentType.Cash = true
		case "CASH_COLLECTION":
			f.AllowPaymentType.CashCollection = true
		case "CASH_ADVANCE_COUPON":
			f.AllowPaymentType.CashAdvanceCoupon = true
		case "CASH_ADVANCE_EPAYMENT":
			f.AllowPaymentType.CashAdvanceEPayment = true
		case "EPAYMENT":
			f.AllowPaymentType.RLP = true
			f.AllowPaymentType.CreditCard = true
			f.AllowPaymentType.LinemanWallet = true
			f.AllowPaymentType.WechatPay = true
		case "RLP":
			f.AllowPaymentType.RLP = true
		case "CREDIT_CARD":
			f.AllowPaymentType.CreditCard = true
		case "LINEMAN_WALLET":
			f.AllowPaymentType.LinemanWallet = true
		case "QR_PROMPTPAY":
			f.AllowPaymentType.QRPromptPay = true
		case "CASH_ADVANCE_QR_PAYMENT":
			f.AllowPaymentType.CashAdvanceQRPayment = true
		case "WECHAT_PAY":
			f.AllowPaymentType.WechatPay = true
		case string(model.PaymentMethodTRUEMoney):
			f.AllowPaymentType.TRUEMoney = true
		}
	}

	return nil
}

type BasketPriceSchemesColumn struct {
	BasketPriceSchemes []BasketPriceScheme
}

func (f BasketPriceSchemesColumn) MarshalCSV() ([]byte, error) {
	s := []string{}
	for _, v := range f.BasketPriceSchemes {
		line := fmt.Sprintf("%v|%v|%v|%v", v.From, v.To, v.Amount, v.CoinAmount)
		s = append(s, line)
	}
	return []byte(strings.Join(s, ",")), nil
}

func (f *BasketPriceSchemesColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}
	ss := strings.Split(s, ",")
	trs := []BasketPriceScheme{}
	for _, v := range ss {
		vv := strings.Split(v, "|")
		if len(vv) != 4 {
			return fmt.Errorf("invalid format expect %v pipe separators but got %v pipe separators", 3, len(vv))
		}
		from, err := strconv.Atoi(vv[0])
		if err != nil {
			return fmt.Errorf("from is invalid format")
		}
		to, err := strconv.Atoi(vv[1])
		if err != nil {
			return fmt.Errorf("to is invalid format")
		}
		amount, err := strconv.ParseFloat(vv[2], 64)
		if err != nil {
			return fmt.Errorf("amount is invalid format")
		}
		coinAmount, err := strconv.ParseInt(vv[3], 10, 64)
		if err != nil {
			return fmt.Errorf("coin amount is invalid format")
		}

		trs = append(trs, BasketPriceScheme{
			From:       from,
			To:         to,
			Amount:     types.NewMoney(amount),
			CoinAmount: int(coinAmount),
		})
	}
	*f = BasketPriceSchemesColumn{
		BasketPriceSchemes: trs,
	}
	return nil
}

type DistancePriceSchemesColumn struct {
	DistancePriceSchemes []DistancePriceScheme
}

func (f DistancePriceSchemesColumn) MarshalCSV() ([]byte, error) {
	s := []string{}
	for _, v := range f.DistancePriceSchemes {
		line := fmt.Sprintf("%v|%v|%v|%v", v.FromInMeter, v.ToInMeter, v.Amount, v.CoinAmount)
		s = append(s, line)
	}
	return []byte(strings.Join(s, ",")), nil
}

func (f *DistancePriceSchemesColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}
	ss := strings.Split(s, ",")
	trs := []DistancePriceScheme{}
	for _, v := range ss {
		vv := strings.Split(v, "|")
		if len(vv) != 4 {
			return fmt.Errorf("invalid format expect %v pipe separators but got %v pipe separators", 3, len(vv))
		}
		fromInMeter, err := strconv.Atoi(vv[0])
		if err != nil {
			return fmt.Errorf("from is invalid format")
		}
		toInMeter, err := strconv.Atoi(vv[1])
		if err != nil {
			return fmt.Errorf("to is invalid format")
		}
		amount, err := strconv.ParseFloat(vv[2], 64)
		if err != nil {
			return fmt.Errorf("amount is invalid format")
		}
		coinAmount, err := strconv.ParseInt(vv[3], 10, 64)
		if err != nil {
			return fmt.Errorf("coin amount is invalid format")
		}
		trs = append(trs, DistancePriceScheme{
			FromInMeter: fromInMeter,
			ToInMeter:   toInMeter,
			Amount:      types.NewMoney(amount),
			CoinAmount:  int(coinAmount),
		})
	}
	*f = DistancePriceSchemesColumn{
		DistancePriceSchemes: trs,
	}
	return nil
}

type PickupDistancePriceSchemesColumn struct {
	PickupDistancePriceSchemes []PickupDistancePriceScheme
}

func (f PickupDistancePriceSchemesColumn) MarshalCSV() ([]byte, error) {
	var s []string
	for _, v := range f.PickupDistancePriceSchemes {
		line := fmt.Sprintf("%v|%v|%v", v.FromInMeter, v.Amount, v.CoinAmount)
		s = append(s, line)
	}
	return []byte(strings.Join(s, ",")), nil
}

func (f *PickupDistancePriceSchemesColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}
	ss := strings.Split(s, ",")
	var trs []PickupDistancePriceScheme
	for _, v := range ss {
		vv := strings.Split(v, "|")
		if len(vv) != 3 {
			return fmt.Errorf("invalid format expect %v pipe separators but got %v pipe separators", 2, len(vv))
		}
		fromInMeter, err := strconv.Atoi(vv[0])
		if err != nil {
			return fmt.Errorf("from is invalid format")
		}
		amount, err := strconv.ParseFloat(vv[1], 64)
		if err != nil {
			return fmt.Errorf("amount is invalid format")
		}
		coinAmount, err := strconv.ParseInt(vv[2], 10, 64)
		if err != nil {
			return fmt.Errorf("coin amount is invalid format")
		}
		trs = append(trs, PickupDistancePriceScheme{
			FromInMeter: fromInMeter,
			Amount:      types.NewMoney(amount),
			CoinAmount:  int(coinAmount),
		})
	}
	*f = PickupDistancePriceSchemesColumn{
		PickupDistancePriceSchemes: trs,
	}
	return nil
}

type InstallmentPriceSchemesColumn struct {
	InstallmentPriceSchemes []InstallmentPriceScheme
}

func (f InstallmentPriceSchemesColumn) MarshalCSV() ([]byte, error) {
	var s []string
	for _, v := range f.InstallmentPriceSchemes {
		line := fmt.Sprintf("%v|%v", v.Amount, v.MaxOrders)
		s = append(s, line)
	}
	return []byte(strings.Join(s, ",")), nil
}

func (f *InstallmentPriceSchemesColumn) UnmarshalCSV(data []byte) error {
	s := strings.TrimSpace(string(data))
	if len(s) == 0 {
		return errors.New("cannot be empty")
	}
	ss := strings.Split(s, ",")
	var trs []InstallmentPriceScheme
	for _, v := range ss {
		vv := strings.Split(v, "|")
		if len(vv) != 2 {
			return fmt.Errorf("invalid format expect %v pipe separators but got %v pipe separators", 2, len(vv))
		}
		amount, err := strconv.ParseFloat(vv[0], 64)
		if err != nil {
			return fmt.Errorf("amount is invalid format")
		}
		maxOrders, err := strconv.ParseInt(vv[1], 10, 64)
		if err != nil {
			return fmt.Errorf("maxOrders is invalid format")
		}
		trs = append(trs, InstallmentPriceScheme{
			Amount:    types.NewMoney(amount),
			MaxOrders: int(maxOrders),
		})
	}
	*f = InstallmentPriceSchemesColumn{
		InstallmentPriceSchemes: trs,
	}
	return nil
}

type CoordinatesColumn struct {
	Coordinates model.OntopCoordinates
}

func (f CoordinatesColumn) MarshalCSV() ([]byte, error) {
	if len(f.Coordinates) == 0 {
		return nil, nil
	}
	return json.Marshal(f.Coordinates)
}

func (f *CoordinatesColumn) UnmarshalCSV(data []byte) error {
	if len(data) == 0 {
		return nil
	}

	coor := model.OntopCoordinates{}
	err := json.Unmarshal(data, &coor)
	if err != nil {
		return fmt.Errorf("invalid format: %v", err)
	}

	*f = CoordinatesColumn{
		Coordinates: coor,
	}
	return nil
}

type EffectiveArea int

const (
	EFFECTIVE_AREA_UNKNOWN EffectiveArea = iota
	EFFECTIVE_AREA_RESTAURANTS
	EFFECTIVE_AREA_RESTAURANTS_CSV
	EFFECTIVE_AREA_COORDINATES
	EFFECTIVE_AREA_ZONE
	EFFECTIVE_AREA_REGION
)

var effectiveAreaLookup = map[string]EffectiveArea{
	"RESTAURANTS":     EFFECTIVE_AREA_RESTAURANTS,
	"RESTAURANTS_CSV": EFFECTIVE_AREA_RESTAURANTS_CSV,
	"COORDINATES":     EFFECTIVE_AREA_COORDINATES,
	"ZONE":            EFFECTIVE_AREA_ZONE,
	"REGION":          EFFECTIVE_AREA_REGION,
	"UNKNOWN":         EFFECTIVE_AREA_UNKNOWN,
}

func (e EffectiveArea) String() string {
	switch e {
	case EFFECTIVE_AREA_RESTAURANTS:
		return "RESTAURANTS"
	case EFFECTIVE_AREA_RESTAURANTS_CSV:
		return "RESTAURANTS_CSV"
	case EFFECTIVE_AREA_ZONE:
		return "ZONE"
	case EFFECTIVE_AREA_COORDINATES:
		return "COORDINATES"
	case EFFECTIVE_AREA_REGION:
		return "REGION"
	default:
		return "UNKNOWN"
	}
}

type EffectiveAreaTypeColumn struct {
	EffectiveAreaType EffectiveArea
}

func (f EffectiveAreaTypeColumn) MarshalCSV() ([]byte, error) {
	e := f.EffectiveAreaType

	return []byte(e.String()), nil
}

func (f *EffectiveAreaTypeColumn) UnmarshalCSV(data []byte) error {
	v, exist := effectiveAreaLookup[string(data)]
	if !exist {
		return fmt.Errorf("invalid value")
	}

	*f = EffectiveAreaTypeColumn{EffectiveAreaType: v}

	return nil
}
