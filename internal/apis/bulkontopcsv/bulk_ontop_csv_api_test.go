package bulkontopcsv

import (
	"context"
	"embed"
	"encoding/csv"
	"fmt"
	"io"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/jszwec/csvutil"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkontopcsv/bulkcsv"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/polygon/mock_polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
)

//go:embed testdata/*
var testdata embed.FS

type bulkOnTopCSVAPIDep struct {
	OnTopFareRepo          *mock_repository.MockOnTopFareRepository
	AuditLogRepo           *mock_repository.MockAuditLogRepository
	IncentiveSourceService *mock_service.MockIncentiveSourceService
	ZoneService            *mock_service.MockZoneService
	PolygonService         *mock_polygon.MockPolygon
}

func newTestBulkCSVConfig() Config {
	cfg := ProvideBulkOnTopCSVConfig()
	cfg.BulkUploadCSVGzipEnabled = false // disable gzip on test
	return cfg
}

func newTestBulkOntopCSVAPI(ctrl *gomock.Controller, cfg Config) (*BulkOntopCSVAPI, *bulkOnTopCSVAPIDep) {
	deps := &bulkOnTopCSVAPIDep{
		OnTopFareRepo:          mock_repository.NewMockOnTopFareRepository(ctrl),
		AuditLogRepo:           mock_repository.NewMockAuditLogRepository(ctrl),
		IncentiveSourceService: mock_service.NewMockIncentiveSourceService(ctrl),
		ZoneService:            mock_service.NewMockZoneService(ctrl),
		PolygonService:         mock_polygon.NewMockPolygon(ctrl),
	}
	api := ProvideBulkOntopCSVAPI(cfg, deps.OnTopFareRepo, deps.AuditLogRepo, deps.IncentiveSourceService, deps.ZoneService, deps.PolygonService)
	return api, deps
}

func makeBulkUploadCSVReq(content string, schemeType string) (*gin.Context, *httptest.ResponseRecorder) {
	recorder := testutil.NewContextWithRecorder().
		Body().
		MultipartForm().
		File("file", "test.csv", content).
		String("type", schemeType).
		Build()

	return recorder.GinCtx(), recorder.ResponseRecorder
}

func makeBulkDeleteCSVReq(content string) (*gin.Context, *httptest.ResponseRecorder) {
	recorder := testutil.NewContextWithRecorder().
		Body().
		MultipartForm().
		File("file", "test.csv", content).
		Build()

	return recorder.GinCtx(), recorder.ResponseRecorder
}

func makeBulkExportCSVReq(schemeType string) (*gin.Context, *httptest.ResponseRecorder) {
	recorder := testutil.NewContextWithRecorder()
	recorder.SetQuery(fmt.Sprintf("type=%v", schemeType))
	return recorder.GinCtx(), recorder.ResponseRecorder
}

func readExportResults(reader io.Reader, v interface{}) error {
	csvReader := csv.NewReader(reader)
	decoder, _ := csvutil.NewDecoder(csvReader)
	err := decoder.Decode(v)
	if err != nil {
		return err
	}

	return nil
}

func assertEmptyFile(t *testing.T, recorder *httptest.ResponseRecorder) {
	var apiErr api.Error
	testutil.DecodeJSON(t, recorder.Body, &apiErr)
	require.Equal(t, "EMPTY_FILE_OR_HEADER", apiErr.Code)
	require.Equal(t, "file empty", apiErr.Message)
}

func assertInvalidCSVFormat(t *testing.T, recorder *httptest.ResponseRecorder) {
	var resp bulkResultResponse
	testutil.DecodeJSON(t, recorder.Body, &resp)
	require.Equal(t, "FAILED", resp.Status)
	require.Equal(t, 0, resp.TotalSuccess)
	require.Equal(t, 2, resp.TotalFailed)
}

func assertOnlyHeader(t *testing.T, recorder *httptest.ResponseRecorder) {
	var apiErr api.Error
	testutil.DecodeJSON(t, recorder.Body, &apiErr)
	require.Equal(t, "NO_DATA_ROWS", apiErr.Code)
	require.Equal(t, "please add data rows", apiErr.Message)
}

func assertCommonConditionExport(t *testing.T, c bulkcsv.CommonCondition) {
	assert.Equal(t, model.StatusActive, c.ConditionsStatus.Status)
	assert.Equal(t, []model.Service{model.ServiceFood}, c.ConditionsServiceTypes.Services)
	assert.Equal(t, []model.Days{"SUN"}, c.ConditionsDays.Days)
	assert.Equal(t, []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, c.ConditionsTimeRanges.TimeRanges)
}

func assertAllowPaymentTypeColumnExport(t *testing.T, expected model.PaymentTypeForBasket, actual bulkcsv.AllowPaymentTypeColumn) {
	assert.Equal(t, expected.Cash, actual.AllowPaymentType.Cash)
	assert.Equal(t, expected.CashCollection, actual.AllowPaymentType.CashCollection)
	assert.Equal(t, expected.CashAdvanceCoupon, actual.AllowPaymentType.CashAdvanceCoupon)
	assert.Equal(t, expected.CashAdvanceEPayment, actual.AllowPaymentType.CashAdvanceEPayment)
	assert.Equal(t, expected.CashAdvanceQRPayment, actual.AllowPaymentType.CashAdvanceQRPayment)
	assert.Equal(t, expected.RLP, actual.AllowPaymentType.RLP)
	assert.Equal(t, expected.CreditCard, actual.AllowPaymentType.CreditCard)
	assert.Equal(t, expected.LinemanWallet, actual.AllowPaymentType.LinemanWallet)
	assert.Equal(t, expected.QRPromptPay, actual.AllowPaymentType.QRPromptPay)
	assert.Equal(t, expected.WechatPay, actual.AllowPaymentType.WechatPay)
	assert.Equal(t, expected.TRUEMoney, actual.AllowPaymentType.TRUEMoney)
}

func TestBulkImportCSVCreate(t *testing.T) {
	t.Run("success flat rate", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/flatrate/bulk_flat_rate_valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(2).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(2).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("fail flat rate (coin base scheme flag enabled)", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/flatrate/bulk_flat_rate_coin_flag_invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 0, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)
		require.Contains(t, resp.Data, "coin base scheme and whole region flag have to be enabled together")
	})

	t.Run("success basket size", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/basket_size/bulk_basket_size_valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(1).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "BASKET_SIZE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("fail basket size (coin base scheme flag enabled)", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/basket_size/bulk_basket_size_coin_flag_invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "BASKET_SIZE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 0, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)
		require.Contains(t, resp.Data, "coin base scheme and whole region flag have to be enabled together")
	})

	t.Run("success distance size", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/distance/bulk_distance_valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(1).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "DISTANCE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("successfully imports installment on-top", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/installment/bulk_installment_on_top.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"EGS_ONTOP"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(3).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(3).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "INSTALLMENT")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 3, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("fail to imports installment on-top (driver_ids is empty)", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/installment/empty_driver_ids_bulk_installment_on_top.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"EGS_ONTOP"}, nil).AnyTimes()

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "INSTALLMENT")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 2, resp.TotalFailed)
	})

	t.Run("fail distance size (coin base scheme flag enabled)", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/distance/bulk_distance_coin_flag_invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "DISTANCE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 0, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)
		require.Contains(t, resp.Data, "coin base scheme and whole region flag have to be enabled together")
	})

	t.Run("success with partial failures due to db error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/flatrate/bulk_flat_rate_valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, model model.OnTopFare) error {
			if model.Name == "Test Bulk Upload" {
				return nil
			}
			return fmt.Errorf("error db")
		}).Times(2)

		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(1).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)

	})

	t.Run("success with partial failures due to validation error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/flatrate/bulk_flat_rate_invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 2, resp.TotalFailed)
	})
	t.Run("empty file", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/empty.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")

		// When
		bulkOntopCSV.BulkImportCSVCreate(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertEmptyFile(t, recorder)
	})

	t.Run("only header but no data row", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())
		b, err := testdata.ReadFile("testdata/only_header.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkUploadCSVReq((string(b)), "FLAT_RATE")

		// When
		bulkOntopCSV.BulkImportCSVCreate(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertOnlyHeader(t, recorder)
	})

	t.Run("invalid csv file format", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/invalid_csv_format.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")

		// When
		bulkOntopCSV.BulkImportCSVCreate(gctx)

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		assertInvalidCSVFormat(t, recorder)
	})
}

func TestBulkImportCSVUpdate(t *testing.T) {
	t.Run("success flat rate", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/flatrate/bulk_flat_rate_valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{ID: "1", Name: "Ontop A"},
			{ID: "2", Name: "Ontop B"},
		}, nil)
		deps.OnTopFareRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Times(2).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(2).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("fail flat rate (coin base scheme flag enabled)", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/flatrate/bulk_flat_rate_coin_flag_invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 0, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)
		require.Contains(t, resp.Data, "coin base scheme and whole region flag have to be enabled together")
	})

	t.Run("success basket size", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/basket_size/bulk_basket_size_valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(1).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "BASKET_SIZE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("fail basket size (coin base scheme flag enabled)", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/basket_size/bulk_basket_size_coin_flag_invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "BASKET_SIZE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 0, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)
		require.Contains(t, resp.Data, "coin base scheme and whole region flag have to be enabled together")
	})

	t.Run("success distance size", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/distance/bulk_distance_valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Times(1).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(1).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "DISTANCE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVCreate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("fail distance size (coin base scheme flag enabled)", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/distance/bulk_distance_coin_flag_invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "DISTANCE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 0, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)
		require.Contains(t, resp.Data, "coin base scheme and whole region flag have to be enabled together")
	})

	t.Run("success with partial failures due to db error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/flatrate/bulk_flat_rate_valid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"A", "B", "C", "D"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{ID: "1", Name: "Ontop A"},
			{ID: "2", Name: "Ontop B"},
		}, nil)

		deps.OnTopFareRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, model model.OnTopFare) error {
			if model.Name == "Test Bulk Upload" {
				return nil
			}
			return fmt.Errorf("error db")
		}).Times(2)

		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(1).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 1, resp.TotalSuccess)
		require.Equal(t, 1, resp.TotalFailed)
	})

	t.Run("success with partial failures due to validation error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/flatrate/bulk_flat_rate_invalid.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 2, resp.TotalFailed)
	})
	t.Run("empty file", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/empty.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")

		// When
		bulkOntopCSV.BulkImportCSVUpdate(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertEmptyFile(t, recorder)
	})

	t.Run("only header but no data row", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/only_header.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")

		// When
		bulkOntopCSV.BulkImportCSVUpdate(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertOnlyHeader(t, recorder)
	})

	t.Run("invalid csv file format", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/invalid_csv_format.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkUploadCSVReq(string(b), "FLAT_RATE")

		// When
		bulkOntopCSV.BulkImportCSVUpdate(gctx)

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		assertInvalidCSVFormat(t, recorder)
	})

	t.Run("succesfully update bulk egs ontop", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/installment/bulk_installment_on_top.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.IncentiveSourceService.EXPECT().Get().Return([]string{"EGS_ONTOP"}, nil).AnyTimes()
		deps.OnTopFareRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{ID: "1", Name: "earth-test-installment-1"},
			{ID: "2", Name: "earth-test-installment-2"},
			{ID: "3", Name: "earth-test-installment-3"},
		}, nil)
		deps.OnTopFareRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Times(3).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(3).Return(nil)

		// When
		gctx, recorder := makeBulkUploadCSVReq(string(b), "INSTALLMENT")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVUpdate(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 3, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})
}

func TestBulkImportCSVDelete(t *testing.T) {
	t.Run("success", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/bulk_delete.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}
		deps.OnTopFareRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{ID: "1", Name: "Ontop A"},
			{ID: "2", Name: "Ontop B"},
			{ID: "3", Name: "Ontop C"},
			{ID: "4", Name: "Ontop D"},
		}, nil)
		deps.OnTopFareRepo.EXPECT().DeleteByIDs(gomock.Any(), gomock.Any()).Times(4).Return(nil)
		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(4).Return(nil)

		// When
		gctx, recorder := makeBulkDeleteCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVDelete(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "SUCCESS", resp.Status)
		require.Equal(t, 4, resp.TotalSuccess)
		require.Equal(t, 0, resp.TotalFailed)
	})

	t.Run("success with partial failures due to db error", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/bulk_delete.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		deps.OnTopFareRepo.EXPECT().FindByIDs(gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{ID: "1", Name: "Ontop A"},
			{ID: "2", Name: "Ontop B"},
			{ID: "3", Name: "Ontop C"},
			{ID: "4", Name: "Ontop D"},
		}, nil)

		deps.OnTopFareRepo.EXPECT().DeleteByIDs(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ids []string) error {
			if stringutil.IsStringInList([]string{"1", "2"}, ids[0]) {
				return nil
			}
			return fmt.Errorf("error db")
		}).Times(4)

		deps.AuditLogRepo.EXPECT().Insert(gomock.Any(), gomock.Any()).Times(2).Return(nil)

		// When
		gctx, recorder := makeBulkDeleteCSVReq(string(b))
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkImportCSVDelete(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var resp bulkResultResponse
		testutil.DecodeJSON(t, recorder.Body, &resp)
		require.Equal(t, "FAILED", resp.Status)
		require.Equal(t, 2, resp.TotalSuccess)
		require.Equal(t, 2, resp.TotalFailed)
	})

	t.Run("empty file", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/empty.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkDeleteCSVReq(string(b))

		// When
		bulkOntopCSV.BulkImportCSVDelete(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertEmptyFile(t, recorder)
	})

	t.Run("only header but no data row", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/only_header.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkDeleteCSVReq(string(b))

		// When
		bulkOntopCSV.BulkImportCSVDelete(gctx)

		// Then
		require.Equal(t, http.StatusBadRequest, recorder.Code, "body: %v", recorder.Body)
		assertOnlyHeader(t, recorder)
	})

	t.Run("invalid csv file format", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, _ := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		b, err := testdata.ReadFile("testdata/invalid_csv_format.csv")
		if err != nil {
			t.Errorf("read testdata err: %v", err)
		}

		gctx, recorder := makeBulkDeleteCSVReq(string(b))

		// When
		bulkOntopCSV.BulkImportCSVDelete(gctx)

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		assertInvalidCSVFormat(t, recorder)
	})

}

func TestBulkExportCSV(t *testing.T) {
	t.Run("success flat rate", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		deps.OnTopFareRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{ID: "1", Name: "A", Scheme: model.FlatRateScheme, Region: "Region A", Conditions: []model.OntopCondition{
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, FlatRatePrices: []model.FlatRatePrice{{FlatRateType: model.DefaultFlatRate, NormalAmount: 5, BundleAmount: 10, NormalCoinAmount: 50, BundleCoinAmount: 100}}},
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, FlatRatePrices: []model.FlatRatePrice{{FlatRateType: model.DefaultFlatRate, NormalAmount: 6, BundleAmount: 11, NormalCoinAmount: 60, BundleCoinAmount: 110}}},
			}},
			{ID: "2", Name: "B", Scheme: model.FlatRateScheme, Region: "Region B", Conditions: []model.OntopCondition{
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, FlatRatePrices: []model.FlatRatePrice{{FlatRateType: model.DefaultFlatRate, NormalAmount: 5, BundleAmount: 10, NormalCoinAmount: 50, BundleCoinAmount: 100}}},
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, FlatRatePrices: []model.FlatRatePrice{{FlatRateType: model.DefaultFlatRate, NormalAmount: 6, BundleAmount: 11, NormalCoinAmount: 60, BundleCoinAmount: 110}}},
			}},
		}, nil)

		// When
		gctx, recorder := makeBulkExportCSVReq("FLAT_RATE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkExportCSV(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)
		var csvResults []bulkcsv.BulkImportCSVFlatRateSchemeDataRow
		err := readExportResults(recorder.Body, &csvResults)
		require.NoError(t, err)
		require.Len(t, csvResults, 4)

		row := csvResults[0]
		assert.Equal(t, "1", row.Id)
		assert.Equal(t, "A", row.Name)
		assert.Equal(t, "Region A", row.Region)
		assert.Equal(t, model.DefaultFlatRate, row.FlatRateCondition.ConditionsFlatRateScheme.Type)
		assert.Equal(t, types.Money(5), row.FlatRateCondition.ConditionsFlatRateNormalAmount.Money)
		assert.Equal(t, types.Money(10), row.FlatRateCondition.ConditionsFlatRateBundleAmount.Money)
		assert.Equal(t, 50, int(row.FlatRateCondition.ConditionsFlatRateNormalCoinAmount))
		assert.Equal(t, 100, int(row.FlatRateCondition.ConditionsFlatRateBundleCoinAmount))
		assertCommonConditionExport(t, row.CommonCondition)

		row = csvResults[1]
		assert.Equal(t, "1", row.Id)
		assert.Equal(t, "A", row.Name)
		assert.Equal(t, "Region A", row.Region)
		assert.Equal(t, model.DefaultFlatRate, row.FlatRateCondition.ConditionsFlatRateScheme.Type)
		assert.Equal(t, types.Money(6), row.FlatRateCondition.ConditionsFlatRateNormalAmount.Money)
		assert.Equal(t, types.Money(11), row.FlatRateCondition.ConditionsFlatRateBundleAmount.Money)
		assert.Equal(t, 60, int(row.FlatRateCondition.ConditionsFlatRateNormalCoinAmount))
		assert.Equal(t, 110, int(row.FlatRateCondition.ConditionsFlatRateBundleCoinAmount))
		assertCommonConditionExport(t, row.CommonCondition)

		row = csvResults[2]
		assert.Equal(t, "2", row.Id)
		assert.Equal(t, "B", row.Name)
		assert.Equal(t, "Region B", row.Region)
		assert.Equal(t, model.DefaultFlatRate, row.FlatRateCondition.ConditionsFlatRateScheme.Type)
		assert.Equal(t, types.Money(5), row.FlatRateCondition.ConditionsFlatRateNormalAmount.Money)
		assert.Equal(t, types.Money(10), row.FlatRateCondition.ConditionsFlatRateBundleAmount.Money)
		assert.Equal(t, 50, int(row.FlatRateCondition.ConditionsFlatRateNormalCoinAmount))
		assert.Equal(t, 100, int(row.FlatRateCondition.ConditionsFlatRateBundleCoinAmount))
		assertCommonConditionExport(t, row.CommonCondition)

		row = csvResults[3]
		assert.Equal(t, "2", row.Id)
		assert.Equal(t, "B", row.Name)
		assert.Equal(t, "Region B", row.Region)
		assert.Equal(t, model.DefaultFlatRate, row.FlatRateCondition.ConditionsFlatRateScheme.Type)
		assert.Equal(t, types.Money(6), row.FlatRateCondition.ConditionsFlatRateNormalAmount.Money)
		assert.Equal(t, types.Money(11), row.FlatRateCondition.ConditionsFlatRateBundleAmount.Money)
		assert.Equal(t, 60, int(row.FlatRateCondition.ConditionsFlatRateNormalCoinAmount))
		assert.Equal(t, 110, int(row.FlatRateCondition.ConditionsFlatRateBundleCoinAmount))
		assertCommonConditionExport(t, row.CommonCondition)
	})

	t.Run("success basket size", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		allPaymentForBasket := model.PaymentTypeForBasket{
			Cash:                 true,
			CashCollection:       true,
			CashAdvanceCoupon:    true,
			CashAdvanceEPayment:  true,
			CashAdvanceQRPayment: true,
			RLP:                  true,
			CreditCard:           true,
			LinemanWallet:        true,
			QRPromptPay:          true,
			WechatPay:            true,
			TRUEMoney:            true,
		}

		deps.OnTopFareRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{
				ID:     "1",
				Name:   "A",
				Scheme: model.BasketSizeScheme,
				Region: "Region A",
				Conditions: []model.OntopCondition{
					{
						Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood},
						Days:             []model.Days{"SUN"},
						Time:             []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}},
						BasketPrices:     []model.OntopBasketPrice{{From: 10, To: 20, Amount: 5}, {From: 11, To: 30, Amount: 10}},
						PaymentForBasket: allPaymentForBasket,
					},
					{
						Status:           model.StatusActive,
						ServiceTypes:     []model.Service{model.ServiceFood},
						Days:             []model.Days{"SUN"},
						Time:             []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}},
						BasketPrices:     []model.OntopBasketPrice{{From: 20, To: 30, Amount: 5}, {From: 40, To: 50, Amount: 10}},
						PaymentForBasket: allPaymentForBasket,
					},
				}},
			{
				ID:     "2",
				Name:   "B",
				Scheme: model.BasketSizeScheme,
				Region: "Region B",
				Conditions: []model.OntopCondition{
					{
						Status:           model.StatusActive,
						ServiceTypes:     []model.Service{model.ServiceFood},
						Days:             []model.Days{"SUN"},
						Time:             []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}},
						BasketPrices:     []model.OntopBasketPrice{{From: 10, To: 20, Amount: 5}, {From: 11, To: 30, Amount: 10}},
						PaymentForBasket: allPaymentForBasket,
					},
					{
						Status:           model.StatusActive,
						ServiceTypes:     []model.Service{model.ServiceFood},
						Days:             []model.Days{"SUN"},
						Time:             []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}},
						BasketPrices:     []model.OntopBasketPrice{{From: 20, To: 30, Amount: 5}, {From: 40, To: 50, Amount: 10}},
						PaymentForBasket: allPaymentForBasket,
					},
				}},
		}, nil)

		// When
		gctx, recorder := makeBulkExportCSVReq("BASKET_SIZE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkExportCSV(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)

		var csvResults []bulkcsv.BulkImportCSVBasketSchemeDataRow
		err := readExportResults(recorder.Body, &csvResults)
		require.NoError(t, err)
		require.Len(t, csvResults, 4)

		row := csvResults[0]
		assert.Equal(t, "1", row.Id)
		assert.Equal(t, "A", row.Name)
		assert.Equal(t, "Region A", row.Region)
		assert.Equal(t, []bulkcsv.BasketPriceScheme{
			{From: 10, To: 20, Amount: 5},
			{From: 11, To: 30, Amount: 10},
		}, row.BasketCondition.ConditionsBasketPriceSchemes.BasketPriceSchemes)
		assertCommonConditionExport(t, row.CommonCondition)
		assertAllowPaymentTypeColumnExport(t, allPaymentForBasket, row.ConditionsAllowPaymentType)

		row = csvResults[1]
		assert.Equal(t, "1", row.Id)
		assert.Equal(t, "A", row.Name)
		assert.Equal(t, "Region A", row.Region)
		assert.Equal(t, []bulkcsv.BasketPriceScheme{
			{From: 20, To: 30, Amount: 5},
			{From: 40, To: 50, Amount: 10},
		}, row.BasketCondition.ConditionsBasketPriceSchemes.BasketPriceSchemes)
		assertCommonConditionExport(t, row.CommonCondition)
		assertAllowPaymentTypeColumnExport(t, allPaymentForBasket, row.ConditionsAllowPaymentType)

		row = csvResults[2]
		assert.Equal(t, "2", row.Id)
		assert.Equal(t, "B", row.Name)
		assert.Equal(t, "Region B", row.Region)
		assert.Equal(t, []bulkcsv.BasketPriceScheme{
			{From: 10, To: 20, Amount: 5},
			{From: 11, To: 30, Amount: 10},
		}, row.BasketCondition.ConditionsBasketPriceSchemes.BasketPriceSchemes)
		assertCommonConditionExport(t, row.CommonCondition)
		assertAllowPaymentTypeColumnExport(t, allPaymentForBasket, row.ConditionsAllowPaymentType)

		row = csvResults[3]
		assert.Equal(t, "2", row.Id)
		assert.Equal(t, "B", row.Name)
		assert.Equal(t, "Region B", row.Region)
		assert.Equal(t, []bulkcsv.BasketPriceScheme{
			{From: 20, To: 30, Amount: 5},
			{From: 40, To: 50, Amount: 10},
		}, row.BasketCondition.ConditionsBasketPriceSchemes.BasketPriceSchemes)
		assertCommonConditionExport(t, row.CommonCondition)
		assertAllowPaymentTypeColumnExport(t, allPaymentForBasket, row.ConditionsAllowPaymentType)
	})

	t.Run("success distance", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		deps.OnTopFareRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{ID: "1", Name: "A", Scheme: model.DistanceScheme, Region: "Region A", Conditions: []model.OntopCondition{
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, DistancePrices: []model.OntopDistancePrice{{From: 10, To: 20, Amount: 5}, {From: 11, To: 30, Amount: 10}}},
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, DistancePrices: []model.OntopDistancePrice{{From: 20, To: 30, Amount: 5}, {From: 40, To: 50, Amount: 10}}},
			}},
			{ID: "2", Name: "B", Scheme: model.DistanceScheme, Region: "Region B", Conditions: []model.OntopCondition{
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, DistancePrices: []model.OntopDistancePrice{{From: 10, To: 20, Amount: 5}, {From: 11, To: 30, Amount: 10}}},
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, DistancePrices: []model.OntopDistancePrice{{From: 20, To: 30, Amount: 5}, {From: 40, To: 50, Amount: 10}}},
			}},
		}, nil)

		// When
		gctx, recorder := makeBulkExportCSVReq("DISTANCE")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkExportCSV(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)

		var csvResults []bulkcsv.BulkImportCSVDistanceSchemeDataRow
		err := readExportResults(recorder.Body, &csvResults)
		require.NoError(t, err)
		require.Len(t, csvResults, 4)

		row := csvResults[0]
		assert.Equal(t, "1", row.Id)
		assert.Equal(t, "A", row.Name)
		assert.Equal(t, "Region A", row.Region)
		assert.Equal(t, []bulkcsv.DistancePriceScheme{
			{FromInMeter: 10, ToInMeter: 20, Amount: 5},
			{FromInMeter: 11, ToInMeter: 30, Amount: 10},
		}, row.DistanceCondition.ConditionsDistanceSchemes.DistancePriceSchemes)
		assertCommonConditionExport(t, row.CommonCondition)

		row = csvResults[1]
		assert.Equal(t, "1", row.Id)
		assert.Equal(t, "A", row.Name)
		assert.Equal(t, "Region A", row.Region)
		assert.Equal(t, []bulkcsv.DistancePriceScheme{
			{FromInMeter: 20, ToInMeter: 30, Amount: 5},
			{FromInMeter: 40, ToInMeter: 50, Amount: 10},
		}, row.DistanceCondition.ConditionsDistanceSchemes.DistancePriceSchemes)
		assertCommonConditionExport(t, row.CommonCondition)

		row = csvResults[2]
		assert.Equal(t, "2", row.Id)
		assert.Equal(t, "B", row.Name)
		assert.Equal(t, "Region B", row.Region)
		assert.Equal(t, []bulkcsv.DistancePriceScheme{
			{FromInMeter: 10, ToInMeter: 20, Amount: 5},
			{FromInMeter: 11, ToInMeter: 30, Amount: 10},
		}, row.DistanceCondition.ConditionsDistanceSchemes.DistancePriceSchemes)
		assertCommonConditionExport(t, row.CommonCondition)

		row = csvResults[3]
		assert.Equal(t, "2", row.Id)
		assert.Equal(t, "B", row.Name)
		assert.Equal(t, "Region B", row.Region)
		assert.Equal(t, []bulkcsv.DistancePriceScheme{
			{FromInMeter: 20, ToInMeter: 30, Amount: 5},
			{FromInMeter: 40, ToInMeter: 50, Amount: 10},
		}, row.DistanceCondition.ConditionsDistanceSchemes.DistancePriceSchemes)
		assertCommonConditionExport(t, row.CommonCondition)
	})

	t.Run("success installment ontop", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		defer t.Cleanup(ctrl.Finish)
		bulkOntopCSV, deps := newTestBulkOntopCSVAPI(ctrl, newTestBulkCSVConfig())

		deps.OnTopFareRepo.EXPECT().Find(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{
			{ID: "1", Name: "installment-1", Scheme: model.InstallmentOnTopScheme, DriverIDs: []string{"driv-1", "driv-2"}, Region: "Region A", Conditions: []model.OntopCondition{
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, InstallmentPrice: model.OntopInstallmentPrice{MaxOrders: 10, Amount: 100.0}},
			}},
			{ID: "2", Name: "installment-2", Scheme: model.InstallmentOnTopScheme, DriverIDs: []string{"driv-3", "driv-4"}, Region: "Region B", Conditions: []model.OntopCondition{
				{Status: model.StatusActive, ServiceTypes: []model.Service{model.ServiceFood}, Days: []model.Days{"SUN"}, Time: []model.StartEndTime{{Begin: "11:00:00", End: "12:59:59"}}, InstallmentPrice: model.OntopInstallmentPrice{MaxOrders: 10, Amount: 100.0}},
			}},
		}, nil)

		// When
		gctx, recorder := makeBulkExportCSVReq("INSTALLMENT")
		wg := safe.CreateWaitGroupOnGctx(gctx)
		bulkOntopCSV.BulkExportCSV(gctx)
		wg.Wait()

		// Then
		require.Equal(t, http.StatusOK, recorder.Code, "body: %v", recorder.Body)

		var csvResults []bulkcsv.BulkImportCSVInstallmentSchemeDataRow
		err := readExportResults(recorder.Body, &csvResults)
		require.NoError(t, err)
		require.Len(t, csvResults, 2)

		row := csvResults[0]
		assert.Equal(t, "1", row.Id)
		assert.Equal(t, "installment-1", row.Name)
		assert.Equal(t, "Region A", row.Region)
		assert.Equal(t, 2, len(row.DriverIDs.DriverIDs))
		assert.Equal(t, types.Money(100.0), row.InstallmentCondition.ConditionsInstallmentAmount.Money)
		assert.Equal(t, 10, int(row.InstallmentCondition.ConditionsInstallmentMaxOrders))
		assertCommonConditionExport(t, row.CommonCondition)

		row = csvResults[1]
		assert.Equal(t, "2", row.Id)
		assert.Equal(t, "installment-2", row.Name)
		assert.Equal(t, "Region B", row.Region)
		assert.Equal(t, 2, len(row.DriverIDs.DriverIDs))
		assert.Equal(t, types.Money(100.0), row.InstallmentCondition.ConditionsInstallmentAmount.Money)
		assert.Equal(t, 10, int(row.InstallmentCondition.ConditionsInstallmentMaxOrders))
		assertCommonConditionExport(t, row.CommonCondition)
	})

}
