package shared

import "testing"

func TestToSchemeType(t *testing.T) {
	type args struct {
		s string
	}
	tests := []struct {
		name    string
		args    args
		want    SchemeType
		wantErr bool
	}{
		{
			name: "FLAT_RATE",
			args: args{
				s: "FLAT_RATE",
			},
			want:    FLAT_RATE,
			wantErr: false,
		},
		{
			name: "BASKET_SIZE",
			args: args{
				s: "BASKET_SIZE",
			},
			want:    BASKET_SIZE,
			wantErr: false,
		},
		{
			name: "DISTANCE",
			args: args{
				s: "DISTANCE",
			},
			want:    DISTANCE,
			wantErr: false,
		},
		{
			name: "UNKNOWN",
			args: args{
				s: "something undefided value",
			},
			want:    UNKNOWN,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := ToSchemeType(tt.args.s)
			if (err != nil) != tt.wantErr {
				t.Errorf("ToSchemeType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("ToSchemeType() = %v, want %v", got, tt.want)
			}
		})
	}
}
