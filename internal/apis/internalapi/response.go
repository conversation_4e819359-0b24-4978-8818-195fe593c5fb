package internalapi

import (
	"fmt"
	"time"

	"google.golang.org/protobuf/types/known/timestamppb"

	poolv1 "git.wndv.co/go/proto/lineman/fleet/pool/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type DistributeOrdersInZoneResponse struct {
	OrderIDs []string `json:"orderIds"`
}

func NewDistributeOrdersInZoneResponse(orderIDs []string) DistributeOrdersInZoneResponse {
	return DistributeOrdersInZoneResponse{
		OrderIDs: orderIDs,
	}
}

type PrechatInfosResponse struct {
	IsMO bool `json:"isMO"`
}

func NewPrechatInfosResponse(trip model.Trip) PrechatInfosResponse {
	return PrechatInfosResponse{
		IsMO: len(trip.Orders) > 1,
	}
}

type CreateTransactionResponse struct {
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"createdAt"`
}

type TotalIncomeResponse struct {
	OrderIDs                     []string    `json:"orderIds,omitempty"`
	TotalWage                    types.Money `json:"totalWage"`
	TotalOntop                   types.Money `json:"totalOntop"`
	TotalCoin                    types.Money `json:"totalCoin"`
	TotalProductivityIncentive   types.Money `json:"totalProductivityIncentive"`
	TotalOrdersInGuaranteePeriod int         `json:"totalOrdersInGuaranteePeriod"`
	TotalOrdersInDay             int         `json:"totalOrdersInDay"`
	TotalPendingWage             types.Money `json:"totalPendingWage"`
}

type ListDriverTransactionByTransRefIDResponse struct {
	Transactions []Transaction `json:"transactions"`
	TotalSize    int           `json:"totalSize"`
}

type Transaction struct {
	TransactionID string          `json:"transactionId"`
	Channel       string          `json:"channel"`
	Status        string          `json:"status"`
	Action        string          `json:"action"`
	Info          TransactionInfo `json:"info"`
	CreatedAt     time.Time       `json:"createdAt"`
}

type TransactionInfo struct {
	Category   string      `json:"category"`
	Type       string      `json:"type"`
	SubType    string      `json:"subType"`
	TransRefID string      `json:"transRefId"`
	TaxRefID   string      `json:"taxRefId"`
	Amount     types.Money `json:"amount"`
}

func NewListDriverTransactionByTransRefIDResponse(transactions []model.Transaction, totalSize int) ListDriverTransactionByTransRefIDResponse {
	var response ListDriverTransactionByTransRefIDResponse
	response.TotalSize = totalSize
	for _, transaction := range transactions {
		response.Transactions = append(response.Transactions, mapTransaction(transaction))
	}

	return response
}

func mapTransaction(transaction model.Transaction) Transaction {
	return Transaction{
		TransactionID: transaction.TransactionID,
		Channel:       string(transaction.Channel),
		Status:        string(transaction.Status),
		Action:        string(transaction.Action),
		Info:          mapTransactionInfo(transaction.Info),
		CreatedAt:     transaction.CreatedAt,
	}
}

func mapTransactionInfo(info model.TransactionInfo) TransactionInfo {
	return TransactionInfo{
		Category:   string(info.Category),
		Type:       string(info.Type),
		SubType:    string(info.SubType),
		TransRefID: string(info.TransRefID),
		TaxRefID:   info.TaxRefID,
		Amount:     info.Amount,
	}
}

type Coordinate struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

type Rider struct {
	Id       string     `json:"id"`
	Location Coordinate `json:"location"`
}

type DriversWithLocationResponse struct {
	Results []Rider `json:"results"`
}

func NewListDriversWithLocationResponse(driversWithLocation []service.DriverWithLocation) *poolv1.GetRidersInRadiusResponse {
	riders := make([]*poolv1.Rider, 0, len(driversWithLocation))
	response := &poolv1.GetRidersInRadiusResponse{Results: riders}
	for _, driver := range driversWithLocation {
		r := &poolv1.Rider{
			DistanceMeter: driver.DistanceMeter,
			RiderMinimal:  parseDriverWithLocationToRiderMinimalResponse(&driver),
			Id:            driver.Driver.DriverID,
			Location: &poolv1.Coordinate{
				Latitude:  driver.Location.Lat,
				Longitude: driver.Location.Lng,
			},
		}
		response.Results = append(response.Results, r)
	}
	return response
}

func parseDriverWithLocationToRiderMinimalResponse(drivWithLocation *service.DriverWithLocation) *poolv1.RiderMinimal {
	if drivWithLocation == nil {
		return nil
	}
	in := drivWithLocation.Driver

	var recoIdleTimeStartPoint *timestamppb.Timestamp
	if in.RecoIdleTimeStartPoint != nil {
		recoIdleTimeStartPoint = timestamppb.New(*in.RecoIdleTimeStartPoint)
	}
	var todayLastCompletedOrderAt *timestamppb.Timestamp
	if in.TodayLastCompletedOrderAt != nil {
		todayLastCompletedOrderAt = timestamppb.New(*in.TodayLastCompletedOrderAt)
	}
	var lastAcceptAttemptAt *timestamppb.Timestamp
	if in.LastAcceptAttemptAt != nil {
		lastAcceptAttemptAt = timestamppb.New(*in.LastAcceptAttemptAt)
	}
	var levelExpiredAt *timestamppb.Timestamp
	if in.Goodness.LevelExpiredAt != nil {
		levelExpiredAt = timestamppb.New(*in.Goodness.LevelExpiredAt)
	}
	var lockedFormQueueingUntil *timestamppb.Timestamp
	if in.LockedFromQueueingUntil != nil {
		lockedFormQueueingUntil = timestamppb.New(*in.LockedFromQueueingUntil)
	}

	return &poolv1.RiderMinimal{
		Id: in.DriverID,
		Information: &poolv1.RiderInformation{
			EncryptedPhone:      in.Phone.String(),
			Region:              in.Region.String(),
			Status:              string(in.Status),
			DriverRole:          string(in.DriverRole),
			EncryptedDriverType: in.DriverType.String(),
			DriverTier:          in.DriverTier.String(),
			EncryptedLineUid:    in.LineUID.String(),
			LineUserId:          in.LINEUserID,
			Options: &poolv1.RiderOptions{
				HaveBox:    in.Options.HaveBox,
				BoxType:    in.Options.BoxType.String(),
				HaveJacket: in.Options.HaveJacket,
				AutoAccept: in.Options.AutoAccept,
			},
			Shifts: in.Shifts,
		},
		Statistic: &poolv1.RiderStatistic{
			RecoIdleTimeStartPoint:       recoIdleTimeStartPoint,
			LastPredictionDisruption:     timestamppb.New(in.LastPredictionDisruption),
			TodayLastCompletedOrderAt:    todayLastCompletedOrderAt,
			LastAcceptAttemptAt:          lastAcceptAttemptAt,
			LastOnlineToAssignedLocation: ParseLocationToCoordinateResponse(in.LastOnlineToAssignedLocation),
			TemporaryBanHistory:          ParseTemporaryBanHistoryToBanHistoryResponse(in.TemporaryBanHistory),
		},
		DistributionInformation: &poolv1.RiderDistributionInformation{
			CurrentOrder:              in.CurrentOrder,
			CurrentTrip:               in.CurrentTrip,
			QueueingOrders:            in.QueueingOrders,
			QueueingTrips:             in.QueueingTrips,
			ServiceTypes:              toStrList(in.ServiceTypes),
			ServicesOptOut:            toStrList(in.ServicesOptOut),
			ServiceTypesSilentBanned:  toStrList(in.ServiceTypesSilentBanned),
			ServiceTypesDeprioritized: toStrList(in.ServiceTypesDeprioritized),
			DedicatedZones:            in.DedicatedZones,
			H3Recommendation: &poolv1.H3Recommendation{
				RecommendationId: in.H3Recommendation.RecommendationID,
				Areas:            ParseArrayRecommendedH3(in.H3Recommendation.Areas),
				H3AreasIndex:     ParseMapRecommendedH3(in.H3Recommendation.H3AreasIndex),
				ExpiredAt:        timestamppb.New(in.H3Recommendation.ExpiredAt),
			},
			IsDeprioritized: in.IsDeprioritized,
			Goodness: &poolv1.Goodness{
				Level:          in.Goodness.Level.String(),
				LevelExpiredAt: levelExpiredAt,
			},
			IsSupplyPositioning:       in.IsSupplyPositioning,
			AllowQueueing:             in.AllowQueueing,
			LockedFromQueueingUntil:   lockedFormQueueingUntil,
			IsAcknowledgementRequired: in.IsAcknowledgementRequired,
		},
		Preference: &poolv1.RiderPreference{
			OfflineLater: in.OfflineLater,
			BanLater:     in.BanLater,
		},
		Benefits: &poolv1.RiderBenefits{
			NegativeBalanceGroup: in.NegativeBalanceGroup,
			OnTopQuotas:          ParseOnTopQuota(in.OnTopQuotas),
		},
		CreatedAt: timestamppb.New(in.CreatedAt),
	}
}

func ParseLocationToCoordinateResponse(in *model.Location) *poolv1.Coordinate {
	if in == nil {
		return nil
	}

	return &poolv1.Coordinate{
		Latitude:  in.Lat,
		Longitude: in.Lng,
	}
}

func ParseOnTopQuota(in []model.OnTopQuota) []*poolv1.OnTopQuota {
	if len(in) == 0 {
		return nil
	}

	results := make([]*poolv1.OnTopQuota, 0, len(in))
	for _, rec := range in {
		results = append(results, &poolv1.OnTopQuota{
			OnTopFareId:    rec.OnTopFareID,
			RemainingQuota: int32(rec.RemainingQuota),
			MaximumQuota:   int32(rec.MaximumQuota),
			QuotaType:      string(rec.QuotaType),
			EndDate:        timestamppb.New(rec.EndDate),
		})
	}

	return results
}

func toStrList[T fmt.Stringer](items []T) []string {
	if items == nil {
		return []string{}
	}
	result := make([]string, 0, len(items))
	for _, item := range items {
		result = append(result, item.String())
	}
	return result
}

func ParseArrayRecommendedH3(in []model.RecommendedH3) []*poolv1.RecommendedH3 {
	if len(in) == 0 {
		return nil
	}

	results := make([]*poolv1.RecommendedH3, 0, len(in))
	for _, rec := range in {
		results = append(results, &poolv1.RecommendedH3{
			H3Id:        rec.H3ID,
			Name:        rec.Name,
			FullAddress: rec.FullAddress,
			PdsScore:    rec.PDSScore,
			WaitingTime: rec.WaitingTime,
			Coordinate: &poolv1.Coordinate{
				Latitude:  rec.Lat,
				Longitude: rec.Lng,
			},
		})
	}
	return results
}

func ParseMapRecommendedH3(in map[string]model.RecommendedH3) map[string]*poolv1.RecommendedH3 {
	if len(in) == 0 {
		return nil
	}

	result := make(map[string]*poolv1.RecommendedH3, len(in))
	for key, value := range in {
		result[key] = &poolv1.RecommendedH3{
			H3Id:        value.H3ID,
			Name:        value.Name,
			FullAddress: value.FullAddress,
			PdsScore:    value.PDSScore,
			WaitingTime: value.WaitingTime,
			Coordinate: &poolv1.Coordinate{
				Latitude:  value.Lat,
				Longitude: value.Lng,
			},
		}
	}
	return result
}

func ParseTemporaryBanHistoryToBanHistoryResponse(in *model.BanHistory) *poolv1.BanHistory {
	if in == nil {
		return nil
	}

	banReasons := make([]*poolv1.BanReason, 0, len(in.BanReasons))
	for _, banReason := range in.BanReasons {
		financialDetail := &poolv1.FinancialDetail{}
		if banReason.FinancialDetail != nil {
			focusedFinancialDetail := banReason.FinancialDetail
			financialDetail = &poolv1.FinancialDetail{
				OrderId:      focusedFinancialDetail.OrderID,
				TicketId:     focusedFinancialDetail.TicketID,
				DamageAmount: focusedFinancialDetail.DamageAmount,
			}
		}

		banReasons = append(banReasons, &poolv1.BanReason{
			Category:        banReason.Category,
			SubCategory:     banReason.SubCategory,
			MessageToDriver: banReason.MessageToDriver,
			InternalReason:  banReason.InternalReason,
			Financial:       banReason.Financial,
			FinancialDetail: financialDetail,
		})
	}

	return &poolv1.BanHistory{
		DriverId:                  in.DriverID,
		Type:                      in.Type,
		Action:                    in.Action,
		Value:                     in.Value,
		Reason:                    in.Reason,
		Category:                  in.Category,
		MessageToDriver:           in.MessageToDriver,
		CreatedBy:                 in.CreatedBy,
		CreatedAt:                 timestamppb.New(in.CreatedAt),
		BannedUntil:               timestamppb.New(in.BannedUntil),
		UnbanImageRefUrl:          in.UnBanImageRefURL,
		BanReasons:                banReasons,
		Url:                       in.Url,
		RetrainingTitle:           in.RetrainingTitle,
		RetrainingMessageToDriver: in.RetrainingMessageToDriver,
	}
}

type CheckIfCandidatesEnoughRes struct {
	Enough bool `json:"enough"`
}
