package internalapi_test

import (
	"bytes"
	"encoding/json"
	"errors"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/internalapi"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/mock_order"
	"git.wndv.co/lineman/fleet-distribution/internal/config/mock_config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker/mock_locker"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/income/mock_income"
)

func TestInternalAPI_GetDriverLocation(t *testing.T) {
	driverID := "driver1"
	driverLocation := model.DriverLocation{
		DriverID: driverID,
		Location: model.Location{Lat: 13.0000, Lng: 100.54},
	}

	t.Run("[200] - found driver location", func(tt *testing.T) {
		tt.Parallel()

		m := createMockery(t)
		defer m.cleanup()
		api := m.createInternalAPI()

		ctx, recorder := newRequest(http.MethodGet, "/driver/"+driverID+"/location", nil)
		ctx.Params = []gin.Param{{Key: "driver_id", Value: driverID}}

		m.MockDriverLocationRepo.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(&driverLocation, nil)

		api.GetDriverLocation(ctx)

		require.Equal(tt, http.StatusOK, recorder.Code, recorder.Body.String())
		assertLocation(tt, recorder.Body.Bytes(), driverLocation.Location)
	})

	t.Run("[404] - not found driver location", func(tt *testing.T) {
		tt.Parallel()
		m := createMockery(t)
		defer m.cleanup()
		api := m.createInternalAPI()

		ctx, recorder := newRequest(http.MethodGet, "/driver/"+driverID+"/location", nil)
		ctx.Params = []gin.Param{{Key: "driver_id", Value: driverID}}

		m.MockDriverLocationRepo.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(nil, mongodb.ErrDataNotFound)

		api.GetDriverLocation(ctx)

		require.Equal(tt, http.StatusNotFound, recorder.Code, recorder.Body.String())
	})

	t.Run("[500] - error", func(tt *testing.T) {
		tt.Parallel()
		m := createMockery(t)
		defer m.cleanup()
		api := m.createInternalAPI()

		ctx, recorder := newRequest(http.MethodGet, "/driver/"+driverID+"/location", nil)
		ctx.Params = []gin.Param{{Key: "driver_id", Value: driverID}}

		m.MockDriverLocationRepo.EXPECT().GetDriverLocation(gomock.Any(), driverID).Return(nil, errors.New("error"))

		api.GetDriverLocation(ctx)

		require.Equal(tt, http.StatusInternalServerError, recorder.Code, recorder.Body.String())
	})
}

func assertLocation(tt *testing.T, body []byte, location model.Location) {
	var loc model.Location
	json.Unmarshal(body, &loc)
	require.Equal(tt, location.Lat, loc.Lat)
	require.Equal(tt, location.Lng, loc.Lng)
}

type Mockery struct {
	MockDriverLocationRepo           *mock_repository.MockDriverLocationRepository
	MockDriverStatisticRepository    *mock_repository.MockDriverStatisticRepository
	MockTxnSchemeRepo                *mock_repository.MockTransactionSchemeRepository
	MockDriverRepository             *mock_repository.MockDriverRepository
	MockDriverTxnSvc                 *mock_service.MockDriverTransactionServiceV2
	MockIncomeSummaryService         *mock_income.MockIncomeSummaryService
	MockUniversalClient              *mock_redis.MockUniversalClient
	MockTripService                  *mock_service.MockTripServices
	MockLocker                       *mock_locker.MockLocker
	MockDistributor                  *mock_order.MockOrderDistributor
	MockOrderService                 *mock_repository.MockOrderRepository
	MockConfigUpdater                *mock_config.MockConfigUpdater
	MockPendingTransactionRepository *mock_repository.MockPendingTransactionRepository
	MockVOSService                   *mock_service.MockVOSService
	MockLocationManager              *mock_service.MockLocationManager
	MockTxnHelper                    *mock_transaction.MockTxnHelper
	MockThrottledOrderRepo           *mock_repository.MockThrottledOrderRepository
	MockZoneRepo                     *mock_repository.MockZoneRepository
	MockFeatureFlagService           *mock_featureflag.MockService
	MockDistributionService          *mock_service.MockDistributionService
	cleanUpFns                       []func()
}

func createMockery(t *testing.T) *Mockery {
	m := Mockery{}

	ctrl := gomock.NewController(t)
	m.MockDriverLocationRepo = mock_repository.NewMockDriverLocationRepository(ctrl)
	m.MockDriverStatisticRepository = mock_repository.NewMockDriverStatisticRepository(ctrl)
	m.MockTxnSchemeRepo = mock_repository.NewMockTransactionSchemeRepository(ctrl)
	m.MockDriverRepository = mock_repository.NewMockDriverRepository(ctrl)
	m.MockDriverTxnSvc = mock_service.NewMockDriverTransactionServiceV2(ctrl)
	m.MockIncomeSummaryService = mock_income.NewMockIncomeSummaryService(ctrl)
	m.MockUniversalClient = mock_redis.NewMockUniversalClient(ctrl)
	m.MockTripService = mock_service.NewMockTripServices(ctrl)
	m.MockLocker = mock_locker.NewMockLocker(ctrl)
	m.MockDistributor = mock_order.NewMockOrderDistributor(ctrl)
	m.MockOrderService = mock_repository.NewMockOrderRepository(ctrl)
	m.MockConfigUpdater = mock_config.NewMockConfigUpdater(ctrl)
	m.MockPendingTransactionRepository = mock_repository.NewMockPendingTransactionRepository(ctrl)
	m.MockVOSService = mock_service.NewMockVOSService(ctrl)
	m.MockLocationManager = mock_service.NewMockLocationManager(ctrl)
	m.MockTxnHelper = mock_transaction.NewMockTxnHelper(ctrl)
	m.MockThrottledOrderRepo = mock_repository.NewMockThrottledOrderRepository(ctrl)
	m.MockZoneRepo = mock_repository.NewMockZoneRepository(ctrl)
	m.MockFeatureFlagService = mock_featureflag.NewMockService(ctrl)
	m.MockDistributionService = mock_service.NewMockDistributionService(ctrl)
	m.cleanUpFns = append(m.cleanUpFns, func() { ctrl.Finish() })

	return &m
}

func (m *Mockery) createInternalAPI() *internalapi.InternalAPI {
	internalApi := internalapi.InternalAPI{
		DriverLocationRepo:         m.MockDriverLocationRepo,
		StatisticRepo:              m.MockDriverStatisticRepository,
		TransactionSchemeRepo:      m.MockTxnSchemeRepo,
		DriverRepository:           m.MockDriverRepository,
		DriverTransactionServiceV2: m.MockDriverTxnSvc,
		IncomeSummaryService:       m.MockIncomeSummaryService,
		UniversalClient:            m.MockUniversalClient,
		Locker:                     m.MockLocker,
		Distributor:                m.MockDistributor,
		OrderService:               m.MockOrderService,
		TripServices:               m.MockTripService,
		PendingTransactionRepo:     m.MockPendingTransactionRepository,
		VosService:                 m.MockVOSService,
		LocationManager:            m.MockLocationManager,
		TxnHelper:                  m.MockTxnHelper,
		ThrottledOrderRepo:         m.MockThrottledOrderRepo,
		ZoneRepo:                   m.MockZoneRepo,
		FeatureFlagService:         m.MockFeatureFlagService,
		DistributionService:        m.MockDistributionService,
	}

	return &internalApi
}

func (m *Mockery) createInternalAPIForBulk(_ *testing.T) *internalapi.InternalAPI {
	internalApi := internalapi.InternalAPI{
		DriverLocationRepo:    m.MockDriverLocationRepo,
		StatisticRepo:         m.MockDriverStatisticRepository,
		TransactionSchemeRepo: m.MockTxnSchemeRepo,
		DriverRepository:      m.MockDriverRepository,
		UniversalClient:       m.MockUniversalClient,
		TripServices:          m.MockTripService,
	}

	return &internalApi
}

func (m *Mockery) createInternalAPIForConfigUpdater(_ *testing.T) *internalapi.InternalAPI {
	internalApi := internalapi.InternalAPI{
		DBConfigUpdater: m.MockConfigUpdater,
	}

	return &internalApi
}

func (m *Mockery) cleanup() {
	for i := 0; i < len(m.cleanUpFns); i++ {
		m.cleanUpFns[i]()
	}
}

func newRequest(method, path string, v interface{}) (ctx *gin.Context, recorder *httptest.ResponseRecorder) {
	var payload bytes.Buffer
	json.NewEncoder(&payload).Encode(v)
	recorder = httptest.NewRecorder()
	ctx, _ = gin.CreateTestContext(recorder)
	ctx.Request, _ = http.NewRequest(method, path, &payload)

	return
}

// The rest of the file remains unchanged.
