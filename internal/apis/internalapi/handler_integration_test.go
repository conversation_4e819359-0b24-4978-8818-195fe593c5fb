//go:build integration_test
// +build integration_test

package internalapi_test

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/go/mongo/mongotest"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/internalapi"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestHandler_RiderTotalIncome(t *testing.T) {
	t.Run("happy case", func(t *testing.T) {
		type testCase struct {
			reqStart time.Time
			reqEnd   time.Time
		}

		tests := []testCase{
			{
				reqStart: time.Date(2024, 5, 10, 5, 0, 0, 0, timeutil.BangkokLocation()),
				reqEnd:   time.Date(2024, 5, 10, 7, 0, 0, 0, timeutil.BangkokLocation()),
			},
			{
				reqStart: time.Date(2024, 5, 9, 23, 0, 0, 0, time.UTC), // 2024-05-10 BKK
				reqEnd:   time.Date(2024, 5, 10, 7, 0, 0, 0, timeutil.BangkokLocation()),
			},
			{
				reqStart: time.Date(2024, 5, 9, 20, 0, 0, 0, time.UTC), // 2024-05-10 BKK
				reqEnd:   time.Date(2024, 5, 9, 23, 0, 0, 0, time.UTC), // 2024-05-10 BKK
			},
		}

		for i := range tests {
			tc := &tests[i]

			ctn := ittest.NewContainer(t)
			if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_total_income"); err != nil {
				t.Errorf("Unexpected error initfixture: %v", err)
			}

			dateTime := time.Date(2024, 5, 11, 22, 0, 0, 0, timeutil.BangkokLocation())
			timeutils.FreezeWithTime(dateTime.Unix() * 1000)
			defer timeutils.Unfreeze()

			gctx := testutil.NewContextWithRecorder()

			driverID := "DRIV"

			gctx.
				SetGET("/v1/internal/driver/%s/total-income", driverID).
				Body().
				JSON(internalapi.TotalIncomeReq{
					Start: tc.reqStart,
					End:   tc.reqEnd,
				}).
				Build()

			gctx.Send(ctn.GinEngineRouter)
			gctx.AssertResponseCode(t, http.StatusOK)

			var actual internalapi.TotalIncomeResponse
			testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
			assert.Equal(t, types.NewMoney(42.5), actual.TotalWage)
			assert.Equal(t, types.NewMoney(17.8), actual.TotalOntop)
			assert.Equal(t, types.NewMoney(20), actual.TotalCoin)                      // 100 * (10/40)
			assert.Equal(t, types.NewMoney(370.33), actual.TotalProductivityIncentive) // 555.5 * (2/3)
			assert.Equal(t, types.NewMoney(114), actual.TotalPendingWage)              // Pending Transaction collection (Pending + Created Transaction + Reject)

			orderIdsExpected := types.NewSetFrom("order-1", "order-2") // order-3 is missing document in coll transactions
			orderIdsActual := types.NewSetFrom(actual.OrderIDs...)
			diff := orderIdsExpected.DiffAll(orderIdsActual)
			require.Emptyf(t, diff.Slice(), "expecting no diff, expecting %v, actual %v", orderIdsExpected.Slice(), orderIdsActual.Slice())
		}
	})
}

func TestCreateTransaction(t *testing.T) {
	t.Run("normal", func(tt *testing.T) {
		ctn := ittest.NewContainer(tt)
		gctx := testutil.NewContextWithRecorder()
		gctx.
			SetPOST("/v1/internal/driver/create-transaction").
			Body().JSON(internalapi.CreateTransactionReq{
			DriverID:  "GRAB-001",
			RefID:     "ref",
			MissionId: "mission",
			Category:  model.WalletTransactionCategory,
			Amount:    100.25,
			Type:      model.IncentiveTransactionType,
			SubType:   "sub-type",
			Sources: []internalapi.SourceCreateTransactionReq{
				{
					"source",
					"name",
					1,
				},
				{
					"source",
					"name",
					2,
				},
			},
			InfoRefIds: []string{"ref-ids-1"},
		},
		).Build()
		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(tt, http.StatusOK)
		var actual internalapi.CreateTransactionResponse
		testutil.DecodeJSON(tt, gctx.ResponseRecorder.Body, &actual)
		assert.NotEqual(tt, "", actual.ID)

		var txn model.Transaction
		err := ctn.TransactionDataStore.FindOne(context.Background(), bson.M{
			"info.mission_id": "mission",
		}, &txn)
		require.NoError(tt, err)
		require.Equal(tt, "ref-ids-1", txn.Info.RefIDs[0])
		require.Equal(tt, 2, len(txn.Sources))
		require.Equal(tt, txn.Info.Amount.String(), "100.25")
		require.Equal(tt, 0, len(txn.Info.IncentiveSources))

		var wht model.Transaction
		err = ctn.TransactionDataStore.FindOne(context.Background(), bson.M{
			"info.tax_ref_id": txn.Info.TaxRefID,
			"info.type":       model.WithholdingTransactionType,
		}, &wht)
		require.Error(tt, err)
		require.Equal(tt, err.Error(), "Data not found")
	})

	t.Run("normal - isSupportWHT, round up", func(tt *testing.T) {
		ctn := ittest.NewContainer(tt)
		gctx := testutil.NewContextWithRecorder()
		gctx.
			SetPOST("/v1/internal/driver/create-transaction").
			Body().JSON(internalapi.CreateTransactionReq{
			DriverID:     "GRAB-001",
			RefID:        "ref",
			MissionId:    "mission",
			Category:     model.WalletTransactionCategory,
			Amount:       100.25,
			Type:         model.IncentiveTransactionType,
			SubType:      "sub-type",
			IsSupportWht: true,
			Sources: []internalapi.SourceCreateTransactionReq{
				{
					"source",
					"name",
					1,
				},
				{
					"source",
					"name",
					2,
				},
			},
			InfoRefIds: []string{"ref-ids-1"},
		},
		).Build()
		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(tt, http.StatusOK)
		var actual internalapi.CreateTransactionResponse
		testutil.DecodeJSON(tt, gctx.ResponseRecorder.Body, &actual)
		assert.NotEqual(tt, "", actual.ID)

		var txn model.Transaction
		err := ctn.TransactionDataStore.FindOne(context.Background(), bson.M{
			"info.mission_id": "mission",
		}, &txn)
		require.NoError(tt, err)
		require.Equal(tt, "ref-ids-1", txn.Info.RefIDs[0])
		require.Equal(tt, txn.CreatedAt.In(timeutil.BangkokLocation()), txn.Info.TransactionDate.In(timeutil.BangkokLocation()))
		require.Equal(tt, 2, len(txn.Sources))
		require.Equal(tt, txn.Info.Amount.String(), "100.25")

		var wht model.Transaction
		err = ctn.TransactionDataStore.FindOne(context.Background(), bson.M{
			"info.tax_ref_id": txn.Info.TaxRefID,
			"info.type":       model.WithholdingTransactionType,
		}, &wht)
		require.NoError(tt, err)
		require.Equal(tt, wht.Info.Amount.String(), "3.01")
	})

	t.Run("normal - isSupportWHT, round down", func(tt *testing.T) {
		ctn := ittest.NewContainer(tt)
		gctx := testutil.NewContextWithRecorder()
		gctx.
			SetPOST("/v1/internal/driver/create-transaction").
			Body().JSON(internalapi.CreateTransactionReq{
			DriverID:     "GRAB-001",
			RefID:        "ref",
			MissionId:    "mission",
			Category:     model.WalletTransactionCategory,
			Amount:       100.01,
			Type:         model.IncentiveTransactionType,
			SubType:      "sub-type",
			IsSupportWht: true,
			Sources: []internalapi.SourceCreateTransactionReq{
				{
					"source",
					"name",
					1,
				},
				{
					"source",
					"name",
					2,
				},
			},
			InfoRefIds: []string{"ref-ids-1"},
		},
		).Build()
		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(tt, http.StatusOK)
		var actual internalapi.CreateTransactionResponse
		testutil.DecodeJSON(tt, gctx.ResponseRecorder.Body, &actual)
		assert.NotEqual(tt, "", actual.ID)

		var txn model.Transaction
		err := ctn.TransactionDataStore.FindOne(context.Background(), bson.M{
			"info.mission_id": "mission",
		}, &txn)
		require.NoError(tt, err)
		require.Equal(tt, "ref-ids-1", txn.Info.RefIDs[0])
		require.Equal(tt, 2, len(txn.Sources))
		require.Equal(tt, txn.Info.Amount.String(), "100.01")

		var wht model.Transaction
		err = ctn.TransactionDataStore.FindOne(context.Background(), bson.M{
			"info.tax_ref_id": txn.Info.TaxRefID,
			"info.type":       model.WithholdingTransactionType,
		}, &wht)
		require.NoError(tt, err)
		require.Equal(tt, wht.Info.Amount.String(), "3.00")
	})

	t.Run("with transaction date", func(tt *testing.T) {
		ctn := ittest.NewContainer(tt)

		gctx := testutil.NewContextWithRecorder()
		gctx.
			SetPOST("/v1/internal/driver/create-transaction").
			Body().JSON(internalapi.CreateTransactionReq{
			DriverID:     "GRAB-001",
			RefID:        "ref",
			MissionId:    "mission",
			Category:     model.WalletTransactionCategory,
			Amount:       10.23,
			Type:         model.IncentiveTransactionType,
			SubType:      model.GuaranteeTransactionSubType,
			IsSupportWht: true,
			Sources: []internalapi.SourceCreateTransactionReq{
				{
					"source",
					"name",
					1,
				},
				{
					"source",
					"name",
					2,
				},
			},
			InfoRefIds:      []string{"ref-ids-1"},
			TransactionDate: "2024-04-05",
		},
		).Build()
		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(tt, http.StatusOK)
		var actual internalapi.CreateTransactionResponse
		testutil.DecodeJSON(tt, gctx.ResponseRecorder.Body, &actual)
		assert.NotEqual(tt, "", actual.ID)

		var txn model.Transaction
		err := ctn.TransactionDataStore.FindOne(context.Background(), bson.M{
			"info.mission_id": "mission",
		}, &txn)
		require.NoError(tt, err)
		require.Equal(tt, time.Date(2024, 4, 5, 0, 0, 0, 0, timeutil.BangkokLocation()), txn.Info.TransactionDate.In(timeutil.BangkokLocation()))

		var i model.IncomeDailySummary
		err = ctn.IncomeDailySummaryStore.FindOne(context.Background(), bson.M{
			"date": time.Date(2024, 4, 5, 0, 0, 0, 0, timeutil.BangkokLocation()),
		}, &i)
		require.NoError(tt, err)
		require.Equal(tt, "GRAB-001", i.DriverID)
		require.Equal(tt, time.Date(2024, 4, 5, 0, 0, 0, 0, timeutil.BangkokLocation()), i.Date.In(timeutil.BangkokLocation()))
		require.Equal(tt, types.NewMoney(9.92), i.TotalIncome)
		require.Equal(tt, 0, i.TotalTrip)
		require.Equal(tt, 0, i.TotalOrder)
		require.True(tt, types.Money(0).Equal(i.IncomeDetail.TotalIncentive))
		require.True(tt, types.Money(10.23).Equal(i.IncomeDetail.TotalGuaranteeIncentive))
		require.True(tt, types.Money(0).Equal(i.IncomeDetail.TotalTip))
		require.True(tt, types.Money(0.31).Equal(i.IncomeDetail.TotalWithholdingTax))
		require.True(tt, types.Money(0).Equal(i.IncomeDetail.TotalWage))
	})

	t.Run("with incentive source", func(tt *testing.T) {
		ctn := ittest.NewContainer(tt)
		gctx := testutil.NewContextWithRecorder()
		gctx.
			SetPOST("/v1/internal/driver/create-transaction").
			Body().JSON(internalapi.CreateTransactionReq{
			DriverID:  "GRAB-001",
			RefID:     "ref",
			MissionId: "mission",
			Category:  model.WalletTransactionCategory,
			Amount:    100.25,
			Type:      model.IncentiveTransactionType,
			SubType:   "sub-type",
			Sources: []internalapi.SourceCreateTransactionReq{
				{
					"source",
					"name",
					1,
				},
				{
					"source",
					"name",
					2,
				},
			},
			InfoRefIds:      []string{"ref-ids-1"},
			IncentiveSource: "GUARANTEE",
		},
		).Build()
		gctx.Send(ctn.GinEngineRouter)
		gctx.AssertResponseCode(tt, http.StatusOK)
		var actual internalapi.CreateTransactionResponse
		testutil.DecodeJSON(tt, gctx.ResponseRecorder.Body, &actual)
		assert.NotEqual(tt, "", actual.ID)

		var txn model.Transaction
		err := ctn.TransactionDataStore.FindOne(context.Background(), bson.M{
			"info.mission_id": "mission",
		}, &txn)
		require.NoError(tt, err)
		require.Equal(tt, 1, len(txn.Info.IncentiveSources))
		require.Equal(tt, "GUARANTEE", txn.Info.IncentiveSources[0])
	})
}

func TestHandler_ListDriverTransactionsByTransRefID(t *testing.T) {
	type request struct {
		driverID    string
		transRefID  string
		queryParams map[string]string
	}

	mNow := func(t *model.Transaction) {
		t.CreatedAt = time.Date(2024, 5, 11, 22, 0, 0, 0, timeutil.BangkokLocation())
	}

	tranGenerate := func(req request, count int) []model.Transaction {
		base := *model.NewTransaction(
			"trans-id-1",
			model.SystemTransactionChannel,
			model.IncentiveTransactionAction,
			model.SuccessTransactionStatus,
			model.TransactionInfo{
				DriverID:   req.driverID,
				TransRefID: crypt.EncryptedString(req.transRefID),
			})
		base.Info.Amount = 5
		base.Info.TaxRefID = "tax-ref-1"
		base.Info.Category = model.WalletTransactionCategory
		base.Info.Type = model.IncentiveTransactionType
		base.Info.SubType = model.GuaranteeTransactionSubType

		trans := make([]model.Transaction, 0, count)
		for i := 0; i < count; i++ {
			tran := base
			tran.TransactionID = fmt.Sprintf("trans-id-%d", i+1)
			trans = append(trans, tran)
		}

		return trans
	}

	testcases := []struct {
		name    string
		req     request
		prepare func(req request) []model.Transaction
		expect  func(t *testing.T, ctn *ittest.IntegrationTestContainer, req request, gctx *testutil.GinContextWithRecorder)
	}{
		{
			name: "mapping",
			req: request{
				driverID:    "LMDSAC9I3-0001",
				transRefID:  "trans-ref-a",
				queryParams: map[string]string{},
			},
			prepare: func(req request) []model.Transaction {
				trans := tranGenerate(req, 1)
				return trans
			},
			expect: func(t *testing.T, ctn *ittest.IntegrationTestContainer, req request, gctx *testutil.GinContextWithRecorder) {
				gctx.AssertResponseCode(t, http.StatusOK)
				var actual internalapi.ListDriverTransactionByTransRefIDResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
				assert.Equal(t, 1, actual.TotalSize)
				assert.Equal(t, "INCENTIVE", actual.Transactions[0].Action)
				assert.Equal(t, "SYSTEM", actual.Transactions[0].Channel)
				assert.Equal(t, "SUCCESS", actual.Transactions[0].Status)
				assert.Equal(t, "trans-id-1", actual.Transactions[0].TransactionID)
				assert.Equal(t, "2024-05-11T22:00:00+07:00", actual.Transactions[0].CreatedAt.In(timeutil.BangkokLocation()).Format(time.RFC3339))

				assert.Equal(t, "trans-ref-a", actual.Transactions[0].Info.TransRefID)
				assert.Equal(t, types.Money(5), actual.Transactions[0].Info.Amount)
				assert.Equal(t, "tax-ref-1", actual.Transactions[0].Info.TaxRefID)
				assert.Equal(t, "WALLET", actual.Transactions[0].Info.Category)
				assert.Equal(t, "INCENTIVE", actual.Transactions[0].Info.Type)
				assert.Equal(t, "GUARANTEE", actual.Transactions[0].Info.SubType)
			},
		},
		{
			name: "filtering: by type",
			req: request{
				driverID:   "LMDSAC9I3-0001",
				transRefID: "trans-ref-a",
				queryParams: map[string]string{
					"type": "ABC",
				},
			},
			prepare: func(req request) []model.Transaction {
				trans := tranGenerate(req, 1)
				trans[0].Info.Type = "ABC"
				return trans
			},
			expect: func(t *testing.T, ctn *ittest.IntegrationTestContainer, req request, gctx *testutil.GinContextWithRecorder) {
				gctx.AssertResponseCode(t, http.StatusOK)
				var actual internalapi.ListDriverTransactionByTransRefIDResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
				assert.Equal(t, 1, actual.TotalSize)
				assert.Equal(t, "ABC", actual.Transactions[0].Info.Type)
			},
		},
		{
			name: "filtering: by subType",
			req: request{
				driverID:   "LMDSAC9I3-0001",
				transRefID: "trans-ref-a",
				queryParams: map[string]string{
					"subType": "ABC",
				},
			},
			prepare: func(req request) []model.Transaction {
				trans := tranGenerate(req, 1)
				trans[0].Info.SubType = "ABC"
				return trans
			},
			expect: func(t *testing.T, ctn *ittest.IntegrationTestContainer, req request, gctx *testutil.GinContextWithRecorder) {
				gctx.AssertResponseCode(t, http.StatusOK)
				var actual internalapi.ListDriverTransactionByTransRefIDResponse
				testutil.DecodeJSON(t, gctx.ResponseRecorder.Body, &actual)
				assert.Equal(t, 1, actual.TotalSize)
				assert.Equal(t, "ABC", actual.Transactions[0].Info.SubType)
			},
		},
	}

	ctn := ittest.NewContainer(t)

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			gctx := testutil.NewContextWithRecorder()
			transactions := tc.prepare(tc.req)
			for idx := range transactions {
				mNow(&transactions[idx])
			}

			col := ctn.DBConnectionForTest.Database().Collection("transactions")
			mongotest.MongoDBClearData(t, col)
			mongotest.MongoDBPrepareData(t, col, transactions...)
			gctx.SetGET("/v1/internal/driver/%s/transactions/by-trans-ref-id/%s", tc.req.driverID, tc.req.transRefID)
			q := url.Values{}
			for k, v := range tc.req.queryParams {
				q.Add(k, v)
			}
			gctx.SetQuery(q.Encode())

			// When
			gctx.Send(ctn.GinEngineRouter)

			// Then
			tc.expect(t, ctn, tc.req, gctx)
		})
	}
}
