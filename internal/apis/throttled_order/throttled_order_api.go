package throttled_order

import (
	"errors"

	"github.com/gin-gonic/gin"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/apiutil"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

type ThrottledOrderAPI struct {
	throttledOrderService service.ThrottledOrderService
}

func ProvideThrottledOrderAPI(throttledOrderService service.ThrottledOrderService) *ThrottledOrderAPI {
	return &ThrottledOrderAPI{
		throttledOrderService: throttledOrderService,
	}
}

func (toa *ThrottledOrderAPI) UpdateMpShouldPickupAt(ctx *gin.Context) {
	var req UpdateMpShouldPickupAtRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}
	if req.MpID == "" {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.New("empty mp id")))
		return
	}

	err := toa.throttledOrderService.UpdateMpShouldPickupAt(ctx, req.MpID, req.ShouldPickupAt)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(ctx, UpdateMpShouldPickupAtResponse{})
}

func (toa *ThrottledOrderAPI) UpdateMpShouldPickupAtAndInvalidate(ctx *gin.Context) {
	var req UpdateMpShouldPickupAtAndInvalidateRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}
	if req.MpID == "" {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.New("empty mp id")))
		return
	}
	err := toa.throttledOrderService.UpdateMpShouldPickupAtAndInvalidate(ctx, req.MpID, req.ShouldPickupAt)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}
	apiutil.OK(ctx, UpdateMpShouldPickupAtAndInvalidateResponse{})
}

func (toa *ThrottledOrderAPI) Find(ctx *gin.Context) {
	var req FindRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, err))
		return
	}

	if req.Query.ZoneID == "" && len(req.Query.ObjectIDs) == 0 && len(req.Query.OrderIDs) == 0 {
		apiutil.ErrBadRequest(ctx, apiutil.NewFromError(api.ERRCODE_INVALID_REQUEST, errors.New("zone id, object ids, and order ids are all empty")))
		return
	}

	query := req.Query.ToThrottledOrderQuery()
	throttledOrders, err := toa.throttledOrderService.Find(ctx, query, req.Skip, req.Limit)
	if err != nil {
		apiutil.ErrInternalError(ctx, apiutil.NewFromError(api.ERRCODE_INTERNAL_ERROR, err))
		return
	}

	apiutil.OK(ctx, FindResponse{
		ThrottledOrders: toThrottledOrderResList(throttledOrders),
	})
}
