package cron

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
)

func TestCitiItemizeReportTask_Save(t *testing.T) {
	t.Run("save file to vos and database", func(t *testing.T) {
		task, deps, finish := newCitiItemizeReportTask(t)
		defer finish()

		b := []byte(fakeItemize())

		deps.tpRepo.EXPECT().InsertAll(gomock.Any(), gomock.Any()).Return(nil)
		deps.vos.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

		task.Save(context.Background(), b, IntraFileName())
	})

	t.Run("save file to vos when insert all database error retry with create all", func(t *testing.T) {
		task, deps, finish := newCitiItemizeReportTask(t)
		defer finish()

		b := []byte(fakeItemize())

		deps.tpRepo.EXPECT().InsertAll(gomock.Any(), gomock.Any()).Return(errors.New("error"))
		deps.tpRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return([]string{})
		deps.vos.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

		task.Save(context.Background(), b, IntraFileName())
	})

	t.Run("save file to vos when insert all database error retry with create all error", func(t *testing.T) {
		task, deps, finish := newCitiItemizeReportTask(t)
		defer finish()

		b := []byte(fakeItemize())

		deps.tpRepo.EXPECT().InsertAll(gomock.Any(), gomock.Any()).Return(errors.New("error"))
		deps.tpRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).Return([]string{"ba19a90d4da2441da8778c2d35f73fc9"})
		deps.vos.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

		task.Save(context.Background(), b, IntraFileName())
	})

	t.Run("save file to vos only if it's EOD file", func(t *testing.T) {
		task, deps, finish := newCitiItemizeReportTask(t)
		defer finish()

		b := []byte(fakeItemize())

		deps.vos.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)

		task.Save(context.Background(), b, EodFileName())
	})

	t.Run("not call insert db when no data", func(t *testing.T) {
		task, deps, finish := newCitiItemizeReportTask(t)
		defer finish()

		b := []byte(fakeUnSuccessItemize())

		deps.vos.EXPECT().UploadTOVOSForInternal(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)

		task.Save(context.Background(), b, IntraFileName())
	})
}

func TestCitiItemizeReportTask_helperFn(t *testing.T) {
	t.Run("isINTRAFile", func(t *testing.T) {
		testCases := []struct {
			fileName string
			expected bool
		}{
			{
				fileName: "yyy_INTRA_xxx",
				expected: true,
			},
			{
				fileName: "yyy__INTRA__xxx",
				expected: true,
			},
			{
				fileName: "yyy_ INTRA__xxx",
				expected: false,
			},
			{
				fileName: "yyy INTRA xxx",
				expected: false,
			},
			{
				fileName: "yyyINTRAxxx",
				expected: false,
			},
		}

		for _, c := range testCases {
			t.Run(c.fileName, func(t *testing.T) {
				cit := &CitiItemizeReportTask{}
				r := cit.isINTRAFile(c.fileName)
				require.Equal(t, c.expected, r)
			})
		}
	})

	t.Run("isEODFile", func(t *testing.T) {

		testCases := []struct {
			fileName string
			expected bool
		}{
			{
				fileName: "yyy_EOD_xxx",
				expected: true,
			},
			{
				fileName: "yyy__EOD__xxx",
				expected: true,
			},
			{
				fileName: "yyy_ EOD__xxx",
				expected: false,
			},
			{
				fileName: "yyy EOD xxx",
				expected: false,
			},
			{
				fileName: "yyyEODxxx",
				expected: false,
			},
		}

		for _, c := range testCases {
			t.Run(c.fileName, func(t *testing.T) {
				cit := &CitiItemizeReportTask{}
				r := cit.isEODFile(c.fileName)
				require.Equal(t, c.expected, r)
			})
		}
	})

	t.Run("parsePostingTimeStamp", func(t *testing.T) {
		testCases := []struct {
			name     string
			time     string
			expected string
		}{
			{
				name:     "valid time format",
				time:     "2023-04-27 16:02:07.061",
				expected: "2023-04-27T16:02:07.061Z",
			},
			{
				name:     "invalid time format",
				time:     "2023-04-2716:02:07.061",
				expected: "0001-01-01T00:00:00Z",
			},
		}

		for _, c := range testCases {
			t.Run(c.name, func(t *testing.T) {
				cit := &CitiItemizeReportTask{}
				r := cit.parsePostingTimeStamp(c.time)
				res := r.Format(time.RFC3339Nano)
				require.Equal(t, c.expected, res)
			})
		}
	})

	t.Run("isSuccessTransaction", func(t *testing.T) {
		testCases := []struct {
			name   string
			val    model.CitiRawTransactionEntry
			expect bool
		}{
			{
				name: "success",
				val: model.CitiRawTransactionEntry{
					Status:           "ACCC",
					StatusDesc:       "ACCC",
					PostingTimeStamp: "2023-05-09 17:02:03.836",
				},
				expect: true,
			},
			{
				name: "not success",
				val: model.CitiRawTransactionEntry{
					Status:           "ACCC",
					StatusDesc:       "ACWP",
					PostingTimeStamp: "2023-05-09 17:02:03.836",
				},
				expect: false,
			},
			{
				name: "not success",
				val: model.CitiRawTransactionEntry{
					Status:           "ACWP",
					StatusDesc:       "ACCC",
					PostingTimeStamp: "2023-05-09 17:02:03.836",
				},
				expect: false,
			},
			{
				name: "not success",
				val: model.CitiRawTransactionEntry{
					Status:           "ACCC",
					StatusDesc:       "ACCC",
					PostingTimeStamp: "",
				},
				expect: false,
			},
			{
				name: "not success",
				val: model.CitiRawTransactionEntry{
					Status:           "ACWP",
					StatusDesc:       "ACWP",
					PostingTimeStamp: "",
				},
				expect: false,
			},
		}

		for _, c := range testCases {
			t.Run(c.name, func(t *testing.T) {
				cit := &CitiItemizeReportTask{
					cfg: CitiItemizeReportTaskConfig{
						CitiItemizeSuccessCode: "ACCC",
					},
				}
				r := cit.isSuccessTransaction(c.val)
				require.Equal(t, c.expect, r, fmt.Sprintf("%v", c.val))
			})
		}
	})
}

type CitiItemizeDeps struct {
	cfg    CitiItemizeReportTaskConfig
	vos    *mock_service.MockVOSService
	tpRepo *mock_repository.MockTopupCreditReportRepository
}

func newCitiItemizeReportTask(t gomock.TestReporter) (*CitiItemizeReportTask, *CitiItemizeDeps, func()) {
	ctrl := gomock.NewController(t)
	deps := &CitiItemizeDeps{
		cfg: CitiItemizeReportTaskConfig{
			CitiItemizeSuccessCode: "ACCC",
			InsertDbBatchSize:      1000,
		},
		vos:    mock_service.NewMockVOSService(ctrl),
		tpRepo: mock_repository.NewMockTopupCreditReportRepository(ctrl),
	}
	return ProvideCitiItemizeReportTask(deps.vos, deps.tpRepo, deps.cfg), deps, func() {
		ctrl.Finish()
	}
}

func fakeItemize() string {
	return `{
	"data_dictionary": {
		"Additional_Info_01": "CHAR",
		"Additional_Info_02": "CHAR",
		"Additional_Info_03": "CHAR",
		"Additional_Info_04": "CHAR",
		"Additional_Info_05": "CHAR",
		"Additional_Info_06": "CHAR",
		"Additional_Info_07": "CHAR",
		"Additional_Info_08": "CHAR",
		"Additional_Info_09": "CHAR",
		"Additional_Info_10": "CHAR",
		"Bill_Ref_1": "CHAR",
		"Bill_Ref_2": "CHAR",
		"Bill_Ref_3": "CHAR",
		"Citibank_Ref_Number": "CHAR",
		"Clearing_Resp_Code": "CHAR",
		"Clearing_Resp_Desc": "CHAR",
		"Clearing_System_Reference": "CHAR",
		"Country_Code": "CHAR",
		"Credit_Account_Ccy": "CHAR",
		"Creditor_Agent_Clearing_Sys_Member_Id": "CHAR",
		"Creditor_Merchant_Type": "CHAR",
		"Creditor_Tax_Id": "CHAR",
		"Debit_Account_Ccy": "CHAR",
		"Debtor_Agent_Clearing_Sys_Member_Id": "CHAR",
		"Debtor_Tax_Id": "CHAR",
		"Dlr_Code": "CHAR",
		"Due_Date_Of_Bill": "DATETIME",
		"End_To_End_Id": "CHAR",
		"Instruction_Id": "CHAR",
		"Method_Of_Tax": "CHAR",
		"Payee_Account_Number": "CHAR",
		"Payee_Account_Type": "CHAR",
		"Payee_Bank_Id": "CHAR",
		"Payee_Bank_Info_01": "CHAR",
		"Payee_Bank_Info_02": "CHAR",
		"Payee_Bank_Info_03": "CHAR",
		"Payee_Info_01": "CHAR",
		"Payee_Info_02": "CHAR",
		"Payee_Info_03": "CHAR",
		"Payee_Proxy_Id_01": "CHAR",
		"Payee_Proxy_Id_02": "CHAR",
		"Payee_Proxy_Id_03": "CHAR",
		"Payer_Account_Number": "CHAR",
		"Payer_Bank_Id": "CHAR",
		"Payer_Bank_Info_1": "CHAR",
		"Payer_Bank_Info_2": "CHAR",
		"Payer_Bank_Info_3": "CHAR",
		"Payer_Id_Bin": "CHAR",
		"Payer_Id_Number": "CHAR",
		"Payer_Info_01": "CHAR",
		"Payer_Info_02": "CHAR",
		"Payer_Info_03": "CHAR",
		"Payer_Proxy_Id_01": "CHAR",
		"Payer_Proxy_Id_02": "CHAR",
		"Payer_Proxy_Id_03": "CHAR",
		"Payment_Details": "CHAR",
		"Payment_Ref": "CHAR",
		"Payment_Source": "CHAR",
		"Payment_System": "CHAR",
		"Payment_Type": "CHAR",
		"Posting_Ref_Number": "CHAR",
		"Posting_Time_Stamp": "DATETIME",
		"Processing_Code": "CHAR",
		"Settlement_Amount": "FLOAT",
		"Status": "CHAR",
		"Status_Desc": "CHAR",
		"Tax_Rate_Vat": "FLOAT",
		"Tax_Rate_Wht": "FLOAT",
		"Total_Tax_Amount": "FLOAT",
		"Total_Tax_Amount_Vat": "FLOAT",
		"Total_Tax_Amount_Wht": "FLOAT",
		"Trans_Completion_Date": "DATE",
		"Trans_Received_Date": "DATE",
		"Trans_Received_Time": "TIME",
		"Transaction_Amount": "FLOAT",
		"Transaction_Id": "CHAR",
		"UETR": "CHAR",
		"Ultimate_Creditor_Id_Type_And_Id": "CHAR",
		"Ultimate_Creditor_Name": "CHAR",
		"Ultimate_Debtor_Id_Type_And_Id": "CHAR",
		"Ultimate_Debtor_Name": "CHAR",
		"Value_Date": "DATE"
	},
	"transaction_entries": [{
		"Trans_Completion_Date": "27-Apr-23",
		"Trans_Received_Date": "27-Apr-23",
		"Trans_Received_Time": "15.46.21",
		"Country_Code": "THX1",
		"Payment_System": "PROMPTPAY",
		"Payment_Type": "Incoming IP",
		"Payment_Source": "Clearing",
		"Citibank_Ref_Number": "ABH9Y81229732799",
		"Clearing_System_Reference": "142179",
		"End_To_End_Id": "********",
		"Instruction_Id": "********",
		"Transaction_Id": "142179",
		"UETR": "ba19a90d4da2441da8778c2d35f73fc9",
		"Payer_Proxy_Id_01": "",
		"Payer_Proxy_Id_02": "",
		"Payer_Proxy_Id_03": "",
		"Payer_Account_Number": "**********",
		"Payer_Info_01": "นาย ณัฐพล ศีตะสุทธิพันธุ์",
		"Payer_Info_02": "",
		"Payer_Info_03": "",
		"Debtor_Tax_Id": "",
		"Payer_Bank_Id": "004",
		"Debtor_Agent_Clearing_Sys_Member_Id": "004",
		"Payer_Bank_Info_1": "",
		"Payer_Bank_Info_2": "",
		"Payer_Bank_Info_3": "",
		"Payer_Id_Bin": "",
		"Payer_Id_Number": "**********",
		"Payee_Account_Type": "Virtual",
		"Payee_Proxy_Id_01": "",
		"Payee_Proxy_Id_02": "",
		"Payee_Proxy_Id_03": "",
		"Payee_Account_Number": "**********",
		"Payee_Info_01": "LINE MAN (THAILAND) CO., LTD.",
		"Payee_Info_02": "",
		"Payee_Info_03": "",
		"Creditor_Tax_Id": "",
		"Creditor_Merchant_Type": "6007",
		"Payee_Bank_Id": "017",
		"Creditor_Agent_Clearing_Sys_Member_Id": "017",
		"Payee_Bank_Info_01": "",
		"Payee_Bank_Info_02": "",
		"Payee_Bank_Info_03": "",
		"Value_Date": "27-Apr-23",
		"Transaction_Amount": "1.0",
		"Debit_Account_Ccy": "THB",
		"Credit_Account_Ccy": "THB",
		"Settlement_Amount": "1.0",
		"Method_Of_Tax": "",
		"Total_Tax_Amount": "",
		"Tax_Rate_Wht": "",
		"Total_Tax_Amount_Wht": "",
		"Tax_Rate_Vat": "",
		"Total_Tax_Amount_Vat": "",
		"Status": "ACCC",
		"Status_Desc": "ACCC",
		"Clearing_Resp_Code": "",
		"Clearing_Resp_Desc": "",
		"Posting_Ref_Number": "0245A2B222159EBE",
		"Posting_Time_Stamp": "2023-04-27 16:02:07.061",
		"Dlr_Code": "",
		"Bill_Ref_1": "",
		"Bill_Ref_2": "",
		"Bill_Ref_3": "",
		"Payment_Ref": "",
		"Due_Date_Of_Bill": "",
		"Processing_Code": "481000",
		"Payment_Details": "",
		"Ultimate_Creditor_Name": "",
		"Ultimate_Debtor_Name": "",
		"Ultimate_Creditor_Id_Type_And_Id": "",
		"Ultimate_Debtor_Id_Type_And_Id": "",
		"Additional_Info_01": "",
		"Additional_Info_02": "",
		"Additional_Info_03": "",
		"Additional_Info_04": "",
		"Additional_Info_05": "",
		"Additional_Info_06": "",
		"Additional_Info_07": "",
		"Additional_Info_08": "",
		"Additional_Info_09": "",
		"Additional_Info_10": ""
	}]
}`
}

func fakeUnSuccessItemize() string {
	return `{
	"data_dictionary": {
		"Additional_Info_01": "CHAR",
		"Additional_Info_02": "CHAR",
		"Additional_Info_03": "CHAR",
		"Additional_Info_04": "CHAR",
		"Additional_Info_05": "CHAR",
		"Additional_Info_06": "CHAR",
		"Additional_Info_07": "CHAR",
		"Additional_Info_08": "CHAR",
		"Additional_Info_09": "CHAR",
		"Additional_Info_10": "CHAR",
		"Bill_Ref_1": "CHAR",
		"Bill_Ref_2": "CHAR",
		"Bill_Ref_3": "CHAR",
		"Citibank_Ref_Number": "CHAR",
		"Clearing_Resp_Code": "CHAR",
		"Clearing_Resp_Desc": "CHAR",
		"Clearing_System_Reference": "CHAR",
		"Country_Code": "CHAR",
		"Credit_Account_Ccy": "CHAR",
		"Creditor_Agent_Clearing_Sys_Member_Id": "CHAR",
		"Creditor_Merchant_Type": "CHAR",
		"Creditor_Tax_Id": "CHAR",
		"Debit_Account_Ccy": "CHAR",
		"Debtor_Agent_Clearing_Sys_Member_Id": "CHAR",
		"Debtor_Tax_Id": "CHAR",
		"Dlr_Code": "CHAR",
		"Due_Date_Of_Bill": "DATETIME",
		"End_To_End_Id": "CHAR",
		"Instruction_Id": "CHAR",
		"Method_Of_Tax": "CHAR",
		"Payee_Account_Number": "CHAR",
		"Payee_Account_Type": "CHAR",
		"Payee_Bank_Id": "CHAR",
		"Payee_Bank_Info_01": "CHAR",
		"Payee_Bank_Info_02": "CHAR",
		"Payee_Bank_Info_03": "CHAR",
		"Payee_Info_01": "CHAR",
		"Payee_Info_02": "CHAR",
		"Payee_Info_03": "CHAR",
		"Payee_Proxy_Id_01": "CHAR",
		"Payee_Proxy_Id_02": "CHAR",
		"Payee_Proxy_Id_03": "CHAR",
		"Payer_Account_Number": "CHAR",
		"Payer_Bank_Id": "CHAR",
		"Payer_Bank_Info_1": "CHAR",
		"Payer_Bank_Info_2": "CHAR",
		"Payer_Bank_Info_3": "CHAR",
		"Payer_Id_Bin": "CHAR",
		"Payer_Id_Number": "CHAR",
		"Payer_Info_01": "CHAR",
		"Payer_Info_02": "CHAR",
		"Payer_Info_03": "CHAR",
		"Payer_Proxy_Id_01": "CHAR",
		"Payer_Proxy_Id_02": "CHAR",
		"Payer_Proxy_Id_03": "CHAR",
		"Payment_Details": "CHAR",
		"Payment_Ref": "CHAR",
		"Payment_Source": "CHAR",
		"Payment_System": "CHAR",
		"Payment_Type": "CHAR",
		"Posting_Ref_Number": "CHAR",
		"Posting_Time_Stamp": "DATETIME",
		"Processing_Code": "CHAR",
		"Settlement_Amount": "FLOAT",
		"Status": "CHAR",
		"Status_Desc": "CHAR",
		"Tax_Rate_Vat": "FLOAT",
		"Tax_Rate_Wht": "FLOAT",
		"Total_Tax_Amount": "FLOAT",
		"Total_Tax_Amount_Vat": "FLOAT",
		"Total_Tax_Amount_Wht": "FLOAT",
		"Trans_Completion_Date": "DATE",
		"Trans_Received_Date": "DATE",
		"Trans_Received_Time": "TIME",
		"Transaction_Amount": "FLOAT",
		"Transaction_Id": "CHAR",
		"UETR": "CHAR",
		"Ultimate_Creditor_Id_Type_And_Id": "CHAR",
		"Ultimate_Creditor_Name": "CHAR",
		"Ultimate_Debtor_Id_Type_And_Id": "CHAR",
		"Ultimate_Debtor_Name": "CHAR",
		"Value_Date": "DATE"
	},
	"transaction_entries": [{
		"Trans_Completion_Date": "27-Apr-23",
		"Trans_Received_Date": "27-Apr-23",
		"Trans_Received_Time": "15.46.21",
		"Country_Code": "THX1",
		"Payment_System": "PROMPTPAY",
		"Payment_Type": "Incoming IP",
		"Payment_Source": "Clearing",
		"Citibank_Ref_Number": "ABH9Y81229732799",
		"Clearing_System_Reference": "142179",
		"End_To_End_Id": "********",
		"Instruction_Id": "********",
		"Transaction_Id": "142179",
		"UETR": "ba19a90d4da2441da8778c2d35f73fc9",
		"Payer_Proxy_Id_01": "",
		"Payer_Proxy_Id_02": "",
		"Payer_Proxy_Id_03": "",
		"Payer_Account_Number": "**********",
		"Payer_Info_01": "นาย ณัฐพล ศีตะสุทธิพันธุ์",
		"Payer_Info_02": "",
		"Payer_Info_03": "",
		"Debtor_Tax_Id": "",
		"Payer_Bank_Id": "004",
		"Debtor_Agent_Clearing_Sys_Member_Id": "004",
		"Payer_Bank_Info_1": "",
		"Payer_Bank_Info_2": "",
		"Payer_Bank_Info_3": "",
		"Payer_Id_Bin": "",
		"Payer_Id_Number": "**********",
		"Payee_Account_Type": "Virtual",
		"Payee_Proxy_Id_01": "",
		"Payee_Proxy_Id_02": "",
		"Payee_Proxy_Id_03": "",
		"Payee_Account_Number": "**********",
		"Payee_Info_01": "LINE MAN (THAILAND) CO., LTD.",
		"Payee_Info_02": "",
		"Payee_Info_03": "",
		"Creditor_Tax_Id": "",
		"Creditor_Merchant_Type": "6007",
		"Payee_Bank_Id": "017",
		"Creditor_Agent_Clearing_Sys_Member_Id": "017",
		"Payee_Bank_Info_01": "",
		"Payee_Bank_Info_02": "",
		"Payee_Bank_Info_03": "",
		"Value_Date": "27-Apr-23",
		"Transaction_Amount": "1.0",
		"Debit_Account_Ccy": "THB",
		"Credit_Account_Ccy": "THB",
		"Settlement_Amount": "1.0",
		"Method_Of_Tax": "",
		"Total_Tax_Amount": "",
		"Tax_Rate_Wht": "",
		"Total_Tax_Amount_Wht": "",
		"Tax_Rate_Vat": "",
		"Total_Tax_Amount_Vat": "",
		"Status": "ACCC",
		"Status_Desc": "ACWP",
		"Clearing_Resp_Code": "",
		"Clearing_Resp_Desc": "",
		"Posting_Ref_Number": "0245A2B222159EBE",
		"Posting_Time_Stamp": "",
		"Dlr_Code": "",
		"Bill_Ref_1": "",
		"Bill_Ref_2": "",
		"Bill_Ref_3": "",
		"Payment_Ref": "",
		"Due_Date_Of_Bill": "",
		"Processing_Code": "481000",
		"Payment_Details": "",
		"Ultimate_Creditor_Name": "",
		"Ultimate_Debtor_Name": "",
		"Ultimate_Creditor_Id_Type_And_Id": "",
		"Ultimate_Debtor_Id_Type_And_Id": "",
		"Additional_Info_01": "",
		"Additional_Info_02": "",
		"Additional_Info_03": "",
		"Additional_Info_04": "",
		"Additional_Info_05": "",
		"Additional_Info_06": "",
		"Additional_Info_07": "",
		"Additional_Info_08": "",
		"Additional_Info_09": "",
		"Additional_Info_10": ""
	}]
}`
}

func EodFileName() string {
	return `LINEMAN_1_Citi_Transaction_Report_**********_EOD_Incoming_20230429035519_1_1.json`
}

func IntraFileName() string {
	return `LINEMAN_1_Citi_Transaction_Report_**********_INTRA_Incoming_20230427053154_1_1.json`
}
