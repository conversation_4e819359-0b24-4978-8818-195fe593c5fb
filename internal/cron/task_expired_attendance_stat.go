package cron

import (
	"context"
	"sort"
	"time"

	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type ExpiredAttendanceStatConfig struct {
	MaxAgeDays int `envconfig:"MAX_AGE_DAYS" default:"92"`
}

type ExpiredAttendanceStatTask struct {
	cfg ExpiredAttendanceStatConfig
	doi repository.DriverOrderInfoRepository
}

func ProvideExpiredAttendanceStatTask(cfg ExpiredAttendanceStatConfig, doi repository.DriverOrderInfoRepository) *ExpiredAttendanceStatTask {
	return &ExpiredAttendanceStatTask{cfg: cfg, doi: doi}
}

func ProvideExpiredAttendanceStatConfig() (cfg ExpiredAttendanceStatConfig) {
	envconfig.MustProcess("", &cfg)

	return
}

func (t *ExpiredAttendanceStatTask) Name() string {
	return "ExpiredAttendanceStat"
}

func (t *ExpiredAttendanceStatTask) Execute(ctx context.Context, params ...interface{}) (Result, error) {
	logrus.WithFields(logrus.Fields{
		"params": params,
	}).Info("expire attendance stat job started")

	if len(params) <= 0 {
		return Result{}, ErrInvalidParam
	}
	dateStr, ok := params[0].(string)
	if !ok {
		return Result{}, ErrInvalidParam
	}

	runningDate, err := t.parseDate(dateStr)
	if err != nil {
		return Result{}, err
	}

	result := NewResult()

	// (1) Find driver who has attendance stat
	dois, err := t.doi.FindWhoHasAttendanceStat(ctx, 0, 0, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.WithError(err).Errorf("find who has attendance stat error: %v", err)
		return *result, err
	}

	logrus.WithFields(logrus.Fields{
		"number_of_order_info": len(dois),
	}).Info("checking driver_order_info")

	// (2) Iterate driver order info
	for _, d := range dois {
		result.AddProceeds(d.DriverID)

		// (2.1) Find dates to remove where older than `maxAgeDays`
		// It's not include end date in calculation (1 day is added)
		// Ref: https://www.timeanddate.com/date/dateadded.html?d1=22&m1=1&y1=2022&type=sub&ay=&am=&aw=&ad=10&rec=
		maxAgeDate := computeMaxAgeDate(runningDate, t.cfg.MaxAgeDays)
		datesToRemove, err := getDatesToRemove(maxAgeDate, d.AttendanceStat)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"driverID":      d.DriverID,
				"datesToRemove": datesToRemove,
			}).Errorf("parse date to remove error: %v", err)
			result.AddFail(d.DriverID)
			continue
		}

		if len(datesToRemove) == 0 {
			logrus.WithFields(logrus.Fields{
				"driverID":      d.DriverID,
				"datesToRemove": datesToRemove,
				"maxAgeDate":    maxAgeDate,
			}).Info("not required to archiving attendance stat")
			result.AddSuccess(d.DriverID)
			continue
		}

		logrus.WithFields(logrus.Fields{
			"driverID":      d.DriverID,
			"datesToRemove": datesToRemove,
			"maxAgeDate":    maxAgeDate,
		}).Info("expired attendance stat")

		// (2.2) Delete attendance stat
		err = t.doi.RemoveAttendanceStats(ctx, d.DriverID, datesToRemove)
		if err != nil {
			logrus.WithError(err).WithFields(logrus.Fields{
				"driverID":      d.DriverID,
				"datesToRemove": datesToRemove,
			}).Errorf("remove attendance stat error: %v", err)
			result.AddFail(d.DriverID)
			continue
		}

		result.AddSuccess(d.DriverID)
	}

	logrus.WithFields(logrus.Fields{
		"params": params,
	}).Info("remove attendance stat job done")

	return *result, nil
}

func (t *ExpiredAttendanceStatTask) parseDate(date string) (time.Time, error) {
	if date == "yesterday" {
		return timeutil.BangkokNow().Add(-1 * 24 * time.Hour), nil
	}

	if date == "today" {
		return timeutil.BangkokNow(), nil
	}

	return time.Parse("2/1/2006", date)
}

func getDatesToRemove(maxAgeDate time.Time, atds model.AttendanceStat) ([]string, error) {
	date := []string{}
	for k := range atds {
		t, err := time.Parse(time.DateOnly, k)
		if err != nil {
			return []string{}, err
		}
		if t.Before(maxAgeDate) {
			date = append(date, k)
		}
	}
	// sort decending order
	sort.Slice(date, func(i, j int) bool {
		return date[i] > date[j]
	})
	return date, nil
}

func computeMaxAgeDate(runningDate time.Time, maxAgeDays int) time.Time {
	return runningDate.AddDate(0, 0, -maxAgeDays)
}
