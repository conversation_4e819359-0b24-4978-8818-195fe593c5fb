package cron_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment/mock_payment"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/cron"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/email/mock_email"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
)

func TestProvidePayoutWithdrawalTask(t *testing.T) {
	t.Run("should return task name correctly", func(tt *testing.T) {
		api, _, _ := newPayoutWithdrawalTask(tt, config.PaymentConfig{})
		require.Equal(tt, "PayoutWithdrawal", api.Name())
	})

	t.Run("should auto payout withdrawal correctly", func(tt *testing.T) {
		task, deps, _ := newPayoutWithdrawalTask(tt, mockConfig())

		deps.txnRepo.EXPECT().ApprovePendingFraudTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsUOBPayoutAutoApprovalTransactionEnabled.Name).Return(true)
		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsUOBPayoutEnabled.Name).Return(true)
		deps.paymentService.EXPECT().DoUobBulkProcessWithdraw(gomock.Any(), gomock.Any()).
			Return(
				&payment.UobBulkWithdrawResultRes{
					Successes: []payment.UobBulkWithdrawResult{
						{
							WithdrawRefID: "ref-1",
						},
					},
				}, nil)

		result, err := task.Execute(context.Background())
		require.NoError(tt, err)
		require.Equal(tt, 1, len(result.Success))
		require.Equal(tt, "ref-1", result.Success[0])
	})

	t.Run("should send email result when auto payout withdrawal error", func(tt *testing.T) {
		task, deps, _ := newPayoutWithdrawalTask(tt, mockConfig())

		deps.txnRepo.EXPECT().ApprovePendingFraudTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsUOBPayoutAutoApprovalTransactionEnabled.Name).Return(true)
		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsUOBPayoutEnabled.Name).Return(true)
		deps.paymentService.EXPECT().DoUobBulkProcessWithdraw(gomock.Any(), gomock.Any()).Return(&payment.UobBulkWithdrawResultRes{}, errors.New("error"))
		deps.emailSvc.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

		result, err := task.Execute(context.Background())
		require.NoError(tt, err)
		require.Equal(tt, 0, len(result.Success))
	})

	t.Run("should not process payout withdrawal when approve pending fraud transaction error", func(tt *testing.T) {
		task, deps, _ := newPayoutWithdrawalTask(tt, mockConfig())

		deps.txnRepo.EXPECT().ApprovePendingFraudTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("error"))
		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsUOBPayoutAutoApprovalTransactionEnabled.Name).Return(true)
		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsUOBPayoutEnabled.Name).Return(true)
		deps.txnRepo.EXPECT().
			FindWalletWithdraw(gomock.Any()).
			Return(nil, nil).Times(0)

		deps.paymentService.EXPECT().DoUobBulkProcessWithdraw(gomock.Any(), gomock.Any()).Return(&payment.UobBulkWithdrawResultRes{}, nil).Times(0)
		deps.emailSvc.EXPECT().Send(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(0)

		_, err := task.Execute(context.Background())
		require.Error(tt, err)
	})

	t.Run("should not run business logic when uob payout flag is disabled", func(tt *testing.T) {
		task, deps, _ := newPayoutWithdrawalTask(tt, mockConfig())

		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), gomock.Any()).Return(false)
		deps.txnRepo.EXPECT().ApprovePendingFraudTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(0)
		deps.paymentService.EXPECT().DoUobBulkProcessWithdraw(gomock.Any(), gomock.Any()).
			Return(
				&payment.UobBulkWithdrawResultRes{
					Successes: []payment.UobBulkWithdrawResult{
						{
							WithdrawRefID: "ref-1",
						},
					},
				}, nil).Times(0)

		result, err := task.Execute(context.Background())
		require.NoError(tt, err)
		require.Equal(tt, 0, len(result.Success))
	})

	t.Run("should not approve any pending transaction if auto withdrawal approval is disabled", func(tt *testing.T) {
		task, deps, _ := newPayoutWithdrawalTask(tt, mockConfig())

		deps.txnRepo.EXPECT().ApprovePendingFraudTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(0)
		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsUOBPayoutAutoApprovalTransactionEnabled.Name).Return(false)
		deps.featureflag.EXPECT().IsEnabledWithDefaultTrue(gomock.Any(), featureflag.IsUOBPayoutEnabled.Name).Return(true)
		deps.paymentService.EXPECT().DoUobBulkProcessWithdraw(gomock.Any(), gomock.Any()).
			Return(
				&payment.UobBulkWithdrawResultRes{
					Successes: []payment.UobBulkWithdrawResult{
						{
							WithdrawRefID: "ref-1",
						},
					},
				}, nil)

		result, err := task.Execute(context.Background())
		require.NoError(tt, err)
		require.Equal(tt, 1, len(result.Success))
		require.Equal(tt, "ref-1", result.Success[0])
	})

}

func mockConfig() config.PaymentConfig {
	return config.PaymentConfig{
		AutoPayoutEnabled: true,
		StartDelayTime:    -10 * time.Minute,
		EndDelayTime:      -5 * time.Minute,
	}
}

type payoutWithdrawalDeps struct {
	txnRepo        *mock_repository.MockTransactionRepository
	paymentService *mock_payment.MockPaymentService
	emailSvc       *mock_email.MockEmailService
	cfg            config.PaymentConfig
	featureflag    *mock_featureflag.MockService
}

func newPayoutWithdrawalTask(r gomock.TestReporter, cfg config.PaymentConfig) (*cron.PayoutWithdrawalTask, payoutWithdrawalDeps, func()) {
	ctrl := gomock.NewController(r)
	deps := payoutWithdrawalDeps{
		txnRepo:        mock_repository.NewMockTransactionRepository(ctrl),
		paymentService: mock_payment.NewMockPaymentService(ctrl),
		emailSvc:       mock_email.NewMockEmailService(ctrl),
		cfg:            cfg,
		featureflag:    mock_featureflag.NewMockService(ctrl),
	}
	return cron.ProvidePayoutWithdrawalTask(deps.txnRepo, deps.paymentService, deps.emailSvc, deps.cfg, deps.featureflag), deps, func() { ctrl.Finish() }
}
