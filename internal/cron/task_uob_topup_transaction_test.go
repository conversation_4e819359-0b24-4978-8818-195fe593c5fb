package cron_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	"git.wndv.co/lineman/fleet-distribution/internal/cron"
	"git.wndv.co/lineman/fleet-distribution/internal/cryptography"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
)

func TestUobTopupTransactionTask_GetTransactionInformation(t *testing.T) {
	t.Run("should get transaction information successfully", func(tt *testing.T) {
		ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			require.Equal(t, "UOBApplicationID", r.Header.Get("Application-ID"))
			require.Equal(t, "UOBApiKey", r.Header.Get("API-Key"))
			require.Equal(t, "UOBClientID", r.Header.Get("Client-ID"))
			require.NotEmpty(t, r.Header.Get("Authorization"))
			w.Header().Set("Content-Type", "application/json;charset=UTF-8")
			w.WriteHeader(http.StatusOK)
			w.Write([]byte(`{
			"ok": "yes!"
		}`))
		}))
		defer ts.Close()
		err, cfg := provideMockConfig(ts.URL)
		require.NoError(tt, err)
		api, _ := newUobTopupTransactionTask(tt, cfg)

		_, err = api.Execute(context.Background())
		require.NoError(tt, err)
	})
}

func provideMockConfig(url string) (error, partners.Config) {
	generator, err := cryptography.NewRsaGenerator(2048)
	if err != nil {
		return err, partners.Config{}
	}
	key, err := generator.GeneratePemPrivatePKCS1Key()
	cfg := partners.Config{
		LMWNPrivate:      key,
		UOBBaseUrl:       url,
		UOBApplicationID: "UOBApplicationID",
		UOBApiKey:        "UOBApiKey",
		UOBClientID:      "UOBClientID",
	}
	return err, cfg
}

func newUobTopupTransactionTask(r gomock.TestReporter, cfg partners.Config) (*cron.UobTopupTransactionTask, func()) {
	ctrl := gomock.NewController(r)
	return cron.ProvideUobTopupTransactionTask(httpclient.New(), cfg), func() { ctrl.Finish() }
}
