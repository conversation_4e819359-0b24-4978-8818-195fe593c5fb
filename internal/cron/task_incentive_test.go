package cron

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive/mock_incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment/mock_payment"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestIncentiveTask_Execute(t *testing.T) {

	t.Parallel()

	now := timeutil.BangkokNow()

	cronTime := time.Date(now.Year(), now.Month(), now.Day(), 20, 30, 0, 0, timeutil.BangkokLocation())
	yesterday := cronTime.Add(time.Hour * time.Duration(-24*1))
	ydUTC := timeutil.DateTruncate(yesterday).In(time.UTC)

	bkk := [][]geom.Coord{{
		{100.32852172851562, 13.551216510435106},
		{100.74943542480469, 13.551216510435106},
		{100.74943542480469, 13.820744675078503},
		{100.32852172851562, 13.820744675078503},
		{100.32852172851562, 13.551216510435106},
	}}

	// Nakonpathom
	pt := [][]geom.Coord{{
		{99.92889404296874, 13.664669434801446},
		{100.20355224609375, 13.664669434801446},
		{100.20355224609375, 13.862080472921035},
		{99.92889404296874, 13.862080472921035},
		{99.92889404296874, 13.664669434801446},
	}}

	bkkStop := []model.Stop{{
		Location: model.Location{Lat: 13.67267581866982, Lng: 100.58807373046875},
	}, {}}

	bkkPoint := model.Quote{
		ServiceType: model.ServiceFood,
		Routes:      bkkStop,
	}

	ptPoint := model.Quote{
		ServiceType: model.ServiceFood,
		Routes: []model.Stop{
			{
				Location: model.Location{Lat: 13.759394168442002, Lng: 100.07720947265624},
			},
			{},
		},
	}

	t.Run("run incentive correctly daily payment type", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)
		createdAt := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 00, 0, 0, time.UTC)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		orders := createFourCompletedOrders(createdAt, history, bkkPoint, ptPoint)
		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(orders,
			nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes().AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			require.Equal(tt, 100.00, infos[0].Amount.Float64())
			require.Equal(tt, 2, len(infos))
			require.Equal(tt, model.IncentiveTransactionType, infos[0].Type)
			require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
			if driverID == "driver-a" {
				require.Equal(tt, []string{"inc1"}, infos[0].IncentiveNames)
				require.ElementsMatch(tt, []string{"order-1", "order-2"}, infos[0].RefIDs)
			} else if driverID == "driver-b" {
				require.Len(tt, infos, 2)
				require.ElementsMatch(tt, []string{"order-3", "order-4"}, infos[0].RefIDs)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here %v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("run incentive correctly streak payment type", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)
		createdAt := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 00, 0, 0, time.UTC)
		completedTime2 := completedTime.AddDate(0, 0, -1)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		ar := 92.9
		cr := 0.0

		inc := incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.PeriodStreak,
			Region:       "BKK",
			Streak: incentive.Streak{
				NoOfDays:     2,
				Amount:       88,
				OrdersPerDay: 4,
			},
			DateRange: incentive.DateRange{
				Start: yesterday.AddDate(0, 0, -1),
				End:   yesterday,
			},
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active: true,
			AR:     &ar,
			CR:     &cr,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		// driver-a pass, driver-b not pass
		deps.orderInfoRepository.EXPECT().GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			[]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 completedTime2.Year(),
							Month:                completedTime2.Month(),
							Day:                  completedTime2.Day(),
							AutoAssignedAccepted: 95,
							AutoAssigned:         100,
						},
						{
							Year:                 completedTime.Year(),
							Month:                completedTime.Month(),
							Day:                  completedTime.Day(),
							AutoAssignedAccepted: 93,
							AutoAssigned:         100,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 completedTime.Year(),
							Month:                completedTime.Month(),
							Day:                  completedTime.Day(),
							AutoAssignedAccepted: 50,
							AutoAssigned:         100,
						},
					},
				},
			}, nil)

		orders := createEightCompletedOrders(createdAt, createdAt.AddDate(0, 0, -1), history, bkkPoint, ptPoint)
		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			[]string{"driver-a"}, []string{model.ServiceFood.String()}, gomock.Any(), gomock.Any()).Return(orders,
			nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			amount := infos[0].Amount
			require.Equal(tt, amount, types.NewMoney(88))
			return
		})

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 1, len(result.Success))
		require.Equal(tt, 1, len(result.Proceeds))
	})

	t.Run("bug report: run incentive must correctly streak payment type, but it's not", func(tt *testing.T) {
		// Error will be happend when order across date in UTC timezone. But it is same date in BKK timezone.
		// That's make streak incentive is invalid. The expectation is this driver should get payout.
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)
		completedTime2 := completedTime.AddDate(0, 0, -1)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		orders := []model.Order{
			{
				OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
		}

		// Bug case: the driver has completed in 2 orders in same date in BKK timezone, but it's not a same in UTC
		// For instance:
		// 	Order 0: is completed at 2024-06-16T23:00:00Z
		// 	Order 1: is completed at 2024-06-17T03:00:00Z
		// These order is a same date in BKK
		// 	Order 0: is completed at 2024-06-17T06:00:00+07:00
		// 	Order 1: is completed at 2024-06-17T10:00:00+07:00
		// The driver should get payout.
		orders[0].CreatedAt = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day()-1, 23, 00, 0, 0, time.UTC)
		orders[1].CreatedAt = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 00, 0, 0, time.UTC)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		ar := 92.9
		cr := 0.0

		inc := incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.PeriodStreak,
			Region:       "BKK",
			Streak: incentive.Streak{
				NoOfDays:     1,
				Amount:       88,
				OrdersPerDay: 2,
			},
			DateRange: incentive.DateRange{
				Start: yesterday,
				End:   yesterday,
			},
			Times: []incentive.Times{
				{
					Start: "00:00:00",
					End:   "23:59:59",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active: true,
			AR:     &ar,
			CR:     &cr,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		// driver-a pass, driver-b not pass
		deps.orderInfoRepository.EXPECT().GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(
			[]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 completedTime2.Year(),
							Month:                completedTime2.Month(),
							Day:                  completedTime2.Day(),
							AutoAssignedAccepted: 95,
							AutoAssigned:         100,
						},
						{
							Year:                 completedTime.Year(),
							Month:                completedTime.Month(),
							Day:                  completedTime.Day(),
							AutoAssignedAccepted: 93,
							AutoAssigned:         100,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 completedTime.Year(),
							Month:                completedTime.Month(),
							Day:                  completedTime.Day(),
							AutoAssignedAccepted: 50,
							AutoAssigned:         100,
						},
					},
				},
			}, nil)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			[]string{"driver-a"}, []string{model.ServiceFood.String()}, gomock.Any(), gomock.Any()).Return(orders,
			nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			amount := infos[0].Amount
			require.Equal(tt, amount, types.NewMoney(88))
			return
		})

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 1, len(result.Success))
		require.Equal(tt, 1, len(result.Proceeds))
	})

	t.Run("run incentive correctly end of period payment type", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)

			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"inc1"}, infos[0].IncentiveNames)
			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here %v", driverID))
			}

			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("run incentive correctly when have daily and end of period", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := []incentive.Incentive{
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Daily,
				Region:       "BKK",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 50,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 150,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				Active: true,
			},
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Period,
				Region:       "nakonpathom",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 100,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 350,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				Active: true,
			},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return(inc, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)

		n3 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n4 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2, n3, n4)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {

			// prepare order with each day
			ord := prepareOrderWithMinute(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" && infos[0].Amount == 50 {
				testTransaction50(tt, infos)
			} else if driverID == "driver-a" && infos[0].Amount == 100 {
				testTransaction100(tt, infos)
			} else if driverID == "driver-b" && infos[0].Amount == 150 {
				testTransaction150(tt, infos)
			} else if driverID == "driver-b" && infos[0].Amount == 350 {
				testTransaction350(tt, infos)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driver id=%v - amount=%v", driverID, infos[0].Amount))
			}
			return
		}).Times(4)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 4, len(result.Success))
		require.Equal(tt, 4, len(result.Proceeds))

	})

	t.Run("not calculate incentive if not valid end date", func(tt *testing.T) {
		cronTime := timeutil.BangkokNow()
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		twoDayAgo := cronTime.Add(time.Hour * -48)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is 2 days ago 18:30
				End: time.Date(twoDayAgo.Year(), twoDayAgo.Month(), twoDayAgo.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))
	})

	t.Run("not calculate incentive if not valid polygon", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk},
			},
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Order{
			{

				OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
			{
				OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
			{
				OrderID: "order-3", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
			{
				OrderID: "order-4", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
		},
			nil)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("valid params in GetActiveIncentiveByTime", func(tt *testing.T) {
		expectedDate := timeutil.DateTruncate(yesterday).In(time.UTC)
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, targetDate time.Time) ([]incentive.Incentive, error) {
				require.True(t, expectedDate.Equal(targetDate), fmt.Sprintf("expectedDate %v, targetDate %v", expectedDate, targetDate))
				return []incentive.Incentive{}, nil
			})

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))
	})

	t.Run("valid params call FindOrderCompletedByTimeAndDriver incentive payment type daily", func(tt *testing.T) {
		fiveDaysAgo := cronTime.Add(time.Hour * time.Duration(-24*5))
		nextFiveDay := cronTime.Add(time.Hour * 24)

		expectedFrom := timeutil.DateTruncate(yesterday).In(time.UTC)
		expectedTo := timeutil.DateCeiling(yesterday).In(time.UTC)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(fiveDaysAgo.Year(), fiveDaysAgo.Month(), fiveDaysAgo.Day(), 17, 0, 0, 0, time.UTC),
				// end yesterday 18:30
				End: time.Date(nextFiveDay.Year(), nextFiveDay.Month(), nextFiveDay.Day(), 16, 59, 59, 999, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk},
			},
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		findOrderFunc := func(ctx context.Context, from time.Time, to time.Time,
			driverIDs []string,
			serviceTypes []string, skip, limit int) ([]model.Order, error) {
			require.True(t, expectedFrom.Equal(from), fmt.Sprintf("expectedFrom %v, from %v", expectedFrom, from))
			require.True(t, expectedTo.Equal(to), fmt.Sprintf("expectedTo %v, to %v", expectedTo, to))
			return []model.Order{}, nil
		}

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(findOrderFunc)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))

	})

	t.Run("valid params when call FindOrderCompletedByTimeAndDriver incentive payment type period", func(tt *testing.T) {
		fiveDaysAgo := cronTime.Add(time.Hour * time.Duration(-24*5))

		fiveDaysAgo = timeutil.DateTruncate(fiveDaysAgo).In(time.UTC)
		expectedFrom := time.Date(fiveDaysAgo.Year(), fiveDaysAgo.Month(), fiveDaysAgo.Day(), 17, 0, 0, 0, time.UTC)
		expectedTo := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start five day ago 00:00:00
				Start: expectedFrom,
				// end yesterday 23:59:59:999
				End: expectedTo,
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk},
			},
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		findOrderFunc := func(ctx context.Context, from time.Time, to time.Time,
			driverIDs []string,
			serviceTypes []string, skip, limit int) ([]model.Order, error) {
			require.True(t, expectedFrom.Equal(from), fmt.Sprintf("expectedFrom %v, from %v", expectedFrom, from))
			require.True(t, expectedTo.Equal(to), fmt.Sprintf("expectedTo %v, to %v", expectedTo, to))
			return []model.Order{}, nil
		}

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(findOrderFunc)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))

	})

	t.Run("calculate incentive correctly when have enable ar only in period", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		ar := 80.00

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := []incentive.Incentive{
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Daily,
				Region:       "BKK",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 50,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 150,
					},
				},
				DateRange: incentive.DateRange{
					// end bkk time is yesterday 8:00
					Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), 1, 0, 0, 0, time.UTC),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				AR:     &ar,
				Active: true,
			},
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Period,
				Region:       "nakonpathom",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 100,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 350,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				AR:     &ar,
				Active: true,
			},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return(inc, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)

		n3 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n4 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)

		gomock.InOrder(n1, n2, n3, n4)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssignedAccepted: 9,
							AutoAssigned:         10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssignedAccepted: 9,
							AutoAssigned:         10,
						},
					},
				},
			}, nil).Times(2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {

			// prepare order with each day
			ord := prepareOrderWithMinute(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			amount := infos[0].Amount
			if driverID == "driver-a" && amount == 50 {
				testTransaction50(tt, infos)
			} else if driverID == "driver-a" && amount == 100 {
				testTransaction100(tt, infos)
			} else if driverID == "driver-b" && amount == 150 {
				testTransaction150(tt, infos)
			} else if driverID == "driver-b" && amount == 350 {
				testTransaction350(tt, infos)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be driverid=%v, amount=%v", driverID, amount))
			}
			return
		}).Times(4)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 4, len(result.Success))
		require.Equal(tt, 4, len(result.Proceeds))

	})

	t.Run("calculate incentive correctly when have enable cr only in period", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		cr := 30.00
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := []incentive.Incentive{
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Daily,
				Region:       "BKK",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 50,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 150,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				CR:     &cr,
				Active: true,
			},
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Period,
				Region:       "nakonpathom",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 100,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 350,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				CR:     &cr,
				Active: true,
			},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return(inc, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		n3 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n4 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2, n3, n4)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
			}, nil).Times(2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {

			// prepare order with each day
			ord := prepareOrderWithMinute(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			amount := infos[0].Amount
			if driverID == "driver-a" && amount == 50 {
				testTransaction50(tt, infos)
			} else if driverID == "driver-a" && amount == 100 {
				testTransaction100(tt, infos)
			} else if driverID == "driver-b" && amount == 150 {
				testTransaction150(tt, infos)
			} else if driverID == "driver-b" && amount == 350 {
				testTransaction350(tt, infos)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driverid=%v, amount=%v", driverID, amount))
			}
			return
		}).Times(4)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 4, len(result.Success))
		require.Equal(tt, 4, len(result.Proceeds))

	})

	t.Run("calculate incentive correctly when have enable ar and cr in period", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		task, deps, finish := newMockIncentiveTask(tt)
		ar := 80.00
		cr := 30.00
		defer finish()

		inc := []incentive.Incentive{
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Daily,
				Region:       "BKK",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 50,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 150,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				AR:     &ar,
				CR:     &cr,
				Active: true,
			},
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Period,
				Region:       "nakonpathom",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 100,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 350,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				AR:     &ar,
				CR:     &cr,
				Active: true,
			},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return(inc, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
							AutoAssigned:         10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
							AutoAssigned:         10,
						},
					},
				},
			}, nil).Times(2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {

			// prepare order with each day
			ord := prepareOrderWithMinute(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			amount := infos[0].Amount
			if driverID == "driver-a" && amount == 50 {
				testTransaction50(tt, infos)
			} else if driverID == "driver-a" && amount == 100 {
				testTransaction100(tt, infos)
			} else if driverID == "driver-b" && amount == 150 {
				testTransaction150(tt, infos)
			} else if driverID == "driver-b" && amount == 350 {
				testTransaction350(tt, infos)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driverid=%v, amount=%v", driverID, amount))
			}
			return
		}).Times(4)

		deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 4, len(result.Success))
		require.Equal(tt, 4, len(result.Proceeds))

	})

	t.Run("calculate incentive correctly when have enable ar only in daily", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)

		ar := 80.00
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			AR:     &ar,
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssignedAccepted: 9,
							AutoAssigned:         10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssignedAccepted: 9,
							AutoAssigned:         10,
						},
					},
				},
			}, nil)

		orders := []model.Order{
			{

				OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-3", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
			{
				OrderID: "order-4", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
		}
		orders[0].CreatedAt = completedTime
		orders[1].CreatedAt = completedTime
		orders[2].CreatedAt = completedTime
		orders[3].CreatedAt = completedTime

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			require.Equal(tt, 2, len(infos))
			require.Equal(tt, model.IncentiveTransactionType, infos[0].Type)
			if driverID == "driver-a" {
				require.ElementsMatch(tt, []string{"order-1", "order-2"}, infos[0].RefIDs)
			} else if driverID == "driver-b" {
				require.Len(tt, infos, 2)
				require.ElementsMatch(tt, []string{"order-3", "order-4"}, infos[0].RefIDs)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here %v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("calculate incentive correctly when have enable cr only in daily", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)

		cr := 30.00
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			CR:     &cr,
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)

		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
			}, nil)

		orders := []model.Order{
			{

				OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-3", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
			{
				OrderID: "order-4", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
		}
		orders[0].CreatedAt = completedTime
		orders[1].CreatedAt = completedTime
		orders[2].CreatedAt = completedTime
		orders[3].CreatedAt = completedTime

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(orders,
			nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			require.Equal(tt, 2, len(infos))
			require.Equal(tt, model.IncentiveTransactionType, infos[0].Type)
			if driverID == "driver-a" {
				require.ElementsMatch(tt, []string{"order-1", "order-2"}, infos[0].RefIDs)
			} else if driverID == "driver-b" {
				require.Len(tt, infos, 2)
				require.ElementsMatch(tt, []string{"order-3", "order-4"}, infos[0].RefIDs)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driverid=%v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("calculate incentive correctly when have enable ar and cr only in daily", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)

		ar := 80.00
		cr := 30.00
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			AR:     &ar,
			CR:     &cr,
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssigned:         10,
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
							AutoAssigned:         10,
						},
					},
				},
			}, nil)

		orders := []model.Order{
			{

				OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-3", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
			{
				OrderID: "order-4", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
		}
		orders[0].CreatedAt = completedTime
		orders[1].CreatedAt = completedTime
		orders[2].CreatedAt = completedTime
		orders[3].CreatedAt = completedTime

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(orders,
			nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			require.Equal(tt, 2, len(infos))
			require.Equal(tt, 100.00, infos[0].Amount.Float64())
			require.Equal(tt, model.IncentiveTransactionType, infos[0].Type)
			if driverID == "driver-a" {
				require.ElementsMatch(tt, []string{"order-1", "order-2"}, infos[0].RefIDs)
			} else if driverID == "driver-b" {
				require.ElementsMatch(tt, []string{"order-3", "order-4"}, infos[0].RefIDs)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here %v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("not calculate incentive daily when driver not match ar", func(tt *testing.T) {
		//bkk time is 10:30
		ar := 80.00
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			AR:     &ar,
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssignedAccepted: 7,
							AutoAssigned:         10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssignedAccepted: 7,
							AutoAssigned:         10,
						},
					},
				},
			}, nil)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))
	})

	t.Run("not calculate incentive daily when driver not match cr", func(tt *testing.T) {
		cr := 10.00
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			CR:     &cr,
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
			}, nil)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))
	})

	t.Run("not calculate incentive daily when driver not match ar and cr", func(tt *testing.T) {

		ar := 100.00
		cr := 10.00
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			AR:     &ar,
			CR:     &cr,
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssigned:         10,
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
							AutoAssigned:         10,
						},
					},
				},
			}, nil)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))
	})

	t.Run("not calculate incentive period when driver not match ar with multiple incentive", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		ar := 100.00

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := []incentive.Incentive{
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Daily,
				Region:       "BKK",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 50,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 150,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				AR:     &ar,
				Active: true,
			},
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Period,
				Region:       "nakonpathom",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 100,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 350,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				AR:     &ar,
				Active: true,
			},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return(inc, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil).Times(2)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil).Times(2)
		gomock.InOrder(n1, n2)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssignedAccepted: 9,
							AutoAssigned:         10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssignedAccepted: 9,
							AutoAssigned:         10,
						},
					},
				},
			}, nil).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))

	})

	t.Run("not calculate incentive period when driver not match cr with multiple incentive", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		cr := 10.00

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := []incentive.Incentive{
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Daily,
				Region:       "BKK",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 50,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 150,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				CR:     &cr,
				Active: true,
			},
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Period,
				Region:       "nakonpathom",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 100,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 350,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				CR:     &cr,
				Active: true,
			},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return(inc, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil).Times(2)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil).Times(2)
		gomock.InOrder(n1, n2)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
			}, nil).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))

	})

	t.Run("not calculate incentive period when driver not match ar and cr with multiple incentive", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		ar := 100.00
		cr := 10.00

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := []incentive.Incentive{
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Daily,
				Region:       "BKK",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 50,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 150,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				AR:     &ar,
				CR:     &cr,
				Active: true,
			},
			{
				ID:           primitive.NewObjectID(),
				ServiceTypes: []model.Service{model.ServiceFood},
				PaymentType:  incentive.Period,
				Region:       "nakonpathom",
				OrderTier: []incentive.OrderTier{
					{
						MinOrderAmount:  1,
						MaxOrderAmount:  2,
						IncentiveAmount: 100,
					},
					{
						MinOrderAmount:  3,
						MaxOrderAmount:  5,
						IncentiveAmount: 350,
					},
				},
				DateRange: incentive.DateRange{
					// start bkk time is 5 days ago 8:30
					Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
					// end bkk time is yesterday 18:30
					End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
				},
				// 9 a.m. to 12 a.m.
				Times: []incentive.Times{
					{
						Start: "09:00",
						End:   "12:00",
					},
				},
				Geometry: incentive.Geometry{
					Type:        "Multipolygon",
					Coordinates: [][][]geom.Coord{bkk, pt},
				},
				AR:     &ar,
				CR:     &cr,
				Active: true,
			},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return(inc, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil).Times(2)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil).Times(2)
		gomock.InOrder(n1, n2)

		deps.orderInfoRepository.EXPECT().
			GetDailyCountsMultipleDrivers(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DriverOrderInfo{
				{
					DriverID: "driver-a",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssigned:         10,
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
				{
					DriverID: "driver-b",
					DailyCounts: []model.DailyCount{
						{
							Year:                 yesterday.Year(),
							Month:                yesterday.Month(),
							Day:                  yesterday.Day(),
							AutoAssigned:         10,
							CancelledNotFree:     2,
							AutoAssignedAccepted: 10,
						},
					},
				},
			}, nil).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))

	})

	t.Run("calculate incentive only whitelist driver", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			WhitelistIDs: []string{"driver-a", "driver-c"},
			Active:       true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		orders := []model.Order{
			{

				OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
		}
		orders[0].CreatedAt = completedTime
		orders[1].CreatedAt = completedTime

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			require.Equal(t, 2, len(infos))
			require.Equal(t, model.IncentiveTransactionType, infos[0].Type)
			require.Equal(t, infos[0].Amount.Float64(), float64(100))
			require.ElementsMatch(t, []string{"order-1", "order-2"}, infos[0].RefIDs)
			return
		})

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 1, len(result.Success))
		require.Equal(tt, 1, len(result.Proceeds))
	})

	t.Run("calculate incentive daily correctly when not set whitelistID", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active: true,
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		orders := []model.Order{
			{

				OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: bkkPoint,
			},
			{
				OrderID: "order-3", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
			{
				OrderID: "order-4", Driver: "driver-b", Status: model.StatusCompleted,
				History: history, Quote: ptPoint,
			},
		}
		orders[0].CreatedAt = completedTime
		orders[1].CreatedAt = completedTime
		orders[2].CreatedAt = completedTime
		orders[3].CreatedAt = completedTime

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			require.Equal(t, infos[0].Amount.Float64(), float64(100))
			require.Equal(t, model.IncentiveTransactionType, infos[0].Type)
			if driverID == "driver-a" {
				require.ElementsMatch(t, []string{"order-1", "order-2"}, infos[0].RefIDs)
			} else if driverID == "driver-b" {
				require.ElementsMatch(t, []string{"order-3", "order-4"}, infos[0].RefIDs)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driverid=%v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("already paid incentive must not pay again", func(tt *testing.T) {
		//bkk time is 10:30
		completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)
		createdAt := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 00, 0, 0, time.UTC)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  10,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		orders := createFourCompletedOrders(createdAt, history, bkkPoint, ptPoint)
		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(orders,
			nil)

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(true, nil).AnyTimes().AnyTimes()

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})
}

func TestIncentiveTask_Execute_HaveBox(t *testing.T) {
	t.Parallel()
	now := timeutil.BangkokNow()

	cronTime := time.Date(now.Year(), now.Month(), now.Day(), 20, 30, 0, 0, timeutil.BangkokLocation())
	yesterday := cronTime.Add(time.Hour * time.Duration(-24*1))

	bkk := [][]geom.Coord{{
		{100.32852172851562, 13.551216510435106},
		{100.74943542480469, 13.551216510435106},
		{100.74943542480469, 13.820744675078503},
		{100.32852172851562, 13.820744675078503},
		{100.32852172851562, 13.551216510435106},
	}}

	// Nakonpathom
	pt := [][]geom.Coord{{
		{99.92889404296874, 13.664669434801446},
		{100.20355224609375, 13.664669434801446},
		{100.20355224609375, 13.862080472921035},
		{99.92889404296874, 13.862080472921035},
		{99.92889404296874, 13.664669434801446},
	}}

	bkkPoint := model.Quote{
		ServiceType: model.ServiceFood,
		Routes: []model.Stop{
			{
				Location: model.Location{Lat: 13.67267581866982, Lng: 100.58807373046875},
			},
			{},
		},
	}

	ptPoint := model.Quote{
		ServiceType: model.ServiceFood,
		Routes: []model.Stop{
			{
				Location: model.Location{Lat: 13.759394168442002, Lng: 100.07720947265624},
			},
			{},
		},
	}

	t.Run("get incentive when match have box condition", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		box := true

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Box:     &box,
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a",
				// case profile histories is empty
				Options: model.Options{BoxType: model.BoxTypeLM},
			},
			{DriverID: "driver-b",
				// case profile histories have one item
				ProfileHistories: []model.ProfileHistory{
					{
						Name:  model.ProfileHaveBoxHistoryName,
						Date:  time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day()-5, 1, 30, 0, 0, time.UTC),
						Value: true,
					},
				},
			},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.ElementsMatch(tt, []string{"inc1"}, infos[0].IncentiveNames)
			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)

			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driverid=%v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("not get incentive when not match have box", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		box := true

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Box:     &box,
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))
	})

	t.Run("get incentive when set have box is `nil`", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Box:     nil,
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.ElementsMatch(tt, []string{"inc1"}, infos[0].IncentiveNames)

			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here %v", driverID))
			}

			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("get incentive when set have box is `false`", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		box := false
		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Box:     &box,
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.ElementsMatch(tt, []string{"inc1"}, infos[0].IncentiveNames)

			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here %v", driverID))
			}

			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})
}

func TestIncentiveTask_Execute_HaveJacket(t *testing.T) {
	t.Parallel()
	now := timeutil.BangkokNow()

	cronTime := time.Date(now.Year(), now.Month(), now.Day(), 20, 30, 0, 0, timeutil.BangkokLocation())
	yesterday := cronTime.Add(time.Hour * time.Duration(-24*1))

	bkk := [][]geom.Coord{{
		{100.32852172851562, 13.551216510435106},
		{100.74943542480469, 13.551216510435106},
		{100.74943542480469, 13.820744675078503},
		{100.32852172851562, 13.820744675078503},
		{100.32852172851562, 13.551216510435106},
	}}

	// Nakonpathom
	pt := [][]geom.Coord{{
		{99.92889404296874, 13.664669434801446},
		{100.20355224609375, 13.664669434801446},
		{100.20355224609375, 13.862080472921035},
		{99.92889404296874, 13.862080472921035},
		{99.92889404296874, 13.664669434801446},
	}}

	bkkPoint := model.Quote{
		ServiceType: model.ServiceFood,
		Routes: []model.Stop{
			{
				Location: model.Location{Lat: 13.67267581866982, Lng: 100.58807373046875},
			},
			{},
		},
	}

	ptPoint := model.Quote{
		ServiceType: model.ServiceFood,
		Routes: []model.Stop{
			{
				Location: model.Location{Lat: 13.759394168442002, Lng: 100.07720947265624},
			},
			{},
		},
	}

	t.Run("get incentive when match have jacket condition", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		jacket := true

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Jacket:  &jacket,
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a",
				// case profile histories is empty
				Options: model.Options{HaveJacket: true},
			},
			{DriverID: "driver-b",
				// case profile histories have one item
				ProfileHistories: []model.ProfileHistory{
					{
						Name:  model.ProfileHaveJacketHistoryName,
						Date:  time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day()-5, 1, 30, 0, 0, time.UTC),
						Value: true,
					},
				},
			},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.ElementsMatch(tt, []string{"inc1"}, infos[0].IncentiveNames)
			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)

			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driverid=%v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("not get incentive when not match have jacket", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		jacket := true

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Jacket:  &jacket,
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))
	})

	t.Run("get incentive when jacket condition is `nil`", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Jacket:  nil,
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.ElementsMatch(tt, []string{"inc1"}, infos[0].IncentiveNames)

			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here %v", driverID))
			}

			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("get incentive when  jacket condition is `false`", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)
		jacket := false

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Jacket:  &jacket,
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{DriverID: "driver-a"},
			{DriverID: "driver-b"},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.ElementsMatch(tt, []string{"inc1"}, infos[0].IncentiveNames)

			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here %v", driverID))
			}

			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

}

func TestIncentiveTask_Execute_Streak_Payout(t *testing.T) {
	bkkStop := []model.Stop{{
		Location: model.Location{Lat: 13.67267581866982, Lng: 100.58807373046875},
	}, {}}

	bkk := [][]geom.Coord{{
		{100.32852172851562, 13.551216510435106},
		{100.74943542480469, 13.551216510435106},
		{100.74943542480469, 13.820744675078503},
		{100.32852172851562, 13.820744675078503},
		{100.32852172851562, 13.551216510435106},
	}}

	// Nakonpathom
	pt := [][]geom.Coord{{
		{99.92889404296874, 13.664669434801446},
		{100.20355224609375, 13.664669434801446},
		{100.20355224609375, 13.862080472921035},
		{99.92889404296874, 13.862080472921035},
		{99.92889404296874, 13.664669434801446},
	}}
	completedHistory := map[string]time.Time{
		string(model.StatusCompleted): {},
	}

	initialDate := time.Date(2024, 7, 7, 0, 0, 0, 0, timeutil.BangkokLocation())
	endDate := initialDate.AddDate(0, 0, 5)
	triggerDate := endDate.AddDate(0, 0, 1)
	drvID := "driver-1"

	testcases := []struct {
		name                string
		inc                 incentive.Incentive
		drv                 model.Driver
		doi                 model.DriverOrderInfo
		orders              []model.Order
		expectGetOrders     bool
		expectPayout        bool
		expectePayoutAmount types.Money
	}{{
		name: "success",
		// streak incentive: period 5 days, no of day: 2, order per day: 2, amount: 88
		// day 1; ar/cr: pass, orders: pass
		// day 2; ar/cr: pass, orders: pass
		// day 3; ar/cr: fail, orders: fail
		// day 4; ar/cr: fail, orders: fail
		// day 5; ar/cr: fail, orders: fail
		expectGetOrders:     true,
		expectPayout:        true,
		expectePayoutAmount: 88,
		inc: incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.PeriodStreak,
			Region:       "BKK",
			Streak: incentive.Streak{
				NoOfDays:     2,
				Amount:       88,
				OrdersPerDay: 2,
			},
			DateRange: incentive.DateRange{
				Start: initialDate,
				End:   endDate,
			},
			Times: []incentive.Times{
				{
					Start: "00:00:00",
					End:   "23:59:59",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active: true,
			AR:     types.Ptr(90.0),
			CR:     types.Ptr(5.0),
		},
		drv: model.Driver{
			DriverID: drvID,
		},
		doi: model.DriverOrderInfo{
			DriverID: drvID,
			DailyCounts: []model.DailyCount{
				{
					Year:                 initialDate.Year(),
					Month:                initialDate.Month(),
					Day:                  initialDate.Day(),
					AutoAssignedAccepted: 95,
					AutoAssigned:         100,
				},
				{
					Year:                 initialDate.AddDate(0, 0, 1).Year(),
					Month:                initialDate.AddDate(0, 0, 1).Month(),
					Day:                  initialDate.AddDate(0, 0, 1).Day(),
					AutoAssignedAccepted: 95,
					AutoAssigned:         100,
				},
			},
		},
		orders: []model.Order{{
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate,
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.Add(10 * time.Minute),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.AddDate(0, 0, 1),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.AddDate(0, 0, 1).Add(10 * time.Minute),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}},
	}, {
		name: "not payout: met ar/cr but order is not met",
		// streak incentive: period 5 days, no of day: 2, order per day: 2, amount: 88
		// day 1; ar/cr: pass, orders: pass
		// day 2; ar/cr: pass, orders: fail
		// day 3; ar/cr: fail, orders: fail
		// day 4; ar/cr: fail, orders: fail
		// day 5; ar/cr: fail, orders: fail
		expectGetOrders: true,
		expectPayout:    false,
		inc: incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.PeriodStreak,
			Region:       "BKK",
			Streak: incentive.Streak{
				NoOfDays:     2,
				Amount:       88,
				OrdersPerDay: 2,
			},
			DateRange: incentive.DateRange{
				Start: initialDate,
				End:   endDate,
			},
			Times: []incentive.Times{
				{
					Start: "00:00:00",
					End:   "23:59:59",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active: true,
			AR:     types.Ptr(90.0),
			CR:     types.Ptr(5.0),
		},
		drv: model.Driver{
			DriverID: drvID,
		},
		doi: model.DriverOrderInfo{
			DriverID: drvID,
			DailyCounts: []model.DailyCount{
				{
					Year:                 initialDate.Year(),
					Month:                initialDate.Month(),
					Day:                  initialDate.Day(),
					AutoAssignedAccepted: 95,
					AutoAssigned:         100,
				},
				{
					Year:                 initialDate.AddDate(0, 0, 1).Year(),
					Month:                initialDate.AddDate(0, 0, 1).Month(),
					Day:                  initialDate.AddDate(0, 0, 1).Day(),
					AutoAssignedAccepted: 95,
					AutoAssigned:         100,
				},
			},
		},
		orders: []model.Order{{
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate,
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.Add(10 * time.Minute),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.AddDate(0, 0, 1),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}},
	}, {
		name: "not payout: met ar/cr but order is not met 2",
		// streak incentive: period 5 days, no of day: 2, order per day: 2, amount: 88
		// day 1; ar/cr: pass, orders: pass
		// day 2; ar/cr: pass, orders: fail
		// day 3; ar/cr: fail, orders: pass
		// day 4; ar/cr: fail, orders: fail
		// day 5; ar/cr: fail, orders: fail
		expectGetOrders: true,
		expectPayout:    false,
		inc: incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.PeriodStreak,
			Region:       "BKK",
			Streak: incentive.Streak{
				NoOfDays:     2,
				Amount:       88,
				OrdersPerDay: 2,
			},
			DateRange: incentive.DateRange{
				Start: initialDate,
				End:   endDate,
			},
			Times: []incentive.Times{
				{
					Start: "00:00:00",
					End:   "23:59:59",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active: true,
			AR:     types.Ptr(90.0),
			CR:     types.Ptr(5.0),
		},
		drv: model.Driver{
			DriverID: drvID,
		},
		doi: model.DriverOrderInfo{
			DriverID: drvID,
			DailyCounts: []model.DailyCount{
				{
					Year:                 initialDate.Year(),
					Month:                initialDate.Month(),
					Day:                  initialDate.Day(),
					AutoAssignedAccepted: 95,
					AutoAssigned:         100,
				},
				{
					Year:                 initialDate.AddDate(0, 0, 1).Year(),
					Month:                initialDate.AddDate(0, 0, 1).Month(),
					Day:                  initialDate.AddDate(0, 0, 1).Day(),
					AutoAssignedAccepted: 95,
					AutoAssigned:         100,
				},
				{
					Year:                 initialDate.AddDate(0, 0, 2).Year(),
					Month:                initialDate.AddDate(0, 0, 2).Month(),
					Day:                  initialDate.AddDate(0, 0, 2).Day(),
					AutoAssignedAccepted: 80,
					AutoAssigned:         100,
				},
			},
		},
		orders: []model.Order{{
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate,
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.Add(10 * time.Minute),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.AddDate(0, 0, 1),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.AddDate(0, 0, 2),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}, {
			Driver: drvID,
			Quote: model.Quote{
				CreatedAt: initialDate.AddDate(0, 0, 2).Add(10 * time.Minute),
				Routes:    bkkStop,
			},
			History: completedHistory,
		}},
	}, {
		name: "not payout: ar/cr is not met, expected no get orders",
		// streak incentive: period 5 days, no of day: 2, order per day: 2, amount: 88
		// day 1; ar/cr: pass, orders: not get
		// day 2; ar/cr: fail, orders: not get
		// day 3; ar/cr: fail, orders: not get
		// day 4; ar/cr: fail, orders: not get
		// day 5; ar/cr: fail, orders: not get
		expectGetOrders: false,
		expectPayout:    false,
		inc: incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.PeriodStreak,
			Region:       "BKK",
			Streak: incentive.Streak{
				NoOfDays:     2,
				Amount:       88,
				OrdersPerDay: 2,
			},
			DateRange: incentive.DateRange{
				Start: initialDate,
				End:   endDate,
			},
			Times: []incentive.Times{
				{
					Start: "00:00:00",
					End:   "23:59:59",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Active: true,
			AR:     types.Ptr(90.0),
			CR:     types.Ptr(5.0),
		},
		drv: model.Driver{
			DriverID: drvID,
		},
		doi: model.DriverOrderInfo{
			DriverID: drvID,
			DailyCounts: []model.DailyCount{
				{
					Year:                 initialDate.Year(),
					Month:                initialDate.Month(),
					Day:                  initialDate.Day(),
					AutoAssignedAccepted: 95,
					AutoAssigned:         100,
				},
				{
					Year:                 initialDate.AddDate(0, 0, 1).Year(),
					Month:                initialDate.AddDate(0, 0, 1).Month(),
					Day:                  initialDate.AddDate(0, 0, 1).Day(),
					AutoAssignedAccepted: 80,
					AutoAssigned:         100,
				},
				{
					Year:                 initialDate.AddDate(0, 0, 2).Year(),
					Month:                initialDate.AddDate(0, 0, 2).Month(),
					Day:                  initialDate.AddDate(0, 0, 2).Day(),
					AutoAssignedAccepted: 80,
					AutoAssigned:         100,
				},
			},
		},
	}}

	for _, tc := range testcases {
		t.Run(tc.name, func(t *testing.T) {
			task, deps, finish := newMockIncentiveTask(t)
			defer finish()

			ctx := timeutil.NewContextWithTime(context.Background(), triggerDate)

			deps.incentiveRepo.EXPECT().
				GetActiveIncentiveByTime(ctx, gomock.Any()).
				Times(1).
				Return([]incentive.Incentive{tc.inc}, nil)
			deps.driverRepository.EXPECT().
				FindWithQueryAndSort(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Times(1).
				Return([]model.Driver{tc.drv}, nil)
			deps.orderInfoRepository.EXPECT().
				GetDailyCountsMultipleDrivers(ctx, []string{drvID}, initialDate, endDate).
				Times(1).
				Return([]model.DriverOrderInfo{tc.doi}, nil)
			if tc.expectGetOrders {
				deps.orderRepo.EXPECT().
					FindOrderCompletedByTimeAndDriver(ctx, initialDate, endDate, []string{drvID}, []string{model.ServiceFood.String()}, 0, 0).
					Times(1).
					Return(tc.orders, nil)
			}
			if tc.expectPayout {
				deps.drivTransService.EXPECT().
					IsIncentivePaid(ctx, tc.inc.IncentiveID, gomock.Any(), drvID).
					Times(1).
					Return(false, nil)
				deps.drivTransService.EXPECT().
					ProcessDriverTransaction(ctx, drvID, model.SystemTransactionChannel, model.IncentiveTransactionAction, model.SuccessTransactionStatus, gomock.Any(), gomock.Any(), gomock.Any()).
					Do(func(
						ctx context.Context,
						driverID string,
						channel model.TransactionChannel,
						action model.TransactionAction,
						status model.TransactionStatus,
						builder service.TransactionInfosBuilder,
						options ...func(*service.ProcessDriverTransactionOption)) {

						actualInfo, err := builder(model.DriverTransaction{})
						assert.NoError(t, err)
						assert.Equal(t, tc.expectePayoutAmount, actualInfo[0].Amount)
					}).
					Times(1).
					Return(model.DriverTransaction{}, []model.Transaction{}, nil)
			}
			deps.driverRepository.EXPECT().
				FindWithQueryAndSort(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Times(1).
				Return([]model.Driver{}, nil)

			_, err := task.Execute(ctx, "yesterday")
			assert.NoError(t, err)
		})
	}
}

func TestIncentiveTask_Execute_DriverTier(t *testing.T) {
	t.Parallel()
	now := timeutil.BangkokNow()

	cronTime := time.Date(now.Year(), now.Month(), now.Day(), 20, 30, 0, 0, timeutil.BangkokLocation())
	yesterday := cronTime.Add(time.Hour * time.Duration(-24*1))

	bkk := [][]geom.Coord{{
		{100.32852172851562, 13.551216510435106},
		{100.74943542480469, 13.551216510435106},
		{100.74943542480469, 13.820744675078503},
		{100.32852172851562, 13.820744675078503},
		{100.32852172851562, 13.551216510435106},
	}}

	// Nakonpathom
	pt := [][]geom.Coord{{
		{99.92889404296874, 13.664669434801446},
		{100.20355224609375, 13.664669434801446},
		{100.20355224609375, 13.862080472921035},
		{99.92889404296874, 13.862080472921035},
		{99.92889404296874, 13.664669434801446},
	}}

	bkkPoint := model.Quote{
		ServiceType: model.ServiceFood,
		Routes: []model.Stop{
			{
				Location: model.Location{Lat: 13.67267581866982, Lng: 100.58807373046875},
			},
			{},
		},
	}

	ptPoint := model.Quote{
		ServiceType: model.ServiceFood,
		Routes: []model.Stop{
			{
				Location: model.Location{Lat: 13.759394168442002, Lng: 100.07720947265624},
			},
			{},
		},
	}

	t.Run("get incentive when match driver tier condition", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Tiers:   []string{string(model.DriverTierStar), string(model.DriverTierMember)},
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{
				// in case profile histories is empty
				DriverID:   "driver-a",
				BaseDriver: model.BaseDriver{DriverTier: model.DriverTierStar},
			},
			{
				DriverID:   "driver-b",
				BaseDriver: model.BaseDriver{DriverTier: model.DriverTierBasic},
				ProfileHistories: []model.ProfileHistory{
					{
						Name:  model.ProfileDriverTierHistoryName,
						Date:  time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day()-5, 1, 30, 0, 0, time.UTC),
						Value: string(model.DriverTierMember),
					},
				},
			},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.ElementsMatch(tt, []string{"inc1"}, infos[0].IncentiveNames)
			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driverid=%v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("get incentive when not set driver tier condition in incentive settings", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Tiers:   []string{},
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{
				DriverID:   "driver-a",
				BaseDriver: model.BaseDriver{DriverTier: model.DriverTierPro},
			},
			{
				DriverID:   "driver-b",
				BaseDriver: model.BaseDriver{DriverTier: model.DriverTierMember},
			},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(gomock.Any(), gomock.Any(), gomock.Any(),
			gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(
			ctx context.Context,
			start time.Time,
			end time.Time,
			driverIDs []string,
			serviceTypes []string,
			skip int,
			limit int,
		) ([]model.Order, error) {
			// make sure send valid start and end time
			require.Equal(tt, fiveDayAgo.Day(), start.Day())
			require.Equal(tt, end.Day(), yesterday.Day())

			// prepare order with each day
			ord := prepareOrder(yesterday)
			orders := mockOrdersWithCreatedAt(ord, bkkPoint, ptPoint, yesterday)
			return orders, nil
		})

		deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

		deps.drivTransService.EXPECT().ProcessDriverTransaction(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).DoAndReturn(func(
			ctx context.Context,
			driverID string,
			channel model.TransactionChannel,
			action model.TransactionAction,
			status model.TransactionStatus,
			builder service.TransactionInfosBuilder,
			options ...func(*service.ProcessDriverTransactionOption),
		) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
			testTransactionChannelStatusAction(tt, channel, action, status)
			infos, _ := builder(model.DriverTransaction{})
			if driverID == "driver-a" {
				testTransaction100(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.ElementsMatch(tt, []string{"inc1"}, infos[0].IncentiveNames)

			} else if driverID == "driver-b" {
				testTransaction350(tt, infos)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
			} else {
				require.Equal(tt, true, false, fmt.Sprintf("it's should not be here driverid=%v", driverID))
			}
			return
		}).Times(2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 2, len(result.Success))
		require.Equal(tt, 2, len(result.Proceeds))
	})

	t.Run("not get incentive when not match driver tier condition in incentive settings", func(tt *testing.T) {
		fiveDayAgo := cronTime.Add(time.Hour * -120)

		task, deps, finish := newMockIncentiveTask(tt)
		defer finish()

		inc := incentive.Incentive{
			ID:           primitive.NewObjectID(),
			Name:         "inc1",
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Period,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 350,
				},
			},
			DateRange: incentive.DateRange{
				// start bkk time is 5 days ago 8:30
				Start: time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day(), 1, 30, 0, 0, time.UTC),
				// end bkk time is yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 11, 30, 0, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},

			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk, pt},
			},
			Tiers:   []string{string(model.DriverTierVip), string(model.DriverTierStar)},
			Active:  true,
			Sources: []string{"s1", "s2"},
		}

		deps.incentiveRepo.EXPECT().
			GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
			Return([]incentive.Incentive{inc}, nil)

		n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{
			{
				// in case profile histories is empty
				DriverID:   "driver-a",
				BaseDriver: model.BaseDriver{DriverTier: model.DriverTierPro},
			},
			{
				DriverID:   "driver-b",
				BaseDriver: model.BaseDriver{DriverTier: model.DriverTierBasic},
				ProfileHistories: []model.ProfileHistory{
					{
						Name:  model.ProfileDriverTierHistoryName,
						Date:  time.Date(fiveDayAgo.Year(), fiveDayAgo.Month(), fiveDayAgo.Day()-5, 1, 30, 0, 0, time.UTC),
						Value: string(model.DriverTierMember),
					},
				},
			},
		}, nil)

		n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return([]model.Driver{}, nil)
		gomock.InOrder(n1, n2)

		result, err := task.Execute(context.Background(), "yesterday")
		require.Nil(tt, err)
		require.Equal(tt, 0, len(result.Success))
		require.Equal(tt, 0, len(result.Proceeds))
	})
}

func TestIncentiveTask_validIncentiveTime(t *testing.T) {
	orderTime := make(map[string]time.Time)
	incs := []incentive.Times{
		{
			Start: "9:00:15",
			End:   "10:00:30",
		},
	}
	tests := []struct {
		name      string
		createdAt time.Time
		expect    bool
	}{
		{
			name:      "within incentive time",
			createdAt: generateTime(10, 0, 10),
			expect:    true,
		},
		{
			name:      "not within incentive time when after",
			createdAt: generateTime(10, 0, 31),
			expect:    false,
		},
		{
			name:      "not within incentive time when before",
			createdAt: generateTime(9, 0, 14),
			expect:    false,
		},
	}

	orderTime[string(model.StatusCompleted)] = generateTime(0, 0, 0)
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			require.Equal(t, tt.expect, validIncentiveTime(orderTime, incs, tt.createdAt))
		})
	}
}

func TestIncentiveTask_ExecuteWithShiftOrderType(tt *testing.T) {
	tt.Parallel()
	// initial data
	now := timeutil.BangkokNow()
	cronTime := time.Date(now.Year(), now.Month(), now.Day(), 20, 30, 0, 0, timeutil.BangkokLocation())
	yesterday := cronTime.AddDate(0, 0, -1)
	ydUTC := timeutil.DateTruncate(yesterday).In(time.UTC)

	//bkk time is 10:30
	completedTime := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)
	createdAt := time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 00, 0, 0, time.UTC)

	incentiveGenerator := func(srcOrderType incentive.OrderShiftType) incentive.Incentive {
		bkk := [][]geom.Coord{{
			{100.32852172851562, 13.551216510435106},
			{100.74943542480469, 13.551216510435106},
			{100.74943542480469, 13.820744675078503},
			{100.32852172851562, 13.820744675078503},
			{100.32852172851562, 13.551216510435106},
		}}

		return incentive.Incentive{
			Name:         "inc1",
			ID:           primitive.NewObjectID(),
			ServiceTypes: []model.Service{model.ServiceFood},
			PaymentType:  incentive.Daily,
			Region:       "BKK",
			OrderTier: []incentive.OrderTier{
				{
					MinOrderAmount:  1,
					MaxOrderAmount:  2,
					IncentiveAmount: 10,
				},
				{
					MinOrderAmount:  3,
					MaxOrderAmount:  5,
					IncentiveAmount: 100,
				},
				{
					MinOrderAmount:  6,
					MaxOrderAmount:  999,
					IncentiveAmount: 1000,
				},
			},
			DateRange: incentive.DateRange{
				// start yesterday 8:30
				Start: time.Date(ydUTC.Year(), ydUTC.Month(), ydUTC.Day(), ydUTC.Hour(), ydUTC.Minute(), ydUTC.Second(), ydUTC.Nanosecond(), ydUTC.Location()),
				// end yesterday 18:30
				End: time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 16, 59, 59, 0, time.UTC),
			},
			// 9 a.m. to 12 a.m.
			Times: []incentive.Times{
				{
					Start: "09:00",
					End:   "12:00",
				},
			},
			Geometry: incentive.Geometry{
				Type:        "Multipolygon",
				Coordinates: [][][]geom.Coord{bkk},
			},
			Active:         true,
			Sources:        []string{"s1", "s2"}, // fixed value
			OrderShiftType: srcOrderType,
		}
	}

	// 2 SHIFT, 4 NON_SHIFT
	generateOrders := func() []model.Order {
		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}

		getOrderQuotes := func(region string, srcTime time.Time, isShift bool) model.Quote {
			resRoute := []model.Stop{{
				Location: model.Location{Lat: 13.67267581866982, Lng: 100.58807373046875},
			}, {}}
			if isShift {
				resRoute[1].PriceSummary = model.PriceSummary{
					DeliveryFee: model.DeliveryFeeSummary{
						ShiftPriceValue: 1,
					},
				}
			}
			return model.Quote{
				ServiceType: model.ServiceFood,
				Routes:      resRoute,
				CreatedAt:   srcTime,
			}
		}

		resOrders := []model.Order{
			{
				OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: getOrderQuotes("bkk", createdAt, true),
				Shifts: []model.OrderShift{{ShiftId: "shift"}},
			},
			{
				OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: getOrderQuotes("bkk", createdAt, false),
				Shifts: []model.OrderShift{},
			},
			{
				OrderID: "order-3", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: getOrderQuotes("bkk", createdAt, true),
				Shifts: []model.OrderShift{{ShiftId: "shift"}, {ShiftId: "shift2"}},
			},
			{
				OrderID: "order-4", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: getOrderQuotes("bkk", createdAt, false),
				Shifts: nil,
			},
			{
				// this order is not SHIFT
				OrderID: "order-5", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: getOrderQuotes("bkk", createdAt, false),
				Shifts: []model.OrderShift{{ShiftId: "shift"}},
			},
			{
				// this order is not SHIFT
				OrderID: "order-6", Driver: "driver-a", Status: model.StatusCompleted,
				History: history, Quote: getOrderQuotes("bkk", createdAt, true),
				Shifts: []model.OrderShift{},
			},
		}
		return resOrders
	}()

	type testData struct {
		incentiveOrderType        incentive.OrderShiftType
		expectedTransactionOrders []string
		expectedIncentiveGain     float64
	}

	testTable := map[string]testData{
		"all_case": {
			incentiveOrderType:        incentive.AllOrderType,
			expectedTransactionOrders: []string{"order-1", "order-2", "order-3", "order-4", "order-5", "order-6"},
			expectedIncentiveGain:     1000,
		},
		"only_shift_case": {
			incentiveOrderType:        incentive.OnlyShiftType,
			expectedTransactionOrders: []string{"order-1", "order-3"},
			expectedIncentiveGain:     10,
		},
		"only_non_shift_case": {
			incentiveOrderType:        incentive.OnlyNonShiftType,
			expectedTransactionOrders: []string{"order-2", "order-4", "order-5", "order-6"},
			expectedIncentiveGain:     100,
		},
	}

	for testName, data := range testTable {
		testData := data
		tt.Run(testName, func(t *testing.T) {
			t.Parallel()
			task, deps, finish := newMockIncentiveTask(tt)
			defer finish()

			inc := incentiveGenerator(testData.incentiveOrderType)

			deps.incentiveRepo.EXPECT().
				GetActiveIncentiveByTime(gomock.Any(), gomock.Any()).
				Return([]incentive.Incentive{inc}, nil)

			n1 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
			).Return([]model.Driver{
				{DriverID: "driver-a"},
			}, nil)

			n2 := deps.driverRepository.EXPECT().FindWithQueryAndSort(
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
			).Return([]model.Driver{}, nil)
			gomock.InOrder(n1, n2)

			deps.orderRepo.EXPECT().FindOrderCompletedByTimeAndDriver(
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
			).Return(generateOrders, nil)

			deps.drivTransService.EXPECT().IsIncentivePaid(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(false, nil).AnyTimes()

			deps.drivTransService.EXPECT().ProcessDriverTransaction(
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
			).DoAndReturn(func(
				ctx context.Context,
				driverID string,
				channel model.TransactionChannel,
				action model.TransactionAction,
				status model.TransactionStatus,
				builder service.TransactionInfosBuilder,
				options ...func(*service.ProcessDriverTransactionOption),
			) (drvTxn model.DriverTransaction, txns []model.Transaction, err error) {
				testTransactionChannelStatusAction(tt, channel, action, status)
				infos, _ := builder(model.DriverTransaction{})
				require.Equal(tt, testData.expectedIncentiveGain, infos[0].Amount.Float64())
				require.Equal(tt, 2, len(infos))
				require.Equal(tt, model.IncentiveTransactionType, infos[0].Type)
				require.Equal(tt, []string{"s1", "s2"}, infos[0].IncentiveSources)
				require.Equal(tt, []string{"inc1"}, infos[0].IncentiveNames)
				require.ElementsMatch(tt, testData.expectedTransactionOrders, infos[0].RefIDs)
				return
			}).Times(1)

			result, err := task.Execute(context.Background(), "yesterday")
			require.Nil(tt, err)
			require.Equal(tt, 1, len(result.Success))
			require.Equal(tt, 1, len(result.Proceeds))
		})
	}
}

type deps struct {
	orderRepo             *mock_repository.MockOrderRepository
	drivTransService      *mock_payment.MockDriverTransactionService
	incentiveRepo         *mock_incentive.MockIncentiveRepository
	driverRepository      *mock_repository.MockDriverRepository
	orderInfoRepository   *mock_repository.MockDriverOrderInfoRepository
	serviceAreaRepository *mock_repository.MockServiceAreaRepository
	cfg                   IncentiveConfig
}

func generateTime(hr int, min int, sec int) time.Time {
	return time.Date(2022, 1, 1, hr, min, sec, 0, timeutil.BangkokLocation())
}

func mockOrdersWithCreatedAt(ord []model.Order, bkkPoint model.Quote, ptPoint model.Quote, yesterday time.Time) []model.Order {
	ord[0].Driver = "driver-a"
	ord[0].Quote = bkkPoint
	ord[0].CreatedAt = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 30, 0, 0, time.UTC)

	ord[1].Driver = "driver-a"
	ord[1].Quote = bkkPoint
	ord[1].CreatedAt = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 35, 0, 0, time.UTC)

	ord[2].Driver = "driver-b"
	ord[2].Quote = ptPoint
	ord[2].CreatedAt = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 40, 0, 0, time.UTC)

	ord[3].Driver = "driver-b"
	ord[3].Quote = ptPoint
	ord[3].CreatedAt = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 45, 0, 0, time.UTC)

	ord[4].Driver = "driver-b"
	ord[4].Quote = ptPoint
	ord[4].CreatedAt = time.Date(yesterday.Year(), yesterday.Month(), yesterday.Day(), 3, 50, 0, 0, time.UTC)

	return ord
}

func createFourCompletedOrders(time time.Time, history map[string]time.Time, bkkPoint model.Quote, ptPoint model.Quote) []model.Order {
	orders := []model.Order{
		{

			OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: bkkPoint,
		},
		{
			OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: bkkPoint,
		},
		{
			OrderID: "order-3", Driver: "driver-b", Status: model.StatusCompleted,
			History: history, Quote: ptPoint,
		},
		{
			OrderID: "order-4", Driver: "driver-b", Status: model.StatusCompleted,
			History: history, Quote: ptPoint,
		},
	}
	orders[0].CreatedAt = time
	orders[1].CreatedAt = time
	orders[2].CreatedAt = time
	orders[3].CreatedAt = time
	return orders
}

func createEightCompletedOrders(time time.Time, time2 time.Time, history map[string]time.Time, bkkPoint model.Quote, ptPoint model.Quote) []model.Order {
	orders := []model.Order{
		{
			OrderID: "order-1", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: bkkPoint,
		},
		{
			OrderID: "order-2", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: bkkPoint,
		},
		{
			OrderID: "order-3", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: ptPoint,
		},
		{
			OrderID: "order-4", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: ptPoint,
		},
		{

			OrderID: "order-5", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: bkkPoint,
		},
		{
			OrderID: "order-6", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: bkkPoint,
		},
		{
			OrderID: "order-7", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: ptPoint,
		},
		{
			OrderID: "order-8", Driver: "driver-a", Status: model.StatusCompleted,
			History: history, Quote: ptPoint,
		},
	}
	orders[0].CreatedAt = time2
	orders[1].CreatedAt = time2
	orders[2].CreatedAt = time2
	orders[3].CreatedAt = time2
	orders[4].CreatedAt = time
	orders[5].CreatedAt = time
	orders[6].CreatedAt = time
	orders[7].CreatedAt = time
	return orders
}

func newMockIncentiveTask(r gomock.TestReporter) (*IncentiveTask, *deps, func()) { // can pass as param
	ctrl := gomock.NewController(r)

	mockOrderRepo := mock_repository.NewMockOrderRepository(ctrl)
	mockDrivTransService := mock_payment.NewMockDriverTransactionService(ctrl)
	mockIncentive := mock_incentive.NewMockIncentiveRepository(ctrl)
	mockProfile := mock_repository.NewMockDriverRepository(ctrl)
	mockDriverOrderInfo := mock_repository.NewMockDriverOrderInfoRepository(ctrl)
	mockServiceAreaRepo := mock_repository.NewMockServiceAreaRepository(ctrl)

	deps := &deps{
		orderRepo:             mockOrderRepo,
		drivTransService:      mockDrivTransService,
		incentiveRepo:         mockIncentive,
		driverRepository:      mockProfile,
		orderInfoRepository:   mockDriverOrderInfo,
		serviceAreaRepository: mockServiceAreaRepo,
		cfg: IncentiveConfig{
			BatchSize: 500,
		},
	}

	return ProvideIncentiveTask(mockOrderRepo, mockDrivTransService, mockIncentive, mockProfile, mockServiceAreaRepo, deps.cfg, mockDriverOrderInfo), deps, func() { ctrl.Finish() }
}

func prepareOrder(dt time.Time) []model.Order {
	ord := make([]model.Order, 5, 5)
	var completedTime time.Time
	for i := 0; i < 5; i++ {
		//bkk time is 10:30
		completedTime = time.Date(dt.Year(), dt.Month(), dt.Day()-i, 3, 30, 0, 0, time.UTC)
		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}
		ord[i] = model.Order{
			OrderID: fmt.Sprintf("order-%d", i),
			Status:  model.StatusCompleted,
			History: history,
		}
	}

	return ord
}

func prepareOrderWithMinute(dt time.Time) []model.Order {
	ord := make([]model.Order, 5, 5)
	var completedTime time.Time
	for i := 0; i < 5; i++ {
		//bkk time is 10:30
		completedTime = time.Date(dt.Year(), dt.Month(), dt.Day(), 3, 30+i*5, 0, 0, time.UTC)
		history := map[string]time.Time{
			string(model.StatusCompleted): completedTime,
		}
		ord[i] = model.Order{
			OrderID: fmt.Sprintf("order-%d", i),
			Status:  model.StatusCompleted,
			History: history,
		}
	}
	return ord
}

func testTransactionChannelStatusAction(t testing.TB, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus) {
	require.Equal(t, channel, model.SystemTransactionChannel)
	require.Equal(t, action, model.IncentiveTransactionAction)
	require.Equal(t, status, model.SuccessTransactionStatus)
}

func testTransaction50(t testing.TB, infos []model.TransactionInfo) {
	require.Equal(t, 2, len(infos))
	require.Equal(t, model.IncentiveTransactionType, infos[0].Type)
	require.Equal(t, infos[0].Amount.Float64(), float64(50))
	require.ElementsMatch(t, []string{"order-0", "order-1"}, infos[0].RefIDs)
}

func testTransaction100(t testing.TB, infos []model.TransactionInfo) {
	require.Equal(t, 2, len(infos))
	require.Equal(t, model.IncentiveTransactionType, infos[0].Type)
	require.Equal(t, infos[0].Amount.Float64(), float64(100))
	require.ElementsMatch(t, []string{"order-0", "order-1"}, infos[0].RefIDs)
}

func testTransaction150(t testing.TB, infos []model.TransactionInfo) {
	require.Len(t, infos, 2)
	require.Equal(t, model.IncentiveTransactionType, infos[0].Type)
	require.Equal(t, infos[0].Amount.Float64(), float64(150))
	require.ElementsMatch(t, []string{"order-2", "order-3", "order-4"}, infos[0].RefIDs)
}

func testTransaction350(t testing.TB, infos []model.TransactionInfo) {
	require.Len(t, infos, 2)
	require.Equal(t, model.IncentiveTransactionType, infos[0].Type)
	require.Equal(t, infos[0].Amount.Float64(), float64(350))
	require.ElementsMatch(t, []string{"order-2", "order-3", "order-4"}, infos[0].RefIDs)
}
