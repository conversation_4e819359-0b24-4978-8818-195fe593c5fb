package cron

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"io"
	"reflect"
	"sort"
	"strings"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack/mock_slack"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/email/mock_email"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fpresult"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestCitiDailyReconcileTask_sendingReportsAndAutoAddTopup(t *testing.T) {
	ctx, now := context.Background(), timeutil.BangkokNow()

	const (
		idFoundAll                        = "idFoundAll"
		idOnlyTransaction                 = "idOnlyTransaction"
		idOnlyTopupCreditReport           = "idOnlyTopupCreditReport"
		idOnlyINTRA                       = "idOnlyINTRA"
		idTopupCreditReportAndINTRA       = "idTopupCreditReportAndINTRA"
		idTopupCreditReportAndTransaction = "idTopupCreditReportAndTransaction"
		idINTRAAndTransaction             = "idINTRAAndTransaction"
		idNotFoundAll                     = "idNotFoundAll"
		idNotInEODButInTransaction        = "idNotInEODButInTransaction"
	)

	getLatestEODFromIDSlice := func(ids []string) func(time.Time) ([]service.CitiTopupEODTransaction, error) {
		return func(time.Time) ([]service.CitiTopupEODTransaction, error) {
			var result []service.CitiTopupEODTransaction
			for _, id := range ids {
				result = append(result, service.CitiTopupEODTransaction{
					UETR: id,
					Raw:  model.CitiRawTransactionEntry{PayerIDNumber: "4717..."},
				})
			}
			return result, nil
		}
	}

	fetcherFromIDSlice := func(ids []string) func(time.Time, time.Time) func(chan<- service.CitiTransactionToCheck) error {
		return func(from time.Time, to time.Time) func(chan<- service.CitiTransactionToCheck) error {
			return func(resultCh chan<- service.CitiTransactionToCheck) error {
				defer close(resultCh)
				for _, id := range ids {
					resultCh <- service.CitiTransactionToCheck{TransactionId: id}
				}
				return nil
			}
		}
	}

	sorted := func(xs []string) (result []string) {
		copy(result, xs)
		sort.Strings(result)
		return
	}

	executeWithIDs := func(
		task *CitiDailyReconcileTask, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs []string,
		notifySlack func(result service.CitiTransactionDiffsResult) error,
		sendEmailReport func(service.CitiDailyReconcileReportData) error,
		upsertEOD func([]service.CitiTopupEODTransaction) error,
	) error {
		processComparisonResult := task.processComparisonResultFunc(notifySlack, sendEmailReport)
		calculateDiff := service.CalculateCitiTransactionDiffsFunc(
			ctx, now,
			fetcherFromIDSlice(intraIDs),
			fetcherFromIDSlice(topupCreditReportIDs),
			fetcherFromIDSlice(transactionIDs),
		)
		_, err := task.execute(
			now,
			getLatestEODFromIDSlice(eodIDs),
			calculateDiff,
			processComparisonResult,
			upsertEOD,
		)
		return err
	}

	executeCaptureMissingRowsAndUnseenRowsFromReport := func(task *CitiDailyReconcileTask, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs []string) ([]string, []string, error) {
		var missingRowIDs, unseenRowIDs []string
		sendSlack := func(result service.CitiTransactionDiffsResult) error { return nil }
		sendEmailReport := func(report service.CitiDailyReconcileReportData) error {
			for _, row := range report.Rows {
				if row.IsUnseen {
					unseenRowIDs = append(unseenRowIDs, row.Unseen.TransactionId)
				} else {
					missingRowIDs = append(missingRowIDs, row.Missing.UETR)
				}
			}
			return nil
		}
		upsertEOD := func([]service.CitiTopupEODTransaction) error { return nil }

		err := executeWithIDs(
			task, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs,
			sendSlack, sendEmailReport, upsertEOD,
		)
		return missingRowIDs, unseenRowIDs, err
	}

	executeCaptureIsReportSent := func(task *CitiDailyReconcileTask, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs []string) (bool, error) {
		var isReportSent bool
		sendSlack := func(result service.CitiTransactionDiffsResult) error { return nil }
		sendEmailReport := func(report service.CitiDailyReconcileReportData) error {
			isReportSent = true
			return nil
		}
		upsertEOD := func([]service.CitiTopupEODTransaction) error { return nil }

		err := executeWithIDs(
			task, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs,
			sendSlack, sendEmailReport, upsertEOD,
		)
		return isReportSent, err
	}

	executeCaptureUpsertedIDs := func(task *CitiDailyReconcileTask, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs []string) ([]string, error) {
		var upsertedIDs []string
		sendSlack := func(result service.CitiTransactionDiffsResult) error { return nil }
		sendEmailReport := func(report service.CitiDailyReconcileReportData) error { return nil }
		upsertEOD := func(eods []service.CitiTopupEODTransaction) error {
			for _, tx := range eods {
				upsertedIDs = append(upsertedIDs, tx.UETR)
			}
			return nil
		}

		err := executeWithIDs(
			task, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs,
			sendSlack, sendEmailReport, upsertEOD,
		)
		return upsertedIDs, err
	}

	t.Run("send result report - all cases", func(tt *testing.T) {
		task, _, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		eodIDs := []string{
			idFoundAll, idOnlyTransaction, idOnlyTopupCreditReport,
			idOnlyINTRA, idTopupCreditReportAndINTRA, idTopupCreditReportAndTransaction,
			idINTRAAndTransaction, idNotFoundAll,
		}
		transactionIDs := []string{
			idFoundAll, idOnlyTransaction, idTopupCreditReportAndTransaction,
			idINTRAAndTransaction, idNotInEODButInTransaction,
		}
		topupCreditReportIDs := []string{
			idFoundAll, idOnlyTopupCreditReport, idTopupCreditReportAndINTRA,
			idTopupCreditReportAndTransaction,
		}
		intraIDs := []string{
			idFoundAll, idOnlyINTRA, idTopupCreditReportAndINTRA, idINTRAAndTransaction,
		}

		missingRowIDs, unseenRowIDs, err := executeCaptureMissingRowsAndUnseenRowsFromReport(
			task, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs,
		)
		assert.NoError(tt, err)

		assert.Equal(tt, sorted([]string{ // yellow columns
			idOnlyTransaction, idOnlyTopupCreditReport, idOnlyINTRA,
			idTopupCreditReportAndINTRA, idINTRAAndTransaction, idNotFoundAll,
		}), sorted(missingRowIDs))

		// blue columns - not in EOD but in transaction
		assert.Equal(tt, sorted([]string{idNotInEODButInTransaction}), sorted(unseenRowIDs))
	})

	t.Run("auto add in topup_credit_report - all cases", func(tt *testing.T) {
		task, _, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		eodIDs := []string{
			idFoundAll, idOnlyTransaction, idOnlyTopupCreditReport,
			idOnlyINTRA, idTopupCreditReportAndINTRA, idTopupCreditReportAndTransaction,
			idINTRAAndTransaction, idNotFoundAll,
		}
		transactionIDs := []string{
			idFoundAll, idOnlyTransaction, idTopupCreditReportAndTransaction,
			idINTRAAndTransaction, idNotInEODButInTransaction,
		}
		topupCreditReportIDs := []string{
			idFoundAll, idOnlyTopupCreditReport, idTopupCreditReportAndINTRA,
			idTopupCreditReportAndTransaction,
		}
		intraIDs := []string{
			idFoundAll, idOnlyINTRA, idTopupCreditReportAndINTRA, idINTRAAndTransaction,
		}

		upsertedIDs, err := executeCaptureUpsertedIDs(
			task, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs,
		)
		assert.NoError(tt, err)

		assert.Equal(tt, sorted([]string{
			idOnlyTransaction, idOnlyINTRA, idINTRAAndTransaction, idNotFoundAll,
		}), sorted(upsertedIDs))
	})

	t.Run("don't send result report and don't auto add in topup_credit_report when no report", func(tt *testing.T) {
		task, _, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		eodIDs := []string{idFoundAll, idTopupCreditReportAndTransaction}
		transactionIDs := []string{idFoundAll, idTopupCreditReportAndTransaction}
		topupCreditReportIDs := []string{idFoundAll, idTopupCreditReportAndTransaction}
		intraIDs := []string{idFoundAll}

		isReportSent, err := executeCaptureIsReportSent(
			task, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs,
		)
		assert.NoError(tt, err)

		assert.False(tt, isReportSent)
	})

	t.Run("don't auto add in topup_credit_report but only send report when all already exists in topup_credit_report", func(tt *testing.T) {
		task, _, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		eodIDs := []string{
			idOnlyTopupCreditReport, idTopupCreditReportAndINTRA, idTopupCreditReportAndTransaction,
		}
		topupCreditReportIDs := []string{
			idOnlyTopupCreditReport, idTopupCreditReportAndINTRA, idTopupCreditReportAndTransaction,
		}
		var transactionIDs []string
		var intraIDs []string

		isReportSent, err := executeCaptureIsReportSent(
			task, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs,
		)
		assert.NoError(tt, err)

		assert.True(tt, isReportSent)
	})

	t.Run("don't auto add in topup_credit_report for case that is only in transactions but not in EOD", func(tt *testing.T) {
		task, _, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		var eodIDs []string
		transactionIDs := []string{idNotInEODButInTransaction}
		var topupCreditReportIDs []string
		var intraIDs []string

		isReportSent, err := executeCaptureIsReportSent(
			task, eodIDs, transactionIDs, topupCreditReportIDs, intraIDs,
		)
		assert.NoError(tt, err)

		assert.True(tt, isReportSent)
	})
}

func TestCitiDailyReconcileTask_execute(t *testing.T) {
	ctx := context.Background()
	t.Run("should count results", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		now := time.Date(2023, time.September, 20, 6, 2, 3, 4, timeutil.BangkokLocation())
		from := time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation())
		to := time.Date(2023, time.September, 20, 0, 4, 59, 0, timeutil.BangkokLocation())
		deps.tpRepo.EXPECT().IterateAllOccurredBetween(context.Background(), from.Add(-5*time.Minute), to).
			Return(newCursorFromSlice([]any{
				model.TopupCreditReport{TransactionID: "A", CitiInfo: model.CitiInfo{PayerIDNumber: "4717..."}},
				model.TopupCreditReport{TransactionID: "B", CitiInfo: model.CitiInfo{PayerIDNumber: "4717..."}},
			}), nil)
		deps.transactionRepo.EXPECT().IterateCitiTopupTransactionsCreatedBetween(context.Background(), from, to).
			Return(newCursorFromSlice([]any{
				model.TopupCreditReport{TransactionID: "A", CitiInfo: model.CitiInfo{PayerIDNumber: "4717..."}},
				model.TopupCreditReport{TransactionID: "C", CitiInfo: model.CitiInfo{PayerIDNumber: "4717..."}},
			}), nil)

		deps.vos.EXPECT().ListObjectsFromVOSInternal(context.Background(), "fleet_private/citi_itemize_report/2023-09-19").
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{Key: pointer(intraFileObjNameMock("2023-09-19", "20230919011532"))},
					{Key: pointer(eodFileObjNameMock("2023-09-19", "20230919033101"))},
				},
			}, nil)
		deps.vos.EXPECT().GetObject(gomock.Any(), intraFileObjNameMock("2023-09-19", "20230919011532"), gomock.Any()).
			Return(newMockTransactionsToCheck([]service.CitiTransactionToCheck{{TransactionId: "A"}, {TransactionId: "B"}}), nil)

		getEOD := func(date time.Time) ([]service.CitiTopupEODTransaction, error) {
			assert.Equal(tt, now, date)
			return []service.CitiTopupEODTransaction{
				{UETR: "A", Raw: model.CitiRawTransactionEntry{PayerIDNumber: "4717..."}},
				{UETR: "B", Raw: model.CitiRawTransactionEntry{PayerIDNumber: "4717..."}},
			}, nil
		}

		processComparisonResult := task.processComparisonResultFunc(func(result service.CitiTransactionDiffsResult) error {
			assert.Equal(tt, fpresult.Value(2), result.TopupCount())
			return nil
		}, func(data service.CitiDailyReconcileReportData) error { return nil })

		calculateDiff := service.CalculateCitiTransactionDiffsFunc(
			ctx, now,
			task.intraFetcher(ctx),
			task.topupReportFetcher(ctx, 5*time.Minute),
			task.actualFetcher(ctx, 5*time.Minute),
		)

		_, _ = task.execute(now, getEOD, calculateDiff, processComparisonResult, func([]service.CitiTopupEODTransaction) error { return nil })

	})
}

func TestCitiDailyReconcileTask_fetchActual(t *testing.T) {
	t.Run("should read from mongodb", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		from := time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation())
		to := time.Date(2023, time.September, 20, 6, 2, 3, 4, timeutil.BangkokLocation())
		deps.transactionRepo.EXPECT().IterateCitiTopupTransactionsCreatedBetween(context.Background(), from, to).
			Return(newCursorFromSlice([]any{
				citiDailyReconcileDBTransaction{Info: citiDailyReconcileDBTransactionInfo{BankRefID: "A"}},
				citiDailyReconcileDBTransaction{Info: citiDailyReconcileDBTransactionInfo{BankRefID: "B"}},
			}), nil)

		fetch := task.actualFetcher(context.Background(), 0)(from, to)
		result, err := fetchCitiDailyReconcileTransactionToCheck(fetch)

		assert.NoError(tt, err)
		assert.Equal(tt, []service.CitiTransactionToCheck{
			{TransactionId: "A"}, {TransactionId: "B"},
		}, result)
	})
}

func TestCitiDailyReconcileTask_fetchTopupReport(t *testing.T) {

	t.Run("should read from mongodb", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		from := time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation())
		to := time.Date(2023, time.September, 20, 6, 2, 3, 4, timeutil.BangkokLocation())
		deps.tpRepo.EXPECT().IterateAllOccurredBetween(context.Background(), from, to).
			Return(newCursorFromSlice([]any{
				model.TopupCreditReport{TransactionID: "A"},
				model.TopupCreditReport{TransactionID: "B"},
			}), nil)

		fetch := task.topupReportFetcher(context.Background(), 0)(from, to)
		result, err := fetchCitiDailyReconcileTransactionToCheck(fetch)

		assert.NoError(tt, err)
		assert.Equal(tt, []service.CitiTransactionToCheck{
			{TransactionId: "A"}, {TransactionId: "B"},
		}, result)
	})
}

func TestCitiDailyReconcileTask_getLatestEODTransactions(t *testing.T) {
	ctx := context.Background()
	now := time.Date(2023, time.September, 20, 1, 2, 3, 4, timeutil.BangkokLocation())
	reportDir := "fleet_private/citi_itemize_report/2023-09-20"
	intraFileName1 := intraFileObjNameMock("2023-09-20", "20230920011532")
	intraFileName2 := intraFileObjNameMock("2023-09-20", "**************")
	eodFileName1o1 := eodFileObjNameMockWithPartitionNumber("2023-09-20", "**************", 1, 1)
	eodFileName1o2 := eodFileObjNameMockWithPartitionNumber("2023-09-20", "**************", 1, 2)
	eodFileName2o2 := eodFileObjNameMockWithPartitionNumber("2023-09-20", "**************", 2, 2)
	eodData1 := []service.CitiTopupEODTransaction{
		{
			UETR:                "A",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-1",
			TransactionAmount:   10.5,
			PostingRefNumber:    "POST-1",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 08:02:03.836",
			},
		},
		{
			UETR:                "B",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-2",
			TransactionAmount:   20.75,
			PostingRefNumber:    "POST-2",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 09:02:03.836",
			},
		},
	}
	eodData2 := []service.CitiTopupEODTransaction{
		{
			UETR:                "C",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-3",
			TransactionAmount:   11.75,
			PostingRefNumber:    "POST-3",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 10:02:03.836",
			},
		},
		{
			UETR:                "D",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-4",
			TransactionAmount:   120.5,
			PostingRefNumber:    "POST-4",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 12:02:03.836",
			},
		},
	}
	eodData2WithUnsuccessfulTx := []service.CitiTopupEODTransaction{
		{
			UETR:                "C",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-3",
			TransactionAmount:   11.75,
			PostingRefNumber:    "POST-3",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 10:02:03.836",
			},
		},
		{
			UETR:                "X",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-7",
			TransactionAmount:   21.75,
			PostingRefNumber:    "POST-7",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "XXXX",
				StatusDesc:       "XXXX",
				PostingTimeStamp: "2023-09-19 11:02:03.836",
			},
		},
		{
			UETR:                "D",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-4",
			TransactionAmount:   120.5,
			PostingRefNumber:    "POST-4",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 12:02:03.836",
			},
		},
	}
	eodData2WithTxThatPayerIDNumberNotStartedWith4717 := []service.CitiTopupEODTransaction{
		{
			UETR:                "C",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-3",
			TransactionAmount:   11.75,
			PostingRefNumber:    "POST-3",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 10:02:03.836",
			},
		},
		{
			UETR:                "X",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-7",
			TransactionAmount:   21.75,
			PostingRefNumber:    "POST-7",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 12:02:03.836",
			},
		},
		{
			UETR:                "D",
			TransCompletionDate: time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
			CitibankRefNumber:   "CITI-4",
			TransactionAmount:   120.5,
			PostingRefNumber:    "POST-4",
			Raw: model.CitiRawTransactionEntry{
				PayerIDNumber:    "**********",
				Status:           "ACCC",
				StatusDesc:       "ACCC",
				PostingTimeStamp: "2023-09-19 12:02:03.836",
			},
		},
	}

	mockVOSFileList := func(deps citiDailyReconcileTaskTestDeps, dir string, fileNames []string) *gomock.Call {
		var objs []*s3.Object
		for _, fileName := range fileNames {
			objs = append(objs, &s3.Object{Key: pointer(fileName)})
		}
		return deps.vos.EXPECT().ListObjectsFromVOSInternal(ctx, reportDir).
			Return(&s3.ListObjectsOutput{Contents: objs}, nil)
	}

	mockVOSFileContent := func(deps citiDailyReconcileTaskTestDeps, fileName string, content *s3.GetObjectOutput) *gomock.Call {
		return deps.vos.EXPECT().GetObject(ctx, fileName, gomock.Any()).Return(content, nil)
	}

	resetRaw := func(eod service.CitiTopupEODTransaction) service.CitiTopupEODTransaction {
		eod.FromFile = ""
		eod.Raw = model.CitiRawTransactionEntry{}
		return eod
	}
	resetRawAll := func(eods []service.CitiTopupEODTransaction) (result []service.CitiTopupEODTransaction) {
		for i := range eods {
			result = append(result, resetRaw(eods[i]))
		}
		return
	}

	t.Run("should read an eod file from vos", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		mockVOSFileList(deps, reportDir,
			[]string{intraFileName1, eodFileName1o1, intraFileName2})

		mockVOSFileContent(deps, eodFileName1o1, newMockEODTransactions(eodData1))

		transactions, err := task.getLatestEODTransactions(ctx, now)

		assert.NoError(tt, err)
		assert.Equal(tt, resetRawAll(eodData1), resetRawAll(transactions))
	})

	t.Run("should read two eod files from vos", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		mockVOSFileList(deps, reportDir,
			[]string{intraFileName1, eodFileName1o2, intraFileName2, eodFileName2o2})

		mockVOSFileContent(deps, eodFileName1o2, newMockEODTransactions(eodData1))
		mockVOSFileContent(deps, eodFileName2o2, newMockEODTransactions(eodData2))

		transactions, err := task.getLatestEODTransactions(ctx, now)

		assert.NoError(tt, err)
		assert.Equal(tt, resetRawAll(append(eodData1, eodData2...)), resetRawAll(transactions))
	})

	t.Run("should not filter out unsuccessful transactions", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		mockVOSFileList(deps, reportDir,
			[]string{intraFileName1, eodFileName1o2, intraFileName2, eodFileName2o2})

		mockVOSFileContent(deps, eodFileName1o2, newMockEODTransactions(eodData1))
		mockVOSFileContent(deps, eodFileName2o2, newMockEODTransactions(eodData2WithUnsuccessfulTx))

		transactions, err := task.getLatestEODTransactions(ctx, now)

		assert.NoError(tt, err)
		assert.Equal(tt, resetRawAll(append(eodData1, eodData2WithUnsuccessfulTx...)), resetRawAll(transactions))
	})

	t.Run("should not filter out transactions with payer id number not started with 4717", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		mockVOSFileList(deps, reportDir,
			[]string{intraFileName1, eodFileName1o2, intraFileName2, eodFileName2o2})

		mockVOSFileContent(deps, eodFileName1o2, newMockEODTransactions(eodData1))
		mockVOSFileContent(deps, eodFileName2o2, newMockEODTransactions(eodData2WithTxThatPayerIDNumberNotStartedWith4717))

		transactions, err := task.getLatestEODTransactions(ctx, now)

		assert.NoError(tt, err)
		assert.Equal(tt, resetRawAll(append(eodData1, eodData2WithTxThatPayerIDNumberNotStartedWith4717...)), resetRawAll(transactions))
	})

	t.Run("should fail when no eod files", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		mockVOSFileList(deps, reportDir, []string{intraFileName1, intraFileName2})

		_, err := task.getLatestEODTransactions(ctx, now)

		assert.EqualError(tt, err, "EOD file not found")
	})

	t.Run("should fail when the number of eod files is less than total partition", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		mockVOSFileList(deps, reportDir, []string{intraFileName1, eodFileName1o2, intraFileName2})

		_, err := task.getLatestEODTransactions(ctx, now)

		assert.EqualError(tt, err, "some EOD file partitions are missing")
	})

}

func TestCitiDailyReconcileTask_fetchINTRA(t *testing.T) {
	t.Run("should read from vos", func(tt *testing.T) {
		task, deps, finish := newCitiDailyReconcileTaskForTest(tt)
		defer finish()

		expectedTransactions1 := []service.CitiTransactionToCheck{{TransactionId: "A"}, {TransactionId: "B"}}
		expectedTransactions2 := []service.CitiTransactionToCheck{{TransactionId: "C"}, {TransactionId: "D"}}
		expectedTransactions3 := []service.CitiTransactionToCheck{{TransactionId: "E"}, {TransactionId: "F"}}

		var expectedTransactions []service.CitiTransactionToCheck
		expectedTransactions = append(expectedTransactions, expectedTransactions1...)
		expectedTransactions = append(expectedTransactions, expectedTransactions2...)
		expectedTransactions = append(expectedTransactions, expectedTransactions3...)

		//now := time.Date(2023, time.September, 20, 1, 2, 3, 4, timeutil.BangkokLocation())
		deps.vos.EXPECT().ListObjectsFromVOSInternal(context.Background(), "fleet_private/citi_itemize_report/2023-09-19").
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{Key: pointer(intraFileObjNameMock("2023-09-19", "20230919011532"))},
					{Key: pointer(eodFileObjNameMock("2023-09-19", "20230919033101"))},
					{Key: pointer(intraFileObjNameMock("2023-09-19", "20230919053532"))},
				},
			}, nil)
		deps.vos.EXPECT().GetObject(gomock.Any(), intraFileObjNameMock("2023-09-19", "20230919011532"), gomock.Any()).
			Return(newMockTransactionsToCheck(expectedTransactions1), nil)
		deps.vos.EXPECT().GetObject(gomock.Any(), intraFileObjNameMock("2023-09-19", "20230919053532"), gomock.Any()).
			Return(newMockTransactionsToCheck(expectedTransactions2), nil)
		deps.vos.EXPECT().ListObjectsFromVOSInternal(context.Background(), "fleet_private/citi_itemize_report/2023-09-20").
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{Key: pointer(eodFileObjNameMock("2023-09-20", "**************"))},
					{Key: pointer(intraFileObjNameMock("2023-09-20", "20230920055959"))},
					{Key: pointer(intraFileObjNameMock("2023-09-20", "20230920060000"))},
				},
			}, nil)
		deps.vos.EXPECT().GetObject(gomock.Any(), intraFileObjNameMock("2023-09-20", "20230920055959"), gomock.Any()).
			Return(newMockTransactionsToCheck(expectedTransactions3), nil)

		from := time.Date(2023, time.September, 19, 0, 0, 0, 0, timeutil.BangkokLocation())
		to := time.Date(2023, time.September, 20, 5, 59, 59, 0, timeutil.BangkokLocation())

		fetch := task.intraFetcher(context.Background())(from, to)
		result, err := fetchCitiDailyReconcileTransactionToCheck(fetch)

		assert.NoError(tt, err)
		assert.Equal(tt, expectedTransactions, result)
	})
}

func TestCitiDailyReconcileTask_retryAndSleepUntilSuccess(t *testing.T) {
	ctx := context.Background()
	sleepDuration := time.Millisecond

	t.Run("should run once and return result if success", func(tt *testing.T) {
		count := 0
		f := func() (int, error) {
			count += 1
			return 123, nil
		}
		result, err := retryAndSleepUntilSuccess(ctx, 999, sleepDuration, f)

		assert.Equal(tt, 1, count)
		assert.Equal(tt, 123, result)
		assert.Nil(tt, err)
	})

	t.Run("should run twice and return second result when the first time fails", func(tt *testing.T) {
		count := 0
		f := func() (int, error) {
			count += 1
			if count == 1 {
				return 0, errors.New("boom")
			}
			return 123, nil
		}
		result, err := retryAndSleepUntilSuccess(ctx, 999, sleepDuration, f)

		assert.Equal(tt, 2, count)
		assert.Equal(tt, 123, result)
		assert.Nil(tt, err)
	})

	t.Run("should stop and return error when maxRetries is reached", func(tt *testing.T) {
		count := 0
		f := func() (int, error) {
			count += 1
			return 0, errors.New("boom")
		}
		_, err := retryAndSleepUntilSuccess(ctx, 3, sleepDuration, f)

		assert.Equal(tt, 3, count)
		assert.Equal(tt, errors.New("boom"), err)
	})
}

func TestCitiDailyReconcileTask_citiDailyReconcileDBTransaction(t *testing.T) {
	assertFieldTypeEqual := func(tt *testing.T, x, y reflect.Type, fieldName string) {
		xField, xFound := x.FieldByName(fieldName)
		yField, yFound := y.FieldByName(fieldName)
		assert.True(tt, xFound)
		assert.True(tt, yFound)
		assert.Equalf(tt, xField.Type, yField.Type, "fieldName: %s", fieldName)
	}
	assertFieldTagEqual := func(tt *testing.T, x, y reflect.Type, fieldName string) {
		xField, xFound := x.FieldByName(fieldName)
		yField, yFound := y.FieldByName(fieldName)
		assert.True(tt, xFound)
		assert.True(tt, yFound)
		assert.Equalf(tt, xField.Tag, yField.Tag, "fieldName: %s", fieldName)
	}

	t.Run("all fields are equal to model.Transaction", func(tt *testing.T) {
		txType := reflect.TypeOf(citiDailyReconcileDBTransaction{})
		mdType := reflect.TypeOf(model.Transaction{})

		assertFieldTypeEqual(tt, txType, mdType, "CreatedAt")
		assertFieldTagEqual(tt, txType, mdType, "CreatedAt")
		assertFieldTagEqual(tt, txType, mdType, "Info")
	})

	t.Run("all info fields are equal to model.TransactionInfo except TransRefID", func(tt *testing.T) {
		txType := reflect.TypeOf(citiDailyReconcileDBTransactionInfo{})
		mdType := reflect.TypeOf(model.TransactionInfo{})

		for i := 0; i < txType.NumField(); i++ {
			name := txType.Field(i).Name
			if name == "TransRefID" {
				continue
			}
			assertFieldTypeEqual(tt, txType, mdType, name)
			assertFieldTagEqual(tt, txType, mdType, name)
		}
	})

	t.Run("TransRefID should have the same tag", func(tt *testing.T) {
		txType := reflect.TypeOf(citiDailyReconcileDBTransactionInfo{})
		mdType := reflect.TypeOf(model.TransactionInfo{})

		assertFieldTagEqual(tt, txType, mdType, "TransRefID")
	})
}

type citiDailyReconcileTaskTestDeps struct {
	vos             *mock_service.MockVOSService
	tpRepo          *mock_repository.MockTopupCreditReportRepository
	slack           *mock_slack.MockSlack
	transactionRepo *mock_repository.MockTransactionRepository
}

func newCitiDailyReconcileTaskForTest(t *testing.T) (*CitiDailyReconcileTask, citiDailyReconcileTaskTestDeps, context.CancelFunc) {
	ctrl := gomock.NewController(t)
	deps := citiDailyReconcileTaskTestDeps{
		vos:             mock_service.NewMockVOSService(ctrl),
		transactionRepo: mock_repository.NewMockTransactionRepository(ctrl),
		tpRepo:          mock_repository.NewMockTopupCreditReportRepository(ctrl),
		slack:           mock_slack.NewMockSlack(ctrl),
	}
	task := ProvideCitiDailyReconcileTask(
		deps.vos,
		deps.transactionRepo,
		deps.tpRepo,
		deps.slack,
		CitiDailyReconcileTaskConfig{
			VOSReportFolder: CitiDailyReconcileTaskTestVOSReportFolder,
		},
		config.GlobalConfig{
			EnvName: "TEST",
		},
		mock_email.NewMockEmailService(ctrl),
	)
	return task, deps, ctrl.Finish
}

type sliceCursor struct {
	data []any
	i    int
}

func newCursorFromSlice(data []any) repository.Cursor {
	return &sliceCursor{
		i:    -1,
		data: data,
	}
}

func (s *sliceCursor) Close(context.Context) error {
	return nil
}

func (s *sliceCursor) Decode(val interface{}) error {
	v := reflect.ValueOf(val)
	if v.Kind() != reflect.Ptr || v.IsNil() {
		return errors.New("val must be a non-nil pointer")
	}

	v = v.Elem() // Dereference the pointer to get the underlying value.
	if !v.CanSet() {
		return errors.New("cannot set value to val")
	}

	if s.i >= len(s.data) {
		return errors.New("index out of range")
	}

	dataValue := reflect.ValueOf(s.data[s.i])
	if dataValue.Type().AssignableTo(v.Type()) {
		v.Set(dataValue)
		return nil
	}

	return errors.New("incompatible types")
}

func (s *sliceCursor) Next(ctx context.Context) bool {
	select {
	case <-ctx.Done():
		return false
	default:
	}

	if s.i < len(s.data) {
		s.i++
	}
	return s.i < len(s.data)
}

func fetchCitiDailyReconcileTransactionToCheck(fetch func(chan<- service.CitiTransactionToCheck) error) ([]service.CitiTransactionToCheck, error) {
	resultCh := make(chan service.CitiTransactionToCheck)
	var err error
	go func() {
		err = fetch(resultCh)
	}()

	var result []service.CitiTransactionToCheck
	for tx := range resultCh {
		result = append(result, tx)
	}
	return result, err
}

func eodFileObjNameMock(date string, time string) string {
	return fmt.Sprintf("fleet_private/citi_itemize_report/%s/62832_LINEMAN_1_Citi_Transaction_Report_0552908023_EOD_Incoming_%s_1_1.json__110bf833794a42fb9d0fa9138a0937fe", date, time)
}

func eodFileObjNameMockWithPartitionNumber(date string, time string, partition int, totalPartitions int) string {
	return fmt.Sprintf("fleet_private/citi_itemize_report/%s/62832_LINEMAN_1_Citi_Transaction_Report_0552908023_EOD_Incoming_%s_%d_%d.json__110bf833794a42fb9d0fa9138a0937fe", date, time, partition, totalPartitions)
}

func intraFileObjNameMock(date string, time string) string {
	return fmt.Sprintf("fleet_private/citi_itemize_report/%s/LINEMAN_1_Citi_Transaction_Report_0552908023_INTRA_Incoming_%s_1_1.json__008ec4733e2046d68f5da1476965794f", date, time)
}

func newMockEODTransactions(transactions []service.CitiTopupEODTransaction) *s3.GetObjectOutput {
	var entries []string
	for _, tx := range transactions {
		completionDate := tx.TransCompletionDate.Format("02-Jan-06")

		entry := fmt.Sprintf(
			`{"Trans_Completion_Date":"%s","Trans_Received_Date":"29-Jul-23","Trans_Received_Time":"09.02.19","Country_Code":"THX1","Payment_System":"PROMPTPAY","Payment_Type":"Incoming IP","Payment_Source":"Clearing","Citibank_Ref_Number":"%s","Clearing_System_Reference":"201548","End_To_End_Id":"********","Instruction_Id":"********","Transaction_Id":"201548","UETR":"%s","Payer_Proxy_Id_01":"","Payer_Proxy_Id_02":"","Payer_Proxy_Id_03":"","Payer_Account_Number":"**********","Payer_Info_01":"นาย นิพันธ์ วิเศษสุนทร","Payer_Info_02":"","Payer_Info_03":"","Debtor_Tax_Id":"*************","Payer_Bank_Id":"014","Debtor_Agent_Clearing_Sys_Member_Id":"014","Payer_Bank_Info_1":"","Payer_Bank_Info_2":"","Payer_Bank_Info_3":"","Payer_Id_Bin":"","Payer_Id_Number":"%s","Payee_Account_Type":"Virtual","Payee_Proxy_Id_01":"","Payee_Proxy_Id_02":"","Payee_Proxy_Id_03":"","Payee_Account_Number":"**********","Payee_Info_01":"LINE MAN (THAILAND) CO., LTD.","Payee_Info_02":"","Payee_Info_03":"","Creditor_Tax_Id":"","Creditor_Merchant_Type":"6007","Payee_Bank_Id":"017","Creditor_Agent_Clearing_Sys_Member_Id":"017","Payee_Bank_Info_01":"","Payee_Bank_Info_02":"","Payee_Bank_Info_03":"","Value_Date":"29-Jul-23","Transaction_Amount":"%.02f","Debit_Account_Ccy":"THB","Credit_Account_Ccy":"THB","Settlement_Amount":"76.0","Method_Of_Tax":"","Total_Tax_Amount":"0.0","Tax_Rate_Wht":".0","Total_Tax_Amount_Wht":"0.0","Tax_Rate_Vat":".0","Total_Tax_Amount_Vat":"0.0","Status":"%s","Status_Desc":"%s","Clearing_Resp_Code":"","Clearing_Resp_Desc":"","Posting_Ref_Number":"%s","Posting_Time_Stamp":"%s","Dlr_Code":"","Bill_Ref_1":"","Bill_Ref_2":"","Bill_Ref_3":"","Payment_Ref":"","Due_Date_Of_Bill":"","Processing_Code":"481000","Payment_Details":"","Ultimate_Creditor_Name":"","Ultimate_Debtor_Name":"","Ultimate_Creditor_Id_Type_And_Id":"","Ultimate_Debtor_Id_Type_And_Id":"","Additional_Info_01":"","Additional_Info_02":"","Additional_Info_03":"","Additional_Info_04":"","Additional_Info_05":"","Additional_Info_06":"","Additional_Info_07":"","Additional_Info_08":"","Additional_Info_09":"","Additional_Info_10":""}`,
			completionDate, tx.CitibankRefNumber, tx.UETR, tx.Raw.PayerIDNumber, tx.TransactionAmount, tx.Raw.Status, tx.Raw.StatusDesc, tx.PostingRefNumber, tx.Raw.PostingTimeStamp,
		)
		entries = append(entries, entry)
	}
	entriesStr := strings.Join(entries, ",")
	json := fmt.Sprintf(`{"data_dictionary":{"Additional_Info_01":"CHAR","Additional_Info_02":"CHAR","Additional_Info_03":"CHAR","Additional_Info_04":"CHAR","Additional_Info_05":"CHAR","Additional_Info_06":"CHAR","Additional_Info_07":"CHAR","Additional_Info_08":"CHAR","Additional_Info_09":"CHAR","Additional_Info_10":"CHAR","Bill_Ref_1":"CHAR","Bill_Ref_2":"CHAR","Bill_Ref_3":"CHAR","Citibank_Ref_Number":"CHAR","Clearing_Resp_Code":"CHAR","Clearing_Resp_Desc":"CHAR","Clearing_System_Reference":"CHAR","Country_Code":"CHAR","Credit_Account_Ccy":"CHAR","Creditor_Agent_Clearing_Sys_Member_Id":"CHAR","Creditor_Merchant_Type":"CHAR","Creditor_Tax_Id":"CHAR","Debit_Account_Ccy":"CHAR","Debtor_Agent_Clearing_Sys_Member_Id":"CHAR","Debtor_Tax_Id":"CHAR","Dlr_Code":"CHAR","Due_Date_Of_Bill":"DATETIME","End_To_End_Id":"CHAR","Instruction_Id":"CHAR","Method_Of_Tax":"CHAR","Payee_Account_Number":"CHAR","Payee_Account_Type":"CHAR","Payee_Bank_Id":"CHAR","Payee_Bank_Info_01":"CHAR","Payee_Bank_Info_02":"CHAR","Payee_Bank_Info_03":"CHAR","Payee_Info_01":"CHAR","Payee_Info_02":"CHAR","Payee_Info_03":"CHAR","Payee_Proxy_Id_01":"CHAR","Payee_Proxy_Id_02":"CHAR","Payee_Proxy_Id_03":"CHAR","Payer_Account_Number":"CHAR","Payer_Bank_Id":"CHAR","Payer_Bank_Info_1":"CHAR","Payer_Bank_Info_2":"CHAR","Payer_Bank_Info_3":"CHAR","Payer_Id_Bin":"CHAR","Payer_Id_Number":"CHAR","Payer_Info_01":"CHAR","Payer_Info_02":"CHAR","Payer_Info_03":"CHAR","Payer_Proxy_Id_01":"CHAR","Payer_Proxy_Id_02":"CHAR","Payer_Proxy_Id_03":"CHAR","Payment_Details":"CHAR","Payment_Ref":"CHAR","Payment_Source":"CHAR","Payment_System":"CHAR","Payment_Type":"CHAR","Posting_Ref_Number":"CHAR","Posting_Time_Stamp":"DATETIME","Processing_Code":"CHAR","Settlement_Amount":"FLOAT","Status":"CHAR","Status_Desc":"CHAR","Tax_Rate_Vat":"FLOAT","Tax_Rate_Wht":"FLOAT","Total_Tax_Amount":"FLOAT","Total_Tax_Amount_Vat":"FLOAT","Total_Tax_Amount_Wht":"FLOAT","Trans_Completion_Date":"DATE","Trans_Received_Date":"DATE","Trans_Received_Time":"TIME","Transaction_Amount":"FLOAT","Transaction_Id":"CHAR","UETR":"CHAR","Ultimate_Creditor_Id_Type_And_Id":"CHAR","Ultimate_Creditor_Name":"CHAR","Ultimate_Debtor_Id_Type_And_Id":"CHAR","Ultimate_Debtor_Name":"CHAR","Value_Date":"DATE"},"transaction_entries":[%s]}`, entriesStr)
	return &s3.GetObjectOutput{
		Body: io.NopCloser(bytes.NewBuffer([]byte(json))),
	}
}

func newMockTransactionsToCheck(transactions []service.CitiTransactionToCheck) *s3.GetObjectOutput {
	var entries []string
	for _, transaction := range transactions {

		entry := fmt.Sprintf(
			`{"Trans_Completion_Date":"30-Jul-23","Trans_Received_Date":"30-Jul-23","Trans_Received_Time":"22.45.09","Country_Code":"THX1","Payment_System":"PROMPTPAY","Payment_Type":"Incoming IP","Payment_Source":"Clearing","Citibank_Ref_Number":"AB7CCRD586206735","Clearing_System_Reference":"308007","End_To_End_Id":"********","Instruction_Id":"********","Transaction_Id":"308007","UETR":"%s","Payer_Proxy_Id_01":"","Payer_Proxy_Id_02":"","Payer_Proxy_Id_03":"","Payer_Account_Number":"**********","Payer_Info_01":"นาย จเร สว่างการ","Payer_Info_02":"","Payer_Info_03":"","Debtor_Tax_Id":"*************","Payer_Bank_Id":"014","Debtor_Agent_Clearing_Sys_Member_Id":"014","Payer_Bank_Info_1":"","Payer_Bank_Info_2":"","Payer_Bank_Info_3":"","Payer_Id_Bin":"","Payer_Id_Number":"**********","Payee_Account_Type":"Virtual","Payee_Proxy_Id_01":"","Payee_Proxy_Id_02":"","Payee_Proxy_Id_03":"","Payee_Account_Number":"**********","Payee_Info_01":"LINE MAN (THAILAND) CO., LTD.","Payee_Info_02":"","Payee_Info_03":"","Creditor_Tax_Id":"","Creditor_Merchant_Type":"6007","Payee_Bank_Id":"017","Creditor_Agent_Clearing_Sys_Member_Id":"017","Payee_Bank_Info_01":"","Payee_Bank_Info_02":"","Payee_Bank_Info_03":"","Value_Date":"30-Jul-23","Transaction_Amount":"90.0","Debit_Account_Ccy":"THB","Credit_Account_Ccy":"THB","Settlement_Amount":"90.0","Method_Of_Tax":"","Total_Tax_Amount":"0.0","Tax_Rate_Wht":".0","Total_Tax_Amount_Wht":"0.0","Tax_Rate_Vat":".0","Total_Tax_Amount_Vat":"0.0","Status":"ACCC","Status_Desc":"ACCC","Clearing_Resp_Code":"","Clearing_Resp_Desc":"","Posting_Ref_Number":"477224887968E362","Posting_Time_Stamp":"DATETIME","Dlr_Code":"","Bill_Ref_1":"","Bill_Ref_2":"","Bill_Ref_3":"","Payment_Ref":"","Due_Date_Of_Bill":"","Processing_Code":"481000","Payment_Details":"","Ultimate_Creditor_Name":"","Ultimate_Debtor_Name":"","Ultimate_Creditor_Id_Type_And_Id":"","Ultimate_Debtor_Id_Type_And_Id":"","Additional_Info_01":"","Additional_Info_02":"","Additional_Info_03":"","Additional_Info_04":"","Additional_Info_05":"","Additional_Info_06":"","Additional_Info_07":"","Additional_Info_08":"","Additional_Info_09":"","Additional_Info_10":""}`,
			transaction.TransactionId,
		)
		entries = append(entries, entry)
	}
	entriesStr := strings.Join(entries, ",")
	json := fmt.Sprintf(`{"data_dictionary":{"Additional_Info_01":"CHAR","Additional_Info_02":"CHAR","Additional_Info_03":"CHAR","Additional_Info_04":"CHAR","Additional_Info_05":"CHAR","Additional_Info_06":"CHAR","Additional_Info_07":"CHAR","Additional_Info_08":"CHAR","Additional_Info_09":"CHAR","Additional_Info_10":"CHAR","Bill_Ref_1":"CHAR","Bill_Ref_2":"CHAR","Bill_Ref_3":"CHAR","Citibank_Ref_Number":"CHAR","Clearing_Resp_Code":"CHAR","Clearing_Resp_Desc":"CHAR","Clearing_System_Reference":"CHAR","Country_Code":"CHAR","Credit_Account_Ccy":"CHAR","Creditor_Agent_Clearing_Sys_Member_Id":"CHAR","Creditor_Merchant_Type":"CHAR","Creditor_Tax_Id":"CHAR","Debit_Account_Ccy":"CHAR","Debtor_Agent_Clearing_Sys_Member_Id":"CHAR","Debtor_Tax_Id":"CHAR","Dlr_Code":"CHAR","Due_Date_Of_Bill":"DATETIME","End_To_End_Id":"CHAR","Instruction_Id":"CHAR","Method_Of_Tax":"CHAR","Payee_Account_Number":"CHAR","Payee_Account_Type":"CHAR","Payee_Bank_Id":"CHAR","Payee_Bank_Info_01":"CHAR","Payee_Bank_Info_02":"CHAR","Payee_Bank_Info_03":"CHAR","Payee_Info_01":"CHAR","Payee_Info_02":"CHAR","Payee_Info_03":"CHAR","Payee_Proxy_Id_01":"CHAR","Payee_Proxy_Id_02":"CHAR","Payee_Proxy_Id_03":"CHAR","Payer_Account_Number":"CHAR","Payer_Bank_Id":"CHAR","Payer_Bank_Info_1":"CHAR","Payer_Bank_Info_2":"CHAR","Payer_Bank_Info_3":"CHAR","Payer_Id_Bin":"CHAR","Payer_Id_Number":"CHAR","Payer_Info_01":"CHAR","Payer_Info_02":"CHAR","Payer_Info_03":"CHAR","Payer_Proxy_Id_01":"CHAR","Payer_Proxy_Id_02":"CHAR","Payer_Proxy_Id_03":"CHAR","Payment_Details":"CHAR","Payment_Ref":"CHAR","Payment_Source":"CHAR","Payment_System":"CHAR","Payment_Type":"CHAR","Posting_Ref_Number":"CHAR","Posting_Time_Stamp":"DATETIME","Processing_Code":"CHAR","Settlement_Amount":"FLOAT","Status":"CHAR","Status_Desc":"CHAR","Tax_Rate_Vat":"FLOAT","Tax_Rate_Wht":"FLOAT","Total_Tax_Amount":"FLOAT","Total_Tax_Amount_Vat":"FLOAT","Total_Tax_Amount_Wht":"FLOAT","Trans_Completion_Date":"DATE","Trans_Received_Date":"DATE","Trans_Received_Time":"TIME","Transaction_Amount":"FLOAT","Transaction_Id":"CHAR","UETR":"CHAR","Ultimate_Creditor_Id_Type_And_Id":"CHAR","Ultimate_Creditor_Name":"CHAR","Ultimate_Debtor_Id_Type_And_Id":"CHAR","Ultimate_Debtor_Name":"CHAR","Value_Date":"DATE"},"transaction_entries":[%s]}`, entriesStr)
	return &s3.GetObjectOutput{
		Body: io.NopCloser(bytes.NewBuffer([]byte(json))),
	}
}

func pointer[T any](x T) *T {
	return &x
}

const CitiDailyReconcileTaskTestVOSReportFolder = "citi_itemize_report"
