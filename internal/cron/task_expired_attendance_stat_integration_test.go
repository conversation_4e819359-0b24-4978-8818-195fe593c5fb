//go:build integration_test
// +build integration_test

package cron_test

import (
	"context"
	"os"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestExpiredAttendanceStat_Execute_IntegrationTest_Execute(t *testing.T) {
	t.Run("Should able expire attendance stat correctly", func(t *testing.T) {
		// Given
		os.Setenv("MAX_AGE_DAYS", "10")
		defer os.Setenv("MAX_AGE_DAYS", "60")

		ctn := ittest.NewContainer(t)
		runningDate := time.Date(2022, 1, 22, 0, 0, 0, 0, timeutil.BangkokLocation())

		atds := make(model.AttendanceStat)
		d := runningDate
		for i := 0; i < 60; i++ {
			d = timeutil.DateTruncate(d).AddDate(0, 0, -1)
			atds[d.Format(time.DateOnly)] = []model.AttendanceTime{}
		}

		dois := []model.DriverOrderInfo{
			{DriverID: "A", AttendanceStat: atds},
		}
		data := make([]interface{}, len(dois))
		for i, ot := range dois {
			data[i] = ot
		}

		err := ctn.DriverOrderInfoDataStore.InsertMany(context.Background(), data)
		require.NoError(t, err)

		// When
		result, err := ctn.ExpiredAttendanceStatTask.Execute(context.Background(), "22/01/2022")

		// Then
		require.NoError(t, err)
		require.Equal(t, []string{"A"}, result.Proceeds)
		require.Equal(t, []string{"A"}, result.Success)
		require.Equal(t, []string{}, result.Fail)
		driverOrderInfoDB := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "driver_order_info")
		var doi model.DriverOrderInfo
		driverOrderInfoDB.FindOneByQuery(bson.M{"driver_id": "A"}, &doi)
		require.Equal(t, 10, len(doi.AttendanceStat))
	})

	t.Run("Should not expire attendance stat if not reach `MAX_AGE_DAYS`", func(t *testing.T) {
		// Given
		os.Setenv("MAX_AGE_DAYS", "10")
		defer os.Setenv("MAX_AGE_DAYS", "60")

		ctn := ittest.NewContainer(t)
		runningDate := time.Date(2022, 1, 22, 0, 0, 0, 0, timeutil.BangkokLocation())

		atds := make(model.AttendanceStat)
		d := runningDate
		for i := 0; i < 5; i++ {
			d = timeutil.DateTruncate(d).AddDate(0, 0, -1)
			atds[d.Format(time.DateOnly)] = []model.AttendanceTime{}
		}

		dois := []model.DriverOrderInfo{
			{DriverID: "A", AttendanceStat: atds},
		}
		data := make([]interface{}, len(dois))
		for i, ot := range dois {
			data[i] = ot
		}

		err := ctn.DriverOrderInfoDataStore.InsertMany(context.Background(), data)
		require.NoError(t, err)

		// When
		result, err := ctn.ExpiredAttendanceStatTask.Execute(context.Background(), "22/01/2022")

		// Then
		require.NoError(t, err)
		require.Equal(t, []string{"A"}, result.Proceeds)
		require.Equal(t, []string{"A"}, result.Success)
		require.Equal(t, []string{}, result.Fail)
		driverOrderInfoDB := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "driver_order_info")
		var doi model.DriverOrderInfo
		driverOrderInfoDB.FindOneByQuery(bson.M{"driver_id": "A"}, &doi)
		require.Equal(t, 5, len(doi.AttendanceStat))
	})

	t.Run("Should able expire multple driver's attendance stat correctly", func(t *testing.T) {
		// Given
		os.Setenv("MAX_AGE_DAYS", "10")
		defer os.Setenv("MAX_AGE_DAYS", "60")

		ctn := ittest.NewContainer(t)
		runningDate := time.Date(2022, 1, 22, 0, 0, 0, 0, timeutil.BangkokLocation())

		atds := make(model.AttendanceStat)
		d := runningDate
		for i := 0; i < 60; i++ {
			d = timeutil.DateTruncate(d).AddDate(0, 0, -1)
			atds[d.Format(time.DateOnly)] = []model.AttendanceTime{}
		}

		dois := []model.DriverOrderInfo{
			{DriverID: "A", AttendanceStat: atds},
			{DriverID: "B", AttendanceStat: atds},
		}
		data := make([]interface{}, len(dois))
		for i, ot := range dois {
			data[i] = ot
		}

		err := ctn.DriverOrderInfoDataStore.InsertMany(context.Background(), data)
		require.NoError(t, err)

		// When
		result, err := ctn.ExpiredAttendanceStatTask.Execute(context.Background(), "22/01/2022")

		// Then
		require.NoError(t, err)
		require.Equal(t, []string{"A", "B"}, result.Proceeds)
		require.Equal(t, []string{"A", "B"}, result.Success)
		require.Equal(t, []string{}, result.Fail)
		driverOrderInfoDB := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "driver_order_info")
		var doi model.DriverOrderInfo
		driverOrderInfoDB.FindOneByQuery(bson.M{"driver_id": "A"}, &doi)
		require.Equal(t, 10, len(doi.AttendanceStat))
		driverOrderInfoDB.FindOneByQuery(bson.M{"driver_id": "B"}, &doi)
		require.Equal(t, 10, len(doi.AttendanceStat))
	})

	t.Run("Unable to parse attendance stat date", func(t *testing.T) {
		// Given
		os.Setenv("MAX_AGE_DAYS", "10")
		defer os.Setenv("MAX_AGE_DAYS", "60")

		ctn := ittest.NewContainer(t)
		runningDate := time.Date(2022, 1, 22, 0, 0, 0, 0, timeutil.BangkokLocation())

		atds := make(model.AttendanceStat)
		d := runningDate
		for i := 0; i < 60; i++ {
			d = timeutil.DateTruncate(d).AddDate(0, 0, -1)
			atds[d.Format("01-02-2006")] = []model.AttendanceTime{}
		}

		dois := []model.DriverOrderInfo{
			{DriverID: "A", AttendanceStat: atds},
		}
		data := make([]interface{}, len(dois))
		for i, ot := range dois {
			data[i] = ot
		}

		err := ctn.DriverOrderInfoDataStore.InsertMany(context.Background(), data)
		require.NoError(t, err)

		// When
		result, err := ctn.ExpiredAttendanceStatTask.Execute(context.Background(), "22/01/2022")

		// Then
		require.NoError(t, err)
		require.Equal(t, []string{"A"}, result.Proceeds)
		require.Equal(t, []string{}, result.Success)
		require.Equal(t, []string{"A"}, result.Fail)
	})
}
