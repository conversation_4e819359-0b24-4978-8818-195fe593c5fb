package cron

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/pdf"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type BNPLSOAFileTask struct {
	instRepo      repository.InstallmentRepository
	pdf           pdf.PDFBuilder
	driverRepo    repository.DriverRepository
	driverDocRepo repository.DriverDocumentRepository
	vosService    service.VOSService
	cfg           BNPLSOAFileTaskConfig
	notifier      service.Notifier
}

func ProvideBNPLSOAFileTask(instRepo repository.InstallmentRepository,
	pdf pdf.PDFBuilder, driverRepo repository.DriverRepository,
	driverDocRepo repository.DriverDocumentRepository,
	vosService service.VOSService,
	cfg BNPLSOAFileTaskConfig,
	notifier service.Notifier,
) *BNPLSOAFileTask {
	return &BNPLSOAFileTask{
		instRepo:      instRepo,
		pdf:           pdf,
		driverRepo:    driverRepo,
		driverDocRepo: driverDocRepo,
		vosService:    vosService,
		cfg:           cfg,
		notifier:      notifier,
	}
}

type driverSOAData struct {
	driverID  string
	uobRefID  string
	citiRefID string
	name      string
	address   string
}

type ProcessSOAData struct {
	AvoidFileNameCollisionNumber int
	LastInstallment              []model.Installment
	ActiveInstallment            []model.Installment
}

func (b *BNPLSOAFileTask) Execute(ctx context.Context, _ ...interface{}) (Result, error) {
	method := "GenerateBNPLSOAFile"
	today := timeutil.BangkokNow()
	inst, err := b.instRepo.FindNotSentLastSOAFile(ctx, 0, 0, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().
			Context(ctx).
			Err(err).
			Msg("FindNotSentLastSOAFile err")
		return Result{}, err
	}
	logx.Info().
		Context(ctx).
		Int("size", len(inst)).
		Msg("All installments for process")

	// Convert the list of installments into a map of driver ids and installments
	drvIst, drivers := b.extractDriverAndInstallments(inst)

	drvData, err := b.getDriverSOAData(ctx, err, drivers)
	if err != nil {
		return Result{}, err
	}

	logx.Info().
		Context(ctx).
		Int("size", len(drvData)).
		Msg("All driver to process")

	r := NewResult()
	workerPool, poolCleanup := safe.NewWorker(b.cfg.WorkerPoolSize)
	defer poolCleanup()
	var wg sync.WaitGroup
	var lock sync.Mutex
	for driverID, processSOAData := range drvIst {
		// Generate the current SOA file
		if len(processSOAData.ActiveInstallment) != 0 {
			wg.Add(1)
			workerPool.GoFuncWithPool(func() {
				defer wg.Done()
				defer lock.Unlock()
				logx.Info().
					Context(ctx).
					Int("size", len(processSOAData.ActiveInstallment)).
					Str(logutil.DriverID, driverID).
					Msg("Process current installment for driver")
				focusedDriverSOAData := drvData[driverID]
				fn := getFileNumber(today, processSOAData.AvoidFileNameCollisionNumber)
				pdfInfo := b.prepareInstallmentForPDF(fn, focusedDriverSOAData, processSOAData.ActiveInstallment, today)
				err = b.SaveFile(ctx, driverID, pdfInfo)
				if err != nil {
					logx.Error().
						Context(ctx).
						Err(err).
						Msg("fail to save file")
					lock.Lock()
					r.AddFail(driverID)
					return
				}
				lock.Lock()
				r.AddSuccess(driverID)
			})
		}

		// Generate the latest SOA file
		if len(processSOAData.LastInstallment) != 0 {
			wg.Add(1)
			workerPool.GoFuncWithPool(func() {
				defer wg.Done()
				defer lock.Unlock()
				logx.Info().
					Context(ctx).
					Int("size", len(processSOAData.LastInstallment)).
					Str(logutil.Method, method).
					Str(logutil.DriverID, driverID).
					Msg("Process last installment for driver")

				focusedDriverSOAData := drvData[driverID]
				// Generate the last installment SOA
				fn := getFileNumber(today, processSOAData.AvoidFileNameCollisionNumber+1)
				pdfInfo := b.prepareInstallmentForPDF(fn, focusedDriverSOAData, processSOAData.LastInstallment, today)
				err = b.SaveFile(ctx, driverID, pdfInfo)
				if err != nil {
					logx.Error().
						Context(ctx).
						Err(err).
						Str(logutil.Method, method).
						Msg("fail to save file")
					lock.Lock()
					r.AddFail(driverID)
					return
				}

				ids := getInstallmentID(processSOAData.LastInstallment)
				err = b.SetSentLatestInstallmentFileFlag(ctx, ids, today)
				if err != nil {
					logx.Error().
						Context(ctx).
						Err(err).
						Str(logutil.Method, method).
						Msg("sent latest installment file flag")
					lock.Lock()
					r.AddFail(driverID)
					return
				}

				lock.Lock()
				r.AddSuccess(driverID)
			})
		}
	}
	wg.Wait()
	return *r, nil

}

func (b *BNPLSOAFileTask) extractDriverAndInstallments(inst []model.Installment) (map[string]*ProcessSOAData, []string) {
	// support feature one driver has many installment.
	drvIst := make(map[string]*ProcessSOAData)
	drivers := make([]string, 0, len(inst))
	avoidFileNameCollisionNumber := 0
	for _, v := range inst {
		// Prevent the duplicate drivers. So no duplicate query occurred.
		if _, ok := drvIst[v.DriverID]; !ok {
			drivers = append(drivers, v.DriverID)
			drvIst[v.DriverID] = &ProcessSOAData{
				AvoidFileNameCollisionNumber: avoidFileNameCollisionNumber,
			}
			// As 1 driver can be issued the SOA file at 2 maximum files.
			avoidFileNameCollisionNumber += 2
		}

		if isLastInstallment(v) {
			drvIst[v.DriverID].LastInstallment = append(drvIst[v.DriverID].LastInstallment, v)
		} else {
			drvIst[v.DriverID].ActiveInstallment = append(drvIst[v.DriverID].ActiveInstallment, v)
		}
	}
	return drvIst, drivers
}

func (b *BNPLSOAFileTask) getDriverSOAData(ctx context.Context, err error, drivers []string) (map[string]driverSOAData, error) {
	drv, err := b.driverRepo.FindDriverIDs(ctx, drivers, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().
			Context(ctx).
			Err(err).
			Str(logutil.Method, "getDriverSOAData").
			Msg("FindDriverIDs err")
		return nil, err
	}

	drvData := make(map[string]driverSOAData)
	for _, v := range drv {
		drvData[v.DriverID] = driverSOAData{
			driverID:  v.DriverID,
			uobRefID:  v.Banking.UOBRefID,
			citiRefID: v.Banking.CitiRefID,
			name:      fmt.Sprintf("%s %s", v.Firstname.String(), v.Lastname.String()),
			address: fmt.Sprintf("%s %s %s %s %s %s",
				v.Address.HouseNumber.String(),
				v.Address.Moo.String(),
				v.Address.Subdistrict.String(),
				v.Address.District.String(),
				v.Address.Province.String(),
				v.Address.Zipcode.String()),
		}
	}
	return drvData, nil
}

func getInstallmentID(inst []model.Installment) []string {
	var s []string
	for _, v := range inst {
		s = append(s, v.ID.Hex())
	}
	return s
}

func isLastInstallment(v model.Installment) bool {
	switch v.Status {
	case model.InstallmentCompleted:
		return v.OverdueAmount == 0 && v.DPD == 0 && v.OutstandingAmount == 0
	case model.InstallmentCancelled:
		return true
	case model.InstallmentRefinanced:
		return true
	default:
		return false
	}
}

func (b *BNPLSOAFileTask) SaveFile(ctx context.Context, driverID string, pdfInfo pdf.SOAForBNPLInfo) error {
	f, err := b.pdf.GenerateSOAForBNPL(pdfInfo)
	if err != nil {
		logx.Error().
			Err(err).
			Context(ctx).
			Str(logutil.DriverID, driverID).
			Msg("SaveFile GenerateSOAForBNPL file")
		return err
	}

	url, fileID, err := b.SaveToVOS(ctx, driverID, f)
	if err != nil {
		logx.Error().
			Err(err).
			Context(ctx).
			Str(logutil.DriverID, driverID).
			Msg("SaveFile SaveToVOS")
		return err
	}

	err = b.SaveDocument(ctx, driverID, url, fileID)
	if err != nil {
		logx.Error().
			Err(err).
			Context(ctx).
			Str(logutil.DriverID, driverID).
			Msg("SaveFile SaveDocument")
		return err
	}

	err = b.SendNotificationToDriver(ctx, driverID)
	if err != nil {
		logx.Error().
			Err(err).
			Context(ctx).
			Str(logutil.DriverID, driverID).
			Msg("SaveFile SendNotificationToDriver")
	}

	return nil
}

func (b *BNPLSOAFileTask) prepareInstallmentForPDF(n string, drv driverSOAData, inst []model.Installment, ct time.Time) pdf.SOAForBNPLInfo {
	today := ct
	tomorrow := ct.Add(24 * time.Hour)

	var pdfInst []pdf.Installment
	var istLogs []model.InstallmentLog
	var sumOverdue float64
	var sumInstallmentAmount float64

	for _, v := range inst {
		pdfInst = append(pdfInst, pdf.Installment{
			ProductName:           v.ProductName,
			StartDate:             getDateFormat(v.Start),
			TotalInstallmentCount: v.GetDayCount(),
			Price:                 v.InitialAmount.Float64(),
			Amount:                1, // Now is `1` if business change have to change this
			DebtAmount:            v.InstallmentAmount.Add(v.OverdueAmount).Float64(),
			PaidAmount:            v.InstallmentAmount.Float64(),
			RemainingAmount:       v.OverdueAmount.Float64(),
			OverdueDate:           getFirstDatePendingInstallmentLogs(v.InstallmentLogs),
		})

		istLogs = append(istLogs, v.InstallmentLogs...)

		sumOverdue += v.OverdueAmount.Float64()
		sumInstallmentAmount += v.InstallmentAmount.Float64()
	}

	pdfInfo := pdf.SOAForBNPLInfo{
		HeadContent: pdf.InstallmentHeadContent{
			FileNumber:   n,
			CurrentDate:  getDateFormat(today),
			DueDate:      getDateFormat(tomorrow),
			PayerName:    drv.name,
			PayerAddress: drv.address,
			DriverID:     drv.driverID,
		},
		InstallmentDetail: pdf.InstallmentDetail{
			Installments: pdfInst,
			DueDate:      getDateFormat(today),
			TotalAmount:  sumOverdue,
		},
		InstallmentTable: pdf.InstallmentTable{
			PaidSummary: pdf.InstallmentPaidSummary{
				DebtTotal:     sumInstallmentAmount,
				Interest:      0,
				OtherFee:      0,
				FollowDebtFee: 0,
			},
			OverdueSummary: pdf.InstallmentOverdueSummary{
				Date:          getFirstDatePendingInstallmentLogs(istLogs),
				Amount:        sumOverdue,
				Interest:      0,
				OtherFee:      0,
				FollowDebtFee: 0,
			},
			CurrentBillSummary: pdf.InstallmentCurrentBillSummary{
				Date:   getDateFormat(today),
				Amount: sumOverdue,
			},
		},
		BankInfos: pdf.InstallmentDriverBankInfos{
			CITINumber: drv.citiRefID,
			UOBNumber:  drv.uobRefID,
		},
	}

	return pdfInfo
}

func (b *BNPLSOAFileTask) SetSentLatestInstallmentFileFlag(ctx context.Context, ids []string, t time.Time) error {
	return b.instRepo.SetTimeSentSOAFile(ctx, ids, t)
}

func (b *BNPLSOAFileTask) SendNotificationToDriver(ctx context.Context, driverID string) error {
	msg := make(map[string]string)
	msg["action"] = "BNPL_INVOICE"
	msg["title"] = "มีเอกสารใหม่! ตรวจสอบ 'ใบแจ้งยอดโครงการผ่อนชำระรายวัน' ได้ที่เอกสารส่วนบุคคลของคุณ"
	msg["body"] = "เอกสาร : ใบแจ้งยอดโครงการจำหน่ายสินค้าอิเล็กทรอนิกส์บนแอปฯ LINE MAN Rider"
	err := b.notifier.Notify(ctx, []string{driverID}, msg)
	return err
}

func (b *BNPLSOAFileTask) SaveToVOS(ctx context.Context, driverID string, f []byte) (string, string, error) {
	reader := bytes.NewReader(f)
	today := timeutil.BangkokNow()
	name := fmt.Sprintf("soa_%s", strings.ReplaceAll(getDateFormat(today), "/", "_"))
	path := fmt.Sprintf("%d/%s", today.Year(), driverID)
	bucket := service.GetVOSBNPLSOABucket(today)
	uploadedFile, err := b.vosService.UploadToVOS(ctx, name, reader, service.WithVOSBucket(bucket), service.WithVOSForCDN, service.WithSaveOptions(
		file.WithPrefixPath(path), file.WithContentType("application/pdf"), file.WithUniqueID))
	if err != nil {
		logx.Error().
			Err(err).
			Context(ctx).
			Str(logutil.DriverID, driverID).
			Str("path", path).
			Msg("SaveToVOS UploadToVOSCDN")
		return "", "", err
	}
	return uploadedFile.Location(), uploadedFile.ID(), nil
}

func (b *BNPLSOAFileTask) SaveDocument(ctx context.Context, driverID, url, fileID string) error {
	m := model.NewDriverDocument(driverID, model.DriverDocumentBNPLType, url, fileID)
	return b.driverDocRepo.Save(ctx, m)
}

func getFirstDatePendingInstallmentLogs(logs []model.InstallmentLog) string {

	var pending []model.InstallmentLog
	for _, v := range logs {
		if v.InstallmentPaymentStatus == model.InstallmentPaymentStatusPending {
			pending = append(pending, v)
		}
	}

	if len(pending) == 0 {
		return ""
	}

	l := logs[0].CreatedAt
	for _, v := range pending {
		if v.CreatedAt.Before(l) {
			l = v.CreatedAt
		}
	}

	return getDateFormat(l)
}

func getDateFormat(t time.Time) string {
	y, m, d := t.Date()
	ys := fmt.Sprintf("%d", y)
	ms := fmt.Sprintf("%02d", int(m))
	ds := fmt.Sprintf("%02d", d)
	return fmt.Sprintf("%s/%s/%s", ds, ms, ys)
}

func getFileNumber(t time.Time, nb int) string {
	y, m, d := t.Date()
	ys := fmt.Sprintf("%d", y)
	ms := fmt.Sprintf("%02d", int(m))
	ds := fmt.Sprintf("%02d", d)
	s := fmt.Sprintf("%s%s%s", ys, ms, ds)
	n := fmt.Sprintf("%05d", nb)
	return fmt.Sprintf("%s%s", s, n)
}

func (b *BNPLSOAFileTask) Name() string {
	return "BNPLSOAFile"
}
