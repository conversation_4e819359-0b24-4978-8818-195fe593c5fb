//go:generate mockgen -source=./task_tawi50_generator.go -destination=./mock_task_tawi50_generator.go -package=cron

package cron

import (
	"bytes"
	"context"
	"fmt"
	"math"

	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/pdf"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type Tawi50GeneratorConfig struct {
	TaxpayerName        string   `envconfig:"TAX_PAYER_NAME" default:"บริษัท ไลน์แมน (ประเทศไทย) จำกัด"`
	TaxIdentificationNo []string `envconfig:"TAX_IDENTIFICATION_NO" default:"0,1,0,5,5,6,2,1,6,0,7,2,1"`
	TaxpayerAddress     string   `envconfig:"TAX_PAYER_ADDRESS" default:"เลขที่ 195 วัน แบงค็อก ทาวเวอร์ 4 ห้องเลขที่ 2411 - 2412 ชั้น 24 และ ห้องเลขที่ 2501-2514 ชั้น 25 ถนนวิทยุ แขวงลุมพินี เขตปทุมวัน กรุงเทพมหานคร 10330"`
	TaxYear             string   `envconfig:"TAX_YEAR" default:"2567"`
	BatchSize           int      `envconfig:"TAX_GENERATOR_BATCH_SIZE" default:"500"`
	UpdateBatchSize     int      `envconfig:"TAX_GENERATOR_UPDATE_BATCH_SIZE" default:"100"`
	VosPath             string   `envconfig:"TAWI50_VOS_PATH" default:"tawi50_2024"`
}

type Tawi50Generator interface {
	GenerateTawi50(ctx context.Context, order model.Order, remark string) error
}

type Tawi50GeneratorTask struct {
	wtcRepo    repository.WithholdingTaxCertificateRepository
	driverRepo repository.DriverRepository
	vosService service.VOSService
	pdf        pdf.PDFBuilder
	cfg        Tawi50GeneratorConfig
}

func ProvideTawi50GeneratorTask(wtcRepo repository.WithholdingTaxCertificateRepository, driverRepo repository.DriverRepository, vosService service.VOSService, pdf pdf.PDFBuilder, cfg Tawi50GeneratorConfig) *Tawi50GeneratorTask {
	return &Tawi50GeneratorTask{wtcRepo: wtcRepo, driverRepo: driverRepo, vosService: vosService, pdf: pdf, cfg: cfg}
}

func (tawi *Tawi50GeneratorTask) Execute(ctx context.Context, _ ...interface{}) (Result, error) {
	result := NewResult()
	wList, err := tawi.wtcRepo.FindByYearAndStatus(ctx, tawi.cfg.TaxYear, model.Tawi50Requested, 0, tawi.cfg.BatchSize, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Errorf("Find requested tawi50 Error: %v", err)
		return Result{}, err
	}
	logrus.Infof("Find %v Tawi50 %v with batch size %v : found = %v ", model.Tawi50Requested, tawi.cfg.TaxYear, tawi.cfg.BatchSize, len(wList))

	taxpayer := tawi.cfg.toTawi50Taxpayer()
	for i, it := range wList {
		item := &wList[i]
		d, err := tawi.driverRepo.FindDriverID(ctx, it.DriverID, repository.WithReadSecondaryPreferred)
		if err != nil {
			result.AddFail(it.DriverID)
			item.Status = model.Tawi50Failed
			logrus.Errorf("Find driver ID %v error: %v", it.DriverID, err)
			item.Remarks = append(item.Remarks, fmt.Sprintf("Find driver ID error: %v", err))
			continue
		}
		item.DriverProfile = d.ToDriverProfile()
		item.Status = model.Tawi50Completed

		tawi50, err := tawi.pdf.GenerateTawi50(taxpayer, *item)
		if err != nil {
			logrus.Errorf("Generate tawi50 pdf error: %v", err)
			result.AddFail(it.DriverID)
			item.Status = model.Tawi50Failed
			item.Remarks = append(item.Remarks, fmt.Sprintf("Generate tawi50 pdf error: %v", err))
			continue
		}
		reader := bytes.NewReader(tawi50)

		path := fmt.Sprintf("driver_private/%s", tawi.cfg.VosPath)
		uploadedFile, err := tawi.vosService.UploadToVOS(ctx, d.DriverID, reader, service.WithVOSForCDN, service.WithSaveOptions(
			file.WithPrefixPath(path), file.WithContentType("application/pdf"), file.WithUniqueID))
		if err != nil {
			logrus.Errorf("Upload tawi50 to vos for internal error: %v", err)
			item.Remarks = append(item.Remarks, fmt.Sprintf("Upload tawi50 to vos for internal erro: %v", err))
			result.AddFail(it.DriverID)
			item.Status = model.Tawi50Failed
			continue
		}
		item.Tawi50Url = uploadedFile.Location()
		item.Tawi50ID = uploadedFile.ID()
		result.AddSuccess(string(tawi50))
	}

	if len(wList) > 0 {
		maxBatchSize := tawi.cfg.UpdateBatchSize
		tawiSize := len(wList)
		skip := 0
		batchSize := int(math.Ceil(float64(tawiSize) / float64(maxBatchSize)))

		for i := 1; i <= batchSize; i++ {
			lowerBound := skip
			upperBound := skip + maxBatchSize
			if upperBound > tawiSize {
				upperBound = tawiSize
			}
			batchItems := wList[lowerBound:upperBound]
			skip += maxBatchSize

			err := tawi.wtcRepo.UpdateAll(ctx, batchItems)
			if err != nil {
				logrus.Errorf("Update tawi50 Error: %v", err)
				return Result{}, err
			}
		}
	}

	return *result, nil
}

func (cfg *Tawi50GeneratorConfig) toTawi50Taxpayer() pdf.Tawi50TaxPayer {
	year, month, day := timeutil.BangkokNow().Date()

	return pdf.Tawi50TaxPayer{
		TaxpayerName:        cfg.TaxpayerName,
		TaxIdentificationNo: cfg.TaxIdentificationNo,
		TaxpayerAddress:     cfg.TaxpayerAddress,
		IssuingDate:         day,
		IssuingMonth:        month,
		IssuingYear:         year + 543,
	}
}

func (tawi *Tawi50GeneratorTask) Name() string {
	return "Tawi50Generator"
}

func ProvideTawi50GeneratorConfig() (cfg Tawi50GeneratorConfig) {
	envconfig.MustProcess("", &cfg)

	return
}
