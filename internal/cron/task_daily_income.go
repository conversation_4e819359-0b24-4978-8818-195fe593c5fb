package cron

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type dailyIncomeMode string

const (
	DailyMode    dailyIncomeMode = "daily"
	RerunMode    dailyIncomeMode = "rerun"
	BackfillMode dailyIncomeMode = "backfill"
)

type dailyIncomeRequest struct {
	mode   dailyIncomeMode
	date   time.Time
	from   time.Time
	to     time.Time
	remark string
}

type dailyIncomeResult struct {
	*Result
	mode                dailyIncomeMode
	totalEmptyDriverId  int
	zeroIncomeDriverIds []string
}

func (r *dailyIncomeResult) AddSuccess(success ...string) {
	r.Result.AddSuccess(success...)
}

func (r *dailyIncomeResult) AddFail(fail ...string) {
	r.Result.AddFail(fail...)
}

func (r *dailyIncomeResult) AddProceeds(proceeds ...string) {
	r.Result.AddProceeds(proceeds...)
}

func (r *dailyIncomeResult) increaseTotalEmptyDriverId() {
	r.m.Lock()
	defer r.m.Unlock()
	r.totalEmptyDriverId++
}

func (r *dailyIncomeResult) addZeroIncomeDriverIds(zeroIncomeDriverIds ...string) {
	r.m.Lock()
	defer r.m.Unlock()
	r.zeroIncomeDriverIds = append(r.zeroIncomeDriverIds, zeroIncomeDriverIds...)
}

func newDailyIncomeResult() *dailyIncomeResult {
	r := NewResult()
	return &dailyIncomeResult{
		Result:              r,
		totalEmptyDriverId:  0,
		zeroIncomeDriverIds: []string{},
	}
}

type DailyIncomeTask struct {
	config                 DailyIncomeConfig
	driverRepo             repository.DriverRepository
	transactionRepo        repository.TransactionRepository
	incomeAggregateService aggregate.IncomeAggregateService
}

func ProvideDailyIncomeTask(
	config DailyIncomeConfig,
	driverRepo repository.DriverRepository,
	transactionRepo repository.TransactionRepository,
	incomeAggregateService aggregate.IncomeAggregateService,
) *DailyIncomeTask {
	return &DailyIncomeTask{
		config:                 config,
		driverRepo:             driverRepo,
		transactionRepo:        transactionRepo,
		incomeAggregateService: incomeAggregateService,
	}
}

func (b *DailyIncomeTask) Name() string {
	return "DailyIncome"
}

func (t *DailyIncomeTask) Execute(ctx context.Context, args ...interface{}) (Result, error) {
	now := time.Now()

	logrus.WithFields(
		logrus.Fields{
			"args": args,
		}).
		Info("daily income task started")

	r := newDailyIncomeResult()
	defer func() {
		logrus.WithFields(
			logrus.Fields{
				"elapsed_ms":               time.Since(now).Milliseconds(),
				"total_empty_driver_id":    r.totalEmptyDriverId,
				"total_zero_income_driver": len(r.zeroIncomeDriverIds),
			}).
			Info("daily income task done")
	}()

	if len(args) == 0 {
		return Result{}, errors.Wrap(ErrInvalidParam, "require argument")
	}

	var req dailyIncomeRequest
	if err := t.parse(ctx, args, &req); err != nil {
		return Result{}, err
	}

	r.mode = req.mode

	err := t.handle(ctx, req, r)
	if err != nil {
		return Result{}, err
	}

	return *r.Result, nil
}

func (t *DailyIncomeTask) parse(ctx context.Context, args []interface{}, req *dailyIncomeRequest) error {
	modeStr, ok := args[0].(string)
	if !ok {
		return errors.Wrap(ErrInvalidParam, "mode is not string")
	}

	var mode dailyIncomeMode
	switch modeStr {
	case string(DailyMode):
		mode = DailyMode
	case string(BackfillMode):
		mode = BackfillMode
	case string(RerunMode):
		mode = RerunMode
	default:
		return errors.Wrap(ErrInvalidParam, "mode not support")
	}

	req.mode = mode

	switch mode {
	case DailyMode:
		if err := t.parseDailyMode(ctx, req, args); err != nil {
			return errors.Wrapf(ErrInvalidParam, "parse daily mode error: %v", err)
		}
	case RerunMode:
		if err := t.parseRerunMode(req, args); err != nil {
			return errors.Wrapf(ErrInvalidParam, "parse rerun mode error: %v", err)
		}
	case BackfillMode:
		if err := t.parseBackfillMode(req, args); err != nil {
			return errors.Wrapf(ErrInvalidParam, "parse backfill mode error %v", err)
		}
	}

	return nil
}

func (t *DailyIncomeTask) parseDailyMode(ctx context.Context, req *dailyIncomeRequest, args []interface{}) error {
	if len(args) < 2 {
		return errors.Wrap(ErrInvalidParam, "daily mode args should at least 2")
	}

	date, ok := args[1].(string)
	if !ok {
		return errors.Wrap(ErrInvalidParam, "arg[1] is not string")
	}

	if !(date == "yesterday") {
		return errors.Wrap(ErrInvalidParam, "arg[1] is not valid")
	}

	if date == "yesterday" {
		now := timeutil.GetTimeFromContext(ctx)
		req.date = now.Add(-1 * 24 * time.Hour)
	}

	if len(args) > 2 {
		remarkStr, ok := args[2].(string)
		if !ok {
			return errors.Wrap(ErrInvalidParam, "arg[2] is not string")
		}

		req.remark = remarkStr
	}

	return nil
}

func (t *DailyIncomeTask) parseRerunMode(req *dailyIncomeRequest, args []interface{}) error {
	if len(args) < 3 {
		return errors.Wrap(ErrInvalidParam, "daily mode args should at least 3")
	}

	dateStr, ok := args[1].(string)
	if !ok {
		return errors.Wrap(ErrInvalidParam, "arg[1] is not string")
	}

	date, err := time.Parse("02/01/2006", dateStr)
	if err != nil {
		return errors.Wrap(ErrInvalidParam, "arg[1] is not in format dd/mm/yyyy")
	}

	req.date = timeutil.DateTruncateBKK(date)

	if len(args) > 2 {
		remarkStr, ok := args[2].(string)
		if !ok {
			return errors.Wrap(ErrInvalidParam, "arg[2] is not string")
		}

		req.remark = remarkStr
	}

	return nil
}

func (t *DailyIncomeTask) parseBackfillMode(req *dailyIncomeRequest, args []interface{}) error {
	if len(args) < 4 {
		return errors.Wrap(ErrInvalidParam, "daily mode args should at least 4")
	}

	fromStr, ok := args[1].(string)
	if !ok {
		return errors.Wrap(ErrInvalidParam, "arg[1] is not string")
	}

	from, err := time.Parse("02/01/2006", fromStr)
	if err != nil {
		return errors.Wrap(ErrInvalidParam, "arg[1] is not in format dd/mm/yyyy")
	}

	req.from = timeutil.DateTruncateBKK(from)

	toStr, ok := args[2].(string)
	if !ok {
		return errors.Wrap(ErrInvalidParam, "arg[2] is not string")
	}

	to, err := time.Parse("02/01/2006", toStr)
	if err != nil {
		return errors.Wrap(ErrInvalidParam, "arg[2] is not string")
	}

	req.to = to

	if len(args) > 3 {
		remarkStr, ok := args[3].(string)
		if !ok {
			return errors.Wrap(ErrInvalidParam, "arg[3] is not string")
		}

		req.remark = remarkStr
	}

	return nil
}

func (t *DailyIncomeTask) handle(ctx context.Context, req dailyIncomeRequest, result *dailyIncomeResult) error {
	switch req.mode {
	case DailyMode:
		err := t.handleDailyMode(ctx, req, result)
		if err != nil {
			return errors.WithMessage(err, "handle daily mode error")
		}
	case RerunMode:
		err := t.handleRerunMode(ctx, req, result)
		if err != nil {
			return errors.WithMessage(err, "handle rerun mode error")
		}
	case BackfillMode:
		err := t.handleBackfill(ctx, req, result)
		if err != nil {
			return errors.WithMessage(err, "handle backfill mode error")
		}
	}

	return nil
}

func (t *DailyIncomeTask) handleDailyMode(ctx context.Context, req dailyIncomeRequest, result *dailyIncomeResult) error {
	runDate := req.date
	opts := []aggregate.IncomeAggregateOption{aggregate.WithRemark(req.remark)}

	err := t.executeDailyRun(ctx, runDate, result, opts...)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("handler daily mode error on date: %s", runDate))
	}

	return nil
}

func (t *DailyIncomeTask) handleRerunMode(ctx context.Context, req dailyIncomeRequest, result *dailyIncomeResult) error {
	runDate := req.date
	opts := []aggregate.IncomeAggregateOption{aggregate.WithRemark(req.remark)}

	err := t.executeDailyRun(ctx, runDate, result, opts...)
	if err != nil {
		return errors.Wrap(err, fmt.Sprintf("handler rerun mode error on date: %s", runDate))
	}

	return nil
}

func (t *DailyIncomeTask) handleBackfill(ctx context.Context, req dailyIncomeRequest, result *dailyIncomeResult) error {
	opts := []aggregate.IncomeAggregateOption{aggregate.WithRemark(req.remark)}

	for d := req.from; d.Before(req.to); d = d.AddDate(0, 0, 1) {
		runDate := d
		err := t.executeDailyRun(ctx, runDate, result, opts...)
		if err != nil {
			return errors.Wrap(err, fmt.Sprintf("handler backfill mode error on date: %s", runDate))
		}
	}

	return nil
}

func (t *DailyIncomeTask) executeDailyRun(ctx context.Context, runDate time.Time, result *dailyIncomeResult, opts ...aggregate.IncomeAggregateOption) error {
	activeDrivers, err := t.getActiveDrivers(runDate, ctx)
	if err != nil {
		return errors.Wrap(err, "unable to find active drivers")
	}

	logrus.WithFields(
		logrus.Fields{
			"active_drivers": len(activeDrivers),
			"run_date":       runDate,
		}).
		Info("execute daily mode started")

	if result.mode != BackfillMode {
		result.AddProceeds(activeDrivers...)
	}

	batchSize := t.config.DailyIncomeBatchSize

	worker, release := safe.NewWorker(t.config.DailyIncomeWorkerPoolSize)
	defer release()

	wg := sync.WaitGroup{}

	for i := 0; i < len(activeDrivers); i += batchSize {
		wg.Add(1)
		// Get the end index for the current batch
		end := i + batchSize
		if end > len(activeDrivers) {
			end = len(activeDrivers)
		}

		// Get the current batch
		batch := activeDrivers[i:end]

		worker.GoFuncWithPool(func() {
			defer wg.Done()
			drivers := batch
			for _, driverId := range drivers {
				if driverId == "" {
					result.increaseTotalEmptyDriverId()
					continue
				}
				err := t.incomeAggregateService.Aggregate(ctx, aggregate.IncomeAggregateRequest{
					Date:     runDate,
					DriverId: driverId,
				}, opts...)
				if err != nil {
					switch {
					case errors.Is(err, aggregate.ErrZeroIncome):
						logrus.WithFields(logrus.Fields{
							"driver_id": driverId,
						}).Info("skipped this driver due to 0 income")
						result.addZeroIncomeDriverIds(driverId)
						continue
					default:
						logrus.WithFields(logrus.Fields{
							"run_date":  runDate,
							"driver_id": driverId,
						}).Error("unable to aggregate this driver")
						if result.mode != BackfillMode {
							result.AddFail(driverId)
						}
						continue
					}
				}
				if result.mode != BackfillMode {
					result.AddSuccess(driverId)
				}
			}
		})
	}

	wg.Wait()

	return nil
}

func (t *DailyIncomeTask) getActiveDrivers(runDate time.Time, ctx context.Context) ([]string, error) {
	fromInclusive := timeutil.DateTruncate(runDate)
	toExclusive := fromInclusive.AddDate(0, 0, 1)

	activeDrivers, err := t.transactionRepo.FindListActiveDriverIdsInRange(ctx, fromInclusive, toExclusive, repository.WithReadSecondaryPreferred)
	if err != nil {
		return nil, err
	}
	return activeDrivers, nil
}
