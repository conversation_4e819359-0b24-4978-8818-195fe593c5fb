package cron

import (
	"context"

	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type TodayEarningTask struct {
	orderRepo  repository.OrderRepository
	driverRepo repository.DriverRepository
}

func ProvideTodayEarningTask(orderRepo repository.OrderRepository, driverRepo repository.DriverRepository) *TodayEarningTask {
	return &TodayEarningTask{
		orderRepo:  orderRepo,
		driverRepo: driverRepo,
	}
}

func (tt *TodayEarningTask) Execute(ctx context.Context, params ...interface{}) (Result, error) {
	if len(params) <= 0 {
		return Result{}, ErrInvalidParam
	}
	driverIDs, ok := params[0].([]string)
	if !ok {
		return Result{}, ErrInvalidParam
	}

	result := NewResult()

	if len(driverIDs) != 0 {
		if err := tt.calculateAndUpdateDriverEarning(ctx, driverIDs); err != nil {
			result.AddFail(driverIDs...)
			logrus.WithFields(logrus.Fields{
				"job":     tt.Name(),
				"drivers": driverIDs,
			}).Errorf("fail to update earning error : %s", err)
		}

		result.AddSuccess(driverIDs...)
	} else {
		driverCount, err := tt.driverRepo.CountDriver(ctx)
		if err != nil {
			return Result{}, err
		}

		batchSize := 100
		for i, round := 0, (driverCount/batchSize)+1; i < round; i++ {
			drivers, err := tt.driverRepo.FindSorted(ctx, i*batchSize, batchSize)
			if err != nil {
				logrus.WithFields(logrus.Fields{
					"job": tt.Name(),
				}).Errorf("find driver error : %s", err)
				continue
			}

			driverIDs := make([]string, len(drivers))
			for i, d := range drivers {
				driverIDs[i] = d.DriverID
			}

			if err := tt.calculateAndUpdateDriverEarning(ctx, driverIDs); err != nil {
				result.AddFail(driverIDs...)
				logrus.WithFields(logrus.Fields{
					"job":     tt.Name(),
					"drivers": driverIDs,
				}).Errorf("fail to update earning error : %s", err)
			}

			result.AddSuccess(driverIDs...)
		}
	}

	return *result, nil
}

func (tt *TodayEarningTask) Name() string {
	return "today-earning"
}

func (tt *TodayEarningTask) calculateAndUpdateDriverEarning(ctx context.Context, driverIDs []string) error {
	orders, err := tt.orderRepo.FindTodayCompleteOrderByDriver(ctx, driverIDs, timeutil.BangkokLocation())
	if err != nil {
		return err
	}

	driverEarning := make(map[string]model.DriverEarning)
	for _, order := range orders {
		earning, ok := driverEarning[order.Driver]
		if !ok {
			earning = *model.NewDriverEarning(order.Driver, types.NewMoney(0), types.NewMoney(0), types.NewMoney(0), types.NewMoney(0))
		}

		deliverySumary := order.PriceSummary()
		summary := deliverySumary.DeliveryFee
		fee := types.NewMoney(summary.SubTotal)
		commission, tax := types.NewMoney(summary.Commission), types.NewMoney(summary.WithholdingTax)
		earning.Add(fee, commission, tax)

		driverEarning[order.Driver] = earning
	}

	earningUpdate := make([]model.DriverEarning, 0, len(orders))
	for _, earning := range driverEarning {
		earningUpdate = append(earningUpdate, earning)
	}

	if len(earningUpdate) != 0 {
		if err := tt.driverRepo.SetTodayEarning(ctx, earningUpdate...); err != nil {
			return err
		}
	}

	return nil
}
