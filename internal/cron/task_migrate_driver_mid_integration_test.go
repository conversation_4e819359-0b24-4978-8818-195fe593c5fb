//go:build integration_test
// +build integration_test

package cron_test

import (
	"context"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	fixturetestdata "git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestMigrateDriverMIDTask_Execute(goTest *testing.T) {
	goTest.Setenv("LINE_INTERNAL_HOST", "https://line-internal-api-host")
	goTest.Setenv("MID_MIGRATOR_LINE_CHANNEL_ID", "line_channel_id_config")
	goTest.Setenv("MID_MIGRATOR_LINE_CHANNEL_SECRET", "line_channel_id_secret")

	goTest.Run("run once with 2 drivers, exist mid and non-exist one", func(t *testing.T) {
		t.Setenv("MID_MIGRATOR_EXIT_TIME", "")
		t.Setenv("MID_MIGRATOR_MAX_RUN", "1")
		t.Setenv("MID_RATE_LIMIT_DURATION", "0.0000001")

		ctx := context.Background()
		ctn := ittest.NewContainer(goTest)
		cleanAllMongoCollection(ctx, t, ctn.DBConnectionForTest.Database())
		ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_mid_migration")
		ctn.DriverRegistrationDataStore.RemoveAll(ctx, bson.M{})

		fakeAPIHandler := GetFakeLINEBulkEncryptionResponder(
			"line_channel_id_config",
			"line_channel_id_secret",
			map[string]string{
				"u012345678901234567890123456789aa": "uid_incomplete_preregistration",
			},
		)
		httpmock.RegisterResponder(http.MethodGet, "https://line-internal-api-host/v1/internal/mid/encrypt/bulk", fakeAPIHandler)

		ctn.MigrateDriverMIDTask.Execute(ctx)

		driverA, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_A")
		require.NoError(t, findErr)
		require.Equal(t, "uid_incomplete_preregistration", driverA.LINEUserID)
		require.Equal(t, 0, driverA.LINEUserIDRetry)

		driverB, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_B")
		require.NoError(t, findErr)
		require.Equal(t, "", driverB.LINEUserID)
		require.Equal(t, 1, driverB.LINEUserIDRetry)
	})

	goTest.Run("run two times with 2 drivers, exist mid and non-exist one", func(t *testing.T) {
		t.Setenv("MID_MIGRATOR_EXIT_TIME", "")
		t.Setenv("MID_MIGRATOR_MAX_RUN", "2")
		t.Setenv("MID_MIGRATOR_RATE_LIMIT_DURATION", "0.0000001")

		ctx := context.Background()
		ctn := ittest.NewContainer(goTest)
		cleanAllMongoCollection(ctx, t, ctn.DBConnectionForTest.Database())
		ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_mid_migration")
		ctn.DriverRegistrationDataStore.RemoveAll(ctx, bson.M{})

		fakeAPIHandler := GetFakeLINEBulkEncryptionResponder(
			"line_channel_id_config",
			"line_channel_id_secret",
			map[string]string{
				"u012345678901234567890123456789aa": "uid_incomplete_preregistration",
			},
		)
		httpmock.RegisterResponder(http.MethodGet, "https://line-internal-api-host/v1/internal/mid/encrypt/bulk", fakeAPIHandler)

		ctn.MigrateDriverMIDTask.Execute(ctx)

		driverA, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_A")
		require.NoError(t, findErr)
		require.Equal(t, "uid_incomplete_preregistration", driverA.LINEUserID)
		require.Equal(t, 0, driverA.LINEUserIDRetry)

		driverB, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_B")
		require.NoError(t, findErr)
		require.Equal(t, "", driverB.LINEUserID)
		require.Equal(t, 2, driverB.LINEUserIDRetry)
	})

	goTest.Run("run two times with 2 drivers, exist mid and non-exist one and limit 2 retry", func(t *testing.T) {
		currentTime := timeutil.BangkokNow()
		t.Setenv("MID_MIGRATOR_EXIT_TIME", fmt.Sprintf("%02d:%02d:%02d", currentTime.Hour(), currentTime.Minute(), 0))
		t.Setenv("MID_MIGRATOR_MAX_RUN", "3")
		t.Setenv("MID_MIGRATOR_RETRY_COUNT", "2")
		t.Setenv("MID_MIGRATOR_RATE_LIMIT_DURATION", "0.0000001")

		ctx := context.Background()
		ctn := ittest.NewContainer(goTest)
		cleanAllMongoCollection(ctx, t, ctn.DBConnectionForTest.Database())
		ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_mid_migration")
		ctn.DriverRegistrationDataStore.RemoveAll(ctx, bson.M{})

		fakeAPIHandler := GetFakeLINEBulkEncryptionResponder(
			"line_channel_id_config",
			"line_channel_id_secret",
			map[string]string{
				"u012345678901234567890123456789aa": "uid_incomplete_preregistration",
			},
		)
		httpmock.RegisterResponder(http.MethodGet, "https://line-internal-api-host/v1/internal/mid/encrypt/bulk", fakeAPIHandler)

		ctn.MigrateDriverMIDTask.Execute(ctx)

		driverA, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_A")
		require.NoError(t, findErr)
		require.Equal(t, "uid_incomplete_preregistration", driverA.LINEUserID)
		require.Equal(t, 0, driverA.LINEUserIDRetry)

		driverB, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_B")
		require.NoError(t, findErr)
		require.Equal(t, "", driverB.LINEUserID)
		require.Equal(t, 2, driverB.LINEUserIDRetry)
	})

	goTest.Run("run both drivers and drivers_registration", func(t *testing.T) {
		// rate limit 1 request per a secound (round)
		t.Setenv("MID_MIGRATOR_REQUEST_SIZE_LIMIT_PER_SECOND", "1")
		// each request will have a maximum of 2 MIDs
		t.Setenv("MID_MIGRATOR_BULK_MID_SIZE", "2")

		currentTime := timeutil.BangkokNow()
		t.Setenv("MID_MIGRATOR_RETRY_COUNT", "2")
		t.Setenv("MID_MIGRATOR_EXIT_TIME", fmt.Sprintf("%02d:%02d:%02d", currentTime.Add(time.Hour*-1).Hour(), currentTime.Minute(), 0))
		t.Setenv("MID_MIGRATOR_MAX_RUN", "0")
		t.Setenv("MID_MIGRATOR_RATE_LIMIT_DURATION", "0.0000001")

		ctx := context.Background()
		ctn := ittest.NewContainer(goTest)
		cleanAllMongoCollection(ctx, t, ctn.DBConnectionForTest.Database())
		ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_mid_migration")

		fakeAPIHandler := GetFakeLINEBulkEncryptionResponder(
			"line_channel_id_config",
			"line_channel_id_secret",
			map[string]string{
				"u012345678901234567890123456789aa": "uid_incomplete_preregistration",
				"u012345678901234567890123456789ac": "uid_drivers_reg_1",
				"u012345678901234567890123456789ad": "uid_drivers_reg_3",
			},
		)
		httpmock.RegisterResponder(http.MethodGet, "https://line-internal-api-host/v1/internal/mid/encrypt/bulk", fakeAPIHandler)

		ctn.MigrateDriverMIDTask.Execute(ctx)

		driverA, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_A")
		require.NoError(t, findErr)
		require.Equal(t, "uid_incomplete_preregistration", driverA.LINEUserID)
		require.Equal(t, 0, driverA.LINEUserIDRetry)

		driverB, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_B")
		require.NoError(t, findErr)
		require.Equal(t, "", driverB.LINEUserID)
		require.Equal(t, 2, driverB.LINEUserIDRetry)

		driverRegA, findErr := ctn.DriverRegistrationRepository.FindOneByID(ctx, fixturetestdata.ObjectId(1))
		require.NoError(t, findErr)
		require.Equal(t, "uid_drivers_reg_1", driverRegA.LINEUserID)
		require.Equal(t, 0, driverRegA.LINEUserIDRetry)

		driverRegB, findErr := ctn.DriverRegistrationRepository.FindOneByID(ctx, fixturetestdata.ObjectId(2))
		require.NoError(t, findErr)
		require.Equal(t, "", driverRegB.LINEUserID)
		require.Equal(t, "นาย", driverRegB.Title.String())
		require.Equal(t, "driver_2", driverRegB.Firstname.String())
		require.Equal(t, 2, driverRegB.LINEUserIDRetry)

		driverRegC, findErr := ctn.DriverRegistrationRepository.FindOneByID(ctx, fixturetestdata.ObjectId(3))
		require.NoError(t, findErr)
		require.Equal(t, "นาย", driverRegC.Title.String())
		require.Equal(t, "driver_3", driverRegC.Firstname.String())
		require.Equal(t, "uid_drivers_reg_3", driverRegC.LINEUserID)
		require.Equal(t, 0, driverRegC.LINEUserIDRetry)
	})

	goTest.Run("run both drivers and drivers_registration but got errors", func(t *testing.T) {
		// rate limit 10 request per a secound (round)
		t.Setenv("MID_MIGRATOR_REQUEST_SIZE_LIMIT_PER_SECOND", "10")
		// each request will have a maximum of 10 MIDs
		t.Setenv("MID_MIGRATOR_BULK_MID_SIZE", "10")

		currentTime := timeutil.BangkokNow()
		t.Setenv("MID_MIGRATOR_RETRY_COUNT", "3")
		t.Setenv("MID_MIGRATOR_EXIT_TIME", fmt.Sprintf("%02d:%02d:%02d", currentTime.Add(time.Hour).Hour(), currentTime.Minute(), 0))
		t.Setenv("MID_MIGRATOR_MAX_RUN", "0")
		t.Setenv("MID_MIGRATOR_RATE_LIMIT_DURATION", "0.0000001")

		ctx := context.Background()
		ctn := ittest.NewContainer(goTest)
		cleanAllMongoCollection(ctx, t, ctn.DBConnectionForTest.Database())
		ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_mid_migration")
		ctn.DriversDataStore.FindOneAndUpdate(ctx, bson.M{"driver_id": "DRIVER_B"}, bson.M{"$set": bson.M{"line_uid": crypt.NewLazyEncryptedString("invalid_mid")}})
		targetID, _ := primitive.ObjectIDFromHex(fixturetestdata.ObjectId(1))
		ctn.DriverRegistrationDataStore.FindOneAndUpdate(ctx, bson.M{"_id": targetID}, bson.M{"$set": bson.M{"line_uid": crypt.NewLazyEncryptedString("invalid_mid")}})

		httpmock.RegisterResponder(http.MethodGet, "https://line-internal-api-host/v1/internal/mid/encrypt/bulk",
			func(req *http.Request) (*http.Response, error) {
				assert.Nil(t, req.Body)

				require.Equal(t, "line_channel_id_config", req.Header.Get("X-Line-ChannelID"))
				require.Equal(t, "line_channel_id_secret", req.Header.Get("X-Line-ChannelSecret"))

				return httpmock.NewStringResponse(http.StatusBadRequest, `{"statusCode":"400","statusMessage":"Invalid mid"}`), nil
			})

		ctn.MigrateDriverMIDTask.Execute(ctx)

		driverA, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_A")
		require.NoError(t, findErr)
		require.Equal(t, "", driverA.LINEUserID)
		require.Equal(t, 3, driverA.LINEUserIDRetry)

		driverB, findErr := ctn.DriverRepository.FindDriverID(ctx, "DRIVER_B")
		require.NoError(t, findErr)
		require.Equal(t, "", driverB.LINEUserID)
		require.Equal(t, 100, driverB.LINEUserIDRetry)

		driverRegA, findErr := ctn.DriverRegistrationRepository.FindOneByID(ctx, fixturetestdata.ObjectId(1))
		require.NoError(t, findErr)
		require.Equal(t, "", driverRegA.LINEUserID)
		require.Equal(t, 100, driverRegA.LINEUserIDRetry)

		driverRegB, findErr := ctn.DriverRegistrationRepository.FindOneByID(ctx, fixturetestdata.ObjectId(2))
		require.NoError(t, findErr)
		require.Equal(t, "", driverRegB.LINEUserID)
		require.Equal(t, 3, driverRegB.LINEUserIDRetry)

		driverRegC, findErr := ctn.DriverRegistrationRepository.FindOneByID(ctx, fixturetestdata.ObjectId(3))
		require.NoError(t, findErr)
		require.Equal(t, "", driverRegC.LINEUserID)
		require.Equal(t, 3, driverRegC.LINEUserIDRetry)
	})
}

func cleanAllMongoCollection(ctx context.Context, t *testing.T, dbConn *mongo.Database) {
	collections, err := dbConn.ListCollectionNames(ctx, bson.M{})
	require.NoError(t, err)
	for _, collectionName := range collections {
		_, err := dbConn.Collection(collectionName).DeleteMany(ctx, bson.M{})
		require.NoError(t, err)
	}
}

func GetFakeLINEBulkEncryptionResponder(expectedChannelID string, expectedSecretID string, midToUID map[string]string) func(req *http.Request) (*http.Response, error) {
	return func(req *http.Request) (*http.Response, error) {
		channelID := req.Header.Get("X-Line-ChannelID")
		if channelID != expectedChannelID {
			errResp := lineinternal.EncryptMIDsErrorResponse{
				StatusCode:    "401",
				StatusMessage: "channel id is not matched.",
			}
			return httpmock.NewStringResponse(http.StatusNotFound, errResp.ToJSONString()), nil
		}
		channelSecret := req.Header.Get("X-Line-ChannelSecret")
		if channelSecret != expectedSecretID {
			errResp := lineinternal.EncryptMIDsErrorResponse{
				StatusCode:    "401",
				StatusMessage: "channel secret is not matched.",
			}
			return httpmock.NewStringResponse(http.StatusNotFound, errResp.ToJSONString()), nil
		}

		mids := req.URL.Query()["mids"]

		if mids == nil {
			errResp := lineinternal.EncryptMIDsErrorResponse{
				StatusCode:    "400",
				StatusMessage: "Parameter conditions \"mids\" OR \"channelId, mids\" not met for actual request parameters: ",
			}
			return httpmock.NewStringResponse(http.StatusBadRequest, errResp.ToJSONString()), nil
		}

		if len(mids) == 0 {
			resp := lineinternal.EncryptMIDsResponse{
				UserIDs: make(map[string]string),
			}
			return httpmock.NewStringResponse(http.StatusOK, resp.ToJSONString()), nil
		}

		userIDs := make(map[string]string)
		for _, mid := range mids {
			if mid == "" {
				errResp := lineinternal.EncryptMIDsErrorResponse{
					StatusCode:    "400",
					StatusMessage: "Invalid mid",
				}
				return httpmock.NewStringResponse(http.StatusBadRequest, errResp.ToJSONString()), nil
			}

			if uid := midToUID[mid]; uid != "" {
				userIDs[mid] = uid
			}
		}

		resp := lineinternal.EncryptMIDsResponse{
			UserIDs: userIDs,
		}
		return httpmock.NewStringResponse(http.StatusOK, resp.ToJSONString()), nil
	}
}
