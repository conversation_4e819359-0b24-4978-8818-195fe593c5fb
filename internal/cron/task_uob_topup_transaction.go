package cron

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/cristalhq/jwt/v3"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	"git.wndv.co/lineman/fleet-distribution/internal/cryptography"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const (
	timeFormat = "15:04"
	dateFormat = "********"
)

type UobTopupTransactionTask struct {
	http *httpclient.Client
	cfg  partners.Config
}

func (it *UobTopupTransactionTask) Name() string {
	return "UobTopupTransaction"
}

type UOBTransactionRequest struct {
	TransactionReference string            `json:"transactionReference"`
	Account              UOBAccountRequest `json:"account"`
}

type UOBAccountRequest struct {
	AccountNumber   string `json:"accountNumber"`
	AccountCurrency string `json:"accountCurrency"`
	AccountType     string `json:"accountType"`
}

func ProvideUobTopupTransactionTask(http *httpclient.Client, cfg partners.Config) *UobTopupTransactionTask {
	return &UobTopupTransactionTask{
		http: http,
		cfg:  cfg,
	}
}

func (it *UobTopupTransactionTask) Execute(ctx context.Context, _ ...interface{}) (Result, error) {
	result := NewResult()
	result.AddProceeds("PollingUobTopupTransactionTask")

	token, err := it.getJwtToken()
	if err != nil {
		return Result{}, err
	}
	now := timeutil.BangkokNow()
	fromTime := now.Add(time.Duration(-it.cfg.UOBTransactionTimeRange) * time.Minute)

	url := fmt.Sprintf("%s/business/v2/accounts/transaction?fromDate=%s&toDate=%s&fromTime=%s&toTime=%s",
		it.cfg.UOBBaseUrl, fromTime.Format(dateFormat), now.Format(dateFormat), fromTime.Format(timeFormat), now.Format(timeFormat))

	response, err := it.http.Get(ctx, url, setHeader(it.cfg, token.String()))
	if err != nil {
		msg := fmt.Sprintf("an error occurred when get account transaction information: %v", err)
		logrus.Error(msg)
		result.AddFail(msg)
		return Result{}, err
	}
	defer response.Body.Close()
	if response.StatusCode != http.StatusOK {
		return Result{}, errors.New("get account transaction information http request failed, status code is not 200")
	}

	msg := fmt.Sprintf("Polling UOB Topup Transaction success, response: %v", response.Body)
	logrus.Info(msg)
	result.AddSuccess(msg)
	return *result, nil
}

func (it *UobTopupTransactionTask) getJwtToken() (*jwt.Token, error) {
	key, err := cryptography.GetRSAPrivateKey(it.cfg.LMWNPrivate)
	if err != nil {
		return nil, err
	}

	signer, err := jwt.NewSignerRS(jwt.RS256, key)
	if err != nil {
		return nil, err
	}

	builder := jwt.NewBuilder(signer)

	pl := getPayload(it.cfg)
	token, err := builder.Build(pl)
	if err != nil {
		return nil, err
	}
	return token, nil
}

func getPayload(cfg partners.Config) UOBTransactionRequest {
	return UOBTransactionRequest{TransactionReference: "TransactionHistory", Account: UOBAccountRequest{
		AccountNumber:   cfg.UOBAccountNumber,
		AccountCurrency: cfg.UOBAccountCurrency,
		AccountType:     cfg.UOBAccountType,
	}}
}

func setHeader(cfg partners.Config, jwt string) http.Header {
	hd := http.Header{}
	hd.Add("Application-ID", cfg.UOBApplicationID)
	hd.Add("API-Key", cfg.UOBApiKey)
	hd.Add("Client-ID", cfg.UOBClientID)
	hd.Add("Country", cfg.UOBCountry)
	hd.Add("Authorization", jwt)
	return hd
}
