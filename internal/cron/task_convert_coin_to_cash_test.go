package cron

import (
	"context"
	"strings"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	dmrv1 "git.wndv.co/go/proto/lineman/driver_mission_reward/v1"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/coinplan/mock_coinplan"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestConvertCoinToCash_Execute(t *testing.T) {
	e := errors.New("err")
	flahCoin2Vault := featureflag.IsEnabledCoinStore.Name

	t.Run("happy case", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		driverID := "driver-id-coin2cash"
		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   driverID,
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: driverID,
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(&model.CoinCashConversionRate{
				Region: "BKK",
				Scheme: model.CoinCashConversionRateScheme{
					Tiers: []model.CoinCashTier{
						{
							Coin: 2,
							Cash: 5,
						},
					},
				},
			}, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.rewardTransactionProcessor.EXPECT().
			GetRewardTransactionsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RewardTransaction{}, nil)

		deps.rewardTransactionProcessor.EXPECT().
			ProcessRewardTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		deps.driverTransactionSvc.EXPECT().
			ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, driverID string, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus, builder service.TransactionInfosBuilder, opts ...func(option *service.ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error) {
				dt := model.NewDriverTransaction(driverID)
				txninfos, err := builder(*dt)
				if err != nil {
					return model.DriverTransaction{}, nil, err
				}
				require.Len(tt, txninfos, 2)
				require.Equal(tt, model.IncentiveTransactionType, txninfos[0].Type)
				require.Equal(tt, model.WalletTransactionCategory, txninfos[0].Category)
				require.Equal(tt, types.Money(5), txninfos[0].Amount)
				require.Equal(tt, model.WithholdingTransactionType, txninfos[1].Type)
				require.Equal(tt, model.CreditTransactionCategory, txninfos[1].Category)
				require.Equal(tt, types.Money(0.15), txninfos[1].Amount)

				require.Len(tt, opts, 2)
				return *dt, model.NewTransactionsFromInfos(utils.GenerateUUID(), model.SystemTransactionChannel, model.ConvertCoinToCashAction, model.SuccessTransactionStatus, txninfos), nil
			})

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("happy, coinplan=coin2cash", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		driverID := "driver-id-coin2cash"
		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   driverID,
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(true)

		deps.coinPlan.EXPECT().
			GetCoinPlan(gomock.Any(), driverID, gomock.Any()).
			Return(dmrv1.CoinPlan_COIN_PLAN_CONVERT_COIN_TO_CASH, nil)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: driverID,
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(&model.CoinCashConversionRate{
				Region: "BKK",
				Scheme: model.CoinCashConversionRateScheme{
					Tiers: []model.CoinCashTier{
						{
							Coin: 2,
							Cash: 5,
						},
					},
				},
			}, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.rewardTransactionProcessor.EXPECT().
			GetRewardTransactionsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RewardTransaction{}, nil)

		deps.rewardTransactionProcessor.EXPECT().
			ProcessRewardTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		deps.driverTransactionSvc.EXPECT().
			ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, driverID string, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus, builder service.TransactionInfosBuilder, opts ...func(option *service.ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error) {
				dt := model.NewDriverTransaction(driverID)
				txninfos, err := builder(*dt)
				if err != nil {
					return model.DriverTransaction{}, nil, err
				}
				require.Len(tt, txninfos, 2)
				require.Equal(tt, model.IncentiveTransactionType, txninfos[0].Type)
				require.Equal(tt, model.WalletTransactionCategory, txninfos[0].Category)
				require.Equal(tt, types.Money(5), txninfos[0].Amount)
				require.Equal(tt, model.WithholdingTransactionType, txninfos[1].Type)
				require.Equal(tt, model.CreditTransactionCategory, txninfos[1].Category)
				require.Equal(tt, types.Money(0.15), txninfos[1].Amount)

				require.Len(tt, opts, 2)
				return *dt, model.NewTransactionsFromInfos(utils.GenerateUUID(), model.SystemTransactionChannel, model.ConvertCoinToCashAction, model.SuccessTransactionStatus, txninfos), nil
			})

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("happy, coinplan=coin2vault - create daily rewards", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		driverID := "driver-id-coin2cash"
		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   driverID,
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(true)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: driverID,
			}, nil)

		deps.coinPlan.EXPECT().
			GetCoinPlan(gomock.Any(), driverID, gomock.Any()).
			Return(dmrv1.CoinPlan_COIN_PLAN_CONVERT_COIN_TO_VAULT, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any()).
			Return(nil, nil)

		deps.dailyRewardRepo.EXPECT().
			GetOrCreateDailyReward(gomock.Any(), driverID, gomock.Any()).
			Return(model.DailyReward{}, mongodb.ErrDataNotFound)

		someTime := time.Now()

		deps.rewardBalanceRepo.EXPECT().
			Get(gomock.Any(), driverID).
			Return(model.RewardBalance{
				CoinAmount: 70, // Note that the driver has old daily balance of 70
				CreatedAt:  someTime,
			}, nil)

		deps.rewardBalanceRepo.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, data model.RewardBalance) error {
				require.Equal(t, driverID, data.DriverID)
				require.Equal(t, 0, data.CoinAmount) // But 0 will be sent to reset this guy's balance (due to coin2vault)
				require.True(t, someTime.Equal(data.CreatedAt))
				return nil
			})

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("happy, coinplan=coin2vault - reset update balance", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		driverID := "driver-id-coin2cash"
		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   driverID,
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(true)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: driverID,
			}, nil)

		deps.coinPlan.EXPECT().
			GetCoinPlan(gomock.Any(), driverID, gomock.Any()).
			Return(dmrv1.CoinPlan_COIN_PLAN_CONVERT_COIN_TO_VAULT, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any()).
			Return(nil, nil)

		deps.dailyRewardRepo.EXPECT().
			GetOrCreateDailyReward(gomock.Any(), driverID, gomock.Any()).
			Return(model.DailyReward{}, nil)

		deps.dailyRewardRepo.EXPECT().
			UpdateDailyReward(gomock.Any(), gomock.Any()).
			Return(nil)

		deps.rewardBalanceRepo.EXPECT().
			Get(gomock.Any(), driverID).
			Return(model.RewardBalance{}, nil)

		deps.rewardBalanceRepo.EXPECT().
			Update(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, data model.RewardBalance) error {
				require.Equal(t, driverID, data.DriverID)
				require.Equal(t, 0, data.CoinAmount)
				return nil
			})

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("happy, coinplan=coin2vault - reset create balance", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		driverID := "driver-id-coin2cash"
		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   driverID,
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(true)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: driverID,
			}, nil)

		deps.coinPlan.EXPECT().
			GetCoinPlan(gomock.Any(), driverID, gomock.Any()).
			Return(dmrv1.CoinPlan_COIN_PLAN_CONVERT_COIN_TO_VAULT, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any()).
			Return(nil, nil)

		deps.dailyRewardRepo.EXPECT().
			GetOrCreateDailyReward(gomock.Any(), driverID, gomock.Any()).
			Return(model.DailyReward{}, nil)

		deps.dailyRewardRepo.EXPECT().
			UpdateDailyReward(gomock.Any(), gomock.Any()).
			Return(nil)

		deps.rewardBalanceRepo.EXPECT().
			Get(gomock.Any(), driverID).
			Return(model.RewardBalance{}, mongodb.ErrDataNotFound)

		deps.rewardBalanceRepo.EXPECT().
			Create(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, argDriverID string) (model.RewardBalance, error) {
				require.Equal(t, driverID, argDriverID)
				return model.RewardBalance{DriverID: driverID}, nil
			})

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("should able to get priority condition as first priority if defined", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   "a",
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).
			Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: "a",
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(&model.CoinCashConversionRate{
				Region: "BKK",
				Scheme: model.CoinCashConversionRateScheme{
					Tiers: []model.CoinCashTier{
						{
							Coin: 2,
							Cash: 5,
						},
					},
				},
				Conditions: []model.DynamicCoinCashConversionRateCondition{
					{
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Scheme: model.CoinCashConversionRateScheme{
							Tiers: []model.CoinCashTier{
								{
									Coin: 2,
									Cash: 6,
								},
							},
						},
					},
				},
				PriorityConditions: []model.PriorityCoinCashConversionRateCondition{
					{
						DriverIds: []string{"a"},
						DynamicScheme: model.DynamicCoinCashConversionRateCondition{
							Days: []model.Days{
								model.Monday,
								model.Tuesday,
								model.Wednesday,
								model.Thursday,
								model.Friday,
								model.Saturday,
								model.Sunday,
							},
							Scheme: model.CoinCashConversionRateScheme{
								Tiers: []model.CoinCashTier{
									{
										Coin: 2,
										Cash: 7,
									},
								},
							},
						},
					},
				},
			}, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.rewardTransactionProcessor.EXPECT().
			GetRewardTransactionsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RewardTransaction{}, nil)

		deps.rewardTransactionProcessor.EXPECT().
			ProcessRewardTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		deps.driverTransactionSvc.EXPECT().
			ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, driverID string, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus, builder service.TransactionInfosBuilder, opts ...func(option *service.ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error) {
				dt := model.NewDriverTransaction(driverID)
				txninfos, err := builder(*dt)
				if err != nil {
					return model.DriverTransaction{}, nil, err
				}
				require.Len(tt, txninfos, 2)
				require.Equal(tt, model.IncentiveTransactionType, txninfos[0].Type)
				require.Equal(tt, model.WalletTransactionCategory, txninfos[0].Category)
				require.Equal(tt, types.Money(7), txninfos[0].Amount)
				require.Equal(tt, model.WithholdingTransactionType, txninfos[1].Type)
				require.Equal(tt, model.CreditTransactionCategory, txninfos[1].Category)
				require.Equal(tt, types.Money(0.21), txninfos[1].Amount)

				require.Len(tt, opts, 2)
				return *dt, model.NewTransactionsFromInfos(utils.GenerateUUID(), model.SystemTransactionChannel, model.ConvertCoinToCashAction, model.SuccessTransactionStatus, txninfos), nil
			})

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("should able to get dynamic condition as second priority if priority condition not met", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   "a",
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).
			Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: "a",
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(&model.CoinCashConversionRate{
				Region: "BKK",
				Scheme: model.CoinCashConversionRateScheme{
					Tiers: []model.CoinCashTier{
						{
							Coin: 2,
							Cash: 5,
						},
					},
				},
				Conditions: []model.DynamicCoinCashConversionRateCondition{
					{
						Days: []model.Days{
							model.Monday,
							model.Tuesday,
							model.Wednesday,
							model.Thursday,
							model.Friday,
							model.Saturday,
							model.Sunday,
						},
						Scheme: model.CoinCashConversionRateScheme{
							Tiers: []model.CoinCashTier{
								{
									Coin: 2,
									Cash: 6,
								},
							},
						},
					},
				},
				PriorityConditions: []model.PriorityCoinCashConversionRateCondition{
					{
						DriverIds: []string{"b"},
						DynamicScheme: model.DynamicCoinCashConversionRateCondition{
							Days: []model.Days{
								model.Monday,
								model.Tuesday,
								model.Wednesday,
								model.Thursday,
								model.Friday,
								model.Saturday,
								model.Sunday,
							},
							Scheme: model.CoinCashConversionRateScheme{
								Tiers: []model.CoinCashTier{
									{
										Coin: 2,
										Cash: 7,
									},
								},
							},
						},
					},
				},
			}, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.rewardTransactionProcessor.EXPECT().
			GetRewardTransactionsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RewardTransaction{}, nil)

		deps.rewardTransactionProcessor.EXPECT().
			ProcessRewardTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		deps.driverTransactionSvc.EXPECT().
			ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, driverID string, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus, builder service.TransactionInfosBuilder, opts ...func(option *service.ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error) {
				dt := model.NewDriverTransaction(driverID)
				txninfos, err := builder(*dt)
				if err != nil {
					return model.DriverTransaction{}, nil, err
				}
				require.Len(tt, txninfos, 2)
				require.Equal(tt, model.IncentiveTransactionType, txninfos[0].Type)
				require.Equal(tt, model.WalletTransactionCategory, txninfos[0].Category)
				require.Equal(tt, types.Money(6), txninfos[0].Amount)
				require.Equal(tt, model.WithholdingTransactionType, txninfos[1].Type)
				require.Equal(tt, model.CreditTransactionCategory, txninfos[1].Category)
				require.Equal(tt, types.Money(0.18), txninfos[1].Amount)

				require.Len(tt, opts, 2)
				return *dt, model.NewTransactionsFromInfos(utils.GenerateUUID(), model.SystemTransactionChannel, model.ConvertCoinToCashAction, model.SuccessTransactionStatus, txninfos), nil
			})

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("empty daily rewards", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{}, nil)

		ret, err := task.Execute(context.Background())
		require.NoError(t, err)
		require.Equal(tt, 0, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("get daily rewards error", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{}, e)

		ret, err := task.Execute(context.Background())
		require.Error(tt, err)
		require.Equal(tt, 0, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("find driver id error", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID: "a",
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(nil, e)

		ret, err := task.Execute(context.Background())
		require.NoError(t, err)
		require.Equal(tt, 0, len(ret.Success))
		require.Equal(tt, 1, len(ret.Fail))
	})

	t.Run("get conversion rate error", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID: "a",
				},
			}, nil)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: "a",
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(nil, e)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(false)

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 0, len(ret.Success))
		require.Equal(tt, 1, len(ret.Fail))
	})

	t.Run("get conversion rate is not set", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID: "a",
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: "a",
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(nil, nil)

		ret, err := task.Execute(context.Background())

		require.NoError(t, err)
		require.Equal(tt, 0, len(ret.Success))
		require.Equal(tt, 1, len(ret.Fail))
	})

	t.Run("ar not meet criteria", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		yesterday := timeutil.BangkokNow().Add(-24 * time.Hour)
		startTime, endTime := timeutil.DateTruncate(yesterday), timeutil.DateCeiling(yesterday)

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   "a",
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: "a",
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(&model.CoinCashConversionRate{
				Region: "BKK",
				Scheme: model.CoinCashConversionRateScheme{
					Tiers: []model.CoinCashTier{
						{
							Coin: 2,
							Cash: 5,
						},
					},
					AR: 80.0,
				},
			}, nil)
		deps.doiRepo.EXPECT().
			GetDailyCounts(gomock.Any(), "a", startTime, endTime).
			Return(&model.DriverOrderInfo{
				DriverID: "a",
				DailyCounts: []model.DailyCount{
					{Day: startTime.Day(), Month: startTime.Month(), Year: startTime.Year(), AutoAssigned: 100, AutoAssignedAccepted: 79},
				},
			}, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.rewardTransactionProcessor.EXPECT().
			ProcessRewardTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		ret, err := task.Execute(context.Background())
		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("cr not meet criteria", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		yesterday := timeutil.BangkokNow().Add(-24 * time.Hour)
		startTime, endTime := timeutil.DateTruncate(yesterday), timeutil.DateCeiling(yesterday)

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   "a",
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: "a",
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(&model.CoinCashConversionRate{
				Region: "BKK",
				Scheme: model.CoinCashConversionRateScheme{
					Tiers: []model.CoinCashTier{
						{
							Coin: 2,
							Cash: 5,
						},
					},
					CR: 5.0,
				},
			}, nil)

		deps.doiRepo.EXPECT().
			GetDailyCounts(gomock.Any(), "a", startTime, endTime).
			Return(&model.DriverOrderInfo{
				DriverID: "a",
				DailyCounts: []model.DailyCount{
					{Day: startTime.Day(), Month: startTime.Month(), Year: startTime.Year(), AutoAssigned: 100, AutoAssignedAccepted: 100, CancelledNotFree: 6},
				},
			}, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.rewardTransactionProcessor.EXPECT().
			ProcessRewardTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		ret, err := task.Execute(context.Background())
		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("profile not meet criteria", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   "a",
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID:     "a",
				ServiceTypes: []model.Service{model.ServiceFood},
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(&model.CoinCashConversionRate{
				Region: "BKK",
				Scheme: model.CoinCashConversionRateScheme{
					Tiers: []model.CoinCashTier{
						{
							Coin: 2,
							Cash: 5,
						},
					},
					CR: 5.0,
				},
				IncludeDedicatedDriver: false,
			}, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.rewardTransactionProcessor.EXPECT().
			ProcessRewardTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		ret, err := task.Execute(context.Background())
		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})

	t.Run("using day condition scheme instead", func(tt *testing.T) {
		task, deps, finish := newConvertCoinToCashTask(tt)
		defer finish()
		timeutils.Freeze()
		defer timeutils.Unfreeze()

		days := model.DaysLookup[strings.ToUpper(timeutil.BangkokNow().Add(-24*time.Hour).Format("Mon"))]

		deps.dailyRewardRepo.EXPECT().
			GetDailyRewardsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.DailyReward{
				{
					DriverID:   "a",
					CoinAmount: 50,
				},
			}, nil)

		deps.featureFlag.EXPECT().
			IsEnabled(gomock.Any(), flahCoin2Vault).Return(false)

		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any()).
			Return(&model.Driver{
				DriverID: "a",
			}, nil)

		deps.coinCashRepo.EXPECT().
			GetConversionRateFromRegionWithoutCache(gomock.Any(), gomock.Any()).
			Return(&model.CoinCashConversionRate{
				Region: "BKK",
				Scheme: model.CoinCashConversionRateScheme{
					Tiers: []model.CoinCashTier{
						{
							Coin: 2,
							Cash: 5,
						},
					},
				},
				Conditions: []model.DynamicCoinCashConversionRateCondition{
					{
						Days: []model.Days{days},
						Scheme: model.CoinCashConversionRateScheme{
							Tiers: []model.CoinCashTier{
								{
									Coin: 2,
									Cash: 10,
								},
							},
						},
					},
				},
			}, nil)

		deps.txnHelper.EXPECT().
			WithTxn(gomock.Any(), gomock.Any(), gomock.Any())

		deps.rewardTransactionProcessor.EXPECT().
			GetRewardTransactionsFromDate(gomock.Any(), gomock.Any(), gomock.Any()).
			Return([]model.RewardTransaction{}, nil)

		deps.rewardTransactionProcessor.EXPECT().
			ProcessRewardTransaction(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil)

		deps.driverTransactionSvc.EXPECT().
			ProcessDriverTransaction(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, driverID string, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus, builder service.TransactionInfosBuilder, opts ...func(option *service.ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error) {
				dt := model.NewDriverTransaction(driverID)
				txninfos, err := builder(*dt)
				if err != nil {
					return model.DriverTransaction{}, nil, err
				}
				require.Len(tt, txninfos, 2)
				require.Equal(tt, model.IncentiveTransactionType, txninfos[0].Type)
				require.Equal(tt, model.WalletTransactionCategory, txninfos[0].Category)
				require.Equal(tt, types.Money(10), txninfos[0].Amount)
				require.Equal(tt, model.WithholdingTransactionType, txninfos[1].Type)
				require.Equal(tt, model.CreditTransactionCategory, txninfos[1].Category)
				require.Equal(tt, types.Money(0.3), txninfos[1].Amount)
				require.Len(tt, opts, 2)
				return *dt, model.NewTransactionsFromInfos(utils.GenerateUUID(), model.SystemTransactionChannel, model.ConvertCoinToCashAction, model.SuccessTransactionStatus, txninfos), nil
			})

		ret, err := task.Execute(context.Background())
		require.NoError(t, err)
		require.Equal(tt, 1, len(ret.Success))
		require.Equal(tt, 0, len(ret.Fail))
	})
}

func TestConvertCoinToCash_ConstructCoinSources(t *testing.T) {
	t.Parallel()

	t.Run("len 0", func(tt *testing.T) {
		task, _, _ := newConvertCoinToCashTask(tt)

		ret := task.constructCoinSources([]model.RewardTransaction{})
		require.Equal(tt, 0, len(ret))
	})

	t.Run("can sum ontop", func(tt *testing.T) {
		task, _, _ := newConvertCoinToCashTask(tt)

		ret := task.constructCoinSources([]model.RewardTransaction{
			{
				Type:   model.OnTopRewardTransactionType,
				Amount: 3,
				Info: model.RewardTransactionInfo{
					ServiceType: model.ServiceFood,
					TripID:      "TRIP-A",
					OrderIDs:    []string{"ORD-A"},
				},
				Sources: []model.RewardTransactionSource{
					{
						Source: "source-a",
						Name:   "name-a",
						Amount: 2,
					},
					{
						Source: "source-b",
						Name:   "name-b",
						Amount: 1,
					},
				},
			},
			{
				Type:   model.OnTopRewardTransactionType,
				Amount: 3,
				Info: model.RewardTransactionInfo{
					ServiceType: model.ServiceFood,
					TripID:      "TRIP-A",
					OrderIDs:    []string{"ORD-A"},
				},
				Sources: []model.RewardTransactionSource{
					{
						Source: "source-a",
						Name:   "name-a",
						Amount: 2,
					},
					{
						Source: "source-b",
						Name:   "name-b",
						Amount: 1,
					},
				},
			},
			{
				Type:   model.OnTopRewardTransactionType,
				Amount: 5,
				Info: model.RewardTransactionInfo{
					ServiceType: model.ServiceMessenger,
					TripID:      "TRIP-A",
					OrderIDs:    []string{"ORD-A"},
				},
				Sources: []model.RewardTransactionSource{
					{
						Source: "source-c",
						Name:   "name-c",
						Amount: 5,
					},
				},
			},
			{
				Type:   model.CompensateRewardTransactionType,
				Amount: 5,
				Info: model.RewardTransactionInfo{
					ServiceType: "",
					TripID:      "",
					OrderIDs:    []string{},
				},
				Sources: []model.RewardTransactionSource{
					{
						Source: "",
						Name:   "incentive-name",
						Amount: 5,
					},
				},
			},
		})
		require.Equal(tt, 3, len(ret))

		for _, r := range ret {
			if r.Type == model.OnTopRewardTransactionType && r.Service == model.ServiceFood {
				require.Equal(tt, 6, r.Count)
			} else if r.Type == model.OnTopRewardTransactionType && r.Service == model.ServiceMessenger {
				require.Equal(tt, 5, r.Count)
			} else if r.Type == model.CompensateRewardTransactionType {
				require.Equal(tt, 5, r.Count)
			}
		}
	})
}

func TestConvertCoinToCash_TrimLatestConvertRewardTransaction(t *testing.T) {
	t.Parallel()

	t.Run("trim latest convert txn correctly", func(tt *testing.T) {
		task, _, _ := newConvertCoinToCashTask(tt)

		ret := task.trimLatestConvertRewardTransaction([]model.RewardTransaction{
			{
				Type: model.OnTopRewardTransactionType,
			},
			{
				Type: model.ConvertToCashRewardTransactionType,
			},
		})
		require.Equal(tt, 1, len(ret))
	})

	t.Run("don't trim latest convert txn if not found", func(tt *testing.T) {
		task, _, _ := newConvertCoinToCashTask(tt)

		ret := task.trimLatestConvertRewardTransaction([]model.RewardTransaction{
			{
				Type: model.OnTopRewardTransactionType,
			},
		})
		require.Equal(tt, 1, len(ret))
	})

	t.Run("remove convert txn when have just one", func(tt *testing.T) {
		task, _, _ := newConvertCoinToCashTask(tt)

		ret := task.trimLatestConvertRewardTransaction([]model.RewardTransaction{
			{
				Type: model.ConvertToCashRewardTransactionType,
			},
		})
		require.Equal(tt, 0, len(ret))
	})
}

func TestConvertCoinToCash_FilterOutConvertedItems(t *testing.T) {
	t.Parallel()

	t.Run("filter out correctly", func(tt *testing.T) {
		task, _, _ := newConvertCoinToCashTask(tt)

		ret := task.filterOutConvertedItems([]model.RewardTransaction{
			{
				Type: model.OnTopRewardTransactionType,
			},
			{
				Type: model.ConvertToCashRewardTransactionType,
			},
			{
				Type: model.CompensateRewardTransactionType,
			},
			{
				Type: model.OnTopRewardTransactionType,
			},
		})
		require.Equal(tt, 2, len(ret))
	})

	t.Run("don't filter when no convert coin cash txn", func(tt *testing.T) {
		task, _, _ := newConvertCoinToCashTask(tt)

		ret := task.filterOutConvertedItems([]model.RewardTransaction{
			{
				Type: model.OnTopRewardTransactionType,
			},
			{
				Type: model.CompensateRewardTransactionType,
			},
			{
				Type: model.OnTopRewardTransactionType,
			},
		})
		require.Equal(tt, 3, len(ret))
	})

	t.Run("dont' crash when empty", func(tt *testing.T) {
		task, _, _ := newConvertCoinToCashTask(tt)

		ret := task.filterOutConvertedItems([]model.RewardTransaction{})
		require.Equal(tt, 0, len(ret))
	})
}

type ConvertCoinToCashDeps struct {
	config                     ConvertCoinToCashConfig
	driverRepo                 *mock_repository.MockDriverRepository
	dailyRewardRepo            *mock_repository.MockDailyRewardRepository
	rewardBalanceRepo          *mock_repository.MockRewardBalanceRepository
	coinCashRepo               *mock_repository.MockCoinCashConversionRateRepository
	driverTransactionSvc       *mock_service.MockDriverTransactionServiceV2
	rewardTransactionProcessor *mock_service.MockRewardTransactionProcessor
	txnHelper                  *mock_transaction.MockTxnHelper
	doiRepo                    *mock_repository.MockDriverOrderInfoRepository
	serviceAreaRepository      *mock_repository.MockServiceAreaRepository
	featureFlag                *mock_featureflag.MockService
	coinPlan                   *mock_coinplan.MockCoinPlanGRPCClient
}

func newConvertCoinToCashTask(
	t gomock.TestReporter,
) (
	*ConvertCoinToCashTask,
	*ConvertCoinToCashDeps,
	func(),
) {
	ctrl := gomock.NewController(t)
	deps := &ConvertCoinToCashDeps{
		config: ConvertCoinToCashConfig{
			ConvertDayOffset: -1,
			WithHoldingTax:   0.03,
		},
		driverRepo:                 mock_repository.NewMockDriverRepository(ctrl),
		dailyRewardRepo:            mock_repository.NewMockDailyRewardRepository(ctrl),
		rewardBalanceRepo:          mock_repository.NewMockRewardBalanceRepository(ctrl),
		coinCashRepo:               mock_repository.NewMockCoinCashConversionRateRepository(ctrl),
		driverTransactionSvc:       mock_service.NewMockDriverTransactionServiceV2(ctrl),
		rewardTransactionProcessor: mock_service.NewMockRewardTransactionProcessor(ctrl),
		txnHelper:                  mock_transaction.NewMockTxnHelper(ctrl),
		doiRepo:                    mock_repository.NewMockDriverOrderInfoRepository(ctrl),
		serviceAreaRepository:      mock_repository.NewMockServiceAreaRepository(ctrl),
		featureFlag:                mock_featureflag.NewMockService(ctrl),
		coinPlan:                   mock_coinplan.NewMockCoinPlanGRPCClient(ctrl),
	}

	return ProvideConvertCoinToCashTask(
			deps.config,
			deps.driverRepo,
			deps.dailyRewardRepo,
			deps.rewardBalanceRepo,
			deps.coinCashRepo,
			deps.driverTransactionSvc,
			deps.rewardTransactionProcessor,
			deps.txnHelper,
			deps.doiRepo,
			deps.serviceAreaRepository,
			deps.featureFlag,
			deps.coinPlan,
		),

		deps,

		func() {
			ctrl.Finish()
		}
}
