// Code generated by MockGen. DO NOT EDIT.
// Source: ./task_provider.go

// Package cron is a generated GoMock package.
package cron

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockTaskProvider is a mock of TaskProvider interface.
type MockTaskProvider struct {
	ctrl     *gomock.Controller
	recorder *MockTaskProviderMockRecorder
}

// MockTaskProviderMockRecorder is the mock recorder for MockTaskProvider.
type MockTaskProviderMockRecorder struct {
	mock *MockTaskProvider
}

// NewMockTaskProvider creates a new mock instance.
func NewMockTaskProvider(ctrl *gomock.Controller) *MockTaskProvider {
	mock := &MockTaskProvider{ctrl: ctrl}
	mock.recorder = &MockTaskProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskProvider) EXPECT() *MockTaskProviderMockRecorder {
	return m.recorder
}

// Provide mocks base method.
func (m *MockTaskProvider) Provide(name TaskName) Task {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Provide", name)
	ret0, _ := ret[0].(Task)
	return ret0
}

// Provide indicates an expected call of Provide.
func (mr *MockTaskProviderMockRecorder) Provide(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Provide", reflect.TypeOf((*MockTaskProvider)(nil).Provide), name)
}
