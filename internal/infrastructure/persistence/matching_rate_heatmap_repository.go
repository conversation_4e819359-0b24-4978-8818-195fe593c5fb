package persistence

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type DataStoreMatchingRateHeatMapRepository struct {
	datastore HeatMapMatchingRateDataStore
}

func (repo *DataStoreMatchingRateHeatMapRepository) Find(query repository.HeatMapQuery, opts ...repository.Option) ([]model.MatchingRateHeatMap, error) {

	var heatmaps []model.MatchingRateHeatMap
	nearSphere := bson.D{
		{Key: "$near", Value: bson.D{
			{Key: "$geometry", Value: model.NewHeatMapPoint(query.Lng, query.Lat)},
			{Key: "$maxDistance", Value: query.DistanceInMeters},
		}},
	}

	filter := bson.M{"location": nearSphere}
	filter["region"] = query.Region
	filter["created_at"] = bson.M{"$gte": time.Now().Add(-24 * time.Hour).UTC()}

	if err := repo.datastore.Find(context.Background(), filter, 0, 0, &heatmaps, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return heatmaps, nil
}

func (repo *DataStoreMatchingRateHeatMapRepository) SaveAll(hm []model.MatchingRateHeatMap) error {
	data := make([]interface{}, len(hm))
	now := time.Now().UTC()
	for i, ot := range hm {
		ot.CreatedAt = now
		ot.UpdatedAt = now
		data[i] = ot
	}
	return repo.datastore.InsertMany(context.Background(), data)
}

func (repo *DataStoreMatchingRateHeatMapRepository) RemoveAll() error {
	if _, err := repo.datastore.RemoveAll(context.Background(), bson.M{}); err != nil {
		return err
	}

	return nil
}

type HeatMapMatchingRateDataStore mongodb.DataStoreInterface

func ProvideMatchingRateHeatMapDataStore(conn *mongodb.Conn) HeatMapMatchingRateDataStore {
	return mongodb.NewDataStoreWithConn(conn, "matching_rate_heatmap")
}

func ProvideMatchingRateHeatMapRepository(ds HeatMapMatchingRateDataStore, meter metric.Meter) *repository.ProxyMatchingRateHeatMapRepository {
	return repository.NewLatencyProxyMatchingRateHeatMapRepository(&DataStoreMatchingRateHeatMapRepository{
		datastore: ds,
	}, meter)
}
