package persistence

import (
	"math"

	"github.com/sirupsen/logrus"
	"github.com/twpayne/go-geom"
	"github.com/uber/h3-go"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func EncodeLatLngStr(lat float64, lng float64, res int) string {
	return h3.ToString(encodeLatLng(lat, lng, res))
}

func encodeLatLng(lat float64, lng float64, res int) h3.H3Index {
	return h3.FromGeo(h3.GeoCoord{
		Latitude:  lat,
		Longitude: lng,
	}, res)
}

func listHashesInRadius(lat float64, lng float64, res int, radiusKM float64) []string {
	hexWidth := getHexagonEdgeLengthAvgKm(res) * 2
	radiusInHexagons := math.Ceil(radiusKM / hexWidth)
	indices := h3.KRing(encodeLatLng(lat, lng, res), int(radiusInHexagons))
	out := make([]string, len(indices))
	for i, id := range indices {
		out[i] = h3.ToString(id)
	}
	return out
}

func h3IndicesToStringList(indices [][]h3.H3Index) [][]string {
	out := make([][]string, len(indices))
	for i := range indices {
		out[i] = make([]string, len(indices[i]))
		for j := range indices[i] {
			out[i][j] = h3.ToString(indices[i][j])
		}
	}
	return out
}

func listSortedHashesInRadius(lat float64, lng float64, res int, radiusKM float64) [][]string {
	hexWidth := getHexagonEdgeLengthAvgKm(res) * 2
	radiusInHexagons := math.Ceil(radiusKM / hexWidth)
	indices := h3.KRingDistances(encodeLatLng(lat, lng, res), int(radiusInHexagons))
	return h3IndicesToStringList(indices)
}

// listHashesByMultiPolygon list h3 hash in given polygon
// get hexagons in higher resolution to make sure hexagons will cover the area
// then transform to target resolution
func listHashesByPolygon(polygon *geom.Polygon, res int, childRes int) []string {
	if polygon == nil || polygon.NumCoords() == 0 {
		logrus.Warnf("found empty polygon on listing hashes")
		return []string{}
	}
	if childRes < res {
		logrus.Warnf("listHashesByPolygon: override childRes from %v to %v", childRes, res)
		childRes = res
	}
	geoPolygon := geomPolygonToH3Polygon(polygon)
	childIndices := h3.Polyfill(geoPolygon, childRes)
	indices := types.NewStringSet()
	for _, i := range childIndices {
		var hash h3.H3Index
		if res < childRes {
			hash = h3.ToParent(i, res)
		} else {
			hash = i
		}
		indices.Add(h3.ToString(hash))
	}

	return indices.GetElements()
}

func geomPolygonToH3Polygon(polygon *geom.Polygon) h3.GeoPolygon {
	geoPolygon := h3.GeoPolygon{
		Geofence: make([]h3.GeoCoord, 0),
		Holes:    make([][]h3.GeoCoord, 0),
	}
	exterior := polygon.LinearRing(0)
	for i := 0; i < exterior.NumCoords(); i++ {
		coord := exterior.Coord(i)
		geoPolygon.Geofence = append(geoPolygon.Geofence, h3.GeoCoord{
			Longitude: coord.X(),
			Latitude:  coord.Y(),
		})
	}

	num := polygon.NumLinearRings()
	for i := 1; i < num; i++ {
		holeIdx := i - 1
		geoPolygon.Holes = append(geoPolygon.Holes, make([]h3.GeoCoord, 0))
		interior := polygon.LinearRing(i)
		for j := 0; j < interior.NumCoords(); j++ {
			coord := interior.Coord(j)
			geoPolygon.Holes[holeIdx] = append(geoPolygon.Holes[holeIdx], h3.GeoCoord{
				Longitude: coord.X(),
				Latitude:  coord.Y(),
			})
		}
	}

	return geoPolygon
}

// getHexagonEdgeLengthAvgKm gets inradius of average hexagon.
// The inradius is the distance between the center and the middle of an edge.
func getHexagonEdgeLengthAvgKm(res int) float64 {
	lens := []float64{
		1107.712591, 418.6760055, 158.2446558, 59.81085794,
		22.6063794, 8.544408276, 3.229482772, 1.220629759,
		0.461354684, 0.174375668, 0.065907807, 0.024910561,
		0.009415526, 0.003559893, 0.001348575, 0.000509713,
	}
	return lens[res]
}
