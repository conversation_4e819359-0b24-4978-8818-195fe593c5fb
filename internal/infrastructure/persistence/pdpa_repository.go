package persistence

import (
	"context"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/go/logx/v2"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	xgotimeutil "git.wndv.co/lineman/xgo/timeutil"
)

var _ repository.PdpaRepository = &PdpaRepo{}

type PdpaRepo struct {
	ds          PdpaDataStore
	redisClient datastore.RedisClient
}

func (p *PdpaRepo) Upsert(ctx context.Context, pdpa *model.Pdpa) error {

	var pdpaResult model.Pdpa
	now := xgotimeutil.GetTimeNowFromContext(ctx).UTC()
	created := false
	if err := p.ds.FindOne(ctx, bson.M{"driver_id": pdpa.DriverId}, &pdpaResult); err != nil {
		if !errors.Is(err, mongodb.ErrDataNotFound) {
			return err
		}
		created = true
		pdpa.CreatedAt = now
		pdpa.UpdatedAt = now
		if err = p.ds.Insert(ctx, pdpa); err != nil {
			return err
		}
	} else if err = p.ds.Update(ctx, bson.M{"driver_id": pdpa.DriverId}, bson.M{"$push": bson.M{"pdpa": bson.M{"$each": pdpa.Pdpa}}, "$set": bson.M{"updated_at": now}}); err != nil {
		return err
	}

	if created {
		pdpaResult = *pdpa
	} else {
		pdpaResult.Pdpa = append(pdpaResult.Pdpa, pdpa.Pdpa...)
		pdpaResult.UpdatedAt = now
	}
	if err := cache.SetPDPA(ctx, p.redisClient, &pdpaResult); err != nil {
		logx.Warn().Err(err).Msgf("unable to set pdpa cache for driver id %s", pdpa.DriverId)
	}
	return nil
}

func (p *PdpaRepo) FindByDriverId(ctx context.Context, driverId string, opts ...repository.Option) (*model.Pdpa, error) {
	if cached, _ := cache.GetPDPA(ctx, p.redisClient, driverId); cached != nil {
		return cached, nil
	}
	var pdpa model.Pdpa
	if err := p.ds.FindOne(ctx, bson.M{"driver_id": driverId}, &pdpa, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	if err := cache.SetPDPA(ctx, p.redisClient, &pdpa); err != nil {
		logx.Warn().Err(err).Msg("unable to set pdpa")
	}

	return &pdpa, nil
}

func ProvidePdpaRepository(datastore PdpaDataStore, meter metric.Meter, redisClient datastore.RedisClient) *repository.ProxyPdpaRepository {
	return repository.NewLatencyProxyPdpaRepository(&PdpaRepo{ds: datastore, redisClient: redisClient}, meter)
}

type PdpaDataStore mongodb.DataStoreInterface

func ProvidePdpaDataStore(conn *mongodb.Conn) PdpaDataStore {
	return mongodb.NewDataStoreWithConn(conn, "pdpa")
}
