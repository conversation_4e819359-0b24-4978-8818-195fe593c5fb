package persistence

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type BCPOrderQuery struct {
	tripIDs  []string
	orderID  string
	driverID string
}

func NewBCPOrderQuery() *BCPOrderQuery {
	return &BCPOrderQuery{}
}

func (q *BCPOrderQuery) SetTripIDs(tripIDs ...string) *BCPOrderQuery {
	q.tripIDs = tripIDs
	return q
}

func (q *BCPOrderQuery) SetDriverID(id string) *BCPOrderQuery {
	q.driverID = id
	return q
}

func (q *BCPOrderQuery) SetOrderID(orderID string) *BCPOrderQuery {
	q.orderID = orderID
	return q
}

func (q *BCPOrderQuery) Query() (bson.M, error) {
	if q == nil {
		return repository.ValidateQuery(nil)
	}

	query := bson.M{}
	if len(q.tripIDs) != 0 {
		query["trip_id"] = bson.M{"$in": q.tripIDs}
	}
	if q.orderID != "" {
		query["order_id"] = q.orderID
	}
	if q.driverID != "" {
		query["driver_id"] = q.driverID
	}

	return repository.ValidateQuery(query)
}

type repoImpl struct {
	ds BCPOrderDataStore
}

func (repo *repoImpl) Find(ctx context.Context, query repository.Query, opts ...repository.Option) ([]model.BCPOrder, error) {
	q, err := query.Query()
	if err != nil {
		return nil, err
	}
	var result []model.BCPOrder
	err = repo.ds.Find(ctx, q, 0, 0, &result, repository.ToDBOptions(opts)...)
	if err != nil {
		return result, err
	}
	return result, nil
}

func (repo *repoImpl) Create(ctx context.Context, bcpOrder model.BCPOrder) error {
	now := timeutil.GetTimeFromContext(ctx)
	bcpOrder.CreatedAt = now

	err := repo.ds.Insert(ctx, bcpOrder)
	if err != nil {
		return err
	}
	return nil
}

func (repo *repoImpl) GetOrdersByStatusInPeriod(ctx context.Context, status model.BCPOrderStatus, startTime time.Time, endTime time.Time, opts ...repository.Option) ([]model.BCPOrder, error) {
	var result []model.BCPOrder
	if err := repo.ds.Find(ctx, bson.M{
		"created_at": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
		"status": status,
	}, 0, 0, &result, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	return result, nil
}

type BCPOrderDataStore mongodb.DataStoreInterface

func ProvideBCPOrderDataStore(conn *mongodb.Conn) BCPOrderDataStore {
	return mongodb.NewDataStoreWithConn(conn, "bcp_orders")
}

func ProvideBCPOrderRepository(datastore BCPOrderDataStore, meter metric.Meter) repository.BCPOrderRepository {
	return repository.NewLatencyProxyBCPOrderRepository(&repoImpl{ds: datastore}, meter)
}
