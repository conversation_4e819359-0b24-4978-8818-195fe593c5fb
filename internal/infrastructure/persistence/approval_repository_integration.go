package persistence

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type DataStoreApprovalRepository struct {
	datastore ApprovalDataStore
}

func (repo *DataStoreApprovalRepository) Create(ctx context.Context, approval *model.Approval) error {
	return repo.datastore.Insert(ctx, approval)
}

func (repo *DataStoreApprovalRepository) Find(ctx context.Context, query repository.ApprovalQuery, skip int, limit int) ([]model.Approval, error) {
	result := []model.Approval{}

	if err := repo.datastore.FindAndSort(ctx, query.ToQuery(), skip, limit, []string{"-updated_at"}, &result); err != nil {
		return nil, err
	}

	return result, nil
}

func (repo *DataStoreApprovalRepository) Get(ctx context.Context, id string) (*model.Approval, error) {
	var approval *model.Approval = &model.Approval{}
	if err := repo.datastore.FindOne(ctx, bson.M{"approval_id": id}, approval); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return approval, nil
}

func (repo *DataStoreApprovalRepository) FindByVoidTxnID(ctx context.Context, txnID string) ([]model.Approval, error) {
	var approvals []model.Approval
	if err := repo.datastore.Find(ctx, bson.M{"info.void_transaction_id": txnID}, 0, 0, &approvals); err != nil {
		return nil, err
	}

	return approvals, nil
}

func (repo *DataStoreApprovalRepository) FindChargeApprovalByOrderID(ctx context.Context, orderID string) ([]model.Approval, error) {
	var approvals []model.Approval
	and := bson.M{
		"$and": []bson.M{
			{"info.order_id": orderID},
			{"info.action": model.ChargeAction},
		},
	}

	if err := repo.datastore.Find(ctx, and, 0, 0, &approvals); err != nil {
		return nil, err
	}

	return approvals, nil
}

func (repo *DataStoreApprovalRepository) Count(ctx context.Context, query repository.ApprovalQuery) (int, error) {
	var count int
	if err := repo.datastore.Count(ctx, query.ToQuery(), &count); err != nil {
		return -1, err
	}

	return count, nil
}

func (repo *DataStoreApprovalRepository) Update(ctx context.Context, approval *model.Approval) error {
	return repo.datastore.Replace(ctx, bson.M{"approval_id": approval.ApprovalID}, approval)
}

func ProvideDataStoreApprovalRepository(ds ApprovalDataStore, meter metric.Meter) *repository.ProxyApprovalRepository {
	return repository.NewLatencyProxyApprovalRepository(&DataStoreApprovalRepository{
		datastore: ds,
	}, meter)
}

type ApprovalDataStore mongodb.DataStoreInterface

func ProvideApprovalDataStore(conn *mongodb.Conn) ApprovalDataStore {
	return mongodb.NewDataStoreWithConn(conn, "approvals")
}
