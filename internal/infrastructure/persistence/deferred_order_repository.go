package persistence

import (
	"context"
	"errors"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type DeferredOrderRepository struct {
	ds DeferredOrderDataStore
}

func (r *DeferredOrderRepository) InsertOrder(ctx context.Context, order model.Order, opt model.DeferredOrderOption) error {
	now := timeutil.BangkokNow()
	shouldDistributeAt := order.CreatedAt.Add(time.Duration(opt.DeferDurationMinute) * time.Minute)
	deferredOrder := &model.DeferredOrder{
		OrderID:             order.OrderID,
		ShouldDistributeAt:  shouldDistributeAt,
		DeferredOrderOption: opt,
		CreatedAt:           now,
		UpdatedAt:           now,
		PickupRound:         0,
	}

	return r.ds.Insert(ctx, deferredOrder)
}

func (r *DeferredOrderRepository) FindUnProcessOrder(
	ctx context.Context,
	pickingTime time.Time,
	retroTime time.Time,
	retryLimit int,
) ([]model.DeferredOrder, error) {
	q := bson.M{
		"pickup_round": bson.M{
			"$lt": retryLimit,
		},
		"should_distribute_at": bson.M{
			"$gte": retroTime,
			"$lt":  pickingTime,
		},
		"succeed_at": bson.M{
			"$exists": false,
		},
	}

	var result []model.DeferredOrder
	if err := r.ds.Find(ctx, q, 0, 0, &result); err != nil {
		return []model.DeferredOrder{}, err
	}

	return result, nil
}

func (r *DeferredOrderRepository) MarkProcessedOrders(ctx context.Context, orderIds []string, retryLimit int) error {
	q := bson.M{
		"order_id": bson.M{
			"$in": orderIds,
		},
	}

	now := timeutil.BangkokNow()
	operations := bson.M{
		"$inc": bson.M{
			"pickup_round": 1,
		},
		"$set": bson.M{
			"pickup_round_limit_config": retryLimit,
			"updated_at":                now,
			"processed_at":              now,
		},
	}

	_, err := r.ds.UpdateAll(ctx, q, operations)
	if err != nil {
		return err
	}

	return nil
}

func (r *DeferredOrderRepository) MarkSuccessOrders(ctx context.Context, orderIds []string) error {
	q := bson.M{
		"order_id": bson.M{
			"$in": orderIds,
		},
	}

	now := timeutil.BangkokNow()
	operations := bson.M{
		"$set": bson.M{
			"updated_at": now,
			"succeed_at": now,
		},
	}

	_, err := r.ds.UpdateAll(ctx, q, operations)
	if err != nil {
		return err
	}

	return nil
}

func (r *DeferredOrderRepository) MarkFailedOrders(ctx context.Context, orderIds []string) error {
	q := bson.M{
		"order_id": bson.M{
			"$in": orderIds,
		},
	}

	now := timeutil.BangkokNow()
	operations := bson.M{
		"$set": bson.M{
			"updated_at": now,
			"failed_at":  now,
		},
	}

	_, err := r.ds.UpdateAll(ctx, q, operations)
	if err != nil {
		return err
	}
	return nil
}

func (r *DeferredOrderRepository) UpdateDistributionTime(ctx context.Context, orderIds []string, distributionTime time.Time) error {
	q := bson.M{
		"order_id": bson.M{
			"$in": orderIds,
		},
	}

	operations := bson.M{
		"$set": bson.M{
			"should_distribute_at": distributionTime,
		},
	}

	err := r.ds.Update(ctx, q, operations)
	if err != nil {
		return err
	}

	return nil
}

func (r *DeferredOrderRepository) IsUnprocessedExist(ctx context.Context, orderId string) (bool, error) {
	q := bson.M{
		"order_id": orderId,
		"processed_at": bson.M{
			"$exists": false,
		},
	}

	var result model.DeferredOrder
	if err := r.ds.FindOne(ctx, q, &result); err != nil {
		if errors.Is(err, mongodb.ErrDataNotFound) {
			return false, nil
		}
		return false, err
	}

	return true, nil
}

type DeferredOrderDataStore mongodb.DataStoreInterface

func ProvideDeferredOrderDataStore(conn *mongodb.Conn) DeferredOrderDataStore {
	return mongodb.NewDataStoreWithConn(conn, "deferred_orders")
}

func ProvideDeferredOrderRepository(datastore DeferredOrderDataStore, meter metric.Meter) repository.DeferredOrderRepository {
	return repository.NewLatencyProxyDeferredOrderRepository(&DeferredOrderRepository{ds: datastore}, meter)
}
