package persistence

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type ShiftCancelRepository struct {
	ds ShiftDataStore
}

func (s ShiftCancelRepository) Find(ctx context.Context, query repository.ShiftCancelQuery, skip int, limit int, opts ...repository.Option) ([]model.ShiftCancel, error) {
	var result []model.ShiftCancel

	filter := bson.M{}

	if query.DriverID != "" {
		filter["driver_id"] = query.DriverID
	}

	if query.ShiftID != "" {
		filter["shift_id"] = query.ShiftID
	}

	sort := []string{"-created_at"}

	if err := s.ds.FindAndSort(ctx, filter, skip, limit, sort, &result, repository.ToDBOptions(opts)...); err != nil {
		return []model.ShiftCancel{}, err
	}

	return result, nil
}

func (s ShiftCancelRepository) Create(ctx context.Context, m *model.ShiftCancel, opts ...repository.Option) error {
	now := timeutil.BangkokNow()
	m.CreatedAt = now
	m.UpdatedAt = now
	return s.ds.Insert(ctx, m, repository.ToDBOptions(opts)...)
}

type ShiftCancelDataStore mongodb.DataStoreInterface

func ProvideShiftCancelRepository(ds ShiftCancelDataStore, meter metric.Meter) *repository.ProxyShiftCancelRepository {
	return repository.NewLatencyProxyShiftCancelRepository(&ShiftCancelRepository{
		ds: ds,
	}, meter)
}

func ProvideShiftCancelDataStore(conn *mongodb.Conn) ShiftCancelDataStore {
	return mongodb.NewDataStoreWithConn(conn, "shift_cancels")
}
