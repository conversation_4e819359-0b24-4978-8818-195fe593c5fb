//go:build integration_test
// +build integration_test

package persistence_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestRainSituationRepository_CreateThenUpdate(t *testing.T) {
	tests := []struct {
		name            string
		rainStatus      model.RainStatus
		lastSeenChanged bool
	}{
		{
			name:            "create with raining status",
			rainStatus:      model.RainStatusHeavyRain,
			lastSeenChanged: true,
		},
		{
			name:            "create with non rain status",
			rainStatus:      model.RainStatusLightRain,
			lastSeenChanged: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctn := ittest.NewContainer(t)
			dbHelper := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "rain_situation")

			t.Parallel()
			rainSituation := &model.RainSituation{
				Name:       "One Bangkok",
				Region:     "BKK",
				RainStatus: tt.rainStatus,
			}

			// given
			rainingStatus := types.NewStringSet(string(model.RainStatusHeavyRain), string(model.RainStatusRainWithNoSupply))

			// pre-check
			assert.Equal(t, 0, dbHelper.Count(bson.M{"name": "One Bangkok"}), "should not has the area")

			err := ctn.RainSituationRepository.Create(context.Background(), rainSituation, rainingStatus)
			assert.NoError(t, err)

			var want model.RainSituation
			dbHelper.FindOneByQuery(bson.M{"name": "One Bangkok"}, &want)

			assert.NotNil(t, rainSituation)
			assert.Equal(t, rainSituation.Name, want.Name)
			assert.Equal(t, rainSituation.Region, want.Region)
			assert.Equal(t, rainSituation.RainStatus, want.RainStatus)
			assert.Equal(t, tt.lastSeenChanged, !want.LastSeenAt.IsZero())

			lastSeenAt := want.LastSeenAt
			give := want

			t.Run("update with non rain status", func(t *testing.T) {
				give.RainStatus = model.RainStatusNone

				err = ctn.RainSituationRepository.Update(context.Background(), give.ID.Hex(), &give, rainingStatus)

				assert.NoError(t, err)

				want = model.RainSituation{} // reset model
				dbHelper.FindOneByQuery(bson.M{"name": "One Bangkok"}, &want)

				assert.Equal(t, lastSeenAt, want.LastSeenAt, "last seen at should not be updated, when rain status not in set")
			})

			t.Run("update with raining status", func(t *testing.T) {
				give.RainStatus = model.RainStatusRainWithNoSupply

				err = ctn.RainSituationRepository.Update(context.Background(), give.ID.Hex(), &give, rainingStatus)

				assert.NoError(t, err)

				want = model.RainSituation{} // reset model
				dbHelper.FindOneByQuery(bson.M{"name": "One Bangkok"}, &want)

				assert.Less(t, lastSeenAt, want.LastSeenAt, "last seen at should be updated,")
			})
		})
	}
}

func TestRainSituationRepository_UpsertAll(t *testing.T) {
	ctn := ittest.NewContainer(t)
	dbHelper := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "rain_situation")

	// given
	rainingStatus := types.NewStringSet(string(model.RainStatusHeavyRain), string(model.RainStatusRainWithNoSupply))

	// pre-check
	assert.Equal(t, 0, dbHelper.Count(bson.M{"region": "BKK"}), "should not has the area")

	t.Run("create all rain situation document", func(t *testing.T) {
		err := ctn.RainSituationRepository.CreateAll(context.Background(), []model.RainSituation{
			{
				Name:       "One Bangkok",
				Region:     "BKK",
				RainStatus: model.RainStatusNone,
			},
			{
				Name:       "New Bangkok",
				Region:     "BKK",
				RainStatus: model.RainStatusHeavyRain,
			},
		}, rainingStatus)
		assert.NoError(t, err)
		assert.Equal(t, 2, dbHelper.Count(bson.M{"region": "BKK"}))

		var want model.RainSituation
		dbHelper.FindOneByQuery(bson.M{"name": "One Bangkok"}, &want)
		assert.True(t, want.LastSeenAt.IsZero())

		want = model.RainSituation{} //reset
		dbHelper.FindOneByQuery(bson.M{"name": "New Bangkok"}, &want)
		assert.True(t, !want.LastSeenAt.IsZero())
	})

	t.Run("if exists update document, unless insert new one", func(t *testing.T) {
		newBangkok := model.RainSituation{} //reset
		dbHelper.FindOneByQuery(bson.M{"name": "New Bangkok"}, &newBangkok)
		newBangkokLastSeenAt := newBangkok.LastSeenAt

		rainSituations := []model.RainSituation{
			{
				Name:       "One Bangkok",
				Region:     "BKK",
				RainStatus: model.RainStatusRainWithNoSupply,
			},
			{
				Name:       "New Bangkok",
				Region:     "BKK",
				RainStatus: model.RainStatusLightRain,
				LastSeenAt: newBangkokLastSeenAt,
			},
			{
				Name:       "T-One",
				Region:     "BKK",
				RainStatus: model.RainStatusNone,
			},
			{
				Name:       "T-New",
				Region:     "BKK",
				RainStatus: model.RainStatusHeavyRain,
			},
		}

		err := ctn.RainSituationRepository.UpsertAll(context.Background(), rainSituations, rainingStatus)
		assert.NoError(t, err)
		assert.Equal(t, 4, dbHelper.Count(bson.M{"region": "BKK"}))

		var want model.RainSituation
		dbHelper.FindOneByQuery(bson.M{"name": "One Bangkok"}, &want)
		assert.True(t, !want.LastSeenAt.IsZero(), "last seen at should be updated")

		want = model.RainSituation{} //reset
		dbHelper.FindOneByQuery(bson.M{"name": "New Bangkok"}, &want)
		assert.Equal(t, newBangkokLastSeenAt, want.LastSeenAt, "last seen at should not be update")

		want = model.RainSituation{} //reset
		dbHelper.FindOneByQuery(bson.M{"name": "T-One"}, &want)
		assert.True(t, want.LastSeenAt.IsZero(), "create without last seen at timestamp")

		want = model.RainSituation{} //reset
		dbHelper.FindOneByQuery(bson.M{"name": "New Bangkok"}, &want)
		assert.True(t, !want.LastSeenAt.IsZero(), "create with last seen at timestamp")
	})
}

func TestRainSituationRepository_HasRained(t *testing.T) {
	ctn := ittest.NewContainer(t)
	dbHelper := testutil.NewDBHelper(t, ctn.DBConnectionForTest, "rain_situation")

	// pre-check
	assert.Equal(t, 0, dbHelper.Count(bson.M{"region": "BKK"}), "should not has the area")

	err := ctn.RainSituationDataStore.InsertMany(context.Background(), bson.A{
		bson.M{
			"name":         "One Bangkok",
			"region":       "BKK",
			"last_seen_at": timeutil.ParseDateStringToDate("2024-11-20T10:30:00+07:00"),
		},
		bson.M{
			"name":         "New Bangkok",
			"region":       "BKK",
			"last_seen_at": timeutil.ParseDateStringToDate("2024-11-21T11:30:00+07:00"),
		},
		bson.M{
			"name":         "T-One",
			"region":       "BKK",
			"last_seen_at": timeutil.ParseDateStringToDate("2024-11-21T14:30:00+07:00"),
		},
	})

	assert.NoError(t, err)
	assert.Equal(t, 3, dbHelper.Count(bson.M{"region": "BKK"}))

	tests := []struct {
		name       string
		region     string
		start, end time.Time
		want       bool
	}{
		{
			name:   "no region",
			region: "unknown",
			start:  timeutil.ParseDateStringToDate("2024-11-21T00:00:00+07:00"),
			end:    timeutil.ParseDateStringToDate("2024-11-22T00:00:00+07:00"),
			want:   false,
		},
		{
			name:   "2024-11-20 has seen rained in BKK",
			region: "BKK",
			start:  timeutil.ParseDateStringToDate("2024-11-20T00:00:00+T07:00"),
			end:    timeutil.ParseDateStringToDate("2024-11-21T00:00:00+07:00"),
			want:   true,
		},
		{
			name:   "2024-11-21 has seen rained in BKK",
			region: "BKK",
			start:  timeutil.ParseDateStringToDate("2024-11-21T00:00:00+07:00"),
			end:    timeutil.ParseDateStringToDate("2024-11-22T00:00:00+07:00"),
			want:   true,
		},
		{
			name:   "2024-11-22 has not seen rained in BKK",
			region: "BKK",
			start:  timeutil.ParseDateStringToDate("2024-11-22T00:00:00+07:00"),
			end:    timeutil.ParseDateStringToDate("2024-11-23T00:00:00+07:00"),
			want:   false,
		},
	}

	for _, tt := range tests {
		tt := tt
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()
			got, err := ctn.RainSituationRepository.HasRained(context.Background(), tt.region, tt.start, tt.end)
			assert.NoError(t, err)
			assert.Equal(t, tt.want, got)
		})

	}

}
