package persistence

import (
	"context"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type WithholdingTaxCertificateRepository struct {
	ds WithholdingTaxCertificateDataStore
}

func (repo WithholdingTaxCertificateRepository) FindByYearAndStatus(ctx context.Context, year string, status model.WHTStatus, skip, limit int, opts ...repository.Option) ([]model.WithholdingTaxCertificate, error) {
	var withholdingTaxCertificate []model.WithholdingTaxCertificate

	if err := repo.ds.Find(ctx, bson.M{"year": year, "status": status}, skip, limit, &withholdingTaxCertificate, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return withholdingTaxCertificate, nil
}
func (repo WithholdingTaxCertificateRepository) UpdateAll(ctx context.Context, wtc []model.WithholdingTaxCertificate, _ ...repository.Option) error {
	var models []mongo.WriteModel
	for _, t := range wtc {

		updateCmd := bson.M{
			"$set": bson.M{
				"updated_at":     timeutils.Now(),
				"tawi50_url":     t.Tawi50Url,
				"tawi50_id":      t.Tawi50ID,
				"status":         t.Status,
				"driver_profile": t.DriverProfile,
				"remarks":        t.Remarks,
			},
		}
		models = append(models, mongo.NewUpdateOneModel().SetFilter(bson.M{"_id": t.ID}).SetUpdate(updateCmd))
	}

	result, err := repo.ds.BulkWrite(ctx, models, &options.BulkWriteOptions{})
	if err != nil {
		return err
	}

	logrus.Infof("found %d, update %d docs", result.MatchedCount, result.ModifiedCount)

	return nil
}

func (repo WithholdingTaxCertificateRepository) FindByDriverIdOrderByYearDesc(ctx context.Context, driverID string, skip, limit int, opts ...repository.Option) (withholdingTaxCertificate []model.WithholdingTaxCertificate, err error) {

	query := bson.M{
		"driver_id": driverID,
		"status":    model.Tawi50Completed,
		"tawi50_id": bson.M{"$exists": true},
	}
	err = repo.ds.FindAndSort(ctx, query, skip, limit, []string{"-year"}, &withholdingTaxCertificate, repository.ToDBOptions(opts)...)

	return
}

func ProvideWithholdingTaxCertificateRepository(ds WithholdingTaxCertificateDataStore, meter metric.Meter) *repository.ProxyWithholdingTaxCertificateRepository {
	return repository.NewLatencyProxyWithholdingTaxCertificateRepository(&WithholdingTaxCertificateRepository{ds: ds}, meter)
}

type WithholdingTaxCertificateDataStore mongodb.DataStoreInterface

const collectionWithholdingTaxCertificate = "withholding_tax_certificates"

func ProvideWithholdingTaxCertificateDataStore(db *mongodb.Conn) WithholdingTaxCertificateDataStore {
	return mongodb.NewDataStoreWithConn(db, collectionWithholdingTaxCertificate)
}
