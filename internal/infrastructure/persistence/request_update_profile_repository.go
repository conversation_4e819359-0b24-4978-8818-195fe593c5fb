package persistence

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type MongoRequestUpdateProfileQuery struct {
	DriverIDs   []string
	Status      []model.RequestProfileStatus
	ObjectIDs   []primitive.ObjectID
	DriverID    string
	SortedQuery []string
}

func BuildRequestUpdateProfileQuery() repository.RequestUpdateProfileQuery {
	return &MongoRequestUpdateProfileQuery{}
}

func (q *MongoRequestUpdateProfileQuery) WithDriverIDs(ids []string) repository.RequestUpdateProfileQuery {
	q.DriverIDs = ids
	return q
}
func (q *MongoRequestUpdateProfileQuery) WithStatus(status []model.RequestProfileStatus) repository.RequestUpdateProfileQuery {
	q.Status = status
	return q
}

func (q *MongoRequestUpdateProfileQuery) WithDriverID(driverID string) repository.RequestUpdateProfileQuery {
	q.DriverID = driverID
	return q
}

func (q *MongoRequestUpdateProfileQuery) WithIDs(objectIDs ...primitive.ObjectID) repository.RequestUpdateProfileQuery {
	q.ObjectIDs = objectIDs
	return q
}

func (q *MongoRequestUpdateProfileQuery) SortByUpdatedAt(isDesc bool) repository.RequestUpdateProfileQuery {
	sortedQuery := "updated_at"
	if isDesc {
		sortedQuery = "-" + sortedQuery
	}
	q.SortedQuery = append(q.SortedQuery, sortedQuery)
	return q
}

func (q *MongoRequestUpdateProfileQuery) SortByStatus(isDesc bool) repository.RequestUpdateProfileQuery {
	sortedQuery := "status"
	if isDesc {
		sortedQuery = "-" + sortedQuery
	}
	q.SortedQuery = append(q.SortedQuery, sortedQuery)
	return q
}

func (q *MongoRequestUpdateProfileQuery) Query() bson.M {
	query := bson.M{}

	if len(q.DriverIDs) > 0 {
		query["driver_id"] = bson.M{"$in": q.DriverIDs}
	}

	if len(q.Status) > 0 {
		query["status"] = bson.M{"$in": q.Status}
	}

	if len(q.ObjectIDs) > 0 {
		query["_id"] = bson.M{"$in": q.ObjectIDs}
	}

	if q.DriverID != "" {
		query["driver_id"] = q.DriverID
	}

	return query
}

var _ repository.RequestUpdateProfileRepository = &RequestUpdateProfileRepo{}

type RequestUpdateProfileRepo struct {
	ds RequestUpdateProfileDataStore
}

func (r *RequestUpdateProfileRepo) CreateAll(ctx context.Context, requestUpdate []model.RequestUpdateDriverProfile, opts ...repository.Option) error {

	now := time.Now().UTC()
	data := make([]interface{}, len(requestUpdate))
	for i, itm := range requestUpdate {
		itm.CreatedAt = now
		itm.UpdatedAt = now
		data[i] = itm
	}
	if err := r.ds.InsertMany(ctx, data); err != nil {
		return err
	}

	return nil
}

func (r *RequestUpdateProfileRepo) ReplaceAll(ctx context.Context, requestUpdateDriverProfiles []model.RequestUpdateDriverProfile) error {
	if len(requestUpdateDriverProfiles) == 0 {
		return nil
	}

	var models []mongo.WriteModel
	for _, requestUpdateProfile := range requestUpdateDriverProfiles {
		selector := bson.M{"_id": requestUpdateProfile.ID}
		replaceModel := mongo.NewReplaceOneModel().SetFilter(selector).SetReplacement(requestUpdateProfile)
		models = append(models, replaceModel)
	}

	result, err := r.ds.BulkWrite(ctx, models, &options.BulkWriteOptions{})
	if err != nil {
		return err
	}

	logrus.Infof("found %d, replaced %d request update profiles", result.MatchedCount, result.ModifiedCount)
	return nil
}

func (r *RequestUpdateProfileRepo) FindWithQueryAndSort(ctx context.Context, query repository.RequestUpdateProfileQuery, skip, limit int, opts ...repository.Option) ([]model.RequestUpdateDriverProfile, error) {
	var driverRequestUpdateProfiles []model.RequestUpdateDriverProfile

	mq, ok := query.(*MongoRequestUpdateProfileQuery)
	if !ok {
		return nil, errors.New("query must be MongoRequestUpdateProfileQuery")
	}

	q := mq.Query()
	sortedQuery := []string{"-created_at"}
	if len(mq.SortedQuery) > 0 {
		sortedQuery = mq.SortedQuery
	}
	if err := r.ds.FindAndSort(ctx, q, skip, limit, sortedQuery, &driverRequestUpdateProfiles, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return driverRequestUpdateProfiles, nil
}

func (r *RequestUpdateProfileRepo) FindInProcessRequestByDriverIDAndSectionIDs(ctx context.Context, driverID string, sectionIDs []string, opts ...repository.Option) ([]model.RequestUpdateDriverProfile, error) {

	var requestUpdateDriverProfiles []model.RequestUpdateDriverProfile

	pipeline := []bson.M{
		{
			"$match": bson.M{
				"driver_id":  driverID,
				"status":     bson.M{"$in": model.GetInProgressRequestProfileStatus()},
				"section_id": bson.M{"$in": sectionIDs},
			},
		},
	}

	err := r.ds.Aggregate(ctx, pipeline, &requestUpdateDriverProfiles, repository.WithReadSecondaryPreferred())
	if err != nil {
		return []model.RequestUpdateDriverProfile{}, err
	}

	return requestUpdateDriverProfiles, nil
}

func (pg *RequestUpdateProfileRepo) UpdateStatus(ctx context.Context, status model.RequestProfileStatus, message string, objectID ...primitive.ObjectID) error {
	selector := bson.M{"_id": bson.M{
		"$in": objectID,
	}}
	timeNow := time.Now().UTC()
	toUpdate := bson.M{
		"status":     status,
		"updated_at": timeNow,
	}
	if message != "" {
		toUpdate["message_to_driver"] = message
	}
	if model.GetCompletedRequestProfileStatus().IsContain(status) {
		toUpdate["completed_at"] = timeNow
	}
	updator := bson.M{
		"$set": toUpdate,
	}

	if _, err := pg.ds.UpdateAll(ctx, selector, updator); err != nil {
		return err
	}
	return nil
}

func (ir *RequestUpdateProfileRepo) CountWithQuery(ctx context.Context, query repository.RequestUpdateProfileQuery, opts ...repository.Option) (int, error) {
	var count int

	mq, ok := query.(*MongoRequestUpdateProfileQuery)
	if !ok {
		return 0, errors.New("query must be MongoRequestUpdateProfileQuery")
	}
	q := mq.Query()

	if err := ir.ds.Count(ctx, q, &count, repository.ToDBOptions(opts)...); err != nil {
		return 0, err
	}

	return count, nil
}

type RequestUpdateProfileDataStore mongodb.DataStoreInterface

func ProvideRequestUpdateProfileRepository(datastore RequestUpdateProfileDataStore, meter metric.Meter) *repository.ProxyRequestUpdateProfileRepository {
	return repository.NewLatencyProxyRequestUpdateProfileRepository(&RequestUpdateProfileRepo{ds: datastore}, meter)
}

func ProvideRequestUpdateProfileDataStore(conn *mongodb.Conn) RequestUpdateProfileDataStore {
	return mongodb.NewDataStoreWithConn(conn, "request_update_profiles")
}
