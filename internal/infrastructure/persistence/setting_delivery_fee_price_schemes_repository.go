package persistence

import (
	"context"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

const (
	KeySettingDeliveryFeePriceSchemesId = "setting_delivery_fee_price_schemes_id"
)

type MongoSettingDeliveryFeePriceSchemesRepository struct {
	datastore   SettingDeliveryFeePriceSchemesDataStore
	redisClient datastore.RedisClient
	cfg         config.DeliveryFeeSettingPriceSchemesRepositoryConfig
}

type SettingDeliveryFeePriceSchemesQuery struct {
	Name string
	IDs  []string
}

func NewSettingDeliveryFeePriceSchemeQuery() *SettingDeliveryFeePriceSchemesQuery {
	return &SettingDeliveryFeePriceSchemesQuery{}
}

func (q *SettingDeliveryFeePriceSchemesQuery) WithName(name string) repository.SettingDeliveryFeePriceSchemesQuery {
	q.Name = name
	return q
}

func (q *SettingDeliveryFeePriceSchemesQuery) WithIDs(ids []string) repository.SettingDeliveryFeePriceSchemesQuery {
	q.IDs = ids
	return q
}

func (q *SettingDeliveryFeePriceSchemesQuery) Query() bson.M {
	query := bson.M{}
	if q.Name != "" {
		query["name"] = q.Name
	}
	if q.IDs != nil {
		query["setting_delivery_fee_price_schemes_id"] = bson.M{
			"$in": q.IDs,
		}
	}
	return query
}

func (q *SettingDeliveryFeePriceSchemesQuery) QueryLike() bson.D {
	query := bson.D{}
	if q.Name != "" {
		query = bson.D{{Key: "name", Value: primitive.Regex{Pattern: q.Name, Options: ""}}}
	}
	return query
}

func (r *MongoSettingDeliveryFeePriceSchemesRepository) Create(ctx context.Context, scheme *model.SettingDeliveryFeePriceScheme) error {
	return r.datastore.Insert(ctx, scheme)
}

func (r *MongoSettingDeliveryFeePriceSchemesRepository) Find(ctx context.Context, query repository.SettingDeliveryFeePriceSchemesQuery, skip int, limit int, opts ...repository.Option) ([]model.SettingDeliveryFeePriceScheme, error) {
	var schemes []model.SettingDeliveryFeePriceScheme
	q, ok := query.(*SettingDeliveryFeePriceSchemesQuery)
	if !ok {
		return nil, ErrInvalidQueryType
	}
	if err := r.datastore.Find(ctx, q.Query(), skip, limit, &schemes, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	return schemes, nil
}

func (r *MongoSettingDeliveryFeePriceSchemesRepository) FindSorted(ctx context.Context, query repository.SettingDeliveryFeePriceSchemesQuery, skip int, limit int, opts ...repository.Option) ([]model.SettingDeliveryFeePriceScheme, error) {
	var schemes []model.SettingDeliveryFeePriceScheme
	q, ok := query.(*SettingDeliveryFeePriceSchemesQuery)
	if !ok {
		return nil, ErrInvalidQueryType
	}
	if err := r.datastore.FindAndSort(ctx, q.QueryLike(), skip, limit, []string{"-created_at", "_id"}, &schemes, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	return schemes, nil
}

func (r *MongoSettingDeliveryFeePriceSchemesRepository) FindById(ctx context.Context, id string, opts ...repository.Option) (*model.SettingDeliveryFeePriceScheme, error) {
	if schemeInCache, err := cache.GetPriceScheme(ctx, r.redisClient, id); err == nil {
		return schemeInCache, nil
	}

	var rawScheme bson.Raw
	if err := r.datastore.FindOne(ctx, bson.M{KeySettingDeliveryFeePriceSchemesId: id}, &rawScheme, repository.ToDBOptions(opts)...); err != nil {
		return nil, repository.ErrNotFound
	}

	if err := cache.SetPriceScheme(ctx, r.redisClient, id, rawScheme, r.cfg); err != nil {
		logrus.Errorf("Unable to set price scheme of id %s in redis", id)
	}

	var scheme model.SettingDeliveryFeePriceScheme
	if err := bson.Unmarshal(rawScheme, &scheme); err != nil {
		return nil, repository.ErrUnmarshall
	}

	return &scheme, nil
}

func (r *MongoSettingDeliveryFeePriceSchemesRepository) Update(ctx context.Context, scheme *model.SettingDeliveryFeePriceScheme) error {
	if err := cache.DelPriceScheme(ctx, r.redisClient, string(scheme.Key())); err != nil {
		logrus.Errorf("Unable to delete price scheme of id %s in redis", string(scheme.Key()))
		return err
	}

	return r.datastore.Replace(ctx, bson.M{KeySettingDeliveryFeePriceSchemesId: scheme.Key()}, scheme)
}

func (r *MongoSettingDeliveryFeePriceSchemesRepository) Delete(ctx context.Context, id string) error {
	if err := cache.DelPriceScheme(ctx, r.redisClient, id); err != nil {
		logrus.Errorf("Unable to delete price scheme of id %s in redis", id)
		return err
	}

	return r.datastore.Remove(ctx, bson.M{KeySettingDeliveryFeePriceSchemesId: id})
}

func (r *MongoSettingDeliveryFeePriceSchemesRepository) Count(ctx context.Context, query repository.SettingDeliveryFeePriceSchemesQuery, opts ...repository.Option) (int, error) {
	q, ok := query.(*SettingDeliveryFeePriceSchemesQuery)
	if !ok {
		return 0, ErrInvalidQueryType
	}
	var count int
	if err := r.datastore.Count(ctx, q.QueryLike(), &count, repository.ToDBOptions(opts)...); err != nil {
		return count, err
	}
	return count, nil
}

func (r *MongoSettingDeliveryFeePriceSchemesRepository) IsExistsByName(ctx context.Context, name string, id model.SettingDeliveryFeePriceSchemesId, opts ...repository.Option) bool {
	query := bson.M{
		"$and": []bson.M{
			{"name": name},
			{"setting_delivery_fee_price_schemes_id": bson.M{"$ne": id}},
		},
	}

	return r.datastore.IsExist(ctx, query, repository.ToDBOptions(opts)...)
}

type SettingDeliveryFeePriceSchemesDataStore mongodb.DataStoreInterface

func ProvideSettingDeliveryFeePriceSchemesDataStore(conn *mongodb.Conn) SettingDeliveryFeePriceSchemesDataStore {
	return mongodb.NewDataStoreWithConn(conn, "setting_delivery_fee_price_schemes")
}

func ProvideSettingDeliveryFeePriceSchemesRepository(
	ds SettingDeliveryFeePriceSchemesDataStore,
	redisClient datastore.RedisClient,
	cfg config.DeliveryFeeSettingPriceSchemesRepositoryConfig,
	meter metric.Meter,
) *repository.ProxySettingDeliveryFeePriceSchemesRepository {
	return repository.NewLatencyProxySettingDeliveryFeePriceSchemesRepository(
		&MongoSettingDeliveryFeePriceSchemesRepository{datastore: ds, redisClient: redisClient, cfg: cfg},
		meter,
	)
}
