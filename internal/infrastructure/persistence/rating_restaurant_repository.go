package persistence

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type RatingRestaurantRepo struct {
	ds RatingRestaurantDataStore
}

func (repo *RatingRestaurantRepo) Create(ctx context.Context, rr model.RatingRestaurant) error {
	now := time.Now().UTC()
	rr.CreatedAt = now
	rr.UpdatedAt = now
	return repo.ds.Insert(ctx, rr)
}

func (repo *RatingRestaurantRepo) Get(ctx context.Context, orderID string) (*model.RatingRestaurant, error) {
	var rr model.RatingRestaurant
	if err := repo.ds.FindOne(ctx, bson.M{"orders": bson.M{"$in": []string{orderID}}}, &rr); err != nil {
		if err == mongodb.ErrDataNotFound {
			return &rr, nil
		}
		return nil, err
	}
	return &rr, nil
}

type RatingRestaurantDataStore mongodb.DataStoreInterface

func ProvideRatingRestaurantRepository(datastore RatingRestaurantDataStore, meter metric.Meter) *repository.ProxyRatingRestaurantRepository {
	return repository.NewLatencyProxyRatingRestaurantRepository(&RatingRestaurantRepo{ds: datastore}, meter)
}

func ProvideRatingRestaurantDataStore(conn *mongodb.Conn) RatingRestaurantDataStore {
	return mongodb.NewDataStoreWithConn(conn, "rating_restaurant")
}
