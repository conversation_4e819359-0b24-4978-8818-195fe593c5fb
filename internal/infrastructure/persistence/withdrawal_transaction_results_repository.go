package persistence

import (
	"context"
	"math"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

var _ repository.WithdrawalTransactionResultsRepository = (*DatastoreResultWithdrawRepository)(nil)

type MongoWithdrawTransactionResultQuery struct {
	Start time.Time
	End   time.Time
}

func (q *MongoWithdrawTransactionResultQuery) WithStart(t time.Time) repository.WithdrawTransactionResultQuery {
	q.Start = t
	return q
}

func (q *MongoWithdrawTransactionResultQuery) WithEnd(t time.Time) repository.WithdrawTransactionResultQuery {
	q.End = t
	return q
}

func (q *MongoWithdrawTransactionResultQuery) Query() bson.M {
	query := bson.M{}

	if !q.Start.IsZero() || !q.End.IsZero() {
		rangeQuery := bson.M{}
		if !q.Start.IsZero() {
			rangeQuery["$gte"] = q.Start
		}
		if !q.End.IsZero() {
			rangeQuery["$lte"] = q.End
		}
		query["created_at"] = rangeQuery
	}

	return query
}

func BuildWithdrawTransactionResultQuery() repository.WithdrawTransactionResultQuery {
	return &MongoWithdrawTransactionResultQuery{}
}

type DatastoreResultWithdrawRepository struct {
	datastore WithdrawalTransactionResultsDataStoreDataStore
}

func (repo *DatastoreResultWithdrawRepository) CreateAll(ctx context.Context, transaction []model.WithdrawalTransactionResult) error {
	castedTrans := make([]interface{}, len(transaction))
	for i, t := range transaction {
		castedTrans[i] = t
	}
	if err := repo.datastore.InsertMany(ctx, castedTrans); err != nil {
		return err
	}

	return nil
}

func (repo *DatastoreResultWithdrawRepository) List(ctx context.Context, query interface{}, opts ...repository.Option) ([]model.WithdrawalTransactionResult, error) {
	mq, ok := query.(*MongoWithdrawTransactionResultQuery)
	if !ok {
		return nil, errors.New("query must be MongoWithdrawTransactionResultQuery")
	}
	q := mq.Query()

	var totalCount int
	batchSize := 500

	err := repo.datastore.Count(ctx, q, &totalCount)
	if err != nil {
		return nil, err
	}

	batch := math.Ceil(float64(totalCount) / float64(batchSize))
	results := make([]model.WithdrawalTransactionResult, 0, totalCount)

	for i := 0; i < int(batch); i++ {
		skip := i * batchSize

		wtrs := make([]model.WithdrawalTransactionResult, 0, batchSize)
		if err := repo.datastore.FindAndSort(ctx, q, skip, batchSize, []string{"created_at"}, &wtrs); err != nil {
			return nil, err
		}

		results = append(results, wtrs...)
	}

	return results, nil
}

func ProvideWithdrawalTransactionResultsRepository(ds WithdrawalTransactionResultsDataStoreDataStore, meter metric.Meter) *repository.ProxyWithdrawalTransactionResultsRepository {
	return repository.NewLatencyProxyWithdrawalTransactionResultsRepository(&DatastoreResultWithdrawRepository{datastore: ds}, meter)
}

type WithdrawalTransactionResultsDataStoreDataStore mongodb.DataStoreInterface

func ProvideWithdrawalTransactionResultsDataStore(conn *mongodb.Conn) WithdrawalTransactionResultsDataStoreDataStore {
	return mongodb.NewDataStoreWithConn(conn, "withdrawal_transaction_results")
}
