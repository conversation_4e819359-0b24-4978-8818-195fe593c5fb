package persistence

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type AmountByAction struct {
	Action model.RewardTransactionAction `bson:"action"`
	Amount int                           `bson:"amount"`
}

type MongoRewardTransactionRepository struct {
	ds MongoRewardTransactionDataStore
}

func (r *MongoRewardTransactionRepository) Insert(ctx context.Context, txn []model.RewardTransaction) error {
	data := make([]interface{}, len(txn))
	for i, t := range txn {
		data[i] = t
	}
	return r.ds.InsertMany(ctx, data)
}

func (r *MongoRewardTransactionRepository) SumCoinBalance(ctx context.Context, driverID string, date time.Time, opts ...repository.Option) (int, error) {
	from := timeutil.DateTruncateTZ(date, timeutil.BangkokLocation())
	to := from.AddDate(0, 0, 1)

	pipelines := []bson.M{
		{"$match": bson.M{
			"driver_id":      driverID,
			"requested_time": bson.M{"$gte": from, "$lt": to},
			"type": bson.M{"$nin": []string{
				string(model.ConvertToCashRewardTransactionType),
				string(model.NotEnoughCoinTransactionType),
			}},
		}},
		{"$addFields": bson.M{
			"amount": bson.M{"$cond": bson.A{
				bson.M{"$eq": bson.A{"$action", "DEDUCT"}},
				bson.M{"$multiply": bson.A{-1, "$amount"}},
				"$amount",
			}},
		}},
		{"$group": bson.M{
			"_id":    nil,
			"amount": bson.M{"$sum": "$amount"},
		}},
	}

	var result struct {
		Amount int `bson:"amount"`
	}

	if err := r.ds.AggregateOne(ctx, pipelines, &result, repository.ToDBOptions(opts)...); err != nil && !errors.Is(err, mongodb.ErrDataNotFound) {
		return 0, err
	}

	return result.Amount, nil
}

func (r *MongoRewardTransactionRepository) FindByBKKDate(ctx context.Context, date time.Time, driverID string) ([]model.RewardTransaction, error) {
	var rewardTxns []model.RewardTransaction

	startTime, endTime := timeutil.DateTruncateTZ(date, timeutil.BangkokLocation()), timeutil.DateCeilingTZ(date, timeutil.BangkokLocation())
	query := bson.M{
		"driver_id": driverID,
		"requested_time": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
	}
	if err := r.ds.Find(ctx, query, 0, 0, &rewardTxns); err != nil {
		return nil, err
	}
	return rewardTxns, nil
}

func (r *MongoRewardTransactionRepository) FindFromBKKCreatedAtInterval(ctx context.Context, start, end time.Time, driverID string, opts ...repository.Option) ([]model.RewardTransaction, error) {
	var rewardTxns []model.RewardTransaction

	startTime, endTime := start.In(timeutil.BangkokLocation()), end.In(timeutil.BangkokLocation())
	query := bson.M{
		"driver_id": driverID,
		"created_at": bson.M{
			"$gte": startTime,
			"$lte": endTime,
		},
	}
	if err := r.ds.Find(ctx, query, 0, 0, &rewardTxns, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	return rewardTxns, nil
}

type MongoRewardTransactionDataStore mongodb.DataStoreInterface

func ProvideMongoRewardTransactionRepository(datastore MongoRewardTransactionDataStore, meter metric.Meter) *repository.ProxyRewardTransactionRepository {
	return repository.NewLatencyProxyRewardTransactionRepository(&MongoRewardTransactionRepository{ds: datastore}, meter)
}

func ProvideMongoRewardTransactionDataStore(conn *mongodb.Conn) MongoRewardTransactionDataStore {
	return mongodb.NewDataStoreWithConn(conn, "reward_transactions")
}
