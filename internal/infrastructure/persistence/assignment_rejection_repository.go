package persistence

import (
	"context"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type DataStoreAssignmentRejectionRepository struct {
	datastore AssignmentRejectionDataStore
}

func (repo *DataStoreAssignmentRejectionRepository) Create(ctx context.Context, ar *model.AssignmentRejection) error {
	return repo.datastore.Insert(ctx, ar)
}

func ProvideDataStoreAssignmentRejectionRepository(ds AssignmentRejectionDataStore, meter metric.Meter) *repository.ProxyAssignmentRejectionRepository {
	return repository.NewLatencyProxyAssignmentRejectionRepository(&DataStoreAssignmentRejectionRepository{
		datastore: ds,
	}, meter)
}

type AssignmentRejectionDataStore mongodb.DataStoreInterface

func ProvideAssignmentRejectionDataStore(conn *mongodb.Conn) AssignmentRejectionDataStore {
	return mongodb.NewDataStoreWithConn(conn, "assignment_rejection")
}
