package persistence

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type ShiftRepository struct {
	ds ShiftDataStore
}

func (sh *ShiftRepository) Find(ctx context.Context, query repository.ShiftQuery, skip, limit int, opts ...repository.Option) ([]model.Shift, int, error) {
	res := make([]model.Shift, limit)

	filter := bson.M{}
	if !query.End.IsZero() {
		filter["end"] = bson.M{"$gte": query.End}
	}
	if query.Region != "" {
		filter["region"] = query.Region
	}
	if query.ServiceType != "" {
		filter["service_type"] = query.ServiceType
	}
	if query.Active.Valid {
		filter["active"] = query.Active.Bool
	}
	if query.IsDeleted.Valid {
		filter["deleted_at"] = bson.M{"$exists": query.IsDeleted.Bool}
	}
	if query.Region != "" {
		filter["region"] = query.Region
	}
	if query.RestaurantIdEnabled.Valid {
		filter["restaurant_id_enabled"] = query.RestaurantIdEnabled.Bool
	}
	if query.RestaurantIds != "" {
		rts := strings.Split(query.RestaurantIds, ",")
		ids := make([]string, 0)
		for _, id := range rts {
			ids = append(ids, id)
		}
		filter["restaurant_ids"] = bson.M{
			"$all": ids,
		}
	}
	if query.BasketPriceEnabled.Valid {
		filter["max_basket_price_enabled"] = query.BasketPriceEnabled.Bool
	}
	if query.BasketPrice != 0 {
		filter["max_basket_price"] = query.BasketPrice
	}
	if !query.StartDate.IsZero() {
		filter["start"] = bson.M{"$gte": query.StartDate}
	}
	if !query.EndDate.IsZero() {
		filter["end"] = bson.M{"$lte": query.EndDate}
	}

	if len(query.DriverIDs) != 0 {
		filter["driver_ids"] = bson.M{"$in": query.DriverIDs}
	}

	sort := []string{"-created_at"}

	if err := sh.ds.FindAndSort(ctx, filter, skip, limit, sort, &res, repository.ToDBOptions(opts)...); err != nil {
		return []model.Shift{}, 0, err
	}

	var count int
	err := sh.ds.Count(ctx, bson.M{
		"deleted_at": bson.M{"$exists": false},
	}, &count, repository.ToDBOptions(opts)...)
	if err != nil {
		return []model.Shift{}, 0, err
	}

	return res, count, nil
}

func (sh *ShiftRepository) FindSorted(ctx context.Context, region, driverId string, isBlockedFromBookingShift bool, day, cutoff int, start, end time.Time, skip, limit int, opts ...repository.Option) ([]model.Shift, error) {
	var shifts []model.Shift
	now := timeutil.BangkokNow()
	lastDateToBook := now.Add(time.Hour * 24 * time.Duration(day))

	cutoffInterval := time.Minute * time.Duration(cutoff)
	co := now.Add(cutoffInterval)
	st := now
	e := lastDateToBook

	if (start != time.Time{}) {
		st = start
		if start.After(co) {
			co = start
		}
	}
	if (end != time.Time{}) {
		e = end
	}

	startFromClient := bson.M{
		"$and": []bson.M{
			{"deleted_at": bson.M{"$exists": false}},
			{"region": region},
			{"start": bson.M{"$gt": st}},
			{"end": bson.M{"$lt": e}},
			{"active": true},
			{"driver_ids": driverId},
		},
	}

	q := startFromClient

	if !isBlockedFromBookingShift {
		startFromCutoff := bson.M{
			"$and": []bson.M{
				{"deleted_at": bson.M{"$exists": false}},
				{"region": region},
				{"start": bson.M{"$gt": co}},
				{"end": bson.M{"$lt": e}},
				{"active": true},
			},
		}
		q = bson.M{"$or": []bson.M{
			startFromClient,
			startFromCutoff,
		}}
	}

	if err := sh.ds.FindAndSort(ctx, q, skip, limit, []string{"start"}, &shifts, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return shifts, nil
}

func (sh *ShiftRepository) CreateAll(ctx context.Context, shiftList []model.Shift, opts ...repository.Option) error {
	shifts := make([]interface{}, len(shiftList))
	now := timeutil.BangkokNow()
	for i, s := range shiftList {
		s.CreatedAt = now
		s.UpdatedAt = now
		shifts[i] = s
	}
	if err := sh.ds.InsertMany(ctx, shifts, repository.ToDBOptions(opts)...); err != nil {
		return err
	}

	return nil
}

func (sh *ShiftRepository) Delete(ctx context.Context, shift *model.Shift, opts ...repository.Option) error {
	if err := sh.ds.Update(ctx, bson.M{
		"_id": shift.ID,
	}, bson.M{
		"$set": bson.M{"deleted_at": timeutil.BangkokNow()},
	}, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}
	return nil
}

func (sh *ShiftRepository) UpdateByID(ctx context.Context, id string, m *model.Shift) error {

	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return err
	}

	m.UpdatedAt = timeutil.BangkokNow()
	err = sh.ds.Replace(ctx, bson.M{"_id": oid}, m)
	return err
}

func (sh *ShiftRepository) AnyStartEndDatetimeDuplicated(ctx context.Context, shiftList []model.Shift, ignoreIDs []string, opts ...repository.Option) error {

	ids := make([]primitive.ObjectID, 0)

	for _, id := range ignoreIDs {
		i, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			logrus.Warnf("can't convert id %s to object id", id)
		} else {
			ids = append(ids, i)
		}
	}

	for _, s := range shiftList {
		query := bson.M{
			"region":     s.Region,
			"start":      s.Start,
			"end":        s.End,
			"geometry":   s.Geometry,
			"deleted_at": bson.M{"$exists": false},
			"_id":        bson.M{"$nin": ids},
		}
		if sh.ds.IsExist(ctx, query, repository.ToDBOptions(opts)...) {
			return fmt.Errorf("start time: %v - end time: %v is duplicated", s.Start, s.End)
		}
	}

	return nil
}

func (sh *ShiftRepository) FindByIdDeletedAtNotExist(ctx context.Context, id string, opts ...repository.Option) (*model.Shift, error) {
	shift := &model.Shift{}
	oid, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
	if err != nil {
		return nil, err
	}

	if err := sh.ds.FindOne(ctx, bson.M{"_id": oid, "deleted_at": bson.M{"$exists": false}}, shift, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return shift, nil
}

func (sh *ShiftRepository) FindExpiredShift(ctx context.Context, current time.Time, shiftIDs []string, opts ...repository.Option) ([]string, error) {
	primitiveIds := make([]primitive.ObjectID, len(shiftIDs))
	for i, id := range shiftIDs {
		pid, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return nil, err
		}
		primitiveIds[i] = pid
	}

	q := bson.M{
		"_id": bson.M{"$in": primitiveIds},
		"end": bson.M{
			"$lt": current,
		},
	}
	s := bson.M{
		"_id": 1,
	}

	var shifts []model.Shift
	if err := sh.ds.FindWithSelector(ctx, q, s, &shifts, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	ids := make([]string, len(shifts))
	for i, sh := range shifts {
		ids[i] = sh.ID.Hex()
	}

	return ids, nil

}

func (sh *ShiftRepository) FindActiveByIdAndRegionAndNotDeleted(ctx context.Context, id, region string, opts ...repository.Option) (*model.Shift, error) {
	shift := &model.Shift{}
	oid, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
	if err != nil {
		return nil, err
	}
	q := bson.M{
		"_id":        oid,
		"active":     true,
		"region":     region,
		"deleted_at": bson.M{"$exists": false},
	}
	if err := sh.ds.FindOne(ctx, q, shift, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return shift, nil
}

func (sh *ShiftRepository) Book(ctx context.Context, shift *model.Shift, driverId string, opts ...repository.Option) error {
	if err := sh.ds.Update(ctx, bson.M{
		"_id":           shift.ID,
		"booked_amount": shift.BookedAmount,
	}, bson.M{
		"$set":      bson.M{"booked_amount": shift.BookedAmount + 1},
		"$addToSet": bson.M{"driver_ids": driverId},
	}, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}

	return nil
}

func (sh *ShiftRepository) UnBook(ctx context.Context, id primitive.ObjectID, driverId string, opts ...repository.Option) error {
	if err := sh.ds.Update(ctx, bson.M{
		"_id": id,
	}, bson.M{
		"$inc":  bson.M{"booked_amount": -1},
		"$pull": bson.M{"driver_ids": driverId},
	}, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}

	return nil
}

func (sh *ShiftRepository) FindByID(ctx context.Context, id string, opts ...repository.Option) (model.Shift, error) {
	var res model.Shift
	oid, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
	if err != nil {
		return model.Shift{}, err
	}

	err = sh.ds.FindOne(ctx, bson.M{"_id": oid}, &res, repository.ToDBOptions(opts)...)
	if err != nil {
		return model.Shift{}, err
	}
	return res, nil
}

func (sh *ShiftRepository) FindIncoming(ctx context.Context, ids []string, driverId string, skip, limit int, opts ...repository.Option) ([]model.Shift, error) {
	oIds := make([]primitive.ObjectID, len(ids))
	for _, id := range ids {
		oid, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
		if err == nil {
			oIds = append(oIds, oid)
		}
	}
	now := timeutil.BangkokNow()
	q := bson.M{
		"_id":        bson.M{"$in": oIds},
		"active":     true,
		"deleted_at": bson.M{"$exists": false},
		"driver_ids": driverId,
		"end":        bson.M{"$gt": now},
	}
	res := make([]model.Shift, limit)
	sort := []string{"start"}

	if err := sh.ds.FindAndSort(ctx, q, skip, limit, sort, &res, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	return res, nil
}

func (sh *ShiftRepository) UnBookMultipleShift(ctx context.Context, driverID string, shiftIDs []string) error {

	ids := make([]primitive.ObjectID, len(shiftIDs))
	for _, id := range shiftIDs {
		oid, err := primitive.ObjectIDFromHex(strings.TrimSpace(id))
		if err == nil {
			ids = append(ids, oid)
		}
	}

	_, err := sh.ds.UpdateAll(ctx, bson.M{
		"_id": bson.M{"$in": ids},
	}, bson.M{
		"$inc":  bson.M{"booked_amount": -1},
		"$pull": bson.M{"driver_ids": driverID},
	})
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}
	return nil
}

func (sh *ShiftRepository) FindStartedOrIncomingForNotification(ctx context.Context, incomingTime, end time.Time, skip, limit int, opts ...repository.Option) ([]model.Shift, error) {
	var shifts []model.Shift
	incomingHaveNotNotified := bson.M{"$or": []bson.M{
		{"is_incoming_notified": false},
		{"is_incoming_notified": bson.M{"$exists": false}},
	}}
	staredHaveNotNotified := bson.M{"$or": []bson.M{
		{"is_started_notified": false},
		{"is_started_notified": bson.M{"$exists": false}},
	}}

	haveNotNotified := bson.M{"$or": []bson.M{
		incomingHaveNotNotified,
		staredHaveNotNotified},
	}
	var q = bson.M{
		"$and": []bson.M{
			{"deleted_at": bson.M{"$exists": false}},
			{"start": bson.M{"$gt": incomingTime}},
			{"end": bson.M{"$lte": end}},
			{"active": true},
			haveNotNotified,
		},
	}

	if err := sh.ds.FindAndSort(ctx, q, skip, limit, []string{"start"}, &shifts, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return shifts, nil
}
func (sh *ShiftRepository) SaveStartedNotified(ctx context.Context, notifiedIDs []model.Shift, opts ...repository.Option) error {
	ids := make([]primitive.ObjectID, len(notifiedIDs))
	for i, s := range notifiedIDs {
		ids[i] = s.ID
	}

	_, err := sh.ds.UpdateAll(ctx, bson.M{
		"_id": bson.M{"$in": ids},
	}, bson.M{
		"$set": bson.M{"is_started_notified": true},
	}, repository.ToDBOptions(opts)...)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}

	return nil
}
func (sh *ShiftRepository) SaveIncomingNotified(ctx context.Context, notifiedIDs []model.Shift, opts ...repository.Option) error {
	ids := make([]primitive.ObjectID, len(notifiedIDs))
	for i, s := range notifiedIDs {
		ids[i] = s.ID
	}
	_, err := sh.ds.UpdateAll(ctx, bson.M{
		"_id": bson.M{"$in": ids},
	}, bson.M{
		"$set": bson.M{"is_incoming_notified": true},
	}, repository.ToDBOptions(opts)...)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}

	return nil
}

func (sh *ShiftRepository) FindCurrentActiveByDriverIdAndRegion(ctx context.Context, driverId, region string, opts ...repository.Option) (*model.Shift, error) {
	shift := &model.Shift{}
	now := timeutil.BangkokNow()
	q := bson.M{
		"active":     true,
		"region":     region,
		"start":      bson.M{"$lte": now},
		"end":        bson.M{"$gt": now},
		"driver_ids": driverId,
		"deleted_at": bson.M{"$exists": false},
	}
	if err := sh.ds.FindOne(ctx, q, shift, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return shift, nil
}

func (sh *ShiftRepository) FindActiveByDriverIdAndRegion(ctx context.Context, driverId, region string, start, end time.Time, opts ...repository.Option) ([]model.Shift, error) {
	var shifts []model.Shift
	q := bson.M{
		"deleted_at": bson.M{"$exists": false},
		"region":     region,
		"active":     true,
		"driver_ids": driverId,
		"start":      bson.M{"$gte": start},
		"end":        bson.M{"$lte": end},
	}

	if err := sh.ds.Find(ctx, q, 0, 0, &shifts, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return shifts, nil
}

type ShiftDataStore mongodb.DataStoreInterface

func ProvideShiftRepository(ds ShiftDataStore, meter metric.Meter) *repository.ProxyShiftRepository {
	return repository.NewLatencyProxyShiftRepository(&ShiftRepository{ds: ds}, meter)
}

func ProvideShiftDataStore(conn *mongodb.Conn) ShiftDataStore {
	return mongodb.NewDataStoreWithConn(conn, "shifts")
}
