package persistence

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/absinthe/crypt"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type DataStoreWhitelistPhoneRepository struct {
	datastore WhitelistPhoneDataStore
}

func (repo *DataStoreWhitelistPhoneRepository) GetByPhone(ctx context.Context, phone crypt.EncryptedString) (*model.WhitelistPhone, error) {
	whitelistPhone := &model.WhitelistPhone{}
	err := repo.datastore.FindOne(ctx, bson.M{"phone_number": phone.DeterministicEqWithContext(ctx)}, whitelistPhone)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}

		return nil, err
	}

	return whitelistPhone, nil
}

func (repo *DataStoreWhitelistPhoneRepository) BulkCreate(ctx context.Context, whitelistPhones []model.WhitelistPhone) error {
	data := make([]interface{}, len(whitelistPhones))
	for i, w := range whitelistPhones {
		data[i] = w
	}
	return repo.datastore.InsertMany(ctx, data)
}

func (repo *DataStoreWhitelistPhoneRepository) FindByPhones(ctx context.Context, nums []crypt.EncryptedString) (model.WhitelistPhoneList, error) {
	phones := crypt.EncryptedStringArray(nums)
	var out []model.WhitelistPhone
	err := repo.datastore.Find(ctx, bson.M{"phone_number": bson.M{"$in": phones.AllPossibleDeterministicValue(ctx)}}, 0, 0, &out)
	return out, err
}

func (repo *DataStoreWhitelistPhoneRepository) List(ctx context.Context, phoneFilter crypt.EncryptedString, skip int, limit int) ([]model.WhitelistPhone, error) {
	query := bson.M{}
	if phoneFilter != "" {
		query = bson.M{"phone_number": phoneFilter.DeterministicEqWithContext(ctx)}
	}
	out := []model.WhitelistPhone{}
	if err := repo.datastore.Find(ctx, query, skip, limit, &out); err != nil {
		return out, err
	}
	return out, nil
}

func (repo *DataStoreWhitelistPhoneRepository) Count(ctx context.Context, phoneFilter crypt.EncryptedString) (int, error) {
	query := bson.M{}
	if phoneFilter != "" {
		query = bson.M{"phone_number": phoneFilter.DeterministicEqWithContext(ctx)}
	}
	var out int
	if err := repo.datastore.Count(ctx, query, &out); err != nil {
		return out, err
	}
	return out, nil
}

func (repo *DataStoreWhitelistPhoneRepository) Update(ctx context.Context, phoneWhitelist *model.WhitelistPhone) error {
	phoneWhitelist.UpdatedAt = time.Now().UTC()
	return repo.datastore.Replace(ctx, bson.M{"phone_number": phoneWhitelist.PhoneNumber}, phoneWhitelist)
}

func ProvideWhitelistPhoneRepository(ds WhitelistPhoneDataStore, meter metric.Meter) *repository.ProxyWhitelistPhoneRepository {
	return repository.NewLatencyProxyWhitelistPhoneRepository(&DataStoreWhitelistPhoneRepository{
		datastore: ds,
	}, meter)
}

type WhitelistPhoneDataStore mongodb.DataStoreInterface

func ProvideWhitelistPhoneDataStore(conn *mongodb.Conn) WhitelistPhoneDataStore {
	return mongodb.NewDataStoreWithConn(conn, "whitelist_phone")
}
