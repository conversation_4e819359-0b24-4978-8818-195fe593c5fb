//go:build integration_test
// +build integration_test

package persistence_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestApprovalRepository(t *testing.T) {
	t.Run("store approval", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		info := model.NewCreditChargeInfo("ord1", "drv1", types.NewMoney(100), model.WithdrawErrorRecallApprovalSubType)
		approval := model.NewApproval("test", "automate", info)

		err := ctn.DataStoreApprovalRepository.Create(context.Background(), approval)

		require.NoError(t, err)
		appr, err := ctn.DataStoreApprovalRepository.Get(context.Background(), approval.ApprovalID)
		apprInfo := appr.Info.(*model.CreditChargeInfo)
		require.NoError(t, err)
		assert.Equal(t, "drv1", apprInfo.DriverID())
		assert.Equal(t, types.NewMoney(100), apprInfo.Amount())
		assert.Equal(t, "ord1", apprInfo.OrderID())
		assert.Equal(t, model.CreditCategory, apprInfo.Category())
		assert.Equal(t, model.ChargeAction, apprInfo.Action())
		assert.Equal(t, model.WithdrawErrorRecallApprovalSubType, apprInfo.SubType())
		assert.Equal(t, info.TransRefID().String(), apprInfo.TransRefID().String())
	})
}
