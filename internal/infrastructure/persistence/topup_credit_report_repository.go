package persistence

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type TopupCreditReportRepository struct {
	ds TopupCreditReportDataStore
}

func (i *TopupCreditReportRepository) CountByQuery(ctx context.Context, q repository.TopupCreditReportQuery, opt ...repository.Option) (int, error) {
	qr := getTopupCreditReportBSONQuery(q)
	var count int
	err := i.ds.Count(ctx, qr, &count, repository.ToDBOptions(opt)...)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (i *TopupCreditReportRepository) GetByQuery(ctx context.Context, q repository.TopupCreditReportQuery, opt ...repository.Option) ([]model.TopupCreditReport, error) {
	qr := getTopupCreditReportBSONQuery(q)
	var res []model.TopupCreditReport
	err := i.ds.FindAndSort(ctx, qr, 0, q.Limit, []string{"_id"}, &res, repository.ToDBOptions(opt)...)
	if err != nil {
		return nil, err
	}

	return res, nil
}

// CreateAll insert all topup credit report and return slice of insert failed transaction id
func (i *TopupCreditReportRepository) CreateAll(ctx context.Context, m []model.TopupCreditReport) []string {
	var failInsert []string
	for _, v := range m {
		err := i.ds.Insert(ctx, v)
		if err != nil {
			logrus.Infof("Transaction ID %v insert failed: %v", v.TransactionID, err)
			failInsert = append(failInsert, v.TransactionID)
		}
	}

	logrus.Infof("All record %v, insert %v, failed %v", len(m), len(m)-len(failInsert), len(failInsert))

	return failInsert

}

func (i *TopupCreditReportRepository) IterateAllOccurredBetween(ctx context.Context, fromInclusive time.Time, toInclusive time.Time) (repository.Cursor, error) {
	query := bson.M{
		"transaction_updated_time": bson.M{
			"$gte": fromInclusive.Add(7 * time.Hour).In(time.UTC),
			"$lte": toInclusive.Add(7 * time.Hour).In(time.UTC),
		},
	}
	return i.ds.collection().Find(ctx, query,
		options.Find().SetBatchSize(10))
}

func (i *TopupCreditReportRepository) UpsertAllWithMatchedTransactionId(ctx context.Context, reports []model.TopupCreditReport) error {
	var operations []mongo.WriteModel
	for _, report := range reports {
		if strings.TrimSpace(report.TransactionID) == "" {
			return fmt.Errorf("assertion fails: transaction_id must not be empty")
		}
		updateOp := mongo.NewUpdateOneModel()
		updateOp.SetFilter(bson.M{"transaction_id": report.TransactionID})
		updateOp.SetUpdate(bson.M{"$set": report})
		updateOp.SetUpsert(true)

		operations = append(operations, updateOp)
	}
	if _, err := i.ds.collection().BulkWrite(ctx, operations); err != nil {
		return fmt.Errorf("BulkWrite fails: %w", err)
	}
	return nil
}

func (i *TopupCreditReportRepository) InsertAll(ctx context.Context, tcr []model.TopupCreditReport) error {

	now := time.Now().UTC()
	data := make([]interface{}, len(tcr))
	for i, itm := range tcr {
		itm.CreatedAt = now
		itm.UpdatedAt = now
		data[i] = itm
	}
	if err := i.ds.InsertMany(ctx, data); err != nil {
		logrus.Errorf("TopupCreditReportRepository CreateAll Err: %v", err)
		return err
	}
	return nil
}

func ProvideTopupCreditReportDataStore(conn *mongodb.Conn) TopupCreditReportDataStore {
	name := "topup_credit_reports"
	return TopupCreditReportDataStore{
		DataStoreInterface: mongodb.NewDataStoreWithConn(conn, name),
		conn:               conn,
		name:               name,
	}
}

func ProvideTopupCreditReportRepository(ds TopupCreditReportDataStore, meter metric.Meter) *repository.ProxyTopupCreditReportRepository {
	return repository.NewLatencyProxyTopupCreditReportRepository(&TopupCreditReportRepository{ds: ds}, meter)
}

func getTopupCreditReportBSONQuery(q repository.TopupCreditReportQuery) bson.M {
	qr := bson.M{}

	if q.Bank != "" {
		qr["bank"] = q.Bank
	}

	if !q.LastID.IsZero() {
		qr["_id"] = bson.M{
			"$gt": q.LastID,
		}
	}

	amount := bson.M{}
	if q.GTEAmount > 0 {
		amount["$gte"] = q.GTEAmount
	}

	if q.LTEAmount > 0 {
		amount["$lte"] = q.LTEAmount
	}

	if len(amount) > 0 {
		qr["amount"] = amount
	}

	transDate := bson.M{}

	if !q.BeginTransactionUpdatedAt.IsZero() {
		transDate["$gte"] = q.BeginTransactionUpdatedAt
	}

	if !q.EndTransactionUpdatedAt.IsZero() {
		transDate["$lte"] = q.EndTransactionUpdatedAt
	}

	if len(transDate) > 0 {
		qr["transaction_updated_time"] = transDate
	}

	return qr
}

type TopupCreditReportDataStore struct {
	mongodb.DataStoreInterface
	conn *mongodb.Conn
	name string
}

func (ds TopupCreditReportDataStore) collection() *mongo.Collection {
	return ds.conn.Database().Collection(ds.name)
}
