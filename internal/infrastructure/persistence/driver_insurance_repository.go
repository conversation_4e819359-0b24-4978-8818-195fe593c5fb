package persistence

import (
	"context"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type MongoDriverInsuranceQuery struct {
	IDs                []string
	DriverIDs          []string
	Status             []model.DriverInsuranceStatus
	MonthCycle         string
	Programs           []model.DriverInsuranceProgram
	ImagesUploadResult *bool
	Type               []model.DriverInsuranceType
	CreatedAtAfter     time.Time
}

func (mdiq *MongoDriverInsuranceQuery) WithID(ids ...string) repository.DriverInsuranceQuery {
	mdiq.IDs = ids
	return mdiq
}

func (mdiq *MongoDriverInsuranceQuery) WithDriverID(driverIds ...string) repository.DriverInsuranceQuery {
	mdiq.DriverIDs = driverIds
	return mdiq
}

func (mdiq *MongoDriverInsuranceQuery) WithStatus(insuranceStatus ...model.DriverInsuranceStatus) repository.DriverInsuranceQuery {
	mdiq.Status = insuranceStatus
	return mdiq
}

func (mdiq *MongoDriverInsuranceQuery) WithMonthCycle(monthCycle string) repository.DriverInsuranceQuery {
	mdiq.MonthCycle = monthCycle
	return mdiq
}

func (mdiq *MongoDriverInsuranceQuery) WithProgram(insuranceProgram ...model.DriverInsuranceProgram) repository.DriverInsuranceQuery {
	mdiq.Programs = insuranceProgram
	return mdiq
}

func (mdiq *MongoDriverInsuranceQuery) WithImagesUploadResult(result bool) repository.DriverInsuranceQuery {
	mdiq.ImagesUploadResult = &result
	return mdiq
}

func (mdiq *MongoDriverInsuranceQuery) WithType(insuranceType ...model.DriverInsuranceType) repository.DriverInsuranceQuery {
	mdiq.Type = insuranceType
	return mdiq
}

func (mdiq *MongoDriverInsuranceQuery) WithCreatedAtAfter(createdAt time.Time) repository.DriverInsuranceQuery {
	mdiq.CreatedAtAfter = createdAt
	return mdiq
}

func (mdiq *MongoDriverInsuranceQuery) Query() bson.M {
	query := bson.M{}
	if len(mdiq.IDs) > 0 {
		var bsonIDs []primitive.ObjectID
		for _, id := range mdiq.IDs {
			oid, err := primitive.ObjectIDFromHex(id)
			if err != nil {
				continue
			}
			bsonIDs = append(bsonIDs, oid)
		}
		query["_id"] = bson.M{"$in": bsonIDs}
	}

	if len(mdiq.DriverIDs) > 0 {
		query["driver_id"] = bson.M{"$in": mdiq.DriverIDs}
	}

	if len(mdiq.Status) > 0 {
		query["status"] = bson.M{"$in": mdiq.Status}
	}

	if mdiq.MonthCycle != "" {
		query["month_cycle"] = mdiq.MonthCycle
	}

	if len(mdiq.Programs) > 0 {
		query["program"] = bson.M{"$in": mdiq.Programs}
	}

	if mdiq.ImagesUploadResult != nil {
		query["images_upload_result.is_all_success"] = *mdiq.ImagesUploadResult
	}

	if !mdiq.CreatedAtAfter.IsZero() {
		query["created_at"] = bson.M{"$gte": mdiq.CreatedAtAfter}
	}

	return query
}

func BuildDriverInsuranceQuery() repository.DriverInsuranceQuery {
	return &MongoDriverInsuranceQuery{}
}

var _ repository.DriverInsuranceRepository = (*DriverInsuranceRepo)(nil)

type DriverInsuranceRepo struct {
	ds DriverInsuranceDataStore
}

func (ir *DriverInsuranceRepo) CreateAll(ctx context.Context, insurances ...model.DriverInsurance) error {
	now := time.Now().UTC()
	data := make([]interface{}, len(insurances))
	for i, itm := range insurances {
		itm.CreatedAt = now
		itm.UpdatedAt = now
		data[i] = itm
	}

	if err := ir.ds.InsertMany(ctx, data); err != nil {
		return err
	}
	return nil
}

func (ir *DriverInsuranceRepo) FindWithQueryAndSort(ctx context.Context, query repository.DriverInsuranceQuery, skip, limit int, opts ...repository.Option) ([]model.DriverInsurance, error) {
	var driverInsurances []model.DriverInsurance

	mq, ok := query.(*MongoDriverInsuranceQuery)
	if !ok {
		return nil, errors.New("query must be MongoInsuranceQuery")
	}
	q := mq.Query()

	if err := ir.ds.FindAndSort(ctx, q, skip, limit, []string{"-created_at"}, &driverInsurances, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return driverInsurances, nil
}

func (ir *DriverInsuranceRepo) FindConsolidateData(ctx context.Context, query repository.DriverInsuranceQuery, skip, limit int, opts ...repository.Option) ([]model.DriverInsurance, error) {
	var driverInsurances []model.DriverInsurance

	mq, ok := query.(*MongoDriverInsuranceQuery)
	if !ok {
		return nil, errors.New("query must be MongoInsuranceQuery")
	}
	q := mq.Query()
	selector := bson.M{
		"driver_id":   1,
		"type":        1,
		"program":     1,
		"detail":      1,
		"month_cycle": 1,
		"status":      1,
		"created_at":  1,
		"updated_at":  1,
	}

	if err := ir.ds.FindWithSelectorAndSort(ctx, q, selector, 0, 0, []string{"-created_at"}, &driverInsurances); err != nil {
		return driverInsurances, err
	}

	return driverInsurances, nil
}

func (ir *DriverInsuranceRepo) UpdateStatusByDriverIDs(ctx context.Context, targetMonthCycle string, targetStatus model.DriverInsuranceStatus, driverIDs ...string) error {
	selector := bson.M{
		"month_cycle": targetMonthCycle,
		"driver_id": bson.M{
			"$in": driverIDs,
		},
	}

	n := time.Now().UTC()
	update := bson.M{
		"$set": bson.M{"status": targetStatus, "updated_at": n},
	}
	if targetStatus == model.DISActive {
		update = bson.M{
			"$set": bson.M{"status": targetStatus, "completed_at": n},
		}
	}

	if _, err := ir.ds.UpdateAll(ctx, selector, update); err != nil {
		logrus.Errorf("UpdateStatusByDriverIDs UpdateAll Err: %v", err)
		return err
	}
	return nil
}

func (ir *DriverInsuranceRepo) UpdateUploadImagesResult(ctx *gin.Context, models []model.DriverInsurance, result model.ImagesUploadResult) error {
	var formIDs []string
	for _, insurance := range models {
		formIDs = append(formIDs, insurance.FormID)
	}
	selector := bson.M{
		"form_id": bson.M{
			"$in": formIDs,
		},
	}

	update := bson.M{
		"$set": bson.M{"images_upload_result": result, "updated_at": time.Now().UTC()},
	}

	if _, err := ir.ds.UpdateAll(ctx, selector, update); err != nil {
		logrus.Errorf("UpdateUploadImagesResult UpdateAll Err: %v", err)
		return err
	}
	return nil
}

func (ir *DriverInsuranceRepo) ReplaceByID(ctx context.Context, insurances model.DriverInsurance) error {
	insurances.UpdatedAt = time.Now().UTC()
	if err := ir.ds.ReplaceID(ctx, insurances.ID, insurances); err != nil {
		return err
	}
	return nil
}

func (ir *DriverInsuranceRepo) BulkUpdateActivePolicyNumber(ctx *gin.Context, monthCycle string,
	period model.PeriodOfInsurance, insuranceType model.DriverInsuranceType, policyStatusInfos []model.PolicyStatusInfo) (*mongo.BulkWriteResult, error) {
	if len(policyStatusInfos) == 0 {
		return &mongo.BulkWriteResult{}, nil
	}

	var models []mongo.WriteModel
	for _, info := range policyStatusInfos {
		filter := bson.M{
			"driver_id":   info.DriverID,
			"month_cycle": monthCycle,
			"type":        insuranceType,
		}

		n := time.Now()
		update := bson.M{
			"$set": bson.M{
				"updated_at":    n,
				"start_date":    period.Start,
				"end_date":      period.End,
				"policy_number": info.PolicyNumber,
				"status":        model.DISActive,
			},
		}

		if info.Remark != "" {
			newRemark := bson.M{
				"remark":     info.Remark,
				"created_at": n,
			}
			update["$push"] = bson.M{"remarks": newRemark}
		}

		m := mongo.NewUpdateOneModel().SetFilter(filter).SetUpdate(update)
		models = append(models, m)
	}

	writeRes, err := ir.ds.BulkWrite(ctx, models, options.BulkWrite().SetOrdered(false))
	if err != nil {
		return writeRes, err
	}

	return writeRes, nil
}

func (ir *DriverInsuranceRepo) BulkUpdateRejectPolicyNumber(ctx *gin.Context, monthCycle string,
	insuranceType model.DriverInsuranceType, policyStatusInfos []model.PolicyStatusInfo) (*mongo.BulkWriteResult, error) {
	if len(policyStatusInfos) == 0 {
		return &mongo.BulkWriteResult{}, nil
	}

	var models []mongo.WriteModel
	for _, info := range policyStatusInfos {
		filter := bson.M{
			"driver_id":   info.DriverID,
			"month_cycle": monthCycle,
			"type":        insuranceType,
		}

		n := time.Now()
		update := bson.M{
			"$set": bson.M{
				"updated_at": n,
				"status":     model.DISRejectedByInsuranceCompany,
			},
		}
		if info.Remark != "" {
			newRemark := bson.M{
				"remark":     info.Remark,
				"created_at": n,
			}
			update["$push"] = bson.M{"remarks": newRemark}
		}
		m := mongo.NewUpdateOneModel().SetFilter(filter).SetUpdate(update)
		models = append(models, m)
	}

	writeRes, err := ir.ds.BulkWrite(ctx, models, options.BulkWrite().SetOrdered(false))
	if err != nil {
		return writeRes, err
	}

	return writeRes, nil
}

func (ir *DriverInsuranceRepo) UpsertAll(ctx context.Context, insurances ...model.DriverInsurance) error {
	now := time.Now().UTC()
	for _, itm := range insurances {
		itm.CreatedAt = now
		itm.UpdatedAt = now

		selector := primitive.M{
			"driver_id":   itm.DriverID,
			"type":        itm.Type,
			"month_cycle": itm.MonthCycle,
		}
		if _, err := ir.ds.Upsert(ctx, selector, itm); err != nil {
			return err
		}
	}
	return nil
}

func ProvideInsuranceRepository(datastore DriverInsuranceDataStore, meter metric.Meter) *repository.ProxyDriverInsuranceRepository {
	return repository.NewLatencyProxyDriverInsuranceRepository(&DriverInsuranceRepo{ds: datastore}, meter)
}

type DriverInsuranceDataStore mongodb.DataStoreInterface

func ProvideDriverInsuranceDataStore(conn *mongodb.Conn) DriverInsuranceDataStore {
	return mongodb.NewDataStoreWithConn(conn, "driver_insurances")
}
