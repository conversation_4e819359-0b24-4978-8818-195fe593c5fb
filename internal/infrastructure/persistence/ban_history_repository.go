package persistence

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

// MongoBanHistoryRepository is implementation of BanHistoryRepository.
type MongoBanHistoryRepository struct {
	ds BanHistoriesDataStore
}

// Record implements BanHistoryRepository.
func (svc *MongoBanHistoryRepository) Record(ctx context.Context, hist model.BanHistory) error {
	hist.CreatedAt = time.Now().UTC()
	return svc.ds.Insert(ctx, hist)
}

// FindHistory implements BanHistoryRepository.
func (svc *MongoBanHistoryRepository) FindHistory(ctx context.Context, driverID string, opts ...mongodb.Option) (hists []model.BanHistory, err error) {
	err = svc.ds.FindAndSort(ctx, bson.M{"driver_id": driverID}, 0, 0, []string{"-created_at"}, &hists, opts...)
	return
}

// RemoveAllHistory remove ban history of driver.
func (svc *MongoBanHistoryRepository) RemoveAllHistory(ctx context.Context, driverID string) (info interface{}, err error) {
	info, err = svc.ds.RemoveAll(ctx, bson.M{"driver_id": driverID})
	return info, err
}

// NewBanHistoryServiceImpl constructs BanHistoryServiceImpl.
func NewBanHistoryServiceImpl(ds BanHistoriesDataStore) *MongoBanHistoryRepository {
	return &MongoBanHistoryRepository{ds: ds}
}

func ProvideBanHistoryServiceImpl(ds BanHistoriesDataStore, meter metric.Meter) *repository.ProxyBanHistoryRepository {
	return repository.NewLatencyProxyBanHistoryRepository(NewBanHistoryServiceImpl(ds), meter)
}

// BanHistoriesDataStore is type wrapper for driver_ban_histories collection.
type BanHistoriesDataStore mongodb.DataStoreInterface

func ProvideBanHistoriesDataStore(conn *mongodb.Conn) BanHistoriesDataStore {
	return mongodb.NewDataStoreWithConn(conn, "driver_ban_histories")
}
