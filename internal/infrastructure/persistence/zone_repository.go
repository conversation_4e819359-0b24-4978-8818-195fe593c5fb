package persistence

import (
	"context"
	"errors"

	"github.com/twpayne/go-geom"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetarea"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
)

var (
	FleetAreaZoneEnabled                           = &featureflag.Flag{Name: "driver.fleet-area.zone-grpc-enabled.kill-switch"}
	_                    repository.ZoneRepository = (*ZoneRepository)(nil)
)

type ZoneRepository struct {
	ds              ZoneDataStore
	featureFlagSvc  featureflag.Service
	fleetAreaClient fleetarea.FleetAreaClient
}

func geometryHasPointQuery(lat, lng float64) bson.M {
	return bson.M{
		"geometry": bson.M{
			"$geoIntersects": bson.M{
				"$geometry": bson.M{
					"type": "Point",
					"coordinates": [2]float64{
						lng,
						lat,
					},
				},
			},
		},
	}
}

func (r *ZoneRepository) Create(ctx context.Context, zone model.Zone) error {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		_, err := r.fleetAreaClient.CreateZone(ctx, zone)
		return err
	} else {
		if isExists := r.ds.IsExist(ctx, bson.M{"name": zone.Name, "region": zone.Region}); isExists {
			return repository.ErrZoneDuplicate
		}
		return r.ds.Insert(ctx, zone)
	}
}

func (r *ZoneRepository) FindBriefZones(ctx context.Context, req model.BriefZoneQueryReq) ([]model.BriefZone, error) {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		zones, err := r.fleetAreaClient.FindBriefZones(ctx, fleetarea.MapFindBriefZonesRequest(req))
		if err != nil {
			return nil, err
		}

		mappedZones, err := fleetarea.MapBriefZoneModelList(zones.Zones)
		if err != nil {
			return nil, err
		}

		return mappedZones, nil
	} else {
		zones, _, err := r.findFromDB(ctx, model.ZoneQueryReq{
			Region: req.Region,
			Active: req.Active,
		}, 0, 0, nil, nil)
		if err != nil {
			return nil, err
		}
		return fp.MapSlice(model.ZoneToBriefZone, zones), nil
	}
}

func (r *ZoneRepository) Find(ctx context.Context, req model.ZoneQueryReq, skip int, limit int, sort []string, opts ...repository.Option) ([]model.Zone, int64, error) {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		zones, err := r.fleetAreaClient.FindZones(ctx, fleetarea.MapFindZonesRequest(req, skip, limit, sort))
		if err != nil {
			return nil, 0, err
		}

		mappedZones, err := fleetarea.MapZoneModelList(zones.Zones)
		if err != nil {
			return nil, 0, err
		}

		return mappedZones, zones.TotalCount, nil
	}
	return r.findFromDB(ctx, req, skip, limit, sort, opts)
}

func (r *ZoneRepository) findFromDB(ctx context.Context, req model.ZoneQueryReq, skip int, limit int, sort []string, opts []repository.Option) ([]model.Zone, int64, error) {
	var zones []model.Zone
	query := req.Query()
	if err := r.ds.FindAndSort(ctx, query, skip, limit, sort, &zones, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, 0, repository.ErrNotFound
		}
		return nil, 0, err
	}

	var count int
	if err := r.ds.Count(ctx, query, &count, repository.ToDBOptions(opts)...); err != nil {
		return nil, 0, err
	}

	return zones, int64(count), nil
}

func (r *ZoneRepository) FindById(ctx context.Context, id string, opts ...repository.Option) (model.Zone, error) {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		resp, err := r.fleetAreaClient.GetZoneByID(ctx, id)
		if err != nil {
			return model.Zone{}, err
		}
		return fleetarea.MapZoneModel(resp.Zone)
	} else {
		objectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return model.Zone{}, errors.New("invalid id")
		}

		var zone model.Zone
		err = r.ds.FindOne(ctx, bson.M{"_id": objectID}, &zone, repository.ToDBOptions(opts)...)
		if err != nil {
			if err == mongodb.ErrDataNotFound {
				return model.Zone{}, repository.ErrNotFound
			}
			return model.Zone{}, err
		}

		return zone, nil
	}
}

func (r *ZoneRepository) FindByZoneCode(ctx context.Context, zoneCode string, opts ...repository.Option) (model.Zone, error) {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		resp, err := r.fleetAreaClient.GetZoneByCode(ctx, zoneCode)
		if err != nil {
			return model.Zone{}, err
		}
		return fleetarea.MapZoneModel(resp.Zone)
	} else {
		var zone model.Zone
		err := r.ds.FindOne(ctx, bson.M{"zone_code": zoneCode}, &zone, repository.ToDBOptions(opts)...)
		if err != nil {
			if errors.Is(err, mongodb.ErrDataNotFound) {
				return model.Zone{}, repository.ErrNotFound
			}
			return model.Zone{}, err
		}

		return zone, nil
	}
}

func (r *ZoneRepository) UpdateById(ctx context.Context, id string, req model.UpdateZoneReq) error {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		resp, err := r.fleetAreaClient.GetZoneByID(ctx, id)
		if err != nil {
			return err
		}

		currentZone, err := fleetarea.MapZoneModel(resp.Zone)
		if err != nil {
			return err
		}
		currentZone.DisplayName = req.DisplayName
		currentZone.Active = req.Active
		currentZone.Geometry = req.Geometry

		_, err = r.fleetAreaClient.UpdateZone(ctx, id, fleetarea.MapZoneRequest(currentZone))
		return err
	} else {
		objectID, err := primitive.ObjectIDFromHex(id)
		if err != nil {
			return errors.New("invalid id")
		}

		selector := bson.M{
			"_id": objectID,
		}

		update := req.BSON()

		_, err = r.ds.FindOneAndUpdate(ctx, selector, update)
		if err != nil {
			if err == mongodb.ErrDataNotFound {
				return repository.ErrNotFound
			}
			return err
		}

		return nil
	}
}

func (r *ZoneRepository) FindZoneCodesByLocation(ctx context.Context, lat float64, lng float64, opts ...repository.Option) ([]string, error) {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		zones, err := r.fleetAreaClient.FindZoneByLocation(ctx, fleetarea.MapFindZoneByLocationRequest(nil, lat, lng))
		if err != nil {
			return nil, err
		}

		result := make([]string, 0, len(zones.Zones))
		for _, zone := range zones.Zones {
			result = append(result, zone.ZoneCode)
		}
		return result, nil
	} else {
		var zones []model.Zone
		q := geometryHasPointQuery(lat, lng)
		if err := r.ds.FindWithSelector(ctx, q, bson.M{"zone_code": 1}, &zones, repository.ToDBOptions(opts)...); err != nil {
			return nil, err
		}

		result := make([]string, 0, len(zones))
		for _, zone := range zones {
			result = append(result, zone.ZoneCode)
		}
		return result, nil
	}
}

func (r *ZoneRepository) FindActiveZoneCodesByLocation(ctx context.Context, lat float64, lng float64, opts ...repository.Option) ([]string, error) {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		zones, err := r.fleetAreaClient.FindZoneByLocation(ctx, fleetarea.MapFindZoneByLocationRequest(types.Ptr(true), lat, lng))
		if err != nil {
			return nil, err
		}

		result := make([]string, 0, len(zones.Zones))
		for _, zone := range zones.Zones {
			result = append(result, zone.ZoneCode)
		}
		return result, nil
	} else {
		var zones []model.Zone
		q := geometryHasPointQuery(lat, lng)
		q["active"] = true
		if err := r.ds.FindWithSelector(ctx, q, bson.M{"zone_code": 1}, &zones, repository.ToDBOptions(opts)...); err != nil {
			return nil, err
		}

		result := make([]string, 0, len(zones))
		for _, zone := range zones {
			result = append(result, zone.ZoneCode)
		}
		return result, nil
	}
}

func (r *ZoneRepository) FindZoneIDsByLocation(ctx context.Context, lat float64, lng float64, opts ...repository.Option) ([]primitive.ObjectID, error) {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		zones, err := r.fleetAreaClient.FindZoneByLocation(ctx, fleetarea.MapFindZoneByLocationRequest(nil, lat, lng))
		if err != nil {
			return nil, err
		}

		result := make([]primitive.ObjectID, 0, len(zones.Zones))
		for _, zone := range zones.Zones {
			objectID, err := primitive.ObjectIDFromHex(zone.Id)
			if err != nil {
				return nil, errors.New("invalid id")
			}
			result = append(result, objectID)
		}
		return result, nil
	} else {
		var zones []model.Zone
		q := geometryHasPointQuery(lat, lng)
		if err := r.ds.FindWithSelector(ctx, q, bson.M{"_id": 1}, &zones, repository.ToDBOptions(opts)...); err != nil {
			return nil, err
		}

		result := make([]primitive.ObjectID, 0, len(zones))
		for _, zone := range zones {
			result = append(result, zone.ID)
		}
		return result, nil
	}
}

func (r *ZoneRepository) FindZonesIntersectPolygon(ctx context.Context, polygon *geom.Polygon, opts ...repository.Option) ([]model.ZoneWithoutGeometry, error) {
	if r.featureFlagSvc.IsEnabledWithDefaultFalse(ctx, FleetAreaZoneEnabled.Name) {
		zones, err := r.fleetAreaClient.FindZonesIntersectPolygon(ctx, fleetarea.MapFindZonesIntersectPolygonRequest(polygon))
		if err != nil {
			return nil, err
		}

		return fleetarea.MapZoneWithoutGeometryModelList(zones.Zones)
	} else {
		var result []model.ZoneWithoutGeometry
		q := bson.M{
			"geometry": bson.M{
				"$geoIntersects": bson.M{
					"$geometry": bson.M{
						"type":        "Polygon",
						"coordinates": polygon.Coords(),
					},
				},
			},
		}
		if err := r.ds.Find(ctx, q, 0, 0, &result, repository.ToDBOptions(opts)...); err != nil {
			return nil, err
		}
		return result, nil
	}
}

type ZoneDataStore mongodb.DataStoreInterface

func ProvideZoneDataStore(conn *mongodb.Conn) ZoneDataStore {
	return mongodb.NewDataStoreWithConn(conn, "zones")
}

func NewZoneRepository(datastore ZoneDataStore, featureFlagSvc featureflag.Service, fleetAreaClient fleetarea.FleetAreaClient) *ZoneRepository {
	return &ZoneRepository{
		ds:              datastore,
		featureFlagSvc:  featureFlagSvc,
		fleetAreaClient: fleetAreaClient,
	}
}

func ProvideZoneRepository(datastore ZoneDataStore, meter metric.Meter, featureFlagSvc featureflag.Service, fleetAreaClient fleetarea.FleetAreaClient) *repository.ProxyZoneRepository {
	return repository.NewLatencyProxyZoneRepository(
		NewZoneRepository(datastore, featureFlagSvc, fleetAreaClient),
		meter,
	)
}
