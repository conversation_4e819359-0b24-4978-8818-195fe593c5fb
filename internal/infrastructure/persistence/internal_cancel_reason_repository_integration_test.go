//go:build integration_test
// +build integration_test

package persistence_test

import (
	"context"
	"encoding/json"
	"fmt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"strings"
	"testing"
	"time"
)

func TestInternalCancelReasonRepositoryIntegration(t *testing.T) {
	suite.Run(t, new(InternalCancelReasonRepositoryIntegrationTest))
}

type InternalCancelReasonRepositoryIntegrationTest struct {
	suite.Suite

	ctx context.Context
	ctn *ittest.IntegrationTestContainer
}

func (s *InternalCancelReasonRepositoryIntegrationTest) SetupSuite() {
	s.ctx = context.Background()
	s.ctn = ittest.NewContainer(s.T())
}

func (s *InternalCancelReasonRepositoryIntegrationTest) SetupSubTest() {
	err := s.ctn.DBConnectionForTest.Database().Collection("internal_cancel_reasons").Drop(s.ctx)
	s.NoError(err)

	_, err = s.ctn.Redis.FlushAll(s.ctx).Result()
	s.NoError(err)
}

func (s *InternalCancelReasonRepositoryIntegrationTest) TearDownSubTest() {
	err := s.ctn.DBConnectionForTest.Database().Collection("internal_cancel_reasons").Drop(s.ctx)
	s.NoError(err)

	_, err = s.ctn.Redis.FlushAll(s.ctx).Result()
	s.NoError(err)
}

func (s *InternalCancelReasonRepositoryIntegrationTest) InsertCollection(ctx context.Context, data []model.InternalCancelReason) {
	dataInterface := make([]interface{}, 0, len(data))
	for _, dataToBeConverted := range data {
		dataInterface = append(dataInterface, dataToBeConverted)
	}

	err := s.ctn.InternalCancelReasonDataStore.InsertMany(ctx, dataInterface)
	s.NoError(err)
}

func (s *InternalCancelReasonRepositoryIntegrationTest) checkAllInternalCancelReason(ctx context.Context, internalCancelReasonIDs ...model.InternalCancelReasonId) []string {
	// The cache should be set.
	allInternalCancelReasonCacheKeys := s.ctn.Redis.Get(ctx, persistence.AllInternalCancelReasonCacheKey).Val()

	internalCancelReasonIDString := make([]string, 0, len(internalCancelReasonIDs))
	for _, internalCancelReasonID := range internalCancelReasonIDs {
		internalCancelReasonIDString = append(internalCancelReasonIDString, internalCancelReasonID.String())
	}

	expected := strings.Join(internalCancelReasonIDString, ",")
	s.Equal(expected, allInternalCancelReasonCacheKeys)

	return internalCancelReasonIDString
}

func (s *InternalCancelReasonRepositoryIntegrationTest) checkEachInternalCancelReasonKey(ctx context.Context, internalCancelReasonKeyToCheck []string, expectedData []model.InternalCancelReason) {
	cacheResults := make([]model.InternalCancelReason, 0, len(internalCancelReasonKeyToCheck))
	for _, allInternalCancelReasonCacheKey := range internalCancelReasonKeyToCheck {
		result, err := s.ctn.Redis.Get(ctx, fmt.Sprintf(persistence.InternalCancelReasonCacheKey, allInternalCancelReasonCacheKey)).Result()
		s.NoError(err)
		var unmarshalledResult model.InternalCancelReason

		err = json.Unmarshal([]byte(result), &unmarshalledResult)
		s.NoError(err)

		cacheResults = append(cacheResults, unmarshalledResult)
	}

	for index, cacheResult := range cacheResults {
		s.checkEqualInternalCancelReason(expectedData[index], cacheResult)
	}
}

func (s *InternalCancelReasonRepositoryIntegrationTest) checkEqualInternalCancelReason(expected, actual model.InternalCancelReason) {
	s.Equal(expected.ID, actual.ID)
	s.Equal(expected.Name, actual.Name)
	s.Equal(expected.Label, actual.Label)
	s.Equal(expected.CancellationRateFree, actual.CancellationRateFree)
	s.Equal(expected.CancellationSource, actual.CancellationSource)
	s.Equal(expected.ShouldAutoClaim, actual.ShouldAutoClaim)
	s.Equal(expected.Message, actual.Message)
	s.Equal(expected.BanDurationInMinute, actual.BanDurationInMinute)
	s.Equal(expected.IsReassign, actual.IsReassign)
	s.Equal(expected.CompensationReason, actual.CompensationReason)
}

func (s *InternalCancelReasonRepositoryIntegrationTest) setInternalCancelReasonToCache(ctx context.Context, internalCancelReasons []model.InternalCancelReason) {
	ids := make([]string, 0, len(internalCancelReasons))
	for _, internalCancelReason := range internalCancelReasons {
		data, err := json.Marshal(internalCancelReason)
		s.NoError(err)

		_, err = s.ctn.Redis.Set(ctx, fmt.Sprintf(persistence.InternalCancelReasonCacheKey, internalCancelReason.ID.String()), data, 24*time.Hour).Result()
		s.NoError(err)

		ids = append(ids, internalCancelReason.ID.String())
	}

	_, err := s.ctn.Redis.Set(ctx, persistence.AllInternalCancelReasonCacheKey, strings.Join(ids, ","), 24*time.Hour).Result()
	s.NoError(err)
}

func (s *InternalCancelReasonRepositoryIntegrationTest) TestFindSorted() {
	s.Run("Success, no existing cache", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}
		s.InsertCollection(s.ctx, mockedData)

		// Should not no cache.
		err := s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)

		// Call FindAndSorted
		results, err := s.ctn.InternalCancelReasonRepository.FindSorted(s.ctx, 0, 0)
		s.NoError(err)
		s.Len(results, 2)

		// Check AllInternalCancelReason should contain all the search key
		allInternalCancelReasons := s.checkAllInternalCancelReason(s.ctx, internalCancelReasonIDs...)
		s.checkEachInternalCancelReasonKey(s.ctx, allInternalCancelReasons, mockedData)
	})
	s.Run("Success, with existing cache", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}

		// Intentionally not set the data to database but to cache only.
		// So if the code continue to query from database, The result would be wrong.
		s.setInternalCancelReasonToCache(s.ctx, mockedData)

		// Call FindAndSorted
		results, err := s.ctn.InternalCancelReasonRepository.FindSorted(s.ctx, 0, 0)
		s.NoError(err)
		s.Len(results, 2)
	})
	s.Run("Success, should not set AllInternalCancelReason when pagination is requested", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}
		s.InsertCollection(s.ctx, mockedData)

		// Should not no cache.
		err := s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)

		// Call FindAndSorted
		results, err := s.ctn.InternalCancelReasonRepository.FindSorted(s.ctx, 0, 5)
		s.NoError(err)
		s.Len(results, 2)

		// Should not set allInternalCancelReason
		allInternalCancelReasons := s.checkAllInternalCancelReason(s.ctx)
		s.Empty(allInternalCancelReasons)

		// But should still set the rest of key
		s.checkEachInternalCancelReasonKey(s.ctx, []string{internalCancelReasonIDs[0].String(), internalCancelReasonIDs[1].String()}, mockedData)
	})
}

func (s *InternalCancelReasonRepositoryIntegrationTest) TestCreate() {
	s.Run("Success, should invalidate AllInternalCancelReason", func() {
		// Set the AllInternalCancelReason to check if it is unset after calling Create
		err := s.ctn.Redis.Set(s.ctx, persistence.AllInternalCancelReasonCacheKey, "A,B,C", 24*time.Hour).Err()
		s.NoError(err)

		mockedData := []model.InternalCancelReason{
			{
				ID:    model.GenerateInternalCancelReasonId(),
				Name:  "Test",
				Label: "Label",
			},
		}
		err = s.ctn.InternalCancelReasonRepository.Create(s.ctx, &mockedData[0])
		s.NoError(err)

		// This cache should be unset
		err = s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)

		s.checkEachInternalCancelReasonKey(s.ctx, []string{mockedData[0].ID.String()}, mockedData)
	})
}

func (s *InternalCancelReasonRepositoryIntegrationTest) TestUpdate() {
	s.Run("Success, should invalidate AllInternalCancelReason", func() {
		// Set the AllInternalCancelReason to check if it is unset after calling Create
		err := s.ctn.Redis.Set(s.ctx, persistence.AllInternalCancelReasonCacheKey, "A,B,C", 24*time.Hour).Err()
		s.NoError(err)

		mockedData := []model.InternalCancelReason{
			{
				ID:    model.GenerateInternalCancelReasonId(),
				Name:  "Test",
				Label: "Label",
			},
		}

		err = s.ctn.InternalCancelReasonDataStore.Insert(s.ctx, mockedData[0])
		s.NoError(err)

		mockedData[0].Name = "TestB"
		err = s.ctn.InternalCancelReasonRepository.Update(s.ctx, &mockedData[0])
		s.NoError(err)

		// This cache should be unset
		err = s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)

		s.checkEachInternalCancelReasonKey(s.ctx, []string{mockedData[0].ID.String()}, mockedData)
	})
}

func (s *InternalCancelReasonRepositoryIntegrationTest) TestFindByID() {
	s.Run("Success, Should hit database when no cache found", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}
		s.InsertCollection(s.ctx, mockedData)

		// Call FindAndSorted
		result, err := s.ctn.InternalCancelReasonRepository.FindById(s.ctx, internalCancelReasonIDs[0].String())
		s.NoError(err)
		s.NotNil(result)
		s.checkEqualInternalCancelReason(mockedData[0], *result)

		// Should set cache for the key
		s.checkEachInternalCancelReasonKey(s.ctx, []string{internalCancelReasonIDs[0].String()}, mockedData[:1])

		// Should not set AllInternalCancelReason cache
		err = s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)
	})
	s.Run("Success, Should return the cache if it already found", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}

		// We intentionally not set the data in database to make sure that the data is actually come from cache.

		// Insert to cache
		mockedDataMarshaled, err := json.Marshal(mockedData[1])
		s.NoError(err)
		_, err = s.ctn.Redis.Set(s.ctx, fmt.Sprintf(persistence.InternalCancelReasonCacheKey, internalCancelReasonIDs[1].String()), mockedDataMarshaled, 24*time.Hour).Result()
		s.NoError(err)

		// Call FindAndSorted
		result, err := s.ctn.InternalCancelReasonRepository.FindById(s.ctx, internalCancelReasonIDs[1].String())
		s.NoError(err)
		s.NotNil(result)
		s.checkEqualInternalCancelReason(mockedData[1], *result)

		// Should set cache for the key
		s.checkEachInternalCancelReasonKey(s.ctx, []string{internalCancelReasonIDs[1].String()}, mockedData[1:])

		// Should not set AllInternalCancelReason cache
		err = s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)

	})
}

func (s *InternalCancelReasonRepositoryIntegrationTest) TestFindByIDs() {
	s.Run("Success, Should hit database when no cache found", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}
		s.InsertCollection(s.ctx, mockedData)

		// Call FindAndSorted
		result, err := s.ctn.InternalCancelReasonRepository.FindByIDs(s.ctx, types.NewStringSet(internalCancelReasonIDs[0].String(), internalCancelReasonIDs[1].String()))
		s.NoError(err)
		s.NotNil(result)

		// Should set cache for the key
		s.checkEachInternalCancelReasonKey(s.ctx, []string{internalCancelReasonIDs[0].String(), internalCancelReasonIDs[1].String()}, mockedData)

		// Should not set AllInternalCancelReason cache
		err = s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)
	})
	s.Run("Success, Should return the cache if it already found", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}

		// We intentionally not set the data in database to make sure that the data is actually come from cache.

		// Insert to cache
		for _, eachMockedData := range mockedData {
			mockedDataMarshaled, err := json.Marshal(eachMockedData)
			s.NoError(err)
			_, err = s.ctn.Redis.Set(s.ctx, fmt.Sprintf(persistence.InternalCancelReasonCacheKey, eachMockedData.ID.String()), mockedDataMarshaled, 24*time.Hour).Result()
			s.NoError(err)
		}

		// Call FindAndSorted
		result, err := s.ctn.InternalCancelReasonRepository.FindByIDs(s.ctx, types.NewStringSet(internalCancelReasonIDs[0].String(), internalCancelReasonIDs[1].String()))
		s.NoError(err)
		s.NotNil(result)

		// Should set cache for the key
		s.checkEachInternalCancelReasonKey(s.ctx, []string{internalCancelReasonIDs[0].String(), internalCancelReasonIDs[1].String()}, mockedData)

		// Should not set AllInternalCancelReason cache
		err = s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)

	})
	s.Run("Success, Should hit database when partially hit cache", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}
		s.InsertCollection(s.ctx, mockedData)

		mockedDataMarshaled, err := json.Marshal(mockedData[0])
		s.NoError(err)
		_, err = s.ctn.Redis.Set(s.ctx, fmt.Sprintf(persistence.InternalCancelReasonCacheKey, mockedData[0].ID.String()), mockedDataMarshaled, 24*time.Hour).Result()
		s.NoError(err)

		// Call FindByIDs
		result, err := s.ctn.InternalCancelReasonRepository.FindByIDs(s.ctx, types.NewStringSet(internalCancelReasonIDs[0].String(), internalCancelReasonIDs[1].String()))
		s.NoError(err)
		s.NotNil(result)

		// Should set cache for the every key searched this time
		s.checkEachInternalCancelReasonKey(s.ctx, []string{internalCancelReasonIDs[0].String(), internalCancelReasonIDs[1].String()}, mockedData)

		// Should not set AllInternalCancelReason cache
		err = s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)
	})
}

func (s *InternalCancelReasonRepositoryIntegrationTest) TestDelete() {
	s.Run("Success, Invalidate all internal cancel reason and the cache with that internal cancel reason id", func() {
		internalCancelReasonIDs := []model.InternalCancelReasonId{
			model.GenerateInternalCancelReasonId(),
			model.GenerateInternalCancelReasonId(),
		}

		mockedData := []model.InternalCancelReason{
			{
				ID:    internalCancelReasonIDs[0],
				Name:  "A",
				Label: "A-Label",
			},
			{
				ID:    internalCancelReasonIDs[1],
				Name:  "B",
				Label: "B-Label",
			},
		}
		s.InsertCollection(s.ctx, mockedData)

		mockedDataMarshaled, err := json.Marshal(mockedData[0])
		s.NoError(err)
		_, err = s.ctn.Redis.Set(s.ctx, fmt.Sprintf(persistence.InternalCancelReasonCacheKey, mockedData[0].ID.String()), mockedDataMarshaled, 24*time.Hour).Result()
		s.NoError(err)

		_, err = s.ctn.Redis.Set(s.ctx, persistence.AllInternalCancelReasonCacheKey, "A,B,C", 24*time.Hour).Result()
		s.NoError(err)

		// Call Delete
		err = s.ctn.InternalCancelReasonRepository.Delete(s.ctx, internalCancelReasonIDs[0].String())
		s.NoError(err)

		// Should invalidate AllInternalCancelReason cache
		err = s.ctn.Redis.Get(s.ctx, persistence.AllInternalCancelReasonCacheKey).Err()
		s.ErrorIs(err, redis.Nil)

		// Should drop the cache for the deleted Internal Cancel Reason
		err = s.ctn.Redis.Get(s.ctx, fmt.Sprintf(persistence.InternalCancelReasonCacheKey, mockedData[0].ID.String())).Err()
		s.ErrorIs(err, redis.Nil)
	})
}
