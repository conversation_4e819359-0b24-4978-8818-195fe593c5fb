package persistence

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type MongoTermAndConditionRepository struct {
	datastore TermAndConditionDataStore
}

func (repo *MongoTermAndConditionRepository) Create(ctx context.Context, termcondition model.TermAndCondition) error {
	return repo.datastore.Insert(ctx, termcondition)
}

func (repo *MongoTermAndConditionRepository) FindLatestByDriverID(ctx context.Context, driverID string, opts ...repository.Option) (*model.TermAndCondition, error) {
	var termConditions []*model.TermAndCondition

	err := repo.datastore.FindAndSort(ctx, bson.M{
		"driver_id": driverID,
	}, 0, 1, []string{"-created_at"}, &termConditions, repository.ToDBOptions(opts)...)

	if err != nil {
		return nil, err
	}
	if len(termConditions) == 0 {
		return nil, repository.ErrNotFound
	}
	return termConditions[0], nil

}

func NewMongoTermAndConditionRepository(ds TermAndConditionDataStore) *MongoTermAndConditionRepository {
	return &MongoTermAndConditionRepository{
		datastore: ds,
	}
}

func ProvideMongoTermAndConditionRepository(ds TermAndConditionDataStore, meter metric.Meter) *repository.ProxyTermAndConditionRepository {
	return repository.NewLatencyProxyTermAndConditionRepository(NewMongoTermAndConditionRepository(ds), meter)
}

type TermAndConditionDataStore mongodb.DataStoreInterface

func ProvideTermAndConditionDataStore(conn *mongodb.Conn) TermAndConditionDataStore {
	return mongodb.NewDataStoreWithConn(conn, "term_and_conditions")
}
