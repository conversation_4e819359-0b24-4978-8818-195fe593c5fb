package persistence

import (
	"context"
	"encoding/json"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/mock_mongodb"
	"git.wndv.co/lineman/absinthe/timeutil"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore/mock_redis"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
	xgotimeutil "git.wndv.co/lineman/xgo/timeutil"
)

func TestPdpaRepo_Upsert(t *testing.T) {
	t.Run("no document must insert to mongodb and set to redis", func(tt *testing.T) {
		repo, redisClient, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		now := timeutil.Now().UTC()
		mockPDPA := model.NewPdpa(driverID, nil)
		mockPDPA.CreatedAt = now
		mockPDPA.UpdatedAt = now
		jsonPDPA, _ := json.Marshal(mockPDPA)
		ctxWithTime := xgotimeutil.WithTime(context.Background(), now)

		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(mongodb.ErrDataNotFound)
		ds.EXPECT().Insert(gomock.Any(), mockPDPA).Return(nil)
		redisClient.EXPECT().Set(gomock.Any(), cache.PdpaKey(driverID), string(jsonPDPA), 3*time.Hour).Return(redis.NewStatusResult("1", nil))

		err := repo.Upsert(ctxWithTime, mockPDPA)
		require.NoError(t, err)
	})

	t.Run("with document must push new pdpa accept and set to redis", func(tt *testing.T) {
		repo, redisClient, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		now := timeutil.Now().UTC()
		existedPdpaItem := model.PdpaItem{PdpaId: "1.0", IsAccepted: true}
		newPdpaItem := model.PdpaItem{PdpaId: "2.0", IsAccepted: true}
		existingPDPA := model.NewPdpa(driverID, []model.PdpaItem{existedPdpaItem})
		ctxWithTime := xgotimeutil.WithTime(context.Background(), now)
		newPDPA := model.NewPdpa(driverID, []model.PdpaItem{existedPdpaItem, newPdpaItem})
		newPDPA.UpdatedAt = now
		jsonPDPA, _ := json.Marshal(newPDPA)

		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).SetArg(2, *existingPDPA)
		ds.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		redisClient.EXPECT().Set(gomock.Any(), cache.PdpaKey(driverID), string(jsonPDPA), 3*time.Hour).Return(redis.NewStatusResult("1", nil))

		err := repo.Upsert(ctxWithTime, model.NewPdpa(driverID, []model.PdpaItem{newPdpaItem}))
		require.NoError(t, err)
	})

	t.Run("insert mongodb error must return error", func(tt *testing.T) {
		repo, _, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		now := timeutil.Now().UTC()
		existedPdpaItem := model.PdpaItem{PdpaId: "1.0", IsAccepted: true}
		newPdpaItem := model.PdpaItem{PdpaId: "2.0", IsAccepted: true}
		ctxWithTime := xgotimeutil.WithTime(context.Background(), now)
		newPDPA := model.NewPdpa(driverID, []model.PdpaItem{existedPdpaItem, newPdpaItem})
		newPDPA.UpdatedAt = now

		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(mongodb.ErrDataNotFound)
		ds.EXPECT().Insert(gomock.Any(), gomock.Any()).Return(errors.New("mock error"))

		err := repo.Upsert(ctxWithTime, model.NewPdpa(driverID, []model.PdpaItem{newPdpaItem}))
		require.Error(t, err)
	})

	t.Run("insert cache error must not return error", func(tt *testing.T) {
		repo, redisClient, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		now := timeutil.Now().UTC()
		mockPDPA := model.NewPdpa(driverID, nil)
		mockPDPA.CreatedAt = now
		mockPDPA.UpdatedAt = now
		jsonPDPA, _ := json.Marshal(mockPDPA)
		ctxWithTime := xgotimeutil.WithTime(context.Background(), now)

		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(mongodb.ErrDataNotFound)
		ds.EXPECT().Insert(gomock.Any(), mockPDPA).Return(nil)
		redisClient.EXPECT().Set(gomock.Any(), cache.PdpaKey(driverID), string(jsonPDPA), 3*time.Hour).Return(redis.NewStatusResult("0", errors.New("mock error")))

		err := repo.Upsert(ctxWithTime, mockPDPA)
		require.NoError(t, err)
	})

	t.Run("update mongodb error must return error", func(tt *testing.T) {
		repo, _, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		now := timeutil.Now().UTC()
		existedPdpaItem := model.PdpaItem{PdpaId: "1.0", IsAccepted: true}
		newPdpaItem := model.PdpaItem{PdpaId: "2.0", IsAccepted: true}
		existingPDPA := model.NewPdpa(driverID, []model.PdpaItem{existedPdpaItem})
		ctxWithTime := xgotimeutil.WithTime(context.Background(), now)
		newPDPA := model.NewPdpa(driverID, []model.PdpaItem{existedPdpaItem, newPdpaItem})
		newPDPA.UpdatedAt = now

		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).SetArg(2, *existingPDPA)
		ds.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("mock error"))

		err := repo.Upsert(ctxWithTime, model.NewPdpa(driverID, []model.PdpaItem{newPdpaItem}))
		require.Error(t, err)
	})

	t.Run("update cache error must return error", func(tt *testing.T) {
		repo, redisClient, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		now := timeutil.Now().UTC()
		existedPdpaItem := model.PdpaItem{PdpaId: "1.0", IsAccepted: true}
		newPdpaItem := model.PdpaItem{PdpaId: "2.0", IsAccepted: true}
		existingPDPA := model.NewPdpa(driverID, []model.PdpaItem{existedPdpaItem})
		ctxWithTime := xgotimeutil.WithTime(context.Background(), now)
		newPDPA := model.NewPdpa(driverID, []model.PdpaItem{existedPdpaItem, newPdpaItem})
		newPDPA.UpdatedAt = now
		jsonPDPA, _ := json.Marshal(newPDPA)

		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).SetArg(2, *existingPDPA)
		ds.EXPECT().Update(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		redisClient.EXPECT().Set(gomock.Any(), cache.PdpaKey(driverID), string(jsonPDPA), 3*time.Hour).Return(redis.NewStatusResult("0", errors.New("mock error")))

		err := repo.Upsert(ctxWithTime, model.NewPdpa(driverID, []model.PdpaItem{newPdpaItem}))
		require.NoError(t, err)
	})
}

func TestPdpaRepo_FindById(t *testing.T) {
	t.Run("no cache must query from mongodb and save to cache", func(t *testing.T) {
		repo, redisClient, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		mockPDPA := model.NewPdpa(driverID, nil)
		jsonPDPA, _ := json.Marshal(mockPDPA)

		redisClient.EXPECT().Get(gomock.Any(), cache.PdpaKey(driverID)).Return(redis.NewStringResult("", nil))
		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).SetArg(2, *mockPDPA)
		redisClient.EXPECT().Set(gomock.Any(), cache.PdpaKey(driverID), string(jsonPDPA), 3*time.Hour).Return(redis.NewStatusResult("1", nil))

		actualPDPA, err := repo.FindByDriverId(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, mockPDPA, actualPDPA)
	})

	t.Run("with cache must query from redis instead", func(t *testing.T) {
		repo, redisClient, _, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		mockPDPA := model.NewPdpa(driverID, nil)
		jsonPDPA, _ := json.Marshal(mockPDPA)

		redisClient.EXPECT().Get(gomock.Any(), cache.PdpaKey(driverID)).Return(redis.NewStringResult(string(jsonPDPA), nil))
		actualPDPA, err := repo.FindByDriverId(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, mockPDPA, actualPDPA)
	})

	t.Run("error query from mongo must return error", func(t *testing.T) {
		repo, redisClient, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"

		redisClient.EXPECT().Get(gomock.Any(), cache.PdpaKey(driverID)).Return(redis.NewStringResult("", nil))
		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("mock error"))

		_, err := repo.FindByDriverId(context.Background(), driverID)
		require.Error(t, err)
	})

	t.Run("save to cache error must not return error", func(tt *testing.T) {
		repo, redisClient, ds, cleanup := newPdpaRepositoryTest(t)
		defer cleanup()
		driverID := "test-1"
		mockPDPA := model.NewPdpa(driverID, nil)
		jsonPDPA, _ := json.Marshal(mockPDPA)

		redisClient.EXPECT().Get(gomock.Any(), cache.PdpaKey(driverID)).Return(redis.NewStringResult("", nil))
		ds.EXPECT().FindOne(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).SetArg(2, *mockPDPA)
		redisClient.EXPECT().Set(gomock.Any(), cache.PdpaKey(driverID), string(jsonPDPA), 3*time.Hour).Return(redis.NewStatusResult("0", errors.New("mock error")))

		actualPDPA, err := repo.FindByDriverId(context.Background(), driverID)
		require.NoError(t, err)
		require.Equal(t, mockPDPA, actualPDPA)
	})
}

func newPdpaRepositoryTest(t gomock.TestReporter) (repository.PdpaRepository, *mock_redis.MockUniversalClient, *mock_mongodb.MockDataStoreInterface, func()) {
	ctrl := gomock.NewController(t)
	cache := mock_redis.NewMockUniversalClient(ctrl)
	meter := testmetric.NewStubMeter()
	pdpads := mock_mongodb.NewMockDataStoreInterface(ctrl)
	return ProvidePdpaRepository(pdpads, meter, cache), cache, pdpads, func() {
		ctrl.Finish()
	}
}
