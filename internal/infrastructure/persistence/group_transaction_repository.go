package persistence

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type MongoGroupTransactionQuery struct {
	statuses        []model.GroupTransactionStatus
	from            time.Time
	to              time.Time
	transactionType string
	start           time.Time
	end             time.Time
	by              string
}

func BuildGroupTransactionQuery() repository.GroupTransactionQuery {
	return &MongoGroupTransactionQuery{}
}

func (q *MongoGroupTransactionQuery) WithStatus(status []model.GroupTransactionStatus) repository.GroupTransactionQuery {
	q.statuses = status
	return q
}

func (q *MongoGroupTransactionQuery) WithFrom(from time.Time) repository.GroupTransactionQuery {
	q.from = from
	return q
}

func (q *MongoGroupTransactionQuery) WithTo(to time.Time) repository.GroupTransactionQuery {
	q.to = to
	return q
}

func (q *MongoGroupTransactionQuery) WithTransactionType(t string) repository.GroupTransactionQuery {
	q.transactionType = t
	return q
}

func (q *MongoGroupTransactionQuery) WithTimeRangeExecute(start time.Time, end time.Time) repository.GroupTransactionQuery {
	q.start = start
	q.end = end
	return q
}

func (q *MongoGroupTransactionQuery) WithRequestedBy(requestedBy string) repository.GroupTransactionQuery {
	q.by = requestedBy
	return q
}

func (q *MongoGroupTransactionQuery) Query() bson.M {
	query := bson.M{}

	if q.statuses != nil {
		query["status"] = bson.M{"$in": q.statuses}
	}

	if q.transactionType == string(model.WalletTransactionCategory) {
		// for backward compat
		query["group_transaction_category"] = bson.M{"$ne": model.CreditTransactionCategory}
	} else if q.transactionType != "" {
		query["group_transaction_category"] = q.transactionType
	}

	if !q.from.IsZero() || !q.to.IsZero() {
		createAtQuery := bson.M{}
		if !q.from.IsZero() {
			createAtQuery["$gt"] = q.from
		}
		if !q.to.IsZero() {
			createAtQuery["$lt"] = q.to
		}

		query["created_at"] = createAtQuery
	}

	if !q.start.IsZero() && !q.end.IsZero() {
		query["$and"] = []bson.M{
			{"effective_time": bson.M{"$gt": q.start}},
			{"effective_time": bson.M{"$lte": q.end}},
		}
	}

	if q.by != "" {
		query["requested_by"] = q.by
	}

	return query
}

type MongoGroupTransactionRepository struct {
	datastore GroupTransactionDataStore
}

func ProvideMongoGroupTransactionRepository(ds GroupTransactionDataStore, meter metric.Meter) *repository.ProxyGroupTransactionRepository {
	return repository.NewLatencyProxyGroupTransactionRepository(&MongoGroupTransactionRepository{datastore: ds}, meter)
}

func (r *MongoGroupTransactionRepository) Create(ctx context.Context, entity *model.GroupTransaction) error {
	return r.datastore.Insert(ctx, entity)
}

func (r *MongoGroupTransactionRepository) CreateMany(ctx context.Context, entities []model.GroupTransaction) error {
	data := make([]interface{}, len(entities))
	for i, entity := range entities {
		data[i] = entity
	}
	return r.datastore.InsertMany(ctx, data)
}

func (r *MongoGroupTransactionRepository) Get(ctx context.Context, id string, opts ...repository.Option) (*model.GroupTransaction, error) {
	var gt model.GroupTransaction
	if err := r.datastore.FindOne(ctx, bson.M{"group_transaction_id": id}, &gt, repository.ToDBOptions(opts)...); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}

	return &gt, nil
}

func (r *MongoGroupTransactionRepository) Find(ctx context.Context, query repository.GroupTransactionQuery, skip, limit int) ([]model.GroupTransaction, error) {
	mq, ok := query.(*MongoGroupTransactionQuery)
	if !ok {
		return nil, errors.New("query must be MongoGroupTransactionQuery")
	}

	q := mq.Query()
	result := make([]model.GroupTransaction, 0, 20)
	if err := r.datastore.FindAndSort(ctx, q, skip, limit, []string{"-created_at"}, &result); err != nil {
		return nil, err
	}

	return result, nil
}

func (r *MongoGroupTransactionRepository) Count(ctx context.Context, query repository.GroupTransactionQuery) (int, error) {
	mq, ok := query.(*MongoGroupTransactionQuery)
	if !ok {
		return -1, errors.New("query must be MongoGroupTransactionQuery")
	}

	q := mq.Query()

	var count int
	if err := r.datastore.Count(ctx, q, &count); err != nil {
		return -1, err
	}

	return count, nil

}

func (r *MongoGroupTransactionRepository) Update(ctx context.Context, entity *model.GroupTransaction) error {
	entity.SetUpdatedAt(time.Now().UTC())

	return r.datastore.Replace(ctx, bson.M{"group_transaction_id": entity.ID()}, entity)
}

func (r *MongoGroupTransactionRepository) Delete(ctx context.Context, groupTxnID string) error {
	return r.datastore.Remove(ctx, bson.M{
		"group_transaction_id": groupTxnID,
	})
}

type GroupTransactionDataStore mongodb.DataStoreInterface

func ProvideGroupTransactionDataStore(conn *mongodb.Conn) GroupTransactionDataStore {
	return mongodb.NewDataStoreWithConn(conn, "group_transactions")
}
