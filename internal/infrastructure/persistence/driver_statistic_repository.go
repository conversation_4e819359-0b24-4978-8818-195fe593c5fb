package persistence

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type MongoDriverStatisticRepository struct {
	datastore DriverStatisticDataStore
}

func ProvideDataStoreDriverStatisticRepository(datastore DriverStatisticDataStore, meter metric.Meter) *repository.ProxyDriverStatisticRepository {
	return repository.NewLatencyProxyDriverStatisticRepository(&MongoDriverStatisticRepository{
		datastore: datastore,
	}, meter)
}

func (r *MongoDriverStatisticRepository) Create(ctx context.Context, entity *model.DriverStatistic) error {
	return r.datastore.Insert(ctx, entity)
}

func (r *MongoDriverStatisticRepository) FindByDriverID(ctx context.Context, driverID string, opts ...repository.Option) (*model.DriverStatistic, error) {
	var result model.DriverStatistic
	if err := r.datastore.FindOne(ctx, bson.M{"driver_id": driverID}, &result); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return &result, nil
}

func (r *MongoDriverStatisticRepository) FindByDriverIDs(ctx context.Context, driverIDs []string) ([]model.DriverStatistic, error) {
	lenOfDriverIDs := len(driverIDs)
	result := make([]model.DriverStatistic, 0, lenOfDriverIDs)
	if err := r.datastore.Find(
		ctx,
		bson.M{
			"driver_id": bson.M{
				"$in": driverIDs,
			},
		},
		0,
		lenOfDriverIDs,
		&result,
	); err != nil {
		return nil, err
	}

	return result, nil
}

func (r *MongoDriverStatisticRepository) Update(ctx context.Context, entity *model.DriverStatistic) error {
	return r.datastore.ReplaceID(ctx, entity.ID, entity)
}

func (r *MongoDriverStatisticRepository) Delete(ctx context.Context, driverID string, opts ...repository.Option) error {
	if err := r.datastore.Remove(ctx, bson.M{"driver_id": driverID}); err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}

	return nil
}

type DriverStatisticDataStore mongodb.DataStoreInterface

func ProvideDriverStatisticDataStore(conn *mongodb.Conn) DriverStatisticDataStore {
	return mongodb.NewDataStoreWithConn(conn, "driver_statistic")
}
