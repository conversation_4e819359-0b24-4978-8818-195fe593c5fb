package persistence

import (
	"context"
	"strconv"
	"time"

	"github.com/redis/go-redis/v9"

	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type UnAcknowledgeReassignRedisClient datastore.RedisClient

const (
	unacknowledgeReassignKey = "unacknowledge_reassign"
)

type UnAcknowledgeReassignRepository struct {
	redisClient UnAcknowledgeReassignRedisClient
}

func (ct *UnAcknowledgeReassignRepository) AddTrip(ctx context.Context, tripID string, t time.Time) error {
	return ct.redisClient.ZAdd(ctx, unacknowledgeReassignKey, redis.Z{
		Score:  float64(t.Unix()),
		Member: tripID,
	}).Err()
}

func (ct *UnAcknowledgeReassignRepository) RemoveTrip(ctx context.Context, tripID string) error {
	return ct.redisClient.ZRem(ctx, unacknowledgeReassignKey, tripID).Err()
}

func (ct *UnAcknowledgeReassignRepository) GetDelayedTrips(ctx context.Context) ([]string, error) {
	now := time.Now().Unix()
	ret, err := ct.redisClient.ZRangeByScore(ctx, unacknowledgeReassignKey, &redis.ZRangeBy{
		Min: strconv.Itoa(int(now) - (30 * 60)), // t-30min lower bound in case of huge backlog (older trips are probably obsolete, safe to delete)
		Max: strconv.Itoa(int(now)),
	}).Result()
	if err != nil {
		return []string{}, err
	}
	if ct.redisClient.ZRemRangeByScore(ctx, unacknowledgeReassignKey, "-inf", strconv.Itoa(int(now))).Err() != nil {
		safe.SentryError(err)
	}
	return ret, nil
}

func ProvideUnAcknowledgeReassignRepository(redisClient datastore.RedisClient, meter metric.Meter) repository.UnAcknowledgeReassignReposity {
	return repository.NewLatencyProxyUnAcknowledgeReassignReposity(&UnAcknowledgeReassignRepository{
		redisClient: redisClient,
	}, meter)
}
