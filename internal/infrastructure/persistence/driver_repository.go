package persistence

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"gopkg.in/guregu/null.v4"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/crypt"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence/setter"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const (
	KeyDriverID              = "driver_id"
	keySupplyPos             = "is_supply_positioning"
	keyUpdatedAt             = "updated_at"
	keyIsDeprioritized       = "is_deprioritized"
	keyServicesDeprioritized = "services_deprioritized"
	driverKeyStatus          = "status"
)

var NoFilterQueryErr = errors.New("empty query is not allowed")

func MongoDriverQuerySelector(field []string) bson.M {
	q := bson.M{}
	for _, v := range field {
		q[v] = 1
	}

	return q
}

type MongoDriverQuery struct {
	driverID                      string
	citizenID                     string
	citizenIDs                    []string
	firstname                     string
	lastname                      string
	phone                         string
	status                        model.DriverStatus
	driverType                    string
	trained                       string
	boxTypes                      []string
	beginAt                       time.Time
	endAt                         time.Time
	region                        string
	regions                       []string
	profileStatus                 model.ProfileStatus
	AssignedReviewer              string
	driverRole                    string
	criminalStatus                model.CriminalStatus
	excludeStatus                 []string
	gteRating                     null.Float
	gteSMARating                  null.Float
	licensePlate                  string
	driverIDs                     []string
	excludeDriverIDs              []string
	gtDriverID                    string
	inTiers                       []string
	IsLINEUserIDExisted           null.Bool
	LINEUserIDRetryCountLowerThan int
}

func BuildDriverQuery() repository.DriverQuery {
	return &MongoDriverQuery{}
}

func (q *MongoDriverQuery) WithInTiers(tiers []string) repository.DriverQuery {
	q.inTiers = tiers
	return q
}

func (q *MongoDriverQuery) WithGTDriverID(driverID string) repository.DriverQuery {
	q.gtDriverID = driverID
	return q
}

func (q *MongoDriverQuery) WithGTERating(rating null.Float) repository.DriverQuery {
	q.gteRating = rating
	return q
}

func (q *MongoDriverQuery) WithGTESMARating(rating null.Float) repository.DriverQuery {
	q.gteSMARating = rating
	return q
}

func (q *MongoDriverQuery) WithDriverID(driverId string) repository.DriverQuery {
	q.driverID = driverId
	return q
}

func (q *MongoDriverQuery) WithCitizenID(citizenID string) repository.DriverQuery {
	q.citizenID = citizenID
	return q
}

func (q *MongoDriverQuery) WithCitizenIDs(citizenIDs []string) repository.DriverQuery {
	q.citizenIDs = citizenIDs
	return q
}

func (q *MongoDriverQuery) WithFirstName(firstname string) repository.DriverQuery {
	q.firstname = firstname
	return q
}

func (q *MongoDriverQuery) WithLastName(lastname string) repository.DriverQuery {
	q.lastname = lastname
	return q
}

func (q *MongoDriverQuery) WithPhone(phone string) repository.DriverQuery {
	q.phone = phone
	return q
}

func (q *MongoDriverQuery) WithStatus(status model.DriverStatus) repository.DriverQuery {
	q.status = status
	return q
}

func (q *MongoDriverQuery) WithDriverType(driverType string) repository.DriverQuery {
	q.driverType = driverType
	return q
}

func (q *MongoDriverQuery) WithTrained(trained string) repository.DriverQuery {
	q.trained = trained
	return q
}

func (q *MongoDriverQuery) WithBoxTypes(boxTypes []string) repository.DriverQuery {
	q.boxTypes = boxTypes
	return q
}

func (q *MongoDriverQuery) WithBeginAt(beginat time.Time) repository.DriverQuery {
	q.beginAt = beginat
	return q
}

func (q *MongoDriverQuery) WithEndAt(endat time.Time) repository.DriverQuery {
	q.endAt = endat
	return q
}

func (q *MongoDriverQuery) WithRegion(region string) repository.DriverQuery {
	q.region = region
	return q
}

func (q *MongoDriverQuery) WithInRegions(regions []string) repository.DriverQuery {
	q.regions = regions
	return q
}

func (q *MongoDriverQuery) WithProfileStatus(profileStatus model.ProfileStatus) repository.DriverQuery {
	q.profileStatus = profileStatus
	return q
}

func (q *MongoDriverQuery) WithAssignedReviewer(assignedReviewer string) repository.DriverQuery {
	q.AssignedReviewer = assignedReviewer
	return q
}

func (q *MongoDriverQuery) WithDriverRole(driverRole string) repository.DriverQuery {
	q.driverRole = driverRole
	return q
}

func (q *MongoDriverQuery) WithCriminalStatus(criminalStatus model.CriminalStatus) repository.DriverQuery {
	q.criminalStatus = criminalStatus
	return q
}

func (q *MongoDriverQuery) WithoutStatus(status []string) repository.DriverQuery {
	q.excludeStatus = status
	return q
}

func (q *MongoDriverQuery) WithLicensePlate(licensePlate string) repository.DriverQuery {
	q.licensePlate = licensePlate
	return q
}

func (q *MongoDriverQuery) WithDriverIDs(drivers []string) repository.DriverQuery {
	q.driverIDs = drivers
	return q
}

func (q *MongoDriverQuery) DriverIds() []string {
	return q.driverIDs
}

func (q *MongoDriverQuery) WithoutDriverIDs(excludeDrivers []string) repository.DriverQuery {
	q.excludeDriverIDs = excludeDrivers
	return q
}

func (q *MongoDriverQuery) WithLINEUserIDExist(isExisted null.Bool) repository.DriverQuery {
	q.IsLINEUserIDExisted = isExisted
	return q
}

func (q *MongoDriverQuery) WithLINEUserIDRetryLower(count int) repository.DriverQuery {
	q.LINEUserIDRetryCountLowerThan = count
	return q
}

func (q *MongoDriverQuery) Query() (bson.M, error) {
	query := bson.M{}

	if q.gteRating.Valid {
		query["rating_score"] = bson.M{"$gte": q.gteRating.Float64}
	}

	if q.gteSMARating.Valid {
		query["sma_rating_score"] = bson.M{"$gte": q.gteSMARating.Float64}
	}

	if q.status != "" {
		query["status"] = q.status
	}

	if q.driverID != "" {
		query["driver_id"] = q.driverID
	}

	if q.citizenID != "" {
		query["citizen_id"] = crypt.EncryptedString(q.citizenID).DeterministicEq()
	}

	if q.citizenIDs != nil {
		cryptedCitizenIDs := make([]crypt.EncryptedString, 0, len(q.citizenIDs))
		for _, citizenID := range q.citizenIDs {
			cryptedCitizenIDs = append(cryptedCitizenIDs, crypt.EncryptedString(citizenID))
		}
		ids := crypt.EncryptedStringArray(cryptedCitizenIDs)

		query["citizen_id"] = bson.M{
			"$in": ids.AllPossibleDeterministicValue(context.Background()),
		}
	}

	if q.firstname != "" {
		query["firstname"] = crypt.EncryptedString(q.firstname).DeterministicEq()
	}

	if q.lastname != "" {
		query["lastname"] = crypt.EncryptedString(q.lastname).DeterministicEq()
	}

	if q.phone != "" {
		query["phone"] = crypt.EncryptedString(q.phone).DeterministicEq()
	}

	if q.licensePlate != "" {
		query["vehicle.plate_number"] = crypt.EncryptedString(q.licensePlate).DeterministicEq()
	}

	if q.driverType != "" {
		query["driver_type"] = crypt.EncryptedString(q.driverType).DeterministicEq()
	}

	if q.trained == "true" || q.trained == "false" {
		query["trained"] = q.trained
	}
	andCondition := []bson.M{}

	if len(q.boxTypes) != 0 {
		query["options.box_type"] = bson.M{
			"$in": q.boxTypes,
		}
	}

	if !q.beginAt.IsZero() || !q.endAt.IsZero() {
		createAtQuery := bson.M{}
		if !q.beginAt.IsZero() {
			createAtQuery["$gt"] = q.beginAt
		}
		if !q.endAt.IsZero() {
			createAtQuery["$lt"] = q.endAt
		}

		query["created_at"] = createAtQuery
	}

	if q.region != "" {
		query["region"] = q.region
	}

	if len(q.regions) != 0 {
		query["region"] = bson.M{
			"$in": q.regions,
		}
	}

	if q.profileStatus != "" {
		query["profile_status"] = q.profileStatus
	}

	if "UNASSIGNED" == q.AssignedReviewer {
		assignedReviewer := bson.M{"$or": []bson.M{
			{"assigned_reviewer": ""},
			{"assigned_reviewer": bson.M{"$exists": false}},
		}}
		andCondition = append(andCondition, assignedReviewer)
	} else if q.AssignedReviewer != "" {
		query["assigned_reviewer"] = q.AssignedReviewer
	}

	if q.driverRole != "" {
		if q.driverRole == string(model.DriverRoleNormal) {
			orDriverRole := bson.M{"$or": []bson.M{
				{"driver_role": model.DriverRoleNormal},
				{"driver_role": ""},
				{"driver_role": bson.M{"$exists": false}},
			}}

			andCondition = append(andCondition, orDriverRole)
		} else {
			query["driver_role"] = q.driverRole
		}
	}

	if q.criminalStatus != "" {
		if q.criminalStatus == model.CriminalStatusPending {
			orCriminalStatus := bson.M{"$or": []bson.M{
				{"criminal_check_status": q.criminalStatus},
				{"criminal_check_status": ""},
				{"criminal_check_status": bson.M{"$exists": false}},
			}}

			andCondition = append(andCondition, orCriminalStatus)
		} else {
			query["criminal_check_status"] = q.criminalStatus
		}
	}

	if q.excludeStatus != nil {
		if _, exists := query[driverKeyStatus]; !exists {
			query[driverKeyStatus] = bson.M{
				"$nin": q.excludeStatus,
			}
		}
	}

	// for incentive whitelist,blacklist id and sort with driver id
	if q.gtDriverID != "" {
		if len(q.driverIDs) > 0 && len(q.excludeDriverIDs) > 0 {
			query["driver_id"] = bson.M{
				"$gt": q.gtDriverID,
				"$and": []bson.M{
					{"driver_id": bson.M{"$in": q.driverIDs}},
					{"driver_id": bson.M{"$nin": q.excludeDriverIDs}},
				},
			}
		} else if len(q.excludeDriverIDs) > 0 {
			query["driver_id"] = bson.M{
				"$gt":  q.gtDriverID,
				"$nin": q.excludeDriverIDs,
			}
		} else if len(q.driverIDs) > 0 {
			query["driver_id"] = bson.M{
				"$gt": q.gtDriverID,
				"$in": q.driverIDs,
			}
		} else {
			query["driver_id"] = bson.M{"$gt": q.gtDriverID}
		}
	} else {
		if len(q.driverIDs) > 0 && len(q.excludeDriverIDs) > 0 {
			query["driver_id"] = bson.M{
				"$gt": q.gtDriverID,
				"$and": []bson.M{
					{"driver_id": bson.M{"$in": q.driverIDs}},
					{"driver_id": bson.M{"$nin": q.excludeDriverIDs}},
				},
			}
		} else if len(q.excludeDriverIDs) > 0 {
			query["driver_id"] = bson.M{
				"$nin": q.excludeDriverIDs,
			}
		} else if len(q.driverIDs) > 0 {
			query["driver_id"] = bson.M{
				"$in": q.driverIDs,
			}
		}
	}

	if len(q.inTiers) != 0 {
		query["driver_tier"] = bson.M{
			"$in": q.inTiers,
		}
	}

	if len(andCondition) > 0 {
		query["$and"] = andCondition
	}

	if !q.IsLINEUserIDExisted.IsZero() {
		if q.IsLINEUserIDExisted.Bool {
			query["line_user_id"] = bson.M{
				"$exists": q.IsLINEUserIDExisted.Bool,
			}
		} else {
			if _, existed := query["$and"]; !existed {
				query["$and"] = []bson.M{}
			}
			srcAnd := query["$and"].([]bson.M)

			srcOr := []bson.M{}
			srcOr = append(srcOr, bson.M{
				"line_user_id": "",
			})
			srcOr = append(srcOr, bson.M{
				"line_user_id": bson.M{
					"$exists": false,
				},
			})

			srcAnd = append(srcAnd, bson.M{
				"$or": srcOr,
			})
			query["$and"] = srcAnd
		}
	}

	if q.LINEUserIDRetryCountLowerThan > 0 {
		if _, existed := query["$and"]; !existed {
			query["$and"] = []bson.M{}
		}
		srcAnd := query["$and"].([]bson.M)

		srcOr := []bson.M{}
		srcOr = append(srcOr, bson.M{
			"line_user_id_retry": bson.M{
				"$lt": q.LINEUserIDRetryCountLowerThan,
			},
		})
		srcOr = append(srcOr, bson.M{
			"line_user_id_retry": bson.M{
				"$exists": false,
			},
		})

		srcAnd = append(srcAnd, bson.M{
			"$or": srcOr,
		})
		query["$and"] = srcAnd
	}

	if len(query) == 0 {
		return nil, NoFilterQueryErr
	}

	return query, nil
}

func (q *MongoDriverQuery) IsEmpty() bool {
	_, err := q.Query()
	return errors.Is(err, NoFilterQueryErr)
}

type MongoDriverUpdator bson.M

func BuildDriverUpdator() *MongoDriverUpdator {
	return &MongoDriverUpdator{}
}

func (u MongoDriverUpdator) set(field string, value any) MongoDriverUpdator {
	if _, existed := u["$set"]; !existed {
		u["$set"] = bson.M{}
	}

	u["$set"].(bson.M)[field] = value
	return u
}

func (u MongoDriverUpdator) inc(field string, value any) MongoDriverUpdator {
	if _, existed := u["$inc"]; !existed {
		u["$inc"] = bson.M{}
	}
	u["$inc"].(bson.M)[field] = value
	return u
}

func (u MongoDriverUpdator) push(field string, value any) MongoDriverUpdator {
	if _, existed := u["$push"]; !existed {
		u["$push"] = bson.M{}
	}
	u["$push"].(bson.M)[field] = value
	return u
}

func (u *MongoDriverUpdator) SetLINEUserID(lineUID string) repository.DriverUpdator {
	u.set("line_user_id", lineUID)
	return u
}

func (u *MongoDriverUpdator) SetLINEMID(lineMID string) repository.DriverUpdator {
	u.set("line_user_mid", crypt.NewLazyEncryptedString(lineMID))
	return u
}

func (u *MongoDriverUpdator) IncreaseLINEUserIDRetryCount(amount int) repository.DriverUpdator {
	u.inc("line_user_id_retry", amount)
	return u
}

func (u *MongoDriverUpdator) SetDSCR(dscr float64) repository.DriverUpdator {
	u.set("dscr", dscr)
	return u
}

func (u *MongoDriverUpdator) SetDSCREffectiveDate(dscrEffectiveDate types.Period) repository.DriverUpdator {
	u.set("dscr_effective_date", dscrEffectiveDate)
	return u
}

func (u *MongoDriverUpdator) SetMaxTenor(maxTenor int) repository.DriverUpdator {
	u.set("max_tenor", maxTenor)
	return u
}

func (u *MongoDriverUpdator) SetMaxExposure(maxExposure float64) repository.DriverUpdator {
	u.set("max_exposure", maxExposure)
	return u
}

func (u *MongoDriverUpdator) PushRemark(remark model.DriverRemark) repository.DriverUpdator {
	u.push("remarks", remark)
	return u
}

var _ repository.DriverRepository = &MongoDriverRepository{}

type MongoDriverRepository struct {
	datastore             DriversDataStore
	cache                 datastore.RedisClient
	fileConnector         file.FileConnector
	config                config.AttendanceRateConfig
	attendanceLogsCounter metric.Counter
}

func (dr *MongoDriverRepository) Create(ctx context.Context, driver *model.Driver) error {
	return dr.datastore.Insert(ctx, driver)
}

func (dr *MongoDriverRepository) Find(ctx context.Context, skip, limit int) ([]model.Driver, error) {
	var drivers []model.Driver
	if err := dr.datastore.Find(ctx, bson.M{}, skip, limit, &drivers); err != nil {
		return nil, err
	}

	return drivers, nil
}

func (dr *MongoDriverRepository) FindSorted(ctx context.Context, skip, limit int) ([]model.Driver, error) {
	var drivers []model.Driver
	if err := dr.datastore.FindAndSort(ctx, bson.M{}, skip, limit, []string{"created_at", "_id"}, &drivers); err != nil {
		return nil, err
	}

	return drivers, nil
}

func (dr *MongoDriverRepository) FindDriverID(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
	var driver model.Driver
	if err := dr.datastore.FindOne(ctx, bson.M{KeyDriverID: driverID}, &driver, repository.ToDBOptions(opts)...); err != nil {
		return nil, repository.ErrNotFound
	}

	return &driver, nil
}

func (dr *MongoDriverRepository) FindDriverIDs(ctx context.Context, driverIDs []string, opts ...repository.Option) ([]model.Driver, error) {
	query := bson.M{"driver_id": bson.M{"$in": driverIDs}}

	var drivers []model.Driver
	if err := dr.datastore.Find(ctx, query, 0, 0, &drivers, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return drivers, nil
}

func (dr *MongoDriverRepository) CountDriver(ctx context.Context) (int, error) {
	var count int
	if err := dr.datastore.Count(ctx, bson.M{"_id": bson.M{"$exists": true}}, &count); err != nil {
		return 0, err
	}

	return count, nil
}

func (dr *MongoDriverRepository) FindWithQueryAndSort(ctx context.Context, query repository.DriverQuery, sort []string, skip, limit int, opts ...repository.Option) ([]model.Driver, error) {
	var drivers []model.Driver

	mq, ok := query.(*MongoDriverQuery)
	if !ok {
		return nil, errors.New("query must be MongoGroupTransactionQuery")
	}
	q, err := mq.Query()
	if err != nil {
		return nil, err
	}

	if err := dr.datastore.FindAndSort(ctx, q, skip, limit, sort, &drivers, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return drivers, nil
}

func (dr *MongoDriverRepository) FindWithQuerySelectorAndSort(ctx context.Context, query repository.DriverQuery, selector []string, sort []string, skip, limit int, opts ...repository.Option) ([]model.Driver, error) {
	var drivers []model.Driver

	mq, ok := query.(*MongoDriverQuery)
	if !ok {
		return nil, errors.New("query must be MongoGroupTransactionQuery")
	}
	q, err := mq.Query()
	if err != nil {
		return nil, err
	}

	if err := dr.datastore.FindWithSelectorAndSort(ctx, q, MongoDriverQuerySelector(selector), skip, limit, sort, &drivers, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}

	return drivers, nil
}

func (dr *MongoDriverRepository) CountDriverWithQuery(ctx context.Context, query repository.DriverQuery, opts ...repository.Option) (int, error) {
	var count int

	mq, ok := query.(*MongoDriverQuery)
	if !ok {
		return 0, errors.New("query must be MongoGroupTransactionQuery")
	}
	q, err := mq.Query()
	if err != nil {
		return 0, err
	}

	if err := dr.datastore.Count(ctx, q, &count, repository.ToDBOptions(opts)...); err != nil {
		return 0, err
	}

	return count, nil
}

func (dr *MongoDriverRepository) IsExists(ctx context.Context, driverID string) bool {
	return dr.datastore.IsExist(ctx, bson.M{KeyDriverID: driverID}, repository.WithReadSecondaryPreferred())
}

func (dr *MongoDriverRepository) Update(ctx context.Context, driver *model.Driver) error {
	driver.UpdatedAt = time.Now().UTC()
	if err := dr.datastore.Replace(ctx, bson.M{"driver_id": driver.DriverID}, driver); err != nil {
		return err
	}
	return cache.SetDriverStatusAndCurrentOrder(ctx, dr.cache, driver.DriverID, driver.Status, driver.CurrentOrder, driver.QueueingOrders, driver.AllowQueueing)
}

func (dr *MongoDriverRepository) UpdateManyByTier(ctx context.Context, driverIDs []string, newTier string) error {
	if len(driverIDs) == 0 {
		return errors.New("no update tier data")
	}

	selectors := bson.M{
		"$and": bson.A{
			bson.M{"driver_id": bson.M{"$in": driverIDs}},
			bson.M{"$or": bson.A{
				bson.M{"driver_tier": bson.M{"$ne": newTier}},
				bson.M{"driver_tier": bson.M{"$exists": false}},
			}},
		},
	}
	update := bson.M{
		"$set": bson.M{
			"driver_tier": newTier,
			"updated_at":  timeutil.BangkokNow(),
		},
		"$push": bson.M{"profile_histories": model.ProfileHistory{
			Name:  model.ProfileDriverTierHistoryName,
			Date:  timeutil.BangkokNow(),
			Value: newTier,
		}},
	}

	_, err := dr.datastore.UpdateAll(ctx, selectors, update)
	return err
}

func (dr *MongoDriverRepository) UpdateManyByNegativeGroup(ctx context.Context, driverIDs []string, group string) error {
	if len(driverIDs) == 0 {
		return nil
	}

	selector := bson.M{
		"driver_id": bson.M{
			"$in": driverIDs,
		},
	}

	update := bson.M{
		"$set": bson.M{
			"negative_balance_group": group,
			"updated_at":             timeutil.BangkokNow(),
		},
	}

	_, err := dr.datastore.UpdateAll(ctx, selector, update)
	return err
}

func (dr *MongoDriverRepository) MultipleUpdateHaveBox(ctx context.Context, driverIDs []string, setHaveBox bool) error {
	selectors := bson.M{"driver_id": bson.M{"$in": driverIDs}}
	update := bson.M{
		"$set": bson.M{"options.have_box": setHaveBox},
		"$push": bson.M{"profile_histories": model.ProfileHistory{
			Name:  model.ProfileHaveBoxHistoryName,
			Date:  timeutil.BangkokNow(),
			Value: setHaveBox,
		}},
	}

	_, err := dr.datastore.UpdateAll(ctx, selectors, update)
	return err
}

func (dr *MongoDriverRepository) GetByUID(ctx context.Context, UID string) (*model.Driver, error) {
	var driver model.Driver
	if err := dr.datastore.FindOne(ctx, bson.M{
		"$or": bson.A{
			bson.M{
				"line_uid": crypt.EncryptedString(UID).DeterministicEqWithContext(ctx),
			},
			bson.M{
				"line_user_id": UID,
			},
		},
		"status": bson.M{
			"$ne": model.StatusDeactivated,
		},
	}, &driver); err != nil {
		return nil, repository.ErrNotFound
	}

	return &driver, nil
}

func (dr *MongoDriverRepository) GetByRefreshToken(ctx context.Context, token string) (*model.Driver, error) {
	var driver model.Driver
	if err := dr.datastore.FindOne(ctx, bson.M{"refresh_token": token}, &driver); err != nil {
		return nil, repository.ErrNotFound
	}

	return &driver, nil
}

func (dr *MongoDriverRepository) SetRefreshToken(ctx context.Context, driverID string, token string) error {
	return dr.datastore.Update(ctx, bson.M{"driver_id": driverID}, bson.M{
		"$set": bson.M{"refresh_token": token},
	})
}

// SetCurrentStatus sets driver status to target.
// If checkBeforeOffline is true, then SetCurrentStatus
// checks that the current status is allowed to transition to Offline
func (dr *MongoDriverRepository) SetCurrentStatus(
	ctx context.Context,
	driverID string,
	target model.DriverStatus,
	opts ...model.SetCurrentStatusOptionFn,
) error {
	var opt model.SetCurrentStatusOption
	for _, fn := range opts {
		fn(&opt)
	}

	sel := bson.M{
		"driver_id": driverID,
		"status": bson.M{
			"$ne": model.StatusDeactivated,
		},
	}

	if opt.IsCheckBeforeOffline && target == model.StatusOffline {
		sel["status"] = bson.M{
			"$nin": bson.A{
				model.StatusDeactivated,
				model.StatusAssigned,
			},
		}
	}

	setter := bson.M{
		"status": target,
	}

	if target != model.StatusAssigned {
		setter["offline_later"] = false
	}
	if target == model.StatusOffline {
		setter["last_offline_at"] = timeutil.BangkokNow()
	}

	updated := bson.M{
		"$set": setter,
	}

	if dr.config.AttendanceRateEnabled {
		updated["$push"] = bson.M{
			"attendance_logs": getAtLog(dr.attendanceLogsCounter, target),
		}
	}

	if target == model.StatusOnline || target == model.StatusOffline {
		// when a driver is set to online or offline, locked_from_queueing should be removed from the profile
		updated["$unset"] = bson.M{
			"locked_from_queueing_until": "",
		}
	}

	err := dr.datastore.Update(ctx, sel, updated)
	if err != nil {
		return err
	}

	return cache.SetDriverStatus(ctx, dr.cache, driverID, target)
}

// CurrentStatus implements DriverRepository.
//
// TODO: change status to DriverStatus.
func (dr *MongoDriverRepository) CurrentStatus(ctx context.Context, driverID string) (ret repository.DriverCurrentState, err error) {
	ret.Status, ret.CurrentOrder, ret.QueueingOrders, ret.AllowQueueing, err = cache.GetDriverStatusAndCurrentOrder(ctx, dr.cache, driverID)
	if err == nil {
		return
	}
	err = dr.datastore.FindOneWithSelector(
		ctx,
		bson.M{
			"driver_id": driverID,
		},
		bson.M{
			"status":          1,
			"current_order":   1,
			"queueing_orders": 1,
			"allow_queueing":  1,
		},
		&ret,
	)
	return
}

// GetProfile implements DriverRepository
func (dr *MongoDriverRepository) GetProfile(ctx context.Context, driverID string, opts ...repository.Option) (*model.Driver, error) {
	var resp *model.Driver
	if err := dr.datastore.FindOne(
		ctx,
		bson.M{
			"driver_id": driverID,
		},
		&resp,
		repository.ToDBOptions(opts)...,
	); err != nil {
		return nil, err
	}

	if resp.Status == model.StatusOnline && resp.CurrentOrder != "" {
		safe.SentryErrorMessage("found a driver with status ONLINE but has current order", safe.WithDriverID(resp.DriverID), safe.WithOrderID(resp.CurrentOrder))
	}
	return resp, nil
}

// SetProfile implements DriverRepository.
func (dr *MongoDriverRepository) SetProfile(ctx context.Context, profile *model.Driver, additionalSel bson.M) error {
	sel := bson.M{"driver_id": profile.DriverID}
	if additionalSel != nil {
		for k, v := range additionalSel {
			sel[k] = v
		}
	}
	if err := dr.datastore.Replace(ctx, sel, profile); err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}
	if err := cache.SetDriverStatusAndCurrentOrder(ctx, dr.cache, profile.DriverID, profile.Status, profile.CurrentOrder, profile.QueueingOrders, profile.AllowQueueing); err != nil {
		return err
	}
	return nil
}

func (dr *MongoDriverRepository) SetProfileCache(ctx context.Context, profile *model.Driver) error {
	if err := cache.SetDriverStatusAndCurrentOrder(ctx, dr.cache, profile.DriverID, profile.Status, profile.CurrentOrder, profile.QueueingOrders, profile.AllowQueueing); err != nil {
		return err
	}
	return nil
}

func (dr *MongoDriverRepository) SetSMARating(ctx context.Context, driverID string, latestRatings []uint32, newAverage float64) error {
	if err := dr.datastore.Update(ctx, bson.M{"driver_id": driverID}, bson.M{"$set": bson.M{"sma_rating_score": newAverage, "latest_ratings": latestRatings}}); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) SetRatingScore(ctx context.Context, driver model.Driver) error {
	update := bson.M{"$set": bson.M{
		"n":                driver.N,
		"sma_rating_score": driver.SMARatingScore,
		"latest_ratings":   driver.LatestRatings,
	}}

	if err := dr.datastore.Update(ctx, bson.M{"driver_id": driver.DriverID}, update); err != nil {
		return err
	}

	return nil
}

// GetProfilesByID implements ProfileManager
func (dr *MongoDriverRepository) GetProfilesByID(ctx context.Context, driverIDs []string) (map[string]*model.Driver, error) {
	lenOfDriverIDs := len(driverIDs)
	driversById := make(map[string]*model.Driver, lenOfDriverIDs)
	batchSize := 100
	for i := 0; i < lenOfDriverIDs; i += batchSize {
		end := i + batchSize
		if end > lenOfDriverIDs {
			end = lenOfDriverIDs
		}

		batchDriverIDS := driverIDs[i:end]
		result := make([]*model.Driver, 0, len(batchDriverIDS))
		if err := dr.datastore.Find(
			ctx,
			bson.M{
				"driver_id": bson.M{
					"$in": batchDriverIDS,
				},
			},
			0,
			len(batchDriverIDS),
			&result,
		); err != nil {
			return nil, err
		}

		for _, d := range result {
			driversById[d.DriverID] = d
		}
	}

	return driversById, nil
}

func (dr *MongoDriverRepository) GetMinimalProfilesByID(ctx context.Context, driverIDs []string) (map[string]*model.DriverMinimal, error) {
	lenOfDriverIDs := len(driverIDs)
	driversById := make(map[string]*model.DriverMinimal, lenOfDriverIDs)
	batchSize := 500
	lock := sync.Mutex{}
	wg := sync.WaitGroup{}
	var bigErr error
	for i := 0; i < lenOfDriverIDs; i += batchSize {
		end := i + batchSize
		if end > lenOfDriverIDs {
			end = lenOfDriverIDs
		}

		batchDriverIDS := driverIDs[i:end]
		wg.Add(1)
		safe.GoFunc(func() {
			defer wg.Done()
			result := make([]*model.DriverMinimal, 0, len(batchDriverIDS))
			if err := dr.datastore.FindWithSelector(
				ctx,
				bson.M{
					"driver_id": bson.M{
						"$in": batchDriverIDS,
					},
				},
				bson.M{
					"driver_id":                        1,
					"region":                           1,
					"status":                           1,
					"current_order":                    1,
					"current_trip":                     1,
					"queueing_orders":                  1,
					"queueing_trips":                   1,
					"offline_later":                    1,
					"ban_later":                        1,
					"temporary_ban_history":            1,
					"driver_role":                      1,
					"driver_type":                      1,
					"options":                          1,
					"created_at":                       1,
					"phone":                            1,
					"shifts":                           1,
					"last_prediction_disruption":       1,
					"today_last_completed_order_at":    1,
					"last_accept_attempt_at":           1,
					"last_online_to_assigned_location": 1,
					"service_types":                    1,
					"service_types_silent_banned":      1,
					"driver_tier":                      1,
					"dedicated_zones":                  1,
					"locked_from_queueing_until":       1,
					"negative_balance_group":           1,
					"line_uid":                         1,
					"is_acknowledgement_required":      1,
					"h3_recommendation":                1,
					"is_deprioritized":                 1,
					"goodness":                         1,
					"services_opt_out":                 1,
					"line_user_id":                     1,
					"on_top_quotas":                    1,
				},
				&result,
				mongodb.WithReadPreference(readpref.Primary()),
			); err != nil {
				bigErr = err
				return
			}

			lock.Lock()
			defer lock.Unlock()
			for _, d := range result {
				driversById[d.DriverID] = d
			}
		})
	}
	wg.Wait()

	return driversById, bigErr
}

// RemoveProfile implements DriverRepository.
func (dr *MongoDriverRepository) RemoveProfile(ctx context.Context, driverID string) error {
	if err := dr.datastore.Remove(ctx, bson.M{"driver_id": driverID}); err != nil {
		return err
	}
	return cache.DelDriverStatus(ctx, dr.cache, driverID)
}

func (dr *MongoDriverRepository) SetTodayEarning(ctx context.Context, driverEarnings ...model.DriverEarning) error {
	return cache.SetDriverTodayEarning(ctx, dr.cache, timeutil.BangkokLocation(), driverEarnings...)
}

func (dr *MongoDriverRepository) GetTodayEarning(ctx context.Context, driverID string) (model.DriverEarning, error) {
	return cache.GetDriverTodayEarning(ctx, dr.cache, driverID)
}

// GetProfileByRefID get driver profile by citi virtual account
func (dr *MongoDriverRepository) GetProfileByRefID(ctx context.Context, refID string) (driver *model.Driver, err error) {
	q := bson.M{"$or": []bson.M{
		{"banking.ref_id": crypt.EncryptedString(refID).DeterministicEqWithContext(ctx)},
		{"ref_id": crypt.EncryptedString(refID).DeterministicEqWithContext(ctx)},
		{"banking.citi_ref_id": refID},
	}}

	err = dr.datastore.FindOne(ctx, q, &driver)
	return
}

func (dr *MongoDriverRepository) GetProfileByUOBRefID(ctx context.Context, refID string) (*model.Driver, error) {
	driver := &model.Driver{}
	if err := dr.datastore.FindOne(ctx, bson.M{"banking.uob_ref_id": refID}, driver); err != nil {
		return driver, err
	}
	return driver, nil
}

func (dr *MongoDriverRepository) CountProfileByRegion(ctx context.Context, region string) (int, error) {
	var result int
	if err := dr.datastore.Count(ctx, bson.M{"region": region}, &result); err != nil {
		return 0, err
	}

	return result, nil
}

func (dr *MongoDriverRepository) ListProfileByRegion(ctx context.Context, region string, skip, limit int) ([]model.Driver, error) {
	var result []model.Driver
	if err := dr.datastore.FindAndSort(ctx, bson.M{"region": region}, skip, limit, []string{"_id"}, &result); err != nil {
		return nil, err
	}

	return result, nil
}

func (dr *MongoDriverRepository) UpdateBanInfo(ctx context.Context, driver *model.Driver) error {
	setter := bson.M{
		"status":                driver.Status,
		"updated_at":            driver.UpdatedAt,
		"ban_later":             driver.BanLater,
		"temporary_ban_history": driver.TemporaryBanHistory,
		"current_trip":          driver.CurrentTrip,
		"current_order":         driver.CurrentOrder,
		"reason":                driver.Reason,
		"banned_until":          driver.BannedUntil,
		"offline_later":         driver.OfflineLater,
		"last_offline_at":       timeutil.BangkokNow(),
	}

	updated := bson.M{"$set": setter}
	if dr.config.AttendanceRateEnabled {
		updated["$push"] = bson.M{
			"attendance_logs": getAtLog(dr.attendanceLogsCounter, driver.Status),
		}
	}

	err := dr.datastore.Update(ctx, bson.M{"driver_id": driver.DriverID}, updated)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrDriverNotFound
		}
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) UpdateBanHistory(ctx context.Context, driver *model.Driver) error {
	setter := bson.M{
		"status":                driver.Status,
		"reason":                driver.Reason,
		"temporary_ban_history": driver.TemporaryBanHistory,
		"banned_until":          driver.BannedUntil,
		"current_trip":          driver.CurrentTrip,
		"current_order":         driver.CurrentOrder,
		"updated_at":            driver.UpdatedAt,
		"offline_later":         driver.OfflineLater,
	}

	if driver.Status == model.StatusBanned {
		setter["last_offline_at"] = timeutil.BangkokNow()
	}

	updated := bson.M{"$set": setter}
	if dr.config.AttendanceRateEnabled {
		updated["$push"] = bson.M{
			"attendance_logs": getAtLog(dr.attendanceLogsCounter, driver.Status),
		}
	}

	err := dr.datastore.Update(ctx, bson.M{"driver_id": driver.DriverID}, updated)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrDriverNotFound
		}
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) GetDriverBannedUntilExceed(ctx context.Context) ([]model.Driver, error) {
	query := bson.M{}
	query["status"] = model.StatusBanned
	query["$and"] = []bson.M{
		{"banned_until": bson.M{"$gt": time.Time{}}},
		{"banned_until": bson.M{"$lte": time.Now()}},
	}
	drivers := []model.Driver{}
	err := dr.datastore.Find(ctx, query, 0, 0, &drivers)
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrDriverNotFound
		}
		return nil, err
	}
	return drivers, nil
}

func (dr *MongoDriverRepository) UpdateCriminalStatus(ctx context.Context, driverIDs []string, status model.CriminalStatus) error {
	cipher := crypt.NewLazyEncryptedString(string(status))

	selector := bson.M{
		"driver_id": bson.M{"$in": driverIDs},
	}
	setter := bson.M{
		"$set": bson.M{"encrypted_criminal_check_status": cipher},
	}
	_, err := dr.datastore.UpdateAll(ctx, selector, setter)
	return err
}

func (dr *MongoDriverRepository) UpdateHaveJacket(ctx context.Context, driverIDs []string, value bool) error {
	selector := bson.M{
		"driver_id": bson.M{"$in": driverIDs},
	}
	setter := bson.M{
		"$set": bson.M{"options.have_jacket": value},
	}
	_, err := dr.datastore.UpdateAll(ctx, selector, setter)
	return err
}

func (dr *MongoDriverRepository) UpdateHaveBox(ctx context.Context, driverIDs []string, value bool) error {
	selector := bson.M{
		"driver_id": bson.M{"$in": driverIDs},
	}
	setter := bson.M{
		"$set": bson.M{"options.have_box": value},
		"$push": bson.M{"profile_histories": model.ProfileHistory{
			Name:  model.ProfileHaveBoxHistoryName,
			Date:  timeutil.BangkokNow(),
			Value: value,
		}},
	}
	_, err := dr.datastore.UpdateAll(ctx, selector, setter)
	return err
}

func (dr *MongoDriverRepository) UpdateBoxType(ctx context.Context, bt model.BoxType, driverIDs []string) error {
	selector := bson.M{
		"driver_id": bson.M{"$in": driverIDs},
	}
	setter := bson.M{
		"$set": bson.M{"options.box_type": bt},
		"$push": bson.M{"profile_histories": model.ProfileHistory{
			Name:  model.ProfileHaveBoxHistoryName,
			Date:  timeutil.BangkokNow(),
			Value: bt,
		}},
	}
	_, err := dr.datastore.UpdateAll(ctx, selector, setter)
	return err
}

func (dr *MongoDriverRepository) SetLastAttemptOrder(ctx context.Context, driverID, orderID string) error {
	updater := bson.M{
		"$set": bson.M{
			"driver_order_statistic.last_attempt_order_at": timeutil.BangkokNow(),
			"driver_order_statistic.last_attempt_order_id": orderID,
		},
	}
	return dr.datastore.Update(ctx, bson.M{"driver_id": driverID}, updater)
}

func (dr *MongoDriverRepository) SetLastCompletedOrder(ctx context.Context, driverID, orderID string) error {
	updater := bson.M{
		"$set": bson.M{
			"driver_order_statistic.last_completed_order_at": timeutil.BangkokNow(),
			"driver_order_statistic.last_completed_order_id": orderID,
		},
	}
	return dr.datastore.Update(ctx, bson.M{"driver_id": driverID}, updater)
}

func (dr *MongoDriverRepository) SetLastCanceledOrder(ctx context.Context, driverID, orderID string) error {
	updater := bson.M{
		"$set": bson.M{
			"driver_order_statistic.last_canceled_order_at": timeutil.BangkokNow(),
			"driver_order_statistic.last_canceled_order_id": orderID,
		},
	}
	return dr.datastore.Update(ctx, bson.M{"driver_id": driverID}, updater)
}

func (dr *MongoDriverRepository) AddShift(ctx context.Context, driverID, shiftId string) error {
	return dr.datastore.Update(ctx, bson.M{"driver_id": driverID}, bson.M{
		"$addToSet": bson.M{"shifts": shiftId},
	})
}

func (dr *MongoDriverRepository) RemoveShift(ctx context.Context, driverID, shiftId string) error {
	return dr.datastore.Update(ctx, bson.M{"driver_id": driverID}, bson.M{
		"$pull": bson.M{"shifts": shiftId},
	})
}

func (dr *MongoDriverRepository) RemoveShifts(ctx context.Context, driverID string, shiftIds []string) error {
	return dr.datastore.Update(ctx, bson.M{"driver_id": driverID}, bson.M{
		"$pullAll": bson.M{"shifts": shiftIds},
	})
}

func (dr *MongoDriverRepository) UpdateUobRefID(ctx context.Context, driver *model.Driver, newUobRefID string) error {
	selector := bson.M{
		"driver_id": driver.DriverID,
	}

	var setter bson.M
	if newUobRefID == "" {
		setter = bson.M{
			"$unset": bson.M{"banking.uob_ref_id": ""},
		}
	} else {
		setter = bson.M{
			"$set": bson.M{"banking.uob_ref_id": newUobRefID},
		}
	}

	if err := dr.datastore.Update(ctx, selector, setter); err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrNotFound
		}
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) SetCancellationQuota(ctx context.Context, driverId string, quota model.CancellationQuota) error {
	setter := bson.M{
		"cancellation_quota": quota,
		"updated_at":         timeutil.BangkokNow(),
	}

	err := dr.datastore.Update(ctx, bson.M{"driver_id": driverId}, bson.M{"$set": setter})
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrDriverNotFound
		}
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) SetWithdrawalQuota(ctx context.Context, driverId string, quota model.WithdrawalQuota) error {
	setter := bson.M{
		"withdrawal_quota": quota,
		"updated_at":       timeutil.BangkokNow(),
	}

	err := dr.datastore.Update(ctx, bson.M{"driver_id": driverId}, bson.M{"$set": setter})
	if err != nil {
		if err == mongodb.ErrDataNotFound {
			return repository.ErrDriverNotFound
		}
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) UnAssignTrip(ctx context.Context, driverId string, status model.DriverStatus) (*model.Driver, error) {
	updated := bson.M{
		"$set": bson.M{
			"current_trip":                "",
			"current_order":               "",
			"queueing_orders":             []string{},
			"queueing_trips":              []string{},
			"status":                      status,
			"is_acknowledgement_required": false,
		},
	}

	return dr.unAssign(ctx, driverId, status, updated)
}

func (dr *MongoDriverRepository) UnAssignOrder(ctx context.Context, driverId string, status model.DriverStatus) (*model.Driver, error) {
	updated := bson.M{
		"$set": bson.M{
			"current_order":               "",
			"queueing_orders":             []string{},
			"queueing_trips":              []string{},
			"status":                      status,
			"is_acknowledgement_required": false,
		},
	}

	return dr.unAssign(ctx, driverId, status, updated)
}

func (dr *MongoDriverRepository) unAssign(ctx context.Context, driverId string, status model.DriverStatus, updated bson.M) (*model.Driver, error) {
	if dr.config.AttendanceRateEnabled {
		updated["$push"] = bson.M{
			"attendance_logs": getAtLog(dr.attendanceLogsCounter, status),
		}
	}

	res, err := dr.datastore.FindOneAndUpdate(
		ctx,
		bson.M{
			"driver_id": driverId,
		},
		updated,
		mongodb.WithReturnDocument(options.ReturnDocument(1)),
	)
	if err != nil {
		return nil, err
	}

	var driv model.Driver
	err = res.Decode(&driv)
	if err != nil {
		return nil, err
	}

	return &driv, nil
}

func (dr *MongoDriverRepository) SetCompletedOrderTime(ctx context.Context, driverId string) error {
	updated := bson.M{
		"$set": bson.M{
			"today_last_completed_order_at": timeutil.BangkokNow(),
		},
	}

	_, err := dr.datastore.FindOneAndUpdate(
		ctx,
		bson.M{
			"driver_id": driverId,
		},
		updated,
		mongodb.WithReturnDocument(options.ReturnDocument(1)),
	)
	if err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) SetRefCode(ctx context.Context, driverId string, code string, opts ...repository.Option) error {
	update := bson.M{"$set": bson.M{
		"referrer_code": code,
	}}

	if err := dr.datastore.Update(ctx, bson.M{"driver_id": driverId}, update, repository.ToDBOptions(opts)...); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) SetOfflineLater(ctx context.Context, driverID string, value bool, opts ...repository.Option) error {
	setter := bson.M{
		"$set": bson.M{
			"offline_later":              value,
			"last_prediction_disruption": time.Now(),
		},
	}
	if err := dr.datastore.Update(ctx, bson.M{KeyDriverID: driverID, driverKeyStatus: model.StatusAssigned}, setter, repository.ToDBOptions(opts)...); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) FindWhoHasAttendanceLogInBetween(ctx context.Context, start time.Time, end time.Time, opts ...repository.Option) ([]model.Driver, error) {
	pipeline := []bson.M{
		{
			// Query documents where in between `start` and `end`
			"$match": bson.M{
				"attendance_logs.time": bson.M{"$gte": start, "$lte": end},
			},
		},
		{
			// Filter out result only attendance log in between `start` and `end`
			"$project": bson.M{
				"driver_id": "$driver_id",
				"attendance_logs": bson.M{
					"$filter": bson.M{
						"input": "$attendance_logs",
						"as":    "attendance_log",
						"cond": bson.M{
							"$and": bson.A{
								bson.M{"$gte": bson.A{"$$attendance_log.time", start}},
								bson.M{"$lte": bson.A{"$$attendance_log.time", end}},
							},
						},
					},
				},
			},
		},
	}

	var drivers []model.Driver
	err := dr.datastore.Aggregate(ctx, pipeline, &drivers, repository.ToDBOptions(opts)...)
	if err != nil {
		return nil, err
	}

	return drivers, nil
}

func (dr *MongoDriverRepository) RemoveAttendanceLogInBetween(ctx context.Context, driverID string, start time.Time, end time.Time) error {
	query := bson.M{
		"driver_id": bson.M{"$eq": driverID},
	}

	update := bson.M{
		"$pull": bson.M{
			"attendance_logs": bson.M{
				"time": bson.M{
					"$gte": start,
					"$lte": end,
				},
			},
		},
	}

	err := dr.datastore.Update(ctx, query, update)
	if err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) GetAttendanceLog(ctx context.Context, driverID string, opts ...repository.Option) (model.DriverAttendance, error) {
	sel := bson.M{
		"attendance_logs": 1,
		"status":          1,
		"region":          1,
		"shifts":          1,
	}

	var resp model.DriverAttendance

	err := dr.datastore.FindOneWithSelector(ctx, bson.M{"driver_id": driverID}, sel, &resp, repository.ToDBOptions(opts)...)
	if err != nil {
		return model.DriverAttendance{DriverStatus: model.StatusOffline}, err
	}

	return resp, nil
}

func (dr *MongoDriverRepository) AddAttendanceLog(ctx context.Context, driverID string, logs []model.AttendanceLog) error {
	sel := bson.M{
		"driver_id": driverID,
	}

	updated := bson.M{
		"$push": bson.M{
			"attendance_logs": bson.M{
				"$each": logs,
			},
		},
	}

	err := dr.datastore.Update(ctx, sel, updated)
	if err != nil {
		return err
	}
	return nil
}

func (dr *MongoDriverRepository) UpdateAfterAcceptingBundleOrder(ctx context.Context, driverID string, record model.Record) error {
	now := time.Now()
	updator := bson.M{
		"$set": bson.M{
			"last_prediction_disruption": now,
			"last_accept_attempt_at":     now,
		},
	}

	if record.IsMultiplePickup {
		updator["$unset"] = bson.M{
			"locked_from_queueing_until": "",
		}
	}

	if err := dr.datastore.Update(ctx, bson.M{KeyDriverID: driverID}, updator); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) UnlockForQueueing(ctx context.Context, driverID string) error {
	unSetter := bson.M{
		"$unset": bson.M{
			"locked_from_queueing_until": "",
		},
	}

	if err := dr.datastore.Update(
		ctx,
		bson.M{
			KeyDriverID: driverID,
		},
		unSetter,
	); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) MarkAsAcknowledged(ctx context.Context, driverID string) (bool, []string, error) {
	setter := bson.M{
		"$set": bson.M{
			"is_acknowledgement_required": false,
		},
	}

	before, err := dr.datastore.FindOneAndUpdate(
		ctx,
		bson.M{
			KeyDriverID: driverID,
		},
		setter,
		mongodb.WithReturnDocument(options.Before),
	)
	if err != nil {
		return false, nil, err
	}

	var driverBeforeUpdated model.Driver
	if err = before.Decode(&driverBeforeUpdated); err != nil {
		return false, nil, err
	}

	var tripIds []string
	if driverBeforeUpdated.IsAcknowledgementRequired && driverBeforeUpdated.CurrentTrip != "" {
		tripIds = append(tripIds, driverBeforeUpdated.CurrentTrip)
		tripIds = append(tripIds, driverBeforeUpdated.QueueingTrips...)
	}

	return driverBeforeUpdated.IsAcknowledgementRequired, tripIds, nil
}

func (dr *MongoDriverRepository) SetLastAcceptAttemptAt(ctx context.Context, driverID string, lastAcceptAttemptAt time.Time) error {
	setter := bson.M{
		"$set": bson.M{
			"last_accept_attempt_at": lastAcceptAttemptAt,
		},
	}
	if err := dr.datastore.Update(ctx, bson.M{KeyDriverID: driverID}, setter); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) SetRecoIdleTimeStartPoint(ctx context.Context, driverID string, t time.Time) error {
	setter := bson.M{
		"$set": bson.M{
			"reco_idle_time_start_point": t,
		},
	}
	if err := dr.datastore.Update(ctx, bson.M{KeyDriverID: driverID}, setter); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) SetTripID(ctx context.Context, driverID string, tripID string) error {
	setter := bson.M{
		"$set": bson.M{
			"current_trip": tripID,
		},
	}
	if err := dr.datastore.Update(ctx, bson.M{KeyDriverID: driverID}, setter); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) DeleteCurrentOrderFromCache(ctx context.Context, driverID string) error {
	return cache.DeleteCurrentOrder(ctx, dr.cache, driverID)
}

func (dr *MongoDriverRepository) SetCurrentStatusToCache(ctx context.Context, driverID string, status model.DriverStatus) error {
	return cache.SetDriverStatus(ctx, dr.cache, driverID, status)
}

func (dr *MongoDriverRepository) SetDriverShiftCountdownBreakingQuota(ctx context.Context, driverID, shiftId string, ttl time.Duration) error {
	return cache.SetDriverShiftCountdownBreakingQuota(ctx, dr.cache, driverID, shiftId, ttl)
}

func (dr *MongoDriverRepository) AddRemark(ctx context.Context, driverID string, remark model.DriverRemark) error {
	sel := bson.M{
		"driver_id": driverID,
	}
	updated := bson.M{
		"$push": bson.M{
			"remarks": remark,
		},
	}

	err := dr.datastore.Update(ctx, sel, updated)
	if err != nil {
		return err
	}
	return nil
}

func (dr *MongoDriverRepository) UpdateDriverTier(ctx context.Context, driver *model.Driver) error {
	err := dr.datastore.Update(
		ctx,
		bson.M{"driver_id": driver.DriverID},
		bson.M{
			"$set": bson.M{
				"driver_tier":       driver.DriverTier,
				"profile_histories": driver.ProfileHistories,
				"updated_at":        timeutil.BangkokNow(),
			},
		},
	)
	if err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) UpdateProfileStatus(ctx context.Context, driverID string, status model.ProfileStatus) error {
	updated := bson.M{
		"$set": bson.M{
			"profile_status": status,
			"updated_at":     timeutil.BangkokNow(),
		},
	}
	if err := dr.datastore.Update(ctx, bson.M{KeyDriverID: driverID}, updated); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) GetProfileByIDForRequestUpdate(ctx context.Context, driverID string) (model.DriverRequestUpdateProfile, error) {
	driver := model.DriverRequestUpdateProfile{}
	result := make([]*model.DriverRequestUpdateProfile, 0)
	if err := dr.datastore.FindWithSelector(
		ctx,
		bson.M{
			"driver_id": driverID,
		},
		bson.M{
			"driver_id":              1,
			"profile_status":         1,
			"request_update_profile": 1,
		},
		&result,
	); err != nil {
		return driver, err
	}

	if len(result) == 0 {
		return driver, repository.ErrNotFound
	}

	driver = *result[0]

	return driver, nil
}

func (dr *MongoDriverRepository) UpdateServiceTypes(ctx context.Context, driverID string, services []model.Service) error {
	sel := bson.M{"driver_id": driverID}

	upd := bson.M{
		"$set": bson.M{
			"updated_at":    timeutil.BangkokNow(),
			"service_types": services,
		},
	}

	err := dr.datastore.Update(ctx, sel, upd)
	return err
}

func (dr *MongoDriverRepository) UpdateManyDedicatedZoneLabels(ctx context.Context, driverIDs []string, dedicatedZoneLabels []string) error {
	if len(driverIDs) == 0 {
		return repository.ErrNotFound
	}

	batchSize := 100
	for i := 0; i < len(driverIDs); i += batchSize {
		end := i + batchSize
		if end > len(driverIDs) {
			end = len(driverIDs)
		}

		batchDriverIDs := driverIDs[i:end]
		_, err := dr.datastore.UpdateAll(
			ctx,
			bson.M{"driver_id": bson.M{"$in": batchDriverIDs}},
			bson.M{
				"$set": bson.M{
					"updated_at":      timeutil.BangkokNow(),
					"dedicated_zones": dedicatedZoneLabels,
				},
			},
		)
		if err != nil {
			return err
		}
	}

	return nil
}

func (dr *MongoDriverRepository) IsExistsByCitizenID(ctx context.Context, citizenID crypt.LazyEncryptedString) bool {
	query := bson.M{
		registrationKeyCitizenID: citizenID.DeterministicEqWithContext(ctx),
	}
	return dr.datastore.IsExist(ctx, query, repository.WithReadSecondaryPreferred())
}

func (dr *MongoDriverRepository) UpdateDriverFinancialRiskControl(ctx context.Context, driverID string, dscr float64, maxTenor int, maxExposure float64, remark model.DriverRemark) error {
	sel := bson.M{"driver_id": driverID}

	upd := bson.M{
		"$set": bson.M{
			"dscr":         dscr,
			"max_tenor":    maxTenor,
			"max_exposure": maxExposure,
			"updated_at":   timeutil.BangkokNow(),
		},
		"$push": bson.M{
			"remarks": remark,
		},
	}

	err := dr.datastore.Update(ctx, sel, upd)
	return err
}

func (dr *MongoDriverRepository) SetDriverH3Recommendation(ctx context.Context, driverID string, rec *model.H3Recommendation) error {
	sel := bson.M{"driver_id": driverID}

	upd := bson.M{
		"$set": bson.M{
			"h3_recommendation": rec,
		},
	}
	if rec == nil {
		upd = bson.M{
			"$unset": bson.M{
				"h3_recommendation": "",
			},
		}
	}
	err := dr.datastore.Update(ctx, sel, upd)
	return err
}

func isSorted(services model.Services) bool {
	if len(services) < 1 {
		return true
	}

	prev := services[0]
	for _, s := range services {
		if prev > s {
			return false
		}

		prev = s
	}

	return true
}

const mergeChar string = "|"

func mapIDsByServicesCombo(m map[string]model.Services) (map[string][]string, bool) {
	/*
		input:
		"LMD01" -> ["food", "mart"]
		"LMD02" -> ["mart"]
		"LMD03" -> ["food", "mart"]

		output:
		"food|mart" -> ["LMD01", "LMD03"]
		"mart"      -> ["LMD02"]

		If there are 4 service types, [bike, food, mart, messenger],
		then there can be 4! combos, reducing number of bulk write models
	*/

	result := make(map[string][]string)
	for driverID, services := range m {
		if !isSorted(services) {
			return nil, false
		}

		merged := strings.Join(services.String(), mergeChar)
		result[merged] = append(result[merged], driverID)
	}

	return result, true
}

func bulkWritesDepriorEnable(comboGroups map[string][]string, updatedAt time.Time) []mongo.WriteModel {
	models := make([]mongo.WriteModel, len(comboGroups))
	c := 0

	for servicesCombo, driverIDs := range comboGroups {
		services := strings.Split(servicesCombo, mergeChar)

		models[c] = mongo.NewUpdateManyModel().
			SetFilter(bson.M{
				KeyDriverID: bson.M{
					"$in": driverIDs,
				},
			}).
			SetUpdate(bson.M{
				"$set": bson.M{
					keyIsDeprioritized:       true,
					keyServicesDeprioritized: services,
					keyUpdatedAt:             updatedAt,
				},
			})

		c++
	}

	return models
}

func (dr *MongoDriverRepository) BulkUpdateDeprioritization(
	ctx context.Context,
	toNormalize []string, // List of driver IDs to disable deprioritization
	toDeprioritize map[string]model.Services, // Maps driver ID to services (sorted) to deprioritize.
) (int, error) {
	driversCount := len(toNormalize) + len(toDeprioritize)
	if driversCount == 0 {
		return 0, nil
	}

	updatedAt := timeutil.BangkokNow()

	combos, sorted := mapIDsByServicesCombo(toDeprioritize)
	if !sorted {
		return 0, errors.New("services not sorted alphabetically")
	}
	writesEnable := bulkWritesDepriorEnable(combos, updatedAt)

	writeDisable := mongo.NewUpdateManyModel().
		SetFilter(bson.M{
			KeyDriverID: bson.M{
				"$in": toNormalize,
			},
		}).
		SetUpdate(bson.M{
			"$set": bson.M{
				keyIsDeprioritized:       false,
				keyServicesDeprioritized: nil,
				keyUpdatedAt:             updatedAt,
			},
		})

	result, err := dr.datastore.BulkWrite(ctx, append(writesEnable, writeDisable), options.BulkWrite())
	if err != nil {
		return 0, err
	}

	modified := int(result.ModifiedCount)
	if driversCount != modified {
		return modified, fmt.Errorf("input data and modified count mismatch: %d vs %d", driversCount, modified)
	}

	return modified, nil
}

func (dr *MongoDriverRepository) ValidateDriverIDs(ctx context.Context, driverIDs []string, opts ...repository.Option) ([]string, []string, error) {
	pipeline := []bson.M{
		{"$match": bson.M{
			"driver_id": bson.M{"$in": driverIDs},
		}},
		{"$group": bson.M{
			"_id":              nil,
			"valid_driver_ids": bson.M{"$push": "$driver_id"},
		}},
	}

	var result struct {
		ValidDriverIDs []string `bson:"valid_driver_ids"`
	}
	err := dr.datastore.AggregateOne(ctx, pipeline, &result, repository.ToDBOptions(opts)...)
	if err != nil {
		return nil, nil, err
	}

	invalidDriverIDs := make([]string, 0, len(driverIDs)-len(result.ValidDriverIDs))

	// construct valid set of driver ID
	vs := types.NewStringSet(result.ValidDriverIDs...)

	for _, driverID := range driverIDs {
		if !vs.Has(driverID) {
			invalidDriverIDs = append(invalidDriverIDs, driverID)
		}
	}

	return result.ValidDriverIDs, invalidDriverIDs, nil
}

func (dr *MongoDriverRepository) SetGoodness(ctx context.Context, driverID string, goodness model.Goodness) error {
	sel := bson.M{"driver_id": driverID}

	upd := bson.M{
		"$set": bson.M{
			"updated_at": timeutil.BangkokNow(),
			"goodness":   goodness,
		},
	}
	err := dr.datastore.Update(ctx, sel, upd)
	if err != nil {
		return err
	}
	return nil
}

func (dr *MongoDriverRepository) FindOneAndSetField(ctx context.Context, query repository.DriverQuery, updator repository.DriverUpdator) error {
	driverQuery, ok := query.(*MongoDriverQuery)
	if !ok {
		logx.Error().
			Str("method", "MongoDriverRepository.FindOneAndSetField").
			Msg("unable to convert an object into DriverQuery")
		return errors.New("invalid query")
	}

	driverUpdator, ok := updator.(*MongoDriverUpdator)
	if !ok {
		logx.Error().
			Str("method", "MongoDriverRepository.FindOneAndSetField").
			Msg("unable to convert an object into DriverUpdator")
		return errors.New("invalid updator")
	}
	q, err := driverQuery.Query()
	if err != nil {
		return err
	}

	result, err := dr.datastore.FindOneAndUpdate(ctx, q, driverUpdator)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "MongoDriverRepository.FindOneAndSetField").
			Msgf("unable to find one and update drivers collection by a query [%v]", q)
		return err
	}

	if result.Err() != nil {
		logx.Error().Err(result.Err()).
			Str("method", "MongoDriverRepository.FindOneAndSetField").
			Msgf("got an error after a find one and update drivers collection by a query [%v]", q)
		return result.Err()
	}
	return nil
}

func (dr *MongoDriverRepository) BulkWriteModel(ctx context.Context, updateOneModels ...repository.DriverUpdateOneModel) error {
	var writeModels []mongo.WriteModel
	for _, updateOneModel := range updateOneModels {
		driverQuery, ok := updateOneModel.Query.(*MongoDriverQuery)
		if !ok {
			logx.Error().
				Str("method", "MongoDriverRepository.BulkWriteModel").
				Msg("unable to convert an object into DriverQuery")
			return errors.New("invalid query")
		}

		driverUpdator, ok := updateOneModel.Updator.(*MongoDriverUpdator)
		if !ok {
			logx.Error().
				Str("method", "MongoDriverRepository.BulkWriteModel").
				Msg("unable to convert an object into DriverUpdator")
			return errors.New("invalid updator")
		}
		q, err := driverQuery.Query()
		if err != nil {
			return err
		}

		m := mongo.NewUpdateOneModel().
			SetFilter(q).
			SetUpdate(driverUpdator)
		writeModels = append(writeModels, m)
	}

	writeRes, err := dr.datastore.BulkWrite(ctx, writeModels, options.BulkWrite().SetOrdered(false))
	if err != nil {
		logx.Error().Err(err).
			Str("method", "MongoDriverRepository.BulkWriteModel").
			Msgf("got an error when bulk write [%d] models", len(writeModels))
		return err
	}

	logx.Info().
		Str("method", "MongoDriverRepository.BulkWriteModel").
		Msgf("document updated [%d]", writeRes.ModifiedCount)
	return nil
}

func (dr *MongoDriverRepository) SetServicesOptOut(ctx context.Context, driverID string, optedOutServices []model.Service) error {
	sel := bson.M{"driver_id": driverID}

	upd := bson.M{
		"$set": bson.M{
			"updated_at":       timeutil.BangkokNow(),
			"services_opt_out": optedOutServices,
		},
	}
	err := dr.datastore.Update(ctx, sel, upd)
	if err != nil {
		return err
	}
	return nil
}

func (dr *MongoDriverRepository) CountDriverIDs(ctx context.Context, driverIDs []string) (int, error) {
	var count int
	if err := dr.datastore.Count(ctx, bson.M{
		"_id":       bson.M{"$exists": true},
		KeyDriverID: bson.M{"$in": driverIDs},
	}, &count); err != nil {
		return -1, err
	}

	return count, nil
}

func (dr *MongoDriverRepository) GlobalToggleDisableSupplyPos(ctx context.Context) error {
	selector := bson.M{
		keySupplyPos: bson.M{
			"$exists": true,
		},
	}

	update := bson.M{
		"$unset": bson.M{keySupplyPos: 1},
		"$set": bson.M{
			keyUpdatedAt: timeutil.BangkokNow(),
		},
	}

	_, err := dr.datastore.UpdateAll(ctx, selector, update)
	if err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) MultipleEnableSupplyPositioning(ctx context.Context, driverIDs []string) error {
	selector := bson.M{KeyDriverID: bson.M{"$in": driverIDs}}
	update := bson.M{
		"$set": bson.M{
			keySupplyPos: true,
			keyUpdatedAt: timeutil.BangkokNow(),
		},
	}

	_, err := dr.datastore.UpdateAll(ctx, selector, update)
	if err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) FindDriverIDsSupplyPositioning(ctx context.Context) ([]string, error) {
	query := bson.M{
		keySupplyPos: true,
	}
	projection := bson.M{
		KeyDriverID: 1,
	}

	var dummies []struct {
		DriverID string `bson:"driver_id"`
	}

	err := dr.datastore.FindWithSelector(ctx, query, projection, &dummies)
	if err != nil {
		return nil, errors.Wrap(err, "mongo error")
	}

	driverIDs := make([]string, len(dummies))
	for i := range dummies {
		driverIDs[i] = dummies[i].DriverID
	}

	return driverIDs, nil
}

func (dr *MongoDriverRepository) FindDriverMinimalByDriverID(ctx context.Context, driverID string, opts ...repository.Option) (*model.DriverMinimal, error) {
	var driver model.DriverMinimal
	if err := dr.datastore.FindOne(ctx, bson.M{KeyDriverID: driverID}, &driver, repository.ToDBOptions(opts)...); err != nil {
		return nil, repository.ErrNotFound
	}

	return &driver, nil
}

func (dr *MongoDriverRepository) SetOnTopQuota(ctx context.Context, driverID string, onTopQuotas []model.OnTopQuota) error {
	setter := bson.M{
		"$set": bson.M{
			"on_top_quotas": onTopQuotas,
		},
	}

	if err := dr.datastore.Update(ctx, bson.M{KeyDriverID: driverID}, setter); err != nil {
		return err
	}

	return nil
}

func (dr *MongoDriverRepository) UpdateDriverBySetter(ctx context.Context, driverID string, driverSetter setter.Driver) error {
	if err := dr.datastore.Update(ctx, bson.M{KeyDriverID: driverID}, driverSetter); err != nil {
		logx.Error().
			Context(ctx).
			Err(err).
			Str(logutil.DriverIDKey, driverID).
			Msgf("UpdateOnTopQuotaByPosition: Fail to update on-top quota. Setter: %s", driverSetter)
		return err
	}
	return nil
}

func (dr *MongoDriverRepository) SetServiceOptInLastOnlineAt(ctx context.Context, driverID string, lastOnlineAt time.Time) error {
	selector := bson.M{KeyDriverID: driverID}
	update := bson.M{
		"$set": bson.M{
			"service_opt_in_reminder.last_online_at": lastOnlineAt,
		},
	}

	err := dr.datastore.Update(ctx, selector, update)
	if err != nil {
		return err
	}
	return nil
}

func (dr *MongoDriverRepository) SetPauseServiceOptInBikeReminderUntil(ctx context.Context, driverID string, until time.Time) error {
	selector := bson.M{KeyDriverID: driverID}
	update := bson.M{
		"$set": bson.M{
			"service_opt_in_reminder.pause_bike_until": until,
		},
	}

	if err := dr.datastore.Update(ctx, selector, update); err != nil {
		return err
	}
	return nil
}

func NewDriverRepository(ds DriversDataStore, cache datastore.RedisClient, config config.AttendanceRateConfig, meter metric.Meter) *MongoDriverRepository {
	return &MongoDriverRepository{
		datastore:             ds,
		cache:                 cache,
		config:                config,
		attendanceLogsCounter: meter.GetCounter("attendance_logs_counter", "Number of attendance logs", "status"),
	}
}

func ProvideDriverRepository(ds DriversDataStore, cache datastore.RedisClient, config config.AttendanceRateConfig, meter metric.Meter) *repository.ProxyDriverRepository {
	return repository.NewLatencyProxyDriverRepository(NewDriverRepository(ds, cache, config, meter), meter)
}

// DriversDataStore is type wrapper for drivers collection.
type DriversDataStore mongodb.DataStoreInterface

func ProvideDriversDataStore(conn *mongodb.Conn) DriversDataStore {
	return mongodb.NewDataStoreWithConn(conn, "drivers")
}

func getAtLog(attrCounter metric.Counter, status model.DriverStatus) model.AttendanceLog {
	defer attrCounter.Inc(string(status))

	switch status {
	case model.StatusBanned, model.StatusDeactivated, model.StatusOffline:
		return model.AttendanceLog{
			Time:   timeutil.BangkokNow(),
			Status: model.AttendanceStatusOffline,
		}
	}

	return model.AttendanceLog{
		Time:   timeutil.BangkokNow(),
		Status: model.AttendanceStatusOnline,
	}
}

func (dr *MongoDriverRepository) FindDriverIDAndStatus(ctx context.Context, driverID string, status model.DriverStatus, opts ...repository.Option) (*model.Driver, error) {
	var driver model.Driver
	if err := dr.datastore.FindOne(ctx, bson.M{KeyDriverID: driverID, driverKeyStatus: status}, &driver, repository.ToDBOptions(opts)...); err != nil {
		return nil, repository.ErrNotFound
	}

	return &driver, nil
}
