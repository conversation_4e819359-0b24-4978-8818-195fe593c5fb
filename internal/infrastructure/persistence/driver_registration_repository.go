package persistence

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"gopkg.in/guregu/null.v4"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/crypt"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

var (
	ErrInvalidQueryType = errors.New("invalid query type")
)

const (
	registrationKeyCitizenID = "citizen_id"
	registrationKeyLineUid   = "line_uid"
	registrationKeyStatus    = "status"
	registrationKeyPreScreen = "pre_screen"
	registrationKeyTrained   = "trained"

	registrationKeyInfoCompleted = "info_completed"
)

type DriverRegistrationQuery struct {
	IDs                           []string
	CitizenID                     crypt.LazyEncryptedString
	Firstname                     crypt.LazyEncryptedString
	Lastname                      crypt.LazyEncryptedString
	Phone                         crypt.LazyEncryptedString
	Status                        string
	PreScreen                     string
	Trained                       string
	DriverType                    crypt.LazyEncryptedString
	From                          time.Time
	To                            time.Time
	InfoCompleted                 string
	InterestedRegionLalamove      string
	InterestedProvince            string
	Region                        string
	Regions                       []string
	AssignedReviewer              string
	InterestedProvinces           []string
	RegistrationLocation          repository.RegistrationLocationQuery
	ExcludeStatus                 []string
	CriminalCheckStatus           crypt.LazyEncryptedString
	IsRegisDuplicatedCitizenId    null.Bool
	IsDriverDuplicatedCitizenId   null.Bool
	IsLINEUserIDExisted           null.Bool
	LINEUserIDRetryCountLowerThan int
	LINEUserID                    string
}

func NewDriverRegistrationQuery() *DriverRegistrationQuery {
	return &DriverRegistrationQuery{}
}

func (q *DriverRegistrationQuery) WithIDs(ids []string) repository.DriverRegistrationQuery {
	q.IDs = ids
	return q
}

func (q *DriverRegistrationQuery) WithCitizenID(citizenID crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	q.CitizenID = citizenID
	return q
}

func (q *DriverRegistrationQuery) WithFirstname(firstname crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	q.Firstname = firstname
	return q
}

func (q *DriverRegistrationQuery) WithLastname(lastname crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	q.Lastname = lastname
	return q
}

func (q *DriverRegistrationQuery) WithPhone(phone crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	q.Phone = phone
	return q
}

func (q *DriverRegistrationQuery) WithStatus(status string) repository.DriverRegistrationQuery {
	q.Status = status
	return q
}

func (q *DriverRegistrationQuery) WithoutStatus(status []string) repository.DriverRegistrationQuery {
	q.ExcludeStatus = status
	return q
}

func (q *DriverRegistrationQuery) WithPreScreen(preScreen string) repository.DriverRegistrationQuery {
	q.PreScreen = preScreen
	return q
}

func (q *DriverRegistrationQuery) WithTrained(trained string) repository.DriverRegistrationQuery {
	q.Trained = trained
	return q
}

func (q *DriverRegistrationQuery) WithInfoCompleted(infoCompleted string) repository.DriverRegistrationQuery {
	q.InfoCompleted = infoCompleted
	return q
}

func (q *DriverRegistrationQuery) WithDriverType(driverType crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	q.DriverType = driverType
	return q
}

func (q *DriverRegistrationQuery) WithFrom(from time.Time) repository.DriverRegistrationQuery {
	q.From = from
	return q
}

func (q *DriverRegistrationQuery) WithTo(to time.Time) repository.DriverRegistrationQuery {
	q.To = to
	return q
}

func (q *DriverRegistrationQuery) WithInterestedRegionLalamove(interestedRegionLalamove string) repository.DriverRegistrationQuery {
	q.InterestedRegionLalamove = interestedRegionLalamove
	return q
}

func (q *DriverRegistrationQuery) WithInterestedProvince(interestedProvince string) repository.DriverRegistrationQuery {
	if interestedProvince != "" && q.InterestedProvinces == nil {
		q.InterestedProvinces = []string{interestedProvince}
	} else if interestedProvince != "" {
		q.InterestedProvinces = append(q.InterestedProvinces, interestedProvince)
	}
	return q
}

func (q *DriverRegistrationQuery) WithRegion(region string) repository.DriverRegistrationQuery {
	q.Region = region
	return q
}

func (q *DriverRegistrationQuery) WithInRegions(regions []string) repository.DriverRegistrationQuery {
	q.Regions = regions
	return q
}

func (q *DriverRegistrationQuery) WithAssignedReviewer(assignedReviewer string) repository.DriverRegistrationQuery {
	q.AssignedReviewer = assignedReviewer
	return q
}

func (q *DriverRegistrationQuery) WithInterestedProvinces(interestedProvinces []string) repository.DriverRegistrationQuery {
	if q.InterestedProvinces == nil {
		q.InterestedProvinces = interestedProvinces
	} else {
		q.InterestedProvinces = append(q.InterestedProvinces, interestedProvinces...)
	}
	return q
}

func (q *DriverRegistrationQuery) WithRegistrationLocation(query repository.RegistrationLocationQuery) repository.DriverRegistrationQuery {
	q.RegistrationLocation = query
	return q
}

func (q *DriverRegistrationQuery) WithCriminalCheckStatus(criminalCheckStatus model.CriminalStatus) repository.DriverRegistrationQuery {
	q.CriminalCheckStatus = crypt.NewLazyEncryptedString(string(criminalCheckStatus))
	return q
}

func (q *DriverRegistrationQuery) WithRegisDuplicatedCitizenId(d null.Bool) repository.DriverRegistrationQuery {
	q.IsRegisDuplicatedCitizenId = d
	return q
}

func (q *DriverRegistrationQuery) WithDriverDuplicatedCitizenId(d null.Bool) repository.DriverRegistrationQuery {
	q.IsDriverDuplicatedCitizenId = d
	return q
}

func (q *DriverRegistrationQuery) WithLINEUserIDExist(isExisted null.Bool) repository.DriverRegistrationQuery {
	q.IsLINEUserIDExisted = isExisted
	return q
}

func (q *DriverRegistrationQuery) WithLINEUserIDRetryLower(count int) repository.DriverRegistrationQuery {
	q.LINEUserIDRetryCountLowerThan = count
	return q
}

func (q *DriverRegistrationQuery) WithLINEUserID(src string) repository.DriverRegistrationQuery {
	q.LINEUserID = src
	return q
}

func (q *DriverRegistrationQuery) Query() bson.M {
	query := bson.M{}

	if len(q.IDs) > 0 {
		var bsonIDs []primitive.ObjectID
		for _, id := range q.IDs {
			oid, err := primitive.ObjectIDFromHex(id)
			if err != nil {
				continue
			}
			bsonIDs = append(bsonIDs, oid)
		}
		query["_id"] = bson.M{"$in": bsonIDs}
	}

	if q.CitizenID.String() != "" {
		query["citizen_id"] = q.CitizenID.DeterministicEq()
	}

	if q.Firstname.String() != "" {
		query["firstname"] = q.Firstname.DeterministicEq()
	}

	if q.Lastname.String() != "" {
		query["lastname"] = q.Lastname.DeterministicEq()
	}

	if q.Phone.String() != "" {
		query["phone"] = q.Phone.DeterministicEq()
	}

	if q.Status != "" {
		query[registrationKeyStatus] = q.Status
	}

	andCondition := []bson.M{}

	if q.PreScreen == "true" {
		query[registrationKeyPreScreen] = true
	} else if q.PreScreen == "false" {
		notPreScreen := bson.M{"$or": []bson.M{
			{registrationKeyPreScreen: false},
			{registrationKeyPreScreen: ""},
			{registrationKeyPreScreen: bson.M{"$exists": false}},
		}}
		andCondition = append(andCondition, notPreScreen)
	}

	if q.Trained == "true" {
		query[registrationKeyTrained] = true
	} else if q.Trained == "false" {
		notTrain := bson.M{"$or": []bson.M{
			{registrationKeyTrained: false},
			{registrationKeyTrained: ""},
			{registrationKeyTrained: bson.M{"$exists": false}},
		}}
		andCondition = append(andCondition, notTrain)
	}

	if q.InfoCompleted == "true" {
		query[registrationKeyInfoCompleted] = true
	} else if q.InfoCompleted == "false" {
		infoNotCompleted := bson.M{"$or": []bson.M{
			{registrationKeyInfoCompleted: false},
			{registrationKeyInfoCompleted: ""},
			{registrationKeyInfoCompleted: bson.M{"$exists": false}},
		}}
		andCondition = append(andCondition, infoNotCompleted)
	}

	if q.DriverType.String() != "" {
		query["driver_type"] = q.DriverType.DeterministicEq()
	}

	if q.InterestedRegionLalamove == "true" {
		interested := bson.M{"$or": []bson.M{
			{"interesting_province": "กรุงเทพมหานคร"},
			{"interesting_province": "นครปฐม"},
			{"interesting_province": "นนทบุรี"},
			{"interesting_province": "ปทุมธานี"},
			{"interesting_province": "สมุทรปราการ"},
			{"interesting_province": "สมุทรสาคร"},
			{"interesting_province": "ชลบุรี"},
		}}
		andCondition = append(andCondition, interested)
	} else if q.InterestedRegionLalamove == "false" {
		notInterested := bson.M{"$and": []bson.M{
			{"interesting_province": bson.M{"$ne": "กรุงเทพมหานคร"}},
			{"interesting_province": bson.M{"$ne": "นครปฐม"}},
			{"interesting_province": bson.M{"$ne": "นนทบุรี"}},
			{"interesting_province": bson.M{"$ne": "ปทุมธานี"}},
			{"interesting_province": bson.M{"$ne": "สมุทรปราการ"}},
			{"interesting_province": bson.M{"$ne": "สมุทรสาคร"}},
			{"interesting_province": bson.M{"$ne": "ชลบุรี"}},
		}}
		andCondition = append(andCondition, notInterested)
	}

	if len(q.InterestedProvinces) > 0 {
		query["interesting_province"] = bson.M{"$in": q.InterestedProvinces}
	}

	if q.Region != "" {
		query["region"] = q.Region
	}

	if len(q.Regions) != 0 {
		query["region"] = bson.M{"$in": q.Regions}
	}

	if "UNASSIGNED" == q.AssignedReviewer {
		query["$or"] = []bson.M{
			{"assigned_reviewer": ""},
			{"assigned_reviewer": bson.M{"$exists": false}},
		}
	} else if q.AssignedReviewer != "" {
		query["assigned_reviewer"] = q.AssignedReviewer
	}

	if q.ExcludeStatus != nil {
		if _, exists := query[registrationKeyStatus]; !exists {
			query[registrationKeyStatus] = bson.M{
				"$nin": q.ExcludeStatus,
			}
		}
	}

	if !q.From.IsZero() || !q.To.IsZero() {
		createAtQuery := bson.M{}
		if !q.From.IsZero() {
			createAtQuery["$gt"] = q.From
		}
		if !q.To.IsZero() {
			createAtQuery["$lt"] = q.To
		}
		query["created_at"] = createAtQuery
	}

	if len(andCondition) > 0 {
		query["$and"] = andCondition
	}

	if !q.RegistrationLocation.IsZero() {
		if q.RegistrationLocation.District != "" {
			query["registration_location.district"] = q.RegistrationLocation.District
		}
		if q.RegistrationLocation.Province != "" {
			query["registration_location.province"] = q.RegistrationLocation.Province
		}
	}

	if q.CriminalCheckStatus.String() != "" {
		if q.CriminalCheckStatus.String() == string(model.CriminalStatusPending) {
			query["encrypted_criminal_check_status"] = crypt.NewLazyEncryptedString("").DeterministicEq()
		} else {
			query["encrypted_criminal_check_status"] = q.CriminalCheckStatus.DeterministicEq()
		}
	}

	if !q.IsRegisDuplicatedCitizenId.IsZero() {
		query["is_duplicated_citizen_id"] = q.IsRegisDuplicatedCitizenId.Bool
	}

	if !q.IsDriverDuplicatedCitizenId.IsZero() {
		query["is_driver_duplicated_citizen_id"] = q.IsDriverDuplicatedCitizenId.Bool
	}

	if !q.IsLINEUserIDExisted.IsZero() {
		if q.IsLINEUserIDExisted.Bool {
			query["line_user_id"] = bson.M{
				"$exists": q.IsLINEUserIDExisted.Bool,
			}
		} else {
			if _, existed := query["$and"]; !existed {
				query["$and"] = []bson.M{}
			}
			srcAnd := query["$and"].([]bson.M)

			srcOr := []bson.M{}
			srcOr = append(srcOr, bson.M{
				"line_user_id": "",
			})
			srcOr = append(srcOr, bson.M{
				"line_user_id": bson.M{
					"$exists": false,
				},
			})
			srcAnd = append(srcAnd, bson.M{
				"$or": srcOr,
			})

			query["$and"] = srcAnd
		}
	}

	if q.LINEUserIDRetryCountLowerThan > 0 {
		if _, existed := query["$and"]; !existed {
			query["$and"] = []bson.M{}
		}
		srcAnd := query["$and"].([]bson.M)

		srcOr := []bson.M{}
		srcOr = append(srcOr, bson.M{
			"line_user_id_retry": bson.M{
				"$lt": q.LINEUserIDRetryCountLowerThan,
			},
		})
		srcOr = append(srcOr, bson.M{
			"line_user_id_retry": bson.M{
				"$exists": false,
			},
		})
		srcAnd = append(srcAnd, bson.M{
			"$or": srcOr,
		})
		query["$and"] = srcAnd
	}

	if q.LINEUserID != "" {
		query["line_user_id"] = q.LINEUserID
	}

	return query
}

type DriverRegisterationUpdator bson.M

func (u DriverRegisterationUpdator) set(field string, value any) DriverRegisterationUpdator {
	if _, existed := u["$set"]; !existed {
		u["$set"] = bson.M{}
	}

	u["$set"].(bson.M)[field] = value
	return u
}

func (u DriverRegisterationUpdator) inc(field string, value any) DriverRegisterationUpdator {
	if _, existed := u["$inc"]; !existed {
		u["$inc"] = bson.M{}
	}
	u["$inc"].(bson.M)[field] = value
	return u
}

func (u *DriverRegisterationUpdator) SetLINEUserID(lineUID string) repository.DriverRegisterationUpdator {
	u.set("line_user_id", lineUID)
	return u
}

func (u *DriverRegisterationUpdator) SetLINEMID(lineMID string) repository.DriverRegisterationUpdator {
	u.set("line_user_mid", crypt.NewLazyEncryptedString(lineMID))
	return u
}

func (u *DriverRegisterationUpdator) IncreaseLINEUserIDRetryCount(amount int) repository.DriverRegisterationUpdator {
	u.inc("line_user_id_retry", amount)
	return u
}

type DriverRegistrationRepository struct {
	datastore DriverRegistrationDataStore
}

func ProvideDriverRegistrationRepository(datastore DriverRegistrationDataStore, meter metric.Meter) *repository.ProxyDriverRegistrationRepository {
	return repository.NewLatencyProxyDriverRegistrationRepository(&DriverRegistrationRepository{
		datastore: datastore,
	}, meter)
}

func (r *DriverRegistrationRepository) IsExistsByCitizenIDAndID(ctx context.Context, citizenID crypt.LazyEncryptedString, id string) bool {
	query := bson.M{"citizen_id": citizenID.DeterministicEqWithContext(ctx)}

	objId, err := primitive.ObjectIDFromHex(id)
	if err == nil {
		query["_id"] = bson.M{"$ne": objId}
	}
	return r.datastore.IsExist(ctx, query)
}

func (r *DriverRegistrationRepository) IsExistsByCitizenID(ctx context.Context, citizenID crypt.LazyEncryptedString) bool {
	query := bson.M{
		registrationKeyCitizenID: citizenID.DeterministicEqWithContext(ctx),
	}
	return r.datastore.IsExist(ctx, query)
}

func (r *DriverRegistrationRepository) IsExistsByLineUid(ctx context.Context, lineUid crypt.LazyEncryptedString) bool {
	query := bson.M{
		"$or": bson.A{
			bson.M{
				registrationKeyLineUid: lineUid.DeterministicEqWithContext(ctx),
			},
			bson.M{
				"line_user_id": lineUid.String(),
			},
		},
		"status": bson.M{
			"$ne": model.RegistrationArchived,
		},
	}
	return r.datastore.IsExist(ctx, query)
}

func (r *DriverRegistrationRepository) Find(ctx context.Context, query repository.DriverRegistrationQuery, skip, limit int, sorts []string, opts ...repository.Option) ([]model.DriverRegistration, error) {
	driverRegistrations := make([]model.DriverRegistration, 0, limit)
	driverRegisQuery, ok := query.(*DriverRegistrationQuery)
	if !ok {
		return nil, ErrInvalidQueryType
	}

	if err := r.datastore.FindAndSort(ctx, driverRegisQuery.Query(), skip, limit, sorts, &driverRegistrations, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	return driverRegistrations, nil
}

func (r *DriverRegistrationRepository) FindOneByID(ctx context.Context, id string) (*model.DriverRegistration, error) {
	var driverRegistration *model.DriverRegistration
	oid, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	if err := r.datastore.FindOne(ctx, bson.M{"_id": oid}, &driverRegistration); err != nil {
		return nil, err
	}
	return driverRegistration, nil
}

func (r *DriverRegistrationRepository) GetByLineUID(ctx context.Context, lineUID string) (*model.DriverRegistration, error) {
	var driverRegistration *model.DriverRegistration
	if err := r.datastore.FindOne(ctx, bson.M{
		"$or": bson.A{
			bson.M{
				"line_uid": crypt.NewLazyEncryptedString(lineUID).DeterministicEqWithContext(ctx),
			},
			bson.M{
				"line_user_id": lineUID,
			},
		},
		"status": bson.M{
			"$ne": model.RegistrationArchived,
		},
	}, &driverRegistration); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return driverRegistration, nil
}

func (r *DriverRegistrationRepository) ListByLineUID(ctx context.Context, lineUID crypt.LazyEncryptedString) ([]model.DriverRegistration, error) {
	var listDriverRegistrations []model.DriverRegistration
	if err := r.datastore.Find(ctx, bson.M{"line_uid": lineUID.DeterministicEqWithContext(ctx)}, 0, 0, &listDriverRegistrations); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return listDriverRegistrations, nil
}

func (r *DriverRegistrationRepository) Insert(ctx context.Context, entity *model.DriverRegistration) error {
	return r.datastore.Insert(ctx, entity)
}

func (r *DriverRegistrationRepository) Update(ctx context.Context, entity *model.DriverRegistration) error {
	entity.UpdatedAt = time.Now()
	return r.datastore.ReplaceID(ctx, entity.ID, entity)
}

func (r *DriverRegistrationRepository) Count(ctx context.Context, query repository.DriverRegistrationQuery) (int, error) {
	var count int
	driverRegisQuery, ok := query.(*DriverRegistrationQuery)
	if !ok {
		return 0, errors.New("query wrong")
	}
	err := r.datastore.Count(ctx, driverRegisQuery.Query(), &count, repository.WithReadSecondaryPreferred())
	if err != nil {
		return -1, err
	}
	return count, nil
}

func (r *DriverRegistrationRepository) RemoveByLineUID(ctx context.Context, lineUid crypt.LazyEncryptedString) error {
	return r.datastore.Remove(ctx, bson.M{registrationKeyLineUid: lineUid.DeterministicEqWithContext(ctx)})
}

func (r *DriverRegistrationRepository) FindRegisByCitizenIDs(ctx context.Context, citizenIDs []crypt.LazyEncryptedString) ([]model.DriverRegistration, error) {
	ids := crypt.LazyEncryptedStringArray(citizenIDs)
	query := bson.M{"citizen_id": bson.M{"$in": ids.AllPossibleDeterministicValue(ctx)}}

	var driverRegis []model.DriverRegistration
	if err := r.datastore.Find(ctx, query, 0, 0, &driverRegis); err != nil {
		return nil, err
	}

	return driverRegis, nil
}

func (r *DriverRegistrationRepository) GetByCitizenID(ctx context.Context, citizenID crypt.LazyEncryptedString) (*model.DriverRegistration, error) {
	var driverRegistration *model.DriverRegistration
	if err := r.datastore.FindOne(ctx, bson.M{"citizen_id": citizenID.DeterministicEqWithContext(ctx)}, &driverRegistration); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return driverRegistration, nil
}

func (r *DriverRegistrationRepository) FindOneAndSetField(ctx context.Context, query repository.DriverRegistrationQuery, updator repository.DriverRegisterationUpdator) error {
	driverRegisQuery, ok := query.(*DriverRegistrationQuery)
	if !ok {
		logx.Error().
			Str("method", "DriverRegistrationRepository.FindOneAndSetField").
			Msg("unable to convert an object into DriverRegistrationQuery")
		return errors.New("invalid query")
	}

	driverRegisUpdator, ok := updator.(*DriverRegisterationUpdator)
	if !ok {
		logx.Error().
			Str("method", "DriverRegistrationRepository.FindOneAndSetField").
			Msg("unable to convert an object into DriverRegistrationUpdator")
		return errors.New("invalid updator")
	}

	result, err := r.datastore.FindOneAndUpdate(ctx, driverRegisQuery.Query(), driverRegisUpdator)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "DriverRegistrationRepository.FindOneAndSetField").
			Msgf("unable to find one and update driver_registrations collection by a query [%v]", driverRegisQuery.Query())
		return err
	}

	if result.Err() != nil {
		logx.Error().Err(result.Err()).
			Str("method", "DriverRegistrationRepository.FindOneAndSetField").
			Msgf("got an error after a find one and update driver_registrations collection by a query [%v]", driverRegisQuery.Query())
		return result.Err()
	}
	return nil
}

func (dr *DriverRegistrationRepository) BulkWriteModel(ctx context.Context, updateOneModels ...repository.DriverRegistrationUpdateOneModel) error {

	var writeModels []mongo.WriteModel
	for _, updateOneModel := range updateOneModels {
		driverRegQuery, ok := updateOneModel.Query.(*DriverRegistrationQuery)
		if !ok {
			logx.Error().
				Str("method", "DriverRegistrationRepository.BulkWriteModel").
				Msg("unable to convert an object into DriverRegistrationQuery")
			return errors.New("invalid query")
		}

		driverRegUpdator, ok := updateOneModel.Updator.(*DriverRegisterationUpdator)
		if !ok {
			logx.Error().
				Str("method", "DriverRegistrationRepository.BulkWriteModel").
				Msg("unable to convert an object into DriverRegisterationUpdator")
			return errors.New("invalid updator")
		}

		m := mongo.NewUpdateOneModel().
			SetFilter(driverRegQuery.Query()).
			SetUpdate(driverRegUpdator)
		writeModels = append(writeModels, m)
	}

	writeRes, err := dr.datastore.BulkWrite(ctx, writeModels, options.BulkWrite().SetOrdered(false))
	if err != nil {
		logx.Error().Err(err).
			Str("method", "DriverRegistrationRepository.BulkWriteModel").
			Msgf("got an error when bulk write [%d] models", len(writeModels))
		return err
	}

	logx.Info().
		Str("method", "DriverRegistrationRepository.BulkWriteModel").
		Msgf("document updated [%d]", writeRes.ModifiedCount)
	return nil
}

func (dr *DriverRegistrationRepository) FindWithQuerySelectorAndSort(ctx context.Context, query repository.DriverRegistrationQuery, selector []string, skip, limit int, sorts []string, opts ...repository.Option) ([]model.DriverRegistration, error) {
	driverRegistrations := make([]model.DriverRegistration, 0, limit)
	driverRegisQuery, ok := query.(*DriverRegistrationQuery)
	if !ok {
		return nil, ErrInvalidQueryType
	}

	if err := dr.datastore.FindWithSelectorAndSort(ctx, driverRegisQuery.Query(), MongoDriverQuerySelector(selector), skip, limit, sorts, &driverRegistrations, repository.ToDBOptions(opts)...); err != nil {
		return nil, err
	}
	return driverRegistrations, nil
}

func (r *DriverRegistrationRepository) ListByLINEUserID(ctx context.Context, lineUID string) ([]model.DriverRegistration, error) {
	var listDriverRegistrations []model.DriverRegistration
	if err := r.datastore.Find(ctx,
		bson.M{"line_user_id": lineUID},
		0, 0, &listDriverRegistrations); err != nil {
		if err == mongodb.ErrDataNotFound {
			return nil, repository.ErrNotFound
		}
		return nil, err
	}
	return listDriverRegistrations, nil
}

type DriverRegistrationDataStore mongodb.DataStoreInterface

func ProvideDriverRegistrationDataStore(conn *mongodb.Conn) DriverRegistrationDataStore {
	return mongodb.NewDataStoreWithConn(conn, "drivers_registration")
}
