//go:build integration_test
// +build integration_test

package persistence_test

import (
	"context"
	"encoding/json"
	"math"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/uber/h3-go"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
)

func TestDriverLocationRepository(t *testing.T) {
	t.Run("lyft geo index saves correctly", func(t *testing.T) {
		t.<PERSON>llel()
		ctn := ittest.NewContainer(t)
		ctx := context.Background()
		driverID := "drv1"
		loc := model.Location{
			Lat: 1,
			Lng: 1,
		}
		updatedLoc := model.LocationWithUpdatedAt{
			Lat: 1,
			Lng: 1,
		}
		hash := h3.ToString(h3.FromGeo(h3.GeoCoord{
			Latitude:  loc.Lat,
			Longitude: loc.Lng,
		}, 6))
		err := ctn.DriverLocationRepositoryForTest.UpdateDriverLocation(ctx, repository.UpdateDriverLocationRequest{
			DriverID: driverID,
			Location: updatedLoc,
		})
		require.NoError(t, err)
		time.Sleep(50 * time.Millisecond)

		result, err := ctn.LocationRedisClient.Get(ctx, "lyft_driver_location:"+driverID).Result()
		require.NoError(t, err)
		var actualLocation model.Location
		require.NoError(t, json.Unmarshal([]byte(result), &actualLocation))
		assert.EqualValues(t, loc, actualLocation)

		ids, err := ctn.LocationRedisClient.ZRange(ctx, "lyft_geo_index_ids:"+hash, math.MinInt64, math.MaxInt64).Result()
		require.NoError(t, err)
		assert.Contains(t, ids, driverID)
	})

	t.Run("lyft geo index expires correctly", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		ctx := context.Background()
		driverID := "drv1"
		loc := model.Location{
			Lat: 1,
			Lng: 1,
		}
		updatedLoc := model.LocationWithUpdatedAt{
			Lat: 1,
			Lng: 1,
		}
		hash := h3.ToString(h3.FromGeo(h3.GeoCoord{
			Latitude:  loc.Lat,
			Longitude: loc.Lng,
		}, 6))
		err := ctn.DriverLocationRepositoryForTest.UpdateDriverLocation(ctx, repository.UpdateDriverLocationRequest{
			DriverID: driverID,
			Location: updatedLoc,
		})
		require.NoError(t, err)
		time.Sleep(50 * time.Millisecond)

		_, err = ctn.LocationRedisClient.Get(ctx, "lyft_driver_location:"+driverID).Result()
		require.NoError(t, err)

		// see if can fetch before expiring
		ids, err := ctn.DriverLocationRepositoryForTest.GetDrivers(ctx, repository.DriverLocationQuery{
			Location: loc,
		})
		require.NoError(t, err)
		assert.Equal(t, ids[0].DriverID, "drv1")

		// expire then fetch again (shouldn't show up)
		ctn.LocationRedisClient.ZAdd(ctx, "lyft_geo_index_ids:"+hash, redis.Z{
			Score:  float64(time.Now().Unix() - 3600),
			Member: driverID,
		})
		ids, err = ctn.DriverLocationRepositoryForTest.GetDrivers(ctx, repository.DriverLocationQuery{
			Location: loc,
		})
		require.NoError(t, err)
		assert.Empty(t, ids)

		// unexpire to make sure it wasn't a fluke
		ctn.LocationRedisClient.ZAdd(ctx, "lyft_geo_index_ids:"+hash, redis.Z{
			Score:  float64(time.Now().Unix() - 1),
			Member: driverID,
		})
		ids, err = ctn.DriverLocationRepositoryForTest.GetDrivers(ctx, repository.DriverLocationQuery{
			Location: loc,
		})
		require.NoError(t, err)
		assert.Equal(t, ids[0].DriverID, "drv1")
	})

	t.Run("can getDrivers of lyft geo index", func(t *testing.T) {
		t.Parallel()
		ctn := ittest.NewContainer(t)
		ctx := context.Background()
		driverID := "drv1"
		loc := model.Location{
			Lat: 1,
			Lng: 1,
		}
		updatedLoc := model.LocationWithUpdatedAt{
			Lat: 1,
			Lng: 1,
		}
		err := ctn.DriverLocationRepositoryForTest.UpdateDriverLocation(ctx, repository.UpdateDriverLocationRequest{
			DriverID: driverID,
			Location: updatedLoc,
		})
		require.NoError(t, err)
		time.Sleep(50 * time.Millisecond)
		actual, err := ctn.DriverLocationRepositoryForTest.GetDrivers(ctx, repository.DriverLocationQuery{
			Location: loc,
		})
		require.NoError(t, err)
		assert.EqualValues(t, driverID, actual[0].DriverID)
		assert.EqualValues(t, loc, actual[0].Location)
	})

	t.Run("getDrivers don't return duplicated id", func(t *testing.T) {
		loc1 := model.LocationWithUpdatedAt{Lat: 1, Lng: 1}
		loc2 := model.LocationWithUpdatedAt{Lat: 1.1, Lng: 1.1}
		hash1 := h3.ToString(h3.FromGeo(h3.GeoCoord{
			Latitude:  loc1.Lat,
			Longitude: loc1.Lng,
		}, 6))
		hash2 := h3.ToString(h3.FromGeo(h3.GeoCoord{
			Latitude:  loc2.Lat,
			Longitude: loc2.Lng,
		}, 6))
		require.NotEqual(t, hash1, hash2)

		ctn := ittest.NewContainer(t)
		ctx := context.Background()
		driverID := "drv1"
		controlDriverID := "ctrl" // this driver uses loc2. if radius is far enough for both loc1&loc2, this rider should be fetched
		updateLocation(t, ctn, driverID, loc1)
		updateLocation(t, ctn, driverID, loc2)
		updateLocation(t, ctn, controlDriverID, loc2)
		actual, err := ctn.DriverLocationRepositoryForTest.GetDrivers(ctx, repository.DriverLocationQuery{
			Location: model.Location{
				Lat: loc1.Lat,
				Lng: loc1.Lng,
			},
			From:  0,
			To:    15000,
			Limit: 5,
		})
		require.NoError(t, err)
		assert.ElementsMatch(t, []string{actual[0].DriverID, actual[1].DriverID}, []string{driverID, controlDriverID})
	})
}

func updateLocation(t *testing.T, ctn *ittest.IntegrationTestContainer, driverID string, location model.LocationWithUpdatedAt) {
	err := ctn.DriverLocationRepositoryForTest.UpdateDriverLocation(context.Background(), repository.UpdateDriverLocationRequest{
		DriverID: driverID,
		Location: model.LocationWithUpdatedAt{
			Lat: location.Lat,
			Lng: location.Lng,
		},
	})
	require.NoError(t, err)
	time.Sleep(50 * time.Millisecond)
}
