//go:build integration_test
// +build integration_test

package persistence_test

import (
	"context"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"github.com/stretchr/testify/require"
	"testing"
)

func TestDeliveryFeeSettingRepository(t *testing.T) {
	var serviceType model.Service = "my_service"
	var region1 model.RegionCode = "region1"
	var region2 model.RegionCode = "region1"

	id := "delivery_fee_setting_id"
	defaultScheme := model.NewLegacyDeliveryFeeSettingScheme(model.RestaurantTypeKey("EMENU"), types.Money(100))
	testDeliveryFeeSetting := model.NewDeliveryFeeSetting(id, serviceType, region1, defaultScheme)
	testDeliveryFeeSetting2 := model.NewDeliveryFeeSetting(id, serviceType, region2, defaultScheme)

	expectEqualDeliveryFeeSetting := func(tt *testing.T, expected model.DeliveryFeeSetting, actual model.DeliveryFeeSetting) {
		require.Equal(tt, testDeliveryFeeSetting.ServiceType(), actual.ServiceType())
		require.Equal(tt, testDeliveryFeeSetting.Region(), actual.Region())
		require.Equal(tt, testDeliveryFeeSetting.ID(), actual.ID())

		expectedPriceScheme, err := testDeliveryFeeSetting.GetScheme(defaultScheme.Key())
		require.NoError(tt, err)
		actualPriceScheme, err := actual.GetScheme(defaultScheme.Key())
		require.NoError(tt, err)

		require.Equal(tt, expectedPriceScheme.BaseFee(), actualPriceScheme.BaseFee())
	}

	t.Run("test getting delivery fee setting", func(tt *testing.T) {
		ctn := ittest.NewContainer(t)
		err := ctn.DeliveryFeeSettingMongo.Create(context.Background(), testDeliveryFeeSetting)
		require.NoError(t, err)

		deliveryFeeSettingFromMongo, err := ctn.DeliveryFeeSettingMongo.GetByRegionAndService(context.Background(), string(region1), serviceType)
		require.NoError(t, err)
		expectEqualDeliveryFeeSetting(tt, *testDeliveryFeeSetting, deliveryFeeSettingFromMongo)

		deliveryFeeSettingFromRedis, err := cache.GetDeliveryFeeSetting(context.Background(), ctn.Redis, string(region1), serviceType)
		require.NoError(t, err)
		expectEqualDeliveryFeeSetting(tt, *testDeliveryFeeSetting, *deliveryFeeSettingFromRedis)
	})

	t.Run("test updating delivery fee setting", func(tt *testing.T) {
		ctn := ittest.NewContainer(t)
		err := ctn.DeliveryFeeSettingMongo.Create(context.Background(), testDeliveryFeeSetting)
		require.NoError(t, err)

		err = ctn.DeliveryFeeSettingMongo.Update(context.Background(), testDeliveryFeeSetting2)
		require.NoError(t, err)

		_, err = cache.GetDeliveryFeeSetting(context.Background(), ctn.Redis, string(region1), serviceType)
		require.Error(t, err)

		updatedDeliveryFeeSetting, err := ctn.DeliveryFeeSettingMongo.GetByRegionAndService(context.Background(), string(region1), serviceType)
		require.NoError(t, err)
		expectEqualDeliveryFeeSetting(tt, *testDeliveryFeeSetting2, updatedDeliveryFeeSetting)
	})

	t.Run("test is exist delivery fee setting without cache", func(tt *testing.T) {
		ctn := ittest.NewContainer(t)
		err := ctn.DeliveryFeeSettingMongo.Create(context.Background(), testDeliveryFeeSetting)
		require.NoError(t, err)

		exist := ctn.DeliveryFeeSettingMongo.IsExistsByRegionAndService(context.Background(), string(region1), serviceType)
		require.Equal(t, true, exist)
	})

	t.Run("test is exist delivery fee setting with cache", func(tt *testing.T) {
		ctn := ittest.NewContainer(t)
		err := ctn.DeliveryFeeSettingMongo.Create(context.Background(), testDeliveryFeeSetting)
		require.NoError(t, err)

		deliveryFeeSettingFromMongo, err := ctn.DeliveryFeeSettingMongo.GetByRegionAndService(context.Background(), string(region1), serviceType)
		require.NoError(t, err)
		expectEqualDeliveryFeeSetting(tt, *testDeliveryFeeSetting, deliveryFeeSettingFromMongo)

		exist := ctn.DeliveryFeeSettingMongo.IsExistsByRegionAndService(context.Background(), string(region1), serviceType)
		require.Equal(t, true, exist)
	})
}
