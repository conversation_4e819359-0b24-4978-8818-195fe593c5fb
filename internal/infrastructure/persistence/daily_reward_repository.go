package persistence

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type MongoDailyRewardRepository struct {
	ds MongoDailyRewardDataStore
}

func (r *MongoDailyRewardRepository) GetDailyReward(ctx context.Context, driverID string, t time.Time) (model.DailyReward, error) {
	var dailyReward model.DailyReward
	dt := timeutil.DateTruncateTZ(t, timeutil.BangkokLocation())
	if err := r.ds.FindOne(ctx, bson.M{"driver_id": driverID, "date": dt}, &dailyReward); err != nil {
		return model.DailyReward{}, err
	}
	return dailyReward, nil
}

func (r *MongoDailyRewardRepository) GetOrCreateDailyReward(ctx context.Context, driverID string, t time.Time) (model.DailyReward, error) {
	var dailyReward model.DailyReward
	now := timeutil.BangkokNow().UTC()
	dt := timeutil.DateTruncateTZ(t, timeutil.BangkokLocation())
	newModel := model.DailyReward{
		DriverID:   driverID,
		Date:       dt,
		CoinAmount: 0,
		CreatedAt:  now,
		UpdatedAt:  now,
	}
	if _, err := r.ds.FindOrCreate(ctx, bson.M{"driver_id": driverID, "date": dt}, newModel, []string{}, &dailyReward, mongodb.WithReadPreference(readpref.Primary())); err != nil {
		return model.DailyReward{}, err
	}
	return dailyReward, nil
}

func (r *MongoDailyRewardRepository) UpdateDailyReward(ctx context.Context, dailyReward model.DailyReward) error {
	dailyReward.UpdatedAt = timeutil.BangkokNow().UTC()
	if _, err := r.ds.Upsert(ctx, bson.M{"driver_id": dailyReward.DriverID, "date": dailyReward.Date}, dailyReward); err != nil {
		return err
	}
	return nil
}

func (r *MongoDailyRewardRepository) GetDailyRewardsFromDate(ctx context.Context, start, end time.Time) ([]model.DailyReward, error) {
	var dailyRewards []model.DailyReward
	q := bson.M{
		"date": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}

	if err := r.ds.Find(ctx, q, 0, 0, &dailyRewards); err != nil {
		return nil, err
	}
	return dailyRewards, nil
}

type MongoDailyRewardDataStore mongodb.DataStoreInterface

func ProvideMongoDailyRewardRepository(datastore MongoDailyRewardDataStore, meter metric.Meter) *repository.ProxyDailyRewardRepository {
	return repository.NewLatencyProxyDailyRewardRepository(&MongoDailyRewardRepository{ds: datastore}, meter)
}

func ProvideMongoDailyRewardDataStore(conn *mongodb.Conn) MongoDailyRewardDataStore {
	return mongodb.NewDataStoreWithConn(conn, "daily_rewards")
}
