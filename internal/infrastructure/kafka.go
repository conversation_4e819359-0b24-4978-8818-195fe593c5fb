package infrastructure

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/domain"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type KafkaConfig struct {
	ProducerURL string `envconfig:"KAFKA_PRODUCER_HOST"`
}

func ProvideKafkaConfig() KafkaConfig {
	var cfg KafkaConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

type KafkaEventBus struct {
	latencyHistogram metric.Histogram
	http             *httpclient.Client
	cfg              KafkaConfig
	wg               sync.WaitGroup
}

func (kc *KafkaEventBus) url(path string) string {
	return fmt.Sprintf("%s/%s", kc.cfg.ProducerURL, path)
}

// Publish publish message with option use synchronous as default
func (kc *KafkaEventBus) Publish(ctx context.Context, topic string, data []byte, opts ...domain.PublishOption) error {
	var cfg domain.PublishConfig
	for _, opt := range opts {
		opt(&cfg)
	}

	url := kc.url(fmt.Sprintf("v1/producers/%s", topic))
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(data))

	if err != nil {
		return err
	}

	if cfg.IsAsync {
		kc.wg.Add(1)
		asynchronousPublish(context.Background(), kc, req, topic)
	} else {
		start := time.Now()
		err := synchronousPublish(ctx, kc, req)
		kc.latencyHistogram.Observe(float64(time.Since(start).Milliseconds()), topic, strconv.FormatBool(err != nil))
		if err != nil {
			return err
		}
	}
	return nil
}

func (kc *KafkaEventBus) Stop() {
	kc.wg.Wait()
}

func asynchronousPublish(ctx context.Context, kc *KafkaEventBus, req *http.Request, topic string) {
	safe.GoFunc(func() {
		defer kc.wg.Done()
		start := time.Now()

		resp, err := kc.http.Do(ctx, req)
		kc.latencyHistogram.Observe(float64(time.Since(start).Milliseconds()), topic, strconv.FormatBool(err != nil))
		if err != nil {
			logrus.Errorf("cannot publish to kafka %v : %v", topic, err)
			return
		}
		if err := resp.Body.Close(); err != nil {
			logrus.Errorf("cannot close body : %v", err)
			return
		}
	})
}

func synchronousPublish(ctx context.Context, kc *KafkaEventBus, req *http.Request) error {
	timeout, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	res, err := kc.http.Do(timeout, req)
	if err != nil {
		return err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusNoContent {
		return domain.ErrFailToPublishMessage
	}
	return nil
}

// @@no-locator-generation@@
func ProvideKafkaConnector(cfg KafkaConfig, http *httpclient.Client, meter metric.Meter) (*KafkaEventBus, func()) {
	latencyHistogram := meter.GetHistogram("kafka_connector_publish_latency_ms", "Latency for publish kafka message",
		metric.DefaultHistogramBucket, "topic", "is_error")
	kc := &KafkaEventBus{
		latencyHistogram: latencyHistogram,
		http:             http,
		cfg:              cfg,
	}
	return kc, func() { kc.Stop() }
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideStubKafkaConnector() *StubKafkaConnector {
	return &StubKafkaConnector{}
}

type StubKafkaConnector struct {
	recorderFn func(ctx context.Context, topic string, data []byte, opts ...domain.PublishOption)
	LogicFn    func(ctx context.Context, topic string, data []byte, opts ...domain.PublishOption) error
	Recorder   struct {
		Parameter struct {
			Ctx   context.Context
			Topic string
			Data  []byte
			Opts  []domain.PublishOption
		}
	}
}

// WithRecorder enables kafka stub to record parameters.
func (kc *StubKafkaConnector) WithRecorder() {
	kc.recorderFn = func(ctx context.Context, topic string, data []byte, opts ...domain.PublishOption) {
		kc.Recorder.Parameter = struct {
			Ctx   context.Context
			Topic string
			Data  []byte
			Opts  []domain.PublishOption
		}{
			Ctx:   ctx,
			Topic: topic,
			Data:  data,
			Opts:  opts,
		}
	}
}

func (kc *StubKafkaConnector) Publish(ctx context.Context, topic string, data []byte, opts ...domain.PublishOption) error {
	// If caller would like to assert parameters
	if kc.recorderFn != nil {
		kc.recorderFn(ctx, topic, data, opts...)
	}

	// If caller would like to control internal logic
	if kc.LogicFn != nil {
		return kc.LogicFn(ctx, topic, data, opts...)
	}

	// By default, always success
	return nil
}
