package infrastructure

import (
	"sync"
	"time"

	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	repUtils "git.wndv.co/lineman/absinthe/rep"
	"git.wndv.co/lineman/absinthe/rep/lineman_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

// REPConfig
type REPConfig struct {
	ConnectionURI string `envconfig:"REP_CONNECTION_URI"`
	Exchange      string `envconfig:"REP_EXCHANGE"`
	Enable        bool   `envconfig:"REP_ENABLE"`
}

func ProvideRepConfig() REPConfig {
	var cfg REPConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

var _ rep.REPService = &RepEventBus{}
var _ rep.REPService = &LazyRepEventBus{}

type RepEventBus struct {
	producer *repUtils.Producer
	cfg      REPConfig
	metrics  *repMetrics
	conn     *repUtils.Connection
}

func ProvideLazyRepEventBus(cfg REPConfig, meter metric.Meter) (*LazyRepEventBus, func()) {
	leb := NewLazyRepEventBus(cfg, meter)

	return leb, leb.stop
}

func ProvideRepEventBus(cfg REPConfig, meter metric.Meter) (*RepEventBus, func()) {
	eb := NewRepEventBus(cfg, meter)

	return eb, eb.stop
}

func NewRepEventBus(cfg REPConfig, meter metric.Meter) *RepEventBus {
	conn, err := repUtils.NewConnection(cfg.ConnectionURI)
	if err != nil {
		panic(err)
	}
	p, err := conn.ProvideProducer(cfg.Exchange)
	if err != nil {
		panic(err)
	}

	return &RepEventBus{
		conn:     conn,
		producer: p,
		cfg:      cfg,
		metrics:  newRepMetric(meter),
	}
}

func (re *RepEventBus) publish(routingKey, msgID, eventName, action string, payload []byte, location *lineman_rep.Location) error {
	if err := re.ensureProducer(); err != nil {
		return err
	}
	em := &lineman_rep.EventMessage{
		Id:            msgID,
		Timestamp:     time.Now().UnixNano(),
		SourceService: "delivery",
		Payload:       string(payload),
		EventName:     eventName,
		EventAction:   action,
		Location:      location,
	}

	begin := time.Now()
	err := re.producer.Publish(routingKey, em)
	if err != nil {
		re.metrics.publishCount.Inc("producer_publish_error", re.cfg.Exchange, routingKey)
		return err
	}
	re.metrics.publishCount.Inc("producer_publish", re.cfg.Exchange, routingKey)
	re.metrics.publishLatencyMs.Observe(float64(time.Since(begin).Milliseconds()), "producer_publish", re.cfg.Exchange, routingKey)

	return nil
}

func (re *RepEventBus) Publish(event rep.Event, payload rep.Payload, opts ...rep.Option) error {
	if !re.cfg.Enable {
		return nil
	}
	publishBegin := time.Now()
	var cfg = rep.DefaultConfig
	for _, o := range opts {
		cfg = o(cfg)
	}

	eventREP, ok := event.(rep.EventREP)
	if !ok {
		re.metrics.publishCount.Inc("publish_error_invalid_type", re.cfg.Exchange, "")
		return errors.New("event is not EventREP")
	}

	rawPayload, err := payload.Marshal()
	if err != nil {
		re.metrics.publishCount.Inc("publish_error_marshal", re.cfg.Exchange, eventREP.RoutingKey())
		return err
	}

	msgID := payload.GetID() + "-" + time.Now().Format(time.RFC3339Nano)

	err = re.publish(eventREP.RoutingKey(), msgID, eventREP.Name(), eventREP.Action(), rawPayload, cfg.Location())
	if err != nil {
		re.metrics.publishCount.Inc("publish_error", re.cfg.Exchange, eventREP.RoutingKey())
		logrus.Error("Publish Error: ", err)
		return nil
	}

	re.metrics.publishCount.Inc("publish", re.cfg.Exchange, eventREP.RoutingKey())
	re.metrics.publishLatencyMs.Observe(float64(time.Since(publishBegin).Milliseconds()), "publish", re.cfg.Exchange, eventREP.RoutingKey())

	return nil
}

func (re *RepEventBus) ensureProducer() (err error) {
	if !re.producer.IsClosed() {
		return nil
	}

	conn := re.producer.Connection()
	if conn.IsClosed() {
		begin := time.Now()
		conn, err = repUtils.NewConnection(re.cfg.ConnectionURI)
		if err != nil {
			re.metrics.publishCount.Inc("ensure_producer_new_conn_error", re.cfg.Exchange, "")
			return err
		}
		re.metrics.publishCount.Inc("ensure_producer_new_conn", re.cfg.Exchange, "")
		re.metrics.publishLatencyMs.Observe(float64(time.Since(begin).Milliseconds()), "ensure_producer_new_conn", re.cfg.Exchange, "")
	}

	begin := time.Now()
	re.producer, err = conn.ProvideProducer(re.cfg.Exchange)
	if err != nil {
		re.metrics.publishCount.Inc("ensure_producer_error", re.cfg.Exchange, "")
		return err
	}
	re.metrics.publishCount.Inc("ensure_producer", re.cfg.Exchange, "")
	re.metrics.publishLatencyMs.Observe(float64(time.Since(begin).Milliseconds()), "ensure_producer", re.cfg.Exchange, "")

	return nil
}

func (re *RepEventBus) stop() {
	if err := re.producer.Close(); err != nil {
		logrus.Errorf("cannot close rep producer : %v", err)
	}
	if err := re.conn.Close(); err != nil {
		logrus.Errorf("cannot close rep connection : %v", err)
	}
}

type repMetrics struct {
	publishCount     metric.Counter
	publishLatencyMs metric.Histogram
}

func newRepMetric(meter metric.Meter) *repMetrics {
	return &repMetrics{
		publishCount: meter.GetCounter("rep_count", "rep counter", "action", "exchange", "topic"),
		publishLatencyMs: meter.GetHistogram("rep_latency_ms", "histogram of publishing latency",
			metric.DefaultHistogramBucket, "action", "exchange", "topic"),
	}
}

type LazyRepEventBus struct {
	cfg    REPConfig
	target *RepEventBus
	lock   *sync.Mutex
	meter  metric.Meter
}

func NewLazyRepEventBus(cfg REPConfig, meter metric.Meter) *LazyRepEventBus {
	return &LazyRepEventBus{
		cfg:   cfg,
		meter: meter,
		lock:  &sync.Mutex{},
	}
}

func (l *LazyRepEventBus) Publish(event rep.Event, payload rep.Payload, opts ...rep.Option) error {
	if !l.cfg.Enable {
		return nil
	}
	if l.target == nil {
		l.init()
	}

	return l.target.Publish(event, payload, opts...)
}

func (l *LazyRepEventBus) init() {
	l.lock.Lock()
	defer l.lock.Unlock()

	if l.target != nil {
		return
	}

	l.target = NewRepEventBus(l.cfg, l.meter)
}

func (l *LazyRepEventBus) stop() {
	if l.target == nil {
		return
	}
	l.target.stop()
}
