package executor

//go:generate mockgen -source=./executor.go -destination=./mock_executor/mock_executor.go -package=mock_executor

import (
	"context"
	"sync"

	"github.com/sirupsen/logrus"
)

type Task func(ctx context.Context) error

type TaskExecutor interface {
	Start()
	Stop()
	Run(task Task) error
}

type LocalTaskExecutor struct {
	wg *sync.WaitGroup
}

func NewLocalTaskExecutor() *LocalTaskExecutor {
	return &LocalTaskExecutor{wg: new(sync.WaitGroup)}
}

func (le *LocalTaskExecutor) Start() {}

func (le *LocalTaskExecutor) Stop() {
	le.wg.Wait()
}

func (le *LocalTaskExecutor) Run(task Task) error {
	le.wg.Add(1)
	go func(wg *sync.WaitGroup, ctx context.Context, task Task) {
		defer wg.Done()
		defer func() {
			if err := recover(); err != nil {
				logrus.Errorf("executed task panic with error %v", err)
			}
		}()

		if err := task(ctx); err != nil {
			logrus.Errorf("executed task return error %v", err)
		}
	}(le.wg, context.Background(), task)
	return nil
}

func ProvideInfraLocalTaskExecutor() (*LocalTaskExecutor, func()) {
	exec := &LocalTaskExecutor{wg: new(sync.WaitGroup)}
	return exec, func() { exec.Stop() }
}
