// Code generated by MockGen. DO NOT EDIT.
// Source: ./duplicate_prevention_store.go

// Package mock_event is a generated GoMock package.
package mock_event

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockDuplicatePreventionStoreInterface is a mock of DuplicatePreventionStoreInterface interface.
type MockDuplicatePreventionStoreInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDuplicatePreventionStoreInterfaceMockRecorder
}

// MockDuplicatePreventionStoreInterfaceMockRecorder is the mock recorder for MockDuplicatePreventionStoreInterface.
type MockDuplicatePreventionStoreInterfaceMockRecorder struct {
	mock *MockDuplicatePreventionStoreInterface
}

// NewMockDuplicatePreventionStoreInterface creates a new mock instance.
func NewMockDuplicatePreventionStoreInterface(ctrl *gomock.Controller) *MockDuplicatePreventionStoreInterface {
	mock := &MockDuplicatePreventionStoreInterface{ctrl: ctrl}
	mock.recorder = &MockDuplicatePreventionStoreInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDuplicatePreventionStoreInterface) EXPECT() *MockDuplicatePreventionStoreInterfaceMockRecorder {
	return m.recorder
}

// Deduplicate mocks base method.
func (m *MockDuplicatePreventionStoreInterface) Deduplicate(ctx context.Context, payload model.OrderEventPayload, eventType string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Deduplicate", ctx, payload, eventType)
	ret0, _ := ret[0].(error)
	return ret0
}

// Deduplicate indicates an expected call of Deduplicate.
func (mr *MockDuplicatePreventionStoreInterfaceMockRecorder) Deduplicate(ctx, payload, eventType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Deduplicate", reflect.TypeOf((*MockDuplicatePreventionStoreInterface)(nil).Deduplicate), ctx, payload, eventType)
}
