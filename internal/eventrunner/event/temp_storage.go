package event

import (
	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"
)

// Do not use generics to reduce runtime reflection cost
type TempStorage struct {
	Messages  []sarama.ConsumerMessage
	FlushSize int
}

func (s *TempStorage) Save(payload sarama.ConsumerMessage) []sarama.ConsumerMessage {
	s.Messages = append(s.Messages, payload)
	if len(s.Messages) >= s.FlushSize {
		return s.Flush()
	}

	return []sarama.ConsumerMessage{}
}

func (s *TempStorage) Flush() []sarama.ConsumerMessage {
	if len(s.Messages) > 0 {
		logrus.Infof("flush %v message(s)", len(s.Messages))

		resources := make([]sarama.ConsumerMessage, len(s.Messages))
		copy(resources, s.Messages)

		s.Messages = []sarama.ConsumerMessage{}
		return resources
	}

	return []sarama.ConsumerMessage{}
}
