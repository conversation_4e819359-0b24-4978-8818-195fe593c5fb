package event_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/IBM/sarama"
	"github.com/alicebob/miniredis/v2"
	"github.com/golang/mock/gomock"
	"github.com/lovoo/goka"
	"github.com/lovoo/goka/tester"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/require"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/go/unleash/test"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverorderinfo/mock_driverorderinfo"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive/mock_incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mq"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event"
	"git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event/mock_event"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_unleash"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type (
	gokaCtx = goka.Context
	wrapper struct {
		gokaCtx
		context context.Context
	}
)

func (w wrapper) Context() context.Context {
	return w.context
}

var (
	inputTopic  goka.Stream = "input"
	outputTopic goka.Stream = "output"
	group       goka.Group  = "test-group"
	driverID                = "test-driver"
	driverID2               = "test-driver2"
)

func createOrderEvent(driverID string, eventType event.EventMessageType, isAutoAssign bool, createdAt time.Time, hasRained bool) event.DriverEventOrderModel {
	payload := event.OrderPayload{
		DriverID:     driverID,
		IsAutoAssign: isAutoAssign,
		CreatedAt:    createdAt,
		HasRained:    hasRained,
	}

	return createGenericEvent(driverID, eventType, payload)
}

func createGenericEvent(driverID string, eventType event.EventMessageType, payload interface{}) event.DriverEventOrderModel {
	payloadJson, _ := json.Marshal(payload)

	msg := event.DriverEventOrderModel{
		Event:   string(eventType),
		Payload: payloadJson,
	}

	return msg
}

func expectUpdateIncentiveProgress(t *testing.T, deps *testDriverOrderInfoProcDeps, ev event.DriverEventOrderModel) {
	if ev.Event != string(event.EventOrderComplete) {
		return
	}

	var payload model.OrderEventPayload
	require.NoError(t, json.Unmarshal(ev.Payload, &payload))

	if payload.DriverID == "" {
		return
	}

	driver := &model.Driver{DriverID: payload.DriverID, Region: "TEST_REGION"}
	deps.driverRepo.EXPECT().FindDriverID(gomock.Any(), payload.DriverID, gomock.Any()).Return(driver, nil)
	deps.incentiveRepo.EXPECT().GetActiveIncentiveByDriverAndTime(gomock.Any(), driver, payload.CreatedAt, gomock.Any()).Return([]incentive.Incentive{}, nil)
}

func TestDriverOrderInfoProcessor_CountCompletedOrderProcess(t *testing.T) {
	testcases := []struct {
		name              string
		events            []event.DriverEventOrderModel
		expectStat        map[string]int
		expectDeduplicate int
	}{
		{
			"when receive only order create event, then no driver stat count",
			[]event.DriverEventOrderModel{createOrderEvent(driverID, event.EventOrderCreate, false, timeutil.BangkokNow(), false)},
			nil,
			1,
		},
		{
			"when receive only order complete event without driver id, then no driver stat count",
			[]event.DriverEventOrderModel{createOrderEvent("", event.EventOrderComplete, false, timeutil.BangkokNow(), false)},
			nil,
			0,
		},
		{
			"when receive order complete event, then expect driver stat count is correct",
			[]event.DriverEventOrderModel{createOrderEvent(driverID, event.EventOrderComplete, false, timeutil.BangkokNow(), false)},
			map[string]int{driverID: 1},
			1,
		},
		{
			"when receive multiple order complete event, then expect driver stat count is correct",
			[]event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderComplete, false, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderComplete, false, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderComplete, false, timeutil.BangkokNow(), false),
			},
			map[string]int{driverID: 3},
			3,
		},
		{
			"when receive variant order event, then count only completed order event",
			[]event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderCreate, false, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderComplete, false, timeutil.BangkokNow(), false),
				createOrderEvent("", event.EventOrderComplete, false, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderComplete, false, timeutil.BangkokNow(), false),
				createOrderEvent(driverID2, event.EventOrderComplete, false, timeutil.BangkokNow(), false),
			},
			map[string]int{
				driverID:  2,
				driverID2: 1,
			},
			4,
		},
		{
			"when driver daily data older than 62 days, then delete that data",
			[]event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderComplete, false, timeutil.BangkokNow().Add(time.Hour*24*-(time.Duration(event.ProvideDriverStatisticConfig().DriverCleanUpDailyCount)+1)), false),
			},
			map[string]int{driverID: 1},
			1,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			s, err := miniredis.Run()
			if err != nil {
				panic(err)
			}
			defer s.Close()

			opts := &redis.Options{
				Addr: s.Addr(),
			}

			goka.SetTableSuffix("-table")

			cache := redis.NewClient(opts)
			doi, deps := newTestDriverOrderInfoProc(t, ctrl, cache)

			for _, ev := range tc.events {
				expectUpdateIncentiveProgress(t, &deps, ev)
			}

			deps.scProvider.EXPECT().SaramaConfig().Return(&sarama.Config{})
			deps.duplicatePreventionStore.EXPECT().Deduplicate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(tc.expectDeduplicate)
			for id, count := range tc.expectStat {
				deps.driverMarker.EXPECT().MarkDriverIDToBeUpdated(gomock.Any(), id).Return(nil).Times(count)
			}
			gkt := tester.New(tt)
			runner := event.NewProcessors()
			runner.Register(doi.NewDriverOrderInfoProcessor, goka.WithTester(gkt))
			runner.RunAll(context.Background())

			// Simulate multiple events firing
			for _, evt := range tc.events {
				gkt.Consume(string(inputTopic), "", &evt)
			}

			for id, count := range tc.expectStat {
				statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), id)
				stat, ok := statVal.(*event.DriverStatTable)
				require.True(tt, ok)
				require.Equal(tt, count, stat.MonthlyCounts[0].Completed)
				if len(stat.DailyCounts) != 0 {
					require.Equal(tt, count, stat.DailyCounts[0].Completed)
				}
			}
		})
	}
}

func TestDriverOrderInfoProcessor_CountAcceptedOrderProcess(t *testing.T) {
	expectDailyAcceptedCount := func(driverId string, autoAssignedAcceptedCount int, autoAssignedAcceptedDuringRainedCount int) func(*testing.T, *tester.Tester) {
		return func(tt *testing.T, gkt *tester.Tester) {
			statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), driverId)
			stat, ok := statVal.(*event.DriverStatTable)
			require.True(t, ok)
			require.Equal(t, autoAssignedAcceptedCount, stat.DailyCounts[0].AutoAssignedAccepted)
			require.Equal(t, autoAssignedAcceptedDuringRainedCount, stat.DailyCounts[0].AutoAssignedAcceptedRain)
		}
	}

	testcases := []struct {
		name                    string
		events                  []event.DriverEventOrderModel
		cfg                     event.DriverStatisticConfig
		expectStat              []func(*testing.T, *tester.Tester)
		expectMarkDriverIDCount map[string]int
		expectDeduplicate       int
	}{
		{
			"when receive only order accepted event without driver id, then no driver stat count",
			[]event.DriverEventOrderModel{createOrderEvent("", event.EventOrderAccept, false, timeutil.BangkokNow(), false)},
			event.ProvideDriverStatisticConfig(),
			nil,
			nil,
			0,
		},
		{
			"when receive order accepted event, then expect driver stat count is correct",
			[]event.DriverEventOrderModel{createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false)},
			event.ProvideDriverStatisticConfig(),
			[]func(*testing.T, *tester.Tester){
				expectDailyAcceptedCount(driverID, 1, 0),
			},
			map[string]int{driverID: 1},
			1,
		},
		{
			"when receive multiple order accepted event, then expect driver stat count is correct",
			[]event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
			},
			event.ProvideDriverStatisticConfig(),
			[]func(*testing.T, *tester.Tester){
				expectDailyAcceptedCount(driverID, 3, 0),
			},
			map[string]int{driverID: 3},
			3,
		},
		{
			"should dirty fix stat if INVALID_COUNTS_DIRTY_FIX_DELTA = 1",
			[]event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
			},
			event.ProvideDriverStatisticConfig(),
			[]func(*testing.T, *tester.Tester){
				expectDailyAcceptedCount(driverID, 2, 0),
			},
			map[string]int{driverID: 3},
			3,
		},
		{
			"when receive variant order event, then count only accepted order event",
			[]event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderCreate, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
				createOrderEvent("", event.EventOrderAccept, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID2, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
			},
			event.ProvideDriverStatisticConfig(),
			[]func(*testing.T, *tester.Tester){
				expectDailyAcceptedCount(driverID, 3, 0),
				expectDailyAcceptedCount(driverID2, 1, 0),
			},
			map[string]int{
				driverID:  3,
				driverID2: 1,
			},
			5,
		},
		{
			"when receive order accepted event with raining",
			[]event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), true),
				createOrderEvent(driverID, event.EventOrderAccept, true, timeutil.BangkokNow(), true),
			},
			event.ProvideDriverStatisticConfig(),
			[]func(*testing.T, *tester.Tester){
				expectDailyAcceptedCount(driverID, 3, 2),
			},
			map[string]int{
				driverID: 3,
			},
			3,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			s, err := miniredis.Run()
			if err != nil {
				panic(err)
			}
			defer s.Close()

			opts := &redis.Options{
				Addr: s.Addr(),
			}

			goka.SetTableSuffix("-table")

			cache := redis.NewClient(opts)
			doi, deps := newTestDriverOrderInfoProcWithCfg(t, ctrl, cache, tc.cfg)

			deps.scProvider.EXPECT().SaramaConfig().Return(&sarama.Config{})
			deps.duplicatePreventionStore.EXPECT().Deduplicate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(tc.expectDeduplicate)
			for id, count := range tc.expectMarkDriverIDCount {
				deps.driverMarker.EXPECT().MarkDriverIDToBeUpdated(gomock.Any(), id).Return(nil).Times(count)
			}

			gkt := tester.New(tt)
			runner := event.NewProcessors()
			runner.Register(doi.NewDriverOrderInfoProcessor, goka.WithTester(gkt))
			runner.RunAll(context.Background())

			// Simulate multiple events firing
			for _, evt := range tc.events {
				gkt.Consume(string(inputTopic), "", &evt)
			}

			for _, expectFunc := range tc.expectStat {
				expectFunc(tt, gkt)
			}
		})
	}
}

func TestDriverOrderInfoProcessor_CountAssignedOrderProcess(t *testing.T) {
	expectDailyAssignedCount := func(driverId string, autoAssignedCount int, AutoAssignedDuringRainedCount int) func(*testing.T, *tester.Tester) {
		return func(tt *testing.T, gkt *tester.Tester) {
			statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), driverId)
			stat, ok := statVal.(*event.DriverStatTable)
			require.True(t, ok)
			require.Equal(t, autoAssignedCount, stat.DailyCounts[0].AutoAssigned)
			require.Equal(t, AutoAssignedDuringRainedCount, stat.DailyCounts[0].AutoAssignedRain)
		}
	}

	testcases := []struct {
		name                    string
		events                  []event.DriverEventOrderModel
		cfg                     event.DriverStatisticConfig
		expectStat              []func(*testing.T, *tester.Tester)
		expectMarkDriverIDCount map[string]int
		expectDeduplicate       int
	}{{
		"when receive only order assigned event without driver id, then no driver stat count",
		[]event.DriverEventOrderModel{createOrderEvent("", event.EventOrderAssign, false, timeutil.BangkokNow(), false)},
		event.ProvideDriverStatisticConfig(),
		nil,
		nil,
		0,
	}, {
		"when receive order assigned event, then expect driver stat count is correct",
		[]event.DriverEventOrderModel{createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), false)},
		event.ProvideDriverStatisticConfig(),
		[]func(*testing.T, *tester.Tester){
			expectDailyAssignedCount(driverID, 1, 0),
		},
		map[string]int{driverID: 1},
		1,
	}, {
		"when receive multiple order assigned event, then expect driver stat count is correct",
		[]event.DriverEventOrderModel{
			createOrderEvent(driverID, event.EventOrderAssign, false, timeutil.BangkokNow(), false),
			createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), false),
			createOrderEvent(driverID, event.EventOrderAssign, false, timeutil.BangkokNow(), false),
		},
		event.ProvideDriverStatisticConfig(),
		[]func(*testing.T, *tester.Tester){
			expectDailyAssignedCount(driverID, 3, 0),
		},
		map[string]int{driverID: 3},
		3,
	}, {
		"when receive variant order event, then count only assigned order event",
		[]event.DriverEventOrderModel{
			createOrderEvent(driverID, event.EventOrderCreate, false, timeutil.BangkokNow(), false),
			createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), false),
			createOrderEvent(driverID, event.EventOrderAssign, false, timeutil.BangkokNow(), false),
			createOrderEvent("", event.EventOrderAssign, false, timeutil.BangkokNow(), false),
			createOrderEvent(driverID, event.EventOrderAssign, false, timeutil.BangkokNow(), false),
			createOrderEvent(driverID2, event.EventOrderAssign, true, timeutil.BangkokNow(), false),
		},
		event.ProvideDriverStatisticConfig(),
		[]func(*testing.T, *tester.Tester){
			expectDailyAssignedCount(driverID, 3, 0),
			expectDailyAssignedCount(driverID2, 1, 0),
		},
		map[string]int{
			driverID:  3,
			driverID2: 1,
		},
		5,
	}, {
		"when receive order assigned event with raining",
		[]event.DriverEventOrderModel{
			createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), false),
			createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), true),
			createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), true),
		},
		event.ProvideDriverStatisticConfig(),
		[]func(*testing.T, *tester.Tester){
			expectDailyAssignedCount(driverID, 3, 2),
		},
		map[string]int{
			driverID: 3,
		},
		3,
	}}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			s, err := miniredis.Run()
			if err != nil {
				panic(err)
			}
			defer s.Close()

			opts := &redis.Options{
				Addr: s.Addr(),
			}

			goka.SetTableSuffix("-table")

			cache := redis.NewClient(opts)
			doi, deps := newTestDriverOrderInfoProcWithCfg(t, ctrl, cache, tc.cfg)

			deps.scProvider.EXPECT().SaramaConfig().Return(&sarama.Config{})
			deps.duplicatePreventionStore.EXPECT().Deduplicate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(tc.expectDeduplicate)
			for id, count := range tc.expectMarkDriverIDCount {
				deps.driverMarker.EXPECT().MarkDriverIDToBeUpdated(gomock.Any(), id).Return(nil).Times(count)
			}

			gkt := tester.New(tt)
			runner := event.NewProcessors()
			runner.Register(doi.NewDriverOrderInfoProcessor, goka.WithTester(gkt))
			runner.RunAll(context.Background())

			// Simulate multiple events firing
			for _, evt := range tc.events {
				gkt.Consume(string(inputTopic), "", &evt)
			}

			for _, expectFunc := range tc.expectStat {
				expectFunc(tt, gkt)
			}
		})
	}
}

func TestDriverOrderInfoProcessor_CountCancelledOrderProcess(t *testing.T) {
	expectDailyCancelledCount := func(driverId string, cancelledNotFreeCount int, cancelledFreeCount int) func(*testing.T, *tester.Tester) {
		return func(tt *testing.T, gkt *tester.Tester) {
			statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), driverId)
			stat, ok := statVal.(*event.DriverStatTable)
			require.True(t, ok)
			require.Equal(t, cancelledNotFreeCount, stat.DailyCounts[0].CancelledNotFree)
			require.Equal(t, cancelledFreeCount, stat.DailyCounts[0].CancelledFree)
		}
	}

	createCancelOrderEvent := func(driverID string, isCancellationFree bool) event.DriverEventOrderModel {
		payload := event.CancelOrderPayload{
			OrderPayload: event.OrderPayload{
				DriverID:  driverID,
				CreatedAt: timeutil.BangkokNow(),
			},
			IsCancellationFree: isCancellationFree,
		}
		payloadJson, _ := json.Marshal(payload)

		msg := event.DriverEventOrderModel{
			Event:   string(event.EventOrderCancel),
			Payload: payloadJson,
		}

		return msg
	}

	testcases := []struct {
		name                    string
		events                  []event.DriverEventOrderModel
		expectStat              []func(*testing.T, *tester.Tester)
		expectMarkDriverIDCount map[string]int
		expectDeduplicate       int
	}{{
		"when receive only order cancelled event without driver id, then no driver stat count",
		[]event.DriverEventOrderModel{createCancelOrderEvent("", false)},
		nil,
		nil,
		0,
	}, {
		"when receive order cancelled event, then expect driver stat count is correct",
		[]event.DriverEventOrderModel{createCancelOrderEvent(driverID, true)},
		[]func(*testing.T, *tester.Tester){
			expectDailyCancelledCount(driverID, 0, 1),
		},
		map[string]int{driverID: 1},
		1,
	}, {
		"when receive multiple order cancelled event, then expect driver stat count is correct",
		[]event.DriverEventOrderModel{
			createCancelOrderEvent(driverID, false),
			createCancelOrderEvent(driverID, true),
			createCancelOrderEvent(driverID, false),
		},
		[]func(*testing.T, *tester.Tester){
			expectDailyCancelledCount(driverID, 2, 1),
		},
		map[string]int{driverID: 3},
		3,
	}, {
		"when receive variant order event, then count only cancelled order event",
		[]event.DriverEventOrderModel{
			createOrderEvent(driverID, event.EventOrderCreate, false, timeutil.BangkokNow(), false),
			createCancelOrderEvent(driverID, true),
			createCancelOrderEvent(driverID, false),
			createCancelOrderEvent("", false),
			createCancelOrderEvent(driverID, false),
			createCancelOrderEvent(driverID2, true),
		},
		[]func(*testing.T, *tester.Tester){
			expectDailyCancelledCount(driverID, 2, 1),
			expectDailyCancelledCount(driverID2, 0, 1),
		},
		map[string]int{
			driverID:  3,
			driverID2: 1,
		},
		5,
	}}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			s, err := miniredis.Run()
			if err != nil {
				panic(err)
			}
			defer s.Close()

			opts := &redis.Options{
				Addr: s.Addr(),
			}

			goka.SetTableSuffix("-table")

			cache := redis.NewClient(opts)
			doi, deps := newTestDriverOrderInfoProc(t, ctrl, cache)

			deps.scProvider.EXPECT().SaramaConfig().Return(&sarama.Config{})
			deps.duplicatePreventionStore.EXPECT().Deduplicate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(tc.expectDeduplicate)
			for id, count := range tc.expectMarkDriverIDCount {
				deps.driverMarker.EXPECT().MarkDriverIDToBeUpdated(gomock.Any(), id).Return(nil).Times(count)
			}

			gkt := tester.New(tt)
			runner := event.NewProcessors()
			runner.Register(doi.NewDriverOrderInfoProcessor, goka.WithTester(gkt))
			runner.RunAll(context.Background())

			// Simulate multiple events firing
			for _, evt := range tc.events {
				gkt.Consume(string(inputTopic), "", &evt)
			}

			for _, expectFunc := range tc.expectStat {
				expectFunc(tt, gkt)
			}
		})
	}
}

func TestProvideDriverOrderInfoProcessor_handleUnAssignEvent(t *testing.T) {
	expectDailyAssignedCount := func(driverId string, assignedCount, autoAssignedCount int, autoAssignedDuringRainedCount int) func(*testing.T, *tester.Tester) {
		return func(tt *testing.T, gkt *tester.Tester) {
			statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), driverId)
			stat, ok := statVal.(*event.DriverStatTable)
			require.True(t, ok)
			require.Equal(t, autoAssignedCount, stat.DailyCounts[0].AutoAssigned)
			require.Equal(t, autoAssignedDuringRainedCount, stat.DailyCounts[0].AutoAssignedRain)
		}
	}

	testcases := []struct {
		name                    string
		events                  []event.DriverEventOrderModel
		expectStat              []func(*testing.T, *tester.Tester)
		expectMarkDriverIDCount map[string]int
		expectDeduplicate       int
	}{
		{
			name:                    "when receive only order unassigned event without driver id, then no driver stat count",
			events:                  []event.DriverEventOrderModel{createOrderEvent("", event.EventOrderUnAssign, false, timeutil.BangkokNow(), false)},
			expectStat:              nil,
			expectMarkDriverIDCount: nil,
			expectDeduplicate:       0,
		},
		{
			name: "when receive order assigned and un assign event, then expect driver stat count is correct",
			events: []event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventOrderUnAssign, true, timeutil.BangkokNow(), false),
			},
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyAssignedCount(driverID, 0, 0, 0),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 2},
			expectDeduplicate:       2,
		},
		{
			name: "when receive order un assign event, then expect driver stat count is correct",
			events: []event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderUnAssign, true, timeutil.BangkokNow(), false),
			},
			expectStat:              nil,
			expectMarkDriverIDCount: map[string]int{driverID: 1},
			expectDeduplicate:       1,
		},
		{
			name: "when receive order un assign event with raining, then expect driver stat count is correct",
			events: []event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventOrderAssign, true, timeutil.BangkokNow(), true),
				createOrderEvent(driverID, event.EventOrderUnAssign, true, timeutil.BangkokNow(), true),
			},
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyAssignedCount(driverID, 0, 0, 0),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 2},
			expectDeduplicate:       2,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			s, err := miniredis.Run()
			if err != nil {
				panic(err)
			}
			defer s.Close()

			opts := &redis.Options{
				Addr: s.Addr(),
			}

			goka.SetTableSuffix("-table")

			cache := redis.NewClient(opts)
			doi, deps := newTestDriverOrderInfoProc(t, ctrl, cache)

			deps.scProvider.EXPECT().SaramaConfig().Return(&sarama.Config{})
			deps.duplicatePreventionStore.EXPECT().Deduplicate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(tc.expectDeduplicate)
			for id, count := range tc.expectMarkDriverIDCount {
				deps.driverMarker.EXPECT().MarkDriverIDToBeUpdated(gomock.Any(), id).Return(nil).Times(count)
			}

			gkt := tester.New(tt)
			runner := event.NewProcessors()
			runner.Register(doi.NewDriverOrderInfoProcessor, goka.WithTester(gkt))
			runner.RunAll(context.Background())

			// Simulate multiple events firing
			for _, evt := range tc.events {
				gkt.Consume(string(inputTopic), "", &evt)
			}

			for _, expectFunc := range tc.expectStat {
				expectFunc(tt, gkt)
			}
		})
	}
}

func TestProvideDriverOrderInfoProcessor_handleVelocityOverLimitEvent(t *testing.T) {
	expectVelocityOverLimitCount := func(driverId string, velocityOverLimit int) func(*testing.T, *tester.Tester) {
		return func(tt *testing.T, gkt *tester.Tester) {
			statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), driverId)
			stat, ok := statVal.(*event.DriverStatTable)
			require.True(t, ok)
			require.Equal(t, velocityOverLimit, stat.DailyCounts[0].VelocityOverLimit)
		}
	}

	testcases := []struct {
		name                    string
		events                  []event.DriverEventOrderModel
		expectStat              []func(*testing.T, *tester.Tester)
		expectMarkDriverIDCount map[string]int
		expectDeduplicate       int
	}{
		{
			name:                    "when receive velocity over limit event without driver id, then no driver stat count",
			events:                  []event.DriverEventOrderModel{createOrderEvent("", event.EventVelocityOverLimit, false, timeutil.BangkokNow(), false)},
			expectStat:              nil,
			expectMarkDriverIDCount: nil,
			expectDeduplicate:       0,
		},
		{
			name: "when receive velocity over limit event, then expect driver stat count is correct",
			events: []event.DriverEventOrderModel{
				createOrderEvent(driverID, event.EventVelocityOverLimit, false, timeutil.BangkokNow(), false),
				createOrderEvent(driverID, event.EventVelocityOverLimit, false, timeutil.BangkokNow(), false),
			},
			expectStat: []func(*testing.T, *tester.Tester){
				expectVelocityOverLimitCount(driverID, 2),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 2},
			expectDeduplicate:       0,
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			s, err := miniredis.Run()
			if err != nil {
				panic(err)
			}
			defer s.Close()

			opts := &redis.Options{
				Addr: s.Addr(),
			}

			goka.SetTableSuffix("-table")

			cache := redis.NewClient(opts)
			doi, deps := newTestDriverOrderInfoProc(t, ctrl, cache)

			deps.scProvider.EXPECT().SaramaConfig().Return(&sarama.Config{})
			deps.duplicatePreventionStore.EXPECT().Deduplicate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(tc.expectDeduplicate)
			for id, count := range tc.expectMarkDriverIDCount {
				deps.driverMarker.EXPECT().MarkDriverIDToBeUpdated(gomock.Any(), id).Return(nil).Times(count)
			}

			gkt := tester.New(tt)
			runner := event.NewProcessors()
			runner.Register(doi.NewDriverOrderInfoProcessor, goka.WithTester(gkt))
			runner.RunAll(context.Background())

			// Simulate multiple events firing
			for _, evt := range tc.events {
				gkt.Consume(string(inputTopic), "", &evt)
			}

			for _, expectFunc := range tc.expectStat {
				expectFunc(tt, gkt)
			}
		})
	}
}

func TestProvideDriverOrderInfoProcessor_handleStatsResetEvent(t *testing.T) {
	expectDailyCount := func(driverId string, autoAssignedAccepted, aaAcceptedRain, autoAssigned, aaRain, cancelledNotFree, accepted int) func(*testing.T, *tester.Tester) {
		return func(tt *testing.T, gkt *tester.Tester) {
			statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), driverId)
			stat, ok := statVal.(*event.DriverStatTable)
			require.True(t, ok)
			require.Equal(t, autoAssignedAccepted, stat.DailyCounts[0].AutoAssignedAccepted)
			require.Equal(t, aaAcceptedRain, stat.DailyCounts[0].AutoAssignedAcceptedRain)
			require.Equal(t, autoAssigned, stat.DailyCounts[0].AutoAssigned)
			require.Equal(t, aaRain, stat.DailyCounts[0].AutoAssignedRain)

			require.Equal(t, cancelledNotFree, stat.DailyCounts[0].CancelledNotFree)
		}
	}

	expectByservice := func(driverId string, service model.Service, autoAssignedAccepted, aaAcceptedRain int) func(*testing.T, *tester.Tester) {
		return func(tt *testing.T, gkt *tester.Tester) {
			statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), driverId)
			stat, ok := statVal.(*event.DriverStatTable)
			require.True(t, ok)
			require.Equal(t, autoAssignedAccepted, stat.DailyCounts[0].ByServices[service].AutoAssignedAccepted)
			require.Equal(t, aaAcceptedRain, stat.DailyCounts[0].ByServices[service].AutoAssignedAcceptedRain)
		}
	}

	intPtr := func(i int) *int { return &i }

	testcases := []struct {
		name                    string
		events                  []event.DriverEventOrderModel
		expectStat              []func(*testing.T, *tester.Tester)
		expectMarkDriverIDCount map[string]int
	}{
		{
			name: "when receive stats reset event, then expect driver stat should reset",
			events: []event.DriverEventOrderModel{
				createGenericEvent(driverID, event.EventCountsSet, model.CountsSetOrderPayload{
					DriverID: driverID,
					DailyCounts: []model.DailyCount{
						{
							Year:                     timeutil.BangkokNow().Year(),
							Month:                    timeutil.BangkokNow().Month(),
							Day:                      timeutil.BangkokNow().Day(),
							AutoAssignedAccepted:     99,
							AutoAssignedAcceptedRain: 99,
							AutoAssigned:             99,
							AutoAssignedRain:         99,
						},
					},
				}),
				createGenericEvent(driverID, event.EventStatsReset, model.StatsResetEventPayload{
					DriverID:                 driverID,
					Date:                     time.Date(timeutil.BangkokNow().Year(), timeutil.BangkokNow().Month(), timeutil.BangkokNow().Day(), 20, 0, 0, 0, timeutils.BangkokLocation()),
					AutoAssignedAccepted:     intPtr(1),
					AutoAssignedAcceptedRain: intPtr(1),
					AutoAssigned:             intPtr(1),
					AutoAssignedRain:         intPtr(1),
					CancelledNotFree:         intPtr(1),
				}),
			},
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyCount(driverID, 1, 1, 1, 1, 1, 1),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 2},
		},
		{
			name: "when receive stats reset event, then expect driver stat should reset by service",
			events: []event.DriverEventOrderModel{
				createGenericEvent(driverID, event.EventCountsSet, model.CountsSetOrderPayload{
					DriverID: driverID,
					DailyCounts: []model.DailyCount{
						{
							Year:                     timeutil.BangkokNow().Year(),
							Month:                    timeutil.BangkokNow().Month(),
							Day:                      timeutil.BangkokNow().Day(),
							AutoAssignedAccepted:     99,
							AutoAssignedAcceptedRain: 99,
							AutoAssigned:             99,
							AutoAssignedRain:         99,
						},
					},
				}),
				createGenericEvent(driverID, event.EventStatsReset, model.StatsResetEventPayload{
					DriverID:                 driverID,
					Date:                     time.Date(timeutil.BangkokNow().Year(), timeutil.BangkokNow().Month(), timeutil.BangkokNow().Day(), 20, 0, 0, 0, timeutils.BangkokLocation()),
					AutoAssignedAccepted:     intPtr(33),
					AutoAssignedAcceptedRain: intPtr(33),
					Service:                  model.ServiceFood,
				}),
			},
			expectStat: []func(*testing.T, *tester.Tester){
				expectByservice(driverID, model.ServiceFood, 33, 33),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 2},
		},
		{
			name: "when receive stats reset event but that not found daily count for that driver, then expect driver stat shouldn't reset",
			events: []event.DriverEventOrderModel{
				createGenericEvent(driverID, event.EventCountsSet, model.CountsSetOrderPayload{
					DriverID: driverID,
					DailyCounts: []model.DailyCount{
						{
							Year:                 timeutil.BangkokNow().Year(),
							Month:                timeutil.BangkokNow().Month(),
							Day:                  timeutil.BangkokNow().Day() - 1,
							AutoAssignedAccepted: 99,
							AutoAssigned:         99,
							CancelledNotFree:     99,
						},
					},
				}),
				createGenericEvent(driverID, event.EventStatsReset, model.StatsResetEventPayload{
					DriverID:             driverID,
					Date:                 time.Date(timeutil.BangkokNow().Year(), timeutil.BangkokNow().Month(), timeutil.BangkokNow().Day(), 20, 0, 0, 0, timeutils.BangkokLocation()),
					AutoAssignedAccepted: intPtr(1),
					AutoAssigned:         intPtr(1),
					CancelledNotFree:     intPtr(1),
				}),
			},
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyCount(driverID, 99, 0, 99, 0, 99, 99),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 1},
		},
		{
			name: "when receive stats reset event with upsert=true, then expect driver stat should be set even the daily count doesn't exist",
			events: []event.DriverEventOrderModel{
				createGenericEvent(driverID, event.EventStatsReset, model.StatsResetEventPayload{
					DriverID:             driverID,
					Date:                 time.Date(timeutil.BangkokNow().Year(), timeutil.BangkokNow().Month(), timeutil.BangkokNow().Day(), 20, 0, 0, 0, timeutils.BangkokLocation()),
					AutoAssignedAccepted: intPtr(99),
					AutoAssigned:         intPtr(99),
					CancelledNotFree:     intPtr(99),
					Upsert:               true,
				}),
			},
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyCount(driverID, 99, 0, 99, 0, 99, 99),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 1},
		},
		{
			name: "when receive stats reset event but sent invalid date, then expect driver stat shouldn't reset",
			events: []event.DriverEventOrderModel{
				createGenericEvent(driverID, event.EventCountsSet, model.CountsSetOrderPayload{
					DriverID: driverID,
					DailyCounts: []model.DailyCount{
						{
							Year:                 timeutil.BangkokNow().Year(),
							Month:                timeutil.BangkokNow().Month(),
							Day:                  timeutil.BangkokNow().Day(),
							AutoAssignedAccepted: 99,
							AutoAssigned:         99,
							CancelledNotFree:     99,
						},
					},
				}),
				createGenericEvent(driverID, event.EventStatsReset, model.StatsResetEventPayload{
					DriverID:             driverID,
					Date:                 time.Time{},
					AutoAssignedAccepted: intPtr(1),
					AutoAssigned:         intPtr(1),
					CancelledNotFree:     intPtr(1),
				}),
			},
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyCount(driverID, 99, 0, 99, 0, 99, 99),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 1},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			s, err := miniredis.Run()
			if err != nil {
				panic(err)
			}
			defer s.Close()

			opts := &redis.Options{
				Addr: s.Addr(),
			}

			goka.SetTableSuffix("-table")

			cache := redis.NewClient(opts)
			doi, deps := newTestDriverOrderInfoProc(t, ctrl, cache)

			deps.scProvider.EXPECT().SaramaConfig().Return(&sarama.Config{})
			for id, count := range tc.expectMarkDriverIDCount {
				deps.driverMarker.EXPECT().MarkDriverIDToBeUpdated(gomock.Any(), id).Return(nil).Times(count)
			}

			gkt := tester.New(tt)
			runner := event.NewProcessors()
			runner.Register(doi.NewDriverOrderInfoProcessor, goka.WithTester(gkt))
			runner.RunAll(context.Background())

			// Simulate multiple events firing
			for _, evt := range tc.events {
				gkt.Consume(string(inputTopic), "", &evt)
			}

			for _, expectFunc := range tc.expectStat {
				expectFunc(tt, gkt)
			}
		})
	}
}

func TestProvideDriverOrderInfoProcessor_exceedARAlertLog(t *testing.T) {
	now := timeutil.BangkokNow()
	expectDailyCount := func(driverId string, autoAssignedAccepted, aaAcceptedRain, autoAssigned, aaRain, cancelledNotFree, accepted int) func(*testing.T, *tester.Tester) {
		return func(tt *testing.T, gkt *tester.Tester) {
			statVal := gkt.TableValue(goka.Table(fmt.Sprintf("%s-table", group)), driverId)
			stat, ok := statVal.(*event.DriverStatTable)
			require.True(t, ok)
			require.Equal(t, autoAssignedAccepted, stat.DailyCounts[0].AutoAssignedAccepted)
			require.Equal(t, aaAcceptedRain, stat.DailyCounts[0].AutoAssignedAcceptedRain)
			require.Equal(t, autoAssigned, stat.DailyCounts[0].AutoAssigned)
			require.Equal(t, aaRain, stat.DailyCounts[0].AutoAssignedRain)

			require.Equal(t, cancelledNotFree, stat.DailyCounts[0].CancelledNotFree)
		}
	}

	exceedARScenarios := map[string][]event.DriverEventOrderModel{
		"assign_unassign_accept": {
			createGenericEvent(driverID, event.EventOrderAssign, model.OrderEventPayload{
				DriverID:     driverID,
				OrderID:      "order-a",
				Region:       "BKK",
				IsAutoAssign: true,
				ServiceType:  model.ServiceFood,
				CreatedAt:    now.Add(-3 * time.Minute),
			}),
			createGenericEvent(driverID, event.EventOrderUnAssign, model.OrderEventPayload{
				DriverID:     driverID,
				OrderID:      "order-a",
				Region:       "BKK",
				IsAutoAssign: true,
				ServiceType:  model.ServiceFood,
				CreatedAt:    now.Add(-2 * time.Minute),
			}),
			createGenericEvent(driverID, event.EventOrderAccept, model.OrderEventPayload{
				DriverID:     driverID,
				OrderID:      "order-a",
				Region:       "BKK",
				IsAutoAssign: true,
				ServiceType:  model.ServiceFood,
				CreatedAt:    now.Add(-1 * time.Minute),
			}),
		},
	}

	testcases := []struct {
		name             string
		events           []event.DriverEventOrderModel
		setupFeatureFlag func(unleash *test.SimpleUnleasher)

		expectDeduplicate       int
		expectStat              []func(*testing.T, *tester.Tester)
		expectMarkDriverIDCount map[string]int
		expectLogWrite          func(t *testing.T, b *bytes.Buffer)
	}{
		{
			name:   `feature flag is on and should log alert is on - assign_unassign_accept`,
			events: exceedARScenarios["assign_unassign_accept"],
			setupFeatureFlag: func(unleash *test.SimpleUnleasher) {
				unleash.SetEnabled(featureflag.IsExceedArLogAlertEnabled.Name, true)
				unleash.SetVariantWithPayloadValue(featureflag.IsExceedArLogAlertEnabled.Name, `{"shouldAlert":true}`)
			},
			expectDeduplicate: 3,
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyCount(
					driverID,
					1,
					0,
					0,
					0,
					0,
					1,
				),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 3},
			expectLogWrite: func(t *testing.T, b *bytes.Buffer) {
				require.Contains(t, b.String(), "AR exceeded")
				require.Contains(t, b.String(), `"should_alert":true`)
			},
		},
		{
			name:   `feature flag is on and should log alert is off - assign_unassign_accept`,
			events: exceedARScenarios["assign_unassign_accept"],
			setupFeatureFlag: func(unleash *test.SimpleUnleasher) {
				unleash.SetEnabled(featureflag.IsExceedArLogAlertEnabled.Name, true)
				unleash.SetVariantWithPayloadValue(featureflag.IsExceedArLogAlertEnabled.Name, `{"shouldAlert":false}`)
			},
			expectDeduplicate: 3,
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyCount(
					driverID,
					1,
					0,
					0,
					0,
					0,
					1,
				),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 3},
			expectLogWrite: func(t *testing.T, b *bytes.Buffer) {
				require.Contains(t, b.String(), "AR exceeded")
				require.Contains(t, b.String(), `"should_alert":false`)
			},
		},
		{
			name:   `feature flag is off and shouldn't log alert - assign_unassign_accept`,
			events: exceedARScenarios["assign_unassign_accept"],
			setupFeatureFlag: func(unleash *test.SimpleUnleasher) {
				unleash.SetEnabled(featureflag.IsExceedArLogAlertEnabled.Name, false)
			},
			expectDeduplicate: 3,
			expectStat: []func(*testing.T, *tester.Tester){
				expectDailyCount(
					driverID,
					1,
					0,
					0,
					0,
					0,
					1,
				),
			},
			expectMarkDriverIDCount: map[string]int{driverID: 3},
			expectLogWrite: func(t *testing.T, b *bytes.Buffer) {
				require.NotContains(t, b.String(), "AR exceeded")
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			ctrl := gomock.NewController(tt)
			defer ctrl.Finish()

			s, err := miniredis.Run()
			if err != nil {
				panic(err)
			}
			defer s.Close()

			opts := &redis.Options{
				Addr: s.Addr(),
			}

			goka.SetTableSuffix("-table")

			cache := redis.NewClient(opts)
			doi, deps := newTestDriverOrderInfoProc(t, ctrl, cache)

			deps.duplicatePreventionStore.EXPECT().Deduplicate(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).Times(tc.expectDeduplicate)
			tc.setupFeatureFlag(deps.fakeUnleash)

			deps.scProvider.EXPECT().SaramaConfig().Return(&sarama.Config{})
			for id, count := range tc.expectMarkDriverIDCount {
				deps.driverMarker.EXPECT().MarkDriverIDToBeUpdated(gomock.Any(), id).Return(nil).Times(count)
			}

			gkt := tester.New(tt)
			runner := event.NewProcessors()
			ctx, wg := safe.CreateWaitGroupOnContext(context.Background())
			runner.Register(doi.NewDriverOrderInfoProcessor, goka.WithTester(gkt), goka.WithContextWrapper(func(gctx goka.Context) goka.Context {
				return wrapper{gokaCtx: gctx, context: ctx}
			}))
			runner.RunAll(context.Background())

			// Simulate multiple events firing
			for _, evt := range tc.events {
				gkt.Consume(string(inputTopic), "", &evt)
			}

			wg.Wait()

			for _, expectFunc := range tc.expectStat {
				expectFunc(tt, gkt)
			}

			tc.expectLogWrite(t, deps.logxByteBuffer)
		})
	}
}

type testDriverOrderInfoProcDeps struct {
	driverMarker             *mock_event.MockDriverMarkable
	driverRepo               *mock_repository.MockDriverRepository
	incentiveRepo            *mock_incentive.MockIncentiveRepository
	incentiveProgressRepo    *mock_incentive.MockIncentiveProgressRepository
	duplicatePreventionStore *mock_event.MockDuplicatePreventionStoreInterface
	doiApi                   *mock_driverorderinfo.MockDriverOrderInfoAPIInterface
	scProvider               *mock_event.MockSaramaConfigProvider
	driverStatisticConfig    event.DriverStatisticConfig
	fakeUnleash              *test.SimpleUnleasher
	logxByteBuffer           *bytes.Buffer
}

func newTestDriverOrderInfoProc(t *testing.T, ctrl *gomock.Controller, cache datastore.RedisClient) (*event.DriverOrderInfoProcessor, testDriverOrderInfoProcDeps) {
	return newTestDriverOrderInfoProcWithCfg(t, ctrl, cache, event.ProvideDriverStatisticConfig())
}

func newTestDriverOrderInfoProcWithCfg(t *testing.T, ctrl *gomock.Controller, cache datastore.RedisClient, driverStatCfg event.DriverStatisticConfig) (*event.DriverOrderInfoProcessor, testDriverOrderInfoProcDeps) {
	driverMarker := mock_event.NewMockDriverMarkable(ctrl)
	duplicatePreventionStore := mock_event.NewMockDuplicatePreventionStoreInterface(ctrl)
	driverRepo := mock_repository.NewMockDriverRepository(ctrl)
	incentiveRepo := mock_incentive.NewMockIncentiveRepository(ctrl)
	incentiveProgressRepo := mock_incentive.NewMockIncentiveProgressRepository(ctrl)
	doiApi := mock_driverorderinfo.NewMockDriverOrderInfoAPIInterface(ctrl)
	scProvider := mock_event.NewMockSaramaConfigProvider(ctrl)
	fakeUnleash := test.ProvideSimpleUnleasher()
	unleashAdmin := mock_unleash.NewMockAdmin(ctrl)
	b := bytes.Buffer{}
	logx := logx.NewLogx(logx.WithWriter(&b))
	exceedArDetector := event.NewExceedARDetector(logx)
	cfg := mq.KafkaConsumerConfig{
		BootstrapServers: "",
		ClientId:         "",
		GroupId:          "",
		DriverStatGroup:  string(group),
		SaslUsername:     "",
		SaslPassword:     "",
		Topics:           []string{string(inputTopic)},
		PollingTimeout:   0,
		FlushSize:        0,
		DriverStatTopic:  string(outputTopic),
	}
	t.Cleanup(ctrl.Finish)
	deps := testDriverOrderInfoProcDeps{
		driverMarker:             driverMarker,
		duplicatePreventionStore: duplicatePreventionStore,
		driverRepo:               driverRepo,
		incentiveRepo:            incentiveRepo,
		incentiveProgressRepo:    incentiveProgressRepo,
		scProvider:               scProvider,
		driverStatisticConfig:    driverStatCfg,
		fakeUnleash:              fakeUnleash,
		logxByteBuffer:           &b,
	}

	return event.ProvideDriverOrderInfoProcessor(driverMarker, duplicatePreventionStore, cache, driverRepo, incentiveRepo, incentiveProgressRepo, cfg, doiApi, event.NewAtomicDriverStatisticConfig(driverStatCfg), scProvider, featureflag.NewFeatureFlagService(fakeUnleash, unleashAdmin), exceedArDetector), deps
}
