package event

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"time"

	"github.com/IBM/sarama"

	"git.wndv.co/go/kafc"
	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverorderinfo"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/heatmap"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/srvarea"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mq"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

type EventPollerV2 struct {
	apiServiceArea     *srvarea.ServiceAreaSettingAPI
	apiDriverOrderInfo *driverorderinfo.DriverOrderInfoAPI
	apiHeatMap         *heatmap.API

	events    chan *sarama.ConsumerMessage
	consumer  kafc.ConsumerGroup
	storage   *TempStorage
	timeoutMs int

	done chan struct{}
}

// handleMsg is the entrypoint for each event
func (c *EventPollerV2) handleMsg(_ context.Context, msg interface{}) error {
	if msg == nil {
		return errors.New("unexpected nil message from kafc.ConsumerGroup")
	}

	switch event := msg.(type) {
	case *sarama.ConsumerMessage:
		c.events <- event

	case *sarama.ConsumerError:
		logx.Warn().Msgf("Kafka event type is error!: %v", event)

	default:
		logx.Warn().Msgf("Unexpected sarama types: %v", reflect.TypeOf(msg).String())
	}

	return nil
}

func (c *EventPollerV2) Start() {
	ctx, cancel := context.WithCancel(context.Background())

	// start kafka consumer to subscribe message and push to events buffer
	safe.GoFunc(func() {
		defer func() {
			close(c.events)
			cancel() // stop poller
		}()
		logx.Info().Msg("Consumer poller is ready")
		if err := c.consumer.OnMessage(c.handleMsg); err != nil {
			panic(fmt.Errorf("consumer error: %w", err))
		}
	})

	// start polling events
	safe.GoFunc(func() {
		logx.Info().Msg("Consumer poller is polling")
		c.Poll(ctx)
	})
}

func (c *EventPollerV2) Poll(ctx context.Context) {
	defer close(c.done)
	for {
		select {
		case <-ctx.Done():
			logx.Info().Msg("Got exit signal, draining message and flushing storage")
			if e, ok := <-c.events; ok {
				logx.Info().Msg("Draining messages")
				c.Feed(e)
			}

			logx.Info().Msg("Flushing storage")
			c.Feed(nil)
			return

		// Process data
		case event := <-c.events:
			c.Feed(event)

		// Poll timeout - reset every time the for loop continues
		case <-time.NewTimer(time.Millisecond * time.Duration(c.timeoutMs)).C:
			logx.Info().Msgf("Kafka poll the consumer timeout in %d ms. Storage will flush immediately", c.timeoutMs)
			c.Feed(nil)
		}
	}
}

func (c *EventPollerV2) Feed(eventMsg *sarama.ConsumerMessage) {
	var messages []sarama.ConsumerMessage

	if eventMsg == nil {
		messages = c.storage.Flush()
	} else {
		messages = c.storage.Save(*eventMsg)
	}

	if len(messages) == 0 {
		return
	}

	c.ToObserver(messages)
}

func (c *EventPollerV2) ToObserver(messages []sarama.ConsumerMessage) {
	logx.Info().Msgf("processing %d events", len(messages))

	var hm HeatMapObserver
	hm.pushMessages(messages)

	if len(hm.GetPayload()) > 0 {
		_, err := c.apiHeatMap.CalculateHeatMap(hm.GetPayload())
		if err != nil {
			logx.Warn().Msgf("CalculateHeatMap error: %v", err)
		}
	}

	var doi DriverOrderInfoObserver
	if doi.pushMessages(messages) {
		for _, m := range doi.GetPayload() {
			doi, err := c.apiDriverOrderInfo.UpdateDriverOrderInfo(context.Background(), m.OrderEventPayload, m.EventTime)
			if err != nil {
				logx.Warn().Msgf("UpdateDriverOrderInfo error: %v", err)
				continue
			}

			serviceArea, err := c.apiServiceArea.AreaRepo.GetByRegion(context.Background(), m.Region)
			if err != nil {
				safe.SentryErrorMessage(fmt.Sprintf("kafka consumer can't get service area: %v", err))
				continue
			}

			if err := c.apiDriverOrderInfo.BanDriverCompleteOrderTooOften(doi, serviceArea.MessengerCompletedOrdersLimit); err != nil {
				logx.Warn().Msgf("BanDriverHighCompleteOrder error. driver: %v, err: %v", doi.DriverID, err)
			}

			if err := c.apiDriverOrderInfo.BanDriverCompleteOrderTooFar(doi); err != nil {
				logx.Warn().Msgf("BanDriverCompleteOrderTooFar error. driver: %v, err: %v", doi.DriverID, err)
			}
		}
	}
}

func (c *EventPollerV2) Shutdown() {
	if err := c.consumer.Close(); err != nil {
		logx.Error().Err(err).Msg("error closing consumer")
	}
	<-c.done // wait draining event
}

func (c *EventPollerV2) Done() <-chan struct{} {
	return c.done
}

// @@no-locator-generation@@
func ProvidePoller(
	kc *mq.KafkaConsumer,
	apiServiceArea *srvarea.ServiceAreaSettingAPI,
	apiDriverOrderInfo *driverorderinfo.DriverOrderInfoAPI,
	apiHeatMap *heatmap.API,
) (*EventPollerV2, func()) {
	logx.Info().Msg("Creating consumer poller")
	consumer, cleanup := kc.InitKafc()
	poller := &EventPollerV2{
		apiServiceArea:     apiServiceArea,
		apiDriverOrderInfo: apiDriverOrderInfo,
		apiHeatMap:         apiHeatMap,
		events:             make(chan *sarama.ConsumerMessage),
		consumer:           consumer,
		storage: &TempStorage{
			FlushSize: kc.Config.FlushSize,
		},
		timeoutMs: kc.Config.PollingTimeout,
		done:      make(chan struct{}),
	}
	return poller, func() {
		poller.Shutdown() // stop consumer group and poller
		cleanup()         // stop kafc client
	}
}
