package event_test

import (
	"context"
	"testing"

	"github.com/lovoo/goka"
	"github.com/lovoo/goka/tester"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event"
	"git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event/goka_tester"
)

func TestProcessors_RunAll(t *testing.T) {
	t.<PERSON>llel()

	var (
		inputTopic  goka.Stream = "input"
		outputTopic goka.Stream = "output"
	)

	t.Run("multiple simple in-out edges processors", func(t *testing.T) {
		runner := event.NewProcessors()

		gkt1 := tester.New(t)
		procCreator1 := func(...goka.ProcessorOption) *goka.Processor {
			return goka_tester.CreateTestProcessor(gkt1, "test_group1", goka_tester.CreateSimpleInOutEdges(inputTopic, outputTopic)...)
		}

		gkt2 := tester.New(t)
		procCreator2 := func(...goka.ProcessorOption) *goka.Processor {
			return goka_tester.CreateTestProcessor(gkt2, "test_group2", goka_tester.CreateSimpleInOutEdges(inputTopic, outputTopic)...)
		}

		runner.Register(procCreator1)
		runner.Register(procCreator2)
		runner.RunAll(context.Background())

		goka_tester.EmitAndExpect(t, gkt1, inputTopic, outputTopic)
		goka_tester.EmitAndExpect(t, gkt2, inputTopic, outputTopic)
	})

	t.Run("silently recover if the edge panics", func(t *testing.T) {
		runner := event.NewProcessors()

		gkt := tester.New(t)
		procCreator := func(...goka.ProcessorOption) *goka.Processor {
			return goka_tester.CreateTestProcessor(gkt, "test_group1", goka_tester.CreatePanicEdges()...)
		}

		runner.Register(procCreator)
		require.NotPanics(t, func() {
			runner.RunAll(context.Background())
		})
	})
}
