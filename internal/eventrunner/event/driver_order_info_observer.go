package event

import (
	"encoding/json"
	"time"

	"github.com/IBM/sarama"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type Location struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type DriverOrderEventPayload struct {
	EventTime time.Time
	model.OrderEventPayload
}

type DriverOrderInfoObserver struct {
	payloads []DriverOrderEventPayload
}

func (doi *DriverOrderInfoObserver) pushMessages(messages []sarama.ConsumerMessage) bool {
	for i := range messages {
		message := &messages[i]

		var m DriverEventOrderModel
		if err := json.Unmarshal(message.Value, &m); err != nil {
			logrus.Error("Skip this message because the message is invalid type:", err)
			continue
		}

		switch EventMessageType(m.Event) {
		case EventOrderComplete:
			doi.update(m, model.StatusCompleted)

		case EventOrderCancel:
			doi.update(m, model.StatusCanceled)
		}
	}

	return len(doi.payloads) > 0
}

func (doi *DriverOrderInfoObserver) GetPayload() []DriverOrderEventPayload {
	return doi.payloads
}

func (doi *DriverOrderInfoObserver) update(deo DriverEventOrderModel, status model.Status) {
	var oc DriverOrderEventPayload
	err := json.Unmarshal(deo.Payload, &oc)
	if err != nil {
		logrus.Warn("DriverOrderInfoObserver Could not unmarshal payload:", err)
	}
	oc.Status = status
	oc.EventTime = deo.EventTime
	doi.payloads = append(doi.payloads, oc)
}
