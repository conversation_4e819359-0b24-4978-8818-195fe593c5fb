package event

import (
	"encoding/json"
	"testing"
	"time"

	"github.com/IBM/sarama"
	"github.com/stretchr/testify/require"
)

func createOrderCreatePayload(lat float64, lng float64, region string) OrderPayload {
	return OrderPayload{
		Location: Location{
			Lat: lat,
			Lng: lng,
		},
		CreatedAt: time.Now(),
		Region:    region,
	}
}

func TestHeatMapObserver_SetPayload(t *testing.T) {
	t.Run("should map OrderCancelPayload payload collectly", func(tt *testing.T) {
		// arrange
		hmo := HeatMapObserver{}
		ocp := createOrderCreatePayload(100.3, 13.2, "BANGKOK")
		raw, _ := json.Marshal(&ocp)
		message := DriverEventOrderModel{
			Event:   string(EventOrderCreate),
			Payload: raw,
		}
		// act
		hmo.update(message)
		// assert
		require.Equal(t, 1, len(hmo.GetPayload()))
		require.Equal(t, "BANGKOK", hmo.GetPayload()[0].Region)
		require.Equal(t, 13.2, hmo.GetPayload()[0].Location.Lng)
		require.Equal(t, 100.3, hmo.GetPayload()[0].Location.Lat)
	})
}

func TestHeatMapObserver_Update(t *testing.T) {
	t.Run("should set all message to payload collectly", func(tt *testing.T) {
		// arrange
		observer := HeatMapObserver{}
		ocp := createOrderCreatePayload(100.3, 13.2, "BANGKOK")
		ocp2 := createOrderCreatePayload(99.3, 14.2, "NONTHABURI")
		raw, _ := json.Marshal(&ocp)
		raw2, _ := json.Marshal(&ocp2)
		msg1 := DriverEventOrderModel{Event: string(EventOrderCreate), Payload: raw}
		msg2 := DriverEventOrderModel{Event: string(EventOrderCreate), Payload: raw2}

		// act
		jsonByte1, err1 := json.Marshal(msg1)
		jsonByte2, err2 := json.Marshal(msg2)
		observer.pushMessages([]sarama.ConsumerMessage{
			{
				Value: jsonByte1,
			}, {
				Value: jsonByte2,
			},
		})

		// assert
		require.NoError(t, err1)
		require.NoError(t, err2)
		require.Equal(t, 2, len(observer.GetPayload()))
		require.Equal(t, "BANGKOK", observer.GetPayload()[0].Region)
		require.Equal(t, 13.2, observer.GetPayload()[0].Location.Lng)
		require.Equal(t, 100.3, observer.GetPayload()[0].Location.Lat)
		require.Equal(t, "NONTHABURI", observer.GetPayload()[1].Region)
		require.Equal(t, 14.2, observer.GetPayload()[1].Location.Lng)
		require.Equal(t, 99.3, observer.GetPayload()[1].Location.Lat)
	})
}
