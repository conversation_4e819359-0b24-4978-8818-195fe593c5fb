package httpcsvwriter

import (
	"encoding/csv"
	"fmt"
	"net/http"
)

var _ HttpCSVWriter = (*HttpChunkedWriter)(nil)

type HttpChunkedWriter struct {
	encoderType EncoderType
}

func NewChunkedWrite(encoderType EncoderType) *HttpChunkedWriter {
	return &HttpChunkedWriter{encoderType: encoderType}
}

// streamWrite implements csvWriter
func (hcw *HttpChunkedWriter) StreamWrite(respWriter http.ResponseWriter, filename string, streamFn streamDataFn) error {
	w := csv.NewWriter(respWriter)
	encoder := encoderFactory(hcw.encoderType, w)

	respWriter.Header().Set("Content-Type", "text/csv")
	respWriter.Header().Set("Content-Disposition", fmt.Sprintf("attachment;filename=%s", filename))

	for u := range streamFn() {
		if err := encoder.Encode(u); err != nil {
			return fmt.Errorf("encode err: %w", err)
		}
		w.Flush() // flush each record
	}

	w.Flush() // make sure no any buffer left

	if err := w.Error(); err != nil {
		return fmt.Errorf("write err: %w", err)
	}

	return nil
}
