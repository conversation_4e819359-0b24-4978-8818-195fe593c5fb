package cryptography

import (
	"crypto/aes"
	"crypto/cipher"
	"fmt"

	"github.com/pkg/errors"
)

type AesGCM struct {
	GcmStandardNonceSize,
	GcmKeySize int
}

func (a *AesGCM) Decrypt(cipherText []byte, key []byte, iv []byte, additionalData []byte) (plainText []byte, err error) {
	if len(key) < a.GcmKeySize {
		return nil, errors.New("key size not match: key should larger than 32 bytes")
	}
	if len(iv) < a.GcmStandardNonceSize {
		return nil, errors.New("iv size not match: iv should larger than 12 bytes")
	}
	block, err := aes.NewCipher(key[:a.GcmKeySize])
	if err != nil {
		return nil, err
	}
	aesGCM, err := cipher.NewGCMWithNonceSize(block, a.GcmStandardNonceSize)
	if err != nil {
		return nil, err
	}
	plainText, err = aesGCM.Open(nil, iv[:a.GcmStandardNonceSize], cipherText, additionalData)
	if err != nil {
		return nil, err
	}
	return plainText, nil
}

func (a *AesGCM) Encrypt(plainText []byte, key []byte, iv []byte, additionalData []byte) (cipherText []byte, err error) {
	if len(key) < a.GcmKeySize {
		return nil, fmt.Errorf("key size not match: key should larger than %d bytes", a.GcmKeySize)
	}
	if len(iv) < a.GcmStandardNonceSize {
		return nil, fmt.Errorf("iv size not match: iv should larger than %d bytes", a.GcmStandardNonceSize)
	}
	block, err := aes.NewCipher(key[:a.GcmKeySize])
	if err != nil {
		return nil, err
	}
	aesGCM, err := cipher.NewGCMWithNonceSize(block, a.GcmStandardNonceSize)
	if err != nil {
		return nil, err
	}
	cipherText = aesGCM.Seal(nil, iv[:a.GcmStandardNonceSize], plainText, additionalData)
	return cipherText, nil
}
