package auth

//go:generate mockgen -source=./token_store.go -destination=./mock_auth/mock_token_store.go -package=mock_auth

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
)

type TokenProps struct {
	// AccessToken is a generated access token that want to store.
	AccessToken string

	// DriverID is driver id.
	DriverID string

	// DriverStatus is driver status.
	DriverStatus string

	// BanUntil is time that system ban.
	BanUntil time.Time

	// Expire is duration that token expire.
	Expire time.Duration
}

// TokenStorage ...
type TokenStorage interface {
	// Store persist token into the storage.
	Store(ctx context.Context, props TokenProps) error
}

// RedisTokenStorage is token storage with Redis backend.
type RedisTokenStorage struct {
	client datastore.RedisClient
}

func (store *RedisTokenStorage) Store(ctx context.Context, props TokenProps) error {
	key := tokenKey(props.AccessToken, props.DriverID)
	val := map[string]interface{}{
		"driver_id": props.DriverID,
		"status":    props.DriverStatus,
	}
	if !props.BanUntil.IsZero() {
		val["banned_until"] = props.BanUntil.Unix()
	}

	err := store.client.HMSet(ctx, key, val).Err()
	if err != nil {
		logrus.Error("cannot persist token: ", err)
		return err
	}
	return store.client.Expire(ctx, key, props.Expire).Err()
}

// ProvideRedisTokenStore construct *RedisTokenStorage.
func ProvideRedisTokenStore(client datastore.RedisClient) *RedisTokenStorage {
	return &RedisTokenStorage{
		client: client,
	}
}

var _ TokenStorage = (*RedisTokenStorage)(nil)

func tokenKey(token, driverID string) string {
	return fmt.Sprintf("%s:%s", token, driverID)
}
