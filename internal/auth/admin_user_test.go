package auth_test

import (
	"fmt"
	"net/http/httptest"
	"testing"

	"github.com/dgrijalva/jwt-go"
	"github.com/gin-gonic/gin"
	"github.com/icrowley/fake"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestExtractAdminUser(t *testing.T) {
	t.Parallel()

	var (
		id = fake.Characters()
	)

	t.Run("extract an admin token correctly", func(tt *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		setClaims(ctx.GinCtx(), jwt.MapClaims{
			"id": id,
		})

		adminUser, _ := auth.ExtractAdminUser(ctx.GinCtx())

		assert.Equal(tt, id, adminUser.GetId())
	})

	t.Run("return an error if admin token is invalid", func(tt *testing.T) {
		ctx := testutil.NewContextWithRecorder()

		adminUser, err := auth.ExtractAdminUser(ctx.GinCtx())

		assert.Error(tt, err)
		assert.Equal(tt, "", adminUser.GetId())
	})

	t.Run("zero admin user if no claim data", func(tt *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		setClaims(ctx.GinCtx(), jwt.MapClaims{})

		adminUser, err := auth.ExtractAdminUser(ctx.GinCtx())

		assert.NoError(tt, err)
		assert.Equal(tt, "", adminUser.GetId())
		assert.Len(tt, adminUser.GetPermission().GetElements(), 0)
	})

	t.Run("extract permission correctly", func(tt *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		setClaims(ctx.GinCtx(), jwt.MapClaims{
			"id":          id,
			"permissions": "ROLE_A, ROLE_B ,ROLE_C ",
		})

		adminUser, err := auth.ExtractAdminUser(ctx.GinCtx())

		assert.NoError(tt, err)
		assert.Equal(tt, id, adminUser.GetId())
		assert.ElementsMatch(tt, []string{"ROLE_A", "ROLE_B", "ROLE_C"}, adminUser.GetPermission().GetElements())
	})
}

func TestAdminUser_CanViewCriminalCheckStatus(t *testing.T) {
	email := "<EMAIL>"
	t.Run("return true if user has permission", func(tt *testing.T) {
		adminUser := auth.NewAdminUser(fake.Characters(), fake.Characters(), email, types.NewStringSet(auth.ViewDriverCriminalStatus))
		require.True(tt, adminUser.CanViewCriminalCheckStatus())
	})

	t.Run("return false if user doesn't has permission", func(tt *testing.T) {
		adminUser := auth.NewAdminUser(fake.Characters(), fake.Characters(), email, types.NewStringSet())
		require.False(tt, adminUser.CanViewCriminalCheckStatus())
	})
}

func setClaims(ctx *gin.Context, claims jwt.MapClaims) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	tokenString, _ := token.SignedString([]byte(""))
	req := httptest.NewRequest("GET", "/test", nil)
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", tokenString))

	ctx.Request = req
}
