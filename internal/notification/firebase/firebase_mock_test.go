// Code generated by MockGen. DO NOT EDIT.
// Source: ./firebase.go

// Package firebase is a generated GoMock package.
package firebase

import (
	context "context"
	reflect "reflect"

	messaging "firebase.google.com/go/v4/messaging"
	gomock "github.com/golang/mock/gomock"
)

// MockFBMessagingClient is a mock of FBMessagingClient interface.
type MockFBMessagingClient struct {
	ctrl     *gomock.Controller
	recorder *MockFBMessagingClientMockRecorder
}

// MockFBMessagingClientMockRecorder is the mock recorder for MockFBMessagingClient.
type MockFBMessagingClientMockRecorder struct {
	mock *MockFBMessagingClient
}

// NewMockFBMessagingClient creates a new mock instance.
func NewMockFBMessagingClient(ctrl *gomock.Controller) *MockFBMessagingClient {
	mock := &MockFBMessagingClient{ctrl: ctrl}
	mock.recorder = &MockFBMessagingClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFBMessagingClient) EXPECT() *MockFBMessagingClientMockRecorder {
	return m.recorder
}

// SendEach mocks base method.
func (m *MockFBMessagingClient) SendEach(arg0 context.Context, arg1 []*messaging.Message) (*messaging.BatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEach", arg0, arg1)
	ret0, _ := ret[0].(*messaging.BatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendEach indicates an expected call of SendEach.
func (mr *MockFBMessagingClientMockRecorder) SendEach(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEach", reflect.TypeOf((*MockFBMessagingClient)(nil).SendEach), arg0, arg1)
}

// SendEachForMulticast mocks base method.
func (m *MockFBMessagingClient) SendEachForMulticast(arg0 context.Context, arg1 *messaging.MulticastMessage) (*messaging.BatchResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendEachForMulticast", arg0, arg1)
	ret0, _ := ret[0].(*messaging.BatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// SendEachForMulticast indicates an expected call of SendEachForMulticast.
func (mr *MockFBMessagingClientMockRecorder) SendEachForMulticast(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendEachForMulticast", reflect.TypeOf((*MockFBMessagingClient)(nil).SendEachForMulticast), arg0, arg1)
}
