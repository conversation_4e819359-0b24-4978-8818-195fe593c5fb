package stub

import (
	"context"
	"fmt"

	"firebase.google.com/go/v4/messaging"
)

type ConsoleLogMessagingClient struct {
}

func (s *ConsoleLogMessagingClient) SendEachForMulticast(ctx context.Context, message *messaging.MulticastMessage) (*messaging.BatchResponse, error) {
	fmt.Printf("[FBSTUB] sending message: %v\n", message)
	return nil, nil
}

func (s *ConsoleLogMessagingClient) SendEach(ctx context.Context, message []*messaging.Message) (*messaging.BatchResponse, error) {
	fmt.Printf("[FBSTUB] sending message: %v\n", message)
	return nil, nil
}

func ProvideConsoleFBMessagingClient() *ConsoleLogMessagingClient {
	return &ConsoleLogMessagingClient{}
}
