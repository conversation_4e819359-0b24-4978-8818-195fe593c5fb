package socketio

import (
	"context"
	"encoding/json"
	"io"
	"net/http"

	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/httpheader"
)

type SendAllRequest struct {
	Messages []*Message `json:"messages"`
}

// Config for socket.io.
type Config struct {
	Host string `envconfig:"SOCKET_IO_BRIDGE_HOST"`
}

// ProvideConfig load config from environment variable.
func ProvideConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}

type client struct {
	cfg        Config
	httpClient *httpclient.Client
}

func (sc client) SendAll(ctx context.Context, messages []*Message) error {
	h := make(http.Header)
	httpheader.AddNotificationHttpHeader(ctx, h)

	resp, err := sc.httpClient.Post(
		ctx,
		sc.cfg.Host+"/v1/room/unique",
		h,
		httpclient.JSON(SendAllRequest{Messages: messages}))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		return apiError(resp.Body)
	}

	return nil
}

func (sc client) SendMulticast(ctx context.Context, msg *MulticastMessage) error {
	h := make(http.Header)
	httpheader.AddNotificationHttpHeader(ctx, h)

	resp, err := sc.httpClient.Post(
		ctx,
		sc.cfg.Host+"/v1/room",
		h,
		httpclient.JSON(msg))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusNoContent {
		return apiError(resp.Body)
	}

	return nil
}

func apiError(dec io.ReadCloser) error {
	var b []byte
	if _, err := dec.Read(b); err != nil {
		return err
	}
	defer dec.Close()

	if !json.Valid(b) {
		return errors.New(string(b))
	}

	var apiErr api.Error
	if err := json.Unmarshal(b, &apiErr); err != nil {
		return err
	}
	return &apiErr
}

func ProvideSocketIOClient(cfg Config, httpClient *httpclient.Client) Client {
	return &client{
		cfg:        cfg,
		httpClient: httpClient,
	}
}
