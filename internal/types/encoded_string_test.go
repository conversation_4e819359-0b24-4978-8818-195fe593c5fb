package types

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestURLEncodedString_String(t *testing.T) {
	tests := []struct {
		value    string
		expected string
	}{
		{"", ""},
		{"Bangkok", "Bangkok"},
		{"Krung Thep", "Krung Thep"},
		{"Krung+Thep", "Krung Thep"},
		{"Krung+Thep+%E0%B8%81%E0%B8%A3%E0%B8%B8%E0%B8%87%E0%B9%80%E0%B8%97%E0%B8%9E", "Krung Thep กรุงเทพ"},
		{"Krung+Thep %E0%B8%81%E0%B8%A3%E0%B8%B8%E0%B8%87%E0%B9%80%E0%B8%97%E0%B8%9E", "Krung Thep กรุงเทพ"},
		{"Krung+Thep %E0%B8%81%E0%B8%A3%E0%B8%B8%E0%B8%87%E0%B9%80%E0%B8%97%E0%B8%9E", "Krung Thep กรุงเทพ"},
		{"%E0%B8%81%20%E0%B8%82", "ก ข"},
		{"กรุงเทพ", "กรุงเทพ"},
		{"ซอย <PERSON>", "ซอย John"},
		{"เมือง พัทยา", "เมือง พัทยา"},
		{"ซอย 10 สุขุมวิท", "ซอย 10 สุขุมวิท"},

		//invalid case
		{"%%", "%%"},
		{"%E", "%E"},
		{"AB%E", "AB%E"},
		{"%ZAB", "%ZAB"},
	}
	for _, test := range tests {
		t.Run(fmt.Sprintf("%s -> %s", test.value, test.expected), func(t *testing.T) {
			s := URLEncodedString(test.value)
			assert.Equal(t, test.expected, s.String())
		})
	}
}
