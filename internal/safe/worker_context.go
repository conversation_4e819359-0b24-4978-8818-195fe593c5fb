package safe

import (
	"context"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/lib/cleanup"
)

/*
How to:
us in the following order
1. implement *WorkerContext dependency in any deps as you needed.
2. call `*WorkerContext.NewContext()` to create new context.
3. Those context will need to listen to `<-yourCtx.Done()` channel.
4. When the context signal to `<-yourCtx.Done()`, You will needed to call *WorkerContext.ContextDone(yourCtx)
5. In case `yourCtx` is not done, There will be a timer to ignore the job completely and will exit the wait process.
*/

//go:generate mockgen -source=./worker_context.go -destination=./mock_safe/mock_worker_context.go -package=mock_safe

type WorkerContext interface {
	CtxCount() int
	NewContext() *ContextWithID
	NewContextWithSameWaitGroup(wgCtx context.Context) *ContextWithID
	ContextDone(ctx context.Context)
	Shutdown() error
	Done() bool
}

var (
	WorkerContextShutdownTimeoutExceed = errors.New("some worker contexts are still working and not yet finished")
)

type WorkerContextConfig struct {
	ShutDownWaitTimeout time.Duration `envconfig:"WORKER_CONTEXT_SHUTDOWN_WAIT_TIMEOUT" default:"1m"`
}

func ProvideWorkerContextConfig() *WorkerContextConfig {
	cfg := new(WorkerContextConfig)
	envconfig.MustProcess("", cfg)
	return cfg
}

type WorkerContextImpl struct {
	cfg *WorkerContextConfig

	wg         sync.WaitGroup
	mu         sync.RWMutex
	runningCtx map[string]workerContext
	done       bool
}

var _ WorkerContext = (*WorkerContextImpl)(nil)

type ContextWithID struct {
	context.Context

	id string
}

type workerContext struct {
	id string

	ctx context.Context
}

func ProvideWorkerContext(cfg *WorkerContextConfig, cleanupPriority cleanup.CleanupPriority) WorkerContext {
	if cfg == nil {
		cfg = &WorkerContextConfig{
			ShutDownWaitTimeout: 1 * time.Minute,
		}
	}

	wkCtx := &WorkerContextImpl{
		cfg:        cfg,
		runningCtx: make(map[string]workerContext),
	}

	cleanupPriority.Add(cleanup.CleanUpFirstPriority, func() { wkCtx.Shutdown() })

	return wkCtx
}

func (wkCtx *WorkerContextImpl) CtxCount() int {
	return len(wkCtx.runningCtx)
}

func (wkCtx *WorkerContextImpl) Shutdown() error {
	logrus.Infof("WorkerContext.Shutdown: have %d running workers", wkCtx.CtxCount())
	wkCtx.mu.Lock()
	wkCtx.done = true
	wkCtx.mu.Unlock()

	waitChan := make(chan struct{})
	GoFunc(func() {
		wkCtx.wg.Wait()
		waitChan <- struct{}{}
		close(waitChan)
	})
	timeout := time.NewTimer(wkCtx.cfg.ShutDownWaitTimeout)
	select {
	case <-waitChan:
		logrus.Info("all worker contexts finished")
		return nil
	case <-timeout.C:
		logrus.Info("worker contexts shutdown timeout")
		return WorkerContextShutdownTimeoutExceed
	}
}

func (wkCtx *WorkerContextImpl) NewContext() *ContextWithID {
	bgCtx := context.Background()
	id, _ := uuid.NewRandom()

	wkCtx.mu.Lock()
	defer wkCtx.mu.Unlock()
	wkCtx.runningCtx[id.String()] = workerContext{
		id:  id.String(),
		ctx: bgCtx,
	}

	wkCtx.wg.Add(1)
	return &ContextWithID{
		id:      id.String(),
		Context: bgCtx,
	}
}

func (wkCtx *WorkerContextImpl) NewContextWithSameWaitGroup(wgCtx context.Context) *ContextWithID {
	ctxWithWg := NewContextWithSameWaitGroup(wgCtx)
	id, _ := uuid.NewRandom()

	wkCtx.mu.Lock()
	defer wkCtx.mu.Unlock()
	wkCtx.runningCtx[id.String()] = workerContext{
		id:  id.String(),
		ctx: ctxWithWg,
	}

	wkCtx.wg.Add(1)
	return &ContextWithID{
		id:      id.String(),
		Context: ctxWithWg,
	}
}

func (wkCtx *WorkerContextImpl) ContextDone(ctx context.Context) {
	ctxWithID, ok := ctx.(*ContextWithID)
	if !ok {
		return
	}

	wkCtx.mu.Lock()
	defer wkCtx.mu.Unlock()
	delete(wkCtx.runningCtx, ctxWithID.id)

	wkCtx.wg.Done()
}

func (wkCtx *WorkerContextImpl) Done() bool {
	return wkCtx.done
}
