package utils

import (
	"time"

	"git.wndv.co/lineman/absinthe/api"
	absintheUtils "git.wndv.co/lineman/absinthe/utils"
)

// ErrorStructResponse return error response format by get from request validation error
func ErrorStructResponse(err error) *api.Error {
	return &api.Error{
		Code:      api.ERRCODE_INVALID_REQUEST,
		Message:   "Invalid request",
		Info:      absintheUtils.ErrorStruct(err),
		Timestamp: api.Timestamp(time.Now().UTC()),
	}
}

func ErrorInternalStructResponse(err error) *api.Error {
	return &api.Error{
		Code:      api.ERRCODE_INTERNAL_ERROR,
		Message:   err.Error(),
		Info:      absintheUtils.ErrorStruct(err),
		Timestamp: api.Timestamp(time.Now().UTC()),
	}
}
