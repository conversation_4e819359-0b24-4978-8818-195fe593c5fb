package utils_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/types/known/wrapperspb"

	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

func Test_ProtoDoubleConversion(t *testing.T) {
	tests := []struct {
		name     string
		input    float64
		expected *wrapperspb.DoubleValue
	}{
		{
			name:     "ValidInput",
			input:    123.45,
			expected: &wrapperspb.DoubleValue{Value: 123.45},
		},
		{
			name:     "ZeroInput",
			input:    0.0,
			expected: &wrapperspb.DoubleValue{Value: 0.0},
		},
		{
			name:     "NegativeInput",
			input:    -123.45,
			expected: &wrapperspb.DoubleValue{Value: -123.45},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := utils.Float64ToProtoDouble(tt.input)
			assert.Equal(t, tt.expected, result)
		})
	}
}
