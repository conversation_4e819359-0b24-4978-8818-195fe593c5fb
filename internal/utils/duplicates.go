package utils

import "git.wndv.co/lineman/fleet-distribution/internal/types"

// Duplicates returns duplicated value as slice of repeated duplicate values.
// It returns nil if no duplicates are found.
//
// If values = [1,1,2,3,4,5,4,4], then Duplicates returns [1,4,4]
func Duplicates[T comparable](values ...T) []T {
	seen := types.NewSet[T]()
	var duplicates []T = nil

	for i := range values {
		value := values[i]
		if seen.Contains(value) {
			duplicates = append(duplicates, value)
			continue
		}

		seen.Add(value)
	}

	return duplicates
}

// Duplicates returns duplicated value as set.
//
// If values = [1,1,2,3,4,5,4], then Duplicates returns [1,4]
func DuplicatesSet[T comparable](values ...T) types.Set[T] {
	seen := types.NewSet[T]()
	var duplicates types.Set[T] // nil

	for i := range values {
		value := values[i]
		if seen.Contains(value) {
			if duplicates == nil {
				duplicates = types.NewSet[T]()
			}

			duplicates.Add(value)
			continue
		}

		seen.Add(value)
	}

	return duplicates
}
