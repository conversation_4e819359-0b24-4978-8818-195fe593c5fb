package bulkutil

import (
	"fmt"
	"strings"
)

type ColumnErrorMapper map[int]*ColumnErrorResult

func NewColumnErrorResult(header string, value string, columnErrors []string) ColumnErrorResult {
	return ColumnErrorResult{
		Header:       header,
		Value:        value,
		ColumnErrors: columnErrors,
	}
}

type RowErrorDetail struct {
	Index             int
	ColumnErrorMapper ColumnErrorMapper
}

func (red *RowErrorDetail) AddErrors(columnIndex int, errs ...ColumnErrorResult) {
	if len(errs) == 0 {
		return
	}
	if red.ColumnErrorMapper == nil {
		red.ColumnErrorMapper = make(ColumnErrorMapper)
	}
	srcColumnErrorResult, ok := red.ColumnErrorMapper[columnIndex]
	if !ok {
		srcColumnErrorResult = &ColumnErrorResult{Header: errs[0].Header, Value: errs[0].Value}
		red.ColumnErrorMapper[columnIndex] = srcColumnErrorResult
	}
	for _, err := range errs {
		srcColumnErrorResult.AppendErrors(err.ColumnErrors)
	}
}

func (red *RowErrorDetail) Append(src RowErrorDetail) {
	if src.IsEmpty() {
		return
	}
	for srcColumnIndex, srcColumnErrorResult := range src.ColumnErrorMapper {
		selfColumnErrorResult, ok := red.ColumnErrorMapper[srcColumnIndex]
		if !ok {
			if red.ColumnErrorMapper == nil {
				red.ColumnErrorMapper = make(ColumnErrorMapper)
			}
			red.ColumnErrorMapper[srcColumnIndex] = srcColumnErrorResult
			continue
		}
		selfColumnErrorResult.AppendErrors(srcColumnErrorResult.ColumnErrors)
	}
}

func (red *RowErrorDetail) IsEmpty() bool {
	return red.CountErrorColumn() == 0
}

func (red RowErrorDetail) CountErrorColumn() int {
	var errorRowCounter int
	for _, item := range red.ColumnErrorMapper {
		errorRowCounter += len(item.ColumnErrors)
	}
	return errorRowCounter
}

type ColumnErrorResult struct {
	Header       string
	Value        string
	ColumnErrors []string
}

func (cer *ColumnErrorResult) AppendErrors(strErrs []string) {
	cer.ColumnErrors = append(cer.ColumnErrors, strErrs...)
}

type ErrorReport struct {
	Row   int    `json:"row"`
	ID    string `json:"id"`
	Error string `json:"error"`
}

type ErrorReporter struct {
	RowErrorDetailMapper map[int]*RowErrorDetail
	RowCount             int
	ColumnCount          int
}

func (errorReporter ErrorReporter) ConvertToReport() []BulkRespInfo {
	errorReports := make([]BulkRespInfo, 0)
	for r := 0; r < errorReporter.RowCount; r++ {
		if rowErr, ok := errorReporter.RowErrorDetailMapper[r]; ok {
			if !rowErr.IsEmpty() {
				var strErrors []string
				for c := 0; c < errorReporter.ColumnCount; c++ {
					if colErr, ok := rowErr.ColumnErrorMapper[c]; ok {
						for _, errItem := range colErr.ColumnErrors {
							strErrors = append(strErrors, fmt.Sprintf("%s in column \"%s\"", errItem, colErr.Header))
						}
					}
				}
				errorReports = append(errorReports, BulkRespInfo{
					ID:      fmt.Sprint(r + 1),
					Message: strings.Join(strErrors, ", "),
				})
			}
		}
	}
	return errorReports
}

func (errorReporter *ErrorReporter) AddRowError(rowIndex int, rowErr RowErrorDetail) {
	if errorReporter.RowErrorDetailMapper == nil {
		errorReporter.RowErrorDetailMapper = make(map[int]*RowErrorDetail)
	}
	existedRowErr, ok := errorReporter.RowErrorDetailMapper[rowIndex]
	if !ok {
		errorReporter.RowErrorDetailMapper[rowIndex] = &rowErr
		return
	}
	for k, v := range rowErr.ColumnErrorMapper {
		existedColumn, ok := existedRowErr.ColumnErrorMapper[k]
		if !ok {
			existedRowErr.ColumnErrorMapper[k] = v
			continue
		}
		existedColumn.AppendErrors(v.ColumnErrors)
	}
}

func (errorReporter ErrorReporter) IsEmpty() bool {
	return errorReporter.CountErrorRow() == 0
}

func (errorReporter ErrorReporter) CountErrorRow() int {
	var errorCounter int
	for _, item := range errorReporter.RowErrorDetailMapper {
		if len(item.ColumnErrorMapper) > 0 {
			errorCounter++
		}
	}
	return errorCounter
}

func (errorReporter *ErrorReporter) CreateRowErrorDetailIfNotExisted() {
	if errorReporter.RowErrorDetailMapper == nil {
		errorReporter.RowErrorDetailMapper = make(map[int]*RowErrorDetail)
	}
}

func (errorReporter *ErrorReporter) AddErrors(rowIndex int, columnIndex int, errs ...ColumnErrorResult) {
	if len(errs) == 0 {
		return
	}
	errorReporter.CreateRowErrorDetailIfNotExisted()
	targetRowErrorDetail, ok := errorReporter.RowErrorDetailMapper[rowIndex]
	if !ok {
		targetRowErrorDetail = &RowErrorDetail{}
		errorReporter.RowErrorDetailMapper[rowIndex] = targetRowErrorDetail
	}
	targetRowErrorDetail.AddErrors(columnIndex, errs...)
}

func (errorReporter *ErrorReporter) Append(src ErrorReporter) {
	if src.IsEmpty() {
		return
	}

	errorReporter.CreateRowErrorDetailIfNotExisted()
	src.CreateRowErrorDetailIfNotExisted()

	for srcRowIndex, srcRowErrorDetail := range src.RowErrorDetailMapper {
		selfRowErrorDetail, ok := errorReporter.RowErrorDetailMapper[srcRowIndex]
		if !ok {
			errorReporter.RowErrorDetailMapper[srcRowIndex] = srcRowErrorDetail
			continue
		}
		selfRowErrorDetail.Append(*srcRowErrorDetail)
	}
}
