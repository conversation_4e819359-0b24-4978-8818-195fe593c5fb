package h3util_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/h3util"
)

func TestGetH3Id(t *testing.T) {
	cases := []struct {
		lat    float64
		lng    float64
		res    int
		name   string
		expect string
	}{
		{
			lat:    13.9645144,
			lng:    100.6362929,
			res:    7,
			name:   "Dream World h3 resolution 7",
			expect: "8764a4166ffffff",
		},
		{
			lat:    13.9645144,
			lng:    100.6362929,
			res:    9999,
			name:   "Invalid resolution",
			expect: "0",
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			act := h3util.GetH3Id(c.lat, c.lng, c.res)
			require.Equal(t, c.expect, act)
		})
	}
}
