package utils_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

func TestDuplicates(t *testing.T) {
	type testCase struct {
		values   []int
		expected []int
	}

	tests := []testCase{
		{
			values:   []int{},
			expected: nil,
		},
		{
			values:   []int{1, 2, 3, 4, 5},
			expected: nil,
		},
		{
			values:   []int{1, 1, 2, 3, 4, 5, 4, 4},
			expected: []int{1, 4, 4},
		},
	}

	for i := range tests {
		tc := &tests[i]
		actual := utils.Duplicates(tc.values...)
		require.Equal(t, tc.expected, actual)
	}
}

func TestDuplicatesSet(t *testing.T) {
	type testCase struct {
		values   []int
		expected types.Set[int]
	}

	tests := []testCase{
		{
			values:   []int{},
			expected: nil,
		},
		{
			values:   []int{1, 2, 3, 4, 5},
			expected: nil,
		},
		{
			values:   []int{1, 1, 2, 3, 4, 5, 4, 4},
			expected: types.NewSetFrom(1, 4),
		},
	}

	for i := range tests {
		tc := &tests[i]
		actual := utils.DuplicatesSet(tc.values...)
		require.Equal(t, tc.expected, actual)
	}
}
