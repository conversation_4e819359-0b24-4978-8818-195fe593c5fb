package fpresult

func Collapse[A, B any](f func(A) B, g func(error) B) func(T[A]) B {
	return func(x T[A]) B {
		if x.IsOk() {
			return f(x.Value())
		}
		return g(x.<PERSON>rror())
	}
}

func Fmap[A, B any](f func(A) B) func(T[A]) T[B] {
	return func(x T[A]) T[B] {
		if x.IsOk() {
			return Value[B](f(x.Value()))
		}
		return Error[B](x.Error())
	}
}

func FmapPartial[A, B any](f func(A) T[B]) func(T[A]) T[B] {
	return func(x T[A]) T[B] {
		if x.IsOk() {
			return f(x.Value())
		}
		return Error[B](x.Error())
	}
}
