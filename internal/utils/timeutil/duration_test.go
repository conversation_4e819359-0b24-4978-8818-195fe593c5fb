package timeutil

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestDMax(t *testing.T) {
	testcases := []struct {
		a, b time.Duration
		max  time.Duration
	}{
		{
			a:   0,
			b:   1,
			max: 1,
		},
		{
			a:   10,
			b:   10,
			max: 10,
		},
		{
			a:   -10,
			b:   10,
			max: 10,
		},
		{
			a:   20,
			b:   10,
			max: 20,
		},
	}

	for _, tc := range testcases {
		require.Equal(t, tc.max, DMax(tc.a, tc.b))
	}
}

func TestDMin(t *testing.T) {
	testcases := []struct {
		a, b time.Duration
		min  time.Duration
	}{
		{
			a:   0,
			b:   1,
			min: 0,
		},
		{
			a:   10,
			b:   10,
			min: 10,
		},
		{
			a:   -10,
			b:   10,
			min: -10,
		},
		{
			a:   20,
			b:   10,
			min: 10,
		},
	}

	for _, tc := range testcases {
		require.Equal(t, tc.min, DMin(tc.a, tc.b))
	}
}

func TestToDurationStringMap(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected map[string]time.Duration
		wantErr  bool
	}{
		{
			name:     "valid input",
			input:    "key1:10s,key2:2m,key3:3h",
			expected: map[string]time.Duration{"key1": 10 * time.Second, "key2": 2 * time.Minute, "key3": 3 * time.Hour},
			wantErr:  false,
		},
		{
			name:     "valid input ignore empty pairs",
			input:    "key1:10s,key2:2m,key3:3h,,",
			expected: map[string]time.Duration{"key1": 10 * time.Second, "key2": 2 * time.Minute, "key3": 3 * time.Hour},
			wantErr:  false,
		},
		{
			name:     "empty input",
			input:    "",
			expected: map[string]time.Duration{},
			wantErr:  false,
		},
		{
			name:     "invalid format",
			input:    "key1:10s,key2,key3:3h",
			expected: nil,
			wantErr:  true,
		},
		{
			name:     "invalid duration",
			input:    "key1:10z,key2:2m,key3:3h",
			expected: nil,
			wantErr:  true,
		},
		{
			name:     "leading and trailing spaces",
			input:    " key1: 10s , key2: 2m , key3: 3h ",
			expected: map[string]time.Duration{"key1": 10 * time.Second, "key2": 2 * time.Minute, "key3": 3 * time.Hour},
			wantErr:  false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			got, err := ToDurationStringMap(tc.input)
			require.Equal(t, tc.expected, got)
			if tc.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

		})
	}
}
