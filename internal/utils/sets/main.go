package sets

import (
	"sync"
)

type zeroByte struct{}

type Of[T comparable] struct {
	set  map[T]zeroByte
	lock *sync.RWMutex
}

func New[T comparable](elements ...T) Of[T] {
	out := Of[T]{
		lock: new(sync.RWMutex),
		set:  make(map[T]zeroByte),
	}
	out.Add(elements...)
	return out
}

func (s Of[T]) IsInitialized() bool {
	return s.set != nil && s.lock != nil
}

func (s Of[T]) Add(elements ...T) {
	s.lock.Lock()
	defer s.lock.Unlock()

	for _, e := range elements {
		s.set[e] = zeroByte{}
	}
}

func (s Of[T]) Remove(elements ...T) {
	s.lock.Lock()
	defer s.lock.Unlock()

	for _, e := range elements {
		delete(s.set, e)
	}
}

func (s Of[T]) GetElements() []T {
	s.lock.RLock()
	defer s.lock.RUnlock()

	keys := make([]T, len(s.set))
	i := 0
	for k := range s.set {
		keys[i] = k
		i++
	}
	return keys
}

func (s Of[T]) Has(element T) bool {
	s.lock.RLock()
	defer s.lock.RUnlock()

	return s.has(element)
}

func (s Of[T]) has(element T) bool {
	_, exists := s.set[element]
	return exists
}

// HasAll the argument set is a subset of S
// i.e. let A be the set of the arguments ; A ⊆ S
func (s Of[T]) HasAll(elements ...T) bool {
	s.lock.RLock()
	defer s.lock.RUnlock()

	return s.hasAll(elements...)
}

func (s Of[T]) hasAll(elements ...T) bool {
	for _, e := range elements {
		if !s.has(e) {
			return false
		}
	}
	return true
}

// HasAny there exists at least one element of the argument set that is also an element of S
// i.e. the intersection of the argument set and S is not empty
// i.e. ∃x | x ∈ A && x ∈ S where A is the set of the arguments
func (s Of[T]) HasAny(elements ...T) bool {
	s.lock.RLock()
	defer s.lock.RUnlock()

	for _, e := range elements {
		if s.has(e) {
			return true
		}
	}
	return false
}

func (s Of[T]) Equal(elements ...T) bool {
	s.lock.RLock()
	defer s.lock.RUnlock()

	if New[T](elements...).count() != s.count() {
		return false
	}

	return s.hasAll(elements...)
}

func (s Of[T]) Minus(in Of[T]) Of[T] {
	s.lock.RLock()
	in.lock.RLock()
	defer s.lock.RUnlock()
	defer in.lock.RUnlock()

	out := New[T]()
	for k := range s.set {
		if !in.has(k) {
			out.Add(k)
		}
	}
	return out
}

func (s Of[T]) Without(elements ...T) Of[T] {
	return s.Minus(New[T](elements...))
}

func (s Of[T]) Intersect(in Of[T]) Of[T] {
	s.lock.RLock()
	in.lock.RLock()
	defer s.lock.RUnlock()
	defer in.lock.RUnlock()

	out := New[T]()
	for k := range s.set {
		if in.has(k) {
			out.Add(k)
		}
	}
	return out
}

func (s Of[T]) Count() int {
	s.lock.RLock()
	defer s.lock.RUnlock()

	return s.count()
}

func (s Of[T]) count() int {
	return len(s.set)
}

func (s Of[T]) IsEmpty() bool {
	return s.Count() == 0
}

func (s Of[T]) Clone() Of[T] {
	out := New[T]()
	out.Add(s.GetElements()...)
	return out
}

func (s *Of[T]) PopToSlice() []T {
	s.lock.Lock()
	defer func() {
		s.set = make(map[T]zeroByte)
		s.lock.Unlock()
	}()

	keys := make([]T, len(s.set))
	i := 0
	for k := range s.set {
		keys[i] = k
		i++
	}
	return keys
}
