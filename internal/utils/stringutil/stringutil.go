package stringutil

import (
	"encoding/json"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"unicode"

	"golang.org/x/text/message"
)

const (
	Alphanumeric = "ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
)

func RemoveInvisibleAndTrimSpace(s string) string {
	// remove invisible
	s = strings.Map(func(r rune) rune {
		if unicode.IsGraphic(r) {
			return r
		} else {
			return -1
		}
	}, s)

	// trim space
	s = strings.TrimFunc(s, func(r rune) bool {
		return unicode.IsSpace(r)
	})

	return s
}

func JsonPrint(i interface{}) {
	s, _ := json.MarshalIndent(i, "", "\t")
	fmt.Println(string(s))
}

func JsonPrintLog(i interface{}, any ...string) {
	s, _ := json.MarshalIndent(i, "", "\t")
	switch len(any) {
	case 0:
		fmt.Println(string(s))
	case 1:
		fmt.Println("[", any[0], "]", string(s))
	default:
		fmt.Println("[", fmt.Sprintf(any[0], "]", any[1:]), string(s))
	}
}

func IsStringInList(ss []string, s string) bool {
	for _, v := range ss {
		if v == s {
			return true
		}
	}
	return false
}

func IsStringOfInt(s string) bool {
	_, err := strconv.Atoi(s)
	return err == nil
}

func Unique(strSlice []string) []string {
	keys := make(map[string]bool)
	list := []string{}
	for _, entry := range strSlice {
		if _, value := keys[entry]; !value {
			keys[entry] = true
			list = append(list, entry)
		}
	}
	return list
}

func GetNumberInText(num float64) string {
	np := message.NewPrinter(message.MatchLanguage("en"))
	return np.Sprintf("%.2f", num)
}

// RandString generate random string from given length
// https://stackoverflow.com/questions/22892120/how-to-generate-a-random-string-of-a-fixed-length-in-go/31832326#31832326
func RandString(n int) string {
	b := make([]byte, n)
	for i := range b {
		b[i] = Alphanumeric[rand.Int63()%int64(len(Alphanumeric))]
	}
	return string(b)
}

// Ref: https://www.socketloop.com/tutorials/golang-underscore-or-snake-case-to-camel-case-example
func SnakeCaseToCamelCase(snakeCase string) (camelCase string) {
	//snake_case to camelCase

	isToUpper := false

	for k, v := range snakeCase {
		if k == 0 {
			camelCase = strings.ToUpper(string(snakeCase[0]))
		} else {
			if isToUpper {
				camelCase += strings.ToUpper(string(v))
				isToUpper = false
			} else {
				if v == '_' {
					isToUpper = true
				} else {
					camelCase += string(v)
				}
			}
		}
	}
	return

}

func NumToThaiWords(number int) string {
	if number == 0 {
		return "ศูนย์"
	}
	var words string
	var unit = map[int]string{
		0: "ล้าน",
		1: "สิบ",
		2: "ร้อย",
		3: "พัน",
		4: "หมื่น",
		5: "แสน",
		6: "",
	}
	var value = map[int]string{
		0:  "",
		1:  "หนึ่ง",
		2:  "สอง",
		3:  "สาม",
		4:  "สี่",
		5:  "ห้า",
		6:  "หก",
		7:  "เจ็ด",
		8:  "แปด",
		9:  "เก้า",
		10: "เอ็ด",
		11: "ยี่",
	}
	lennumber := len(strconv.Itoa(number))
	for i := 0; i < lennumber; i++ {
		indexword := number % 10
		indexunit := i % 6
		if indexword == 0 {
			if indexunit == 0 {
				indexunit = 0
			} else {
				indexunit = 6
			}
		}
		if indexword == 1 {
			if indexunit == 0 {
				indexword = 10
			}
			if i == lennumber-1 {
				indexword = 1
			}
			if indexunit == 1 {
				indexword = 0
			}
		}
		if indexword == 2 && indexunit == 1 {
			indexword = 11
		}
		if i == 0 {
			indexunit = 6
		}
		newword := value[indexword] + unit[indexunit]
		words = newword + words
		number = number / 10
	}
	return words
}

func JoinNonEmpty(strs []string, sep string) string {
	var filtered []string
	for _, str := range strs {
		if str != "" {
			filtered = append(filtered, str)
		}
	}
	return strings.Join(filtered, sep)
}

func ToLowerStrings(strs []string) []string {
	lowerStrings := []string{}
	for _, str := range strs {
		lowerStrings = append(lowerStrings, strings.ToLower(str))
	}
	return lowerStrings
}

func TrimAll(strs []string) []string {
	trimmedStrings := []string{}
	for _, str := range strs {
		trimmedStrings = append(trimmedStrings, strings.TrimSpace(str))
	}
	return trimmedStrings
}
