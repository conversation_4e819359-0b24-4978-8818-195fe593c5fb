package utils

import (
	"context"
	"errors"
	"sync"

	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

func BatchProcessFunc[T any](f func(batch []T) error, maxBatchSize int) func(xs []T) error {
	return func(xs []T) error {
		var buffer []T
		for _, x := range xs {
			buffer = append(buffer, x)
			if len(buffer) >= maxBatchSize {
				if err := f(buffer); err != nil {
					return err
				}
				buffer = buffer[:0]
			}
		}
		if len(buffer) > 0 {
			return f(buffer)
		}
		return nil
	}
}

func ConcurrentBatchFunc[T any, V any](ctx context.Context, fn func(context.Context, []T) ([]V, error), batchSize int) func(input []T) ([]V, error) {
	return func(input []T) ([]V, error) {
		if batchSize == 0 {
			batchSize = len(input)
		}
		var mu sync.Mutex
		var wg sync.WaitGroup

		var resError error
		result := make([]V, 0, len(input))
		for start := 0; start < len(input); start += batchSize {
			end := start + batchSize
			if end > len(input) {
				end = len(input)
			}

			cur := input[start:end]
			wg.Add(1)
			safe.GoFuncWithCtx(ctx, func() {
				defer wg.Done()
				vs, err := fn(ctx, cur)

				mu.Lock()
				defer mu.Unlock()
				result = append(result, vs...)
				if err != nil {
					resError = errors.Join(resError, err)
				}
			})
		}
		wg.Wait()
		return result, resError
	}
}
