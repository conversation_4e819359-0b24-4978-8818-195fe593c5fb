package utils

func CloneMap[T any](srcMap map[string]T) map[string]T {
	cpMap := make(map[string]T)
	for k, v := range srcMap {
		cpMap[k] = v
	}
	return cpMap
}

func SliceToMapKeepFirst[T, V any, K comparable](toKey func(T) K, toValue func(T) V, xs []T) map[K]V {
	result := make(map[K]V)
	for _, x := range xs {
		k := toKey(x)
		if _, existed := result[k]; !existed {
			result[k] = toValue(x)
		}
	}
	return result
}

func SliceToMapKeepLast[T, V any, K comparable](toKey func(T) K, toValue func(T) V, xs []T) map[K]V {
	result := make(map[K]V)
	for _, x := range xs {
		result[toKey(x)] = toValue(x)
	}
	return result
}

func CollectKeys[K comparable, V any](m map[K]V) []K {
	keys := make([]K, len(m))

	i := 0
	for key := range m {
		keys[i] = key
		i++
	}

	return keys
}

func DeleteKeys[K comparable, V any](m map[K]V, keys ...K) {
	for i := range keys {
		delete(m, keys[i])
	}
}
