package scheduler

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/asynqtask"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
)

func TestExpireRecommendation_ProcessTask(t *testing.T) {
	t.Run("happy flow should reset reco the push noti to client", func(tt *testing.T) {
		task, deps, cleanup := newTaskExpireRecommendationTest(tt, true)
		defer cleanup()
		ctx := context.Background()

		ta, _ := asynqtask.NewExpireRecommendationTask("a")
		deps.driverRepo.EXPECT().SetRecoIdleTimeStartPoint(ctx, gomock.Any(), gomock.Any()).Return(nil)
		deps.driverRepo.EXPECT().SetDriverH3Recommendation(ctx, gomock.Any(), gomock.Any()).Return(nil)
		deps.noti.EXPECT().Notify(ctx, gomock.Any(), gomock.Any()).Return(nil)
		err := task.ProcessTask(ctx, ta)

		assert.NoError(tt, err)
	})

	t.Run("do nothing when disable flag", func(tt *testing.T) {
		task, _, cleanup := newTaskExpireRecommendationTest(tt, false)
		defer cleanup()
		ctx := context.Background()

		ta, _ := asynqtask.NewExpireRecommendationTask("a")
		err := task.ProcessTask(ctx, ta)

		assert.NoError(tt, err)
	})
}

type taskDeps struct {
	cfg        TaskExpireRecommendationConfig
	driverRepo *mock_repository.MockDriverRepository
	noti       *mock_service.MockNotifier
}

func newTaskExpireRecommendationTest(tt *testing.T, enableFlag bool) (*TaskExpireRecommendation, *taskDeps, func()) {
	ctrl := gomock.NewController(tt)
	driverRepo := mock_repository.NewMockDriverRepository(ctrl)
	noti := mock_service.NewMockNotifier(ctrl)

	deps := &taskDeps{
		cfg: TaskExpireRecommendationConfig{
			TaskExpireRecommendationEnabled: enableFlag,
		},
		driverRepo: driverRepo,
		noti:       noti,
	}
	task := ProvideTaskExpireRecommendation(deps.cfg, deps.driverRepo, deps.noti)

	return task, deps, func() { ctrl.Finish() }
}
