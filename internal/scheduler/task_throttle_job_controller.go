package scheduler

//go:generate mockgen -source=task_throttle_job_controller.go -destination=./mock_task_throttle_job_controller/mock_task_throttle_job_controller.go -package=mock_scheduler

import (
	"context"
	"math/rand"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/go-co-op/gocron"
	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const DefaultPartitionKey = "DEFAULT"

type TaskThrottleJobControllerConfig struct {
	Interval time.Duration `envconfig:"TASK_THROTTLE_JOB_CONTROLLER_INTERVAL" default:"1m"`
}

type ThrottleScheduler interface {
	FindJobsByTag(tags ...string) ([]*gocron.Job, error)
	UpdateEvery(name string, every time.Duration, options ...Option) error
	Run(name string, task func(ctx context.Context), every time.Duration, options ...Option) error
}

type TaskThrottleJobController struct {
	ThrottledDispatchDetailRepo repository.ThrottledDispatchDetailRepository
	ThrottledOrderRepo          repository.ThrottledOrderRepository
	Scheduler                   ThrottleScheduler
	Cfg                         TaskThrottleJobControllerConfig
	Dispatcher                  dispatcher.Dispatcher
	JobDetails                  map[string]model.ThrottledDispatchDetailWithZoneCode
	jobDetailsMutex             sync.RWMutex
	AtomicH3Global              *AtomicH3Global
}

func ProvideTaskThrottleJobControllerConfig() TaskThrottleJobControllerConfig {
	var cfg TaskThrottleJobControllerConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

func ProvideTaskThrottleJobController(scheduler *Scheduler, throttledDispatchDetailRepo repository.ThrottledDispatchDetailRepository, throttledOrderRepo repository.ThrottledOrderRepository, cfg TaskThrottleJobControllerConfig, dispatcher dispatcher.Dispatcher, h3GlobalCfg *AtomicH3Global) *TaskThrottleJobController {
	task := TaskThrottleJobController{
		ThrottledDispatchDetailRepo: throttledDispatchDetailRepo,
		ThrottledOrderRepo:          throttledOrderRepo,
		Scheduler:                   scheduler,
		Cfg:                         cfg,
		Dispatcher:                  dispatcher,
		JobDetails:                  make(map[string]model.ThrottledDispatchDetailWithZoneCode),
		jobDetailsMutex:             sync.RWMutex{},
		AtomicH3Global:              h3GlobalCfg,
	}

	if err := scheduler.Run("task_throttle_job_controller", task.Execute, cfg.Interval, withSingletonMode()); err != nil {
		panic("cannot schedule throttle job controller task")
	}

	return &task
}

func (t *TaskThrottleJobController) UpsertJobDetail(tag string, d model.ThrottledDispatchDetailWithZoneCode) {
	t.jobDetailsMutex.Lock()
	defer t.jobDetailsMutex.Unlock()
	t.JobDetails[tag] = d
}

func (t *TaskThrottleJobController) GetJobDetail(tag string) (model.ThrottledDispatchDetailWithZoneCode, bool) {
	t.jobDetailsMutex.RLock()
	defer t.jobDetailsMutex.RUnlock()

	v, ok := t.JobDetails[tag]
	return v, ok
}

// Execute watch throttled dispatch detail
// this controller will spawn distribution job for each zone and keep it up-to-date
// assuming throttling zone can't be deleted
func (t *TaskThrottleJobController) Execute(ctx context.Context) {
	details, err := t.ThrottledDispatchDetailRepo.Find(ctx, 0, 0)
	if err != nil {
		logx.Error().Context(ctx).
			Err(err).
			Str("task", string(TaskNameThrottleJobController)).
			Msg("task_throttle_job_controller: can't find throttled dispatch details")
		return
	}
	now := time.Now()
	for _, d := range details {
		d := d
		tag := ThrottleTagName(d)
		jobs, err := t.Scheduler.FindJobsByTag(tag)
		if err != nil && err != gocron.ErrJobNotFoundWithTag {
			logx.Error().Context(ctx).
				Err(err).
				Str("task", string(TaskNameThrottleJobController)).
				Msg("task_throttle_job_controller: can't find jobs")
			return
		}

		if len(jobs) > 1 {
			logx.Warn().Context(ctx).
				Str("task", string(TaskNameThrottleJobController)).
				Msgf("task_throttle_job_controller: found duplicate jobs for zoneCode: %v", d.ZoneCode)
		}

		job, ok := t.GetJobDetail(tag)
		randomInterval := -time.Duration(rand.Float64()*float64(d.Interval.Milliseconds())) * time.Millisecond
		startAt := now.Add(randomInterval)
		if ok && len(jobs) > 0 {
			if job.Interval != d.Interval {
				err := t.Scheduler.UpdateEvery(tag, d.Interval, withStartAt(&startAt))
				if err != nil {
					logx.Error().Context(ctx).
						Err(err).
						Str("task", string(TaskNameThrottleJobController)).
						Msgf("task_throttle_job_controller: can't update job interval for zoneCode: %v", d.ZoneCode)
					continue
				}
			}
			t.UpsertJobDetail(tag, d)
		} else if !ok && len(jobs) <= 0 {
			err := t.Scheduler.Run(tag, func(ctx context.Context) {
				t.Distribute(ctx, tag)
			}, d.Interval, withSingletonMode(), withStartAt(&startAt))
			if err != nil {
				logx.Error().Context(ctx).
					Err(err).
					Str("task", string(TaskNameThrottleJobController)).
					Msgf("task_throttle_job_controller: can't run new job for zoneCode: %v", d.ZoneCode)
				continue
			}
			t.UpsertJobDetail(tag, d)
		} else {
			safe.SentryErrorMessage("task_throttle_job_controller job distributor is out of sync", safe.WithInfo("zone", d.ZoneCode))
		}
	}
}

// NOTE: Please consider to update shouldUpdateThrottleJob function if you use the new field from zone
func (t *TaskThrottleJobController) Distribute(ctx context.Context, tag string) {
	zone, ok := t.GetJobDetail(tag)
	if !ok {
		logx.Error().Context(ctx).
			Str("task", string(TaskNameThrottleJobController)).
			Str("tag", tag).
			Msg("task_throttle_job_distributor: can't retrive throttling zone detail")
		return
	}

	start := timeutil.BangkokNow()
	logx.Info().Context(ctx).
		Str("task", string(TaskNameThrottleJobController)).
		Str("zoneCode", zone.ZoneCode).
		Msgf("task_throttle_job_distributor: start distribute on zoneCode: %v", zone.ZoneCode)
	defer func() {
		diff := timeutil.BangkokNow().Sub(start)
		if diff > zone.Interval {
			logx.Warn().Context(ctx).
				Str("task", string(TaskNameThrottleJobController)).
				Str("zoneCode", zone.ZoneCode).
				Msgf("task_throttle_job_distributor: found distribute job for zoneCode: %v running over interval", zone.ZoneCode)
		}
		logx.Info().Context(ctx).
			Str("task", string(TaskNameThrottleJobController)).
			Str("zoneCode", zone.ZoneCode).
			Msgf("task_throttle_job_distributor: the execution time for zoneCode: %v is %v seconds", zone.ZoneCode, diff.Seconds())
	}()

	orders, err := t.ThrottledOrderRepo.Find(ctx, model.ThrottledOrderQuery{ZoneID: &zone.ZoneID, IsProcessed: types.NewBool(false)}, 0, 0)
	if err != nil {
		logx.Error().Context(ctx).
			Err(err).
			Str("task", string(TaskNameThrottleJobController)).
			Str("zoneCode", zone.ZoneCode).
			Msgf("task_throttle_job_distributor: can't getThrottledOrders on zoneCode: %v", zone.ZoneCode)
		return
	}
	orderTotal := len(orders)
	if orderTotal == 0 {
		logx.Info().Context(ctx).
			Str("task", string(TaskNameThrottleJobController)).
			Str("zoneCode", zone.ZoneCode).
			Str("orderIds", "[]").
			Msgf("task_throttle_job_distributor: distributed %v orders for zoneCode: %v", 0, zone.ZoneCode)
		return
	}

	threshold := t.AtomicH3Global.Get().AutoZoneBreakdownOrdersThreshold
	isAutoBreakdown := t.AtomicH3Global.Get().EnabledAutoZoneBreakdownGlobal && orderTotal > threshold
	if isAutoBreakdown && !zone.EnabledH3Partitioning {
		logx.Info().Context(ctx).
			Str("task", string(TaskNameThrottleJobController)).
			Str("zoneCode", zone.ZoneCode).
			Msgf("task_throttle_job_distributor: auto breakdown zone has enabled for zoneCode: %v with %v orders", zone.ZoneCode, orderTotal)
	}

	partitionedOrders := map[string][]model.ThrottledOrder{DefaultPartitionKey: orders}
	if zone.EnabledMedianSplit && isAutoBreakdown {
		partitionedOrders = PartitionOrdersByMedianSplit(orders, threshold)
	} else if zone.EnabledH3Partitioning || isAutoBreakdown {
		partitionedOrders = PartitionOrdersByH3(orders, zone.GetH3PartitioningResolution())
	}

	var wg sync.WaitGroup
	for partitionKey, orders := range partitionedOrders {
		if len(orders) == 0 {
			continue
		}
		pKey := partitionKey
		objectIDs := model.ThrottledOrdersToObjectIDList(orders)

		wg.Add(1)
		safe.GoFuncWithCtx(ctx, func() {
			defer wg.Done()

			req := dispatcher.DistributeOrdersInZoneRequest{ZoneID: zone.ZoneID.Hex(), ZoneCode: zone.ZoneCode, ThrottledOrderObjectIDs: objectIDs, UsingFetchedThrottledOrders: true}
			res, err := t.Dispatcher.DistributeOrdersInZone(ctx, req)
			if err != nil {
				logx.Error().Context(ctx).
					Err(err).
					Str("task", string(TaskNameThrottleJobController)).
					Str("zoneCode", zone.ZoneCode).
					Str("partitionKey", pKey).
					Msgf("task_throttle_job_distributor: can't distribute for zoneCode: %v", zone.ZoneCode)
				return
			}
			logx.Info().Context(ctx).
				Str("task", string(TaskNameThrottleJobController)).
				Str("zoneCode", zone.ZoneCode).
				Str("partitionKey", pKey).
				Str("orderIds", "["+strings.Join(res.OrderIDs, ",")+"]").
				Msgf("task_throttle_job_distributor: distributed %v orders for zoneCode: %v", len(res.OrderIDs), zone.ZoneCode)
		})
	}
	wg.Wait()
}

func PartitionOrdersByH3(throttledOrders []model.ThrottledOrder, res int) map[string][]model.ThrottledOrder {
	// assuming mp are throttled in the same zone
	mpGroupedKey := make(map[string]string)
	partitionedOrders := make(map[string][]model.ThrottledOrder)
	for _, o := range throttledOrders {
		key := persistence.EncodeLatLngStr(o.FromLat, o.FromLng, res)
		if o.MpID != "" {
			if existingKey, ok := mpGroupedKey[o.MpID]; ok {
				key = existingKey
			} else {
				mpGroupedKey[o.MpID] = key
			}
		}
		partitionedOrders[key] = append(partitionedOrders[key], o)
	}
	return partitionedOrders
}

func recurseMedianSplit(orders []model.ThrottledOrder, threshold int, key string, isLat bool, result map[string][]model.ThrottledOrder) {
	if len(orders) <= threshold || len(orders) <= 1 {
		result[key] = make([]model.ThrottledOrder, 0)
		result[key] = append(result[key], orders...)
		return
	}
	slices.SortFunc(orders, func(a model.ThrottledOrder, b model.ThrottledOrder) int {
		if isLat {
			return int(a.FromLat - b.FromLat)
		}
		return int(a.FromLng - b.FromLng)
	})
	med := len(orders) / 2
	recurseMedianSplit(orders[:med], threshold, key+"0", !isLat, result)
	recurseMedianSplit(orders[med:], threshold, key+"1", !isLat, result)
}

func PartitionOrdersByMedianSplit(throttledOrders []model.ThrottledOrder, threshold int) map[string][]model.ThrottledOrder {
	res := make(map[string][]model.ThrottledOrder)
	recurseMedianSplit(throttledOrders, threshold, "", true, res)
	mpKeys := make(map[string]string)
	for key, os := range res {
		for i := len(os) - 1; i >= 0; i-- {
			if os[i].MpID == "" {
				continue
			}
			if origKey, exists := mpKeys[os[i].MpID]; exists && origKey != key {
				res[origKey] = append(res[origKey], os[i])
				res[key] = append(res[key][:i], res[key][i+1:]...)
			} else {
				mpKeys[os[i].MpID] = key
			}
		}
	}
	return res
}

func ThrottleTagName(d model.ThrottledDispatchDetailWithZoneCode) string {
	return "task_throttle_job_distributor_" + d.ZoneCode
}
