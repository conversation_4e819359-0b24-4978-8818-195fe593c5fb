package scheduler

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	lmdelivery "git.wndv.co/lineman/delivery-service/pkg/client"
	mock_asynq_client "git.wndv.co/lineman/fleet-distribution/internal/asynqclient/mock_asynqclient"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery/mock_delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/cron"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

func TestExpireOrderTask_Execute(t *testing.T) {
	t.Run("should change all expired order status to EXPIRED and send update to food svc", func(tt *testing.T) {
		task, deps, cleanup := newTaskExpireOrderTest(tt)
		defer cleanup()
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.client.EXPECT().Enqueue(gomock.Any(), gomock.Any()).Return(nil, nil)

		task.Execute(ctx)

		wg.Wait()
	})
}

func TestExpireOrderTask_Process(t *testing.T) {
	t.Run("should change all expired order status to EXPIRED and send update to food svc", func(tt *testing.T) {
		task, deps, cleanup := newTaskExpireOrderTest(tt)
		defer cleanup()

		now := time.Now()
		expireAt := now.Truncate(10 * time.Minute)
		orders := []model.Order{
			{
				OrderID:  "test-1",
				Status:   model.StatusAssigningDriver,
				History:  make(map[string]time.Time),
				ExpireAt: expireAt,
			},
			{
				OrderID:  "test-2",
				Status:   model.StatusAssigningDriver,
				History:  make(map[string]time.Time),
				ExpireAt: expireAt,
			},
		}

		deps.orderRepo.EXPECT().
			FindAssigningDriverOrdersByTime(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, t time.Time, skip, limit int) ([]model.Order, error) {
				start := skip
				end := skip + limit
				if end >= len(orders) {
					end = len(orders)
				}
				return orders[start:end], nil
			}).
			AnyTimes()

		deps.orderRepo.EXPECT().
			UpdateExpireOrder(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, o *model.Order, opts ...repository.Option) error {
				_, ok := o.History[string(model.StatusExpired)]
				require.Equal(tt, model.StatusExpired, o.Status)
				require.True(tt, ok)
				return nil
			}).
			AnyTimes()

		deps.delivery.EXPECT().
			UpdateStatus(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, req *lmdelivery.UpdateStatusRequest) error {
				require.Equal(tt, string(model.StatusExpired), req.Status)
				require.Len(tt, req.Deliveries, 1)
				require.True(tt, req.OrderID == req.Deliveries[0].OrderID)
				require.Equal(tt, string(model.StatusExpired), req.Deliveries[0].Status)
				return nil
			}).
			AnyTimes()
		ctx, wg := safe.CreateCtxWithWaitGroup()

		wg.Add(len(orders))
		wk, release := safe.NewWorker(task.cfg.ProcessBatchSize)
		defer release()

		result := cron.NewResult()
		for i := range orders {
			task.process(ctx, &orders[i], wg, wk, result)
		}
		wg.Wait()

		require.Len(tt, result.Success, 2)
		require.Len(tt, result.Fail, 0)
	})
}

type expireOrderTaskDeps struct {
	orderRepo *mock_repository.MockOrderRepository
	delivery  *mock_delivery.MockDelivery
	cfg       TaskOrderExpireConfig
	client    *mock_asynq_client.MockAsynqClient
}

func newTaskExpireOrderTest(tt *testing.T) (*TaskExpireOrder, *expireOrderTaskDeps, func()) {
	ctrl := gomock.NewController(tt)
	orderRepo := mock_repository.NewMockOrderRepository(ctrl)
	delivery := mock_delivery.NewMockDelivery(ctrl)
	tmp := mock_asynq_client.NewMockAsynqClient(ctrl)

	deps := &expireOrderTaskDeps{
		orderRepo: orderRepo,
		delivery:  delivery,
		cfg: TaskOrderExpireConfig{
			OrderBatchSize:         50,
			ProcessBatchSize:       10,
			TaskOrderExpireEnabled: true,
		},
		client: tmp,
	}
	task := NewTaskExpireOrder(deps.orderRepo, deps.delivery, deps.cfg, tmp)

	return task, deps, func() { ctrl.Finish() }
}
