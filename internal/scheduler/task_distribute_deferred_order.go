package scheduler

import (
	"context"
	"strconv"
	"sync"
	"time"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	v1 "git.wndv.co/lineman/delivery-service/types/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type TaskDistributeDeferredOrdersConfig struct {
	DeferredDispatchTaskWorker         int           `envconfig:"TASK_DISTRIBUTE_DEFERRED_TASK_WORKER" default:"100"`
	DeferredDispatchInterval           time.Duration `envconfig:"TASK_DISTRIBUTE_DEFERRED_ORDER_INTERVAL" default:"1m"`
	DeferredDispatchRetrospectDuration time.Duration `envconfig:"TASK_DISTRIBUTE_DEFERRED_ORDER_RETROSPECT_DURATION" default:"30m"`
	DeferredDispatchPickupRoundLimit   int           `envconfig:"TASK_DISTRIBUTE_DEFERRED_PICKUP_ROUND_LIMIT" default:"5"`
}

type TaskDistributeDeferredOrders struct {
	Cfg                           TaskDistributeDeferredOrdersConfig
	DeferredOrderRepo             repository.DeferredOrderRepository
	OrderRepo                     repository.OrderRepository
	Delivery                      delivery.Delivery
	Dispatcher                    dispatcher.Dispatcher
	TxnHelper                     transaction.TxnHelper
	OrderDistributionEventManager service.OrderDistributionEventManager
	FeatureFlagService            featureflag.Service
	DistributionService           service.DistributionService
}

func ProvideTaskDistributeDeferredOrdersConfig() TaskDistributeDeferredOrdersConfig {
	var cfg TaskDistributeDeferredOrdersConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

func NewTaskDistributeDeferredOrders(
	deferredOrderRepo repository.DeferredOrderRepository,
	cfg TaskDistributeDeferredOrdersConfig,
	dispatcher dispatcher.Dispatcher,
	txnHelper transaction.TxnHelper,
	orderRepo repository.OrderRepository,
	delivery delivery.Delivery,
	orderDistributionEventManager service.OrderDistributionEventManager,
	featureFlagService featureflag.Service,
	distributionService service.DistributionService,
) *TaskDistributeDeferredOrders {
	return &TaskDistributeDeferredOrders{
		Cfg:                           cfg,
		DeferredOrderRepo:             deferredOrderRepo,
		Dispatcher:                    dispatcher,
		TxnHelper:                     txnHelper,
		OrderRepo:                     orderRepo,
		Delivery:                      delivery,
		OrderDistributionEventManager: orderDistributionEventManager,
		FeatureFlagService:            featureFlagService,
		DistributionService:           distributionService,
	}
}

func ProvideTaskDistributeDeferredOrders(
	scheduler *Scheduler,
	deferredOrderRepo repository.DeferredOrderRepository,
	cfg TaskDistributeDeferredOrdersConfig,
	dispatcher dispatcher.Dispatcher,
	txnHelper transaction.TxnHelper,
	orderRepo repository.OrderRepository,
	delivery delivery.Delivery,
	orderDistributionEventManager service.OrderDistributionEventManager,
	featureFlagService featureflag.Service,
	distributionService service.DistributionService,
) *TaskDistributeDeferredOrders {
	task := NewTaskDistributeDeferredOrders(deferredOrderRepo, cfg, dispatcher, txnHelper, orderRepo, delivery, orderDistributionEventManager, featureFlagService, distributionService)

	if err := scheduler.Run("task_distribute_deferred_order", task.Execute, cfg.DeferredDispatchInterval); err != nil {
		panic("cannot schedule task distribute deferred order")
	}

	return task
}

// Execute distribute all of unprocessed throttled orders in given zone
func (t *TaskDistributeDeferredOrders) Execute(ctx context.Context) {
	infoLogger := logx.Info().Context(ctx).Str("task", string(TaskNameDistributeDeferredOrder))
	errLogger := logx.Error().Context(ctx).Str("task", string(TaskNameDistributeDeferredOrder))

	infoLogger.Msgf("%v: start distributing.", TaskNameDistributeDeferredOrder)
	res, err := t.TxnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		now := timeutil.BangkokNow()
		retroTime := now.Add(-t.Cfg.DeferredDispatchRetrospectDuration)

		orders, err := t.DeferredOrderRepo.FindUnProcessOrder(ctx, now, retroTime, t.Cfg.DeferredDispatchPickupRoundLimit)
		if err != nil {
			errLogger.Err(err).Msgf("%v: error finding orders", TaskNameDistributeDeferredOrder)
			return nil, err
		}

		orderIds := types.NewStringSet()
		deduplicatedOrders := make([]model.DeferredOrder, 0, len(orders))
		for _, o := range orders {
			if !orderIds.Has(o.OrderID) {
				deduplicatedOrders = append(deduplicatedOrders, o)
			}
			orderIds.Add(o.OrderID)
		}
		err = t.DeferredOrderRepo.MarkProcessedOrders(ctx, orderIds.GetElements(), t.Cfg.DeferredDispatchPickupRoundLimit)
		if err != nil {
			errLogger.Err(err).Msgf("%v: error marking orders", TaskNameDistributeDeferredOrder)
			return nil, err
		}

		return deduplicatedOrders, nil
	}, transaction.WithLabel("TaskDistributeDeferredOrders.Execute"))
	if err != nil {
		safe.SentryErrorMessage(
			"task_distribute_deferred_orders: error marking processed to orders",
			safe.WithInfo("err", err.Error()),
		)
		return
	}

	orders, ok := res.([]model.DeferredOrder)
	if !ok {
		errLogger.Msgf("%v: failed with error, cannot parse deferred orders strct", TaskNameDistributeDeferredOrder)
		return
	}

	ordersMap := make(map[string]model.Order)
	ords, getManyOrdersErr := t.OrderRepo.GetMany(ctx, model.DeferredOrders(orders).OrderIDs())
	if getManyOrdersErr == nil {
		for i := range ords {
			ordersMap[ords[i].OrderID] = ords[i]
		}
	} else {
		safe.SentryErrorMessage( // since these orders are retrieved just for publishing events
			"task_distribute_deferred_orders: error getting orders for publishing events",
			safe.WithInfo("err", getManyOrdersErr.Error()),
		)
	}

	failed := types.NewStringSet()
	success := types.NewStringSet()
	worker, wg, release := t.spawnTaskWorker(t.Cfg.DeferredDispatchTaskWorker, len(orders))
	for _, o := range orders {
		oId := o.OrderID
		worker.GoFuncWithPool(func() {
			defer wg.Done()

			if ord, found := ordersMap[oId]; found {
				if err := t.OrderDistributionEventManager.PublishSearchingOrAssigningDriver(ctx, ord, timeutil.BangkokNow()); err != nil {
					logx.Errorf(ctx, err, "task_distribute_deferred_orders: cannot publish searching/assigning event orderID=%s", ord.OrderID)
				}
			}

			actualAssigningAt := time.Now()
			if err := t.OrderRepo.SetActualAssigningAtFromNil(ctx, oId, actualAssigningAt); err == nil {
				if err := t.Delivery.UpdateActualAssigning(ctx, oId, v1.UpdateActualAssigningRequest{
					ActualAssigningAt: actualAssigningAt,
				}); err != nil {
					errLogger.Err(err).Str("order_id", oId).Msgf("%v: error calling update-actual-assigning for order %s", TaskNameDistributeDeferredOrder, oId)
				}
			} else if err == mongodb.ErrDataNotFound {
				logx.Warn().Context(ctx).
					Str("task", string(TaskNameDistributeDeferredOrder)).
					Str("order_id", oId).
					Msgf("cannot update actual_assigning_at for deferred order %s because (most likely) actual_assigning_at is nil or cannot find order", oId)
			} else {
				errLogger.Err(err).Str("order_id", oId).Msgf("%v cannot update actual_assigning_at for deferred order %s", TaskNameDistributeDeferredOrder, oId)
			}

			err := t.DistributionService.PublishRedistributeOrderEvent(ctx, oId, nil)
			if err != nil {
				errLogger.Err(err).Str("order_id", oId).Msgf("%v: error distibute order %s", TaskNameDistributeDeferredOrder, oId)
				failed.Add(oId)
				safe.SentryErrorMessage(
					"task_distribute_deferred_orders: error distributing order",
					safe.WithOrderID(oId),
					safe.WithInfo("err", err.Error()),
				)
				return
			}
			success.Add(oId)
		})
	}
	wg.Wait()
	release()

	if err := t.DeferredOrderRepo.MarkSuccessOrders(ctx, success.GetElements()); err != nil {
		errLogger.Err(err).Msgf("%v: error marking orders as success: %v", TaskNameDistributeDeferredOrder, success)
		safe.SentryErrorMessage(
			"task_distribute_deferred_orders: error marking succeed orders",
			safe.WithInfo("order_count", strconv.Itoa(success.Count())),
			safe.WithInfo("err", err.Error()),
		)
		return
	}

	if err := t.DeferredOrderRepo.MarkFailedOrders(ctx, failed.GetElements()); err != nil {
		errLogger.Err(err).Msgf("%v: error marking orders as failed: %v", TaskNameDistributeDeferredOrder, failed)
		safe.SentryErrorMessage(
			"task_distribute_deferred_orders: error marking failed orders",
			safe.WithInfo("order_count", strconv.Itoa(failed.Count())),
			safe.WithInfo("err", err.Error()),
		)
		return
	}

	infoLogger.Msgf("%v: %v orders successfully distributed", TaskNameDistributeDeferredOrder, success.Count())
	infoLogger.Msgf("%v: %v orders cannot distributed", TaskNameDistributeDeferredOrder, failed.Count())
}

func (t *TaskDistributeDeferredOrders) spawnTaskWorker(workerCount int, taskCount int) (*safe.Worker, *sync.WaitGroup, func()) {
	var wg sync.WaitGroup
	wg.Add(taskCount)
	if workerCount == 0 || taskCount < workerCount {
		workerCount = taskCount
	}

	wk, release := safe.NewWorker(workerCount)

	return wk, &wg, release
}
