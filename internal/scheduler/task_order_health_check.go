package scheduler

import (
	"context"
	"math"
	"sync"
	"time"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	lmwnConfig "git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type TaskOrderHealthCheckDbConfig struct {
	Enable   bool          `envconfig:"TASK_ORDER_HEALTH_CHECK_ENABLE" default:"true"`
	Interval time.Duration `envconfig:"TASK_ORDER_HEALTH_CHECK_INTERVAL" default:"1m"`

	ForceUnlock bool `envconfig:"TASK_ORDER_HEALTH_CHECK_FORCE_UNLOCK" default:"true"`

	DistributeCountLimit     int           `envconfig:"TASK_ORDER_HEALTH_CHECK_DISTRIBUTE_COUNT_LIMIT" default:"5"`
	RetryDistributeWaitLimit time.Duration `envconfig:"TASK_ORDER_HEALTH_CHECK_RETRY_DISTRIBUTE_WAIT_LIMIT" default:"1h"`
}

type AtomicTaskOrderHealthCheckDbConfig struct {
	lock   sync.RWMutex
	Config TaskOrderHealthCheckDbConfig
}

func (cfg *AtomicTaskOrderHealthCheckDbConfig) Parse() {
	cfg.lock.Lock()
	defer cfg.lock.Unlock()
	envconfig.MustProcess("", &cfg.Config)
}

func (cfg *AtomicTaskOrderHealthCheckDbConfig) Get() TaskOrderHealthCheckDbConfig {
	cfg.lock.RLock()
	defer cfg.lock.RUnlock()
	return cfg.Config
}

func ProvideTaskOrderHealthCheckDbConfig(configUpdater *lmwnConfig.DBConfigUpdater) *AtomicTaskOrderHealthCheckDbConfig {
	var cfg AtomicTaskOrderHealthCheckDbConfig
	configUpdater.Register(&cfg)
	return &cfg
}

type TaskOrderHealthCheck struct {
	locker                locker.Locker
	dispatcher            dispatcher.Dispatcher
	cfg                   *AtomicTaskOrderHealthCheckDbConfig
	orderHeartbeatService service.OrderHeartbeatService
	distributionService   service.DistributionService
	featureFlagService    featureflag.Service
	orderRepository       repository.OrderRepository

	unhealthyCountMetric   metric.Counter
	succeedCountMetric     metric.Counter
	failedCountMetric      metric.Counter
	exceedLimitCountMetric metric.Counter
}

func NewTaskOrderHealthCheck(
	cfg *AtomicTaskOrderHealthCheckDbConfig,
	locker locker.Locker,
	dispatcher dispatcher.Dispatcher,
	orderHeartbeatService service.OrderHeartbeatService,
	meter metric.Meter,
	distributionService service.DistributionService,
	featureFlagService featureflag.Service,
	orderRepository repository.OrderRepository,
) *TaskOrderHealthCheck {
	return &TaskOrderHealthCheck{
		cfg:                    cfg,
		locker:                 locker,
		dispatcher:             dispatcher,
		orderHeartbeatService:  orderHeartbeatService,
		distributionService:    distributionService,
		featureFlagService:     featureFlagService,
		orderRepository:        orderRepository,
		unhealthyCountMetric:   meter.GetCounter("order_health_check_found_count", "number of orders found unhealthy"),
		succeedCountMetric:     meter.GetCounter("order_health_check_redistribute_succeed_count", "number of unhealthy order that redistribute successfully"),
		failedCountMetric:      meter.GetCounter("order_health_check_redistribute_failed_count", "number of unhealthy order that cannot redistribute"),
		exceedLimitCountMetric: meter.GetCounter("order_health_check_redistribute_exceed_limit_count", "number of unhealthy order that cannot exceed redistribution count"),
	}
}

func ProvideTaskOrderHealthCheck(
	scheduler *Scheduler,
	cfg *AtomicTaskOrderHealthCheckDbConfig,
	locker locker.Locker,
	dispatcher dispatcher.Dispatcher,
	orderHeartbeatService service.OrderHeartbeatService,
	meter metric.Meter,
	distributionService service.DistributionService,
	featureFlagService featureflag.Service,
	orderRepository repository.OrderRepository,
) *TaskOrderHealthCheck {
	task := NewTaskOrderHealthCheck(cfg, locker, dispatcher, orderHeartbeatService, meter, distributionService, featureFlagService, orderRepository)

	if err := scheduler.Run("task_order_health_check", task.Execute, cfg.Get().Interval); err != nil {
		panic("cannot order health check task")
	}

	return task
}

func (task *TaskOrderHealthCheck) Execute(ctx context.Context) {
	if !task.cfg.Get().Enable {
		logx.Info().Context(ctx).Msg("task_order_health_check is disabled")
		return
	}

	orders, err := task.orderHeartbeatService.FindUnhealthyOrderIDs(ctx)
	if err != nil {
		logx.Error().Context(ctx).Err(err).Msg("task_order_health_check: cannot find unhealthy order ids")
		return
	}
	task.unhealthyCountMetric.Add(float64(len(orders)))
	logx.Warn().Context(ctx).Msgf("task_order_health_check: found %d unhealthy orders", len(orders))

	var mt sync.RWMutex
	var wg sync.WaitGroup
	failedOrders := make([]model.OrderHearthBeatStateWithOrderID, 0)
	succeedOrders := make([]model.OrderHearthBeatStateWithOrderID, 0)
	wg.Add(len(orders))
	for _, o := range orders {
		o := o
		safe.GoFunc(func() {
			defer wg.Done()

			if time.Now().UTC().Unix() < o.State.UnhealthyDistributeWaitUntil {
				return
			}

			logx.Info().Context(ctx).Str("order_id", o.OrderID).Msgf("task_order_health_check: redistributing unhealthy order %s", o.OrderID)
			if task.cfg.Get().ForceUnlock {
				// To prevent unhealthy order that still locked.
				task.locker.RemoveState(ctx, locker.OrderAutoAssignState(o.OrderID))
			}

			redistributeErr := task.distributionService.PublishRedistributeOrderEvent(ctx, o.OrderID, nil)

			if redistributeErr != nil {
				mt.Lock()
				failedOrders = append(failedOrders, o)
				mt.Unlock()

				task.failedCountMetric.Inc()
				logx.Error().Context(ctx).Err(redistributeErr).Str("order_id", o.OrderID).Msg("task_order_health_check: cannot redistribute order")
				return
			}

			mt.Lock()
			succeedOrders = append(succeedOrders, o)
			mt.Unlock()
			task.succeedCountMetric.Inc()
			return
		})
	}
	wg.Wait()
	logx.Warn().Context(ctx).Msgf("task_order_health_check: redistribute %d orders done", len(orders))

	task.HandlePostDistribution(ctx, failedOrders, succeedOrders)
}

func (task *TaskOrderHealthCheck) Name() string {
	return "TaskOrderHealthCheck"
}

func (task *TaskOrderHealthCheck) HandlePostDistribution(ctx context.Context, failedOrders []model.OrderHearthBeatStateWithOrderID, succeedOrders []model.OrderHearthBeatStateWithOrderID) {
	if len(succeedOrders) < 1 && len(failedOrders) < 1 {
		return
	}

	orderToRemove := make([]string, 0)
	orderToUpdate := make([]model.OrderHearthBeatStateWithOrderID, 0)
	for _, o := range succeedOrders {
		orderToRemove = append(orderToRemove, o.OrderID)
	}

	for _, o := range failedOrders {
		o.State.UnhealthyDistributeCount += 1
		if o.State.UnhealthyDistributeCount > task.cfg.Get().DistributeCountLimit {
			logx.Info().Context(ctx).Str("order_id", o.OrderID).Msgf("task_order_health_check: order %s have exceed retry limit %d, order will be remove", o.OrderID, task.cfg.Get().DistributeCountLimit)
			orderToRemove = append(orderToRemove, o.OrderID)
			task.exceedLimitCountMetric.Add(1)
			continue
		}

		nowUTC := time.Now().UTC().Add(task.CalculateRetryExponential(o.State.UnhealthyDistributeCount))
		o.State.UnhealthyDistributeWaitUntil = nowUTC.Unix()

		orderToUpdate = append(orderToUpdate, o)
		logx.Info().Context(ctx).Str("order_id", o.OrderID).Msgf("task_order_health_check: order %s have failed %d time and will skip redistribution until %v", o.OrderID, o.State.UnhealthyDistributeCount, nowUTC)
	}

	if len(orderToUpdate) > 0 {
		if err := task.orderHeartbeatService.UpdateStateBatch(ctx, orderToUpdate); err != nil {
			logx.Error().Context(ctx).Err(err).Interface("orders_to_update", failedOrders).Msgf("task_order_health_check: update orders heartbeat state failed")
		}
	}

	if len(orderToRemove) > 0 {
		if err := task.orderHeartbeatService.DoneUnhealthy(ctx, orderToRemove); err != nil {
			logx.Error().Context(ctx).Err(err).Interface("orders_to_remove", orderToRemove).Msgf("task_order_health_check: remove orders from heartbeat failed")
		} else {
			logx.Warn().Context(ctx).Msgf("task_order_health_check: remove %d orders from healthcheck", len(orderToRemove))
		}
	}
}

func (task *TaskOrderHealthCheck) CalculateRetryExponential(retryCount int) time.Duration {
	waitLimit := task.cfg.Get().RetryDistributeWaitLimit.Minutes()
	interval := task.cfg.Get().Interval.Minutes() * 2
	if task.cfg.Get().Interval.Minutes() < 1 {
		interval = ((1 * time.Minute) * 2).Minutes()
	}

	waitUntil := math.Pow(interval, float64(retryCount))
	if waitUntil > waitLimit {
		waitUntil = waitLimit
	}

	return time.Duration(waitUntil) * time.Minute
}
