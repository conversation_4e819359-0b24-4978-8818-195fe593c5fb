package scheduler

import (
	"context"
	"errors"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/absinthe/metrics/push"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
)

var _ push.MetricPusher = (*metricPusherStub)(nil)

type metricPusherStub struct {
	push.MetricPusher
}

func (*metricPusherStub) Add(ctx context.Context) {
}

var _ push.GaugeMetricPush = (*gaugeMetricPushStub)(nil)

type gaugeMetricPushStub struct {
	push.GaugeMetricPush
	observeValues []observeValue
}

func (m *gaugeMetricPushStub) Observe(ctx context.Context, v float64, attrs ...push.Attribute) {
	m.observeValues = append(m.observeValues, observeValue{
		value: v,
		attrs: attrs,
	})
}

type observeValue struct {
	value float64
	attrs []push.Attribute
}

type taskDetectInactiveDriverDeps struct {
	driverRepo                          *mock_repository.MockDriverRepository
	driverLastUpdateLocationTrackerRepo *mock_repository.MockDriverLastUpdateLocationTrackerRepository
	driverUpdateLocationEventService    *mock_service.MockDriverUpdateLocationEventService
	metricPusherStub                    *metricPusherStub
	totalSuccessMetric                  *gaugeMetricPushStub
	totalIneligibleDriversMetric        *gaugeMetricPushStub
}

func newTaskDetectInactiveDriverForTest(ctrl *gomock.Controller, cfg TaskDetectInactiveDriverConfig) (*TaskDetectInactiveDriver, *taskDetectInactiveDriverDeps, func()) {
	deps := &taskDetectInactiveDriverDeps{
		driverRepo:                          mock_repository.NewMockDriverRepository(ctrl),
		driverLastUpdateLocationTrackerRepo: mock_repository.NewMockDriverLastUpdateLocationTrackerRepository(ctrl),
		driverUpdateLocationEventService:    mock_service.NewMockDriverUpdateLocationEventService(ctrl),
		metricPusherStub:                    &metricPusherStub{},
		totalSuccessMetric:                  &gaugeMetricPushStub{},
		totalIneligibleDriversMetric:        &gaugeMetricPushStub{},
	}

	task := NewTaskDetectInactiveDriver(deps.driverLastUpdateLocationTrackerRepo, cfg, deps.driverUpdateLocationEventService, deps.driverRepo, deps.metricPusherStub, deps.totalSuccessMetric, deps.totalIneligibleDriversMetric)

	return task, deps, func() { ctrl.Finish() }
}

func TestTaskDetectInactiveDriver_Execute(t *testing.T) {
	t.Parallel()

	cfg := TaskDetectInactiveDriverConfig{DetectInactiveDriverEnabled: true}

	// test with just 1 keyslot to reduce parallelism

	t.Run("should able to inactive driver if eligible", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		task, deps, cleanup := newTaskDetectInactiveDriverForTest(ctrl, cfg)
		t.Cleanup(cleanup)

		deps.driverLastUpdateLocationTrackerRepo.EXPECT().GetKeySlot().Return([]string{"1"})
		deps.driverLastUpdateLocationTrackerRepo.EXPECT().GetExpiredDriverID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DriverLastUpdateLocationAttempt{
			{
				DriverID:  "driver-a",
				Timestamp: 12345,
			},
		}, nil)
		deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Driver{
			{
				DriverID: "driver-a",
				Status:   model.StatusOnline,
			},
		}, nil)
		deps.driverLastUpdateLocationTrackerRepo.EXPECT().RemoveDriverIDs(gomock.Any(), "1", []string{"driver-a"})
		deps.driverUpdateLocationEventService.EXPECT().PublishDriverInactiveEvents(gomock.Any(), []model.DriverLastUpdateLocationAttempt{
			{DriverID: "driver-a", Timestamp: 12345},
		})
		// When
		task.Execute(context.Background())

		// Then
		assert.ElementsMatch(t, []observeValue{
			{
				value: 1,
				attrs: []push.Attribute{
					{Key: "state", Value: "success"},
				},
			},
			{
				value: 0,
				attrs: []push.Attribute{
					{Key: "state", Value: "fail"},
				},
			},
		}, deps.totalSuccessMetric.observeValues)
	})

	t.Run("shouldn't to inactive driver if not eligible (driver status is assigned)", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		task, deps, cleanup := newTaskDetectInactiveDriverForTest(ctrl, cfg)
		t.Cleanup(cleanup)

		deps.driverLastUpdateLocationTrackerRepo.EXPECT().GetKeySlot().Return([]string{"1"})
		deps.driverLastUpdateLocationTrackerRepo.EXPECT().GetExpiredDriverID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DriverLastUpdateLocationAttempt{
			{
				DriverID:  "driver-a",
				Timestamp: 12345,
			},
		}, nil)
		deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Driver{
			{
				DriverID: "driver-a",
				Status:   model.StatusAssigned,
			},
		}, nil)
		deps.driverLastUpdateLocationTrackerRepo.EXPECT().RemoveDriverIDs(gomock.Any(), "1", []string{"driver-a"}).Times(0)
		deps.driverUpdateLocationEventService.EXPECT().PublishDriverInactiveEvents(gomock.Any(), []model.DriverLastUpdateLocationAttempt{
			{DriverID: "driver-a", Timestamp: 12345},
		}).Times(0)

		// When
		task.Execute(context.Background())

		// Then
		assert.ElementsMatch(t, []observeValue{
			{
				value: 0,
				attrs: []push.Attribute{
					{Key: "state", Value: "success"},
				},
			},
			{
				value: 0,
				attrs: []push.Attribute{
					{Key: "state", Value: "fail"},
				},
			},
		}, deps.totalSuccessMetric.observeValues)

		assert.ElementsMatch(t, []observeValue{
			{
				value: 1,
				attrs: []push.Attribute{
					{Key: "caused", Value: "driver_assigned"},
				},
			},
		}, deps.totalIneligibleDriversMetric.observeValues)
	})

	t.Run("error unable to remove driver ids from redis", func(t *testing.T) {
		// Given
		ctrl := gomock.NewController(t)
		task, deps, cleanup := newTaskDetectInactiveDriverForTest(ctrl, cfg)
		t.Cleanup(cleanup)

		deps.driverLastUpdateLocationTrackerRepo.EXPECT().GetKeySlot().Return([]string{"1"})
		deps.driverLastUpdateLocationTrackerRepo.EXPECT().GetExpiredDriverID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DriverLastUpdateLocationAttempt{
			{
				DriverID:  "driver-a",
				Timestamp: 12345,
			},
		}, nil)
		deps.driverRepo.EXPECT().FindWithQuerySelectorAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Driver{
			{
				DriverID: "driver-a",
				Status:   model.StatusOnline,
			},
		}, nil)
		deps.driverLastUpdateLocationTrackerRepo.EXPECT().RemoveDriverIDs(gomock.Any(), "1", []string{"driver-a"}).Return(0, errors.New("error"))

		// When
		task.Execute(context.Background())

		// Then
		assert.ElementsMatch(t, []observeValue{
			{
				value: 0,
				attrs: []push.Attribute{
					{Key: "state", Value: "success"},
				},
			},
			{
				value: 1,
				attrs: []push.Attribute{
					{Key: "state", Value: "fail"},
				},
			},
		}, deps.totalSuccessMetric.observeValues)
	})
}
