package scheduler

import (
	"context"
	"time"

	"github.com/hibiken/asynq"
	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/metrics/push"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqclient"
	internalasynq "git.wndv.co/lineman/fleet-distribution/internal/asynqtask"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

type TaskFetchRainSituationConfig struct {
	TaskFetchRainSituationInterval  time.Duration `envconfig:"TASK_FETCH_RAIN_SITUATION_INTERVAL" default:"2m"`
	TaskFetchRainSituationRetention time.Duration `envconfig:"TASK_FETCH_RAIN_SITUATION_RETENTION_HR" default:"48h"`
	IsTaskFetchRainSituationEnabled bool          `envconfig:"IS_TASK_FETCH_RAIN_SITUATION_ENABLED" default:"true"`
}

func ProvideTaskFetchRainSituationConfig() (cfg TaskFetchRainSituationConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

// TaskFetchRainSituation
type TaskFetchRainSituation struct {
	cfg                  TaskFetchRainSituationConfig
	rainSituationService service.RainSituationService
	mPusher              push.MetricPusher
	totalMetric          push.GaugeMetricPush
	client               asynqclient.AsynqClient
}

func ProvideTaskFetchRainSituation(scheduler *Scheduler, rainSituationService service.RainSituationService, cfg TaskFetchRainSituationConfig, client *asynq.Client) *TaskFetchRainSituation {
	task := NewTaskFetchRainSituation(rainSituationService, cfg, client)
	if err := scheduler.Run(string(TaskNameFetchRainSituation), task.Execute, cfg.TaskFetchRainSituationInterval); err != nil {
		logx.Panic().
			Str("method", string(TaskNameFetchRainSituation)).
			Msgf("%v failed: unable to schedule task", TaskNameFetchRainSituation)
		panic("cannot schedule task")
	}
	return task
}

func (tu *TaskFetchRainSituation) Execute(ctx context.Context) {
	if !tu.cfg.IsTaskFetchRainSituationEnabled {
		logx.Info().Context(ctx).
			Str("method", string(TaskNameFetchRainSituation)).
			Msgf("%v is disabled", TaskNameFetchRainSituation)
		return
	}

	if tu.client == nil {
		return
	}

	t1, initTaskErr := internalasynq.NewFetchRainSituationTask()
	if initTaskErr != nil {
		logx.Error().Context(ctx).
			Err(initTaskErr).
			Str("method", string(TaskNameFetchRainSituation)).
			Msgf("%v failed: unable to init a task", TaskNameFetchRainSituation)
		return
	}

	if _, err := tu.client.Enqueue(t1, asynq.Retention(tu.cfg.TaskFetchRainSituationRetention)); err != nil {
		logx.Error().Context(ctx).
			Err(err).
			Str("method", string(TaskNameFetchRainSituation)).
			Msgf("%v failed: unable to enqueue a task", TaskNameFetchRainSituation)
		return
	}
}

// ProcessTask implements asynq.Handler.
func (tu *TaskFetchRainSituation) ProcessTask(ctx context.Context, _ *asynq.Task) error {
	defer tu.mPusher.Add(ctx)

	var success, fail int
	defer func() {
		tu.totalMetric.Observe(ctx, float64(success), push.NewAttribute("state", "success"))
		tu.totalMetric.Observe(ctx, float64(fail), push.NewAttribute("state", "fail"))
	}()

	if !tu.cfg.IsTaskFetchRainSituationEnabled {
		logx.Info().Context(ctx).Str("method", string(TaskNameFetchRainSituation)).Msg("disabled")
		return nil
	}

	logx.Info().Context(ctx).Str("method", string(TaskNameFetchRainSituation)).Msg("running...")

	result := tu.rainSituationService.Fetch(ctx)
	success = result.Success
	fail = result.Fail

	return nil
}

func (tu *TaskFetchRainSituation) Name() string {
	return "TaskFetchRainSituation"
}

func NewTaskFetchRainSituation(
	rainSituationService service.RainSituationService,
	cfg TaskFetchRainSituationConfig,
	client asynqclient.AsynqClient,
) *TaskFetchRainSituation {
	mPusher := push.NewDefaultMetricPusher("task-driver-fetch-rain-situation", "task_driver_fetch_rain_situation")
	totalMetric := push.NewGaugeMetricPush(
		mPusher,
		"fetch_rain_situation_count",
		"number of fetch rain situation processing",
		"state")

	return &TaskFetchRainSituation{
		rainSituationService: rainSituationService,
		cfg:                  cfg,
		mPusher:              mPusher,
		totalMetric:          totalMetric,
		client:               client,
	}
}
