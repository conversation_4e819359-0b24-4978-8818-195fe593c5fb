package scheduler

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"

	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery/mock_delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher/mock_dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func TestTaskDistributeDeferredOrders_Execute(t *testing.T) {
	t.Run("should distribute correctly when enabled publish event", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		task, deps := NewMockTaskDistributeDeferredOrdersDeps(ctrl, TaskDistributeDeferredOrdersConfig{DeferredDispatchPickupRoundLimit: 6})
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.TxnHelper.EXPECT().WithTxn(ctx, gomock.Any(), gomock.Any())
		deps.DeferredOrderRepo.EXPECT().FindUnProcessOrder(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DeferredOrder{
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
		}, nil)
		expectedOrderId := []string{"o1", "o2", "o3", "o4"}
		deps.DeferredOrderRepo.EXPECT().MarkProcessedOrders(ctx, gomock.InAnyOrder(expectedOrderId), 6)
		deps.OrderRepo.EXPECT().GetMany(ctx, []string{"o1", "o2", "o3", "o4"}).Return([]model.Order{
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
		}, nil)
		deps.OrderDistributionManager.EXPECT().PublishSearchingOrAssigningDriver(ctx, gomock.Any(), gomock.Any()).Times(4)
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o1", nil)
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o2", nil)
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o3", nil)
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o4", nil)
		deps.DeferredOrderRepo.EXPECT().MarkSuccessOrders(ctx, gomock.Any()).Do(func(ctx context.Context, orderId []string) {
			expectedMarkOrderId(tt, expectedOrderId, orderId)
		})
		deps.DeferredOrderRepo.EXPECT().MarkFailedOrders(ctx, gomock.Any()).Do(func(ctx context.Context, orderId []string) {
			expectedMarkOrderId(tt, []string{}, orderId)
		})
		deps.OrderRepo.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		deps.Delivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		task.Execute(ctx)
		wg.Wait()
	})

	t.Run("should pick deferred order", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		task, deps := NewMockTaskDistributeDeferredOrdersDeps(ctrl, TaskDistributeDeferredOrdersConfig{DeferredDispatchPickupRoundLimit: 6})
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.TxnHelper.EXPECT().WithTxn(ctx, gomock.Any(), gomock.Any())
		deps.DeferredOrderRepo.EXPECT().FindUnProcessOrder(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DeferredOrder{
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
		}, nil)
		expectedOrderId := []string{"o1", "o2", "o3", "o4"}
		deps.DeferredOrderRepo.EXPECT().MarkProcessedOrders(ctx, gomock.InAnyOrder(expectedOrderId), 6)
		deps.OrderRepo.EXPECT().GetMany(ctx, []string{"o1", "o2", "o3", "o4"}).Return([]model.Order{
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
		}, nil)
		deps.OrderDistributionManager.EXPECT().PublishSearchingOrAssigningDriver(ctx, gomock.Any(), gomock.Any()).Times(4)
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o1", gomock.Any())
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o2", gomock.Any())
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o3", gomock.Any())
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o4", gomock.Any())
		deps.DeferredOrderRepo.EXPECT().MarkSuccessOrders(ctx, gomock.Any()).Do(func(ctx context.Context, orderId []string) {
			expectedMarkOrderId(tt, expectedOrderId, orderId)
		})
		deps.DeferredOrderRepo.EXPECT().MarkFailedOrders(ctx, gomock.Any()).Do(func(ctx context.Context, orderId []string) {
			expectedMarkOrderId(tt, []string{}, orderId)
		})
		deps.OrderRepo.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		deps.Delivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		task.Execute(ctx)
		wg.Wait()
	})

	t.Run("should deduplicate deferred order", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		task, deps := NewMockTaskDistributeDeferredOrdersDeps(ctrl, TaskDistributeDeferredOrdersConfig{DeferredDispatchPickupRoundLimit: 6})
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deferOrder := []model.DeferredOrder{
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
		}
		deps.TxnHelper.EXPECT().WithTxn(ctx, gomock.Any(), gomock.Any())
		deps.DeferredOrderRepo.EXPECT().FindUnProcessOrder(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(deferOrder, nil)
		expectedOrderId := []string{"o1", "o2", "o3", "o4"}
		deps.DeferredOrderRepo.EXPECT().MarkProcessedOrders(ctx, gomock.InAnyOrder(expectedOrderId), 6)
		deps.OrderRepo.EXPECT().GetMany(ctx, []string{"o1", "o2", "o3", "o4"}).Return([]model.Order{
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
		}, nil)
		deps.OrderDistributionManager.EXPECT().PublishSearchingOrAssigningDriver(ctx, gomock.Any(), gomock.Any()).Times(4)
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o1", gomock.Any())
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o2", gomock.Any())
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o3", gomock.Any())
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o4", gomock.Any())
		deps.DeferredOrderRepo.EXPECT().MarkSuccessOrders(ctx, gomock.Any()).Do(func(ctx context.Context, orderId []string) {
			expectedMarkOrderId(tt, expectedOrderId, orderId)
		})
		deps.DeferredOrderRepo.EXPECT().MarkFailedOrders(ctx, gomock.Any()).Do(func(ctx context.Context, orderId []string) {
			expectedMarkOrderId(tt, []string{}, orderId)
		})
		deps.OrderRepo.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		deps.Delivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		task.Execute(ctx)
		wg.Wait()
	})

	t.Run("should now mark process to failed order", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		task, deps := NewMockTaskDistributeDeferredOrdersDeps(ctrl, TaskDistributeDeferredOrdersConfig{DeferredDispatchPickupRoundLimit: 6})
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.TxnHelper.EXPECT().WithTxn(ctx, gomock.Any(), gomock.Any())
		deps.DeferredOrderRepo.EXPECT().FindUnProcessOrder(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.DeferredOrder{
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
		}, nil)
		expectedOrderId := []string{"o1", "o2", "o3", "o4"}
		expectedSucceedOrderId := []string{"o1", "o2"}
		expectedFailedOrderId := []string{"o3", "o4"}
		deps.DeferredOrderRepo.EXPECT().MarkProcessedOrders(ctx, gomock.InAnyOrder(expectedOrderId), 6)
		deps.OrderRepo.EXPECT().GetMany(ctx, []string{"o1", "o2", "o3", "o4"}).Return([]model.Order{
			{OrderID: "o1"},
			{OrderID: "o2"},
			{OrderID: "o3"},
			{OrderID: "o4"},
		}, nil)
		deps.OrderDistributionManager.EXPECT().PublishSearchingOrAssigningDriver(ctx, gomock.Any(), gomock.Any()).Times(4)
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o1", gomock.Any())
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o2", gomock.Any())
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o3", gomock.Any()).Return(errors.New("failed test"))
		deps.DistributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "o4", gomock.Any()).Return(errors.New("failed test"))
		deps.DeferredOrderRepo.EXPECT().MarkSuccessOrders(ctx, gomock.Any()).Do(func(ctx context.Context, orderId []string) {
			expectedMarkOrderId(tt, expectedSucceedOrderId, orderId)
		})
		deps.DeferredOrderRepo.EXPECT().MarkFailedOrders(ctx, gomock.Any()).Do(func(ctx context.Context, orderId []string) {
			expectedMarkOrderId(tt, expectedFailedOrderId, orderId)
		})
		deps.OrderRepo.EXPECT().SetActualAssigningAtFromNil(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
		deps.Delivery.EXPECT().UpdateActualAssigning(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

		task.Execute(ctx)
		wg.Wait()
	})
}

func expectedMarkOrderId(t *testing.T, expectedId, orderIds []string) {
	if len(orderIds) != len(expectedId) {
		t.Errorf("order_ids size is invalid in mark processed orders, expected %v got %v", len(expectedId), len(orderIds))
		t.FailNow()
	}

	orderIdsSet := types.NewStringSet(orderIds...)
	if !orderIdsSet.HasAll(expectedId...) {
		t.Errorf("order id mismatch in mark processed orders, expected %v got %v", expectedId, orderIds)
		t.FailNow()
	}
}

type MockTaskDistributeDeferredOrdersDeps struct {
	DeferredOrderRepo        *mock_repository.MockDeferredOrderRepository
	Dispatcher               *mock_dispatcher.MockDispatcher
	TxnHelper                *mock_transaction.MockTxnHelper
	OrderRepo                *mock_repository.MockOrderRepository
	Delivery                 *mock_delivery.MockDelivery
	OrderDistributionManager *mock_service.MockOrderDistributionEventManager
	FeatureFlagService       *mock_featureflag.MockService
	DistributionService      *mock_service.MockDistributionService
}

func NewMockTaskDistributeDeferredOrdersDeps(ctrl *gomock.Controller, cfg TaskDistributeDeferredOrdersConfig) (*TaskDistributeDeferredOrders, *MockTaskDistributeDeferredOrdersDeps) {
	deps := &MockTaskDistributeDeferredOrdersDeps{
		Dispatcher:               mock_dispatcher.NewMockDispatcher(ctrl),
		DeferredOrderRepo:        mock_repository.NewMockDeferredOrderRepository(ctrl),
		TxnHelper:                mock_transaction.NewMockTxnHelper(ctrl),
		OrderRepo:                mock_repository.NewMockOrderRepository(ctrl),
		Delivery:                 mock_delivery.NewMockDelivery(ctrl),
		OrderDistributionManager: mock_service.NewMockOrderDistributionEventManager(ctrl),
		FeatureFlagService:       mock_featureflag.NewMockService(ctrl),
		DistributionService:      mock_service.NewMockDistributionService(ctrl),
	}
	task := NewTaskDistributeDeferredOrders(deps.DeferredOrderRepo, cfg, deps.Dispatcher, deps.TxnHelper, deps.OrderRepo, deps.Delivery, deps.OrderDistributionManager, deps.FeatureFlagService, deps.DistributionService)
	return task, deps
}
