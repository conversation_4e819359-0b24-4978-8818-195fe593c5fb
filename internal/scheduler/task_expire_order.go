package scheduler

import (
	"context"
	"strings"
	"sync"
	"time"

	"github.com/hibiken/asynq"
	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/metrics/push"
	lmdelivery "git.wndv.co/lineman/delivery-service/pkg/client"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqclient"
	internalasynq "git.wndv.co/lineman/fleet-distribution/internal/asynqtask"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/cron"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

type TaskExpireOrder struct {
	orderRepo repository.OrderRepository
	delivery  delivery.Delivery
	cfg       TaskOrderExpireConfig
	client    asynqclient.AsynqClient
}

type TaskOrderExpireConfig struct {
	OrderBatchSize           int           `envconfig:"ORDER_EXPIRE_BATCH_SIZE" default:"50"`
	ProcessBatchSize         int           `envconfig:"ORDER_EXPIRE_PROCESS_BATCH_SIZE" default:"10"`
	OrderExpireTaskInterval  time.Duration `envconfig:"TASK_ORDER_EXPIRE_INTERVAL" default:"1m"`
	TaskOrderExpireRetention time.Duration `envconfig:"TASK_ORDER_EXPIRE_RETENTION_HR" default:"48h"`
	TaskOrderExpireEnabled   bool          `envconfig:"TASK_ORDER_EXPIRE_ENABLED" default:"true"`

	// ExpireContingencyDuration is the duration where order needs to be expired for more than this duration to be processed
	// This is used to prevent the race conditions that could occur when an order is assigned at the last minute
	ExpireContingencyDuration time.Duration `envconfig:"EXPIRE_CONTINGENCY_DURATION" default:"45s"`
}

func ProvideTaskOrderExpireConfig() (cfg TaskOrderExpireConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func NewTaskExpireOrder(orderRepository repository.OrderRepository, delivery delivery.Delivery, cfg TaskOrderExpireConfig, cl asynqclient.AsynqClient) *TaskExpireOrder {
	return &TaskExpireOrder{
		orderRepo: orderRepository,
		delivery:  delivery,
		cfg:       cfg,
		client:    cl,
	}
}

func ProvideTaskExpireOrder(scheduler *Scheduler, orderRepository repository.OrderRepository, delivery delivery.Delivery, cfg TaskOrderExpireConfig, client *asynq.Client) *TaskExpireOrder {
	task := NewTaskExpireOrder(orderRepository, delivery, cfg, client)

	if err := scheduler.Run("task_expire_order", task.Execute, cfg.OrderExpireTaskInterval); err != nil {
		panic("cannot expire order task")
	}

	return task
}

func (teo *TaskExpireOrder) Execute(ctx context.Context) {
	if !teo.cfg.TaskOrderExpireEnabled {
		logx.Info().Context(ctx).Msg("task_expire_order is disabled.")
		return
	}

	if teo.client == nil {
		return
	}

	t1, err := internalasynq.NewExpireOrderTask()
	if err != nil {
		logx.Error().Context(ctx).
			Err(err).
			Str("method", string(TaskNameExpireOrder)).
			Msg("task_expire_order failed: cannot count assigning driver orders by time")

		return
	}

	_, err = teo.client.Enqueue(t1, asynq.Retention(teo.cfg.TaskOrderExpireRetention))
	if err != nil {
		logx.Error().Context(ctx).
			Err(err).
			Str("method", string(TaskNameExpireOrder)).
			Msgf("fail to enqueue task: %v", err)
	}
}

func (teo *TaskExpireOrder) process(ctx context.Context, order *model.Order, wg *sync.WaitGroup, wk *safe.Worker, result *cron.Result) {
	wk.GoFuncWithPool(func() {
		defer wg.Done()
		order.SetStatus(model.StatusExpired)
		if err := teo.orderRepo.UpdateExpireOrder(ctx, order); err != nil {
			result.AddFail(order.OrderID)
			logx.Error().Context(ctx).
				Err(err).
				Str("method", string(TaskNameExpireOrder)).
				Str("order_id", order.OrderID).
				Msgf("fail to expire order %s", order.OrderID)
			return
		}

		if err := teo.delivery.UpdateStatus(ctx, &lmdelivery.UpdateStatusRequest{
			OrderID:    order.OrderID,
			Status:     string(order.Status),
			StatusTime: order.GetCurrentStatusTime().Unix(),
			Deliveries: []lmdelivery.DeliveryStatus{
				{
					OrderID: order.OrderID,
					Status:  string(order.Status),
				},
			},
		}); err != nil {
			result.AddFail(order.OrderID)
			logx.Error().Context(ctx).
				Err(err).
				Str("method", string(TaskNameExpireOrder)).
				Str("order_id", order.OrderID).
				Msgf("fail to update to delivery service for order %s", order.OrderID)
			return
		}
		result.AddSuccess(order.OrderID)
	})
}

// ProcessTask implements asynq.Handler.
func (teo *TaskExpireOrder) ProcessTask(ctx context.Context, _ *asynq.Task) error {
	now := time.Now()

	mPusher := push.NewDefaultMetricPusher("driver-expire-order-cron", "clear_pending")
	totalSuccessMetric := push.NewGaugeMetricPush(
		mPusher,
		"clear_pending_total_count",
		"number of pending processing",
		"state",
		"match_restaurant_first",
	)

	t := now.Add(-teo.cfg.ExpireContingencyDuration)
	count, err := teo.orderRepo.CountAssigningDriverOrdersByTime(ctx, t)
	if err != nil {
		logx.Error().Context(ctx).
			Err(err).
			Str("method", string(TaskNameExpireOrder)).
			Msg("task_expire_order failed: cannot count assigning driver orders by time")

		return nil
	}

	result := cron.NewResult()
	batchSize := teo.cfg.OrderBatchSize
	matchRestaurantFirstOrderIDMap := make(map[string]struct{})
	for offset := 0; offset < count; offset += batchSize {
		orders, err := teo.orderRepo.FindAssigningDriverOrdersByTime(ctx, t, 0, batchSize)
		if err != nil {
			logx.Error().Context(ctx).
				Err(err).
				Str("method", string(TaskNameExpireOrder)).
				Msgf("fail to expire order %v", err)
			continue
		}
		var orderIDs []string
		for _, order := range orders {
			orderIDs = append(orderIDs, order.OrderID)
			if order.IsSwitchFlow() {
				matchRestaurantFirstOrderIDMap[order.OrderID] = struct{}{}
			}

		}
		logx.Info().
			Str("method", string(TaskNameExpireOrder)).
			Str("order_ids", strings.Join(orderIDs, ",")).
			Msg("expiring order")

		var wg sync.WaitGroup
		wg.Add(len(orders))
		wk, release := safe.NewWorker(teo.cfg.ProcessBatchSize)
		for i := range orders {
			teo.process(ctx, &orders[i], &wg, wk, result)
		}

		wg.Wait()
		release()
	}

	teo.observeSuccessMetric(ctx, totalSuccessMetric, result.Success, "success", matchRestaurantFirstOrderIDMap)
	teo.observeSuccessMetric(ctx, totalSuccessMetric, result.Fail, "fail", matchRestaurantFirstOrderIDMap)
	mPusher.Add(ctx)

	logx.Info().Context(ctx).
		Str("method", string(TaskNameExpireOrder)).
		Msgf("task_expire_order done. success %v ,  fail %v", len(result.Success), len(result.Fail))

	return nil
}

func (teo *TaskExpireOrder) observeSuccessMetric(
	ctx context.Context,
	totalSuccessMetric push.GaugeMetricPush,
	orderIDs []string,
	state string,
	matchRestaurantFirstOrderIDMap map[string]struct{},
) {
	matchRestaurantFirstCount := 0
	for _, orderID := range orderIDs {
		if _, ok := matchRestaurantFirstOrderIDMap[orderID]; ok {
			matchRestaurantFirstCount += 1
		}
	}
	totalSuccessMetric.Observe(
		ctx,
		float64(matchRestaurantFirstCount),
		push.NewAttribute("state", state),
		push.NewAttribute("match_restaurant_first", "true"),
	)
	totalSuccessMetric.Observe(
		ctx,
		float64(len(orderIDs)-matchRestaurantFirstCount),
		push.NewAttribute("state", state),
		push.NewAttribute("match_restaurant_first", "false"),
	)
}

func (teo *TaskExpireOrder) Name() string {
	return "TaskExpireOrder"
}
