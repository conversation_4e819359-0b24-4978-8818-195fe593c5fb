package scheduler_test

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/hibiken/asynq"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	mockAsynqClient "git.wndv.co/lineman/fleet-distribution/internal/asynqclient/mock_asynqclient"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqtask"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/scheduler"
)

func TestTaskQRIncidentOrderCompletion_ProcessTask(t *testing.T) {
	t.<PERSON>()
	t.Run("do nothing when QRIncidentOrderCompletion disable flag", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt, map[string]time.Duration{}, map[string]time.Duration{})
		defer cleanup()
		ctx := context.Background()

		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(ctx, featureflag.IsQRIncidentOrderCompletionEnabled.Name).Return(false)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(ctx, featureflag.IsUploadSlipByPassValidationEnabled.Name).Return(false).Times(0)

		ta, _ := asynqtask.NewQRIncidentOrderCompletionTask()
		err := task.ProcessTask(ctx, ta)

		assert.NoError(tt, err)
	})

	t.Run("fail when normal durations is nil", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt, nil, map[string]time.Duration{})
		defer cleanup()
		ctx := context.Background()

		ta, _ := asynqtask.NewQRIncidentOrderCompletionTask()
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(ctx, featureflag.IsQRIncidentOrderCompletionEnabled.Name).Return(true)

		err := task.ProcessTask(ctx, ta)

		assert.Error(tt, err)
	})

	t.Run("fail when incident durations is nil", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt, map[string]time.Duration{}, nil)
		defer cleanup()
		ctx := context.Background()

		ta, _ := asynqtask.NewQRIncidentOrderCompletionTask()
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(ctx, featureflag.IsQRIncidentOrderCompletionEnabled.Name).Return(true)

		err := task.ProcessTask(ctx, ta)

		assert.Error(tt, err)
	})

	t.Run("normal scenario", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt, map[string]time.Duration{}, map[string]time.Duration{})
		defer cleanup()
		ctx := context.Background()

		ta, _ := asynqtask.NewQRIncidentOrderCompletionTask()
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(ctx, featureflag.IsQRIncidentOrderCompletionEnabled.Name).Return(true)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(ctx, featureflag.IsUploadSlipByPassValidationEnabled.Name).Return(false)
		deps.qrIncidentOrderService.EXPECT().NotifyRiderCompleteOrderThemselves(ctx, service.QRSelfCompleteDurationByServiceType{}).Return([]string{}, []string{}, nil)

		err := task.ProcessTask(ctx, ta)

		assert.NoError(tt, err)
	})

	t.Run("incident scenario", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt, map[string]time.Duration{}, map[string]time.Duration{})
		defer cleanup()
		ctx := context.Background()

		ta, _ := asynqtask.NewQRIncidentOrderCompletionTask()
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(ctx, featureflag.IsQRIncidentOrderCompletionEnabled.Name).Return(true)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(ctx, featureflag.IsUploadSlipByPassValidationEnabled.Name).Return(true)
		deps.qrIncidentOrderService.EXPECT().NotifyRiderCompleteOrderThemselves(ctx, service.QRSelfCompleteDurationByServiceType{}).Return([]string{}, []string{}, nil)

		err := task.ProcessTask(ctx, ta)

		assert.NoError(tt, err)
	})
}

func TestTaskQRIncidentOrderCompletion_GetSelfCompleteDurationByServiceType(t *testing.T) {
	t.Parallel()
	bikeNormalDuration := 0 * time.Minute
	foodIncidentDuration := 0 * time.Minute
	bikeIncidentDuration := 5 * time.Minute
	invalidDuration := 0 * time.Minute
	t.Run("error when qr normal config is nil", func(tt *testing.T) {
		task, _, cleanup := newTaskQRIncidentOrderCompletionTest(tt, nil, map[string]time.Duration{})
		defer cleanup()
		ctx := context.Background()

		durations, err := task.GetSelfCompleteDurationByServiceType(ctx)
		require.Nil(t, durations)
		require.Error(t, err)
	})
	t.Run("error when qr incident config is nil", func(tt *testing.T) {
		task, _, cleanup := newTaskQRIncidentOrderCompletionTest(tt, map[string]time.Duration{}, nil)
		defer cleanup()
		ctx := context.Background()

		durations, err := task.GetSelfCompleteDurationByServiceType(ctx)
		require.Nil(t, durations)
		require.Error(t, err)
	})
	t.Run("success with qr normal scenario", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt,
			map[string]time.Duration{
				"bike":    bikeNormalDuration,
				"invalid": invalidDuration,
			},
			map[string]time.Duration{
				"food":    foodIncidentDuration,
				"bike":    bikeIncidentDuration,
				"invalid": invalidDuration,
			})
		defer cleanup()
		ctx := context.Background()

		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(ctx, featureflag.IsUploadSlipByPassValidationEnabled.Name).Return(false)

		durations, err := task.GetSelfCompleteDurationByServiceType(ctx)
		require.Equal(t, service.QRSelfCompleteDurationByServiceType{}, durations)
		require.NoError(t, err)
	})
	t.Run("success with qr incident scenario", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt,
			map[string]time.Duration{
				"bike":    bikeNormalDuration,
				"invalid": invalidDuration,
			},
			map[string]time.Duration{
				"food":    foodIncidentDuration,
				"bike":    bikeIncidentDuration,
				"invalid": invalidDuration,
			})
		defer cleanup()
		ctx := context.Background()

		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(ctx, featureflag.IsUploadSlipByPassValidationEnabled.Name).Return(true)

		durations, err := task.GetSelfCompleteDurationByServiceType(ctx)
		require.Equal(t, service.QRSelfCompleteDurationByServiceType{
			model.ServiceBike: bikeIncidentDuration,
		}, durations)
		require.NoError(t, err)
	})

}

type taskQRIncidentOrderCompletionDeps struct {
	qrIncidentOrderService *mock_service.MockQRIncidentOrderService
	featureflagService     *mock_featureflag.MockService
	client                 *mockAsynqClient.MockAsynqClient
	cfg                    scheduler.TaskQRIncidentOrderCompletionConfig
}

func TestTaskQRIncidentOrderCompletion_Execute(t *testing.T) {
	t.Parallel()
	t.Run("does nothing when task is disabled", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt, map[string]time.Duration{}, map[string]time.Duration{})
		defer cleanup()
		ctx := context.Background()

		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(ctx, featureflag.IsQRIncidentOrderCompletionEnabled.Name).Return(false)
		deps.featureflagService.EXPECT().IsEnabledWithDefaultFalse(ctx, featureflag.IsUploadSlipByPassValidationEnabled.Name).Return(false).Times(0)

		task.Execute(ctx)
	})

	t.Run("logs error and returns when unable to enqueue a task", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt, map[string]time.Duration{}, map[string]time.Duration{})
		defer cleanup()
		ctx := context.Background()
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(ctx, featureflag.IsQRIncidentOrderCompletionEnabled.Name).Return(true)
		deps.client.EXPECT().Enqueue(gomock.Any(), gomock.Any()).Return(nil, errors.New("unable to enqueue a task"))

		task.Execute(ctx)
	})

	t.Run("enqueues a task successfully", func(tt *testing.T) {
		task, deps, cleanup := newTaskQRIncidentOrderCompletionTest(tt, map[string]time.Duration{}, map[string]time.Duration{})
		defer cleanup()
		ctx := context.Background()
		deps.featureflagService.EXPECT().IsEnabledWithDefaultTrue(ctx, featureflag.IsQRIncidentOrderCompletionEnabled.Name).Return(true)
		deps.client.EXPECT().Enqueue(gomock.Any(), gomock.Any()).Return(&asynq.TaskInfo{}, nil)

		task.Execute(ctx)
	})
}

func newTaskQRIncidentOrderCompletionTest(tt *testing.T, normalDurations, incidentDurations map[string]time.Duration) (*scheduler.TaskQRIncidentOrderCompletion, *taskQRIncidentOrderCompletionDeps, func()) {
	ctrl := gomock.NewController(tt)
	qrIncidentOrderService := mock_service.NewMockQRIncidentOrderService(ctrl)
	featureflagService := mock_featureflag.NewMockService(ctrl)
	client := mockAsynqClient.NewMockAsynqClient(ctrl)

	cfg := scheduler.TaskQRIncidentOrderCompletionConfig{
		QRIncidentOrderCompletionInterval:  time.Minute,
		QRIncidentOrderCompletionRetention: time.Hour * 48,
		AtomicOrderDBConfig: &order.AtomicOrderDBConfig{
			Config: order.OrderDBConfig{
				QRNormalSelfCompleteDurationByService:   normalDurations,
				QRIncidentSelfCompleteDurationByService: incidentDurations,
			},
		},
	}

	deps := &taskQRIncidentOrderCompletionDeps{
		qrIncidentOrderService: qrIncidentOrderService,
		featureflagService:     featureflagService,
		client:                 client,
		cfg:                    cfg,
	}

	task := scheduler.NewTaskQRIncidentOrderCompletion(client, deps.featureflagService, deps.qrIncidentOrderService, deps.cfg)
	return task, deps, func() { ctrl.Finish() }
}
