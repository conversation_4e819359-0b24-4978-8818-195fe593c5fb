package scheduler_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher/mock_dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker/mock_locker"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/scheduler"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
)

func TestTaskOrderHealthCheck(t *testing.T) {
	t.Run("should redistribute orders correctly (enabled publish event)", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		task, deps := newTestTaskOrderHealthCheck(ctrl, defaultTaskOrderHealthCheckDbConfig())
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.orderHeartbeatService.EXPECT().FindUnhealthyOrderIDs(ctx).Return(
			[]model.OrderHearthBeatStateWithOrderID{
				{OrderID: "ord-1", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 0}},
				{OrderID: "ord-2", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 0}},
			},
			nil,
		)
		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "ord-1", nil)
		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "ord-2", nil)

		deps.orderHeartbeatService.EXPECT().DoneUnhealthy(gomock.Any(), gomock.Any()).Return(nil)

		task.Execute(ctx)
		wg.Wait()
	})

	t.Run("should redistribute non-batch order correctly", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		task, deps := newTestTaskOrderHealthCheck(ctrl, defaultTaskOrderHealthCheckDbConfig())
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.orderHeartbeatService.EXPECT().FindUnhealthyOrderIDs(ctx).Return(
			[]model.OrderHearthBeatStateWithOrderID{
				{OrderID: "ord-1", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 0}},
			},
			nil,
		)
		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(ctx, "ord-1", gomock.Any())

		deps.orderHeartbeatService.EXPECT().DoneUnhealthy(gomock.Any(), gomock.Any()).Return(nil)

		task.Execute(ctx)
		wg.Wait()
	})

	t.Run("should skip redistribute if order is not reach waitUntil", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		task, deps := newTestTaskOrderHealthCheck(ctrl, defaultTaskOrderHealthCheckDbConfig())
		ctx, wg := safe.CreateCtxWithWaitGroup()

		nowUnix := time.Now().UTC().Add(10 * time.Minute).Unix()

		deps.orderHeartbeatService.EXPECT().FindUnhealthyOrderIDs(ctx).Return(
			[]model.OrderHearthBeatStateWithOrderID{
				{OrderID: "ord-1", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 1, UnhealthyDistributeWaitUntil: nowUnix}},
				{OrderID: "ord-2", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 1, UnhealthyDistributeWaitUntil: nowUnix}},
			},
			nil,
		)

		task.Execute(ctx)
		wg.Wait()
	})

	t.Run("should unlock if force unlock is enable", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		cfg := defaultTaskOrderHealthCheckDbConfig()
		cfg.ForceUnlock = true
		task, deps := newTestTaskOrderHealthCheck(ctrl, cfg)
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.orderHeartbeatService.EXPECT().FindUnhealthyOrderIDs(ctx).Return(
			[]model.OrderHearthBeatStateWithOrderID{
				{OrderID: "ord-1", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 0}},
				{OrderID: "ord-2", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 0}},
			},
			nil,
		)
		deps.locker.EXPECT().RemoveState(ctx, locker.OrderAutoAssignState("ord-1"))
		deps.locker.EXPECT().RemoveState(ctx, locker.OrderAutoAssignState("ord-2"))
		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), "ord-1", gomock.Any())
		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), "ord-2", gomock.Any())
		deps.orderHeartbeatService.EXPECT().DoneUnhealthy(gomock.Any(), gomock.Any()).Return(nil)

		task.Execute(ctx)
		wg.Wait()
	})

	t.Run("should update orders if redistribution failed", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		cfg := defaultTaskOrderHealthCheckDbConfig()
		task, deps := newTestTaskOrderHealthCheck(ctrl, cfg)
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.orderHeartbeatService.EXPECT().FindUnhealthyOrderIDs(ctx).Return(
			[]model.OrderHearthBeatStateWithOrderID{
				{OrderID: "ord-1", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 0}},
				{OrderID: "ord-2", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 0}},
			},
			nil,
		)

		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), "ord-1", gomock.Any()).Return(errors.Errorf("failed"))
		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), "ord-2", gomock.Any()).Return(errors.Errorf("failed"))

		deps.orderHeartbeatService.EXPECT().UpdateStateBatch(gomock.Any(), gomock.Any()).Do(func(ctx context.Context, orders []model.OrderHearthBeatStateWithOrderID) {
			require.Len(t, orders, 2)
			for _, o := range orders {
				require.Equal(t, 1, o.State.UnhealthyDistributeCount)
				require.NotZero(t, o.State.UnhealthyDistributeWaitUntil)
			}
		})

		task.Execute(ctx)
		wg.Wait()
	})

	t.Run("should remove orders if exceed redistribution limit", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		cfg := defaultTaskOrderHealthCheckDbConfig()
		task, deps := newTestTaskOrderHealthCheck(ctrl, cfg)
		ctx, wg := safe.CreateCtxWithWaitGroup()

		deps.orderHeartbeatService.EXPECT().FindUnhealthyOrderIDs(ctx).Return(
			[]model.OrderHearthBeatStateWithOrderID{
				{OrderID: "ord-1", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 0}},
				{OrderID: "ord-2", State: &model.OrderHearthBeatState{UnhealthyDistributeCount: 5}},
			},
			nil,
		)
		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), "ord-1", gomock.Any()).Return(errors.New("mock failed"))
		deps.distributionService.EXPECT().PublishRedistributeOrderEvent(gomock.Any(), "ord-2", gomock.Any()).Return(errors.New("mock failed"))
		deps.orderHeartbeatService.EXPECT().UpdateStateBatch(ctx, gomock.Any()).Do(func(ctx context.Context, orders []model.OrderHearthBeatStateWithOrderID) {
			require.Len(t, orders, 1)

			require.Equal(t, "ord-1", orders[0].OrderID)
			require.Equal(t, 1, orders[0].State.UnhealthyDistributeCount)
			require.NotZero(t, orders[0].State.UnhealthyDistributeWaitUntil)
		})

		deps.orderHeartbeatService.EXPECT().DoneUnhealthy(gomock.Any(), gomock.Any()).Return(nil)

		task.Execute(ctx)
		wg.Wait()
	})
}

func TestTaskOrderHealthCheck_CalculateRetryExponential(t *testing.T) {
	t.Run("calculate with 1 minute interval", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		cfg := defaultTaskOrderHealthCheckDbConfig()
		cfg.Interval = 1 * time.Minute
		task, _ := newTestTaskOrderHealthCheck(ctrl, cfg)

		require.Equal(t, 2*time.Minute, task.CalculateRetryExponential(1))
		require.Equal(t, 4*time.Minute, task.CalculateRetryExponential(2))
		require.Equal(t, 8*time.Minute, task.CalculateRetryExponential(3))
		require.Equal(t, 16*time.Minute, task.CalculateRetryExponential(4))
		require.Equal(t, 32*time.Minute, task.CalculateRetryExponential(5))
	})

	t.Run("calculate with 0.5 minute interval", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		cfg := defaultTaskOrderHealthCheckDbConfig()
		cfg.Interval = 30 * time.Second
		task, _ := newTestTaskOrderHealthCheck(ctrl, cfg)

		require.Equal(t, 2*time.Minute, task.CalculateRetryExponential(1))
		require.Equal(t, 4*time.Minute, task.CalculateRetryExponential(2))
		require.Equal(t, 8*time.Minute, task.CalculateRetryExponential(3))
		require.Equal(t, 16*time.Minute, task.CalculateRetryExponential(4))
		require.Equal(t, 32*time.Minute, task.CalculateRetryExponential(5))
	})

	t.Run("calculate with 0.2 minute interval", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		cfg := defaultTaskOrderHealthCheckDbConfig()
		cfg.Interval = 12 * time.Second
		task, _ := newTestTaskOrderHealthCheck(ctrl, cfg)

		require.Equal(t, 2*time.Minute, task.CalculateRetryExponential(1))
		require.Equal(t, 4*time.Minute, task.CalculateRetryExponential(2))
		require.Equal(t, 8*time.Minute, task.CalculateRetryExponential(3))
		require.Equal(t, 16*time.Minute, task.CalculateRetryExponential(4))
		require.Equal(t, 32*time.Minute, task.CalculateRetryExponential(5))
	})

	t.Run("calculate with 3 minute interval", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		cfg := defaultTaskOrderHealthCheckDbConfig()
		cfg.Interval = 3 * time.Minute
		task, _ := newTestTaskOrderHealthCheck(ctrl, cfg)

		require.Equal(t, 6*time.Minute, task.CalculateRetryExponential(1))
		require.Equal(t, 36*time.Minute, task.CalculateRetryExponential(2))
		require.Equal(t, 60*time.Minute, task.CalculateRetryExponential(3))
		require.Equal(t, 60*time.Minute, task.CalculateRetryExponential(4))
		require.Equal(t, 60*time.Minute, task.CalculateRetryExponential(5))
	})

	t.Run("calculate with 10 minute interval", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		cfg := defaultTaskOrderHealthCheckDbConfig()
		cfg.Interval = 10 * time.Minute
		task, _ := newTestTaskOrderHealthCheck(ctrl, cfg)

		require.Equal(t, 20*time.Minute, task.CalculateRetryExponential(1))
		require.Equal(t, 60*time.Minute, task.CalculateRetryExponential(2))
		require.Equal(t, 60*time.Minute, task.CalculateRetryExponential(3))
		require.Equal(t, 60*time.Minute, task.CalculateRetryExponential(4))
		require.Equal(t, 60*time.Minute, task.CalculateRetryExponential(5))
	})
}

type TaskOrderHealthCheckDeps struct {
	locker                *mock_locker.MockLocker
	dispatcher            *mock_dispatcher.MockDispatcher
	orderHeartbeatService *mock_service.MockOrderHeartbeatService
	distributionService   *mock_service.MockDistributionService
	featureFlagService    *mock_featureflag.MockService
	orderRepository       *mock_repository.MockOrderRepository
}

func defaultTaskOrderHealthCheckDbConfig() scheduler.TaskOrderHealthCheckDbConfig {
	return scheduler.TaskOrderHealthCheckDbConfig{
		Enable:                   true,
		Interval:                 3 * time.Minute,
		ForceUnlock:              false,
		DistributeCountLimit:     5,
		RetryDistributeWaitLimit: 1 * time.Hour,
	}
}

func newTestTaskOrderHealthCheck(ctrl *gomock.Controller, cfg scheduler.TaskOrderHealthCheckDbConfig) (*scheduler.TaskOrderHealthCheck, *TaskOrderHealthCheckDeps) {
	deps := &TaskOrderHealthCheckDeps{
		locker:                mock_locker.NewMockLocker(ctrl),
		dispatcher:            mock_dispatcher.NewMockDispatcher(ctrl),
		orderHeartbeatService: mock_service.NewMockOrderHeartbeatService(ctrl),
		distributionService:   mock_service.NewMockDistributionService(ctrl),
		featureFlagService:    mock_featureflag.NewMockService(ctrl),
		orderRepository:       mock_repository.NewMockOrderRepository(ctrl),
	}

	task := scheduler.NewTaskOrderHealthCheck(
		&scheduler.AtomicTaskOrderHealthCheckDbConfig{
			Config: cfg,
		},
		deps.locker,
		deps.dispatcher,
		deps.orderHeartbeatService,
		testmetric.NewStubMeter(),
		deps.distributionService,
		deps.featureFlagService,
		deps.orderRepository,
	)

	return task, deps
}
