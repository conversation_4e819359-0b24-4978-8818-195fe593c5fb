package scheduler

import (
	"context"
	"sync"
	"time"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type TaskUnacknowledgeReassignConfig struct {
	UnacknowledgeReassignInterval time.Duration `envconfig:"TASK_UNACKNOWLEDGE_REASSIGN_INTERVAL" default:"1m"`
}

type TaskUnacknowledgeReassign struct {
	Deps                                     order.ProviderDeps
	DriverStatusAPI                          *driver.DriverStatusAPI
	Cfg                                      TaskUnacknowledgeReassignConfig
	DriverUnacknowledgeCountMetric           metric.Counter
	CancelledOrderByUnAcknowledgeCountMetric metric.Counter
}

func ProvideTaskUnacknowledgeReassignConfig() TaskUnacknowledgeReassignConfig {
	var cfg TaskUnacknowledgeReassignConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

func ProvideTaskUnacknowledgeReassign(scheduler *Scheduler, cfg TaskUnacknowledgeReassignConfig, deps order.ProviderDeps, driverStatusAPI *driver.DriverStatusAPI, meter metric.Meter) *TaskUnacknowledgeReassign {
	task := &TaskUnacknowledgeReassign{
		Cfg:                                      cfg,
		Deps:                                     deps,
		DriverStatusAPI:                          driverStatusAPI,
		DriverUnacknowledgeCountMetric:           meter.GetCounter("fully_auto_accept_driver_unacknowledge", "for fully auto accept driver acknowledge: how many acknowledgement are after automatically reassign"),
		CancelledOrderByUnAcknowledgeCountMetric: meter.GetCounter("cancelled_order_by_unacknowledge", "for fully auto accept driver acknowledge: how many cancelled orders are after automatically reassign due to no acknowledge"),
	}
	if err := scheduler.Run("task_unacknowledge_reassign", task.Execute, cfg.UnacknowledgeReassignInterval); err != nil {
	}
	return task
}

func (t *TaskUnacknowledgeReassign) Execute(ctx context.Context) {
	infoLogger := logx.Info().Context(ctx).Str("task", string(TaskNameUnacknowledgeReassign))
	errLogger := logx.Error().Context(ctx).Str("task", string(TaskNameUnacknowledgeReassign))

	if !t.Deps.Cfg.AtomicOrderDBConfig.Get().EnableAcknowledgeFullyAutoAcceptWithReassigning {
		infoLogger.Msgf("%v has skipped because disabled by ENABLE_ACKNOWLEDGE_FULLY_AUTO_ACCEPT_WITH_REASSIGNING in db config", TaskNameUnacknowledgeReassign)
		return
	}

	tripIDs, err := t.Deps.UnAcknowledgeReassignRepo.GetDelayedTrips(ctx)
	if err != nil {
		errLogger.Err(err).Msgf("%v failed: cannot get list of trips", TaskNameUnacknowledgeReassign)
		return
	}

	trips, err := t.Deps.TripRepo.GetMany(ctx, tripIDs)
	if err != nil {
		errLogger.Err(err).Msgf("%v failed: cannot get trips", TaskNameUnacknowledgeReassign)
		return
	}

	wg := sync.WaitGroup{}
	muxmap := make(map[string]*sync.Mutex)
	infoLogger.Msgf("%v has started on %v trips", TaskNameUnacknowledgeReassign, len(trips))
	for _, trip := range trips {
		if muxmap[trip.DriverID] == nil {
			muxmap[trip.DriverID] = &sync.Mutex{}
		}
		mux := muxmap[trip.DriverID]
		trip := trip
		wg.Add(1)
		safe.GoFunc(func() {
			defer wg.Done()
			mux.Lock()
			defer mux.Unlock()
			if err := t.ReassignIfUnAcknowledge(ctx, trip); err != nil {
				errLogger.Err(err).Msgf("%v: process on order %s failed", TaskNameUnacknowledgeReassign, trip.TripID)
				return
			}
		})
	}
	wg.Wait()

	infoLogger.Msgf("%v done", TaskNameUnacknowledgeReassign)
}

func (t *TaskUnacknowledgeReassign) ReassignIfUnAcknowledge(ctx context.Context, trip model.Trip) error {
	infoLogger := logx.Info().Context(ctx).Str("task", string(TaskNameUnacknowledgeReassign))

	driv, err := t.Deps.DriverRepository.GetProfile(ctx, trip.DriverID)
	if err != nil {
		return err
	}

	if !driv.IsAcknowledgementRequired {
		infoLogger.Msgf("reassignIfUnAcknowledge has skipped because rider has already acknowledged driverID: %s", driv.DriverID)
		return nil
	}

	ordersToReassign := types.NewStringSet()
	onGoingOrders := trip.GetOngoingTripOrders()

	for _, o := range onGoingOrders {
		isFoodOrMart := o.ServiceType == model.ServiceFood || o.ServiceType == model.ServiceMart
		if isFoodOrMart {
			ordersToReassign.Add(o.OrderID)
		}
	}

	ordersToReassignElements := ordersToReassign.GetElements()
	if len(ordersToReassignElements) > 0 {
		for _, oid := range ordersToReassignElements {
			reason := "System Reassign - AUTO ACCEPT NO ACKNOWLEDGE"
			if err := t.Deps.Canceller.CancelOrder(ctx, oid, model.CancelDetail{
				CancelledBy:                    "RIDER_SYSTEM",
				Source:                         model.SourceDriver,
				Reason:                         reason,
				Remark:                         "ยกเลิกโดย system เพื่อทำการ Reassign ใหม่ เนื่องจาก Rider ไม่ได้กด acknowledge",
				Requestor:                      "system",
				BanDurationInMinute:            0,
				Label:                          reason,
				CancellationRateFree:           false,
				ShouldAutoClaim:                false,
				IsReassign:                     true,
				ReassignOnly:                   true,
				ForceCancellationRateFree:      true,
				IsFullyAutoAcceptNoAcknowledge: true,
			}, order.NoopStatusValidator, order.NoopServiceValidator, nil, false); err != nil {
				return err
			}
			logx.Info().Context(ctx).
				Str("task", string(TaskNameUnacknowledgeReassign)).
				Str("order_id", oid).
				Str("trip_id", trip.TripID).
				Msgf("%v: order %s in trip %s has been cancelled, due to unacknowledge", TaskNameUnacknowledgeReassign, oid, trip.TripID)
			t.CancelledOrderByUnAcknowledgeCountMetric.Inc()
		}

		isForceOffline, err := t.DriverStatusAPI.SetDriverToOffline(ctx, trip.DriverID, true)
		if err != nil {
			logx.Error().Context(ctx).
				Err(err).
				Str("method", string(TaskNameUnacknowledgeReassign)).
				Str("driver_id", trip.DriverID).
				Msgf("[%v] error force offline driver for driver %s", TaskNameUnacknowledgeReassign, trip.DriverID)
			return err
		}
		if isForceOffline {
			logx.Info().Context(ctx).Msgf("[%v] driver %s is now offline, due to unacknowledge", TaskNameUnacknowledgeReassign, trip.DriverID)
		}

		t.DriverUnacknowledgeCountMetric.Inc()
	}

	return nil
}
