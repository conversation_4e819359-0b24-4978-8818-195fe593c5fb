package aws

//go:generate mockgen -source=./rekognition.go -destination=./mock_aws/mock_rekognition.go -package=mock_aws

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/rekognition"
	"github.com/aws/aws-sdk-go/service/rekognition/rekognitioniface"
	"github.com/sirupsen/logrus"
)

type Rekognition interface {
	CompareFaces(p CompareParam) (CompareFacesOutput, error)
}

type RekognitionImpl struct {
	rekognition rekognitioniface.RekognitionAPI
}

func ProvideRekognitionClient(cfg Config) *RekognitionImpl {
	sess, err := session.NewSession(&aws.Config{Region: aws.String(cfg.Region), Credentials: credentials.NewStaticCredentials(cfg.<PERSON><PERSON>, cfg.<PERSON><PERSON>ey, cfg.SessionToken)})
	if err != nil {
		logrus.Errorf("unable to initialize rekognition session: %v", err)
		return nil
	}
	return &RekognitionImpl{
		rekognition: rekognition.New(sess),
	}
}

type CompareParam struct {
	SourceImage []byte
	TargetImage []byte
	Threshold   float64
}

func (r *RekognitionImpl) CompareFaces(p CompareParam) (CompareFacesOutput, error) {
	input := &rekognition.CompareFacesInput{
		SimilarityThreshold: aws.Float64(p.Threshold),
		SourceImage: &rekognition.Image{
			Bytes: p.SourceImage,
		},
		TargetImage: &rekognition.Image{
			Bytes: p.TargetImage,
		},
	}

	res, err := r.rekognition.CompareFaces(input)
	if err == nil && len(res.FaceMatches) > 0 {
		for _, matchedFace := range res.FaceMatches {
			return CompareFacesOutput{
				Similarity: *matchedFace.Similarity,
				Confidence: *matchedFace.Face.Confidence,
			}, nil
		}
	}

	logrus.Warnf("AWS Rekognition Error : %v", err)
	return CompareFacesOutput{}, err
}
