// Code generated by MockGen. DO NOT EDIT.
// Source: git.wndv.co/go/proto/lineman/egs/v1 (interfaces: BranchServiceClient)

// Package mock_grpc is a generated GoMock package.
package mock_grpc

import (
	context "context"
	reflect "reflect"

	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockBranchServiceClient is a mock of BranchServiceClient interface.
type MockBranchServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockBranchServiceClientMockRecorder
}

// MockBranchServiceClientMockRecorder is the mock recorder for MockBranchServiceClient.
type MockBranchServiceClientMockRecorder struct {
	mock *MockBranchServiceClient
}

// NewMockBranchServiceClient creates a new mock instance.
func NewMockBranchServiceClient(ctrl *gomock.Controller) *MockBranchServiceClient {
	mock := &MockBranchServiceClient{ctrl: ctrl}
	mock.recorder = &MockBranchServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBranchServiceClient) EXPECT() *MockBranchServiceClientMockRecorder {
	return m.recorder
}

// CheckExistingVendor mocks base method.
func (m *MockBranchServiceClient) CheckExistingVendor(arg0 context.Context, arg1 *egsv1.CheckExistingVendorRequest, arg2 ...grpc.CallOption) (*egsv1.CheckExistingVendorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckExistingVendor", varargs...)
	ret0, _ := ret[0].(*egsv1.CheckExistingVendorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckExistingVendor indicates an expected call of CheckExistingVendor.
func (mr *MockBranchServiceClientMockRecorder) CheckExistingVendor(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckExistingVendor", reflect.TypeOf((*MockBranchServiceClient)(nil).CheckExistingVendor), varargs...)
}

// CheckoutBranchStock mocks base method.
func (m *MockBranchServiceClient) CheckoutBranchStock(arg0 context.Context, arg1 *egsv1.CheckoutBranchStockRequest, arg2 ...grpc.CallOption) (*egsv1.CheckoutBranchStockResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckoutBranchStock", varargs...)
	ret0, _ := ret[0].(*egsv1.CheckoutBranchStockResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckoutBranchStock indicates an expected call of CheckoutBranchStock.
func (mr *MockBranchServiceClientMockRecorder) CheckoutBranchStock(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckoutBranchStock", reflect.TypeOf((*MockBranchServiceClient)(nil).CheckoutBranchStock), varargs...)
}

// GetBranch mocks base method.
func (m *MockBranchServiceClient) GetBranch(arg0 context.Context, arg1 *egsv1.GetBranchRequest, arg2 ...grpc.CallOption) (*egsv1.GetBranchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBranch", varargs...)
	ret0, _ := ret[0].(*egsv1.GetBranchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBranch indicates an expected call of GetBranch.
func (mr *MockBranchServiceClientMockRecorder) GetBranch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBranch", reflect.TypeOf((*MockBranchServiceClient)(nil).GetBranch), varargs...)
}

// ListBranch mocks base method.
func (m *MockBranchServiceClient) ListBranch(arg0 context.Context, arg1 *egsv1.ListBranchRequest, arg2 ...grpc.CallOption) (*egsv1.ListBranchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListBranch", varargs...)
	ret0, _ := ret[0].(*egsv1.ListBranchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBranch indicates an expected call of ListBranch.
func (mr *MockBranchServiceClientMockRecorder) ListBranch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBranch", reflect.TypeOf((*MockBranchServiceClient)(nil).ListBranch), varargs...)
}

// ReturnBranchStock mocks base method.
func (m *MockBranchServiceClient) ReturnBranchStock(arg0 context.Context, arg1 *egsv1.ReturnBranchStockRequest, arg2 ...grpc.CallOption) (*egsv1.ReturnBranchStockResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ReturnBranchStock", varargs...)
	ret0, _ := ret[0].(*egsv1.ReturnBranchStockResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ReturnBranchStock indicates an expected call of ReturnBranchStock.
func (mr *MockBranchServiceClientMockRecorder) ReturnBranchStock(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReturnBranchStock", reflect.TypeOf((*MockBranchServiceClient)(nil).ReturnBranchStock), varargs...)
}

// UpdateBranch mocks base method.
func (m *MockBranchServiceClient) UpdateBranch(arg0 context.Context, arg1 *egsv1.UpdateBranchRequest, arg2 ...grpc.CallOption) (*egsv1.UpdateBranchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateBranch", varargs...)
	ret0, _ := ret[0].(*egsv1.UpdateBranchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateBranch indicates an expected call of UpdateBranch.
func (mr *MockBranchServiceClientMockRecorder) UpdateBranch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateBranch", reflect.TypeOf((*MockBranchServiceClient)(nil).UpdateBranch), varargs...)
}
