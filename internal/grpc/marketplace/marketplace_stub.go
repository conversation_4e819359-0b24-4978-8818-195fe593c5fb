package marketplace

import (
	gomock "github.com/golang/mock/gomock"

	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/marketplace/mock_grpc"
)

var _ egsv1.MarketplaceServiceClient = &mock_grpc.MockMarketplaceServiceClient{}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideStubGRPCMarketplaceService(mockCtl *gomock.Controller) *mock_grpc.MockMarketplaceServiceClient {
	return mock_grpc.NewMockMarketplaceServiceClient(mockCtl)
}
