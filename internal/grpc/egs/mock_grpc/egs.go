// Code generated by MockGen. DO NOT EDIT.
// Source: git.wndv.co/go/proto/lineman/egs/v1 (interfaces: EGSServiceClient)

// Package mock_grpc is a generated GoMock package.
package mock_grpc

import (
	context "context"
	reflect "reflect"

	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockEGSServiceClient is a mock of EGSServiceClient interface.
type MockEGSServiceClient struct {
	ctrl     *gomock.Controller
	recorder *MockEGSServiceClientMockRecorder
}

// MockEGSServiceClientMockRecorder is the mock recorder for MockEGSServiceClient.
type MockEGSServiceClientMockRecorder struct {
	mock *MockEGSServiceClient
}

// NewMockEGSServiceClient creates a new mock instance.
func NewMockEGSServiceClient(ctrl *gomock.Controller) *MockEGSServiceClient {
	mock := &MockEGSServiceClient{ctrl: ctrl}
	mock.recorder = &MockEGSServiceClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEGSServiceClient) EXPECT() *MockEGSServiceClientMockRecorder {
	return m.recorder
}

// CreateEGSBatch mocks base method.
func (m *MockEGSServiceClient) CreateEGSBatch(arg0 context.Context, arg1 *egsv1.CreateEGSBatchRequest, arg2 ...grpc.CallOption) (*egsv1.CreateEGSBatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateEGSBatch", varargs...)
	ret0, _ := ret[0].(*egsv1.CreateEGSBatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateEGSBatch indicates an expected call of CreateEGSBatch.
func (mr *MockEGSServiceClientMockRecorder) CreateEGSBatch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateEGSBatch", reflect.TypeOf((*MockEGSServiceClient)(nil).CreateEGSBatch), varargs...)
}

// GetActiveEGSBatch mocks base method.
func (m *MockEGSServiceClient) GetActiveEGSBatch(arg0 context.Context, arg1 *egsv1.GetActiveEGSBatchRequest, arg2 ...grpc.CallOption) (*egsv1.GetActiveEGSBatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveEGSBatch", varargs...)
	ret0, _ := ret[0].(*egsv1.GetActiveEGSBatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveEGSBatch indicates an expected call of GetActiveEGSBatch.
func (mr *MockEGSServiceClientMockRecorder) GetActiveEGSBatch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveEGSBatch", reflect.TypeOf((*MockEGSServiceClient)(nil).GetActiveEGSBatch), varargs...)
}

// GetAvailableBranchesByProduct mocks base method.
func (m *MockEGSServiceClient) GetAvailableBranchesByProduct(arg0 context.Context, arg1 *egsv1.GetAvailableBranchesByProductRequest, arg2 ...grpc.CallOption) (*egsv1.GetAvailableBranchesByProductResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAvailableBranchesByProduct", varargs...)
	ret0, _ := ret[0].(*egsv1.GetAvailableBranchesByProductResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAvailableBranchesByProduct indicates an expected call of GetAvailableBranchesByProduct.
func (mr *MockEGSServiceClientMockRecorder) GetAvailableBranchesByProduct(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAvailableBranchesByProduct", reflect.TypeOf((*MockEGSServiceClient)(nil).GetAvailableBranchesByProduct), varargs...)
}

// GetBatchGoodsDetail mocks base method.
func (m *MockEGSServiceClient) GetBatchGoodsDetail(arg0 context.Context, arg1 *egsv1.GetBatchGoodsDetailRequest, arg2 ...grpc.CallOption) (*egsv1.GetBatchGoodsDetailResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBatchGoodsDetail", varargs...)
	ret0, _ := ret[0].(*egsv1.GetBatchGoodsDetailResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBatchGoodsDetail indicates an expected call of GetBatchGoodsDetail.
func (mr *MockEGSServiceClientMockRecorder) GetBatchGoodsDetail(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchGoodsDetail", reflect.TypeOf((*MockEGSServiceClient)(nil).GetBatchGoodsDetail), varargs...)
}

// GetBatchWithGoodsDetails mocks base method.
func (m *MockEGSServiceClient) GetBatchWithGoodsDetails(arg0 context.Context, arg1 *egsv1.GetBatchWithGoodsDetailsRequest, arg2 ...grpc.CallOption) (*egsv1.GetBatchWithGoodsDetailsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetBatchWithGoodsDetails", varargs...)
	ret0, _ := ret[0].(*egsv1.GetBatchWithGoodsDetailsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBatchWithGoodsDetails indicates an expected call of GetBatchWithGoodsDetails.
func (mr *MockEGSServiceClientMockRecorder) GetBatchWithGoodsDetails(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchWithGoodsDetails", reflect.TypeOf((*MockEGSServiceClient)(nil).GetBatchWithGoodsDetails), varargs...)
}

// GetEGSBatch mocks base method.
func (m *MockEGSServiceClient) GetEGSBatch(arg0 context.Context, arg1 *egsv1.GetEGSBatchRequest, arg2 ...grpc.CallOption) (*egsv1.GetEGSBatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEGSBatch", varargs...)
	ret0, _ := ret[0].(*egsv1.GetEGSBatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEGSBatch indicates an expected call of GetEGSBatch.
func (mr *MockEGSServiceClientMockRecorder) GetEGSBatch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEGSBatch", reflect.TypeOf((*MockEGSServiceClient)(nil).GetEGSBatch), varargs...)
}

// GetEGSBatchBlacklistedUsers mocks base method.
func (m *MockEGSServiceClient) GetEGSBatchBlacklistedUsers(arg0 context.Context, arg1 *egsv1.GetEGSBatchBlacklistedUsersRequest, arg2 ...grpc.CallOption) (*egsv1.GetEGSBatchBlacklistedUsersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEGSBatchBlacklistedUsers", varargs...)
	ret0, _ := ret[0].(*egsv1.GetEGSBatchBlacklistedUsersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEGSBatchBlacklistedUsers indicates an expected call of GetEGSBatchBlacklistedUsers.
func (mr *MockEGSServiceClientMockRecorder) GetEGSBatchBlacklistedUsers(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEGSBatchBlacklistedUsers", reflect.TypeOf((*MockEGSServiceClient)(nil).GetEGSBatchBlacklistedUsers), varargs...)
}

// GetEGSBatchGoods mocks base method.
func (m *MockEGSServiceClient) GetEGSBatchGoods(arg0 context.Context, arg1 *egsv1.GetEGSBatchGoodsRequest, arg2 ...grpc.CallOption) (*egsv1.GetEGSBatchGoodsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEGSBatchGoods", varargs...)
	ret0, _ := ret[0].(*egsv1.GetEGSBatchGoodsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEGSBatchGoods indicates an expected call of GetEGSBatchGoods.
func (mr *MockEGSServiceClientMockRecorder) GetEGSBatchGoods(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEGSBatchGoods", reflect.TypeOf((*MockEGSServiceClient)(nil).GetEGSBatchGoods), varargs...)
}

// GetEGSBatchUsers mocks base method.
func (m *MockEGSServiceClient) GetEGSBatchUsers(arg0 context.Context, arg1 *egsv1.GetEGSBatchUsersRequest, arg2 ...grpc.CallOption) (*egsv1.GetEGSBatchUsersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetEGSBatchUsers", varargs...)
	ret0, _ := ret[0].(*egsv1.GetEGSBatchUsersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEGSBatchUsers indicates an expected call of GetEGSBatchUsers.
func (mr *MockEGSServiceClientMockRecorder) GetEGSBatchUsers(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEGSBatchUsers", reflect.TypeOf((*MockEGSServiceClient)(nil).GetEGSBatchUsers), varargs...)
}

// GetPurchasingPower mocks base method.
func (m *MockEGSServiceClient) GetPurchasingPower(arg0 context.Context, arg1 *egsv1.GetPurchasingPowerRequest, arg2 ...grpc.CallOption) (*egsv1.GetPurchasingPowerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPurchasingPower", varargs...)
	ret0, _ := ret[0].(*egsv1.GetPurchasingPowerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPurchasingPower indicates an expected call of GetPurchasingPower.
func (mr *MockEGSServiceClientMockRecorder) GetPurchasingPower(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPurchasingPower", reflect.TypeOf((*MockEGSServiceClient)(nil).GetPurchasingPower), varargs...)
}

// ListEGSBatch mocks base method.
func (m *MockEGSServiceClient) ListEGSBatch(arg0 context.Context, arg1 *egsv1.ListEGSBatchRequest, arg2 ...grpc.CallOption) (*egsv1.ListEGSBatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListEGSBatch", varargs...)
	ret0, _ := ret[0].(*egsv1.ListEGSBatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListEGSBatch indicates an expected call of ListEGSBatch.
func (mr *MockEGSServiceClientMockRecorder) ListEGSBatch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListEGSBatch", reflect.TypeOf((*MockEGSServiceClient)(nil).ListEGSBatch), varargs...)
}

// UpdateEGSBatch mocks base method.
func (m *MockEGSServiceClient) UpdateEGSBatch(arg0 context.Context, arg1 *egsv1.UpdateEGSBatchRequest, arg2 ...grpc.CallOption) (*egsv1.UpdateEGSBatchResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateEGSBatch", varargs...)
	ret0, _ := ret[0].(*egsv1.UpdateEGSBatchResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateEGSBatch indicates an expected call of UpdateEGSBatch.
func (mr *MockEGSServiceClientMockRecorder) UpdateEGSBatch(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateEGSBatch", reflect.TypeOf((*MockEGSServiceClient)(nil).UpdateEGSBatch), varargs...)
}
