package egs

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"

	"git.wndv.co/go/grpclib"
	egsPb "git.wndv.co/go/proto/lineman/egs/v1"
)

//go:generate mockgen -destination=./mock_grpc/egs.go -package=mock_grpc git.wndv.co/go/proto/lineman/egs/v1 EGSServiceClient

// @@wire-set-name@@ name:"Main"
func ProvideGRPCEGSServiceClient(cfg Config) (egsPb.EGSServiceClient, func()) {
	ai := &AuthInterceptor{
		config: cfg,
	}

	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	cf, err := grpclib.NewConnectionFactory()
	if err != nil {
		logrus.Errorf("unable to NewConnectionFactory gRPC egs service: %s", err.Error())
		return nil, func() {}
	}

	conn, err := cf.CreateV2(ctx,
		cfg.Endpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()), grpc.WithUnaryInterceptor(ai.UnaryClientInterceptor),
	)
	if err != nil {
		logrus.Errorf("unable to connect gRPC egs service: %s", err.Error())
		return nil, func() {}
	}

	cleanup := func() {
		if closeErr := conn.Close(); closeErr != nil {
			logrus.Errorf("found error during close connection: %s", err.Error())
		}
	}

	return egsPb.NewEGSServiceClient(conn), cleanup
}

type AuthInterceptor struct {
	config Config
}

func (jwt *AuthInterceptor) UnaryClientInterceptor(ctx context.Context, method string, req interface{}, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	ctx = getEGSClientCtx(ctx, jwt.config)
	return invoker(ctx, method, req, reply, cc, opts...)
}

func getEGSClientCtx(inCtx context.Context, config Config) context.Context {
	m := map[string]string{
		"client_id":  config.ClientID,
		"secret_key": config.SecretKey,
		"key_id":     config.KeyID,
	}
	md := metadata.New(m)
	ctx := metadata.NewOutgoingContext(inCtx, md)
	return ctx
}
