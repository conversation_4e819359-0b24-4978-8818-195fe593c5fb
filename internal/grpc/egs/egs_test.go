package egs

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/metadata"
)

func Test_getEGSClientCtx(t *testing.T) {
	t.Run("should add secret correctly", func(tt *testing.T) {
		cfg := Config{ClientID: "LM-DRIVER", <PERSON><PERSON><PERSON>: "Secret", KeyID: "KeyID"}
		ctx := getEGSClientCtx(context.Background(), cfg)
		rawMD, _ := metadata.FromOutgoingContext(ctx)
		assert.Equal(tt, "LM-DRIVER", rawMD.Get("client_id")[0])
		assert.Equal(tt, "Secret", rawMD.Get("secret_key")[0])
		assert.Equal(tt, "KeyID", rawMD.Get("key_id")[0])
	})
}
