package polygon

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"git.wndv.co/go/grpclib"
	polygonPb "git.wndv.co/go/proto/lineman/polygon/v2"
)

//go:generate mockgen -destination=./mock_grpc/polygon.go -package=mock_grpc git.wndv.co/go/proto/lineman/polygon/v2 UserPolygonServiceClient

// @@wire-set-name@@ name:"Main"
func ProvideGRPCUserPolygonServiceClient(cfg Config) (polygonPb.UserPolygonServiceClient, func()) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	cf, err := grpclib.NewConnectionFactory()
	if err != nil {
		logrus.Errorf("unable to NewConnectionFactory gRPC polygon service: %s", err.Error())
		return nil, func() {}
	}

	conn, err := cf.CreateV2(ctx,
		cfg.Endpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		logrus.Errorf("unable to connect gRPC polygon service: %s", err.Error())
		return nil, func() {}
	}

	cleanup := func() {
		if closeErr := conn.Close(); closeErr != nil {
			logrus.Errorf("found error during close connection: %s", err.Error())
		}
	}

	return polygonPb.NewUserPolygonServiceClient(conn), cleanup
}
