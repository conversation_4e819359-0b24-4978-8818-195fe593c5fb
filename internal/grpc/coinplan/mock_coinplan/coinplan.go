// Code generated by MockGen. DO NOT EDIT.
// Source: ./coinplan.go

// Package mock_coinplan is a generated GoMock package.
package mock_coinplan

import (
	context "context"
	reflect "reflect"
	time "time"

	driver_mission_rewardv1 "git.wndv.co/go/proto/lineman/driver_mission_reward/v1"
	gomock "github.com/golang/mock/gomock"
)

// MockCoinPlanGRPCClient is a mock of CoinPlanGRPCClient interface.
type MockCoinPlanGRPCClient struct {
	ctrl     *gomock.Controller
	recorder *MockCoinPlanGRPCClientMockRecorder
}

// MockCoinPlanGRPCClientMockRecorder is the mock recorder for MockCoinPlanGRPCClient.
type MockCoinPlanGRPCClientMockRecorder struct {
	mock *MockCoinPlanGRPCClient
}

// NewMockCoinPlanGRPCClient creates a new mock instance.
func NewMockCoinPlanGRPCClient(ctrl *gomock.Controller) *MockCoinPlanGRPCClient {
	mock := &MockCoinPlanGRPCClient{ctrl: ctrl}
	mock.recorder = &MockCoinPlanGRPCClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCoinPlanGRPCClient) EXPECT() *MockCoinPlanGRPCClientMockRecorder {
	return m.recorder
}

// GetCoinPlan mocks base method.
func (m *MockCoinPlanGRPCClient) GetCoinPlan(ctx context.Context, driverID string, date time.Time) (driver_mission_rewardv1.CoinPlan, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCoinPlan", ctx, driverID, date)
	ret0, _ := ret[0].(driver_mission_rewardv1.CoinPlan)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCoinPlan indicates an expected call of GetCoinPlan.
func (mr *MockCoinPlanGRPCClientMockRecorder) GetCoinPlan(ctx, driverID, date interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCoinPlan", reflect.TypeOf((*MockCoinPlanGRPCClient)(nil).GetCoinPlan), ctx, driverID, date)
}
