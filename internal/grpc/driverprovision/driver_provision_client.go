package driverprovision

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"git.wndv.co/go/grpclib"
	driver_provisionv1 "git.wndv.co/go/proto/lineman/driver_provision/v1"
	absintheGRPC "git.wndv.co/lineman/absinthe/api/middleware/grpc"
)

//go:generate mockgen -source=./driver_provision_client.go -destination=./mock_grpc/mock_driver_provision_client.go -package=mock_grpc

type DriverProvisionClient interface {
	CreateRecommendation(ctx context.Context, in *driver_provisionv1.CreateRecommendationRequest, opts ...grpc.CallOption) (*driver_provisionv1.CreateRecommendationResponse, error)
	EnterRecommendedArea(ctx context.Context, in *driver_provisionv1.EnterRecommendedAreaRequest, opts ...grpc.CallOption) (*driver_provisionv1.EnterRecommendedAreaResponse, error)
}

type driverProvisionClient struct {
	serviceClient driver_provisionv1.RecommendationServiceClient
	config        Config
}

func (c *driverProvisionClient) CreateRecommendation(ctx context.Context, req *driver_provisionv1.CreateRecommendationRequest, opts ...grpc.CallOption) (*driver_provisionv1.CreateRecommendationResponse, error) {
	return c.serviceClient.CreateRecommendation(ctx, req, opts...)
}

func (c *driverProvisionClient) EnterRecommendedArea(ctx context.Context, req *driver_provisionv1.EnterRecommendedAreaRequest, opts ...grpc.CallOption) (*driver_provisionv1.EnterRecommendedAreaResponse, error) {
	return c.serviceClient.EnterRecommendedArea(ctx, req, opts...)
}

// @@wire-set-name@@ name:"Main"
func ProvideGRPCDriverProvisionClient(cfg Config) (DriverProvisionClient, func()) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	cf, err := grpclib.NewConnectionFactory()
	if err != nil {
		logrus.Errorf("unable to NewConnectionFactory gRPC DriverProvisionClient: %s", err.Error())
		return nil, func() {}
	}

	conn, err := cf.CreateV2(ctx,
		cfg.Endpoint,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithChainUnaryInterceptor(
			absintheGRPC.ErrorUnaryClientInterceptor,
		),
	)
	if err != nil {
		logrus.Errorf("unable to connect gRPC form service: %s", err.Error())
		return nil, func() {}
	}

	cleanup := func() {
		if closeErr := conn.Close(); closeErr != nil {
			logrus.Errorf("found error during close connection: %s", err.Error())
		}
	}

	return &driverProvisionClient{
		serviceClient: driver_provisionv1.NewRecommendationServiceClient(conn),
		config:        cfg,
	}, cleanup
}
