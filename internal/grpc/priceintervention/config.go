package priceintervention

import (
	"time"

	"github.com/kelseyhightower/envconfig"
)

type Config struct {
	IsEnabled                  bool          `envconfig:"PRICE_INTERVENTION_API_ENABLE"  default:"false"`
	IsTransportationPIPEnabled bool          `envconfig:"IS_TRANSPORTATION_PIP_ENABLED"  default:"true"`
	Endpoint                   string        `envconfig:"PRICE_INTERVENTION_API_ENDPOINT"  default:""`
	Timeout                    time.Duration `envconfig:"PRICE_INTERVENTION_API_TIMEOUT" default:"5s"`
}

func ProvideConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
