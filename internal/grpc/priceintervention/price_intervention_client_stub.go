package priceintervention

import (
	"context"

	"google.golang.org/grpc"

	interventionV1 "git.wndv.co/go/proto/lineman/price_intervention/v1"
	transportationV1 "git.wndv.co/go/proto/lineman/transportation_price_intervention/v1"
)

var _ PriceInterventionClient = &PriceInterventionClientStub{}

type PriceInterventionClientStub struct {
}

func (pipcs *PriceInterventionClientStub) GetEstimatedUserFareOnTopRequest(ctx context.Context, req *transportationV1.GetEstimatedUserFareOntopRequest, opts ...grpc.CallOption) (*transportationV1.GetEstimatedUserFareOntopResponse, error) {
	return nil, nil
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvidePriceInterventionServiceClientStub(cfg Config) *PriceInterventionClientStub {
	return &PriceInterventionClientStub{}
}

func (pipcs *PriceInterventionClientStub) GetDeliveryFeeOntopByRegion(ctx context.Context, req *interventionV1.GetDeliveryFeeOntopByRegionRequest, opts ...grpc.CallOption) (*interventionV1.GetDeliveryFeeOntopByRegionResponse, error) {
	return nil, nil
}
