package translation

import (
	"context"
	"errors"

	"google.golang.org/grpc"

	"git.wndv.co/go/grpclib"
	"git.wndv.co/go/logx/v2"
	grpcTranslation "git.wndv.co/go/proto/lineman/translation/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	errorgrpc "git.wndv.co/lineman/xgo/errors/mwgrpc"
	logxgrpc "git.wndv.co/lineman/xgo/logx/mwgrpc"
	tracegrpc "git.wndv.co/lineman/xgo/trace/mwgrpc"
)

// Config
type GRPCClient struct {
	Config Config
	Conn   *grpc.ClientConn
}

func ProvideTranslationGRPCClient(appCfg Config, cf *grpclib.ConnectionFactory) (*GRPCClient, func()) {
	if cf == nil {
		err := errors.New("ConnectionFactory is nil")
		logx.Error().Err(err).Msg("can not create grpc connection for TranslationGRPCClient")
		return nil, func() {}
	}
	conn, err := cf.CreateV2(context.Background(),
		appCfg.Endpoint,
		grpc.WithChainUnaryInterceptor(
			grpclib.GrpcDefaultTimeoutInterceptor(appCfg.DefaultInternalClientTimeout),
			grpclib.GrpcLogInterceptorCustom("translation-grpc", logxgrpc.GRPCOutgoingLogDecorator()),
			errorgrpc.ErrorUnaryClientInterceptor,
			tracegrpc.RequestIDUnaryClientInterceptor),
	)
	if err != nil {
		logx.Error().Err(err).Msg("can not create grpc connection for TranslationGRPCClient")
		return nil, func() {}
	}

	var cleanup func()
	if conn != nil {
		cleanup = func() {
			if err := conn.Close(); err != nil {
				logx.Error().Err(err).Msg("can not close grpc connection")
			}
		}
	}

	return &GRPCClient{
		Config: appCfg,
		Conn:   conn,
	}, cleanup
}

//go:generate go run github.com/golang/mock/mockgen -destination=./mock_translation/mock_translation.go -package=mock_translation git.wndv.co/go/proto/lineman/translation/v1 TranslationServiceClient

func ProvideTranslationServiceClient(translationGRPC *GRPCClient, meter metric.Meter) grpcTranslation.TranslationServiceClient {
	if translationGRPC == nil {
		err := errors.New("translationGRPC is nil")
		logx.Error().Err(err).Msg("can not create TranslationServiceClient")
		return nil
	}
	client := grpcTranslation.NewTranslationServiceClient(translationGRPC.Conn)
	return NewTranslationServiceClient(client, meter)
}
