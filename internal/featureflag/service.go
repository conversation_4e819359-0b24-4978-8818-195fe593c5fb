package featureflag

//go:generate mockgen -source=./service.go -destination=./mock_featureflag/mock_service.go -package=mock_featureflag

import (
	"context"

	"github.com/Unleash/unleash-client-go/v3"
	"github.com/Unleash/unleash-client-go/v3/api"
	unleashstrategy "github.com/Unleash/unleash-client-go/v3/strategy"
	"github.com/afex/hystrix-go/hystrix"
	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/go/unleash/admin"
	unleashCfg "git.wndv.co/go/unleash/config"
	"git.wndv.co/go/unleash/lmwnunleash"
)

type Service interface {
	IsEnabled(ctx context.Context, feature string, options ...unleash.FeatureOption) bool
	IsEnabledWithDefaultTrue(ctx context.Context, feature string, options ...unleash.FeatureOption) bool
	IsEnabledWithDefaultFalse(ctx context.Context, feature string, options ...unleash.FeatureOption) bool
	IsEnabledWithDefault(ctx context.Context, feature string, d bool, options ...unleash.FeatureOption) bool
	GetVariant(feature string, options ...unleash.VariantOption) *api.Variant
	ListFeatures(features []string) []api.Feature
	GetFeatureFlagConfig(ctx context.Context, featureName string) (admin.FeatureFlagConfig, error)
	UpdateFeatureFlagConfigToggleTurnOn(ctx context.Context, feature string) error
	UpdateFeatureFlagConfigToggleTurnOff(ctx context.Context, feature string) error
	UpdateStrategy(ctx context.Context, featureName string, strategy admin.UpdateStrategyReq) error
}

type service struct {
	unleash      lmwnunleash.LMWNUnleasher
	unleashAdmin admin.Admin
}

func NewFeatureFlagService(unleash lmwnunleash.LMWNUnleasher, unleashAdmin admin.Admin) Service {
	return &service{
		unleash:      unleash,
		unleashAdmin: unleashAdmin,
	}
}

type UnleashConfig struct {
	unleashCfg.UnleashConfig
	unleashCfg.AdminConfig
	IsUnleashEnabled bool `envconfig:"IS_UNLEASH_ENABLED"  default:"false"`
}

func ProvideUnleashConfig() (cfg UnleashConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

// @@wire-set-name@@ name:"Main"
func ProvideFeatureFlagService(cfg UnleashConfig, s []unleashstrategy.Strategy, customizer lmwnunleash.UnleashCustomizer) (Service, func()) {
	unleash, admin, cleanup := newUnleasher(cfg, s, customizer)
	return NewFeatureFlagService(unleash, admin), cleanup
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideFeatureFlagServiceForIntegration(cfg UnleashConfig, s []unleashstrategy.Strategy, customizer lmwnunleash.UnleashCustomizer, unl lmwnunleash.LMWNUnleasher) (Service, func()) {
	_, admin, cleanup := newUnleasher(cfg, s, customizer)
	return &service{
		unleash:      unl,
		unleashAdmin: admin,
	}, cleanup
}

func newUnleasher(config UnleashConfig, s []unleashstrategy.Strategy, customizer lmwnunleash.UnleashCustomizer) (lmwnunleash.LMWNUnleasher, admin.Admin, func()) {
	if !config.IsUnleashEnabled {
		logx.Warn().Msg("Unleash is not enabled. The Default LMWNUnleasher was provided")
		return &DefaultLMWNUnleasher{}, &DefaultUnleashAdmin{}, func() {}
	}

	hystrix.ConfigureCommand("provide-unleash", hystrix.CommandConfig{
		Timeout: 30000,
	})

	var unleash lmwnunleash.LMWNUnleasher
	var cleanupFunc func()

	err := hystrix.Do("provide-unleash", func() error {
		var err error
		unleash, cleanupFunc, err = lmwnunleash.ProvideUnleash(config.UnleashConfig, s, customizer)
		return err
	}, nil)

	if err != nil {
		logx.Error().Err(err).Msgf("ProvideUnleash Error. The Default LMWNUnleasher was provided")
		return &DefaultLMWNUnleasher{}, &DefaultUnleashAdmin{}, func() {}
	}

	unleashAdmin := admin.ProvideUnleashAdmin(config.AdminConfig)

	return unleash, unleashAdmin, cleanupFunc
}

func (s service) IsEnabledWithDefaultTrue(_ context.Context, feature string, options ...unleash.FeatureOption) bool {
	return s.unleash.IsEnabledWithDefault(feature, true, options...)
}

func (s service) IsEnabledWithDefaultFalse(_ context.Context, feature string, options ...unleash.FeatureOption) bool {
	return s.unleash.IsEnabledWithDefault(feature, false, options...)
}

func (s service) IsEnabledWithDefault(_ context.Context, feature string, d bool, options ...unleash.FeatureOption) bool {
	return s.unleash.IsEnabledWithDefault(feature, d, options...)
}

func (s service) IsEnabled(_ context.Context, feature string, options ...unleash.FeatureOption) bool {
	return s.unleash.IsEnabled(feature, options...)
}

func (s service) GetVariant(feature string, options ...unleash.VariantOption) *api.Variant {
	return s.unleash.GetVariant(feature, options...)
}

func (s service) ListFeatures(features []string) []api.Feature {
	return s.unleash.ListFeatures(features)
}

func (s service) GetFeatureFlagConfig(ctx context.Context, feature string) (admin.FeatureFlagConfig, error) {
	return s.unleashAdmin.GetFeatureFlagConfig(ctx, feature)
}

func (s service) UpdateFeatureFlagConfigToggleTurnOn(ctx context.Context, feature string) error {
	return s.unleashAdmin.TurnOn(ctx, feature)
}
func (s service) UpdateFeatureFlagConfigToggleTurnOff(ctx context.Context, feature string) error {
	return s.unleashAdmin.TurnOff(ctx, feature)
}

func (s service) UpdateStrategy(ctx context.Context, feature string, strategy admin.UpdateStrategyReq) error {
	return s.unleashAdmin.UpdateStrategy(ctx, feature, strategy)
}
