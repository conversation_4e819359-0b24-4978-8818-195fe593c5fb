package featureflag

type Flag struct {
	Label string
	Name  string
}

var (
	IsUOBPayoutEnabled                              = &Flag{Label: "UOB Payout Enabled", Name: "fleet.driver.enable-uob-payout.kill-switch"}
	IsUOBPayoutAutoApprovalTransactionEnabled       = &Flag{Label: "UOB Auto Withdrawal Approval Enabled", Name: "fleet.driver.uob-payout-auto-withdrawal-approval-transaction.kill-switch"}
	ServicePreferenceAllowedTypesForSelection       = &Flag{Name: "fleet.driver.service-preference-allowed-types-for-selection.kill-switch"}
	ServicePreferenceMergeFoodMart                  = &Flag{Name: "fleet.driver.service-preference-allowed-types-for-selection.merge-food-mart"}
	ReviseBulkARCR                                  = &Flag{Name: "fleet.driver.revise-bulk-arcr.enabled"}
	SupplyPositioningServices                       = &Flag{Name: "fleet.driver.supply-positioning-services.kill-switch"}
	IsExceedArLogAlertEnabled                       = &Flag{Name: "fleet.driver-eventtracking-consumer.enable-exceed-ar-log-alert.kill-switch"}
	IsQuotePIPOnTopEnabled                          = &Flag{Name: "fleet.driver.quote.pip-ontop.kill-switch"}
	IsProfileSummaryOfChangeEnabled                 = &Flag{Name: "fleet.driver.profile.summary-of-change.kill-switch"}
	IsLINEUserIDUsed                                = &Flag{Name: "fleet.driver.is-line-user-id-used.kill-switch"}
	DriverAvailableCapacity                         = &Flag{Name: "fleet.driver.driver-available-capacity.kill-switch"}
	IsQRIncidentOrderCompletionEnabled              = &Flag{Label: "QR Incident Order Completion Enabled", Name: "fleet.driver.enable-qr-incident-order-completion.kill-switch"}
	IsUploadSlipByPassValidationEnabled             = &Flag{Name: "payment.payment-grpc.enable-upload-slip-by-pass-validation.kill-switch"}
	IsVerifyOrderTimestampEnabled                   = &Flag{Name: "fleet.order.verify-order-timestamp-enabled.kill-switch", Label: "Verify Order Timestamp Enabled"}
	IsDriverLoginBackwardCompatibleEnabled          = &Flag{Name: "fleet.driver.driver-login-backward-compatible.kill-switch"}
	IsNewPurchasingPowerCalculationEnabled          = &Flag{Name: "fleet.driver.new-purchasing-power-calculation.kill-switch"}
	LINETokenLifetimeAllowedDurationInSecondsConfig = &Flag{Name: "fleet.driver.line-token-lifetime-allowed-duration-in-seconds.config", Label: "LINE Token allowed duration in seconds"}
	IsPublishMissionLogOrderCompletedEnabled        = &Flag{Name: "fleet.driver.publish-mission-log-order-completed.kill-switch"}
	IsPublishMissionLogTripCompletedEnabled         = &Flag{Name: "fleet.driver.publish-mission-log-trip-completed.kill-switch"}
	IsQRPaymentClaimFormEnabled                     = &Flag{Name: "fleet.driver.enabled-qr-payment-claim-form.kill-switch"}
	IsNewRegisterOnboardingExamEnabled              = &Flag{Name: "fleet.driver.enabled-new-register-question.kill-switch"}
	IsEnableBikeAgentModelCommissionAndTaxV2        = &Flag{Label: "Agent Model Commission and Tax V2 (Bike Service)", Name: "fleet.driver.agent-model.commission-tax-v2.bike.kill-switch"}
	IsEnableRevampRiderReviewRestaurant             = &Flag{Label: "Revamp Rider Review Restaurant", Name: "fleet.driver.enabled-revamp-rider-review-restaurant.kill-switch"}
	IsRetryBikeQRPaymentDetailEnabled               = &Flag{Name: "fleet.driver.enabled-retry-bike-qr-payment-detail.kill-switch"}
	ReportAdminServiceAreaApiWasCalled              = &Flag{Name: "fleet.driver.report-admin-service-areas-api-called.kill-switch", Label: "Report Sentry error when admin service areas was called"}
	IsAccountingHubTransactionCollectorEnabled      = &Flag{Name: "fleet.driver.enabled-accounting-hub-transaction-collector.kill-switch"}
	IsBCPFlagEnabled                                = &Flag{Name: "fleet.driver.enabled-bcp.kill-switch"}
	IsEnabledFleetPoolService                       = &Flag{Label: "Use Fleet Pool for Driver Location Data", Name: "fleet.driver.fleet-pool-service-enabled.kill-switch"}
	IsEnabledPurchasingRiderGearLimitation          = &Flag{Label: "Enable prevent rider purchase rider gear limit exceed", Name: "fleet.driver.prevent-fraud-rider-gear-purchase.kill-switch"}
	IsLINEInternalOAuthTokenEnabled                 = &Flag{Label: "Enable LINE Internal OAuth Token", Name: "fleet.driver.line-internal-oauth-token-enabled.kill-switch"}
	IsEnabledAssignAPIOnDistribution                = &Flag{Label: "Enable assign api on distribution", Name: "fleet.driver.enabled-assign-api-on-distribution.release"}
	IsEnabledReassignViaDispatcherAPI               = &Flag{Label: "Enable reassign via dispatcher API", Name: "fleet.driver.is-enabled-reassign-via-dispatcher-api.release"}
	IsOrderMemoTranslationAsyncEnabled              = &Flag{Label: "Enable order memo translation async", Name: "fleet.driver.is-order-memo-translation-async-enabled.release"}
	IsEnabledCoinStore                              = &Flag{Label: "Enable coin store", Name: "fleet.fleet-bff.coin-store.kill-switch"}
	IsEnabledDeferStandaloneTransitionToSingle      = &Flag{Label: "Enable defer standalone transition to single optimize", Name: "fleet.driver.defer-standalone-transition-to-single.kill-switch"}
)

func GetFeatureFlagList() []Flag {
	return []Flag{
		*IsUOBPayoutEnabled,
		*IsUOBPayoutAutoApprovalTransactionEnabled,
		*IsEnableBikeAgentModelCommissionAndTaxV2,
	}
}

func GetFeatureFlagNameList() []string {
	nameList := []string{}
	for _, flag := range GetFeatureFlagList() {
		nameList = append(nameList, flag.Name)
	}
	return nameList
}

func IsAllowToUpdateFeatureFlag(name string) (Flag, bool) {
	for _, featureFlag := range GetFeatureFlagList() {
		if featureFlag.Name == name {
			return featureFlag, true
		}
	}
	return Flag{}, false
}
