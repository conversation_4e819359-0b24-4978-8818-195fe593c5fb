package featureflag_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_unleash"
)

type unleashServiceDeps struct {
	unleash *mock_unleash.MockLMWNUnleasher
	admin   *mock_unleash.MockAdmin
}

func newTestService(t *testing.T) (featureflag.Service, *unleashServiceDeps, func()) {
	ctl := gomock.NewController(t)

	deps := &unleashServiceDeps{
		unleash: mock_unleash.NewMockLMWNUnleasher(ctl),
		admin:   mock_unleash.NewMockAdmin(ctl),
	}

	return featureflag.NewFeatureFlagService(deps.unleash, deps.admin), deps, func() { ctl.Finish() }
}

func Test_service_IsEnabledWithDefaultTrue(t *testing.T) {
	t.Run("should return true if feature is enabled", func(t *testing.T) {

		srv, deps, finish := newTestService(t)
		defer finish()
		deps.unleash.EXPECT().IsEnabledWithDefault(featureflag.IsUOBPayoutEnabled.Name, true, gomock.Any()).Return(true)
		result := srv.IsEnabledWithDefaultTrue(context.Background(), featureflag.IsUOBPayoutEnabled.Name)
		assert.True(t, result)
	})

	t.Run("should return false if feature is disabled", func(t *testing.T) {

		srv, deps, finish := newTestService(t)
		defer finish()
		deps.unleash.EXPECT().IsEnabledWithDefault(featureflag.IsUOBPayoutEnabled.Name, true, gomock.Any()).Return(false)
		result := srv.IsEnabledWithDefaultTrue(context.Background(), featureflag.IsUOBPayoutEnabled.Name)
		assert.False(t, result)
	})

}
