package serviceprovider

import (
	"fmt"
	"net/http/httptest"
	"testing"

	gin "github.com/gin-gonic/gin"
	"github.com/golang/mock/gomock"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/repositorytestutil"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func TestAcceptOrderHandler(t *testing.T) {
	makeReq := func(orderID, driverID string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("POST", fmt.Sprintf("/v1/order/%s/accept", orderID), nil)
		gctx.Params = append(gctx.Params, gin.Param{Key: "orderID", Value: orderID})
		driver.SetDriverIDToContext(gctx, driverID)
		gctx.Request.Header.Set("X-DEVICE-ID", "test-device-id")
		return gctx, recorder
	}
	t.Run("prevent accept same order when db replication lag", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		handler, deps := newTestServiceSelector(ctrl, tt)
		orderID := "LMF-1"
		driverID := "driv:1"
		ctx, _ := makeReq(orderID, driverID)

		order := model.Order{Quote: model.Quote{ServiceType: model.ServiceFood}}
		deps.OrderRepo.EXPECT().Get(gomock.Any(), orderID, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return(&order, nil)
		deps.MockFoodProvider.EXPECT().AcceptOrderHandler(gomock.Any(), &order, orderID, driverID, "test-device-id")
		handler.AcceptOrderHandler(ctx)
	})
}

func TestUpdateOrderStatusHandler(t *testing.T) {
	makeReq := func(orderID, driverID string) (*gin.Context, *httptest.ResponseRecorder) {
		gctx, recorder := testutil.TestRequestContext("PUT", fmt.Sprintf("/v1/order/%s/next-status", orderID), nil)
		gctx.Params = append(gctx.Params, gin.Param{Key: "orderID", Value: orderID})
		driver.SetDriverIDToContext(gctx, driverID)
		return gctx, recorder
	}
	t.Run("check read primary to prevent error from db replication lag", func(tt *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		handler, deps := newTestServiceSelector(ctrl, tt)
		orderID := "LMF-1"
		driverID := "driv:1"
		ctx, _ := makeReq(orderID, driverID)

		order := model.Order{Quote: model.Quote{ServiceType: model.ServiceFood}}
		deps.OrderRepo.EXPECT().Get(gomock.Any(), orderID, repositorytestutil.NewRepositoryOptionMatcher(repository.WithReadPrimary)).Return(&order, nil)
		deps.MockFoodProvider.EXPECT().UpdateOrderStatus(gomock.Any(), &order, orderID, driverID)
		handler.UpdateOrderStatus(ctx)
	})
}

type serviceSelectorDeps struct {
	OrderRepo        *mock_repository.MockOrderRepository
	MockFoodProvider *MockServiceProvider
}

func newTestServiceSelector(ctrl *gomock.Controller, t *testing.T) (*ServiceSelector, *serviceSelectorDeps) {
	deps := &serviceSelectorDeps{
		OrderRepo:        mock_repository.NewMockOrderRepository(ctrl),
		MockFoodProvider: NewMockServiceProvider(ctrl),
	}

	serviceSelector := ProvideServiceSelectorAPI(deps.OrderRepo, ProvideRegistry())
	serviceSelector.RegisterSelector(model.ServiceFood, deps.MockFoodProvider)
	return serviceSelector, deps
}
