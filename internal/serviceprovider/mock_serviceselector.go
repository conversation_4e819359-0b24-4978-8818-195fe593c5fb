// Code generated by MockGen. DO NOT EDIT.
// Source: ./service_selector.go

// Package serviceprovider is a generated GoMock package.
package serviceprovider

import (
	reflect "reflect"

	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockOrderServiceSelector is a mock of OrderServiceSelector interface.
type MockOrderServiceSelector struct {
	ctrl     *gomock.Controller
	recorder *MockOrderServiceSelectorMockRecorder
}

// MockOrderServiceSelectorMockRecorder is the mock recorder for MockOrderServiceSelector.
type MockOrderServiceSelectorMockRecorder struct {
	mock *MockOrderServiceSelector
}

// NewMockOrderServiceSelector creates a new mock instance.
func NewMockOrderServiceSelector(ctrl *gomock.Controller) *MockOrderServiceSelector {
	mock := &MockOrderServiceSelector{ctrl: ctrl}
	mock.recorder = &MockOrderServiceSelectorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderServiceSelector) EXPECT() *MockOrderServiceSelectorMockRecorder {
	return m.recorder
}

// AcceptOrderHandler mocks base method.
func (m *MockOrderServiceSelector) AcceptOrderHandler(ctx *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AcceptOrderHandler", ctx)
}

// AcceptOrderHandler indicates an expected call of AcceptOrderHandler.
func (mr *MockOrderServiceSelectorMockRecorder) AcceptOrderHandler(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptOrderHandler", reflect.TypeOf((*MockOrderServiceSelector)(nil).AcceptOrderHandler), ctx)
}

// AdminCompleteOrder mocks base method.
func (m *MockOrderServiceSelector) AdminCompleteOrder(ctx *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AdminCompleteOrder", ctx)
}

// AdminCompleteOrder indicates an expected call of AdminCompleteOrder.
func (mr *MockOrderServiceSelectorMockRecorder) AdminCompleteOrder(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminCompleteOrder", reflect.TypeOf((*MockOrderServiceSelector)(nil).AdminCompleteOrder), ctx)
}

// UpdateOrderStatus mocks base method.
func (m *MockOrderServiceSelector) UpdateOrderStatus(ctx *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdateOrderStatus", ctx)
}

// UpdateOrderStatus indicates an expected call of UpdateOrderStatus.
func (mr *MockOrderServiceSelectorMockRecorder) UpdateOrderStatus(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderStatus", reflect.TypeOf((*MockOrderServiceSelector)(nil).UpdateOrderStatus), ctx)
}

// UpdatePrice mocks base method.
func (m *MockOrderServiceSelector) UpdatePrice(ctx *gin.Context) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "UpdatePrice", ctx)
}

// UpdatePrice indicates an expected call of UpdatePrice.
func (mr *MockOrderServiceSelectorMockRecorder) UpdatePrice(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePrice", reflect.TypeOf((*MockOrderServiceSelector)(nil).UpdatePrice), ctx)
}
