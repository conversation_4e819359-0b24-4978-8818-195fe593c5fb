// Code generated by MockGen. DO NOT EDIT.
// Source: ./environment.go

// Package mock_sim is a generated GoMock package.
package mock_sim

import (
	context "context"
	reflect "reflect"
	time "time"

	gomock "github.com/golang/mock/gomock"
)

// MockEnvironment is a mock of Environment interface.
type MockEnvironment struct {
	ctrl     *gomock.Controller
	recorder *MockEnvironmentMockRecorder
}

// MockEnvironmentMockRecorder is the mock recorder for MockEnvironment.
type MockEnvironmentMockRecorder struct {
	mock *MockEnvironment
}

// NewMockEnvironment creates a new mock instance.
func NewMockEnvironment(ctrl *gomock.Controller) *MockEnvironment {
	mock := &MockEnvironment{ctrl: ctrl}
	mock.recorder = &MockEnvironmentMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEnvironment) EXPECT() *MockEnvironmentMockRecorder {
	return m.recorder
}

// After mocks base method.
func (m *MockEnvironment) After(duration time.Duration) <-chan time.Time {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "After", duration)
	ret0, _ := ret[0].(<-chan time.Time)
	return ret0
}

// After indicates an expected call of After.
func (mr *MockEnvironmentMockRecorder) After(duration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "After", reflect.TypeOf((*MockEnvironment)(nil).After), duration)
}

// Execute mocks base method.
func (m *MockEnvironment) Execute(fn func()) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Execute", fn)
}

// Execute indicates an expected call of Execute.
func (mr *MockEnvironmentMockRecorder) Execute(fn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockEnvironment)(nil).Execute), fn)
}

// Now mocks base method.
func (m *MockEnvironment) Now() time.Time {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Now")
	ret0, _ := ret[0].(time.Time)
	return ret0
}

// Now indicates an expected call of Now.
func (mr *MockEnvironmentMockRecorder) Now() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Now", reflect.TypeOf((*MockEnvironment)(nil).Now))
}

// RunGo mocks base method.
func (m *MockEnvironment) RunGo(fn func()) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RunGo", fn)
}

// RunGo indicates an expected call of RunGo.
func (mr *MockEnvironmentMockRecorder) RunGo(fn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunGo", reflect.TypeOf((*MockEnvironment)(nil).RunGo), fn)
}

// RunGoWithCtx mocks base method.
func (m *MockEnvironment) RunGoWithCtx(ctx context.Context, fn func()) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RunGoWithCtx", ctx, fn)
}

// RunGoWithCtx indicates an expected call of RunGoWithCtx.
func (mr *MockEnvironmentMockRecorder) RunGoWithCtx(ctx, fn interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunGoWithCtx", reflect.TypeOf((*MockEnvironment)(nil).RunGoWithCtx), ctx, fn)
}

// Schedule mocks base method.
func (m *MockEnvironment) Schedule(fn func(), at time.Time) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Schedule", fn, at)
}

// Schedule indicates an expected call of Schedule.
func (mr *MockEnvironmentMockRecorder) Schedule(fn, at interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Schedule", reflect.TypeOf((*MockEnvironment)(nil).Schedule), fn, at)
}

// Sleep mocks base method.
func (m *MockEnvironment) Sleep(duration time.Duration) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "Sleep", duration)
}

// Sleep indicates an expected call of Sleep.
func (mr *MockEnvironmentMockRecorder) Sleep(duration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Sleep", reflect.TypeOf((*MockEnvironment)(nil).Sleep), duration)
}
