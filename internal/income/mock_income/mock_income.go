// Code generated by MockGen. DO NOT EDIT.
// Source: ./service.go

// Package mock_income is a generated GoMock package.
package mock_income

import (
	context "context"
	reflect "reflect"
	time "time"

	income "git.wndv.co/lineman/fleet-distribution/internal/income"
	types "git.wndv.co/lineman/fleet-distribution/internal/types"
	gomock "github.com/golang/mock/gomock"
)

// MockIncomeSummaryService is a mock of IncomeSummaryService interface.
type MockIncomeSummaryService struct {
	ctrl     *gomock.Controller
	recorder *MockIncomeSummaryServiceMockRecorder
}

// MockIncomeSummaryServiceMockRecorder is the mock recorder for MockIncomeSummaryService.
type MockIncomeSummaryServiceMockRecorder struct {
	mock *MockIncomeSummaryService
}

// NewMockIncomeSummaryService creates a new mock instance.
func NewMockIncomeSummaryService(ctrl *gomock.Controller) *MockIncomeSummaryService {
	mock := &MockIncomeSummaryService{ctrl: ctrl}
	mock.recorder = &MockIncomeSummaryServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIncomeSummaryService) EXPECT() *MockIncomeSummaryServiceMockRecorder {
	return m.recorder
}

// CalculateProrateCoin mocks base method.
func (m *MockIncomeSummaryService) CalculateProrateCoin(ctx context.Context, start, end time.Time, driverID string) (types.Money, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateProrateCoin", ctx, start, end, driverID)
	ret0, _ := ret[0].(types.Money)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateProrateCoin indicates an expected call of CalculateProrateCoin.
func (mr *MockIncomeSummaryServiceMockRecorder) CalculateProrateCoin(ctx, start, end, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateProrateCoin", reflect.TypeOf((*MockIncomeSummaryService)(nil).CalculateProrateCoin), ctx, start, end, driverID)
}

// CalculateProrateIncentive mocks base method.
func (m *MockIncomeSummaryService) CalculateProrateIncentive(ctx context.Context, today time.Time, driverID string, periodOrders, dayOrders int) (types.Money, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateProrateIncentive", ctx, today, driverID, periodOrders, dayOrders)
	ret0, _ := ret[0].(types.Money)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateProrateIncentive indicates an expected call of CalculateProrateIncentive.
func (mr *MockIncomeSummaryServiceMockRecorder) CalculateProrateIncentive(ctx, today, driverID, periodOrders, dayOrders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateProrateIncentive", reflect.TypeOf((*MockIncomeSummaryService)(nil).CalculateProrateIncentive), ctx, today, driverID, periodOrders, dayOrders)
}

// CountCompletedOrdersInDay mocks base method.
func (m *MockIncomeSummaryService) CountCompletedOrdersInDay(ctx context.Context, date time.Time, driverID string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountCompletedOrdersInDay", ctx, date, driverID)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountCompletedOrdersInDay indicates an expected call of CountCompletedOrdersInDay.
func (mr *MockIncomeSummaryServiceMockRecorder) CountCompletedOrdersInDay(ctx, date, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountCompletedOrdersInDay", reflect.TypeOf((*MockIncomeSummaryService)(nil).CountCompletedOrdersInDay), ctx, date, driverID)
}

// Query mocks base method.
func (m *MockIncomeSummaryService) Query(ctx context.Context, req income.IncomeSummaryRequest, opts ...income.IncomeSummaryOption) (income.IncomeSummary, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Query", varargs...)
	ret0, _ := ret[0].(income.IncomeSummary)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Query indicates an expected call of Query.
func (mr *MockIncomeSummaryServiceMockRecorder) Query(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Query", reflect.TypeOf((*MockIncomeSummaryService)(nil).Query), varargs...)
}
