//go:generate mockgen -source=./service.go -destination=./mock_aggregate/mock_aggregate.go -package=mock_aggregate

package aggregate

import (
	"context"
	"fmt"

	"github.com/pkg/errors"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var ErrZeroIncome = errors.New("driver has zero income")

type IncomeAggregateOption func(req *IncomeAggregateRequest)

func WithRemark(remark string) IncomeAggregateOption {
	return func(req *IncomeAggregateRequest) {
		req.remark = remark
	}
}

type IncomeAggregateService interface {
	Aggregate(ctx context.Context, req IncomeAggregateRequest, opts ...IncomeAggregateOption) error
	Inc(ctx context.Context, req IncomeIncRequest) error
}

type IncomeAggregateServiceImpl struct {
	incomeSummaryService         income.IncomeSummaryService
	incomeDailySummaryRepository repository.IncomeDailySummaryRepository
}

func (svc *IncomeAggregateServiceImpl) Aggregate(ctx context.Context, req IncomeAggregateRequest, opts ...IncomeAggregateOption) error {
	for _, opt := range opts {
		opt(&req)
	}

	dailyIncome, err := svc.incomeSummaryService.Query(ctx, income.IncomeSummaryRequest{
		Granularity: income.DAILY,
		StartDate:   req.Date,
		DriverId:    req.DriverId,
	}, income.WithoutCompare(), income.WithRealtimeQuery())
	if err != nil {
		return errors.Wrap(err, "unable to query daily rider income")
	}

	if dailyIncome.Summary.TotalIncome() == 0 {
		return ErrZeroIncome
	}

	incomeDailySummary := svc.convertToDailyIncomeSummary(req, dailyIncome)

	_, err = svc.incomeDailySummaryRepository.Upsert(ctx, incomeDailySummary)
	if err != nil {
		return errors.Wrap(err, "unable to upsert rider income")
	}

	return nil
}

func (svc *IncomeAggregateServiceImpl) Inc(ctx context.Context, req IncomeIncRequest) error {
	fromInclusive := timeutil.DateTruncateTZ(req.Date, timeutil.BangkokLocation())
	toExclusive := fromInclusive.AddDate(0, 0, 1)

	q := persistence.BuildIncomeDailySummaryQuery()
	q.WithDriverID(req.DriverId)
	q.WithFromInclusive(fromInclusive)
	q.WithToExclusive(toExclusive)

	dailyIncome, err := svc.incomeSummaryService.Query(ctx, income.IncomeSummaryRequest{
		Granularity: income.DAILY,
		StartDate:   req.Date,
		DriverId:    req.DriverId,
	}, income.WithoutCompare(), income.WithRealtimeQuery(), income.WithTransactionIDs(req.TransactionIDs))
	if err != nil {
		return fmt.Errorf("unable to query daily rider income: %v", err)
	}

	updater := model.NewIncomeDailySummary(
		fromInclusive,
		req.DriverId,
		dailyIncome.Summary.TotalIncome(),
		dailyIncome.Summary.TotalTrips(),
		dailyIncome.Summary.TotalOrders(),
		model.NewIncomeDailyDetail(
			dailyIncome.Summary.TotalDriverWage(),
			dailyIncome.Summary.TotalIncentive(),
			dailyIncome.Summary.TotalWithholdingTax(),
			dailyIncome.Summary.TotalTipAndOther(),
			dailyIncome.Summary.TotalEGSOnTopBonus(),
			dailyIncome.Summary.TotalGuaranteeIncentive(),
		),
		"",
	)

	r, err := svc.incomeDailySummaryRepository.Inc(ctx, q, updater)
	if err != nil {
		return fmt.Errorf("unable to increment rider income: %v", err)
	}

	logx.Info().
		Str("driver_id", req.DriverId).
		Int64("matched_count", r.MatchedCount).
		Int64("modified_count", r.ModifiedCount).
		Int64("upserted_count", r.UpsertedCount).
		Interface("upserted_id", r.UpsertedID).
		Msg("increment rider income successfully")

	return nil
}

func (svc *IncomeAggregateServiceImpl) convertToDailyIncomeSummary(req IncomeAggregateRequest, dailyIncome income.IncomeSummary) model.IncomeDailySummary {
	out := model.NewIncomeDailySummary(
		dailyIncome.MetaData.From,
		req.DriverId,
		dailyIncome.Summary.TotalIncome(),
		dailyIncome.Summary.TotalTrips(),
		dailyIncome.Summary.TotalOrders(),
		model.NewIncomeDailyDetail(
			dailyIncome.Summary.TotalDriverWage(),
			dailyIncome.Summary.TotalIncentive(),
			dailyIncome.Summary.TotalWithholdingTax(),
			dailyIncome.Summary.TotalTipAndOther(),
			dailyIncome.Summary.TotalEGSOnTopBonus(),
			dailyIncome.Summary.TotalGuaranteeIncentive(),
		),
		req.remark,
	)

	return out
}

func ProvideIncomeAggregateService(
	incomeSummaryService income.IncomeSummaryService,
	incomeDailySummaryRepository repository.IncomeDailySummaryRepository,
) *IncomeAggregateServiceImpl {
	return &IncomeAggregateServiceImpl{
		incomeSummaryService:         incomeSummaryService,
		incomeDailySummaryRepository: incomeDailySummaryRepository,
	}
}
