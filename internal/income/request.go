package income

import (
	"fmt"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func parseRequest(req IncomeSummaryRequest, opts ...IncomeSummaryOption) (IncomeSummaryRequest, error) {
	r := IncomeSummaryRequest{
		Granularity:   req.Granularity,
		DriverId:      req.DriverId,
		compare:       true,
		realtimeQuery: false,
	}

	for _, opt := range opts {
		opt(&r)
	}

	switch r.Granularity {
	case DAILY, WEEKLY, MONTHLY:
		r.StartDate = req.StartDate.In(timeutil.BangkokLocation())
		r.from, r.to, r.toExclusive = DateBeginningAndEnd(r.Granularity, r.StartDate)
	case CUSTOM:
		if !r.customStartInclusive.IsZero() && !r.customEndInclusive.IsZero() {
			r.customStartInclusive = r.customStartInclusive.In(timeutil.BangkokLocation())
			r.customEndInclusive = r.customEndInclusive.In(timeutil.BangkokLocation())
			r.from = r.customStartInclusive
			r.to = r.customEndInclusive
			r.toExclusive = timeutil.DateTruncate(r.customEndInclusive.AddDate(0, 0, 1))
			r.compare = false
		}
	case LASTDAYAGO:
		if r.dayAgo != 0 {
			r.fromAgo = r.fromAgo.In(timeutil.BangkokLocation())
			r.to = timeutil.DateCeiling(r.fromAgo)
			r.from = timeutil.DateTruncate(r.to.AddDate(0, 0, -r.dayAgo))
			r.toExclusive = timeutil.DateTruncate(r.to.AddDate(0, 0, 1))
			r.compare = false
		}

	default:
		return IncomeSummaryRequest{}, fmt.Errorf("invalid granularity %v", r.Granularity)
	}

	return r, nil
}

func previous(req IncomeSummaryRequest) IncomeSummaryRequest {
	clone := req

	var currentFrom time.Time
	currentFrom, _, _ = DateBeginningAndEnd(clone.Granularity, req.from)

	var date time.Time
	switch clone.Granularity {
	case DAILY:
		date = currentFrom.AddDate(0, 0, -1)
	case WEEKLY:
		date = currentFrom.AddDate(0, 0, -7)
	case MONTHLY:
		date = currentFrom.AddDate(0, -1, 0)
	}

	clone.from, clone.to, clone.toExclusive = DateBeginningAndEnd(clone.Granularity, date)

	return clone
}

func today(req IncomeSummaryRequest, now time.Time) IncomeSummaryRequest {
	clone := req
	clone.Granularity = DAILY
	clone.from, clone.to, clone.toExclusive = DateBeginningAndEnd(clone.Granularity, now)

	return clone
}

func dayToYesterday(req IncomeSummaryRequest, now time.Time) IncomeSummaryRequest {
	clone := req

	yesterday := now.AddDate(0, 0, -1)

	clone.to = timeutil.DateCeiling(yesterday)
	clone.toExclusive = timeutil.DateTruncate(now)

	return clone
}
