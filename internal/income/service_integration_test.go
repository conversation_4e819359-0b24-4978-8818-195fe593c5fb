//go:build integration_test
// +build integration_test

package income_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestIncomeSummaryServiceImpl_Query(t *testing.T) {
	t.<PERSON>llel()

	t.Run("food order", func(t *testing.T) {
		t.<PERSON>()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/query/food")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view mo daily 20-02-2023", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2023, 2, 20, 0, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMD9YHGIL",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2023, 2, 20, 23, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(74.67), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(74.67), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(36.98), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(40), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalGuaranteeIncentive())
			assert.Equal(t, types.NewMoney(2.31), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			assert.Equal(t, 2, resp.Summary.TotalOrders())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2023, 2, 20, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(74.67), do.TotalIncome())
		})
		// When
		t.Run("view daily 13-12-2022", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(70.47), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(-10.05), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(10), resp.Summary.TotalGuaranteeIncentive())
			assert.Equal(t, types.NewMoney(2.03), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			assert.Equal(t, 1, resp.Summary.TotalOrders())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(70.47), do.TotalIncome())
		})

		// When
		t.Run("view daily 12-12-2022 in the past", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 12, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(80.52), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(80.52), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(83), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalGuaranteeIncentive())
			assert.Equal(t, types.NewMoney(2.48), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 2, resp.Summary.TotalTrips())
			assert.Equal(t, 2, resp.Summary.TotalOrders())

			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 12, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(80.52), do.TotalIncome())
		})

		// When
		t.Run("view daily 11-12-2022 in the past", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 11, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(0), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(0), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalGuaranteeIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 0, resp.Summary.TotalTrips())
			assert.Equal(t, 0, resp.Summary.TotalOrders())

			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 11, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(0), do.TotalIncome())
		})

		// When
		t.Run("view weekly 12-12-2022 to 18-12-2022", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(150.99), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(-271.12), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(125.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(10), resp.Summary.TotalGuaranteeIncentive())
			assert.Equal(t, types.NewMoney(4.51), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 3, resp.Summary.TotalTrips())
			assert.Equal(t, 3, resp.Summary.TotalOrders())

			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 12, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(80.52), do.TotalIncome())
			do = resp.Overview.DailyOverview[1]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(70.47), do.TotalIncome())
		})

		t.Run("view weekly 05-12-2022 to 11-12-2022 in the past", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 7, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(422.11), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(422.11), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(353.6), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(30), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalGuaranteeIncentive())
			assert.Equal(t, types.NewMoney(11.49), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(50), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 6, resp.Summary.TotalTrips())
			assert.Equal(t, 6, resp.Summary.TotalOrders())

			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 5, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(251.33), do.TotalIncome())
			do = resp.Overview.DailyOverview[3]
			assert.Equal(t, time.Date(2022, 12, 8, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(170.78), do.TotalIncome())
		})

		// When
		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(573.1), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(573.1), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(479.1), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(45), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(10), resp.Summary.TotalGuaranteeIncentive())
			assert.Equal(t, types.NewMoney(16), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(55), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 9, resp.Summary.TotalTrips())
			assert.Equal(t, 9, resp.Summary.TotalOrders())

			require.Len(t, resp.Overview.DailyOverview, 31)
			do := resp.Overview.DailyOverview[4]
			assert.Equal(t, time.Date(2022, 12, 5, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(251.33), do.TotalIncome())
			do = resp.Overview.DailyOverview[7]
			assert.Equal(t, time.Date(2022, 12, 8, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(170.78), do.TotalIncome())
			do = resp.Overview.DailyOverview[11]
			assert.Equal(t, time.Date(2022, 12, 12, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(80.52), do.TotalIncome())
			do = resp.Overview.DailyOverview[12]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(70.47), do.TotalIncome())
		})
	})

	t.Run("coin incentive", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/query/coin")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily 13-12-2022", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(60.77), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(-19.75), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.73), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			assert.Equal(t, 1, resp.Summary.TotalOrders())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})

		// When
		t.Run("view weekly 12-12-2022 to 18-12-2022", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(141.29), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(-280.82), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(125.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(4.21), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 3, resp.Summary.TotalTrips())
			assert.Equal(t, 3, resp.Summary.TotalOrders())

			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 12, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(80.52), do.TotalIncome())
			do = resp.Overview.DailyOverview[1]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})

		// When
		t.Run("view weekly 05-12-2022 to 11-12-2022 in the past", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 7, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(422.11), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(422.11), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(353.6), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(30), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(11.49), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(50), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 6, resp.Summary.TotalTrips())
			assert.Equal(t, 6, resp.Summary.TotalOrders())

			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 5, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(251.33), do.TotalIncome())
			do = resp.Overview.DailyOverview[3]
			assert.Equal(t, time.Date(2022, 12, 8, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(170.78), do.TotalIncome())
		})

		// When
		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMDSAC9I3",
			}

			ctx := timeutil.NewContextWithTime(context.Background(), time.Date(2022, 12, 13, 20, 0, 0, 0, timeutil.BangkokLocation()))
			resp, err := container.IncomeSummaryService.Query(ctx, req)

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(563.4), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(563.4), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(479.1), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(45), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(15.7), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(55), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 9, resp.Summary.TotalTrips())
			assert.Equal(t, 9, resp.Summary.TotalOrders())

			require.Len(t, resp.Overview.DailyOverview, 31)
			do := resp.Overview.DailyOverview[4]
			assert.Equal(t, time.Date(2022, 12, 5, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(251.33), do.TotalIncome())
			do = resp.Overview.DailyOverview[7]
			assert.Equal(t, time.Date(2022, 12, 8, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(170.78), do.TotalIncome())
			do = resp.Overview.DailyOverview[11]
			assert.Equal(t, time.Date(2022, 12, 12, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(80.52), do.TotalIncome())
			do = resp.Overview.DailyOverview[12]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})
	})
}

func TestIncomeSummaryServiceImpl_Query_WithRealTimeQueryOption(t *testing.T) {
	t.Parallel()

	t.Run("food order", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/realtime/food")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(60.77), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(60.77), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.73), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			assert.Equal(t, 1, resp.Summary.TotalOrders())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})

		// When
		t.Run("view weekly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(60.77), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(60.77), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.73), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[1]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})

		// When
		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(60.77), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(60.77), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.73), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 31)
			do := resp.Overview.DailyOverview[12]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})
	})

	t.Run("messenger order", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/realtime/messenger")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(95.62), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(95.62), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(95.62), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 2, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(95.62), do.TotalIncome())
		})

		t.Run("view weekly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(95.62), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(95.62), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(95.62), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 2, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[1]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(95.62), do.TotalIncome())
		})

		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(95.62), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(95.62), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(95.62), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 2, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 31)
			do := resp.Overview.DailyOverview[12]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(95.62), do.TotalIncome())
		})
	})

	t.Run("compensation (without original trans)", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/realtime/compensation")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(41.22), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(41.22), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.28), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(41.22), do.TotalIncome())
		})

		// When
		t.Run("view weekly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(41.22), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(41.22), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.28), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[1]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(41.22), do.TotalIncome())
		})

		// When
		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(41.22), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(41.22), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.28), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 31)
			do := resp.Overview.DailyOverview[12]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(41.22), do.TotalIncome())
		})
	})

	t.Run("messenger cash coupon and ontop order", func(t *testing.T) {
		t.Parallel()

		givenFixtures := []string{
			// backward compatible
			"messenger_cash_coupon_without_commission_subtype",
			"messenger_cash_coupon",
		}
		for _, givenFixture := range givenFixtures {
			t.Run(givenFixture, func(t *testing.T) {

				// Given
				container := ittest.NewContainer(t)
				err := container.Fixtures.InitFixture(container.DBConnectionForTest, fmt.Sprintf("fixtures_income_summary/service/realtime/%s", givenFixture))
				require.NoError(t, err, "unable to setup fixture")

				// When
				t.Run("view daily", func(t *testing.T) {
					t.Parallel()

					req := income.IncomeSummaryRequest{
						StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
						Granularity: income.DAILY,
						DriverId:    "LMDSAC9I3",
					}
					resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

					// Then
					require.NoError(t, err)
					assert.Equal(t, types.Money(79.47), resp.Summary.TotalIncome())
					assert.Equal(t, types.Money(79.47), resp.Summary.Changes())
					assert.Equal(t, types.NewMoney(69.27), resp.Summary.TotalDriverWage())
					assert.Equal(t, types.NewMoney(10.2), resp.Summary.TotalIncentive())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
					assert.Equal(t, 1, resp.Summary.TotalTrips())
					require.Len(t, resp.Overview.DailyOverview, 1)
					do := resp.Overview.DailyOverview[0]
					assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
					assert.Equal(t, types.Money(79.47), do.TotalIncome())
				})

				t.Run("view weekly", func(t *testing.T) {
					t.Parallel()

					req := income.IncomeSummaryRequest{
						StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
						Granularity: income.WEEKLY,
						DriverId:    "LMDSAC9I3",
					}
					resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

					// Then
					require.NoError(t, err)
					assert.Equal(t, types.Money(79.47), resp.Summary.TotalIncome())
					assert.Equal(t, types.Money(79.47), resp.Summary.Changes())
					assert.Equal(t, types.NewMoney(69.27), resp.Summary.TotalDriverWage())
					assert.Equal(t, types.NewMoney(10.2), resp.Summary.TotalIncentive())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
					assert.Equal(t, 1, resp.Summary.TotalTrips())
					require.Len(t, resp.Overview.DailyOverview, 7)
					do := resp.Overview.DailyOverview[1]
					assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
					assert.Equal(t, types.Money(79.47), do.TotalIncome())
				})

				t.Run("view monthly", func(t *testing.T) {
					t.Parallel()

					req := income.IncomeSummaryRequest{
						StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
						Granularity: income.MONTHLY,
						DriverId:    "LMDSAC9I3",
					}
					resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

					// Then
					require.NoError(t, err)
					assert.Equal(t, types.Money(79.47), resp.Summary.TotalIncome())
					assert.Equal(t, types.Money(79.47), resp.Summary.Changes())
					assert.Equal(t, types.NewMoney(69.27), resp.Summary.TotalDriverWage())
					assert.Equal(t, types.NewMoney(10.2), resp.Summary.TotalIncentive())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
					assert.Equal(t, 1, resp.Summary.TotalTrips())
					require.Len(t, resp.Overview.DailyOverview, 31)
					do := resp.Overview.DailyOverview[12]
					assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
					assert.Equal(t, types.Money(79.47), do.TotalIncome())
				})
			})
		}
	})

	t.Run("messenger order (additional service)", func(t *testing.T) {
		t.Parallel()

		givenFixtures := []string{
			// backward compatible
			"messenger_additional_services_without_commission_subtype",
			"messenger_additional_services",
		}

		for _, givenFixture := range givenFixtures {
			t.Run(givenFixture, func(t *testing.T) {
				// Given
				container := ittest.NewContainer(t)
				err := container.Fixtures.InitFixture(container.DBConnectionForTest, fmt.Sprintf("fixtures_income_summary/service/realtime/%s", givenFixture))
				require.NoError(t, err, "unable to setup fixture")

				// When
				t.Run("view daily", func(t *testing.T) {
					t.Parallel()

					req := income.IncomeSummaryRequest{
						StartDate:   time.Date(2023, 3, 9, 11, 0, 0, 0, timeutil.BangkokLocation()),
						Granularity: income.DAILY,
						DriverId:    "LMD33A06D",
					}
					resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

					// Then
					require.NoError(t, err)
					assert.Equal(t, types.Money(89.25), resp.Summary.TotalIncome()) // 45 (DELIVERY_FEE_USER) + 10 (ADDITIONAL_FEE) + 50 (COUPON) - 6.75 (COMMISSION WAGE) - 9 (COMMISSION ADDITIONAL SERVICE)
					assert.Equal(t, types.Money(89.25), resp.Summary.Changes())
					assert.Equal(t, types.NewMoney(89.25), resp.Summary.TotalDriverWage())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
					assert.Equal(t, 1, resp.Summary.TotalTrips())
					require.Len(t, resp.Overview.DailyOverview, 1)
					do := resp.Overview.DailyOverview[0]
					assert.Equal(t, time.Date(2023, 3, 9, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
					assert.Equal(t, types.Money(89.25), do.TotalIncome())
				})

				t.Run("view weekly", func(t *testing.T) {
					t.Parallel()

					req := income.IncomeSummaryRequest{
						StartDate:   time.Date(2023, 3, 9, 11, 0, 0, 0, timeutil.BangkokLocation()),
						Granularity: income.WEEKLY,
						DriverId:    "LMD33A06D",
					}
					resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

					// Then
					require.NoError(t, err)
					assert.Equal(t, types.Money(89.25), resp.Summary.TotalIncome()) // 45 (DELIVERY_FEE_USER) + 10 (ADDITIONAL_FEE) + 50 (COUPON) - 6.75 (COMMISSION WAGE) - 9 (COMMISSION ADDITIONAL SERVICE)
					assert.Equal(t, types.Money(89.25), resp.Summary.Changes())
					assert.Equal(t, types.NewMoney(89.25), resp.Summary.TotalDriverWage())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
					assert.Equal(t, 1, resp.Summary.TotalTrips())
					require.Len(t, resp.Overview.DailyOverview, 7)
					do := resp.Overview.DailyOverview[3]
					assert.Equal(t, time.Date(2023, 3, 9, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
					assert.Equal(t, types.Money(89.25), do.TotalIncome())
				})

				t.Run("view monthly", func(t *testing.T) {
					t.Parallel()

					req := income.IncomeSummaryRequest{
						StartDate:   time.Date(2023, 3, 9, 11, 0, 0, 0, timeutil.BangkokLocation()),
						Granularity: income.MONTHLY,
						DriverId:    "LMD33A06D",
					}
					resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

					// Then
					require.NoError(t, err)
					assert.Equal(t, types.Money(89.25), resp.Summary.TotalIncome()) // 45 (DELIVERY_FEE_USER) + 10 (ADDITIONAL_FEE) + 50 (COUPON) - 6.75 (COMMISSION WAGE) - 9 (COMMISSION ADDITIONAL SERVICE)
					assert.Equal(t, types.Money(89.25), resp.Summary.Changes())
					assert.Equal(t, types.NewMoney(89.25), resp.Summary.TotalDriverWage())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalIncentive())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
					assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
					assert.Equal(t, 1, resp.Summary.TotalTrips())
					require.Len(t, resp.Overview.DailyOverview, 31)
					do := resp.Overview.DailyOverview[8]
					assert.Equal(t, time.Date(2023, 3, 9, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
					assert.Equal(t, types.Money(89.25), do.TotalIncome())
				})
			})
		}

	})

	t.Run("coin incentive", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/realtime/coin")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(60.77), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(60.77), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.73), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			assert.Equal(t, 1, resp.Summary.TotalOrders())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})

		// When
		t.Run("view weekly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(60.77), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(60.77), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.73), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[1]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})

		// When
		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2022, 12, 13, 11, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMDSAC9I3",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(60.77), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(60.77), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(42.5), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(15), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(1.73), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(5), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 31)
			do := resp.Overview.DailyOverview[12]
			assert.Equal(t, time.Date(2022, 12, 13, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(60.77), do.TotalIncome())
		})
	})

	t.Run("bike on-topped orders with both subsidize and coupon types", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/realtime/bike")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2023, 10, 25, 10, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMD750JHN",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(146.3*2), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(146.3*2), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(138.7*2), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(7.6*2), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 2, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2023, 10, 25, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(146.3*2), do.TotalIncome())
		})

		t.Run("view weekly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2023, 10, 25, 10, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMD750JHN",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(146.3*2), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(146.3*2), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(138.7*2), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(7.6*2), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 2, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[2]
			assert.Equal(t, time.Date(2023, 10, 25, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(146.3*2), do.TotalIncome())
		})

		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2023, 10, 25, 10, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMD750JHN",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(146.3*2), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(146.3*2), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(138.7*2), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(7.6*2), resp.Summary.TotalIncentive())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 2, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 31)
			do := resp.Overview.DailyOverview[24]
			assert.Equal(t, time.Date(2023, 10, 25, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(146.3*2), do.TotalIncome())
		})
	})

	t.Run("bike qr completed order with payment pending", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/realtime/bike_qr")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2024, 8, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMD750JHN",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(12.6), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(12.6), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(12.6), resp.Summary.TotalIncentive()) // 14.0 (On-top) - 1.4 (On-top Commission)
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2024, 8, 19, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(12.6), do.TotalIncome())
		})

		t.Run("view weekly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2024, 8, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMD750JHN",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(12.6), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(12.6), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(12.6), resp.Summary.TotalIncentive()) // 14.0 (On-top) - 1.4 (On-top Commission)
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 7)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2024, 8, 19, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(12.6), do.TotalIncome())
		})

		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2024, 8, 19, 0, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMD750JHN",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(12.6), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(12.6), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalDriverWage())
			assert.Equal(t, types.NewMoney(12.6), resp.Summary.TotalIncentive()) // 14.0 (On-top) - 1.4 (On-top Commission)
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, types.NewMoney(0), resp.Summary.TotalTipAndOther())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 31)
			do := resp.Overview.DailyOverview[18]
			assert.Equal(t, time.Date(2024, 8, 19, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(12.6), do.TotalIncome())
		})
	})

	t.Run("EGS on-topped orders", func(t *testing.T) {
		t.Parallel()

		// Given
		container := ittest.NewContainer(t)
		err := container.Fixtures.InitFixture(container.DBConnectionForTest, "fixtures_income_summary/service/realtime/egs")
		require.NoError(t, err, "unable to setup fixture")

		// When
		t.Run("view daily", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2024, 7, 25, 7, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.DAILY,
				DriverId:    "LMD0HDWL5",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(96.03), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(96.03), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(99), resp.Summary.TotalEGSOnTopBonus())
			assert.Equal(t, types.NewMoney(2.97), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, 1, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 1)
			do := resp.Overview.DailyOverview[0]
			assert.Equal(t, time.Date(2024, 7, 25, 0, 0, 0, 0, timeutil.BangkokLocation()), do.Date())
			assert.Equal(t, types.Money(96.03), do.TotalIncome())
		})

		t.Run("view weekly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2024, 7, 25, 10, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.WEEKLY,
				DriverId:    "LMD0HDWL5",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(288.09), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(288.09), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(297), resp.Summary.TotalEGSOnTopBonus())
			assert.Equal(t, types.NewMoney(8.91), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, 3, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 7)
		})

		t.Run("view monthly", func(t *testing.T) {
			t.Parallel()

			req := income.IncomeSummaryRequest{
				StartDate:   time.Date(2024, 7, 25, 10, 0, 0, 0, timeutil.BangkokLocation()),
				Granularity: income.MONTHLY,
				DriverId:    "LMD0HDWL5",
			}
			resp, err := container.IncomeSummaryService.Query(context.Background(), req, income.WithRealtimeQuery())

			// Then
			require.NoError(t, err)
			assert.Equal(t, types.Money(288.09), resp.Summary.TotalIncome())
			assert.Equal(t, types.Money(288.09), resp.Summary.Changes())
			assert.Equal(t, types.NewMoney(297), resp.Summary.TotalEGSOnTopBonus())
			assert.Equal(t, types.NewMoney(8.91), resp.Summary.TotalWithholdingTax())
			assert.Equal(t, 3, resp.Summary.TotalTrips())
			require.Len(t, resp.Overview.DailyOverview, 31)
		})
	})

}
