package income

import (
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type classifier struct {
}

func newClassifier() *classifier {
	return &classifier{}
}

type classified struct {
	c completedOrderClassified
	n nonCompletedOrderClassified
}

type completedOrderClassified struct {
	ca       completedOrderActivity
	orderMap map[string]model.Order
}

type nonCompletedOrderClassified struct {
	na                   nonCompletedOrderActivity
	wht                  map[string]model.Transaction
	notFoundOriginalTran map[string]struct{}
}

func (svc *classifier) classify(ca completedOrderActivity, na nonCompletedOrderActivity) classified {
	classified := classified{
		c: completedOrderClassified{
			ca: ca,
		},
		n: nonCompletedOrderClassified{
			na: na,
		},
	}

	svc.classifyCompletedOrderActivity(&classified.c)

	svc.classifyNonCompletedOrderActivity(&classified.n)

	return classified
}

func (svc *classifier) classifyCompletedOrderActivity(c *completedOrderClassified) {
	orderMap := orders(c.ca.orders).toMap()
	c.orderMap = orderMap
}

func (svc *classifier) classifyNonCompletedOrderActivity(n *nonCompletedOrderClassified) {
	// as of first launch, compensation transaction is not taken into account yet.
	// but WHT of compensation transactions are into transaction list that
	// we queried but left orphan without original transaction since query has filtered out.
	// so, here we keep the list of not found orignal tran and when calculate we skip it.
	n.wht, n.notFoundOriginalTran = svc.findTaxRefIdPair(n.na.transactions)
}

func (svc *classifier) findTaxRefIdPair(trans []model.Transaction) (map[string]model.Transaction, map[string]struct{}) {
	wht := map[string]model.Transaction{}
	notFoundOriginalTran := map[string]struct{}{}

	taxTrans := svc.findTaxRefTrans(trans)

	for _, taxTran := range taxTrans {
		found := svc.tryToFindOriginalTranWithTaxRefId(taxTran, trans)
		if found {
			wht[taxTran.TransactionID] = taxTran
		} else {
			notFoundOriginalTran[taxTran.TransactionID] = struct{}{}
		}
	}

	return wht, notFoundOriginalTran
}

func (svc *classifier) findTaxRefTrans(trans []model.Transaction) []model.Transaction {
	taxesTrans := []model.Transaction{}
	for _, t := range trans {
		if t.Info.Type == model.WithholdingTransactionType {
			taxesTrans = append(taxesTrans, t)
		}
	}
	return taxesTrans
}

func (svc *classifier) tryToFindOriginalTranWithTaxRefId(taxTran model.Transaction, trans []model.Transaction) bool {
	if taxTran.Info.TaxRefID != "" {
		for _, t := range trans {
			if taxTran.TransactionID != t.TransactionID {
				if taxTran.Info.TaxRefID == t.Info.TaxRefID {
					return true
				}
			}
		}
	}
	return false
}
