package income

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type calculator struct{}

func newCalculator() *calculator {
	return &calculator{}
}

func (svc *calculator) calculate(req IncomeSummaryRequest, data classified) IncomeSummary {
	income := newIncomeSummary(req)
	svc.calculateCompletedOrderClassified(&income, data.c)
	svc.calculateNonCompletedOrderClassified(&income, data.n)
	return income
}

func (svc *calculator) calculateFromAggregated(req IncomeSummaryRequest, aggregated []model.IncomeDailySummary) IncomeSummary {
	income := newIncomeSummary(req)

	for _, v := range aggregated {
		income.Summary.totalCompletedTrip += v.TotalTrip
		income.Summary.totalIncome = income.Summary.totalIncome.Add(v.TotalIncome)
		income.Summary.totalDriverWage = income.Summary.totalDriverWage.Add(v.IncomeDetail.TotalWage)
		income.Summary.totalWithholdingTax = income.Summary.totalWithholdingTax.Add(v.IncomeDetail.TotalWithholdingTax)
		income.Summary.totalIncentive = income.Summary.totalIncentive.Add(v.IncomeDetail.TotalIncentive)
		income.Summary.totalGuaranteeIncentive = income.Summary.totalGuaranteeIncentive.Add(v.IncomeDetail.TotalGuaranteeIncentive)
		income.Summary.totalTip = income.Summary.totalTip.Add(v.IncomeDetail.TotalTip)
		income.Summary.totalCompletedOrder += v.TotalOrder
		income.Summary.totalEGSOnTopBonus = income.Summary.totalEGSOnTopBonus.Add(v.IncomeDetail.TotalEGSOnTopBonus)

		do, exists := income.Overview.m[v.Date.Unix()]
		if !exists {
			continue
		}
		do.totalIncome = v.TotalIncome
		do.totalCompletedTrip = v.TotalTrip
		do.totalDriverWage = v.IncomeDetail.TotalWage
		do.totalWithholdingTax = v.IncomeDetail.TotalWithholdingTax
		do.totalIncentive = v.IncomeDetail.TotalIncentive
		do.totalGuaranteeIncentive = v.IncomeDetail.TotalGuaranteeIncentive
		do.totalTipAndOther = v.IncomeDetail.TotalTip
		do.totalCompletedOrder = v.TotalOrder
	}

	return income
}

func (svc *calculator) calculateFromTodayRealtimeAndAggregated(req IncomeSummaryRequest, today IncomeSummary, aggregatedSummary IncomeSummary) IncomeSummary {
	income := newIncomeSummary(req)

	list := []IncomeSummary{today, aggregatedSummary}

	for _, v := range list {
		income.Summary.totalCompletedTrip += v.Summary.TotalCompletedTrip()
		income.Summary.totalIncome = income.Summary.totalIncome.Add(v.Summary.TotalIncome())
		income.Summary.totalDriverWage = income.Summary.totalDriverWage.Add(v.Summary.TotalDriverWage())
		income.Summary.totalWithholdingTax = income.Summary.totalWithholdingTax.Add(v.Summary.TotalWithholdingTax())
		income.Summary.totalIncentive = income.Summary.totalIncentive.Add(v.Summary.TotalIncentive())
		income.Summary.totalGuaranteeIncentive = income.Summary.totalGuaranteeIncentive.Add(v.Summary.TotalGuaranteeIncentive())
		income.Summary.totalTip = income.Summary.totalTip.Add(v.Summary.TotalTipAndOther())
		income.Summary.totalCompletedOrder += v.Summary.totalCompletedOrder
		income.Summary.totalEGSOnTopBonus = income.Summary.totalEGSOnTopBonus.Add(v.Summary.TotalEGSOnTopBonus())

		for _, vv := range v.Overview.DailyOverview {
			do, exist := income.Overview.m[vv.Date().Unix()]
			if !exist {
				continue
			}
			do.totalIncome = vv.totalIncome
			do.totalCompletedTrip = vv.totalCompletedTrip
			do.totalDriverWage = vv.totalDriverWage
			do.totalWithholdingTax = vv.totalWithholdingTax
			do.totalIncentive = vv.totalIncentive
			do.totalTipAndOther = vv.totalTipAndOther
			do.totalCompletedOrder = vv.totalCompletedOrder
			do.totalGuaranteeIncentive = vv.totalGuaranteeIncentive
		}

	}

	return income
}

func (svc *calculator) recordChanges(i *IncomeSummary, changes changes) {
	i.Summary.changes = changes.totalIncomeChanges
	i.Summary.percentChanges = changes.totalIncomePercentChanges
}

func (svc *calculator) compare(original IncomeSummary, compareTo IncomeSummary) changes {
	totalIncomeChanges := original.Summary.totalIncome.Sub(compareTo.Summary.totalIncome)
	totalIncomePercentChanges := totalIncomeChanges.Div(original.Summary.totalIncome).Float64()
	return changes{
		totalIncomeChanges:        totalIncomeChanges,
		totalIncomePercentChanges: totalIncomePercentChanges,
	}
}

func (svc *calculator) calculateCompletedOrderClassified(income *IncomeSummary, c completedOrderClassified) {
	tripSet := make(map[string]time.Time)
	orderSet := make(map[string]time.Time)

	// for backward compatibility
	orderCommission := make(map[string]bool)

	for _, t := range c.ca.transactions {
		e := newEarning(t.Info.TransactionDate)
		amount := t.Info.Amount
		switch t.Info.Type {
		case model.DriverWageType:
			e.driverWage = e.driverWage.Add(amount)
		case model.OnTopTransactionType:
			if t.Info.SubType == model.InstallmentOnTopSubType {
				e.egsOnTop = e.egsOnTop.Add(amount)
				break
			}
			e.incentive = e.incentive.Add(amount)
			e.onTop = e.onTop.Add(amount)
		case model.WithholdingTransactionType:
			e.withholdingTax = e.withholdingTax.Add(amount)
		case model.DeliveryFeeTransactionType:
			e.deliveryFee = e.deliveryFee.Add(amount)
		case model.AdditionalServiceFeeTransactionType:
			e.additionalService = e.additionalService.Add(amount)
		case model.CouponTransactionType, model.SubsidizeTransactionType:
			svc.handleCoupon(t, c, &e, amount)
		case model.CommissionTransactionType:
			switch t.Info.SubType {
			case model.CommissionDeliveryFeeUserTransactionSubType:
				e.commission = e.commission.Add(amount)
				orderCommission[t.Info.OrderID] = true
			case model.CommissionDeliveryFeeDynamicOnTopTransactionSubType:
				e.onTopCommission = e.onTopCommission.Add(amount)
				orderCommission[t.Info.OrderID] = true
			case model.CommissionAdditionalServiceFee:
				e.additionalServiceCommission = e.additionalServiceCommission.Add(amount)
				orderCommission[t.Info.OrderID] = true
			}
		default:
			continue
		}

		if tripId := t.Info.TripID; tripId != "" {
			if _, found := tripSet[tripId]; !found {
				e.trip = 1
				tripSet[tripId] = t.Info.TransactionDate
			}
		}

		orderIds := getCompletedOrderIDsFromTransaction(t)
		for _, orderId := range orderIds {
			_, has := orderSet[orderId]
			if !has {
				orderSet[orderId] = t.Info.TransactionDate
				e.order += 1
			}
		}
		svc.computed(income, e)
	}

	for _, o := range c.orderMap {
		if txTime, found := tripSet[o.TripID]; found && !o.RevenuePrincipalModel {
			e := newEarning(txTime)
			isBikeQR := (o.ServiceType == model.ServiceBike && o.PriceSummary().DeliveryFee.PaymentMethod == model.PaymentMethodQRPromptPay)
			// Ignore commission for 2 conditions
			// 1. An agent commission transaction containing subtype since they were already included in an income. (for backward compatibility, remove later).
			// 2. A Bike QR order since it supports only forward compatibility. If the order hasn't paid, it won't fall into 1.) condition.
			ignoreCommission := orderCommission[o.OrderID] || isBikeQR
			svc.handleAgentModel(&o, &e, ignoreCommission)
			svc.computed(income, e)
		}
	}
}

func (svc *calculator) handleCoupon(t model.Transaction, c completedOrderClassified, e *earning, amount types.Money) {
	orderIds := getCompletedOrderIDsFromTransaction(t)
	for _, v := range orderIds {
		o, exist := c.orderMap[v]
		if !exist {
			return
		}
		if !o.RevenuePrincipalModel {
			// for agent model, coupon will be an income of the rider. (either cash or e-payment)
			e.coupon = e.coupon.Add(amount)
		}
	}
}

func (svc *calculator) handleAgentModel(o *model.Order, e *earning, ignoreCommission bool) {
	ps := o.PriceSummary()
	df := ps.DeliveryFee

	// TODO: remove this after LMF-13043 has released for 1 month
	// For backward compatible
	if !ignoreCommission {
		e.commission = e.commission.Add(types.Money(df.Commission))
		e.onTopCommission = e.onTopCommission.Add(types.NewMoney(df.OnTopCommissionFare))
		e.additionalServiceCommission = e.additionalServiceCommission.Add(types.Money(df.AdditionalServiceFee.Commission))
	}

	if o.IsDeliveryFeeCash() {
		// for agent model and received delivery in cash
		// will take cash into account
		e.cash = e.cash.Add(types.Money(df.Total))
	}

	// TODO actually we should handle agent discount(coupon) here too
}

func (svc *calculator) calculateNonCompletedOrderClassified(income *IncomeSummary, nc nonCompletedOrderClassified) {
	for _, t := range nc.na.transactions {
		if _, exist := nc.notFoundOriginalTran[t.TransactionID]; exist {
			continue
		}

		e := newEarning(t.Info.TransactionDate)

		amount := t.Info.Amount
		switch {
		case t.Info.Type == model.TipTransactionType:
			e.tip = e.tip.Add(amount)
		case t.Info.Type == model.IncentiveTransactionType:
			switch {
			case t.Info.SubType == model.GuaranteeTransactionSubType:
				e.guaranteeIncentive = e.guaranteeIncentive.Add(amount)
			default:
				e.incentive = e.incentive.Add(amount)
			}
		case t.Info.Type == model.WithholdingTransactionType:
			e.withholdingTax = e.withholdingTax.Add(amount)
		default:
			continue
		}

		svc.computed(income, e)
	}
}

func (svc *calculator) computed(income *IncomeSummary, e earning) {
	income.Summary.totalDriverWage = income.Summary.totalDriverWage.Add(e.driverWage)
	income.Summary.totalAdditionalService = income.Summary.totalAdditionalService.Add(e.additionalService)
	income.Summary.totalCash = income.Summary.totalCash.Add(e.cash)
	income.Summary.totalDeliveryFee = income.Summary.totalDeliveryFee.Add(e.deliveryFee)
	income.Summary.totalCoupon = income.Summary.totalCoupon.Add(e.coupon)
	income.Summary.totalIncentive = income.Summary.totalIncentive.Add(e.incentive)
	income.Summary.totalTip = income.Summary.totalTip.Add(e.tip)
	income.Summary.totalWithholdingTax = income.Summary.totalWithholdingTax.Add(e.withholdingTax)
	income.Summary.totalCommision = income.Summary.totalCommision.Add(e.commission)
	income.Summary.totalAdditionalServiceCommision = income.Summary.totalAdditionalServiceCommision.Add(e.additionalServiceCommission)
	income.Summary.totalOntop = income.Summary.totalOntop.Add(e.onTop)
	income.Summary.totalOntopCommision = income.Summary.totalOntopCommision.Add(e.onTopCommission)
	income.Summary.totalEGSOnTopBonus = income.Summary.totalEGSOnTopBonus.Add(e.egsOnTop)
	income.Summary.totalIncome = income.Summary.totalIncome.Add(e.Total())
	income.Summary.totalCompletedTrip += e.trip
	income.Summary.totalCompletedOrder += e.order
	income.Summary.totalGuaranteeIncentive = income.Summary.totalGuaranteeIncentive.Add(e.guaranteeIncentive)
	svc.computedOverview(&income.Overview, e)
}

func (svc *calculator) computedOverview(o *Overview, e earning) {
	do, exists := o.m[e.day.Unix()]
	if !exists {
		return
	}

	do.totalIncome = do.totalIncome.Add(e.Total())
	do.totalCash = do.totalCash.Add(e.cash)
	do.totalDriverWage = do.totalDriverWage.Add(e.driverWage)
	do.totalAdditionalService = do.totalAdditionalService.Add(e.additionalService)
	do.totalIncentive = do.totalIncentive.Add(e.incentive)
	do.totalDeliveryFee = do.totalDeliveryFee.Add(e.deliveryFee)
	do.totalCoupon = do.totalCoupon.Add(e.coupon)
	do.totalWithholdingTax = do.totalWithholdingTax.Add(e.withholdingTax)
	do.totalCommission = do.totalCommission.Add(e.commission)
	do.totalTipAndOther = do.totalTipAndOther.Add(e.tip)
	do.totalOntopCommission = do.totalOntopCommission.Add(e.onTopCommission)
	do.totalAdditionalServiceCommission = do.totalAdditionalServiceCommission.Add(e.additionalServiceCommission)
	do.totalCompletedTrip += e.trip
	do.totalCompletedOrder += e.order
	do.totalGuaranteeIncentive = do.totalGuaranteeIncentive.Add(e.guaranteeIncentive)
}
