package income

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type fetcher struct {
	transactionRepo        repository.TransactionRepository
	rewardTxnRepo          repository.RewardTransactionRepository
	incentiveRepo          incentive.IncentiveRepository
	orderRepo              repository.OrderRepository
	incomeDailySummaryRepo repository.IncomeDailySummaryRepository
}

func newFetcher(
	transactionRepo repository.TransactionRepository,
	rewardTxnRepo repository.RewardTransactionRepository,
	incentiveRepo incentive.IncentiveRepository,
	orderRepo repository.OrderRepository,
	incomeDailySummaryRepo repository.IncomeDailySummaryRepository,
) *fetcher {
	return &fetcher{
		transactionRepo:        transactionRepo,
		orderRepo:              orderRepo,
		incentiveRepo:          incentiveRepo,
		rewardTxnRepo:          rewardTxnRepo,
		incomeDailySummaryRepo: incomeDailySummaryRepo,
	}
}

type completedOrderActivity struct {
	transactions []model.Transaction
	orders       []model.Order
	orderIds     []string
}

func (svc *fetcher) fetchCompletedOrderRelated(ctx context.Context, req IncomeSummaryRequest) (completedOrderActivity, error) {
	query := persistence.TransactionQuery{
		DriverID:                req.DriverId,
		InfoTransactionDateFrom: req.from,
		InfoTransactionDateTo:   req.to,
		Statuses: []model.TransactionStatus{
			model.SuccessTransactionStatus,
		},
		Actions: []model.TransactionAction{
			model.CommissionDeductionTransactionAction,
		},
		Types: []model.TransactionType{
			model.DriverWageType,
			model.OnTopTransactionType,
			model.DeliveryFeeTransactionType,
			model.CommissionTransactionType,
			model.CouponTransactionType,
			model.SubsidizeTransactionType,
			model.WithholdingTransactionType,
			model.AdditionalServiceFeeTransactionType,
		},
		BlacklistTransactionType:    req.blacklistTransactionTypes,
		ExcludePendingTransactionId: req.excludePendingTransactionId,
	}
	if len(req.transactionIds) > 0 {
		query.TransactionIds = req.transactionIds
	}

	transactions, err := svc.transactionRepo.FindWithSort(ctx, query, 0, 0, []string{"-info.transaction_date"}, repository.WithReadSecondaryPreferred)
	if err != nil {
		return completedOrderActivity{}, errors.Wrap(err, "unable to get transaction")
	}

	orderIdsTx := getOrderIdsFromTransactions(transactions)
	os, err := svc.orderRepo.GetMany(ctx, orderIdsTx, repository.WithReadSecondaryPreferred)
	if err != nil {
		return completedOrderActivity{}, errors.Wrap(err, "unable to get orders")
	}

	// Collect order IDs here
	// orders values is overwritten by repository results 'os',
	// so we will map order IDs after the overwrite
	orders := orders(os)
	orderIds := make([]string, len(orders))
	for i := range orders {
		orderIds[i] = orders[i].OrderID
	}

	// Log something
	if lenOrdersTx, lenOrders := len(orderIdsTx), len(orderIds); lenOrdersTx != lenOrders {
		diffCount := lenOrdersTx - lenOrders
		setOrderIdsTx := types.NewSetFrom(orderIdsTx...)
		setOrderIds := types.NewSetFrom(orderIds...)

		diffIds := setOrderIdsTx.DiffAll(setOrderIds).Slice()

		fields := logrus.Fields{
			"driver_id":   req.DriverId,
			"from":        req.from.Format(time.RFC3339),
			"to":          req.to.Format(time.RFC3339),
			"check_sane":  len(diffIds) == lenOrdersTx-lenOrders,
			"diff_count":  diffCount,
			"diff":        diffIds,
			"expected_id": orderIdsTx,
		}

		logrus.WithFields(fields).Warn("unexpected length of order IDs from collection transactions vs orders")
	}

	return completedOrderActivity{
		transactions: transactions,
		orders:       orders,
		orderIds:     orderIds,
	}, nil
}

type nonCompletedOrderActivity struct {
	transactions []model.Transaction
}

func (svc *fetcher) fetchNonCompletedOrderRelated(ctx context.Context, req IncomeSummaryRequest) (nonCompletedOrderActivity, error) {
	q := persistence.TransactionQuery{
		DriverID:                req.DriverId,
		InfoTransactionDateFrom: req.from,
		InfoTransactionDateTo:   req.to,
		Statuses: []model.TransactionStatus{
			model.SuccessTransactionStatus,
		},
		Actions: []model.TransactionAction{
			model.IncentiveTransactionAction,
			model.WalletTopUpTransactionAction,
			model.ConvertCoinToCashAction,
		},
		Types: []model.TransactionType{
			model.TipTransactionType,
			model.IncentiveTransactionType,
			model.WithholdingTransactionType,
		},
	}

	if len(req.transactionIds) > 0 {
		q.TransactionIds = req.transactionIds
	}

	transactions, err := svc.transactionRepo.FindWithSort(ctx, q, 0, 0, []string{"-info.transaction_date"}, repository.WithReadSecondaryPreferred)
	if err != nil {
		return nonCompletedOrderActivity{}, errors.Wrap(err, "unable to get transaction")
	}

	return nonCompletedOrderActivity{
		transactions: transactions,
	}, nil
}

func (svc *fetcher) fetchAggregatedIncomeDailySummary(ctx context.Context, driverId string, fromInclusive, toExclusive time.Time) ([]model.IncomeDailySummary, error) {
	q := persistence.BuildIncomeDailySummaryQuery()
	q.WithDriverID(driverId)
	q.WithFromInclusive(fromInclusive)
	q.WithToExclusive(toExclusive)

	outs, err := svc.incomeDailySummaryRepo.Query(ctx, q, 0, 0, []string{"date"}, repository.WithReadSecondaryPreferred)
	if err != nil {
		return []model.IncomeDailySummary{}, errors.Wrap(err, "unable to get income daily summary")
	}

	return outs, nil
}

func (svc *fetcher) countCompletedOrders(ctx context.Context, date time.Time, driverId string) (int, error) {
	q := bson.M{
		"driver": driverId,
		"status": model.StatusCompleted,
		"created_at": bson.M{
			"$lte": timeutil.DateCeilingBKK(date),
			"$gte": timeutil.DateTruncateBKK(date),
		},
	}

	count, err := svc.orderRepo.Count(ctx, q, repository.WithReadSecondaryPreferred)
	if err != nil {
		return 0, err
	}

	return count, nil
}

func (svc *fetcher) fetchConvertCoinCashTransaction(ctx context.Context, date time.Time, driverId string) (*model.Transaction, error) {
	txn, err := svc.transactionRepo.Find(ctx, persistence.TransactionQuery{
		DriverID: driverId,
		From:     timeutil.DateTruncate(date),
		To:       timeutil.DateCeiling(date),
		Actions:  []model.TransactionAction{model.ConvertCoinToCashAction},
		Category: model.WalletTransactionCategory,
	}, 0, 0, repository.WithReadSecondaryPreferred)
	if err != nil {
		return nil, err
	}
	if len(txn) == 0 {
		return nil, nil
	}

	return &txn[0], nil
}

func (svc *fetcher) fetchIncentives(ctx context.Context, incentiveIds []string) ([]incentive.Incentive, error) {
	incentives, err := svc.incentiveRepo.FindByIDsWithSelector(ctx, incentiveIds, repository.WithReadSecondaryPreferred)
	if err != nil {
		return nil, err
	}
	return incentives, nil
}

func (svc *fetcher) fetchIncentiveTransaction(ctx context.Context, date time.Time, driverId string) ([]model.Transaction, error) {
	txns, err := svc.transactionRepo.Find(ctx, persistence.TransactionQuery{
		DriverID: driverId,
		From:     timeutil.DateTruncate(date),
		To:       timeutil.DateCeiling(date),
		Actions:  []model.TransactionAction{model.IncentiveTransactionAction},
		Category: model.WalletTransactionCategory,
	}, 0, 0, repository.WithReadSecondaryPreferred)
	if err != nil {
		return nil, err
	}

	return txns, nil
}

func (svc *fetcher) fetchRewardTransaction(ctx context.Context, start, end time.Time, driverId string) ([]model.RewardTransaction, error) {
	transactions, err := svc.rewardTxnRepo.FindFromBKKCreatedAtInterval(ctx, start, end, driverId, repository.WithReadSecondaryPreferred)
	if err != nil {
		return nil, err
	}
	return transactions, nil
}
