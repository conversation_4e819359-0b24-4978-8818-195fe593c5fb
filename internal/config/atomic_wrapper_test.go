package config

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

type TestConfig struct {
	X string `envconfig:"X"`
	Y string `envconfig:"Y"`
}

func TestAnyAtomicConfig(t *testing.T) {
	cfg := NewAtomicWrapper(TestConfig{})

	t.Setenv("X", "string X")
	t.Setenv("Y", "string Y")

	cfg.Parse()
	actual := cfg.Get()

	assert.Equal(t, "string X", actual.X)
	assert.Equal(t, "string Y", actual.Y)
}
