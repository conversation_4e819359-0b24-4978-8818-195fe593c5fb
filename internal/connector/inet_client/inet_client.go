package inet_client

import (
	"bytes"
	"context"
	_ "embed"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	netUrl "net/url"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

//go:generate mockgen -source=inet_client.go -destination=./mock_inet_client/inet_client.go -package=mock_inet_client

type INetClient interface {
	IssueDocument(ctx context.Context, payload INetDocumentPayload, pdfBytes []byte) (*INetDocumentIssueResponse, error)
	ReplaceHost(url string) (string, error)
	BuildINetDocumentPayloadEtaxInvoice(etax model.EtaxInvoice) (*INetDocumentPayload, error)
	BuildINetDocumentPayloadReplacementEtaxInvoice(etax model.EtaxInvoice, ref model.EtaxInvoice, params INetParams) (*INetDocumentPayload, error)
	BuildINetDocumentPayloadCreditNote(etax model.EtaxInvoice, ref model.EtaxInvoice, params INetParams) (*INetDocumentPayload, error)
	GetDocument(ctx context.Context, invoiceNumber string) (*INetGetDocumentParamAPIResponse, error)
}

type INetClientImpl struct {
	cfg  Config
	http *httpclient.Client
}

var (
	ErrUnableToIssueDocument = errors.New("unable to issue document")
	ErrUnableToGetDocument   = errors.New("unable to get document")
	ErrUnableToDecodeJson    = errors.New("unable to decode json")
	ErrStatusNotOk           = errors.New("status not ok")
	ErrNoDocumentFound       = errors.New("no document found")
)

func (c *INetClientImpl) buildDocument(payload INetDocumentPayload, pdfBytes []byte) (*bytes.Buffer, *multipart.Writer, error) {

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	// Add TextContent field
	{
		textContent := bytes.NewBufferString(ParseINetData(c.cfg, payload))
		fw, err := writer.CreateFormField("TextContent")
		if err != nil {
			return nil, nil, err
		}
		_, err = io.Copy(fw, textContent)
		if err != nil {
			return nil, nil, err
		}
	}

	// Add PDFContent field
	{

		fw, err := writer.CreateFormFile("PDFContent", "content.pdf")
		if err != nil {
			return nil, nil, err
		}

		_, err = io.Copy(fw, bytes.NewBuffer(pdfBytes))

		if err != nil {
			return nil, nil, err
		}
	}

	c.writeCommonBody(writer)

	writer.Close()

	return body, writer, nil
}

func (c *INetClientImpl) writeCommonBody(writer *multipart.Writer) {
	writer.WriteField("SellerTaxId", c.cfg.SellerTaxID)
	writer.WriteField("SellerBranchId", c.cfg.SellerBranchID)
	writer.WriteField("UserCode", c.cfg.UserCode)
	writer.WriteField("AccessKey", c.cfg.AccessKey)
	writer.WriteField("APIKey", c.cfg.ApiKey)
}

func (c *INetClientImpl) IssueDocument(ctx context.Context, payload INetDocumentPayload, pdfBytes []byte) (*INetDocumentIssueResponse, error) {

	var url string

	if c.cfg.UseCustomTemplate {
		url = fmt.Sprintf("%s%s?ServiceCode=S06", c.cfg.ServiceHost, c.cfg.ServicePath)
	} else {
		url = fmt.Sprintf("%s%s?ServiceCode=S03", c.cfg.ServiceHost, c.cfg.ServicePath)
	}

	body, writer, err := c.buildDocument(payload, pdfBytes)

	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(body.Bytes()))

	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())
	rsp, err := c.http.Do(ctx, req)

	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()

	if rsp.StatusCode != http.StatusOK {
		return nil, ErrUnableToIssueDocument
	}

	var rspBody INetDocumentIssueAPIResponse

	if err := safe.DecodeJSON(rsp.Body, &rspBody); err != nil {
		return nil, ErrUnableToDecodeJson
	}

	if rspBody.Status != "OK" {
		return nil, fmt.Errorf("%s", rspBody.ErrorMessage)
	}

	return &INetDocumentIssueResponse{
		TransactionCode: rspBody.TransactionCode,
		PdfUrl:          rspBody.PdfUrl,
		XmlUrl:          rspBody.XmlUrl,
	}, nil
}

func (c *INetClientImpl) GetDocument(ctx context.Context, invoiceNumber string) (*INetGetDocumentParamAPIResponse, error) {
	// Prepare the body
	body, writer, err := c.getRequestBodyForGetDocumentRequest(invoiceNumber)
	if err != nil {
		return nil, err
	}

	url := fmt.Sprintf("%s%s", c.cfg.ServiceHost, c.cfg.GetDocumentParamPath)
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(body.Bytes()))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Send the request
	rsp, err := c.http.Do(ctx, req)
	if err != nil {
		return nil, err
	}
	defer rsp.Body.Close()

	if rsp.StatusCode != http.StatusOK {
		return nil, ErrUnableToGetDocument
	}

	var result INetGetDocumentParamAPIResponse
	if err := safe.DecodeJSON(rsp.Body, &result); err != nil {
		return nil, ErrUnableToDecodeJson
	}

	if result.Status != StatusOK {
		if result.Status == StatusError && result.ErrorCode == DataNotFoundINETErrorCode {
			return nil, ErrNoDocumentFound
		}
		return nil, ErrStatusNotOk
	}

	return &result, nil
}

func (c *INetClientImpl) getRequestBodyForGetDocumentRequest(invoiceNumber string) (*bytes.Buffer, *multipart.Writer, error) {
	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)
	writer.WriteField("InvoiceNumber", invoiceNumber)
	c.writeCommonBody(writer)
	writer.Close()
	return body, writer, nil
}

func (c *INetClientImpl) ReplaceHost(u string) (string, error) {
	url, err := netUrl.Parse(u)
	if err != nil {
		return "", err
	}
	replacedUrl := c.cfg.ServiceHost + url.Path
	if url.RawQuery != "" {
		replacedUrl += fmt.Sprintf("?%s", url.RawQuery)
	}
	return replacedUrl, nil
}

func ProvideINetClient(cfg Config) INetClient {
	return &INetClientImpl{
		cfg:  cfg,
		http: httpclient.NewWithTimeout(cfg.HTTPTimeout),
	}
}
