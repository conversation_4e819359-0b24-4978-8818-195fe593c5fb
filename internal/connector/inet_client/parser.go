package inet_client

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const delimiter string = ","
const eol string = "\n"

func ParseINetRowData(p interface{}) string {
	var dataType INetDataType

	switch p.(type) {
	case INetDocumentFileControl:
		dataType = FileControl
	case INetDocumentHeader:
		dataType = Header
	case INetDocumentBuyerInfo:
		dataType = BuyerInfo
	case INetDocumentTradeLineItemInfo:
		dataType = TradeLineItemInfo
	case INetDocumentFooter:
		dataType = Footer
	case INetDocumentFileTrailer:
		dataType = FileTrailer
	}

	sos := fmt.Sprintf("\"%s\"", dataType)
	tokens := []string{sos}

	v := reflect.ValueOf(p)
	t := v.Type()

	for i := 0; i < v.NumField(); i++ {
		fieldType := t.Field(i).Type
		fieldValue := v.Field(i)

		if fieldValue.Kind() == reflect.Ptr {
			if fieldValue.IsNil() {
				tokens = append(tokens, `""`)
				continue
			}
			fieldValue = fieldValue.Elem()
			fieldType = fieldValue.Type()
		}

		var token string

		if fieldType.AssignableTo(reflect.TypeOf(time.Time{})) {
			token = fieldValue.Interface().(time.Time).In(timeutil.BangkokLocation()).Format(time.RFC3339)
		} else if fieldType.AssignableTo(reflect.TypeOf(reflect.Float32)) {
			token = fmt.Sprintf("%.2f", fieldValue.Float())
		} else {
			token = strconv.Quote(fmt.Sprint(fieldValue))
		}

		tokens = append(tokens, token)
	}

	return strings.Join(tokens, delimiter)
}

func ParseINetData(cfg Config, payload INetDocumentPayload) string {
	payload.FileControl.SellerBranchID = cfg.SellerBranchID
	payload.FileControl.SellerTaxID = cfg.SellerTaxID
	fileControl := ParseINetRowData(payload.FileControl)
	header := ParseINetRowData(payload.Header)
	buyerInfo := ParseINetRowData(payload.BuyerInformation)
	tradeLineItemInfos := []string{}
	for _, info := range payload.TradeLineItemInfos {
		tradeLineItemInfos = append(tradeLineItemInfos, ParseINetRowData(info))
	}
	footer := ParseINetRowData(payload.Footer)
	fileTrailer := ParseINetRowData(payload.FileTrailer)

	txt := strings.Join([]string{
		fileControl,
		header,
		buyerInfo,
		strings.Join(tradeLineItemInfos, eol),
		footer,
		fileTrailer,
	}, eol)

	return txt
}
