package uobclient

import (
	"sync"
	"time"
)

type MakePaymentRes struct {
	HttpStatusCode    int
	TransactionStatus MakePaymentTransactionStatus `json:"transactionStatus"`
}

type MakePaymentTransactionStatus struct {
	TransactionReference string `json:"transactionReference"`
	BankReference        string `json:"bankReference"`
	Code                 string `json:"code"`
	Description          string `json:"description"`
}

type MakePaymentResultPair struct {
	MakePaymentReq MakePaymentReq
	MakePaymentRes MakePaymentRes
	MakePaymentAt  time.Time
}

type MakePaymentResult struct {
	isSuspended bool
	m           *sync.Mutex
	Success     []MakePaymentResultPair `bson:"successes" json:"successes"`
	Fail        []MakePaymentResultPair `bson:"fails" json:"fails"`
	Processing  []MakePaymentResultPair
}

func NewMakePaymentResult() *MakePaymentResult {
	return &MakePaymentResult{
		Success: []MakePaymentResultPair{},
		Fail:    []MakePaymentResultPair{},
		m:       &sync.Mutex{},
	}
}

func (mpr *MakePaymentResult) AddSuccess(success ...MakePaymentResultPair) {
	mpr.m.Lock()
	defer mpr.m.Unlock()
	mpr.Success = append(mpr.Success, success...)
}

func (mpr *MakePaymentResult) AddFail(fail ...MakePaymentResultPair) {
	mpr.m.Lock()
	defer mpr.m.Unlock()
	mpr.Fail = append(mpr.Fail, fail...)
}

func (mpr *MakePaymentResult) AddProcessing(p ...MakePaymentResultPair) {
	mpr.m.Lock()
	defer mpr.m.Unlock()
	mpr.Processing = append(mpr.Processing, p...)
}

func (mpr *MakePaymentResult) GetAll() []MakePaymentResultPair {
	var d []MakePaymentResultPair
	for _, v := range mpr.Fail {
		d = append(d, v)
	}

	for _, v := range mpr.Success {
		d = append(d, v)
	}

	return d
}

func (mpr *MakePaymentResult) Size() int {
	return len(mpr.Success) + len(mpr.Fail) + len(mpr.Processing)
}

/* ---Enquire-- */

type MakeEnquireTransactionStatus struct {
	TransactionReference string `json:"transactionReference"`
	BankReference        string `json:"bankReference"`
	Code                 string `json:"code"`
	Description          string `json:"description"`
}

type MakeEnquireRes struct {
	HttpStatusCode    int
	TransactionStatus MakeEnquireTransactionStatus `json:"transactionStatus"`
}

type MakeEnquireResultPair struct {
	MakeEnquireReq MakeEnquireReq
	MakeEnquireRes MakeEnquireRes
}

type MakeEnquireResult struct {
	isSuspended bool
	m           *sync.Mutex
	Success     []MakeEnquireResultPair `bson:"successes" json:"successes"`
	Fail        []MakeEnquireResultPair `bson:"fails" json:"fails"`
	Processing  []MakeEnquireResultPair
}

func NewMakeEnquireResult() *MakeEnquireResult {
	return &MakeEnquireResult{
		Success: []MakeEnquireResultPair{},
		Fail:    []MakeEnquireResultPair{},
		m:       &sync.Mutex{},
	}
}

func (mpr *MakeEnquireResult) AddSuccess(success ...MakeEnquireResultPair) {
	mpr.m.Lock()
	defer mpr.m.Unlock()
	mpr.Success = append(mpr.Success, success...)
}

func (mpr *MakeEnquireResult) AddFail(fail ...MakeEnquireResultPair) {
	mpr.m.Lock()
	defer mpr.m.Unlock()
	mpr.Fail = append(mpr.Fail, fail...)
}

func (mpr *MakeEnquireResult) AddProcessing(p ...MakeEnquireResultPair) {
	mpr.m.Lock()
	defer mpr.m.Unlock()
	mpr.Processing = append(mpr.Processing, p...)
}

func (mpr *MakeEnquireResult) GetAll() []MakeEnquireResultPair {
	var d []MakeEnquireResultPair
	for _, v := range mpr.Fail {
		d = append(d, v)
	}

	for _, v := range mpr.Success {
		d = append(d, v)
	}

	return d
}

func (mpr *MakeEnquireResult) Size() int {
	return len(mpr.Success) + len(mpr.Fail) + len(mpr.Processing)
}
