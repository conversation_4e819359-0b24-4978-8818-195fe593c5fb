package uobclient

import (
	"strings"
	"time"

	"github.com/kelseyhightower/envconfig"
)

type Config struct {
	UOBPayoutApplicationID      string        `envconfig:"UOB_PAYOUT_APPLICATION_ID"  default:""`
	UOBPayoutApiKey             string        `envconfig:"UOB_PAYOUT_API_KEY"  default:""`
	UOBPayoutClientID           string        `envconfig:"UOB_PAYOUT_CLIENT_ID"  default:""`
	UOBPayoutCountry            string        `envconfig:"UOB_PAYOUT_COUNTRY"  default:"TH"`
	UOBBaseUrl                  string        `envconfig:"UOB_BASE_URL"  default:"https://man-internal.line-apps-rc.com/lm-proxy-ext/uob/"`
	LMWNPrivate                 string        `envconfig:"LMWN_PRIVATE_KEY"  default:""`
	UOBClientTimeout            time.Duration `envconfig:"UOB_CLIENT_TIMEOUT"  default:"60s"`
	ErrorCodeNotDefinedInSystem []string      `envconfig:"UOB_PROCESSING_ERROR_CODE" default:"ER9998,ER9999"`
	UOBClientSuspendedTime      time.Duration `envconfig:"UOB_CLIENT_SUSPENDED_TIME"  default:"15s"`
	UOBWorkerPoolSize           int           `envconfig:"UOB_WORKER_POOL_SIZE" default:"3"`
	UOBFailLimit                int64         `envconfig:"UOB_FAIL_LIMIT" default:"5"`
}

func ProvideUobConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	cfg.LMWNPrivate = strings.Replace(cfg.LMWNPrivate, `\n`, "\n", 5)

	return
}
