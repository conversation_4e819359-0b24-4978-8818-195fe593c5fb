package fleetarea

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/status"

	"git.wndv.co/go/grpclib"
	fleetareav1 "git.wndv.co/go/proto/lineman/fleet/area/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	tracegrpc "git.wndv.co/lineman/xgo/trace/mwgrpc"
)

//go:generate mockgen -source=./client.go -destination=./mock_fleetarea/mock_client.go -package=mock_fleetarea

type FleetAreaClient interface {
	FindZones(ctx context.Context, in *fleetareav1.FindZonesRequest, opts ...grpc.CallOption) (*fleetareav1.FindZonesResponse, error)
	FindBriefZones(ctx context.Context, in *fleetareav1.FindBriefZonesRequest, opts ...grpc.CallOption) (*fleetareav1.FindBriefZonesResponse, error)
	FindZoneByLocation(ctx context.Context, in *fleetareav1.FindZoneByLocationRequest, opts ...grpc.CallOption) (*fleetareav1.FindZoneByLocationResponse, error)
	FindZonesIntersectPolygon(ctx context.Context, in *fleetareav1.FindZonesIntersectPolygonRequest, opts ...grpc.CallOption) (*fleetareav1.FindZonesIntersectPolygonResponse, error)
	GetZoneByID(ctx context.Context, id string, opts ...grpc.CallOption) (*fleetareav1.GetZoneResponse, error)
	GetZoneByCode(ctx context.Context, code string, opts ...grpc.CallOption) (*fleetareav1.GetZoneResponse, error)
	CreateZone(ctx context.Context, in model.Zone, opts ...grpc.CallOption) (*fleetareav1.CreateZoneResponse, error)
	UpdateZone(ctx context.Context, id string, z *fleetareav1.Zone, opts ...grpc.CallOption) (*fleetareav1.UpdateZoneResponse, error)

	GetServiceArea(ctx context.Context, req *fleetareav1.GetServiceAreaRequest, opts ...grpc.CallOption) (*fleetareav1.GetServiceAreaResponse, error)
	GetServiceAreaByRegion(ctx context.Context, req *fleetareav1.GetServiceAreaByRegionRequest, opts ...grpc.CallOption) (*fleetareav1.GetServiceAreaByRegionResponse, error)
	FindByRegions(ctx context.Context, req *fleetareav1.FindByRegionsRequest, opts ...grpc.CallOption) (*fleetareav1.FindByRegionsResponse, error)
	GetAutoAssignRegions(ctx context.Context, req *fleetareav1.GetAutoAssignRegionsRequest, opts ...grpc.CallOption) (*fleetareav1.GetAutoAssignRegionsResponse, error)
	DeleteServiceArea(ctx context.Context, req *fleetareav1.DeleteServiceAreaRequest, opts ...grpc.CallOption) (*fleetareav1.DeleteServiceAreaResponse, error)
}

type FleetAreaClientImpl struct {
	client            fleetareav1.ZoneServiceClient
	serviceAreaClient fleetareav1.ServiceAreaServiceClient
	cfg               *FleetAreaClientConfig
}

func ProvideFleetAreaClient(cfg *FleetAreaClientConfig) (FleetAreaClient, func()) {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()

	cf, err := grpclib.NewConnectionFactory()
	if err != nil {
		logrus.Errorf("error from NewConnectionFactory for RiderLevelClient: %s", err.Error())
		return nil, func() {}
	}

	conn, err := cf.CreateV2(ctx,
		cfg.URL,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		// TODO should have some builder, help to create default set of interceptor e.g. configurable log
		// https://linemanwongnai.atlassian.net/browse/LMF-13819
		grpc.WithChainUnaryInterceptor(
			grpclib.GrpcLogInterceptorCustom("fleet-area", grpclib.DefaultOutgoingLogDecorator().LogBody(true)),
			tracegrpc.RequestIDUnaryClientInterceptor,
		),
	)
	if err != nil {
		logrus.Errorf("unable to connect to gRPC endpoint: %s", err.Error())
		return nil, func() {}
	}

	cleanup := func() {
		if closeErr := conn.Close(); closeErr != nil {
			logrus.Errorf("found error during close connection: %s", err.Error())
		}
	}

	grpcClient := fleetareav1.NewZoneServiceClient(conn)
	if grpcClient == nil {
		panic("nil grpc client")
	}

	serviceAreaClient := fleetareav1.NewServiceAreaServiceClient(conn)
	if serviceAreaClient == nil {
		panic("nil service area grpc client")
	}

	return &FleetAreaClientImpl{
		client:            grpcClient,
		serviceAreaClient: serviceAreaClient,
		cfg:               cfg,
	}, cleanup
}

func IsNotFoundErr(err error) bool {
	s, ok := status.FromError(err)
	if ok && s.Code() == codes.NotFound {
		return true
	}
	return false
}

func (impl *FleetAreaClientImpl) FindZones(ctx context.Context, in *fleetareav1.FindZonesRequest, opts ...grpc.CallOption) (*fleetareav1.FindZonesResponse, error) {
	return impl.client.FindZones(ctx, in, opts...)
}

func (impl *FleetAreaClientImpl) FindBriefZones(ctx context.Context, in *fleetareav1.FindBriefZonesRequest, opts ...grpc.CallOption) (*fleetareav1.FindBriefZonesResponse, error) {
	return impl.client.FindBriefZones(ctx, in, opts...)
}

func (impl *FleetAreaClientImpl) FindZoneByLocation(ctx context.Context, in *fleetareav1.FindZoneByLocationRequest, opts ...grpc.CallOption) (*fleetareav1.FindZoneByLocationResponse, error) {
	return impl.client.FindZoneByLocation(ctx, in, opts...)
}

func (impl *FleetAreaClientImpl) FindZonesIntersectPolygon(ctx context.Context, in *fleetareav1.FindZonesIntersectPolygonRequest, opts ...grpc.CallOption) (*fleetareav1.FindZonesIntersectPolygonResponse, error) {
	return impl.client.FindZonesIntersectPolygon(ctx, in, opts...)
}

func (impl *FleetAreaClientImpl) GetZoneByID(ctx context.Context, id string, opts ...grpc.CallOption) (*fleetareav1.GetZoneResponse, error) {
	return impl.client.GetZone(ctx, &fleetareav1.GetZoneRequest{
		Id: id,
	}, opts...)
}

func (impl *FleetAreaClientImpl) GetZoneByCode(ctx context.Context, code string, opts ...grpc.CallOption) (*fleetareav1.GetZoneResponse, error) {
	return impl.client.GetZone(ctx, &fleetareav1.GetZoneRequest{
		ZoneCode: code,
	}, opts...)
}

func (impl *FleetAreaClientImpl) CreateZone(ctx context.Context, in model.Zone, opts ...grpc.CallOption) (*fleetareav1.CreateZoneResponse, error) {
	return impl.client.CreateZone(ctx, &fleetareav1.CreateZoneRequest{
		Name:        in.Name,
		DisplayName: in.DisplayName,
		Region:      in.Region,
	}, opts...)
}

func (impl *FleetAreaClientImpl) UpdateZone(ctx context.Context, id string, z *fleetareav1.Zone, opts ...grpc.CallOption) (*fleetareav1.UpdateZoneResponse, error) {
	return impl.client.UpdateZone(ctx, &fleetareav1.UpdateZoneRequest{
		Id:   id,
		Zone: z,
	}, opts...)
}

func (impl *FleetAreaClientImpl) GetServiceArea(ctx context.Context, req *fleetareav1.GetServiceAreaRequest, opts ...grpc.CallOption) (*fleetareav1.GetServiceAreaResponse, error) {
	return impl.serviceAreaClient.GetServiceArea(ctx, req, opts...)
}

func (impl *FleetAreaClientImpl) GetServiceAreaByRegion(ctx context.Context, req *fleetareav1.GetServiceAreaByRegionRequest, opts ...grpc.CallOption) (*fleetareav1.GetServiceAreaByRegionResponse, error) {
	return impl.serviceAreaClient.GetServiceAreaByRegion(ctx, req, opts...)
}

func (impl *FleetAreaClientImpl) FindByRegions(ctx context.Context, req *fleetareav1.FindByRegionsRequest, opts ...grpc.CallOption) (*fleetareav1.FindByRegionsResponse, error) {
	return impl.serviceAreaClient.FindByRegions(ctx, req, opts...)
}

func (impl *FleetAreaClientImpl) GetAutoAssignRegions(ctx context.Context, req *fleetareav1.GetAutoAssignRegionsRequest, opts ...grpc.CallOption) (*fleetareav1.GetAutoAssignRegionsResponse, error) {
	return impl.serviceAreaClient.GetAutoAssignRegions(ctx, req, opts...)
}

func (impl *FleetAreaClientImpl) DeleteServiceArea(ctx context.Context, req *fleetareav1.DeleteServiceAreaRequest, opts ...grpc.CallOption) (*fleetareav1.DeleteServiceAreaResponse, error) {
	return impl.serviceAreaClient.DeleteServiceArea(ctx, req, opts...)
}
