// Code generated by MockGen. DO NOT EDIT.
// Source: ./client.go

// Package mock_fleetarea is a generated GoMock package.
package mock_fleetarea

import (
	context "context"
	reflect "reflect"

	areav1 "git.wndv.co/go/proto/lineman/fleet/area/v1"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockFleetAreaClient is a mock of FleetAreaClient interface.
type MockFleetAreaClient struct {
	ctrl     *gomock.Controller
	recorder *MockFleetAreaClientMockRecorder
}

// MockFleetAreaClientMockRecorder is the mock recorder for MockFleetAreaClient.
type MockFleetAreaClientMockRecorder struct {
	mock *MockFleetAreaClient
}

// NewMockFleetAreaClient creates a new mock instance.
func NewMockFleetAreaClient(ctrl *gomock.Controller) *MockFleetAreaClient {
	mock := &MockFleetAreaClient{ctrl: ctrl}
	mock.recorder = &MockFleetAreaClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFleetAreaClient) EXPECT() *MockFleetAreaClientMockRecorder {
	return m.recorder
}

// CreateZone mocks base method.
func (m *MockFleetAreaClient) CreateZone(ctx context.Context, in model.Zone, opts ...grpc.CallOption) (*areav1.CreateZoneResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateZone", varargs...)
	ret0, _ := ret[0].(*areav1.CreateZoneResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateZone indicates an expected call of CreateZone.
func (mr *MockFleetAreaClientMockRecorder) CreateZone(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateZone", reflect.TypeOf((*MockFleetAreaClient)(nil).CreateZone), varargs...)
}

// DeleteServiceArea mocks base method.
func (m *MockFleetAreaClient) DeleteServiceArea(ctx context.Context, req *areav1.DeleteServiceAreaRequest, opts ...grpc.CallOption) (*areav1.DeleteServiceAreaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteServiceArea", varargs...)
	ret0, _ := ret[0].(*areav1.DeleteServiceAreaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteServiceArea indicates an expected call of DeleteServiceArea.
func (mr *MockFleetAreaClientMockRecorder) DeleteServiceArea(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceArea", reflect.TypeOf((*MockFleetAreaClient)(nil).DeleteServiceArea), varargs...)
}

// FindBriefZones mocks base method.
func (m *MockFleetAreaClient) FindBriefZones(ctx context.Context, in *areav1.FindBriefZonesRequest, opts ...grpc.CallOption) (*areav1.FindBriefZonesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindBriefZones", varargs...)
	ret0, _ := ret[0].(*areav1.FindBriefZonesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindBriefZones indicates an expected call of FindBriefZones.
func (mr *MockFleetAreaClientMockRecorder) FindBriefZones(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindBriefZones", reflect.TypeOf((*MockFleetAreaClient)(nil).FindBriefZones), varargs...)
}

// FindByRegions mocks base method.
func (m *MockFleetAreaClient) FindByRegions(ctx context.Context, req *areav1.FindByRegionsRequest, opts ...grpc.CallOption) (*areav1.FindByRegionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByRegions", varargs...)
	ret0, _ := ret[0].(*areav1.FindByRegionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByRegions indicates an expected call of FindByRegions.
func (mr *MockFleetAreaClientMockRecorder) FindByRegions(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByRegions", reflect.TypeOf((*MockFleetAreaClient)(nil).FindByRegions), varargs...)
}

// FindZoneByLocation mocks base method.
func (m *MockFleetAreaClient) FindZoneByLocation(ctx context.Context, in *areav1.FindZoneByLocationRequest, opts ...grpc.CallOption) (*areav1.FindZoneByLocationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindZoneByLocation", varargs...)
	ret0, _ := ret[0].(*areav1.FindZoneByLocationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindZoneByLocation indicates an expected call of FindZoneByLocation.
func (mr *MockFleetAreaClientMockRecorder) FindZoneByLocation(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindZoneByLocation", reflect.TypeOf((*MockFleetAreaClient)(nil).FindZoneByLocation), varargs...)
}

// FindZones mocks base method.
func (m *MockFleetAreaClient) FindZones(ctx context.Context, in *areav1.FindZonesRequest, opts ...grpc.CallOption) (*areav1.FindZonesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindZones", varargs...)
	ret0, _ := ret[0].(*areav1.FindZonesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindZones indicates an expected call of FindZones.
func (mr *MockFleetAreaClientMockRecorder) FindZones(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindZones", reflect.TypeOf((*MockFleetAreaClient)(nil).FindZones), varargs...)
}

// FindZonesIntersectPolygon mocks base method.
func (m *MockFleetAreaClient) FindZonesIntersectPolygon(ctx context.Context, in *areav1.FindZonesIntersectPolygonRequest, opts ...grpc.CallOption) (*areav1.FindZonesIntersectPolygonResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindZonesIntersectPolygon", varargs...)
	ret0, _ := ret[0].(*areav1.FindZonesIntersectPolygonResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindZonesIntersectPolygon indicates an expected call of FindZonesIntersectPolygon.
func (mr *MockFleetAreaClientMockRecorder) FindZonesIntersectPolygon(ctx, in interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindZonesIntersectPolygon", reflect.TypeOf((*MockFleetAreaClient)(nil).FindZonesIntersectPolygon), varargs...)
}

// GetAutoAssignRegions mocks base method.
func (m *MockFleetAreaClient) GetAutoAssignRegions(ctx context.Context, req *areav1.GetAutoAssignRegionsRequest, opts ...grpc.CallOption) (*areav1.GetAutoAssignRegionsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetAutoAssignRegions", varargs...)
	ret0, _ := ret[0].(*areav1.GetAutoAssignRegionsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAutoAssignRegions indicates an expected call of GetAutoAssignRegions.
func (mr *MockFleetAreaClientMockRecorder) GetAutoAssignRegions(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAutoAssignRegions", reflect.TypeOf((*MockFleetAreaClient)(nil).GetAutoAssignRegions), varargs...)
}

// GetServiceArea mocks base method.
func (m *MockFleetAreaClient) GetServiceArea(ctx context.Context, req *areav1.GetServiceAreaRequest, opts ...grpc.CallOption) (*areav1.GetServiceAreaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetServiceArea", varargs...)
	ret0, _ := ret[0].(*areav1.GetServiceAreaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceArea indicates an expected call of GetServiceArea.
func (mr *MockFleetAreaClientMockRecorder) GetServiceArea(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceArea", reflect.TypeOf((*MockFleetAreaClient)(nil).GetServiceArea), varargs...)
}

// GetServiceAreaByRegion mocks base method.
func (m *MockFleetAreaClient) GetServiceAreaByRegion(ctx context.Context, req *areav1.GetServiceAreaByRegionRequest, opts ...grpc.CallOption) (*areav1.GetServiceAreaByRegionResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetServiceAreaByRegion", varargs...)
	ret0, _ := ret[0].(*areav1.GetServiceAreaByRegionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceAreaByRegion indicates an expected call of GetServiceAreaByRegion.
func (mr *MockFleetAreaClientMockRecorder) GetServiceAreaByRegion(ctx, req interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceAreaByRegion", reflect.TypeOf((*MockFleetAreaClient)(nil).GetServiceAreaByRegion), varargs...)
}

// GetZoneByCode mocks base method.
func (m *MockFleetAreaClient) GetZoneByCode(ctx context.Context, code string, opts ...grpc.CallOption) (*areav1.GetZoneResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, code}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetZoneByCode", varargs...)
	ret0, _ := ret[0].(*areav1.GetZoneResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetZoneByCode indicates an expected call of GetZoneByCode.
func (mr *MockFleetAreaClientMockRecorder) GetZoneByCode(ctx, code interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, code}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetZoneByCode", reflect.TypeOf((*MockFleetAreaClient)(nil).GetZoneByCode), varargs...)
}

// GetZoneByID mocks base method.
func (m *MockFleetAreaClient) GetZoneByID(ctx context.Context, id string, opts ...grpc.CallOption) (*areav1.GetZoneResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetZoneByID", varargs...)
	ret0, _ := ret[0].(*areav1.GetZoneResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetZoneByID indicates an expected call of GetZoneByID.
func (mr *MockFleetAreaClientMockRecorder) GetZoneByID(ctx, id interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetZoneByID", reflect.TypeOf((*MockFleetAreaClient)(nil).GetZoneByID), varargs...)
}

// UpdateZone mocks base method.
func (m *MockFleetAreaClient) UpdateZone(ctx context.Context, id string, z *areav1.Zone, opts ...grpc.CallOption) (*areav1.UpdateZoneResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id, z}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateZone", varargs...)
	ret0, _ := ret[0].(*areav1.UpdateZoneResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateZone indicates an expected call of UpdateZone.
func (mr *MockFleetAreaClientMockRecorder) UpdateZone(ctx, id, z interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id, z}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateZone", reflect.TypeOf((*MockFleetAreaClient)(nil).UpdateZone), varargs...)
}
