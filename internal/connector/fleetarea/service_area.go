package fleetarea

import (
	"errors"
	"fmt"
	"time"

	fleetareav1 "git.wndv.co/go/proto/lineman/fleet/area/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

func MapServiceAreaList(serviceAreas []*fleetareav1.ServiceArea) ([]model.ServiceArea, error) {
	res := make([]model.ServiceArea, 0, len([]*fleetareav1.ServiceArea{}))
	for i := range serviceAreas {
		if serviceAreas[i] == nil {
			continue
		}

		// TODO ServiceAreaRepository.FindByRegions - should change method signature to return []Region instead of whole ServiceArea object
		// https://linemanwongnai.atlassian.net/browse/LMF-12254
		res = append(res, model.ServiceArea{
			Region: model.RegionCode(serviceAreas[i].Region),
		})
	}
	return res, nil
}

func MapServiceArea(s *fleetareav1.ServiceArea) (serviceArea *model.ServiceArea, err error) {
	defer func() {
		if r := recover(); r != nil {
			err = errors.New(fmt.Sprintf("MapServiceArea, recovered from panic: %v", r))
		}
	}()

	return &model.ServiceArea{
		ID:                            model.ServiceAreaID(s.Id),
		Region:                        model.RegionCode(s.Region),
		DistributionLogic:             model.DistributionLogic(s.DistributionLogic),
		Distribution:                  deRef(mapAutoAssignDistribution(s.Distribution)),
		Benefit:                       s.Benefit,
		HeatmapType:                   model.HeatMapType(s.HeatmapType),
		HeatMapByTimes:                mapHeatmapByTimes(s.HeatmapByTimes),
		CreatedAt:                     s.CreatedAt.AsTime(),
		UpdatedAt:                     s.UpdatedAt.AsTime(),
		VerifyDriverPhotoRate:         int(s.VerifyDriverPhotoRate),
		RequiredPhotoOfJacketAndBox:   s.RequiredPhotoOfJacketAndBox,
		RequiredPhotoOfJacketAndBoxes: assignArr(s.RequiredPhotoOfJacketAndBoxes),
		TopUpConfig: func(src *fleetareav1.ServiceAreaTopUpConfig) (dest model.ServiceAreaTopUpConfig) {
			if src != nil {
				dest = model.ServiceAreaTopUpConfig{
					EnabledCiti:      src.EnabledCiti,
					EnabledUob:       src.EnabledUob,
					GenerateUobRefID: src.GenerateUobRefId,
				}
			}
			return
		}(s.TopUpConfig),
		ShiftDriverIDs:              assignArr(s.ShiftDriverIds),
		BlackListBookShiftDriverIDs: assignArr(s.BlacklistBookShiftDriverIds),
		NegativeBalanceGroups:       mapNegativeBalanceGroups(s.NegativeBalanceGroups),
		NegativeBalanceConfig: func(src *fleetareav1.NegativeBalanceConfig) (dest model.NegativeBalanceConfig) {
			if src != nil {
				dest = model.NegativeBalanceConfig{
					Enabled:              s.NegativeBalanceConfig.Enabled,
					MinimumCreditBalance: s.NegativeBalanceConfig.MinimumCreditBalance,
					DriverIDs:            assignArr(s.NegativeBalanceConfig.DriverIds),
				}
			}
			return
		}(s.NegativeBalanceConfig),
		TierNegativeBalanceConfig:       mapTierNegativeBalanceConfig(s.TierNegativeBalanceConfig),
		SupplyPositioningServices:       model.ServicesFromString(s.SupplyPositioningServices...),
		MessengerCompletedOrdersLimit:   int(s.MessengerCompletedOrdersLimit),
		WhitelistUsersEnabled:           s.WhitelistUsersEnabled,
		WhitelistUsers:                  assignArr(s.WhitelistUsers),
		CookingTimeDelayReassignEnabled: s.CookingTimeDelayReassignEnabled,
		CookingTimeDelayThreshold:       time.Duration(s.CookingTimeDelayThreshold),
		ShowSubRegionsPolygon:           s.ShowSubRegionsPolygon,
		ShowProductivityIncentive:       s.ShowProductivityIncentive,
		ShowCoinIncentive:               s.ShowCoinIncentive,
		OfflineLaterBreakDuration:       time.Duration(s.OfflineLaterBreakDuration),
	}, nil
}

func mapHeatmapByTimes(heatMapByTimes []*fleetareav1.HeatMapByTime) []model.HeatMapByTime {
	res := make([]model.HeatMapByTime, 0, len(heatMapByTimes))
	for _, v := range heatMapByTimes {
		res = append(res, model.HeatMapByTime{
			StartTime:   v.StartTime,
			EndTime:     v.EndTime,
			HeatmapType: model.HeatMapType(v.HeatmapType),
		})
	}
	return res
}

func mapNegativeBalanceGroups(negativeBalanceGroups []*fleetareav1.NegativeBalanceGroupConfig) []model.NegativeBalanceGroupConfig {
	res := make([]model.NegativeBalanceGroupConfig, 0, len(negativeBalanceGroups))
	for _, v := range negativeBalanceGroups {
		res = append(res, model.NegativeBalanceGroupConfig{
			Name:                 v.Name,
			Enabled:              v.Enabled,
			MinimumCreditBalance: v.MinimumCreditBalance,
		})
	}
	return res
}

func mapTierNegativeBalanceConfig(negativeBalanceConfig *fleetareav1.TierNegativeBalanceConfig) (dest model.TierNegativeBalanceConfig) {
	if negativeBalanceConfig != nil {
		negativeBalanceByTier := make(model.DriverTierMap)

		for k, v := range negativeBalanceConfig.NegativeBalanceByTier {
			negativeBalanceByTier[model.DriverTier(k)] = v
		}

		dest = model.TierNegativeBalanceConfig{
			Enabled:               negativeBalanceConfig.Enabled,
			NegativeBalanceByTier: negativeBalanceByTier,
			BlacklistIDs:          assignArr(negativeBalanceConfig.BlacklistIds),
		}
	}
	return
}

func mapAutoAssignDistribution(ad *fleetareav1.AutoAssignDistribution) *model.AutoAssignDistribution {
	if ad == nil {
		return nil
	}

	ret := &model.AutoAssignDistribution{
		MinRadiusInKm:                                 ad.MinRadiusInKm,
		MaxRadiusInKm:                                 ad.MaxRadiusInKm,
		GroupDistInKm:                                 ad.GroupDistInKm,
		DistanceScoreWeight:                           ad.DistanceScoreWeight,
		AcceptingDurationInSecond:                     ad.AcceptingDurationInSecond,
		AutoAcceptScore:                               ad.AutoAcceptScore,
		NewbieMaxDays:                                 ad.NewbieMaxDays,
		NewbieScoreWeight:                             ad.NewbieScoreWeight,
		AcceptancePositiveRate:                        ad.AcceptancePositiveRate,
		AcceptanceMinOrders:                           ad.AcceptanceMinOrders,
		AcceptanceScoreWeight:                         ad.AcceptanceScoreWeight,
		IncentiveScoreA:                               ad.IncentiveScoreA,
		IncentiveScoreWeight:                          ad.IncentiveScoreWeight,
		MinIdleInMinute:                               ad.MinIdleInMinute,
		MaxIdleInMinute:                               ad.MaxIdleInMinute,
		IdleScoreWeight:                               ad.IdleScoreWeight,
		NotifyViaSocketIOEnabled:                      ad.NotifyViaSocketIoEnabled,
		AssignmentType:                                prediction.AssignmentType(ad.AssignmentType),
		MOType:                                        prediction.MOType(ad.MoType),
		BypassIdleTime:                                ad.BypassIdleTime,
		DistanceFromZoneLimit:                         ad.DistanceFromZoneLimit,
		PredictionWhitelist:                           assignArr(ad.PredictionWhitelist),
		PredictionBlacklist:                           assignArr(ad.PredictionBlacklist),
		PredictionRestaurantWhitelist:                 assignArr(ad.PredictionRestaurantWhitelist),
		BoxTypeScoreEnabled:                           ad.BoxTypeScoreEnabled,
		BoxTypeScoreWeight:                            ad.BoxTypeScoreWeight,
		BoxTypeScoreSettings:                          ad.BoxTypeScoreSettings,
		BoxTypeScoreServiceTypes:                      assignArr(ad.BoxTypeScoreServiceTypes),
		PredictionRestaurantBlacklistTimeSlots:        newPredictionRestaurantBlacklistTimeSlots(ad.PredictionRestaurantBlacklistTimeSlots),
		PredictionServiceEnabled:                      ad.PredictionServiceEnabled,
		PredictionPredictVersion:                      ad.PredictionPredictVersion,
		PredictionOptimizeVersion:                     ad.PredictionOptimizeVersion,
		PredictionBatchOptimizeVersion:                ad.PredictionBatchOptimizeVersion,
		PredictionRouteVersion:                        ad.PredictionRouteVersion,
		BackToBackExtendStatusConfig:                  ad.BackToBackExtendStatusConfig,
		BackToBackAllowStatus:                         model.NewStatusListFromString(ad.BackToBackAllowStatus...),
		RedistributionDelay:                           time.Duration(ad.RedistributionDelay),
		OptimizationTopN:                              ad.OptimizationTopN,
		OptimizationCandidateNumber:                   ad.OptimizationCandidateNumber,
		MultipleOrderAggressiveLevel:                  ad.MultipleOrderAggressiveLevel,
		BikeBiasLevel:                                 ad.BikeBiasLevel,
		SkipDedicatedRoundPercentage:                  ad.SkipDedicatedRoundPercentage,
		DalianDrivingDurationInSeconds:                ad.DalianDrivingDurationInSeconds,
		SkipQuota:                                     int(ad.SkipQuota),
		BanPeriod:                                     time.Duration(ad.BanPeriod),
		QuotaRefresh:                                  time.Duration(ad.QuotaRefresh),
		PriorityDedicatedRidersEnabled:                ad.PriorityDedicatedRidersEnabled,
		PriorityDedicatedRidersDistance:               ad.PriorityDedicatedRidersDistance,
		PriorityDedicatedRidersAllowOrderServiceTypes: model.ServicesFromString(ad.PriorityDedicatedRidersAllowOrderServiceTypes...),
		PriorityDedicatedRidersOnlySingleService:      model.ServicesFromString(ad.PriorityDedicatedRidersOnlySingleService...),
		CompleteTimeCalculation:                       newCompleteTimeCalculation(ad.CompleteTimeCalculation),
		RadiusTimeSlots:                               newRadiusTimeSlots(ad.RadiusTimeSlots),
		EnableBlacklistWhitelistForMPATR:              ad.EnableBlacklistWhitelistForMpAtr,
		DeferredDispatchFeatureEnabled:                ad.DeferredDispatchFeatureEnabled,
		DeferredDispatchFeatureEnabledMart:            ad.DeferredDispatchFeatureEnabledMart,
		DeferredDispatchRestaurantWhitelist:           assignArr(ad.DeferredDispatchRestaurantWhitelist),
		DeferredDispatchRestaurantBlacklist:           assignArr(ad.DeferredDispatchRestaurantBlacklist),
		DeferredDispatchDrivingDuration:               time.Duration(ad.DeferredDispatchDrivingDuration),
		DeferredDispatchBufferDuration:                time.Duration(ad.DeferredDispatchBufferDuration),
		DeferredDispatchBlacklistTimeSlots:            newDeferredDispatchBlacklistTimeSlots(ad.DeferredDispatchBlacklistTimeSlots),
		BatchAssignmentEnabled:                        ad.BatchAssignmentEnabled,
		FullyAutoAcceptEnabled:                        ad.FullyAutoAcceptEnabled,
		DalianOSRMEnabled:                             ad.DalianOsrmEnabled,
		DalianOSRMPhase:                               ad.DalianOsrmPhase,
		DalianOptimizeOSRMEnabled:                     ad.DalianOptimizeOsrmEnabled,
		RushDalianEnabled:                             ad.RushDalianEnabled,
		RushMode:                                      prediction.RushMode(ad.RushMode),
		RushExperimentWhitelist:                       assignArr(ad.RushExperimentWhitelist),
		NoRushExperimentWhitelist:                     assignArr(ad.NoRushExperimentWhitelist),
		SmartDistributionDeprioritizationRatio:        ad.SmartDistributionDeprioritizationRatio,
		SmartDistributionGoodnessBiasLevel:            prediction.BiasLevelType(ad.SmartDistributionGoodnessBiasLevel),
		SmartDistributionBlacklistZoneCodes:           assignArr(ad.SmartDistributionBlacklistZoneCodes),
		DalianMPEnabled:                               ad.DalianMpEnabled,
		OverrideOrderMPDeferUntil:                     ad.OverrideOrderMpDeferUntil,
		ServicePreference: model.ServicePreference{
			IsEnabled:                ad.ServicePreference.IsEnabled,
			CanEnable:                ad.ServicePreference.CanEnable,
			AllowedTypesForSelection: model.ServicesFromString(ad.ServicePreference.AllowedTypesForSelection...),
			AllTypes:                 model.ServicesFromString(ad.ServicePreference.AllTypes...),
		},
		BikeB2BEnabled:                  ad.BikeB2BEnabled,
		BikeCrossServiceEnabled:         ad.BikeCrossServiceEnabled,
		MartMOB2BEnabled:                ad.MartMoB2BEnabled,
		MartCrossServiceEnabled:         ad.MartCrossServiceEnabled,
		PredictionSwitchbackExperiments: newPredictionSwitchbackExperiments(ad.PredictionSwitchbackExperiments),
		BikePriorityTimeSlots:           newBikePriorityTimeSlots(ad.BikePriorityTimeSlots),
	}
	if ad.MaxOrdersPerRider != nil {
		ret.MaxOrdersPerRider = *ad.MaxOrdersPerRider
	} else {
		ret.MaxOrdersPerRider = 4
	}
	return ret
}

func newPredictionRestaurantBlacklistTimeSlots(slots []*fleetareav1.RestaurantBlacklistTimeSlot) model.RestaurantBlacklistTimeSlots {
	res := make(model.RestaurantBlacklistTimeSlots, 0, len(slots))
	for _, v := range slots {
		res = append(res, model.RestaurantBlacklistTimeSlot{
			Time: model.StartEndTime{
				Begin: v.Time.Begin,
				End:   v.Time.End,
			},
			IDs:             v.Ids,
			BlacklistOption: model.RestaurantBlacklistOption(v.BlacklistOption),
		})
	}
	return res
}

func newRadiusTimeSlots(r []*fleetareav1.RadiusTimeSlotWithServiceType) model.RadiusTimeSlotsWithServiceTypes {
	res := make(model.RadiusTimeSlotsWithServiceTypes, 0, len(r))
	for _, v := range r {
		radiiByServiceType := make([]model.RadiusByServiceType, 0, len(v.RadiiByServiceType))
		for _, serviceType := range v.RadiiByServiceType {
			radiiByServiceType = append(radiiByServiceType, model.RadiusByServiceType{
				ServiceType:   model.Service(serviceType.ServiceType),
				MaxRadiusInKm: serviceType.MaxRadiusInKm,
			})
		}
		res = append(res, model.RadiusTimeSlotWithServiceTypes{
			Time: model.StartEndTime{
				Begin: v.Time.Begin,
				End:   v.Time.End,
			},
			RadiiByServiceType: radiiByServiceType,
		})
	}
	return res
}

func newDeferredDispatchBlacklistTimeSlots(entity []*fleetareav1.DeferBlacklistTimeSlots) model.DeferBlacklistTimeSlots {
	res := make(model.DeferBlacklistTimeSlots, 0, len(entity))
	for _, v := range entity {
		res = append(res, model.DeferBlacklistTimeSlot{
			Time: model.StartEndTime{
				Begin: v.Time.Begin,
				End:   v.Time.End,
			},
		})
	}
	return res
}

func mapCompleteTimeDuration(v int64) *model.Duration {
	return types.Ptr(model.Duration{Duration: time.Duration(v)})
}

func newCompleteTimeCalculation(c *fleetareav1.CompleteTimeCalculation) model.CompleteTimeCalculation {
	res := model.CompleteTimeCalculation{}
	if c.MeanAbsoluteError != nil {
		res.MeanAbsoluteError = mapCompleteTimeDuration(*c.MeanAbsoluteError)
	}
	if c.AbsoluteError != nil {
		res.AbsoluteError = mapCompleteTimeDuration(*c.AbsoluteError)
	}
	return res
}

func newPredictionSwitchbackExperiments(entity []*fleetareav1.PredictionSwitchbackExperiment) model.PredictionSwitchbackExperiments {
	if entity == nil {
		// keep behavior same as getting model directly from MongoDB
		return nil
	}
	res := make(model.PredictionSwitchbackExperiments, 0, len(entity))
	for _, v := range entity {
		timeRanges := make([]model.PredictionSwitchbackTimeRange, 0, len(v.TimeRanges))
		for _, t := range v.TimeRanges {
			timeRanges = append(timeRanges, model.PredictionSwitchbackTimeRange{
				BeginDateTime: t.BeginDateTime.AsTime(),
				EndDateTime:   t.EndDateTime.AsTime(),
			})
		}
		res = append(res, model.PredictionSwitchbackExperiment{
			TimeRanges:           timeRanges,
			Name:                 v.Name,
			PredictVersion:       v.PredictVersion,
			OptimizeVersion:      v.OptimizeVersion,
			BatchOptimizeVersion: v.BatchOptimizeVersion,
			RouteVersion:         v.RouteVersion,
		})
	}
	return res
}

func newBikePriorityTimeSlots(entity []*fleetareav1.BikePriorityTimeSlot) model.BikePriorityTimeSlots {
	if entity == nil {
		// keep behavior same as getting model directly from MongoDB
		return nil
	}
	res := make(model.BikePriorityTimeSlots, 0, len(entity))
	for _, v := range entity {
		timeRanges := make([]model.StartEndTime, 0, len(v.TimeRanges))
		for _, t := range v.TimeRanges {
			if t == nil {
				continue
			}
			timeRanges = append(timeRanges, model.StartEndTime{
				Begin: t.Begin,
				End:   t.End,
			})
		}
		daysOfWeek := make([]model.Days, 0, len(v.DaysOfWeek))
		for _, d := range v.DaysOfWeek {
			daysOfWeek = append(daysOfWeek, model.Days(d))
		}
		res = append(res, model.BikePriorityTimeSlot{
			TimeRanges: timeRanges,
			DaysOfWeek: daysOfWeek,
		})
	}
	return res
}

func deRef[T any](v *T) T {
	var zero T
	if v == nil {
		return zero
	}
	return *v
}

func assignArr[T any](arr []T) []T {
	if arr == nil {
		return make([]T, 0)
	}
	return arr
}
