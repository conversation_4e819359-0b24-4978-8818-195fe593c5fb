package mongotxn

import (
	"context"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

type MongoTxnWrapper struct {
	txnHelper                          transaction.TxnHelper
	secondaryDBTxnHelper               transaction.TxnHelper
	config                             *config.AtomicMongoTxnConfig
	transactionNumberOfRetryHistogram  metric.Histogram
	transactionProcessingTimeHistogram metric.Histogram
}

func (m *MongoTxnWrapper) WithTxn(ctx context.Context, fn func(sessCtx context.Context) (interface{}, error), opts ...transaction.Option) (interface{}, error) {
	cfg := m.config.Get()
	defaultOpts := []transaction.Option{}
	if cfg.EnableTxnCommitTime {
		defaultOpts = append(defaultOpts, transaction.WithMaxCommitTime(cfg.TxnCommitTime))
	}
	if cfg.EnableTxnRetryWindow {
		defaultOpts = append(defaultOpts, transaction.WithRetryWindow(cfg.TxnRetryWindow))
	}
	defaultOpts = append(defaultOpts, transaction.WithOnRetry(func(number int, label string) {
		m.transactionNumberOfRetryHistogram.Observe(float64(number), label)
	}))
	defaultOpts = append(defaultOpts, transaction.WithOnProcessingTxnTime(func(latencyMS float64, label string, layer string) {
		m.transactionProcessingTimeHistogram.Observe(latencyMS, label, layer)
	}))

	return m.txnHelperFromContext(ctx).WithTxn(ctx, fn, append(defaultOpts, opts...)...)
}

func (m *MongoTxnWrapper) NumberOfSessionsInProgress() int {
	return m.txnHelper.NumberOfSessionsInProgress() + m.secondaryDBTxnHelper.NumberOfSessionsInProgress()
}

func ProvideMongoTxnHelper(meter metric.Meter, conn *mongodb.Conn, config *config.AtomicMongoTxnConfig, secondaryDBConnection datastore.SecondaryDBConnection) transaction.TxnHelper {
	return &MongoTxnWrapper{
		txnHelper:                          transaction.NewTxnHelper(conn),
		secondaryDBTxnHelper:               transaction.NewTxnHelper(secondaryDBConnection),
		config:                             config,
		transactionNumberOfRetryHistogram:  meter.GetHistogram("transaction_number_of_retry", "transaction number of retry", []float64{-1, 0, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, 233, 377, 610, 987}, "function_name"),
		transactionProcessingTimeHistogram: meter.GetHistogram("transaction_processing_time_ms", "transaction processing time in millisecond", []float64{50, 80, 130, 340, 550, 890, 1440, 2330, 3770, 6100, 9870, 15970, 25840, 41810, 67650, 109460, 177100}, "function_name", "layer"),
	}
}

type contextKey string
type zerobyte struct{}

var secondaryDBKey = contextKey("secondary_db")

func WithSecondaryDB(ctx context.Context) context.Context {
	if ctx == nil {
		ctx = context.Background()
	}
	return context.WithValue(ctx, secondaryDBKey, zerobyte{})
}

func (m *MongoTxnWrapper) txnHelperFromContext(ctx context.Context) transaction.TxnHelper {
	if v := ctx.Value(secondaryDBKey); v != nil {
		return m.secondaryDBTxnHelper
	}
	return m.txnHelper
}
