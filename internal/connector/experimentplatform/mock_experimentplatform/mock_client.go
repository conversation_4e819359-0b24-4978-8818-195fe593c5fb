// Code generated by MockGen. DO NOT EDIT.
// Source: ./client.go

// Package mock_experimentplatform is a generated GoMock package.
package mock_experimentplatform

import (
	reflect "reflect"

	exp "git.wndv.co/dalian-dev/experiment-sdk"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockExperimentPlatformClient is a mock of ExperimentPlatformClient interface.
type MockExperimentPlatformClient struct {
	ctrl     *gomock.Controller
	recorder *MockExperimentPlatformClientMockRecorder
}

// MockExperimentPlatformClientMockRecorder is the mock recorder for MockExperimentPlatformClient.
type MockExperimentPlatformClientMockRecorder struct {
	mock *MockExperimentPlatformClient
}

// NewMockExperimentPlatformClient creates a new mock instance.
func NewMockExperimentPlatformClient(ctrl *gomock.Controller) *MockExperimentPlatformClient {
	mock := &MockExperimentPlatformClient{ctrl: ctrl}
	mock.recorder = &MockExperimentPlatformClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExperimentPlatformClient) EXPECT() *MockExperimentPlatformClientMockRecorder {
	return m.recorder
}

// GetSwitchbackExperimentsWithConditions mocks base method.
func (m *MockExperimentPlatformClient) GetSwitchbackExperimentsWithConditions(distributedID string, conditions ...*exp.Condition) (*model.SwitchbackExperiments, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{distributedID}
	for _, a := range conditions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSwitchbackExperimentsWithConditions", varargs...)
	ret0, _ := ret[0].(*model.SwitchbackExperiments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSwitchbackExperimentsWithConditions indicates an expected call of GetSwitchbackExperimentsWithConditions.
func (mr *MockExperimentPlatformClientMockRecorder) GetSwitchbackExperimentsWithConditions(distributedID interface{}, conditions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{distributedID}, conditions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSwitchbackExperimentsWithConditions", reflect.TypeOf((*MockExperimentPlatformClient)(nil).GetSwitchbackExperimentsWithConditions), varargs...)
}

// MockDistributionExperimentPlatformClient is a mock of DistributionExperimentPlatformClient interface.
type MockDistributionExperimentPlatformClient struct {
	ctrl     *gomock.Controller
	recorder *MockDistributionExperimentPlatformClientMockRecorder
}

// MockDistributionExperimentPlatformClientMockRecorder is the mock recorder for MockDistributionExperimentPlatformClient.
type MockDistributionExperimentPlatformClientMockRecorder struct {
	mock *MockDistributionExperimentPlatformClient
}

// NewMockDistributionExperimentPlatformClient creates a new mock instance.
func NewMockDistributionExperimentPlatformClient(ctrl *gomock.Controller) *MockDistributionExperimentPlatformClient {
	mock := &MockDistributionExperimentPlatformClient{ctrl: ctrl}
	mock.recorder = &MockDistributionExperimentPlatformClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDistributionExperimentPlatformClient) EXPECT() *MockDistributionExperimentPlatformClientMockRecorder {
	return m.recorder
}

// GetSwitchbackExperimentsWithConditions mocks base method.
func (m *MockDistributionExperimentPlatformClient) GetSwitchbackExperimentsWithConditions(distributedID string, conditions ...*exp.Condition) (*model.SwitchbackExperiments, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{distributedID}
	for _, a := range conditions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSwitchbackExperimentsWithConditions", varargs...)
	ret0, _ := ret[0].(*model.SwitchbackExperiments)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSwitchbackExperimentsWithConditions indicates an expected call of GetSwitchbackExperimentsWithConditions.
func (mr *MockDistributionExperimentPlatformClientMockRecorder) GetSwitchbackExperimentsWithConditions(distributedID interface{}, conditions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{distributedID}, conditions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSwitchbackExperimentsWithConditions", reflect.TypeOf((*MockDistributionExperimentPlatformClient)(nil).GetSwitchbackExperimentsWithConditions), varargs...)
}
