package rep

import "git.wndv.co/lineman/absinthe/rep/lineman_rep"

var DefaultConfig = config{
	location: &lineman_rep.Location{},
}

type config struct {
	location *lineman_rep.Location
}

func (cfg *config) Location() *lineman_rep.Location {
	return cfg.location
}

type Option func(opt config) config

var WithLocation = func(location *lineman_rep.Location) Option {
	return func(opt config) config {
		opt.location = location
		return opt
	}
}
