package slack

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
)

//go:generate mockgen -source=slack.go -destination=./mock_slack/slack.go -package=mock_slack

type Slack interface {
	Notify(ctx context.Context, req *NotifyRequest) error
}

type SlackImpl struct {
	cfg    Config
	client *httpclient.Client
}

func (s *SlackImpl) Notify(ctx context.Context, request *NotifyRequest) error {
	if !s.cfg.Enable {
		return nil
	}

	if request.Webhook == nil {
		return errors.New("webhook is required")
	}

	body, err := json.Marshal(request)
	if err != nil {
		return err
	}

	url := s.getURL(*request.Webhook)
	if url == "" {
		return errors.New("unhandled webhook type")
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(body))
	if err != nil {
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := s.client.Do(ctx, req)
	if err != nil {
		return err
	}
	defer func() { _ = resp.Body.Close() }()

	if resp.StatusCode != http.StatusOK {
		var b []byte
		b, err = io.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		return errors.New(string(b))
	}

	return nil
}

func (s *SlackImpl) getURL(webhook Webhook) string {
	var webhookToken string
	switch webhook {
	case WebhookFleetRainPODStatusMonitoring:
		webhookToken = s.cfg.WebhookTokenFleetRainPODStatusMonitoring
	case WebhookFleetTopupReconcileMonitoring:
		webhookToken = s.cfg.WebhookTokenFleetTopupReconcileMonitoring
	case WebhookFleetCitiInquiryMonitoring:
		webhookToken = s.cfg.WebhookTokenFleetCitiInquiryMonitoring
	case WebhookFleetEgsServiceMonitoring:
		webhookToken = s.cfg.WebhookTokenFleetEgsServiceMonitoring
	case WebhookFleetBulkSchedulerMonitoring:
		webhookToken = s.cfg.WebhookTokenFleetBulkSchedulerMonitoring
	default:
		return ""
	}

	return fmt.Sprintf("%s%s%s", s.cfg.Host, s.cfg.Path, webhookToken)
}

// @@wire-set-name@@ name:"Main"
func ProvideSlack(cfg Config) Slack {
	return &SlackImpl{
		cfg:    cfg,
		client: httpclient.NewWithTimeout(cfg.HTTPTimeout),
	}
}
