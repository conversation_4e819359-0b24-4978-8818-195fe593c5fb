//go:generate mockgen -source=kafka_producer.go -destination=./mock_kafka/mock_kafka_producer.go -package=mock_kafka_producer

package kafcclient

import (
	"context"

	"github.com/kelseyhightower/envconfig"
)

type IMFKafkaProducer interface {
	kafkaProducer
	connectionChecker
}

type IMFKafkaProducerConfig struct {
	Enabled            bool `envconfig:"KAFKA_PRODUCER_ENABLED" default:"true"`
	KafkaProducerDebug bool `envconfig:"KAFKA_PRODUCER_DEBUG" default:"false"`
}

func ProvideIMFKafkaProducerConfig() IMFKafkaProducerConfig {
	var cfg IMFKafkaProducerConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

type SecureIMFKafkaProducer interface {
	kafkaProducer
	connectionChecker
}

type SecureIMFKafkaProducerConfig struct {
	Enabled bool `envconfig:"SECURE_KAFKA_PRODUCER_ENABLED" default:"true"`
}

func ProvideSecureIMFKafkaProducerConfig() SecureIMFKafkaProducerConfig {
	var cfg SecureIMFKafkaProducerConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

type kafkaProducer interface {
	SendMessage(ctx context.Context, topic string, key string, payload []byte, headers map[string]string) error
}

type connectionChecker interface {
	CheckConnection(ctx context.Context) error
}
