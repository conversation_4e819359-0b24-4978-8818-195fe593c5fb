package kafcclientconsumer

import (
	"context"

	"github.com/IBM/sarama"
	"github.com/kelseyhightower/envconfig"
	"google.golang.org/protobuf/proto"

	"git.wndv.co/go/kafc"
	"git.wndv.co/go/logx/v2"
	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
)

type EGSServiceKafkaConsumer interface {
	Run(ctx context.Context)
}

type egsServiceKafkaConsumer struct {
	consumer        kafc.ConsumerGroup
	egsOrderService service.EGSOrderService
}

// @@no-locator-generation@@
func ProvideEGSServiceKafkaConsumer(cfg KafkaEGSServiceConsumerConfig, egsOrderService service.EGSOrderService) (EGSServiceKafkaConsumer, func()) {
	var kafkaCgf kafc.KafkaConfig
	envconfig.MustProcess("EGS_ORDER", &kafkaCgf)
	consumer, err := kafc.NewConsumerGroup(kafkaCgf, cfg.ConsumerGroup, cfg.Topic, false)
	if err != nil {
		logx.Error().Err(err).Msg("unable to init egs service kafka consumer")
		return nil, func() {}
	}

	cleanUp := func() {
		if consumer != nil {
			err := consumer.Close()
			if err != nil {
				logx.Error().Err(err).Msg("error closing consumer")
			}
		}
	}

	s := &egsServiceKafkaConsumer{
		consumer:        consumer,
		egsOrderService: egsOrderService,
	}

	return s, cleanUp
}

func (k *egsServiceKafkaConsumer) Run(ctx context.Context) {
	logx.Info().Msg("EGSServiceKafkaConsumer: run")
	if err := k.consumer.OnMessage(func(ctx context.Context, msg interface{}) error {
		saramaMsg, ok := msg.(*sarama.ConsumerMessage)
		if !ok {
			logx.Error().Msg("cannot convert to sarama consumer message")
			return nil
		}

		var event egsv1.EGSOrderEvent
		if err := proto.Unmarshal(saramaMsg.Value, &event); err != nil {
			return err
		}
		logx.Info().Msgf("EGSConsumer OnMessage Event: %+v", &event)

		if err := k.egsOrderService.ConsumeEGSOrderEvent(ctx, &event); err != nil {
			logx.Error().Err(err).Msgf("EGSConsumer OnMessage Event: %+v", &event)
		}

		return nil
	}); err != nil {
		logx.Error().Err(err).Msg("kafka consumer group error occurred")
		panic(err) //consumer group already stop message consume, raise panic to exit application
	}
}

type KafkaEGSServiceConsumerConfig struct {
	ConsumerGroup string `envconfig:"EGS_ORDER_KAFKA_CONSUMER_GROUP_NAME"`
	Topic         string `envconfig:"EGS_ORDER_KAFKA_TOPIC"`
}

func ProvideKafkaEGSServiceConsumerConfig() KafkaEGSServiceConsumerConfig {
	var cfg KafkaEGSServiceConsumerConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}
