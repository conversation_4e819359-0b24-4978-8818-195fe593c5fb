package kafcclient

import (
	"context"

	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/kafc"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/saramaprom"
)

func newKafkaClient(cfg kafc.KafkaConfig, metricOpts ...func(*saramaprom.Options)) (kafc.AsyncProducer, func()) {
	client, err := kafc.NewKafc(cfg, kafc.WithDefaultPartitioner())
	if err != nil {
		logrus.Errorf("cannot connect kafka: %v", err)
		panic(err)
	}

	ctx, cancel := context.WithCancel(context.Background())

	cleanup := func() {
		// close producer
		if err := client.Close(); err != nil {
			logrus.Errorf("cannot close kafka default producer: %v", err)
			return
		}

		// close client
		if err := client.Unwrap().Close(); err != nil {
			logrus.Errorf("cannot close kafka client: %v", err)
			return
		}

		cancel()
		logrus.Info("gracefully shutdown kafka successfully")
	}

	err = saramaprom.ExportMetrics(ctx, client.Unwrap().Config().MetricRegistry, metricOpts...)
	if err != nil {
		panic(err)
	}

	return client, cleanup
}

func NewClient(cfg kafc.KafkaConfig, metricOpts ...func(*saramaprom.Options)) (kafc.AsyncProducer, func()) {
	return newKafkaClient(cfg, metricOpts...)
}

func SaramaPromOptions(namespace, label string) func(*saramaprom.Options) {
	if namespace == "" {
		namespace = "driver"
	}
	return func(opt *saramaprom.Options) {
		opt.Label = label
		opt.Namespace = namespace
		opt.OnError = func(err error) {
			logrus.Errorf("error when export saramaprom: %v", err)
		}
	}
}

type VerdaKafkaClient struct {
	kafc.AsyncProducer
}

type VerdaKafkaCfg kafc.KafkaConfig

func (c VerdaKafkaCfg) KafkaConfig() kafc.KafkaConfig {
	return kafc.KafkaConfig(c)
}

func ProvideVerdaKafkaCfg() VerdaKafkaCfg {
	var cfg kafc.KafkaConfig
	envconfig.MustProcess("VERDA", &cfg)
	return VerdaKafkaCfg(cfg)
}

// @@no-locator-generation@@
func ProvideVerdaKafkaClient(cfg VerdaKafkaCfg) (*VerdaKafkaClient, func()) {
	client, cleanUp := newKafkaClient(cfg.KafkaConfig(), SaramaPromOptions(cfg.ClientID, "Verda-Kafka"))
	return &VerdaKafkaClient{AsyncProducer: client}, cleanUp
}

type IMFKafkaClient struct {
	kafc.AsyncProducer
	cfg kafc.KafkaConfig
}

// @@no-locator-generation@@
func ProvideIMFKafkaClient(kafkaCfg IMFKafkaProducerConfig) (*IMFKafkaClient, func()) {
	var cfg kafc.KafkaConfig
	envconfig.MustProcess("", &cfg)

	if !kafkaCfg.Enabled {
		return &IMFKafkaClient{AsyncProducer: nil, cfg: cfg}, func() {}
	}

	client, cleanUp := newKafkaClient(cfg, SaramaPromOptions(cfg.ClientID, "IMF-Kafka"))
	return &IMFKafkaClient{AsyncProducer: client, cfg: cfg}, cleanUp
}

type SecureIMFKafkaClient struct {
	kafc.AsyncProducer
	cfg kafc.KafkaConfig
}

// @@no-locator-generation@@
func ProvideSecureIMFKafkaClient(kafkaCfg SecureIMFKafkaProducerConfig) (*SecureIMFKafkaClient, func()) {
	var cfg kafc.KafkaConfig
	envconfig.MustProcess("SECURE", &cfg)

	if !kafkaCfg.Enabled {
		return &SecureIMFKafkaClient{AsyncProducer: nil, cfg: cfg}, func() {}
	}

	client, cleanUp := newKafkaClient(cfg, SaramaPromOptions(cfg.ClientID, "Secure-IMF-Kafka"))
	return &SecureIMFKafkaClient{AsyncProducer: client, cfg: cfg}, cleanUp
}
