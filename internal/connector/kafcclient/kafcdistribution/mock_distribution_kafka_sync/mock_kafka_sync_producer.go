// Code generated by MockGen. DO NOT EDIT.
// Source: kafka_sync_producer.go

// Package mock_distribution_kafka_sync_producer is a generated GoMock package.
package mock_distribution_kafka_sync_producer

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockSecureIMFKafkaSyncProducer is a mock of SecureIMFKafkaSyncProducer interface.
type MockSecureIMFKafkaSyncProducer struct {
	ctrl     *gomock.Controller
	recorder *MockSecureIMFKafkaSyncProducerMockRecorder
}

// MockSecureIMFKafkaSyncProducerMockRecorder is the mock recorder for MockSecureIMFKafkaSyncProducer.
type MockSecureIMFKafkaSyncProducerMockRecorder struct {
	mock *MockSecureIMFKafkaSyncProducer
}

// NewMockSecureIMFKafkaSyncProducer creates a new mock instance.
func NewMockSecureIMFKafkaSyncProducer(ctrl *gomock.Controller) *MockSecureIMFKafkaSyncProducer {
	mock := &MockSecureIMFKafkaSyncProducer{ctrl: ctrl}
	mock.recorder = &MockSecureIMFKafkaSyncProducerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSecureIMFKafkaSyncProducer) EXPECT() *MockSecureIMFKafkaSyncProducerMockRecorder {
	return m.recorder
}

// SendMessage mocks base method.
func (m *MockSecureIMFKafkaSyncProducer) SendMessage(ctx context.Context, topic, key string, payload []byte, headers map[string]string) (int32, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", ctx, topic, key, payload, headers)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockSecureIMFKafkaSyncProducerMockRecorder) SendMessage(ctx, topic, key, payload, headers interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockSecureIMFKafkaSyncProducer)(nil).SendMessage), ctx, topic, key, payload, headers)
}

// MockkafkaSyncProducer is a mock of kafkaSyncProducer interface.
type MockkafkaSyncProducer struct {
	ctrl     *gomock.Controller
	recorder *MockkafkaSyncProducerMockRecorder
}

// MockkafkaSyncProducerMockRecorder is the mock recorder for MockkafkaSyncProducer.
type MockkafkaSyncProducerMockRecorder struct {
	mock *MockkafkaSyncProducer
}

// NewMockkafkaSyncProducer creates a new mock instance.
func NewMockkafkaSyncProducer(ctrl *gomock.Controller) *MockkafkaSyncProducer {
	mock := &MockkafkaSyncProducer{ctrl: ctrl}
	mock.recorder = &MockkafkaSyncProducerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockkafkaSyncProducer) EXPECT() *MockkafkaSyncProducerMockRecorder {
	return m.recorder
}

// SendMessage mocks base method.
func (m *MockkafkaSyncProducer) SendMessage(ctx context.Context, topic, key string, payload []byte, headers map[string]string) (int32, int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SendMessage", ctx, topic, key, payload, headers)
	ret0, _ := ret[0].(int32)
	ret1, _ := ret[1].(int64)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// SendMessage indicates an expected call of SendMessage.
func (mr *MockkafkaSyncProducerMockRecorder) SendMessage(ctx, topic, key, payload, headers interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SendMessage", reflect.TypeOf((*MockkafkaSyncProducer)(nil).SendMessage), ctx, topic, key, payload, headers)
}
