//go:generate mockgen -source=kafka_sync_producer.go -destination=./mock_distribution_kafka_sync/mock_kafka_sync_producer.go -package=mock_distribution_kafka_sync_producer

package kafcclientdistribution

import (
	"context"

	"github.com/kelseyhightower/envconfig"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/kafc"
)

type SecureIMFKafkaSyncProducer interface {
	kafkaSyncProducer
}

type SecureIMFKafkaSyncProducerConfig struct {
	Enabled bool `envconfig:"SECURE_KAFKA_SYNC_PRODUCER_ENABLED" default:"true"`
}

func ProvideSecureIMFKafkaSyncProducerConfig() SecureIMFKafkaSyncProducerConfig {
	var cfg SecureIMFKafkaSyncProducerConfig
	envconfig.MustProcess("", &cfg)
	return cfg
}

type kafkaSyncProducer interface {
	SendMessage(ctx context.Context, topic string, key string, payload []byte, headers map[string]string) (partition int32, offset int64, err error)
}

var _ SecureIMFKafkaSyncProducer = (*KafcSyncProducer)(nil)

type KafcSyncProducer struct {
	cfg  kafc.KafkaConfig
	send func(ctx context.Context, topic string, messageKey string, payload []byte, headers map[string]string) (partition int32, offset int64, err error)
}

// @@wire-set-name@@ name:"Main"
// @@no-locator-generation@@
func ProvideSecureIMFKafkaSyncProducer(kafkaCfg SecureIMFKafkaSyncProducerConfig, client *SecureIMFKafkaSyncClient) SecureIMFKafkaSyncProducer {
	defer func() {
		logrus.WithFields(logrus.Fields{
			"enabled":           kafkaCfg.Enabled,
			"bootstrap_servers": client.cfg.BootstrapServers,
		}).Info("Secure IMF kafc sync producer initialized successfully")
	}()

	producer := &KafcSyncProducer{
		cfg: client.cfg,
	}

	if !kafkaCfg.Enabled {
		logrus.Info("Secure IMF kafc sync producer flag disabled, work in connection checker mode")
		producer.send = func(ctx context.Context, topic string, messageKey string, payload []byte, headers map[string]string) (partition int32, offset int64, err error) {
			return 0, 0, nil
		}
	} else {
		producer.send = client.PublishRawAtLeastOnce
	}

	return producer
}

func (k *KafcSyncProducer) SendMessage(ctx context.Context, topic string, key string, payload []byte, headers map[string]string) (partition int32, offset int64, err error) {
	return k.send(ctx, topic, key, payload, headers)
}
