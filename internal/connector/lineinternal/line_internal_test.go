package lineinternal_test

import (
	"context"
	"net/http"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal/mock_lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
)

func TestEncryptMIDs_Success(t *testing.T) {
	ctx := context.Background()

	httpClient := &http.Client{}
	httpmock.ActivateNonDefault(httpClient)
	defer httpmock.DeactivateAndReset()

	deps, finish := provideTestDependency(t)
	defer finish()

	deps.mockFeatureFlag.EXPECT().
		IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEInternalOAuthTokenEnabled.Name).
		Return(false)

	lineChannelID := "1234567890"
	lineChannelSecret := "abcdefghijklmnopqrstuvwxyz012345"

	httpmock.RegisterResponder(http.MethodGet, "https://internal-api.line-apps-beta-test.com/v1/internal/mid/encrypt/bulk?mids=uxxx&mids=uyyy",
		func(req *http.Request) (*http.Response, error) {
			assert.Nil(t, req.Body)

			require.Equal(t, "1234567890", req.Header.Get("X-Line-ChannelID"))
			require.Equal(t, "abcdefghijklmnopqrstuvwxyz012345", req.Header.Get("X-Line-ChannelSecret"))

			mids := req.URL.Query()["mids"]
			require.Contains(t, mids, "uxxx")
			require.Contains(t, mids, "uyyy")
			return httpmock.NewStringResponse(http.StatusOK, `{"userIds":{"uxxx":"Uxxx","uyyy":"Uyyy"}}`), nil
		})

	lineHttpClient := httpclient.LINEHTTPClient{
		Client: httpClient,
	}
	svc := lineinternal.ProvideLINEInternalClient(&lineHttpClient, lineinternal.LINEInternalConfig{
		Host: "https://internal-api.line-apps-beta-test.com",
	}, deps.mockFeatureFlag, deps.mockLINEClient)

	resp, resErr := svc.EncryptMIDs(ctx, lineChannelID, lineChannelSecret, "uxxx", "uyyy")
	require.NoError(t, resErr)
	require.Equal(t, &lineinternal.EncryptMIDsResponse{
		UserIDs: map[string]string{
			"uxxx": "Uxxx",
			"uyyy": "Uyyy",
		},
	}, resp)
}

func TestEncryptMIDs_Failed(t *testing.T) {
	ctx := context.Background()
	httpClient := &http.Client{}
	httpmock.ActivateNonDefault(httpClient)
	defer httpmock.DeactivateAndReset()

	deps, finish := provideTestDependency(t)
	defer finish()

	deps.mockFeatureFlag.EXPECT().
		IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEInternalOAuthTokenEnabled.Name).
		Return(false)

	lineChannelID := "1234567890"
	lineChannelSecret := "abcdefghijklmnopqrstuvwxyz012345"

	httpmock.RegisterResponder(http.MethodGet, "https://internal-api.line-apps-beta-test.com/v1/internal/mid/encrypt/bulk?mids=uxxx&mids=uyyy&mids=",
		func(req *http.Request) (*http.Response, error) {
			assert.Nil(t, req.Body)

			require.Equal(t, "1234567890", req.Header.Get("X-Line-ChannelID"))
			require.Equal(t, "abcdefghijklmnopqrstuvwxyz012345", req.Header.Get("X-Line-ChannelSecret"))

			mids := req.URL.Query()["mids"]
			require.Contains(t, mids, "uxxx")
			require.Contains(t, mids, "uyyy")
			require.Contains(t, mids, "")
			return httpmock.NewStringResponse(http.StatusBadRequest, `{"statusCode":"400","statusMessage":"Invalid mid"}`), nil
		})

	lineHttpClient := httpclient.LINEHTTPClient{
		Client: httpClient,
	}
	svc := lineinternal.ProvideLINEInternalClient(&lineHttpClient, lineinternal.LINEInternalConfig{
		Host: "https://internal-api.line-apps-beta-test.com",
	}, deps.mockFeatureFlag, deps.mockLINEClient)

	resp, resErr := svc.EncryptMIDs(ctx, lineChannelID, lineChannelSecret, "uxxx", "uyyy", "")
	expectedError := &api.Error{
		Code:    "INVALID_REQUEST",
		Message: "400: Invalid mid",
	}
	require.Error(t, resErr)
	require.Equal(t, expectedError.Error(), resErr.Error())
	require.Nil(t, resp)
}

func TestEncryptMIDsInternal(t *testing.T) {
	ctx := context.Background()
	httpClient := &http.Client{}
	httpmock.ActivateNonDefault(httpClient)
	defer httpmock.DeactivateAndReset()

	deps, finish := provideTestDependency(t)
	defer finish()

	deps.mockFeatureFlag.EXPECT().
		IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEInternalOAuthTokenEnabled.Name).
		Return(false)

	httpmock.RegisterResponder(http.MethodGet, "https://internal-api.line-apps-beta-test.com/v1/internal/mid/encrypt/bulk?mids=uxxx&mids=uyyy",
		func(req *http.Request) (*http.Response, error) {
			assert.Nil(t, req.Body)

			require.Equal(t, "1234567890", req.Header.Get("X-Line-ChannelID"))
			require.Equal(t, "abcdefghijklmnopqrstuvwxyz012345", req.Header.Get("X-Line-ChannelSecret"))

			mids := req.URL.Query()["mids"]
			require.Contains(t, mids, "uxxx")
			require.Contains(t, mids, "uyyy")
			return httpmock.NewStringResponse(http.StatusOK, `{"userIds":{"uxxx":"Uxxx","uyyy":"Uyyy"}}`), nil
		})

	lineHttpClient := httpclient.LINEHTTPClient{
		Client: httpClient,
	}
	svc := lineinternal.ProvideLINEInternalClient(&lineHttpClient, lineinternal.LINEInternalConfig{
		Host:              "https://internal-api.line-apps-beta-test.com",
		LINEChannelID:     "1234567890",
		LINEChannelSecret: "abcdefghijklmnopqrstuvwxyz012345",
	}, deps.mockFeatureFlag, deps.mockLINEClient)

	resp, resErr := svc.EncryptMIDsInternal(ctx, "uxxx", "uyyy")
	require.NoError(t, resErr)
	require.Equal(t, &lineinternal.EncryptMIDsResponse{
		UserIDs: map[string]string{
			"uxxx": "Uxxx",
			"uyyy": "Uyyy",
		},
	}, resp)
}

func TestDecryptUIDs_Success(t *testing.T) {
	ctx := context.Background()
	httpClient := &http.Client{}
	httpmock.ActivateNonDefault(httpClient)
	defer httpmock.DeactivateAndReset()

	deps, finish := provideTestDependency(t)
	defer finish()

	deps.mockFeatureFlag.EXPECT().
		IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEInternalOAuthTokenEnabled.Name).
		Return(false)

	lineChannelID := "1234567890"
	lineChannelSecret := "abcdefghijklmnopqrstuvwxyz012345"

	httpmock.RegisterResponder(http.MethodGet, "https://internal-api.line-apps-beta-test.com/v1/internal/mid/decrypt/bulk?userIds=Uxxx&userIds=Uyyy",
		func(req *http.Request) (*http.Response, error) {
			assert.Nil(t, req.Body)

			require.Equal(t, "1234567890", req.Header.Get("X-Line-ChannelID"))
			require.Equal(t, "abcdefghijklmnopqrstuvwxyz012345", req.Header.Get("X-Line-ChannelSecret"))

			userIds := req.URL.Query()["userIds"]
			require.Contains(t, userIds, "Uxxx")
			require.Contains(t, userIds, "Uyyy")
			return httpmock.NewStringResponse(http.StatusOK, `{"mids":{"Uxxx":"xxx","Uyyy":"yyy"}}`), nil
		})

	lineHttpClient := httpclient.LINEHTTPClient{
		Client: httpClient,
	}
	svc := lineinternal.ProvideLINEInternalClient(&lineHttpClient, lineinternal.LINEInternalConfig{
		Host: "https://internal-api.line-apps-beta-test.com",
	}, deps.mockFeatureFlag, deps.mockLINEClient)

	resp, resErr := svc.DecryptUIDs(ctx, lineChannelID, lineChannelSecret, []string{"Uxxx", "Uyyy"})
	require.NoError(t, resErr)
	require.Equal(t, lineinternal.DecryptUIDsResponse{
		MIDs: map[string]string{
			"Uxxx": "xxx",
			"Uyyy": "yyy",
		},
	}, resp)
}

func TestDecryptUIDs_Failed(t *testing.T) {
	ctx := context.Background()
	httpClient := &http.Client{}
	httpmock.ActivateNonDefault(httpClient)
	defer httpmock.DeactivateAndReset()

	deps, finish := provideTestDependency(t)
	defer finish()

	deps.mockFeatureFlag.EXPECT().
		IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEInternalOAuthTokenEnabled.Name).
		Return(false)

	lineChannelID := "1234567890"
	lineChannelSecret := "abcdefghijklmnopqrstuvwxyz012345"

	httpmock.RegisterResponder(http.MethodGet, "https://internal-api.line-apps-beta-test.com/v1/internal/mid/decrypt/bulk?userIds=Uxxx&userIds=Uyyy",
		func(req *http.Request) (*http.Response, error) {
			assert.Nil(t, req.Body)

			require.Equal(t, "1234567890", req.Header.Get("X-Line-ChannelID"))
			require.Equal(t, "abcdefghijklmnopqrstuvwxyz012345", req.Header.Get("X-Line-ChannelSecret"))

			userIds := req.URL.Query()["userIds"]
			require.Contains(t, userIds, "Uxxx")
			require.Contains(t, userIds, "Uyyy")
			return httpmock.NewStringResponse(http.StatusBadRequest, `{"statusCode":"400","statusMessage":"Invalid mid"}`), nil
		})

	lineHttpClient := httpclient.LINEHTTPClient{
		Client: httpClient,
	}
	svc := lineinternal.ProvideLINEInternalClient(&lineHttpClient, lineinternal.LINEInternalConfig{
		Host: "https://internal-api.line-apps-beta-test.com",
	}, deps.mockFeatureFlag, deps.mockLINEClient)

	resp, resErr := svc.DecryptUIDs(ctx, lineChannelID, lineChannelSecret, []string{"Uxxx", "Uyyy"})
	require.Error(t, resErr)
	require.Empty(t, resp)
	require.Equal(t, &lineinternal.DecryptUIDsErrorResponse{
		StatusCode:    "400",
		StatusMessage: "Invalid mid",
	}, resErr)
}

func TestDecryptUIDsInternal_Success(t *testing.T) {
	ctx := context.Background()
	httpClient := &http.Client{}
	httpmock.ActivateNonDefault(httpClient)
	defer httpmock.DeactivateAndReset()

	deps, finish := provideTestDependency(t)
	defer finish()

	deps.mockFeatureFlag.EXPECT().
		IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEInternalOAuthTokenEnabled.Name).
		Return(false)

	httpmock.RegisterResponder(http.MethodGet, "https://internal-api.line-apps-beta-test.com/v1/internal/mid/decrypt/bulk?userIds=Uxxx",
		func(req *http.Request) (*http.Response, error) {
			assert.Nil(t, req.Body)

			require.Equal(t, "1234567890", req.Header.Get("X-Line-ChannelID"))
			require.Equal(t, "abcdefghijklmnopqrstuvwxyz012345", req.Header.Get("X-Line-ChannelSecret"))

			userIds := req.URL.Query()["userIds"]
			require.Contains(t, userIds, "Uxxx")
			return httpmock.NewStringResponse(http.StatusOK, `{"mids":{"Uxxx":"xxx"}}`), nil
		})

	lineHttpClient := httpclient.LINEHTTPClient{
		Client: httpClient,
	}
	svc := lineinternal.ProvideLINEInternalClient(&lineHttpClient, lineinternal.LINEInternalConfig{
		Host:                        "https://internal-api.line-apps-beta-test.com",
		DecryptionLINEChannelID:     "1234567890",
		DecryptionLINEChannelSecret: "abcdefghijklmnopqrstuvwxyz012345",
	}, deps.mockFeatureFlag, deps.mockLINEClient)

	resp, resErr := svc.DecryptUIDsInternal(ctx, "Uxxx")
	require.NoError(t, resErr)
	require.Equal(t, lineinternal.DecryptUIDsResponse{
		MIDs: map[string]string{
			"Uxxx": "xxx",
		},
	}, resp)
}

func TestDecryptUIDs_SuccessWithToken(t *testing.T) {
	ctx := context.Background()
	httpClient := &http.Client{}
	httpmock.ActivateNonDefault(httpClient)
	defer httpmock.DeactivateAndReset()

	deps, finish := provideTestDependency(t)
	defer finish()

	deps.mockFeatureFlag.EXPECT().
		IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEInternalOAuthTokenEnabled.Name).
		Return(true)
	deps.mockLINEClient.EXPECT().
		GetAccessToken(ctx, "1234567890", "abcdefghijklmnopqrstuvwxyz012345").
		Return("temp_access_token", nil)

	lineChannelID := "1234567890"
	lineChannelSecret := "abcdefghijklmnopqrstuvwxyz012345"

	httpmock.RegisterResponder(http.MethodGet, "https://internal-api.line-apps-beta-test.com/v1/internal/mid/decrypt/bulk?userIds=Uxxx&userIds=Uyyy",
		func(req *http.Request) (*http.Response, error) {
			assert.Nil(t, req.Body)

			require.Equal(t, "1234567890", req.Header.Get("X-Line-ChannelID"))
			require.Equal(t, "abcdefghijklmnopqrstuvwxyz012345", req.Header.Get("X-Line-ChannelSecret"))
			require.Equal(t, "Bearer temp_access_token", req.Header.Get("Authorization"))

			userIds := req.URL.Query()["userIds"]
			require.Contains(t, userIds, "Uxxx")
			require.Contains(t, userIds, "Uyyy")
			return httpmock.NewStringResponse(http.StatusOK, `{"mids":{"Uxxx":"xxx","Uyyy":"yyy"}}`), nil
		})

	lineHttpClient := httpclient.LINEHTTPClient{
		Client: httpClient,
	}
	svc := lineinternal.ProvideLINEInternalClient(&lineHttpClient, lineinternal.LINEInternalConfig{
		Host: "https://internal-api.line-apps-beta-test.com",
	}, deps.mockFeatureFlag, deps.mockLINEClient)

	resp, resErr := svc.DecryptUIDs(ctx, lineChannelID, lineChannelSecret, []string{"Uxxx", "Uyyy"})
	require.NoError(t, resErr)
	require.Equal(t, lineinternal.DecryptUIDsResponse{
		MIDs: map[string]string{
			"Uxxx": "xxx",
			"Uyyy": "yyy",
		},
	}, resp)
}

type testDependency struct {
	mockFeatureFlag *mock_featureflag.MockService
	mockLINEClient  *mock_lineinternal.MockLINEClient
}

func provideTestDependency(t *testing.T) (testDependency, func()) {
	goMockCtrl := gomock.NewController(t)

	deps := testDependency{
		mockFeatureFlag: mock_featureflag.NewMockService(goMockCtrl),
		mockLINEClient:  mock_lineinternal.NewMockLINEClient(goMockCtrl),
	}

	return deps, func() {
		goMockCtrl.Finish()
	}
}
