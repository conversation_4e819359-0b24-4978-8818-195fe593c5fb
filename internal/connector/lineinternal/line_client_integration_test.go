//go:build integration_test
// +build integration_test

package lineinternal_test

import (
	"context"
	"net/http"
	"testing"
	"time"

	"github.com/jarcoal/httpmock"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
)

func TestLINEClient_GetAccessToken(goTest *testing.T) {
	goTest.Setenv("LINE_ENV", "beta")

	goTest.Run("successfully get token and save to redis", func(tt *testing.T) {
		ctn := ittest.NewContainer(tt)

		ctx := context.TODO()
		fakeAPIHandler := func(req *http.Request) (*http.Response, error) {
			assert.Equal(tt, "client_credentials", req.FormValue("grant_type"))
			assert.Equal(tt, "mocked_channel_id", req.FormValue("client_id"))
			assert.Equal(tt, "mocked_channel_secret", req.<PERSON>al<PERSON>("client_secret"))
			return httpmock.NewStringResponse(http.StatusOK, `{"token_type":"Bearer","access_token":"response_access_token","expires_in":900}`), nil
		}
		httpmock.RegisterResponder(http.MethodPost, "https://api.line-beta.me/oauth2/v3/token", fakeAPIHandler)

		respToken, err := ctn.LINEClient.GetAccessToken(ctx, "mocked_channel_id", "mocked_channel_secret")
		assert.NoError(tt, err)
		assert.Equal(tt, "response_access_token", respToken)

		res, err := ctn.RedisConn.Client().Get(ctx, cache.LINEAccessTokenKey()).Result()
		assert.NoError(tt, err)
		assert.Equal(tt, "response_access_token", res)
	})

	goTest.Run("get token from redis instead of call LINE api", func(tt *testing.T) {
		ctx := context.TODO()
		ctn := ittest.NewContainer(tt)

		err := ctn.RedisConn.Client().Set(ctx, cache.LINEAccessTokenKey(), "saved_access_token", time.Second*time.Duration(10)).Err()
		assert.NoError(tt, err)

		respToken, err := ctn.LINEClient.GetAccessToken(ctx, "mocked_channel_id", "mocked_channel_secret")
		assert.NoError(tt, err)
		assert.Equal(tt, "saved_access_token", respToken)

		res, err := ctn.RedisConn.Client().Get(ctx, cache.LINEAccessTokenKey()).Result()
		assert.NoError(tt, err)
		assert.Equal(tt, "saved_access_token", res)
	})

	goTest.Run("got error after call LINE api, Invalid client_credentials", func(tt *testing.T) {
		ctn := ittest.NewContainer(tt)

		ctx := context.TODO()
		fakeAPIHandler := func(req *http.Request) (*http.Response, error) {
			assert.Equal(tt, "client_credentials", req.FormValue("grant_type"))
			assert.Equal(tt, "mocked_channel_id", req.FormValue("client_id"))
			assert.Equal(tt, "mocked_channel_secret", req.FormValue("client_secret"))
			return httpmock.NewStringResponse(http.StatusBadRequest, `{"error":"invalid_request","error_description":"Invalid 'client_credentials'."}`), nil
		}
		httpmock.RegisterResponder(http.MethodPost, "https://api.line-beta.me/oauth2/v3/token", fakeAPIHandler)

		respToken, err := ctn.LINEClient.GetAccessToken(ctx, "mocked_channel_id", "mocked_channel_secret")
		assert.ErrorContains(tt, err, "invalid_request: Invalid 'client_credentials'")
		assert.Equal(tt, "", respToken)

		res, err := ctn.RedisConn.Client().Get(ctx, cache.LINEAccessTokenKey()).Result()
		assert.ErrorContains(tt, err, "redis: nil")
		assert.Equal(tt, "", res)
	})
}
