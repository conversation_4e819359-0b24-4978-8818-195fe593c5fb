package lineinternal

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"regexp"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
)

const (
	encryptMIDsPath = "/v1/internal/mid/encrypt/bulk"
	decryptMIDsPath = "/v1/internal/mid/decrypt/bulk"
)

// Error is error from LINE api.
type Error struct {
	Err              string `json:"error" validate:"required"`
	ErrorDescription string `json:"error_description" validate:"required"`
}

func (err Error) Error() string {
	return fmt.Sprintf("%s: %s", err.Err, err.ErrorDescription)
}

// Client for line social api.
type client struct {
	config           LINEInternalConfig
	httpClient       *http.Client
	featureFlag      featureflag.Service
	linePublicClient LINEClient
}

//go:generate mockgen -source=./line_internal.go -destination=./mock_lineinternal/mock_line_internal.go -package=mock_lineinternal
type Client interface {
	EncryptMIDs(ctx context.Context, channelID string, channelSecret string, lineMIDs ...string) (*EncryptMIDsResponse, error)
	EncryptMIDsInternal(ctx context.Context, lineMIDs ...string) (*EncryptMIDsResponse, error)
	DecryptUIDs(ctx context.Context, channelID string, channelSecret string, lineUIDs []string) (DecryptUIDsResponse, error)
	DecryptUIDsInternal(ctx context.Context, lineUID string) (DecryptUIDsResponse, error)
}

// ProvideLINEInternalClient constructs *Client.
func ProvideLINEInternalClient(
	httpClient *httpclient.LINEHTTPClient,
	config LINEInternalConfig,
	featureFlag featureflag.Service,
	linePublicClient LINEClient,
) Client {
	return &client{
		config:           config,
		httpClient:       httpClient.Client,
		featureFlag:      featureFlag,
		linePublicClient: linePublicClient,
	}
}

func (c *client) buildEndpoint(path string) (*url.URL, error) {
	u, err := url.Parse(c.config.Host)
	if err != nil {
		return nil, err
	}
	u.Path = path
	return u, nil
}

// Verify access token.
func (c *client) EncryptMIDs(ctx context.Context, channelID string, channelSecret string, lineMIDs ...string) (*EncryptMIDsResponse, error) {
	u, err := c.buildEndpoint(encryptMIDsPath)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "EncryptMIDs").
			Msgf("unable to build endpoint [%s] with path [%s]", c.config.Host, encryptMIDsPath)
		return nil, err
	}

	queryURL := u.Query()
	queryURL["mids"] = lineMIDs
	u.RawQuery = queryURL.Encode()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "EncryptMIDs").
			Msgf("unable to create a request object for URL [%s]", u.String())
		return nil, err
	}

	req = c.addHeader(ctx, req, channelID, channelSecret)

	res, err := c.httpClient.Do(req)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "EncryptMIDs").
			Msgf("unable to send a request to [%s]", u.String())
		return nil, err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		var respErr EncryptMIDsErrorResponse
		if err := safe.DecodeJSON(res.Body, &respErr); err != nil {
			logx.Error().Err(err).
				Str("method", "EncryptMIDs").
				Msgf("unable to decode JSON for an error response")
			return nil, &api.Error{
				Code:    api.ERRCODE_INVALID_REQUEST,
				Message: "unable to convert an error response into EncryptMIDsErrorResponse struct",
			}
		}

		logx.Error().
			Str("method", "EncryptMIDs").
			Msgf("Status Code: [%s] Message: [%s]", respErr.StatusCode, respErr.StatusMessage)
		return nil, &respErr
	}

	var resp EncryptMIDsResponse
	if err := safe.DecodeJSON(res.Body, &resp); err != nil {
		logx.Error().Err(err).
			Str("method", "EncryptMIDs").
			Msgf("unable to decode JSON for a response")
		return nil, &api.Error{
			Code:    api.ERRCODE_INVALID_REQUEST,
			Message: "unable to convert a response into EncryptMIDsResponse struct",
		}
	}
	return &resp, nil
}

// Verify access token.
func (c *client) EncryptMIDsInternal(ctx context.Context, lineMIDs ...string) (*EncryptMIDsResponse, error) {
	return c.EncryptMIDs(ctx, c.config.LINEChannelID, c.config.LINEChannelSecret, lineMIDs...)
}

func (c *client) DecryptUIDs(ctx context.Context, channelID string, channelSecret string, lineUIDs []string) (DecryptUIDsResponse, error) {
	u, err := c.buildEndpoint(decryptMIDsPath)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "DecryptMIDs").
			Msgf("unable to build endpoint [%s] with path [%s]", c.config.Host, encryptMIDsPath)
		return DecryptUIDsResponse{}, err
	}

	queryURL := u.Query()
	queryURL["userIds"] = lineUIDs
	u.RawQuery = queryURL.Encode()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, u.String(), nil)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "DecryptMIDs").
			Msgf("unable to create a request object for URL [%s]", u.String())
		return DecryptUIDsResponse{}, err
	}

	req = c.addHeader(ctx, req, channelID, channelSecret)

	res, err := c.httpClient.Do(req)
	if err != nil {
		logx.Error().Err(err).
			Str("method", "DecryptMIDs").
			Msgf("unable to send a request to [%s]", u.String())
		return DecryptUIDsResponse{}, err
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		var respErr DecryptUIDsErrorResponse
		if err := safe.DecodeJSON(res.Body, &respErr); err != nil {
			logx.Error().Err(err).
				Str("method", "DecryptMIDs").
				Msgf("unable to decode JSON for an error response")
			return DecryptUIDsResponse{}, &api.Error{
				Code:    api.ERRCODE_INVALID_REQUEST,
				Message: "unable to convert an error response into DecryptUIDsErrorResponse struct",
			}
		}

		logx.Error().
			Str("method", "DecryptMIDs").
			Msgf("Status Code: [%s] Message: [%s]", respErr.StatusCode, respErr.StatusMessage)
		return DecryptUIDsResponse{}, &respErr
	}

	var resp DecryptUIDsResponse
	if err := safe.DecodeJSON(res.Body, &resp); err != nil {
		logx.Error().Err(err).
			Str("method", "DecryptMIDs").
			Msgf("unable to decode JSON for a response")
		return DecryptUIDsResponse{}, &api.Error{
			Code:    api.ERRCODE_INVALID_REQUEST,
			Message: "unable to convert a response into EncryptMIDsResponse struct",
		}
	}
	return resp, nil
}

func (c *client) DecryptUIDsInternal(ctx context.Context, lineUID string) (DecryptUIDsResponse, error) {
	return c.DecryptUIDs(ctx, c.config.DecryptionLINEChannelID, c.config.DecryptionLINEChannelSecret, []string{lineUID})
}

var (
	regexCheckLINEMIDFormat *regexp.Regexp
	regexCheckLINEUIDFormat *regexp.Regexp
)

func IsLINEUIDFormat(src string) bool {
	if len(src) == 0 {
		return false
	}

	if regexCheckLINEUIDFormat == nil {
		regexCheckLINEUIDFormat = regexp.MustCompile("U[0-9a-f]{32}")
	}

	return regexCheckLINEUIDFormat.Match([]byte(src))
}

func IsLINEMIDFormat(src string) bool {
	if len(src) == 0 {
		return false
	}

	if regexCheckLINEMIDFormat == nil {
		regexCheckLINEMIDFormat = regexp.MustCompile("u[0-9a-f]{32}")
	}

	return regexCheckLINEMIDFormat.Match([]byte(src))
}

func (c *client) addHeader(ctx context.Context, srcReq *http.Request, channelID, channelSecret string) *http.Request {
	srcReq.Header.Add("X-Line-ChannelID", channelID)
	srcReq.Header.Add("X-Line-ChannelSecret", channelSecret)

	if c.featureFlag.IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEInternalOAuthTokenEnabled.Name) {
		token, err := c.linePublicClient.GetAccessToken(ctx, channelID, channelSecret)
		if err != nil {
			logx.Error().Context(ctx).Err(err).
				Str(logutil.Module, "LINEInternalClient").
				Str(logutil.Method, "addHeader").
				Msg("unable to get LINE access token")
			return srcReq
		}

		if token != "" {
			logx.Info().Context(ctx).
				Str(logutil.Module, "LINEInternalClient").
				Str(logutil.Method, "addHeader").
				Msgf("attach LINE token with length (%d) to a request header", len(token))
			srcReq.Header.Add("Authorization", fmt.Sprintf("Bearer %s", token))
		}
	}

	return srcReq
}
