package heatmapdemand_test

import (
	"net/http"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/heatmapdemand"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
)

const Endpoint = "https://lm-dtp-staging.line-apps.com/fleet/demand"

func TestHttp_GetHeatMapDemand(t *testing.T) {

	t.Run("should return heatmap demand condition", func(tt *testing.T) {
		vrc, finish := httpclient.NewVCRRoundTripper("./testdata/200")

		defer finish()

		hmd, done := newHttpHeatMapDemand(tt, vrc)
		defer done()

		c, err := hmd.GetHeatmapCondition("12:37", "13:07")

		require.NoError(tt, err)
		require.Len(tt, c.Conditions, 2)
	})
}

func newHttpHeatMapDemand(r gomock.TestReporter, vrc http.RoundTripper) (heatmapdemand.HeatMapDemand, func()) {
	ctrl := gomock.NewController(r)

	hmd := heatmapdemand.NewHeatMapDemandClient(heatmapdemand.Config{Endpoint: Endpoint}, httpclient.NewWithRoundTripper(vrc))

	return hmd, func() { ctrl.Finish() }
}
