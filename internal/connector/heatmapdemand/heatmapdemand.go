package heatmapdemand

//go:generate mockgen -source=heatmapdemand.go -destination=./mock_heatmapdemand/mock_heatmapdemand.go -package=mock_heatmapdemand

import (
	"context"
	"fmt"
	"net/http"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

type HeatMapDemand interface {
	GetHeatmapCondition(start string, end string) (dh model.Condition, err error)
}

type Client struct {
	endpoint string
	http     *httpclient.Client
}

func (c *Client) GetHeatmapCondition(start string, end string) (dh model.Condition, err error) {
	ctx := context.Background()

	url := fmt.Sprintf("%s?start=%s&end=%s", c.endpoint, start, end)
	res, err := c.http.Get(ctx, url, make(http.Header))
	if err != nil {
		return dh, err
	}

	defer res.Body.Close()

	var cond model.Condition

	if err := safe.DecodeJSON(res.Body, &cond); err != nil {
		return cond, err
	}

	return cond, nil
}

func NewHeatMapDemandClient(cfg Config, http *httpclient.Client) *Client {
	return &Client{
		endpoint: cfg.Endpoint,
		http:     http,
	}
}
