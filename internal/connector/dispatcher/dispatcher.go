package dispatcher

//go:generate mockgen -source=dispatcher.go -destination=./mock_dispatcher/mock_dispatcher.go -package=mock_dispatcher

import (
	"context"
	"fmt"
	"net/http"

	"github.com/avast/retry-go"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

type Dispatcher interface {
	DistributeV2(ctx context.Context, req DistributeOrderV2Request, options ...DispatcherOptionFunc) (DistributeOrderV2Response, error)
	DistributeOrdersInZone(ctx context.Context, req DistributeOrdersInZoneRequest, options ...DispatcherOptionFunc) (DistributeOrdersInZoneResponse, error)
	CheckIfCandidatesEnough(ctx context.Context, req CheckIfCandidatesEnoughRequest, options ...DispatcherOptionFunc) (CheckIfCandidatesEnoughResponse, error)
}

var (
	ErrConnectionRefuse            = errors.New("distribution service connection refuse")
	_                   Dispatcher = (*DriverServiceDispatcher)(nil)
)

type ApiErrResponse struct {
	Code      string         `json:"code"`
	Message   string         `json:"message"`
	Detail    string         `json:"detail,omitempty"`
	Info      map[string]any `json:"info,omitempty"`
	Timestamp int64          `json:"timestamp"`
}

type DispatcherErrorResponse struct {
	StatusCode int
	Url        string
	Data       ApiErrResponse
}

func (d *DispatcherErrorResponse) Error() string {
	return fmt.Sprintf("%s API responds with code %d with response: %#v", d.Url, d.StatusCode, d.Data)
}

func NewDispatcherErrorResponse(statusCode int, url string, data ApiErrResponse) *DispatcherErrorResponse {
	return &DispatcherErrorResponse{
		StatusCode: statusCode,
		Url:        url,
		Data:       data,
	}
}

type DriverServiceDispatcher struct {
	cfg                DispatcherConfig
	httpClient         *httpclient.Client
	featureFlagService featureflag.Service
}

type DispatcherOption struct {
	noRetry bool
}

type DispatcherOptionFunc func(*DispatcherOption)

func formatDispatcherOption(opts []DispatcherOptionFunc) DispatcherOption {
	opt := DispatcherOption{}
	for _, f := range opts {
		f(&opt)
	}
	return opt
}

func WithNoRetry() DispatcherOptionFunc {
	return func(o *DispatcherOption) {
		o.noRetry = true
	}
}

func (p *DriverServiceDispatcher) Distribute(ctx context.Context, req DistributeOrderRequest, options ...DispatcherOptionFunc) (DistributeOrderV2Response, error) {
	return p.DistributeV2(ctx, DistributeOrderV2Request{
		OrderID: req.OrderID,
	}, options...)
}

func (p *DriverServiceDispatcher) Redistribute(ctx context.Context, req RedistributeOrderRequest, options ...DispatcherOptionFunc) (DistributeOrderV2Response, error) {
	return p.DistributeV2(ctx, DistributeOrderV2Request{
		OrderID:              req.OrderID,
		RidersTriedAssigning: req.RidersTriedAssigning,
	}, options...)
}

func (p *DriverServiceDispatcher) DistributeV2(ctx context.Context, req DistributeOrderV2Request, options ...DispatcherOptionFunc) (DistributeOrderV2Response, error) {
	var res DistributeOrderV2Response
	url := p.cfg.DispatcherFleetSingleDistributeOrderV2URL
	err := p.call(ctx, url, req, &res, options...)
	return res, err
}

func (p *DriverServiceDispatcher) DistributeOrdersInZone(ctx context.Context, req DistributeOrdersInZoneRequest, options ...DispatcherOptionFunc) (DistributeOrdersInZoneResponse, error) {
	var res DistributeOrdersInZoneResponse
	err := p.call(ctx, p.cfg.DispatcherDistributeOrdersInZoneURL, req, &res, options...)
	return res, err
}

func (p *DriverServiceDispatcher) CheckIfCandidatesEnough(ctx context.Context, req CheckIfCandidatesEnoughRequest, options ...DispatcherOptionFunc) (CheckIfCandidatesEnoughResponse, error) {
	var res CheckIfCandidatesEnoughResponse
	err := p.call(ctx, p.cfg.DispatcherCheckCandidateEnoughURL, req, &res, options...)
	return res, err
}

func (p *DriverServiceDispatcher) call(ctx context.Context, url string, req interface{}, res interface{}, options ...DispatcherOptionFunc) error {
	opt := formatDispatcherOption(options)
	attempts := p.cfg.DispatcherRetryLimit
	if opt.noRetry {
		attempts = 1
	}

	reqCtx, cancelCtx := context.WithTimeout(ctx, p.cfg.DispatcherRequestTimeout)
	defer cancelCtx()

	err := retry.Do(
		func() error {
			httpRes, httpRequestErr := p.httpClient.Post(reqCtx, url, make(http.Header), httpclient.JSON(req))
			if httpRequestErr != nil {
				logrus.Errorf("call to %s failed, with error: %s", url, httpRequestErr.Error())
				return ErrConnectionRefuse
			}
			defer httpRes.Body.Close()

			if httpRes.StatusCode != http.StatusOK {
				var apiErr ApiErrResponse
				if err := safe.DecodeJSON(httpRes.Body, &apiErr); err != nil {
					return fmt.Errorf("%s API responds with code %d, but read error response is unreadable because of error: %w", url, httpRes.StatusCode, err)
				}
				return NewDispatcherErrorResponse(httpRes.StatusCode, url, apiErr)
			}
			if err := safe.DecodeJSON(httpRes.Body, &res); err != nil {
				return fmt.Errorf("can't decode response because of error: %w", err)
			}
			return nil
		},
		retry.Attempts(attempts),
		retry.Delay(p.cfg.DispatcherRetryDelay),
		retry.RetryIf(func(err error) bool {
			return errors.Cause(err) == ErrConnectionRefuse
		}),
		retry.OnRetry(func(n uint, err error) {
			logrus.Warnf("call to %s retrying round %d request body : %v", url, n, req)
		}),
		retry.LastErrorOnly(true),
	)
	if err != nil {
		if errors.Cause(err) == ErrConnectionRefuse {
			safe.SentryErrorMessage(fmt.Sprintf("order distribution has been reached limit retrying, retried %d rounds with request body: %v", p.cfg.DispatcherRetryLimit, req), safe.WithInfo("url", url))
		}
		return err
	}
	return nil
}

func ProvideDriverServiceDispatcher(
	cfg DispatcherConfig,
	httpClient *httpclient.Client,
	featureFlagService featureflag.Service,
) Dispatcher {
	return &DriverServiceDispatcher{
		cfg:                cfg,
		httpClient:         httpClient,
		featureFlagService: featureFlagService,
	}
}
