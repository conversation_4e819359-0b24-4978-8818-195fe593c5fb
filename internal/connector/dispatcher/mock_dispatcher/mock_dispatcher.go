// Code generated by MockGen. DO NOT EDIT.
// Source: dispatcher.go

// Package mock_dispatcher is a generated GoMock package.
package mock_dispatcher

import (
	context "context"
	reflect "reflect"

	dispatcher "git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	gomock "github.com/golang/mock/gomock"
)

// MockDispatcher is a mock of Dispatcher interface.
type MockDispatcher struct {
	ctrl     *gomock.Controller
	recorder *MockDispatcherMockRecorder
}

// MockDispatcherMockRecorder is the mock recorder for MockDispatcher.
type MockDispatcherMockRecorder struct {
	mock *MockDispatcher
}

// NewMockDispatcher creates a new mock instance.
func NewMockDispatcher(ctrl *gomock.Controller) *MockDispatcher {
	mock := &MockDispatcher{ctrl: ctrl}
	mock.recorder = &MockDispatcherMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDispatcher) EXPECT() *MockDispatcherMockRecorder {
	return m.recorder
}

// CheckIfCandidatesEnough mocks base method.
func (m *MockDispatcher) CheckIfCandidatesEnough(ctx context.Context, req dispatcher.CheckIfCandidatesEnoughRequest, options ...dispatcher.DispatcherOptionFunc) (dispatcher.CheckIfCandidatesEnoughResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckIfCandidatesEnough", varargs...)
	ret0, _ := ret[0].(dispatcher.CheckIfCandidatesEnoughResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckIfCandidatesEnough indicates an expected call of CheckIfCandidatesEnough.
func (mr *MockDispatcherMockRecorder) CheckIfCandidatesEnough(ctx, req interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckIfCandidatesEnough", reflect.TypeOf((*MockDispatcher)(nil).CheckIfCandidatesEnough), varargs...)
}

// DistributeOrdersInZone mocks base method.
func (m *MockDispatcher) DistributeOrdersInZone(ctx context.Context, req dispatcher.DistributeOrdersInZoneRequest, options ...dispatcher.DispatcherOptionFunc) (dispatcher.DistributeOrdersInZoneResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DistributeOrdersInZone", varargs...)
	ret0, _ := ret[0].(dispatcher.DistributeOrdersInZoneResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DistributeOrdersInZone indicates an expected call of DistributeOrdersInZone.
func (mr *MockDispatcherMockRecorder) DistributeOrdersInZone(ctx, req interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistributeOrdersInZone", reflect.TypeOf((*MockDispatcher)(nil).DistributeOrdersInZone), varargs...)
}

// DistributeV2 mocks base method.
func (m *MockDispatcher) DistributeV2(ctx context.Context, req dispatcher.DistributeOrderV2Request, options ...dispatcher.DispatcherOptionFunc) (dispatcher.DistributeOrderV2Response, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, req}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DistributeV2", varargs...)
	ret0, _ := ret[0].(dispatcher.DistributeOrderV2Response)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DistributeV2 indicates an expected call of DistributeV2.
func (mr *MockDispatcherMockRecorder) DistributeV2(ctx, req interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, req}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DistributeV2", reflect.TypeOf((*MockDispatcher)(nil).DistributeV2), varargs...)
}
