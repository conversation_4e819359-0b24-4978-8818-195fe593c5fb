package manmap

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/geo"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type StubMapService struct {
	FastestRoute             *model.MapRoute
	DistancesTable           *mapservice.Table
	StubDistanceFn           func(sources []mapservice.Location, destination mapservice.Location) (*mapservice.Table, error)
	StubDestinatonDistanceFn func(sources mapservice.Location, destination []mapservice.Location) (*mapservice.Table, error)
}

func (s *StubMapService) FindFastestRoute(ctx context.Context, location mapservice.Location, location2 mapservice.Location, _ bool, _ ...mapservice.RouteOptFn) (*model.MapRoute, []model.MapWaypoint, error) {
	s.FastestRoute = &model.MapRoute{
		Distance: geo.DistanceInKm(location.Lat, location.Lng, location2.Lat, location2.Lng) * float64(1000*types.M),
		Duration: location.Lat + location.Lng,
	}
	return s.FastestRoute, []model.MapWaypoint{{Distance: 3.0}, {Distance: 5.0}}, nil
}

func (s *StubMapService) FindDistances(ctx context.Context, sources []mapservice.Location, destination mapservice.Location) (*mapservice.Table, error) {
	if s.StubDistanceFn != nil {
		return s.StubDistanceFn(sources, destination)
	}

	s.DistancesTable = &mapservice.Table{
		Distances: make([]float64, len(sources)),
	}

	// distance = Lat * 1000 + Lng * 1000 (fake distance calculation)
	for i, loc := range sources {
		s.DistancesTable.Distances[i] = loc.Lat*1000 + loc.Lng*1000
	}

	return s.DistancesTable, nil
}

func (s *StubMapService) FindDestinationDistances(ctx context.Context, sources mapservice.Location, destination []mapservice.Location) (*mapservice.Table, error) {
	if s.StubDistanceFn != nil {
		return s.StubDestinatonDistanceFn(sources, destination)
	}

	s.DistancesTable = &mapservice.Table{
		Distances: make([]float64, len(destination)),
	}

	// distance = Lat * 1000 + Lng * 1000 (fake distance calculation)
	for i, loc := range destination {
		s.DistancesTable.Distances[i] = loc.Lat*1000 + loc.Lng*1000
	}

	return s.DistancesTable, nil
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideStubMapService() *StubMapService {
	return &StubMapService{}
}
