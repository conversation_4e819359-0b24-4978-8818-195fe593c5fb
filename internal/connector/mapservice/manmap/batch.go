package manmap

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
)

type distanceFetchFunc func(ctx context.Context, sources []mapservice.Location, dest mapservice.Location) ([]float64, error)

type distanceBatchFetch struct {
	BatchSize   int
	sources     []mapservice.Location
	destination mapservice.Location
	distances   []float64
}

func NewDistanceBatchFetch(batchSize int, sources []mapservice.Location, destination mapservice.Location) *distanceBatchFetch {
	return &distanceBatchFetch{
		BatchSize:   batchSize,
		sources:     sources,
		destination: destination,
		distances:   make([]float64, len(sources)),
	}
}

func (f *distanceBatchFetch) Fetch(ctx context.Context, fn distanceFetchFunc) ([]float64, error) {
	maxLen := len(f.sources)
	for pos := 0; pos < maxLen; pos += f.BatchSize {
		end := pos + f.BatchSize
		if end >= maxLen {
			end = maxLen
		}

		sourceBatch := f.sources[pos:end]
		result, err := fn(ctx, sourceBatch, f.destination)
		if err != nil {
			return nil, err
		}

		for i, dist := range result {
			index := pos + i
			f.distances[index] = dist
		}
	}

	return f.distances, nil
}

type destinationDistanceFetchFunc func(ctx context.Context, source mapservice.Location, destinations []mapservice.Location) ([]float64, error)

type destinationDistanceBatchFetch struct {
	BatchSize    int
	source       mapservice.Location
	destinations []mapservice.Location
	distances    []float64
}

func NewDestinationDistanceBatchFetch(batchSize int, source mapservice.Location, destinations []mapservice.Location) *destinationDistanceBatchFetch {
	return &destinationDistanceBatchFetch{
		BatchSize:    batchSize,
		source:       source,
		destinations: destinations,
		distances:    make([]float64, len(destinations)),
	}
}

func (f *destinationDistanceBatchFetch) Fetch(ctx context.Context, fn destinationDistanceFetchFunc) ([]float64, error) {
	maxLen := len(f.destinations)
	for pos := 0; pos < maxLen; pos += f.BatchSize {
		end := pos + f.BatchSize
		if end >= maxLen {
			end = maxLen
		}

		destinationBatch := f.destinations[pos:end]
		result, err := fn(ctx, f.source, destinationBatch)
		if err != nil {
			return nil, err
		}

		for i, dist := range result {
			index := pos + i
			f.distances[index] = dist
		}
	}

	return f.distances, nil
}
