package fraudadvisor

//go:generate mockgen -source=./fraudadvisor.go -destination=./mock_fraudadvisor/mock_fraudadvisor_service.go -package=mock_fraudadvisor

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	internalhttpclient "git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

func ProvideFraudAdvisorServiceImpl(cfg FraudConfig, client *internalhttpclient.FraudAdvisorHTTPClient, featureFlagService featureflag.Service) *FraudAdvisorServiceImpl {
	return NewFraudAdviserServiceImpl(client, cfg, featureFlagService)
}

type FraudAdvisorServiceImpl struct {
	config             FraudConfig
	httpClient         *internalhttpclient.FraudAdvisorHTTPClient
	featureFlagService featureflag.Service
}

// FraudAdvisorService ...
type FraudAdvisorService interface {
	// CheckCompleteOrderFraud ...
	CheckCompleteOrderFraud(ctx context.Context, driverMid, orderID, driverLINEUID string) ([]FraudRule, error)
	GetDriverFraudScore(ctx context.Context, driverId string, from, to time.Time) ([]FraudScoreResponse, error)
}

type Error struct {
	Message string `json:"message" validate:"required"`
	Err     string `json:"error" validate:"required"`
}

func (err Error) Error() string {
	return fmt.Sprintf("%s: %s", err.Err, err.Message)
}

// Result is a string of fraud type "POSITIVE", "NEGATIVE"
type Result string

const (
	ResultPositive Result = "POSITIVE"
)

// FraudRule represents rule that fraud adviser used to validate to fraud request.
type FraudRule struct {
	// HasError tell this rule has an error during it's validate or not.
	HasError bool `json:"error"`
	// Result of this rule. See type Result for available results.
	Result Result `json:"result"`
	// Name of this rule.
	Name string `json:"name"`
	// Data additional data contain in fraud response
	Data map[string]interface{} `json:"data,omitempty"`
}

func (fa *FraudAdvisorServiceImpl) CheckCompleteOrderFraud(ctx context.Context, driverMid, orderID, driverLINEUID string) ([]FraudRule, error) {
	body := &CheckCompletedOrderFraudRequest{
		DriverMID: driverMid,
		OrderID:   orderID,
	}

	if fa.featureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsLINEUserIDUsed.Name) {
		// a fraud advisor service didn't implement a feature flag, the service will checking for a `driver_line_uid` field
		body.DriverLINEUID = driverLINEUID
	}

	json, err := json.Marshal(body)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, fa.config.FraudadvisorHost+"/fleet/fraud/complete-order", bytes.NewReader(json))
	if err != nil {
		return nil, err
	}

	req.Header.Set("Content-Type", "application/json")

	resp, err := fa.httpClient.Client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		var err Error
		_ = safe.DecodeJSON(resp.Body, &err)
		return nil, fmt.Errorf("http request failed: %w", err)
	}

	var rules []FraudRule
	if err := safe.DecodeJSON(resp.Body, &rules); err != nil {
		return nil, fmt.Errorf("couldn't parse http response: %w", err)
	}

	return rules, nil
}

func (fa *FraudAdvisorServiceImpl) GetDriverFraudScore(ctx context.Context, driverId string, from, to time.Time) ([]FraudScoreResponse, error) {
	url := fmt.Sprintf("%s/fleet/fraud/drivers/%s/score?to=%s", fa.config.FraudadvisorHost, driverId, to.Format("2006-01-02T15:04:05.000Z"))
	if !from.IsZero() {
		url = fmt.Sprintf("%s&from=%s", url, from.Format("2006-01-02T15:04:05.000Z"))
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}

	resp, err := fa.httpClient.Client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		var apiErr Error
		_ = safe.DecodeJSON(resp.Body, &apiErr)
		return nil, fmt.Errorf("http request failed: %w", apiErr)
	}

	var fraudScores []FraudScoreResponse
	if err := safe.DecodeJSON(resp.Body, &fraudScores); err != nil {
		return nil, err
	}

	return fraudScores, nil
}

func NewFraudAdviserServiceImpl(http *internalhttpclient.FraudAdvisorHTTPClient, cfg FraudConfig, featureFlagService featureflag.Service) *FraudAdvisorServiceImpl {
	return &FraudAdvisorServiceImpl{
		httpClient:         http,
		config:             cfg,
		featureFlagService: featureFlagService,
	}
}
