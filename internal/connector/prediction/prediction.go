package prediction

//go:generate mockgen -source=prediction.go -destination=./mock_prediction/mock_prediction.go -package=mock_prediction

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

type Prediction interface {
	Predict(ctx context.Context, req PredictRequest, version string) (PredictResponse, error)
	Optimize(ctx context.Context, req OptimizeRequest, version string) (OptimizeResponse, error)
	BatchOptimize(ctx context.Context, req BatchOptimizeRequest, version string) (BatchOptimizeResponse, error)
	Route(ctx context.Context, req RouteRequest, version string) (RouteResponse, error)
	EstimateRoute(ctx context.Context, req EstimateRouteRequest, version string) (RouteResponse, error)
}

type HttpClient interface {
	Post(ctx context.Context, url string, header http.Head<PERSON>, payload httpclient.JSON) (resp *http.Response, err error)
}

type PredictionImpl struct {
	Cfg        PredictionConfig
	HttpClient HttpClient
}

var ErrModelUnreachable = errors.New("prediction model not found. maybe try another version")

func (p *PredictionImpl) Predict(ctx context.Context, req PredictRequest, version string) (PredictResponse, error) {
	var res PredictResponse
	err := p.callWithFallback(ctx, req, &res, "predict", "predict", version, false)
	return res, err
}

func (p *PredictionImpl) Optimize(ctx context.Context, req OptimizeRequest, version string) (OptimizeResponse, error) {
	var res OptimizeResponse
	err := p.callWithFallback(ctx, req, &res, "optimize", "optimize", version, req.IsExperimental)
	return res, err
}

func (p *PredictionImpl) BatchOptimize(ctx context.Context, req BatchOptimizeRequest, version string) (BatchOptimizeResponse, error) {
	var res BatchOptimizeResponse
	err := p.callWithFallback(ctx, req, &res, "batch-optimize", "batch-optimize", version, false)
	return res, err
}

func (p *PredictionImpl) Route(ctx context.Context, req RouteRequest, version string) (RouteResponse, error) {
	var res RouteResponse
	err := p.callWithFallback(ctx, req, &res, "route", "route", version, req.IsExperimental)
	return res, err
}

func (p *PredictionImpl) EstimateRoute(ctx context.Context, req EstimateRouteRequest, version string) (RouteResponse, error) {
	var res RouteResponse
	err := p.callWithFallback(ctx, req, &res, "route", "estimate-route", version, false)
	return res, err
}

func (p *PredictionImpl) callWithFallback(ctx context.Context, req interface{}, res interface{}, pathFirstModel string, pathLastModel string, version string, experimental bool) error {
	err := p.call(ctx, buildPath(pathFirstModel, pathLastModel, version), req, &res, experimental)
	if version == "" || version == "latest" {
		return err
	}
	logx.Error().Msgf("call dalian's %s failed with version %s and Err %s", pathLastModel, version, err)
	if err == ErrModelUnreachable {
		safe.SentryErrorMessage(fmt.Sprintf("call dalian's %s failed with version %s", pathLastModel, version))
		return p.call(ctx, buildPath(pathFirstModel, pathLastModel, ""), req, &res, experimental)
	}
	return nil
}

func buildPath(pathFirstModel string, pathLastModel string, version string) string {
	var endpoint string
	if version == "" || version == "latest" {
		endpoint = fmt.Sprintf("%s/%s", pathFirstModel, "latest")
	} else {
		endpoint = version
	}

	return fmt.Sprintf("/model/%s/api/v1/distribute/%s", endpoint, pathLastModel)
}

func (p *PredictionImpl) call(ctx context.Context, path string, req interface{}, res interface{}, experimental bool) error {
	reqCtx, cancelCtx := context.WithTimeout(ctx, p.Cfg.PredictionServiceRequestTimeout)
	defer cancelCtx()

	url := p.Cfg.PredictionServiceBaseURL + path
	header := make(http.Header)
	if experimental {
		header.Set("experimental", "true")
	}

	httpRes, err := p.HttpClient.Post(reqCtx, url, header, httpclient.JSON(req))
	if err != nil {
		return err
	}
	defer httpRes.Body.Close()

	if httpRes.StatusCode == http.StatusNotFound || httpRes.StatusCode == http.StatusServiceUnavailable {
		return ErrModelUnreachable
	}
	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, httpRes.Body)
	if err != nil {
		return fmt.Errorf("%s API responds with code %d, PredictionImpl call failed to Copy buf from response body: %w", url, httpRes.StatusCode, err)
	}
	if buf.Len() == 0 {
		logx.Warn().Msgf("[PredictionCall] %s API responds with code %d, Response body is empty.", url, httpRes.StatusCode)
	}
	if httpRes.StatusCode == http.StatusBadRequest {
		var resBody ErrorResponse
		err = json.Unmarshal(buf.Bytes(), &resBody)
		if err != nil {
			return fmt.Errorf("%s API responds with code %d, but Unmarshal error: %w", url, httpRes.StatusCode, err)
		}
		resBody.Url = url
		resBody.StatusCode = httpRes.StatusCode
		return resBody
	} else if httpRes.StatusCode != http.StatusOK {
		var resBody ErrorResponse
		err = json.Unmarshal(buf.Bytes(), &resBody)
		if err != nil {
			return fmt.Errorf("%s API responds with code %d, but Unmarshal error: %w", url, httpRes.StatusCode, err)
		}
		return fmt.Errorf("%s API responds with code %d with response: %s ", url, httpRes.StatusCode, resBody)
	}
	err = json.Unmarshal(buf.Bytes(), &res)
	if err != nil {
		return fmt.Errorf("PredictionImpl call failed to unmarshal response body: %w", err)
	}
	return nil
}

func ProvidePrediction(cfg PredictionConfig, httpClient *httpclient.DalianClient) Prediction {
	return &PredictionImpl{
		Cfg:        cfg,
		HttpClient: httpClient,
	}
}
