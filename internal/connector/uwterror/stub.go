package uwterror

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/featureplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/localcache"
)

var _ Service = &StubUWTErrorService{}

type StubUWTErrorService struct{}

func (c *StubUWTErrorService) GetAbsoluteError(ctx context.Context, region string, service model.Service, deliveryMode string, source ErrorSource) (*AbsoluteError, error) {
	if region == "BKK" {
		return &AbsoluteError{
			P85: 668 * time.Second,
			Avg: 352 * time.Second,
		}, nil
	}
	return nil, nil
}

// @@wire-set-name@@ name:"IntegrationTest"
func ProvideStubUWTErrorService(config Config, client featureplatform.GRPCFeaturePlatformClient, cache localcache.Caches) *StubUWTErrorService {
	return &StubUWTErrorService{}
}
