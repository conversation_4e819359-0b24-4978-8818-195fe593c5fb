package uwterror_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/structpb"

	mlp "git.wndv.co/go/proto/dap/mlp/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uwterror"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/featureplatform/mock_grpc"
	cache2 "git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/localcache/mock_localcache"
)

func TestGetAbsoluteError(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	region := "BKK"
	avgDuration := 20 * time.Second
	p85Duration := 40 * time.Second

	expectGetWithRefresh := func(tt *testing.T, cache *mock_localcache.MockCaches, avgFeatureName, p85FeatureName string) {
		expectedKey := cache2.UWTErrorKey(region, []string{
			avgFeatureName,
			p85FeatureName,
		})
		cache.EXPECT().GetWithRefresh(gomock.Any(), expectedKey, gomock.Any(), gomock.Any()).Return(&mlp.GetFeatureResponse{
			FeatureResults: map[string]*structpb.Value{
				avgFeatureName: structpb.NewNumberValue(avgDuration.Seconds()),
				p85FeatureName: structpb.NewNumberValue(p85Duration.Seconds()),
			},
		}, nil)
	}
	expectAbsoluteError := func(tt *testing.T, ae *uwterror.AbsoluteError) {
		require.Equal(tt, avgDuration, ae.Avg)
		require.Equal(tt, p85Duration, ae.P85)
	}

	t.Run("expect to get mart - predict correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_mart_avg_predict_uwt_absolute_error",
			"past_15_days_mart_p85_predict_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceMart, "", uwterror.ErrorSourcePredict)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})

	t.Run("expect to get mart - route correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_mart_avg_route_uwt_absolute_error",
			"past_15_days_mart_p85_route_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceMart, "", uwterror.ErrorSourceRoute)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})

	t.Run("expect to error on getting food - predict", func(tt *testing.T) {
		tt.Parallel()
		fp, _ := provideFeaturePlatformClient(tt)
		_, err := fp.GetAbsoluteError(ctx, region, model.ServiceFood, "", uwterror.ErrorSourcePredict)
		require.NotNil(tt, err)
	})

	t.Run("expect to get food - route correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_food_avg_route_uwt_absolute_error",
			"past_15_days_food_p85_route_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceFood, "", uwterror.ErrorSourceRoute)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})

	t.Run("expect to get food - rush - predict correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_food_avg_rush_predict_uwt_absolute_error",
			"past_15_days_food_p85_rush_predict_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceFood, model.OrderDeliveryModeRush, uwterror.ErrorSourcePredict)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})

	t.Run("expect to get food - normal - predict correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_food_avg_normal_predict_uwt_absolute_error",
			"past_15_days_food_p85_normal_predict_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceFood, model.OrderDeliveryModeNormal, uwterror.ErrorSourcePredict)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})

	t.Run("expect to get food - no rush - predict correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_food_avg_no_rush_predict_uwt_absolute_error",
			"past_15_days_food_p85_no_rush_predict_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceFood, model.OrderDeliveryModeNoRush, uwterror.ErrorSourcePredict)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})

	t.Run("expect to get food - rush - route correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_food_avg_rush_route_uwt_absolute_error",
			"past_15_days_food_p85_rush_route_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceFood, model.OrderDeliveryModeRush, uwterror.ErrorSourceRoute)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})

	t.Run("expect to get food - normal - route correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_food_avg_normal_route_uwt_absolute_error",
			"past_15_days_food_p85_normal_route_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceFood, model.OrderDeliveryModeNormal, uwterror.ErrorSourceRoute)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})

	t.Run("expect to get food - no rush - route correctly", func(tt *testing.T) {
		tt.Parallel()
		fp, cache := provideFeaturePlatformClient(tt)
		expectGetWithRefresh(tt, cache,
			"past_15_days_food_avg_no_rush_route_uwt_absolute_error",
			"past_15_days_food_p85_no_rush_route_uwt_absolute_error",
		)
		ae, err := fp.GetAbsoluteError(ctx, region, model.ServiceFood, model.OrderDeliveryModeNoRush, uwterror.ErrorSourceRoute)
		require.Nil(tt, err)
		expectAbsoluteError(tt, ae)
	})
}

func provideFeaturePlatformClient(t *testing.T) (uwterror.Service, *mock_localcache.MockCaches) {
	ctrl := gomock.NewController(t)
	grpcFeaturePlatformClient := mock_grpc.NewMockGRPCFeaturePlatformClient(ctrl)
	localCache := mock_localcache.NewMockCaches(ctrl)
	return uwterror.ProvideUWTErrorService(uwterror.Config{
		MartAvgPredictFeatureName: "past_15_days_mart_avg_predict_uwt_absolute_error",
		MartP85PredictFeatureName: "past_15_days_mart_p85_predict_uwt_absolute_error",

		MartAvgRouteFeatureName: "past_15_days_mart_avg_route_uwt_absolute_error",
		MartP85RouteFeatureName: "past_15_days_mart_p85_route_uwt_absolute_error",

		FoodAvgRouteFeatureName: "past_15_days_food_avg_route_uwt_absolute_error",
		FoodP85RouteFeatureName: "past_15_days_food_p85_route_uwt_absolute_error",

		FoodAvgRushPredictFeatureName:   "past_15_days_food_avg_rush_predict_uwt_absolute_error",
		FoodAvgNormalPredictFeatureName: "past_15_days_food_avg_normal_predict_uwt_absolute_error",
		FoodAvgNoRushPredictFeatureName: "past_15_days_food_avg_no_rush_predict_uwt_absolute_error",

		FoodP85RushPredictFeatureName:   "past_15_days_food_p85_rush_predict_uwt_absolute_error",
		FoodP85NormalPredictFeatureName: "past_15_days_food_p85_normal_predict_uwt_absolute_error",
		FoodP85NoRushPredictFeatureName: "past_15_days_food_p85_no_rush_predict_uwt_absolute_error",

		FoodAvgRushRouteFeatureName:   "past_15_days_food_avg_rush_route_uwt_absolute_error",
		FoodAvgNormalRouteFeatureName: "past_15_days_food_avg_normal_route_uwt_absolute_error",
		FoodAvgNoRushRouteFeatureName: "past_15_days_food_avg_no_rush_route_uwt_absolute_error",

		FoodP85RushRouteFeatureName:   "past_15_days_food_p85_rush_route_uwt_absolute_error",
		FoodP85NormalRouteFeatureName: "past_15_days_food_p85_normal_route_uwt_absolute_error",
		FoodP85NoRushRouteFeatureName: "past_15_days_food_p85_no_rush_route_uwt_absolute_error",
	}, grpcFeaturePlatformClient, localCache), localCache
}
