package polygon

//go:generate mockgen -source=polygon.go -destination=./mock_polygon/mock_polygon.go -package=mock_polygon

import "context"

type Polygon interface {
	ListRegionByService(ctx context.Context, service string) ([]string, error)
	GetRegionByLocation(ctx context.Context, location Location) (string, error)
	GetRawRegion(ctx context.Context, regionCode string) ([]byte, error)
	GetRegion(ctx context.Context, regionCode string) (GetRawRegionRes, error)
}
