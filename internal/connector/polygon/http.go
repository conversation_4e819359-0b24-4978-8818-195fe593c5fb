package polygon

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"

	"git.wndv.co/lineman/absinthe/api"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

var (
	ErrNotFound             = errors.New("not found")
	ErrNotInAvailableRegion = errors.New("not in available region")
	ErrInvalidArguments     = errors.New("invalid arguments")
	ErrPolygonService       = errors.New("polygon-service error")
)

type Config struct {
	Endpoint                string `envconfig:"POLYGON_ENDPOINT" required:"true"`
	PolygonFleetServiceName string `envconfig:"POLYGON_FLEET_SERVICE_NAME" required:"true"`
}

func ProvidePolygonConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}

func ProvideHttpPolygon(cfg Config, client *httpclient.Client) *HttpPolygon {
	return NewHttpPolygon(cfg, client)
}

type HttpPolygon struct {
	cfg  Config
	http *httpclient.Client
}

func NewHttpPolygon(cfg Config, http *httpclient.Client) *HttpPolygon {
	return &HttpPolygon{
		cfg:  cfg,
		http: http,
	}
}

func (h *HttpPolygon) url(path string) string {
	return fmt.Sprintf("%s/%s", h.cfg.Endpoint, path)
}

func (h *HttpPolygon) ListRegionByService(ctx context.Context, service string) ([]string, error) {
	url := h.url(fmt.Sprintf("v1/area/%s/regions", service))
	res, err := h.http.Get(ctx, url, nil)
	if err != nil {
		return []string{}, err
	}
	defer res.Body.Close()

	switch res.StatusCode {
	case http.StatusOK:
		var payload ListRegionRes
		if err := safe.DecodeJSON(res.Body, &payload); err != nil {
			return []string{}, err
		}
		return payload.Regions, nil
	case http.StatusNotFound:
		return []string{}, ErrNotFound
	default:
		var payload ErrorRes
		if err := safe.DecodeJSON(res.Body, &payload); err != nil {
			return []string{}, fmt.Errorf("http request failed: %w", err)
		}
		return []string{}, payload.Error()
	}
}

func (h *HttpPolygon) GetRegionByLocation(ctx context.Context, location Location) (string, error) {
	url := h.url(fmt.Sprintf("v1/area/%s/check-region", h.cfg.PolygonFleetServiceName))
	data, err := json.Marshal(location)
	if err != nil {
		return "", err
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(data))
	if err != nil {
		return "", err
	}

	res, err := h.http.Do(ctx, req)
	if err != nil {
		return "", err
	}
	defer res.Body.Close()

	switch res.StatusCode {
	case http.StatusOK:
		var payload GetRegionRes
		if err := safe.DecodeJSON(res.Body, &payload); err != nil {
			return "", err
		}
		if !payload.Available {
			return "", ErrNotInAvailableRegion
		}
		return payload.Region, nil
	default:
		var payload ErrorRes
		if err := safe.DecodeJSON(res.Body, &payload); err != nil {
			return "", fmt.Errorf("http request failed: %w", err)
		}
		return "", payload.Error()
	}
}

func (h *HttpPolygon) GetRawRegion(ctx context.Context, regionCode string) ([]byte, error) {
	if regionCode == "" {
		return nil, ErrInvalidArguments
	}

	url := h.url(fmt.Sprintf("v1/area/%s/regions/%s", h.cfg.PolygonFleetServiceName, regionCode))
	res, err := h.http.Get(ctx, url, nil)
	if err != nil {
		return nil, ErrPolygonService
	}
	defer res.Body.Close()

	if res.StatusCode != http.StatusOK {
		var errorRes ErrorRes
		err := safe.DecodeJSON(res.Body, &errorRes)
		if err == nil && errorRes.Code == api.ERRCODE_NOT_FOUND {
			return nil, ErrNotFound
		}

		return nil, ErrPolygonService
	}

	return io.ReadAll(res.Body)
}

func (h *HttpPolygon) GetRegion(ctx context.Context, regionCode string) (GetRawRegionRes, error) {
	b, err := h.GetRawRegion(ctx, regionCode)
	if err != nil {
		return GetRawRegionRes{}, fmt.Errorf("unable to get raw region: %v", err)
	}

	var res GetRawRegionRes
	err = json.Unmarshal(b, &res)
	if err != nil {
		return GetRawRegionRes{}, fmt.Errorf("unable to unmarshal: %v", err)
	}

	return res, nil
}
