package polygon

import (
	"time"

	"github.com/pkg/errors"
	"github.com/twpayne/go-geom"
)

type ErrorRes struct {
	Code    string `json:"code" validate:"required"`
	Message string `json:"message" validate:"required"`
}

func (er *ErrorRes) Error() error {
	return errors.Errorf("code: %s, message: %s", er.Code, er.Message)
}

type ListRegionRes struct {
	Regions []string `json:"regions" validate:"required"`
}

type GetRegionRes struct {
	Available bool     `json:"available"`
	Message   string   `json:"message,omitempty"`
	Region    string   `json:"region,omitempty"`
	Address   *Address `json:"address,omitempty"`
}

type Geometry struct {
	Type        string         `json:"type"`
	Coordinates [][]geom.Coord `json:"coordinates,omitempty"`
}

type GetRawRegionRes struct {
	Name        string    `json:"name,omitempty"`
	Code        string    `json:"code,omitempty"`
	Description string    `json:"description,omitempty"`
	Region      string    `json:"region,omitempty"`
	Geometry    Geometry  `json:"geometry,omitempty"`
	CreatedAt   time.Time `json:"createdAt,omitempty"`
	UpdatedAt   time.Time `json:"updatedAt,omitempty"`
}

// Address address detail response
type Address struct {
	FullAddress string    `json:"fullAddress,omitempty"`
	PlaceID     string    `json:"placeId,omitempty"`
	Location    *Location `json:"location,omitempty"`
}
