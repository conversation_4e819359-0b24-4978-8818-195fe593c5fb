package locker

//go:generate mockgen -source=./locker.go -destination=./mock_locker/mock_locker.go -package=mock_locker

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
)

var (
	defaultConfig = Config{
		Expiration: 5 * time.Minute,
	}
)

type Config struct {
	Expiration time.Duration
}

type Option func(cfg Config) Config

type options []Option

func (o options) Config(defaultCfg Config) Config {
	cfg := defaultCfg
	for _, opt := range o {
		cfg = opt(cfg)
	}

	return cfg
}

// DriverAutoAssignState returns auto-assignment state key of the given driver.
func DriverAutoAssignState(driverID string) string {
	return "autoassign:" + driverID
}

func OrderAutoAssignState(orderID string) string {
	return "autoassign_order:" + orderID
}

func AssigningOrderState(orderID string) string {
	return "assigning_order:" + orderID
}

func DriverOrderRecommendedState(driverID string) string {
	return "driver_recommendation:" + driverID
}

func CreateOrderByIdempotencyLockKey(idempotencyKey string) string {
	return fmt.Sprintf("create_order_idempotency_key:%s", idempotencyKey)
}

func GetOrderInfoByIdempotencyLockKey(idempotencyKey string) string {
	return fmt.Sprintf("order_info_idempotency_key:%s", idempotencyKey)
}

// Locker provides mechanism to lock an order.
type Locker interface {
	Lock(ctx context.Context, id string, opts ...Option) (exists bool, err error)
	Unlock(ctx context.Context, id string) error

	// SetState stores state in a form of key/value and returns true if state can be set.
	SetState(ctx context.Context, stateKey string, value string, ttl time.Duration) bool

	// GetState returns value of state.
	GetState(ctx context.Context, stateKey string) string

	// RemoveState removes state.
	RemoveState(ctx context.Context, stateKey string)
}

// RedisLocker implements Locker with redis in-memory.
type RedisLocker struct {
	redisClient datastore.RedisClient
}

// Lock implements Locker.
func (r *RedisLocker) Lock(ctx context.Context, id string, opts ...Option) (exists bool, err error) {
	cfg := options(opts).Config(defaultConfig)
	return r.redisClient.SetNX(ctx, id, 1, cfg.Expiration).Result()
}

// Unlock implements Locker.
func (r *RedisLocker) Unlock(ctx context.Context, id string) error {
	return r.redisClient.Del(ctx, id).Err()
}

func (r RedisLocker) SetState(ctx context.Context, stateKey string, value string, ttl time.Duration) bool {
	key := r.buildKey(stateKey)
	// On production, the redis is setup as single-master/multiple-slaves with shards
	// so we don't need to use redlock -- simple setnx is enough.
	success, err := r.redisClient.SetNX(ctx, key, value, ttl).Result()

	if err != nil {
		logrus.Warnf("key=%s value=%s cannot set value to redis %v", key, value, err)
		return false
	}

	return success
}

func (r RedisLocker) buildKey(stateKey string) string {
	return "atomstate:" + stateKey
}

func (r RedisLocker) GetState(ctx context.Context, stateKey string) string {
	key := r.buildKey(stateKey)
	v, err := r.redisClient.Get(ctx, key).Result()

	if err != nil {
		if err != redis.Nil {
			logrus.Errorf("key=%s cannot get value from redis %v", key, err)
		}
		return ""
	}

	return v
}

func (r RedisLocker) RemoveState(ctx context.Context, stateKey string) {
	key := r.buildKey(stateKey)
	_, err := r.redisClient.Del(ctx, key).Result()

	if err != nil {
		logrus.Warnf("key=%s cannot delete key from redis %v", key, err)
	}
}

// ProvideRedisLocker constructs *RedisLocker.
func ProvideRedisLocker(redisClient datastore.RedisClient) *RedisLocker {
	return &RedisLocker{
		redisClient: redisClient,
	}
}
