// Code generated by MockGen. DO NOT EDIT.
// Source: ./locker.go

// Package mock_locker is a generated GoMock package.
package mock_locker

import (
	context "context"
	reflect "reflect"
	time "time"

	locker "git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	gomock "github.com/golang/mock/gomock"
)

// MockLocker is a mock of Locker interface.
type MockLocker struct {
	ctrl     *gomock.Controller
	recorder *MockLockerMockRecorder
}

// MockLockerMockRecorder is the mock recorder for MockLocker.
type MockLockerMockRecorder struct {
	mock *MockLocker
}

// NewMockLocker creates a new mock instance.
func NewMockLocker(ctrl *gomock.Controller) *MockLocker {
	mock := &MockLocker{ctrl: ctrl}
	mock.recorder = &MockLockerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockLocker) EXPECT() *MockLockerMockRecorder {
	return m.recorder
}

// GetState mocks base method.
func (m *MockLocker) GetState(ctx context.Context, stateKey string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetState", ctx, stateKey)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetState indicates an expected call of GetState.
func (mr *MockLockerMockRecorder) GetState(ctx, stateKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetState", reflect.TypeOf((*MockLocker)(nil).GetState), ctx, stateKey)
}

// Lock mocks base method.
func (m *MockLocker) Lock(ctx context.Context, id string, opts ...locker.Option) (bool, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Lock", varargs...)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Lock indicates an expected call of Lock.
func (mr *MockLockerMockRecorder) Lock(ctx, id interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Lock", reflect.TypeOf((*MockLocker)(nil).Lock), varargs...)
}

// RemoveState mocks base method.
func (m *MockLocker) RemoveState(ctx context.Context, stateKey string) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "RemoveState", ctx, stateKey)
}

// RemoveState indicates an expected call of RemoveState.
func (mr *MockLockerMockRecorder) RemoveState(ctx, stateKey interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveState", reflect.TypeOf((*MockLocker)(nil).RemoveState), ctx, stateKey)
}

// SetState mocks base method.
func (m *MockLocker) SetState(ctx context.Context, stateKey, value string, ttl time.Duration) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetState", ctx, stateKey, value, ttl)
	ret0, _ := ret[0].(bool)
	return ret0
}

// SetState indicates an expected call of SetState.
func (mr *MockLockerMockRecorder) SetState(ctx, stateKey, value, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetState", reflect.TypeOf((*MockLocker)(nil).SetState), ctx, stateKey, value, ttl)
}

// Unlock mocks base method.
func (m *MockLocker) Unlock(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unlock", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unlock indicates an expected call of Unlock.
func (mr *MockLockerMockRecorder) Unlock(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unlock", reflect.TypeOf((*MockLocker)(nil).Unlock), ctx, id)
}
