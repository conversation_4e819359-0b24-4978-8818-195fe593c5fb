// Code generated by MockGen. DO NOT EDIT.
// Source: file.go

// Package mock_file is a generated GoMock package.
package mock_file

import (
	context "context"
	io "io"
	reflect "reflect"
	time "time"

	file "git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	s3 "github.com/aws/aws-sdk-go/service/s3"
	gomock "github.com/golang/mock/gomock"
)

// MockFileConnector is a mock of FileConnector interface.
type MockFileConnector struct {
	ctrl     *gomock.Controller
	recorder *MockFileConnectorMockRecorder
}

// MockFileConnectorMockRecorder is the mock recorder for MockFileConnector.
type MockFileConnectorMockRecorder struct {
	mock *MockFileConnector
}

// NewMockFileConnector creates a new mock instance.
func NewMockFileConnector(ctrl *gomock.Controller) *MockFileConnector {
	mock := &MockFileConnector{ctrl: ctrl}
	mock.recorder = &MockFileConnectorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockFileConnector) EXPECT() *MockFileConnectorMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockFileConnector) Get(arg0 context.Context, key string, expiration time.Duration) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, key, expiration)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockFileConnectorMockRecorder) Get(arg0, key, expiration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockFileConnector)(nil).Get), arg0, key, expiration)
}

// GetConfigBucket mocks base method.
func (m *MockFileConnector) GetConfigBucket(ctx context.Context) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigBucket", ctx)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigBucket indicates an expected call of GetConfigBucket.
func (mr *MockFileConnectorMockRecorder) GetConfigBucket(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigBucket", reflect.TypeOf((*MockFileConnector)(nil).GetConfigBucket), ctx)
}

// GetObject mocks base method.
func (m *MockFileConnector) GetObject(arg0 context.Context, key string) (*s3.GetObjectOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObject", arg0, key)
	ret0, _ := ret[0].(*s3.GetObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *MockFileConnectorMockRecorder) GetObject(arg0, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*MockFileConnector)(nil).GetObject), arg0, key)
}

// HeadObject mocks base method.
func (m *MockFileConnector) HeadObject(arg0 context.Context, key string) (*s3.HeadObjectOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HeadObject", arg0, key)
	ret0, _ := ret[0].(*s3.HeadObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HeadObject indicates an expected call of HeadObject.
func (mr *MockFileConnectorMockRecorder) HeadObject(arg0, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HeadObject", reflect.TypeOf((*MockFileConnector)(nil).HeadObject), arg0, key)
}

// ListObjects mocks base method.
func (m *MockFileConnector) ListObjects(arg0 context.Context, prefix string, opts ...file.SaveOption) (*s3.ListObjectsOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, prefix}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListObjects", varargs...)
	ret0, _ := ret[0].(*s3.ListObjectsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListObjects indicates an expected call of ListObjects.
func (mr *MockFileConnectorMockRecorder) ListObjects(arg0, prefix interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, prefix}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjects", reflect.TypeOf((*MockFileConnector)(nil).ListObjects), varargs...)
}

// Save mocks base method.
func (m *MockFileConnector) Save(ctx context.Context, key string, reader io.Reader, opts ...file.SaveOption) (*file.File, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, key, reader}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Save", varargs...)
	ret0, _ := ret[0].(*file.File)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Save indicates an expected call of Save.
func (mr *MockFileConnectorMockRecorder) Save(ctx, key, reader interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, key, reader}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Save", reflect.TypeOf((*MockFileConnector)(nil).Save), varargs...)
}
