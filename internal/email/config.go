package email

import (
	"github.com/kelseyhightower/envconfig"
)

// Config is configuration for email.
type Config struct {
	// EmailServer, email server.
	SMTPHost       string `envconfig:"EMAIL_SMTP_HOST"`
	SMTPPort       string `envconfig:"EMAIL_SMTP_PORT"`
	SMTPRequireTLS bool   `envconfig:"EMAIL_SMTP_REQUIRE_TLS" default:"false"`
}

// ProvideEmailConfig bind email configuration from environment variables.
func ProvideEmailConfig() (cfg Config) {
	envconfig.MustProcess("", &cfg)
	return
}
