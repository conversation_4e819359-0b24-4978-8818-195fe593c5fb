// Code generated by MockGen. DO NOT EDIT.
// Source: ../email.go

// Package mock_email is a generated GoMock package.
package mock_email

import (
	reflect "reflect"

	email "git.wndv.co/lineman/fleet-distribution/internal/email"
	gomock "github.com/golang/mock/gomock"
)

// MockEmailService is a mock of EmailService interface.
type MockEmailService struct {
	ctrl     *gomock.Controller
	recorder *MockEmailServiceMockRecorder
}

// MockEmailServiceMockRecorder is the mock recorder for MockEmailService.
type MockEmailServiceMockRecorder struct {
	mock *MockEmailService
}

// NewMockEmailService creates a new mock instance.
func NewMockEmailService(ctrl *gomock.Controller) *MockEmailService {
	mock := &MockEmailService{ctrl: ctrl}
	mock.recorder = &MockEmailServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEmailService) EXPECT() *MockEmailServiceMockRecorder {
	return m.recorder
}

// Send mocks base method.
func (m *MockEmailService) Send(subject, body string, tos []string, from, password string, files []email.File) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Send", subject, body, tos, from, password, files)
	ret0, _ := ret[0].(error)
	return ret0
}

// Send indicates an expected call of Send.
func (mr *MockEmailServiceMockRecorder) Send(subject, body, tos, from, password, files interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Send", reflect.TypeOf((*MockEmailService)(nil).Send), subject, body, tos, from, password, files)
}
