// Code generated by MockGen. DO NOT EDIT.
// Source: ./new_tmpl.go

// Package mock_tmpl is a generated GoMock package.
package mock_tmpl

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockTemplate is a mock of Template interface.
type MockTemplate struct {
	ctrl     *gomock.Controller
	recorder *MockTemplateMockRecorder
}

// MockTemplateMockRecorder is the mock recorder for MockTemplate.
type MockTemplateMockRecorder struct {
	mock *MockTemplate
}

// NewMockTemplate creates a new mock instance.
func NewMockTemplate(ctrl *gomock.Controller) *MockTemplate {
	mock := &MockTemplate{ctrl: ctrl}
	mock.recorder = &MockTemplateMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTemplate) EXPECT() *MockTemplateMockRecorder {
	return m.recorder
}

// Execute mocks base method.
func (m *MockTemplate) Execute(name string, payload interface{}) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Execute", name, payload)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Execute indicates an expected call of Execute.
func (mr *MockTemplateMockRecorder) Execute(name, payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Execute", reflect.TypeOf((*MockTemplate)(nil).Execute), name, payload)
}
