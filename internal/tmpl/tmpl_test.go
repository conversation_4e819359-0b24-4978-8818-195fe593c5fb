package tmpl

import (
	"embed"
	"testing"
	"text/template"

	"github.com/stretchr/testify/assert"
)

//go:embed testdata/*.tmpl
var content embed.FS

func init() {
	var err error
	root, err = template.ParseFS(content, "testdata/*.tmpl")
	if err != nil {
		panic(err)
	}
}

func TestExecute(t *testing.T) {
	type args struct {
		fileName string
		payload  interface{}
	}

	testCases := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "able to retrieve file",
			args: args{
				fileName: "test.tmpl",
				payload:  nil,
			},
			want:    "",
			wantErr: false,
		},
		{
			name: "should error if not able to retrieve file",
			args: args{
				fileName: "not_exists.tmpl",
				payload:  nil,
			},
			want:    "",
			wantErr: true,
		},
		{
			name: "able to replace payload",
			args: args{
				fileName: "test_payload.tmpl",
				payload: struct {
					TotalRecords int
				}{
					TotalRecords: 5,
				},
			},
			want:    "5\n",
			wantErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			s, err := Execute(tc.args.fileName, tc.args.payload)
			assert.Equal(t, tc.want, s)
			assert.Equal(t, tc.wantErr, err != nil)
		})
	}
}
