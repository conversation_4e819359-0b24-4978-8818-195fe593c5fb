package asynqclient

import (
	"github.com/hibiken/asynq"

	internalconfig "git.wndv.co/lineman/fleet-distribution/internal/config"
)

//go:generate mockgen -source=./asynqclient.go -destination=./mock_asynqclient/mock_asynqclient.go -package=mock_asynq_client

// AsynqClient
type AsynqClient interface {
	Enqueue(task *asynq.Task, opts ...asynq.Option) (*asynq.TaskInfo, error)
}

func ProvideAsynqClient(redisCfg internalconfig.RedisConfig) *asynq.Client {
	client := asynq.NewClient(asynq.RedisClusterClientOpt{
		Addrs:    redisCfg.Host,
		Password: redisCfg.Password,
	})
	return client
}
