package repository

//go:generate mockgen -source=./withdrawal_transaction_results_repository.go -destination=./mock_repository/withdrawal_transaction_results_repository.go -package=mock_repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type WithdrawTransactionResultQuery interface {
	WithStart(start time.Time) WithdrawTransactionResultQuery
	WithEnd(end time.Time) WithdrawTransactionResultQuery
}

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type WithdrawalTransactionResultsRepository interface {
	CreateAll(ctx context.Context, transaction []model.WithdrawalTransactionResult) error
	List(ctx context.Context, query interface{}, opts ...Option) ([]model.WithdrawalTransactionResult, error)
}
