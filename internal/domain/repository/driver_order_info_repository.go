package repository

//go:generate mockgen -source=./driver_order_info_repository.go -destination=./mock_repository/mock_driver_order_info_repository.go -package=mock_repository

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/mongo"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type DriverOrderInfoQuery interface {
	WithDriverID(driverID string) DriverQuery
}

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type DriverOrderInfoRepository interface {
	FindDriverID(ctx context.Context, driverID string, opts ...Option) (*model.DriverOrderInfo, error)
	IsExists(ctx context.Context, driverID string, opts ...Option) bool
	GetOrCreate(_ context.Context, driverID string) (*model.DriverOrderInfo, error)
	GetDailyCounts(_ context.Context, driverID string, startDate time.Time, endDate time.Time) (*model.DriverOrderInfo, error)
	GetDailyCountsMultipleDrivers(_ context.Context, driverIDs []string, startDate time.Time, endDate time.Time, opts ...Option) ([]model.DriverOrderInfo, error)
	Update(_ context.Context, m model.DriverOrderInfo) error
	UpdateManyCounts(ctx context.Context, payload []model.OrderUpdatePayload) (*mongo.BulkWriteResult, error)
	FindWhoHasAttendanceStat(ctx context.Context, skip, limit int, opts ...Option) ([]model.DriverOrderInfo, error)
	AddAttendanceStat(ctx context.Context, driverID string, atds model.AttendanceStat) error
	RemoveAttendanceStats(ctx context.Context, driverID string, dates []string) error
	FindDriverAttendanceStat(ctx context.Context, driverID string, opts ...Option) (model.DriverOrderInfo, error)
}
