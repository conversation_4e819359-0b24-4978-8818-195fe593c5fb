// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyQuestionRepository(delegate QuestionRepository, meter metric.Meter) *ProxyQuestionRepository {
	return &ProxyQuestionRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyQuestionRepository-tracer"),
	}
}

type ProxyQuestionRepository struct {
	Delegate         QuestionRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyQuestionRepository) GetQuestionAnswers(i0 context.Context) ([]model.QuestionAnswer, error) {

	_, span := p.Tracer.Start(i0, "QuestionRepository.GetQuestionAnswers")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetQuestionAnswers(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "QuestionRepository.GetQuestionAnswers")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
