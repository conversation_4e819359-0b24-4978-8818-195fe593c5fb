// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyThrottledDispatchDetailRepository(delegate ThrottledDispatchDetailRepository, meter metric.Meter) *ProxyThrottledDispatchDetailRepository {
	return &ProxyThrottledDispatchDetailRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyThrottledDispatchDetailRepository-tracer"),
	}
}

type ProxyThrottledDispatchDetailRepository struct {
	Delegate         ThrottledDispatchDetailRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyThrottledDispatchDetailRepository) Create(i0 context.Context, i1 *model.ThrottledDispatchDetail) error {

	_, span := p.Tracer.Start(i0, "ThrottledDispatchDetailRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ThrottledDispatchDetailRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyThrottledDispatchDetailRepository) FindByZoneID(i0 context.Context, i1 primitive.ObjectID, i2 ...Option) (model.ThrottledDispatchDetailWithZoneCode, error) {

	_, span := p.Tracer.Start(i0, "ThrottledDispatchDetailRepository.FindByZoneID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByZoneID(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ThrottledDispatchDetailRepository.FindByZoneID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyThrottledDispatchDetailRepository) Update(i0 context.Context, i1 *model.ThrottledDispatchDetail) error {

	_, span := p.Tracer.Start(i0, "ThrottledDispatchDetailRepository.Update")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Update(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ThrottledDispatchDetailRepository.Update")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyThrottledDispatchDetailRepository) Find(i0 context.Context, i1 int, i2 int, i3 ...Option) ([]model.ThrottledDispatchDetailWithZoneCode, error) {

	_, span := p.Tracer.Start(i0, "ThrottledDispatchDetailRepository.Find")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Find(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ThrottledDispatchDetailRepository.Find")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyThrottledDispatchDetailRepository) Count(i0 context.Context, i1 ...Option) (int, error) {

	_, span := p.Tracer.Start(i0, "ThrottledDispatchDetailRepository.Count")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Count(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ThrottledDispatchDetailRepository.Count")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyThrottledDispatchDetailRepository) FindOneFromLocation(i0 context.Context, i1 model.Location, i2 model.DistributeRegions, i3 ...Option) (*model.ThrottledDispatchDetailWithZoneCode, error) {

	_, span := p.Tracer.Start(i0, "ThrottledDispatchDetailRepository.FindOneFromLocation")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindOneFromLocation(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ThrottledDispatchDetailRepository.FindOneFromLocation")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyThrottledDispatchDetailRepository) FindOneFromOrder(i0 context.Context, i1 *model.Order, i2 ...Option) (*model.ThrottledDispatchDetailWithZoneCode, error) {

	_, span := p.Tracer.Start(i0, "ThrottledDispatchDetailRepository.FindOneFromOrder")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindOneFromOrder(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ThrottledDispatchDetailRepository.FindOneFromOrder")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyThrottledDispatchDetailRepository) FindManyFromLocation(i0 context.Context, i1 model.Location, i2 model.DistributeRegions, i3 ...Option) ([]model.ThrottledDispatchDetailWithZoneCode, error) {

	_, span := p.Tracer.Start(i0, "ThrottledDispatchDetailRepository.FindManyFromLocation")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindManyFromLocation(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "ThrottledDispatchDetailRepository.FindManyFromLocation")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
