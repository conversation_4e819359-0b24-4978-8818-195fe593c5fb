package repository

import (
	"fmt"

	"go.mongodb.org/mongo-driver/bson"
)

type Query interface {
	Query() (bson.M, error)
}

func ValidateQuery(m bson.M) (bson.M, error) {
	if len(m) == 0 {
		return nil, fmt.<PERSON>rrorf("empty query")
	}
	return m, nil
}

func ValidateQueryAny(q any) (any, error) {
	switch x := q.(type) {
	case bson.M:
		return ValidateQuery(x)

	case map[string]interface{}:
		return ValidateQuery(x)

	case []interface{}:
		if len(x) == 0 {
			return nil, fmt.<PERSON><PERSON><PERSON>("empty query")
		}
	}
	return nil, fmt.<PERSON><PERSON>rf("unknown query")
}
