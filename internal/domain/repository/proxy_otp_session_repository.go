// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyOTPSessionRepo(delegate OTPSessionRepo, meter metric.Meter) *ProxyOTPSessionRepo {
	return &ProxyOTPSessionRepo{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyOTPSessionRepo-tracer"),
	}
}

type ProxyOTPSessionRepo struct {
	Delegate         OTPSessionRepo
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyOTPSessionRepo) Save(i0 context.Context, i1 model.OTPSession) error {

	_, span := p.Tracer.Start(i0, "OTPSessionRepo.Save")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Save(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OTPSessionRepo.Save")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOTPSessionRepo) Load(i0 context.Context, i1 string) (model.OTPSession, error) {

	_, span := p.Tracer.Start(i0, "OTPSessionRepo.Load")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Load(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OTPSessionRepo.Load")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOTPSessionRepo) LoadOrNew(i0 context.Context, i1 string) (model.OTPSession, error) {

	_, span := p.Tracer.Start(i0, "OTPSessionRepo.LoadOrNew")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.LoadOrNew(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OTPSessionRepo.LoadOrNew")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOTPSessionRepo) Remove(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "OTPSessionRepo.Remove")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Remove(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OTPSessionRepo.Remove")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
