package repository

//go:generate mockgen -source=./transaction_repository.go -destination=./mock_repository/mock_transaction_repository.go -package=mock_repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type FindWalletWithdrawOption struct {
	AnyWithdrawRefID bool
	Limit            int
	MongoOption      []Option
}

type FindWalletWithdrawOptionFunc func(*FindWalletWithdrawOption)

func WithAnyWithdrawRefID() FindWalletWithdrawOptionFunc {
	return func(opt *FindWalletWithdrawOption) {
		opt.AnyWithdrawRefID = true
	}
}

// WithLimit sets the limit of the query
// if limit is 0, it will return all the data
func WithLimit(size int) FindWalletWithdrawOptionFunc {
	return func(opt *FindWalletWithdrawOption) {
		opt.Limit = size
	}
}

func WithMongoOption(option Option) FindWalletWithdrawOptionFunc {
	return func(opt *FindWalletWithdrawOption) {
		opt.MongoOption = append(opt.MongoOption, option)
	}
}

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type TransactionRepository interface {
	Create(ctx context.Context, transaction *model.Transaction) error
	CreateAll(ctx context.Context, transaction []model.Transaction) error
	Find(ctx context.Context, query Query, skip int, limit int, opts ...Option) ([]model.Transaction, error)
	FindWithSort(ctx context.Context, query Query, skip int, limit int, sort []string, opts ...Option) ([]model.Transaction, error)
	FindByID(ctx context.Context, id string) (*model.Transaction, error)
	FindByIDs(ctx context.Context, ids []string) ([]model.Transaction, error)
	FindWithdrawByID(ctx context.Context, id string, from time.Time, to time.Time) ([]model.Transaction, error)
	FindWalletWithdraw(ctx context.Context, opt ...FindWalletWithdrawOptionFunc) ([]model.Transaction, error)
	FindByWithdrawRefID(ctx context.Context, refIDs []crypt.EncryptedString) ([]model.Transaction, error)
	FindByTransactionRefID(ctx context.Context, refID string) (*model.Transaction, error)
	FindByTransactionVanIDAndDate(ctx context.Context, refID string, date time.Time) (*model.Transaction, error)
	FindLatestWithdrawTransactionByID(ctx context.Context, id string) (*model.Transaction, error)
	FindItemFeeByOrderID(ctx context.Context, orderID string) (*model.Transaction, error)
	FindOnTopsByOrderID(ctx context.Context, orderID string) ([]model.Transaction, error)
	Count(ctx context.Context, query Query) (int, error)
	Update(ctx context.Context, transaction *model.Transaction) error
	UpdateAll(ctx context.Context, transaction []model.Transaction) error
	DeleteByID(ctx context.Context, id string) error
	ApproveTransactionIDs(ctx context.Context, ids []string, requestedBy string) (failCount int, err error)
	FindByOrderID(ctx context.Context, orderID string) ([]model.Transaction, error)
	FindByTripIDWithNoOrderID(ctx context.Context, tripID string) ([]model.Transaction, error)
	FindLatestWage(ctx context.Context, tripID string) ([]model.Transaction, error)
	ApprovePendingFraudTransaction(ctx context.Context, requestedBy string, start, end time.Time, opts ...Option) error
	CountWithdrawal(ctx context.Context, driverId string, start, end time.Time, opts ...Option) (int, error)
	UnsetWithdrawRefIdByWithdrawRefId(ctx context.Context, withdrawRefId string) error
	FindListActiveDriverIdsInRange(ctx context.Context, fromInclusive time.Time, toExclusive time.Time, opts ...Option) ([]string, error)
	FindMissingCITITransaction(ctx context.Context, trxIDs []string, startTime, endTime time.Time) ([]string, error)
	IterateCitiTopupTransactionsCreatedBetween(ctx context.Context, fromInclusive time.Time, toInclusive time.Time) (Cursor, error)
	CreateAllTransactionTracking(ctx context.Context, transaction []model.Transaction) error
	IsIncentivePaid(ctx context.Context, incentiveID, incentivePaymentDate, driverID string) (bool, error)
}
