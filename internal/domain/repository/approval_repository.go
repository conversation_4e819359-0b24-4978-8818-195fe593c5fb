package repository

//go:generate mockgen -source=./approval_repository.go -destination=./mock_repository/mock_approval_repository.go -package=mock_repository

import (
	"context"
	"strings"
	"time"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type ApprovalRepository interface {
	Create(ctx context.Context, approval *model.Approval) error
	Find(ctx context.Context, query ApprovalQuery, skip int, limit int) ([]model.Approval, error)
	Get(ctx context.Context, id string) (*model.Approval, error)
	FindByVoidTxnID(ctx context.Context, txnID string) ([]model.Approval, error)
	Count(ctx context.Context, query ApprovalQuery) (int, error)
	Update(ctx context.Context, approval *model.Approval) error
	FindChargeApprovalByOrderID(ctx context.Context, orderID string) ([]model.Approval, error)
}

type ApprovalQuery struct {
	VoidDriverID     string
	PurchaseDriverID string
	TransRefID       string
	Category         model.ApprovalCategory
	Action           model.ApprovalAction
	Status           model.ApprovalStatus
	From             time.Time
	To               time.Time
	IsFree           string
}

func (q ApprovalQuery) ToQuery() bson.M {
	queries := make([]bson.M, 0, 5)

	if q.VoidDriverID != "" {
		queries = append(queries, bson.M{
			"info.driver_id": q.VoidDriverID,
			"info.action": bson.M{
				"$in": []model.ApprovalAction{
					model.VoidAction,
					model.VoidPurchaseAction,
					model.VoidAddAction,
					model.VoidChargeAction,
					model.VoidReturnCreditAction,
				},
			},
		})
	}

	if q.PurchaseDriverID != "" {
		queries = append(queries, bson.M{
			"$and": []bson.M{
				{
					"$or": []bson.M{
						{"info.purchase_driver_id": q.PurchaseDriverID},
						{"info.driver_id": q.PurchaseDriverID},
					},
				},
				{
					"info.purchase_driver_id": q.PurchaseDriverID,
					"info.action": bson.M{
						"$in": []model.ApprovalAction{
							model.PurchaseAction,
							model.VoidPurchaseAction,
						},
					},
				},
			},
		})
	}

	if q.TransRefID != "" {
		queries = append(queries, bson.M{"info.trans_ref_id": crypt.EncryptedString(q.TransRefID).Encrypt()})
	}

	if q.Category != "" {
		queries = append(queries, bson.M{"info.category": q.Category})
	}

	if q.Action != "" {
		queries = append(queries, bson.M{"info.action": q.Action})
	}

	if q.Status != "" {
		queries = append(queries, bson.M{"status": q.Status})
	}

	if !q.From.IsZero() || !q.To.IsZero() {
		createAtQuery := bson.M{}
		if !q.From.IsZero() {
			createAtQuery["$gte"] = q.From
		}
		if !q.To.IsZero() {
			createAtQuery["$lte"] = q.To
		}

		queries = append(queries, bson.M{"created_at": createAtQuery})
	}

	if strings.ToLower(q.IsFree) == "y" {
		queries = append(queries, bson.M{"info.is_free": true})
	} else if strings.ToLower(q.IsFree) == "n" {
		queries = append(queries, bson.M{"info.is_free": false})
	}

	if len(queries) == 0 {
		return bson.M{}
	}

	return bson.M{"$and": queries}
}
