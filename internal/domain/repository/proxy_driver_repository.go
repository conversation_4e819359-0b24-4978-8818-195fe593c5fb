// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence/setter"
	"go.mongodb.org/mongo-driver/bson"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyDriverRepository(delegate DriverRepository, meter metric.Meter) *ProxyDriverRepository {
	return &ProxyDriverRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyDriverRepository-tracer"),
	}
}

type ProxyDriverRepository struct {
	Delegate         DriverRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyDriverRepository) Create(i0 context.Context, i1 *model.Driver) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) Find(i0 context.Context, i1 int, i2 int) ([]model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.Find")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Find(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.Find")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) FindSorted(i0 context.Context, i1 int, i2 int) ([]model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindSorted")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindSorted(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindSorted")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) FindDriverID(i0 context.Context, i1 string, i2 ...Option) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindDriverID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindDriverID(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindDriverID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) FindDriverIDs(i0 context.Context, i1 []string, i2 ...Option) ([]model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindDriverIDs")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindDriverIDs(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindDriverIDs")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) CountDriver(i0 context.Context) (int, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.CountDriver")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountDriver(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.CountDriver")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) IsExists(i0 context.Context, i1 string) bool {

	_, span := p.Tracer.Start(i0, "DriverRepository.IsExists")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.IsExists(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.IsExists")

	return r0
}

func (p *ProxyDriverRepository) FindWithQueryAndSort(i0 context.Context, i1 DriverQuery, i2 []string, i3 int, i4 int, i5 ...Option) ([]model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindWithQueryAndSort")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindWithQueryAndSort(i0, i1, i2, i3, i4, i5...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindWithQueryAndSort")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) FindWithQuerySelectorAndSort(i0 context.Context, i1 DriverQuery, i2 []string, i3 []string, i4 int, i5 int, i6 ...Option) ([]model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindWithQuerySelectorAndSort")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindWithQuerySelectorAndSort(i0, i1, i2, i3, i4, i5, i6...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindWithQuerySelectorAndSort")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) CountDriverWithQuery(i0 context.Context, i1 DriverQuery, i2 ...Option) (int, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.CountDriverWithQuery")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountDriverWithQuery(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.CountDriverWithQuery")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) Update(i0 context.Context, i1 *model.Driver) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.Update")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Update(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.Update")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateManyByTier(i0 context.Context, i1 []string, i2 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateManyByTier")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateManyByTier(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateManyByTier")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateManyByNegativeGroup(i0 context.Context, i1 []string, i2 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateManyByNegativeGroup")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateManyByNegativeGroup(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateManyByNegativeGroup")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) MultipleUpdateHaveBox(i0 context.Context, i1 []string, i2 bool) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.MultipleUpdateHaveBox")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.MultipleUpdateHaveBox(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.MultipleUpdateHaveBox")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) GetByUID(i0 context.Context, i1 string) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetByUID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetByUID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetByUID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) GetByRefreshToken(i0 context.Context, i1 string) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetByRefreshToken")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetByRefreshToken(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetByRefreshToken")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) SetRefreshToken(i0 context.Context, i1 string, i2 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetRefreshToken")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetRefreshToken(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetRefreshToken")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) CurrentStatus(i0 context.Context, i1 string) (DriverCurrentState, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.CurrentStatus")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CurrentStatus(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.CurrentStatus")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) SetCurrentStatus(i0 context.Context, i1 string, i2 model.DriverStatus, i3 ...model.SetCurrentStatusOptionFn) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetCurrentStatus")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetCurrentStatus(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetCurrentStatus")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) GetProfile(i0 context.Context, i1 string, i2 ...Option) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetProfile")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetProfile(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetProfile")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) SetProfile(i0 context.Context, i1 *model.Driver, i2 bson.M) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetProfile")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetProfile(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetProfile")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetProfileCache(i0 context.Context, i1 *model.Driver) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetProfileCache")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetProfileCache(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetProfileCache")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) GetProfilesByID(i0 context.Context, i1 []string) (map[string]*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetProfilesByID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetProfilesByID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetProfilesByID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) GetMinimalProfilesByID(i0 context.Context, i1 []string) (map[string]*model.DriverMinimal, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetMinimalProfilesByID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetMinimalProfilesByID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetMinimalProfilesByID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) RemoveProfile(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.RemoveProfile")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.RemoveProfile(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.RemoveProfile")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetTodayEarning(i0 context.Context, i1 ...model.DriverEarning) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetTodayEarning")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetTodayEarning(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetTodayEarning")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) GetTodayEarning(i0 context.Context, i1 string) (model.DriverEarning, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetTodayEarning")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetTodayEarning(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetTodayEarning")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) GetProfileByRefID(i0 context.Context, i1 string) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetProfileByRefID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetProfileByRefID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetProfileByRefID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) GetProfileByUOBRefID(i0 context.Context, i1 string) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetProfileByUOBRefID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetProfileByUOBRefID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetProfileByUOBRefID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) CountProfileByRegion(i0 context.Context, i1 string) (int, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.CountProfileByRegion")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountProfileByRegion(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.CountProfileByRegion")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) ListProfileByRegion(i0 context.Context, i1 string, i2 int, i3 int) ([]model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.ListProfileByRegion")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.ListProfileByRegion(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.ListProfileByRegion")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) UpdateBanInfo(i0 context.Context, i1 *model.Driver) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateBanInfo")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateBanInfo(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateBanInfo")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateBanHistory(i0 context.Context, i1 *model.Driver) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateBanHistory")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateBanHistory(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateBanHistory")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) GetDriverBannedUntilExceed(i0 context.Context) ([]model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetDriverBannedUntilExceed")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDriverBannedUntilExceed(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetDriverBannedUntilExceed")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) UpdateCriminalStatus(i0 context.Context, i1 []string, i2 model.CriminalStatus) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateCriminalStatus")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateCriminalStatus(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateCriminalStatus")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateHaveJacket(i0 context.Context, i1 []string, i2 bool) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateHaveJacket")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateHaveJacket(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateHaveJacket")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateHaveBox(i0 context.Context, i1 []string, i2 bool) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateHaveBox")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateHaveBox(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateHaveBox")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateBoxType(i0 context.Context, i1 model.BoxType, i2 []string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateBoxType")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateBoxType(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateBoxType")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetRatingScore(i0 context.Context, i1 model.Driver) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetRatingScore")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetRatingScore(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetRatingScore")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetSMARating(i0 context.Context, i1 string, i2 []uint32, i3 float64) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetSMARating")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetSMARating(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetSMARating")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) AddShift(i0 context.Context, i1 string, i2 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.AddShift")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.AddShift(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.AddShift")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) RemoveShift(i0 context.Context, i1 string, i2 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.RemoveShift")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.RemoveShift(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.RemoveShift")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) RemoveShifts(i0 context.Context, i1 string, i2 []string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.RemoveShifts")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.RemoveShifts(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.RemoveShifts")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateUobRefID(i0 context.Context, i1 *model.Driver, i2 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateUobRefID")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateUobRefID(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateUobRefID")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetCancellationQuota(i0 context.Context, i1 string, i2 model.CancellationQuota) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetCancellationQuota")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetCancellationQuota(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetCancellationQuota")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetWithdrawalQuota(i0 context.Context, i1 string, i2 model.WithdrawalQuota) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetWithdrawalQuota")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetWithdrawalQuota(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetWithdrawalQuota")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UnAssignOrder(i0 context.Context, i1 string, i2 model.DriverStatus) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.UnAssignOrder")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.UnAssignOrder(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UnAssignOrder")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) UnAssignTrip(i0 context.Context, i1 string, i2 model.DriverStatus) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.UnAssignTrip")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.UnAssignTrip(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UnAssignTrip")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) SetOfflineLater(i0 context.Context, i1 string, i2 bool, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetOfflineLater")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetOfflineLater(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetOfflineLater")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetCompletedOrderTime(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetCompletedOrderTime")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetCompletedOrderTime(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetCompletedOrderTime")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetRefCode(i0 context.Context, i1 string, i2 string, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetRefCode")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetRefCode(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetRefCode")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) FindWhoHasAttendanceLogInBetween(i0 context.Context, i1 time.Time, i2 time.Time, i3 ...Option) ([]model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindWhoHasAttendanceLogInBetween")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindWhoHasAttendanceLogInBetween(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindWhoHasAttendanceLogInBetween")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) RemoveAttendanceLogInBetween(i0 context.Context, i1 string, i2 time.Time, i3 time.Time) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.RemoveAttendanceLogInBetween")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.RemoveAttendanceLogInBetween(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.RemoveAttendanceLogInBetween")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) GetAttendanceLog(i0 context.Context, i1 string, i2 ...Option) (model.DriverAttendance, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetAttendanceLog")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetAttendanceLog(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetAttendanceLog")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) AddAttendanceLog(i0 context.Context, i1 string, i2 []model.AttendanceLog) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.AddAttendanceLog")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.AddAttendanceLog(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.AddAttendanceLog")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) DeleteCurrentOrderFromCache(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.DeleteCurrentOrderFromCache")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.DeleteCurrentOrderFromCache(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.DeleteCurrentOrderFromCache")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetCurrentStatusToCache(i0 context.Context, i1 string, i2 model.DriverStatus) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetCurrentStatusToCache")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetCurrentStatusToCache(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetCurrentStatusToCache")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetDriverShiftCountdownBreakingQuota(i0 context.Context, i1 string, i2 string, i3 time.Duration) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetDriverShiftCountdownBreakingQuota")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDriverShiftCountdownBreakingQuota(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetDriverShiftCountdownBreakingQuota")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateAfterAcceptingBundleOrder(i0 context.Context, i1 string, i2 model.Record) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateAfterAcceptingBundleOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateAfterAcceptingBundleOrder(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateAfterAcceptingBundleOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UnlockForQueueing(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UnlockForQueueing")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UnlockForQueueing(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UnlockForQueueing")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) MarkAsAcknowledged(i0 context.Context, i1 string) (bool, []string, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.MarkAsAcknowledged")
	defer span.End()

	start := time.Now()

	r0, r1, r2 := p.Delegate.MarkAsAcknowledged(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.MarkAsAcknowledged")

	if r2 != nil {
		span.RecordError(r2)
		span.SetStatus(codes.Error, r2.Error())
	}

	return r0, r1, r2
}

func (p *ProxyDriverRepository) SetLastAcceptAttemptAt(i0 context.Context, i1 string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetLastAcceptAttemptAt")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetLastAcceptAttemptAt(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetLastAcceptAttemptAt")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetRecoIdleTimeStartPoint(i0 context.Context, i1 string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetRecoIdleTimeStartPoint")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetRecoIdleTimeStartPoint(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetRecoIdleTimeStartPoint")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetTripID(i0 context.Context, i1 string, i2 string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetTripID")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetTripID(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetTripID")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) AddRemark(i0 context.Context, i1 string, i2 model.DriverRemark) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.AddRemark")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.AddRemark(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.AddRemark")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateDriverTier(i0 context.Context, i1 *model.Driver) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateDriverTier")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateDriverTier(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateDriverTier")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) GetProfileByIDForRequestUpdate(i0 context.Context, i1 string) (model.DriverRequestUpdateProfile, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.GetProfileByIDForRequestUpdate")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetProfileByIDForRequestUpdate(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GetProfileByIDForRequestUpdate")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) UpdateProfileStatus(i0 context.Context, i1 string, i2 model.ProfileStatus) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateProfileStatus")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateProfileStatus(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateProfileStatus")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateServiceTypes(i0 context.Context, i1 string, i2 []model.Service) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateServiceTypes")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateServiceTypes(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateServiceTypes")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateManyDedicatedZoneLabels(i0 context.Context, i1 []string, i2 []string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateManyDedicatedZoneLabels")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateManyDedicatedZoneLabels(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateManyDedicatedZoneLabels")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) IsExistsByCitizenID(i0 context.Context, i1 crypt.LazyEncryptedString) bool {

	_, span := p.Tracer.Start(i0, "DriverRepository.IsExistsByCitizenID")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.IsExistsByCitizenID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.IsExistsByCitizenID")

	return r0
}

func (p *ProxyDriverRepository) UpdateDriverFinancialRiskControl(i0 context.Context, i1 string, i2 float64, i3 int, i4 float64, i5 model.DriverRemark) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateDriverFinancialRiskControl")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateDriverFinancialRiskControl(i0, i1, i2, i3, i4, i5)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateDriverFinancialRiskControl")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetDriverH3Recommendation(i0 context.Context, i1 string, i2 *model.H3Recommendation) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetDriverH3Recommendation")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDriverH3Recommendation(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetDriverH3Recommendation")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) BulkUpdateDeprioritization(i0 context.Context, i1 []string, i2 map[string]model.Services) (int, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.BulkUpdateDeprioritization")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.BulkUpdateDeprioritization(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.BulkUpdateDeprioritization")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) ValidateDriverIDs(i0 context.Context, i1 []string, i2 ...Option) ([]string, []string, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.ValidateDriverIDs")
	defer span.End()

	start := time.Now()

	r0, r1, r2 := p.Delegate.ValidateDriverIDs(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.ValidateDriverIDs")

	if r2 != nil {
		span.RecordError(r2)
		span.SetStatus(codes.Error, r2.Error())
	}

	return r0, r1, r2
}

func (p *ProxyDriverRepository) SetGoodness(i0 context.Context, i1 string, i2 model.Goodness) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetGoodness")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetGoodness(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetGoodness")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) FindOneAndSetField(i0 context.Context, i1 DriverQuery, i2 DriverUpdator) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindOneAndSetField")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.FindOneAndSetField(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindOneAndSetField")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) BulkWriteModel(i0 context.Context, i1 ...DriverUpdateOneModel) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.BulkWriteModel")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.BulkWriteModel(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.BulkWriteModel")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetServicesOptOut(i0 context.Context, i1 string, i2 []model.Service) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetServicesOptOut")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetServicesOptOut(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetServicesOptOut")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) GlobalToggleDisableSupplyPos(i0 context.Context) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.GlobalToggleDisableSupplyPos")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.GlobalToggleDisableSupplyPos(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.GlobalToggleDisableSupplyPos")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) MultipleEnableSupplyPositioning(i0 context.Context, i1 []string) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.MultipleEnableSupplyPositioning")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.MultipleEnableSupplyPositioning(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.MultipleEnableSupplyPositioning")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) FindDriverIDsSupplyPositioning(i0 context.Context) ([]string, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindDriverIDsSupplyPositioning")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindDriverIDsSupplyPositioning(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindDriverIDsSupplyPositioning")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) FindDriverMinimalByDriverID(i0 context.Context, i1 string, i2 ...Option) (*model.DriverMinimal, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindDriverMinimalByDriverID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindDriverMinimalByDriverID(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindDriverMinimalByDriverID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverRepository) SetOnTopQuota(i0 context.Context, i1 string, i2 []model.OnTopQuota) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetOnTopQuota")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetOnTopQuota(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetOnTopQuota")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) UpdateDriverBySetter(i0 context.Context, i1 string, i2 setter.Driver) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.UpdateDriverBySetter")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateDriverBySetter(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.UpdateDriverBySetter")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetServiceOptInLastOnlineAt(i0 context.Context, i1 string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetServiceOptInLastOnlineAt")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetServiceOptInLastOnlineAt(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetServiceOptInLastOnlineAt")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) SetPauseServiceOptInBikeReminderUntil(i0 context.Context, i1 string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "DriverRepository.SetPauseServiceOptInBikeReminderUntil")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetPauseServiceOptInBikeReminderUntil(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.SetPauseServiceOptInBikeReminderUntil")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverRepository) FindDriverIDAndStatus(i0 context.Context, i1 string, i2 model.DriverStatus, i3 ...Option) (*model.Driver, error) {

	_, span := p.Tracer.Start(i0, "DriverRepository.FindDriverIDAndStatus")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindDriverIDAndStatus(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverRepository.FindDriverIDAndStatus")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
