//go:generate mockgen -source=./shift_cancel_repository.go -destination=./mock_repository/mock_shift_cancel_repository.go -package=mock_repository

package repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type ShiftCancelRepository interface {
	Find(ctx context.Context, query ShiftCancelQuery, skip int, limit int, opts ...Option) ([]model.ShiftCancel, error)
	Create(ctx context.Context, m *model.ShiftCancel, opts ...Option) error
}

type ShiftCancelQuery struct {
	DriverID string
	ShiftID  string
}
