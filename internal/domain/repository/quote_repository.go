package repository

//go:generate mockgen -source=./quote_repository.go -destination=./mock_repository/mock_quote_repository.go -package=mock_repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type QuoteRepository interface {
	// CreateOrUpdateQuote update existing quote or create a new one if it's not exist.
	CreateOrUpdateQuote(context.Context, *model.Quote) error

	// Get quote from quote id.
	Find(ctx context.Context, qid string, opts ...Option) (*model.Quote, error)
}
