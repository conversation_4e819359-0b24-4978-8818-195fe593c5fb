// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"go.mongodb.org/mongo-driver/mongo"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyIncomeDailySummaryRepository(delegate IncomeDailySummaryRepository, meter metric.Meter) *ProxyIncomeDailySummaryRepository {
	return &ProxyIncomeDailySummaryRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyIncomeDailySummaryRepository-tracer"),
	}
}

type ProxyIncomeDailySummaryRepository struct {
	Delegate         IncomeDailySummaryRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyIncomeDailySummaryRepository) Insert(i0 context.Context, i1 model.IncomeDailySummary, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "IncomeDailySummaryRepository.Insert")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Insert(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "IncomeDailySummaryRepository.Insert")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyIncomeDailySummaryRepository) Upsert(i0 context.Context, i1 model.IncomeDailySummary, i2 ...Option) (mongo.UpdateResult, error) {

	_, span := p.Tracer.Start(i0, "IncomeDailySummaryRepository.Upsert")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Upsert(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "IncomeDailySummaryRepository.Upsert")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyIncomeDailySummaryRepository) Query(i0 context.Context, i1 IncomeDailySummaryQuery, i2 int, i3 int, i4 []string, i5 ...Option) ([]model.IncomeDailySummary, error) {

	_, span := p.Tracer.Start(i0, "IncomeDailySummaryRepository.Query")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Query(i0, i1, i2, i3, i4, i5...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "IncomeDailySummaryRepository.Query")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyIncomeDailySummaryRepository) Inc(i0 context.Context, i1 IncomeDailySummaryQuery, i2 model.IncomeDailySummary) (*mongo.UpdateResult, error) {

	_, span := p.Tracer.Start(i0, "IncomeDailySummaryRepository.Inc")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Inc(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "IncomeDailySummaryRepository.Inc")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyIncomeDailySummaryRepository) DeleteByDriverID(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "IncomeDailySummaryRepository.DeleteByDriverID")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.DeleteByDriverID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "IncomeDailySummaryRepository.DeleteByDriverID")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
