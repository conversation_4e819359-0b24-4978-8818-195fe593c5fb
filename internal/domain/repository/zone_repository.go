package repository

//go:generate mockgen -source=./zone_repository.go -destination=./mock_repository/mock_zone_repository.go -package=mock_repository

import (
	"context"

	"github.com/pkg/errors"
	"github.com/twpayne/go-geom"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

var ErrZoneDuplicate = errors.New("zone is duplicate")

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type ZoneRepository interface {
	Create(ctx context.Context, zone model.Zone) error
	FindById(ctx context.Context, id string, opts ...Option) (model.Zone, error)
	FindByZoneCode(ctx context.Context, zoneCode string, opts ...Option) (model.Zone, error)
	FindBriefZones(ctx context.Context, req model.BriefZoneQueryReq) ([]model.BriefZone, error)
	Find(ctx context.Context, req model.ZoneQueryReq, skip int, limit int, sort []string, opts ...Option) ([]model.Zone, int64, error)
	UpdateById(ctx context.Context, id string, req model.UpdateZoneReq) error
	FindZoneCodesByLocation(ctx context.Context, lat float64, lng float64, opts ...Option) ([]string, error)
	FindActiveZoneCodesByLocation(ctx context.Context, lat float64, lng float64, opts ...Option) ([]string, error)
	FindZoneIDsByLocation(ctx context.Context, lat float64, lng float64, opts ...Option) ([]primitive.ObjectID, error)
	FindZonesIntersectPolygon(ctx context.Context, polygon *geom.Polygon, opts ...Option) ([]model.ZoneWithoutGeometry, error)
}
