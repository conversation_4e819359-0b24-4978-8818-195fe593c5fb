//go:generate mockgen -source=./driver_last_update_location_tracker_repository.go -destination=./mock_repository/mock_driver_last_update_location_tracker_repository.go -package=mock_repository

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

//go:generate mockgen -source=./order_hearthbeat_repository.go -destination=./mock_repository/mock_order_hearthbeat_repository.go -package=mock_repository

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type OrderHeartbeatRepository interface {
	GetKeySlot() []string
	UpdateHearthBeat(ctx context.Context, orderIDs []string, t time.Time) error
	UpdateOrderRedistributionState(ctx context.Context, states []model.OrderHearthBeatStateWithOrderID) error
	RemoveOrderHearthBeat(ctx context.Context, orderIDs []string) error
	RemoveOrderRedistributionState(ctx context.Context, orderIDs []string) error
	GetUnhealthyOrders(ctx context.Context, threshold time.Duration) ([]model.OrderHearthBeatStateWithOrderID, error)
	GetUnhealthyOrderScore(ctx context.Context, threshold time.Duration) (map[string]float64, error)
}
