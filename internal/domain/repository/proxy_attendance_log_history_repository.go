// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyAttendanceLogHistoryRepository(delegate AttendanceLogHistoryRepository, meter metric.Meter) *ProxyAttendanceLogHistoryRepository {
	return &ProxyAttendanceLogHistoryRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyAttendanceLogHistoryRepository-tracer"),
	}
}

type ProxyAttendanceLogHistoryRepository struct {
	Delegate         AttendanceLogHistoryRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyAttendanceLogHistoryRepository) Create(i0 context.Context, i1 *model.AttendanceLogHistory) error {

	_, span := p.Tracer.Start(i0, "AttendanceLogHistoryRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "AttendanceLogHistoryRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyAttendanceLogHistoryRepository) FindByDriverID(i0 context.Context, i1 string) ([]*model.AttendanceLogHistory, error) {

	_, span := p.Tracer.Start(i0, "AttendanceLogHistoryRepository.FindByDriverID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByDriverID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "AttendanceLogHistoryRepository.FindByDriverID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
