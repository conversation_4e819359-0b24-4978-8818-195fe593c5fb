package repository

//go:generate mockgen -source=./term_condition_repository.go -destination=./mock_repository/mock_term_condition_repository.go -package=mock_repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type TermAndConditionRepository interface {
	Create(ctx context.Context, termcondition model.TermAndCondition) error

	FindLatestByDriverID(ctx context.Context, driverID string, opt ...Option) (*model.TermAndCondition, error)
}
