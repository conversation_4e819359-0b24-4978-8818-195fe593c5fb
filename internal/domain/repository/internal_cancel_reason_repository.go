package repository

//go:generate mockgen -source=./internal_cancel_reason_repository.go -destination=./mock_repository/mock_internal_cancel_reason_repository.go -package=mock_repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type InternalCancelReasonRepository interface {
	Create(ctx context.Context, internalCancelReason *model.InternalCancelReason) error
	Find(ctx context.Context, skip, limit int, opts ...Option) ([]model.InternalCancelReason, error)
	FindSorted(ctx context.Context, skip, limit int, opts ...Option) ([]model.InternalCancelReason, error)
	FindById(ctx context.Context, internalCancelReasonId string, opts ...Option) (*model.InternalCancelReason, error)
	FindByIDs(ctx context.Context, icrIDs types.StringSet, opts ...Option) (map[string]model.InternalCancelReason, error)
	FindByLabel(ctx context.Context, label string, opts ...Option) (*model.InternalCancelReason, error)
	Update(ctx context.Context, driver *model.InternalCancelReason) error
	Delete(ctx context.Context, internalCancelReasonId string) error
}
