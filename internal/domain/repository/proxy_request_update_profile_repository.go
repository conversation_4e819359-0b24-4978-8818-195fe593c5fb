// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyRequestUpdateProfileRepository(delegate RequestUpdateProfileRepository, meter metric.Meter) *ProxyRequestUpdateProfileRepository {
	return &ProxyRequestUpdateProfileRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyRequestUpdateProfileRepository-tracer"),
	}
}

type ProxyRequestUpdateProfileRepository struct {
	Delegate         RequestUpdateProfileRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyRequestUpdateProfileRepository) CreateAll(i0 context.Context, i1 []model.RequestUpdateDriverProfile, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "RequestUpdateProfileRepository.CreateAll")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.CreateAll(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RequestUpdateProfileRepository.CreateAll")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyRequestUpdateProfileRepository) FindWithQueryAndSort(i0 context.Context, i1 RequestUpdateProfileQuery, i2 int, i3 int, i4 ...Option) ([]model.RequestUpdateDriverProfile, error) {

	_, span := p.Tracer.Start(i0, "RequestUpdateProfileRepository.FindWithQueryAndSort")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindWithQueryAndSort(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RequestUpdateProfileRepository.FindWithQueryAndSort")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyRequestUpdateProfileRepository) ReplaceAll(i0 context.Context, i1 []model.RequestUpdateDriverProfile) error {

	_, span := p.Tracer.Start(i0, "RequestUpdateProfileRepository.ReplaceAll")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.ReplaceAll(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RequestUpdateProfileRepository.ReplaceAll")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyRequestUpdateProfileRepository) FindInProcessRequestByDriverIDAndSectionIDs(i0 context.Context, i1 string, i2 []string, i3 ...Option) ([]model.RequestUpdateDriverProfile, error) {

	_, span := p.Tracer.Start(i0, "RequestUpdateProfileRepository.FindInProcessRequestByDriverIDAndSectionIDs")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindInProcessRequestByDriverIDAndSectionIDs(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RequestUpdateProfileRepository.FindInProcessRequestByDriverIDAndSectionIDs")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyRequestUpdateProfileRepository) UpdateStatus(i0 context.Context, i1 model.RequestProfileStatus, i2 string, i3 ...primitive.ObjectID) error {

	_, span := p.Tracer.Start(i0, "RequestUpdateProfileRepository.UpdateStatus")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateStatus(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RequestUpdateProfileRepository.UpdateStatus")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyRequestUpdateProfileRepository) CountWithQuery(i0 context.Context, i1 RequestUpdateProfileQuery, i2 ...Option) (int, error) {

	_, span := p.Tracer.Start(i0, "RequestUpdateProfileRepository.CountWithQuery")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountWithQuery(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "RequestUpdateProfileRepository.CountWithQuery")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
