package repository

//go:generate mockgen -source=./income_daily_summary_repository.go -destination=./mock_repository/mock_income_daily_summary_repository.go -package=mock_repository

import (
	"context"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type IncomeDailySummaryQuery interface {
	WithDriverID(driverID string) IncomeDailySummaryQuery
	WithFromInclusive(fromInclusive time.Time) IncomeDailySummaryQuery
	WithToExclusive(toInclusive time.Time) IncomeDailySummaryQuery
	WithToEqExclusive(toInclusive time.Time) IncomeDailySummaryQuery
	Query() bson.M
}

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type IncomeDailySummaryRepository interface {
	Insert(ctx context.Context, daily model.IncomeDailySummary, opts ...Option) error
	Upsert(ctx context.Context, daily model.IncomeDailySummary, opts ...Option) (mongo.UpdateResult, error)
	Query(ctx context.Context, query IncomeDailySummaryQuery, skip, limit int, sort []string, opts ...Option) ([]model.IncomeDailySummary, error)
	Inc(ctx context.Context, query IncomeDailySummaryQuery, update model.IncomeDailySummary) (*mongo.UpdateResult, error)
	DeleteByDriverID(ctx context.Context, driverId string) error
}
