package repository

//go:generate mockgen -source=./driver_registration_repository.go -destination=./mock_repository/mock_driver_registration_repository.go -package=mock_repository

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"gopkg.in/guregu/null.v4"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

var (
	ErrDuplicateLineUid = errors.New("duplicate line uid")
	DefaultSort         = []string{"-created_at"}
)

type DriverRegistrationQuery interface {
	WithIDs(ids []string) DriverRegistrationQuery
	WithCitizenID(citizenID crypt.LazyEncryptedString) DriverRegistrationQuery
	WithFirstname(firstname crypt.LazyEncryptedString) DriverRegistrationQuery
	WithLastname(lastname crypt.LazyEncryptedString) DriverRegistrationQuery
	WithPhone(phone crypt.LazyEncryptedString) DriverRegistrationQuery
	WithStatus(status string) DriverRegistrationQuery
	WithoutStatus(status []string) DriverRegistrationQuery
	WithPreScreen(preScreen string) DriverRegistrationQuery
	WithTrained(trained string) DriverRegistrationQuery
	WithInfoCompleted(infoCompleted string) DriverRegistrationQuery
	WithDriverType(driverType crypt.LazyEncryptedString) DriverRegistrationQuery
	WithFrom(from time.Time) DriverRegistrationQuery
	WithTo(to time.Time) DriverRegistrationQuery
	WithInterestedRegionLalamove(interestedRegionLalamove string) DriverRegistrationQuery
	WithInterestedProvince(interestedProvince string) DriverRegistrationQuery
	WithRegion(region string) DriverRegistrationQuery
	WithInRegions(regions []string) DriverRegistrationQuery
	WithAssignedReviewer(assignedReviewer string) DriverRegistrationQuery
	WithInterestedProvinces(interestedProvinces []string) DriverRegistrationQuery
	WithRegistrationLocation(query RegistrationLocationQuery) DriverRegistrationQuery
	WithCriminalCheckStatus(criminalCheckStatus model.CriminalStatus) DriverRegistrationQuery
	WithRegisDuplicatedCitizenId(duplicated null.Bool) DriverRegistrationQuery
	WithDriverDuplicatedCitizenId(duplicated null.Bool) DriverRegistrationQuery
	WithLINEUserIDExist(isExisted null.Bool) DriverRegistrationQuery
	WithLINEUserIDRetryLower(count int) DriverRegistrationQuery
	WithLINEUserID(lineuid string) DriverRegistrationQuery
}

type DriverRegisterationUpdator interface {
	SetLINEUserID(lineUID string) DriverRegisterationUpdator
	SetLINEMID(lineMID string) DriverRegisterationUpdator
	IncreaseLINEUserIDRetryCount(amount int) DriverRegisterationUpdator
}

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type DriverRegistrationRepository interface {
	IsExistsByCitizenID(ctx context.Context, citizenID crypt.LazyEncryptedString) bool
	IsExistsByCitizenIDAndID(ctx context.Context, citizenID crypt.LazyEncryptedString, id string) bool
	IsExistsByLineUid(ctx context.Context, lineUid crypt.LazyEncryptedString) bool
	Find(ctx context.Context, query DriverRegistrationQuery, skip, limit int, sort []string, opts ...Option) ([]model.DriverRegistration, error)
	FindRegisByCitizenIDs(ctx context.Context, citizenIDs []crypt.LazyEncryptedString) ([]model.DriverRegistration, error)
	FindOneByID(ctx context.Context, id string) (*model.DriverRegistration, error)
	GetByLineUID(ctx context.Context, lineUID string) (*model.DriverRegistration, error)
	GetByCitizenID(ctx context.Context, citizenID crypt.LazyEncryptedString) (*model.DriverRegistration, error)
	Count(ctx context.Context, query DriverRegistrationQuery) (int, error)
	RemoveByLineUID(ctx context.Context, lineUID crypt.LazyEncryptedString) error
	Insert(ctx context.Context, entity *model.DriverRegistration) error
	Update(ctx context.Context, entity *model.DriverRegistration) error
	ListByLineUID(ctx context.Context, lineUID crypt.LazyEncryptedString) ([]model.DriverRegistration, error)
	FindOneAndSetField(ctx context.Context, query DriverRegistrationQuery, updator DriverRegisterationUpdator) error
	FindWithQuerySelectorAndSort(ctx context.Context, query DriverRegistrationQuery, selector []string, skip, limit int, sort []string, opts ...Option) ([]model.DriverRegistration, error)
	BulkWriteModel(ctx context.Context, updateOneModels ...DriverRegistrationUpdateOneModel) error
	ListByLINEUserID(ctx context.Context, lineUID string) ([]model.DriverRegistration, error)
}

type RegistrationLocationQuery struct {
	Province string
	District string
}

func (l RegistrationLocationQuery) IsZero() bool {
	return l == RegistrationLocationQuery{}
}

type DriverRegistrationUpdateOneModel struct {
	Query   DriverRegistrationQuery
	Updator DriverRegisterationUpdator
}
