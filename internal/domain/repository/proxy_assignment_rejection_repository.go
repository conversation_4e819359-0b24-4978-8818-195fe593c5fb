// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyAssignmentRejectionRepository(delegate AssignmentRejectionRepository, meter metric.Meter) *ProxyAssignmentRejectionRepository {
	return &ProxyAssignmentRejectionRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyAssignmentRejectionRepository-tracer"),
	}
}

type ProxyAssignmentRejectionRepository struct {
	Delegate         AssignmentRejectionRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyAssignmentRejectionRepository) Create(i0 context.Context, i1 *model.AssignmentRejection) error {

	_, span := p.Tracer.Start(i0, "AssignmentRejectionRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "AssignmentRejectionRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
