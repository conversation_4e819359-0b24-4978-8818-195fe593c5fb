package repository

//go:generate mockgen -source=./driver_repository.go -destination=./mock_repository/mock_driver_repository.go -package=mock_repository

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"
	"gopkg.in/guregu/null.v4"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence/setter"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

// ErrDriverNotFound tells that this driver doesn't exists in service.
var ErrDriverNotFound = errors.New("driver not found in our service")

type DriverQuery interface {
	WithDriverID(driverID string) DriverQuery
	WithCitizenID(citizenID string) DriverQuery
	WithCitizenIDs(citizenIDs []string) DriverQuery
	WithFirstName(firstname string) DriverQuery
	WithLastName(lastname string) DriverQuery
	WithPhone(phone string) DriverQuery
	WithStatus(status model.DriverStatus) DriverQuery
	WithDriverType(driverType string) DriverQuery
	WithTrained(trained string) DriverQuery
	WithBeginAt(beginat time.Time) DriverQuery
	WithEndAt(endat time.Time) DriverQuery
	WithBoxTypes(boxTypes []string) DriverQuery
	WithRegion(region string) DriverQuery
	WithInRegions(regions []string) DriverQuery
	WithProfileStatus(region model.ProfileStatus) DriverQuery
	WithAssignedReviewer(assignedReviewer string) DriverQuery
	WithDriverRole(driverRole string) DriverQuery
	WithCriminalStatus(criminalStatus model.CriminalStatus) DriverQuery
	WithoutStatus(status []string) DriverQuery
	WithGTERating(rating null.Float) DriverQuery
	WithGTESMARating(rating null.Float) DriverQuery
	WithLicensePlate(licensePlate string) DriverQuery
	WithDriverIDs(drivers []string) DriverQuery
	WithGTDriverID(driverID string) DriverQuery
	WithInTiers(tiers []string) DriverQuery
	WithoutDriverIDs(drivers []string) DriverQuery
	WithLINEUserIDExist(isExisted null.Bool) DriverQuery
	WithLINEUserIDRetryLower(count int) DriverQuery
	IsEmpty() bool
}

type DriverUpdator interface {
	SetLINEUserID(lineUID string) DriverUpdator
	SetLINEMID(lineMID string) DriverUpdator
	IncreaseLINEUserIDRetryCount(amount int) DriverUpdator
	SetDSCR(dscr float64) DriverUpdator
	SetDSCREffectiveDate(dscrEffectiveDate types.Period) DriverUpdator
	SetMaxTenor(maxTenor int) DriverUpdator
	SetMaxExposure(maxExposure float64) DriverUpdator
	PushRemark(remark model.DriverRemark) DriverUpdator
}

type DriverUpdateOneModel struct {
	Query   DriverQuery
	Updator DriverUpdator
}

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type DriverRepository interface {
	Create(ctx context.Context, driver *model.Driver) error
	Find(ctx context.Context, skip, limit int) ([]model.Driver, error)
	FindSorted(ctx context.Context, skip, limit int) ([]model.Driver, error)
	FindDriverID(ctx context.Context, driverID string, opts ...Option) (*model.Driver, error)
	FindDriverIDs(ctx context.Context, driverIDs []string, opts ...Option) ([]model.Driver, error)
	CountDriver(ctx context.Context) (int, error)
	IsExists(ctx context.Context, driverID string) bool
	FindWithQueryAndSort(ctx context.Context, query DriverQuery, sort []string, skip, limit int, opts ...Option) ([]model.Driver, error)
	FindWithQuerySelectorAndSort(ctx context.Context, query DriverQuery, selector []string, sort []string, skip, limit int, opts ...Option) ([]model.Driver, error)
	CountDriverWithQuery(ctx context.Context, query DriverQuery, opts ...Option) (int, error)
	Update(ctx context.Context, driver *model.Driver) error
	UpdateManyByTier(ctx context.Context, driverIDs []string, newTier string) error
	UpdateManyByNegativeGroup(ctx context.Context, driverIDs []string, group string) error
	MultipleUpdateHaveBox(ctx context.Context, drivers []string, setHaveBox bool) error
	GetByUID(ctx context.Context, UID string) (*model.Driver, error)
	GetByRefreshToken(ctx context.Context, token string) (*model.Driver, error)
	SetRefreshToken(ctx context.Context, driverID string, token string) error
	// CurrentStatus get current driver status. This api will return orderID when it's available.
	CurrentStatus(ctx context.Context, driverID string) (DriverCurrentState, error)
	// SetCurrentStatus set driver status to this driver.
	SetCurrentStatus(ctx context.Context, driverID string, status model.DriverStatus, opts ...model.SetCurrentStatusOptionFn) error
	// GetProfile get driver profile.
	GetProfile(ctx context.Context, driverID string, opts ...Option) (*model.Driver, error)
	// SetProfile set driver profile to redis cache
	SetProfile(ctx context.Context, profile *model.Driver, additionalSel bson.M) error
	SetProfileCache(ctx context.Context, profile *model.Driver) error
	// GetProfilesByID returns map of driverID and model
	GetProfilesByID(ctx context.Context, ds []string) (map[string]*model.Driver, error)
	// GetMinimalProfilesByID returns map of driverID and model
	GetMinimalProfilesByID(ctx context.Context, ds []string) (map[string]*model.DriverMinimal, error)
	// RemoveProfile remove driver profile out of system.
	RemoveProfile(ctx context.Context, driverID string) error
	// SetTodayEarning set driver today earning
	SetTodayEarning(ctx context.Context, driverEarnings ...model.DriverEarning) error
	// GetTodayEarning get driver today earning
	GetTodayEarning(ctx context.Context, driverID string) (model.DriverEarning, error)
	// GetProfileByRefID get driver by driver reference id
	GetProfileByRefID(ctx context.Context, refID string) (driver *model.Driver, err error)
	// GetProfileByUOBRefID get driver by driver uob reference id
	GetProfileByUOBRefID(ctx context.Context, refID string) (driver *model.Driver, err error)
	// CountProfileByRegion count driver by region
	CountProfileByRegion(ctx context.Context, region string) (int, error)
	// ListProfileByRegion list driver by region
	ListProfileByRegion(ctx context.Context, region string, skip, limit int) ([]model.Driver, error)
	// UpdateBanInfo update driver ban info
	UpdateBanInfo(ctx context.Context, driver *model.Driver) error
	UpdateBanHistory(ctx context.Context, driver *model.Driver) error
	// GetDriverStatusBanned get driver status banned and temporary ban time exceed
	GetDriverBannedUntilExceed(ctx context.Context) ([]model.Driver, error)
	UpdateCriminalStatus(ctx context.Context, driverIDs []string, status model.CriminalStatus) error
	UpdateHaveJacket(ctx context.Context, driverIDs []string, value bool) error
	UpdateHaveBox(ctx context.Context, driverIDs []string, value bool) error
	UpdateBoxType(ctx context.Context, boxType model.BoxType, driverIDs []string) error
	SetRatingScore(ctx context.Context, profile model.Driver) error
	SetSMARating(ctx context.Context, driverID string, latestRatings []uint32, newAverage float64) error
	AddShift(ctx context.Context, driverID, shiftId string) error
	RemoveShift(ctx context.Context, driverID, shiftId string) error
	RemoveShifts(ctx context.Context, driverID string, shiftIds []string) error
	UpdateUobRefID(ctx context.Context, driver *model.Driver, newUobRefID string) error
	SetCancellationQuota(ctx context.Context, driverId string, quota model.CancellationQuota) error
	SetWithdrawalQuota(ctx context.Context, driverId string, quota model.WithdrawalQuota) error
	UnAssignOrder(ctx context.Context, driverId string, status model.DriverStatus) (*model.Driver, error)
	UnAssignTrip(ctx context.Context, driverId string, status model.DriverStatus) (*model.Driver, error)
	SetOfflineLater(ctx context.Context, driverId string, value bool, opts ...Option) error
	SetCompletedOrderTime(ctx context.Context, driverId string) error
	SetRefCode(ctx context.Context, driverId string, code string, opts ...Option) error

	// FindWhoHasAttendanceLogInBetween get all drivers which has attendance log time in between `start` and `end`
	FindWhoHasAttendanceLogInBetween(ctx context.Context, start time.Time, end time.Time, opts ...Option) ([]model.Driver, error)

	// RemoveAttendanceLogInBetween remove driver's attendance logs where time in between `start` and `end`
	RemoveAttendanceLogInBetween(ctx context.Context, driverID string, start time.Time, end time.Time) error

	// GetAttendanceLog get only attendance log field in driver
	GetAttendanceLog(ctx context.Context, driverID string, opts ...Option) (model.DriverAttendance, error)

	// AddAttendanceLog fill attendance log in driver
	AddAttendanceLog(ctx context.Context, driverID string, logs []model.AttendanceLog) error

	DeleteCurrentOrderFromCache(ctx context.Context, driverID string) error

	SetCurrentStatusToCache(ctx context.Context, driverID string, status model.DriverStatus) error

	SetDriverShiftCountdownBreakingQuota(ctx context.Context, driverID, shiftId string, ttl time.Duration) error

	UpdateAfterAcceptingBundleOrder(ctx context.Context, driverID string, record model.Record) error

	UnlockForQueueing(ctx context.Context, driverID string) error

	MarkAsAcknowledged(ctx context.Context, driverID string) (bool, []string, error)

	SetLastAcceptAttemptAt(ctx context.Context, driverID string, lastAcceptedOrderAt time.Time) error

	SetRecoIdleTimeStartPoint(ctx context.Context, driverID string, idleTime time.Time) error

	SetTripID(ctx context.Context, driverID string, tripID string) error

	AddRemark(ctx context.Context, driverID string, remark model.DriverRemark) error

	UpdateDriverTier(ctx context.Context, driver *model.Driver) error

	GetProfileByIDForRequestUpdate(ctx context.Context, driverID string) (model.DriverRequestUpdateProfile, error)

	UpdateProfileStatus(ctx context.Context, driverID string, status model.ProfileStatus) error

	UpdateServiceTypes(ctx context.Context, driverID string, services []model.Service) error

	// UpdateManyDedicatedZoneLabels update dedicated zone labels for each driver
	UpdateManyDedicatedZoneLabels(ctx context.Context, driverIDs []string, dedicatedZoneLabels []string) error

	IsExistsByCitizenID(ctx context.Context, citizenID crypt.LazyEncryptedString) bool

	UpdateDriverFinancialRiskControl(ctx context.Context, driverID string, dscr float64, maxTenor int, maxExposure float64, remark model.DriverRemark) error

	SetDriverH3Recommendation(ctx context.Context, driverID string, rec *model.H3Recommendation) error

	BulkUpdateDeprioritization(ctx context.Context, toNormalize []string, toDeprioritize map[string]model.Services) (int, error)

	// ValidateDriverIDs groups driverIDs into valid & invalid slice (doesn't exist on our system)
	// remark: this function don't preserve the order of driver ID
	ValidateDriverIDs(ctx context.Context, driverIDs []string, opts ...Option) ([]string, []string, error)

	SetGoodness(ctx context.Context, driverID string, goodness model.Goodness) error
	FindOneAndSetField(ctx context.Context, query DriverQuery, updator DriverUpdator) error
	BulkWriteModel(ctx context.Context, updateOneModels ...DriverUpdateOneModel) error

	SetServicesOptOut(ctx context.Context, driverID string, optedOutServices []model.Service) error

	// GlobalToggleDisableSupplyPos globally deletes all `supply_positioning` keys
	// from all records with the keys set
	GlobalToggleDisableSupplyPos(ctx context.Context) error

	// MultipleEnableSupplyPositioning writes key `supply_positioning: true` for all records
	// with driver_id matching driverIDs
	MultipleEnableSupplyPositioning(ctx context.Context, driverIDs []string) error

	FindDriverIDsSupplyPositioning(ctx context.Context) ([]string, error)
	FindDriverMinimalByDriverID(ctx context.Context, driverID string, opts ...Option) (*model.DriverMinimal, error)

	SetOnTopQuota(ctx context.Context, driverID string, onTopQuotas []model.OnTopQuota) error
	UpdateDriverBySetter(ctx context.Context, driverID string, driverSetter setter.Driver) error

	SetServiceOptInLastOnlineAt(ctx context.Context, driverIDs string, lastOnlineAt time.Time) error
	SetPauseServiceOptInBikeReminderUntil(ctx context.Context, driverID string, until time.Time) error
	FindDriverIDAndStatus(ctx context.Context, driverID string, status model.DriverStatus, opts ...Option) (*model.Driver, error)
}

type DriverCurrentState struct {
	Status         string   `bson:"status"`
	CurrentOrder   string   `bson:"current_order"`
	QueueingOrders []string `bson:"queueing_orders"`
	AllowQueueing  bool     `bson:"allow_queueing"`
}
