// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyOrderRepository(delegate OrderRepository, meter metric.Meter) *ProxyOrderRepository {
	return &ProxyOrderRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyOrderRepository-tracer"),
	}
}

type ProxyOrderRepository struct {
	Delegate         OrderRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyOrderRepository) CreateOrder(i0 context.Context, i1 *model.Order) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.CreateOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.CreateOrder(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.CreateOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) Find(i0 context.Context, i1 interface{}, i2 int, i3 int, i4 ...Option) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.Find")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Find(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.Find")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) FindRevision(i0 context.Context, i1 OrderRevisionQuery, i2 int, i3 int, i4 ...Option) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindRevision")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindRevision(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindRevision")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) FindAndSort(i0 context.Context, i1 interface{}, i2 int, i3 int, i4 []string, i5 ...Option) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindAndSort")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindAndSort(i0, i1, i2, i3, i4, i5...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindAndSort")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) GetExDrivers(i0 context.Context, i1 string, i2 ...Option) (types.StringSet, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetExDrivers")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetExDrivers(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetExDrivers")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) Count(i0 context.Context, i1 interface{}, i2 ...Option) (int, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.Count")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Count(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.Count")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) Get(i0 context.Context, i1 string, i2 ...Option) (*model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.Get")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Get(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.Get")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) GetRevisionByOrderIdAndDriverId(i0 context.Context, i1 string, i2 string, i3 ...Option) (*model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetRevisionByOrderIdAndDriverId")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetRevisionByOrderIdAndDriverId(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetRevisionByOrderIdAndDriverId")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) GetMany(i0 context.Context, i1 []string, i2 ...Option) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetMany")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetMany(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetMany")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) GetManyByDeliveringRounds(i0 context.Context, i1 map[string]int, i2 ...Option) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetManyByDeliveringRounds")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetManyByDeliveringRounds(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetManyByDeliveringRounds")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) GetActiveAssigningOrders(i0 context.Context, i1 []string, i2 time.Time, i3 ...Option) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetActiveAssigningOrders")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetActiveAssigningOrders(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetActiveAssigningOrders")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) CurrentStatus(i0 context.Context, i1 string) (model.Status, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.CurrentStatus")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CurrentStatus(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.CurrentStatus")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) FindByDriver(i0 context.Context, i1 string, i2 int, i3 int, i4 ...model.Status) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindByDriver")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByDriver(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindByDriver")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) FindLatestRatings(i0 context.Context, i1 string, i2 int) ([]uint32, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindLatestRatings")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindLatestRatings(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindLatestRatings")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) CountByDriverID(i0 context.Context, i1 string, i2 ...model.Status) (int, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.CountByDriverID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountByDriverID(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.CountByDriverID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) UpdateOrder(i0 context.Context, i1 *model.Order, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateOrder(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UpdateOrderCancellationMetadata(i0 context.Context, i1 *model.Order, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateOrderCancellationMetadata")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateOrderCancellationMetadata(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateOrderCancellationMetadata")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetTripID(i0 context.Context, i1 string, i2 string) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetTripID")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetTripID(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetTripID")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetIsFraud(i0 context.Context, i1 string, i2 bool) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetIsFraud")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetIsFraud(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetIsFraud")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetDistanceCompensatoryInfo(i0 context.Context, i1 string, i2 *model.DistanceCompensatoryInfo, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetDistanceCompensatoryInfo")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDistanceCompensatoryInfo(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetDistanceCompensatoryInfo")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UpsertCancelRevision(i0 context.Context, i1 model.Order, i2 model.CancelDetail, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpsertCancelRevision")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpsertCancelRevision(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpsertCancelRevision")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UpdateAndUpsertRevision(i0 context.Context, i1 *model.Order, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateAndUpsertRevision")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateAndUpsertRevision(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateAndUpsertRevision")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UpdateAndUpsertCanceledRevision(i0 context.Context, i1 *model.Order, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateAndUpsertCanceledRevision")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateAndUpsertCanceledRevision(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateAndUpsertCanceledRevision")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UpdateExpireOrder(i0 context.Context, i1 *model.Order, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateExpireOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateExpireOrder(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateExpireOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) CountOrderCompletedByCreatedTimeAndDriver(i0 context.Context, i1 time.Time, i2 time.Time, i3 []string) (int, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.CountOrderCompletedByCreatedTimeAndDriver")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountOrderCompletedByCreatedTimeAndDriver(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.CountOrderCompletedByCreatedTimeAndDriver")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) CountOrderCompletedByCompletedTimeAndDriver(i0 context.Context, i1 time.Time, i2 time.Time, i3 []string) (int, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.CountOrderCompletedByCompletedTimeAndDriver")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountOrderCompletedByCompletedTimeAndDriver(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.CountOrderCompletedByCompletedTimeAndDriver")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) CountAssigningDriverOrdersByTime(i0 context.Context, i1 time.Time) (int, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.CountAssigningDriverOrdersByTime")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountAssigningDriverOrdersByTime(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.CountAssigningDriverOrdersByTime")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) FindAssigningDriverOrdersByTime(i0 context.Context, i1 time.Time, i2 int, i3 int) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindAssigningDriverOrdersByTime")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindAssigningDriverOrdersByTime(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindAssigningDriverOrdersByTime")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) FindOrderCompletedByTimeAndDriver(i0 context.Context, i1 time.Time, i2 time.Time, i3 []string, i4 []string, i5 int, i6 int) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindOrderCompletedByTimeAndDriver")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindOrderCompletedByTimeAndDriver(i0, i1, i2, i3, i4, i5, i6)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindOrderCompletedByTimeAndDriver")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) FindTodayCompleteOrderByDriver(i0 context.Context, i1 []string, i2 *time.Location) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindTodayCompleteOrderByDriver")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindTodayCompleteOrderByDriver(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindTodayCompleteOrderByDriver")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) IsExist(i0 context.Context, i1 string) bool {

	_, span := p.Tracer.Start(i0, "OrderRepository.IsExist")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.IsExist(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.IsExist")

	return r0
}

func (p *ProxyOrderRepository) RemoveCompletedOrderByDriverID(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.RemoveCompletedOrderByDriverID")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.RemoveCompletedOrderByDriverID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.RemoveCompletedOrderByDriverID")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) CountCancelOrderByCancelDetail(i0 context.Context, i1 string, i2 time.Time, i3 string, i4 string, i5 string, i6 string) (int, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.CountCancelOrderByCancelDetail")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountCancelOrderByCancelDetail(i0, i1, i2, i3, i4, i5, i6)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.CountCancelOrderByCancelDetail")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) GroupCancelOrderByDriverID(i0 context.Context, i1 string, i2 []string, i3 string) ([]model.CountCancelOrderType, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GroupCancelOrderByDriverID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GroupCancelOrderByDriverID(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GroupCancelOrderByDriverID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) GetDriverLastAttempt(i0 context.Context, i1 string) (*model.LastAttempt, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetDriverLastAttempt")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDriverLastAttempt(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetDriverLastAttempt")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) SetDriverLastAttempt(i0 context.Context, i1 string, i2 string, i3 time.Duration) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetDriverLastAttempt")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDriverLastAttempt(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetDriverLastAttempt")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetOrderShifts(i0 context.Context, i1 *model.Order, i2 []model.OrderShift) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetOrderShifts")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetOrderShifts(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetOrderShifts")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetPrediction(i0 context.Context, i1 string, i2 model.PredictionFeatures) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetPrediction")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetPrediction(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetPrediction")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetDeliveryPhoto(i0 context.Context, i1 string, i2 []string, i3 string, i4 int) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetDeliveryPhoto")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDeliveryPhoto(i0, i1, i2, i3, i4)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetDeliveryPhoto")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetMOSaving(i0 context.Context, i1 model.Order, i2 float64) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetMOSaving")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetMOSaving(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetMOSaving")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) CountValidOrder(i0 context.Context, i1 []string, i2 []model.Service, i3 time.Time, i4 time.Time) (map[string]int, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.CountValidOrder")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.CountValidOrder(i0, i1, i2, i3, i4)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.CountValidOrder")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) SetVerifiedRiderPhotoCompareFace(i0 context.Context, i1 string, i2 model.VerifiedRiderPhotoUrl) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetVerifiedRiderPhotoCompareFace")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetVerifiedRiderPhotoCompareFace(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetVerifiedRiderPhotoCompareFace")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UpdateVerifiedRiderPhoto(i0 context.Context, i1 string, i2 string, i3 []model.VerifiedRiderPhotoUrl) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateVerifiedRiderPhoto")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateVerifiedRiderPhoto(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateVerifiedRiderPhoto")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) AddRemark(i0 context.Context, i1 string, i2 string) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.AddRemark")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.AddRemark(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.AddRemark")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) FindDriverOrderAutoAssignedCompleted(i0 context.Context, i1 []string, i2 time.Time, i3 time.Time, i4 ...Option) (model.CountDriverAutoAssignedRecords, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindDriverOrderAutoAssignedCompleted")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindDriverOrderAutoAssignedCompleted(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindDriverOrderAutoAssignedCompleted")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) FindDriverOrderAutoAssigned(i0 context.Context, i1 []string, i2 time.Time, i3 time.Time, i4 ...Option) (model.CountDriverAutoAssignedRecords, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindDriverOrderAutoAssigned")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindDriverOrderAutoAssigned(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindDriverOrderAutoAssigned")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) SetDistance0(i0 context.Context, i1 string, i2 types.Distance, i3 model.DurationSecond) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetDistance0")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDistance0(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetDistance0")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetDistance0AndPriceSummary(i0 context.Context, i1 model.Order) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetDistance0AndPriceSummary")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDistance0AndPriceSummary(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetDistance0AndPriceSummary")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) GetCreationDetails(i0 context.Context, i1 []string, i2 ...Option) ([]model.OrderCreationDetails, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetCreationDetails")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetCreationDetails(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetCreationDetails")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) SetPredictedCompletedTime(i0 context.Context, i1 string, i2 time.Time, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetPredictedCompletedTime")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetPredictedCompletedTime(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetPredictedCompletedTime")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) PushRoutedUWT(i0 context.Context, i1 string, i2 time.Time, i3 int64, i4 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.PushRoutedUWT")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.PushRoutedUWT(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.PushRoutedUWT")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetAsDistributed(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetAsDistributed")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetAsDistributed(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetAsDistributed")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetPauseFlag(i0 context.Context, i1 string, i2 int, i3 model.Pause) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetPauseFlag")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetPauseFlag(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetPauseFlag")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UnsetPauseFlag(i0 context.Context, i1 string, i2 int, i3 model.Pause) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UnsetPauseFlag")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UnsetPauseFlag(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UnsetPauseFlag")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetDeferredOrder(i0 context.Context, i1 string, i2 time.Time, i3 time.Duration) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetDeferredOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDeferredOrder(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetDeferredOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetThrottledOrder(i0 context.Context, i1 string, i2 string, i3 *model.RedistributionState) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetThrottledOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetThrottledOrder(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetThrottledOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetThrottlingDeferredOrder(i0 context.Context, i1 string, i2 time.Time, i3 time.Duration) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetThrottlingDeferredOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetThrottlingDeferredOrder(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetThrottlingDeferredOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetDriverAction(i0 context.Context, i1 string, i2 model.DriverAction, i3 time.Time) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetDriverAction")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDriverAction(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetDriverAction")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetActualAssigningAtFromNil(i0 context.Context, i1 string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetActualAssigningAtFromNil")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetActualAssigningAtFromNil(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetActualAssigningAtFromNil")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetSaversExperimentRecords(i0 context.Context, i1 string, i2 int, i3 []model.SaversExperimentRecord) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetSaversExperimentRecords")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetSaversExperimentRecords(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetSaversExperimentRecords")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetLeavePrevStopAt(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetLeavePrevStopAt")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetLeavePrevStopAt(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetLeavePrevStopAt")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetPredictionDeferTime(i0 context.Context, i1 string, i2 time.Time, i3 time.Time, i4 time.Time, i5 time.Duration) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetPredictionDeferTime")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetPredictionDeferTime(i0, i1, i2, i3, i4, i5)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetPredictionDeferTime")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) GetOtherActiveMP(i0 context.Context, i1 model.Order) (*model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetOtherActiveMP")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetOtherActiveMP(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetOtherActiveMP")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) SetProcessedBySingleDistribution(i0 context.Context, i1 string, i2 bool, i3 model.SearchRiderStrategy) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetProcessedBySingleDistribution")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetProcessedBySingleDistribution(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetProcessedBySingleDistribution")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UpdateOrderByField(i0 context.Context, i1 string, i2 model.OrderUpdater, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateOrderByField")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateOrderByField(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateOrderByField")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) UpdateOrderRevisionByField(i0 context.Context, i1 string, i2 int, i3 model.OrderUpdater, i4 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateOrderRevisionByField")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateOrderRevisionByField(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateOrderRevisionByField")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetDeferDuration(i0 context.Context, i1 string, i2 time.Duration) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetDeferDuration")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetDeferDuration(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetDeferDuration")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) GetDriversOfOtherMPs(i0 context.Context, i1 []model.Order) ([]DriverMP, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.GetDriversOfOtherMPs")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDriversOfOtherMPs(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.GetDriversOfOtherMPs")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) SetCancelDetailAndCancelDetailLog(i0 context.Context, i1 string, i2 model.Order, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetCancelDetailAndCancelDetailLog")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetCancelDetailAndCancelDetailLog(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetCancelDetailAndCancelDetailLog")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) FindWithSelector(i0 context.Context, i1 interface{}, i2 interface{}, i3 ...Option) ([]model.Order, error) {

	_, span := p.Tracer.Start(i0, "OrderRepository.FindWithSelector")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindWithSelector(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.FindWithSelector")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyOrderRepository) UpdateSkipPauseQRPayment(i0 context.Context, i1 []string, i2 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.UpdateSkipPauseQRPayment")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateSkipPauseQRPayment(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.UpdateSkipPauseQRPayment")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetCellPhoneContact(i0 context.Context, i1 string, i2 string, i3 time.Time, i4 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetCellPhoneContact")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetCellPhoneContact(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetCellPhoneContact")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetZeroFullTimeDriverOrderRevision(i0 context.Context, i1 string, i2 string, i3 int, i4 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetZeroFullTimeDriverOrderRevision")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetZeroFullTimeDriverOrderRevision(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetZeroFullTimeDriverOrderRevision")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetQRPromptPayInfo(i0 context.Context, i1 model.Order, i2 model.QRPromptPayInfo, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetQRPromptPayInfo")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetQRPromptPayInfo(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetQRPromptPayInfo")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetHasAnotherMPOrder(i0 context.Context, i1 string, i2 *bool, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetHasAnotherMPOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetHasAnotherMPOrder(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetHasAnotherMPOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetRedistributionState(i0 context.Context, i1 string, i2 *model.RedistributionState, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetRedistributionState")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetRedistributionState(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetRedistributionState")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetIsTriedForceAssignMP(i0 context.Context, i1 string, i2 bool) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetIsTriedForceAssignMP")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetIsTriedForceAssignMP(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetIsTriedForceAssignMP")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetLatestSyncCompletionTime(i0 context.Context, i1 string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetLatestSyncCompletionTime")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetLatestSyncCompletionTime(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetLatestSyncCompletionTime")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyOrderRepository) SetMemoTranslation(i0 context.Context, i1 string, i2 int, i3 string, i4 model.TranslationStatus, i5 ...Option) error {

	_, span := p.Tracer.Start(i0, "OrderRepository.SetMemoTranslation")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.SetMemoTranslation(i0, i1, i2, i3, i4, i5...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "OrderRepository.SetMemoTranslation")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
