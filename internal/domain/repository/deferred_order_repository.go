package repository

//go:generate mockgen -source=./deferred_order_repository.go -destination=./mock_repository/mock_deferred_order_repository.go -package=mock_repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type DeferredOrderRepository interface {
	InsertOrder(ctx context.Context, order model.Order, opt model.DeferredOrderOption) error
	FindUnProcessOrder(ctx context.Context, pickingTime time.Time, retroTime time.Time, retryLimit int) ([]model.DeferredOrder, error)
	MarkProcessedOrders(ctx context.Context, orderIds []string, retryLimit int) error
	MarkSuccessOrders(ctx context.Context, orderIds []string) error
	MarkFailedOrders(ctx context.Context, orderIds []string) error
	UpdateDistributionTime(ctx context.Context, orderIds []string, distributionTime time.Time) error
	IsUnprocessedExist(ctx context.Context, orderId string) (bool, error)
}
