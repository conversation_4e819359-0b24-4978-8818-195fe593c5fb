package repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

//go:generate mockgen -source=./counter_repository.go -destination=./mock_repository/mock_counter_repository.go -package=mock_repository

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type CounterRepository interface {
	GetNextSequence(ctx context.Context, cn model.CounterName) (int64, error)
}
