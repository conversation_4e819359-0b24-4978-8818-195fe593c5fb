package repository

//go:generate mockgen -source=./matching_rate_heatmap_repository.go -destination=./mock_repository/mock_matching_rate_heatmap_repository.go -package=mock_repository

import (
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type MatchingRateHeatMapRepository interface {
	Find(query HeatMapQuery, opts ...Option) ([]model.MatchingRateHeatMap, error)
	SaveAll(hm []model.MatchingRateHeatMap) error
	RemoveAll() error
}
