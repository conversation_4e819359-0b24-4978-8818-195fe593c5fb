package repository

//go:generate mockgen -source=./bulk_process_info_repository.go -destination=./mock_repository/mock_bulk_process_info_repository.go -package=mock_repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type BulkProcessInfoRepository interface {
	Get(ctx context.Context, q Query, opts ...Option) ([]model.BulkProcessInfo, error)
	Create(ctx context.Context, info *model.BulkProcessInfo, opts ...Option) error
	Update(ctx context.Context, info *model.BulkProcessInfo, opts ...Option) error
}
