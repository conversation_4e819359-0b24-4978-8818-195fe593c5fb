// Code generated by MockGen. DO NOT EDIT.
// Source: ./minimal_order_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockMinimalOrderRepository is a mock of MinimalOrderRepository interface.
type MockMinimalOrderRepository struct {
	ctrl     *gomock.Controller
	recorder *MockMinimalOrderRepositoryMockRecorder
}

// MockMinimalOrderRepositoryMockRecorder is the mock recorder for MockMinimalOrderRepository.
type MockMinimalOrderRepositoryMockRecorder struct {
	mock *MockMinimalOrderRepository
}

// NewMockMinimalOrderRepository creates a new mock instance.
func NewMockMinimalOrderRepository(ctrl *gomock.Controller) *MockMinimalOrderRepository {
	mock := &MockMinimalOrderRepository{ctrl: ctrl}
	mock.recorder = &MockMinimalOrderRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMinimalOrderRepository) EXPECT() *MockMinimalOrderRepositoryMockRecorder {
	return m.recorder
}

// Find mocks base method.
func (m *MockMinimalOrderRepository) Find(ctx context.Context, query interface{}, skip, limit int, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockMinimalOrderRepositoryMockRecorder) Find(ctx, query, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockMinimalOrderRepository)(nil).Find), varargs...)
}
