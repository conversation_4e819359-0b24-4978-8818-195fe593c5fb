// Code generated by MockGen. DO NOT EDIT.
// Source: ./product_group_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
)

// MockProductGroupQuery is a mock of ProductGroupQuery interface.
type MockProductGroupQuery struct {
	ctrl     *gomock.Controller
	recorder *MockProductGroupQueryMockRecorder
}

// MockProductGroupQueryMockRecorder is the mock recorder for MockProductGroupQuery.
type MockProductGroupQueryMockRecorder struct {
	mock *MockProductGroupQuery
}

// NewMockProductGroupQuery creates a new mock instance.
func NewMockProductGroupQuery(ctrl *gomock.Controller) *MockProductGroupQuery {
	mock := &MockProductGroupQuery{ctrl: ctrl}
	mock.recorder = &MockProductGroupQueryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProductGroupQuery) EXPECT() *MockProductGroupQueryMockRecorder {
	return m.recorder
}

// WithEnd mocks base method.
func (m *MockProductGroupQuery) WithEnd(end time.Time) repository.ProductGroupQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithEnd", end)
	ret0, _ := ret[0].(repository.ProductGroupQuery)
	return ret0
}

// WithEnd indicates an expected call of WithEnd.
func (mr *MockProductGroupQueryMockRecorder) WithEnd(end interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithEnd", reflect.TypeOf((*MockProductGroupQuery)(nil).WithEnd), end)
}

// WithIDs mocks base method.
func (m *MockProductGroupQuery) WithIDs(ids []primitive.ObjectID) repository.ProductGroupQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithIDs", ids)
	ret0, _ := ret[0].(repository.ProductGroupQuery)
	return ret0
}

// WithIDs indicates an expected call of WithIDs.
func (mr *MockProductGroupQueryMockRecorder) WithIDs(ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithIDs", reflect.TypeOf((*MockProductGroupQuery)(nil).WithIDs), ids)
}

// WithName mocks base method.
func (m *MockProductGroupQuery) WithName(name string) repository.ProductGroupQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithName", name)
	ret0, _ := ret[0].(repository.ProductGroupQuery)
	return ret0
}

// WithName indicates an expected call of WithName.
func (mr *MockProductGroupQueryMockRecorder) WithName(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithName", reflect.TypeOf((*MockProductGroupQuery)(nil).WithName), name)
}

// WithPrioritySort mocks base method.
func (m *MockProductGroupQuery) WithPrioritySort(sort string) repository.ProductGroupQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithPrioritySort", sort)
	ret0, _ := ret[0].(repository.ProductGroupQuery)
	return ret0
}

// WithPrioritySort indicates an expected call of WithPrioritySort.
func (mr *MockProductGroupQueryMockRecorder) WithPrioritySort(sort interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithPrioritySort", reflect.TypeOf((*MockProductGroupQuery)(nil).WithPrioritySort), sort)
}

// WithStart mocks base method.
func (m *MockProductGroupQuery) WithStart(start time.Time) repository.ProductGroupQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithStart", start)
	ret0, _ := ret[0].(repository.ProductGroupQuery)
	return ret0
}

// WithStart indicates an expected call of WithStart.
func (mr *MockProductGroupQueryMockRecorder) WithStart(start interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithStart", reflect.TypeOf((*MockProductGroupQuery)(nil).WithStart), start)
}

// MockProductGroupRepository is a mock of ProductGroupRepository interface.
type MockProductGroupRepository struct {
	ctrl     *gomock.Controller
	recorder *MockProductGroupRepositoryMockRecorder
}

// MockProductGroupRepositoryMockRecorder is the mock recorder for MockProductGroupRepository.
type MockProductGroupRepositoryMockRecorder struct {
	mock *MockProductGroupRepository
}

// NewMockProductGroupRepository creates a new mock instance.
func NewMockProductGroupRepository(ctrl *gomock.Controller) *MockProductGroupRepository {
	mock := &MockProductGroupRepository{ctrl: ctrl}
	mock.recorder = &MockProductGroupRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProductGroupRepository) EXPECT() *MockProductGroupRepositoryMockRecorder {
	return m.recorder
}

// CountProductGroupWithQuery mocks base method.
func (m *MockProductGroupRepository) CountProductGroupWithQuery(ctx context.Context, query repository.ProductGroupQuery, opts ...repository.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CountProductGroupWithQuery", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountProductGroupWithQuery indicates an expected call of CountProductGroupWithQuery.
func (mr *MockProductGroupRepositoryMockRecorder) CountProductGroupWithQuery(ctx, query interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountProductGroupWithQuery", reflect.TypeOf((*MockProductGroupRepository)(nil).CountProductGroupWithQuery), varargs...)
}

// CreateAll mocks base method.
func (m *MockProductGroupRepository) CreateAll(ctx context.Context, productGroups []model.ProductGroup, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, productGroups}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAll", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAll indicates an expected call of CreateAll.
func (mr *MockProductGroupRepositoryMockRecorder) CreateAll(ctx, productGroups interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, productGroups}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAll", reflect.TypeOf((*MockProductGroupRepository)(nil).CreateAll), varargs...)
}

// FindWithQueryAndSort mocks base method.
func (m *MockProductGroupRepository) FindWithQueryAndSort(ctx context.Context, query repository.ProductGroupQuery, skip, limit int, sort []string, opts ...repository.Option) ([]model.ProductGroup, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit, sort}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindWithQueryAndSort", varargs...)
	ret0, _ := ret[0].([]model.ProductGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindWithQueryAndSort indicates an expected call of FindWithQueryAndSort.
func (mr *MockProductGroupRepositoryMockRecorder) FindWithQueryAndSort(ctx, query, skip, limit, sort interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit, sort}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindWithQueryAndSort", reflect.TypeOf((*MockProductGroupRepository)(nil).FindWithQueryAndSort), varargs...)
}

// GetById mocks base method.
func (m *MockProductGroupRepository) GetById(ctx context.Context, id string, opts ...repository.Option) (*model.ProductGroup, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetById", varargs...)
	ret0, _ := ret[0].(*model.ProductGroup)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetById indicates an expected call of GetById.
func (mr *MockProductGroupRepositoryMockRecorder) GetById(ctx, id interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetById", reflect.TypeOf((*MockProductGroupRepository)(nil).GetById), varargs...)
}

// IsExists mocks base method.
func (m *MockProductGroupRepository) IsExists(ctx context.Context, productGroupID primitive.ObjectID, opts ...repository.Option) bool {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, productGroupID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsExists", varargs...)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsExists indicates an expected call of IsExists.
func (mr *MockProductGroupRepositoryMockRecorder) IsExists(ctx, productGroupID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, productGroupID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExists", reflect.TypeOf((*MockProductGroupRepository)(nil).IsExists), varargs...)
}

// Update mocks base method.
func (m *MockProductGroupRepository) Update(ctx context.Context, srcProductGroup model.ProductGroup) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, srcProductGroup)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockProductGroupRepositoryMockRecorder) Update(ctx, srcProductGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockProductGroupRepository)(nil).Update), ctx, srcProductGroup)
}
