// Code generated by MockGen. DO NOT EDIT.
// Source: ./quote_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockQuoteRepository is a mock of QuoteRepository interface.
type MockQuoteRepository struct {
	ctrl     *gomock.Controller
	recorder *MockQuoteRepositoryMockRecorder
}

// MockQuoteRepositoryMockRecorder is the mock recorder for MockQuoteRepository.
type MockQuoteRepositoryMockRecorder struct {
	mock *MockQuoteRepository
}

// NewMockQuoteRepository creates a new mock instance.
func NewMockQuoteRepository(ctrl *gomock.Controller) *MockQuoteRepository {
	mock := &MockQuoteRepository{ctrl: ctrl}
	mock.recorder = &MockQuoteRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockQuoteRepository) EXPECT() *MockQuoteRepositoryMockRecorder {
	return m.recorder
}

// CreateOrUpdateQuote mocks base method.
func (m *MockQuoteRepository) CreateOrUpdateQuote(arg0 context.Context, arg1 *model.Quote) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateQuote", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateQuote indicates an expected call of CreateOrUpdateQuote.
func (mr *MockQuoteRepositoryMockRecorder) CreateOrUpdateQuote(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateQuote", reflect.TypeOf((*MockQuoteRepository)(nil).CreateOrUpdateQuote), arg0, arg1)
}

// Find mocks base method.
func (m *MockQuoteRepository) Find(ctx context.Context, qid string, opts ...repository.Option) (*model.Quote, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, qid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].(*model.Quote)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockQuoteRepositoryMockRecorder) Find(ctx, qid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, qid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockQuoteRepository)(nil).Find), varargs...)
}
