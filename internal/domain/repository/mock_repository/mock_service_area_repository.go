// Code generated by MockGen. DO NOT EDIT.
// Source: ./service_area_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
	bson "go.mongodb.org/mongo-driver/bson"
)

// MockServiceAreaRepository is a mock of ServiceAreaRepository interface.
type MockServiceAreaRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServiceAreaRepositoryMockRecorder
}

// MockServiceAreaRepositoryMockRecorder is the mock recorder for MockServiceAreaRepository.
type MockServiceAreaRepositoryMockRecorder struct {
	mock *MockServiceAreaRepository
}

// NewMockServiceAreaRepository creates a new mock instance.
func NewMockServiceAreaRepository(ctrl *gomock.Controller) *MockServiceAreaRepository {
	mock := &MockServiceAreaRepository{ctrl: ctrl}
	mock.recorder = &MockServiceAreaRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceAreaRepository) EXPECT() *MockServiceAreaRepositoryMockRecorder {
	return m.recorder
}

// CountWithQuery mocks base method.
func (m *MockServiceAreaRepository) CountWithQuery(ctx context.Context, query repository.ServiceAreaQuery, opts ...repository.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CountWithQuery", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountWithQuery indicates an expected call of CountWithQuery.
func (mr *MockServiceAreaRepositoryMockRecorder) CountWithQuery(ctx, query interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountWithQuery", reflect.TypeOf((*MockServiceAreaRepository)(nil).CountWithQuery), varargs...)
}

// Create mocks base method.
func (m *MockServiceAreaRepository) Create(ctx context.Context, model *model.ServiceArea) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, model)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockServiceAreaRepositoryMockRecorder) Create(ctx, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockServiceAreaRepository)(nil).Create), ctx, model)
}

// Delete mocks base method.
func (m *MockServiceAreaRepository) Delete(ctx context.Context, model *model.ServiceArea) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Delete", ctx, model)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockServiceAreaRepositoryMockRecorder) Delete(ctx, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockServiceAreaRepository)(nil).Delete), ctx, model)
}

// FindByRegions mocks base method.
func (m *MockServiceAreaRepository) FindByRegions(ctx context.Context, regions []string) ([]model.ServiceArea, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByRegions", ctx, regions)
	ret0, _ := ret[0].([]model.ServiceArea)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByRegions indicates an expected call of FindByRegions.
func (mr *MockServiceAreaRepositoryMockRecorder) FindByRegions(ctx, regions interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByRegions", reflect.TypeOf((*MockServiceAreaRepository)(nil).FindByRegions), ctx, regions)
}

// FindWithQueryAndSort mocks base method.
func (m *MockServiceAreaRepository) FindWithQueryAndSort(ctx context.Context, query repository.ServiceAreaQuery, skip, limit int, sort []string, opts ...repository.Option) ([]model.ServiceArea, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit, sort}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindWithQueryAndSort", varargs...)
	ret0, _ := ret[0].([]model.ServiceArea)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindWithQueryAndSort indicates an expected call of FindWithQueryAndSort.
func (mr *MockServiceAreaRepositoryMockRecorder) FindWithQueryAndSort(ctx, query, skip, limit, sort interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit, sort}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindWithQueryAndSort", reflect.TypeOf((*MockServiceAreaRepository)(nil).FindWithQueryAndSort), varargs...)
}

// Get mocks base method.
func (m *MockServiceAreaRepository) Get(ctx context.Context, id string) (*model.ServiceArea, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*model.ServiceArea)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockServiceAreaRepositoryMockRecorder) Get(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockServiceAreaRepository)(nil).Get), ctx, id)
}

// GetAutoAssignRegion mocks base method.
func (m *MockServiceAreaRepository) GetAutoAssignRegion(ctx context.Context) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetAutoAssignRegion", ctx)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetAutoAssignRegion indicates an expected call of GetAutoAssignRegion.
func (mr *MockServiceAreaRepositoryMockRecorder) GetAutoAssignRegion(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetAutoAssignRegion", reflect.TypeOf((*MockServiceAreaRepository)(nil).GetAutoAssignRegion), ctx)
}

// GetByRegion mocks base method.
func (m *MockServiceAreaRepository) GetByRegion(ctx context.Context, region string) (*model.ServiceArea, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRegion", ctx, region)
	ret0, _ := ret[0].(*model.ServiceArea)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRegion indicates an expected call of GetByRegion.
func (mr *MockServiceAreaRepositoryMockRecorder) GetByRegion(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRegion", reflect.TypeOf((*MockServiceAreaRepository)(nil).GetByRegion), ctx, region)
}

// GetByRegionWithoutCache mocks base method.
func (m *MockServiceAreaRepository) GetByRegionWithoutCache(ctx context.Context, region string) (*model.ServiceArea, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByRegionWithoutCache", ctx, region)
	ret0, _ := ret[0].(*model.ServiceArea)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRegionWithoutCache indicates an expected call of GetByRegionWithoutCache.
func (mr *MockServiceAreaRepositoryMockRecorder) GetByRegionWithoutCache(ctx, region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRegionWithoutCache", reflect.TypeOf((*MockServiceAreaRepository)(nil).GetByRegionWithoutCache), ctx, region)
}

// SetShiftDriverIDs mocks base method.
func (m *MockServiceAreaRepository) SetShiftDriverIDs(ctx context.Context, serviceAreaID model.ServiceAreaID, region string, shiftDriverIDs []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetShiftDriverIDs", ctx, serviceAreaID, region, shiftDriverIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetShiftDriverIDs indicates an expected call of SetShiftDriverIDs.
func (mr *MockServiceAreaRepositoryMockRecorder) SetShiftDriverIDs(ctx, serviceAreaID, region, shiftDriverIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetShiftDriverIDs", reflect.TypeOf((*MockServiceAreaRepository)(nil).SetShiftDriverIDs), ctx, serviceAreaID, region, shiftDriverIDs)
}

// Update mocks base method.
func (m *MockServiceAreaRepository) Update(ctx context.Context, model *model.ServiceArea) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, model)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockServiceAreaRepositoryMockRecorder) Update(ctx, model interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockServiceAreaRepository)(nil).Update), ctx, model)
}

// UpdateSet mocks base method.
func (m *MockServiceAreaRepository) UpdateSet(ctx context.Context, region string, set bson.M) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSet", ctx, region, set)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSet indicates an expected call of UpdateSet.
func (mr *MockServiceAreaRepositoryMockRecorder) UpdateSet(ctx, region, set interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSet", reflect.TypeOf((*MockServiceAreaRepository)(nil).UpdateSet), ctx, region, set)
}
