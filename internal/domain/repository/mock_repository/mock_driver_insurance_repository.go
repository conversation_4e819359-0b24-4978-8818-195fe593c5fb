// Code generated by MockGen. DO NOT EDIT.
// Source: ./driver_insurance_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
	mongo "go.mongodb.org/mongo-driver/mongo"
)

// MockDriverInsuranceQuery is a mock of DriverInsuranceQuery interface.
type MockDriverInsuranceQuery struct {
	ctrl     *gomock.Controller
	recorder *MockDriverInsuranceQueryMockRecorder
}

// MockDriverInsuranceQueryMockRecorder is the mock recorder for MockDriverInsuranceQuery.
type MockDriverInsuranceQueryMockRecorder struct {
	mock *MockDriverInsuranceQuery
}

// NewMockDriverInsuranceQuery creates a new mock instance.
func NewMockDriverInsuranceQuery(ctrl *gomock.Controller) *MockDriverInsuranceQuery {
	mock := &MockDriverInsuranceQuery{ctrl: ctrl}
	mock.recorder = &MockDriverInsuranceQueryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverInsuranceQuery) EXPECT() *MockDriverInsuranceQueryMockRecorder {
	return m.recorder
}

// WithCreatedAtAfter mocks base method.
func (m *MockDriverInsuranceQuery) WithCreatedAtAfter(createdAt time.Time) repository.DriverInsuranceQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithCreatedAtAfter", createdAt)
	ret0, _ := ret[0].(repository.DriverInsuranceQuery)
	return ret0
}

// WithCreatedAtAfter indicates an expected call of WithCreatedAtAfter.
func (mr *MockDriverInsuranceQueryMockRecorder) WithCreatedAtAfter(createdAt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithCreatedAtAfter", reflect.TypeOf((*MockDriverInsuranceQuery)(nil).WithCreatedAtAfter), createdAt)
}

// WithDriverID mocks base method.
func (m *MockDriverInsuranceQuery) WithDriverID(driverIds ...string) repository.DriverInsuranceQuery {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range driverIds {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WithDriverID", varargs...)
	ret0, _ := ret[0].(repository.DriverInsuranceQuery)
	return ret0
}

// WithDriverID indicates an expected call of WithDriverID.
func (mr *MockDriverInsuranceQueryMockRecorder) WithDriverID(driverIds ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithDriverID", reflect.TypeOf((*MockDriverInsuranceQuery)(nil).WithDriverID), driverIds...)
}

// WithID mocks base method.
func (m *MockDriverInsuranceQuery) WithID(ids ...string) repository.DriverInsuranceQuery {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range ids {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WithID", varargs...)
	ret0, _ := ret[0].(repository.DriverInsuranceQuery)
	return ret0
}

// WithID indicates an expected call of WithID.
func (mr *MockDriverInsuranceQueryMockRecorder) WithID(ids ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithID", reflect.TypeOf((*MockDriverInsuranceQuery)(nil).WithID), ids...)
}

// WithImagesUploadResult mocks base method.
func (m *MockDriverInsuranceQuery) WithImagesUploadResult(result bool) repository.DriverInsuranceQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithImagesUploadResult", result)
	ret0, _ := ret[0].(repository.DriverInsuranceQuery)
	return ret0
}

// WithImagesUploadResult indicates an expected call of WithImagesUploadResult.
func (mr *MockDriverInsuranceQueryMockRecorder) WithImagesUploadResult(result interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithImagesUploadResult", reflect.TypeOf((*MockDriverInsuranceQuery)(nil).WithImagesUploadResult), result)
}

// WithMonthCycle mocks base method.
func (m *MockDriverInsuranceQuery) WithMonthCycle(monthCycle string) repository.DriverInsuranceQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithMonthCycle", monthCycle)
	ret0, _ := ret[0].(repository.DriverInsuranceQuery)
	return ret0
}

// WithMonthCycle indicates an expected call of WithMonthCycle.
func (mr *MockDriverInsuranceQueryMockRecorder) WithMonthCycle(monthCycle interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithMonthCycle", reflect.TypeOf((*MockDriverInsuranceQuery)(nil).WithMonthCycle), monthCycle)
}

// WithProgram mocks base method.
func (m *MockDriverInsuranceQuery) WithProgram(insuranceProgram ...model.DriverInsuranceProgram) repository.DriverInsuranceQuery {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range insuranceProgram {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WithProgram", varargs...)
	ret0, _ := ret[0].(repository.DriverInsuranceQuery)
	return ret0
}

// WithProgram indicates an expected call of WithProgram.
func (mr *MockDriverInsuranceQueryMockRecorder) WithProgram(insuranceProgram ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithProgram", reflect.TypeOf((*MockDriverInsuranceQuery)(nil).WithProgram), insuranceProgram...)
}

// WithStatus mocks base method.
func (m *MockDriverInsuranceQuery) WithStatus(insuranceStatus ...model.DriverInsuranceStatus) repository.DriverInsuranceQuery {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range insuranceStatus {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WithStatus", varargs...)
	ret0, _ := ret[0].(repository.DriverInsuranceQuery)
	return ret0
}

// WithStatus indicates an expected call of WithStatus.
func (mr *MockDriverInsuranceQueryMockRecorder) WithStatus(insuranceStatus ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithStatus", reflect.TypeOf((*MockDriverInsuranceQuery)(nil).WithStatus), insuranceStatus...)
}

// WithType mocks base method.
func (m *MockDriverInsuranceQuery) WithType(insuraceType ...model.DriverInsuranceType) repository.DriverInsuranceQuery {
	m.ctrl.T.Helper()
	varargs := []interface{}{}
	for _, a := range insuraceType {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "WithType", varargs...)
	ret0, _ := ret[0].(repository.DriverInsuranceQuery)
	return ret0
}

// WithType indicates an expected call of WithType.
func (mr *MockDriverInsuranceQueryMockRecorder) WithType(insuraceType ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithType", reflect.TypeOf((*MockDriverInsuranceQuery)(nil).WithType), insuraceType...)
}

// MockDriverInsuranceRepository is a mock of DriverInsuranceRepository interface.
type MockDriverInsuranceRepository struct {
	ctrl     *gomock.Controller
	recorder *MockDriverInsuranceRepositoryMockRecorder
}

// MockDriverInsuranceRepositoryMockRecorder is the mock recorder for MockDriverInsuranceRepository.
type MockDriverInsuranceRepositoryMockRecorder struct {
	mock *MockDriverInsuranceRepository
}

// NewMockDriverInsuranceRepository creates a new mock instance.
func NewMockDriverInsuranceRepository(ctrl *gomock.Controller) *MockDriverInsuranceRepository {
	mock := &MockDriverInsuranceRepository{ctrl: ctrl}
	mock.recorder = &MockDriverInsuranceRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverInsuranceRepository) EXPECT() *MockDriverInsuranceRepositoryMockRecorder {
	return m.recorder
}

// BulkUpdateActivePolicyNumber mocks base method.
func (m *MockDriverInsuranceRepository) BulkUpdateActivePolicyNumber(ctx *gin.Context, monthCycle string, period model.PeriodOfInsurance, insuranceType model.DriverInsuranceType, policyStatusInfos []model.PolicyStatusInfo) (*mongo.BulkWriteResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkUpdateActivePolicyNumber", ctx, monthCycle, period, insuranceType, policyStatusInfos)
	ret0, _ := ret[0].(*mongo.BulkWriteResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkUpdateActivePolicyNumber indicates an expected call of BulkUpdateActivePolicyNumber.
func (mr *MockDriverInsuranceRepositoryMockRecorder) BulkUpdateActivePolicyNumber(ctx, monthCycle, period, insuranceType, policyStatusInfos interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkUpdateActivePolicyNumber", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).BulkUpdateActivePolicyNumber), ctx, monthCycle, period, insuranceType, policyStatusInfos)
}

// BulkUpdateRejectPolicyNumber mocks base method.
func (m *MockDriverInsuranceRepository) BulkUpdateRejectPolicyNumber(ctx *gin.Context, monthCycle string, insuranceType model.DriverInsuranceType, policyStatusInfos []model.PolicyStatusInfo) (*mongo.BulkWriteResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BulkUpdateRejectPolicyNumber", ctx, monthCycle, insuranceType, policyStatusInfos)
	ret0, _ := ret[0].(*mongo.BulkWriteResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BulkUpdateRejectPolicyNumber indicates an expected call of BulkUpdateRejectPolicyNumber.
func (mr *MockDriverInsuranceRepositoryMockRecorder) BulkUpdateRejectPolicyNumber(ctx, monthCycle, insuranceType, policyStatusInfos interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkUpdateRejectPolicyNumber", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).BulkUpdateRejectPolicyNumber), ctx, monthCycle, insuranceType, policyStatusInfos)
}

// CreateAll mocks base method.
func (m *MockDriverInsuranceRepository) CreateAll(ctx context.Context, insurances ...model.DriverInsurance) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range insurances {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAll", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAll indicates an expected call of CreateAll.
func (mr *MockDriverInsuranceRepositoryMockRecorder) CreateAll(ctx interface{}, insurances ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, insurances...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAll", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).CreateAll), varargs...)
}

// FindConsolidateData mocks base method.
func (m *MockDriverInsuranceRepository) FindConsolidateData(ctx context.Context, query repository.DriverInsuranceQuery, skip, limit int, opts ...repository.Option) ([]model.DriverInsurance, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindConsolidateData", varargs...)
	ret0, _ := ret[0].([]model.DriverInsurance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindConsolidateData indicates an expected call of FindConsolidateData.
func (mr *MockDriverInsuranceRepositoryMockRecorder) FindConsolidateData(ctx, query, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindConsolidateData", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).FindConsolidateData), varargs...)
}

// FindWithQueryAndSort mocks base method.
func (m *MockDriverInsuranceRepository) FindWithQueryAndSort(ctx context.Context, query repository.DriverInsuranceQuery, skip, limit int, opts ...repository.Option) ([]model.DriverInsurance, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindWithQueryAndSort", varargs...)
	ret0, _ := ret[0].([]model.DriverInsurance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindWithQueryAndSort indicates an expected call of FindWithQueryAndSort.
func (mr *MockDriverInsuranceRepositoryMockRecorder) FindWithQueryAndSort(ctx, query, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindWithQueryAndSort", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).FindWithQueryAndSort), varargs...)
}

// ReplaceByID mocks base method.
func (m *MockDriverInsuranceRepository) ReplaceByID(ctx context.Context, insurances model.DriverInsurance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ReplaceByID", ctx, insurances)
	ret0, _ := ret[0].(error)
	return ret0
}

// ReplaceByID indicates an expected call of ReplaceByID.
func (mr *MockDriverInsuranceRepositoryMockRecorder) ReplaceByID(ctx, insurances interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ReplaceByID", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).ReplaceByID), ctx, insurances)
}

// UpdateStatusByDriverIDs mocks base method.
func (m *MockDriverInsuranceRepository) UpdateStatusByDriverIDs(ctx context.Context, targetMonthCycle string, targetStatus model.DriverInsuranceStatus, driverIDs ...string) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, targetMonthCycle, targetStatus}
	for _, a := range driverIDs {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateStatusByDriverIDs", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateStatusByDriverIDs indicates an expected call of UpdateStatusByDriverIDs.
func (mr *MockDriverInsuranceRepositoryMockRecorder) UpdateStatusByDriverIDs(ctx, targetMonthCycle, targetStatus interface{}, driverIDs ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, targetMonthCycle, targetStatus}, driverIDs...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateStatusByDriverIDs", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).UpdateStatusByDriverIDs), varargs...)
}

// UpdateUploadImagesResult mocks base method.
func (m *MockDriverInsuranceRepository) UpdateUploadImagesResult(ctx *gin.Context, models []model.DriverInsurance, result model.ImagesUploadResult) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateUploadImagesResult", ctx, models, result)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateUploadImagesResult indicates an expected call of UpdateUploadImagesResult.
func (mr *MockDriverInsuranceRepositoryMockRecorder) UpdateUploadImagesResult(ctx, models, result interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateUploadImagesResult", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).UpdateUploadImagesResult), ctx, models, result)
}

// UpsertAll mocks base method.
func (m *MockDriverInsuranceRepository) UpsertAll(ctx context.Context, insurances ...model.DriverInsurance) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range insurances {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertAll", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertAll indicates an expected call of UpsertAll.
func (mr *MockDriverInsuranceRepositoryMockRecorder) UpsertAll(ctx interface{}, insurances ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, insurances...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAll", reflect.TypeOf((*MockDriverInsuranceRepository)(nil).UpsertAll), varargs...)
}
