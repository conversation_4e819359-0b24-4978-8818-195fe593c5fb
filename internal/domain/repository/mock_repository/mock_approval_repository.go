// Code generated by MockGen. DO NOT EDIT.
// Source: ./approval_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockApprovalRepository is a mock of ApprovalRepository interface.
type MockApprovalRepository struct {
	ctrl     *gomock.Controller
	recorder *MockApprovalRepositoryMockRecorder
}

// MockApprovalRepositoryMockRecorder is the mock recorder for MockApprovalRepository.
type MockApprovalRepositoryMockRecorder struct {
	mock *MockApprovalRepository
}

// NewMockApprovalRepository creates a new mock instance.
func NewMockApprovalRepository(ctrl *gomock.Controller) *MockApprovalRepository {
	mock := &MockApprovalRepository{ctrl: ctrl}
	mock.recorder = &MockApprovalRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockApprovalRepository) EXPECT() *MockApprovalRepositoryMockRecorder {
	return m.recorder
}

// Count mocks base method.
func (m *MockApprovalRepository) Count(ctx context.Context, query repository.ApprovalQuery) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", ctx, query)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockApprovalRepositoryMockRecorder) Count(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockApprovalRepository)(nil).Count), ctx, query)
}

// Create mocks base method.
func (m *MockApprovalRepository) Create(ctx context.Context, approval *model.Approval) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, approval)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockApprovalRepositoryMockRecorder) Create(ctx, approval interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockApprovalRepository)(nil).Create), ctx, approval)
}

// Find mocks base method.
func (m *MockApprovalRepository) Find(ctx context.Context, query repository.ApprovalQuery, skip, limit int) ([]model.Approval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Find", ctx, query, skip, limit)
	ret0, _ := ret[0].([]model.Approval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockApprovalRepositoryMockRecorder) Find(ctx, query, skip, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockApprovalRepository)(nil).Find), ctx, query, skip, limit)
}

// FindByVoidTxnID mocks base method.
func (m *MockApprovalRepository) FindByVoidTxnID(ctx context.Context, txnID string) ([]model.Approval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByVoidTxnID", ctx, txnID)
	ret0, _ := ret[0].([]model.Approval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByVoidTxnID indicates an expected call of FindByVoidTxnID.
func (mr *MockApprovalRepositoryMockRecorder) FindByVoidTxnID(ctx, txnID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByVoidTxnID", reflect.TypeOf((*MockApprovalRepository)(nil).FindByVoidTxnID), ctx, txnID)
}

// FindChargeApprovalByOrderID mocks base method.
func (m *MockApprovalRepository) FindChargeApprovalByOrderID(ctx context.Context, orderID string) ([]model.Approval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindChargeApprovalByOrderID", ctx, orderID)
	ret0, _ := ret[0].([]model.Approval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindChargeApprovalByOrderID indicates an expected call of FindChargeApprovalByOrderID.
func (mr *MockApprovalRepositoryMockRecorder) FindChargeApprovalByOrderID(ctx, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindChargeApprovalByOrderID", reflect.TypeOf((*MockApprovalRepository)(nil).FindChargeApprovalByOrderID), ctx, orderID)
}

// Get mocks base method.
func (m *MockApprovalRepository) Get(ctx context.Context, id string) (*model.Approval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, id)
	ret0, _ := ret[0].(*model.Approval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockApprovalRepositoryMockRecorder) Get(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockApprovalRepository)(nil).Get), ctx, id)
}

// Update mocks base method.
func (m *MockApprovalRepository) Update(ctx context.Context, approval *model.Approval) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, approval)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockApprovalRepositoryMockRecorder) Update(ctx, approval interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockApprovalRepository)(nil).Update), ctx, approval)
}
