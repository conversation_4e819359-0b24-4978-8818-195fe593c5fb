// Code generated by MockGen. DO NOT EDIT.
// Source: ./order_hearthbeat_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockOrderHeartbeatRepository is a mock of OrderHeartbeatRepository interface.
type MockOrderHeartbeatRepository struct {
	ctrl     *gomock.Controller
	recorder *MockOrderHeartbeatRepositoryMockRecorder
}

// MockOrderHeartbeatRepositoryMockRecorder is the mock recorder for MockOrderHeartbeatRepository.
type MockOrderHeartbeatRepositoryMockRecorder struct {
	mock *MockOrderHeartbeatRepository
}

// NewMockOrderHeartbeatRepository creates a new mock instance.
func NewMockOrderHeartbeatRepository(ctrl *gomock.Controller) *MockOrderHeartbeatRepository {
	mock := &MockOrderHeartbeatRepository{ctrl: ctrl}
	mock.recorder = &MockOrderHeartbeatRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderHeartbeatRepository) EXPECT() *MockOrderHeartbeatRepositoryMockRecorder {
	return m.recorder
}

// GetKeySlot mocks base method.
func (m *MockOrderHeartbeatRepository) GetKeySlot() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKeySlot")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetKeySlot indicates an expected call of GetKeySlot.
func (mr *MockOrderHeartbeatRepositoryMockRecorder) GetKeySlot() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKeySlot", reflect.TypeOf((*MockOrderHeartbeatRepository)(nil).GetKeySlot))
}

// GetUnhealthyOrderScore mocks base method.
func (m *MockOrderHeartbeatRepository) GetUnhealthyOrderScore(ctx context.Context, threshold time.Duration) (map[string]float64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnhealthyOrderScore", ctx, threshold)
	ret0, _ := ret[0].(map[string]float64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnhealthyOrderScore indicates an expected call of GetUnhealthyOrderScore.
func (mr *MockOrderHeartbeatRepositoryMockRecorder) GetUnhealthyOrderScore(ctx, threshold interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnhealthyOrderScore", reflect.TypeOf((*MockOrderHeartbeatRepository)(nil).GetUnhealthyOrderScore), ctx, threshold)
}

// GetUnhealthyOrders mocks base method.
func (m *MockOrderHeartbeatRepository) GetUnhealthyOrders(ctx context.Context, threshold time.Duration) ([]model.OrderHearthBeatStateWithOrderID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetUnhealthyOrders", ctx, threshold)
	ret0, _ := ret[0].([]model.OrderHearthBeatStateWithOrderID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUnhealthyOrders indicates an expected call of GetUnhealthyOrders.
func (mr *MockOrderHeartbeatRepositoryMockRecorder) GetUnhealthyOrders(ctx, threshold interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUnhealthyOrders", reflect.TypeOf((*MockOrderHeartbeatRepository)(nil).GetUnhealthyOrders), ctx, threshold)
}

// RemoveOrderHearthBeat mocks base method.
func (m *MockOrderHeartbeatRepository) RemoveOrderHearthBeat(ctx context.Context, orderIDs []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveOrderHearthBeat", ctx, orderIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveOrderHearthBeat indicates an expected call of RemoveOrderHearthBeat.
func (mr *MockOrderHeartbeatRepositoryMockRecorder) RemoveOrderHearthBeat(ctx, orderIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveOrderHearthBeat", reflect.TypeOf((*MockOrderHeartbeatRepository)(nil).RemoveOrderHearthBeat), ctx, orderIDs)
}

// RemoveOrderRedistributionState mocks base method.
func (m *MockOrderHeartbeatRepository) RemoveOrderRedistributionState(ctx context.Context, orderIDs []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveOrderRedistributionState", ctx, orderIDs)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveOrderRedistributionState indicates an expected call of RemoveOrderRedistributionState.
func (mr *MockOrderHeartbeatRepositoryMockRecorder) RemoveOrderRedistributionState(ctx, orderIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveOrderRedistributionState", reflect.TypeOf((*MockOrderHeartbeatRepository)(nil).RemoveOrderRedistributionState), ctx, orderIDs)
}

// UpdateHearthBeat mocks base method.
func (m *MockOrderHeartbeatRepository) UpdateHearthBeat(ctx context.Context, orderIDs []string, t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHearthBeat", ctx, orderIDs, t)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateHearthBeat indicates an expected call of UpdateHearthBeat.
func (mr *MockOrderHeartbeatRepositoryMockRecorder) UpdateHearthBeat(ctx, orderIDs, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHearthBeat", reflect.TypeOf((*MockOrderHeartbeatRepository)(nil).UpdateHearthBeat), ctx, orderIDs, t)
}

// UpdateOrderRedistributionState mocks base method.
func (m *MockOrderHeartbeatRepository) UpdateOrderRedistributionState(ctx context.Context, states []model.OrderHearthBeatStateWithOrderID) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOrderRedistributionState", ctx, states)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderRedistributionState indicates an expected call of UpdateOrderRedistributionState.
func (mr *MockOrderHeartbeatRepositoryMockRecorder) UpdateOrderRedistributionState(ctx, states interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderRedistributionState", reflect.TypeOf((*MockOrderHeartbeatRepository)(nil).UpdateOrderRedistributionState), ctx, states)
}
