// Code generated by MockGen. DO NOT EDIT.
// Source: ./heatmap_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockHeatMapRepository is a mock of HeatMapRepository interface.
type MockHeatMapRepository struct {
	ctrl     *gomock.Controller
	recorder *MockHeatMapRepositoryMockRecorder
}

// MockHeatMapRepositoryMockRecorder is the mock recorder for MockHeatMapRepository.
type MockHeatMapRepositoryMockRecorder struct {
	mock *MockHeatMapRepository
}

// NewMockHeatMapRepository creates a new mock instance.
func NewMockHeatMapRepository(ctrl *gomock.Controller) *MockHeatMapRepository {
	mock := &MockHeatMapRepository{ctrl: ctrl}
	mock.recorder = &MockHeatMapRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockHeatMapRepository) EXPECT() *MockHeatMapRepositoryMockRecorder {
	return m.recorder
}

// DeleteByCreatedAtLte mocks base method.
func (m *MockHeatMapRepository) DeleteByCreatedAtLte(time time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByCreatedAtLte", time)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByCreatedAtLte indicates an expected call of DeleteByCreatedAtLte.
func (mr *MockHeatMapRepositoryMockRecorder) DeleteByCreatedAtLte(time interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByCreatedAtLte", reflect.TypeOf((*MockHeatMapRepository)(nil).DeleteByCreatedAtLte), time)
}

// DeleteByHourOfWeekday mocks base method.
func (m *MockHeatMapRepository) DeleteByHourOfWeekday(weekday, hour int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByHourOfWeekday", weekday, hour)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByHourOfWeekday indicates an expected call of DeleteByHourOfWeekday.
func (mr *MockHeatMapRepositoryMockRecorder) DeleteByHourOfWeekday(weekday, hour interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByHourOfWeekday", reflect.TypeOf((*MockHeatMapRepository)(nil).DeleteByHourOfWeekday), weekday, hour)
}

// Find mocks base method.
func (m *MockHeatMapRepository) Find(query repository.HeatMapQuery, opts ...repository.Option) ([]model.HeatMap, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{query}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.HeatMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockHeatMapRepositoryMockRecorder) Find(query interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{query}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockHeatMapRepository)(nil).Find), varargs...)
}

// FindByH3IDIn mocks base method.
func (m *MockHeatMapRepository) FindByH3IDIn(h3ids []string, opts ...repository.Option) ([]model.HeatMap, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{h3ids}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByH3IDIn", varargs...)
	ret0, _ := ret[0].([]model.HeatMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByH3IDIn indicates an expected call of FindByH3IDIn.
func (mr *MockHeatMapRepositoryMockRecorder) FindByH3IDIn(h3ids interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{h3ids}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByH3IDIn", reflect.TypeOf((*MockHeatMapRepository)(nil).FindByH3IDIn), varargs...)
}

// UpsertAll mocks base method.
func (m *MockHeatMapRepository) UpsertAll(listHeatMap []model.HeatMap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpsertAll", listHeatMap)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertAll indicates an expected call of UpsertAll.
func (mr *MockHeatMapRepositoryMockRecorder) UpsertAll(listHeatMap interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAll", reflect.TypeOf((*MockHeatMapRepository)(nil).UpsertAll), listHeatMap)
}
