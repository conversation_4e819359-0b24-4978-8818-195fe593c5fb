// Code generated by MockGen. DO NOT EDIT.
// Source: ./illegal_driver_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockIllegalDriverRepository is a mock of IllegalDriverRepository interface.
type MockIllegalDriverRepository struct {
	ctrl     *gomock.Controller
	recorder *MockIllegalDriverRepositoryMockRecorder
}

// MockIllegalDriverRepositoryMockRecorder is the mock recorder for MockIllegalDriverRepository.
type MockIllegalDriverRepositoryMockRecorder struct {
	mock *MockIllegalDriverRepository
}

// NewMockIllegalDriverRepository creates a new mock instance.
func NewMockIllegalDriverRepository(ctrl *gomock.Controller) *MockIllegalDriverRepository {
	mock := &MockIllegalDriverRepository{ctrl: ctrl}
	mock.recorder = &MockIllegalDriverRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockIllegalDriverRepository) EXPECT() *MockIllegalDriverRepositoryMockRecorder {
	return m.recorder
}

// AddIllegalDriver mocks base method.
func (m *MockIllegalDriverRepository) AddIllegalDriver(ctx context.Context, orderID string, deliveringRound int, orderExpireAt time.Time, driverID string, reason model.RiderFilterName, illegalUntil time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddIllegalDriver", ctx, orderID, deliveringRound, orderExpireAt, driverID, reason, illegalUntil)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddIllegalDriver indicates an expected call of AddIllegalDriver.
func (mr *MockIllegalDriverRepositoryMockRecorder) AddIllegalDriver(ctx, orderID, deliveringRound, orderExpireAt, driverID, reason, illegalUntil interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddIllegalDriver", reflect.TypeOf((*MockIllegalDriverRepository)(nil).AddIllegalDriver), ctx, orderID, deliveringRound, orderExpireAt, driverID, reason, illegalUntil)
}

// GetIllegalDrivers mocks base method.
func (m *MockIllegalDriverRepository) GetIllegalDrivers(ctx context.Context, orderID string, deliveringRound int) (*model.RidersIllegalData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetIllegalDrivers", ctx, orderID, deliveringRound)
	ret0, _ := ret[0].(*model.RidersIllegalData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIllegalDrivers indicates an expected call of GetIllegalDrivers.
func (mr *MockIllegalDriverRepositoryMockRecorder) GetIllegalDrivers(ctx, orderID, deliveringRound interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIllegalDrivers", reflect.TypeOf((*MockIllegalDriverRepository)(nil).GetIllegalDrivers), ctx, orderID, deliveringRound)
}
