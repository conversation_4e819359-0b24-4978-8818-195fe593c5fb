// Code generated by MockGen. DO NOT EDIT.
// Source: ./order_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	types "git.wndv.co/lineman/fleet-distribution/internal/types"
	gomock "github.com/golang/mock/gomock"
)

// MockOrderRepository is a mock of OrderRepository interface.
type MockOrderRepository struct {
	ctrl     *gomock.Controller
	recorder *MockOrderRepositoryMockRecorder
}

// MockOrderRepositoryMockRecorder is the mock recorder for MockOrderRepository.
type MockOrderRepositoryMockRecorder struct {
	mock *MockOrderRepository
}

// NewMockOrderRepository creates a new mock instance.
func NewMockOrderRepository(ctrl *gomock.Controller) *MockOrderRepository {
	mock := &MockOrderRepository{ctrl: ctrl}
	mock.recorder = &MockOrderRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockOrderRepository) EXPECT() *MockOrderRepositoryMockRecorder {
	return m.recorder
}

// AddRemark mocks base method.
func (m *MockOrderRepository) AddRemark(ctx context.Context, orderId, remark string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddRemark", ctx, orderId, remark)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddRemark indicates an expected call of AddRemark.
func (mr *MockOrderRepositoryMockRecorder) AddRemark(ctx, orderId, remark interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddRemark", reflect.TypeOf((*MockOrderRepository)(nil).AddRemark), ctx, orderId, remark)
}

// Count mocks base method.
func (m *MockOrderRepository) Count(ctx context.Context, query interface{}, opts ...repository.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Count", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockOrderRepositoryMockRecorder) Count(ctx, query interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockOrderRepository)(nil).Count), varargs...)
}

// CountAssigningDriverOrdersByTime mocks base method.
func (m *MockOrderRepository) CountAssigningDriverOrdersByTime(ctx context.Context, t time.Time) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountAssigningDriverOrdersByTime", ctx, t)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountAssigningDriverOrdersByTime indicates an expected call of CountAssigningDriverOrdersByTime.
func (mr *MockOrderRepositoryMockRecorder) CountAssigningDriverOrdersByTime(ctx, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountAssigningDriverOrdersByTime", reflect.TypeOf((*MockOrderRepository)(nil).CountAssigningDriverOrdersByTime), ctx, t)
}

// CountByDriverID mocks base method.
func (m *MockOrderRepository) CountByDriverID(ctx context.Context, driverID string, statuses ...model.Status) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, driverID}
	for _, a := range statuses {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CountByDriverID", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByDriverID indicates an expected call of CountByDriverID.
func (mr *MockOrderRepositoryMockRecorder) CountByDriverID(ctx, driverID interface{}, statuses ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, driverID}, statuses...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByDriverID", reflect.TypeOf((*MockOrderRepository)(nil).CountByDriverID), varargs...)
}

// CountCancelOrderByCancelDetail mocks base method.
func (m *MockOrderRepository) CountCancelOrderByCancelDetail(ctx context.Context, driverID string, since time.Time, reasonName, source, cancelledBy, serviceType string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountCancelOrderByCancelDetail", ctx, driverID, since, reasonName, source, cancelledBy, serviceType)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountCancelOrderByCancelDetail indicates an expected call of CountCancelOrderByCancelDetail.
func (mr *MockOrderRepositoryMockRecorder) CountCancelOrderByCancelDetail(ctx, driverID, since, reasonName, source, cancelledBy, serviceType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountCancelOrderByCancelDetail", reflect.TypeOf((*MockOrderRepository)(nil).CountCancelOrderByCancelDetail), ctx, driverID, since, reasonName, source, cancelledBy, serviceType)
}

// CountOrderCompletedByCompletedTimeAndDriver mocks base method.
func (m *MockOrderRepository) CountOrderCompletedByCompletedTimeAndDriver(ctx context.Context, from, to time.Time, driverIDs []string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrderCompletedByCompletedTimeAndDriver", ctx, from, to, driverIDs)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountOrderCompletedByCompletedTimeAndDriver indicates an expected call of CountOrderCompletedByCompletedTimeAndDriver.
func (mr *MockOrderRepositoryMockRecorder) CountOrderCompletedByCompletedTimeAndDriver(ctx, from, to, driverIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrderCompletedByCompletedTimeAndDriver", reflect.TypeOf((*MockOrderRepository)(nil).CountOrderCompletedByCompletedTimeAndDriver), ctx, from, to, driverIDs)
}

// CountOrderCompletedByCreatedTimeAndDriver mocks base method.
func (m *MockOrderRepository) CountOrderCompletedByCreatedTimeAndDriver(ctx context.Context, from, to time.Time, driverIDs []string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountOrderCompletedByCreatedTimeAndDriver", ctx, from, to, driverIDs)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountOrderCompletedByCreatedTimeAndDriver indicates an expected call of CountOrderCompletedByCreatedTimeAndDriver.
func (mr *MockOrderRepositoryMockRecorder) CountOrderCompletedByCreatedTimeAndDriver(ctx, from, to, driverIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountOrderCompletedByCreatedTimeAndDriver", reflect.TypeOf((*MockOrderRepository)(nil).CountOrderCompletedByCreatedTimeAndDriver), ctx, from, to, driverIDs)
}

// CountValidOrder mocks base method.
func (m *MockOrderRepository) CountValidOrder(ctx context.Context, driverIDs []string, serviceTypes []model.Service, start, end time.Time) (map[string]int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountValidOrder", ctx, driverIDs, serviceTypes, start, end)
	ret0, _ := ret[0].(map[string]int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountValidOrder indicates an expected call of CountValidOrder.
func (mr *MockOrderRepositoryMockRecorder) CountValidOrder(ctx, driverIDs, serviceTypes, start, end interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountValidOrder", reflect.TypeOf((*MockOrderRepository)(nil).CountValidOrder), ctx, driverIDs, serviceTypes, start, end)
}

// CreateOrder mocks base method.
func (m *MockOrderRepository) CreateOrder(arg0 context.Context, arg1 *model.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockOrderRepositoryMockRecorder) CreateOrder(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockOrderRepository)(nil).CreateOrder), arg0, arg1)
}

// CurrentStatus mocks base method.
func (m *MockOrderRepository) CurrentStatus(ctx context.Context, ordid string) (model.Status, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CurrentStatus", ctx, ordid)
	ret0, _ := ret[0].(model.Status)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CurrentStatus indicates an expected call of CurrentStatus.
func (mr *MockOrderRepositoryMockRecorder) CurrentStatus(ctx, ordid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CurrentStatus", reflect.TypeOf((*MockOrderRepository)(nil).CurrentStatus), ctx, ordid)
}

// Find mocks base method.
func (m *MockOrderRepository) Find(ctx context.Context, query interface{}, skip, limit int, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockOrderRepositoryMockRecorder) Find(ctx, query, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockOrderRepository)(nil).Find), varargs...)
}

// FindAndSort mocks base method.
func (m *MockOrderRepository) FindAndSort(ctx context.Context, query interface{}, skip, limit int, sort []string, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit, sort}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindAndSort", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAndSort indicates an expected call of FindAndSort.
func (mr *MockOrderRepositoryMockRecorder) FindAndSort(ctx, query, skip, limit, sort interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit, sort}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAndSort", reflect.TypeOf((*MockOrderRepository)(nil).FindAndSort), varargs...)
}

// FindAssigningDriverOrdersByTime mocks base method.
func (m *MockOrderRepository) FindAssigningDriverOrdersByTime(ctx context.Context, t time.Time, skip, limit int) ([]model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAssigningDriverOrdersByTime", ctx, t, skip, limit)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAssigningDriverOrdersByTime indicates an expected call of FindAssigningDriverOrdersByTime.
func (mr *MockOrderRepositoryMockRecorder) FindAssigningDriverOrdersByTime(ctx, t, skip, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAssigningDriverOrdersByTime", reflect.TypeOf((*MockOrderRepository)(nil).FindAssigningDriverOrdersByTime), ctx, t, skip, limit)
}

// FindByDriver mocks base method.
func (m *MockOrderRepository) FindByDriver(ctx context.Context, driverID string, skip, limit int, statuses ...model.Status) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, driverID, skip, limit}
	for _, a := range statuses {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByDriver", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByDriver indicates an expected call of FindByDriver.
func (mr *MockOrderRepositoryMockRecorder) FindByDriver(ctx, driverID, skip, limit interface{}, statuses ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, driverID, skip, limit}, statuses...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByDriver", reflect.TypeOf((*MockOrderRepository)(nil).FindByDriver), varargs...)
}

// FindDriverOrderAutoAssigned mocks base method.
func (m *MockOrderRepository) FindDriverOrderAutoAssigned(ctx context.Context, drivers []string, start, end time.Time, opts ...repository.Option) (model.CountDriverAutoAssignedRecords, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, drivers, start, end}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindDriverOrderAutoAssigned", varargs...)
	ret0, _ := ret[0].(model.CountDriverAutoAssignedRecords)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindDriverOrderAutoAssigned indicates an expected call of FindDriverOrderAutoAssigned.
func (mr *MockOrderRepositoryMockRecorder) FindDriverOrderAutoAssigned(ctx, drivers, start, end interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, drivers, start, end}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindDriverOrderAutoAssigned", reflect.TypeOf((*MockOrderRepository)(nil).FindDriverOrderAutoAssigned), varargs...)
}

// FindDriverOrderAutoAssignedCompleted mocks base method.
func (m *MockOrderRepository) FindDriverOrderAutoAssignedCompleted(ctx context.Context, drivers []string, start, end time.Time, opts ...repository.Option) (model.CountDriverAutoAssignedRecords, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, drivers, start, end}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindDriverOrderAutoAssignedCompleted", varargs...)
	ret0, _ := ret[0].(model.CountDriverAutoAssignedRecords)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindDriverOrderAutoAssignedCompleted indicates an expected call of FindDriverOrderAutoAssignedCompleted.
func (mr *MockOrderRepositoryMockRecorder) FindDriverOrderAutoAssignedCompleted(ctx, drivers, start, end interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, drivers, start, end}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindDriverOrderAutoAssignedCompleted", reflect.TypeOf((*MockOrderRepository)(nil).FindDriverOrderAutoAssignedCompleted), varargs...)
}

// FindLatestRatings mocks base method.
func (m *MockOrderRepository) FindLatestRatings(ctx context.Context, driverID string, N int) ([]uint32, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindLatestRatings", ctx, driverID, N)
	ret0, _ := ret[0].([]uint32)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindLatestRatings indicates an expected call of FindLatestRatings.
func (mr *MockOrderRepositoryMockRecorder) FindLatestRatings(ctx, driverID, N interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindLatestRatings", reflect.TypeOf((*MockOrderRepository)(nil).FindLatestRatings), ctx, driverID, N)
}

// FindOrderCompletedByTimeAndDriver mocks base method.
func (m *MockOrderRepository) FindOrderCompletedByTimeAndDriver(ctx context.Context, from, to time.Time, driverIDs, serviceTypes []string, skip, limit int) ([]model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOrderCompletedByTimeAndDriver", ctx, from, to, driverIDs, serviceTypes, skip, limit)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOrderCompletedByTimeAndDriver indicates an expected call of FindOrderCompletedByTimeAndDriver.
func (mr *MockOrderRepositoryMockRecorder) FindOrderCompletedByTimeAndDriver(ctx, from, to, driverIDs, serviceTypes, skip, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOrderCompletedByTimeAndDriver", reflect.TypeOf((*MockOrderRepository)(nil).FindOrderCompletedByTimeAndDriver), ctx, from, to, driverIDs, serviceTypes, skip, limit)
}

// FindRevision mocks base method.
func (m *MockOrderRepository) FindRevision(ctx context.Context, query repository.OrderRevisionQuery, skip, limit int, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindRevision", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindRevision indicates an expected call of FindRevision.
func (mr *MockOrderRepositoryMockRecorder) FindRevision(ctx, query, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindRevision", reflect.TypeOf((*MockOrderRepository)(nil).FindRevision), varargs...)
}

// FindTodayCompleteOrderByDriver mocks base method.
func (m *MockOrderRepository) FindTodayCompleteOrderByDriver(ctx context.Context, driverIds []string, loc *time.Location) ([]model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindTodayCompleteOrderByDriver", ctx, driverIds, loc)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindTodayCompleteOrderByDriver indicates an expected call of FindTodayCompleteOrderByDriver.
func (mr *MockOrderRepositoryMockRecorder) FindTodayCompleteOrderByDriver(ctx, driverIds, loc interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindTodayCompleteOrderByDriver", reflect.TypeOf((*MockOrderRepository)(nil).FindTodayCompleteOrderByDriver), ctx, driverIds, loc)
}

// FindWithSelector mocks base method.
func (m *MockOrderRepository) FindWithSelector(ctx context.Context, query, selector interface{}, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, selector}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindWithSelector", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindWithSelector indicates an expected call of FindWithSelector.
func (mr *MockOrderRepositoryMockRecorder) FindWithSelector(ctx, query, selector interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, selector}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindWithSelector", reflect.TypeOf((*MockOrderRepository)(nil).FindWithSelector), varargs...)
}

// Get mocks base method.
func (m *MockOrderRepository) Get(ctx context.Context, ordid string, opts ...repository.Option) (*model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, ordid}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(*model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockOrderRepositoryMockRecorder) Get(ctx, ordid interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, ordid}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockOrderRepository)(nil).Get), varargs...)
}

// GetActiveAssigningOrders mocks base method.
func (m *MockOrderRepository) GetActiveAssigningOrders(ctx context.Context, orderIDs []string, now time.Time, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderIDs, now}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetActiveAssigningOrders", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveAssigningOrders indicates an expected call of GetActiveAssigningOrders.
func (mr *MockOrderRepositoryMockRecorder) GetActiveAssigningOrders(ctx, orderIDs, now interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderIDs, now}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveAssigningOrders", reflect.TypeOf((*MockOrderRepository)(nil).GetActiveAssigningOrders), varargs...)
}

// GetCreationDetails mocks base method.
func (m *MockOrderRepository) GetCreationDetails(ctx context.Context, orderIDs []string, opts ...repository.Option) ([]model.OrderCreationDetails, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderIDs}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetCreationDetails", varargs...)
	ret0, _ := ret[0].([]model.OrderCreationDetails)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCreationDetails indicates an expected call of GetCreationDetails.
func (mr *MockOrderRepositoryMockRecorder) GetCreationDetails(ctx, orderIDs interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderIDs}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCreationDetails", reflect.TypeOf((*MockOrderRepository)(nil).GetCreationDetails), varargs...)
}

// GetDriverLastAttempt mocks base method.
func (m *MockOrderRepository) GetDriverLastAttempt(ctx context.Context, driverID string) (*model.LastAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDriverLastAttempt", ctx, driverID)
	ret0, _ := ret[0].(*model.LastAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDriverLastAttempt indicates an expected call of GetDriverLastAttempt.
func (mr *MockOrderRepositoryMockRecorder) GetDriverLastAttempt(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDriverLastAttempt", reflect.TypeOf((*MockOrderRepository)(nil).GetDriverLastAttempt), ctx, driverID)
}

// GetDriversOfOtherMPs mocks base method.
func (m *MockOrderRepository) GetDriversOfOtherMPs(ctx context.Context, orders []model.Order) ([]repository.DriverMP, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDriversOfOtherMPs", ctx, orders)
	ret0, _ := ret[0].([]repository.DriverMP)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDriversOfOtherMPs indicates an expected call of GetDriversOfOtherMPs.
func (mr *MockOrderRepositoryMockRecorder) GetDriversOfOtherMPs(ctx, orders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDriversOfOtherMPs", reflect.TypeOf((*MockOrderRepository)(nil).GetDriversOfOtherMPs), ctx, orders)
}

// GetExDrivers mocks base method.
func (m *MockOrderRepository) GetExDrivers(ctx context.Context, orderID string, opts ...repository.Option) (types.StringSet, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetExDrivers", varargs...)
	ret0, _ := ret[0].(types.StringSet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExDrivers indicates an expected call of GetExDrivers.
func (mr *MockOrderRepositoryMockRecorder) GetExDrivers(ctx, orderID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExDrivers", reflect.TypeOf((*MockOrderRepository)(nil).GetExDrivers), varargs...)
}

// GetMany mocks base method.
func (m *MockOrderRepository) GetMany(ctx context.Context, orderIDs []string, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderIDs}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetMany", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetMany indicates an expected call of GetMany.
func (mr *MockOrderRepositoryMockRecorder) GetMany(ctx, orderIDs interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderIDs}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetMany", reflect.TypeOf((*MockOrderRepository)(nil).GetMany), varargs...)
}

// GetManyByDeliveringRounds mocks base method.
func (m *MockOrderRepository) GetManyByDeliveringRounds(ctx context.Context, orderIDsWithDeliveringRound map[string]int, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderIDsWithDeliveringRound}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetManyByDeliveringRounds", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManyByDeliveringRounds indicates an expected call of GetManyByDeliveringRounds.
func (mr *MockOrderRepositoryMockRecorder) GetManyByDeliveringRounds(ctx, orderIDsWithDeliveringRound interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderIDsWithDeliveringRound}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManyByDeliveringRounds", reflect.TypeOf((*MockOrderRepository)(nil).GetManyByDeliveringRounds), varargs...)
}

// GetOtherActiveMP mocks base method.
func (m *MockOrderRepository) GetOtherActiveMP(ctx context.Context, ord model.Order) (*model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOtherActiveMP", ctx, ord)
	ret0, _ := ret[0].(*model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOtherActiveMP indicates an expected call of GetOtherActiveMP.
func (mr *MockOrderRepositoryMockRecorder) GetOtherActiveMP(ctx, ord interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOtherActiveMP", reflect.TypeOf((*MockOrderRepository)(nil).GetOtherActiveMP), ctx, ord)
}

// GetRevisionByOrderIdAndDriverId mocks base method.
func (m *MockOrderRepository) GetRevisionByOrderIdAndDriverId(ctx context.Context, ordId, driverID string, opts ...repository.Option) (*model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, ordId, driverID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRevisionByOrderIdAndDriverId", varargs...)
	ret0, _ := ret[0].(*model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRevisionByOrderIdAndDriverId indicates an expected call of GetRevisionByOrderIdAndDriverId.
func (mr *MockOrderRepositoryMockRecorder) GetRevisionByOrderIdAndDriverId(ctx, ordId, driverID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, ordId, driverID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRevisionByOrderIdAndDriverId", reflect.TypeOf((*MockOrderRepository)(nil).GetRevisionByOrderIdAndDriverId), varargs...)
}

// GroupCancelOrderByDriverID mocks base method.
func (m *MockOrderRepository) GroupCancelOrderByDriverID(ctx context.Context, driverID string, reasons []string, serviceType string) ([]model.CountCancelOrderType, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupCancelOrderByDriverID", ctx, driverID, reasons, serviceType)
	ret0, _ := ret[0].([]model.CountCancelOrderType)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GroupCancelOrderByDriverID indicates an expected call of GroupCancelOrderByDriverID.
func (mr *MockOrderRepositoryMockRecorder) GroupCancelOrderByDriverID(ctx, driverID, reasons, serviceType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupCancelOrderByDriverID", reflect.TypeOf((*MockOrderRepository)(nil).GroupCancelOrderByDriverID), ctx, driverID, reasons, serviceType)
}

// IsExist mocks base method.
func (m *MockOrderRepository) IsExist(ctx context.Context, orderID string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsExist", ctx, orderID)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsExist indicates an expected call of IsExist.
func (mr *MockOrderRepositoryMockRecorder) IsExist(ctx, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExist", reflect.TypeOf((*MockOrderRepository)(nil).IsExist), ctx, orderID)
}

// PushRoutedUWT mocks base method.
func (m *MockOrderRepository) PushRoutedUWT(ctx context.Context, orderID string, now time.Time, routedUWT int64, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, now, routedUWT}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PushRoutedUWT", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// PushRoutedUWT indicates an expected call of PushRoutedUWT.
func (mr *MockOrderRepositoryMockRecorder) PushRoutedUWT(ctx, orderID, now, routedUWT interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, now, routedUWT}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PushRoutedUWT", reflect.TypeOf((*MockOrderRepository)(nil).PushRoutedUWT), varargs...)
}

// RemoveCompletedOrderByDriverID mocks base method.
func (m *MockOrderRepository) RemoveCompletedOrderByDriverID(ctx context.Context, driverID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveCompletedOrderByDriverID", ctx, driverID)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveCompletedOrderByDriverID indicates an expected call of RemoveCompletedOrderByDriverID.
func (mr *MockOrderRepositoryMockRecorder) RemoveCompletedOrderByDriverID(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveCompletedOrderByDriverID", reflect.TypeOf((*MockOrderRepository)(nil).RemoveCompletedOrderByDriverID), ctx, driverID)
}

// SetActualAssigningAtFromNil mocks base method.
func (m *MockOrderRepository) SetActualAssigningAtFromNil(ctx context.Context, orderID string, actualAssigningAt time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetActualAssigningAtFromNil", ctx, orderID, actualAssigningAt)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetActualAssigningAtFromNil indicates an expected call of SetActualAssigningAtFromNil.
func (mr *MockOrderRepositoryMockRecorder) SetActualAssigningAtFromNil(ctx, orderID, actualAssigningAt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetActualAssigningAtFromNil", reflect.TypeOf((*MockOrderRepository)(nil).SetActualAssigningAtFromNil), ctx, orderID, actualAssigningAt)
}

// SetAsDistributed mocks base method.
func (m *MockOrderRepository) SetAsDistributed(ctx context.Context, orderId string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetAsDistributed", ctx, orderId)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetAsDistributed indicates an expected call of SetAsDistributed.
func (mr *MockOrderRepositoryMockRecorder) SetAsDistributed(ctx, orderId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetAsDistributed", reflect.TypeOf((*MockOrderRepository)(nil).SetAsDistributed), ctx, orderId)
}

// SetCancelDetailAndCancelDetailLog mocks base method.
func (m *MockOrderRepository) SetCancelDetailAndCancelDetailLog(ctx context.Context, orderID string, order model.Order, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, order}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetCancelDetailAndCancelDetailLog", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCancelDetailAndCancelDetailLog indicates an expected call of SetCancelDetailAndCancelDetailLog.
func (mr *MockOrderRepositoryMockRecorder) SetCancelDetailAndCancelDetailLog(ctx, orderID, order interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, order}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCancelDetailAndCancelDetailLog", reflect.TypeOf((*MockOrderRepository)(nil).SetCancelDetailAndCancelDetailLog), varargs...)
}

// SetCellPhoneContact mocks base method.
func (m *MockOrderRepository) SetCellPhoneContact(ctx context.Context, orderID, driverID string, now time.Time, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, driverID, now}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetCellPhoneContact", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetCellPhoneContact indicates an expected call of SetCellPhoneContact.
func (mr *MockOrderRepositoryMockRecorder) SetCellPhoneContact(ctx, orderID, driverID, now interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, driverID, now}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetCellPhoneContact", reflect.TypeOf((*MockOrderRepository)(nil).SetCellPhoneContact), varargs...)
}

// SetDeferDuration mocks base method.
func (m *MockOrderRepository) SetDeferDuration(ctx context.Context, orderID string, deferDuration time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeferDuration", ctx, orderID, deferDuration)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDeferDuration indicates an expected call of SetDeferDuration.
func (mr *MockOrderRepositoryMockRecorder) SetDeferDuration(ctx, orderID, deferDuration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeferDuration", reflect.TypeOf((*MockOrderRepository)(nil).SetDeferDuration), ctx, orderID, deferDuration)
}

// SetDeferredOrder mocks base method.
func (m *MockOrderRepository) SetDeferredOrder(ctx context.Context, orderId string, expiredAt time.Time, deferDuration time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeferredOrder", ctx, orderId, expiredAt, deferDuration)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDeferredOrder indicates an expected call of SetDeferredOrder.
func (mr *MockOrderRepositoryMockRecorder) SetDeferredOrder(ctx, orderId, expiredAt, deferDuration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeferredOrder", reflect.TypeOf((*MockOrderRepository)(nil).SetDeferredOrder), ctx, orderId, expiredAt, deferDuration)
}

// SetDeliveryPhoto mocks base method.
func (m *MockOrderRepository) SetDeliveryPhoto(ctx context.Context, orderID string, photoURL []string, deviceID string, headTo int) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDeliveryPhoto", ctx, orderID, photoURL, deviceID, headTo)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDeliveryPhoto indicates an expected call of SetDeliveryPhoto.
func (mr *MockOrderRepositoryMockRecorder) SetDeliveryPhoto(ctx, orderID, photoURL, deviceID, headTo interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDeliveryPhoto", reflect.TypeOf((*MockOrderRepository)(nil).SetDeliveryPhoto), ctx, orderID, photoURL, deviceID, headTo)
}

// SetDistance0 mocks base method.
func (m *MockOrderRepository) SetDistance0(ctx context.Context, orderId string, distance0 types.Distance, estimatedDeliveryTime model.DurationSecond) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDistance0", ctx, orderId, distance0, estimatedDeliveryTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDistance0 indicates an expected call of SetDistance0.
func (mr *MockOrderRepositoryMockRecorder) SetDistance0(ctx, orderId, distance0, estimatedDeliveryTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDistance0", reflect.TypeOf((*MockOrderRepository)(nil).SetDistance0), ctx, orderId, distance0, estimatedDeliveryTime)
}

// SetDistance0AndPriceSummary mocks base method.
func (m *MockOrderRepository) SetDistance0AndPriceSummary(ctx context.Context, order model.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDistance0AndPriceSummary", ctx, order)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDistance0AndPriceSummary indicates an expected call of SetDistance0AndPriceSummary.
func (mr *MockOrderRepositoryMockRecorder) SetDistance0AndPriceSummary(ctx, order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDistance0AndPriceSummary", reflect.TypeOf((*MockOrderRepository)(nil).SetDistance0AndPriceSummary), ctx, order)
}

// SetDistanceCompensatoryInfo mocks base method.
func (m *MockOrderRepository) SetDistanceCompensatoryInfo(ctx context.Context, orderID string, info *model.DistanceCompensatoryInfo, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, info}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetDistanceCompensatoryInfo", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDistanceCompensatoryInfo indicates an expected call of SetDistanceCompensatoryInfo.
func (mr *MockOrderRepositoryMockRecorder) SetDistanceCompensatoryInfo(ctx, orderID, info interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, info}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDistanceCompensatoryInfo", reflect.TypeOf((*MockOrderRepository)(nil).SetDistanceCompensatoryInfo), varargs...)
}

// SetDriverAction mocks base method.
func (m *MockOrderRepository) SetDriverAction(ctx context.Context, orderID string, action model.DriverAction, actionAt time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDriverAction", ctx, orderID, action, actionAt)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDriverAction indicates an expected call of SetDriverAction.
func (mr *MockOrderRepositoryMockRecorder) SetDriverAction(ctx, orderID, action, actionAt interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDriverAction", reflect.TypeOf((*MockOrderRepository)(nil).SetDriverAction), ctx, orderID, action, actionAt)
}

// SetDriverLastAttempt mocks base method.
func (m *MockOrderRepository) SetDriverLastAttempt(ctx context.Context, driverID, orderID string, ttl time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetDriverLastAttempt", ctx, driverID, orderID, ttl)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetDriverLastAttempt indicates an expected call of SetDriverLastAttempt.
func (mr *MockOrderRepositoryMockRecorder) SetDriverLastAttempt(ctx, driverID, orderID, ttl interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetDriverLastAttempt", reflect.TypeOf((*MockOrderRepository)(nil).SetDriverLastAttempt), ctx, driverID, orderID, ttl)
}

// SetHasAnotherMPOrder mocks base method.
func (m *MockOrderRepository) SetHasAnotherMPOrder(ctx context.Context, orderID string, hasAnotherMPOrder *bool, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, hasAnotherMPOrder}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetHasAnotherMPOrder", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetHasAnotherMPOrder indicates an expected call of SetHasAnotherMPOrder.
func (mr *MockOrderRepositoryMockRecorder) SetHasAnotherMPOrder(ctx, orderID, hasAnotherMPOrder interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, hasAnotherMPOrder}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetHasAnotherMPOrder", reflect.TypeOf((*MockOrderRepository)(nil).SetHasAnotherMPOrder), varargs...)
}

// SetIsFraud mocks base method.
func (m *MockOrderRepository) SetIsFraud(ctx context.Context, orderID string, isFraud bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetIsFraud", ctx, orderID, isFraud)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetIsFraud indicates an expected call of SetIsFraud.
func (mr *MockOrderRepositoryMockRecorder) SetIsFraud(ctx, orderID, isFraud interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIsFraud", reflect.TypeOf((*MockOrderRepository)(nil).SetIsFraud), ctx, orderID, isFraud)
}

// SetIsTriedForceAssignMP mocks base method.
func (m *MockOrderRepository) SetIsTriedForceAssignMP(ctx context.Context, orderID string, isTriedForceAssignMP bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetIsTriedForceAssignMP", ctx, orderID, isTriedForceAssignMP)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetIsTriedForceAssignMP indicates an expected call of SetIsTriedForceAssignMP.
func (mr *MockOrderRepositoryMockRecorder) SetIsTriedForceAssignMP(ctx, orderID, isTriedForceAssignMP interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetIsTriedForceAssignMP", reflect.TypeOf((*MockOrderRepository)(nil).SetIsTriedForceAssignMP), ctx, orderID, isTriedForceAssignMP)
}

// SetLatestSyncCompletionTime mocks base method.
func (m *MockOrderRepository) SetLatestSyncCompletionTime(ctx context.Context, orderID string, completionTime time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLatestSyncCompletionTime", ctx, orderID, completionTime)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLatestSyncCompletionTime indicates an expected call of SetLatestSyncCompletionTime.
func (mr *MockOrderRepositoryMockRecorder) SetLatestSyncCompletionTime(ctx, orderID, completionTime interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLatestSyncCompletionTime", reflect.TypeOf((*MockOrderRepository)(nil).SetLatestSyncCompletionTime), ctx, orderID, completionTime)
}

// SetLeavePrevStopAt mocks base method.
func (m *MockOrderRepository) SetLeavePrevStopAt(ctx context.Context, orderID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLeavePrevStopAt", ctx, orderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetLeavePrevStopAt indicates an expected call of SetLeavePrevStopAt.
func (mr *MockOrderRepositoryMockRecorder) SetLeavePrevStopAt(ctx, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLeavePrevStopAt", reflect.TypeOf((*MockOrderRepository)(nil).SetLeavePrevStopAt), ctx, orderID)
}

// SetMOSaving mocks base method.
func (m *MockOrderRepository) SetMOSaving(ctx context.Context, order model.Order, moSaving float64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetMOSaving", ctx, order, moSaving)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMOSaving indicates an expected call of SetMOSaving.
func (mr *MockOrderRepositoryMockRecorder) SetMOSaving(ctx, order, moSaving interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMOSaving", reflect.TypeOf((*MockOrderRepository)(nil).SetMOSaving), ctx, order, moSaving)
}

// SetMemoTranslation mocks base method.
func (m *MockOrderRepository) SetMemoTranslation(ctx context.Context, orderID string, payAtStop int, translatedMemo string, memoTransactionStatus model.TranslationStatus, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, payAtStop, translatedMemo, memoTransactionStatus}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetMemoTranslation", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetMemoTranslation indicates an expected call of SetMemoTranslation.
func (mr *MockOrderRepositoryMockRecorder) SetMemoTranslation(ctx, orderID, payAtStop, translatedMemo, memoTransactionStatus interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, payAtStop, translatedMemo, memoTransactionStatus}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetMemoTranslation", reflect.TypeOf((*MockOrderRepository)(nil).SetMemoTranslation), varargs...)
}

// SetOrderShifts mocks base method.
func (m *MockOrderRepository) SetOrderShifts(ctx context.Context, order *model.Order, shifts []model.OrderShift) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetOrderShifts", ctx, order, shifts)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetOrderShifts indicates an expected call of SetOrderShifts.
func (mr *MockOrderRepositoryMockRecorder) SetOrderShifts(ctx, order, shifts interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetOrderShifts", reflect.TypeOf((*MockOrderRepository)(nil).SetOrderShifts), ctx, order, shifts)
}

// SetPauseFlag mocks base method.
func (m *MockOrderRepository) SetPauseFlag(ctx context.Context, orderID string, headTo int, pauseFlag model.Pause) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPauseFlag", ctx, orderID, headTo, pauseFlag)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPauseFlag indicates an expected call of SetPauseFlag.
func (mr *MockOrderRepositoryMockRecorder) SetPauseFlag(ctx, orderID, headTo, pauseFlag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPauseFlag", reflect.TypeOf((*MockOrderRepository)(nil).SetPauseFlag), ctx, orderID, headTo, pauseFlag)
}

// SetPredictedCompletedTime mocks base method.
func (m *MockOrderRepository) SetPredictedCompletedTime(ctx context.Context, orderID string, t time.Time, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, t}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetPredictedCompletedTime", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPredictedCompletedTime indicates an expected call of SetPredictedCompletedTime.
func (mr *MockOrderRepositoryMockRecorder) SetPredictedCompletedTime(ctx, orderID, t interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, t}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPredictedCompletedTime", reflect.TypeOf((*MockOrderRepository)(nil).SetPredictedCompletedTime), varargs...)
}

// SetPrediction mocks base method.
func (m *MockOrderRepository) SetPrediction(ctx context.Context, ordid string, prediction model.PredictionFeatures) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPrediction", ctx, ordid, prediction)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPrediction indicates an expected call of SetPrediction.
func (mr *MockOrderRepositoryMockRecorder) SetPrediction(ctx, ordid, prediction interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPrediction", reflect.TypeOf((*MockOrderRepository)(nil).SetPrediction), ctx, ordid, prediction)
}

// SetPredictionDeferTime mocks base method.
func (m *MockOrderRepository) SetPredictionDeferTime(ctx context.Context, orderID string, predictedDeferTime, mpDeferTime, matchingPriorityAt time.Time, deferDuration time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetPredictionDeferTime", ctx, orderID, predictedDeferTime, mpDeferTime, matchingPriorityAt, deferDuration)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetPredictionDeferTime indicates an expected call of SetPredictionDeferTime.
func (mr *MockOrderRepositoryMockRecorder) SetPredictionDeferTime(ctx, orderID, predictedDeferTime, mpDeferTime, matchingPriorityAt, deferDuration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPredictionDeferTime", reflect.TypeOf((*MockOrderRepository)(nil).SetPredictionDeferTime), ctx, orderID, predictedDeferTime, mpDeferTime, matchingPriorityAt, deferDuration)
}

// SetProcessedBySingleDistribution mocks base method.
func (m *MockOrderRepository) SetProcessedBySingleDistribution(ctx context.Context, orderID string, v bool, searchRiderStrategy model.SearchRiderStrategy) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetProcessedBySingleDistribution", ctx, orderID, v, searchRiderStrategy)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetProcessedBySingleDistribution indicates an expected call of SetProcessedBySingleDistribution.
func (mr *MockOrderRepositoryMockRecorder) SetProcessedBySingleDistribution(ctx, orderID, v, searchRiderStrategy interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetProcessedBySingleDistribution", reflect.TypeOf((*MockOrderRepository)(nil).SetProcessedBySingleDistribution), ctx, orderID, v, searchRiderStrategy)
}

// SetQRPromptPayInfo mocks base method.
func (m *MockOrderRepository) SetQRPromptPayInfo(ctx context.Context, order model.Order, qrPromptPayInfo model.QRPromptPayInfo, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order, qrPromptPayInfo}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetQRPromptPayInfo", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetQRPromptPayInfo indicates an expected call of SetQRPromptPayInfo.
func (mr *MockOrderRepositoryMockRecorder) SetQRPromptPayInfo(ctx, order, qrPromptPayInfo interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order, qrPromptPayInfo}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetQRPromptPayInfo", reflect.TypeOf((*MockOrderRepository)(nil).SetQRPromptPayInfo), varargs...)
}

// SetRedistributionState mocks base method.
func (m *MockOrderRepository) SetRedistributionState(ctx context.Context, orderID string, state *model.RedistributionState, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, state}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetRedistributionState", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetRedistributionState indicates an expected call of SetRedistributionState.
func (mr *MockOrderRepositoryMockRecorder) SetRedistributionState(ctx, orderID, state interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, state}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetRedistributionState", reflect.TypeOf((*MockOrderRepository)(nil).SetRedistributionState), varargs...)
}

// SetSaversExperimentRecords mocks base method.
func (m *MockOrderRepository) SetSaversExperimentRecords(ctx context.Context, orderID string, deliveringRound int, records []model.SaversExperimentRecord) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetSaversExperimentRecords", ctx, orderID, deliveringRound, records)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetSaversExperimentRecords indicates an expected call of SetSaversExperimentRecords.
func (mr *MockOrderRepositoryMockRecorder) SetSaversExperimentRecords(ctx, orderID, deliveringRound, records interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetSaversExperimentRecords", reflect.TypeOf((*MockOrderRepository)(nil).SetSaversExperimentRecords), ctx, orderID, deliveringRound, records)
}

// SetThrottledOrder mocks base method.
func (m *MockOrderRepository) SetThrottledOrder(ctx context.Context, orderId, zoneID string, state *model.RedistributionState) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetThrottledOrder", ctx, orderId, zoneID, state)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetThrottledOrder indicates an expected call of SetThrottledOrder.
func (mr *MockOrderRepositoryMockRecorder) SetThrottledOrder(ctx, orderId, zoneID, state interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetThrottledOrder", reflect.TypeOf((*MockOrderRepository)(nil).SetThrottledOrder), ctx, orderId, zoneID, state)
}

// SetThrottlingDeferredOrder mocks base method.
func (m *MockOrderRepository) SetThrottlingDeferredOrder(ctx context.Context, orderId string, expiredAt time.Time, deferDuration time.Duration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetThrottlingDeferredOrder", ctx, orderId, expiredAt, deferDuration)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetThrottlingDeferredOrder indicates an expected call of SetThrottlingDeferredOrder.
func (mr *MockOrderRepositoryMockRecorder) SetThrottlingDeferredOrder(ctx, orderId, expiredAt, deferDuration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetThrottlingDeferredOrder", reflect.TypeOf((*MockOrderRepository)(nil).SetThrottlingDeferredOrder), ctx, orderId, expiredAt, deferDuration)
}

// SetTripID mocks base method.
func (m *MockOrderRepository) SetTripID(ctx context.Context, orderID, tripID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetTripID", ctx, orderID, tripID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetTripID indicates an expected call of SetTripID.
func (mr *MockOrderRepositoryMockRecorder) SetTripID(ctx, orderID, tripID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetTripID", reflect.TypeOf((*MockOrderRepository)(nil).SetTripID), ctx, orderID, tripID)
}

// SetVerifiedRiderPhotoCompareFace mocks base method.
func (m *MockOrderRepository) SetVerifiedRiderPhotoCompareFace(ctx context.Context, ordId string, v model.VerifiedRiderPhotoUrl) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetVerifiedRiderPhotoCompareFace", ctx, ordId, v)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetVerifiedRiderPhotoCompareFace indicates an expected call of SetVerifiedRiderPhotoCompareFace.
func (mr *MockOrderRepositoryMockRecorder) SetVerifiedRiderPhotoCompareFace(ctx, ordId, v interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetVerifiedRiderPhotoCompareFace", reflect.TypeOf((*MockOrderRepository)(nil).SetVerifiedRiderPhotoCompareFace), ctx, ordId, v)
}

// SetZeroFullTimeDriverOrderRevision mocks base method.
func (m *MockOrderRepository) SetZeroFullTimeDriverOrderRevision(ctx context.Context, driverID, orderID string, payAtStop int, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, driverID, orderID, payAtStop}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "SetZeroFullTimeDriverOrderRevision", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetZeroFullTimeDriverOrderRevision indicates an expected call of SetZeroFullTimeDriverOrderRevision.
func (mr *MockOrderRepositoryMockRecorder) SetZeroFullTimeDriverOrderRevision(ctx, driverID, orderID, payAtStop interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, driverID, orderID, payAtStop}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetZeroFullTimeDriverOrderRevision", reflect.TypeOf((*MockOrderRepository)(nil).SetZeroFullTimeDriverOrderRevision), varargs...)
}

// UnsetPauseFlag mocks base method.
func (m *MockOrderRepository) UnsetPauseFlag(ctx context.Context, orderID string, headTo int, pauseFlag model.Pause) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnsetPauseFlag", ctx, orderID, headTo, pauseFlag)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnsetPauseFlag indicates an expected call of UnsetPauseFlag.
func (mr *MockOrderRepositoryMockRecorder) UnsetPauseFlag(ctx, orderID, headTo, pauseFlag interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnsetPauseFlag", reflect.TypeOf((*MockOrderRepository)(nil).UnsetPauseFlag), ctx, orderID, headTo, pauseFlag)
}

// UpdateAndUpsertCanceledRevision mocks base method.
func (m *MockOrderRepository) UpdateAndUpsertCanceledRevision(ctx context.Context, order *model.Order, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAndUpsertCanceledRevision", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAndUpsertCanceledRevision indicates an expected call of UpdateAndUpsertCanceledRevision.
func (mr *MockOrderRepositoryMockRecorder) UpdateAndUpsertCanceledRevision(ctx, order interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAndUpsertCanceledRevision", reflect.TypeOf((*MockOrderRepository)(nil).UpdateAndUpsertCanceledRevision), varargs...)
}

// UpdateAndUpsertRevision mocks base method.
func (m *MockOrderRepository) UpdateAndUpsertRevision(ctx context.Context, order *model.Order, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateAndUpsertRevision", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAndUpsertRevision indicates an expected call of UpdateAndUpsertRevision.
func (mr *MockOrderRepositoryMockRecorder) UpdateAndUpsertRevision(ctx, order interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAndUpsertRevision", reflect.TypeOf((*MockOrderRepository)(nil).UpdateAndUpsertRevision), varargs...)
}

// UpdateExpireOrder mocks base method.
func (m *MockOrderRepository) UpdateExpireOrder(ctx context.Context, order *model.Order, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateExpireOrder", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateExpireOrder indicates an expected call of UpdateExpireOrder.
func (mr *MockOrderRepositoryMockRecorder) UpdateExpireOrder(ctx, order interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateExpireOrder", reflect.TypeOf((*MockOrderRepository)(nil).UpdateExpireOrder), varargs...)
}

// UpdateOrder mocks base method.
func (m *MockOrderRepository) UpdateOrder(ctx context.Context, order *model.Order, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateOrder", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrder indicates an expected call of UpdateOrder.
func (mr *MockOrderRepositoryMockRecorder) UpdateOrder(ctx, order interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrder", reflect.TypeOf((*MockOrderRepository)(nil).UpdateOrder), varargs...)
}

// UpdateOrderByField mocks base method.
func (m *MockOrderRepository) UpdateOrderByField(ctx context.Context, orderID string, updater model.OrderUpdater, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, updater}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateOrderByField", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderByField indicates an expected call of UpdateOrderByField.
func (mr *MockOrderRepositoryMockRecorder) UpdateOrderByField(ctx, orderID, updater interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, updater}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderByField", reflect.TypeOf((*MockOrderRepository)(nil).UpdateOrderByField), varargs...)
}

// UpdateOrderCancellationMetadata mocks base method.
func (m *MockOrderRepository) UpdateOrderCancellationMetadata(ctx context.Context, order *model.Order, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateOrderCancellationMetadata", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderCancellationMetadata indicates an expected call of UpdateOrderCancellationMetadata.
func (mr *MockOrderRepositoryMockRecorder) UpdateOrderCancellationMetadata(ctx, order interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderCancellationMetadata", reflect.TypeOf((*MockOrderRepository)(nil).UpdateOrderCancellationMetadata), varargs...)
}

// UpdateOrderRevisionByField mocks base method.
func (m *MockOrderRepository) UpdateOrderRevisionByField(ctx context.Context, orderID string, orderDeliveringRound int, updater model.OrderUpdater, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orderID, orderDeliveringRound, updater}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateOrderRevisionByField", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateOrderRevisionByField indicates an expected call of UpdateOrderRevisionByField.
func (mr *MockOrderRepositoryMockRecorder) UpdateOrderRevisionByField(ctx, orderID, orderDeliveringRound, updater interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orderID, orderDeliveringRound, updater}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOrderRevisionByField", reflect.TypeOf((*MockOrderRepository)(nil).UpdateOrderRevisionByField), varargs...)
}

// UpdateSkipPauseQRPayment mocks base method.
func (m *MockOrderRepository) UpdateSkipPauseQRPayment(ctx context.Context, orders []string, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, orders}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSkipPauseQRPayment", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSkipPauseQRPayment indicates an expected call of UpdateSkipPauseQRPayment.
func (mr *MockOrderRepositoryMockRecorder) UpdateSkipPauseQRPayment(ctx, orders interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, orders}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSkipPauseQRPayment", reflect.TypeOf((*MockOrderRepository)(nil).UpdateSkipPauseQRPayment), varargs...)
}

// UpdateVerifiedRiderPhoto mocks base method.
func (m *MockOrderRepository) UpdateVerifiedRiderPhoto(ctx context.Context, orderID, deviceID string, photoURLs []model.VerifiedRiderPhotoUrl) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateVerifiedRiderPhoto", ctx, orderID, deviceID, photoURLs)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateVerifiedRiderPhoto indicates an expected call of UpdateVerifiedRiderPhoto.
func (mr *MockOrderRepositoryMockRecorder) UpdateVerifiedRiderPhoto(ctx, orderID, deviceID, photoURLs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateVerifiedRiderPhoto", reflect.TypeOf((*MockOrderRepository)(nil).UpdateVerifiedRiderPhoto), ctx, orderID, deviceID, photoURLs)
}

// UpsertCancelRevision mocks base method.
func (m *MockOrderRepository) UpsertCancelRevision(ctx context.Context, order model.Order, detail model.CancelDetail, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order, detail}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpsertCancelRevision", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertCancelRevision indicates an expected call of UpsertCancelRevision.
func (mr *MockOrderRepositoryMockRecorder) UpsertCancelRevision(ctx, order, detail interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order, detail}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertCancelRevision", reflect.TypeOf((*MockOrderRepository)(nil).UpsertCancelRevision), varargs...)
}
