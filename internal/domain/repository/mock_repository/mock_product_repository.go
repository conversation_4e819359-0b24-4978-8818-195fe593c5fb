// Code generated by MockGen. DO NOT EDIT.
// Source: ./product_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
)

// MockProductQuery is a mock of ProductQuery interface.
type MockProductQuery struct {
	ctrl     *gomock.Controller
	recorder *MockProductQueryMockRecorder
}

// MockProductQueryMockRecorder is the mock recorder for MockProductQuery.
type MockProductQueryMockRecorder struct {
	mock *MockProductQuery
}

// NewMockProductQuery creates a new mock instance.
func NewMockProductQuery(ctrl *gomock.Controller) *MockProductQuery {
	mock := &MockProductQuery{ctrl: ctrl}
	mock.recorder = &MockProductQueryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProductQuery) EXPECT() *MockProductQueryMockRecorder {
	return m.recorder
}

// WithName mocks base method.
func (m *MockProductQuery) WithName(name string) repository.ProductQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithName", name)
	ret0, _ := ret[0].(repository.ProductQuery)
	return ret0
}

// WithName indicates an expected call of WithName.
func (mr *MockProductQueryMockRecorder) WithName(name interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithName", reflect.TypeOf((*MockProductQuery)(nil).WithName), name)
}

// WithPrice mocks base method.
func (m *MockProductQuery) WithPrice(min, max float64) repository.ProductQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithPrice", min, max)
	ret0, _ := ret[0].(repository.ProductQuery)
	return ret0
}

// WithPrice indicates an expected call of WithPrice.
func (mr *MockProductQueryMockRecorder) WithPrice(min, max interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithPrice", reflect.TypeOf((*MockProductQuery)(nil).WithPrice), min, max)
}

// WithPrioritySort mocks base method.
func (m *MockProductQuery) WithPrioritySort(sort string) repository.ProductQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithPrioritySort", sort)
	ret0, _ := ret[0].(repository.ProductQuery)
	return ret0
}

// WithPrioritySort indicates an expected call of WithPrioritySort.
func (mr *MockProductQueryMockRecorder) WithPrioritySort(sort interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithPrioritySort", reflect.TypeOf((*MockProductQuery)(nil).WithPrioritySort), sort)
}

// WithProductGroup mocks base method.
func (m *MockProductQuery) WithProductGroup(productGroup string) repository.ProductQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithProductGroup", productGroup)
	ret0, _ := ret[0].(repository.ProductQuery)
	return ret0
}

// WithProductGroup indicates an expected call of WithProductGroup.
func (mr *MockProductQueryMockRecorder) WithProductGroup(productGroup interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithProductGroup", reflect.TypeOf((*MockProductQuery)(nil).WithProductGroup), productGroup)
}

// WithSKU mocks base method.
func (m *MockProductQuery) WithSKU(SKU string) repository.ProductQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithSKU", SKU)
	ret0, _ := ret[0].(repository.ProductQuery)
	return ret0
}

// WithSKU indicates an expected call of WithSKU.
func (mr *MockProductQueryMockRecorder) WithSKU(SKU interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithSKU", reflect.TypeOf((*MockProductQuery)(nil).WithSKU), SKU)
}

// MockProductRepository is a mock of ProductRepository interface.
type MockProductRepository struct {
	ctrl     *gomock.Controller
	recorder *MockProductRepositoryMockRecorder
}

// MockProductRepositoryMockRecorder is the mock recorder for MockProductRepository.
type MockProductRepositoryMockRecorder struct {
	mock *MockProductRepository
}

// NewMockProductRepository creates a new mock instance.
func NewMockProductRepository(ctrl *gomock.Controller) *MockProductRepository {
	mock := &MockProductRepository{ctrl: ctrl}
	mock.recorder = &MockProductRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProductRepository) EXPECT() *MockProductRepositoryMockRecorder {
	return m.recorder
}

// CountProductWithQuery mocks base method.
func (m *MockProductRepository) CountProductWithQuery(ctx context.Context, query repository.ProductQuery, opts ...repository.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CountProductWithQuery", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountProductWithQuery indicates an expected call of CountProductWithQuery.
func (mr *MockProductRepositoryMockRecorder) CountProductWithQuery(ctx, query interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountProductWithQuery", reflect.TypeOf((*MockProductRepository)(nil).CountProductWithQuery), varargs...)
}

// CreateAll mocks base method.
func (m *MockProductRepository) CreateAll(ctx context.Context, products []model.Product, opts ...repository.Option) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, products}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAll", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAll indicates an expected call of CreateAll.
func (mr *MockProductRepositoryMockRecorder) CreateAll(ctx, products interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, products}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAll", reflect.TypeOf((*MockProductRepository)(nil).CreateAll), varargs...)
}

// FindByIDAndLookupInstallment mocks base method.
func (m *MockProductRepository) FindByIDAndLookupInstallment(ctx context.Context, id string, opts ...repository.Option) (*model.Product, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByIDAndLookupInstallment", varargs...)
	ret0, _ := ret[0].(*model.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDAndLookupInstallment indicates an expected call of FindByIDAndLookupInstallment.
func (mr *MockProductRepositoryMockRecorder) FindByIDAndLookupInstallment(ctx, id interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDAndLookupInstallment", reflect.TypeOf((*MockProductRepository)(nil).FindByIDAndLookupInstallment), varargs...)
}

// FindByIDsAndLookupInstallment mocks base method.
func (m *MockProductRepository) FindByIDsAndLookupInstallment(ctx context.Context, productIDs []primitive.ObjectID, skip, limit int, opts ...repository.Option) ([]model.Product, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, productIDs, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByIDsAndLookupInstallment", varargs...)
	ret0, _ := ret[0].([]model.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDsAndLookupInstallment indicates an expected call of FindByIDsAndLookupInstallment.
func (mr *MockProductRepositoryMockRecorder) FindByIDsAndLookupInstallment(ctx, productIDs, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, productIDs, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDsAndLookupInstallment", reflect.TypeOf((*MockProductRepository)(nil).FindByIDsAndLookupInstallment), varargs...)
}

// FindProductsBySKUs mocks base method.
func (m *MockProductRepository) FindProductsBySKUs(ctx context.Context, skus []string, opts ...repository.Option) (map[string]*model.Product, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, skus}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindProductsBySKUs", varargs...)
	ret0, _ := ret[0].(map[string]*model.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindProductsBySKUs indicates an expected call of FindProductsBySKUs.
func (mr *MockProductRepositoryMockRecorder) FindProductsBySKUs(ctx, skus interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, skus}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindProductsBySKUs", reflect.TypeOf((*MockProductRepository)(nil).FindProductsBySKUs), varargs...)
}

// FindWithQueryAndSort mocks base method.
func (m *MockProductRepository) FindWithQueryAndSort(ctx context.Context, query repository.ProductQuery, skip, limit int, sort []string, opts ...repository.Option) ([]model.Product, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit, sort}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindWithQueryAndSort", varargs...)
	ret0, _ := ret[0].([]model.Product)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindWithQueryAndSort indicates an expected call of FindWithQueryAndSort.
func (mr *MockProductRepositoryMockRecorder) FindWithQueryAndSort(ctx, query, skip, limit, sort interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit, sort}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindWithQueryAndSort", reflect.TypeOf((*MockProductRepository)(nil).FindWithQueryAndSort), varargs...)
}

// Update mocks base method.
func (m *MockProductRepository) Update(ctx context.Context, product *model.Product) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, product)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockProductRepositoryMockRecorder) Update(ctx, product interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockProductRepository)(nil).Update), ctx, product)
}

// UpdateAll mocks base method.
func (m *MockProductRepository) UpdateAll(ctx context.Context, products []model.Product) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateAll", ctx, products)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateAll indicates an expected call of UpdateAll.
func (mr *MockProductRepositoryMockRecorder) UpdateAll(ctx, products interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateAll", reflect.TypeOf((*MockProductRepository)(nil).UpdateAll), ctx, products)
}
