// Code generated by MockGen. DO NOT EDIT.
// Source: ./repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	config "git.wndv.co/lineman/fleet-distribution/internal/config"
	gomock "github.com/golang/mock/gomock"
)

// MockDBConfigRepository is a mock of DBConfigRepository interface.
type MockDBConfigRepository struct {
	ctrl     *gomock.Controller
	recorder *MockDBConfigRepositoryMockRecorder
}

// MockDBConfigRepositoryMockRecorder is the mock recorder for MockDBConfigRepository.
type MockDBConfigRepositoryMockRecorder struct {
	mock *MockDBConfigRepository
}

// NewMockDBConfigRepository creates a new mock instance.
func NewMockDBConfigRepository(ctrl *gomock.Controller) *MockDBConfigRepository {
	mock := &MockDBConfigRepository{ctrl: ctrl}
	mock.recorder = &MockDBConfigRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDBConfigRepository) EXPECT() *MockDBConfigRepositoryMockRecorder {
	return m.recorder
}

// Find mocks base method.
func (m *MockDBConfigRepository) Find(ctx context.Context) (*config.DBConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Find", ctx)
	ret0, _ := ret[0].(*config.DBConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockDBConfigRepositoryMockRecorder) Find(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockDBConfigRepository)(nil).Find), ctx)
}

// FindAll mocks base method.
func (m *MockDBConfigRepository) FindAll(ctx context.Context) (*config.DBConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindAll", ctx)
	ret0, _ := ret[0].(*config.DBConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindAll indicates an expected call of FindAll.
func (mr *MockDBConfigRepositoryMockRecorder) FindAll(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindAll", reflect.TypeOf((*MockDBConfigRepository)(nil).FindAll), ctx)
}

// UpdateOne mocks base method.
func (m *MockDBConfigRepository) UpdateOne(ctx context.Context, key string, value any) (*config.DBConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateOne", ctx, key, value)
	ret0, _ := ret[0].(*config.DBConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateOne indicates an expected call of UpdateOne.
func (mr *MockDBConfigRepositoryMockRecorder) UpdateOne(ctx, key, value interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateOne", reflect.TypeOf((*MockDBConfigRepository)(nil).UpdateOne), ctx, key, value)
}

// Upsert mocks base method.
func (m *MockDBConfigRepository) Upsert(ctx context.Context, newDbConfig *config.DBConfig) (*config.DBConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upsert", ctx, newDbConfig)
	ret0, _ := ret[0].(*config.DBConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Upsert indicates an expected call of Upsert.
func (mr *MockDBConfigRepositoryMockRecorder) Upsert(ctx, newDbConfig interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upsert", reflect.TypeOf((*MockDBConfigRepository)(nil).Upsert), ctx, newDbConfig)
}
