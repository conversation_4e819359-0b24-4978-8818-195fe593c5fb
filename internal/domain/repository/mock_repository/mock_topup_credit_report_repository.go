// Code generated by MockGen. DO NOT EDIT.
// Source: ./topup_credit_report_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockTopupCreditReportRepository is a mock of TopupCreditReportRepository interface.
type MockTopupCreditReportRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTopupCreditReportRepositoryMockRecorder
}

// MockTopupCreditReportRepositoryMockRecorder is the mock recorder for MockTopupCreditReportRepository.
type MockTopupCreditReportRepositoryMockRecorder struct {
	mock *MockTopupCreditReportRepository
}

// NewMockTopupCreditReportRepository creates a new mock instance.
func NewMockTopupCreditReportRepository(ctrl *gomock.Controller) *MockTopupCreditReportRepository {
	mock := &MockTopupCreditReportRepository{ctrl: ctrl}
	mock.recorder = &MockTopupCreditReportRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTopupCreditReportRepository) EXPECT() *MockTopupCreditReportRepositoryMockRecorder {
	return m.recorder
}

// CountByQuery mocks base method.
func (m *MockTopupCreditReportRepository) CountByQuery(ctx context.Context, q repository.TopupCreditReportQuery, opt ...repository.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, q}
	for _, a := range opt {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CountByQuery", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByQuery indicates an expected call of CountByQuery.
func (mr *MockTopupCreditReportRepositoryMockRecorder) CountByQuery(ctx, q interface{}, opt ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, q}, opt...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByQuery", reflect.TypeOf((*MockTopupCreditReportRepository)(nil).CountByQuery), varargs...)
}

// CreateAll mocks base method.
func (m_2 *MockTopupCreditReportRepository) CreateAll(ctx context.Context, m []model.TopupCreditReport) []string {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "CreateAll", ctx, m)
	ret0, _ := ret[0].([]string)
	return ret0
}

// CreateAll indicates an expected call of CreateAll.
func (mr *MockTopupCreditReportRepositoryMockRecorder) CreateAll(ctx, m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAll", reflect.TypeOf((*MockTopupCreditReportRepository)(nil).CreateAll), ctx, m)
}

// GetByQuery mocks base method.
func (m *MockTopupCreditReportRepository) GetByQuery(ctx context.Context, q repository.TopupCreditReportQuery, opt ...repository.Option) ([]model.TopupCreditReport, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, q}
	for _, a := range opt {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByQuery", varargs...)
	ret0, _ := ret[0].([]model.TopupCreditReport)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByQuery indicates an expected call of GetByQuery.
func (mr *MockTopupCreditReportRepositoryMockRecorder) GetByQuery(ctx, q interface{}, opt ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, q}, opt...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByQuery", reflect.TypeOf((*MockTopupCreditReportRepository)(nil).GetByQuery), varargs...)
}

// InsertAll mocks base method.
func (m_2 *MockTopupCreditReportRepository) InsertAll(ctx context.Context, m []model.TopupCreditReport) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "InsertAll", ctx, m)
	ret0, _ := ret[0].(error)
	return ret0
}

// InsertAll indicates an expected call of InsertAll.
func (mr *MockTopupCreditReportRepositoryMockRecorder) InsertAll(ctx, m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InsertAll", reflect.TypeOf((*MockTopupCreditReportRepository)(nil).InsertAll), ctx, m)
}

// IterateAllOccurredBetween mocks base method.
func (m *MockTopupCreditReportRepository) IterateAllOccurredBetween(ctx context.Context, fromInclusive, toInclusive time.Time) (repository.Cursor, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IterateAllOccurredBetween", ctx, fromInclusive, toInclusive)
	ret0, _ := ret[0].(repository.Cursor)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IterateAllOccurredBetween indicates an expected call of IterateAllOccurredBetween.
func (mr *MockTopupCreditReportRepositoryMockRecorder) IterateAllOccurredBetween(ctx, fromInclusive, toInclusive interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IterateAllOccurredBetween", reflect.TypeOf((*MockTopupCreditReportRepository)(nil).IterateAllOccurredBetween), ctx, fromInclusive, toInclusive)
}

// UpsertAllWithMatchedTransactionId mocks base method.
func (m_2 *MockTopupCreditReportRepository) UpsertAllWithMatchedTransactionId(ctx context.Context, m []model.TopupCreditReport) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "UpsertAllWithMatchedTransactionId", ctx, m)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpsertAllWithMatchedTransactionId indicates an expected call of UpsertAllWithMatchedTransactionId.
func (mr *MockTopupCreditReportRepositoryMockRecorder) UpsertAllWithMatchedTransactionId(ctx, m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpsertAllWithMatchedTransactionId", reflect.TypeOf((*MockTopupCreditReportRepository)(nil).UpsertAllWithMatchedTransactionId), ctx, m)
}

// MockCursor is a mock of Cursor interface.
type MockCursor struct {
	ctrl     *gomock.Controller
	recorder *MockCursorMockRecorder
}

// MockCursorMockRecorder is the mock recorder for MockCursor.
type MockCursorMockRecorder struct {
	mock *MockCursor
}

// NewMockCursor creates a new mock instance.
func NewMockCursor(ctrl *gomock.Controller) *MockCursor {
	mock := &MockCursor{ctrl: ctrl}
	mock.recorder = &MockCursorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCursor) EXPECT() *MockCursorMockRecorder {
	return m.recorder
}

// Close mocks base method.
func (m *MockCursor) Close(ctx context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close", ctx)
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockCursorMockRecorder) Close(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockCursor)(nil).Close), ctx)
}

// Decode mocks base method.
func (m *MockCursor) Decode(val interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Decode", val)
	ret0, _ := ret[0].(error)
	return ret0
}

// Decode indicates an expected call of Decode.
func (mr *MockCursorMockRecorder) Decode(val interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Decode", reflect.TypeOf((*MockCursor)(nil).Decode), val)
}

// Next mocks base method.
func (m *MockCursor) Next(ctx context.Context) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Next", ctx)
	ret0, _ := ret[0].(bool)
	return ret0
}

// Next indicates an expected call of Next.
func (mr *MockCursorMockRecorder) Next(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Next", reflect.TypeOf((*MockCursor)(nil).Next), ctx)
}
