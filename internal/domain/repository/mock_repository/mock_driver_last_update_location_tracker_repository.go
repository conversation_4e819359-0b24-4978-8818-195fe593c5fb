// Code generated by MockGen. DO NOT EDIT.
// Source: ./driver_last_update_location_tracker_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockDriverLastUpdateLocationTrackerRepository is a mock of DriverLastUpdateLocationTrackerRepository interface.
type MockDriverLastUpdateLocationTrackerRepository struct {
	ctrl     *gomock.Controller
	recorder *MockDriverLastUpdateLocationTrackerRepositoryMockRecorder
}

// MockDriverLastUpdateLocationTrackerRepositoryMockRecorder is the mock recorder for MockDriverLastUpdateLocationTrackerRepository.
type MockDriverLastUpdateLocationTrackerRepositoryMockRecorder struct {
	mock *MockDriverLastUpdateLocationTrackerRepository
}

// NewMockDriverLastUpdateLocationTrackerRepository creates a new mock instance.
func NewMockDriverLastUpdateLocationTrackerRepository(ctrl *gomock.Controller) *MockDriverLastUpdateLocationTrackerRepository {
	mock := &MockDriverLastUpdateLocationTrackerRepository{ctrl: ctrl}
	mock.recorder = &MockDriverLastUpdateLocationTrackerRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverLastUpdateLocationTrackerRepository) EXPECT() *MockDriverLastUpdateLocationTrackerRepositoryMockRecorder {
	return m.recorder
}

// GetDriverKeySlot mocks base method.
func (m *MockDriverLastUpdateLocationTrackerRepository) GetDriverKeySlot(driverID string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDriverKeySlot", driverID)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetDriverKeySlot indicates an expected call of GetDriverKeySlot.
func (mr *MockDriverLastUpdateLocationTrackerRepositoryMockRecorder) GetDriverKeySlot(driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDriverKeySlot", reflect.TypeOf((*MockDriverLastUpdateLocationTrackerRepository)(nil).GetDriverKeySlot), driverID)
}

// GetDriverLatestUpdateLocationAttempt mocks base method.
func (m *MockDriverLastUpdateLocationTrackerRepository) GetDriverLatestUpdateLocationAttempt(ctx context.Context, driverID string) (time.Time, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDriverLatestUpdateLocationAttempt", ctx, driverID)
	ret0, _ := ret[0].(time.Time)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDriverLatestUpdateLocationAttempt indicates an expected call of GetDriverLatestUpdateLocationAttempt.
func (mr *MockDriverLastUpdateLocationTrackerRepositoryMockRecorder) GetDriverLatestUpdateLocationAttempt(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDriverLatestUpdateLocationAttempt", reflect.TypeOf((*MockDriverLastUpdateLocationTrackerRepository)(nil).GetDriverLatestUpdateLocationAttempt), ctx, driverID)
}

// GetExpiredDriverID mocks base method.
func (m *MockDriverLastUpdateLocationTrackerRepository) GetExpiredDriverID(ctx context.Context, keyslot string, ttl, t time.Duration) ([]model.DriverLastUpdateLocationAttempt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetExpiredDriverID", ctx, keyslot, ttl, t)
	ret0, _ := ret[0].([]model.DriverLastUpdateLocationAttempt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetExpiredDriverID indicates an expected call of GetExpiredDriverID.
func (mr *MockDriverLastUpdateLocationTrackerRepositoryMockRecorder) GetExpiredDriverID(ctx, keyslot, ttl, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetExpiredDriverID", reflect.TypeOf((*MockDriverLastUpdateLocationTrackerRepository)(nil).GetExpiredDriverID), ctx, keyslot, ttl, t)
}

// GetKeySlot mocks base method.
func (m *MockDriverLastUpdateLocationTrackerRepository) GetKeySlot() []string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKeySlot")
	ret0, _ := ret[0].([]string)
	return ret0
}

// GetKeySlot indicates an expected call of GetKeySlot.
func (mr *MockDriverLastUpdateLocationTrackerRepositoryMockRecorder) GetKeySlot() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKeySlot", reflect.TypeOf((*MockDriverLastUpdateLocationTrackerRepository)(nil).GetKeySlot))
}

// RemoveDriverIDs mocks base method.
func (m *MockDriverLastUpdateLocationTrackerRepository) RemoveDriverIDs(ctx context.Context, key string, driverIDs []string) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveDriverIDs", ctx, key, driverIDs)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RemoveDriverIDs indicates an expected call of RemoveDriverIDs.
func (mr *MockDriverLastUpdateLocationTrackerRepositoryMockRecorder) RemoveDriverIDs(ctx, key, driverIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveDriverIDs", reflect.TypeOf((*MockDriverLastUpdateLocationTrackerRepository)(nil).RemoveDriverIDs), ctx, key, driverIDs)
}

// UpdateLatestLocationAttempt mocks base method.
func (m *MockDriverLastUpdateLocationTrackerRepository) UpdateLatestLocationAttempt(ctx context.Context, driverID string, t time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateLatestLocationAttempt", ctx, driverID, t)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateLatestLocationAttempt indicates an expected call of UpdateLatestLocationAttempt.
func (mr *MockDriverLastUpdateLocationTrackerRepositoryMockRecorder) UpdateLatestLocationAttempt(ctx, driverID, t interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateLatestLocationAttempt", reflect.TypeOf((*MockDriverLastUpdateLocationTrackerRepository)(nil).UpdateLatestLocationAttempt), ctx, driverID, t)
}
