// Code generated by MockGen. DO NOT EDIT.
// Source: ./throttled_dispatch_detail_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
	primitive "go.mongodb.org/mongo-driver/bson/primitive"
)

// MockThrottledDispatchDetailRepository is a mock of ThrottledDispatchDetailRepository interface.
type MockThrottledDispatchDetailRepository struct {
	ctrl     *gomock.Controller
	recorder *MockThrottledDispatchDetailRepositoryMockRecorder
}

// MockThrottledDispatchDetailRepositoryMockRecorder is the mock recorder for MockThrottledDispatchDetailRepository.
type MockThrottledDispatchDetailRepositoryMockRecorder struct {
	mock *MockThrottledDispatchDetailRepository
}

// NewMockThrottledDispatchDetailRepository creates a new mock instance.
func NewMockThrottledDispatchDetailRepository(ctrl *gomock.Controller) *MockThrottledDispatchDetailRepository {
	mock := &MockThrottledDispatchDetailRepository{ctrl: ctrl}
	mock.recorder = &MockThrottledDispatchDetailRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockThrottledDispatchDetailRepository) EXPECT() *MockThrottledDispatchDetailRepositoryMockRecorder {
	return m.recorder
}

// Count mocks base method.
func (m *MockThrottledDispatchDetailRepository) Count(ctx context.Context, opts ...repository.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Count", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockThrottledDispatchDetailRepositoryMockRecorder) Count(ctx interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockThrottledDispatchDetailRepository)(nil).Count), varargs...)
}

// Create mocks base method.
func (m *MockThrottledDispatchDetailRepository) Create(ctx context.Context, detail *model.ThrottledDispatchDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, detail)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockThrottledDispatchDetailRepositoryMockRecorder) Create(ctx, detail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockThrottledDispatchDetailRepository)(nil).Create), ctx, detail)
}

// Find mocks base method.
func (m *MockThrottledDispatchDetailRepository) Find(ctx context.Context, skip, limit int, opts ...repository.Option) ([]model.ThrottledDispatchDetailWithZoneCode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.ThrottledDispatchDetailWithZoneCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockThrottledDispatchDetailRepositoryMockRecorder) Find(ctx, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockThrottledDispatchDetailRepository)(nil).Find), varargs...)
}

// FindByZoneID mocks base method.
func (m *MockThrottledDispatchDetailRepository) FindByZoneID(ctx context.Context, zoneID primitive.ObjectID, opts ...repository.Option) (model.ThrottledDispatchDetailWithZoneCode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, zoneID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByZoneID", varargs...)
	ret0, _ := ret[0].(model.ThrottledDispatchDetailWithZoneCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByZoneID indicates an expected call of FindByZoneID.
func (mr *MockThrottledDispatchDetailRepositoryMockRecorder) FindByZoneID(ctx, zoneID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, zoneID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByZoneID", reflect.TypeOf((*MockThrottledDispatchDetailRepository)(nil).FindByZoneID), varargs...)
}

// FindManyFromLocation mocks base method.
func (m *MockThrottledDispatchDetailRepository) FindManyFromLocation(ctx context.Context, location model.Location, distributeRegions model.DistributeRegions, opts ...repository.Option) ([]model.ThrottledDispatchDetailWithZoneCode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, location, distributeRegions}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindManyFromLocation", varargs...)
	ret0, _ := ret[0].([]model.ThrottledDispatchDetailWithZoneCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindManyFromLocation indicates an expected call of FindManyFromLocation.
func (mr *MockThrottledDispatchDetailRepositoryMockRecorder) FindManyFromLocation(ctx, location, distributeRegions interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, location, distributeRegions}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindManyFromLocation", reflect.TypeOf((*MockThrottledDispatchDetailRepository)(nil).FindManyFromLocation), varargs...)
}

// FindOneFromLocation mocks base method.
func (m *MockThrottledDispatchDetailRepository) FindOneFromLocation(ctx context.Context, location model.Location, distributeRegions model.DistributeRegions, opts ...repository.Option) (*model.ThrottledDispatchDetailWithZoneCode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, location, distributeRegions}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindOneFromLocation", varargs...)
	ret0, _ := ret[0].(*model.ThrottledDispatchDetailWithZoneCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneFromLocation indicates an expected call of FindOneFromLocation.
func (mr *MockThrottledDispatchDetailRepositoryMockRecorder) FindOneFromLocation(ctx, location, distributeRegions interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, location, distributeRegions}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneFromLocation", reflect.TypeOf((*MockThrottledDispatchDetailRepository)(nil).FindOneFromLocation), varargs...)
}

// FindOneFromOrder mocks base method.
func (m *MockThrottledDispatchDetailRepository) FindOneFromOrder(ctx context.Context, order *model.Order, opts ...repository.Option) (*model.ThrottledDispatchDetailWithZoneCode, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindOneFromOrder", varargs...)
	ret0, _ := ret[0].(*model.ThrottledDispatchDetailWithZoneCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneFromOrder indicates an expected call of FindOneFromOrder.
func (mr *MockThrottledDispatchDetailRepositoryMockRecorder) FindOneFromOrder(ctx, order interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneFromOrder", reflect.TypeOf((*MockThrottledDispatchDetailRepository)(nil).FindOneFromOrder), varargs...)
}

// Update mocks base method.
func (m *MockThrottledDispatchDetailRepository) Update(ctx context.Context, detail *model.ThrottledDispatchDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, detail)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockThrottledDispatchDetailRepositoryMockRecorder) Update(ctx, detail interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockThrottledDispatchDetailRepository)(nil).Update), ctx, detail)
}
