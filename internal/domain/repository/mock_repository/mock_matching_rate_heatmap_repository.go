// Code generated by MockGen. DO NOT EDIT.
// Source: ./matching_rate_heatmap_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockMatchingRateHeatMapRepository is a mock of MatchingRateHeatMapRepository interface.
type MockMatchingRateHeatMapRepository struct {
	ctrl     *gomock.Controller
	recorder *MockMatchingRateHeatMapRepositoryMockRecorder
}

// MockMatchingRateHeatMapRepositoryMockRecorder is the mock recorder for MockMatchingRateHeatMapRepository.
type MockMatchingRateHeatMapRepositoryMockRecorder struct {
	mock *MockMatchingRateHeatMapRepository
}

// NewMockMatchingRateHeatMapRepository creates a new mock instance.
func NewMockMatchingRateHeatMapRepository(ctrl *gomock.Controller) *MockMatchingRateHeatMapRepository {
	mock := &MockMatchingRateHeatMapRepository{ctrl: ctrl}
	mock.recorder = &MockMatchingRateHeatMapRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMatchingRateHeatMapRepository) EXPECT() *MockMatchingRateHeatMapRepositoryMockRecorder {
	return m.recorder
}

// Find mocks base method.
func (m *MockMatchingRateHeatMapRepository) Find(query repository.HeatMapQuery, opts ...repository.Option) ([]model.MatchingRateHeatMap, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{query}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.MatchingRateHeatMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockMatchingRateHeatMapRepositoryMockRecorder) Find(query interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{query}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockMatchingRateHeatMapRepository)(nil).Find), varargs...)
}

// RemoveAll mocks base method.
func (m *MockMatchingRateHeatMapRepository) RemoveAll() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveAll")
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveAll indicates an expected call of RemoveAll.
func (mr *MockMatchingRateHeatMapRepositoryMockRecorder) RemoveAll() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveAll", reflect.TypeOf((*MockMatchingRateHeatMapRepository)(nil).RemoveAll))
}

// SaveAll mocks base method.
func (m *MockMatchingRateHeatMapRepository) SaveAll(hm []model.MatchingRateHeatMap) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SaveAll", hm)
	ret0, _ := ret[0].(error)
	return ret0
}

// SaveAll indicates an expected call of SaveAll.
func (mr *MockMatchingRateHeatMapRepositoryMockRecorder) SaveAll(hm interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SaveAll", reflect.TypeOf((*MockMatchingRateHeatMapRepository)(nil).SaveAll), hm)
}
