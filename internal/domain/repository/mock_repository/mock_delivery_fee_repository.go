// Code generated by MockGen. DO NOT EDIT.
// Source: ./delivery_fee_setting_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
	bson "go.mongodb.org/mongo-driver/bson"
)

// MockDeliveryFeeSettingRepository is a mock of DeliveryFeeSettingRepository interface.
type MockDeliveryFeeSettingRepository struct {
	ctrl     *gomock.Controller
	recorder *MockDeliveryFeeSettingRepositoryMockRecorder
}

// MockDeliveryFeeSettingRepositoryMockRecorder is the mock recorder for MockDeliveryFeeSettingRepository.
type MockDeliveryFeeSettingRepositoryMockRecorder struct {
	mock *MockDeliveryFeeSettingRepository
}

// NewMockDeliveryFeeSettingRepository creates a new mock instance.
func NewMockDeliveryFeeSettingRepository(ctrl *gomock.Controller) *MockDeliveryFeeSettingRepository {
	mock := &MockDeliveryFeeSettingRepository{ctrl: ctrl}
	mock.recorder = &MockDeliveryFeeSettingRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeliveryFeeSettingRepository) EXPECT() *MockDeliveryFeeSettingRepositoryMockRecorder {
	return m.recorder
}

// Count mocks base method.
func (m *MockDeliveryFeeSettingRepository) Count(ctx context.Context, query bson.M, opts ...repository.Option) (int, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Count", varargs...)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) Count(ctx, query interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).Count), varargs...)
}

// Create mocks base method.
func (m *MockDeliveryFeeSettingRepository) Create(ctx context.Context, setting *model.DeliveryFeeSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, setting)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) Create(ctx, setting interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).Create), ctx, setting)
}

// FindByPriceSchemeRefId mocks base method.
func (m *MockDeliveryFeeSettingRepository) FindByPriceSchemeRefId(ctx context.Context, id string, opts ...repository.Option) ([]model.DeliveryFeeSetting, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindByPriceSchemeRefId", varargs...)
	ret0, _ := ret[0].([]model.DeliveryFeeSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByPriceSchemeRefId indicates an expected call of FindByPriceSchemeRefId.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) FindByPriceSchemeRefId(ctx, id interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByPriceSchemeRefId", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).FindByPriceSchemeRefId), varargs...)
}

// Get mocks base method.
func (m *MockDeliveryFeeSettingRepository) Get(ctx context.Context, id string, opts ...repository.Option) (model.DeliveryFeeSetting, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, id}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(model.DeliveryFeeSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) Get(ctx, id interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, id}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).Get), varargs...)
}

// GetByRegionAndService mocks base method.
func (m *MockDeliveryFeeSettingRepository) GetByRegionAndService(ctx context.Context, region string, serviceType model.Service, opts ...repository.Option) (model.DeliveryFeeSetting, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, region, serviceType}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetByRegionAndService", varargs...)
	ret0, _ := ret[0].(model.DeliveryFeeSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByRegionAndService indicates an expected call of GetByRegionAndService.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) GetByRegionAndService(ctx, region, serviceType interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, region, serviceType}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByRegionAndService", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).GetByRegionAndService), varargs...)
}

// IsExistsByRegionAndService mocks base method.
func (m *MockDeliveryFeeSettingRepository) IsExistsByRegionAndService(ctx context.Context, region string, serviceType model.Service, opts ...repository.Option) bool {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, region, serviceType}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsExistsByRegionAndService", varargs...)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsExistsByRegionAndService indicates an expected call of IsExistsByRegionAndService.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) IsExistsByRegionAndService(ctx, region, serviceType interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, region, serviceType}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExistsByRegionAndService", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).IsExistsByRegionAndService), varargs...)
}

// List mocks base method.
func (m *MockDeliveryFeeSettingRepository) List(ctx context.Context, opts ...repository.Option) ([]model.DeliveryFeeSetting, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].([]model.DeliveryFeeSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// List indicates an expected call of List.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) List(ctx interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).List), varargs...)
}

// ListAndSort mocks base method.
func (m *MockDeliveryFeeSettingRepository) ListAndSort(ctx context.Context, query bson.M, limit, skip int, sort []string, pts ...repository.Option) ([]model.DeliveryFeeSetting, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, limit, skip, sort}
	for _, a := range pts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ListAndSort", varargs...)
	ret0, _ := ret[0].([]model.DeliveryFeeSetting)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListAndSort indicates an expected call of ListAndSort.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) ListAndSort(ctx, query, limit, skip, sort interface{}, pts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, limit, skip, sort}, pts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListAndSort", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).ListAndSort), varargs...)
}

// Update mocks base method.
func (m *MockDeliveryFeeSettingRepository) Update(ctx context.Context, setting *model.DeliveryFeeSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, setting)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockDeliveryFeeSettingRepositoryMockRecorder) Update(ctx, setting interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockDeliveryFeeSettingRepository)(nil).Update), ctx, setting)
}
