// Code generated by MockGen. DO NOT EDIT.
// Source: ./accounting_hub_transaction_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockAccountingHubTransactionRepository is a mock of AccountingHubTransactionRepository interface.
type MockAccountingHubTransactionRepository struct {
	ctrl     *gomock.Controller
	recorder *MockAccountingHubTransactionRepositoryMockRecorder
}

// MockAccountingHubTransactionRepositoryMockRecorder is the mock recorder for MockAccountingHubTransactionRepository.
type MockAccountingHubTransactionRepositoryMockRecorder struct {
	mock *MockAccountingHubTransactionRepository
}

// NewMockAccountingHubTransactionRepository creates a new mock instance.
func NewMockAccountingHubTransactionRepository(ctrl *gomock.Controller) *MockAccountingHubTransactionRepository {
	mock := &MockAccountingHubTransactionRepository{ctrl: ctrl}
	mock.recorder = &MockAccountingHubTransactionRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAccountingHubTransactionRepository) EXPECT() *MockAccountingHubTransactionRepositoryMockRecorder {
	return m.recorder
}

// CreateAll mocks base method.
func (m *MockAccountingHubTransactionRepository) CreateAll(ctx context.Context, transactions ...model.AccountingHubTransaction) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range transactions {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAll", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAll indicates an expected call of CreateAll.
func (mr *MockAccountingHubTransactionRepositoryMockRecorder) CreateAll(ctx interface{}, transactions ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, transactions...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAll", reflect.TypeOf((*MockAccountingHubTransactionRepository)(nil).CreateAll), varargs...)
}
