// Code generated by MockGen. DO NOT EDIT.
// Source: ./transaction_fraud_score_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockTransactionFraudScoreRepository is a mock of TransactionFraudScoreRepository interface.
type MockTransactionFraudScoreRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTransactionFraudScoreRepositoryMockRecorder
}

// MockTransactionFraudScoreRepositoryMockRecorder is the mock recorder for MockTransactionFraudScoreRepository.
type MockTransactionFraudScoreRepositoryMockRecorder struct {
	mock *MockTransactionFraudScoreRepository
}

// NewMockTransactionFraudScoreRepository creates a new mock instance.
func NewMockTransactionFraudScoreRepository(ctrl *gomock.Controller) *MockTransactionFraudScoreRepository {
	mock := &MockTransactionFraudScoreRepository{ctrl: ctrl}
	mock.recorder = &MockTransactionFraudScoreRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTransactionFraudScoreRepository) EXPECT() *MockTransactionFraudScoreRepositoryMockRecorder {
	return m.recorder
}

// Create mocks base method.
func (m *MockTransactionFraudScoreRepository) Create(ctx context.Context, txnFraudScore *model.TransactionFraudScore) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, txnFraudScore)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockTransactionFraudScoreRepositoryMockRecorder) Create(ctx, txnFraudScore interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockTransactionFraudScoreRepository)(nil).Create), ctx, txnFraudScore)
}

// FindByIDs mocks base method.
func (m *MockTransactionFraudScoreRepository) FindByIDs(ctx context.Context, transactionIds []string) ([]model.TransactionFraudScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindByIDs", ctx, transactionIds)
	ret0, _ := ret[0].([]model.TransactionFraudScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindByIDs indicates an expected call of FindByIDs.
func (mr *MockTransactionFraudScoreRepositoryMockRecorder) FindByIDs(ctx, transactionIds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindByIDs", reflect.TypeOf((*MockTransactionFraudScoreRepository)(nil).FindByIDs), ctx, transactionIds)
}

// GetByTransactionID mocks base method.
func (m *MockTransactionFraudScoreRepository) GetByTransactionID(ctx context.Context, transactionId string) (*model.TransactionFraudScore, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByTransactionID", ctx, transactionId)
	ret0, _ := ret[0].(*model.TransactionFraudScore)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByTransactionID indicates an expected call of GetByTransactionID.
func (mr *MockTransactionFraudScoreRepositoryMockRecorder) GetByTransactionID(ctx, transactionId interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByTransactionID", reflect.TypeOf((*MockTransactionFraudScoreRepository)(nil).GetByTransactionID), ctx, transactionId)
}
