// Code generated by MockGen. DO NOT EDIT.
// Source: ./driver_order_info_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
	mongo "go.mongodb.org/mongo-driver/mongo"
)

// MockDriverOrderInfoQuery is a mock of DriverOrderInfoQuery interface.
type MockDriverOrderInfoQuery struct {
	ctrl     *gomock.Controller
	recorder *MockDriverOrderInfoQueryMockRecorder
}

// MockDriverOrderInfoQueryMockRecorder is the mock recorder for MockDriverOrderInfoQuery.
type MockDriverOrderInfoQueryMockRecorder struct {
	mock *MockDriverOrderInfoQuery
}

// NewMockDriverOrderInfoQuery creates a new mock instance.
func NewMockDriverOrderInfoQuery(ctrl *gomock.Controller) *MockDriverOrderInfoQuery {
	mock := &MockDriverOrderInfoQuery{ctrl: ctrl}
	mock.recorder = &MockDriverOrderInfoQueryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverOrderInfoQuery) EXPECT() *MockDriverOrderInfoQueryMockRecorder {
	return m.recorder
}

// WithDriverID mocks base method.
func (m *MockDriverOrderInfoQuery) WithDriverID(driverID string) repository.DriverQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithDriverID", driverID)
	ret0, _ := ret[0].(repository.DriverQuery)
	return ret0
}

// WithDriverID indicates an expected call of WithDriverID.
func (mr *MockDriverOrderInfoQueryMockRecorder) WithDriverID(driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithDriverID", reflect.TypeOf((*MockDriverOrderInfoQuery)(nil).WithDriverID), driverID)
}

// MockDriverOrderInfoRepository is a mock of DriverOrderInfoRepository interface.
type MockDriverOrderInfoRepository struct {
	ctrl     *gomock.Controller
	recorder *MockDriverOrderInfoRepositoryMockRecorder
}

// MockDriverOrderInfoRepositoryMockRecorder is the mock recorder for MockDriverOrderInfoRepository.
type MockDriverOrderInfoRepositoryMockRecorder struct {
	mock *MockDriverOrderInfoRepository
}

// NewMockDriverOrderInfoRepository creates a new mock instance.
func NewMockDriverOrderInfoRepository(ctrl *gomock.Controller) *MockDriverOrderInfoRepository {
	mock := &MockDriverOrderInfoRepository{ctrl: ctrl}
	mock.recorder = &MockDriverOrderInfoRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverOrderInfoRepository) EXPECT() *MockDriverOrderInfoRepositoryMockRecorder {
	return m.recorder
}

// AddAttendanceStat mocks base method.
func (m *MockDriverOrderInfoRepository) AddAttendanceStat(ctx context.Context, driverID string, atds model.AttendanceStat) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddAttendanceStat", ctx, driverID, atds)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddAttendanceStat indicates an expected call of AddAttendanceStat.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) AddAttendanceStat(ctx, driverID, atds interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddAttendanceStat", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).AddAttendanceStat), ctx, driverID, atds)
}

// FindDriverAttendanceStat mocks base method.
func (m *MockDriverOrderInfoRepository) FindDriverAttendanceStat(ctx context.Context, driverID string, opts ...repository.Option) (model.DriverOrderInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, driverID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindDriverAttendanceStat", varargs...)
	ret0, _ := ret[0].(model.DriverOrderInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindDriverAttendanceStat indicates an expected call of FindDriverAttendanceStat.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) FindDriverAttendanceStat(ctx, driverID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, driverID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindDriverAttendanceStat", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).FindDriverAttendanceStat), varargs...)
}

// FindDriverID mocks base method.
func (m *MockDriverOrderInfoRepository) FindDriverID(ctx context.Context, driverID string, opts ...repository.Option) (*model.DriverOrderInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, driverID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindDriverID", varargs...)
	ret0, _ := ret[0].(*model.DriverOrderInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindDriverID indicates an expected call of FindDriverID.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) FindDriverID(ctx, driverID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, driverID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindDriverID", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).FindDriverID), varargs...)
}

// FindWhoHasAttendanceStat mocks base method.
func (m *MockDriverOrderInfoRepository) FindWhoHasAttendanceStat(ctx context.Context, skip, limit int, opts ...repository.Option) ([]model.DriverOrderInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, skip, limit}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindWhoHasAttendanceStat", varargs...)
	ret0, _ := ret[0].([]model.DriverOrderInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindWhoHasAttendanceStat indicates an expected call of FindWhoHasAttendanceStat.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) FindWhoHasAttendanceStat(ctx, skip, limit interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, skip, limit}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindWhoHasAttendanceStat", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).FindWhoHasAttendanceStat), varargs...)
}

// GetDailyCounts mocks base method.
func (m *MockDriverOrderInfoRepository) GetDailyCounts(arg0 context.Context, driverID string, startDate, endDate time.Time) (*model.DriverOrderInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDailyCounts", arg0, driverID, startDate, endDate)
	ret0, _ := ret[0].(*model.DriverOrderInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDailyCounts indicates an expected call of GetDailyCounts.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) GetDailyCounts(arg0, driverID, startDate, endDate interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDailyCounts", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).GetDailyCounts), arg0, driverID, startDate, endDate)
}

// GetDailyCountsMultipleDrivers mocks base method.
func (m *MockDriverOrderInfoRepository) GetDailyCountsMultipleDrivers(arg0 context.Context, driverIDs []string, startDate, endDate time.Time, opts ...repository.Option) ([]model.DriverOrderInfo, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, driverIDs, startDate, endDate}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDailyCountsMultipleDrivers", varargs...)
	ret0, _ := ret[0].([]model.DriverOrderInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDailyCountsMultipleDrivers indicates an expected call of GetDailyCountsMultipleDrivers.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) GetDailyCountsMultipleDrivers(arg0, driverIDs, startDate, endDate interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, driverIDs, startDate, endDate}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDailyCountsMultipleDrivers", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).GetDailyCountsMultipleDrivers), varargs...)
}

// GetOrCreate mocks base method.
func (m *MockDriverOrderInfoRepository) GetOrCreate(arg0 context.Context, driverID string) (*model.DriverOrderInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreate", arg0, driverID)
	ret0, _ := ret[0].(*model.DriverOrderInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrCreate indicates an expected call of GetOrCreate.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) GetOrCreate(arg0, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreate", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).GetOrCreate), arg0, driverID)
}

// IsExists mocks base method.
func (m *MockDriverOrderInfoRepository) IsExists(ctx context.Context, driverID string, opts ...repository.Option) bool {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, driverID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "IsExists", varargs...)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsExists indicates an expected call of IsExists.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) IsExists(ctx, driverID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, driverID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExists", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).IsExists), varargs...)
}

// RemoveAttendanceStats mocks base method.
func (m *MockDriverOrderInfoRepository) RemoveAttendanceStats(ctx context.Context, driverID string, dates []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveAttendanceStats", ctx, driverID, dates)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveAttendanceStats indicates an expected call of RemoveAttendanceStats.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) RemoveAttendanceStats(ctx, driverID, dates interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveAttendanceStats", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).RemoveAttendanceStats), ctx, driverID, dates)
}

// Update mocks base method.
func (m_2 *MockDriverOrderInfoRepository) Update(arg0 context.Context, m model.DriverOrderInfo) error {
	m_2.ctrl.T.Helper()
	ret := m_2.ctrl.Call(m_2, "Update", arg0, m)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) Update(arg0, m interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).Update), arg0, m)
}

// UpdateManyCounts mocks base method.
func (m *MockDriverOrderInfoRepository) UpdateManyCounts(ctx context.Context, payload []model.OrderUpdatePayload) (*mongo.BulkWriteResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateManyCounts", ctx, payload)
	ret0, _ := ret[0].(*mongo.BulkWriteResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateManyCounts indicates an expected call of UpdateManyCounts.
func (mr *MockDriverOrderInfoRepositoryMockRecorder) UpdateManyCounts(ctx, payload interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateManyCounts", reflect.TypeOf((*MockDriverOrderInfoRepository)(nil).UpdateManyCounts), ctx, payload)
}
