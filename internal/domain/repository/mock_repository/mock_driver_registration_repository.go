// Code generated by MockGen. DO NOT EDIT.
// Source: ./driver_registration_repository.go

// Package mock_repository is a generated GoMock package.
package mock_repository

import (
	context "context"
	reflect "reflect"
	time "time"

	crypt "git.wndv.co/lineman/absinthe/crypt"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
	null "gopkg.in/guregu/null.v4"
)

// MockDriverRegistrationQuery is a mock of DriverRegistrationQuery interface.
type MockDriverRegistrationQuery struct {
	ctrl     *gomock.Controller
	recorder *MockDriverRegistrationQueryMockRecorder
}

// MockDriverRegistrationQueryMockRecorder is the mock recorder for MockDriverRegistrationQuery.
type MockDriverRegistrationQueryMockRecorder struct {
	mock *MockDriverRegistrationQuery
}

// NewMockDriverRegistrationQuery creates a new mock instance.
func NewMockDriverRegistrationQuery(ctrl *gomock.Controller) *MockDriverRegistrationQuery {
	mock := &MockDriverRegistrationQuery{ctrl: ctrl}
	mock.recorder = &MockDriverRegistrationQueryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverRegistrationQuery) EXPECT() *MockDriverRegistrationQueryMockRecorder {
	return m.recorder
}

// WithAssignedReviewer mocks base method.
func (m *MockDriverRegistrationQuery) WithAssignedReviewer(assignedReviewer string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithAssignedReviewer", assignedReviewer)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithAssignedReviewer indicates an expected call of WithAssignedReviewer.
func (mr *MockDriverRegistrationQueryMockRecorder) WithAssignedReviewer(assignedReviewer interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithAssignedReviewer", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithAssignedReviewer), assignedReviewer)
}

// WithCitizenID mocks base method.
func (m *MockDriverRegistrationQuery) WithCitizenID(citizenID crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithCitizenID", citizenID)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithCitizenID indicates an expected call of WithCitizenID.
func (mr *MockDriverRegistrationQueryMockRecorder) WithCitizenID(citizenID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithCitizenID", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithCitizenID), citizenID)
}

// WithCriminalCheckStatus mocks base method.
func (m *MockDriverRegistrationQuery) WithCriminalCheckStatus(criminalCheckStatus model.CriminalStatus) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithCriminalCheckStatus", criminalCheckStatus)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithCriminalCheckStatus indicates an expected call of WithCriminalCheckStatus.
func (mr *MockDriverRegistrationQueryMockRecorder) WithCriminalCheckStatus(criminalCheckStatus interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithCriminalCheckStatus", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithCriminalCheckStatus), criminalCheckStatus)
}

// WithDriverDuplicatedCitizenId mocks base method.
func (m *MockDriverRegistrationQuery) WithDriverDuplicatedCitizenId(duplicated null.Bool) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithDriverDuplicatedCitizenId", duplicated)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithDriverDuplicatedCitizenId indicates an expected call of WithDriverDuplicatedCitizenId.
func (mr *MockDriverRegistrationQueryMockRecorder) WithDriverDuplicatedCitizenId(duplicated interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithDriverDuplicatedCitizenId", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithDriverDuplicatedCitizenId), duplicated)
}

// WithDriverType mocks base method.
func (m *MockDriverRegistrationQuery) WithDriverType(driverType crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithDriverType", driverType)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithDriverType indicates an expected call of WithDriverType.
func (mr *MockDriverRegistrationQueryMockRecorder) WithDriverType(driverType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithDriverType", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithDriverType), driverType)
}

// WithFirstname mocks base method.
func (m *MockDriverRegistrationQuery) WithFirstname(firstname crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithFirstname", firstname)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithFirstname indicates an expected call of WithFirstname.
func (mr *MockDriverRegistrationQueryMockRecorder) WithFirstname(firstname interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithFirstname", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithFirstname), firstname)
}

// WithFrom mocks base method.
func (m *MockDriverRegistrationQuery) WithFrom(from time.Time) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithFrom", from)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithFrom indicates an expected call of WithFrom.
func (mr *MockDriverRegistrationQueryMockRecorder) WithFrom(from interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithFrom", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithFrom), from)
}

// WithIDs mocks base method.
func (m *MockDriverRegistrationQuery) WithIDs(ids []string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithIDs", ids)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithIDs indicates an expected call of WithIDs.
func (mr *MockDriverRegistrationQueryMockRecorder) WithIDs(ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithIDs", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithIDs), ids)
}

// WithInRegions mocks base method.
func (m *MockDriverRegistrationQuery) WithInRegions(regions []string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithInRegions", regions)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithInRegions indicates an expected call of WithInRegions.
func (mr *MockDriverRegistrationQueryMockRecorder) WithInRegions(regions interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithInRegions", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithInRegions), regions)
}

// WithInfoCompleted mocks base method.
func (m *MockDriverRegistrationQuery) WithInfoCompleted(infoCompleted string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithInfoCompleted", infoCompleted)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithInfoCompleted indicates an expected call of WithInfoCompleted.
func (mr *MockDriverRegistrationQueryMockRecorder) WithInfoCompleted(infoCompleted interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithInfoCompleted", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithInfoCompleted), infoCompleted)
}

// WithInterestedProvince mocks base method.
func (m *MockDriverRegistrationQuery) WithInterestedProvince(interestedProvince string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithInterestedProvince", interestedProvince)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithInterestedProvince indicates an expected call of WithInterestedProvince.
func (mr *MockDriverRegistrationQueryMockRecorder) WithInterestedProvince(interestedProvince interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithInterestedProvince", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithInterestedProvince), interestedProvince)
}

// WithInterestedProvinces mocks base method.
func (m *MockDriverRegistrationQuery) WithInterestedProvinces(interestedProvinces []string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithInterestedProvinces", interestedProvinces)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithInterestedProvinces indicates an expected call of WithInterestedProvinces.
func (mr *MockDriverRegistrationQueryMockRecorder) WithInterestedProvinces(interestedProvinces interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithInterestedProvinces", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithInterestedProvinces), interestedProvinces)
}

// WithInterestedRegionLalamove mocks base method.
func (m *MockDriverRegistrationQuery) WithInterestedRegionLalamove(interestedRegionLalamove string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithInterestedRegionLalamove", interestedRegionLalamove)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithInterestedRegionLalamove indicates an expected call of WithInterestedRegionLalamove.
func (mr *MockDriverRegistrationQueryMockRecorder) WithInterestedRegionLalamove(interestedRegionLalamove interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithInterestedRegionLalamove", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithInterestedRegionLalamove), interestedRegionLalamove)
}

// WithLINEUserID mocks base method.
func (m *MockDriverRegistrationQuery) WithLINEUserID(lineuid string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithLINEUserID", lineuid)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithLINEUserID indicates an expected call of WithLINEUserID.
func (mr *MockDriverRegistrationQueryMockRecorder) WithLINEUserID(lineuid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithLINEUserID", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithLINEUserID), lineuid)
}

// WithLINEUserIDExist mocks base method.
func (m *MockDriverRegistrationQuery) WithLINEUserIDExist(isExisted null.Bool) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithLINEUserIDExist", isExisted)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithLINEUserIDExist indicates an expected call of WithLINEUserIDExist.
func (mr *MockDriverRegistrationQueryMockRecorder) WithLINEUserIDExist(isExisted interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithLINEUserIDExist", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithLINEUserIDExist), isExisted)
}

// WithLINEUserIDRetryLower mocks base method.
func (m *MockDriverRegistrationQuery) WithLINEUserIDRetryLower(count int) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithLINEUserIDRetryLower", count)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithLINEUserIDRetryLower indicates an expected call of WithLINEUserIDRetryLower.
func (mr *MockDriverRegistrationQueryMockRecorder) WithLINEUserIDRetryLower(count interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithLINEUserIDRetryLower", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithLINEUserIDRetryLower), count)
}

// WithLastname mocks base method.
func (m *MockDriverRegistrationQuery) WithLastname(lastname crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithLastname", lastname)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithLastname indicates an expected call of WithLastname.
func (mr *MockDriverRegistrationQueryMockRecorder) WithLastname(lastname interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithLastname", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithLastname), lastname)
}

// WithPhone mocks base method.
func (m *MockDriverRegistrationQuery) WithPhone(phone crypt.LazyEncryptedString) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithPhone", phone)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithPhone indicates an expected call of WithPhone.
func (mr *MockDriverRegistrationQueryMockRecorder) WithPhone(phone interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithPhone", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithPhone), phone)
}

// WithPreScreen mocks base method.
func (m *MockDriverRegistrationQuery) WithPreScreen(preScreen string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithPreScreen", preScreen)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithPreScreen indicates an expected call of WithPreScreen.
func (mr *MockDriverRegistrationQueryMockRecorder) WithPreScreen(preScreen interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithPreScreen", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithPreScreen), preScreen)
}

// WithRegion mocks base method.
func (m *MockDriverRegistrationQuery) WithRegion(region string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithRegion", region)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithRegion indicates an expected call of WithRegion.
func (mr *MockDriverRegistrationQueryMockRecorder) WithRegion(region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithRegion", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithRegion), region)
}

// WithRegisDuplicatedCitizenId mocks base method.
func (m *MockDriverRegistrationQuery) WithRegisDuplicatedCitizenId(duplicated null.Bool) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithRegisDuplicatedCitizenId", duplicated)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithRegisDuplicatedCitizenId indicates an expected call of WithRegisDuplicatedCitizenId.
func (mr *MockDriverRegistrationQueryMockRecorder) WithRegisDuplicatedCitizenId(duplicated interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithRegisDuplicatedCitizenId", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithRegisDuplicatedCitizenId), duplicated)
}

// WithRegistrationLocation mocks base method.
func (m *MockDriverRegistrationQuery) WithRegistrationLocation(query repository.RegistrationLocationQuery) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithRegistrationLocation", query)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithRegistrationLocation indicates an expected call of WithRegistrationLocation.
func (mr *MockDriverRegistrationQueryMockRecorder) WithRegistrationLocation(query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithRegistrationLocation", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithRegistrationLocation), query)
}

// WithStatus mocks base method.
func (m *MockDriverRegistrationQuery) WithStatus(status string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithStatus", status)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithStatus indicates an expected call of WithStatus.
func (mr *MockDriverRegistrationQueryMockRecorder) WithStatus(status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithStatus", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithStatus), status)
}

// WithTo mocks base method.
func (m *MockDriverRegistrationQuery) WithTo(to time.Time) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTo", to)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithTo indicates an expected call of WithTo.
func (mr *MockDriverRegistrationQueryMockRecorder) WithTo(to interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTo", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithTo), to)
}

// WithTrained mocks base method.
func (m *MockDriverRegistrationQuery) WithTrained(trained string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTrained", trained)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithTrained indicates an expected call of WithTrained.
func (mr *MockDriverRegistrationQueryMockRecorder) WithTrained(trained interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTrained", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithTrained), trained)
}

// WithoutStatus mocks base method.
func (m *MockDriverRegistrationQuery) WithoutStatus(status []string) repository.DriverRegistrationQuery {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithoutStatus", status)
	ret0, _ := ret[0].(repository.DriverRegistrationQuery)
	return ret0
}

// WithoutStatus indicates an expected call of WithoutStatus.
func (mr *MockDriverRegistrationQueryMockRecorder) WithoutStatus(status interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithoutStatus", reflect.TypeOf((*MockDriverRegistrationQuery)(nil).WithoutStatus), status)
}

// MockDriverRegisterationUpdator is a mock of DriverRegisterationUpdator interface.
type MockDriverRegisterationUpdator struct {
	ctrl     *gomock.Controller
	recorder *MockDriverRegisterationUpdatorMockRecorder
}

// MockDriverRegisterationUpdatorMockRecorder is the mock recorder for MockDriverRegisterationUpdator.
type MockDriverRegisterationUpdatorMockRecorder struct {
	mock *MockDriverRegisterationUpdator
}

// NewMockDriverRegisterationUpdator creates a new mock instance.
func NewMockDriverRegisterationUpdator(ctrl *gomock.Controller) *MockDriverRegisterationUpdator {
	mock := &MockDriverRegisterationUpdator{ctrl: ctrl}
	mock.recorder = &MockDriverRegisterationUpdatorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverRegisterationUpdator) EXPECT() *MockDriverRegisterationUpdatorMockRecorder {
	return m.recorder
}

// IncreaseLINEUserIDRetryCount mocks base method.
func (m *MockDriverRegisterationUpdator) IncreaseLINEUserIDRetryCount(amount int) repository.DriverRegisterationUpdator {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncreaseLINEUserIDRetryCount", amount)
	ret0, _ := ret[0].(repository.DriverRegisterationUpdator)
	return ret0
}

// IncreaseLINEUserIDRetryCount indicates an expected call of IncreaseLINEUserIDRetryCount.
func (mr *MockDriverRegisterationUpdatorMockRecorder) IncreaseLINEUserIDRetryCount(amount interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncreaseLINEUserIDRetryCount", reflect.TypeOf((*MockDriverRegisterationUpdator)(nil).IncreaseLINEUserIDRetryCount), amount)
}

// SetLINEMID mocks base method.
func (m *MockDriverRegisterationUpdator) SetLINEMID(lineMID string) repository.DriverRegisterationUpdator {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLINEMID", lineMID)
	ret0, _ := ret[0].(repository.DriverRegisterationUpdator)
	return ret0
}

// SetLINEMID indicates an expected call of SetLINEMID.
func (mr *MockDriverRegisterationUpdatorMockRecorder) SetLINEMID(lineMID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLINEMID", reflect.TypeOf((*MockDriverRegisterationUpdator)(nil).SetLINEMID), lineMID)
}

// SetLINEUserID mocks base method.
func (m *MockDriverRegisterationUpdator) SetLINEUserID(lineUID string) repository.DriverRegisterationUpdator {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetLINEUserID", lineUID)
	ret0, _ := ret[0].(repository.DriverRegisterationUpdator)
	return ret0
}

// SetLINEUserID indicates an expected call of SetLINEUserID.
func (mr *MockDriverRegisterationUpdatorMockRecorder) SetLINEUserID(lineUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetLINEUserID", reflect.TypeOf((*MockDriverRegisterationUpdator)(nil).SetLINEUserID), lineUID)
}

// MockDriverRegistrationRepository is a mock of DriverRegistrationRepository interface.
type MockDriverRegistrationRepository struct {
	ctrl     *gomock.Controller
	recorder *MockDriverRegistrationRepositoryMockRecorder
}

// MockDriverRegistrationRepositoryMockRecorder is the mock recorder for MockDriverRegistrationRepository.
type MockDriverRegistrationRepositoryMockRecorder struct {
	mock *MockDriverRegistrationRepository
}

// NewMockDriverRegistrationRepository creates a new mock instance.
func NewMockDriverRegistrationRepository(ctrl *gomock.Controller) *MockDriverRegistrationRepository {
	mock := &MockDriverRegistrationRepository{ctrl: ctrl}
	mock.recorder = &MockDriverRegistrationRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverRegistrationRepository) EXPECT() *MockDriverRegistrationRepositoryMockRecorder {
	return m.recorder
}

// BulkWriteModel mocks base method.
func (m *MockDriverRegistrationRepository) BulkWriteModel(ctx context.Context, updateOneModels ...repository.DriverRegistrationUpdateOneModel) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range updateOneModels {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "BulkWriteModel", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// BulkWriteModel indicates an expected call of BulkWriteModel.
func (mr *MockDriverRegistrationRepositoryMockRecorder) BulkWriteModel(ctx interface{}, updateOneModels ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, updateOneModels...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BulkWriteModel", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).BulkWriteModel), varargs...)
}

// Count mocks base method.
func (m *MockDriverRegistrationRepository) Count(ctx context.Context, query repository.DriverRegistrationQuery) (int, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Count", ctx, query)
	ret0, _ := ret[0].(int)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Count indicates an expected call of Count.
func (mr *MockDriverRegistrationRepositoryMockRecorder) Count(ctx, query interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Count", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).Count), ctx, query)
}

// Find mocks base method.
func (m *MockDriverRegistrationRepository) Find(ctx context.Context, query repository.DriverRegistrationQuery, skip, limit int, sort []string, opts ...repository.Option) ([]model.DriverRegistration, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, skip, limit, sort}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Find", varargs...)
	ret0, _ := ret[0].([]model.DriverRegistration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Find indicates an expected call of Find.
func (mr *MockDriverRegistrationRepositoryMockRecorder) Find(ctx, query, skip, limit, sort interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, skip, limit, sort}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Find", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).Find), varargs...)
}

// FindOneAndSetField mocks base method.
func (m *MockDriverRegistrationRepository) FindOneAndSetField(ctx context.Context, query repository.DriverRegistrationQuery, updator repository.DriverRegisterationUpdator) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneAndSetField", ctx, query, updator)
	ret0, _ := ret[0].(error)
	return ret0
}

// FindOneAndSetField indicates an expected call of FindOneAndSetField.
func (mr *MockDriverRegistrationRepositoryMockRecorder) FindOneAndSetField(ctx, query, updator interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneAndSetField", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).FindOneAndSetField), ctx, query, updator)
}

// FindOneByID mocks base method.
func (m *MockDriverRegistrationRepository) FindOneByID(ctx context.Context, id string) (*model.DriverRegistration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindOneByID", ctx, id)
	ret0, _ := ret[0].(*model.DriverRegistration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindOneByID indicates an expected call of FindOneByID.
func (mr *MockDriverRegistrationRepositoryMockRecorder) FindOneByID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindOneByID", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).FindOneByID), ctx, id)
}

// FindRegisByCitizenIDs mocks base method.
func (m *MockDriverRegistrationRepository) FindRegisByCitizenIDs(ctx context.Context, citizenIDs []crypt.LazyEncryptedString) ([]model.DriverRegistration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FindRegisByCitizenIDs", ctx, citizenIDs)
	ret0, _ := ret[0].([]model.DriverRegistration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindRegisByCitizenIDs indicates an expected call of FindRegisByCitizenIDs.
func (mr *MockDriverRegistrationRepositoryMockRecorder) FindRegisByCitizenIDs(ctx, citizenIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindRegisByCitizenIDs", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).FindRegisByCitizenIDs), ctx, citizenIDs)
}

// FindWithQuerySelectorAndSort mocks base method.
func (m *MockDriverRegistrationRepository) FindWithQuerySelectorAndSort(ctx context.Context, query repository.DriverRegistrationQuery, selector []string, skip, limit int, sort []string, opts ...repository.Option) ([]model.DriverRegistration, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, query, selector, skip, limit, sort}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FindWithQuerySelectorAndSort", varargs...)
	ret0, _ := ret[0].([]model.DriverRegistration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FindWithQuerySelectorAndSort indicates an expected call of FindWithQuerySelectorAndSort.
func (mr *MockDriverRegistrationRepositoryMockRecorder) FindWithQuerySelectorAndSort(ctx, query, selector, skip, limit, sort interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, query, selector, skip, limit, sort}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FindWithQuerySelectorAndSort", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).FindWithQuerySelectorAndSort), varargs...)
}

// GetByCitizenID mocks base method.
func (m *MockDriverRegistrationRepository) GetByCitizenID(ctx context.Context, citizenID crypt.LazyEncryptedString) (*model.DriverRegistration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByCitizenID", ctx, citizenID)
	ret0, _ := ret[0].(*model.DriverRegistration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByCitizenID indicates an expected call of GetByCitizenID.
func (mr *MockDriverRegistrationRepositoryMockRecorder) GetByCitizenID(ctx, citizenID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByCitizenID", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).GetByCitizenID), ctx, citizenID)
}

// GetByLineUID mocks base method.
func (m *MockDriverRegistrationRepository) GetByLineUID(ctx context.Context, lineUID string) (*model.DriverRegistration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByLineUID", ctx, lineUID)
	ret0, _ := ret[0].(*model.DriverRegistration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByLineUID indicates an expected call of GetByLineUID.
func (mr *MockDriverRegistrationRepositoryMockRecorder) GetByLineUID(ctx, lineUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByLineUID", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).GetByLineUID), ctx, lineUID)
}

// Insert mocks base method.
func (m *MockDriverRegistrationRepository) Insert(ctx context.Context, entity *model.DriverRegistration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Insert", ctx, entity)
	ret0, _ := ret[0].(error)
	return ret0
}

// Insert indicates an expected call of Insert.
func (mr *MockDriverRegistrationRepositoryMockRecorder) Insert(ctx, entity interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Insert", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).Insert), ctx, entity)
}

// IsExistsByCitizenID mocks base method.
func (m *MockDriverRegistrationRepository) IsExistsByCitizenID(ctx context.Context, citizenID crypt.LazyEncryptedString) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsExistsByCitizenID", ctx, citizenID)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsExistsByCitizenID indicates an expected call of IsExistsByCitizenID.
func (mr *MockDriverRegistrationRepositoryMockRecorder) IsExistsByCitizenID(ctx, citizenID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExistsByCitizenID", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).IsExistsByCitizenID), ctx, citizenID)
}

// IsExistsByCitizenIDAndID mocks base method.
func (m *MockDriverRegistrationRepository) IsExistsByCitizenIDAndID(ctx context.Context, citizenID crypt.LazyEncryptedString, id string) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsExistsByCitizenIDAndID", ctx, citizenID, id)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsExistsByCitizenIDAndID indicates an expected call of IsExistsByCitizenIDAndID.
func (mr *MockDriverRegistrationRepositoryMockRecorder) IsExistsByCitizenIDAndID(ctx, citizenID, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExistsByCitizenIDAndID", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).IsExistsByCitizenIDAndID), ctx, citizenID, id)
}

// IsExistsByLineUid mocks base method.
func (m *MockDriverRegistrationRepository) IsExistsByLineUid(ctx context.Context, lineUid crypt.LazyEncryptedString) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsExistsByLineUid", ctx, lineUid)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsExistsByLineUid indicates an expected call of IsExistsByLineUid.
func (mr *MockDriverRegistrationRepositoryMockRecorder) IsExistsByLineUid(ctx, lineUid interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsExistsByLineUid", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).IsExistsByLineUid), ctx, lineUid)
}

// ListByLINEUserID mocks base method.
func (m *MockDriverRegistrationRepository) ListByLINEUserID(ctx context.Context, lineUID string) ([]model.DriverRegistration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByLINEUserID", ctx, lineUID)
	ret0, _ := ret[0].([]model.DriverRegistration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByLINEUserID indicates an expected call of ListByLINEUserID.
func (mr *MockDriverRegistrationRepositoryMockRecorder) ListByLINEUserID(ctx, lineUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByLINEUserID", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).ListByLINEUserID), ctx, lineUID)
}

// ListByLineUID mocks base method.
func (m *MockDriverRegistrationRepository) ListByLineUID(ctx context.Context, lineUID crypt.LazyEncryptedString) ([]model.DriverRegistration, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByLineUID", ctx, lineUID)
	ret0, _ := ret[0].([]model.DriverRegistration)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByLineUID indicates an expected call of ListByLineUID.
func (mr *MockDriverRegistrationRepositoryMockRecorder) ListByLineUID(ctx, lineUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByLineUID", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).ListByLineUID), ctx, lineUID)
}

// RemoveByLineUID mocks base method.
func (m *MockDriverRegistrationRepository) RemoveByLineUID(ctx context.Context, lineUID crypt.LazyEncryptedString) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RemoveByLineUID", ctx, lineUID)
	ret0, _ := ret[0].(error)
	return ret0
}

// RemoveByLineUID indicates an expected call of RemoveByLineUID.
func (mr *MockDriverRegistrationRepositoryMockRecorder) RemoveByLineUID(ctx, lineUID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RemoveByLineUID", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).RemoveByLineUID), ctx, lineUID)
}

// Update mocks base method.
func (m *MockDriverRegistrationRepository) Update(ctx context.Context, entity *model.DriverRegistration) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, entity)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockDriverRegistrationRepositoryMockRecorder) Update(ctx, entity interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockDriverRegistrationRepository)(nil).Update), ctx, entity)
}
