// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"go.mongodb.org/mongo-driver/mongo"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyAssignmentBenchmarkRepository(delegate AssignmentBenchmarkRepository, meter metric.Meter) *ProxyAssignmentBenchmarkRepository {
	return &ProxyAssignmentBenchmarkRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyAssignmentBenchmarkRepository-tracer"),
	}
}

type ProxyAssignmentBenchmarkRepository struct {
	Delegate         AssignmentBenchmarkRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyAssignmentBenchmarkRepository) Count(i0 context.Context, i1 Filter, i2 *int, i3 ...Option) error {

	_, span := p.Tracer.Start(i0, "AssignmentBenchmarkRepository.Count")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Count(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "AssignmentBenchmarkRepository.Count")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyAssignmentBenchmarkRepository) Find(i0 context.Context, i1 Filter, i2 int, i3 int, i4 ...Option) ([]model.AssignmentBenchmark, error) {

	_, span := p.Tracer.Start(i0, "AssignmentBenchmarkRepository.Find")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Find(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "AssignmentBenchmarkRepository.Find")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyAssignmentBenchmarkRepository) FindOne(i0 context.Context, i1 *FindAssignmentBenchmarkParams, i2 ...Option) (*model.AssignmentBenchmark, error) {

	_, span := p.Tracer.Start(i0, "AssignmentBenchmarkRepository.FindOne")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindOne(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "AssignmentBenchmarkRepository.FindOne")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyAssignmentBenchmarkRepository) BulkWrite(i0 context.Context, i1 []mongo.WriteModel, i2 ...Option) (*mongo.BulkWriteResult, error) {

	_, span := p.Tracer.Start(i0, "AssignmentBenchmarkRepository.BulkWrite")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.BulkWrite(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "AssignmentBenchmarkRepository.BulkWrite")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyAssignmentBenchmarkRepository) DeleteMany(i0 context.Context, i1 Filter, i2 ...Option) (*mongo.DeleteResult, error) {

	_, span := p.Tracer.Start(i0, "AssignmentBenchmarkRepository.DeleteMany")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.DeleteMany(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "AssignmentBenchmarkRepository.DeleteMany")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
