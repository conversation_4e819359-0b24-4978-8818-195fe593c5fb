//go:generate mockgen -source=./driver_document_repository.go -destination=./mock_repository/mock_driver_document_repository.go -package=mock_repository

package repository

import (
	"context"

	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type DriverDocumentRepository interface {
	GetByQuery(ctx context.Context, query bson.M, opts ...Option) ([]model.DriverDocument, error)
	Save(ctx context.Context, m *model.DriverDocument) error
	FindByName(ctx context.Context, name string) (*model.DriverDocument, error)
	Update(ctx context.Context, m *model.DriverDocument) error
	SoftDeleteMany(ctx context.Context, query bson.M, remark model.Remark, opts ...Option) error
}
