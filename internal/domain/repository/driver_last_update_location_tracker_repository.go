//go:generate mockgen -source=./driver_last_update_location_tracker_repository.go -destination=./mock_repository/mock_driver_last_update_location_tracker_repository.go -package=mock_repository

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type DriverLastUpdateLocationTrackerRepository interface {
	GetKeySlot() []string
	GetDriverKeySlot(driverID string) string
	GetDriverLatestUpdateLocationAttempt(ctx context.Context, driverID string) (time.Time, error)
	UpdateLatestLocationAttempt(ctx context.Context, driverID string, t time.Time) error
	GetExpiredDriverID(ctx context.Context, keyslot string, ttl time.Duration, t time.Duration) ([]model.DriverLastUpdateLocationAttempt, error)
	RemoveDriverIDs(ctx context.Context, key string, driverIDs []string) (int, error)
}
