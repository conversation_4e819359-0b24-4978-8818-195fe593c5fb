// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyUnAcknowledgeReassignReposity(delegate UnAcknowledgeReassignReposity, meter metric.Meter) *ProxyUnAcknowledgeReassignReposity {
	return &ProxyUnAcknowledgeReassignReposity{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyUnAcknowledgeReassignReposity-tracer"),
	}
}

type ProxyUnAcknowledgeReassignReposity struct {
	Delegate         UnAcknowledgeReassignReposity
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyUnAcknowledgeReassignReposity) AddTrip(i0 context.Context, i1 string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "UnAcknowledgeReassignReposity.AddTrip")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.AddTrip(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "UnAcknowledgeReassignReposity.AddTrip")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyUnAcknowledgeReassignReposity) RemoveTrip(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "UnAcknowledgeReassignReposity.RemoveTrip")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.RemoveTrip(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "UnAcknowledgeReassignReposity.RemoveTrip")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyUnAcknowledgeReassignReposity) GetDelayedTrips(i0 context.Context) ([]string, error) {

	_, span := p.Tracer.Start(i0, "UnAcknowledgeReassignReposity.GetDelayedTrips")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDelayedTrips(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "UnAcknowledgeReassignReposity.GetDelayedTrips")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
