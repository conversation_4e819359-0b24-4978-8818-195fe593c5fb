// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyMatchingRateHeatMapRepository(delegate MatchingRateHeatMapRepository, meter metric.Meter) *ProxyMatchingRateHeatMapRepository {
	return &ProxyMatchingRateHeatMapRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyMatchingRateHeatMapRepository-tracer"),
	}
}

type ProxyMatchingRateHeatMapRepository struct {
	Delegate         MatchingRateHeatMapRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyMatchingRateHeatMapRepository) Find(i0 HeatMapQuery, i1 ...Option) ([]model.MatchingRateHeatMap, error) {

	start := time.Now()

	r0, r1 := p.Delegate.Find(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "MatchingRateHeatMapRepository.Find")

	return r0, r1
}

func (p *ProxyMatchingRateHeatMapRepository) SaveAll(i0 []model.MatchingRateHeatMap) error {

	start := time.Now()

	r0 := p.Delegate.SaveAll(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "MatchingRateHeatMapRepository.SaveAll")

	return r0
}

func (p *ProxyMatchingRateHeatMapRepository) RemoveAll() error {

	start := time.Now()

	r0 := p.Delegate.RemoveAll()

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "MatchingRateHeatMapRepository.RemoveAll")

	return r0
}
