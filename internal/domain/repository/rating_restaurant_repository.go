package repository

//go:generate mockgen -source=./rating_restaurant_repository.go -destination=./mock_repository/mock_rating_restaurant_repository.go -package=mock_repository

import (
	"context"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type RatingRestaurantRepository interface {
	Create(ctx context.Context, ro model.RatingRestaurant) error
	Get(ctx context.Context, orderId string) (*model.RatingRestaurant, error)
}
