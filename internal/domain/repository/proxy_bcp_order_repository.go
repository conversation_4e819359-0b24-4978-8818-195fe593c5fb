// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyBCPOrderRepository(delegate BCPOrderRepository, meter metric.Meter) *ProxyBCPOrderRepository {
	return &ProxyBCPOrderRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyBCPOrderRepository-tracer"),
	}
}

type ProxyBCPOrderRepository struct {
	Delegate         BCPOrderRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyBCPOrderRepository) Find(i0 context.Context, i1 Query, i2 ...Option) ([]model.BCPOrder, error) {

	_, span := p.Tracer.Start(i0, "BCPOrderRepository.Find")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Find(i0, i1, i2...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "BCPOrderRepository.Find")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyBCPOrderRepository) Create(i0 context.Context, i1 model.BCPOrder) error {

	_, span := p.Tracer.Start(i0, "BCPOrderRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "BCPOrderRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyBCPOrderRepository) GetOrdersByStatusInPeriod(i0 context.Context, i1 model.BCPOrderStatus, i2 time.Time, i3 time.Time, i4 ...Option) ([]model.BCPOrder, error) {

	_, span := p.Tracer.Start(i0, "BCPOrderRepository.GetOrdersByStatusInPeriod")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetOrdersByStatusInPeriod(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "BCPOrderRepository.GetOrdersByStatusInPeriod")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
