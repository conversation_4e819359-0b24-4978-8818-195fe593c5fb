// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/mongo"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyDriverInsuranceRepository(delegate DriverInsuranceRepository, meter metric.Meter) *ProxyDriverInsuranceRepository {
	return &ProxyDriverInsuranceRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyDriverInsuranceRepository-tracer"),
	}
}

type ProxyDriverInsuranceRepository struct {
	Delegate         DriverInsuranceRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyDriverInsuranceRepository) CreateAll(i0 context.Context, i1 ...model.DriverInsurance) error {

	_, span := p.Tracer.Start(i0, "DriverInsuranceRepository.CreateAll")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.CreateAll(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.CreateAll")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverInsuranceRepository) FindWithQueryAndSort(i0 context.Context, i1 DriverInsuranceQuery, i2 int, i3 int, i4 ...Option) ([]model.DriverInsurance, error) {

	_, span := p.Tracer.Start(i0, "DriverInsuranceRepository.FindWithQueryAndSort")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindWithQueryAndSort(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.FindWithQueryAndSort")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverInsuranceRepository) FindConsolidateData(i0 context.Context, i1 DriverInsuranceQuery, i2 int, i3 int, i4 ...Option) ([]model.DriverInsurance, error) {

	_, span := p.Tracer.Start(i0, "DriverInsuranceRepository.FindConsolidateData")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindConsolidateData(i0, i1, i2, i3, i4...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.FindConsolidateData")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverInsuranceRepository) UpdateStatusByDriverIDs(i0 context.Context, i1 string, i2 model.DriverInsuranceStatus, i3 ...string) error {

	_, span := p.Tracer.Start(i0, "DriverInsuranceRepository.UpdateStatusByDriverIDs")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateStatusByDriverIDs(i0, i1, i2, i3...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.UpdateStatusByDriverIDs")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverInsuranceRepository) UpdateUploadImagesResult(i0 *gin.Context, i1 []model.DriverInsurance, i2 model.ImagesUploadResult) error {

	start := time.Now()

	r0 := p.Delegate.UpdateUploadImagesResult(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.UpdateUploadImagesResult")

	return r0
}

func (p *ProxyDriverInsuranceRepository) ReplaceByID(i0 context.Context, i1 model.DriverInsurance) error {

	_, span := p.Tracer.Start(i0, "DriverInsuranceRepository.ReplaceByID")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.ReplaceByID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.ReplaceByID")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverInsuranceRepository) BulkUpdateActivePolicyNumber(i0 *gin.Context, i1 string, i2 model.PeriodOfInsurance, i3 model.DriverInsuranceType, i4 []model.PolicyStatusInfo) (*mongo.BulkWriteResult, error) {

	start := time.Now()

	r0, r1 := p.Delegate.BulkUpdateActivePolicyNumber(i0, i1, i2, i3, i4)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.BulkUpdateActivePolicyNumber")

	return r0, r1
}

func (p *ProxyDriverInsuranceRepository) BulkUpdateRejectPolicyNumber(i0 *gin.Context, i1 string, i2 model.DriverInsuranceType, i3 []model.PolicyStatusInfo) (*mongo.BulkWriteResult, error) {

	start := time.Now()

	r0, r1 := p.Delegate.BulkUpdateRejectPolicyNumber(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.BulkUpdateRejectPolicyNumber")

	return r0, r1
}

func (p *ProxyDriverInsuranceRepository) UpsertAll(i0 context.Context, i1 ...model.DriverInsurance) error {

	_, span := p.Tracer.Start(i0, "DriverInsuranceRepository.UpsertAll")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpsertAll(i0, i1...)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverInsuranceRepository.UpsertAll")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
