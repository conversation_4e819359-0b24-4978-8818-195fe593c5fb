// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyDriverLocationRepository(delegate DriverLocationRepository, meter metric.Meter) *ProxyDriverLocationRepository {
	return &ProxyDriverLocationRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyDriverLocationRepository-tracer"),
	}
}

type ProxyDriverLocationRepository struct {
	Delegate         DriverLocationRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyDriverLocationRepository) UpdateDriverLocation(i0 context.Context, i1 UpdateDriverLocationRequest) error {

	_, span := p.Tracer.Start(i0, "DriverLocationRepository.UpdateDriverLocation")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateDriverLocation(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLocationRepository.UpdateDriverLocation")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverLocationRepository) GetDrivers(i0 context.Context, i1 DriverLocationQuery) ([]model.DriverLocation, error) {

	_, span := p.Tracer.Start(i0, "DriverLocationRepository.GetDrivers")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDrivers(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLocationRepository.GetDrivers")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverLocationRepository) GetDriversInMultiPolygon(i0 context.Context, i1 DriverLocationInMultiPolygonQuery) ([]model.DriverLocation, error) {

	_, span := p.Tracer.Start(i0, "DriverLocationRepository.GetDriversInMultiPolygon")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDriversInMultiPolygon(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLocationRepository.GetDriversInMultiPolygon")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverLocationRepository) GetDriverLocation(i0 context.Context, i1 string) (*model.DriverLocation, error) {

	_, span := p.Tracer.Start(i0, "DriverLocationRepository.GetDriverLocation")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDriverLocation(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLocationRepository.GetDriverLocation")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverLocationRepository) GetDriverLocationWithUpdatedAt(i0 context.Context, i1 string) (*model.LocationWithUpdatedAt, error) {

	_, span := p.Tracer.Start(i0, "DriverLocationRepository.GetDriverLocationWithUpdatedAt")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDriverLocationWithUpdatedAt(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLocationRepository.GetDriverLocationWithUpdatedAt")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverLocationRepository) GetDriversByIDs(i0 context.Context, i1 []string) []model.DriverLocation {

	_, span := p.Tracer.Start(i0, "DriverLocationRepository.GetDriversByIDs")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.GetDriversByIDs(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLocationRepository.GetDriversByIDs")

	return r0
}
