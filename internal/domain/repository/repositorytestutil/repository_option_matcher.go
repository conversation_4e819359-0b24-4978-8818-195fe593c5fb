package repositorytestutil

import (
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

type RepositoryOptionMatcher struct {
	option repository.Option
}

func NewRepositoryOptionMatcher(option repository.Option) *RepositoryOptionMatcher {
	return &RepositoryOptionMatcher{
		option: option,
	}
}

func (dm *RepositoryOptionMatcher) Matches(x interface{}) bool {
	opt, ok := x.(repository.Option)
	if !ok {
		panic("not repository option")
	}

	return getDbMode(opt.MongoOption()) == getDbMode(dm.option.MongoOption())
}

func (dm *RepositoryOptionMatcher) String() string {
	return "matchrepooption"
}
