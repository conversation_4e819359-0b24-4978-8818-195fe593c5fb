package repositorytestutil

import (
	"strconv"

	"go.mongodb.org/mongo-driver/mongo/readpref"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
)

type DatabaseReadPrefMatcher struct {
	readPref *readpref.ReadPref
}

func NewDatabaseModeMatcher(readPref *readpref.ReadPref) *DatabaseReadPrefMatcher {
	return &DatabaseReadPrefMatcher{
		readPref: readPref,
	}
}

func (dm *DatabaseReadPrefMatcher) Matches(x interface{}) bool {
	return getDbMode(x) == dm.readPref.Mode()
}

func (dm *DatabaseReadPrefMatcher) String() string {
	return "matchdbmode" + strconv.Itoa(int(dm.readPref.Mode()))
}

func getDbMode(f interface{}) readpref.Mode {
	dbOpt, ok := f.(mongodb.Option)
	if !ok {
		panic("not option function")
	}
	s := mongodb.MongoOption{}
	dbOpt(&s)

	return s.ReadPref.Mode()
}
