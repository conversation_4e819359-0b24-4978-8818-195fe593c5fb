// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyDeferredOrderRepository(delegate DeferredOrderRepository, meter metric.Meter) *ProxyDeferredOrderRepository {
	return &ProxyDeferredOrderRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyDeferredOrderRepository-tracer"),
	}
}

type ProxyDeferredOrderRepository struct {
	Delegate         DeferredOrderRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyDeferredOrderRepository) InsertOrder(i0 context.Context, i1 model.Order, i2 model.DeferredOrderOption) error {

	_, span := p.Tracer.Start(i0, "DeferredOrderRepository.InsertOrder")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.InsertOrder(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DeferredOrderRepository.InsertOrder")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDeferredOrderRepository) FindUnProcessOrder(i0 context.Context, i1 time.Time, i2 time.Time, i3 int) ([]model.DeferredOrder, error) {

	_, span := p.Tracer.Start(i0, "DeferredOrderRepository.FindUnProcessOrder")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindUnProcessOrder(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DeferredOrderRepository.FindUnProcessOrder")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDeferredOrderRepository) MarkProcessedOrders(i0 context.Context, i1 []string, i2 int) error {

	_, span := p.Tracer.Start(i0, "DeferredOrderRepository.MarkProcessedOrders")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.MarkProcessedOrders(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DeferredOrderRepository.MarkProcessedOrders")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDeferredOrderRepository) MarkSuccessOrders(i0 context.Context, i1 []string) error {

	_, span := p.Tracer.Start(i0, "DeferredOrderRepository.MarkSuccessOrders")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.MarkSuccessOrders(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DeferredOrderRepository.MarkSuccessOrders")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDeferredOrderRepository) MarkFailedOrders(i0 context.Context, i1 []string) error {

	_, span := p.Tracer.Start(i0, "DeferredOrderRepository.MarkFailedOrders")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.MarkFailedOrders(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DeferredOrderRepository.MarkFailedOrders")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDeferredOrderRepository) UpdateDistributionTime(i0 context.Context, i1 []string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "DeferredOrderRepository.UpdateDistributionTime")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateDistributionTime(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DeferredOrderRepository.UpdateDistributionTime")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDeferredOrderRepository) IsUnprocessedExist(i0 context.Context, i1 string) (bool, error) {

	_, span := p.Tracer.Start(i0, "DeferredOrderRepository.IsUnprocessedExist")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.IsUnprocessedExist(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DeferredOrderRepository.IsUnprocessedExist")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
