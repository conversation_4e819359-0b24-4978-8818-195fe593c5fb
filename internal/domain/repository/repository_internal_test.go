package repository

import (
	"testing"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/readpref"

	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func Test_dbOpt_ReadOpt(t *testing.T) {
	tests := []struct {
		name string
		cfg  DBOptConfig
		want Option
	}{
		{
			"ReadPrimary true, WithReadSecondaryPreferred true -> WithReadSecondaryPreferred",
			DBOptConfig{
				true, true,
			},
			WithReadSecondaryPreferred,
		},
		{
			"ReadPrimary true, WithReadSecondaryPreferred false -> WithReadPrimary",
			DBOptConfig{
				true, false,
			},
			WithReadPrimary,
		},
		{
			"ReadPrimary false, WithReadSecondaryPreferred false -> WithReadSecondaryPreferred",
			DBOptConfig{
				false, false,
			},
			WithReadSecondaryPreferred,
		},
		{
			"ReadPrimary false, WithReadSecondaryPreferred true -> WithReadSecondaryPreferred",
			DBOptConfig{
				false, true,
			},
			WithReadSecondaryPreferred,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ro := &dbOpt{
				cfg: tt.cfg,
			}
			if got := ro.ReadOpt(); getDbMode(got.MongoOption()) != getDbMode(tt.want.MongoOption()) {
				t.Errorf("ReadOpt() = %v, want %v", got, tt.want)
			}
		})
	}
}

func getDbMode(f interface{}) readpref.Mode {
	dbOpt, ok := f.(mongodb.Option)
	if !ok {
		panic("not option function")
	}
	s := mongodb.MongoOption{}
	dbOpt(&s)

	return s.ReadPref.Mode()
}

func TestToFilter(t *testing.T) {
	t.Run("should set driverID filter when using OnlyAuthDriverID option", func(tt *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		ctx.GinCtx().Set("driverID", "test-driver-id")
		options := []Option{OnlyAuthDriverID}
		filter := ToFilter(ctx.GinCtx(), bson.M{}, options)

		require.Equal(tt, "test-driver-id", filter["driver_id"])
	})

	t.Run("should override driverID filter when using OnlyAuthDriverID option", func(tt *testing.T) {
		ctx := testutil.NewContextWithRecorder()
		ctx.GinCtx().Set("driverID", "test-driver-id")
		options := []Option{OnlyAuthDriverID}
		filter := ToFilter(ctx.GinCtx(), bson.M{"driver_Id": "invalid-driver-id"}, options)

		require.Equal(tt, "test-driver-id", filter["driver_id"])
	})
}
