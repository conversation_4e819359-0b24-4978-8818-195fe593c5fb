// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyWhitelistPhoneRepository(delegate WhitelistPhoneRepository, meter metric.Meter) *ProxyWhitelistPhoneRepository {
	return &ProxyWhitelistPhoneRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyWhitelistPhoneRepository-tracer"),
	}
}

type ProxyWhitelistPhoneRepository struct {
	Delegate         WhitelistPhoneRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyWhitelistPhoneRepository) GetByPhone(i0 context.Context, i1 crypt.EncryptedString) (*model.WhitelistPhone, error) {

	_, span := p.Tracer.Start(i0, "WhitelistPhoneRepository.GetByPhone")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetByPhone(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "WhitelistPhoneRepository.GetByPhone")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyWhitelistPhoneRepository) BulkCreate(i0 context.Context, i1 []model.WhitelistPhone) error {

	_, span := p.Tracer.Start(i0, "WhitelistPhoneRepository.BulkCreate")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.BulkCreate(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "WhitelistPhoneRepository.BulkCreate")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyWhitelistPhoneRepository) FindByPhones(i0 context.Context, i1 []crypt.EncryptedString) (model.WhitelistPhoneList, error) {

	_, span := p.Tracer.Start(i0, "WhitelistPhoneRepository.FindByPhones")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.FindByPhones(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "WhitelistPhoneRepository.FindByPhones")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyWhitelistPhoneRepository) List(i0 context.Context, i1 crypt.EncryptedString, i2 int, i3 int) ([]model.WhitelistPhone, error) {

	_, span := p.Tracer.Start(i0, "WhitelistPhoneRepository.List")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.List(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "WhitelistPhoneRepository.List")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyWhitelistPhoneRepository) Count(i0 context.Context, i1 crypt.EncryptedString) (int, error) {

	_, span := p.Tracer.Start(i0, "WhitelistPhoneRepository.Count")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.Count(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "WhitelistPhoneRepository.Count")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyWhitelistPhoneRepository) Update(i0 context.Context, i1 *model.WhitelistPhone) error {

	_, span := p.Tracer.Start(i0, "WhitelistPhoneRepository.Update")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Update(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "WhitelistPhoneRepository.Update")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
