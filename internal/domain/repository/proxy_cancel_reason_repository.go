// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyCancelReasonRepository(delegate CancelReasonRepository, meter metric.Meter) *ProxyCancelReasonRepository {
	return &ProxyCancelReasonRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyCancelReasonRepository-tracer"),
	}
}

type ProxyCancelReasonRepository struct {
	Delegate         CancelReasonRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyCancelReasonRepository) GetByReasonName(i0 context.Context, i1 string, i2 string) (model.CancelReason, error) {

	_, span := p.Tracer.Start(i0, "CancelReasonRepository.GetByReasonName")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetByReasonName(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "CancelReasonRepository.GetByReasonName")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyCancelReasonRepository) GetByService(i0 context.Context, i1 string) ([]model.CancelReason, error) {

	_, span := p.Tracer.Start(i0, "CancelReasonRepository.GetByService")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetByService(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "CancelReasonRepository.GetByService")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyCancelReasonRepository) Create(i0 context.Context, i1 *model.CancelReason) error {

	_, span := p.Tracer.Start(i0, "CancelReasonRepository.Create")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Create(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "CancelReasonRepository.Create")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyCancelReasonRepository) GetByID(i0 context.Context, i1 string) (*model.CancelReason, error) {

	_, span := p.Tracer.Start(i0, "CancelReasonRepository.GetByID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetByID(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "CancelReasonRepository.GetByID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyCancelReasonRepository) GetAll(i0 context.Context) ([]model.CancelReason, error) {

	_, span := p.Tracer.Start(i0, "CancelReasonRepository.GetAll")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetAll(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "CancelReasonRepository.GetAll")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyCancelReasonRepository) Remove(i0 context.Context, i1 string) error {

	_, span := p.Tracer.Start(i0, "CancelReasonRepository.Remove")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Remove(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "CancelReasonRepository.Remove")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyCancelReasonRepository) Update(i0 context.Context, i1 string, i2 *model.CancelReason) error {

	_, span := p.Tracer.Start(i0, "CancelReasonRepository.Update")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.Update(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "CancelReasonRepository.Update")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}
