package repository

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestRegistrationLocationQuery_IsZero(t *testing.T) {
	tests := []struct {
		obj      RegistrationLocationQuery
		expected bool
	}{
		{RegistrationLocationQuery{}, true},
		{RegistrationLocationQuery{Province: "", District: ""}, true},
		{RegistrationLocationQuery{Province: "Bangkok"}, false},
		{RegistrationLocationQuery{District: "Sathon"}, false},
	}
	for i, test := range tests {
		t.Run(fmt.Sprintf("case-%d", i), func(t *testing.T) {
			assert.Equal(t, test.expected, test.obj.IsZero())
			assert.Equal(t, test.expected, (&test.obj).IsZero())
		})
	}
}
