package repository

//go:generate mockgen -source=./group_transaction_repository.go -destination=./mock_repository/mock_group_transaction_repository.go -package=mock_repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type GroupTransactionQuery interface {
	WithStatus(status []model.GroupTransactionStatus) GroupTransactionQuery
	WithFrom(from time.Time) GroupTransactionQuery
	WithTo(to time.Time) GroupTransactionQuery
	WithTransactionType(t string) GroupTransactionQuery
	WithTimeRangeExecute(start time.Time, end time.Time) GroupTransactionQuery
	WithRequestedBy(requestedBy string) GroupTransactionQuery
}

// @@generate-prometheus-proxy@@ name:"repository_latency_ms"
type GroupTransactionRepository interface {
	Create(ctx context.Context, entity *model.GroupTransaction) error
	CreateMany(ctx context.Context, entities []model.GroupTransaction) error
	Get(ctx context.Context, id string, opts ...Option) (*model.GroupTransaction, error)
	Find(ctx context.Context, query GroupTransactionQuery, skip, limit int) ([]model.GroupTransaction, error)
	Count(ctx context.Context, query GroupTransactionQuery) (int, error)
	Update(ctx context.Context, entity *model.GroupTransaction) error
	Delete(ctx context.Context, groupTxnID string) error
}
