// Code generated by metricproxy. DO NOT EDIT.

package repository

import (
	"context"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"

	"go.opentelemetry.io/otel/codes"

	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
)

func NewLatencyProxyDriverLastUpdateLocationTrackerRepository(delegate DriverLastUpdateLocationTrackerRepository, meter metric.Meter) *ProxyDriverLastUpdateLocationTrackerRepository {
	return &ProxyDriverLastUpdateLocationTrackerRepository{
		Delegate:         delegate,
		LatencyHistogram: meter.GetHistogram("repository_latency_ms", "repository_latency_ms", metric.DefaultHistogramBucket, "name"),
		Tracer:           otel.Tracer("ProxyDriverLastUpdateLocationTrackerRepository-tracer"),
	}
}

type ProxyDriverLastUpdateLocationTrackerRepository struct {
	Delegate         DriverLastUpdateLocationTrackerRepository
	LatencyHistogram metric.Histogram
	Tracer           trace.Tracer
}

func (p *ProxyDriverLastUpdateLocationTrackerRepository) GetKeySlot() []string {

	start := time.Now()

	r0 := p.Delegate.GetKeySlot()

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLastUpdateLocationTrackerRepository.GetKeySlot")

	return r0
}

func (p *ProxyDriverLastUpdateLocationTrackerRepository) GetDriverKeySlot(i0 string) string {

	start := time.Now()

	r0 := p.Delegate.GetDriverKeySlot(i0)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLastUpdateLocationTrackerRepository.GetDriverKeySlot")

	return r0
}

func (p *ProxyDriverLastUpdateLocationTrackerRepository) GetDriverLatestUpdateLocationAttempt(i0 context.Context, i1 string) (time.Time, error) {

	_, span := p.Tracer.Start(i0, "DriverLastUpdateLocationTrackerRepository.GetDriverLatestUpdateLocationAttempt")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetDriverLatestUpdateLocationAttempt(i0, i1)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLastUpdateLocationTrackerRepository.GetDriverLatestUpdateLocationAttempt")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverLastUpdateLocationTrackerRepository) UpdateLatestLocationAttempt(i0 context.Context, i1 string, i2 time.Time) error {

	_, span := p.Tracer.Start(i0, "DriverLastUpdateLocationTrackerRepository.UpdateLatestLocationAttempt")
	defer span.End()

	start := time.Now()

	r0 := p.Delegate.UpdateLatestLocationAttempt(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLastUpdateLocationTrackerRepository.UpdateLatestLocationAttempt")

	if r0 != nil {
		span.RecordError(r0)
		span.SetStatus(codes.Error, r0.Error())
	}

	return r0
}

func (p *ProxyDriverLastUpdateLocationTrackerRepository) GetExpiredDriverID(i0 context.Context, i1 string, i2 time.Duration, i3 time.Duration) ([]model.DriverLastUpdateLocationAttempt, error) {

	_, span := p.Tracer.Start(i0, "DriverLastUpdateLocationTrackerRepository.GetExpiredDriverID")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.GetExpiredDriverID(i0, i1, i2, i3)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLastUpdateLocationTrackerRepository.GetExpiredDriverID")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}

func (p *ProxyDriverLastUpdateLocationTrackerRepository) RemoveDriverIDs(i0 context.Context, i1 string, i2 []string) (int, error) {

	_, span := p.Tracer.Start(i0, "DriverLastUpdateLocationTrackerRepository.RemoveDriverIDs")
	defer span.End()

	start := time.Now()

	r0, r1 := p.Delegate.RemoveDriverIDs(i0, i1, i2)

	p.LatencyHistogram.Observe(float64(time.Since(start).Milliseconds()), "DriverLastUpdateLocationTrackerRepository.RemoveDriverIDs")

	if r1 != nil {
		span.RecordError(r1)
		span.SetStatus(codes.Error, r1.Error())
	}

	return r0, r1
}
