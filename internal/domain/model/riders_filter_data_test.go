package model

import (
	"fmt"
	"sync"
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

func Test_RidersFilterData(t *testing.T) {
	t.<PERSON>llel()

	t.Run("should add filter data and get result correctly", func(tt *testing.T) {
		tt.Parallel()

		filterData := NewRiderFilterData()
		filterData.Add(B2BDistanceLimitExceeded, "D1")
		filterData.AddMany(OrderNotFound, []string{"D2", "D3"})

		filterData2 := NewRiderFilterData()
		filterData2.Add(FirstDistanceLimitExceeded, "D4")

		filterData.AddExisting(filterData2)
		result := filterData.Result()
		require.Len(t, result, 3)
		require.Equal(t, []string{"D1"}, result[B2BDistanceLimitExceeded])
		require.Equal(t, []string{"D2", "D3"}, result[OrderNotFound])
		require.Equal(t, []string{"D4"}, result[FirstDistanceLimitExceeded])
	})

	t.Run("should add filter data and get result correctly with concurrency", func(tt *testing.T) {
		tt.Parallel()
		filterData := NewRiderFilterData()

		var wg sync.WaitGroup
		for i := 0; i < 1000; i++ {
			wg.Add(1)
			safe.GoFunc(func() {
				defer wg.Done()
				filterData.Add(B2BDistanceLimitExceeded, fmt.Sprintf("D%v", i))
				filterData2 := NewRiderFilterData()
				filterData2.Add(FirstDistanceLimitExceeded, fmt.Sprintf("D%v", i))
				filterData.AddExisting(filterData2)
			})
		}
		wg.Wait()

		result := filterData.Result()
		require.Len(t, result, 2)
		require.Len(t, result[B2BDistanceLimitExceeded], 1000)
		require.Len(t, result[FirstDistanceLimitExceeded], 1000)
	})
}
