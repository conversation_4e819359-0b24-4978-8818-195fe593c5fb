package model

import (
	"time"

	"github.com/pkg/errors"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

var (
	ErrDuplicatedArea = errors.New("service-area already exists")
)

type ClientAreaID string

func newClientAreaID(id string) ClientAreaID {
	return ClientAreaID(id)
}

func (pi ClientAreaID) String() string {
	return string(pi)
}

func GenerateClientAreaID() (ClientAreaID, error) {
	id := utils.GenerateUUID()
	return ClientAreaID(id), nil
}

type ClientArea struct {
	id           ClientAreaID
	serviceType  Service
	area         string
	serviceAreas []RegionCode

	createdAt time.Time
	updatedAt time.Time
}

func NewClientArea(id ClientAreaID, area string, st Service) *ClientArea {
	now := time.Now().UTC()
	return &ClientArea{
		id:           id,
		serviceType:  st,
		area:         area,
		serviceAreas: make([]RegionCode, 0, 10),

		createdAt: now,
		updatedAt: now,
	}
}

func (pa ClientArea) ID() ClientAreaID {
	return pa.id
}

func (pa ClientArea) Area() string {
	return pa.area
}
func (pa ClientArea) ServiceType() Service {
	return pa.serviceType
}

func (pa ClientArea) ServiceAreas() []RegionCode {
	return pa.serviceAreas
}

func (pa ClientArea) CreatedAt() time.Time {
	return pa.createdAt
}

func (pa ClientArea) UpdatedAt() time.Time {
	return pa.updatedAt
}

func (pa *ClientArea) SetUpdatedAt(update time.Time) {
	pa.updatedAt = update
}

func (pa *ClientArea) AddServiceArea(area RegionCode) error {
	for _, sa := range pa.serviceAreas {
		if sa == area {
			return ErrDuplicatedArea
		}
	}

	pa.serviceAreas = append(pa.serviceAreas, area)
	return nil
}

func (pa *ClientArea) SetServiceAreas(areas []RegionCode) error {
	exists := types.NewStringSet()
	for _, a := range areas {
		if exists.Has(a.String()) {
			return ErrDuplicatedArea
		}

		exists.Add(a.String())
	}

	pa.serviceAreas = areas
	return nil
}

type ClientAreas []ClientArea

func (ca ClientAreas) UniqueServiceTypes() []Service {
	out := []Service{}
	set := types.NewStringSet()
	for _, v := range ca {
		set.Add(string(v.serviceType))
	}
	for _, v := range set.GetElements() {
		out = append(out, newServiceType(v))
	}
	return out
}
