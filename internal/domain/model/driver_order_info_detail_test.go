package model_test

import (
	"testing"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func TestDriverOrderInfoDetail_CountMessengerCompletedOrder(t *testing.T) {

	t.Run("count completed messenger orders correctly", func(tt *testing.T) {
		list := model.DriverOrderInfoList{
			Head: &model.DriverOrderInfoNode{
				Order: model.DriverOrderInfoDetail{
					OrderId: "LMF-1",
					Status:  model.StatusCompleted,
				},
				Next: &model.DriverOrderInfoNode{
					Order: model.DriverOrderInfoDetail{
						OrderId: "LMM-1",
						Status:  model.StatusCompleted,
					},
					Next: &model.DriverOrderInfoNode{
						Order: model.DriverOrderInfoDetail{
							OrderId: "LMM-2",
							Status:  model.StatusCompleted,
						},
						Next: &model.DriverOrderInfoNode{},
					},
				},
			},
		}

		act := list.CountMessengerCompleteOrder()
		require.Equal(tt, 2, act)
	})

}
