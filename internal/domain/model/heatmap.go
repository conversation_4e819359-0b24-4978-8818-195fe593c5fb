package model

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type HeatMapType string

func (si HeatMapType) String() string {
	return string(si)
}

const (
	DemandHm       HeatMapType = "DEMAND_HEATMAP"
	MatchingRateHm HeatMapType = "MATCHING_RATE_HEATMAP"
)

type HeatMap struct {
	H3ID       string          `bson:"h3id" json:"h3id"`
	Tier       int             `bson:"tier" json:"tier"`
	Location   HeatMapLocation `bson:"location" json:"location"`
	Total      int             `bson:"total" json:"total"`
	CreatedAt  time.Time       `bson:"created_at" json:"created_at"`
	UpdatedAt  time.Time       `bson:"updated_at" json:"updated_at"`
	Start      string          `bson:"start" json:"start"`
	End        string          `bson:"end" json:"end"`
	Region     string          `bson:"region" json:"region"`
	Thresholds []Threshold     `bson:"thresholds" json:"thresholds"`
}

type HeatMapByTime struct {
	StartTime   string      `bson:"start_time" json:"startTime"`
	EndTime     string      `bson:"end_time" json:"endTime"`
	HeatmapType HeatMapType `bson:"heatmap_type" json:"heatmapType"`
}

type MatchingRateHeatMap struct {
	UpdatedAt             time.Time       `bson:"updated_at"`
	CreatedAt             time.Time       `bson:"created_at"`
	H3Index               string          `bson:"h3index"`
	H3IndexParent         string          `bson:"h3index_parent"`
	Region                string          `bson:"region"`
	Location              HeatMapLocation `bson:"location"`
	AcceptOrder           int             `bson:"accept_order"`
	UnmatchedOrder        int             `bson:"unmatched_order"`
	UnmatchedOrderMax     int             `bson:"unmatched_order_max"`
	MatchingRate          float64         `bson:"matching_rate"`
	AcceptRate            float64         `bson:"accept_rate"`
	TotalOrder            int             `bson:"total_order"`
	UnmatchedScorePercent float64         `bson:"unmatched_score_percent"`
	UnmatchedScoreMax     float64         `bson:"unmatched_score_max"`
	UnmatchedScore        float64         `bson:"unmatched_score"`
}

type Slot struct {
	Weekday int `bson:"weekday" json:"weekday"`
	Hour    int `bson:"hour" json:"hour"`
}

type ConditionItem struct {
	Region string      `json:"region"`
	Start  string      `json:"start_time"`
	End    string      `json:"end_time"`
	Tiers  []Threshold `json:"tiers"`
}

type Threshold struct {
	Threshold int
}

type Condition struct {
	Conditions []ConditionItem `json:"data" validate:"required"`
}

func (c *Condition) GetConditionItem(o OrderEventPayload) (bool, ConditionItem) {
	var condItem ConditionItem

	existing := false
	for _, c := range c.Conditions {
		oCreatedAt := o.CreatedAt.In(timeutil.BangkokLocation()).Format("15:04")
		if o.Region == c.Region &&
			len(c.Start) > 0 &&
			len(c.End) > 0 &&
			c.Start <= oCreatedAt &&
			c.End >= oCreatedAt {
			condItem = c
			existing = true
			break
		}
	}
	return existing, condItem
}

func (ci *ConditionItem) GetHeatMapTier(total int) int {
	for i, tier := range ci.Tiers {
		if tier.Threshold != 0 && total >= tier.Threshold {
			return i + 1
		}
	}
	return 0
}
