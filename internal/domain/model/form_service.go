package model

import (
	"context"
	"errors"
	"time"

	commonv1 "git.wndv.co/go/proto/lineman/egs/common/v1"
	formServicePb "git.wndv.co/go/proto/lineman/form_service/v1"
	"git.wndv.co/lineman/absinthe/crypt"
)

type FormService struct {
	FormType    FormType               `json:"formType"`
	FormSubtype FormSubtype            `json:"formSubtype"`
	Prefill     map[string]interface{} `json:"prefill"`
}

type FormServiceFields struct {
	DriverID                    string                    `json:"driverId"`
	FirstName                   crypt.LazyEncryptedString `json:"firstName"`
	LastName                    crypt.LazyEncryptedString `json:"lastName"`
	CitizenID                   crypt.LazyEncryptedString `json:"citizenId"`
	CitizenIDCardPhotoURL       string                    `json:"citizenIdCardPhotoURL"`
	PhoneNumber                 crypt.LazyEncryptedString `json:"phoneNumber"`
	Title                       crypt.LazyEncryptedString `json:"title"`
	Birthday                    time.Time                 `json:"birthday"`
	Address                     Address                   `json:"address"`
	VehicleRegistrationDate     time.Time                 `json:"vehicleRegistrationDate"`
	VehicleRegistrationPhotoURL string                    `json:"vehicleRegistrationPhotoURL"`
}

type FormType string

type FormSubType string

const (
	FormTypeInsuranceRegistration FormType = "FORM_TYPE_INSURANCE_REGISTRATION"
	FormTypePurchaseEGS           FormType = "FORM_TYPE_PURCHASE_EGS"
	FormTypeDriversClaim          FormType = "FORM_TYPE_DRIVERS_CLAIM"
)

type FormSubtype string

const (
	FormSubtypeRiderCompensation           FormSubtype = "FORM_SUBTYPE_RIDER_COMPENSATION"
	FormSubtypeCreditTopupClaim            FormSubtype = "FORM_SUBTYPE_CREDIT_TOPUP_CLAIM"
	FormSubtypeFairDisputeClaim            FormSubtype = "FORM_SUBTYPE_FAIR_DISPUTE_CLAIM"
	FormSubtypeFoodClaim                   FormSubtype = "FORM_SUBTYPE_FOOD_CLAIM"
	FormSubtypeCovidClaim                  FormSubtype = "FORM_SUBTYPE_COVID_CLAIM"
	FormSubtypeParkingClaim                FormSubtype = "FORM_SUBTYPE_PARKING_CLAIM"
	FormSubtypeAccidentClaim               FormSubtype = "FORM_SUBTYPE_ACCIDENT_CLAIM"
	FormSubtypeLongWaitingTimeCompensation FormSubtype = "FORM_SUBTYPE_LONG_WAITING_TIME_COMPENSATION"
	FormSubtypeFareDisputePickupClaim      FormSubtype = "FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM"
	FormSubtypeDriver2WQRPaymentClaim      FormSubtype = "FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM"
	FormSubtypePositiveCredit              FormSubtype = "FORM_SUBTYPE_POSITIVE_CREDIT"
)

func (fst FormSubtype) ToString() string {
	return string(fst)
}

var FormSubTypeTitle = map[formServicePb.FormSubtype]string{
	formServicePb.FormSubtype_FORM_SUBTYPE_FOOD_CLAIM:                     "ขอชดเชยค่าอาหาร",
	formServicePb.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION:             "ขอชดเชยค่าเสียเวลา: ออเดอร์ยกเลิก",
	formServicePb.FormSubtype_FORM_SUBTYPE_LONG_WAITING_TIME_COMPENSATION: "ขอชดเชยค่าเสียเวลา: รออาหารนาน",
	formServicePb.FormSubtype_FORM_SUBTYPE_PARKING_CLAIM:                  "ขอชดเชยค่าที่จอดรถ",
	formServicePb.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM:             "ขอชดเชยระยะทางไปส่งไม่ตรง (ร้านค้า ไป ลูกค้า)",
	formServicePb.FormSubtype_FORM_SUBTYPE_COVID_CLAIM:                    "ขอชดเชยค่าโควิด",
	formServicePb.FormSubtype_FORM_SUBTYPE_CREDIT_TOPUP_CLAIM:             "แจ้งปัญหาเติมเครดิตไม่เข้า",
	formServicePb.FormSubtype_FORM_SUBTYPE_ACCIDENT_CLAIM:                 "ขอชดเชยอุบัติเหตุ",
	formServicePb.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM:      "ขอชดเชยระยะทางไปรับไม่ตรง (ไรเดอร์ ไป ร้านค้า)",
	formServicePb.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM:     "ขอตรวจสอบค่าโดยสารที่ยังไม่ได้รับ",
}

func NewFormServicePrefill(formType FormType, driver Driver) (FormService, error) {
	prefill := make(map[string]interface{}, 0)
	switch formType {
	case FormTypeInsuranceRegistration:
		prefill["driverId"] = driver.DriverID
		prefill["tier"] = driver.DriverTier.ToString()
		prefill["firstName"] = driver.Firstname.String()
		prefill["lastName"] = driver.Lastname.String()
		prefill["citizenId"] = driver.CitizenID.String()
		prefill["phoneNumber"] = driver.Phone.String()
		prefill["title"] = driver.Title.String()

		if driver.Birthday != nil {
			prefill["birthday"] = driver.Birthday.Format(time.DateOnly)
		}

		prefill["address"] = map[string]interface{}{
			"houseNumber": driver.Address.HouseNumber.String(),
			"moo":         driver.Address.Moo.String(),
			"subdistrict": driver.Address.Subdistrict.String(),
			"district":    driver.Address.District.String(),
			"province":    driver.Address.Province.String(),
			"zipcode":     driver.Address.Zipcode.String(),
		}

		if driver.Vehicle.RegistrationDate != nil {
			prefill["vehicleRegistrationDate"] = driver.Vehicle.RegistrationDate.Format("2006")
		}
	case FormTypePurchaseEGS:
		prefill["driverId"] = driver.DriverID
		prefill["title"] = driver.Title.String()
		prefill["firstName"] = driver.Firstname.String()
		prefill["lastName"] = driver.Lastname.String()
		prefill["phoneNumber"] = driver.Phone.String()
		prefill["address"] = map[string]interface{}{
			"houseNumber": driver.Address.HouseNumber.String(),
			"moo":         driver.Address.Moo.String(),
			"subdistrict": driver.Address.Subdistrict.String(),
			"district":    driver.Address.District.String(),
			"province":    driver.Address.Province.String(),
			"zipcode":     driver.Address.Zipcode.String(),
		}
	default:
		return FormService{}, errors.New("form type not found")
	}

	return FormService{
		FormType: formType,
		Prefill:  prefill,
	}, nil
}

type PaymentTenor struct {
	NetPrice     float64 `form:"netPrice" binding:"required" bson:"net_price"`
	Tenor        int     `form:"tenor" binding:"required" bson:"tenor"`
	DailyAmount  float64 `form:"dailyAmount" binding:"required" bson:"daliy_amount"`
	RewardLabel  string  `form:"rewardLabel" bson:"reward_label"`
	RewardAmount float64 `form:"rewardAmount" bson:"reward_amount"`
	RewardSku    string  `form:"rewardSku" bson:"reward_sku"`
}

type FormPrefillBuilder func(formSubtype FormSubtype, driver Driver, prefill map[string]interface{}) (map[string]interface{}, error)

func NewFormServicePrefillV2(formType FormType, formSubtype FormSubtype, driver Driver, value map[string]interface{}, builders ...FormPrefillBuilder) (FormService, error) {
	prefill := make(map[string]interface{}, 0)
	switch formType {
	case FormTypeDriversClaim, FormTypePurchaseEGS:
		var err error
		for _, builder := range builders {
			prefill, err = builder(formSubtype, driver, prefill)
			if err != nil {
				return FormService{}, err
			}
		}
	default:
		return FormService{}, errors.New("form type not found")
	}

	return FormService{
		FormType:    formType,
		FormSubtype: formSubtype,
		Prefill:     prefill,
	}, nil
}

func ToServiceFormType(formType FormType) formServicePb.FormType {
	switch formType {
	case FormTypeInsuranceRegistration:
		return formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION
	case FormTypePurchaseEGS:
		return formServicePb.FormType_FORM_TYPE_PURCHASE_EGS
	case FormTypeDriversClaim:
		return formServicePb.FormType_FORM_TYPE_DRIVERS_CLAIM
	default:
		return formServicePb.FormType_FORM_TYPE_UNSPECIFIED
	}
}

func ToServiceFormSubtype(formSubtype FormSubtype) formServicePb.FormSubtype {
	if string(formSubtype) == "" {
		return formServicePb.FormSubtype_FORM_SUBTYPE_NONE
	}
	switch formSubtype {
	case FormSubtypeCreditTopupClaim:
		return formServicePb.FormSubtype_FORM_SUBTYPE_CREDIT_TOPUP_CLAIM
	case FormSubtypeFairDisputeClaim:
		return formServicePb.FormSubtype_FORM_SUBTYPE_FAIR_DISPUTE_CLAIM
	case FormSubtypeFoodClaim:
		return formServicePb.FormSubtype_FORM_SUBTYPE_FOOD_CLAIM
	case FormSubtypeRiderCompensation:
		return formServicePb.FormSubtype_FORM_SUBTYPE_RIDER_COMPENSATION
	case FormSubtypeAccidentClaim:
		return formServicePb.FormSubtype_FORM_SUBTYPE_ACCIDENT_CLAIM
	case FormSubtypeCovidClaim:
		return formServicePb.FormSubtype_FORM_SUBTYPE_COVID_CLAIM
	case FormSubtypeLongWaitingTimeCompensation:
		return formServicePb.FormSubtype_FORM_SUBTYPE_LONG_WAITING_TIME_COMPENSATION
	case FormSubtypeParkingClaim:
		return formServicePb.FormSubtype_FORM_SUBTYPE_PARKING_CLAIM
	case FormSubtypeFareDisputePickupClaim:
		return formServicePb.FormSubtype_FORM_SUBTYPE_FARE_DISPUTE_PICKUP_CLAIM
	case FormSubtypeDriver2WQRPaymentClaim:
		return formServicePb.FormSubtype_FORM_SUBTYPE_DRIVER_2W_QR_PAYMENT_CLAIM
	case FormSubtypePositiveCredit:
		return formServicePb.FormSubtype_FORM_SUBTYPE_POSITIVE_CREDIT
	default:
		return formServicePb.FormSubtype_FORM_SUBTYPE_UNSPECIFIED
	}
}

const (
	FormPrefillDriverId                         string = "driverId"
	FormPrefillOrderId                          string = "orderId"
	FormPrefillTripId                           string = "tripId"
	FormPrefillOrderStatus                      string = "orderStatus"
	FormPrefillWorkingRegion                    string = "region"
	FormPrefillRequesterCantConnectPhotoUrls    string = "requesterCannotContactPhotoURLs"
	FormPrefillFoodPhotoUrls                    string = "foodPhotoURLs"
	FormPrefillSlipDetailName                   string = "slipDetailName"
	FormPrefillOrderDistanceFromRiderApp        string = "distanceFromLinemanRiderApp"
	FormPrefillRiderSelfCancelPhotoUrls         string = "riderSelfCancelPhotoURLs"
	FormPrefillFirstName                        string = "firstName"
	FormPrefillLastName                         string = "lastName"
	FormPrefillPaymentMethod                    string = "paymentMethod"
	FormPrefillDriverMoneyFlow                  string = "driverMoneyFlow"
	FormPrefillFoodCompensationAmount           string = "foodCompensationAmount"
	FormPrefillDriverWage                       string = "driverWage"
	FormPrefillOriginalRiderSelfCancelPhotoURLs string = "originalRiderSelfCancelPhotoURLs"
	FormPrefillPhoneNumber                      string = "phoneNumber"
	FormPrefillServiceType                      string = "serviceType"
	FormPrefillPrivacyPolicyURL                 string = "privacyPolicyURL"
	FormPrefillDriverMatchedLocation            string = "driverMatchedLocation"
	FormPrefillPickupLocation                   string = "pickupLocation"
	FormPrefillBeforePickupLocation             string = "beforePickupLocation"
	FormPrefillGoogleDirectionClaimDistanceURL  string = "googleDirectionClaimDistanceURL"
	FormPrefillDriverType                       string = "driverType"
	FormPrefillDriverVendorID                   string = "driverVendorId"
	FormPrefillDriverCommission                 string = "driverCommission"
	FormPrefillOrderType                        string = "orderType"
	FormPrefillTripFare                         string = "tripFare"
	FormPrefillPaymentStatus                    string = "paymentStatus"
	FormPrefillTier                             string = "tier"
	FormPrefillTitle                            string = "title"
	FormPrefillAddress                          string = "address"
	FormPrefillHouseNumberAndMoo                string = "houseNumberAndMoo"
	FormPrefillSubdistrict                      string = "subdistrict"
	FormPrefillDistrict                         string = "district"
	FormPrefillProvince                         string = "province"
	FormPrefillZipcode                          string = "zipcode"
	FormPrefillPaymentTenor                     string = "paymentTenor"
	FormPrefillBatchId                          string = "batchId"
	FormPrefillTemplateId                       string = "templateId"
	FormPrefillProductSku                       string = "productSku"
	FormPrefillNetPrice                         string = "netPrice"
	FormPrefillTenor                            string = "tenor"
	FormPrefillDailyAmount                      string = "dailyAmount"
	FormPrefillRewardAmount                     string = "rewardAmount"
	FormPrefillRewardLabel                      string = "rewardLabel"
	FormPrefillProductName                      string = "productName"
	FormPrefillProductBundleSkus                string = "productBundleSkus"
	FormPrefillProductImageUrls                 string = "productImageUrls"
	FormPrefillBatchGroupType                   string = "batchGroupType"
	FormPrefillDeliverBy                        string = "deliverBy"
	FormPrefillType                             string = "type"
	FormPrefillRewardSku                        string = "rewardSku"
)

type FormBuilder interface {
	Validate(ctx context.Context) (bool, error)
	Get(ctx context.Context) (FormService, error)
}

type FormEGSBatchGroupType string

const (
	FormEGSBatchGroupTypeUnspecified    FormEGSBatchGroupType = "BATCH_GROUP_TYPE_UNSPECIFIED"
	FormEGSBatchGroupTypeEGS            FormEGSBatchGroupType = "BATCH_GROUP_TYPE_EGS"
	FormEGSBatchGroupTypeGold           FormEGSBatchGroupType = "BATCH_GROUP_TYPE_GOLD"
	FormEGSBatchGroupTypeRiderGear      FormEGSBatchGroupType = "BATCH_GROUP_TYPE_RIDER_GEAR"
	FormEGSBatchGroupTypePositiveCredit FormEGSBatchGroupType = "BATCH_GROUP_TYPE_POSITIVE_CREDIT"
)

func (f FormEGSBatchGroupType) ToProtoBatchGroupType() commonv1.BatchGroupType {
	switch f {
	case FormEGSBatchGroupTypeUnspecified:
		return commonv1.BatchGroupType_BATCH_GROUP_TYPE_UNSPECIFIED
	case FormEGSBatchGroupTypeEGS:
		return commonv1.BatchGroupType_BATCH_GROUP_TYPE_EGS
	case FormEGSBatchGroupTypeGold:
		return commonv1.BatchGroupType_BATCH_GROUP_TYPE_GOLD
	case FormEGSBatchGroupTypeRiderGear:
		return commonv1.BatchGroupType_BATCH_GROUP_TYPE_RIDER_GEAR
	case FormEGSBatchGroupTypePositiveCredit:
		return commonv1.BatchGroupType_BATCH_GROUP_TYPE_POSITIVE_CREDIT
	default:
		return commonv1.BatchGroupType_BATCH_GROUP_TYPE_UNSPECIFIED
	}
}
