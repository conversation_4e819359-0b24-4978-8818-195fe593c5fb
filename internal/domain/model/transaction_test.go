package model

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

func ramarkContains(remarks []Remark, s string) bool {
	for _, remark := range remarks {
		if remark.Message == s {
			return true
		}
	}
	return false
}

func TestTransaction(t *testing.T) {
	t.<PERSON>l()

	t.Run("SetProfileStatus", func(tt *testing.T) {
		tt.Run("Pending -> Processing should success", func(ttt *testing.T) {
			transaction := NewTransaction(
				"tran-1",
				UserTransactionChannel,
				PurchaseTransactionAction,
				PendingTransactionStatus,
				*NewWithdrawWalletTransactionInfo("driver-1", 100),
			)

			err := transaction.SetStatus(ProcessingTransactionStatus)

			require.Nil(ttt, err)
			require.Equal(ttt, ProcessingTransactionStatus, transaction.Status)
			require.True(ttt,
				ramarkContains(
					transaction.Remarks,
					GetTransactionBalanceLog(
						transaction.Info.CreditBalance,
						transaction.Info.WalletBalance,
						transaction.Info.InstallmentAmount,
					),
				),
			)
		})

		tt.Run("Pending -> Reject should success", func(ttt *testing.T) {
			transaction := NewTransaction(
				"tran-1",
				UserTransactionChannel,
				PurchaseTransactionAction,
				PendingTransactionStatus,
				*NewWithdrawWalletTransactionInfo("driver-1", 100),
			)

			err := transaction.SetStatus(RejectedTransactionStatus)

			require.Nil(ttt, err)
			require.Equal(ttt, RejectedTransactionStatus, transaction.Status)
			require.True(ttt,
				ramarkContains(
					transaction.Remarks,
					GetTransactionBalanceLog(
						transaction.Info.CreditBalance,
						transaction.Info.WalletBalance,
						transaction.Info.InstallmentAmount,
					),
				),
			)
		})

		tt.Run("Pending -> not [Processing, Reject] should error", func(ttt *testing.T) {
			transaction := NewTransaction(
				"tran-1",
				UserTransactionChannel,
				PurchaseTransactionAction,
				PendingTransactionStatus,
				*NewWithdrawWalletTransactionInfo("driver-1", 100),
			)

			err := transaction.SetStatus(SuccessTransactionStatus)

			require.NotNil(ttt, err)
			require.Equal(ttt, PendingTransactionStatus, transaction.Status)

			err = transaction.SetStatus(UnmatchedTransactionStatus)

			require.NotNil(ttt, err)
			require.Equal(ttt, PendingTransactionStatus, transaction.Status)
		})

		tt.Run("Processing -> Completed should success", func(ttt *testing.T) {
			transaction := NewTransaction(
				"tran-1",
				UserTransactionChannel,
				PurchaseTransactionAction,
				ProcessingTransactionStatus,
				*NewWithdrawWalletTransactionInfo("driver-1", 100),
			)

			err := transaction.SetStatus(SuccessTransactionStatus)

			require.Nil(ttt, err)
			require.Equal(ttt, SuccessTransactionStatus, transaction.Status)
			require.True(ttt,
				ramarkContains(
					transaction.Remarks,
					GetTransactionBalanceLog(
						transaction.Info.CreditBalance,
						transaction.Info.WalletBalance,
						transaction.Info.InstallmentAmount,
					),
				),
			)
		})

		tt.Run("Processing -> Fail should success", func(ttt *testing.T) {
			transaction := NewTransaction(
				"tran-1",
				UserTransactionChannel,
				PurchaseTransactionAction,
				ProcessingTransactionStatus,
				*NewWithdrawWalletTransactionInfo("driver-1", 100),
			)

			err := transaction.SetStatus(FailTransactionStatus)

			require.Nil(ttt, err)
			require.Equal(ttt, FailTransactionStatus, transaction.Status)
			require.True(ttt,
				ramarkContains(
					transaction.Remarks,
					GetTransactionBalanceLog(
						transaction.Info.CreditBalance,
						transaction.Info.WalletBalance,
						transaction.Info.InstallmentAmount,
					),
				),
			)
		})

		tt.Run("Processing -> not [Completed] should error", func(ttt *testing.T) {
			transaction := NewTransaction(
				"tran-1",
				UserTransactionChannel,
				PurchaseTransactionAction,
				ProcessingTransactionStatus,
				*NewWithdrawWalletTransactionInfo("driver-1", 100),
			)

			err := transaction.SetStatus(UnmatchedTransactionStatus)

			require.NotNil(ttt, err)
			require.Equal(ttt, ProcessingTransactionStatus, transaction.Status)

			err = transaction.SetStatus(RejectedTransactionStatus)

			require.NotNil(ttt, err)
			require.Equal(ttt, ProcessingTransactionStatus, transaction.Status)
		})
	})
}

func TestTransaction_Void(t *testing.T) {
	type expect struct {
		isError  bool
		category TransactionCategory
		txnType  TransactionType
		amount   types.Money
		subtype  TransactionSubType
	}

	testcases := []struct {
		name   string
		target Transaction
		expect expect
	}{
		{
			name: "void wallet coupon",
			target: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCouponTransactionInfo(
				"driver-1",
				"order-1",
				"trip-1",
				"code-1",
				20,
			)),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   20,
			},
		},
		{
			name:   "void wallet subsidize",
			target: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewSubsidizeTransactionInfo("driver-1", "order-1", "trip-1", "code-1", 20)),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   20,
				subtype:  ParkingFeeTransactionSubType,
			},
		},
		{
			name: "void wallet incentive",
			target: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewIncentiveTransactionInfo(
				"driver-1",
				20,
				[]string{},
			)),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   20,
			},
		},
		{
			name: "void wallet claim",
			target: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewClaimTransactionInfo(
				"driver-1",
				20,
			)),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   20,
			},
		},
		{
			name: "void wallet compensation",
			target: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCompensationTransactionInfo(
				"driver-1",
				20,
			)),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   20,
				subtype:  CovidCompensationTransactionSubType,
			},
		},
		{
			name: "void wallet new rider incentive",
			target: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewNewRiderIncentiveTransactionInfo(
				"driver-1",
				20,
				[]string{},
			)),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   20,
			},
		},
		{
			name: "void wallet not allow type should error",
			target: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWithdrawWalletTransactionInfo(
				"driver-1",
				20,
			)),
			expect: expect{
				isError: true,
			},
		},
		{
			name:   "void on top transaction",
			target: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewOnTopTransactionInfo("driver-1", "order1", "trip-1", 20, "", nil)),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   20,
			},
		},
		{
			name: "[WALLET][ADDITION] void custom transaction and new type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: "NEW_TYPE", Category: WalletTransactionCategory, Operator: AdditionOperator},
				WalletTopUpTransactionAction,
			),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[WALLET][ADDITION] void custom transaction and new type and sub type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: "NEW_TYPE", SubType: "NEW_SUBTYPE", Category: WalletTransactionCategory, Operator: AdditionOperator},
				WalletTopUpTransactionAction,
			),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[WALLET][ADDITION] void custom transaction and exist type but new sub type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: CompensationTransactionType, SubType: "NEW_SUBTYPE", Category: WalletTransactionCategory, Operator: AdditionOperator},
				WalletTopUpTransactionAction,
			),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  DeductVoidTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[WALLET][SUBTRACTION] void custom transaction and new type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: "NEW_TYPE", Category: WalletTransactionCategory, Operator: SubtractionOperator},
				WalletTopUpTransactionAction,
			),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  VoidTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[WALLET][SUBTRACTION] void custom transaction and new type and sub type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: "NEW_TYPE", SubType: "NEW_SUBTYPE", Category: WalletTransactionCategory, Operator: SubtractionOperator},
				ChargeTransactionAction,
			),
			expect: expect{
				category: WalletTransactionCategory,
				txnType:  VoidTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[CREDIT][ADDITION] void custom transaction new type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: "NEW_TYPE", Category: CreditTransactionCategory, Operator: AdditionOperator},
				ChargeTransactionAction,
			),
			expect: expect{
				category: CreditTransactionCategory,
				txnType:  VoidTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[CREDIT][ADDITION] void custom transaction with new type and sub type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: "NEW_TYPE", SubType: "NEW_SUBTYPE", Category: CreditTransactionCategory, Operator: AdditionOperator},
				ChargeTransactionAction,
			),
			expect: expect{
				category: CreditTransactionCategory,
				txnType:  VoidTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[CREDIT][SUBTRACTION] void custom transaction type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: "NEW_TYPE", Category: CreditTransactionCategory, Operator: SubtractionOperator},
				ChargeTransactionAction,
			),
			expect: expect{
				category: CreditTransactionCategory,
				txnType:  VoidReturnCreditTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[CREDIT][SUBTRACTION] void custom transaction with new type and sub type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: "NEW_TYPE", SubType: "NEW_SUBTYPE", Category: CreditTransactionCategory, Operator: SubtractionOperator},
				ChargeTransactionAction,
			),
			expect: expect{
				category: CreditTransactionCategory,
				txnType:  VoidReturnCreditTransactionType,
				amount:   100.0,
			},
		},
		{
			name: "[CREDIT][SUBTRACTION] void custom transaction with exist type but new sub type",
			target: createTransactionWithScheme(
				TransactionScheme{Type: ChargeTransactionType, SubType: "NEW_SUBTYPE", Category: CreditTransactionCategory, Operator: SubtractionOperator},
				ChargeTransactionAction,
			),
			expect: expect{
				category: CreditTransactionCategory,
				txnType:  VoidReturnCreditTransactionType,
				amount:   100.0,
			},
		},
	}

	for _, tc := range testcases {
		actual, err := tc.target.Void()

		if tc.expect.isError {
			require.Error(t, err, tc.name)
		} else {
			require.NoError(t, err, tc.name)
			require.Equal(t, tc.expect.category, actual.Category, tc.name)
			require.Equal(t, tc.expect.amount, actual.Amount, tc.name)
			require.Equal(t, tc.expect.txnType, actual.Type, tc.name)
			require.Equal(t, tc.target.TransactionID, actual.RefID, tc.name)
			require.True(t,
				ramarkContains(
					tc.target.Remarks,
					GetTransactionBalanceLog(
						tc.target.Info.CreditBalance,
						tc.target.Info.WalletBalance,
						tc.target.Info.InstallmentAmount,
					),
				),
			)
		}
	}

	t.Run("when void with type ItemFeeTransactionType should create void return credit transaction", func(ttt *testing.T) {
		// arrange
		transaction := createTransaction(ItemFeeTransactionType)
		// act
		voidTransaction, _ := transaction.Void()
		// assert
		require.Equal(t, CreditTransactionCategory, voidTransaction.Category)
		require.Equal(t, VoidReturnCreditTransactionType, voidTransaction.Type)
	})

	t.Run("when void with type CommissionTransactionType should create void return credit transaction", func(ttt *testing.T) {
		// arrange
		transaction := createTransaction(CommissionTransactionType)
		// act
		voidTransaction, _ := transaction.Void()
		// assert
		require.Equal(t, CreditTransactionCategory, voidTransaction.Category)
		require.Equal(t, VoidReturnCreditTransactionType, voidTransaction.Type)
	})

	t.Run("when void with type WithholdingTransactionType should create void return credit transaction", func(ttt *testing.T) {
		// arrange
		transaction := createTransaction(WithholdingTransactionType)
		// act
		voidTransaction, _ := transaction.Void()
		// assert
		require.Equal(t, CreditTransactionCategory, voidTransaction.Category)
		require.Equal(t, VoidReturnCreditTransactionType, voidTransaction.Type)
	})

	t.Run("when void with type ChargeTransactionType should create void return credit transaction", func(ttt *testing.T) {
		// arrange
		transaction := createTransaction(ChargeTransactionType)
		// act
		voidTransaction, _ := transaction.Void()
		// assert
		require.Equal(t, CreditTransactionCategory, voidTransaction.Category)
		require.Equal(t, VoidReturnCreditTransactionType, voidTransaction.Type)
	})
}

func TestTransaction_Unvoid(t *testing.T) {
	t.Run("unvoid credit transaction category", func(tt *testing.T) {
		type expect struct {
			isError    bool
			category   TransactionCategory
			txnType    TransactionType
			amount     types.Money
			subtype    TransactionSubType
			creditType TransactionCreditType
			ref        string
		}

		testcases := []struct {
			name     string
			void     Transaction
			original Transaction
			expect   expect
		}{
			{
				name: "unvoid FreeCreditTransactionInfo",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewVoidCreditTransactionInfo(
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewFreeCreditTransactionInfo(
					"driver-1",
					20,
				)),
				expect: expect{
					isError:  false,
					category: CreditTransactionCategory,
					txnType:  PurchaseTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid ItemFeeTransactionType ItemFeeInfo",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewVoidReturnCreditTransactionInfo(
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewItemFeeInfo("driver-1", "order-1", "trip-1", 20)),
				expect: expect{
					isError:  false,
					category: CreditTransactionCategory,
					txnType:  ItemFeeTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid CommissionTransactionType FreeTransactionCreditType ItemFeeInfo",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewVoidReturnCreditTransactionInfo(
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCommissionFreeCreditTransactionInfo(
					"driver-1",
					"order-1",
					20,
				)),
				expect: expect{
					isError:  false,
					category: CreditTransactionCategory,
					txnType:  CommissionTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid CommissionTransactionType ItemFeeInfo",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewVoidReturnCreditTransactionInfo(
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCommissionCreditTransactionInfo("driver-1", "order-1", "trip-1", 20, "")),
				expect: expect{
					isError:  false,
					category: CreditTransactionCategory,
					txnType:  CommissionTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid WithholdingTransactionType ItemFeeInfo",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewVoidReturnCreditTransactionInfo(
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWithholdingCreditTransactionInfo("driver-1", "order-1", "trip-1", 20)),
				expect: expect{
					isError:  false,
					category: CreditTransactionCategory,
					txnType:  WithholdingTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid WithholdingTransactionType FreeTransactionCreditType ItemFeeInfo",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewVoidReturnCreditTransactionInfo(
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWithholdingFreeCreditTransactionInfo(
					"driver-1",
					"order-1",
					20,
				)),
				expect: expect{
					isError:  false,
					category: CreditTransactionCategory,
					txnType:  WithholdingTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid ChargeTransactionType ItemFeeInfo",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewVoidReturnCreditTransactionInfo(
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewChargeTransactionInfo(
					"driver-1",
					"order-1",
					ItemFeeTransactionSubType,
					20,
				)),
				expect: expect{
					isError:  false,
					category: CreditTransactionCategory,
					txnType:  ChargeTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid unknown type",
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCompensationTransactionInfo(
					"driver-1",
					20,
				)),
				expect: expect{
					isError: true,
				},
			},
		}

		for _, tc := range testcases {
			actual, err := tc.void.AddVoid(tc.original)

			if tc.expect.isError {
				require.Error(t, err, tc.name)
			} else {
				require.NoError(t, err, tc.name)
				require.Equal(t, tc.expect.category, actual.Category, tc.name)
				require.Equal(t, tc.expect.amount, actual.Amount, tc.name)
				require.Equal(t, tc.expect.txnType, actual.Type, tc.name)
				require.Equal(t, tc.void.TransactionID, actual.RefID, tc.name)
				require.True(t,
					ramarkContains(
						tc.void.Remarks,
						GetTransactionBalanceLog(
							tc.void.Info.CreditBalance,
							tc.void.Info.WalletBalance,
							tc.void.Info.InstallmentAmount,
						),
					),
				)
			}
		}
	})
	t.Run("unvoid wallet transaction category", func(tt *testing.T) {
		type expect struct {
			isError    bool
			category   TransactionCategory
			txnType    TransactionType
			amount     types.Money
			subtype    TransactionSubType
			creditType TransactionCreditType
			ref        string
		}

		testcases := []struct {
			name     string
			void     Transaction
			original Transaction
			expect   expect
		}{
			{
				name: "unvoid CouponTransactionType",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
					"txn-1",
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCouponTransactionInfo(
					"driver-1",
					"order-1",
					"trip-1",
					"",
					20,
				)),
				expect: expect{
					isError:  false,
					category: WalletTransactionCategory,
					txnType:  CouponTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid SubsidizeTransactionType",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
					"txn-1",
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewSubsidizeTransactionInfo("driver-1", "order-1", "trip-1", "", 20)),
				expect: expect{
					isError:  false,
					category: WalletTransactionCategory,
					txnType:  SubsidizeTransactionType,
					amount:   20,
				},
			},
			{
				name: "unvoid IncentiveTransactionType",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
					"txn-1",
					"driver-1",
					48,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewIncentiveTransactionInfo(
					"driver-1",
					48,
					[]string{""},
				)),
				expect: expect{
					isError:  false,
					category: WalletTransactionCategory,
					txnType:  IncentiveTransactionType,
					amount:   48,
				},
			},
			{
				name: "unvoid ClaimTransactionType",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
					"txn-1",
					"driver-1",
					49,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewClaimTransactionInfo(
					"driver-1",
					49,
				)),
				expect: expect{
					isError:  false,
					category: WalletTransactionCategory,
					txnType:  ClaimTransactionType,
					amount:   49,
				},
			},
			{
				name: "unvoid CompensationTransactionType",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
					"txn-1",
					"driver-1",
					50,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCompensationTransactionInfo(
					"driver-1",
					50,
				)),
				expect: expect{
					isError:  false,
					category: WalletTransactionCategory,
					txnType:  CompensationTransactionType,
					amount:   50,
				},
			},
			{
				name: "unvoid NewRiderIncentiveTransactionType",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
					"txn-1",
					"driver-1",
					50,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewNewRiderIncentiveTransactionInfo(
					"driver-1",
					50,
					[]string{""},
				)),
				expect: expect{
					isError:  false,
					category: WalletTransactionCategory,
					txnType:  NewRiderIncentiveTransactionType,
					amount:   50,
				},
			},
			{
				name: "unvoid CashAdvanceCouponTransactionType",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
					"txn-1",
					"driver-1",
					99,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCashAdvanceCouponTransaction(
					"driver-1",
					"order-1",
					99,
				)),
				expect: expect{
					isError:  false,
					category: WalletTransactionCategory,
					txnType:  CashAdvanceCouponTransactionType,
					amount:   99,
				},
			},
			{
				name: "unvoid Unknown type",

				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewCompensationTransactionInfo(
					"driver-1",
					99,
				)),
				expect: expect{
					isError: true,
				},
			},
			{
				name: "unvoid OnTopTransactionType",
				void: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
					"txn-1",
					"driver-1",
					20,
				)),
				original: *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewOnTopTransactionInfo("driver-1", "order-1", "trip-1", 20, "", nil)),
				expect: expect{
					isError:  false,
					category: WalletTransactionCategory,
					txnType:  OnTopTransactionType,
					amount:   20,
				},
			},
		}

		for _, tc := range testcases {
			actual, err := tc.void.AddVoid(tc.original)

			if tc.expect.isError {
				require.Error(t, err, tc.name)
			} else {
				require.NoError(t, err, tc.name)
				require.Equal(t, tc.expect.category, actual.Category, tc.name)
				require.Equal(t, tc.expect.amount, actual.Amount, tc.name)
				require.Equal(t, tc.expect.txnType, actual.Type, tc.name)
				require.Equal(t, tc.void.TransactionID, actual.RefID, tc.name)
				require.True(t,
					ramarkContains(
						tc.void.Remarks,
						GetTransactionBalanceLog(
							tc.void.Info.CreditBalance,
							tc.void.Info.WalletBalance,
							tc.void.Info.InstallmentAmount,
						),
					),
				)
			}
		}
	})
	t.Run("unvoid not allowed status", func(tt *testing.T) {
		trans := *NewTransaction("txn-1", UserTransactionChannel, PurchaseTransactionAction, SuccessTransactionStatus, *NewWalletDeductVoidTransaction(
			"txn-1",
			"driver-1",
			20,
		))
		trans.Status = RejectedTransactionStatus
		_, err := trans.AddVoid(trans)
		require.Error(t, err)
	})
}

func TestTransaction_IsTransactionTypeNegative(t *testing.T) {
	testcases := []struct {
		transType TransactionType
		operator  TransactionSchemeOperator
		expect    bool
	}{
		{
			transType: ChargeTransactionType,
			expect:    true,
		},
		{
			transType: CommissionTransactionType,
			expect:    true,
		},
		{
			transType: ExpiredTransactionType,
			expect:    true,
		},
		{
			transType: ItemFeeTransactionType,
			expect:    true,
		},
		{
			transType: WithholdingTransactionType,
			expect:    true,
		},
		{
			transType: WithdrawTransactionType,
			expect:    true,
		},
		{
			transType: DeductVoidTransactionType,
			expect:    true,
		},
		{
			transType: PurchaseTransactionType,
			expect:    false,
		},
		{
			transType: VoidReturnCreditTransactionType,
			expect:    false,
		},
		{
			transType: AddTransactionType,
			expect:    false,
		},
		{
			transType: CashAdvanceCouponTransactionType,
			expect:    false,
		},
		{
			transType: ClaimTransactionType,
			expect:    false,
		},
		{
			transType: CompensationTransactionType,
			expect:    false,
		},
		{
			transType: CouponTransactionType,
			expect:    false,
		},
		{
			transType: DeliveryFeeTransactionType,
			expect:    false,
		},
		{
			transType: NewRiderIncentiveTransactionType,
			expect:    false,
		},
		{
			transType: RiderReferralIncentiveTransactionType,
			expect:    false,
		},
		{
			transType: IncentiveTransactionType,
			expect:    false,
		},
		{
			transType: SubsidizeTransactionType,
			expect:    false,
		},
		{
			transType: OtherIncentiveTransactionType,
			expect:    false,
		},
		{
			transType: OnTopTransactionType,
			expect:    false,
		},
		{
			transType: "CUSTOM_ADDITION_TYPE",
			expect:    false,
			operator:  AdditionOperator,
		},
		{
			transType: "CUSTOM_SUBTRACTION_TYPE",
			expect:    true,
			operator:  SubtractionOperator,
		},
	}

	for _, tc := range testcases {
		info := TransactionInfo{Type: tc.transType, Category: CreditTransactionCategory, Operator: tc.operator}
		res := info.IsTransactionTypeNegative()
		require.Equal(t, tc.expect, res)
	}

	voidtc := []struct {
		expect bool
		cate   TransactionCategory
	}{
		{
			expect: true,
			cate:   CreditTransactionCategory,
		},
		{
			expect: false,
			cate:   WalletTransactionCategory,
		},
	}

	for _, tc := range voidtc {
		info := TransactionInfo{Type: VoidTransactionType, Category: tc.cate}
		res := info.IsTransactionTypeNegative()
		require.Equal(t, tc.expect, res)
	}
}

func createTransaction(typ TransactionType) *Transaction {
	info := TransactionInfo{
		Type:       typ,
		Category:   CreditTransactionCategory,
		TransRefID: "txn-ref-id",
	}
	return NewTransaction("txn-1", UserTransactionChannel, VoidTransactionAction, SuccessTransactionStatus, info)
}

func createTransactionWithScheme(scheme TransactionScheme, action TransactionAction) Transaction {
	return *NewTransaction(
		utils.GenerateUUID(),
		AdminTransactionChannel,
		action,
		SuccessTransactionStatus,
		*NewTransactionInfoFromScheme(
			scheme,
			"driver-id",
			"order-id",
			100.0,
		),
	)
}

func TestTransaction_IsBackDateTransaction(t *testing.T) {
	type fields struct {
		Info      TransactionInfo
		CreatedAt time.Time
	}
	tests := []struct {
		name   string
		fields fields
		want   bool
	}{
		{
			name: "back date transaction",
			fields: fields{
				Info: TransactionInfo{
					TransactionDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				CreatedAt: time.Date(2021, 1, 2, 0, 0, 0, 0, time.UTC),
			},
			want: true,
		},
		{
			name: "transaction date is equal created at",
			fields: fields{
				Info: TransactionInfo{
					TransactionDate: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
				},
				CreatedAt: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			want: false,
		},
		{
			name: "transaction date is after created at",
			fields: fields{
				Info: TransactionInfo{
					TransactionDate: time.Date(2021, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				CreatedAt: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			want: false,
		},
		{
			name: "transaction date is zero",
			fields: fields{
				Info: TransactionInfo{
					TransactionDate: time.Time{},
				},
				CreatedAt: time.Date(2021, 1, 1, 0, 0, 0, 0, time.UTC),
			},
			want: false,
		},
		{
			name: "created at is zero",
			fields: fields{
				Info: TransactionInfo{
					TransactionDate: time.Date(2021, 1, 2, 0, 0, 0, 0, time.UTC),
				},
				CreatedAt: time.Time{},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			i := &Transaction{
				Info:      tt.fields.Info,
				CreatedAt: tt.fields.CreatedAt,
			}
			if got := i.IsBackDateTransaction(); got != tt.want {
				t.Errorf("Transaction.IsBackDateTransaction() = %v, want %v", got, tt.want)
			}
		})
	}
}
