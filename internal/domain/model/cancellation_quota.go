package model

import (
	"github.com/pkg/errors"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
)

var QuotaNotAvailable = errors.New("cancellation rate free quota not available")

func NewCancellationQuota(cfg config.CancellationRateConfig) *CancellationQuota {
	return &CancellationQuota{CancellationRateFreeQuota: cfg.InitialQuotaSize}
}

type CancellationQuota struct {
	CancellationRateFreeQuota     int `bson:"cancellation_rate_free_quota" json:"cancellationRateFreeQuota"`
	CompletedOrdersSinceLastQuota int `bson:"completed_orders_since_last_quota" json:"completedOrdersSinceLastQuota"`
}

func (cq *CancellationQuota) IsQuotaAvailable() bool {
	return cq.CancellationRateFreeQuota > 0
}

func (cq *CancellationQuota) AddCompletedOrderQuota(cfg config.CancellationRateConfig) {
	if cq.CancellationRateFreeQuota >= cfg.MaxQuotaSize {
		cq.CancellationRateFreeQuota = cfg.MaxQuotaSize
		cq.CompletedOrdersSinceLastQuota = 0
		return
	}

	cq.CompletedOrdersSinceLastQuota += 1
	if cq.CompletedOrdersSinceLastQuota >= cfg.CompleteOrdersPerQuota {
		cq.CancellationRateFreeQuota += 1
		cq.CompletedOrdersSinceLastQuota = 0
	}
}

func (cq *CancellationQuota) UseCancellationRateFreeQuota() error {
	if !cq.IsQuotaAvailable() {
		return QuotaNotAvailable
	}
	cq.CancellationRateFreeQuota -= 1
	return nil
}
