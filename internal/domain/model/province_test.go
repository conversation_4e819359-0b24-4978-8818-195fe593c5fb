package model

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestProvince_IsReviseApprovalFlow(t *testing.T) {
	tests := []struct {
		ApprovalFlow RegistrationFlow
		RegistFlow   RegistrationFlow
		expected     bool
	}{
		{"", "", false},
		{"", "NORMAL", false},
		{"", "REVISE", true},
		{"NORMAL", "", false},
		{"REVISE", "", true},
		{"NORMAL", "NORMAL", false},
		{"NORMAL", "REVISE", false},
		{"REVISE", "NORMAL", true},
		{"REVISE", "REVISE", true},
	}
	for _, test := range tests {
		t.Run(fmt.Sprintf("%v", test), func(t *testing.T) {
			p := Province{
				RegistrationFlow:  test.RegistFlow,
				AdminApprovalFlow: test.ApprovalFlow,
			}
			assert.Equal(t, test.expected, p.IsReviseApprovalFlow())
		})
	}
}

func TestProvince_IsReviseRegisterFlow(t *testing.T) {
	tests := []struct {
		RegistFlow RegistrationFlow
		expected   bool
	}{
		{"", false},
		{"NORMAL", false},
		{"REVISE", true},
	}
	for _, test := range tests {
		t.Run(fmt.Sprintf("%v", test), func(t *testing.T) {
			p := Province{
				RegistrationFlow: test.RegistFlow,
			}
			assert.Equal(t, test.expected, p.IsReviseRegisterFlow())
		})
	}
}
