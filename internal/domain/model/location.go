package model

import (
	"fmt"
	"strconv"
	"strings"
	"time"
)

type Location struct {
	Lat float64 `json:"lat" bson:"lat"`
	Lng float64 `json:"lng" bson:"lng"`
}

func (loc Location) IsLocationEmpty() bool {
	return loc.Lat == 0.0 && loc.Lng == 0.0
}

type LocationWithUpdatedAt struct {
	Lat       float64   `json:"lat" bson:"lat"`
	Lng       float64   `json:"lng" bson:"lng"`
	UpdatedAt time.Time `json:"updatedAt" bson:"updated_at"`
}

func ParseLocation(s string) (Location, error) {
	pos := strings.SplitN(s, ",", 3)
	if len(pos) != 2 {
		return Location{}, fmt.Errorf("expect string in format 'lat,lng'")
	}
	lat, err := strconv.ParseFloat(strings.TrimSpace(pos[0]), 64)
	if err != nil {
		return Location{}, fmt.<PERSON><PERSON><PERSON>("invalid latitude number %s", err)
	}
	lng, err := strconv.ParseFloat(strings.TrimSpace(pos[1]), 64)
	if err != nil {
		return Location{}, fmt.<PERSON><PERSON>rf("invalid longitude number %s", err)
	}
	return Location{lat, lng}, nil
}
