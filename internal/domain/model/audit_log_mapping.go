package model

import (
	"time"

	"github.com/naamancurtis/mongo-go-struct-to-bson/mapper"
	"go.mongodb.org/mongo-driver/bson"
)

type AuditLogMapping struct {
	Object    AuditObject   `bson:"object"`
	Actor     AuditLogActor `bson:"actor"`
	Timestamp time.Time     `bson:"timestamp"`
	Action    AuditAction   `bson:"action"`
	Event     AuditEvent    `bson:"event"`
	Before    interface{}   `bson:"before"`
	After     interface{}   `bson:"after"`
}

func newAuditLogMapping(log AuditLog) *AuditLogMapping {
	return &AuditLogMapping{
		Object:    log.Object,
		Actor:     log.Actor,
		Timestamp: log.Timestamp,
		Action:    log.Action,
		Event:     log.Event,
		Before:    log.Before,
		After:     log.After,
	}
}

func (m AuditLogMapping) AuditLog() *AuditLog {
	return &AuditLog{
		Object:    m.Object,
		Actor:     m.Actor,
		Timestamp: m.Timestamp,
		Action:    m.Action,
		Event:     m.Event,
		Before:    m.Before,
		After:     m.After,
	}
}

func (al *AuditLog) MarshalBSON() ([]byte, error) {
	al.Before = excludeField(al.Before)
	al.After = excludeField(al.After)
	mapping := newAuditLogMapping(*al)
	return bson.Marshal(mapping)
}

func (al *AuditLog) UnmarshalBSON(data []byte) error {
	mapping := &AuditLogMapping{}
	if err := bson.Unmarshal(data, mapping); err != nil {
		return err
	}

	*al = *mapping.AuditLog()

	return nil
}

func excludeField(obj interface{}) interface{} {
	filter, ok := obj.(AuditLogFilter)
	if !ok {
		return obj
	}

	// TODO instead of using reflection, the performance might be improved by code generating to generate conversion method
	m := mapper.ConvertStructToBSONMap(obj, &mapper.MappingOpts{})
	for _, exclude := range filter.ExcludeFields() {
		delete(m, exclude)
	}
	return m
}
