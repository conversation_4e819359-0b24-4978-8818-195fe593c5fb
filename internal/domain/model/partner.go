package model

import (
	"fmt"

	humanize "github.com/dustin/go-humanize"
)

// ActionKind is an action that uses for notify driver.
type ActionKind string

const (
	// ActionDriverTopUp notify to driver when Partners send topup notification.
	ActionDriverTopUp ActionKind = "DRIVER_TOPUP"
)

func EventTopUpNotification(dateTime []string, amount float64) map[string]string {
	return map[string]string{
		"action": string(ActionDriverTopUp),
		"title":  "โอนเงินเข้าเครดิตรับงาน",
		"body":   fmt.Sprintf("%s บาท วันที่ %s เวลา %s", humanize.CommafWithDigits(amount, 2), dateTime[0], dateTime[1]),
	}
}
