package model

import "time"

const (
	SingleDistribution DistributionType = "SINGLE"
	BatchDistribution  DistributionType = "BATCH"
)

type DistributionType string

const (
	SharedRound            OptimizationRound = "SHARED"
	DedicatedRound         OptimizationRound = "DEDICATED"
	SupplyPositioningRound OptimizationRound = "SUPPLY_POSITIONING"
)

type OptimizationRound string

const (
	Pre  FilterStep = "PRE"
	Post FilterStep = "POST"
)

type FilterStep string

type DistributionLogSearchEvent struct {
	DistributionID string
	Type           DistributionType
	Region         string
	Zone           *string
	Drivers        []string
	Orders         []string
	CapturedAt     time.Time
	StartedAt      time.Time
}

type DistributionLogOptimizeEvent struct {
	DistributionID       string
	Type                 DistributionType
	Region               string
	Zone                 *string
	Drivers              []string
	Orders               []string
	OptimizationRound    OptimizationRound
	OptimizationResponse string
	CapturedAt           time.Time
}

type DistributionLogFilterEvent struct {
	DistributionID    string
	Type              DistributionType
	Region            string
	Zone              *string
	TargetDrivers     []string
	TargetOrders      []string
	OptimizationRound *OptimizationRound
	Step              FilterStep
	Filter            RiderFilterName
	CapturedAt        time.Time
}
