package model

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type BikePriorityTimeSlot struct {
	TimeRanges []StartEndTime `json:"timeRanges" binding:"required,min=1" bson:"time_ranges"`
	DaysOfWeek []Days         `json:"daysOfWeek" binding:"required,min=1" bson:"days_of_week"`
}

func (bpts BikePriorityTimeSlot) IsActiveIn(t time.Time) bool {
	currentDayOfWeek := GetDays(t)
	if currentDayOfWeek == "" {
		return false
	}
	isInDaysOfWeek := false
	for _, dayOfWeek := range bpts.DaysOfWeek {
		if dayOfWeek == currentDayOfWeek {
			isInDaysOfWeek = true
			break
		}
	}
	if !isInDaysOfWeek {
		return false
	}
	for _, timeRange := range bpts.TimeRanges {
		if IsInStartEndTimeCondition(t.In(timeutil.BangkokLocation()), []StartEndTime{timeRange}) {
			return true
		}
	}
	return false
}

type BikePriorityTimeSlots []BikePriorityTimeSlot

func (bptss BikePriorityTimeSlots) IsEligible(t time.Time, region string, eligibleRegionsForBikePriority types.StringSet) bool {
	if !eligibleRegionsForBikePriority.IsInitialized() || !eligibleRegionsForBikePriority.Has(region) {
		return false
	}
	for _, timeSlot := range bptss {
		if timeSlot.IsActiveIn(t) {
			return true
		}
	}
	return false
}
