package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type AttendanceLogHistory struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	DriverID       string             `bson:"driver_id"`
	Shifts         []Shift            `bson:"shifts"`
	AttendanceLogs []AttendanceLog    `bson:"attendance_logs"`
	RunAt          time.Time          `bson:"run_at"`
	CreatedAt      time.Time          `bson:"created_at"`
	ExpiredAt      time.Time          `bson:"expired_at"`
}
