package model

type RegionCode string

func newRegionCode(code string) RegionCode {
	return RegionCode(code)
}

func (rc RegionCode) String() string {
	return string(rc)
}

type Region struct {
	code  RegionCode
	label string
}

func NewRegion(code, label string) *Region {
	return &Region{
		code:  newRegionCode(code),
		label: label,
	}
}

func (r Region) Code() RegionCode {
	return r.code
}

func (r Region) Label() string {
	return r.label
}

type RegionSet map[RegionCode]interface{}

func RegionSetFromList(regions []RegionCode) RegionSet {
	set := make(map[RegionCode]interface{})
	for _, r := range regions {
		set[r] = nil
	}

	return set
}

func (rs RegionSet) Add(region RegionCode) {
	rs[region] = nil
}

func (rs RegionSet) Contains(region RegionCode) bool {
	_, exists := rs[region]
	return exists
}

func (rs RegionSet) Elements() []RegionCode {
	result := make([]RegionCode, 0, len(rs))
	for regionCode := range rs {
		result = append(result, regionCode)
	}
	return result
}

func RegionsString(regions []RegionCode) string {
	regionsString := ""
	for i, r := range regions {
		if i > 0 {
			regionsString += "_"
		}
		regionsString += r.String()
	}

	return regionsString
}
