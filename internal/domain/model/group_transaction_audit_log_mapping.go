package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type GroupTransactionAuditLogMapping struct {
	Message   string    `bson:"message"`
	CreatedBy string    `bson:"created_by"`
	CreatedAt time.Time `bson:"created_at"`
}

func newGroupTransactionAuditLogMapping(log GroupTransactionAuditLog) GroupTransactionAuditLogMapping {
	return GroupTransactionAuditLogMapping{
		Message:   log.message,
		CreatedBy: log.createdBy,
		CreatedAt: log.createdAt,
	}
}

func (m GroupTransactionAuditLogMapping) AuditLog() GroupTransactionAuditLog {
	return GroupTransactionAuditLog{
		message:   m.Message,
		createdBy: m.CreatedBy,
		createdAt: m.CreatedAt,
	}
}

func (al GroupTransactionAuditLog) MarshalBSON() ([]byte, error) {
	mapping := newGroupTransactionAuditLogMapping(al)
	return bson.Marshal(mapping)
}

func (al *GroupTransactionAuditLog) UnmarshalBSON(data []byte) error {
	mapping := &GroupTransactionAuditLogMapping{}
	if err := bson.Unmarshal(data, mapping); err != nil {
		return err
	}

	*al = mapping.AuditLog()

	return nil
}
