package model

import (
	"time"

	"github.com/pkg/errors"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type TierType string

var (
	TierTypeFix      TierType = "FIX"
	TierTypeRelative TierType = "RELATIVE"
)

type SchemeMapping struct {
	Key              string               `bson:"key"`
	BaseFee          types.Money          `bson:"base_fee"`
	RoadFeeTiers     []RoadFeeTierMapping `bson:"road_fee_tiers"`
	BaseTier         []TierMapping        `bson:"base_tiers,omitempty"`
	PriceSchemeRefId string               `bson:"price_scheme_ref_id"`
	PriceSchemeName  string               `bson:"price_scheme_name"`
}

func newSchemeMapping(s DeliveryFeeSettingScheme) (*SchemeMapping, error) {

	tiers := make([]RoadFeeTierMapping, len(s.roadFeeTiers))
	for i, tier := range s.roadFeeTiers {
		tiers[i] = RoadFeeTierMapping{
			From:     tier.from.Float64(),
			FeePerKM: tier.feePerKM,
		}
	}

	return &SchemeMapping{
		Key:              s.key.Key(),
		BaseFee:          s.baseFee,
		RoadFeeTiers:     tiers,
		PriceSchemeRefId: s.priceSchemeRefId,
		PriceSchemeName:  s.priceSchemeName,
	}, nil
}

func newPricePolygonGeometryMapping(g PricePolygonGeometry) PricePolygonGeometryMapping {
	return PricePolygonGeometryMapping{
		Coordinates: g.Coordinates,
		Type:        g.Type,
	}
}

func newPricePolygonMapping(p PricePolygon) (PricePolygonMapping, error) {
	geo := newPricePolygonGeometryMapping(p.coordinates)

	schemes := make([]SchemeMapping, len(p.schemes))
	for i, s := range p.schemes {
		sm, err := newSchemeMapping(s)
		if err != nil {
			return PricePolygonMapping{}, err
		}

		schemes[i] = *sm
	}
	return PricePolygonMapping{
		ID:          p.id,
		Name:        p.name,
		Title:       p.title,
		Description: p.description,
		ZoneColor:   p.zoneColor,
		Geometry:    geo,
		Schemes:     schemes,
		CreatedAt:   p.createdAt,
		UpdatedAt:   p.updatedAt,
	}, nil
}

func (s SchemeMapping) Scheme() (DeliveryFeeSettingScheme, error) {
	var key DeliveryFeeSettingSchemeKey
	switch s.Key {
	case RestaurantTypeKeyRMS.Key():
		key = RestaurantTypeKeyRMS
	case RestaurantTypeKeyEMenu.Key():
		key = RestaurantTypeKeyEMenu
	case RestaurantTypeKeySocialMenu.Key():
		key = RestaurantTypeKeySocialMenu
	case RestaurantTypeKeyUnKnown.Key():
		key = PriceSchemeKeyDefault
	case PriceSchemeKeyDefault.Key():
		key = PriceSchemeKeyDefault
	default:
		return DeliveryFeeSettingScheme{}, errors.Errorf("invalid key of %v", s.Key)
	}

	scheme := DeliveryFeeSettingScheme{
		key:              key,
		baseFee:          s.BaseFee,
		roadFeeTiers:     make([]RoadFeeTier, 0, len(s.BaseTier)),
		priceSchemeRefId: s.PriceSchemeRefId,
		priceSchemeName:  s.PriceSchemeName,
	}

	if len(s.BaseTier) > 0 {
		for i, tm := range s.BaseTier {
			t, err := tm.Tier()
			if err != nil {
				return DeliveryFeeSettingScheme{}, err
			}
			switch ct := t.(type) {
			case *FixTier:
				scheme.baseFee = ct.fee
			case *RelativeTier:
				if i == 1 && tm.Head == 100 {
					scheme.roadFeeTiers = append(scheme.roadFeeTiers, RoadFeeTier{
						from:     100,
						feePerKM: ct.FeePerKM(),
					})
				} else {
					scheme.roadFeeTiers = append(scheme.roadFeeTiers, RoadFeeTier{
						from:     ct.head - 1,
						feePerKM: ct.FeePerKM(),
					})
				}
			}
		}
	} else if len(s.RoadFeeTiers) > 0 {
		for _, rt := range s.RoadFeeTiers {
			scheme.roadFeeTiers = append(scheme.roadFeeTiers, RoadFeeTier{
				from:     types.Distance(rt.From),
				feePerKM: rt.FeePerKM,
			})
		}
	}

	return scheme, nil
}

type DeliveryFeeSettingMapping struct {
	ID          string    `bson:"setting_delivery_fee_id"`
	Region      string    `bson:"region"`
	ServiceType string    `bson:"service_type"`
	CreatedAt   time.Time `bson:"created_at"`
	UpdatedAt   time.Time `bson:"updated_at"`

	// Single scheme version
	BaseTier      []TierMapping `bson:"base_tiers,omitempty"`
	IncrementRate types.Money   `bson:"increment_rate,omitempty"`

	// Multiple scheme version
	Schemes       []SchemeMapping       `bson:"schemes,omitempty"`
	PricePolygons []PricePolygonMapping `bson:"price_polygons"`
}

type PricePolygonMapping struct {
	ID          string                      `bson:"price_polygon_id"`
	Name        string                      `bson:"name"`
	Title       string                      `bson:"title,omitempty"`
	Description string                      `bson:"description,omitempty"`
	ZoneColor   ZoneColor                   `bson:"zone_color,omitempty"`
	Geometry    PricePolygonGeometryMapping `bson:"geometry"`
	Schemes     []SchemeMapping             `bson:"schemes,omitempty"`
	CreatedAt   time.Time                   `bson:"created_at"`
	UpdatedAt   time.Time                   `bson:"updated_at"`
}

type PricePolygonGeometryMapping struct {
	Type        string                  `bson:"type"`
	Coordinates PricePolygonCoordinates `bson:"coordinates"`
}

func (m DeliveryFeeSettingMapping) isMultipleSchemeVersion() bool {
	return len(m.Schemes) > 0
}

func newDeliveryFeeSettingMapping(setting DeliveryFeeSetting) (*DeliveryFeeSettingMapping, error) {
	schemes := make([]SchemeMapping, len(setting.schemes))
	for i, s := range setting.schemes {
		sm, err := newSchemeMapping(s)
		if err != nil {
			return nil, err
		}

		schemes[i] = *sm
	}

	pricePolygons := make([]PricePolygonMapping, len(setting.pricePolygons))
	for i, p := range setting.pricePolygons {
		pricePolygon, err := newPricePolygonMapping(p)
		if err != nil {
			return nil, err
		}
		pricePolygons[i] = pricePolygon
	}

	return &DeliveryFeeSettingMapping{
		ID:            setting.id,
		Region:        setting.region.String(),
		ServiceType:   setting.serviceType.String(),
		CreatedAt:     setting.createdAt,
		UpdatedAt:     setting.updatedAt,
		Schemes:       schemes,
		PricePolygons: pricePolygons,
	}, nil
}

func (m *DeliveryFeeSettingMapping) DeliveryFeeSetting() (*DeliveryFeeSetting, error) {
	setting := DeliveryFeeSetting{}
	setting.id = m.ID
	setting.region = newRegionCode(m.Region)
	setting.serviceType = newServiceType(m.ServiceType)
	setting.createdAt = m.CreatedAt
	setting.updatedAt = m.UpdatedAt

	defaultScheme := DeliveryFeeSettingScheme{
		key:          PriceSchemeKeyDefault,
		baseFee:      m.BaseTier[0].Fee,
		roadFeeTiers: make([]RoadFeeTier, 0, len(m.BaseTier)-1),
	}
	for _, tm := range m.BaseTier {
		t, err := tm.Tier()
		if err != nil {
			return nil, err
		}

		switch tt := t.(type) {
		case *FixTier:
			defaultScheme.baseFee = tt.fee
		case *RelativeTier:
			defaultScheme.roadFeeTiers = append(defaultScheme.roadFeeTiers, RoadFeeTier{
				from:     tt.head - 1,
				feePerKM: tt.FeePerKM(),
			})
		}
	}
	setting.schemes = []DeliveryFeeSettingScheme{defaultScheme}

	pricePolygons := make([]PricePolygon, len(m.PricePolygons))
	for index, pp := range m.PricePolygons {
		var pricePolygon PricePolygon
		// set geo
		pricePolygon.coordinates = PricePolygonGeometry{
			Type:        pp.Geometry.Type,
			Coordinates: pp.Geometry.Coordinates,
		}
		// set schemes
		schemes := make([]DeliveryFeeSettingScheme, len(pp.Schemes))
		for index, s := range pp.Schemes {
			scheme, err := s.Scheme()
			if err != nil {
				return &DeliveryFeeSetting{}, nil
			}
			schemes[index] = scheme
		}
		// set price polygon
		pricePolygon.schemes = schemes
		pricePolygons[index] = pricePolygon
	}
	setting.pricePolygons = pricePolygons

	return &setting, nil
}

func (m *DeliveryFeeSettingMapping) DeliveryFeeSettingFromMultipleScheme() (*DeliveryFeeSetting, error) {
	setting := DeliveryFeeSetting{
		id:          m.ID,
		region:      newRegionCode(m.Region),
		serviceType: newServiceType(m.ServiceType),
		createdAt:   m.CreatedAt,
		updatedAt:   m.UpdatedAt,
	}

	setting.schemes = make([]DeliveryFeeSettingScheme, len(m.Schemes))
	for i, sm := range m.Schemes {
		s, err := sm.Scheme()
		if err != nil {
			return nil, err
		}

		setting.schemes[i] = s
	}

	pricePolygons := make([]PricePolygon, len(m.PricePolygons))
	for index, pp := range m.PricePolygons {
		var pricePolygon PricePolygon
		// set geo
		pricePolygon.coordinates = PricePolygonGeometry{
			Type:        pp.Geometry.Type,
			Coordinates: pp.Geometry.Coordinates,
		}
		// set schemes
		schemes := make([]DeliveryFeeSettingScheme, len(pp.Schemes))
		for index, s := range pp.Schemes {
			scheme, err := s.Scheme()
			if err != nil {
				return &DeliveryFeeSetting{}, nil
			}
			schemes[index] = scheme
		}
		// set price polygon
		pricePolygon.id = pp.ID
		pricePolygon.name = pp.Name
		pricePolygon.schemes = schemes
		pricePolygon.title = pp.Title
		pricePolygon.description = pp.Description
		pricePolygon.zoneColor = pp.ZoneColor
		pricePolygon.createdAt = pp.CreatedAt
		pricePolygon.updatedAt = pp.UpdatedAt
		pricePolygons[index] = pricePolygon
	}
	setting.pricePolygons = pricePolygons

	return &setting, nil
}

type TierMapping struct {
	Type TierType `bson:"type"`
	Head float64  `bson:"head"`
	Tail float64  `bson:"tail"`

	// FIX Type
	Fee types.Money `bson:"fee,omitempty"`

	// Relative Type
	MaxFee types.Money `bson:"max_fee,omitempty"`
	MinFee types.Money `bson:"min_fee,omitempty"`
}

func newTierMapping(tier Tier) (*TierMapping, error) {
	head, tail := tier.Bound()
	switch t := tier.(type) {
	case *FixTier:
		return &TierMapping{
			Type: TierTypeFix,
			Head: head.Float64(),
			Tail: tail.Float64(),
			Fee:  t.fee,
		}, nil
	case *RelativeTier:
		return &TierMapping{
			Type:   TierTypeRelative,
			Head:   head.Float64(),
			Tail:   tail.Float64(),
			MaxFee: t.maxFee,
			MinFee: t.minFee,
		}, nil
	default:
		return nil, errors.Errorf("unknown type of %v", t)
	}
}

func (m *TierMapping) Tier() (Tier, error) {
	switch m.Type {
	case TierTypeFix:
		return &FixTier{
			Range: Range{
				head: types.Distance(m.Head),
				tail: types.Distance(m.Tail),
			},
			fee: m.Fee,
		}, nil
	case TierTypeRelative:
		return &RelativeTier{
			Range: Range{
				head: types.Distance(m.Head),
				tail: types.Distance(m.Tail),
			},
			minFee: m.MinFee,
			maxFee: m.MaxFee,
		}, nil
	default:
		return nil, errors.Errorf("unknown type of %v", m.Type)
	}
}

type RoadFeeTierMapping struct {
	From     float64     `bson:"from"`
	FeePerKM types.Money `bson:"fee_per_km"`
}

func (s DeliveryFeeSetting) MarshalBSON() ([]byte, error) {
	mapping, err := newDeliveryFeeSettingMapping(s)
	if err != nil {
		return nil, err
	}
	return bson.Marshal(mapping)
}

func (s *DeliveryFeeSetting) UnmarshalBSON(data []byte) error {
	mapping := &DeliveryFeeSettingMapping{}
	if err := bson.Unmarshal(data, mapping); err != nil {
		return err
	}

	var (
		setting *DeliveryFeeSetting
		err     error
	)
	if mapping.isMultipleSchemeVersion() {
		setting, err = mapping.DeliveryFeeSettingFromMultipleScheme()
	} else {
		setting, err = mapping.DeliveryFeeSetting()
	}

	if err != nil {
		return nil
	}

	*s = *setting

	return nil
}
