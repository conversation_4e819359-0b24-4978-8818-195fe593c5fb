package model

import "time"

// AssignmentRejection stores order assignment rejection
type AssignmentRejection struct {
	OrderID      string    `bson:"order_id,omitempty"`
	OrderIDs     []string  `bson:"order_ids,omitempty"`
	AssignmentID string    `bson:"assignment_id,omitempty"`
	DriverID     string    `bson:"driver_id"`
	RejectAt     time.Time `bson:"reject_at"`
	Reason       string    `bson:"reason"`
}
