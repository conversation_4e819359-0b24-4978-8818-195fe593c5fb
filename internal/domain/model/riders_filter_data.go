package model

import "sync"

type Rider<PERSON>ilterName string

const (
	// System errors
	EmptyOngoingOrders                      RiderFilterName = "EMPTY_ONGOING_ORDERS"
	QueueingTripNotFound                    RiderFilterName = "QUEUEING_TRIP_NOT_FOUND"
	OngoingOrderNotFound                    RiderFilterName = "ONGOING_ORDER_NOT_FOUND"
	RiderWithQueueingTripsButNotCurrentTrip RiderFilterName = "RIDER_WITH_QUEUEING_TRIPS_BUT_NOT_CURRENT_TRIP"
	TripNotFound                            RiderFilterName = "TRIP_NOT_FOUND"
	OrderNotFound                           RiderFilterName = "ORDER_NOT_FOUND"
	RiderProfileNotFound                    RiderFilterName = "RIDER_PROFILE_NOT_FOUND"
	DistanceNotFound                        RiderFilterName = "DISTANCE_NOT_FOUND"
	DriverTransactionNotFound               RiderFilterName = "DRIVER_TRANSACTION_NOT_FOUND"
	ErrorRouting                            RiderFilterName = "ERROR_ROUTING"

	// Unmatched status
	RiderBanned                                  RiderFilterName = "RIDER_BANNED"
	RiderNotOnlineOrAssigned                     RiderFilterName = "RIDER_NOT_ONLINE_OR_ASSIGNED"
	RiderNotOnline                               RiderFilterName = "RIDER_NOT_ONLINE"
	RiderOnline                                  RiderFilterName = "RIDER_ONLINE"
	RiderStatusUnmatchedWithAssignmentTypeOnline RiderFilterName = "RIDER_STATUS_UNMATCHED_WITH_ASSIGNMENT_TYPE_ONLINE"
	RiderStatusUnmatchedWithAssignmentTypeMO     RiderFilterName = "RIDER_STATUS_UNMATCHED_WITH_ASSIGNMENT_TYPE_MO"
	RiderNotOnlineWhenAssigningMp1               RiderFilterName = "RIDER_NOT_ONLINE_WHEN_ASSIGNING_MP1"
	RiderNotAssignedWhenAssigningMp2             RiderFilterName = "RIDER_NOT_ASSIGNED_WHEN_ASSIGNING_MP2"
	RiderNotOnlineWhenAssigningRush              RiderFilterName = "RIDER_NOT_ONLINE_WHEN_ASSIGNING_RUSH"

	// Unmatched constrints
	MaxLoadExceeded                              RiderFilterName = "MAX_LOAD_EXCEEDED"
	RiderLockedFromQueueing                      RiderFilterName = "RIDER_LOCKED_FROM_QUEUEING"
	CapacityNotEnoughForMP                       RiderFilterName = "CAPACITY_NOT_ENOUGH_FOR_MP"
	TripCapacityViolation                        RiderFilterName = "TRIP_CAPACITY_VIOLATION"
	B2BNotCompatible                             RiderFilterName = "B2B_NOT_COMPATIBLE"
	BikeCrossServiceNotAllowed                   RiderFilterName = "BIKE_CROSS_SERVICE_NOT_ALLOWED"
	MartCrossServiceNotAllowed                   RiderFilterName = "MART_CROSS_SERVICE_NOT_ALLOWED"
	AssigningBikeButNotAfterHeadingToDestination RiderFilterName = "ASSIGNING_BIKE_BUT_NOT_AFTER_HEADING_TO_DESTINATION"
	B2B2BNotAllowed                              RiderFilterName = "B2B2B_NOT_ALLOWED"
	RiderHasNoBikeCapacity                       RiderFilterName = "RIDER_HAS_NO_BIKE_CAPACITY"
	CurrentOrderNotCompatible                    RiderFilterName = "CURRENT_ORDER_NOT_COMPATIBLE"
	ExDriver                                     RiderFilterName = "EX_DRIVERS"
	IllegalDriver                                RiderFilterName = "ILLEGAL_DRIVER"
	RiderNotMocked                               RiderFilterName = "RIDER_NOT_MOCKED"
	CreditWalletNotEnough                        RiderFilterName = "CREDIT_WALLET_NOT_ENOUGH"
	NotEnoughCash                                RiderFilterName = "NOT_ENOUGH_CASH"
	BannedForWithdrawal                          RiderFilterName = "BANNED_FOR_WITHDRAWAL"
	NotInRegion                                  RiderFilterName = "NOT_IN_REGION"
	SamePhoneNumberFromUser                      RiderFilterName = "SAME_PHONE_NUMBER_FROM_USER"
	SameUIDFromUser                              RiderFilterName = "SAME_UID_FROM_USER"
	ServiceTypesUnmatched                        RiderFilterName = "SERVICE_TYPES_UNMATCHED"
	RiderOptedOut                                RiderFilterName = "RIDER_OPTED_OUT"
	RiderSilentBanned                            RiderFilterName = "RIDER_SILENT_BANNED"
	RiderDeprioritized                           RiderFilterName = "RiDER_DEPRIORITIZED"
	RiderLockedForAcknowledgement                RiderFilterName = "RIDER_LOCKED_FOR_ACKNOWLEDGEMENT"
	OfflineLater                                 RiderFilterName = "OFFLINE_LATER"
	NotTester                                    RiderFilterName = "NOT_TESTER"
	WithoutCurrentRider                          RiderFilterName = "WITHOUT_CURRENT_DRIVER"
	WithoutNotifiedRiders                        RiderFilterName = "WITHOUT_NOTIFIED_RIDERS"
	OtherMPRider                                 RiderFilterName = "OTHER_MP_RIDER"
	RiderNotWhitelisted                          RiderFilterName = "RIDER_NOT_WHITELISTED"
	LastAcceptNotWithinDuration                  RiderFilterName = "LAST_ACCEPT_NOT_WITHIN_DURATION"
	DistanceOverMaxRadius                        RiderFilterName = "DISTANCE_OVER_MAX_RADIUS"
	BoxTypeUnmatched                             RiderFilterName = "BOX_TYPE_UNMATCHED"
	BoxTypeExcluded                              RiderFilterName = "BOX_TYPE_EXCLUDED"
	DedicatedZoneUnmatched                       RiderFilterName = "DEDICATED_ZONE_UNMATCHED"
	NotDedicated                                 RiderFilterName = "NOT_DEDICATED"
	NotSupplyPositioned                          RiderFilterName = "NOT_SUPPLY_POSITIONED"
	AlreadyAssigned                              RiderFilterName = "ALREADY_ASSIGNED"
	AlreadyOptimized                             RiderFilterName = "ALREADY_OPTIMIZED"
	DriverIsBeingAssignedAnotherOrder            RiderFilterName = "DRIVER_IS_BEING_ASSIGNED_ANOTHER_ORDER"
	Mp1ATRCannotBeAssigned                       RiderFilterName = "MP1_ATR_CANNOT_BE_ASSIGNED"
	BikeB2BDisabled                              RiderFilterName = "BIKE_B2B_DISABLED"
	IneligibleInSingleOptimize                   RiderFilterName = "INELIGIBLE_IN_SINGLE_OPTIMIZE"
	FirstDistanceLimitExceeded                   RiderFilterName = "FIRST_DISTANCE_LIMIT_EXCEEDED"
	DistanceFromZoneExceeded                     RiderFilterName = "DISTANCE_FROM_ZONE_EXCEEDED"
	B2BDistanceLimitExceeded                     RiderFilterName = "B2B_DISTANCE_LIMIT_EXCEEDED"
	TripWageReduced                              RiderFilterName = "TRIP_WAGE_REDUCED"
	UnableToBackFillTrips                        RiderFilterName = "UNABLE_TO_BACK_FILL_TRIPS"
	PlanRoutesNotAllowed                         RiderFilterName = "PLAN_ROUTES_NOT_ALLOWED"
	RestaurantBlacklisted                        RiderFilterName = "RESTAURANT_BLACKLISTED"
	SameRestaurantsNotAllowed                    RiderFilterName = "SAME_RESTAURANTS_NOT_ALLOWED"
	BundleNotAllowed                             RiderFilterName = "BUNDLE_NOT_ALLOWED"
	ManipulateMultipleTripsNotAllowed            RiderFilterName = "MANIPULATE_MULTIPLE_TRIPS_NOT_ALLOWED"
	IncompatiblePlanRoutes                       RiderFilterName = "INCOMPATIBLE_PLAN_ROUTES"
	LastPredictionDisruptionChanged              RiderFilterName = "LAST_PREDICTION_DISRUPTION_CHANGED"
	RiderIneligibleForAssigningMp2               RiderFilterName = "RIDER_INELIGIBLE_FOR_ASSIGNING_MP2"
	PlanRoutesUnmatchedWithAssignmentTypeMO      RiderFilterName = "PLAN_ROUTES_UNMATCHED_WITH_ASSIGNMENT_TYPE_MO"
	B2BQueuedToRecentlyCreatedTrip               RiderFilterName = "B2B_QUEUED_TO_RECENTLY_CREATED_TRIP"
	NoLegalPathFound                             RiderFilterName = "NO_LEGAL_PATH_FOUND"
)

func (rfn RiderFilterName) WithSuffix(suffixFilterName RiderFilterName) RiderFilterName {
	if suffixFilterName == "" {
		return rfn
	}
	return rfn + "_" + suffixFilterName
}

type RidersFilterData struct {
	data map[RiderFilterName][]string
	lock *sync.RWMutex
}

func (fd *RidersFilterData) Add(name RiderFilterName, driverID string) {
	if name == "" || driverID == "" {
		return
	}

	fd.lock.Lock()
	defer fd.lock.Unlock()

	fd.data[name] = append(fd.data[name], driverID)
}

func (fd *RidersFilterData) AddMany(name RiderFilterName, driverIDs []string) {
	if name == "" || len(driverIDs) == 0 {
		return
	}

	fd.lock.Lock()
	defer fd.lock.Unlock()

	for _, driverID := range driverIDs {
		if driverID == "" {
			continue
		}
		fd.data[name] = append(fd.data[name], driverID)
	}
}

func (fd *RidersFilterData) AddExisting(existingFilterData *RidersFilterData) {
	if existingFilterData == nil {
		return
	}

	fd.lock.Lock()
	defer fd.lock.Unlock()

	for k, v := range existingFilterData.Result() {
		fd.data[k] = append(fd.data[k], v...)
	}
}

func (fd *RidersFilterData) Result() map[RiderFilterName][]string {
	fd.lock.RLock()
	defer fd.lock.RUnlock()

	newFilterData := make(map[RiderFilterName][]string)
	for k, v := range fd.data {
		newFilterData[k] = v
	}
	return newFilterData
}

func NewRiderFilterData() *RidersFilterData {
	return &RidersFilterData{
		data: make(map[RiderFilterName][]string),
		lock: new(sync.RWMutex),
	}
}
