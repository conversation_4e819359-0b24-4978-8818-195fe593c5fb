package model

import (
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/mathutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var (
	ErrGroupTransactionInvalidTransitStatus = errors.New("invalid transit status")
	ErrGroupTransactionAlreadyApprove       = errors.New("cannot approve approved group-transaction")
	ErrGroupTransactionNotYetApproved       = errors.New("not yet approved")
)

type GroupTransactionStatus string

var (
	GroupTransactionStatusPending        GroupTransactionStatus = "PENDING"
	GroupTransactionStatusApproved       GroupTransactionStatus = "APPROVED"
	GroupTransactionStatusSuccess        GroupTransactionStatus = "SUCCESS"
	GroupTransactionStatusPartialSuccess GroupTransactionStatus = "PARTIAL_SUCCESS"
	GroupTransactionStatusFail           GroupTransactionStatus = "FAIL"
	GroupTransactionStatusRejected       GroupTransactionStatus = "REJECTED"
)

type GroupTransactionItemStatus string

var (
	GroupTransactionItemStatusPending  GroupTransactionItemStatus = "PENDING"
	GroupTransactionItemStatusApproved GroupTransactionItemStatus = "APPROVED"
	GroupTransactionItemStatusFail     GroupTransactionItemStatus = "FAIL"
	GroupTransactionItemStatusSuccess  GroupTransactionItemStatus = "SUCCESS"
	GroupTransactionItemStatusRejected GroupTransactionItemStatus = "REJECTED"
)

type GroupTransactionAuditLogStatus string

var (
	GroupTransactionAuditLogStatusRejected GroupTransactionAuditLogStatus = "REJECTED"
	GroupTransactionAuditLogStatusApproved GroupTransactionAuditLogStatus = "APPROVED"
	GroupTransactionAuditLogStatusFail     GroupTransactionAuditLogStatus = "FAIL"
	GroupTransactionAuditLogStatusSuccess  GroupTransactionAuditLogStatus = "SUCCESS"
)

type GroupTransaction struct {
	Entity

	items       []GroupTransactionItem
	status      GroupTransactionStatus
	requestedBy string

	action                   TransactionAction
	auditLogs                []GroupTransactionAuditLog
	groupTransactionCategory string
	effectiveTime            *time.Time
	executedAt               *time.Time
}

func NewGroupTransaction(id string, requestBy string, gt string, action TransactionAction, effectiveTime ...time.Time) *GroupTransaction {
	var newEffectiveTime *time.Time
	if len(effectiveTime) > 0 {
		newEffectiveTime = &effectiveTime[0]
	}

	return &GroupTransaction{
		Entity:                   NewBasicEntity(id),
		items:                    make([]GroupTransactionItem, 0, 50),
		status:                   GroupTransactionStatusPending,
		requestedBy:              requestBy,
		auditLogs:                make([]GroupTransactionAuditLog, 0, 20),
		groupTransactionCategory: gt,
		action:                   action,
		effectiveTime:            newEffectiveTime,
	}
}

func (gt *GroupTransaction) Status() GroupTransactionStatus {
	return gt.status
}

func (gt *GroupTransaction) Items() []GroupTransactionItem {
	return gt.items
}

func (gt *GroupTransaction) RequestedBy() string {
	return gt.requestedBy
}

func (gt *GroupTransaction) AuditLogs() []GroupTransactionAuditLog {
	return gt.auditLogs
}

func (gt *GroupTransaction) GroupTransactionCategory() string {
	return gt.groupTransactionCategory
}

func (gt *GroupTransaction) SetEffectiveTime(effectiveTime *time.Time) {
	gt.effectiveTime = effectiveTime
}

func (gt *GroupTransaction) EffectiveTime() *time.Time {
	return gt.effectiveTime
}

func (gt *GroupTransaction) ExecutedAt() *time.Time {
	return gt.executedAt
}

func (gt *GroupTransaction) SetGroupTransactionCategory(t string) {
	gt.groupTransactionCategory = t
}

func (gt *GroupTransaction) SetGroupTransactionStatus(status GroupTransactionStatus) {
	gt.status = status
}

func (gt *GroupTransaction) SetItemStatus(status GroupTransactionItemStatus) {
	for i, item := range gt.Items() {
		item.status = status
		gt.items[i] = item
	}
}

func (gt *GroupTransaction) SetExecutedAt(time time.Time) {
	gt.executedAt = &time
}

func (gt *GroupTransaction) AddAuditLog(status GroupTransactionAuditLogStatus, requestedBy string) {
	gt.auditLogs = append(gt.auditLogs, NewGroupTransactionAuditLog(string(status), requestedBy))
}

func (gt *GroupTransaction) AddItem(item GroupTransactionItem) {
	gt.items = append(gt.items, item)
}

func (gt *GroupTransaction) UniqueDriverIDs() []string {
	uids := types.NewStringSet()
	for _, i := range gt.Items() {
		uids.Add(i.DriverID())
	}

	return uids.GetElements()
}

func (gt *GroupTransaction) Reject(by string) error {
	if gt.status != GroupTransactionStatusPending {
		return ErrGroupTransactionInvalidTransitStatus
	}

	gt.status = GroupTransactionStatusRejected
	for i, item := range gt.Items() {
		if err := item.setStatus(GroupTransactionItemStatusRejected); err != nil {
			logrus.Warnf("GroupTransaction.Reject: id=%s itemId=%v err=%v", gt.ID(), item, err)
		}
		gt.items[i] = item
	}

	gt.AddAuditLog(GroupTransactionAuditLogStatusRejected, by)

	return nil
}

func (gt *GroupTransaction) Approve(by string) error {
	if gt.Status() != GroupTransactionStatusPending {
		return ErrGroupTransactionAlreadyApprove
	}
	gt.status = GroupTransactionStatusApproved
	gt.AddAuditLog(GroupTransactionAuditLogStatusApproved, by)
	return nil
}

func (gt *GroupTransaction) Process(by string, driverTxns []DriverTransaction, channel TransactionChannel) ([]DriverTransaction, []Transaction, error) {
	if gt.Status() != GroupTransactionStatusApproved {
		return []DriverTransaction{}, []Transaction{}, ErrGroupTransactionNotYetApproved
	}
	driverTxnMap := make(map[string]*DriverTransaction)
	for i, size := 0, len(driverTxns); i < size; i++ {
		dtx := &driverTxns[i]
		driverTxnMap[dtx.DriverID] = &driverTxns[i]
	}

	txns := make([]Transaction, 0, len(gt.Items()))
	hasFail, hasSuccess := false, false
	for i, item := range gt.Items() {
		driverTxn, ok := driverTxnMap[item.DriverID()]
		if !ok {
			item.SetFail("driver is not exists")
			hasFail = true
			gt.items[i] = item
			continue
		}

		infos, err := item.process(driverTxn)
		gt.items[i] = item

		if err != nil {
			hasFail = true
			continue
		}

		for _, info := range infos {
			info.RequestedBy = by
			if item.formID != "" {
				info.FormID = item.formID
			}
			txn := *NewTransaction(
				utils.GenerateUUID(),
				channel,
				gt.action,
				SuccessTransactionStatus,
				info,
			)

			if info.Type == OtherIncentiveTransactionType || info.Type == IncentiveTransactionType || info.Type == NewRiderIncentiveTransactionType || info.Type == RiderReferralIncentiveTransactionType {
				tranSource := TransactionSource{}
				if len(info.IncentiveNames) != 0 {
					tranSource.Name = info.IncentiveNames[0]
				}
				if len(info.IncentiveSources) != 0 {
					tranSource.Source = info.IncentiveSources[0]
				}
				tranSource.Amount = info.Amount
				txn.Sources = []TransactionSource{tranSource}
			}

			if item.remark != "" {
				txn.AddRemark(item.remark, by)
			}

			txns = append(txns, txn)
		}

		hasSuccess = true
	}

	if hasSuccess && !hasFail {
		gt.status = GroupTransactionStatusSuccess
	} else if hasFail && hasSuccess {
		gt.status = GroupTransactionStatusPartialSuccess
	} else {
		gt.status = GroupTransactionStatusFail
	}

	if gt.effectiveTime != nil {
		if !gt.effectiveTime.IsZero() {
			gt.SetExecutedAt(timeutil.BangkokNow())
		}
	}

	gt.AddAuditLog(GroupTransactionAuditLogStatusSuccess, by)

	return driverTxns, txns, nil
}

type GroupTransactionItem struct {
	driverID         string
	category         TransactionCategory
	txnType          TransactionType
	subType          TransactionSubType
	amount           types.Money
	status           GroupTransactionItemStatus
	reason           string
	orderID          string
	tripID           string
	refID            string
	transRefID       crypt.EncryptedString
	taxRefID         string
	incentiveNames   []string
	incentiveSources []string
	remark           string
	formID           string

	transactionScheme *TransactionScheme
}

func newGroupTransactionItem(driverID string, category TransactionCategory, txnType TransactionType, amount types.Money) *GroupTransactionItem {
	groupTransactionItem := &GroupTransactionItem{
		driverID: driverID,
		category: category,
		txnType:  txnType,
		amount:   amount,
		status:   GroupTransactionItemStatusPending,
	}

	return groupTransactionItem
}

func NewGroupTransactionItemFromScheme(ts *TransactionScheme, driverID string, orderID string, amount types.Money) *GroupTransactionItem {
	return &GroupTransactionItem{
		driverID:          driverID,
		orderID:           orderID,
		category:          ts.Category,
		txnType:           ts.Type,
		subType:           ts.SubType,
		amount:            amount,
		status:            GroupTransactionItemStatusPending,
		transactionScheme: ts,
	}
}

func NewIncentiveGroupTransactionItem(driverID string, amount types.Money, orderID string, subType string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		WalletTransactionCategory,
		IncentiveTransactionType,
		amount,
	)

	item.SetSubType(TransactionSubType(subType))
	item.SetOrderID(orderID)

	return item
}

func NewOtherIncentiveGroupTransactionItem(driverID string, amount types.Money, orderID string, subType string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		WalletTransactionCategory,
		OtherIncentiveTransactionType,
		amount,
	)

	item.SetSubType(TransactionSubType(subType))
	item.SetOrderID(orderID)

	return item
}

func NewChargeGroupTransactionItem(driverID string, amount types.Money, orderID string, subType string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		CreditTransactionCategory,
		ChargeTransactionType,
		amount,
	)

	item.SetSubType(TransactionSubType(subType))
	item.SetOrderID(orderID)

	return item
}

func NewPenaltyChargeGroupTransactionItem(txnInfo TransactionInfo, transRefId crypt.EncryptedString, remark string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		txnInfo.DriverID,
		txnInfo.Category,
		txnInfo.Type,
		txnInfo.Amount,
	)

	item.SetSubType(txnInfo.SubType)
	item.SetOrderID(txnInfo.OrderID)
	item.SetTripID(txnInfo.TripID)
	item.SetRefID(txnInfo.RefID)

	item.SetTransRefID(transRefId)
	item.SetRemark(remark)

	return item
}

func NewNewRiderGroupTransactionItem(driverID string, amount types.Money, orderID string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		WalletTransactionCategory,
		NewRiderIncentiveTransactionType,
		amount,
	)

	item.SetOrderID(orderID)

	return item
}

func NewClaimGroupTransactionItem(driverID string, amount types.Money, orderID, subType string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		WalletTransactionCategory,
		ClaimTransactionType,
		amount,
	)

	item.SetSubType(TransactionSubType(subType))
	item.SetOrderID(orderID)

	return item
}

func NewCompensationGroupTransactionItem(driverID string, amount types.Money, orderID string, subType string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		WalletTransactionCategory,
		CompensationTransactionType,
		amount,
	)

	item.SetSubType(TransactionSubType(subType))
	item.SetOrderID(orderID)

	return item
}

func NewSubsidiseGroupTransactionItem(driverID string, amount types.Money, orderID string, subType string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		WalletTransactionCategory,
		SubsidizeTransactionType,
		amount,
	)
	item.SetSubType(TransactionSubType(subType))
	item.SetOrderID(orderID)

	return item
}

func NewCashAdvanceCouponGroupTransactionItem(driverID string, amount types.Money, orderID string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		WalletTransactionCategory,
		CashAdvanceCouponTransactionType,
		amount,
	)

	item.SetOrderID(orderID)

	return item
}

func NewRiderReferralGroupTransactionItem(driverID string, amount types.Money, orderID string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		WalletTransactionCategory,
		RiderReferralIncentiveTransactionType,
		amount,
	)

	item.SetOrderID(orderID)

	return item
}

func NewWithHoldingTaxTransactionItem(driverID string, amount types.Money, orderID string) *GroupTransactionItem {
	item := newGroupTransactionItem(
		driverID,
		CreditTransactionCategory,
		WithholdingTransactionType,
		amount,
	)

	item.SetOrderID(orderID)

	return item
}

func (i *GroupTransactionItem) TaxAmount(tax float64) float64 {
	return mathutil.RoundFloat64(i.amount.Float64() * tax)
}

func (i *GroupTransactionItem) IsRequiredTax() bool {
	if i.HasTransactionScheme() {
		return i.transactionScheme.RequiredTax
	}

	if i.txnType == CashAdvanceCouponTransactionType || i.txnType == ClaimTransactionType {
		return false
	}

	if i.txnType == CompensationTransactionType &&
		(i.subType == CreditNotFoundTransactionSubType || i.subType == ParkingFeeTransactionSubType || i.subType == "") {
		return false
	}

	if i.txnType == ChargeTransactionType &&
		(i.subType == RiderGearSubType || i.subType == PenaltyItemFeeSubType || i.subType == PenaltyUserCouponSubType) {
		return false
	}

	return true
}

func (i *GroupTransactionItem) DriverID() string {
	return i.driverID
}

func (i *GroupTransactionItem) Category() TransactionCategory {
	return i.category
}

func (i *GroupTransactionItem) Type() TransactionType {
	return i.txnType
}

func (i *GroupTransactionItem) SubType() TransactionSubType {
	return i.subType
}

func (i *GroupTransactionItem) Amount() types.Money {
	return i.amount
}

func (i *GroupTransactionItem) Status() GroupTransactionItemStatus {
	return i.status
}

func (i *GroupTransactionItem) Reason() string {
	return i.reason
}

func (i *GroupTransactionItem) OrderID() string {
	return i.orderID
}

func (i *GroupTransactionItem) TripID() string {
	return i.tripID
}

func (i *GroupTransactionItem) RefID() string {
	return i.refID
}

func (i *GroupTransactionItem) TaxRefID() string {
	return i.taxRefID
}

func (i *GroupTransactionItem) TransRefID() crypt.EncryptedString {
	return i.transRefID
}

func (i *GroupTransactionItem) Remark() string {
	return i.remark
}

func (i *GroupTransactionItem) SetOrderID(orderID string) {
	i.orderID = orderID
}

func (i *GroupTransactionItem) SetTripID(tripID string) {
	i.tripID = tripID
}

func (i *GroupTransactionItem) SetRemark(remark string) {
	i.remark = remark
}

func (i *GroupTransactionItem) SetFormID(formID string) {
	i.formID = formID
}

func (i *GroupTransactionItem) SetSubType(subType TransactionSubType) {
	i.subType = subType
}

func (i *GroupTransactionItem) setStatus(new GroupTransactionItemStatus) error {
	switch i.status {
	case GroupTransactionItemStatusPending:
		i.status = new
		return nil
	case GroupTransactionItemStatusFail:
		if new == GroupTransactionItemStatusPending || new == GroupTransactionItemStatusSuccess {
			return ErrGroupTransactionInvalidTransitStatus
		}

		return nil
	case GroupTransactionItemStatusSuccess:
		if new == GroupTransactionItemStatusPending || new == GroupTransactionItemStatusFail {
			return ErrGroupTransactionInvalidTransitStatus
		}

		return nil
	default:
		return ErrGroupTransactionInvalidTransitStatus
	}
}

func (i *GroupTransactionItem) SetFail(reason string) {
	i.status = GroupTransactionItemStatusFail
	i.reason = reason
}

func (i *GroupTransactionItem) SetTaxRefID(id string) {
	i.taxRefID = id
}

func (i *GroupTransactionItem) SetRefID(id string) {
	i.refID = id
}

func (i *GroupTransactionItem) SetTransRefID(transRefID crypt.EncryptedString) {
	i.transRefID = transRefID
}

func (i *GroupTransactionItem) process(driverTxn *DriverTransaction) ([]TransactionInfo, error) {
	if i.Status() != GroupTransactionItemStatusPending {
		return []TransactionInfo{}, ErrGroupTransactionInvalidTransitStatus
	}

	var txn *TransactionInfo
	if i.HasTransactionScheme() {
		txn = driverTxn.NewTransactionFromScheme(*i.transactionScheme, i.orderID, i.Amount())
	} else {
		switch i.txnType {
		case IncentiveTransactionType:
			txn = driverTxn.NewIncentiveTransaction(i.Amount())
			txn.SubType = i.SubType()
		case NewRiderIncentiveTransactionType:
			txn = driverTxn.NewNewRiderIncentiveTransaction(i.Amount())
		case ClaimTransactionType:
			txn = driverTxn.NewClaimTransaction(i.Amount())
			txn.SubType = i.SubType()
		case CompensationTransactionType:
			txn = driverTxn.NewCompensationTransaction(i.Amount())
			txn.SubType = i.SubType()
		case SubsidizeTransactionType:
			txn = driverTxn.NewWalletSubsidizeTransaction(i.orderID, "", i.Amount())
			txn.SubType = i.SubType()
		case CashAdvanceCouponTransactionType:
			txn = driverTxn.NewCashAdvanceCouponTransaction(i.orderID, i.Amount())
		case RiderReferralIncentiveTransactionType:
			txn = driverTxn.NewRiderReferralIncentiveTransaction(i.Amount())
		case OtherIncentiveTransactionType:
			txn = driverTxn.NewOtherIncentiveTransaction(i.Amount())
			txn.SubType = i.SubType()
		case WithholdingTransactionType:
			txn = driverTxn.NewWithHoldingTaxTransaction(i.Amount())
			txn.SubType = i.SubType()
		case ChargeTransactionType:
			txn = driverTxn.NewChargeTransaction(i.Amount(), i.orderID, i.subType)
		case PenaltyChargeTransactionType:
			var err error
			txn, err = driverTxn.NewPenaltyChargeTransaction(i.Amount(), i.OrderID(), i.TripID(), i.SubType())
			if err != nil {
				i.SetFail(err.Error())
				return []TransactionInfo{}, err
			}
		default:
			err := errors.Errorf("unknown type %s", i.txnType)
			i.SetFail(err.Error())
			return []TransactionInfo{}, err
		}
	}

	// LMF-4497 stamp two new fields
	txn.IncentiveSources = i.incentiveSources
	txn.IncentiveNames = i.incentiveNames

	txn.OrderID = i.OrderID()
	txn.TripID = i.TripID()
	txn.TaxRefID = i.TaxRefID()
	txn.RefID = i.RefID()

	transRefId := i.TransRefID()
	if transRefId.String() == "" {
		transRefId = crypt.EncryptedString(utils.GenerateUUID())
	}

	result, err := driverTxn.AddTransactionWithReference([]TransactionInfo{*txn}, transRefId)
	if err != nil {
		i.SetFail(err.Error())
		return []TransactionInfo{}, err
	}

	err = i.setStatus(GroupTransactionItemStatusSuccess)
	if err != nil {
		return []TransactionInfo{}, err
	}

	return result, nil
}

func (i GroupTransactionItem) HasTransactionScheme() bool {
	return i.transactionScheme != nil
}

func (i *GroupTransactionItem) IncentiveName() []string {
	return i.incentiveNames
}

func (i *GroupTransactionItem) SetIncentiveName(name string) {
	i.incentiveNames = append(i.incentiveNames, name)
}

func (i *GroupTransactionItem) IncentiveSources() []string {
	return i.incentiveSources
}

func (i *GroupTransactionItem) SetIncentiveSources(sources []string) {
	i.incentiveSources = sources
}
