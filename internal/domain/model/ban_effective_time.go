package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type BanEffectiveTime struct {
	ID              primitive.ObjectID     `bson:"_id,omitempty" json:"id"`
	BanType         BanType                `bson:"ban_type" json:"banType"`
	BanReasons      []*BanReason           `bson:"ban_reasons" json:"banReason"`
	Detail          BanDriverDetail        `bson:"detail" json:"detail"`
	Status          BanEffectiveTimeStatus `bson:"status"  json:"status"`
	EffectDriverIDs []string               `bson:"driver_ids"  json:"driverIds"`
	EffectiveTime   time.Time              `bson:"effective_time"  json:"effectiveTime"`
	ExecutedAt      *time.Time             `bson:"executed_at"  json:"executedAt"`

	Remarks   []string  `bson:"remarks"  json:"remarks"`
	CreatedAt time.Time `bson:"created_at" json:"createdAt"`
}

type BanEffectiveTimeStatus string

const (
	BanEffectiveWaiting   BanEffectiveTimeStatus = "WAITING"
	BanEffectiveDone      BanEffectiveTimeStatus = "DONE"
	BanEffectiveCancelled BanEffectiveTimeStatus = "CANCELLED"
	BanEffectiveFailed    BanEffectiveTimeStatus = "FAILED"
)
