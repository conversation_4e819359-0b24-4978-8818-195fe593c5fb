package model_test

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestRainSituation_IsRaining(t *testing.T) {
	type testData struct {
		name             string
		rainSituation    model.RainSituation
		rainStatusConfig types.StringSet
		expectedResult   bool
	}

	currentTime := timeutil.BangkokNow()
	testSet := []testData{
		{
			name: "status_matched_with_the_config",
			rainSituation: model.RainSituation{
				RainStatus: model.RainStatusHeavyRain,
			},
			rainStatusConfig: types.NewStringSet(model.RainStatusHeavyRain.String(), model.RainStatusLightRain.String()),
			expectedResult:   true,
		},
		{
			name: "status_matched_with_the_config_rain_with_no_supply",
			rainSituation: model.RainSituation{
				RainStatus: model.RainStatusRainWithNoSupply,
			},
			rainStatusConfig: types.NewStringSet(model.RainStatusRainWithNoSupply.String()),
			expectedResult:   true,
		},
		{
			name: "status_unmatched_with_the_config",
			rainSituation: model.RainSituation{
				RainStatus: model.RainStatusLightRain,
			},
			rainStatusConfig: types.NewStringSet(model.RainStatusHeavyRain.String()),
			expectedResult:   false,
		},
		{
			name: "the_status_overrided_by_admin_but_unmatched_with_the_config",
			rainSituation: model.RainSituation{
				RainStatus:         model.RainStatusHeavyRain,
				OverrideRainStatus: model.RainStatusLightRain,
				OverridePeriod: model.OverridePeriod{
					Start: currentTime,
					End:   currentTime.Add(time.Hour),
				},
			},
			rainStatusConfig: types.NewStringSet(model.RainStatusHeavyRain.String()),
			expectedResult:   false,
		},
		{
			name: "the_status_wont_overrided_by_admin_and_matched_with_config",
			rainSituation: model.RainSituation{
				RainStatus:         model.RainStatusLightRain,
				OverrideRainStatus: model.RainStatusHeavyRain,
				OverridePeriod: model.OverridePeriod{
					Start: currentTime.Add(time.Hour * -2),
					End:   currentTime.Add(time.Hour * -1),
				},
			},
			rainStatusConfig: types.NewStringSet(model.RainStatusHeavyRain.String(), model.RainStatusLightRain.String()),
			expectedResult:   true,
		},
	}

	for _, testItem := range testSet {
		t.Run(testItem.name, func(tt *testing.T) {
			require.Equal(tt, testItem.expectedResult, testItem.rainSituation.IsRaining(testItem.rainStatusConfig))
		})
	}
}
