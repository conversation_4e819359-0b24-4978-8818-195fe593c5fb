package model

import (
	"time"

	"github.com/twpayne/go-geom"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/zoneutill"
)

type Zone struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Name        string             `bson:"name"`
	DisplayName string             `bson:"display_name"`
	ZoneCode    string             `bson:"zone_code"`
	Region      string             `bson:"region"`
	Active      bool               `bson:"active"`
	Geometry    ZoneGeometry       `bson:"geometry"`
	CreatedAt   time.Time          `bson:"created_at"`
	UpdatedAt   time.Time          `bson:"updated_at"`
	DeletedAt   *time.Time         `bson:"deleted_at,omitempty"`
}

type BriefZone struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	DisplayName string             `bson:"display_name"`
	ZoneCode    string             `bson:"zone_code"`
}

func NewBriefZone(name string, displayName string, region string) BriefZone {
	return BriefZone{
		ID:          primitive.NewObjectID(),
		DisplayName: displayName,
		ZoneCode:    zoneutill.GenerateZoneCode(region, name),
	}
}

func ZoneToBriefZone(zone Zone) BriefZone {
	return BriefZone{
		ID:          zone.ID,
		DisplayName: zone.DisplayName,
		ZoneCode:    zone.ZoneCode,
	}
}

type ZoneWithoutGeometry struct {
	ID          primitive.ObjectID `bson:"_id,omitempty" json:"-"`
	Name        string             `bson:"name"`
	DisplayName string             `bson:"display_name"`
	ZoneCode    string             `bson:"zone_code"`
	Region      string             `bson:"region"`
	Active      bool               `bson:"active"`
	CreatedAt   time.Time          `bson:"created_at"`
	UpdatedAt   time.Time          `bson:"updated_at"`
	DeletedAt   *time.Time         `bson:"deleted_at,omitempty"`
}

func NewZone(name string, displayName string, region string, geometry ZoneGeometry) Zone {
	t := time.Now()
	z := Zone{
		ID:          primitive.NewObjectID(),
		Name:        name,
		DisplayName: displayName,
		ZoneCode:    zoneutill.GenerateZoneCode(region, name),
		Active:      true,
		Region:      region,
		Geometry:    geometry,
		CreatedAt:   t,
		UpdatedAt:   t,
	}
	return z
}

type ZoneGeometry struct {
	Type        string          `bson:"type"`
	Coordinates ZoneCoordinates `bson:"coordinates"`
}

func (geo ZoneGeometry) IsZero() bool {
	return geo.Type == "" && len(geo.Coordinates) == 0
}

func NewZoneGeometry(coordinates [][][]geom.Coord) ZoneGeometry {
	return ZoneGeometry{
		Type:        "MultiPolygon",
		Coordinates: coordinates,
	}
}

func (geo ZoneGeometry) ToOnTopCoordinates() [][][][]float64 {
	input := geo.Coordinates
	output := make([][][][]float64, len(input))
	for i, a := range input {
		output[i] = make([][][]float64, len(a))
		for j, b := range a {
			output[i][j] = make([][]float64, len(b))
			for k, c := range b {
				output[i][j][k] = make([]float64, len(c))
				for l, d := range c {
					output[i][j][k][l] = d
				}
			}
		}
	}
	return output
}

type ZoneCoordinates [][][]geom.Coord

type UpdateZoneReq struct {
	DisplayName string
	Geometry    ZoneGeometry
	Active      bool
}

func (r UpdateZoneReq) BSON() bson.M {
	fields := bson.M{}

	set := bson.M{
		"display_name": r.DisplayName,
		"geometry":     r.Geometry,
		"active":       r.Active,
		"updated_at":   time.Now(),
	}
	unset := bson.M{}

	if !r.Active {
		set["deleted_at"] = time.Now()
	} else {
		unset = bson.M{
			"deleted_at": "",
		}
	}

	fields["$set"] = set
	fields["$unset"] = unset

	return fields
}

type ZoneQueryReq struct {
	Region    *string
	Active    *bool
	ZoneCodes []string
}

type BriefZoneQueryReq struct {
	Region *string
	Active *bool
}

func (r ZoneQueryReq) Query() bson.M {
	query := bson.M{}
	if r.Region != nil {
		query["region"] = r.Region
	}
	if r.Active != nil {
		query["active"] = r.Active
	}
	if r.ZoneCodes != nil {
		query["zone_code"] = bson.M{"$in": r.ZoneCodes}
	}
	return query
}
