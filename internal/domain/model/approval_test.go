package model

import (
	"testing"

	"github.com/stretchr/testify/require"
)

var approval = func(id string, status ApprovalStatus) Approval {
	approval := *NewApproval(id, "admin", NewCreditPurchaseInfo("driver-1", "ref-1", 200))

	switch status {
	case ApprovedApproval:
		approval.Approve()
	case RejectedApproval:
		approval.Reject()
	}

	return approval
}

func TestApproval_Approve(t *testing.T) {
	testcases := []struct {
		actual   Approval
		exStatus ApprovalStatus
		exError  error
	}{
		{approval("a-1", PendingApproval), ApprovedApproval, nil},
		{approval("a-2", ApprovedApproval), ApprovedApproval, ErrInvalidTransitApprovalStatus},
		{approval("a-3", RejectedApproval), RejectedApproval, ErrInvalidTransitApprovalStatus},
	}

	for _, tc := range testcases {
		err := tc.actual.Approve()

		require.Equal(t, tc.exStatus, tc.actual.Status)
		require.Equal(t, tc.exError, err)
	}
}

func TestApproval_Reject(t *testing.T) {
	testcases := []struct {
		actual   Approval
		exStatus ApprovalStatus
		exError  error
	}{
		{approval("a-1", PendingApproval), RejectedApproval, nil},
		{approval("a-2", ApprovedApproval), ApprovedApproval, ErrInvalidTransitApprovalStatus},
		{approval("a-3", RejectedApproval), RejectedApproval, ErrInvalidTransitApprovalStatus},
	}

	for _, tc := range testcases {
		err := tc.actual.Reject()

		require.Equal(t, tc.exStatus, tc.actual.Status)
		require.Equal(t, tc.exError, err)
	}
}
