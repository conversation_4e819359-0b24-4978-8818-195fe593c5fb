package model

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

const UobRefAuditObject ObjectType = "uob_ref"
const UobRefAssignAuditAction AuditAction = "assign"
const UobRefUnAssignAuditAction AuditAction = "unassign"

type UobRef struct {
	UobRefID  string    `json:"uobRefID" bson:"uob_ref_id"`
	DriverID  string    `json:"driverID" bson:"driver_id"`
	IsUsed    bool      `json:"is_used" bson:"is_used"`
	CreatedAt time.Time `bson:"created_at"`
	UpdatedAt time.Time `bson:"updated_at"`
}

func (u *UobRef) SetDriverID(driverID string) {
	u.DriverID = driverID
	u.IsUsed = true
	u.UpdatedAt = timeutil.BangkokNow()
}
