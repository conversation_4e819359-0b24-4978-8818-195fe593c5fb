package model

import "go.mongodb.org/mongo-driver/bson/primitive"

type BanMetadata struct {
	ID              primitive.ObjectID `bson:"_id,omitempty"`
	Category        string             `bson:"category"`
	SubCategory     string             `bson:"sub_category"`
	MessageToDriver string             `bson:"message_to_driver"`
	Financial       bool               `bson:"financial"`
	Roles           []string           `bson:"roles"`
}

func (b *BanMetadata) IsDuplicated(m *BanMetadata) bool {
	// if it is the same object, it's not duplicated
	if !b.ID.IsZero() && !m.ID.IsZero() && b.ID.Hex() == m.ID.Hex() {
		return false
	}

	return b.Category == m.Category && b.SubCategory == m.SubCategory
}
