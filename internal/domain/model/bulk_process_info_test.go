package model

import (
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestBulkProcessInfo(t *testing.T) {
	info := BulkProcessInfo{
		ID:          "info-id",
		Status:      "PENDING",
		ProcessedAt: time.Date(2024, 2, 5, 0, 0, 0, 0, timeutil.BangkokLocation()),
		CreatedAt:   time.Date(2024, 2, 1, 10, 30, 0, 0, timeutil.BangkokLocation()),
		UpdatedAt:   time.Date(2024, 2, 1, 10, 30, 0, 0, timeutil.BangkokLocation()),
		RequestedBy: "<EMAIL>",
	}

	payload := DriverServiceTypePayload{
		"LMDAIEIQE": DriverServiceInfo{
			Status:   BulkProcessInfoPayloadStatusPending,
			Services: Services{ServiceBike},
		},
	}

	// Test SetBulkDriverServiceType
	info.SetBulkDriverServiceType(payload)
	require.Equal(t, BulkProcessDriverServiceType, info.Type)
	require.Equal(t, payload, info.Payload)

	// Test UnmarshalBSON
	bs, err := bson.Marshal(info)
	require.NoError(t, err)

	var actual BulkProcessInfo
	err = bson.Unmarshal(bs, &actual)
	require.NoError(t, err)

	require.Equal(t, info.ID, actual.ID)
	require.Equal(t, info.Type, actual.Type)
	require.Equal(t, info.Status, actual.Status)
	require.Equal(t, info.Payload, actual.Payload)
	require.True(t, info.ProcessedAt.Equal(actual.ProcessedAt))
	require.True(t, info.CreatedAt.Equal(actual.CreatedAt))
	require.True(t, info.UpdatedAt.Equal(actual.UpdatedAt))
	require.Equal(t, info.RequestedBy, actual.RequestedBy)
}
