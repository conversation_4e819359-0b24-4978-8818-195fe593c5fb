package model

import (
	"errors"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
)

type RewardTransactionChannel string

const (
	AdminRewardTransactionChannel  RewardTransactionChannel = "ADMIN"
	SystemRewardTransactionChannel RewardTransactionChannel = "SYSTEM"
	UserRewardTransactionChannel   RewardTransactionChannel = "USER"
)

type RewardTransactionAction string

const (
	AddRewardTransactionAction    RewardTransactionAction = "ADD"
	DeductRewardTransactionAction RewardTransactionAction = "DEDUCT"
)

type RewardType string

const (
	CoinRewardType RewardType = "COIN"
)

type RewardTransactionType string

const (
	ConvertToCashRewardTransactionType RewardTransactionType = "CONVERT_TO_CASH"
	CompensateRewardTransactionType    RewardTransactionType = "COMPENSATE"
	AdjustRewardTransactionType        RewardTransactionType = "ADJUST"
	ChargeRewardTransactionType        RewardTransactionType = "CHARGE"
	ExchangeRewardTransactionType      RewardTransactionType = "EXCHANGE"
	OtherRewardTransactionType         RewardTransactionType = "OTHER"
	NotEnoughCoinTransactionType       RewardTransactionType = "NOT_ENOUGH_COIN"
	OnTopRewardTransactionType         RewardTransactionType = "ONTOP"
)

var (
	ErrRewardTransactionActionInvalid = errors.New("reward transaction action is invalid")
	ErrRewardTransactionTypeInvalid   = errors.New("reward transaction type is invalid")
)

type RewardTransactionSource struct {
	Source string      `bson:"source,omitempty"`
	Name   string      `bson:"name,omitempty"`
	Amount types.Money `bson:"amount"`
}

type RewardTransaction struct {
	TransactionID       string                     `bson:"transaction_id"`
	Channel             RewardTransactionChannel   `bson:"channel"`
	Action              RewardTransactionAction    `bson:"action"`
	Category            RewardType                 `bson:"category"`
	Type                RewardTransactionType      `bson:"type"`
	DriverID            string                     `bson:"driver_id"`
	BeforeRewardBalance int                        `bson:"before_reward_balance"`
	Amount              int                        `bson:"amount"`
	RequestedBy         string                     `bson:"requested_by,omitempty"`
	RequestedTime       time.Time                  `bson:"requested_time,omitempty"`
	DisplayText         string                     `bson:"display_text"`
	Info                RewardTransactionInfo      `bson:"info,omitempty"`
	GroupID             string                     `bson:"group_id"`
	Remark              string                     `bson:"remark"`
	VoidRefID           string                     `bson:"void_ref_id"`
	Sources             []RewardTransactionSource  `bson:"sources" json:"sources"`
	Metadata            *RewardTransactionMetadata `bson:"metadata,omitempty"`
	CreatedAt           time.Time                  `bson:"created_at"`
	UpdatedAt           time.Time                  `bson:"updated_at"`
}

type RewardTransactionInfo struct {
	OrderIDs    []string `bson:"order_ids,omitempty"`
	TripID      string   `bson:"trip_id,omitempty"`
	ServiceType Service  `bson:"service_type,omitempty"`
}

type RewardTransactionMetadata struct {
	AR    float64        `bson:"ar,omitempty"`
	CR    float64        `bson:"cr,omitempty"`
	Baht  int            `bson:"baht,omitempty"`
	Tiers []CoinCashTier `bson:"tiers,omitempty"`
}

func (rti RewardTransactionInfo) IsZero() bool {
	if len(rti.OrderIDs) > 0 {
		return false
	}
	if rti.TripID != "" {
		return false
	}
	if rti.ServiceType != "" {
		return false
	}
	return true
}

func (a RewardTransactionAction) IsValid() error {
	switch a {
	case AddRewardTransactionAction, DeductRewardTransactionAction:
		return nil
	}
	return ErrRewardTransactionActionInvalid
}

func (t RewardTransactionType) IsValid() error {
	switch t {
	case CompensateRewardTransactionType, AdjustRewardTransactionType, ChargeRewardTransactionType, ConvertToCashRewardTransactionType, ExchangeRewardTransactionType, OtherRewardTransactionType:
		return nil
	}
	return ErrRewardTransactionTypeInvalid
}
