package model

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestDriverOrderInfoList_UpdateLastOrder(t *testing.T) {
	captureInterval := time.Duration(-60) * time.Minute
	distanceLimit := 2000.0

	t.Run("should add and count last 1 hour's orders correctly", func(tt *testing.T) {
		doi := DriverOrderInfo{}

		doiDetailA1 := createDriverOrderInfoDetail("A1", 2000, time.Now())
		doi.AddDriverOrderInfoDetail(doiDetailA1, captureInterval, distanceLimit)
		require.Equal(tt, "A1", doi.LastOrderID)
		require.Equal(tt, 0, doi.LastTooFarCompleteOrder)
		require.Equal(tt, "[]", fmt.Sprintf("%v", doi.LastTooFarCompleteOrders.ListCompleteOrderID()))
		require.Equal(tt, 1, doi.LastHourCompletedOrder)

		doiDetailA2 := createDriverOrderInfoDetail("A2", 3000, time.Now().Add(-70*time.Minute))
		doi.AddDriverOrderInfoDetail(doiDetailA2, captureInterval, distanceLimit)
		require.Equal(tt, "A2", doi.LastOrderID)
		require.Equal(tt, 1, doi.LastTooFarCompleteOrder)
		require.Equal(tt, "[A2]", fmt.Sprintf("%v", doi.LastTooFarCompleteOrders.ListCompleteOrderID()))
		require.Equal(tt, 1, doi.LastHourCompletedOrder)

		doiDetailA3 := createDriverOrderInfoDetail("A3", 3000, time.Now())
		doi.AddDriverOrderInfoDetail(doiDetailA3, captureInterval, distanceLimit)
		require.Equal(tt, "A3", doi.LastOrderID)
		require.Equal(tt, 2, doi.LastTooFarCompleteOrder)
		require.Equal(tt, "[A2 A3]", fmt.Sprintf("%v", doi.LastTooFarCompleteOrders.ListCompleteOrderID()))
		require.Equal(tt, 2, doi.LastHourCompletedOrder)

		doiDetailA4 := createDriverOrderInfoDetail("A4", 2000, time.Now())
		doi.AddDriverOrderInfoDetail(doiDetailA4, captureInterval, distanceLimit)
		require.Equal(tt, "A4", doi.LastOrderID)
		require.Equal(tt, 0, doi.LastTooFarCompleteOrder)
		require.Equal(tt, "[]", fmt.Sprintf("%v", doi.LastTooFarCompleteOrders.ListCompleteOrderID()))
		require.Equal(tt, 3, doi.LastHourCompletedOrder)
	})

	t.Run("should not count duplicated order id", func(tt *testing.T) {
		doi := DriverOrderInfo{}

		doiDetailA1 := createDriverOrderInfoDetail("A1", 2000, time.Now())
		doi.AddDriverOrderInfoDetail(doiDetailA1, captureInterval, distanceLimit)
		require.Equal(tt, 0, doi.LastTooFarCompleteOrder)
		require.Equal(tt, 1, doi.LastHourCompletedOrder)

		doiDetailA1 = createDriverOrderInfoDetail("A1", 3000, time.Now().Add(-70*time.Minute))
		doi.AddDriverOrderInfoDetail(doiDetailA1, captureInterval, distanceLimit)
		require.Equal(tt, 1, doi.LastTooFarCompleteOrder)
		require.Equal(tt, 1, doi.LastHourCompletedOrder)

		doiDetailA1 = createDriverOrderInfoDetail("A1", 3000, time.Now())
		doi.AddDriverOrderInfoDetail(doiDetailA1, captureInterval, distanceLimit)
		require.Equal(tt, 1, doi.LastTooFarCompleteOrder)
		require.Equal(tt, 1, doi.LastHourCompletedOrder)

		doiDetailA1 = createDriverOrderInfoDetail("A1", 2000, time.Now())
		doi.AddDriverOrderInfoDetail(doiDetailA1, captureInterval, distanceLimit)
		require.Equal(tt, 0, doi.LastTooFarCompleteOrder)
		require.Equal(tt, 1, doi.LastHourCompletedOrder)
	})
}

func TestDriverOrderInfo_PerformanceAR(t *testing.T) {
	tests := []struct {
		name                 string
		dailyCounts          []DailyCount
		expectedAR           float64
		expectedARByServices map[Service]float64
		startDate            time.Time
		endDate              time.Time
	}{
		{
			name:        "no daily counts",
			dailyCounts: []DailyCount{},
			expectedAR:  0,
			startDate:   createStartDate(2021, 10, 1),
			endDate:     createEndDate(2021, 12, 31),
		},
		{
			name: "should calculate AR correctly for a day",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 100, AutoAssignedAccepted: 46},
			},
			expectedAR: 46,
			startDate:  createStartDate(2021, 11, 1),
			endDate:    createEndDate(2021, 11, 1),
		},
		{
			name: "should calculate AR with rain correctly for a day",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 100, AutoAssignedAccepted: 46, AutoAssignedRain: 10, AutoAssignedAcceptedRain: 6},
			},
			expectedAR: 47.9, // [ 46 / ( 100 - 4 ) ] = 47.9
			startDate:  createStartDate(2021, 11, 1),
			endDate:    createEndDate(2021, 11, 1),
		},
		{
			name: "should calculate AR correctly for many days",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 30, AutoAssignedAccepted: 22},
				{Year: 2021, Month: 11, Day: 2, AutoAssigned: 24, AutoAssignedAccepted: 11},
				{Year: 2021, Month: 11, Day: 3, AutoAssigned: 19, AutoAssignedAccepted: 9},
			},
			expectedAR: 57.5,
			startDate:  createStartDate(2021, 11, 1),
			endDate:    createEndDate(2021, 11, 3),
		},
		{
			name: "should calculate AR with rain correctly for many days ",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 55, AutoAssignedAccepted: 31, AutoAssignedRain: 15, AutoAssignedAcceptedRain: 6},
				{Year: 2021, Month: 11, Day: 2, AutoAssigned: 30, AutoAssignedAccepted: 12, AutoAssignedRain: 10, AutoAssignedAcceptedRain: 6},
				{Year: 2021, Month: 11, Day: 3, AutoAssigned: 15, AutoAssignedAccepted: 10, AutoAssignedRain: 5, AutoAssignedAcceptedRain: 3},
			},
			expectedAR: 62.4, // [ ( 31 + 12 + 10 ) / ( 100 - ( 9 + 4 + 2 ) ) ] = 80
			startDate:  createStartDate(2021, 11, 1),
			endDate:    createEndDate(2021, 11, 3),
		},
		{
			name: "should calculate AR correctly and ignore counts that are out of range",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 10, Day: 31, AutoAssigned: 26, AutoAssignedAccepted: 5},
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 30, AutoAssignedAccepted: 22},
				{Year: 2021, Month: 11, Day: 2, AutoAssigned: 24, AutoAssignedAccepted: 11},
				{Year: 2021, Month: 11, Day: 3, AutoAssigned: 19, AutoAssignedAccepted: 9},
				{Year: 2021, Month: 12, Day: 1, AutoAssigned: 49, AutoAssignedAccepted: 24},
			},
			expectedAR: 57.5,
			startDate:  createStartDate(2021, 11, 1),
			endDate:    createEndDate(2021, 11, 3),
		},
		{
			name: "should calculate AR by services correctly",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 30, AutoAssignedAccepted: 22, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssigned: 20, AutoAssignedAccepted: 14},
					ServiceMart:      {AutoAssigned: 8, AutoAssignedAccepted: 6},
					ServiceMessenger: {AutoAssigned: 2, AutoAssignedAccepted: 2},
				}},
				{Year: 2021, Month: 11, Day: 2, AutoAssigned: 24, AutoAssignedAccepted: 11, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {AutoAssigned: 20, AutoAssignedAccepted: 10},
					ServiceMart: {AutoAssigned: 4, AutoAssignedAccepted: 1},
				}},
				{Year: 2021, Month: 11, Day: 3, AutoAssigned: 19, AutoAssignedAccepted: 9, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssigned: 10, AutoAssignedAccepted: 5},
					ServiceMart:      {AutoAssigned: 5, AutoAssignedAccepted: 2},
					ServiceMessenger: {AutoAssigned: 4, AutoAssignedAccepted: 2},
				}},
			},
			expectedAR: 57.5,
			expectedARByServices: map[Service]float64{
				ServiceFood:      58.0,
				ServiceMart:      52.9,
				ServiceMessenger: 66.7,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "should calculate AR with rain by services correctly",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 30, AutoAssignedAccepted: 22, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssigned: 20, AutoAssignedAccepted: 14, AutoAssignedRain: 3, AutoAssignedAcceptedRain: 1},
					ServiceMart:      {AutoAssigned: 8, AutoAssignedAccepted: 6},
					ServiceMessenger: {AutoAssigned: 2, AutoAssignedAccepted: 2},
				}},
				{Year: 2021, Month: 11, Day: 2, AutoAssigned: 24, AutoAssignedAccepted: 11, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {AutoAssigned: 20, AutoAssignedAccepted: 10, AutoAssignedRain: 5, AutoAssignedAcceptedRain: 3},
					ServiceMart: {AutoAssigned: 4, AutoAssignedAccepted: 1},
				}},
				{Year: 2021, Month: 11, Day: 3, AutoAssigned: 19, AutoAssignedAccepted: 9, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssigned: 10, AutoAssignedAccepted: 5},
					ServiceMart:      {AutoAssigned: 5, AutoAssignedAccepted: 2},
					ServiceMessenger: {AutoAssigned: 4, AutoAssignedAccepted: 2},
				}},
			},
			expectedAR: 57.5,
			expectedARByServices: map[Service]float64{
				ServiceFood:      63.0, // [ ( 14 + 10 + 5 ) / ( 20 + 20 + 10 ) - ( 2 + 2 ) ] = 63.04
				ServiceMart:      52.9,
				ServiceMessenger: 66.7,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "should not calculate AR by services if some day data is missing",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 30, AutoAssignedAccepted: 22, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssigned: 20, AutoAssignedAccepted: 14},
					ServiceMart:      {AutoAssigned: 8, AutoAssignedAccepted: 6},
					ServiceMessenger: {AutoAssigned: 2, AutoAssignedAccepted: 2},
				}},
				{Year: 2021, Month: 11, Day: 2, AutoAssigned: 24, AutoAssignedAccepted: 11},
				{Year: 2021, Month: 11, Day: 3, AutoAssigned: 19, AutoAssignedAccepted: 9, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssigned: 10, AutoAssignedAccepted: 5},
					ServiceMart:      {AutoAssigned: 5, AutoAssignedAccepted: 2},
					ServiceMessenger: {AutoAssigned: 4, AutoAssignedAccepted: 2},
				}},
			},
			expectedAR:           57.5,
			expectedARByServices: nil,
			startDate:            createStartDate(2021, 11, 1),
			endDate:              createEndDate(2021, 11, 3),
		},
		{
			name: "should not calculate AR by services if every day assigned is zero",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssigned: 30, AutoAssignedAccepted: 22, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssigned: 0, AutoAssignedAccepted: 14},
					ServiceMart:      {AutoAssigned: 8, AutoAssignedAccepted: 6},
					ServiceMessenger: {AutoAssigned: 2, AutoAssignedAccepted: 2},
				}},
				{Year: 2021, Month: 11, Day: 2, AutoAssigned: 24, AutoAssignedAccepted: 11, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {AutoAssigned: 0, AutoAssignedAccepted: 10},
					ServiceMart: {AutoAssigned: 4, AutoAssignedAccepted: 1},
				}},
				{Year: 2021, Month: 11, Day: 3, AutoAssigned: 19, AutoAssignedAccepted: 9, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssigned: 0, AutoAssignedAccepted: 5},
					ServiceMart:      {AutoAssigned: 5, AutoAssignedAccepted: 2},
					ServiceMessenger: {AutoAssigned: 4, AutoAssignedAccepted: 2},
				}},
			},
			expectedAR: 57.5,
			expectedARByServices: map[Service]float64{
				ServiceFood:      0,
				ServiceMart:      52.9,
				ServiceMessenger: 66.7,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(tt *testing.T) {
			driverOrderInfo := DriverOrderInfo{DailyCounts: test.dailyCounts}
			_, ar, arByServices := driverOrderInfo.GetAR(test.startDate, test.endDate)
			require.Equal(tt, test.expectedAR, ar)
			if test.expectedARByServices != nil {
				require.Len(tt, arByServices, len(test.expectedARByServices))
				for service, serviceAR := range test.expectedARByServices {
					require.Equal(tt, serviceAR, arByServices[service])
				}
			}
		})
	}
}

func TestDriverOrderInfo_PerformanceAR_WithRainFeatureAndFlagTurnOn(t *testing.T) {
	tests := []struct {
		name                 string
		dailyCounts          []DailyCount
		expectedAR           float64
		expectedARByServices map[Service]float64
		startDate            time.Time
		endDate              time.Time
	}{
		// NEW driver never accept order before
		{
			name: "new driver: accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             1,
					AutoAssignedAccepted:     1,
					AutoAssignedRain:         1,
					AutoAssignedAcceptedRain: 1,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 1, AutoAssignedAccepted: 1, AutoAssignedRain: 1, AutoAssignedAcceptedRain: 1},
					}},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "new driver: accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             1,
					AutoAssignedAccepted:     1,
					AutoAssignedRain:         0,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 1, AutoAssignedAccepted: 1, AutoAssignedRain: 0, AutoAssignedAcceptedRain: 0},
					}},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "new driver: not accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             1,
					AutoAssignedAccepted:     0,
					AutoAssignedRain:         1,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 1, AutoAssignedAccepted: 0, AutoAssignedRain: 1, AutoAssignedAcceptedRain: 0},
					}},
			},
			expectedAR: 0,
			expectedARByServices: map[Service]float64{
				ServiceFood: 0,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "new driver: not accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             1,
					AutoAssignedAccepted:     0,
					AutoAssignedRain:         0,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 1, AutoAssignedAccepted: 0, AutoAssignedRain: 0, AutoAssignedAcceptedRain: 0},
					}},
			},
			expectedAR: 0,
			expectedARByServices: map[Service]float64{
				ServiceFood: 0,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},

		// Driver NEVER not accept order (never accept while raining)
		{
			name: "never not accept order (never accept while raining): accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             11,
					AutoAssignedAccepted:     11,
					AutoAssignedRain:         1,
					AutoAssignedAcceptedRain: 1,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 11, AutoAssignedAccepted: 11, AutoAssignedRain: 1, AutoAssignedAcceptedRain: 1},
					}},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "never not accept order (never accept while raining): accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             11,
					AutoAssignedAccepted:     11,
					AutoAssignedRain:         0,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 11, AutoAssignedAccepted: 11, AutoAssignedRain: 0, AutoAssignedAcceptedRain: 0},
					}},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "never not accept order (never accept while raining): not accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             11,
					AutoAssignedAccepted:     10,
					AutoAssignedRain:         1,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 11, AutoAssignedAccepted: 10, AutoAssignedRain: 1, AutoAssignedAcceptedRain: 0},
					},
				},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "never not accept order (never accept while raining): not accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             11,
					AutoAssignedAccepted:     10,
					AutoAssignedRain:         0,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 11, AutoAssignedAccepted: 10, AutoAssignedRain: 0, AutoAssignedAcceptedRain: 0},
					},
				},
			},
			expectedAR: 90.9,
			expectedARByServices: map[Service]float64{
				ServiceFood: 90.9,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},

		// Driver NEVER not accept order (has accepted while raining)
		{
			name: "never not accept order (has accepted while raining): accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             16,
					AutoAssignedAccepted:     16,
					AutoAssignedRain:         4,
					AutoAssignedAcceptedRain: 4,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 16, AutoAssignedAccepted: 16, AutoAssignedRain: 4, AutoAssignedAcceptedRain: 4},
					}},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "never not accept order (has accepted while raining): accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             16,
					AutoAssignedAccepted:     16,
					AutoAssignedRain:         3,
					AutoAssignedAcceptedRain: 3,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 16, AutoAssignedAccepted: 16, AutoAssignedRain: 3, AutoAssignedAcceptedRain: 3},
					}},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "never not accept order (has accepted while raining): not accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             16,
					AutoAssignedAccepted:     15,
					AutoAssignedRain:         4,
					AutoAssignedAcceptedRain: 3,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 16, AutoAssignedAccepted: 15, AutoAssignedRain: 4, AutoAssignedAcceptedRain: 3},
					},
				},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "never not accept order (has accepted while raining): not accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             16,
					AutoAssignedAccepted:     15,
					AutoAssignedRain:         3,
					AutoAssignedAcceptedRain: 3,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 16, AutoAssignedAccepted: 15, AutoAssignedRain: 3, AutoAssignedAcceptedRain: 3},
					},
				},
			},
			expectedAR: 93.8,
			expectedARByServices: map[Service]float64{
				ServiceFood: 93.8,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},

		// Driver not accept order while NOT rainning
		{
			name: "not accept order while not rainning: accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             4,
					AutoAssignedAccepted:     3,
					AutoAssignedRain:         2,
					AutoAssignedAcceptedRain: 2,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 4, AutoAssignedAccepted: 3, AutoAssignedRain: 2, AutoAssignedAcceptedRain: 2},
					}},
			},
			expectedAR: 75,
			expectedARByServices: map[Service]float64{
				ServiceFood: 75,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "not accept order while not rainning: accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             4,
					AutoAssignedAccepted:     3,
					AutoAssignedRain:         1,
					AutoAssignedAcceptedRain: 1,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 4, AutoAssignedAccepted: 3, AutoAssignedRain: 1, AutoAssignedAcceptedRain: 1},
					}},
			},
			expectedAR: 75,
			expectedARByServices: map[Service]float64{
				ServiceFood: 75,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "not accept order while not rainning: not accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             4,
					AutoAssignedAccepted:     2,
					AutoAssignedRain:         2,
					AutoAssignedAcceptedRain: 1,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 4, AutoAssignedAccepted: 2, AutoAssignedRain: 2, AutoAssignedAcceptedRain: 1},
					},
				},
			},
			expectedAR: 66.7,
			expectedARByServices: map[Service]float64{
				ServiceFood: 66.7,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "not accept order while not rainning: not accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             4,
					AutoAssignedAccepted:     2,
					AutoAssignedRain:         1,
					AutoAssignedAcceptedRain: 1,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {AutoAssigned: 4, AutoAssignedAccepted: 2, AutoAssignedRain: 1, AutoAssignedAcceptedRain: 1},
					},
				},
			},
			expectedAR: 50,
			expectedARByServices: map[Service]float64{
				ServiceFood: 50,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},

		// Driver not accept MANY order while NOT rainning
		{
			name: "not accept many order while not rainning: accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             46,
					AutoAssignedAccepted:     31,
					AutoAssignedRain:         1,
					AutoAssignedAcceptedRain: 1,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             46,
							AutoAssignedAccepted:     31,
							AutoAssignedRain:         1,
							AutoAssignedAcceptedRain: 1,
						},
					},
				},
			},
			expectedAR: 67.4,
			expectedARByServices: map[Service]float64{
				ServiceFood: 67.4,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "not accept many order while not rainning: accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             46,
					AutoAssignedAccepted:     31,
					AutoAssignedRain:         0,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             46,
							AutoAssignedAccepted:     31,
							AutoAssignedRain:         0,
							AutoAssignedAcceptedRain: 0,
						},
					},
				},
			},
			expectedAR: 67.4,
			expectedARByServices: map[Service]float64{
				ServiceFood: 67.4,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "not accept many order while not rainning: not accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             46,
					AutoAssignedAccepted:     30,
					AutoAssignedRain:         1,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             46,
							AutoAssignedAccepted:     30,
							AutoAssignedRain:         1,
							AutoAssignedAcceptedRain: 0,
						},
					},
				},
			},
			expectedAR: 66.7,
			expectedARByServices: map[Service]float64{
				ServiceFood: 66.7,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "not accept many order while not rainning: not accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             46,
					AutoAssignedAccepted:     30,
					AutoAssignedRain:         0,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             46,
							AutoAssignedAccepted:     30,
							AutoAssignedRain:         0,
							AutoAssignedAcceptedRain: 0,
						},
					},
				},
			},
			expectedAR: 65.2,
			expectedARByServices: map[Service]float64{
				ServiceFood: 65.2,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},

		// Driver hasn't accept order ONLY while rainning
		{
			name: "hasn't accept order only while rainning: accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             21,
					AutoAssignedAccepted:     18,
					AutoAssignedRain:         4,
					AutoAssignedAcceptedRain: 1,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             21,
							AutoAssignedAccepted:     18,
							AutoAssignedRain:         4,
							AutoAssignedAcceptedRain: 1,
						},
					},
				},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "hasn't accept order only while rainning: accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             21,
					AutoAssignedAccepted:     18,
					AutoAssignedRain:         3,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             21,
							AutoAssignedAccepted:     18,
							AutoAssignedRain:         3,
							AutoAssignedAcceptedRain: 0,
						},
					},
				},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "hasn't accept order only while rainning: not accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             21,
					AutoAssignedAccepted:     17,
					AutoAssignedRain:         4,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             21,
							AutoAssignedAccepted:     17,
							AutoAssignedRain:         4,
							AutoAssignedAcceptedRain: 0,
						},
					},
				},
			},
			expectedAR: 100,
			expectedARByServices: map[Service]float64{
				ServiceFood: 100,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "hasn't accept order only while rainning: not accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             21,
					AutoAssignedAccepted:     17,
					AutoAssignedRain:         3,
					AutoAssignedAcceptedRain: 0,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             21,
							AutoAssignedAccepted:     17,
							AutoAssignedRain:         3,
							AutoAssignedAcceptedRain: 0,
						},
					},
				},
			},
			expectedAR: 94.4,
			expectedARByServices: map[Service]float64{
				ServiceFood: 94.4,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},

		// Driver hasn't accepted order BOTH while rainning and not raining
		{
			name: "hasn't accepted order both while rainning and not raining: accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             30,
					AutoAssignedAccepted:     12,
					AutoAssignedRain:         8,
					AutoAssignedAcceptedRain: 3,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             30,
							AutoAssignedAccepted:     12,
							AutoAssignedRain:         8,
							AutoAssignedAcceptedRain: 3,
						},
					},
				},
			},
			expectedAR: 48,
			expectedARByServices: map[Service]float64{
				ServiceFood: 48,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "hasn't accepted order both while rainning and not raining: accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             30,
					AutoAssignedAccepted:     12,
					AutoAssignedRain:         7,
					AutoAssignedAcceptedRain: 2,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             30,
							AutoAssignedAccepted:     12,
							AutoAssignedRain:         7,
							AutoAssignedAcceptedRain: 2,
						},
					},
				},
			},
			expectedAR: 48,
			expectedARByServices: map[Service]float64{
				ServiceFood: 48,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "hasn't accepted order both while rainning and not raining: not accept new order while raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             30,
					AutoAssignedAccepted:     11,
					AutoAssignedRain:         8,
					AutoAssignedAcceptedRain: 2,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             30,
							AutoAssignedAccepted:     11,
							AutoAssignedRain:         8,
							AutoAssignedAcceptedRain: 2,
						},
					},
				},
			},
			expectedAR: 45.8,
			expectedARByServices: map[Service]float64{
				ServiceFood: 45.8,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "hasn't accepted order both while rainning and not raining: not accept new order while not raining",
			dailyCounts: []DailyCount{
				{
					Year:                     2021,
					Month:                    11,
					Day:                      1,
					AutoAssigned:             30,
					AutoAssignedAccepted:     11,
					AutoAssignedRain:         7,
					AutoAssignedAcceptedRain: 2,
					ByServices: map[Service]*DailyCountByServiceStat{
						ServiceFood: {
							AutoAssigned:             30,
							AutoAssignedAccepted:     11,
							AutoAssignedRain:         7,
							AutoAssignedAcceptedRain: 2,
						},
					},
				},
			},
			expectedAR: 44,
			expectedARByServices: map[Service]float64{
				ServiceFood: 44,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(tt *testing.T) {
			driverOrderInfo := DriverOrderInfo{DailyCounts: test.dailyCounts}
			_, ar, arByServices := driverOrderInfo.GetAR(test.startDate, test.endDate)
			require.Equal(tt, test.expectedAR, ar)
			if test.expectedARByServices != nil {
				require.Len(tt, arByServices, len(test.expectedARByServices))
				for service, serviceAR := range test.expectedARByServices {
					require.Equal(tt, serviceAR, arByServices[service])
				}
			}
		})
	}
}

func TestDriverOrderInfo_PerformanceCR(t *testing.T) {
	tests := []struct {
		name                 string
		dailyCounts          []DailyCount
		expectedCR           float64
		expectedCRByServices map[Service]float64
		startDate            time.Time
		endDate              time.Time
	}{
		{
			name:        "no daily counts",
			dailyCounts: []DailyCount{},
			expectedCR:  0,
			startDate:   createStartDate(2021, 10, 1),
			endDate:     createEndDate(2021, 12, 31),
		},
		{
			name: "should calculate CR correctly for a day",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssignedAccepted: 100, CancelledNotFree: 46},
			},
			expectedCR: 46,
			startDate:  createStartDate(2021, 11, 1),
			endDate:    createEndDate(2021, 11, 1),
		},
		{
			name: "should calculate CR correctly for many days",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssignedAccepted: 30, CancelledNotFree: 22},
				{Year: 2021, Month: 11, Day: 2, AutoAssignedAccepted: 24, CancelledNotFree: 11},
				{Year: 2021, Month: 11, Day: 3, AutoAssignedAccepted: 19, CancelledNotFree: 9},
			},
			expectedCR: 57.5,
			startDate:  createStartDate(2021, 11, 1),
			endDate:    createEndDate(2021, 11, 3),
		},
		{
			name: "should calculate CR correctly and ignore counts that are out of range",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 10, Day: 31, AutoAssignedAccepted: 26, CancelledNotFree: 5},
				{Year: 2021, Month: 11, Day: 1, AutoAssignedAccepted: 30, CancelledNotFree: 22},
				{Year: 2021, Month: 11, Day: 2, AutoAssignedAccepted: 24, CancelledNotFree: 11},
				{Year: 2021, Month: 11, Day: 3, AutoAssignedAccepted: 19, CancelledNotFree: 9},
				{Year: 2021, Month: 12, Day: 1, AutoAssignedAccepted: 49, CancelledNotFree: 24},
			},
			expectedCR: 57.5,
			startDate:  createStartDate(2021, 11, 1),
			endDate:    createEndDate(2021, 11, 3),
		},
		{
			name: "should calculate CR by services correctly",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssignedAccepted: 30, CancelledNotFree: 22, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssignedAccepted: 24, CancelledNotFree: 20},
					ServiceMart:      {AutoAssignedAccepted: 4, CancelledNotFree: 1},
					ServiceMessenger: {AutoAssignedAccepted: 2, CancelledNotFree: 1},
				}},
				{Year: 2021, Month: 11, Day: 2, AutoAssignedAccepted: 24, CancelledNotFree: 11, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {AutoAssignedAccepted: 22, CancelledNotFree: 11},
					ServiceMart: {AutoAssignedAccepted: 2, CancelledNotFree: 0},
				}},
				{Year: 2021, Month: 11, Day: 3, AutoAssignedAccepted: 19, CancelledNotFree: 9, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssignedAccepted: 16, CancelledNotFree: 6},
					ServiceMart:      {AutoAssignedAccepted: 1, CancelledNotFree: 1},
					ServiceMessenger: {AutoAssignedAccepted: 2, CancelledNotFree: 2},
				}},
			},
			expectedCR: 57.5,
			expectedCRByServices: map[Service]float64{
				ServiceFood:      59.7,
				ServiceMart:      28.6,
				ServiceMessenger: 75.0,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
		{
			name: "should not calculate CR by services if some day data is missing",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssignedAccepted: 30, CancelledNotFree: 22, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssignedAccepted: 24, CancelledNotFree: 20},
					ServiceMart:      {AutoAssignedAccepted: 4, CancelledNotFree: 1},
					ServiceMessenger: {AutoAssignedAccepted: 2, CancelledNotFree: 1},
				}},
				{Year: 2021, Month: 11, Day: 2, AutoAssignedAccepted: 24, CancelledNotFree: 11},
				{Year: 2021, Month: 11, Day: 3, AutoAssignedAccepted: 19, CancelledNotFree: 9, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssignedAccepted: 16, CancelledNotFree: 6},
					ServiceMart:      {AutoAssignedAccepted: 1, CancelledNotFree: 1},
					ServiceMessenger: {AutoAssignedAccepted: 2, CancelledNotFree: 2},
				}},
			},
			expectedCR:           57.5,
			expectedCRByServices: nil,
			startDate:            createStartDate(2021, 11, 1),
			endDate:              createEndDate(2021, 11, 3),
		},
		{
			name: "should not calculate CR by services if if every day accepted is zero",
			dailyCounts: []DailyCount{
				{Year: 2021, Month: 11, Day: 1, AutoAssignedAccepted: 30, CancelledNotFree: 22, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssignedAccepted: 0, CancelledNotFree: 20},
					ServiceMart:      {AutoAssignedAccepted: 4, CancelledNotFree: 1},
					ServiceMessenger: {AutoAssignedAccepted: 2, CancelledNotFree: 1},
				}},
				{Year: 2021, Month: 11, Day: 2, AutoAssignedAccepted: 24, CancelledNotFree: 11, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {AutoAssignedAccepted: 0, CancelledNotFree: 11},
					ServiceMart: {AutoAssignedAccepted: 2, CancelledNotFree: 0},
				}},
				{Year: 2021, Month: 11, Day: 3, AutoAssignedAccepted: 19, CancelledNotFree: 9, ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood:      {AutoAssignedAccepted: 0, CancelledNotFree: 6},
					ServiceMart:      {AutoAssignedAccepted: 1, CancelledNotFree: 1},
					ServiceMessenger: {AutoAssignedAccepted: 2, CancelledNotFree: 2},
				}},
			},
			expectedCR: 57.5,
			expectedCRByServices: map[Service]float64{
				ServiceFood:      0.0,
				ServiceMart:      28.6,
				ServiceMessenger: 75.0,
			},
			startDate: createStartDate(2021, 11, 1),
			endDate:   createEndDate(2021, 11, 3),
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(tt *testing.T) {
			driverOrderInfo := DriverOrderInfo{DailyCounts: test.dailyCounts}
			_, cr, crByServices := driverOrderInfo.GetCR(test.startDate, test.endDate)
			require.Equal(tt, test.expectedCR, cr)
			require.Len(tt, crByServices, len(test.expectedCRByServices))
			for service, serviceAR := range test.expectedCRByServices {
				require.Equal(tt, serviceAR, crByServices[service])
			}
		})
	}
}

func TestDriverOrderInfo_DailyCount(t *testing.T) {

	t.Run("error SetAutoAssignedRain", func(tt *testing.T) {
		dc := DailyCount{}
		err := dc.SetAutoAssignedRain("invalid", 0)
		require.Equal(tt, errInvalidServiceType, err)
	})

	t.Run("error SetAutoAssignedAcceptedRain", func(tt *testing.T) {
		dc := DailyCount{}
		err := dc.SetAutoAssignedAcceptedRain("invalid", 0)
		require.Equal(tt, errInvalidServiceType, err)
	})

}

func createStartDate(year, month, day int) time.Time {
	return timeutil.DateTruncate(time.Date(year, time.Month(month), day, 0, 0, 0, 0, timeutil.BangkokLocation()))
}

func createEndDate(year, month, day int) time.Time {
	return timeutil.DateCeiling(time.Date(year, time.Month(month), day, 0, 0, 0, 0, timeutil.BangkokLocation()))
}

func createDriverOrderInfoDetail(orderID string, distance float64, createdAt time.Time) DriverOrderInfoDetail {
	return DriverOrderInfoDetail{
		OrderId:                 orderID,
		Status:                  StatusCompleted,
		DistanceFromDestination: distance,
		CreatedAt:               createdAt,
		IsSwitchFlow:            false,
	}
}

func TestDriverOrderInfo_DailyCount_Validate(t *testing.T) {
	tests := []struct {
		name          string
		dailyCount    DailyCount
		expectedError error
	}{
		{
			name: "happy case",
			dailyCount: DailyCount{
				Year:                     2023,
				Month:                    8,
				Day:                      15,
				AutoAssigned:             3,
				AutoAssignedAccepted:     1,
				AutoAssignedRain:         3,
				AutoAssignedAcceptedRain: 1,
				ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {
						AutoAssigned:             3,
						AutoAssignedAccepted:     1,
						AutoAssignedRain:         3,
						AutoAssignedAcceptedRain: 1,
					},
				},
			},
			expectedError: nil,
		},
		{
			name: "accept while raining: auto assigned accepted rain not count up (error)",
			dailyCount: DailyCount{
				Year:                     2023,
				Month:                    8,
				Day:                      15,
				AutoAssigned:             4,
				AutoAssignedAccepted:     2,
				AutoAssignedRain:         4,
				AutoAssignedAcceptedRain: 1, // should be 2
				ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {
						AutoAssigned:             4,
						AutoAssignedAccepted:     2,
						AutoAssignedRain:         4,
						AutoAssignedAcceptedRain: 1, // should be 2
					},
				},
			},
			expectedError: ErrInvalidCounts,
		},
		{
			name: "accept while not raining: auto assigned rain over count (error)",
			dailyCount: DailyCount{
				Year:                     2023,
				Month:                    8,
				Day:                      15,
				AutoAssigned:             4,
				AutoAssignedAccepted:     2,
				AutoAssignedRain:         4, // should be 3
				AutoAssignedAcceptedRain: 1,
				ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {
						AutoAssigned:             4,
						AutoAssignedAccepted:     2,
						AutoAssignedRain:         4, // should be 3
						AutoAssignedAcceptedRain: 1,
					},
				},
			},
			expectedError: ErrInvalidCounts,
		},
		{
			name: "not accept while raining: assigned and auto assigned are not count up (error)",
			dailyCount: DailyCount{
				Year:                     2023,
				Month:                    8,
				Day:                      15,
				AutoAssigned:             3, // should be 4
				AutoAssignedAccepted:     1,
				AutoAssignedRain:         4,
				AutoAssignedAcceptedRain: 1,
				ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {
						AutoAssigned:             3, // should be 4
						AutoAssignedAccepted:     1,
						AutoAssignedRain:         4,
						AutoAssignedAcceptedRain: 1,
					},
				},
			},
			expectedError: ErrInvalidCounts,
		},
		{
			name: "not accept while not raining: pass",
			dailyCount: DailyCount{
				Year:                     2023,
				Month:                    8,
				Day:                      15,
				AutoAssigned:             4,
				AutoAssignedAccepted:     2,
				AutoAssignedRain:         3,
				AutoAssignedAcceptedRain: 2,
				ByServices: map[Service]*DailyCountByServiceStat{
					ServiceFood: {
						AutoAssigned:             4,
						AutoAssignedAccepted:     2,
						AutoAssignedRain:         3,
						AutoAssignedAcceptedRain: 2,
					},
				},
			},
			expectedError: nil,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(tt *testing.T) {

			err := test.dailyCount.Validate()

			require.Equal(tt, test.expectedError, err)
		})
	}
}
