package model

import (
	"errors"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var WithdrawalQuotaNotAvailable = errors.New("free withdrawal quota not available")

func NewWithdrawalQuota(withdrawalCount int, cfg config.PaymentConfig) *WithdrawalQuota {
	quotaUsedToday := withdrawalCount
	if quotaUsedToday > cfg.WalletWithdrawMaximumReq {
		quotaUsedToday = cfg.WalletWithdrawMaximumReq
	}

	return &WithdrawalQuota{
		MaxFreeQuotaToday:  cfg.WalletWithdrawMaximumReq,
		FreeQuotaUsedToday: quotaUsedToday,
		LastQuotaResetAt:   time.Now(),
	}
}

func (wa *WithdrawalQuota) GetFee(cfg config.PaymentConfig) (fee types.Money, isLimitReached bool) {
	if wa.IsResetRequired() {
		wa.ResetQuotaToday(cfg)
	}
	if wa.FreeQuotaUsedToday < wa.MaxFreeQuotaToday {
		return types.Money(0), false
	}
	if !cfg.WithdrawalChargeFeeEnabled {
		return types.Money(0), true
	}
	return cfg.WithdrawalFee, true
}

func (wa *WithdrawalQuota) IncreaseForToday(cfg config.PaymentConfig) {
	if wa.IsResetRequired() {
		wa.ResetQuotaToday(cfg)
	}
	wa.MaxFreeQuotaToday += 1
}

func (wa *WithdrawalQuota) UseFreeWithdrawalQuota(cfg config.PaymentConfig) error {
	if wa.IsResetRequired() {
		wa.ResetQuotaToday(cfg)
	}
	if wa.FreeQuotaUsedToday >= wa.MaxFreeQuotaToday {
		return WithdrawalQuotaNotAvailable
	}
	wa.FreeQuotaUsedToday += 1
	return nil
}

func (wa *WithdrawalQuota) IsResetRequired() bool {
	lastQuotaResetAt := timeutil.DateTruncate(wa.LastQuotaResetAt.In(timeutil.BangkokLocation()))
	now := timeutil.DateTruncate(timeutil.BangkokNow())
	if lastQuotaResetAt != now {
		return true
	}
	return false
}

func (wa *WithdrawalQuota) ResetQuotaToday(cfg config.PaymentConfig) {
	wa.FreeQuotaUsedToday = 0
	wa.MaxFreeQuotaToday = cfg.WalletWithdrawMaximumReq
	wa.LastQuotaResetAt = time.Now()
}

type WithdrawalQuota struct {
	MaxFreeQuotaToday  int       `bson:"max_free_quota_today" json:"maxFreeQuotaToday"`
	FreeQuotaUsedToday int       `bson:"free_quota_used_today" json:"freeQuotaUsedToday"`
	LastQuotaResetAt   time.Time `bson:"last_quota_reset_at" json:"lastUpdatedAt"`
}
