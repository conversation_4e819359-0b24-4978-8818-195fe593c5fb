package model

import "git.wndv.co/lineman/fleet-distribution/internal/types"

type TripDriverWageSummary struct {
	BaseWage     types.Money `bson:"base_wage" json:"baseWage"`
	DistanceWage types.Money `bson:"distance_wage" json:"distanceWage"`
	// This field support only messenger round trip order.
	ExtraCharge               types.Money `bson:"extra_charge" json:"extraCharge"`
	AdditionServiceWage       types.Money `bson:"addition_service_wage" json:"additionServiceWage"`
	ShiftDeduction            types.Money `bson:"shift_deduction" json:"shiftDeduction"`
	TotalDriverWage           types.Money `bson:"total_driver_wage" json:"totalDriverWage"`
	CommissionRate            float64     `bson:"commission_rate" json:"commissionRate"`
	Commission                types.Money `bson:"commission" json:"commission"`
	AdditionServiceCommission types.Money `bson:"addition_service_commission" json:"additionServiceCommission"`
	WithHoldingTax            types.Money `bson:"withholding_tax" json:"withholdingTax"`
	WithHoldingOnTopTax       types.Money `bson:"withholding_on_top_tax" json:"withholdingOnTopTax"`
	WithHoldingOnTopTaxRate   float64     `bson:"withholding_on_top_tax_rate" json:"withholdingOnTopTaxRate"`
	TransferAmount            types.Money `bson:"transfer_amount" json:"transferAmount"`
	Outstanding               types.Money `bson:"outstanding" json:"outstanding"`
}

func (t *TripDriverWageSummary) CalculateCommissionAndWithHoldingTax(commissionRate, withHoldingTaxRate float64) {
	t.TotalDriverWage = t.BaseWage.Add(t.DistanceWage).Add(t.ExtraCharge).Add(t.AdditionServiceWage).Sub(t.ShiftDeduction)
	t.CommissionRate = commissionRate
	t.Commission = t.TotalDriverWage.Mul(types.NewMoney(commissionRate))
	t.AdditionServiceCommission = t.AdditionServiceCommission.Mul(types.NewMoney(commissionRate))
	t.WithHoldingTax = t.TotalDriverWage.Sub(t.Commission).Mul(types.NewMoney(withHoldingTaxRate))
}

func (t *TripDriverWageSummary) TotalCommission() types.Money {
	return t.Commission.Add(t.AdditionServiceCommission)
}
