package model

import (
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type OrderCancelPayload struct {
	Location  Location  `json:"location,omitempty"`
	CreatedAt time.Time `json:"created_at,omitempty"`
	Region    string    `json:"region,omitempty"`
}

type CancelOrderEventPayload struct {
	OrderEventPayload
	IsCancellationFree bool `json:"is_cancellation_free,omitempty"`
}

type CountsDeleteOrderPayload struct {
	DriverID            string `json:"driver_id,omitempty"`
	DeleteDailyCounts   bool   `json:"delete_daily_counts,omitempty"`
	DeleteMonthlyCounts bool   `json:"delete_monthly_counts,omitempty"`
}

type CountsSetOrderPayload struct {
	DriverID      string         `json:"driver_id,omitempty"`
	DailyCounts   []DailyCount   `json:"daily_counts,omitempty"`
	MonthlyCounts []MonthlyCount `json:"monthly_counts,omitempty"`
}

type VelocityOverLimitPayload struct {
	DriverID  string    `json:"driver_id,omitempty"`
	CreatedAt time.Time `json:"created_at,omitempty"`
}

func (volp *VelocityOverLimitPayload) GetCreatedAt() (int, time.Month, int) {
	return volp.CreatedAt.In(timeutil.BangkokLocation()).Date()
}

type OrderEventPayload struct {
	OrderID             string    `json:"order_id,omitempty"`
	DriverID            string    `json:"driver_id,omitempty"`
	Location            Location  `json:"location,omitempty"`
	DestinationLocation Location  `json:"destination_location,omitempty"`
	DriverLocation      Location  `json:"driver_location,omitempty"`
	CreatedAt           time.Time `json:"created_at,omitempty"`
	Region              string    `json:"region,omitempty"`
	SwitchFlow          bool      `json:"switch_flow,omitempty"`
	Status              Status    `json:"status,omitempty"`
	IsAutoAssign        bool      `json:"is_auto_assign,omitempty"`
	ServiceType         Service   `json:"service_type,omitempty"`
	DeliveringRound     int       `json:"delivering_round,omitempty"`
	HasRained           bool      `json:"has_rained,omitempty"`
}

func (oep *OrderEventPayload) GetCreatedAt() (int, time.Month, int) {
	return oep.CreatedAt.In(timeutil.BangkokLocation()).Date()
}

type StatsResetEventPayload struct {
	DriverID                 string    `json:"driver_id,omitempty"`
	Date                     time.Time `json:"date,omitempty"`
	Service                  Service   `json:"service"`
	AutoAssignedAccepted     *int      `json:"auto_assigned_accepted,omitempty"`
	AutoAssignedAcceptedRain *int      `json:"auto_assigned_accepted_rain,omitempty"`
	AutoAssigned             *int      `json:"auto_assigned,omitempty"`
	AutoAssignedRain         *int      `json:"auto_assigned_rain,omitempty"`
	CancelledNotFree         *int      `json:"cancelled_not_free,omitempty"`
	Completed                *int      `json:"completed,omitempty"`
	CancelledFree            *int      `json:"cancelled_free,omitempty"`
	RequestedBy              string    `json:"requested_by,omitempty"`
	Upsert                   bool      `json:"upsert,omitempty"`
}
