package model

import (
	"errors"
	"sort"
)

var (
	ErrBenefitNotFound   = errors.New("benefits of tier not found")
	ErrInvalidDriverTier = errors.New("invalid driver tier")
	ErrInvalidBenefit    = errors.New("invalid benefit")
)

type BenefitsJSON struct {
	Banner     string      `json:"banner" validate:"required"`
	DriverTier DriverTier  `json:"driverTier" validate:"required"`
	Title      string      `json:"title" validate:"required"`
	Benefit    BenefitList `json:"benefit" validate:"required"`
	CTA        *CTA        `json:"cta,omitempty" validate:"omitempty"`
}

type Benefits struct {
	BenefitsJSON
	Remark string `json:"remark" validate:"required"`
}

type BenefitList struct {
	Active   []BenefitItem `json:"active" validate:"required"`
	Inactive []BenefitItem `json:"inactive" validate:"required"`
}

type BenefitItem struct {
	ID          string `json:"id" validate:"required"`
	Icon        string `json:"icon" validate:"required"`
	Title       string `json:"title" validate:"required"`
	Description string `json:"description" validate:"required"`
	Sequence    int    `json:"sequence" validate:"required"`
}

type CTA struct {
	Title    string `json:"title" validate:"required"`
	DeepLink string `json:"deeplink" validate:"required"`
}

func (bl BenefitList) Sort() {
	// Sort by sequence ascending
	sort.Slice(bl.Active, func(i, j int) bool {
		return bl.Active[i].Sequence < bl.Active[j].Sequence
	})

	sort.Slice(bl.Inactive, func(i, j int) bool {
		return bl.Inactive[i].Sequence < bl.Inactive[j].Sequence
	})
}
