package model

type QuestionAnswers struct {
	Questions []QuestionAnswer `json:"questions"`
}

type QuestionAnswer struct {
	ID       int      `json:"id"`
	Title    string   `json:"question"`
	Choices  []Choice `json:"choices"`
	AnswerID int      `json:"answerId"`
}

type Choice struct {
	ID     int    `json:"id"`
	Answer string `json:"answer"`
}

type UserAnswer struct {
	QuestionID int `json:"questionId"`
	AnswerID   int `json:"answerId"`
}

type ValidateResult struct {
	Pass      bool
	Score     int
	FullScore int
}

func (q *QuestionAnswers) Validate(userAnswers []UserAnswer) ValidateResult {
	validAnswer := make(map[int]int)
	for _, v := range q.Questions {
		validAnswer[v.ID] = v.AnswerID
	}

	var score int
	validatedAnswer := make(map[int]int)
	for _, v := range userAnswers {
		if _, found := validatedAnswer[v.QuestionID]; !found {
			if validAnswer[v.QuestionID] == v.AnswerID {
				score += 1
				validatedAnswer[v.QuestionID] = v.AnswerID
			}
		}
	}

	fullScore := len(q.Questions)
	pass := float64(score) >= 0.8*float64(fullScore)

	return ValidateResult{
		Pass:      pass,
		Score:     score,
		FullScore: fullScore,
	}
}
