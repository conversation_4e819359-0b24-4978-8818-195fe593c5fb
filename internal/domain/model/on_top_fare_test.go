package model_test

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

var priceSchemeRMS = string(model.PriceSchemeRMS)
var priceSchemeDefault = string(model.PriceSchemeDefault)
var dummyQuote = model.Quote{
	PriceScheme: priceSchemeRMS,
	PayAtStop:   1,
	ServiceType: model.ServiceFood,
	Routes: []model.Stop{
		{},
		{PriceSummary: model.PriceSummary{
			ItemFee: model.ItemFeeSummary{
				PaymentMethod: model.PaymentMethodCreditCard,
				Total:         20,
			},
		}}},
}

func TestValidateFlatRatePrice(t *testing.T) {
	t.Run("default flatrate if amount is less than 1 should be error", func(tt *testing.T) {
		flatRatePrices := []model.FlatRatePrice{
			{
				FlatRateType: model.DefaultFlatRate,
				NormalAmount: 0,
			},
		}

		err := model.ValidateFlatRatePrice(flatRatePrices)
		require.Error(tt, err)
	})

	t.Run("default flatrate if amount is greater than 1 should no error", func(tt *testing.T) {
		flatRatePrices := []model.FlatRatePrice{
			{
				FlatRateType: model.DefaultFlatRate,
				NormalAmount: 1,
			},
		}

		err := model.ValidateFlatRatePrice(flatRatePrices)
		require.NoError(tt, err)
	})

	t.Run("flexible flatrate if amount is less than 1 should be error", func(tt *testing.T) {
		flatRatePrices := []model.FlatRatePrice{
			{
				FlatRateType: model.FlexibleFlatRate,
				NormalAmount: 0,
				BundleAmount: 0,
			},
		}

		err := model.ValidateFlatRatePrice(flatRatePrices)
		require.Error(tt, err)
	})

	t.Run("flexible flatrate if normal amount is greater than 1 should no error", func(tt *testing.T) {
		flatRatePrices := []model.FlatRatePrice{
			{
				FlatRateType: model.FlexibleFlatRate,
				NormalAmount: 1,
				BundleAmount: 0,
			},
		}

		err := model.ValidateFlatRatePrice(flatRatePrices)
		require.NoError(tt, err)
	})

	t.Run("flexible flatrate if flexible amount is greater than 1 should no error", func(tt *testing.T) {
		flatRatePrices := []model.FlatRatePrice{
			{
				FlatRateType: model.FlexibleFlatRate,
				NormalAmount: 0,
				BundleAmount: 1,
			},
		}

		err := model.ValidateFlatRatePrice(flatRatePrices)
		require.NoError(tt, err)
	})

	t.Run("flexible flatrate if coin is greater than 1 should no error", func(tt *testing.T) {
		flatRatePrices := []model.FlatRatePrice{
			{
				FlatRateType:     model.FlexibleFlatRate,
				NormalAmount:     0,
				BundleAmount:     0,
				NormalCoinAmount: 1,
				BundleCoinAmount: 0,
			},
		}

		err := model.ValidateFlatRatePrice(flatRatePrices)
		require.NoError(tt, err)
	})

	t.Run("flexible flatrate if bundle coin is greater than 1 should no error", func(tt *testing.T) {
		flatRatePrices := []model.FlatRatePrice{
			{
				FlatRateType:     model.FlexibleFlatRate,
				NormalAmount:     0,
				BundleAmount:     0,
				NormalCoinAmount: 0,
				BundleCoinAmount: 1,
			},
		}

		err := model.ValidateFlatRatePrice(flatRatePrices)
		require.NoError(tt, err)
	})
}

func TestValidateBasketPrices(t *testing.T) {

	t.Run("prices range over 10 should be error", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 1, To: 3, Amount: types.NewMoney(10)},
			{From: 4, To: 6, Amount: types.NewMoney(11)},
			{From: 7, To: 9, Amount: types.NewMoney(12)},
			{From: 10, To: 12, Amount: types.NewMoney(13)},
			{From: 13, To: 15, Amount: types.NewMoney(14)},
			{From: 16, To: 18, Amount: types.NewMoney(15)},
			{From: 19, To: 21, Amount: types.NewMoney(16)},
			{From: 22, To: 24, Amount: types.NewMoney(17)},
			{From: 25, To: 27, Amount: types.NewMoney(18)},
			{From: 28, To: 30, Amount: types.NewMoney(19)},
			{From: 31, To: 33, Amount: types.NewMoney(20)},
		}

		err := model.ValidateBasketPrices(basketPrices)

		require.Error(tt, err)
	})

	t.Run("maximum each ontop should be not over 1000", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 1, To: 3, Amount: types.NewMoney(1001)},
			{From: 4, To: 6, Amount: types.NewMoney(111)},
			{From: 7, To: 9, Amount: types.NewMoney(222)},
			{From: 10, To: 12, Amount: types.NewMoney(333)},
			{From: 13, To: 15, Amount: types.NewMoney(444)},
		}

		err := model.ValidateBasketPrices(basketPrices)

		require.Error(tt, err)
	})

	t.Run("price range should no exceed maximum should not be error", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 1, To: 3, Amount: types.NewMoney(100)},
			{From: 4, To: 6, Amount: types.NewMoney(999.999)},
			{From: 7, To: 9, Amount: types.NewMoney(1000)},
			{From: 10, To: 12, Amount: types.NewMoney(333)},
			{From: 13, To: 15, Amount: types.NewMoney(444)},
		}

		err := model.ValidateBasketPrices(basketPrices)

		require.NoError(tt, err)
	})

	t.Run("Should not have grab between price range previous to and next from", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 5, To: 6, Amount: types.NewMoney(1)},
			{From: 1, To: 3, Amount: types.NewMoney(2)},
			{From: 10, To: 12, Amount: types.NewMoney(3)},
			{From: 7, To: 9, Amount: types.NewMoney(4)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}

		err := model.ValidateBasketPrices(basketPrices)

		require.Error(tt, err)
	})

	t.Run("Same price range from should not more than to", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 5, To: 6, Amount: types.NewMoney(1)},
			{From: 1, To: 3, Amount: types.NewMoney(2)},
			{From: 10, To: 12, Amount: types.NewMoney(3)},
			{From: 7, To: 9, Amount: types.NewMoney(4)},
			{From: 16, To: 15, Amount: types.NewMoney(5)},
		}

		err := model.ValidateBasketPrices(basketPrices)

		require.Error(tt, err)
	})

	t.Run("one of cash/coin amount must be set", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 1, To: 3},
			{From: 4, To: 6, Amount: types.NewMoney(222)},
			{From: 7, To: 9, Amount: types.NewMoney(333)},
			{From: 10, To: 12, Amount: types.NewMoney(444)},
			{From: 13, To: 15, Amount: types.NewMoney(555)},
		}

		err := model.ValidateBasketPrices(basketPrices)

		require.Error(tt, err)
	})

	t.Run("cash amount must not below 0", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 1, To: 3, Amount: types.NewMoney(-1)},
			{From: 4, To: 6, Amount: types.NewMoney(222)},
			{From: 7, To: 9, Amount: types.NewMoney(333)},
			{From: 10, To: 12, Amount: types.NewMoney(444)},
			{From: 13, To: 15, Amount: types.NewMoney(555)},
		}

		err := model.ValidateBasketPrices(basketPrices)

		require.Error(tt, err)
	})

	t.Run("coin amount must not below 0", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 1, To: 3, CoinAmount: -1},
			{From: 4, To: 6, CoinAmount: 2},
			{From: 7, To: 9, CoinAmount: 3},
			{From: 10, To: 12, CoinAmount: 4},
			{From: 13, To: 15, CoinAmount: 5},
		}

		err := model.ValidateBasketPrices(basketPrices)

		require.Error(tt, err)
	})
}

func TestValidateUserFarePrices(t *testing.T) {

	t.Run("prices range over 10 should be error", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 1, To: 3, Amount: types.NewMoney(10)},
			{From: 4, To: 6, Amount: types.NewMoney(11)},
			{From: 7, To: 9, Amount: types.NewMoney(12)},
			{From: 10, To: 12, Amount: types.NewMoney(13)},
			{From: 13, To: 15, Amount: types.NewMoney(14)},
			{From: 16, To: 18, Amount: types.NewMoney(15)},
			{From: 19, To: 21, Amount: types.NewMoney(16)},
			{From: 22, To: 24, Amount: types.NewMoney(17)},
			{From: 25, To: 27, Amount: types.NewMoney(18)},
			{From: 28, To: 30, Amount: types.NewMoney(19)},
			{From: 31, To: 33, Amount: types.NewMoney(20)},
		}

		err := model.ValidateUserFarePrices(userFarePrices)

		require.Error(tt, err)
	})

	t.Run("maximum each on-top should be not over 1000", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 1, To: 3, Amount: types.NewMoney(1001)},
			{From: 4, To: 6, Amount: types.NewMoney(111)},
			{From: 7, To: 9, Amount: types.NewMoney(222)},
			{From: 10, To: 12, Amount: types.NewMoney(333)},
			{From: 13, To: 15, Amount: types.NewMoney(444)},
		}

		err := model.ValidateUserFarePrices(userFarePrices)

		require.Error(tt, err)
	})

	t.Run("price range should no exceed maximum should not be error", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 1, To: 3, Amount: types.NewMoney(100)},
			{From: 4, To: 6, Amount: types.NewMoney(999.999)},
			{From: 7, To: 9, Amount: types.NewMoney(1000)},
			{From: 10, To: 12, Amount: types.NewMoney(333)},
			{From: 13, To: 15, Amount: types.NewMoney(444)},
		}

		err := model.ValidateUserFarePrices(userFarePrices)

		require.NoError(tt, err)
	})

	t.Run("Should not have grab between price range previous to and next from", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 5, To: 6, Amount: types.NewMoney(1)},
			{From: 1, To: 3, Amount: types.NewMoney(2)},
			{From: 10, To: 12, Amount: types.NewMoney(3)},
			{From: 7, To: 9, Amount: types.NewMoney(4)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}

		err := model.ValidateUserFarePrices(userFarePrices)

		require.Error(tt, err)
	})

	t.Run("Same price range from should not more than to", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 5, To: 6, Amount: types.NewMoney(1)},
			{From: 1, To: 3, Amount: types.NewMoney(2)},
			{From: 10, To: 12, Amount: types.NewMoney(3)},
			{From: 7, To: 9, Amount: types.NewMoney(4)},
			{From: 16, To: 15, Amount: types.NewMoney(5)},
		}

		err := model.ValidateUserFarePrices(userFarePrices)

		require.Error(tt, err)
	})

	t.Run("one of cash/coin amount must be set", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 1, To: 3},
			{From: 4, To: 6, Amount: types.NewMoney(222)},
			{From: 7, To: 9, Amount: types.NewMoney(333)},
			{From: 10, To: 12, Amount: types.NewMoney(444)},
			{From: 13, To: 15, Amount: types.NewMoney(555)},
		}

		err := model.ValidateUserFarePrices(userFarePrices)

		require.Error(tt, err)
	})

	t.Run("cash amount must not below 0", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 1, To: 3, Amount: types.NewMoney(-1)},
			{From: 4, To: 6, Amount: types.NewMoney(222)},
			{From: 7, To: 9, Amount: types.NewMoney(333)},
			{From: 10, To: 12, Amount: types.NewMoney(444)},
			{From: 13, To: 15, Amount: types.NewMoney(555)},
		}

		err := model.ValidateUserFarePrices(userFarePrices)

		require.Error(tt, err)
	})

	t.Run("coin amount must not below 0", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 1, To: 3, CoinAmount: -1},
			{From: 4, To: 6, CoinAmount: 2},
			{From: 7, To: 9, CoinAmount: 3},
			{From: 10, To: 12, CoinAmount: 4},
			{From: 13, To: 15, CoinAmount: 5},
		}

		err := model.ValidateUserFarePrices(userFarePrices)

		require.Error(tt, err)
	})
}

func TestValidateDistancePrices(t *testing.T) {

	tests := []struct {
		name      string
		prices    []model.OntopDistancePrice
		expectErr string
	}{
		{name: "empty distance prices should be error", prices: []model.OntopDistancePrice{}, expectErr: "invalid distance prices size"},
		{name: "distance prices exceed length should be error",
			prices: []model.OntopDistancePrice{
				{},
				{},
				{},
				{},
				{},
				{},
				{},
				{},
				{},
				{},
				{},
			},
			expectErr: "invalid distance prices size",
		},
		{name: "nil distance prices should be error", prices: nil, expectErr: "invalid distance prices size"},
		{name: "cash must not below zero", prices: []model.OntopDistancePrice{{From: 1, To: 100, Amount: -1}}, expectErr: "cash/coin amount must more than 0"},
		{name: "coin must not below zero", prices: []model.OntopDistancePrice{{From: 1, To: 100, CoinAmount: -1}}, expectErr: "cash/coin amount must more than 0"},
		{name: "one of coin/cash amount must be set", prices: []model.OntopDistancePrice{{From: 1, To: 100, Amount: 0, CoinAmount: 0}}, expectErr: "one of cash/coin amount must be set"},
	}
	for _, test := range tests {
		t.Run(test.name, func(tt *testing.T) {
			err := model.ValidateDistancePrices(test.prices)
			require.Error(tt, err)
			require.EqualError(t, err, test.expectErr)
		})
	}
}

func TestSortBucketRange(t *testing.T) {

	t.Run("Should sort price range correctly", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 4, To: 6, Amount: types.NewMoney(111)},
			{From: 1, To: 3, Amount: types.NewMoney(100)},
			{From: 10, To: 12, Amount: types.NewMoney(333)},
			{From: 7, To: 9, Amount: types.NewMoney(222)},
			{From: 13, To: 15, Amount: types.NewMoney(444)},
		}
		actual := model.SortBucketRange(basketPrices)
		require.Equal(tt, 1, actual[0].From)
		require.Equal(tt, 4, actual[1].From)
		require.Equal(tt, 7, actual[2].From)
		require.Equal(tt, 10, actual[3].From)
		require.Equal(tt, 13, actual[4].From)
		require.Equal(tt, 3, actual[0].To)
		require.Equal(tt, 6, actual[1].To)
		require.Equal(tt, 9, actual[2].To)
		require.Equal(tt, 12, actual[3].To)
		require.Equal(tt, 15, actual[4].To)
		require.Equal(tt, types.NewMoney(100), actual[0].Amount)
		require.Equal(tt, types.NewMoney(111), actual[1].Amount)
		require.Equal(tt, types.NewMoney(222), actual[2].Amount)
		require.Equal(tt, types.NewMoney(333), actual[3].Amount)
		require.Equal(tt, types.NewMoney(444), actual[4].Amount)
	})

	t.Run("sort empty basket prices should be work", func(tt *testing.T) {
		var basketPrices []model.OntopBasketPrice
		actual := model.SortBucketRange(basketPrices)
		require.Equal(tt, basketPrices, actual)
	})

	t.Run("sort size 1 basket prices should be work", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{{From: 5, To: 6, Amount: types.NewMoney(1)}}
		actual := model.SortBucketRange(basketPrices)
		require.Equal(tt, 5, actual[0].From)
		require.Equal(tt, 6, actual[0].To)
		require.Equal(tt, types.NewMoney(1), actual[0].Amount)
	})

	t.Run("sort size 2 with equal basketprices should be work", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 5, To: 6, Amount: types.NewMoney(1)},
			{From: 5, To: 6, Amount: types.NewMoney(1)},
		}
		actual := model.SortBucketRange(basketPrices)
		require.Equal(tt, 5, actual[0].From)
		require.Equal(tt, 6, actual[0].To)
		require.Equal(tt, types.NewMoney(1), actual[0].Amount)
		require.Equal(tt, 5, actual[1].From)
		require.Equal(tt, 6, actual[1].To)
		require.Equal(tt, types.NewMoney(1), actual[0].Amount)
	})
}

func TestSortUserFarePriceRange(t *testing.T) {

	t.Run("sort price range correctly", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 4, To: 6, Amount: types.NewMoney(111)},
			{From: 1, To: 3, Amount: types.NewMoney(100)},
			{From: 10, To: 12, Amount: types.NewMoney(333)},
			{From: 7, To: 9, Amount: types.NewMoney(222)},
			{From: 13, To: 15, Amount: types.NewMoney(444)},
		}
		actual := model.SortUserFareRange(userFarePrices)
		require.Equal(tt, 1, actual[0].From)
		require.Equal(tt, 4, actual[1].From)
		require.Equal(tt, 7, actual[2].From)
		require.Equal(tt, 10, actual[3].From)
		require.Equal(tt, 13, actual[4].From)
		require.Equal(tt, 3, actual[0].To)
		require.Equal(tt, 6, actual[1].To)
		require.Equal(tt, 9, actual[2].To)
		require.Equal(tt, 12, actual[3].To)
		require.Equal(tt, 15, actual[4].To)
		require.Equal(tt, types.NewMoney(100), actual[0].Amount)
		require.Equal(tt, types.NewMoney(111), actual[1].Amount)
		require.Equal(tt, types.NewMoney(222), actual[2].Amount)
		require.Equal(tt, types.NewMoney(333), actual[3].Amount)
		require.Equal(tt, types.NewMoney(444), actual[4].Amount)
	})

	t.Run("sort empty user fare prices should be work", func(tt *testing.T) {
		var userFarePrices []model.OntopUserFarePrice
		actual := model.SortUserFareRange(userFarePrices)
		require.Equal(tt, userFarePrices, actual)
	})

	t.Run("sort size 1 user fare prices should be work", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{{From: 5, To: 6, Amount: types.NewMoney(1)}}
		actual := model.SortUserFareRange(userFarePrices)
		require.Equal(tt, 5, actual[0].From)
		require.Equal(tt, 6, actual[0].To)
		require.Equal(tt, types.NewMoney(1), actual[0].Amount)
	})

	t.Run("sort size 2 with equal user fare prices should be work", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 5, To: 6, Amount: types.NewMoney(1)},
			{From: 5, To: 6, Amount: types.NewMoney(1)},
		}
		actual := model.SortUserFareRange(userFarePrices)
		require.Equal(tt, 5, actual[0].From)
		require.Equal(tt, 6, actual[0].To)
		require.Equal(tt, types.NewMoney(1), actual[0].Amount)
		require.Equal(tt, 5, actual[1].From)
		require.Equal(tt, 6, actual[1].To)
		require.Equal(tt, types.NewMoney(1), actual[0].Amount)
	})
}

func TestCalculateOnTopFlexibleFlatRate(t *testing.T) {
	t.Run("DefaultFlatRate should calculate amount correctly", func(tt *testing.T) {
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days: nil,
					Time: nil,
					FlatRatePrices: []model.FlatRatePrice{
						{
							FlatRateType: model.DefaultFlatRate,
							NormalAmount: 10,
							BundleAmount: 20,
						},
					},
					ServiceTypes: []model.Service{model.ServiceFood},
				},
			},
		}
		normal, bundle, _, _ := ots.CalculateOnTopFlexibleFlatRate(ots.Conditions[0])

		require.Equal(tt, 10.0, normal)
		require.Equal(tt, 10.0, bundle)
	})

	t.Run("FlexibleFlatRate should calculate amount correctly", func(tt *testing.T) {
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days: nil,
					Time: nil,
					FlatRatePrices: []model.FlatRatePrice{
						{
							FlatRateType:     model.FlexibleFlatRate,
							NormalAmount:     10,
							BundleAmount:     20,
							NormalCoinAmount: 30,
							BundleCoinAmount: 40,
						},
					},
					ServiceTypes: []model.Service{model.ServiceFood},
				},
			},
		}
		normal, bundle, c, bc := ots.CalculateOnTopFlexibleFlatRate(ots.Conditions[0])

		require.Equal(tt, 10.0, normal)
		require.Equal(tt, 20.0, bundle)
		require.Equal(tt, 30, c)
		require.Equal(tt, 40, bc)
	})

	t.Run("FlexibleFlatRate should calculate amount correctly with multiple prices", func(tt *testing.T) {
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days: nil,
					Time: nil,
					FlatRatePrices: []model.FlatRatePrice{
						{
							FlatRateType:     model.FlexibleFlatRate,
							NormalAmount:     0,
							BundleAmount:     20,
							NormalCoinAmount: 30,
							BundleCoinAmount: 40,
						},
						{
							FlatRateType: model.FlexibleFlatRate,
							NormalAmount: 10,
							BundleAmount: 0,
						},
					},
					ServiceTypes: []model.Service{model.ServiceFood},
				},
			},
		}
		normal, bundle, c, bc := ots.CalculateOnTopFlexibleFlatRate(ots.Conditions[0])

		require.Equal(tt, 0.0, normal)
		require.Equal(tt, 20.0, bundle)
		require.Equal(tt, 30, c)
		require.Equal(tt, 40, bc)
	})
}

func TestCalculateOnTopBasketSizeScheme(t *testing.T) {
	t.Run("Should calculate basket price correct", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 1, To: 3, Amount: types.NewMoney(1)},
			{From: 10, To: 12, Amount: types.NewMoney(4)},
			{From: 7, To: 9, Amount: types.NewMoney(3)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					BasketPrices:   basketPrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}
		price := 10.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				ItemFee: model.ItemFeeSummary{
					PaymentMethod: model.PaymentMethodCash,
					Total:         price,
				},
			}}},
			PriceScheme: priceSchemeRMS,
			PayAtStop:   1,
		}

		actualCash, _ := ots.CalculateOnTopBasketSizeScheme(ots.Conditions[0], q)

		require.Equal(tt, 4.0, actualCash)
	})

	t.Run("Should calculate basket price correct when a price is between a range of scheme", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 1, To: 3, Amount: types.NewMoney(1)},
			{From: 10, To: 12, Amount: types.NewMoney(4)},
			{From: 7, To: 9, Amount: types.NewMoney(3)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					BasketPrices:   basketPrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}
		price := 6.10
		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				ItemFee: model.ItemFeeSummary{
					PaymentMethod: model.PaymentMethodCash,
					Total:         price,
				},
			}}},
			PriceScheme: priceSchemeRMS,
			PayAtStop:   1,
		}

		actualCash, _ := ots.CalculateOnTopBasketSizeScheme(ots.Conditions[0], q)

		require.Equal(tt, 2.0, actualCash)
	})

	t.Run("Should calculate coin amount correctly", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 1, To: 3, CoinAmount: 1},
			{From: 4, To: 6, CoinAmount: 2},
			{From: 7, To: 9, CoinAmount: 3},
			{From: 10, To: 12, CoinAmount: 4},
			{From: 13, To: 15, CoinAmount: 5},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					BasketPrices:   basketPrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}
		price := 10.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				ItemFee: model.ItemFeeSummary{
					PaymentMethod: model.PaymentMethodCash,
					Total:         price,
				},
			}}},
			PriceScheme: priceSchemeRMS,
			PayAtStop:   1,
		}

		actualCash, actualCoin := ots.CalculateOnTopBasketSizeScheme(ots.Conditions[0], q)

		require.Equal(tt, 0.0, actualCash)
		require.Equal(tt, 4, actualCoin)
	})

	t.Run("Should calculate both cash & coin amount correctly", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 1, To: 3, Amount: types.NewMoney(1), CoinAmount: 2},
			{From: 4, To: 6, Amount: types.NewMoney(2), CoinAmount: 4},
			{From: 7, To: 9, Amount: types.NewMoney(3), CoinAmount: 6},
			{From: 10, To: 12, Amount: types.NewMoney(4), CoinAmount: 8},
			{From: 13, To: 15, Amount: types.NewMoney(5), CoinAmount: 10},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					BasketPrices:   basketPrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}
		price := 10.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				ItemFee: model.ItemFeeSummary{
					PaymentMethod: model.PaymentMethodCash,
					Total:         price,
				},
			}}},
			PriceScheme: priceSchemeRMS,
			PayAtStop:   1,
		}

		actualCash, actualCoin := ots.CalculateOnTopBasketSizeScheme(ots.Conditions[0], q)

		require.Equal(tt, 4.0, actualCash)
		require.Equal(tt, 8, actualCoin)
	})

	t.Run("Should get 0 of amount when a price is over a range of scheme", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 1, To: 3, Amount: types.NewMoney(1)},
			{From: 10, To: 12, Amount: types.NewMoney(4)},
			{From: 7, To: 9, Amount: types.NewMoney(3)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					BasketPrices:   basketPrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}
		price := 16.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				ItemFee: model.ItemFeeSummary{
					PaymentMethod: model.PaymentMethodCash,
					Total:         price,
				},
			}}},
			PriceScheme: priceSchemeRMS,
		}

		actualCash, _ := ots.CalculateOnTopBasketSizeScheme(ots.Conditions[0], q)

		require.Equal(tt, 0.0, actualCash)
	})

	t.Run("Should get ontop of amount correctly when payment method is not cash", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 1, To: 3, Amount: types.NewMoney(1)},
			{From: 10, To: 12, Amount: types.NewMoney(4)},
			{From: 7, To: 9, Amount: types.NewMoney(3)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					BasketPrices:   basketPrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}
		price := 11.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				ItemFee: model.ItemFeeSummary{
					PaymentMethod: model.PaymentMethodCreditCard,
					Total:         price,
				},
			}}},
			PriceScheme: priceSchemeRMS,
			PayAtStop:   1,
		}

		actualCash, _ := ots.CalculateOnTopBasketSizeScheme(ots.Conditions[0], q)

		require.Equal(tt, 4.0, actualCash)
	})

	t.Run("Should get ontop correctly when payment method is set.", func(tt *testing.T) {
		testcases := []struct {
			paymentForBasket model.PaymentTypeForBasket
			itemFeePayment   model.PaymentMethod
			itemFeeDiscounts model.DiscountList
			orderOptions     model.OrderOptions
			expected         float64
		}{
			{
				paymentForBasket: model.PaymentTypeForBasket{
					Cash:                 true,
					CashCollection:       false,
					CashAdvanceCoupon:    false,
					CashAdvanceEPayment:  false,
					CashAdvanceQRPayment: false,
					RLP:                  false,
					CreditCard:           false,
					LinemanWallet:        false,
					QRPromptPay:          false,
					WechatPay:            false,
				},
				itemFeePayment: model.PaymentMethodCash,
				expected:       2.0,
			},
			{
				paymentForBasket: model.PaymentTypeForBasket{
					Cash:                 false,
					CashCollection:       false,
					CashAdvanceCoupon:    false,
					CashAdvanceEPayment:  false,
					CashAdvanceQRPayment: false,
					RLP:                  false,
					CreditCard:           true,
					LinemanWallet:        false,
					QRPromptPay:          false,
					WechatPay:            false,
				},
				itemFeePayment: model.PaymentMethodCreditCard,
				expected:       2.0,
			},
			{
				paymentForBasket: model.PaymentTypeForBasket{
					Cash:                 true,
					CashCollection:       false,
					CashAdvanceCoupon:    false,
					CashAdvanceEPayment:  false,
					CashAdvanceQRPayment: false,
					RLP:                  false,
					CreditCard:           false,
					LinemanWallet:        false,
					QRPromptPay:          false,
					WechatPay:            false,
				},
				itemFeePayment: model.PaymentMethodCreditCard,
				expected:       0.0,
			},
			{
				paymentForBasket: model.PaymentTypeForBasket{
					Cash:                 true,
					CashCollection:       false,
					CashAdvanceCoupon:    false,
					CashAdvanceEPayment:  false,
					CashAdvanceQRPayment: false,
					RLP:                  false,
					CreditCard:           false,
					LinemanWallet:        false,
					QRPromptPay:          false,
					WechatPay:            false,
				},
				itemFeePayment: model.PaymentMethodCash,
				orderOptions:   model.OrderOptions{},
				expected:       2.0,
			},
			{
				paymentForBasket: model.PaymentTypeForBasket{
					Cash:                 true,
					CashCollection:       true,
					CashAdvanceCoupon:    false,
					CashAdvanceEPayment:  false,
					CashAdvanceQRPayment: false,
					RLP:                  false,
					CreditCard:           false,
					LinemanWallet:        false,
					QRPromptPay:          false,
					WechatPay:            false,
				},
				itemFeePayment: model.PaymentMethodCash,
				orderOptions:   model.OrderOptions{DriverMoneyFlow: model.FlowCashCollection},
				expected:       2.0,
			},
			{
				paymentForBasket: model.PaymentTypeForBasket{
					Cash:                 true,
					CashCollection:       false,
					CashAdvanceCoupon:    true,
					CashAdvanceEPayment:  false,
					CashAdvanceQRPayment: false,
					RLP:                  false,
					CreditCard:           false,
					LinemanWallet:        false,
					QRPromptPay:          false,
					WechatPay:            false,
				},
				itemFeePayment: model.PaymentMethodCash,
				itemFeeDiscounts: []model.Discount{
					{Type: model.DiscountTypeCouponAdvance, Discount: 5.0},
				},
				orderOptions: model.OrderOptions{DriverMoneyFlow: model.FlowCashAdvancementCoupon},
				expected:     3.0,
			},
			{
				paymentForBasket: model.PaymentTypeForBasket{
					Cash:                 true,
					CashCollection:       false,
					CashAdvanceCoupon:    false,
					CashAdvanceEPayment:  false,
					CashAdvanceQRPayment: true,
					RLP:                  false,
					CreditCard:           false,
					LinemanWallet:        false,
					QRPromptPay:          false,
					WechatPay:            false,
				},
				itemFeePayment: model.PaymentMethodQRPromptPay,
				orderOptions:   model.OrderOptions{DriverMoneyFlow: model.FlowCashAdvancementEpayment},
				expected:       3.0,
			},
			{
				paymentForBasket: model.PaymentTypeForBasket{
					Cash:                 true,
					CashCollection:       false,
					CashAdvanceCoupon:    false,
					CashAdvanceEPayment:  true,
					CashAdvanceQRPayment: false,
					RLP:                  false,
					CreditCard:           false,
					LinemanWallet:        false,
					QRPromptPay:          false,
					WechatPay:            false,
				},
				itemFeePayment: model.PaymentMethodQRPromptPay,
				orderOptions:   model.OrderOptions{DriverMoneyFlow: model.FlowCashAdvancementEpayment},
				expected:       0.0,
			},
		}

		for i, tc := range testcases {
			basketPrices := []model.OntopBasketPrice{
				{From: 1, To: 5, Amount: types.NewMoney(1)},
				{From: 6, To: 10, Amount: types.NewMoney(2)},
				{From: 11, To: 20, Amount: types.NewMoney(3)},
			}

			ots := model.OnTopFare{
				Conditions: []model.OntopCondition{
					{
						Days:             nil,
						Time:             nil,
						FlatRateAmount:   0,
						BasketPrices:     basketPrices,
						PaymentForBasket: tc.paymentForBasket,
						ServiceTypes:     []model.Service{model.ServiceFood},
					},
				},
			}

			q := model.Quote{Routes: []model.Stop{
				{},
				{PriceSummary: model.PriceSummary{
					ItemFee: model.ItemFeeSummary{
						Total:         8.0,
						SubTotal:      13.0,
						PaymentMethod: tc.itemFeePayment,
						Discounts:     tc.itemFeeDiscounts,
					},
				}}},
				Options:     tc.orderOptions,
				PriceScheme: priceSchemeRMS,
				PayAtStop:   1,
			}

			tt.Run(fmt.Sprintf("testcase:%v", i+1), func(ttt *testing.T) {
				actualCash, _ := ots.CalculateOnTopBasketSizeScheme(ots.Conditions[0], q)
				require.Equal(ttt, tc.expected, actualCash)
			})
		}
	})

	t.Run("Should get 0 of amount when restaurant type is not RMS", func(tt *testing.T) {
		basketPrices := []model.OntopBasketPrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 1, To: 3, Amount: types.NewMoney(1)},
			{From: 10, To: 12, Amount: types.NewMoney(4)},
			{From: 7, To: 9, Amount: types.NewMoney(3)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					BasketPrices:   basketPrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}
		price := 16.0
		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				ItemFee: model.ItemFeeSummary{
					PaymentMethod: model.PaymentMethodCreditCard,
					Total:         price,
				},
			}}},
			PayAtStop: 1,
		}

		actualCash, _ := ots.CalculateOnTopBasketSizeScheme(ots.Conditions[0], q)

		require.Equal(tt, 0.0, actualCash)
	})

}

func TestCalculateOnTopDistanceScheme(t *testing.T) {
	t.Run("Should get cash on-top when meet criteria", func(tt *testing.T) {
		distancePrices := []model.OntopDistancePrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 200, To: 500, Amount: types.NewMoney(4)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					DistancePrices: distancePrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, actualCoin := ots.CalculateOnTopDistanceScheme(ots.Conditions[0], types.Distance(400))
		require.Equal(tt, 4.0, actualCash)
		require.Equal(tt, 0, actualCoin)
	})

	t.Run("Should get coin on-top when meet criteria", func(tt *testing.T) {
		distancePrices := []model.OntopDistancePrice{
			{From: 4, To: 6, CoinAmount: 2},
			{From: 200, To: 500, CoinAmount: 4},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					DistancePrices: distancePrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, actualCoin := ots.CalculateOnTopDistanceScheme(ots.Conditions[0], types.Distance(400))
		require.Equal(tt, 0.0, actualCash)
		require.Equal(tt, 4, actualCoin)
	})

	t.Run("Should get cash & coin on-top when meet criteria", func(tt *testing.T) {
		distancePrices := []model.OntopDistancePrice{
			{From: 4, To: 6, Amount: types.NewMoney(2), CoinAmount: 2},
			{From: 200, To: 500, Amount: types.NewMoney(4), CoinAmount: 4},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					DistancePrices: distancePrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, actuaoCoin := ots.CalculateOnTopDistanceScheme(ots.Conditions[0], types.Distance(400))
		require.Equal(tt, 4.0, actualCash)
		require.Equal(tt, 4, actuaoCoin)
	})

	t.Run("Should round distance down and get on-top", func(tt *testing.T) {
		distancePrices := []model.OntopDistancePrice{
			{From: 100, To: 300, Amount: types.NewMoney(2)},
			{From: 301, To: 500, Amount: types.NewMoney(4)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					DistancePrices: distancePrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, _ := ots.CalculateOnTopDistanceScheme(ots.Conditions[0], types.Distance(300.5))
		require.Equal(tt, 2.0, actualCash)
	})

	t.Run("Should not get on-top when doesn't meet criteria", func(tt *testing.T) {
		distancePrices := []model.OntopDistancePrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 200, To: 500, Amount: types.NewMoney(4)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					DistancePrices: distancePrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, _ := ots.CalculateOnTopDistanceScheme(ots.Conditions[0], types.Distance(1000))
		require.Equal(tt, 0.0, actualCash)
	})
}

func TestCalculateOnTopPickupDistanceScheme(t *testing.T) {
	t.Run("Should get cash on-top when meet criteria", func(tt *testing.T) {
		pickupDistancePrices := []model.OntopPickupDistancePrice{
			{From: 4, Amount: types.NewMoney(2)},
			{From: 200, Amount: types.NewMoney(4)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:                 nil,
					Time:                 nil,
					FlatRateAmount:       0,
					PickupDistancePrices: pickupDistancePrices,
					ServiceTypes:         []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, actualCoin := ots.CalculateOnTopPickupDistanceScheme(ots.Conditions[0], types.Distance(400))
		require.Equal(tt, 4.0, actualCash)
		require.Equal(tt, 0, actualCoin)
	})

	t.Run("Should get coin on-top when meet criteria", func(tt *testing.T) {
		pickupDistancePrices := []model.OntopPickupDistancePrice{
			{From: 4, CoinAmount: 2},
			{From: 200, CoinAmount: 4},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:                 nil,
					Time:                 nil,
					FlatRateAmount:       0,
					PickupDistancePrices: pickupDistancePrices,
					ServiceTypes:         []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, actualCoin := ots.CalculateOnTopPickupDistanceScheme(ots.Conditions[0], types.Distance(400))
		require.Equal(tt, 0.0, actualCash)
		require.Equal(tt, 4, actualCoin)
	})

	t.Run("Should get cash & coin on-top when meet criteria", func(tt *testing.T) {
		pickupDistancePrices := []model.OntopPickupDistancePrice{
			{From: 4, Amount: types.NewMoney(2), CoinAmount: 2},
			{From: 200, Amount: types.NewMoney(4), CoinAmount: 4},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:                 nil,
					Time:                 nil,
					FlatRateAmount:       0,
					PickupDistancePrices: pickupDistancePrices,
					ServiceTypes:         []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, actualCoin := ots.CalculateOnTopPickupDistanceScheme(ots.Conditions[0], types.Distance(400))
		require.Equal(tt, 4.0, actualCash)
		require.Equal(tt, 4, actualCoin)
	})

	t.Run("Should round distance down and get on-top", func(tt *testing.T) {
		pickupDistancePrices := []model.OntopPickupDistancePrice{
			{From: 100, Amount: types.NewMoney(2)},
			{From: 301, Amount: types.NewMoney(4)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:                 nil,
					Time:                 nil,
					FlatRateAmount:       0,
					PickupDistancePrices: pickupDistancePrices,
					ServiceTypes:         []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, actualCoin := ots.CalculateOnTopPickupDistanceScheme(ots.Conditions[0], types.Distance(300.5))
		require.Equal(tt, 2.0, actualCash)
		require.Equal(tt, 0, actualCoin)
	})

	t.Run("Should not get on-top when doesn't meet criteria", func(tt *testing.T) {
		pickupDistancePrices := []model.OntopPickupDistancePrice{
			{From: 4, Amount: types.NewMoney(2)},
			{From: 200, Amount: types.NewMoney(4)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:                 nil,
					Time:                 nil,
					FlatRateAmount:       0,
					PickupDistancePrices: pickupDistancePrices,
					ServiceTypes:         []model.Service{model.ServiceFood},
				},
			},
		}

		actualCash, _ := ots.CalculateOnTopPickupDistanceScheme(ots.Conditions[0], types.Distance(1))
		require.Equal(tt, 0.0, actualCash)
	})
}

func TestCalculateOnTopUserFareScheme(t *testing.T) {
	t.Run("calculate user fare price correctly", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 1, To: 3, Amount: types.NewMoney(1)},
			{From: 10, To: 12, Amount: types.NewMoney(4)},
			{From: 7, To: 9, Amount: types.NewMoney(3)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					UserFarePrices: userFarePrices,
					ServiceTypes:   []model.Service{model.ServiceBike},
				},
			},
		}
		price := 10.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				DeliveryFee: model.DeliveryFeeSummary{
					SubTotal: price,
				},
			}}},
			PriceScheme: priceSchemeDefault,
			PayAtStop:   1,
		}

		actualCash, _ := ots.CalculateOnTopUserFareScheme(ots.Conditions[0], q)

		require.Equal(tt, 4.0, actualCash)
	})

	t.Run("calculate user fare price correct when a price is between a range of scheme", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 1, To: 3, Amount: types.NewMoney(1)},
			{From: 10, To: 12, Amount: types.NewMoney(4)},
			{From: 7, To: 9, Amount: types.NewMoney(3)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					UserFarePrices: userFarePrices,
					ServiceTypes:   []model.Service{model.ServiceBike},
				},
			},
		}
		price := 6.10
		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				DeliveryFee: model.DeliveryFeeSummary{
					SubTotal: price,
				},
			}}},
			PriceScheme: priceSchemeDefault,
			PayAtStop:   1,
		}

		actualCash, _ := ots.CalculateOnTopUserFareScheme(ots.Conditions[0], q)

		require.Equal(tt, 2.0, actualCash)
	})

	t.Run("calculate coin amount correctly", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 1, To: 3, CoinAmount: 1},
			{From: 4, To: 6, CoinAmount: 2},
			{From: 7, To: 9, CoinAmount: 3},
			{From: 10, To: 12, CoinAmount: 4},
			{From: 13, To: 15, CoinAmount: 5},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					UserFarePrices: userFarePrices,
					ServiceTypes:   []model.Service{model.ServiceBike},
				},
			},
		}
		price := 10.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				DeliveryFee: model.DeliveryFeeSummary{
					SubTotal: price,
				},
			}}},
			PriceScheme: priceSchemeDefault,
			PayAtStop:   1,
		}

		actualCash, actualCoin := ots.CalculateOnTopUserFareScheme(ots.Conditions[0], q)

		require.Equal(tt, 0.0, actualCash)
		require.Equal(tt, 4, actualCoin)
	})

	t.Run("calculate both cash & coin amount correctly", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 1, To: 3, Amount: types.NewMoney(1), CoinAmount: 2},
			{From: 4, To: 6, Amount: types.NewMoney(2), CoinAmount: 4},
			{From: 7, To: 9, Amount: types.NewMoney(3), CoinAmount: 6},
			{From: 10, To: 12, Amount: types.NewMoney(4), CoinAmount: 8},
			{From: 13, To: 15, Amount: types.NewMoney(5), CoinAmount: 10},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					UserFarePrices: userFarePrices,
					ServiceTypes:   []model.Service{model.ServiceBike},
				},
			},
		}
		price := 10.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				DeliveryFee: model.DeliveryFeeSummary{
					SubTotal: price,
				},
			}}},
			PriceScheme: priceSchemeDefault,
			PayAtStop:   1,
		}

		actualCash, actualCoin := ots.CalculateOnTopUserFareScheme(ots.Conditions[0], q)

		require.Equal(tt, 4.0, actualCash)
		require.Equal(tt, 8, actualCoin)
	})

	t.Run("get 0 of amount when a price is over a range of scheme", func(tt *testing.T) {
		userFarePrices := []model.OntopUserFarePrice{
			{From: 4, To: 6, Amount: types.NewMoney(2)},
			{From: 1, To: 3, Amount: types.NewMoney(1)},
			{From: 10, To: 12, Amount: types.NewMoney(4)},
			{From: 7, To: 9, Amount: types.NewMoney(3)},
			{From: 13, To: 15, Amount: types.NewMoney(5)},
		}
		ots := model.OnTopFare{
			Conditions: []model.OntopCondition{
				{
					Days:           nil,
					Time:           nil,
					FlatRateAmount: 0,
					UserFarePrices: userFarePrices,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
		}
		price := 16.0

		q := model.Quote{Routes: []model.Stop{
			{},
			{PriceSummary: model.PriceSummary{
				DeliveryFee: model.DeliveryFeeSummary{
					SubTotal: price,
				},
			}}},
			PriceScheme: priceSchemeDefault,
			PayAtStop:   1,
		}

		actualCash, _ := ots.CalculateOnTopUserFareScheme(ots.Conditions[0], q)

		require.Equal(tt, 0.0, actualCash)
	})

}

func TestOnTopFare_CalculateOnTopAmount(t *testing.T) {
	t.Run("return 0.0 if status inactive ", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusInactive,
		}
		amount, bundleAmount, _, _, _, _ := ot.CalculateOnTopAmount(timeutil.BangkokNow(), model.Quote{ServiceType: model.ServiceFood, PriceScheme: priceSchemeRMS}, 0, 0, false)
		require.Equal(t, 0.0, amount)
		require.Equal(t, 0.0, bundleAmount)
	})

	t.Run("flat rate scheme return valid amount if order is in condition", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRateAmount: 20,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// MONDAY
		orderTime := time.Date(2021, 7, 5, 21, 0, 0, 0, timeutil.BangkokLocation())
		amount, bundleAmount, _, _, _, _ := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, float64(20), amount)
		require.Equal(t, float64(20), bundleAmount)
	})

	t.Run("flat rate scheme return 0.0 if order is not in time", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRateAmount: 20,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// MONDAY
		orderTime := time.Date(2021, 7, 5, 23, 0, 0, 0, timeutil.BangkokLocation())
		amount, bundleAmount, _, _, _, _ := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, 0.0, amount)
		require.Equal(t, 0.0, bundleAmount)
	})

	t.Run("flat rate scheme return 0.0 if order is not in days", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRateAmount: 20,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// THURSDAY
		orderTime := time.Date(2021, 7, 8, 21, 0, 0, 0, timeutil.BangkokLocation())
		amount, bundleAmount, _, _, _, _ := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, 0.0, amount)
		require.Equal(t, 0.0, bundleAmount)
	})

	t.Run("flat rate scheme return 0.0 if order is not in active time", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI", "SAT"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRateAmount: 20,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
			Region:           "BKK",
			EnableActiveTime: true,
			StartTime:        time.Date(2022, 4, 1, 0, 0, 0, 0, timeutil.BangkokLocation()),
			EndTime:          time.Date(2022, 4, 8, 0, 0, 0, 0, timeutil.BangkokLocation()),
		}

		// SAT
		orderTime := time.Date(2022, 4, 9, 21, 0, 0, 0, timeutil.BangkokLocation())
		amount, bundleAmount, _, _, _, _ := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, 0.0, amount)
		require.Equal(t, 0.0, bundleAmount)
	})

	t.Run("flat rate amount return valid amount when conditions more than one", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRateAmount: 20,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
				{
					Status: model.StatusActive,
					Days:   []model.Days{"TUE", "WED"},
					Time: []model.StartEndTime{
						{
							Begin: "10:00:00",
							End:   "11:00:00",
						},
					},
					FlatRateAmount: 30,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// FRIDAY
		orderTime := time.Date(2021, 7, 9, 20, 30, 0, 0, timeutil.BangkokLocation())
		amount, bundleAmount, _, _, _, _ := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, 20.00, amount)
		require.Equal(t, 20.00, bundleAmount)
	})

	t.Run("flat rate amount return valid amount when some condition is inactive", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusInactive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRateAmount: 20,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRateAmount: 30,
					ServiceTypes:   []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// FRIDAY
		orderTime := time.Date(2021, 7, 9, 20, 30, 0, 0, timeutil.BangkokLocation())
		amount, bundleAmount, _, _, _, _ := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, 30.00, amount)
		require.Equal(t, 30.00, bundleAmount)
	})

	t.Run("flexible flat rate amount return valid amount", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlexibleFlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRatePrices: []model.FlatRatePrice{
						{FlatRateType: model.FlexibleFlatRate, NormalAmount: 20, BundleAmount: 30},
					},
					ServiceTypes: []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// FRIDAY
		orderTime := time.Date(2021, 7, 9, 20, 30, 0, 0, timeutil.BangkokLocation())
		amount, bundleAmount, _, _, _, _ := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, 20.00, amount)
		require.Equal(t, 30.00, bundleAmount)
	})

	t.Run("default flat rate amount return valid coin", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlexibleFlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRatePrices: []model.FlatRatePrice{
						{FlatRateType: model.DefaultFlatRate, NormalCoinAmount: 30, BundleCoinAmount: 40},
					},
					ServiceTypes: []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// FRIDAY
		orderTime := time.Date(2021, 7, 9, 20, 30, 0, 0, timeutil.BangkokLocation())
		_, _, _, _, coin, bundleCoin := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, 30, coin)
		// default flat rate will fall back to use normal coin
		require.Equal(t, 30, bundleCoin)

	})

	t.Run("flexible flat rate amount return valid coin", func(t *testing.T) {
		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.FlexibleFlatRateScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"MON", "FRI"},
					Time: []model.StartEndTime{
						{
							Begin: "20:00:00",
							End:   "22:00:00",
						},
					},
					FlatRatePrices: []model.FlatRatePrice{
						{FlatRateType: model.FlexibleFlatRate, NormalCoinAmount: 30, BundleCoinAmount: 40},
					},
					ServiceTypes: []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// FRIDAY
		orderTime := time.Date(2021, 7, 9, 20, 30, 0, 0, timeutil.BangkokLocation())
		_, _, _, _, coin, bundleCoin := ot.CalculateOnTopAmount(orderTime, dummyQuote, 0, 0, false)
		require.Equal(t, 30, coin)
		require.Equal(t, 40, bundleCoin)

	})

	t.Run("pickup distance amount return correct", func(t *testing.T) {
		// 2024-02-14 01:30 UTC+7 WED
		date := time.Date(2024, 2, 14, 01, 30, 0, 0, timeutil.BangkokLocation())
		timeutils.FreezeWithTime(date.Unix() * 1000)
		defer timeutils.Unfreeze()

		ot := model.OnTopFare{
			Status: model.StatusActive,
			Scheme: model.PickupDistanceScheme,
			Conditions: []model.OntopCondition{
				{
					Status: model.StatusActive,
					Days:   []model.Days{"WED"},
					Time: []model.StartEndTime{
						{
							Begin: "00:00:00",
							End:   "04:59:00",
						},
					},
					PickupDistancePrices: []model.OntopPickupDistancePrice{
						{From: 5, Amount: 30},
					},
					ServiceTypes: []model.Service{model.ServiceFood},
				},
			},
			Region: "BKK",
		}

		// 2024-02-13 18:30 TUE UTC
		// 2024-02-14 01:30 WED UTC+7
		orderTime := time.Date(2024, 2, 13, 18, 30, 0, 0, time.UTC)
		amt, _, _, _, _, _ := ot.CalculateOnTopAmount(orderTime, dummyQuote, 6, 0, false)
		require.Equal(t, 30.0, amt)
	})

}

func TestValidateAllowedServices(t *testing.T) {
	type args struct {
		services        []model.Service
		allowedServices []string
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "partial contain",
			args: args{
				services:        []model.Service{model.ServiceFood},
				allowedServices: []string{model.ServiceFood.String(), model.ServiceMart.String()},
			},
			want: true,
		},
		{
			name: "contain all",
			args: args{
				services:        []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				allowedServices: []string{model.ServiceFood.String(), model.ServiceMart.String(), model.ServiceMessenger.String()},
			},
			want: true,
		},
		{
			name: "contain not allow svc",
			args: args{
				services:        []model.Service{model.ServiceFood, model.ServiceMart, model.ServiceMessenger},
				allowedServices: []string{model.ServiceFood.String(), model.ServiceMart.String()},
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := model.ValidateAllowedServices(tt.args.services, tt.args.allowedServices); got != tt.want {
				t.Errorf("ValidateAllowedServices() = %v, want %v", got, tt.want)
			}
		})
	}
}
