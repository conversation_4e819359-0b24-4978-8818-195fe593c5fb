package model

import (
	"time"

	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

// DriverStatistic contains a driver's temporarily statistic which is generally used for scoring driver.
type DriverStatistic struct {
	ID              primitive.ObjectID             `bson:"_id,omitempty"`
	DriverID        string                         `bson:"driver_id"`
	WeeklyStatistic map[string]AcceptanceStatistic `bson:"weekly"`
	DailyStatistic  map[string]DailyStatistic      `bson:"daily"`
}

// AddNotified adds number of notified at the given time
func (ds *DriverStatistic) AddNotified(t time.Time) {
	ds.addNotifiedWeek(t)
	ds.addNotifiedDay(t)
}

func (ds *DriverStatistic) addNotifiedWeek(t time.Time) {
	weekly := ds.getAcceptanceStatistic(t)
	weekly.AddNotified()

	ds.WeeklyStatistic[weekly.Key] = weekly
}

func (ds *DriverStatistic) getAcceptanceStatistic(t time.Time) AcceptanceStatistic {
	week := timeutil.ToWeekNumber(t)

	if ds.WeeklyStatistic == nil {
		ds.WeeklyStatistic = make(map[string]AcceptanceStatistic)
	}
	s, ok := ds.WeeklyStatistic[week]
	if !ok {
		s = AcceptanceStatistic{
			DriverID: ds.DriverID,
			Key:      week,
		}
	}

	return s
}

func (ds *DriverStatistic) addNotifiedDay(t time.Time) {
	daily := ds.getDailyStatistic(t)
	daily.AddNotified()

	ds.DailyStatistic[daily.Key] = daily
}

func (ds *DriverStatistic) getDailyStatistic(t time.Time) DailyStatistic {
	day := timeutil.ToYYYYMMDD(t)

	if ds.DailyStatistic == nil {
		ds.DailyStatistic = make(map[string]DailyStatistic)
	}
	d, ok := ds.DailyStatistic[day]
	if !ok {
		d = DailyStatistic{
			AcceptanceStatistic: AcceptanceStatistic{
				DriverID: ds.DriverID,
				Key:      day,
			},
		}
	}

	return d
}

// AddDone adds number of done at the given time
func (ds *DriverStatistic) AddDone(t time.Time, autoAssigned bool) {
	ds.addDoneWeek(t, autoAssigned)
	ds.addDoneDay(t, autoAssigned)
}

func (ds *DriverStatistic) addDoneWeek(t time.Time, autoAssigned bool) {
	weekly := ds.getAcceptanceStatistic(t)
	weekly.AddDone(autoAssigned)

	ds.WeeklyStatistic[weekly.Key] = weekly
}

func (ds *DriverStatistic) addDoneDay(t time.Time, autoAssigned bool) {
	daily := ds.getDailyStatistic(t)
	daily.AddDone(autoAssigned)

	ds.DailyStatistic[daily.Key] = daily
}

func (ds *DriverStatistic) UpdateDailyNextIncentive(t time.Time, nextIncentive int64) {
	daily := ds.getDailyStatistic(t)
	daily.OrdersToNextIncentive = nextIncentive

	ds.DailyStatistic[daily.Key] = daily
}

func (ds *DriverStatistic) CleanUp() {
	ds.cleanUpWeekly()
	ds.cleanUpDaily()
}

func (ds *DriverStatistic) cleanUpWeekly() {
	week := timeutil.ToWeekNumber(timeutil.BangkokNow())

	s, ok := ds.WeeklyStatistic[week]
	if ok {
		ds.WeeklyStatistic = map[string]AcceptanceStatistic{week: s}
	} else {
		ds.WeeklyStatistic = map[string]AcceptanceStatistic{}
	}
}

func (ds *DriverStatistic) cleanUpDaily() {
	day := timeutil.ToYYYYMMDD(timeutil.BangkokNow())

	s, ok := ds.DailyStatistic[day]
	if ok {
		ds.DailyStatistic = map[string]DailyStatistic{day: s}
	} else {
		ds.DailyStatistic = map[string]DailyStatistic{}
	}
}

// AcceptanceStatistic holds order acceptance statistic.
type AcceptanceStatistic struct {
	DriverID         string    `bson:"driver_id"`
	Key              string    `bson:"key"`
	Notified         int64     `bson:"notified"`
	Done             int64     `bson:"done"`
	DoneAutoAssigned int64     `bson:"done_a"`
	Rate             float64   `bson:"rate"`
	LastNotifiedTime time.Time `bson:"lastNotifiedTime"`
}

// AddNotified counts notified up and calculates acceptance rate
func (s *AcceptanceStatistic) AddNotified() {
	s.Notified = s.Notified + 1
	s.LastNotifiedTime = timeutil.BangkokNow()
	s.UpdateRate()
}

// UpdateRate calculates acceptance rate
func (s *AcceptanceStatistic) UpdateRate() {
	if s.Notified == 0 {
		s.Rate = 0
		return
	}
	if s.Notified < s.DoneAutoAssigned {
		logrus.Warnf("driverID=%s key=%s notified=%v doneAutoAssigned=%v there should be some mistakes, notified count is less than done count.", s.DriverID, s.Key, s.Notified, s.DoneAutoAssigned)
		s.Rate = 1
		return
	}
	s.Rate = float64(s.DoneAutoAssigned) / float64(s.Notified)
}

// AddDone counts done up and calculates acceptance rate
func (s *AcceptanceStatistic) AddDone(autoAssigned bool) {
	s.Done = s.Done + 1
	if autoAssigned {
		s.DoneAutoAssigned = s.DoneAutoAssigned + 1
		s.UpdateRate()
	}
}

// NewDriverStatistic creates new instance of DriverStatistic
func NewDriverStatistic(driverID string) *DriverStatistic {
	return &DriverStatistic{
		DriverID: driverID,
	}
}

type DailyStatistic struct {
	AcceptanceStatistic   `bson:",inline"`
	OrdersToNextIncentive int64 `bson:"to_incentive"`
}
