package model

import "fmt"

var _ fmt.Stringer = (*DriverLastUpdateLocationAttempt)(nil)

type DriverLastUpdateLocationAttempts []DriverLastUpdateLocationAttempt

func (d DriverLastUpdateLocationAttempts) DriverIDs() []string {
	ids := make([]string, len(d))
	for idx, v := range d {
		ids[idx] = v.DriverID
	}

	return ids
}

type DriverLastUpdateLocationAttempt struct {
	DriverID  string
	Timestamp int64
}

func (d DriverLastUpdateLocationAttempt) String() string {
	return fmt.Sprintf("DriverLastUpdateLocationAttempt[DriverID:%v,Timestamp:%v]", d.DriverID, d.Timestamp)
}
