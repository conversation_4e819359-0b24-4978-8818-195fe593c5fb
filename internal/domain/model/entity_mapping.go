package model

import (
	"time"
)

type EntityMapping struct {
	CreatedAt time.Time `bson:"created_at"`
	UpdatedAt time.Time `bson:"updated_at"`
	Version   uint      `bson:"version"`
}

func NewEntityMapping(entity Entity) EntityMapping {
	return EntityMapping{
		CreatedAt: entity.CreatedAt(),
		UpdatedAt: entity.UpdatedAt(),
		Version:   entity.Version(),
	}
}

func (e *EntityMapping) Entity(id string) Entity {
	return &BasicEntity{
		id:        id,
		createdAt: e.CreatedAt,
		updatedAt: e.UpdatedAt,
		version:   e.Version,
	}
}
