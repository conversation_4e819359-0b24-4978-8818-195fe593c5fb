package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

const DefaultH3PartitioningResolution int = 7

type ThrottledDispatchDetail struct {
	ZoneID   primitive.ObjectID `bson:"zone_id"`
	Interval time.Duration      `bson:"interval"` // interval between each batch
	Enabled  bool               `bson:"enabled"`

	EnabledSearchRadiusOffset bool    `bson:"enabled_search_radius_offset"`
	SearchRadiusOffsetKM      float64 `bson:"search_radius_offset_km"`

	EnableOverrideMatchRestaurantFirstMRThreshold bool    `bson:"enable_override_match_restaurant_first_mr_threshold"`
	MatchRestaurantFirstMRThreshold               float64 `bson:"match_restaurant_first_mr_threshold"`

	EnabledH3Partitioning        bool   `bson:"enabled_h3_partitioning"`
	H3PartitioningResolution     *int   `bson:"h3_partitioning_resolution,omitempty"`
	MultipleOrderAggressiveLevel string `bson:"multiple_order_aggressive_level,omitempty"`
	EnabledMedianSplit           bool   `bson:"enabled_median_split"`

	CreatedAt time.Time `bson:"created_at"`
	UpdatedAt time.Time `bson:"updated_at"`
}

func (detail *ThrottledDispatchDetail) GetSearchRadiusOffsetInMeter() float64 {
	return detail.SearchRadiusOffsetKM * 1000
}

func (detail ThrottledDispatchDetail) GetH3PartitioningResolution() int {
	if detail.H3PartitioningResolution == nil {
		return DefaultH3PartitioningResolution
	}
	// maximum h3 resolution is 15
	if *detail.H3PartitioningResolution > 15 {
		return 15
	}
	return *detail.H3PartitioningResolution
}

type ThrottledDispatchDetailWithZoneCode struct {
	ThrottledDispatchDetail `bson:",inline"`
	ZoneCode                string `bson:"zone_code"`
}

type ThrottledDispatchDetailQuery struct {
	ZoneIDs []string
}

func (q ThrottledDispatchDetailQuery) Query() bson.M {
	query := bson.M{}
	if q.ZoneIDs != nil {
		query["zone_id"] = bson.M{
			"$in": q.ZoneIDs,
		}
	}
	return query
}
