package model

import (
	"encoding/json"
	"fmt"
	"reflect"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"go.mongodb.org/mongo-driver/bson"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	xgotimeutil "git.wndv.co/lineman/xgo/timeutil"
)

func TestPriceSummary_Sum(t *testing.T) {
	t.Parallel()

	testcases := []struct {
		testcase string

		itemFee       float64
		itemDiscounts []Discount

		baseFee              float64
		roadFee              float64
		addFee               map[string]float64
		deliveryFeeDiscounts []Discount

		expectedSubTotalDelivery float64
		expectedTotalDelivery    float64
		expectedTotalPrice       float64
	}{
		{
			testcase:                 "Calculate total price without any discount",
			itemFee:                  120.0,
			itemDiscounts:            []Discount{},
			baseFee:                  55,
			roadFee:                  27,
			addFee:                   map[string]float64{},
			deliveryFeeDiscounts:     []Discount{},
			expectedSubTotalDelivery: 82.0,
			expectedTotalDelivery:    82.0,
			expectedTotalPrice:       202.0,
		},
		{
			testcase:      "Calculate total price with coupon discount",
			itemFee:       120.0,
			itemDiscounts: NewDiscountList(),
			baseFee:       50,
			roadFee:       20,
			addFee:        map[string]float64{},
			deliveryFeeDiscounts: []Discount{
				{Type: DiscountTypeCoupon, Code: "DISCOUNT50", Discount: 50},
			},
			expectedSubTotalDelivery: 70.0,
			expectedTotalDelivery:    20.0,
			expectedTotalPrice:       140.0,
		},
		{
			testcase:      "Calculate total price with gp discount",
			itemFee:       120.0,
			itemDiscounts: []Discount{},
			baseFee:       55,
			roadFee:       27,
			addFee: map[string]float64{
				"NIGHT": 40,
			},
			deliveryFeeDiscounts: []Discount{
				{Type: DiscountTypeSubsidize, Code: "GP", Discount: 112},
			},
			expectedSubTotalDelivery: 122.0,
			expectedTotalDelivery:    10.0,
			expectedTotalPrice:       130.0,
		},
		{
			testcase:      "Calculate total price with both coupon and gp discount",
			itemFee:       120.0,
			itemDiscounts: []Discount{},
			baseFee:       55,
			roadFee:       27,
			addFee: map[string]float64{
				"NIGHT": 40,
			},
			deliveryFeeDiscounts: []Discount{
				{Type: DiscountTypeCoupon, Code: "DISCOUNT50", Discount: 50},
				{Type: DiscountTypeSubsidize, Code: "GP", Discount: 62},
			},
			expectedSubTotalDelivery: 122.0,
			expectedTotalDelivery:    10,
			expectedTotalPrice:       130.0,
		},
	}

	for _, tc := range testcases {
		deliveryFee := NewDeliveryFeeSummary(PaymentMethodCash, tc.baseFee, tc.roadFee)
		deliveryFee.Discounts = tc.deliveryFeeDiscounts
		deliveryFee.AdditionalFee = tc.addFee

		itemFee := NewItemFeeSummary(PaymentMethodCash, tc.itemFee, types.NewFloat64(200.0))
		itemFee.Discounts = tc.itemDiscounts

		actual := PriceSummary{
			DeliveryFee: *deliveryFee,
			ItemFee:     *itemFee,
			Total:       0,
		}
		actual = actual.Sum()
		require.Equal(t, tc.expectedSubTotalDelivery, actual.DeliveryFee.SubTotal, tc.testcase)
		require.Equal(t, tc.expectedTotalDelivery, actual.DeliveryFee.Total, tc.testcase)
		require.Equal(t, tc.expectedTotalPrice, actual.Total, tc.testcase)
	}
}

func TestDeliveryFeeSummary(t *testing.T) {
	t.Parallel()
	t.Run("CommissionSummary", func(tt *testing.T) {
		tt.Run("Should return 2 decimal point", func(ttt *testing.T) {
			summary := DeliveryFeeSummary{
				RawBaseFee:    50.7,
				RoadFee:       15,
				AdditionalFee: nil,
				Discounts:     []Discount{},
				SubTotal:      65.7,
				Total:         65.7,
			}

			deduction := summary.CalculateCommission(0.15, 0.03)

			require.Equal(ttt, 9.86, deduction.Commission)
			require.Equal(ttt, 1.68, deduction.WithholdingTax)
		})

		tt.Run("Should calculate by subTotal", func(ttt *testing.T) {
			summary := DeliveryFeeSummary{
				RawBaseFee:    50.7,
				RoadFee:       15,
				AdditionalFee: nil,
				Discounts: []Discount{
					{Type: DiscountTypeCoupon, Code: "TEST30", Discount: 30},
				},
				SubTotal: 65.7,
				Total:    35.7,
			}

			deduction := summary.CalculateCommission(0.15, 0.03)

			require.Equal(ttt, 9.86, deduction.Commission)
			require.Equal(ttt, 1.68, deduction.WithholdingTax)
		})
	})
}

func TestOrder_Expired(t *testing.T) {
	t.Parallel()

	ord := &Order{}
	ord.History = make(map[string]time.Time)
	ord.SetStatus(StatusAssigningDriver)
	ord.ExpireAt = time.Now().Add(-5 * time.Minute)
	require.True(t, ord.Expired())

	ord = &Order{}
	ord.History = make(map[string]time.Time)
	ord.SetStatus(StatusCanceled)
	ord.ExpireAt = time.Now().Add(-5 * time.Minute)
	require.False(t, ord.Expired(), "status CANCELED cannot be expired")

	ord = &Order{}
	ord.History = make(map[string]time.Time)
	ord.SetStatus(StatusCompleted)
	ord.ExpireAt = time.Now().Add(-5 * time.Minute)
	require.False(t, ord.Expired(), "status COMPLETED cannot be expired")
}

func TestQuote_SetPayAtStop(t *testing.T) {
	quote := Quote{
		Routes: []Stop{
			{
				PriceSummary: PriceSummary{
					DeliveryFee: DeliveryFeeSummary{
						PaymentMethod: PaymentMethodCash,
					},
				},
				CollectPayment: true,
			},
			{
				PriceSummary:   PriceSummary{},
				CollectPayment: false,
			},
		},
		PayAtStop: 0,
	}

	testcases := []struct {
		name          string
		newPayAtStop  int
		expectedError error
		expectedQuote Quote
	}{
		{
			name:          "should not set payAtStop to negative value",
			newPayAtStop:  -1,
			expectedError: ErrStopIndexOutOfBound,
		},
		{
			name:          "should not set payAtStop to out-of-bound index",
			newPayAtStop:  2,
			expectedError: ErrStopIndexOutOfBound,
		},
		{
			name:         "should set payAtStop, price summary and collect payment",
			newPayAtStop: 1,
			expectedQuote: Quote{
				Routes: []Stop{
					{
						PriceSummary:   PriceSummary{},
						CollectPayment: false,
					},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								PaymentMethod: PaymentMethodCash,
							},
						},
						CollectPayment: true,
					},
				},
				PayAtStop: 1,
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			err := quote.SetPayAtStop(tc.newPayAtStop)
			require.Equal(tt, tc.expectedError, err, tc.name)
			if tc.expectedError == nil {
				require.Equal(tt, tc.expectedQuote, quote, tc.name)
			}
		})
	}
}

type completedTxn struct {
	category TransactionCategory
	txnType  TransactionType
	amount   float64
}

func TestFoodOrder_ChangeDeliveryFeePaymentMethod(t *testing.T) {
	order := Order{
		Quote: Quote{
			Routes: []Stop{
				{
					PriceSummary: PriceSummary{},
				},
				{
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodRLP,
						},
					},
				},
			},
		},
	}

	testcases := []struct {
		name                 string
		expect               error
		newMethod            PaymentMethod
		newItemFeeMethod     PaymentMethod
		newDeliveryFeeMethod PaymentMethod
		order                func() *Order
		postCheck            func(tt *testing.T, src *Order)
	}{
		{
			name:      "should change delivery payment method  from `RLP` to `CASH",
			expect:    nil,
			newMethod: PaymentMethodCash,
			order: func() *Order {
				order.Quote.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = PaymentMethodRLP
				return &order
			},
		},
		{
			name:      "Should not change delivery fee payment method from `CASH` to `RLP`",
			expect:    ErrInvalidPaymentMethod,
			newMethod: PaymentMethodRLP,
			order: func() *Order {
				order.Quote.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = PaymentMethodCash
				return &order
			},
		},
		{
			name:      "Should not change delivery fee payment method from `QR` to `RLP`",
			expect:    ErrInvalidPaymentMethod,
			newMethod: PaymentMethodRLP,
			order: func() *Order {
				order.Quote.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = PaymentMethodQRPromptPay
				return &order
			},
		},
		{
			name:      "Should not change item fee payment method from `QR` to `CASH`",
			expect:    nil,
			newMethod: PaymentMethodCash,
			order: func() *Order {
				order.PayAtStop = 1
				order.Quote.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = PaymentMethodQRPromptPay
				order.Quote.Routes[1].PriceSummary.ItemFee.PaymentMethod = PaymentMethodQRPromptPay
				return &order
			},
			postCheck: func(tt *testing.T, src *Order) {
				priceSummaryAtStop := src.Routes[src.PayAtStop].PriceSummary
				require.Equal(tt, PaymentMethodCash, priceSummaryAtStop.DeliveryFee.PaymentMethod)
				require.Equal(tt, PaymentMethodQRPromptPay, priceSummaryAtStop.ItemFee.PaymentMethod)
			},
		},
		{
			name:                 "Should change both delivery fee and item fee payment method from `QR` to `CASH`",
			expect:               nil,
			newMethod:            PaymentMethodCash,
			newItemFeeMethod:     PaymentMethodCash,
			newDeliveryFeeMethod: PaymentMethodCash,
			order: func() *Order {
				order.PayAtStop = 1
				order.Quote.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = PaymentMethodQRPromptPay
				order.Quote.Routes[1].PriceSummary.ItemFee.PaymentMethod = PaymentMethodQRPromptPay
				return &order
			},
			postCheck: func(tt *testing.T, src *Order) {
				priceSummaryAtStop := src.Routes[src.PayAtStop].PriceSummary
				require.Equal(tt, PaymentMethodCash, priceSummaryAtStop.DeliveryFee.PaymentMethod)
				require.Equal(tt, PaymentMethodCash, priceSummaryAtStop.ItemFee.PaymentMethod)
			},
		},
		{
			name:                 "Should change both delivery fee and item fee payment method from `CASH` to `QR`",
			expect:               nil,
			newMethod:            PaymentMethodQRPromptPay,
			newItemFeeMethod:     PaymentMethodQRPromptPay,
			newDeliveryFeeMethod: PaymentMethodQRPromptPay,
			order: func() *Order {
				order.PayAtStop = 1
				order.Quote.Routes[1].PriceSummary.DeliveryFee.PaymentMethod = PaymentMethodCash
				order.Quote.Routes[1].PriceSummary.ItemFee.PaymentMethod = PaymentMethodCash
				return &order
			},
			postCheck: func(tt *testing.T, src *Order) {
				priceSummaryAtStop := src.Routes[src.PayAtStop].PriceSummary
				require.Equal(tt, PaymentMethodQRPromptPay, priceSummaryAtStop.DeliveryFee.PaymentMethod)
				require.Equal(tt, PaymentMethodQRPromptPay, priceSummaryAtStop.ItemFee.PaymentMethod)
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			actualOrder := tc.order()
			err := actualOrder.ChangeDeliveryFeePaymentMethod(tc.newMethod, tc.newItemFeeMethod, tc.newDeliveryFeeMethod)
			require.Equal(tt, tc.expect, err, tc.name)
			if tc.postCheck != nil {
				tc.postCheck(tt, actualOrder)
			}
		})
	}
}

func TestFoodNewQuoteDeliveryFeeEPayment(t *testing.T) {
	testCases := []struct {
		name   string
		expect error
		quote  func() *Quote
	}{
		{
			name:   "validate error `ErrInvalidCouponType` when delivery fee contain coupon advance discount",
			expect: ErrInvalidCouponType,
			quote: func() *Quote {
				return &Quote{
					ServiceType: ServiceFood,
					Routes: []Stop{
						{
							ID: "x1",
						},
						{
							ID: "x2",
							PriceSummary: PriceSummary{
								DeliveryFee: DeliveryFeeSummary{
									PaymentMethod: PaymentMethodRLP,
									Discounts: []Discount{
										{
											Type: DiscountTypeCouponAdvance,
										},
									},
								},
							},
						},
					},
				}
			},
		},
		{
			name:   "validate error `ErrInvalidCouponType` when item fee contain coupon advance discount",
			expect: ErrInvalidCouponType,
			quote: func() *Quote {
				return &Quote{
					ServiceType: ServiceFood,
					Routes: []Stop{
						{
							ID: "x1",
						},
						{
							ID: "x2",
							PriceSummary: PriceSummary{
								ItemFee: ItemFeeSummary{
									PaymentMethod: PaymentMethodRLP,
									Discounts: []Discount{
										{
											Type: DiscountTypeCouponAdvance,
										},
									},
								},
								DeliveryFee: DeliveryFeeSummary{
									PaymentMethod: PaymentMethodRLP,
									Discounts:     []Discount{},
								},
							},
						},
					},
				}
			},
		},
		{
			name:   "validate error `nil` when item fee contain coupon advance discount",
			expect: nil,
			quote: func() *Quote {
				return &Quote{
					ServiceType: ServiceFood,
					Routes: []Stop{
						{
							ID: "x1",
						},
						{
							ID: "x2",
							PriceSummary: PriceSummary{
								ItemFee: ItemFeeSummary{
									PaymentMethod: PaymentMethodRLP,
									Discounts: []Discount{
										{
											Type: DiscountTypeCoupon,
										},
									},
								},
								DeliveryFee: DeliveryFeeSummary{
									PaymentMethod: PaymentMethodRLP,
									Discounts: []Discount{
										{
											Type: DiscountTypeCoupon,
										},
									},
								},
							},
						},
					},
				}
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			q := tc.quote()
			err := q.validate()
			require.Equal(tt, tc.expect, err, tc.name)
		})
	}
}

func TestOrder_GetMockDriver(t *testing.T) {
	createOrderWithMemo := func(mock, noteToDriver string, deliveringRound int, service Service) Order {
		return Order{
			Quote: Quote{
				NoteToDriver: noteToDriver,
				ServiceType:  service,
				Routes: []Stop{
					{},
					{
						Memo: mock,
					},
				},
			},
			DeliveringRound: deliveringRound,
		}
	}

	testCases := []struct {
		name            string
		order           Order
		expectMockFound bool
		expectDriverID  string
	}{
		{
			name:            "no memo",
			order:           createOrderWithMemo("", "", 0, ServiceFood),
			expectMockFound: false,
			expectDriverID:  "",
		},
		{
			name:            "random memo",
			order:           createOrderWithMemo("blackpink in your area", "", 0, ServiceFood),
			expectMockFound: false,
			expectDriverID:  "",
		},
		{
			name:            "memo with 1 mock, delivering round = 0",
			order:           createOrderWithMemo("mock p1d1 LMDKEY", "", 0, ServiceFood),
			expectMockFound: true,
			expectDriverID:  "LMDKEY",
		},
		{
			name:            "memo with 2 mocks, delivering round = 1",
			order:           createOrderWithMemo("mock p1d1 LMDKEY,mock p1d1 LMDKEY2", "", 1, ServiceFood),
			expectMockFound: true,
			expectDriverID:  "LMDKEY2",
		},
		{
			name:            "memo with 2 mocks with space, delivering round = 1",
			order:           createOrderWithMemo("mock p1d1 LMDKEY, mock p1d1 LMDKEY2", "", 1, ServiceFood),
			expectMockFound: true,
			expectDriverID:  "LMDKEY2",
		},
		{
			name:            "memo with 1 mock, delivering round = 1",
			order:           createOrderWithMemo("mock p1d1 LMDKEY", "", 1, ServiceFood),
			expectMockFound: false,
			expectDriverID:  "",
		},
		{
			name:            "bike not to driver with 1 mock, delivering round = 1",
			order:           createOrderWithMemo("", "mock p1d1 LMDKEYBIKE", 1, ServiceBike),
			expectMockFound: true,
			expectDriverID:  "LMDKEYBIKE",
		},
		{
			name:            "memo with 1 mock and more than 3 args",
			order:           createOrderWithMemo("mock p1d1 LMDKEY test", "", 0, ServiceFood),
			expectMockFound: true,
			expectDriverID:  "LMDKEY",
		},
		{
			name:            "memo with 2 mocks with space and  more than 3 args, delivering round = 1",
			order:           createOrderWithMemo("mock p1d1 LMDKEY test1, mock p1d1 LMDKEY2 test2", "", 1, ServiceFood),
			expectMockFound: true,
			expectDriverID:  "LMDKEY2",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			mockFound, mockDriverID := tc.order.GetMockDriver()
			require.Equal(tt, tc.expectMockFound, mockFound)
			require.Equal(tt, tc.expectDriverID, mockDriverID)
		})
	}
}

func TestOrder_PriceSummary(t *testing.T) {
	stop1 := Stop{ID: "stop1", PriceSummary: PriceSummary{
		Total: 3000,
	}}
	stop2 := Stop{ID: "stop2", PriceSummary: PriceSummary{
		Total: 5000,
	}}

	t.Run("missing stop2 when service food must not panic", func(t *testing.T) {
		o1 := Order{
			Quote: Quote{
				ServiceType: ServiceFood,
				Routes:      []Stop{stop1},
			},
		}
		require.Equal(t, ImpossibleToPayPriceSummary, o1.PriceSummary())
	})
	t.Run("return stop2 if service is food", func(t *testing.T) {
		o2 := Order{
			Quote: Quote{
				ServiceType: ServiceFood,
				Routes:      []Stop{stop1, stop2},
			},
		}
		require.Equal(t, stop2.PriceSummary, o2.PriceSummary())
	})
	t.Run("return stop1 if specify pay at stop for messenger", func(t *testing.T) {
		o2 := Order{
			Quote: Quote{
				ServiceType: ServiceMessenger,
				Routes:      []Stop{stop1, stop2},
				PayAtStop:   0,
			},
		}
		require.Equal(t, stop1.PriceSummary, o2.PriceSummary())
	})
	t.Run("return stop2 if specify pay at stop for messenger", func(t *testing.T) {
		o2 := Order{
			Quote: Quote{
				ServiceType: ServiceMessenger,
				Routes:      []Stop{stop1, stop2},
				PayAtStop:   1,
			},
		}
		require.Equal(t, stop2.PriceSummary, o2.PriceSummary())
	})
}

func TestOrder_Complete(t *testing.T) {
	t.Run("success complete should set commission and withholding", func(tt *testing.T) {
		from := Stop{}
		to := Stop{CollectPayment: true}
		deliveryFee := NewDeliveryFee(70.0, 30.0, 1000)
		to.PriceSummary = *NewPriceSummary(from.ItemsPrice, types.NewFloat64(200.0), deliveryFee, deliveryFee, PaymentMethodCash)

		q, err := NewQuote(
			"quote_1",
			"user-1",
			ServiceFood,
			[]Stop{from, to},
			"",
			false,
			deliveryFee,
			deliveryFee,
			[]RegionCode{"AYUTTHAYA"},
			0,
		)
		require.NoError(tt, err)
		o := NewOrder(*q, "order-1")
		o.SetStatus(StatusCompleted)

		o.SetCommission(0.15, 0.0)
		o.SetWithholdingTax(0.03)
		_, _, err = o.completeAgent(*NewDriverTransaction("<test-id>"), func(o *Order, s Status) error {
			o.SetStatus(s)
			return nil
		})
		actual := o.PriceSummary()
		require.NoError(tt, err)
		require.Equal(tt, 15.0, actual.DeliveryFee.Commission)
		require.Equal(tt, 2.55, actual.DeliveryFee.WithholdingTax)
	})

	t.Run("success complete should return correct transaction", func(tt *testing.T) {
		testcases := []struct {
			name              string
			payment           PaymentMethod
			commissionRate    float64
			withholdingRate   float64
			deliveryFee       float64
			incrementFee      float64
			deliveryDiscounts []Discount
			itemFee           float64
			options           OrderOptions
			driverTxn         DriverTransaction

			expect []completedTxn
		}{
			{
				name:            "withholding rate 0 should not return txn",
				payment:         PaymentMethodCash,
				commissionRate:  0.0,
				withholdingRate: 0.0,
				deliveryFee:     100.0,
				incrementFee:    0.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},

				expect: []completedTxn{},
			},
			{
				name:            "commission and withholding should return correct",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     70.0,
				incrementFee:    30.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},

				expect: []completedTxn{
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
				},
			},
			{
				name:            "with GP discount should return subsidize",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     70.0,
				incrementFee:    30.0,
				itemFee:         100.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeSubsidize, Discount: 90.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: SubsidizeTransactionType, amount: 90.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
				},
			},
			{
				name:            "with discount should return coupon",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     70.0,
				incrementFee:    30.0,
				itemFee:         100.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeCoupon, Discount: 50.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: CouponTransactionType, amount: 50.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
				},
			},
			{
				name:            "rlp w/o GP",
				payment:         PaymentMethodRLP,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    10.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: DeliveryFeeTransactionType, amount: 40.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 6.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.02},
				},
			},
			{
				name:            "rlp w/ GP",
				payment:         PaymentMethodCreditCard,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    10.0,
				itemFee:         100.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeSubsidize, Discount: 30.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: SubsidizeTransactionType, amount: 30.0},
					{category: WalletTransactionCategory, txnType: DeliveryFeeTransactionType, amount: 10.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 6.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.02},
				},
			},
			{
				name:            "credit-card w/o GP",
				payment:         PaymentMethodCreditCard,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    10.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: DeliveryFeeTransactionType, amount: 40.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 6.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.02},
				},
			},
			{
				name:            "credit-card w/ GP",
				payment:         PaymentMethodCreditCard,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    10.0,
				itemFee:         100.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeSubsidize, Discount: 30.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: SubsidizeTransactionType, amount: 30.0},
					{category: WalletTransactionCategory, txnType: DeliveryFeeTransactionType, amount: 10.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 6.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.02},
				},
			},
			{
				name:            "cash w/ coupon_advance",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    10.0,
				itemFee:         100.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeCouponAdvance, Discount: 30.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 100,
				},

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: CouponTransactionType, amount: 30.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 6.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.02},
				},
			},
			{
				name:            "[Cash collection] should deduct credit by item fee",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 300,
					WalletBalance:         100,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
			{
				name:            "insufficient credit should not generate transfer wallet to credit transaction if driverMoneyFlow is not cash collection",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 10,
					WalletBalance:         100,
				},

				expect: []completedTxn{
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
				},
			},
			{
				name:            "[Cash collection] insufficient credit should transfer wallet to credit",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 10,
					WalletBalance:         200,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: WithdrawTransactionType, amount: 107.55},
					{category: CreditTransactionCategory, txnType: PurchaseTransactionType, amount: 107.55},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
			{
				name:            "[Cash collection] insufficient credit should transfer all wallet balance to credit",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 10,
					WalletBalance:         100,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: WithdrawTransactionType, amount: 100.0},
					{category: CreditTransactionCategory, txnType: PurchaseTransactionType, amount: 100.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
			{
				name:            "[Cash collection] insufficient credit should not transfer wallet to credit transaction if wallet balance is outstanding",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 10,
					WalletBalance:         -10,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
			{
				name:            "[Cash collection] amount from subsidize should transfer to credit if credit and wallet is insufficient",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         50.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeSubsidize, Discount: 90.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 10,
					WalletBalance:         0,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: SubsidizeTransactionType, amount: 90.0},
					{category: WalletTransactionCategory, txnType: WithdrawTransactionType, amount: 57.55},
					{category: CreditTransactionCategory, txnType: PurchaseTransactionType, amount: 57.55},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 50},
				},
			},
			{
				name:            "[Cash collection] all subsidize amount should transfer to credit if credit and wallet is insufficient",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeSubsidize, Discount: 90.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 10,
					WalletBalance:         0,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: SubsidizeTransactionType, amount: 90.0},
					{category: WalletTransactionCategory, txnType: WithdrawTransactionType, amount: 90.0},
					{category: CreditTransactionCategory, txnType: PurchaseTransactionType, amount: 90.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
			{
				name:            "[Cash collection] should not have credit outstanding transaction when transfer wallet to credit but credit is still outstanding",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeSubsidize, Discount: 90.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: -100,
					WalletBalance:         0,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: SubsidizeTransactionType, amount: 90.0},
					{category: WalletTransactionCategory, txnType: WithdrawTransactionType, amount: 90.0},
					{category: CreditTransactionCategory, txnType: PurchaseTransactionType, amount: 90.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
			{
				name:            "[Cash collection] should not have wallet outstanding transaction when subsidize driver with delivery discount",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeSubsidize, Discount: 90.0},
				},
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 10,
					WalletBalance:         -100,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: SubsidizeTransactionType, amount: 90.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
		}

		for _, tc := range testcases {
			from := Stop{ItemsPrice: tc.itemFee}
			to := Stop{CollectPayment: true}
			deliveryFee := NewDeliveryFee(tc.deliveryFee, tc.incrementFee, 1000)
			paymentMethod := tc.payment
			to.PriceSummary = *NewPriceSummary(from.ItemsPrice, types.NewFloat64(200.0), deliveryFee, deliveryFee, paymentMethod)
			q, err := NewQuote(
				"quote_1",
				"user-1",
				ServiceFood,
				[]Stop{from, to},
				"",
				false,
				deliveryFee,
				deliveryFee,
				[]RegionCode{"AYUTTHAYA"},
				0,
			)

			require.NoError(t, err)
			q.SetDiscount(tc.deliveryDiscounts)
			q.SetOption(tc.options)
			q.Summary(tc.commissionRate, tc.withholdingRate)
			q.SetOnTopFare()
			q.SetCommission(tc.commissionRate, 0.0)
			q.SetWithholdingTax(tc.withholdingRate)

			o := NewOrder(*q, "order-1")
			o.SetStatus(StatusDropOffDone)

			txns, _, err := o.completeAgent(tc.driverTxn, func(o *Order, s Status) error {
				o.SetStatus(s)
				return nil
			})

			require.Equal(t, StatusCompleted, o.Status, tc.name)
			require.NoError(tt, err, tc.name)
			require.Equal(tt, len(tc.expect), len(txns), tc.name)

			for i, e := range tc.expect {
				require.Equal(t, e.category, txns[i].Category, tc.name)
				require.Equal(t, e.txnType, txns[i].Type, tc.name)
				require.Equal(t, e.amount, txns[i].Amount.Float64(), tc.name)
			}
		}
	})
}

func TestOrderSuite(t *testing.T) {
	suite.Run(t, &testOrderSuite{})
}

type testOrderSuite struct {
	suite.Suite
}

func (s *testOrderSuite) TestCompleteOrder() {
	s.Run("[Messenger] complete order cases", func() {
		type Test struct {
			name               string
			from               Stop
			to                 Stop
			paymentMethod      PaymentMethod
			commissionRate     float64
			withholdingRate    float64
			deliveryFee        float64
			incrementFee       float64
			deliveryDiscounts  []Discount
			itemFee            float64
			options            OrderOptions
			additionServiceFee AdditionalServiceSummary
			driverTxn          DriverTransaction
			service            Service
			payAtStop          int

			expect []completedTxn
		}

		var (
			itemFee        = 0.0
			baseFee        = 30.0
			incrementFee   = 0.0
			distance       = 1000.0
			commissionRate = 0.15

			deliveryFee = NewDeliveryFee(baseFee, incrementFee, distance)
		)

		testcases := []Test{
			{
				name: "[E-payment no discount] should return correct transaction",
				from: Stop{
					PriceSummary: *NewPriceSummary(itemFee, types.NewFloat64(200.0), deliveryFee, deliveryFee, PaymentMethodRLP),
				},
				additionServiceFee: AdditionalServiceSummary{
					Total: 20.0,
					AdditionalServiceItems: []AdditionalServiceItem{
						{}, {},
					},
				},
				commissionRate: commissionRate,
				deliveryFee:    baseFee,
				incrementFee:   incrementFee,
				service:        ServiceMessenger,
				payAtStop:      0,

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: AdditionalServiceFeeTransactionType, amount: 20},
					{category: WalletTransactionCategory, txnType: DeliveryFeeTransactionType, amount: 30},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 4.5},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 3},
				},
			},
			{
				name: "[Cash one trip no discount] should return correct transaction",
				to: Stop{
					PriceSummary: *NewPriceSummary(itemFee, types.NewFloat64(200.0), deliveryFee, deliveryFee, PaymentMethodCash),
				},
				additionServiceFee: AdditionalServiceSummary{
					Total: 20.0,
					AdditionalServiceItems: []AdditionalServiceItem{
						{}, {},
					},
				},
				commissionRate: commissionRate,
				deliveryFee:    baseFee,
				incrementFee:   incrementFee,
				service:        ServiceMessenger,
				payAtStop:      1,

				expect: []completedTxn{
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 4.5},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 3},
				},
			},
			{
				name: "[Cash one trip with discount] should return correct transaction with coupon cover total price",
				to: Stop{
					PriceSummary: *NewPriceSummary(itemFee, types.NewFloat64(200.0), deliveryFee, deliveryFee, PaymentMethodCash),
				},
				additionServiceFee: AdditionalServiceSummary{
					Total: 20.0,
					AdditionalServiceItems: []AdditionalServiceItem{
						{}, {},
					},
				},
				commissionRate: commissionRate,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeCoupon, Discount: 50.0},
				},
				deliveryFee:  baseFee,
				incrementFee: incrementFee,
				service:      ServiceMessenger,
				payAtStop:    1,

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: CouponTransactionType, amount: 50.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 4.5},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 3},
				},
			},
			{
				name: "[E-payment one trip with discount] should return correct transaction with coupon less than additional service price",
				to: Stop{
					PriceSummary: *NewPriceSummary(itemFee, types.NewFloat64(200.0), deliveryFee, deliveryFee, PaymentMethodRLP),
				},
				additionServiceFee: AdditionalServiceSummary{
					Total: 20.0,
					AdditionalServiceItems: []AdditionalServiceItem{
						{}, {},
					},
				},
				commissionRate: commissionRate,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeCoupon, Discount: 10.0},
				},
				deliveryFee:  baseFee,
				incrementFee: incrementFee,
				service:      ServiceMessenger,
				payAtStop:    1,

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: CouponTransactionType, amount: 10.0},
					{category: WalletTransactionCategory, txnType: AdditionalServiceFeeTransactionType, amount: 10.0},
					{category: WalletTransactionCategory, txnType: DeliveryFeeTransactionType, amount: 30.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 4.5},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 3},
				},
			},
			{
				name: "[E-payment one trip with discount] should return correct transaction with coupon over than additional service price but less than total",
				to: Stop{
					PriceSummary: *NewPriceSummary(itemFee, types.NewFloat64(200.0), deliveryFee, deliveryFee, PaymentMethodRLP),
				},
				additionServiceFee: AdditionalServiceSummary{
					Total: 20.0,
					AdditionalServiceItems: []AdditionalServiceItem{
						{}, {},
					},
				},
				commissionRate: commissionRate,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeCoupon, Discount: 40.0},
				},
				deliveryFee:  baseFee,
				incrementFee: incrementFee,
				service:      ServiceMessenger,
				payAtStop:    1,

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: CouponTransactionType, amount: 40.0},
					{category: WalletTransactionCategory, txnType: DeliveryFeeTransactionType, amount: 10.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 4.5},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 3},
				},
			},
			{
				name: "[E-payment one trip with discount] should return correct transaction with coupon cover total",
				to: Stop{
					PriceSummary: *NewPriceSummary(itemFee, types.NewFloat64(200.0), deliveryFee, deliveryFee, PaymentMethodRLP),
				},
				additionServiceFee: AdditionalServiceSummary{
					Total: 20.0,
					AdditionalServiceItems: []AdditionalServiceItem{
						{}, {},
					},
				},
				commissionRate: commissionRate,
				deliveryDiscounts: []Discount{
					{Type: DiscountTypeCoupon, Discount: 50.0},
				},
				deliveryFee:  baseFee,
				incrementFee: incrementFee,
				service:      ServiceMessenger,
				payAtStop:    1,

				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: CouponTransactionType, amount: 50.0},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 4.5},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 3},
				},
			},
		}

		for _, tc := range testcases {
			s.Run(tc.name, func() {
				q, err := NewQuote(
					"quote_1",
					"user-1",
					tc.service,
					[]Stop{tc.from, tc.to},
					"",
					false,
					deliveryFee,
					deliveryFee,
					[]RegionCode{"AYUTTHAYA"},
					tc.payAtStop,
				)

				s.NoError(err, tc.name)
				q.SetDiscount(tc.deliveryDiscounts)
				q.SetOption(tc.options)
				q.SetAdditionalService(tc.additionServiceFee)
				q.Summary(tc.commissionRate, tc.withholdingRate)
				q.SetOnTopFare()
				q.SetCommission(tc.commissionRate, 0.0)
				q.SetWithholdingTax(tc.withholdingRate)

				o := NewOrder(*q, "order-1")
				o.SetStatus(StatusDropOffDone)

				txns, _, err := o.completeAgent(tc.driverTxn, func(o *Order, s Status) error {
					o.SetStatus(s)
					return nil
				})

				s.Equal(StatusCompleted, o.Status, tc.name)
				s.NoError(err, tc.name)
				s.Equal(len(tc.expect), len(txns), tc.name)

				for i, e := range tc.expect {
					s.Equal(e.category, txns[i].Category, tc.name)
					s.Equal(e.txnType, txns[i].Type, tc.name)
					s.Equal(e.amount, txns[i].Amount.Float64(), tc.name)
				}
			})
		}
	})
}

func TestCashCollectionWithOnTopOrder(t *testing.T) {
	t.Parallel()

	t.Run("transfer wallet to credit correctly", func(tt *testing.T) {
		testcases := []struct {
			name              string
			payment           PaymentMethod
			commissionRate    float64
			withholdingRate   float64
			deliveryFee       float64
			incrementFee      float64
			deliveryDiscounts []Discount
			itemFee           float64
			options           OrderOptions
			driverTxn         DriverTransaction
			expect            []completedTxn
		}{
			{
				name:            "[Cash collection] insufficient credit should transfer wallet to credit",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 0,
					WalletBalance:         300,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: OnTopTransactionType, amount: 10},
					{category: WalletTransactionCategory, txnType: WithdrawTransactionType, amount: 119.31},
					{category: CreditTransactionCategory, txnType: PurchaseTransactionType, amount: 119.31},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 1.5},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 0.26},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
			{
				name:            "[Cash collection] insufficient credit should transfer all wallet balance to credit",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 0,
					WalletBalance:         100,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: OnTopTransactionType, amount: 10},
					{category: WalletTransactionCategory, txnType: WithdrawTransactionType, amount: 110},
					{category: CreditTransactionCategory, txnType: PurchaseTransactionType, amount: 110},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 1.5},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 0.26},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
			{
				name:            "[Cash collection] insufficient credit should not transfer wallet to credit transaction if wallet balance is outstanding",
				payment:         PaymentMethodCash,
				commissionRate:  0.15,
				withholdingRate: 0.03,
				deliveryFee:     30.0,
				incrementFee:    70.0,
				itemFee:         100.0,
				driverTxn: DriverTransaction{
					PurchaseCreditBalance: 0,
					WalletBalance:         -10,
				},
				options: OrderOptions{
					DriverMoneyFlow: FlowCashCollection,
				},
				expect: []completedTxn{
					{category: WalletTransactionCategory, txnType: OnTopTransactionType, amount: 10},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 15.0},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 2.55},
					{category: CreditTransactionCategory, txnType: CommissionTransactionType, amount: 1.5},
					{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 0.26},
					{category: CreditTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
				},
			},
		}

		for _, tc := range testcases {
			from := Stop{ItemsPrice: tc.itemFee}
			to := Stop{CollectPayment: true}
			deliveryFee := NewDeliveryFee(tc.deliveryFee, tc.incrementFee, 1000)

			paymentMethod := tc.payment
			to.PriceSummary = *NewPriceSummary(from.ItemsPrice, types.NewFloat64(200.0), deliveryFee, deliveryFee, paymentMethod)
			to.PriceSummary.DeliveryFee.OnTopScheme = []OnTopScheme{
				{
					Name:   "Test on top scheme",
					Amount: 10.0,
				},
			}
			q, err := NewQuote(
				"quote_1",
				"user-1",
				ServiceFood,
				[]Stop{from, to},
				"",
				false,
				deliveryFee,
				deliveryFee,
				[]RegionCode{"AYUTTHAYA"},
				1,
			)

			require.NoError(t, err)
			q.SetDiscount(tc.deliveryDiscounts)
			q.SetOption(tc.options)
			q.Summary(tc.commissionRate, tc.withholdingRate)
			q.SetOnTopFare()
			q.SetCommission(tc.commissionRate, 0.0)
			q.SetWithholdingTax(tc.withholdingRate)

			o := NewOrder(*q, "order-1")
			o.SetStatus(StatusDropOffDone)

			txns, _, err := o.completeAgent(tc.driverTxn, func(o *Order, s Status) error {
				o.SetStatus(s)
				return nil
			})

			require.Equal(t, StatusCompleted, o.Status, tc.name)
			require.NoError(tt, err, tc.name)
			require.Equal(tt, len(tc.expect), len(txns), tc.name)

			for i, e := range tc.expect {
				require.Equal(t, e.category, txns[i].Category, tc.name)
				require.Equal(t, e.txnType, txns[i].Type, tc.name)
				require.Equal(t, e.amount, txns[i].Amount.Float64(), tc.name)
			}
		}
	})
}

func TestGetChangeDeliveryLocationStop(t *testing.T) {
	t.Parallel()

	t.Run("get delivery location", func(tt *testing.T) {
		order := Order{
			Quote: Quote{
				Routes: []Stop{
					{
						Name: "Stop 1",
					},
					{
						Name: "Delivery Location",
					},
				},
				Options: OrderOptions{
					RoundTrip: false,
				},
				ServiceType: ServiceFood,
			},
		}

		result := order.GetChangeDeliveryLocationStop()
		require.Equal(tt, 1, result)
	})

	t.Run("get delivery location for round trip", func(tt *testing.T) {
		order := Order{
			Quote: Quote{
				Routes: []Stop{
					{
						Name: "Stop 1",
					},
					{
						Name: "Stop 2",
					},
					{
						Name: "Delivery Location",
					},
				},
				Options: OrderOptions{
					RoundTrip: true,
				},
				ServiceType: ServiceMessenger,
			},
		}

		result := order.GetChangeDeliveryLocationStop()
		require.Equal(tt, 1, result)
	})
}

func TestOrderHasOnTopDeliveryFee(t *testing.T) {
	t.Parallel()

	t.Run("should return true if on top delivery fee exists", func(tt *testing.T) {
		order := Order{
			Quote: Quote{
				ServiceType: ServiceFood,
				Routes: []Stop{
					{
						PriceSummary: PriceSummary{},
					},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								PaymentMethod: PaymentMethodRLP,
								OnTopScheme: []OnTopScheme{
									{
										Name:   "Test on top scheme",
										Amount: 10.0,
									},
								},
							},
						},
					},
				},
			},
		}

		require.Equal(tt, order.HasOnTopDeliveryFee(), true)
	})

	t.Run("should return false if on top delivery fee not exist", func(tt *testing.T) {
		order := Order{
			Quote: Quote{
				ServiceType: ServiceFood,
				Routes: []Stop{
					{
						PriceSummary: PriceSummary{},
					},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								PaymentMethod: PaymentMethodRLP,
							},
						},
					},
				},
			},
		}

		require.Equal(tt, order.HasOnTopDeliveryFee(), false)
	})
}

func TestOrder_CompleteWithOnTop(t *testing.T) {
	t.Parallel()

	type expectedCase struct {
		transactionType TransactionType
		category        TransactionCategory
		amount          types.Money
	}

	assertExpectedCase := func(tt *testing.T, expectedCases []expectedCase, transactionInfo []TransactionInfo) {
		for idx, e := range expectedCases {
			require.Equal(tt, e.transactionType, transactionInfo[idx].Type)
			require.Equal(tt, e.category, transactionInfo[idx].Category)
			require.Equal(tt, e.amount, transactionInfo[idx].Amount)
		}
	}

	t.Run("should create on-top, commission, and withholding tax transactions", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									Name:   "On-Top 10 Baht",
									Amount: 10.0,
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-001")

		order.SetStatus(StatusCompleted)
		order.SetOnTopFare()
		order.SetCommission(0.15, 0.0)
		order.SetWithholdingTax(0.03)

		transactionInfo, _, _ := order.completeAgent(
			*NewDriverTransaction("<test-id>"),
			func(o *Order, s Status) error {
				o.SetStatus(s)
				return nil
			},
		)

		require.Equal(tt, 3, len(transactionInfo))

		expectedCases := []expectedCase{
			{
				transactionType: OnTopTransactionType,
				category:        WalletTransactionCategory,
				amount:          types.NewMoney(10.0),
			},
			{
				transactionType: CommissionTransactionType,
				category:        CreditTransactionCategory,
				amount:          types.NewMoney(1.5),
			},
			{
				transactionType: WithholdingTransactionType,
				category:        CreditTransactionCategory,
				amount:          types.NewMoney(0.26),
			},
		}

		assertExpectedCase(tt, expectedCases, transactionInfo)
	})

	t.Run("should create on-top and commission transactions", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									Name:   "On-Top 1 Baht",
									Amount: 10.0,
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-001")

		order.SetStatus(StatusCompleted)
		order.SetOnTopFare()
		order.SetCommission(0.15, 0.0)
		order.SetWithholdingTax(0.0)

		transactionInfo, _, _ := order.completeAgent(
			*NewDriverTransaction("<test-id>"),
			func(o *Order, s Status) error {
				o.SetStatus(s)
				return nil
			},
		)

		require.Equal(tt, 2, len(transactionInfo))

		expectedCases := []expectedCase{
			{
				transactionType: OnTopTransactionType,
				category:        WalletTransactionCategory,
				amount:          types.NewMoney(10.0),
			},
			{
				transactionType: CommissionTransactionType,
				category:        CreditTransactionCategory,
				amount:          types.NewMoney(1.5),
			},
		}

		assertExpectedCase(tt, expectedCases, transactionInfo)
	})

	t.Run("should create on-top transaction", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									Name:   "On-Top 1 Baht",
									Amount: 10.0,
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-001")

		order.SetStatus(StatusCompleted)
		order.SetOnTopFare()
		order.SetCommission(0.0, 0.0)
		order.SetWithholdingTax(0.0)

		transactionInfo, _, _ := order.completeAgent(
			*NewDriverTransaction("<test-id>"),
			func(o *Order, s Status) error {
				o.SetStatus(s)
				return nil
			},
		)

		require.Equal(tt, 1, len(transactionInfo))

		expectedCases := []expectedCase{
			{
				transactionType: OnTopTransactionType,
				category:        WalletTransactionCategory,
				amount:          types.NewMoney(10.0),
			},
		}

		assertExpectedCase(tt, expectedCases, transactionInfo)
	})

	t.Run("[2 on-top] should create on-top, commission, and withholding tax transactions", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									ID:     "on-top-10-thb",
									Name:   "On-Top 10 Baht",
									Amount: 10.0,
								},
								{
									ID:     "on-top-5-thb",
									Name:   "On-top 5 Baht",
									Amount: 5.0,
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-001")

		order.SetStatus(StatusCompleted)
		order.SetOnTopFare()
		order.SetCommission(0.15, 0.0)
		order.SetWithholdingTax(0.03)

		transactionInfo, _, _ := order.completeAgent(
			*NewDriverTransaction("<test-id>"),
			func(o *Order, s Status) error {
				o.SetStatus(s)
				return nil
			},
		)

		require.Equal(tt, 3, len(transactionInfo))

		expectedCases := []expectedCase{
			{
				transactionType: OnTopTransactionType,
				category:        WalletTransactionCategory,
				amount:          types.NewMoney(15.0),
			},
			{
				transactionType: CommissionTransactionType,
				category:        CreditTransactionCategory,
				amount:          types.NewMoney(2.25),
			},
			{
				transactionType: WithholdingTransactionType,
				category:        CreditTransactionCategory,
				amount:          types.NewMoney(0.38),
			},
		}

		assertExpectedCase(tt, expectedCases, transactionInfo)
	})

	t.Run("[0 on-top] should not create on-top, commission, and withholding tax transactions", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-001")

		order.SetStatus(StatusCompleted)
		order.SetOnTopFare()
		order.SetCommission(0.15, 0.0)
		order.SetWithholdingTax(0.03)

		transactionInfo, _, _ := order.completeAgent(
			*NewDriverTransaction("<test-id>"),
			func(o *Order, s Status) error {
				o.SetStatus(s)
				return nil
			},
		)

		require.Equal(tt, 0, len(transactionInfo))
	})
}

func TestOrder_GetOnTopRemark(t *testing.T) {
	t.Parallel()

	t.Run("should be empty string in remark", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-empty-string-remark")

		require.Equal(tt, "", order.getOnTopRemark())
	})

	t.Run("should have 1 on-top in remark", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									ID:     "on-top-10-thb",
									Name:   "On-Top 10 Baht",
									Amount: 10.0,
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-1-on-top-remark")

		require.Equal(tt, "On-Top: On-Top 10 Baht amount: 10.00 THB\n", order.getOnTopRemark())
	})

	t.Run("should have 2 on-tops in remark", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									ID:     "on-top-10-thb",
									Name:   "On-Top 10 Baht",
									Amount: 10.0,
								},
								{
									ID:     "on-top-5-thb",
									Name:   "On-top 5 Baht",
									Amount: 5.0,
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-2-on-top-remark")

		require.Equal(tt, "On-Top: On-Top 10 Baht amount: 10.00 THB\nOn-Top: On-top 5 Baht amount: 5.00 THB\n", order.getOnTopRemark())
	})

	t.Run("should have coin remark", func(tt *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									ID:     "on-top-10-thb",
									Name:   "On-Top 10 Baht",
									Amount: 10.0,
									Coin:   20,
								},
								{
									ID:     "on-top-5-thb",
									Name:   "On-top 5 Baht",
									Amount: 5.0,
									Coin:   30,
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "on-top-2-on-top-remark")

		require.Equal(tt, "On-Top: On-Top 10 Baht amount: 10.00 THB Coin: 20 coin\nOn-Top: On-top 5 Baht amount: 5.00 THB Coin: 30 coin\n", order.getOnTopRemark())
	})
}

func TestValidateDriverBalance(t *testing.T) {
	tcs := []struct {
		wallet                      float64
		credit                      float64
		sub                         float64
		coupon                      float64
		foodFee                     float64
		expected                    bool
		transfer                    float64
		outstanding                 float64
		isFlowCashCollection        bool
		DeliveryFee                 float64
		AdditionalServiceCommission float64
		Commission                  float64
		WithholdingTax              float64
	}{
		// cash collection - credit enough
		{135, 5, 10, 0, 140, true, 142.02, 0, true, 40, 0, 0.15, 0.03},
		{140, 0, 10, 0, 140, true, 147.02, 0, true, 40, 0, 0.15, 0.03},
		{150, 0, 10, 0, 140, true, 147.02, 0, true, 40, 0, 0.15, 0.03},
		{0, 210, 0, 0, 200, true, 0, 0, true, 40, 0, 0.15, 0.03},
		{0, 299, 40, 0, 300, true, 8.02, 0, true, 40, 0, 0.15, 0.03},
		{0, 310, 40, 0, 300, true, 0, 0, true, 40, 0, 0.15, 0.03},
		{100, 250, 0, 0, 300, true, 57.02, 0, true, 40, 0, 0.15, 0.03},
		{100, 500, 0, 0, 300, true, 0, 0, true, 40, 0, 0.15, 0.03},
		{50, 100, 30, 10, 140, true, 47.02, 0, true, 40, 0, 0.15, 0.03},

		// cash collection - credit not enough
		{100, 100, 40, 0, 300, false, 140, 67.02, true, 40, 0, 0.15, 0.03},
		{0, 100, 0, 40, 140, false, 40, 7.02, true, 40, 0, 0.15, 0.03},
		{0, 100, 30, 10, 140, false, 40, 7.02, true, 40, 0, 0.15, 0.03},
		{0, 100, 0, 0, 300, false, 0, 207.02, true, 40, 0, 0.15, 0.03},
		{0, -100, 40, 0, 300, false, 40, 267.02, true, 40, 0, 0.15, 0.03},
		{-100, 100, 40, 0, 300, false, 0, 207.02, true, 40, 0, 0.15, 0.03},
		{-100, -100, 40, 0, 300, false, 0, 307.02, true, 40, 0, 0.15, 0.03},

		// non-cash collection - credit enough
		{10, 10, 10, 0, 140, true, 0, 0, false, 40, 0, 0.15, 0.03},
		{100, 500, 0, 0, 300, true, 0, 0, false, 40, 0, 0.15, 0.03},
		{0, 0, 0, 10, 140, true, 7.02, 0, false, 40, 0, 0.15, 0.03},

		// non-cash collection - credit not enough
		{0, 5, 0, 0, 140, false, 0, 2.02, false, 40, 0, 0.15, 0.03},
		{0, 0, 0, 0, 140, false, 0, 7.02, false, 40, 0, 0.15, 0.03},
		{0, -100, 0, 0, 140, false, 0, 7.02, false, 40, 0, 0.15, 0.03},
		{-100, 0, 0, 0, 140, false, 0, 7.02, false, 40, 0, 0.15, 0.03},
		{-100, -100, 0, 0, 140, false, 0, 7.02, false, 40, 0, 0.15, 0.03},

		// Without WithholdingTax
		{135, 5, 10, 0, 140, true, 141, 0, true, 40, 0, 0.15, 0.0},
		{135, 500, 10, 0, 140, true, 0, 0, true, 200, 0, 0.15, 0.0},
		{100, 100, 40, 0, 300, false, 140, 66, true, 40, 0, 0.15, 0.00},
	}
	for _, tc := range tcs {
		driverTrans := *NewDriverTransaction("driverID")

		discounts := DiscountList{}
		if tc.coupon > 0 {
			discounts = append(discounts, Discount{Type: DiscountTypeCoupon, Category: "", Code: "", Discount: tc.coupon})
		}

		if tc.sub > 0 {
			discounts = append(discounts, Discount{Type: DiscountTypeSubsidize, Category: "", Code: "", Discount: tc.sub})
		}

		order := &Order{
			Quote: Quote{
				ServiceType: ServiceFood,
				Routes: []Stop{
					{},
					{
						CollectPayment: true,
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								SubTotal:  tc.DeliveryFee,
								Discounts: discounts,
							},
							ItemFee: ItemFeeSummary{Total: tc.foodFee},
						},
					},
				},
			},
		}

		if tc.isFlowCashCollection {
			order.Options.DriverMoneyFlow = FlowCashCollection
		}

		driverTrans.WalletBalance = types.NewMoney(tc.wallet)
		driverTrans.PurchaseCreditBalance = types.NewMoney(tc.credit)
		order.SetCommission(tc.Commission, 0.0)
		order.SetWithholdingTax(tc.WithholdingTax)
		enough, transfer, outstanding := validateDriverBalanceEnoughAgent(driverTrans, order)

		require.Equal(t, tc.expected, enough)
		require.Equal(t, types.Money(tc.transfer), transfer)
		require.Equal(t, types.Money(tc.outstanding), outstanding)
	}
}

func TestPrincipleValidateDriverBalance(t *testing.T) {
	tcs := []struct {
		wallet               float64
		credit               float64
		coupon               float64
		foodFee              float64
		expected             bool
		transfer             float64
		outstanding          float64
		isFlowCashCollection bool
		wage                 float64
		userDeliveryFee      float64
		onTopFare            float64
	}{
		// epayment
		{0, 0, 0, 1000, true, 0, 0, false, 0, 0, 0},

		// cash | enough credit
		{0, 1, 0, 1000, true, 0, 0, false, 0, 1, 0},
		// cash | not enough credit but can transfer from wallet
		{1, 1, 0, 0, true, 1, 0, false, 0, 2, 0},
		// cash | not enough credit and not enough wallet
		{1, 1, 0, 0, false, 1, 1, false, 0, 3, 0},
		// cash | not enough credit but enough wallet after including wage
		{0, 1, 0, 0, true, 2.5, 0, false, 10, 2, 0},
		// cash | not enough credit but enough wallet after including ontop
		{0, 1, 0, 0, true, 2.5, 0, false, 0, 2, 10},
		// cash | not enough credit because withholding tax
		{0, 0, 0, 0, true, 0.15, 0, false, 1, 0, 0},
		// cash | negative credit but can transfer
		{2, -1, 0, 0, true, 2, 0, false, 0, 1, 0},
		// cash | does not deduct credit but negative credit but can transfer to non-negative from wallet
		{2, -1, 0, 0, true, 1, 0, false, 0, 0, 0},
		// cash | does not deduct credit but negative credit and can't transfer to non-negative from wallet
		{1, -2, 0, 0, false, 1, 1, false, 0, 0, 0},
		// cash | should try to bring credit up from non-negative
		{5, -3, 0, 0, true, 4, 0, false, 0, 1, 0},
		// cash | negative credit and can't transfer
		{0, -2, 0, 0, false, 0, 3, false, 0, 1, 0},
		// cash | negative credit and negative wallet
		{-1, -1, 0, 0, false, 0, 2, false, 0, 1, 0},

		// cash-collection | enough credit
		{0, 2, 0, 1, true, 0, 0, true, 0, 1, 0},
		// cash-collection | not enough credit but can transfer from wallet
		{1, 1, 0, 1, true, 1, 0, true, 0, 1, 0},
		// cash-collection | not enough credit and not enough wallet
		{1, 1, 0, 2, false, 1, 1, true, 0, 1, 0},
		// cash-collection | not enough credit but enough wallet after including wage
		{0, 1, 0, 1, true, 2.5, 0, true, 10, 1, 0},
		// cash-collection | not enough credit but enough wallet after including ontop
		{0, 1, 0, 1, true, 2.5, 0, true, 0, 1, 10},
	}
	for _, tc := range tcs {
		driverTrans := *NewDriverTransaction("driverID")

		discounts := DiscountList{}
		if tc.coupon > 0 {
			discounts = append(discounts, Discount{Type: DiscountTypeCoupon, Category: "", Code: "", Discount: tc.coupon})
		}

		order := &Order{
			Quote: Quote{
				ServiceType: ServiceFood,
				Routes: []Stop{
					{},
					{
						CollectPayment: true,
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								SubTotal:        tc.wage,
								UserDeliveryFee: tc.userDeliveryFee,
								Discounts:       discounts,
								OnTopScheme:     []OnTopScheme{{Amount: tc.onTopFare}},
							},
							ItemFee: ItemFeeSummary{Total: tc.foodFee},
						},
					},
				},
				RevenuePrincipalModel: true,
			},
		}
		order.SetOnTopFare()
		order.SetWithholdingTax(0.15)

		if tc.isFlowCashCollection {
			order.Options.DriverMoneyFlow = FlowCashCollection
		}

		driverTrans.WalletBalance = types.NewMoney(tc.wallet)
		driverTrans.PurchaseCreditBalance = types.NewMoney(tc.credit)
		enough, transfer, outstanding := validateDriverBalanceEnoughPrincipal(driverTrans, order)

		require.Equal(t, tc.expected, enough)
		require.Equal(t, types.Money(tc.transfer), transfer)
		require.Equal(t, types.Money(tc.outstanding), outstanding)

	}
}

func TestOrder_GetCurrentShiftDiscountPrice(t *testing.T) {
	testCases := []struct {
		name          string
		orderShift    []OrderShift
		driverShift   []string
		discountPrice float64
	}{
		{
			name: "get valid discount price",
			orderShift: []OrderShift{
				{
					ShiftId:       "xx",
					DiscountPrice: 3.0,
				},
				{
					ShiftId:       "yy",
					DiscountPrice: 2.0,
				},
			},
			driverShift:   []string{"xx"},
			discountPrice: float64(3),
		},
		{
			name:          "get valid price when no shift in order",
			orderShift:    []OrderShift{},
			driverShift:   []string{"xx"},
			discountPrice: float64(0),
		},
		{
			name: "get valid price when no shift in driver",
			orderShift: []OrderShift{
				{
					ShiftId:       "xx",
					DiscountPrice: 3.0,
				},
				{
					ShiftId:       "yy",
					DiscountPrice: 2.0,
				},
			},
			driverShift:   []string{},
			discountPrice: float64(0),
		},
		{
			name: "get valid shift if driver has 2 shift",
			orderShift: []OrderShift{
				{
					ShiftId:       "zz",
					DiscountPrice: 3.0,
				},
				{
					ShiftId:       "yy",
					DiscountPrice: 2.0,
				},
			},
			driverShift:   []string{"yy", "xx"},
			discountPrice: float64(2),
		},
		{
			name: "get valid shift if no shift in driver",
			orderShift: []OrderShift{
				{
					ShiftId:       "zz",
					DiscountPrice: 3.0,
				},
				{
					ShiftId:       "yy",
					DiscountPrice: 2.0,
				},
			},
			driverShift:   []string{""},
			discountPrice: float64(0),
		},

		{
			name:          "get valid shift if no shift in order",
			orderShift:    []OrderShift{},
			driverShift:   []string{""},
			discountPrice: float64(0),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			o := &Order{
				Shifts: tc.orderShift,
			}
			d := &Driver{Shifts: tc.driverShift}
			p := o.GetCurrentShiftDiscountPrice(d)
			require.Equal(t, tc.discountPrice, p)
		})
	}
}

func TestGetLegacyStatus(t *testing.T) {
	testcases := []struct {
		Routes         []Stop
		RevampedStatus Status
		HeadTo         int
		LegacyStatus   Status
		Service        Service
	}{
		{
			RevampedStatus: StatusReady,
			LegacyStatus:   StatusRestaurantAccepted,
			HeadTo:         0,
		},
		{
			RevampedStatus: StatusDriveTo,
			LegacyStatus:   StatusDriverToRestaurant,
			HeadTo:         0,
		},
		{
			RevampedStatus: StatusArrivedAt,
			LegacyStatus:   StatusDriverArrivedRestaurant,
			HeadTo:         0,
		},
		{
			RevampedStatus: StatusDriveTo,
			LegacyStatus:   StatusDriverToDestination,
			HeadTo:         1,
		},
		{
			RevampedStatus: StatusArrivedAt,
			LegacyStatus:   StatusDriverArrived,
			HeadTo:         1,
		},
		{
			Routes: []Stop{
				{
					Pauses: PauseSet{
						PauseConfirmPrice: true,
					},
				}, {},
			},
			RevampedStatus: StatusDriveTo,
			HeadTo:         0,
			LegacyStatus:   StatusDriverToRestaurant,
		},
		{
			Routes: []Stop{
				{
					Pauses: PauseSet{
						PauseConfirmPrice: true,
					},
				}, {},
			},
			RevampedStatus: StatusArrivedAt,
			HeadTo:         0,
			LegacyStatus:   StatusDriverArrivedRestaurant,
		},
		{
			Routes: []Stop{
				{
					Pauses: PauseSet{
						PauseConfirmPrice: true,
					},
					MetaData: map[string]interface{}{
						"UPDATED_PRICE": true,
					},
				}, {},
			},
			RevampedStatus: StatusArrivedAt,
			HeadTo:         0,
			LegacyStatus:   StatusWaitingUserConfirmPrice,
		},
		{
			Routes: []Stop{
				{
					Pauses: PauseSet{},
					MetaData: map[string]interface{}{
						"UPDATED_PRICE": true,
					},
				}, {},
			},
			RevampedStatus: StatusArrivedAt,
			HeadTo:         0,
			LegacyStatus:   StatusUserConfirmedPrice,
		},
		{
			Routes: []Stop{
				{
					Pauses: PauseSet{
						PauseConfirmPrice: true,
					},
				}, {},
			},
			RevampedStatus: StatusArrivedAt,
			HeadTo:         0,
			LegacyStatus:   StatusOnGoing,
			Service:        ServiceBike,
		},
		{
			Routes: []Stop{
				{
					Pauses: PauseSet{
						PauseConfirmPrice: true,
					},
				}, {},
			},
			RevampedStatus: StatusDriveTo,
			HeadTo:         0,
			LegacyStatus:   StatusOnGoing,
			Service:        ServiceBike,
		},
		{
			Routes: []Stop{
				{
					Pauses: PauseSet{
						PauseConfirmPrice: true,
					},
				}, {},
			},
			RevampedStatus: StatusReady,
			HeadTo:         0,
			LegacyStatus:   StatusOnGoing,
			Service:        ServiceBike,
		},
	}

	for i, testcase := range testcases {
		order := &Order{
			Quote: Quote{
				Routes:      testcase.Routes,
				ServiceType: testcase.Service,
			},
			Status: testcase.RevampedStatus,
			HeadTo: testcase.HeadTo,
		}
		require.Equal(t, testcase.LegacyStatus, order.GetLegacyStatus(), fmt.Sprintf("Test case # %d", i+1))
	}
}

func TestSBE_CompleteCashAdvancementEpayment(t *testing.T) {
	testcases := []struct {
		name              string
		payment           PaymentMethod
		commissionRate    float64
		withholdingRate   float64
		deliveryFee       float64
		incrementFee      float64
		deliveryDiscounts []Discount
		itemFee           float64
		options           OrderOptions
		driverTxn         DriverTransaction
		expect            []completedTxn
	}{
		{
			name:            "[Cash Advancement Epayment] Complete order RMS",
			payment:         PaymentMethodCreditCard,
			commissionRate:  0,
			withholdingRate: 0.03,
			deliveryFee:     42.52,
			incrementFee:    1,
			itemFee:         100.0,
			driverTxn: DriverTransaction{
				PurchaseCreditBalance: 200,
				WalletBalance:         50,
			},
			options: OrderOptions{
				DriverMoneyFlow: FlowCashAdvancementEpayment,
			},
			expect: []completedTxn{
				{category: WalletTransactionCategory, txnType: DriverWageType, amount: 43.52},
				{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.31},
				{category: WalletTransactionCategory, txnType: ItemFeeTransactionType, amount: 100},
			},
		},
		{
			name:            "[Cash Advancement Epayment] Complete order RMS + Coupon",
			payment:         PaymentMethodCreditCard,
			commissionRate:  0,
			withholdingRate: 0.03,
			deliveryFee:     42.52,
			incrementFee:    1,
			itemFee:         50.0,
			driverTxn: DriverTransaction{
				PurchaseCreditBalance: 200,
				WalletBalance:         50,
			},
			options: OrderOptions{
				DriverMoneyFlow: FlowCashAdvancementEpayment,
			},
			deliveryDiscounts: DiscountList{
				{Type: DiscountTypeCouponAdvance, Discount: 50.0},
			},
			expect: []completedTxn{
				{category: WalletTransactionCategory, txnType: DriverWageType, amount: 43.52},
				{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.31},
				{category: WalletTransactionCategory, txnType: ItemFeeTransactionType, amount: 50},
				{category: WalletTransactionCategory, txnType: CouponTransactionType, amount: 50},
			},
		},
		{
			name:            "[Cash Advancement Epayment] Complete RMS with price 15 should be correct",
			payment:         PaymentMethodCreditCard,
			commissionRate:  0,
			withholdingRate: 0.03,
			deliveryFee:     42.52,
			incrementFee:    1,
			itemFee:         15.0,
			driverTxn: DriverTransaction{
				PurchaseCreditBalance: 200,
				WalletBalance:         50,
			},
			options: OrderOptions{
				DriverMoneyFlow: FlowCashAdvancementEpayment,
			},
			expect: []completedTxn{
				{category: WalletTransactionCategory, txnType: DriverWageType, amount: 43.52},
				{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.31},
				{category: WalletTransactionCategory, txnType: ItemFeeTransactionType, amount: 15},
			},
		},
		{
			name:            "[Cash Advancement Epayment] Complete RMS with price 180 with discount 50 should be correct",
			payment:         PaymentMethodCreditCard,
			commissionRate:  0,
			withholdingRate: 0.03,
			deliveryFee:     42.52,
			incrementFee:    1,
			itemFee:         130.0,
			driverTxn: DriverTransaction{
				PurchaseCreditBalance: 200,
				WalletBalance:         50,
			},
			deliveryDiscounts: DiscountList{
				{Type: DiscountTypeCouponAdvance, Discount: 50.0},
			},
			options: OrderOptions{
				DriverMoneyFlow: FlowCashAdvancementEpayment,
			},
			expect: []completedTxn{
				{category: WalletTransactionCategory, txnType: DriverWageType, amount: 43.52},
				{category: CreditTransactionCategory, txnType: WithholdingTransactionType, amount: 1.31},
				{category: WalletTransactionCategory, txnType: ItemFeeTransactionType, amount: 130},
				{category: WalletTransactionCategory, txnType: CouponTransactionType, amount: 50},
			},
		},
	}

	for _, tc := range testcases {
		from := Stop{ItemsPrice: tc.itemFee}
		to := Stop{CollectPayment: false}
		deliveryFee := NewDeliveryFee(tc.deliveryFee, tc.incrementFee, 0)

		paymentMethod := tc.payment
		to.PriceSummary = *NewPriceSummary(from.ItemsPrice, types.NewFloat64(200.0), deliveryFee, deliveryFee, paymentMethod)
		to.PriceSummary.ItemFee.PaymentMethod = paymentMethod

		q, err := NewQuote(
			"quote_1",
			"user-1",
			ServiceFood,
			[]Stop{from, to},
			"",
			false,
			deliveryFee,
			deliveryFee,
			[]RegionCode{"AYUTTHAYA"},
			1,
		)

		require.NoError(t, err)
		q.SetDiscount(tc.deliveryDiscounts)
		q.SetOption(tc.options)
		q.Summary(tc.commissionRate, tc.withholdingRate)

		o := NewOrder(*q, "order-1")
		o.SetStatus(StatusDropOffDone)
		o.SetCommission(tc.commissionRate, 0.0)
		o.SetWithholdingTax(tc.withholdingRate)

		txns, _, err := o.completePrincipal(tc.driverTxn, func(o *Order, s Status) error {
			o.SetStatus(s)
			return nil
		})

		require.Equal(t, StatusCompleted, o.Status, tc.name)
		require.NoError(t, err, tc.name)
		require.Equal(t, len(tc.expect), len(txns), tc.name)

		for i, e := range tc.expect {
			require.Equal(t, e.category, txns[i].Category, tc.name)
			require.Equal(t, e.txnType, txns[i].Type, tc.name)
			require.Equal(t, e.amount, txns[i].Amount.Float64(), tc.name)
		}
	}
}

func TestStatusFromString(t *testing.T) {
	t.Run("should success create status list", func(t *testing.T) {
		str := "DRIVER_MATCHED, DRIVER_TO_RESTAURANT,DRIVER_ARRIVED_RESTAURANT"
		expected := []Status{StatusDriverMatched, StatusDriverToRestaurant, StatusDriverArrivedRestaurant}

		result := StatusFromString(str)

		assert.Equal(t, expected, result)
	})

	t.Run("should success create status list(with white space)", func(t *testing.T) {
		str := "DRIVER_MATCHED, DRIVER_TO_RESTAURANT, DRIVER_ARRIVED_RESTAURANT"
		expected := []Status{StatusDriverMatched, StatusDriverToRestaurant, StatusDriverArrivedRestaurant}

		result := StatusFromString(str)

		assert.Equal(t, expected, result)
	})

	t.Run("should return empty status list(empty string)", func(t *testing.T) {
		str := ""
		expected := []Status{}

		result := StatusFromString(str)

		assert.Equal(t, expected, result)
	})

	t.Run("should return empty status list(whitespace string)", func(t *testing.T) {
		str := " \t \n"
		expected := []Status{}

		result := StatusFromString(str)

		assert.Equal(t, expected, result)
	})

	t.Run("should return empty status list(string with comma)", func(t *testing.T) {
		str := ", , "
		expected := []Status{}

		result := StatusFromString(str)

		assert.Equal(t, expected, result)
	})
}

func TestStatusFromStringList(t *testing.T) {
	t.Run("should success create status list", func(t *testing.T) {
		strList := []string{"DRIVER_MATCHED", "DRIVER_TO_RESTAURANT", "DRIVER_ARRIVED_RESTAURANT"}
		expected := []Status{StatusDriverMatched, StatusDriverToRestaurant, StatusDriverArrivedRestaurant}

		result := StatusFromStringList(strList)

		assert.Equal(t, expected, result)
	})

	t.Run("should success create status list(with whitespace string)", func(t *testing.T) {
		strList := []string{" DRIVER_MATCHED ", " DRIVER_TO_RESTAURANT", "DRIVER_ARRIVED_RESTAURANT "}
		expected := []Status{StatusDriverMatched, StatusDriverToRestaurant, StatusDriverArrivedRestaurant}

		result := StatusFromStringList(strList)

		assert.Equal(t, expected, result)
	})

	t.Run("should return empty status list(empty string list)", func(t *testing.T) {
		strList := []string{}
		expected := []Status{}

		result := StatusFromStringList(strList)

		assert.Equal(t, expected, result)
	})

	t.Run("should return empty status list(list with one empty string)", func(t *testing.T) {
		strList := []string{""}
		expected := []Status{}

		result := StatusFromStringList(strList)

		assert.Equal(t, expected, result)
	})

	t.Run("should return empty status list(list with multiple empty strings)", func(t *testing.T) {
		strList := []string{"", "", "", ""}
		expected := []Status{}

		result := StatusFromStringList(strList)

		assert.Equal(t, expected, result)
	})

	t.Run("should return status list(list with multiple empty strings and correct status string)", func(t *testing.T) {
		strList := []string{"", "", "DRIVER_TO_RESTAURANT", "DRIVER_ARRIVED_RESTAURANT"}
		expected := []Status{StatusDriverToRestaurant, StatusDriverArrivedRestaurant}

		result := StatusFromStringList(strList)

		assert.Equal(t, expected, result)
	})
}

func TestStringFromStatusList(t *testing.T) {
	t.Run("should return empty string with empty status slice", func(t *testing.T) {
		statsusList := []Status{}
		expected := ""

		result := StringFromStatusList(statsusList)

		assert.Equal(t, expected, result)
	})

	t.Run("should return correct string(multiple slice element)", func(t *testing.T) {
		statsusList := []Status{StatusDriverMatched, StatusDriverArrivedRestaurant}
		expected := "DRIVER_MATCHED,DRIVER_ARRIVED_RESTAURANT"

		result := StringFromStatusList(statsusList)

		assert.Equal(t, expected, result)
	})

	t.Run("should return correct string(1 slice element)", func(t *testing.T) {
		statsusList := []Status{StatusDriverArrivedRestaurant}
		expected := "DRIVER_ARRIVED_RESTAURANT"

		result := StringFromStatusList(statsusList)

		assert.Equal(t, expected, result)
	})
}

func TestOrder_IsRiderInvolveCashPayment(t *testing.T) {
	tests := []struct {
		name   string
		order  Order
		expect bool
	}{
		{
			"cash true",
			Order{
				Quote: Quote{
					ServiceType: ServiceFood,
					Routes: []Stop{
						{},
						{PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								PaymentMethod: PaymentMethodCash,
							},
						}},
					},
				},
			},
			true,
		},
		{
			"cash collection false",
			Order{
				Quote: Quote{
					ServiceType: ServiceFood,
					Routes: []Stop{
						{},
						{PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								PaymentMethod: PaymentMethodCash,
							},
						}},
					},
					Options: OrderOptions{DriverMoneyFlow: FlowCashCollection},
				},
			},
			false,
		},
		{
			"cash advance true",
			Order{
				Quote: Quote{
					ServiceType: ServiceFood,
					Routes: []Stop{
						{},
						{PriceSummary: PriceSummary{
							ItemFee: ItemFeeSummary{
								Discounts: DiscountList{},
							},
							DeliveryFee: DeliveryFeeSummary{},
						}},
					},
					Options: OrderOptions{
						DriverMoneyFlow: FlowCashAdvancementCoupon,
					},
				},
			},
			true,
		},
		{
			"cash advance epayment true",
			Order{
				Quote: Quote{
					ServiceType: ServiceFood,
					Routes: []Stop{
						{},
						{PriceSummary: PriceSummary{
							ItemFee: ItemFeeSummary{
								Discounts: DiscountList{},
							},
							DeliveryFee: DeliveryFeeSummary{},
						}},
					},
					Options: OrderOptions{
						DriverMoneyFlow: FlowCashAdvancementEpayment,
					},
				},
			},
			true,
		},
		{
			"not match return false",
			Order{
				Quote: Quote{
					ServiceType: ServiceFood,
					Routes: []Stop{
						{},
						{PriceSummary: PriceSummary{
							ItemFee: ItemFeeSummary{
								Discounts: DiscountList{},
							},
							DeliveryFee: DeliveryFeeSummary{},
						}},
					},
					Options: OrderOptions{
						DriverMoneyFlow: FlowCashCollection,
					},
				},
			},
			false,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			act := test.order.IsRiderInvolveCashPayment()
			assert.Equal(t, test.expect, act)
		})
	}
}

func TestIsB2BCompatible(t *testing.T) {
	t.Run("should return true(with correct status)", func(t *testing.T) {
		status := StatusDriverToRestaurant
		allowStatus := []Status{StatusDriverToRestaurant}
		expected := true

		result := status.IsB2BCompatible(allowStatus)

		assert.Equal(t, expected, result)
	})

	t.Run("should return true(with StatusDriverToDestination status)", func(t *testing.T) {
		status := StatusDriverToDestination
		allowStatus := []Status{StatusDriverToRestaurant}
		expected := true

		result := status.IsB2BCompatible(allowStatus)

		assert.Equal(t, expected, result)
	})

	t.Run("should return true(with StatusDriverArrived status)", func(t *testing.T) {
		status := StatusDriverArrived
		allowStatus := []Status{StatusDriverToRestaurant}
		expected := true

		result := status.IsB2BCompatible(allowStatus)

		assert.Equal(t, expected, result)
	})

	t.Run("should return true(with StatusDropOffDone status)", func(t *testing.T) {
		status := StatusDropOffDone
		allowStatus := []Status{StatusDriverToRestaurant}
		expected := true

		result := status.IsB2BCompatible(allowStatus)

		assert.Equal(t, expected, result)
	})

	t.Run("should return true(with empty allow status)", func(t *testing.T) {
		status := StatusDriverArrived
		allowStatus := []Status{}
		expected := true

		result := status.IsB2BCompatible(allowStatus)

		assert.Equal(t, expected, result)
	})

	t.Run("should return false(with empty allow status)", func(t *testing.T) {
		status := StatusDriverToRestaurant
		allowStatus := []Status{}
		expected := false

		result := status.IsB2BCompatible(allowStatus)

		assert.Equal(t, expected, result)
	})

	t.Run("should return false(with not match allow status)", func(t *testing.T) {
		status := StatusDriverToRestaurant
		allowStatus := []Status{StatusDriverMatched, StatusDriverArrivedRestaurant}
		expected := false

		result := status.IsB2BCompatible(allowStatus)

		assert.Equal(t, expected, result)
	})
}

func TestOrder_IsFinished(t *testing.T) {
	t.Parallel()

	testcases := []struct {
		OrderStatus Status
		isFinished  bool
	}{
		{
			OrderStatus: StatusCompleted,
			isFinished:  true,
		},
		{
			OrderStatus: StatusCanceled,
			isFinished:  true,
		},
		{
			OrderStatus: StatusDriverMatched,
			isFinished:  false,
		},
		{
			OrderStatus: StatusExpired,
			isFinished:  false,
		},
		{
			OrderStatus: StatusArrivedAt,
			isFinished:  false,
		},
	}

	for i, testcase := range testcases {
		order := &Order{
			Status: testcase.OrderStatus,
		}
		require.Equal(t, testcase.isFinished, order.IsFinished(), fmt.Sprintf("Test case # %d", i+1))
	}
}

func TestOrder_Cancel(t *testing.T) {
	t.Run("should not cancel order when the order is already completed", func(tt *testing.T) {
		order := Order{Status: StatusCompleted}
		require.Error(tt, order.Cancel(CancelDetail{}))
	})

	t.Run("should not cancel order when the order is already canceled", func(tt *testing.T) {
		order := Order{Status: StatusCanceled}
		require.Error(tt, order.Cancel(CancelDetail{}))
	})
}

func TestOrder_useOnTop(t *testing.T) {
	onTop := []OnTopScheme{
		{
			Scheme:       FlexibleFlatRateScheme,
			Amount:       10,
			BundleAmount: 20,
		},
	}
	deliveryFee := DeliveryFeeSummary{
		Total:              0,
		RawBaseFee:         50,
		OnTopFare:          0,
		RawOnTopFare:       10,
		RawBundleOnTopFare: 20,
		OnTopScheme:        onTop,
	}

	t.Run("should use normal ontop(principal)", func(tt *testing.T) {
		order := &Order{}
		order.PayAtStop = 0
		order.Routes = []Stop{
			{
				PriceSummary: PriceSummary{
					DeliveryFee: deliveryFee,
				},
			},
		}
		order.RevenuePrincipalModel = true
		order.UseNormalOnTop(0.1, 0.1)

		assert.Equal(tt, float64(10), order.PriceSummary().DeliveryFee.OnTopFare)
		assert.Equal(tt, float64(1), order.PriceSummary().DeliveryFee.OnTopWithholdingTax)
		assert.Equal(tt, float64(0), order.PriceSummary().DeliveryFee.OnTopCommissionFare)
	})

	t.Run("should use bundle ontop(principal)", func(tt *testing.T) {
		order := &Order{}
		order.PayAtStop = 0
		order.Routes = []Stop{
			{
				PriceSummary: PriceSummary{
					DeliveryFee: deliveryFee,
				},
			},
		}
		order.RevenuePrincipalModel = true
		order.UseBundleOnTop(0.1, 0.1)

		assert.Equal(tt, float64(20), order.PriceSummary().DeliveryFee.OnTopFare)
		assert.Equal(tt, float64(2), order.PriceSummary().DeliveryFee.OnTopWithholdingTax)
		assert.Equal(tt, float64(0), order.PriceSummary().DeliveryFee.OnTopCommissionFare)
	})

	t.Run("should use normal ontop(agent)", func(tt *testing.T) {
		order := &Order{}
		order.PayAtStop = 0
		order.Routes = []Stop{
			{
				PriceSummary: PriceSummary{
					DeliveryFee: deliveryFee,
				},
			},
		}
		order.RevenuePrincipalModel = false
		order.UseNormalOnTop(0.1, 0.1)

		assert.Equal(tt, float64(10), order.PriceSummary().DeliveryFee.OnTopFare)
		assert.Equal(tt, float64(0), order.PriceSummary().DeliveryFee.OnTopWithholdingTax)
		assert.Equal(tt, float64(1), order.PriceSummary().DeliveryFee.OnTopCommissionFare)
	})

	t.Run("should use bundle ontop(agent)", func(tt *testing.T) {
		order := &Order{}
		order.PayAtStop = 0
		order.Routes = []Stop{
			{
				PriceSummary: PriceSummary{
					DeliveryFee: deliveryFee,
				},
			},
		}
		order.RevenuePrincipalModel = false
		order.UseBundleOnTop(0.1, 0.1)

		assert.Equal(tt, float64(20), order.PriceSummary().DeliveryFee.OnTopFare)
		assert.Equal(tt, float64(0), order.PriceSummary().DeliveryFee.OnTopWithholdingTax)
		assert.Equal(tt, float64(2), order.PriceSummary().DeliveryFee.OnTopCommissionFare)
	})
}

func TestCountDriverAutoAssignedCompletedRecords_GroupByDriverID(t *testing.T) {
	tests := []struct {
		name string
		r    CountDriverAutoAssignedRecords
		want map[string]CountDriverAutoAssigned
	}{
		{
			name: "empty",
			r:    CountDriverAutoAssignedRecords{},
			want: map[string]CountDriverAutoAssigned{},
		},
		{
			name: "group by driver id correctly",
			r: CountDriverAutoAssignedRecords{
				CountDriverAutoAssigned{
					Driver:                     "A",
					AutoAssignedCompletedCount: types.NewInt(1),
				},
				CountDriverAutoAssigned{
					Driver:                     "B",
					AutoAssignedCompletedCount: types.NewInt(2),
				},
				CountDriverAutoAssigned{
					Driver:                     "C",
					AutoAssignedCompletedCount: types.NewInt(3),
				},
			},
			want: map[string]CountDriverAutoAssigned{
				"A": {
					Driver:                     "A",
					AutoAssignedCompletedCount: types.NewInt(1),
				},
				"B": {
					Driver:                     "B",
					AutoAssignedCompletedCount: types.NewInt(2),
				},
				"C": {
					Driver:                     "C",
					AutoAssignedCompletedCount: types.NewInt(3),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.r.GroupByDriverID(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CountDriverAutoAssignedRecords.GroupByDriverID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestCountDriverAutoAssignedCompletedRecords_FillDriverMissingServiceType(t *testing.T) {
	tests := []struct {
		name string
		r    CountDriverAutoAssignedRecords
		m    map[string]CountDriverAutoAssigned
		want CountDriverAutoAssignedRecords
	}{
		{
			name: "should fill empty service type",
			r: CountDriverAutoAssignedRecords{
				CountDriverAutoAssigned{
					Driver:                     "A",
					AutoAssignedCompletedCount: types.NewInt(1),
					AutoAssignedCanceledCount:  types.NewInt(0),
					Service:                    "food",
				},
				CountDriverAutoAssigned{
					Driver:                     "A",
					AutoAssignedCompletedCount: types.NewInt(2),
					AutoAssignedCanceledCount:  types.NewInt(0),
					Service:                    "mart",
				},
				CountDriverAutoAssigned{
					Driver:                     "B",
					AutoAssignedCompletedCount: types.NewInt(5),
					AutoAssignedCanceledCount:  types.NewInt(0),
					Service:                    "messenger",
				},
			},
			m: map[string]CountDriverAutoAssigned{
				"A": {},
				"B": {},
			},
			want: CountDriverAutoAssignedRecords{
				{
					Service:                    "food",
					Driver:                     "A",
					AutoAssignedCompletedCount: types.NewInt(1),
					AutoAssignedCanceledCount:  types.NewInt(0),
				},
				{
					Service:                    "mart",
					Driver:                     "A",
					AutoAssignedCompletedCount: types.NewInt(2),
					AutoAssignedCanceledCount:  types.NewInt(0),
				},
				{
					Service:                    "messenger",
					Driver:                     "B",
					AutoAssignedCompletedCount: types.NewInt(5),
					AutoAssignedCanceledCount:  types.NewInt(0),
				},
				// fill missing svc
				{
					Service:                    "messenger",
					Driver:                     "A",
					AutoAssignedCompletedCount: types.NewInt(0),
					AutoAssignedCanceledCount:  types.NewInt(0),
				},
				// fill missing svc
				{
					Service:                    "food",
					Driver:                     "B",
					AutoAssignedCompletedCount: types.NewInt(0),
					AutoAssignedCanceledCount:  types.NewInt(0),
				},
				// fill missing svc
				{
					Service:                    "mart",
					Driver:                     "B",
					AutoAssignedCompletedCount: types.NewInt(0),
					AutoAssignedCanceledCount:  types.NewInt(0),
				},
				// fill missing svc
				{
					Service:                    "bike",
					Driver:                     "A",
					AutoAssignedCompletedCount: types.NewInt(0),
					AutoAssignedCanceledCount:  types.NewInt(0),
				},
				// fill missing svc
				{
					Service:                    "bike",
					Driver:                     "B",
					AutoAssignedCompletedCount: types.NewInt(0),
					AutoAssignedCanceledCount:  types.NewInt(0),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := tt.r.FillDriverMissingServiceType(tt.m)
			require.ElementsMatch(t, got, tt.want)
		})
	}
}

func TestMapDateCountAutoAssignedCompletedRecords_MapDriverAndDateWithAutoAssignedCompletedRecords(t *testing.T) {
	tests := []struct {
		name string
		r    MapDateCountAutoAssignedRecords
		want MapDriverAndDateWithAutoAssignedRecords
	}{
		{
			name: "empty",
			r:    MapDateCountAutoAssignedRecords{},
			want: MapDriverAndDateWithAutoAssignedRecords{},
		},
		{
			name: "success",
			r: MapDateCountAutoAssignedRecords{
				timeutil.ConvertYYYYMMDDToDate("2022-09-22"): CountDriverAutoAssignedRecords{
					{Driver: "A", Service: "food"},
					{Driver: "B", Service: "food"},
					{Driver: "C", Service: "mart"},
				},
				timeutil.ConvertYYYYMMDDToDate("2022-09-23"): CountDriverAutoAssignedRecords{
					{Driver: "AA", Service: "food"},
					{Driver: "BB", Service: "food"},
					{Driver: "CC", Service: "mart"},
				},
			},
			want: MapDriverAndDateWithAutoAssignedRecords{
				"2022-09-22_A_food":  CountDriverAutoAssigned{Driver: "A", Service: "food"},
				"2022-09-22_B_food":  CountDriverAutoAssigned{Driver: "B", Service: "food"},
				"2022-09-22_C_mart":  CountDriverAutoAssigned{Driver: "C", Service: "mart"},
				"2022-09-23_AA_food": CountDriverAutoAssigned{Driver: "AA", Service: "food"},
				"2022-09-23_BB_food": CountDriverAutoAssigned{Driver: "BB", Service: "food"},
				"2022-09-23_CC_mart": CountDriverAutoAssigned{Driver: "CC", Service: "mart"},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.r.MapDriverAndDateWithAutoAssignedRecords(); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MapDateCountAutoAssignedRecords.MapDriverAndDateWithAutoAssignedRecords() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestMapDriverAndDateWithAutoAssignedCompletedRecords_Get(t *testing.T) {
	type args struct {
		date     time.Time
		driverID string
		service  string
	}
	tests := []struct {
		name  string
		r     MapDriverAndDateWithAutoAssignedRecords
		args  args
		want  CountDriverAutoAssigned
		want1 bool
	}{
		{
			name: "get success",
			r: MapDriverAndDateWithAutoAssignedRecords{
				"2022-09-22_A_food": CountDriverAutoAssigned{Driver: "A", Service: "food"},
			},
			args: args{
				date:     timeutil.ConvertYYYYMMDDToDate("2022-09-22"),
				driverID: "A",
				service:  "food",
			},
			want:  CountDriverAutoAssigned{Driver: "A", Service: "food"},
			want1: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1 := tt.r.Get(tt.args.date, tt.args.driverID, tt.args.service)
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MapDriverAndDateWithAutoAssignedRecords.Get() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("MapDriverAndDateWithAutoAssignedRecords.Get() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func TestOrder_GetTransactionInfoOnTopDetails(t *testing.T) {
	t.Parallel()

	t.Run("should return empty array when order has no on-top scheme", func(t *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "orderID")

		require.Equal(t, []TransactionInfoOnTopDetail{}, order.getTransactionInfoOnTopDetails())
	})

	t.Run("should return the transaction on-top detail correctly", func(t *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									Name:             "RAIN_SURGE",
									Amount:           10.0,
									BundleAmount:     10.0,
									IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
								},
								{
									Name:             "SPAM_SURGE",
									Amount:           15.0,
									BundleAmount:     15.0,
									IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "orderID")

		require.Equal(t, []TransactionInfoOnTopDetail{
			{
				Name:   "RAIN_SURGE",
				Source: "USER_PRICE_INTERVENTION",
				Amount: 10.0,
			},
			{
				Name:   "SPAM_SURGE",
				Source: "USER_PRICE_INTERVENTION",
				Amount: 15.0,
			},
		}, order.getTransactionInfoOnTopDetails())
	})

	t.Run("should return the transaction PIP custom on-top detail correctly", func(t *testing.T) {
		quote := Quote{
			PayAtStop: 1,
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									Name:             "SPAM_SURGE",
									Amount:           15.0,
									BundleAmount:     15.0,
									IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
								},
								{
									Name:             "DRIVER PIP SURGE",
									Amount:           48.0,
									BundleAmount:     48.0,
									IncentiveSources: []string{"DRIVER_PRICE_INTERVENTION"},
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceBike,
		}

		order := NewOrder(quote, "orderID")

		require.Equal(t, []TransactionInfoOnTopDetail{
			{
				Name:   "SPAM_SURGE",
				Source: "USER_PRICE_INTERVENTION",
				Amount: 15.0,
			},
			{
				Name:   "DRIVER PIP SURGE",
				Source: "DRIVER_PRICE_INTERVENTION",
				Amount: 48.0,
			},
		}, order.getTransactionInfoOnTopDetails())
	})

	t.Run("should return the transaction on-top detail correctly with optional source", func(t *testing.T) {
		quote := Quote{
			Routes: []Stop{
				{
					ID:           "San Yod Restaurant",
					PriceSummary: PriceSummary{},
				},
				{
					ID: "X's home",
					PriceSummary: PriceSummary{
						DeliveryFee: DeliveryFeeSummary{
							PaymentMethod: PaymentMethodCash,
							OnTopScheme: []OnTopScheme{
								{
									Name:         "SOME_ON_TOP",
									Amount:       10.0,
									BundleAmount: 10.0,
								},
							},
						},
					},
				},
			},
			ServiceType: ServiceFood,
		}

		order := NewOrder(quote, "orderID")

		require.Equal(t, []TransactionInfoOnTopDetail{
			{
				Name:   "SOME_ON_TOP",
				Amount: 10.0,
			},
		}, order.getTransactionInfoOnTopDetails())
	})
}

func TestQuote_SetUserPriceInterventionSchemes(t *testing.T) {
	quote := Quote{
		PayAtStop: 1,
		Routes: []Stop{
			{
				ID:           "San Yod Restaurant",
				PriceSummary: PriceSummary{},
			},
			{
				ID: "X's home",
				PriceSummary: PriceSummary{
					DeliveryFee: DeliveryFeeSummary{
						PaymentMethod: PaymentMethodCash,
						OnTopScheme: []OnTopScheme{
							{
								Name:         "OTHER_ON_TOP_A",
								Scheme:       FlatRateScheme,
								Amount:       15.0,
								BundleAmount: 15.0,
							},
							{
								Name:         "RAIN_SURGE",
								Scheme:       UserPriceInterventionOnTopScheme,
								Amount:       20.0,
								BundleAmount: 20.0,
							},
							{
								Name:         "OTHER_ON_TOP_B",
								Scheme:       DistanceScheme,
								Amount:       10.0,
								BundleAmount: 10.0,
							},
						},
					},
				},
			},
		},
		ServiceType: ServiceFood,
	}

	t.Run("should remove existing price intervention on-top and append the new one", func(t *testing.T) {
		pi := []OnTopScheme{
			{
				Scheme:           UserPriceInterventionOnTopScheme,
				Name:             "RAIN_SURGE",
				Amount:           5.0,
				BundleAmount:     5.0,
				IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
			},
		}
		quote.SetUserPriceInterventionSchemes(pi)
		require.Equal(t, []OnTopScheme{
			{
				Name:         "OTHER_ON_TOP_A",
				Scheme:       FlatRateScheme,
				Amount:       15.0,
				BundleAmount: 15.0,
			},
			{
				Name:         "OTHER_ON_TOP_B",
				Scheme:       DistanceScheme,
				Amount:       10.0,
				BundleAmount: 10.0,
			},
			{
				Scheme:           UserPriceInterventionOnTopScheme,
				Name:             "RAIN_SURGE",
				Amount:           5.0,
				BundleAmount:     5.0,
				IncentiveSources: []string{"USER_PRICE_INTERVENTION"},
			},
		}, quote.Routes[quote.GetPayAtStop()].PriceSummary.DeliveryFee.OnTopScheme)
	})
}

func TestOrder_GetTotalDurationInSeconds(t *testing.T) {
	t.Run("should return correct value", func(t *testing.T) {
		order := &Order{
			Quote: Quote{
				CreatedAt: time.Date(2023, 10, 3, 9, 0, 0, 0, timeutil.BangkokLocation()),
			},
			History: map[string]time.Time{
				string(StatusCompleted): time.Date(2023, 10, 3, 10, 0, 0, 0, timeutil.BangkokLocation()),
			},
		}
		totalDurationInSeconds := order.GetTotalDurationInSeconds()
		require.Equal(t, 3600.0, totalDurationInSeconds)
	})

	t.Run("should return zero when order not completed", func(t *testing.T) {
		order := &Order{
			Quote: Quote{
				CreatedAt: time.Date(2023, 10, 3, 9, 0, 0, 0, timeutil.BangkokLocation()),
			},
		}
		totalDurationInSeconds := order.GetTotalDurationInSeconds()
		require.Equal(t, 0.0, totalDurationInSeconds)
	})

	t.Run("should return zero when complete time is before created time", func(t *testing.T) {
		order := &Order{
			Quote: Quote{
				CreatedAt: time.Date(2023, 10, 3, 9, 0, 0, 0, timeutil.BangkokLocation()),
			},
			History: map[string]time.Time{
				string(StatusCompleted): time.Date(2023, 10, 3, 8, 0, 0, 0, timeutil.BangkokLocation()),
			},
		}
		totalDurationInSeconds := order.GetTotalDurationInSeconds()
		require.Equal(t, 0.0, totalDurationInSeconds)
	})
}

func Test_buildRequestedTime(t *testing.T) {
	t.Parallel()

	type args struct {
		o *Order
	}
	tests := []struct {
		name string
		args args
		want time.Time
	}{
		{
			name: "Before midnight (BKK Time)",
			args: args{
				o: &Order{
					Quote: Quote{
						CreatedAt: time.Date(2023, 11, 22, 16, 59, 59, 0, time.UTC), // == 22/11/2023 23:59:59 BKK
					},
				},
			},
			want: time.Date(2023, 11, 22, 0, 0, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "Midnight (BKK Time)",
			args: args{
				o: &Order{
					Quote: Quote{
						CreatedAt: time.Date(2023, 11, 22, 17, 0, 0, 0, time.UTC), // == 23/11/2023 00:00:00 BKK
					},
				},
			},
			want: time.Date(2023, 11, 23, 0, 0, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "After midnight (BKK Time)",
			args: args{
				o: &Order{
					Quote: Quote{
						CreatedAt: time.Date(2023, 11, 22, 17, 1, 0, 0, time.UTC), // == 23/11/2023 00:01:00 BKK
					},
				},
			},
			want: time.Date(2023, 11, 23, 0, 0, 0, 0, timeutil.BangkokLocation()),
		},
		{
			name: "After midnight (UTC Time)",
			args: args{
				o: &Order{
					Quote: Quote{
						CreatedAt: time.Date(2023, 11, 22, 0, 1, 0, 0, time.UTC), // == 22/11/2023 07:01:00 BKK
					},
				},
			},
			want: time.Date(2023, 11, 22, 0, 0, 0, 0, timeutil.BangkokLocation()),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := buildRequestedTime(tt.args.o); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("buildRequestedTime() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestOrder_UserPhones(t *testing.T) {
	phones0 := []string{"0800000000"}
	phones1 := []string{"0811111111"}
	order := &Order{
		Quote: Quote{
			Routes: []Stop{{Phones: phones0}, {Phones: phones1}},
		},
	}

	type TestCase struct {
		service Service
		expect  []string
	}

	testCases := []TestCase{
		{
			service: ServiceFood,
			expect:  phones1,
		},
		{
			service: ServiceMart,
			expect:  phones1,
		},
		{
			service: ServiceMessenger,
			expect:  phones1,
		},
		{
			service: ServiceBike,
			expect:  phones0,
		},
	}

	for _, tc := range testCases {
		t.Run(fmt.Sprintf("should return user phones in order correctly with service %s", tc.service), func(t *testing.T) {
			order := order
			order.ServiceType = tc.service
			require.Equal(t, tc.expect, order.UserPhones())
		})
	}
}

func TestOrder_Memo(t *testing.T) {
	type MemoTest struct {
		Memo     string   `json:"memo" bson:"memo"`
		MemoType MemoType `json:"memoType" bson:"memoType"`
	}

	shouldOk := []MemoType{
		MemoTypeText,
		MemoTypeHandToUser,
		MemoTypeLeaveAtPlace,
		MemoTypeLeaveWithPerson,
	}

	t.Run("MemoTypes", func(tt *testing.T) {
		for i := range shouldOk {
			require.True(tt, shouldOk[i].IsValid())
			require.Equal(tt, shouldOk[i], shouldOk[i].ToValid())
		}
	})

	t.Run("MemoTypes JSON values", func(tt *testing.T) {
		for i := range shouldOk {
			memoType := shouldOk[i]
			b, err := json.Marshal(memoType)
			require.NoError(tt, err, "failed to marshal MemoType JSON")

			var resultMemoType MemoType
			err = json.Unmarshal(b, &resultMemoType)
			require.NoError(tt, err, "failed to unmarshal MemoType JSON")

			require.Equal(tt, memoType, resultMemoType)
		}
	})

	t.Run("MemoType JSON unmarshal", func(tt *testing.T) {
		type test struct {
			jsonStr  string
			memo     string
			memoType MemoType
		}

		tests := []test{
			{
				jsonStr:  `{"memo":"1","memoType":"HAND_TO_USER"}`,
				memo:     "1",
				memoType: MemoTypeHandToUser,
			},
			{
				jsonStr:  `{"memo":"2","memoType":"LEAVE_AT_PLACE"}`,
				memo:     "2",
				memoType: MemoTypeLeaveAtPlace,
			},
			{
				jsonStr:  `{"memo":"3","memoType":"LEAVE_WITH_PERSON"}`,
				memo:     "3",
				memoType: MemoTypeLeaveWithPerson,
			},
		}

		for i := range tests {
			testCase := &tests[i]
			var memoTest MemoTest
			err := json.Unmarshal([]byte(testCase.jsonStr), &memoTest)
			require.NoError(tt, err, "failed to unmarshal MemoTest JSON")
			require.Equal(tt, testCase.memoType, memoTest.MemoType)
		}
	})

	t.Run("MemoType JSON default values", func(tt *testing.T) {
		memo := MemoTest{
			Memo:     "someMemo",
			MemoType: MemoType("some bad value"),
		}

		j, err := json.Marshal(memo)
		require.NoError(tt, err, "failed to marshal MemoTest JSON")

		var m map[string]interface{}
		err = json.Unmarshal(j, &m)
		require.NoError(tt, err, "failed to unmarshal MemoTest JSON into a map")

		require.Equal(tt, memo.Memo, m["memo"])
		require.Equal(tt, MemoTypeText.String(), m["memoType"])
	})

	t.Run("MemoType JSON OK values", func(tt *testing.T) {
		goodMemoTypes := []MemoType{
			MemoTypeHandToUser,
			MemoTypeLeaveAtPlace,
			MemoTypeLeaveWithPerson,
			MemoTypeText,
		}

		for i := range goodMemoTypes {
			memoType := goodMemoTypes[i]
			memo := MemoTest{
				Memo:     "someMemo",
				MemoType: memoType,
			}

			j, err := json.Marshal(memo)
			require.NoError(tt, err, "failed to marshal MemoTest JSON")

			var m map[string]interface{}
			err = json.Unmarshal(j, &m)
			require.NoError(tt, err, "failed to unmarshal MemoTest JSON into a map")

			require.Equal(tt, memo.Memo, m["memo"])
			require.Equal(tt, memoType.String(), m["memoType"])
		}
	})

	t.Run("MemoType BSON default values", func(tt *testing.T) {
		memo := MemoTest{
			Memo:     "someMemo",
			MemoType: MemoType("some bad value"),
		}

		b, err := bson.Marshal(memo)
		require.NoError(tt, err, "failed to marshal MemoTest BSON")

		var m map[string]interface{}
		err = bson.Unmarshal(b, &m)
		require.NoError(tt, err, "failed to unmarshal MemoTest BSON into a map")

		require.Equal(tt, memo.Memo, m["memo"])
		require.Equal(tt, MemoTypeText.String(), m["memoType"])
	})

	t.Run("MemoType BSON OK values", func(tt *testing.T) {
		goodMemoTypes := []MemoType{
			MemoTypeHandToUser,
			MemoTypeLeaveAtPlace,
			MemoTypeLeaveWithPerson,
			MemoTypeText,
		}

		for i := range goodMemoTypes {
			memoType := goodMemoTypes[i]
			memo := MemoTest{
				Memo:     "someMemo",
				MemoType: memoType,
			}

			j, err := bson.Marshal(memo)
			require.NoError(tt, err, "failed to marshal MemoTest JSON")

			var m map[string]interface{}
			err = bson.Unmarshal(j, &m)
			require.NoError(tt, err, "failed to unmarshal MemoTest JSON into a map")

			require.Equal(tt, memo.Memo, m["memo"])
			require.Equal(tt, memoType.String(), m["memoType"])
		}
	})
}

func TestOrder_SetStatusAndHistoryIfPossible(t *testing.T) {
	type testCase struct {
		name               string
		order              Order
		status             Status
		location           Location
		addHistoryLocation bool
		shouldFail         bool
	}

	tests := []testCase{
		{
			name: "Test Case 1: Valid status and location for Revamped Service",
			order: Order{
				Status:  StatusAssigningDriver,
				History: make(map[string]time.Time),
				Quote:   Quote{ServiceType: ServiceBike},
			},
			status: StatusDriverMatched,
			location: Location{
				Lat: 10.0,
				Lng: 20.0,
			},
			addHistoryLocation: true,
		},
		{
			name: "Test Case 2: Valid status and location for Revamped Service ServiceMessenger",
			order: Order{
				Status:  StatusAssigningDriver,
				History: make(map[string]time.Time),
				Quote:   Quote{ServiceType: ServiceMessenger},
			},
			status: StatusDriverMatched,
			location: Location{
				Lat: 10.0,
				Lng: 20.0,
			},
			addHistoryLocation: true,
		},
		{
			name: "Test Case 3: Valid status and location for Non-Revamped Service ServiceFood",
			order: Order{
				Status:  StatusAssigningDriver,
				History: make(map[string]time.Time),
				Quote:   Quote{ServiceType: ServiceFood},
			},
			status: StatusDriverMatched,
			location: Location{
				Lat: 10.0,
				Lng: 20.0,
			},
			addHistoryLocation: false,
		},
		{
			name: "Test Case 4: Valid status and location for Non-Revamped Service ServiceMart",
			order: Order{
				Status:  StatusAssigningDriver,
				History: make(map[string]time.Time),
				Quote:   Quote{ServiceType: ServiceMart},
			},
			status: StatusDriverMatched,
			location: Location{
				Lat: 10.0,
				Lng: 20.0,
			},
			addHistoryLocation: false,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			tc.order.SetStatusAndHistoryIfPossible(tc.status, tc.location)
			assert.Equal(t, tc.status, tc.order.Status)
			if tc.addHistoryLocation {
				assert.Equal(t, tc.location.Lng, tc.order.HistoryLocation[fmt.Sprintf("%s", tc.status)].Lng)
				assert.Equal(t, tc.location.Lat, tc.order.HistoryLocation[fmt.Sprintf("%s", tc.status)].Lat)
			} else {
				assert.Nil(t, tc.order.HistoryLocation)
			}
		})
	}
}

func TestOrder_PauseSetSlice(t *testing.T) {
	t.Parallel()
	t.Run("success with 2 elements", func(t *testing.T) {
		ps := &PauseSet{
			PauseCheckout:  true,
			PauseQRPayment: true,
		}
		pauses := ps.Slice()
		assert.Len(t, pauses, 2)
		assert.Contains(t, pauses, string(PauseCheckout))
		assert.Contains(t, pauses, string(PauseQRPayment))
	})
	t.Run("success with 0 elements", func(t *testing.T) {
		ps := &PauseSet{}
		pauses := ps.Slice()
		assert.Len(t, pauses, 0)
	})
	t.Run("should append only true value", func(t *testing.T) {
		ps := &PauseSet{
			PauseCheckout:  true,
			PauseQRPayment: false,
		}
		pauses := ps.Slice()
		assert.Len(t, pauses, 1)
		assert.Contains(t, pauses, string(PauseCheckout))
	})
}

func TestOrder_IsArrivedAtDestination(t *testing.T) {
	t.Parallel()
	type TestCase struct {
		name                         string
		givenOrder                   Order
		expectIsArrivedAtDestination bool
	}
	testCases := []TestCase{
		{
			name: "bike arrived at destination",
			givenOrder: Order{
				Quote:  Quote{ServiceType: ServiceBike},
				Status: StatusArrivedAt,
				HeadTo: 1,
			},
			expectIsArrivedAtDestination: true,
		},
		{
			name: "bike drive to destination",
			givenOrder: Order{
				Quote:  Quote{ServiceType: ServiceBike},
				Status: StatusDriveTo,
				HeadTo: 1,
			},
			expectIsArrivedAtDestination: false,
		},
		{
			name: "bike completed",
			givenOrder: Order{
				Quote:  Quote{ServiceType: ServiceBike},
				Status: StatusCompleted,
				HeadTo: 1,
			},
			expectIsArrivedAtDestination: false,
		},
		{
			name: "not implemented",
			givenOrder: Order{
				Quote:  Quote{ServiceType: ServiceMessenger},
				Status: StatusArrivedAt,
				HeadTo: 1,
			},
			expectIsArrivedAtDestination: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			assert.Equal(t, tc.expectIsArrivedAtDestination, tc.givenOrder.IsArrivedAtDestination())
		})
	}
}

func TestOrderTestSuite(t *testing.T) {
	suite.Run(t, new(OrderTestSuite))
}

type OrderTestSuite struct {
	suite.Suite
}

func (s *OrderTestSuite) TestGetTripTransactions() {
	s.Run("Principal Model - Only InstallmentOnTopScheme", func() {
		underTest := Order{
			Quote: Quote{
				ServiceType:           ServiceFood,
				RevenuePrincipalModel: true,
				Routes: []Stop{
					{},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								OnTopScheme: []OnTopScheme{
									{
										Scheme: InstallmentOnTopScheme,
									},
								},
							},
						},
					},
				},
			},
		}

		result := underTest.GetTripTransactions()
		s.Equal([]TransactionInfo{
			{
				Category: CreditTransactionCategory,
				Type:     UserDeliveryFeeType,
			},
		}, result)
	})
	s.Run("Principal Model - Only Other Scheme", func() {
		underTest := Order{
			OrderID: "TestOrder",
			TripID:  "TestTrip",
			Quote: Quote{
				ServiceType:           ServiceFood,
				RevenuePrincipalModel: true,
				Routes: []Stop{
					{},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								OnTopFare: 12.34,
								OnTopScheme: []OnTopScheme{
									{
										Scheme:           BasketSizeScheme,
										Amount:           12.34,
										Name:             "test-name",
										IncentiveSources: []string{"test-source"},
									},
								},
							},
						},
					},
				},
			},
		}

		result := underTest.GetTripTransactions()
		s.Equal([]TransactionInfo{
			{
				Category:         WalletTransactionCategory,
				Type:             OnTopTransactionType,
				SubType:          DeliveryFeeDynamicOnTopSubType,
				Amount:           12.34,
				IncentiveNames:   []string{"test-name"},
				IncentiveSources: []string{"test-source"},
				OrderID:          "TestOrder",
				TripID:           "TestTrip",
				Remark:           "On-Top: test-name amount: 12.34 THB\n",
				OnTopDetails: []TransactionInfoOnTopDetail{
					{
						Name:            "test-name",
						Source:          "test-source",
						Amount:          12.34,
						OnTopFareScheme: BasketSizeScheme,
					},
				},
			},
			{
				Category: CreditTransactionCategory,
				Type:     UserDeliveryFeeType,
				OrderID:  "TestOrder",
				TripID:   "TestTrip",
			},
		}, result)
	})
	s.Run("Principal Model - 2 Schemes", func() {
		underTest := Order{
			OrderID: "TestOrder",
			TripID:  "TestTrip",
			Quote: Quote{
				ServiceType:           ServiceFood,
				RevenuePrincipalModel: true,
				Routes: []Stop{
					{},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								OnTopFare: 123.45,
								OnTopScheme: []OnTopScheme{
									{
										Scheme:           BasketSizeScheme,
										Amount:           12.34,
										Name:             "test-name-basket",
										IncentiveSources: []string{"test-source-basket"},
									},
									{
										Scheme:           InstallmentOnTopScheme,
										Amount:           56.78,
										Name:             "test-name-install",
										IncentiveSources: []string{"test-source-install"},
									},
								},
							},
						},
					},
				},
			},
		}

		result := underTest.GetTripTransactions()
		s.Equal([]TransactionInfo{
			{
				Category:         WalletTransactionCategory,
				Type:             OnTopTransactionType,
				SubType:          DeliveryFeeDynamicOnTopSubType,
				Amount:           66.67,
				IncentiveNames:   []string{"test-name-basket"},
				IncentiveSources: []string{"test-source-basket"},
				OrderID:          "TestOrder",
				TripID:           "TestTrip",
				Remark:           "On-Top: test-name-basket amount: 12.34 THB\n",
				OnTopDetails: []TransactionInfoOnTopDetail{
					{
						Name:            "test-name-basket",
						Source:          "test-source-basket",
						Amount:          12.34,
						OnTopFareScheme: BasketSizeScheme,
					},
				},
			},
			{
				Category: CreditTransactionCategory,
				Type:     UserDeliveryFeeType,
				OrderID:  "TestOrder",
				TripID:   "TestTrip",
			},
		}, result)
	})
	s.Run("Agent Model - Only InstallmentOnTopScheme", func() {
		underTest := Order{
			Quote: Quote{
				ServiceType:           ServiceFood,
				RevenuePrincipalModel: false,
				Routes: []Stop{
					{},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								OnTopScheme: []OnTopScheme{
									{
										Scheme: InstallmentOnTopScheme,
									},
								},
							},
						},
					},
				},
			},
		}

		result := underTest.GetTripTransactions()
		s.Equal([]TransactionInfo{}, result)
	})
	s.Run("Agent Model - Only Other Scheme", func() {
		underTest := Order{
			OrderID: "TestOrder",
			TripID:  "TestTrip",
			Quote: Quote{
				ServiceType:           ServiceFood,
				RevenuePrincipalModel: false,
				Routes: []Stop{
					{},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								OnTopFare: 12.34,
								OnTopScheme: []OnTopScheme{
									{
										Scheme:           BasketSizeScheme,
										Amount:           12.34,
										Name:             "test-name",
										IncentiveSources: []string{"test-source"},
									},
								},
							},
						},
					},
				},
			},
		}

		result := underTest.GetTripTransactions()
		s.Equal([]TransactionInfo{
			{
				Category: WalletTransactionCategory,
				Type:     OnTopTransactionType,
				SubType:  DeliveryFeeDynamicOnTopSubType,
				Amount:   12.34,
				OrderID:  "TestOrder",
				TripID:   "TestTrip",
				Remark:   "On-Top: test-name amount: 12.34 THB\n",
				OnTopDetails: []TransactionInfoOnTopDetail{
					{
						Name:            "test-name",
						Source:          "test-source",
						Amount:          12.34,
						OnTopFareScheme: BasketSizeScheme,
					},
				},
			},
		}, result)
	})
	s.Run("Agent Model - 2 Schemes", func() {
		underTest := Order{
			OrderID: "TestOrder",
			TripID:  "TestTrip",
			Quote: Quote{
				ServiceType:           ServiceFood,
				RevenuePrincipalModel: false,
				Routes: []Stop{
					{},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								OnTopFare: 69.12,
								OnTopScheme: []OnTopScheme{
									{
										Scheme:           BasketSizeScheme,
										Amount:           12.34,
										Name:             "test-name-basket",
										IncentiveSources: []string{"test-source-basket"},
									},
									{
										Scheme:           InstallmentOnTopScheme,
										Amount:           56.78,
										Name:             "test-name-install",
										IncentiveSources: []string{"test-source-install"},
									},
								},
							},
						},
					},
				},
			},
		}

		result := underTest.GetTripTransactions()
		s.Equal([]TransactionInfo{
			{
				Category: WalletTransactionCategory,
				Type:     OnTopTransactionType,
				SubType:  DeliveryFeeDynamicOnTopSubType,
				Amount:   12.34,
				OrderID:  "TestOrder",
				TripID:   "TestTrip",
				Remark:   "On-Top: test-name-basket amount: 12.34 THB\n",
				OnTopDetails: []TransactionInfoOnTopDetail{
					{
						Name:            "test-name-basket",
						Source:          "test-source-basket",
						Amount:          12.34,
						OnTopFareScheme: BasketSizeScheme,
					},
				},
			},
		}, result)
	})
	s.Run("Agent Model - Bike Order", func() {
		mockOrderID := "TestOrderID"
		mockTripID := "TestTripID"

		underTest := Order{
			OrderID: mockOrderID,
			TripID:  mockTripID,
			Tips: []TipRecord{
				{
					ID:        mockOrderID,
					Amount:    99,
					CreatedAt: time.Time{},
				},
			},
			Quote: Quote{
				ServiceType:           ServiceBike,
				PayAtStop:             1,
				RevenuePrincipalModel: false,
				Routes: []Stop{
					{},
					{
						PriceSummary: PriceSummary{
							DeliveryFee: DeliveryFeeSummary{
								PaymentMethod:  PaymentMethodQRPromptPay,
								CommissionRate: 0.1,

								DistanceUnitFee: 266,
								StartingFee:     56,
								CustomOnTop:     37,
								CustomOnTops: CustomOnTopList{{
									Type:   "surge_fee",
									Amount: 37,
									Custom: false,
								}},

								// 266 + 56 + 37 = 359
								SubTotal:   359,
								Total:      359,
								Commission: 35.9,

								OnTopFare:           69.0,
								RawOnTopFare:        69.0,
								RawBundleOnTopFare:  69.0,
								OnTopCommissionFare: 6.9,

								OnTopScheme: []OnTopScheme{
									{
										Scheme:           BasketSizeScheme,
										Amount:           12.3,
										Name:             "test-name-basket",
										IncentiveSources: []string{"test-source-basket"},
									},
									{
										Scheme:           InstallmentOnTopScheme,
										Amount:           56.7,
										Name:             "test-name-install",
										IncentiveSources: []string{"test-source-install"},
									},
								},
							},
						},
					},
				},
			},
		}

		mockWalletDeliveryFeeTxn := TransactionInfo{
			Category: WalletTransactionCategory,
			Type:     DeliveryFeeTransactionType,
			OrderID:  mockOrderID,
			TripID:   mockTripID,
			Amount:   359,
		}

		mockCreditDeliveryFeeCommissionTxn := TransactionInfo{
			Category: CreditTransactionCategory,
			Type:     CommissionTransactionType,
			SubType:  CommissionDeliveryFeeUserTransactionSubType,
			OrderID:  mockOrderID,
			TripID:   mockTripID,
			Amount:   35.9,
		}

		mockWalletTipTxn := TransactionInfo{
			Category:   WalletTransactionCategory,
			Type:       TipTransactionType,
			TransRefID: "TestOrderID",
			OrderID:    mockOrderID,
			RefID:      mockOrderID,
			Amount:     99,
		}

		mockWalletOnTopTxn := TransactionInfo{
			Category: WalletTransactionCategory,
			Type:     OnTopTransactionType,
			SubType:  DeliveryFeeDynamicOnTopSubType,
			OrderID:  mockOrderID,
			TripID:   mockTripID,
			Amount:   12.3,
			Remark:   "On-Top: test-name-basket amount: 12.30 THB\n",
			OnTopDetails: []TransactionInfoOnTopDetail{
				{
					Name:   "test-name-basket",
					Source: "test-source-basket",
					Amount: 12.3,
				},
			},
		}

		mockCreditOnTopCommissionTxn := TransactionInfo{
			Category: CreditTransactionCategory,
			Type:     CommissionTransactionType,
			SubType:  CommissionDeliveryFeeDynamicOnTopTransactionSubType,
			OrderID:  mockOrderID,
			TripID:   mockTripID,
			Amount:   6.9,
		}

		s.Run("normal", func() {
			result := underTest.GetTripTransactions()
			s.Equal(result[0].Category, mockWalletDeliveryFeeTxn.Category)
			s.Equal(result[0].Type, mockWalletDeliveryFeeTxn.Type)
			s.Equal(result[0].SubType, mockWalletDeliveryFeeTxn.SubType)
			s.Equal(result[0].Amount, mockWalletDeliveryFeeTxn.Amount)
			s.Empty(result[0].PendingTransactionID)

			s.Equal(result[1].Category, mockCreditDeliveryFeeCommissionTxn.Category)
			s.Equal(result[1].Type, mockCreditDeliveryFeeCommissionTxn.Type)
			s.Equal(result[1].SubType, mockCreditDeliveryFeeCommissionTxn.SubType)
			s.Equal(result[1].Amount, mockCreditDeliveryFeeCommissionTxn.Amount)
			s.Empty(result[1].PendingTransactionID)

			s.Equal(result[2].Category, mockWalletTipTxn.Category)
			s.Equal(result[2].Type, mockWalletTipTxn.Type)
			s.Equal(result[2].SubType, mockWalletTipTxn.SubType)
			s.Equal(result[2].Amount, mockWalletTipTxn.Amount)
			s.Empty(result[2].PendingTransactionID)

			s.Equal(result[3].Category, mockWalletOnTopTxn.Category)
			s.Equal(result[3].Type, mockWalletOnTopTxn.Type)
			s.Equal(result[3].SubType, mockWalletOnTopTxn.SubType)
			s.Equal(result[3].Amount, mockWalletOnTopTxn.Amount)
			s.Empty(result[3].PendingTransactionID)

			s.Equal(result[4].Category, mockCreditOnTopCommissionTxn.Category)
			s.Equal(result[4].Type, mockCreditOnTopCommissionTxn.Type)
			s.Equal(result[4].SubType, mockCreditOnTopCommissionTxn.SubType)
			s.Equal(result[4].Amount, mockCreditOnTopCommissionTxn.Amount)
			s.Empty(result[4].PendingTransactionID)
		})

		s.Run("pending delivery fee", func() {
			result := underTest.GetTripTransactions(WithDeliveryFeePending)
			s.Equal(result[0].Category, mockWalletDeliveryFeeTxn.Category)
			s.Equal(result[0].Type, mockWalletDeliveryFeeTxn.Type)
			s.Equal(result[0].SubType, mockWalletDeliveryFeeTxn.SubType)
			s.Equal(result[0].Amount, mockWalletDeliveryFeeTxn.Amount)
			s.True(result[0].IsPendingTransaction)

			s.Equal(result[1].Category, mockCreditDeliveryFeeCommissionTxn.Category)
			s.Equal(result[1].Type, mockCreditDeliveryFeeCommissionTxn.Type)
			s.Equal(result[1].SubType, mockCreditDeliveryFeeCommissionTxn.SubType)
			s.Equal(result[1].Amount, mockCreditDeliveryFeeCommissionTxn.Amount)
			s.True(result[1].IsPendingTransaction)

			s.Equal(result[2].Category, mockWalletTipTxn.Category)
			s.Equal(result[2].Type, mockWalletTipTxn.Type)
			s.Equal(result[2].SubType, mockWalletTipTxn.SubType)
			s.Equal(result[2].Amount, mockWalletTipTxn.Amount)
			s.False(result[2].IsPendingTransaction)

			s.Equal(result[3].Category, mockWalletOnTopTxn.Category)
			s.Equal(result[3].Type, mockWalletOnTopTxn.Type)
			s.Equal(result[3].SubType, mockWalletOnTopTxn.SubType)
			s.Equal(result[3].Amount, mockWalletOnTopTxn.Amount)
			s.False(result[3].IsPendingTransaction)

			s.Equal(result[4].Category, mockCreditOnTopCommissionTxn.Category)
			s.Equal(result[4].Type, mockCreditOnTopCommissionTxn.Type)
			s.Equal(result[4].SubType, mockCreditOnTopCommissionTxn.SubType)
			s.Equal(result[4].Amount, mockCreditOnTopCommissionTxn.Amount)
			s.False(result[4].IsPendingTransaction)
		})

	})

}

func TestAddQRPromptPayInfoIfPauseQRPayment(t *testing.T) {
	xgotimeutil.Freeze()
	t.Run("should set QRPromptPayInfo when PauseQRPayment is true", func(t *testing.T) {
		n := xgotimeutil.Now().UTC()
		route := &Stop{
			PriceSummary: PriceSummary{
				DeliveryFee: DeliveryFeeSummary{},
			},
			Pauses: map[Pause]bool{
				PauseQRPayment: true,
			},
		}

		addQRPromptPayInfoIfPauseQRPayment(route, n)

		require.Equal(t, QRPromptPayStatusWaitingForPayment, route.PriceSummary.DeliveryFee.QRPromptPayInfo.Status)
	})

	t.Run("should not set QRPromptPayInfo when PauseQRPayment is false", func(t *testing.T) {
		n := xgotimeutil.Now().UTC()
		route := &Stop{
			PriceSummary: PriceSummary{
				DeliveryFee: DeliveryFeeSummary{},
			},
			Pauses: map[Pause]bool{},
		}

		addQRPromptPayInfoIfPauseQRPayment(route, n)

		require.Equal(t, QRPromptPayInfo{}, route.PriceSummary.DeliveryFee.QRPromptPayInfo)
	})
	xgotimeutil.Unfreeze()
}

func TestQuote_EvaluateAutoStart(t *testing.T) {
	stopRMS := Stop{Info: StopInfoCollector{StopInfo: &StopInfoFood{
		Service:        "food",
		PriceScheme:    PriceSchemeRMS,
		RestaurantType: PriceSchemeRMS,
	}}}

	stopEMENU := Stop{Info: StopInfoCollector{StopInfo: &StopInfoFood{
		Service:        "food",
		PriceScheme:    PriceSchemeEMenu,
		RestaurantType: PriceSchemeEMenu,
	}}}

	testcases := []struct {
		name              string
		quote             Quote
		expectedAutoStart bool
	}{
		{
			name: "[Food EMENU] non-defer with normal flow",
			quote: Quote{
				ServiceType: ServiceFood,
				Options:     OrderOptions{SwitchFlow: false, CanDefer: false},
				Routes:      []Stop{stopEMENU, {}},
			},
			expectedAutoStart: true,
		},
		{
			name: "[Food RMS] non-defer with normal flow",
			quote: Quote{
				ServiceType: ServiceFood,
				Options:     OrderOptions{SwitchFlow: false, CanDefer: false},
				Routes:      []Stop{stopRMS, {}},
			},
			expectedAutoStart: false,
		},
		{
			name: "[Food RMS] non-defer with switch flow",
			quote: Quote{
				ServiceType: ServiceFood,
				Options:     OrderOptions{SwitchFlow: true, CanDefer: false},
				Routes:      []Stop{stopRMS, {}},
			},
			expectedAutoStart: true,
		},
		{
			name: "[Food RMS] defer",
			quote: Quote{
				ServiceType: ServiceFood,
				Options:     OrderOptions{SwitchFlow: true, CanDefer: true},
				Routes:      []Stop{stopRMS, {}},
			},
			expectedAutoStart: true,
		},
		{
			name: "[Mart RMS] non-defer",
			quote: Quote{
				ServiceType: ServiceMart,
				Options:     OrderOptions{SwitchFlow: false, CanDefer: false},
				StoreType:   "RMS",
				Routes:      []Stop{{}, {}},
			},
			expectedAutoStart: false,
		},
		{
			name: "[Mart RMS] defer",
			quote: Quote{
				ServiceType: ServiceMart,
				Options:     OrderOptions{SwitchFlow: true, CanDefer: true},
				StoreType:   "RMS",
				Routes:      []Stop{{}, {}},
			},
			expectedAutoStart: true,
		},
		{
			name: "[Mart SP] non-defer",
			quote: Quote{
				ServiceType: ServiceMart,
				Options:     OrderOptions{SwitchFlow: true, CanDefer: false},
				StoreType:   "SP",
				Routes:      []Stop{{}, {}},
			},
			expectedAutoStart: true,
		},
		{
			name: "[Mart SP] defer",
			quote: Quote{
				ServiceType: ServiceMart,
				Options:     OrderOptions{SwitchFlow: true, CanDefer: true},
				StoreType:   "SP",
				Routes:      []Stop{{}, {}},
			},
			expectedAutoStart: true,
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			q := tt.quote
			q.EvaluateAutoStart()
			assert.Equal(t, tt.expectedAutoStart, q.Autostart)
		})
	}
}

func TestQuote_IsDeliveryFeeChanged(t *testing.T) {
	t.Parallel()

	testcases := []struct {
		name           string
		quote          Quote
		anotherQuote   *Quote
		expectedResult bool
	}{
		{
			name: "Both quotes have same delivery fee",
			quote: Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 10.0}}}},
				PayAtStop: 0,
			},
			anotherQuote: &Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 10.0}}}},
				PayAtStop: 0,
			},
			expectedResult: false,
		},
		{
			name: "Different delivery fees at the same stop",
			quote: Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 10.0}}}},
				PayAtStop: 0,
			},
			anotherQuote: &Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 15.0}}}},
				PayAtStop: 0,
			},
			expectedResult: true,
		},
		{
			name: "Invalid PayAtStop in quote",
			quote: Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 10.0}}}},
				PayAtStop: -1,
			},
			anotherQuote: &Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 10.0}}}},
				PayAtStop: 0,
			},
			expectedResult: false,
		},
		{
			name: "Invalid PayAtStop in anotherQuote",
			quote: Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 10.0}}}},
				PayAtStop: 0,
			},
			anotherQuote: &Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 10.0}}}},
				PayAtStop: -1,
			},
			expectedResult: false,
		},
		{
			name: "anotherQuote is nil",
			quote: Quote{
				Routes:    []Stop{{PriceSummary: PriceSummary{DeliveryFee: DeliveryFeeSummary{SubTotal: 10.0}}}},
				PayAtStop: 0,
			},
			anotherQuote:   nil,
			expectedResult: false,
		},
	}

	for _, tt := range testcases {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.quote.IsDeliveryFeeChanged(tt.anotherQuote)
			assert.Equal(t, tt.expectedResult, result)
		})
	}
}

func TestAdditionalServiceSummary_SetCommission(t *testing.T) {
	t.Parallel()

	t.Run("given total 25 and commission rate 0.15 commission should be 3.75", func(t *testing.T) {
		sum := &AdditionalServiceSummary{
			Total:      25.00,
			Commission: 0,
		}

		sum.SetCommission(0.15)

		assert.Equal(t, 3.75, sum.Commission)
	})

	t.Run("given total 24 and commission rate 0.15 commission should be 3.6", func(t *testing.T) {
		sum := &AdditionalServiceSummary{
			Total:      24.00,
			Commission: 0,
		}

		sum.SetCommission(0.15)

		assert.Equal(t, 3.6, sum.Commission)
	})

	t.Run("given total 23 and commission rate 0.15 commission should be 3.45", func(t *testing.T) {
		sum := &AdditionalServiceSummary{
			Total:      23.00,
			Commission: 0,
		}

		sum.SetCommission(0.15)

		assert.Equal(t, 3.45, sum.Commission)
	})

	t.Run("given total 20 and commission rate 0.15 commission should be 3", func(t *testing.T) {
		sum := &AdditionalServiceSummary{
			Total:      20.00,
			Commission: 0,
		}

		sum.SetCommission(0.15)

		assert.Equal(t, 3.0, sum.Commission)
	})

	t.Run("given total 100 and commission rate 0.009 commission should be 0.9", func(t *testing.T) {
		sum := &AdditionalServiceSummary{
			Total:      100.00,
			Commission: 0,
		}

		sum.SetCommission(0.009)

		assert.Equal(t, 0.9, sum.Commission)
	})

	t.Run("given total 100 and commission rate 0.0009 commission should be 0.09", func(t *testing.T) {
		sum := &AdditionalServiceSummary{
			Total:      100.00,
			Commission: 0,
		}

		sum.SetCommission(0.0009)

		assert.Equal(t, 0.09, sum.Commission)
	})

	t.Run("given total 100 and commission rate 0.00009 commission should be 0.01", func(t *testing.T) {
		sum := &AdditionalServiceSummary{
			Total:      100.00,
			Commission: 0,
		}

		sum.SetCommission(0.00009)

		assert.Equal(t, 0.01, sum.Commission)
	})
}
