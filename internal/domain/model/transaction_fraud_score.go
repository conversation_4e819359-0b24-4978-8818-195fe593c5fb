package model

type TransactionFraudScore struct {
	TransactionID    string
	UnusualScore     int
	OrderFraudScores []OrderFraudScore
}

func NewTransactionFraudScore(transactionId string, unusualScore int, orderFraudScores []OrderFraudScore) *TransactionFraudScore {
	return &TransactionFraudScore{
		TransactionID:    transactionId,
		UnusualScore:     unusualScore,
		OrderFraudScores: orderFraudScores,
	}
}

type OrderFraudScore struct {
	OrderID                   string
	Score                     int
	TurnAroundTime            int
	DriverRestaurantDistance  int
	DriverDestinationDistance int
	MatchSameUser             int
	FraudRule                 map[string]bool
}

func NewOrderFraudScore(orderID string, score, turnAroundTime, driverRestaurantDistance, driverDestinationDistance, matchSameUser int, fraudRule map[string]bool) *OrderFraudScore {
	return &OrderFraudScore{
		OrderID:                   orderID,
		Score:                     score,
		TurnAroundTime:            turnAroundTime,
		DriverRestaurantDistance:  driverRestaurantDistance,
		DriverDestinationDistance: driverDestinationDistance,
		MatchSameUser:             matchSameUser,
		FraudRule:                 fraudRule,
	}
}
