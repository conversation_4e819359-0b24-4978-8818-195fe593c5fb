package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/xgo/timeutil"
)

type PendingTransactionCollectionStatus string

func (p PendingTransactionCollectionStatus) String() string {
	return string(p)
}

const (
	PendingPendingTransactionCollectionStatus            PendingTransactionCollectionStatus = "PENDING"
	TransactionCreatedPendingTransactionCollectionStatus PendingTransactionCollectionStatus = "TRANSACTION_CREATED"
	RejectedPendingTransactionCollectionStatus           PendingTransactionCollectionStatus = "REJECTED"
)

func GetAllowStatusToUpdatePendingTransactionStatus() types.StringSet {
	return types.NewStringSet([]string{
		string(TransactionCreatedPendingTransactionCollectionStatus),
		string(RejectedPendingTransactionCollectionStatus),
	}...,
	)
}

type PendingTransaction struct {
	ID              primitive.ObjectID                 `json:"id" bson:"_id,omitempty"`
	TransactionInfo TransactionInfo                    `json:"info" bson:"info"`
	Status          PendingTransactionCollectionStatus `json:"status" bson:"status"`
	CreatedAt       time.Time                          `json:"createdAt" bson:"created_at"`
	UpdatedAt       time.Time                          `json:"updatedAt" bson:"updated_at"`
}

func ConvertTransactionInfosToPendingTransactions(status PendingTransactionCollectionStatus, transactionInfos []TransactionInfo) []PendingTransaction {
	if len(transactionInfos) == 0 {
		return []PendingTransaction{}
	}

	newPendingTransactions := make([]PendingTransaction, len(transactionInfos))

	for index, transactionInfo := range transactionInfos {
		newPendingTransactions[index] = PendingTransaction{
			TransactionInfo: transactionInfo,
			Status:          status,
			CreatedAt:       timeutil.Now(),
			UpdatedAt:       timeutil.Now(),
		}
	}

	return newPendingTransactions
}

func ConvertPendingTransactionsToTransactionInfos(pendingTransactions []PendingTransaction, shouldConvertToGoodwill bool) []TransactionInfo {
	if len(pendingTransactions) == 0 {
		return []TransactionInfo{}
	}

	transactionInfos := make([]TransactionInfo, len(pendingTransactions))

	for index, pendingTransaction := range pendingTransactions {

		// Set pending transaction ID into new transaction info
		pendingTransactionInfo := pendingTransaction.TransactionInfo
		pendingTransactionInfo.PendingTransactionID = pendingTransaction.ID.Hex()

		// Convert delivery fee transaction to goodwill
		// Goodwill transaction come from approve form claim by admin
		if pendingTransactionInfo.Type == DeliveryFeeTransactionType && shouldConvertToGoodwill {
			pendingTransactionInfo.Type = GoodwillTransactionType
			pendingTransactionInfo.SubType = DeliveryFeeUserTransactionSubType
		}

		transactionInfos[index] = pendingTransactionInfo
	}

	return transactionInfos
}
