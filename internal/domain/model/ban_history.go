package model

import (
	"time"
)

const (
	ReasonUnbanExceed = "System auto unban due to temporary ban time exceeded"

	// ReasonOfflineLater cannot be changed since it's used by client to render specific page
	ReasonOfflineLater = "OFFLINE_LATER"

	CreatedBySystem = "system"
)

type FinancialDetail struct {
	OrderID      string  `bson:"order_id" json:"orderId"`
	TicketID     string  `bson:"ticket_id" json:"ticketId"`
	DamageAmount float64 `bson:"damage_amount" json:"damageAmount"`
}

type BanReasonMetadata struct {
	MetadataID      string           `json:"id"`
	InternalReason  string           `json:"internalReason"`
	FinancialDetail *FinancialDetail `json:"financialDetail"`
}

type BanReason struct {
	Category        string           `bson:"category" json:"category"`
	SubCategory     string           `bson:"sub_category" json:"subCategory"`
	MessageToDriver string           `bson:"message_to_driver" json:"messageToDriver"`
	InternalReason  string           `bson:"internal_reason" json:"internalReason"`
	Financial       bool             `bson:"financial" json:"financial"`
	FinancialDetail *FinancialDetail `bson:"financial_detail" json:"financialDetail"`
}

func NewBanReason(metadata *BanMetadata, internalReason string, financialDetail *FinancialDetail) *BanReason {
	return &BanReason{
		Category:        metadata.Category,
		SubCategory:     metadata.SubCategory,
		MessageToDriver: metadata.MessageToDriver,
		InternalReason:  internalReason,
		Financial:       metadata.Financial,
		FinancialDetail: financialDetail,
	}
}

// BanHistory ...
type BanHistory struct {
	// DriverID is id of driver who received banValidate.
	DriverID string `bson:"driver_id" json:"driverId"`

	// Type is kind of banValidate. PERMANENT or TEMPORARY.
	Type            string `bson:"type" json:"type"`
	Action          string `bson:"action" json:"action"`
	Value           string `bson:"value" json:"value"`
	Reason          string `bson:"reason" json:"reason"`
	Category        string `bson:"category" json:"category"`
	MessageToDriver string `bson:"message_to_driver" json:"messageToDriver"`

	CreatedBy        string    `bson:"created_by" json:"createdBy"`
	CreatedAt        time.Time `bson:"created_at" json:"createdAt"`
	BannedUntil      time.Time `bson:"banned_until" json:"bannedUntil"`
	UnBanImageRefURL string    `bson:"unban_image_ref_url" json:"unbanImageRefURL"`

	BanReasons []*BanReason `bson:"ban_reasons" json:"banReasons"`

	Url                       string `bson:"url,omitempty" json:"url,omitempty"`
	RetrainingTitle           string `bson:"retraining_title,omitempty" json:"retrainingTitle,omitempty"`
	RetrainingMessageToDriver string `bson:"retraining_message_to_driver,omitempty" json:"retrainingMessageToDriver,omitempty"`
}
