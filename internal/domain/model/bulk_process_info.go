package model

import (
	"time"

	"go.mongodb.org/mongo-driver/bson"
)

type BulkProcessInfoType string

const (
	BulkProcessDriverServiceType              BulkProcessInfoType = "DRIVER_SERVICE_TYPE"
	BulkProcessDriverDedicatedZone            BulkProcessInfoType = "DRIVER_DEDICATED_ZONE"
	BulkProcessDriverServiceTypeSilentBanned  BulkProcessInfoType = "DRIVER_SERVICE_TYPE_SILENT_BANNED"
	BulkProcessToggleOnRiderServicePreference BulkProcessInfoType = "TOGGLE_ON_RIDER_SERVICE_PREFERENCE"
	BulkIncentiveUpdate                       BulkProcessInfoType = "INCENTIVE_UPDATE"
	BulkIncentiveCreate                       BulkProcessInfoType = "INCENTIVE_CREATE"
	BulkIncentiveDelete                       BulkProcessInfoType = "INCENTIVE_DELETE"
	BulkIncentiveExport                       BulkProcessInfoType = "INCENTIVE_EXPORT"
)

type BulkProcessInfoStatus string

const (
	BulkProcessInfoStatusPending   BulkProcessInfoStatus = "PENDING"
	BulkProcessInfoStatusCancelled BulkProcessInfoStatus = "CANCELLED"
	BulkProcessInfoStatusSuccess   BulkProcessInfoStatus = "SUCCESS"
	BulkProcessInfoStatusFailed    BulkProcessInfoStatus = "FAILED"
)

type BulkProcessInfoPayloadStatus string

const (
	BulkProcessInfoPayloadStatusPending   BulkProcessInfoPayloadStatus = "PENDING"
	BulkProcessInfoPayloadStatusCancelled BulkProcessInfoPayloadStatus = "CANCELLED"
	BulkProcessInfoPayloadStatusSuccess   BulkProcessInfoPayloadStatus = "SUCCESS"
	BulkProcessInfoPayloadStatusFailed    BulkProcessInfoPayloadStatus = "FAILED"
)

type BulkProcessInfo struct {
	ID     string                `bson:"bulk_process_info_id" json:"bulkProcessInfoId"`
	Type   BulkProcessInfoType   `bson:"type" json:"type"`
	Status BulkProcessInfoStatus `bson:"status" json:"status"`

	Payload interface{} `bson:"payload" json:"payload"`

	ProcessedAt time.Time `bson:"processed_at" json:"processedAt"`
	CreatedAt   time.Time `bson:"created_at" json:"createdAt"`
	UpdatedAt   time.Time `bson:"updated_at" json:"updatedAt"`
	RequestedBy string    `bson:"requested_by" json:"requestedBy"`
}

func (p *BulkProcessInfo) UnmarshalBSON(data []byte) error {
	var payload struct {
		Type BulkProcessInfoType `bson:"type"`
	}
	if err := bson.Unmarshal(data, &payload); err != nil {
		return err
	}

	switch payload.Type {
	case BulkProcessDriverServiceType:
		var m BulkProcessInfoDriverServiceTypeMapping
		if err := bson.Unmarshal(data, &m); err != nil {
			return err
		}
		*p = *m.Info()
	case BulkProcessDriverDedicatedZone:
		var m BulkProcessInfoDriverZoneMapping
		if err := bson.Unmarshal(data, &m); err != nil {
			return err
		}
		*p = *m.Info()
	default:
		// hard code for generic bulk compatibility
		// m is related from BulkProcessInfo, we have to duplicate struct for prevent recursive bson unmarshal
		var m struct {
			ID     string                `bson:"bulk_process_info_id" json:"bulkProcessInfoId"`
			Type   BulkProcessInfoType   `bson:"type" json:"type"`
			Status BulkProcessInfoStatus `bson:"status" json:"status"`

			Payload map[string]struct {
				Status  BulkProcessInfoStatus
				Payload map[string]interface{}
			}

			ProcessedAt time.Time `bson:"processed_at" json:"processedAt"`
			CreatedAt   time.Time `bson:"created_at" json:"createdAt"`
			UpdatedAt   time.Time `bson:"updated_at" json:"updatedAt"`
			RequestedBy string    `bson:"requested_by" json:"requestedBy"`
		}
		if err := bson.Unmarshal(data, &m); err != nil {
			return err
		}
		payload := map[string]map[string]interface{}{}

		for key, value := range m.Payload {
			payload[key] = map[string]interface{}{
				"status": value.Status,
			}

			for key2, value2 := range value.Payload {
				payload[key][key2] = value2
			}
		}

		*p = BulkProcessInfo{
			ID:          m.ID,
			Type:        m.Type,
			Status:      m.Status,
			Payload:     payload,
			ProcessedAt: m.ProcessedAt,
			CreatedAt:   m.CreatedAt,
			UpdatedAt:   m.UpdatedAt,
			RequestedBy: m.RequestedBy,
		}
	}
	// TODO: Add more type

	return nil
}

type DriverServiceTypePayload map[string]DriverServiceInfo

type DriverServiceInfo struct {
	Status   BulkProcessInfoPayloadStatus `json:"status"`
	Services Services                     `json:"services"`
}

type BulkProcessInfoDriverServiceTypeMapping struct {
	ID     string                `bson:"bulk_process_info_id"`
	Type   BulkProcessInfoType   `bson:"type"`
	Status BulkProcessInfoStatus `bson:"status"`

	Payload DriverServiceTypePayload `bson:"payload"`

	ProcessedAt time.Time `bson:"processed_at"`
	CreatedAt   time.Time `bson:"created_at"`
	UpdatedAt   time.Time `bson:"updated_at"`
	RequestedBy string    `bson:"requested_by"`
}

func (mapping *BulkProcessInfoDriverServiceTypeMapping) Info() *BulkProcessInfo {
	return &BulkProcessInfo{
		ID:          mapping.ID,
		Type:        mapping.Type,
		Status:      mapping.Status,
		Payload:     mapping.Payload,
		ProcessedAt: mapping.ProcessedAt,
		CreatedAt:   mapping.CreatedAt,
		UpdatedAt:   mapping.UpdatedAt,
		RequestedBy: mapping.RequestedBy,
	}
}

func (p *BulkProcessInfo) SetBulkDriverServiceType(payload DriverServiceTypePayload) {
	p.Type = BulkProcessDriverServiceType
	p.Payload = payload
}

type DriverZoneInfo struct {
	Status  BulkProcessInfoPayloadStatus `json:"status"`
	Drivers []string                     `json:"drivers"`
}

type DriverZonePayload map[string]DriverZoneInfo

type BulkProcessInfoDriverZoneMapping struct {
	ID     string                `bson:"bulk_process_info_id"`
	Type   BulkProcessInfoType   `bson:"type"`
	Status BulkProcessInfoStatus `json:"status"`

	Payload DriverZonePayload `bson:"payload"`

	ProcessedAt time.Time `bson:"processed_at"`
	CreatedAt   time.Time `bson:"created_at"`
	UpdatedAt   time.Time `bson:"updated_at"`
	RequestedBy string    `bson:"requested_by"`
}

func (mapping *BulkProcessInfoDriverZoneMapping) Info() *BulkProcessInfo {
	return &BulkProcessInfo{
		ID:          mapping.ID,
		Type:        mapping.Type,
		Status:      mapping.Status,
		Payload:     mapping.Payload,
		ProcessedAt: mapping.ProcessedAt,
		CreatedAt:   mapping.CreatedAt,
		UpdatedAt:   mapping.UpdatedAt,
		RequestedBy: mapping.RequestedBy,
	}
}

func (p *BulkProcessInfo) SetBulkDriverDedicatedZone(payload DriverZonePayload) {
	p.Type = BulkProcessDriverDedicatedZone
	p.Payload = payload
}
