package model

import (
	"strings"
	"time"
)

func NewDriverOrderInfoDetail(op OrderEventPayload, distance float64) DriverOrderInfoDetail {
	return DriverOrderInfoDetail{
		OrderId:                 op.OrderID,
		Status:                  op.Status,
		DistanceFromDestination: distance,
		CreatedAt:               op.CreatedAt,
		IsSwitchFlow:            op.SwitchFlow,
	}
}

type DriverOrderInfoDetail struct {
	OrderId                 string    `bson:"order_id"`
	Status                  Status    `bson:"status"`
	DistanceFromDestination float64   `bson:"distance_from_destination"`
	CreatedAt               time.Time `bson:"created_at"`
	IsSwitchFlow            bool      `bson:"is_switch_flow"`
}

type DriverOrderInfoNode struct {
	Order DriverOrderInfoDetail `bson:"order"`
	Next  *DriverOrderInfoNode  `bson:"next"`
}

type DriverOrderInfoList struct {
	Head *DriverOrderInfoNode `bson:"Head"`
}

func (l *DriverOrderInfoList) Add(o DriverOrderInfoDetail) {
	n := &DriverOrderInfoNode{Order: o}
	if l.Head == nil {
		l.Head = n
	} else if n.Order.CreatedAt.Before(l.Head.Order.CreatedAt) {
		if l.Head.Order.OrderId == n.Order.OrderId {
			return
		}
		cn := l.Head
		for cn.Next != nil {
			if cn.Next.Order.OrderId == n.Order.OrderId {
				return
			}
			cn = cn.Next
		}
		temp := l.Head
		l.Head = n
		n.Next = temp
	} else {
		cn := l.Head
		for cn.Next != nil {
			if cn.Next.Order.OrderId == n.Order.OrderId {
				return
			}
			cn = cn.Next
		}
		if cn.Order.OrderId == n.Order.OrderId {
			return
		}
		cn.Next = n
	}
}

func (l *DriverOrderInfoList) RemoveBefore(t time.Time) {
	for e := l.Head; e != nil; e = e.Next {
		if e.Next == nil && e.Order.CreatedAt.Before(t) {
			l.Head = nil
		} else if e.Next != nil && e.Order.CreatedAt.Before(t) {
			l.Head = e.Next
		} else if e.Next != nil && e.Next.Order.CreatedAt.Before(t) {
			e.Next = nil
		}
	}
}

func (l *DriverOrderInfoList) Clear() {
	l.Head = nil
}

func (l *DriverOrderInfoList) ListCompleteDistanceFromDestination() []float64 {
	var distances []float64
	for e := l.Head; e != nil && e.Order.Status == StatusCompleted; e = e.Next {
		distances = append(distances, e.Order.DistanceFromDestination)
	}
	return distances
}

func (l *DriverOrderInfoList) ListCompleteOrderID() []string {
	var orderIDs []string
	for e := l.Head; e != nil && e.Order.Status == StatusCompleted; e = e.Next {
		orderIDs = append(orderIDs, e.Order.OrderId)
	}
	return orderIDs
}

func (l *DriverOrderInfoList) LastID() string {
	orderId := ""
	h := l.Head
	if h != nil {
		for h.Next != nil {
			h = h.Next
		}
		orderId = h.Order.OrderId
	}
	return orderId
}

func (l *DriverOrderInfoList) CountCompleteOrder() int {
	length := 0
	for e := l.Head; e != nil && e.Order.Status == StatusCompleted; e = e.Next {
		length++
	}
	return length
}

func (l *DriverOrderInfoList) isMessenger(oid string) bool {
	ret := strings.Split(oid, "-")
	return ret[0] == "LMM"
}

func (l *DriverOrderInfoList) CountMessengerCompleteOrder() int {
	length := 0
	for e := l.Head; e != nil; {
		if e.Order.Status == StatusCompleted && l.isMessenger(e.Order.OrderId) {
			length++
		}
		e = e.Next
	}
	return length
}
