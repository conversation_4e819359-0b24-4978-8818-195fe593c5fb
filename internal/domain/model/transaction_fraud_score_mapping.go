package model

import (
	"go.mongodb.org/mongo-driver/bson"
)

type TransactionFraudScoreMapping struct {
	TransactionID    string                   `bson:"transaction_id"`
	UnusualScore     int                      `bson:"unusual_score"`
	OrderFraudScores []OrderFraudScoreMapping `bson:"order_fraud_scores"`
}

func newTransactionFraudScoreMapping(entity TransactionFraudScore) *TransactionFraudScoreMapping {
	orderFraudScoreMappings := make([]OrderFraudScoreMapping, len(entity.OrderFraudScores))
	for i, item := range entity.OrderFraudScores {
		orderFraudScoreMappings[i] = *newOrderFraudScoreMapping(item)
	}

	return &TransactionFraudScoreMapping{
		TransactionID:    entity.TransactionID,
		UnusualScore:     entity.UnusualScore,
		OrderFraudScores: orderFraudScoreMappings,
	}
}

func (tfs *TransactionFraudScoreMapping) TransactionFraudScore() *TransactionFraudScore {
	items := make([]OrderFraudScore, len(tfs.OrderFraudScores))
	for i, im := range tfs.OrderFraudScores {
		items[i] = *im.OrderFraudScore()
	}

	return &TransactionFraudScore{
		TransactionID:    tfs.TransactionID,
		UnusualScore:     tfs.UnusualScore,
		OrderFraudScores: items,
	}
}

type OrderFraudScoreMapping struct {
	OrderID                   string          `bson:"order_id"`
	Score                     int             `bson:"score"`
	TurnAroundTime            int             `bson:"turn_around_time"`
	DriverRestaurantDistance  int             `bson:"driver_restaurant_distance"`
	DriverDestinationDistance int             `bson:"driver_destination_distance"`
	MatchSameUser             int             `bson:"match_same_user"`
	FraudRule                 map[string]bool `bson:"fraud_rule"`
}

func newOrderFraudScoreMapping(entity OrderFraudScore) *OrderFraudScoreMapping {
	return &OrderFraudScoreMapping{
		OrderID:                   entity.OrderID,
		Score:                     entity.Score,
		TurnAroundTime:            entity.TurnAroundTime,
		DriverRestaurantDistance:  entity.DriverRestaurantDistance,
		DriverDestinationDistance: entity.DriverDestinationDistance,
		MatchSameUser:             entity.MatchSameUser,
		FraudRule:                 entity.FraudRule,
	}
}

func (ofs *OrderFraudScoreMapping) OrderFraudScore() *OrderFraudScore {
	return &OrderFraudScore{
		OrderID:                   ofs.OrderID,
		Score:                     ofs.Score,
		TurnAroundTime:            ofs.TurnAroundTime,
		DriverRestaurantDistance:  ofs.DriverRestaurantDistance,
		DriverDestinationDistance: ofs.DriverDestinationDistance,
		MatchSameUser:             ofs.MatchSameUser,
		FraudRule:                 ofs.FraudRule,
	}
}

func (s TransactionFraudScore) MarshalBSON() ([]byte, error) {
	return bson.Marshal(newTransactionFraudScoreMapping(s))
}

func (s *TransactionFraudScore) UnmarshalBSON(data []byte) error {
	mapping := &TransactionFraudScoreMapping{}
	if err := bson.Unmarshal(data, mapping); err != nil {
		return err
	}

	tfs := mapping.TransactionFraudScore()

	*s = *tfs

	return nil
}
