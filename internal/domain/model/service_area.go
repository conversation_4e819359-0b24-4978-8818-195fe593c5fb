package model

import (
	"encoding/json"
	"strings"
	"time"

	"github.com/kelseyhightower/envconfig"
	"go.mongodb.org/mongo-driver/bson"

	absintheUtils "git.wndv.co/lineman/absinthe/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
)

var (
	DefaultSkipQuota    = 3
	DefaultBanPeriod    = time.Duration(2) * time.Hour
	DefaultQuotaRefresh = time.Duration(2) * time.Hour

	DefaultAutoAssignConfig = AutoAssignDistribution{}

	DefaultCompleteTimeCalculation = CompleteTimeCalculation{}
)

var defaultBenefit = `<h2 data-pm-slice="1 1 []"><strong>SUPER</strong></h2>
<p data-pm-slice="1 1 []"><strong>เงื่อนไขของระดับ</strong></p>
<ul>
<li data-pm-slice="1 1 []">สำเร็จงานในระหว่างวันที่ 15 เดือนก่อน ถึง วันที่ 16 ของเดือนปัจจุบัน มากกว่า 450 งาน</li>
<li data-pm-slice="1 1 []">ไม่พบการทุจริตในการรับงานหรือถูกแบนถาวร</li>
<li data-pm-slice="1 1 []">ระดับจะถูกคำนวณใหม่ทุกเดือนสิทธิประโยชน์</li>
</ul>
<p><strong>ผลประโยชน์พิเศษที่ได้</strong></p>
<ul>
<li>ประกันรถจักรยานยนต์
<ul>
<li>ประกันความเสียหายต่อตัวรถจักรยานยนต์ สูงสุด 10,000 บาท/ครั้ง</li>
<li>เสียชีวิต สูญเสียอวัยวะ ทุพพลภาพถาวร (ผู้ขับขี่ 1 คน และ ผู้โดยสาร 1 คน) สูงสุด 50,000 บาท/ครั้ง</li>
<li>ความเสียหายต่อชีวิต ร่างกาย หรือพลานามัย เฉพาะส่วนเกินวงเงินสูงสุดตาม พ.ร.บ. (บุคคลภายนอก) สูงสุด 500,000 บาท/คน แต่ไม่เกิน 10,000,000 บาท/ครั้ง-ประกันอุบัติเหตุ</li>
<li>การรักษาพยาบาลต่ออุบัติเหตุแต่ละครั้ง สูงสุด 10,000 บาท</li>
<li>ชดเชยรายได้ระหว่างการเข้ารักษาตัวในโรงพยาบาล 500 บาท/วัน (สูงสุด 3 วัน)</li>
<li>อุบัติเหตุจากการขับขี่ หรือโดยสารรถจักรยานยนต์ (ไม่รวมถึงการแข่งขัน หรือประลองความเร็ว) สูงสุด 100,000 บาท</li>
</ul>
</li>
</ul>
<p>*เงื่อนไขและความคุ้มครองเป็นไปตามที่บริษัทกำหนด</p>
<h2><strong>PRO</strong></h2>
<p><strong>เงื่อนไขของระดับ</strong></p>
<ul>
<li>สำเร็จงานในระหว่างวันที่ 15 เดือนก่อน ถึง วันที่ 16 ของเดือนปัจจุบัน มากกว่า 350 งาน</li>
<li>ไม่พบการทุจริตในการรับงานหรือถูกแบนถาวร</li>
<li>ระดับจะถูกคำนวณใหม่ทุกเดือนสิทธิประโยชน์</li>
</ul>
<p><strong>ผลประโยชน์พิเศษที่ได้</strong></p>
<ul>
<li>ประกันรถจักรยานยนต์
<ul>
<li>ประกันความเสียหายต่อตัวรถจักรยานยนต์ สูงสุด 10,000 บาท/ครั้ง</li>
<li>เสียชีวิต สูญเสียอวัยวะ ทุพพลภาพถาวร (ผู้ขับขี่ 1 คน และ ผู้โดยสาร 1 คน) สูงสุด 50,000 บาท/ครั้ง</li>
<li>ความเสียหายต่อชีวิต ร่างกาย หรือพลานามัย เฉพาะส่วนเกินวงเงินสูงสุดตาม พ.ร.บ. (บุคคลภายนอก) สูงสุด 500,000 บาท/คน แต่ไม่เกิน 10,000,000 บาท/ครั้ง</li>
</ul>
</li>
</ul>
<p>*เงื่อนไขและความคุ้มครองเป็นไปตามที่บริษัทกำหนด</p>`

type DistributionLogic string

func (dl DistributionLogic) String() string {
	return string(dl)
}

const (
	DistributionLogicBroadcast  DistributionLogic = "BROADCAST"
	DistributionLogicAutoAssign DistributionLogic = "AUTO_ASSIGN"
	DistributionLogicHybrid     DistributionLogic = "HYBRID"
)

var (
	AllDistributionLogic = []DistributionLogic{DistributionLogicBroadcast, DistributionLogicAutoAssign, DistributionLogicHybrid}
)

type Distribution interface{}

type ServiceAreaTopUpConfig struct {
	EnabledCiti      bool `json:"enabledCiti" bson:"enabled_citi"`
	EnabledUob       bool `json:"enabledUob" bson:"enabled_uob"`
	GenerateUobRefID bool `json:"generateUobRefID" bson:"generate_uob_ref_id"`
}

func NewServiceAreaTopUpConfig() ServiceAreaTopUpConfig {
	return ServiceAreaTopUpConfig{
		EnabledCiti:      true,
		EnabledUob:       false,
		GenerateUobRefID: false,
	}
}

type NegativeBalanceGroupConfig struct {
	Enabled              bool    `json:"enabled" bson:"enabled"`
	Name                 string  `json:"name" bson:"name"`
	MinimumCreditBalance float64 `json:"minimumCreditBalance" bson:"minimum_credit_balance"`
}

type NegativeBalanceConfig struct {
	Enabled              bool     `json:"enabled" bson:"enabled"`
	MinimumCreditBalance float64  `json:"minimumCreditBalance" bson:"minimum_credit_balance"`
	DriverIDs            []string `json:"driverIDs" bson:"driver_ids"`
}

type TierNegativeBalanceConfig struct {
	Enabled               bool          `json:"enabled" bson:"enabled"`
	NegativeBalanceByTier DriverTierMap `json:"negativeBalanceByTier" bson:"negative_balance_by_tier"`
	BlacklistIDs          []string      `json:"blacklistIDs" bson:"blacklist_ids"`
}

type ForcedPODConfig struct {
	Enabled bool      `json:"enabled" bson:"enabled"`
	EndAt   time.Time `json:"endAt" bson:"end_at"`
}

func (tbc *TierNegativeBalanceConfig) IsDriverBlacklisted(driverId string) bool {
	for _, drivId := range tbc.BlacklistIDs {
		if drivId == driverId {
			return true
		}
	}
	return false
}

func NewNegativeBalanceConfig() NegativeBalanceConfig {
	return NegativeBalanceConfig{
		MinimumCreditBalance: 0,
	}
}

func NewTierNegativeBalanceConfig() TierNegativeBalanceConfig {
	return TierNegativeBalanceConfig{
		Enabled:               false,
		NegativeBalanceByTier: map[DriverTier]float64{},
		BlacklistIDs:          []string{},
	}
}

func NewForcedPODConfig() ForcedPODConfig {
	return ForcedPODConfig{
		Enabled: false,
	}
}

type ServiceAreaID string

func GenerateServiceAreaID() ServiceAreaID {
	return ServiceAreaID(utils.GenerateUUID())
}

func newServiceAreaID(id string) ServiceAreaID {
	return ServiceAreaID(id)
}

func (si ServiceAreaID) String() string {
	return string(si)
}

func (rs RainStatus) String() string {
	return string(rs)
}

func (rs RainStatus) IsValid() bool {
	switch rs {
	case RainStatusNone, RainStatusLightRain, RainStatusHeavyRain, RainStatusRainWithNoSupply:
		return true
	default:
		return false
	}
}

type ServiceArea struct {
	ID                              ServiceAreaID                `bson:"service_area_id" json:"-"`
	Region                          RegionCode                   `bson:"region" json:"-"`
	DistributionLogic               DistributionLogic            `bson:"distribution_logic" json:"-"`
	Distribution                    AutoAssignDistribution       `bson:"-" json:"distribution" binding:"omitempty"`
	Benefit                         string                       `bson:"benefit" json:"-"`
	HeatmapType                     HeatMapType                  `bson:"heatmap_type" json:"heatmapType" binding:"omitempty"`
	HeatMapByTimes                  []HeatMapByTime              `bson:"heatmap_by_times" json:"heatmapByTimes" binding:"omitempty"`
	CreatedAt                       time.Time                    `bson:"created_at" json:"-"`
	UpdatedAt                       time.Time                    `bson:"updated_at" json:"-"`
	VerifyDriverPhotoRate           int                          `bson:"verify_driver_photo_rate" json:"verifyDriverPhotoRate" binding:"omitempty"`
	RequiredPhotoOfJacketAndBox     bool                         `bson:"required_photo_of_jacket_and_box" json:"-"`
	RequiredPhotoOfJacketAndBoxes   []string                     `bson:"required_photo_of_jacket_and_boxes" json:"requiredPhotoOfJacketAndBoxes"`
	TopUpConfig                     ServiceAreaTopUpConfig       `bson:"top_up_config" json:"topUpConfig" binding:"required"`
	ShiftDriverIDs                  []string                     `bson:"shift_driver_ids" json:"-"`
	BlackListBookShiftDriverIDs     []string                     `bson:"blacklist_book_shift_driver_ids" json:"-"`
	NegativeBalanceGroups           []NegativeBalanceGroupConfig `bson:"negative_balance_groups" json:"negativeBalanceGroups"`
	NegativeBalanceConfig           NegativeBalanceConfig        `bson:"negative_balance_config" json:"-"`
	TierNegativeBalanceConfig       TierNegativeBalanceConfig    `bson:"tier_negative_balance_config" json:"-"`
	SupplyPositioningServices       Services                     `bson:"supply_positioning_services" json:"supplyPositioningServices"`
	MessengerCompletedOrdersLimit   int                          `bson:"messenger_completed_orders_limit" json:"messengerCompletedOrdersLimit"`
	WhitelistUsersEnabled           bool                         `bson:"whitelist_users_enabled" json:"whitelistUsersEnabled"`
	WhitelistUsers                  []string                     `bson:"whitelist_users" json:"-"`
	CookingTimeDelayReassignEnabled bool                         `bson:"cooking_time_delay_reassign_enabled" json:"cookingTimeDelayReassignEnabled"`
	CookingTimeDelayThreshold       time.Duration                `bson:"cooking_time_delay_threshold" json:"-"`
	ShowSubRegionsPolygon           bool                         `bson:"show_sub_regions_polygon" json:"showSubRegionsPolygon"`
	ShowProductivityIncentive       bool                         `bson:"show_productivity_incentive" json:"showProductivityIncentive"`
	ShowCoinIncentive               bool                         `bson:"show_coin_incentive" json:"showCoinIncentive"`
	OfflineLaterBreakDuration       time.Duration                `bson:"offline_later_break_duration" json:"-"`
}

func (ss *ServiceArea) MarshalJSON() ([]byte, error) {
	type TmpServiceArea ServiceArea
	tier := DefaultDriverTierMap
	var mergedItems = DriverTierMap{}
	for key, element := range tier {
		if val, ok := ss.TierNegativeBalanceConfig.NegativeBalanceByTier[key]; ok {
			mergedItems[key] = val
		} else {
			mergedItems[key] = element
		}
	}
	res := struct {
		TmpServiceArea
		ID                                 string                       `json:"id"`
		Region                             string                       `json:"region"`
		Benefit                            string                       `json:"benefit"`
		Logic                              DistributionLogic            `json:"distributionLogic"`
		TierNegativeBalanceConfig          TierNegativeBalanceConfigRes `json:"tierNegativeBalanceConfig"`
		SupplyPositioningServices          Services                     `json:"supplyPositioningServices"`
		CookingTimeDelayThresholdSeconds   float64                      `json:"cookingTimeDelayThresholdSeconds"`
		OfflineLaterBreakDurationInMinutes int                          `json:"offlineLaterBreakDurationInMinutes"`
		UpdatedAt                          time.Time                    `json:"updatedAt"`
		ShiftDriverIDs                     string                       `json:"shiftDriverIds"`
		BlackListBookShiftDriverIDs        string                       `json:"blackListBookShiftDriverIDs"`
		WhitelistUsers                     string                       `json:"whitelistUsers"`
	}{
		TmpServiceArea:                     TmpServiceArea(*ss),
		ID:                                 ss.ID.String(),
		Region:                             ss.Region.String(),
		Benefit:                            ss.GetBenefit(),
		Logic:                              ss.DistributionLogic,
		TierNegativeBalanceConfig:          NewTierNegativeBalanceConfigRes(ss.TierNegativeBalanceConfig),
		SupplyPositioningServices:          ss.SupplyPositioningServices,
		CookingTimeDelayThresholdSeconds:   ss.CookingTimeDelayThreshold.Seconds(),
		OfflineLaterBreakDurationInMinutes: int(ss.OfflineLaterBreakDuration.Minutes()),
		UpdatedAt:                          ss.UpdatedAt,
		ShiftDriverIDs:                     joinString(ss.ShiftDriverIDs),
		BlackListBookShiftDriverIDs:        joinString(ss.BlackListBookShiftDriverIDs),
		WhitelistUsers:                     joinString(ss.WhitelistUsers),
	}

	// For compatible with old service area.
	if res.RequiredPhotoOfJacketAndBox && !ss.IsRequiredPhotoOfJacketAndBoxes(ServiceFood) {
		res.RequiredPhotoOfJacketAndBoxes = append(res.RequiredPhotoOfJacketAndBoxes, string(ServiceFood))
	}
	if res.HeatMapByTimes == nil {
		res.HeatMapByTimes = []HeatMapByTime{}
	}
	if res.NegativeBalanceGroups == nil {
		res.NegativeBalanceGroups = []NegativeBalanceGroupConfig{}
	}
	return json.Marshal(res)
}

func joinString(ids []string) string {
	var driverIDs string
	if len(ids) == 0 {
		return driverIDs
	}

	return strings.Join(ids, ", ")
}

type TierNegativeBalanceConfigRes struct {
	Enabled               bool          `json:"enabled"`
	NegativeBalanceByTier DriverTierMap `json:"negativeBalanceByTier"`
	BlacklistIDs          string        `json:"blackListIDs"`
}

func NewTierNegativeBalanceConfigRes(config TierNegativeBalanceConfig) TierNegativeBalanceConfigRes {
	tier := DefaultDriverTierMap
	var mergedItems = DriverTierMap{}
	for key, element := range tier {
		if val, ok := config.NegativeBalanceByTier[key]; ok {
			mergedItems[key] = val
		} else {
			mergedItems[key] = element
		}
	}
	return TierNegativeBalanceConfigRes{
		Enabled:               config.Enabled,
		NegativeBalanceByTier: mergedItems,
		BlacklistIDs:          joinString(config.BlacklistIDs),
	}
}

type TmpServiceArea ServiceArea

type distributionMapping struct {
	TmpServiceArea `bson:",inline"`
	Distribution   struct {
		Logic   DistributionLogic `bson:"logic"`
		Payload struct {
			AutoAssign AutoAssignDistribution `bson:"auto_assign"`
		} `bson:"payload"`
	} `bson:"distribution"`
}

func (ss *ServiceArea) MarshalBSON() ([]byte, error) {
	if ss == nil {
		return bson.Marshal(nil)
	}
	tmp := distributionMapping{}
	tmp.TmpServiceArea = TmpServiceArea(*ss)
	tmp.Distribution.Payload.AutoAssign = ss.Distribution
	tmp.Distribution.Logic = DistributionLogicAutoAssign
	return bson.Marshal(tmp)
}

func (ss *ServiceArea) UnmarshalBSON(data []byte) error {
	tmp := distributionMapping{}
	tmp.Distribution.Payload.AutoAssign = *NewAutoAssignDistribution()
	tmp.Distribution.Payload.AutoAssign.AssignmentType = prediction.AssignmentTypeSingle
	tmp.ShowProductivityIncentive = true
	if err := bson.Unmarshal(data, &tmp); err != nil {
		return err
	}
	if tmp.HeatmapType == "" {
		tmp.HeatmapType = DemandHm
	}
	*ss = ServiceArea(tmp.TmpServiceArea)
	ss.Distribution = tmp.Distribution.Payload.AutoAssign
	return nil
}

func NewServiceArea(id ServiceAreaID, region RegionCode) *ServiceArea {
	return &ServiceArea{
		ID:                        id,
		Region:                    region,
		DistributionLogic:         DistributionLogicAutoAssign,
		Distribution:              *NewAutoAssignDistribution(),
		TopUpConfig:               NewServiceAreaTopUpConfig(),
		NegativeBalanceConfig:     NewNegativeBalanceConfig(),
		TierNegativeBalanceConfig: NewTierNegativeBalanceConfig(),
		CreatedAt:                 time.Now().UTC(),
		UpdatedAt:                 time.Now().UTC(),
	}
}

func (ss *ServiceArea) GetBenefit() string {
	if len(ss.Benefit) == 0 {
		return defaultBenefit
	}
	return ss.Benefit
}

func (ss *ServiceArea) SetDistribution(dist *AutoAssignDistribution) {
	ss.Distribution = *dist
}

func (ss *ServiceArea) SetDistributionLogic(logic DistributionLogic) {
	ss.DistributionLogic = logic
}

func (ss *ServiceArea) SetRequiredPhotoOfJacketAndBox(b bool) {
	ss.RequiredPhotoOfJacketAndBox = b
}

func (ss *ServiceArea) SetRequiredPhotoOfJacketAndBoxes(s []string) {
	ss.RequiredPhotoOfJacketAndBoxes = s
}

func (ss *ServiceArea) IsRequiredPhotoOfJacketAndBoxes(s Service) bool {
	return absintheUtils.StrContains(s.String(), ss.RequiredPhotoOfJacketAndBoxes)
}

func (ss *ServiceArea) SetUpdatedAt(at time.Time) {
	ss.UpdatedAt = at
}

func (ss *ServiceArea) SetBenefit(benefit string) {
	ss.Benefit = benefit
}

func (ss *ServiceArea) SetHeatMapType(hmType HeatMapType) {
	ss.HeatmapType = hmType
}

func (ss *ServiceArea) SetHeatMapByTimes(hm []HeatMapByTime) {
	ss.HeatMapByTimes = hm
}

func (ss *ServiceArea) SetVerifyDriverPhotoRate(rate int) {
	ss.VerifyDriverPhotoRate = rate
}

func (ss *ServiceArea) SetTopUpConfig(topUpConfig ServiceAreaTopUpConfig) {
	ss.TopUpConfig = topUpConfig
}

func (ss *ServiceArea) SetShiftDriverIDs(driverIDs []string) {
	ss.ShiftDriverIDs = driverIDs
}

func (ss *ServiceArea) SetBlackListBookShiftDriverIDs(driverIDs []string) {
	ss.BlackListBookShiftDriverIDs = driverIDs
}

func (ss *ServiceArea) SetNegativeBalanceGroups(cfg []NegativeBalanceGroupConfig) {
	ss.NegativeBalanceGroups = cfg
}

func (ss *ServiceArea) SetTierNegativeBalanceConfig(tierNegativeBalanceConfig TierNegativeBalanceConfig) {
	ss.TierNegativeBalanceConfig = tierNegativeBalanceConfig
}

func (ss *ServiceArea) SetSupplyPositioningServices(s Services) {
	ss.SupplyPositioningServices = s
}

func (ss *ServiceArea) SetMessengerCompletedOrdersLimit(limit int) {
	ss.MessengerCompletedOrdersLimit = limit
}

func (ss *ServiceArea) SetWhitelistUsersEnabled(enabled bool) {
	ss.WhitelistUsersEnabled = enabled
}

func (ss *ServiceArea) SetWhitelistUsers(userIds []string) {
	ss.WhitelistUsers = userIds
}

func (ss *ServiceArea) SetCookingTimeDelayReassignEnabled(enabled bool) {
	ss.CookingTimeDelayReassignEnabled = enabled
}

func (ss *ServiceArea) SetCookingTimeDelayThreshold(duration time.Duration) {
	ss.CookingTimeDelayThreshold = duration
}

func (ss *ServiceArea) SetShowSubRegionsPolygon(subRegionsEnabled bool) {
	ss.ShowSubRegionsPolygon = subRegionsEnabled
}

func (ss *ServiceArea) SetOfflineLaterBreakDuration(breakDuration time.Duration) {
	ss.OfflineLaterBreakDuration = breakDuration
}

func (ss *ServiceArea) SetShowProductivityIncentive(show bool) {
	ss.ShowProductivityIncentive = show
}

func (ss *ServiceArea) SetShowCoinIncentive(show bool) {
	ss.ShowCoinIncentive = show
}

type AutoAssignDistribution struct {
	MinRadiusInKm                 float64                   `envconfig:"AUTOASSIGN_MIN_RADIUS" default:"0.5" bson:"min_radius_in_km" json:"minRadiusInKm" binding:"omitempty,gte=0"`
	MaxRadiusInKm                 float64                   `envconfig:"AUTOASSIGN_MAX_RADIUS" default:"10" bson:"max_radius_in_km" json:"maxRadiusInKm" binding:"omitempty,gte=0"`
	GroupDistInKm                 float64                   `envconfig:"AUTOASSIGN_GROUP_DIST" default:"0" bson:"group_dist_in_km" json:"groupDistInKm" binding:"omitempty,gte=0"`
	DistanceScoreWeight           float64                   `envconfig:"AUTOASSIGN_DISTANCE_SCORE_WEIGHT" default:"1" bson:"distance_score_weight" json:"distanceScoreWeight" binding:"omitempty,gte=0"`
	AcceptingDurationInSecond     int64                     `envconfig:"AUTOASSIGN_ACCEPT_DURATION_SECONDS" default:"15" bson:"accepting_duration_in_second" json:"acceptingDurationInSecond" binding:"omitempty,gte=0"`
	AutoAcceptScore               float64                   `envconfig:"AUTOASSIGN_AUTO_ACCEPT_SCORE" default:"0.5" bson:"auto_accept_score" json:"autoAcceptScore" binding:"omitempty,gte=0"`
	NewbieMaxDays                 float64                   `envconfig:"AUTOASSIGN_NEWBIE_MAX_DAYS" default:"5" bson:"newbie_max_days" json:"newbieMaxDays" binding:"omitempty,gte=0"`
	NewbieScoreWeight             float64                   `envconfig:"AUTOASSIGN_NEWBIE_SCORE_WEIGHT" default:"1" bson:"newbie_score_weight" json:"newbieScoreWeight" binding:"omitempty,gte=0"`
	AcceptancePositiveRate        float64                   `envconfig:"AUTOASSIGN_ACCEPTANCE_POSITIVE_RATE" default:"0.8" bson:"acceptance_positive_rate" json:"acceptancePositiveRate" binding:"omitempty,gte=0"`
	AcceptanceMinOrders           int64                     `envconfig:"AUTOASSIGN_ACCEPTANCE_MIN_ORDERS" default:"5" bson:"acceptance_min_orders" json:"acceptanceMinOrders" binding:"omitempty,gte=0"`
	AcceptanceScoreWeight         float64                   `envconfig:"AUTOASSIGN_ACCEPTANCE_SCORE_WEIGHT" default:"1" bson:"acceptance_score_weight" json:"acceptanceScoreWeight" binding:"omitempty,gte=0"`
	IncentiveScoreA               float64                   `envconfig:"AUTOASSIGN_INCENTIVE_SCORE_A" default:"2" bson:"incentive_score_a" json:"incentiveScoreA" binding:"omitempty,gte=0"`
	IncentiveScoreWeight          float64                   `envconfig:"AUTOASSIGN_INCENTIVE_SCORE_WEIGHT" default:"1" bson:"incentive_score_weight" json:"incentiveScoreWeight" binding:"omitempty,gte=0"`
	MinIdleInMinute               int64                     `envconfig:"AUTOASSIGN_IDLE_SCORE_MIN_MINUTE" default:"30" bson:"idle_score_min_minute" json:"minIdleInMinute" binding:"omitempty,gte=0"`
	MaxIdleInMinute               int64                     `envconfig:"AUTOASSIGN_IDLE_SCORE_MAX_MINUTE" default:"60" bson:"idle_score_max_minute" json:"maxIdleInMinute" binding:"omitempty,gte=0"`
	IdleScoreWeight               float64                   `envconfig:"AUTOASSIGN_IDLE_SCORE_WEIGHT" default:"1" bson:"idle_score_weight" json:"idleScoreWeight" binding:"omitempty,gte=0"`
	NotifyViaSocketIOEnabled      bool                      `bson:"notify_via_socket_io_enabled" json:"notifyViaSocketIOEnabled"`
	AssignmentType                prediction.AssignmentType `envconfig:"AUTOASSIGN_ASSIGNMENT_TYPE" default:"single" bson:"assignment_type" json:"assignmentType"`
	MOType                        prediction.MOType         `bson:"mo_type" json:"moType"`
	BypassIdleTime                bool                      `bson:"bypass_idle_time" json:"bypassIdleTime"`
	DistanceFromZoneLimit         float64                   `bson:"distance_from_zone_limit" json:"distanceFromZoneLimit"`
	PredictionWhitelist           []string                  `bson:"prediction_whitelist" json:"predictionWhitelist"`
	PredictionBlacklist           []string                  `bson:"prediction_blacklist" json:"predictionBlacklist"`
	PredictionRestaurantWhitelist []string                  `bson:"prediction_restaurant_whitelist" json:"predictionRestaurantWhitelist"`
	BoxTypeScoreEnabled           bool                      `envconfig:"BOX_TYPE_SCORE_ENABLED" default:"false" bson:"box_type_score_enabled" json:"boxTypeScoreEnabled"`
	BoxTypeScoreWeight            float64                   `envconfig:"BOX_TYPE_SCORE_WEIGHT" default:"0.9" bson:"box_type_score_weight" json:"boxTypeScoreWeight"`
	BoxTypeScoreSettings          map[string]float64        `envconfig:"BOX_TYPE_SCORE_SETTINGS" default:"LM:0.0,STANDARD:0.0,NONSTANDARD:0.0,NONE:1000.0,FOLDABLE:0.0" bson:"box_type_score_settings" json:"boxTypeScoreSettings"`
	BoxTypeScoreServiceTypes      []string                  `envconfig:"BOX_TYPE_SCORE_SERVICE_TYPES" default:"" bson:"box_type_score_service_types" json:"boxTypeScoreServiceTypes"`

	PredictionRestaurantBlacklistTimeSlots RestaurantBlacklistTimeSlots `bson:"prediction_restaurant_blacklist_time_slots" json:"predictionRestaurantBlacklistTimeSlots" binding:"dive"`
	PredictionServiceEnabled               bool                         `bson:"prediction_service_enabled" json:"predictionServiceEnabled"`
	PredictionPredictVersion               string                       `bson:"prediction_predict_version" json:"predictionPredictVersion"`
	PredictionOptimizeVersion              string                       `bson:"prediction_optimize_version" json:"predictionOptimizeVersion"`
	PredictionBatchOptimizeVersion         string                       `bson:"prediction_batch_optimize_version" json:"predictionBatchOptimizeVersion"`
	PredictionRouteVersion                 string                       `bson:"prediction_route_version" json:"predictionRouteVersion"`
	BackToBackExtendStatusConfig           bool                         `bson:"back_to_back_extend_stastus_config" json:"backToBackExtendStatusConfig"`
	BackToBackAllowStatus                  []Status                     `bson:"back_to_back_allow_status" json:"-"`
	RedistributionDelay                    time.Duration                `envconfig:"AUTOASSIGN_REDISTRIBUTION_DELAY" default:"5s" bson:"redistribution_delay" json:"-"`
	OptimizationTopN                       int64                        `envconfig:"AUTOASSIGN_OPTIMIZATION_TOP_N" default:"0" bson:"optimization_top_n" json:"optimizationTopN" binding:"omitempty,gte=0"`
	OptimizationCandidateNumber            int64                        `envconfig:"AUTOASSIGN_OPTIMIZATION_CANDIDATE_NUMBER" default:"5" bson:"optimization_candidate_number" json:"optimizationCandidateNumber"`
	MultipleOrderAggressiveLevel           string                       `bson:"multiple_order_aggressive_level" json:"multipleOrderAggressiveLevel"`
	BikeBiasLevel                          string                       `bson:"bike_bias_level" json:"bikeBiasLevel"`
	SkipDedicatedRoundPercentage           int64                        `bson:"skip_dedicated_round_percentage" json:"skipDedicatedRoundPercentage"`
	DalianDrivingDurationInSeconds         int64                        `envconfig:"AUTOASSIGN_DALIAN_DRIVING_DURATION_IN_SECONDS" default:"300" bson:"dalian_driving_duration_in_seconds" json:"dalianDrivingDurationInSeconds" binding:"omitempty,gte=180"`

	SkipQuota    int           `bson:"skip_quota" json:"skipQuota" binding:"gte=-1"`
	BanPeriod    time.Duration `bson:"ban_period" json:"-"`
	QuotaRefresh time.Duration `bson:"quota_refresh" json:"-"`

	// priority for dedicated riders
	PriorityDedicatedRidersEnabled                bool      `envconfig:"PRIORITY_DEDICATED_RIDERS_ENABLED" default:"false" bson:"priority_dedicated_riders_enabled" json:"priorityDedicatedRidersEnabled"`
	PriorityDedicatedRidersDistance               float64   `envconfig:"PRIORITY_DEDICATED_RIDERS_DISTANCE" default:"1500" bson:"priority_dedicated_riders_distance" json:"priorityDedicatedRidersDistance"`
	PriorityDedicatedRidersAllowOrderServiceTypes []Service `envconfig:"PRIORITY_DEDICATED_RIDERS_ALLOW_ORDER_SERVICE_TYPES" default:"mart,messenger" bson:"priority_dedicated_riders_allow_order_service_types" json:"priorityDedicatedRidersAllowOrderServiceTypes"`
	PriorityDedicatedRidersOnlySingleService      []Service `envconfig:"PRIORITY_DEDICATED_RIDERS_ONLY_SINGLE_SERVICE" default:"" bson:"priority_dedicated_riders_only_single_service" json:"priorityDedicatedRidersOnlySingleService"`

	CompleteTimeCalculation CompleteTimeCalculation         `bson:"complete_time_calculation" json:"completeTimeCalculation"`
	RadiusTimeSlots         RadiusTimeSlotsWithServiceTypes `bson:"radius_time_slots" json:"radiusTimeSlots" binding:"dive"`

	// Deferred dispatch
	EnableBlacklistWhitelistForMPATR    bool                    `bson:"enable_blacklist_whitelist_for_mp_atr" json:"enableBlacklistWhitelistForMPATR"`
	DeferredDispatchFeatureEnabled      bool                    `bson:"deferred_dispatch_feature_enabled" json:"deferredDispatchFeatureEnabled"`
	DeferredDispatchFeatureEnabledMart  bool                    `bson:"deferred_dispatch_feature_enabled_mart" json:"deferredDispatchFeatureEnabledMart"`
	DeferredDispatchRestaurantWhitelist []string                `bson:"deferred_dispatch_restaurant_whitelist" json:"deferredDispatchRestaurantWhitelist"`
	DeferredDispatchRestaurantBlacklist []string                `bson:"deferred_dispatch_restaurant_blacklist" json:"deferredDispatchRestaurantBlacklist"`
	DeferredDispatchDrivingDuration     time.Duration           `bson:"deferred_dispatch_driving_duration" json:"-"`
	DeferredDispatchBufferDuration      time.Duration           `bson:"deferred_dispatch_buffer_duration" json:"-"`
	DeferredDispatchBlacklistTimeSlots  DeferBlacklistTimeSlots `bson:"deferred_dispatch_blacklist_time_slots" json:"deferredDispatchBlacklistTimeSlots"`

	BatchAssignmentEnabled    bool                `bson:"batch_assignment_enabled" json:"batchAssignmentEnabled"`
	FullyAutoAcceptEnabled    bool                `bson:"fully_auto_accept_enabled" json:"fullyAutoAcceptEnabled"`
	DalianOSRMEnabled         bool                `bson:"dalian_osrm_enabled" json:"dalianOSRMEnabled"`
	DalianOSRMPhase           int64               `bson:"dalian_osrm_phase" json:"dalianOSRMPhase" binding:"omitempty,oneof=1 2"`
	DalianOptimizeOSRMEnabled bool                `bson:"dalian_optimize_osrm_enabled" json:"dalianOptimizeOSRMEnabled"`
	RushDalianEnabled         bool                `bson:"rush_dalian_enabled" json:"rushDalianEnabled"`
	RushMode                  prediction.RushMode `bson:"rush_mode" json:"rushMode"`

	RushExperimentWhitelist   []string `bson:"rush_experiment_whitelist" json:"rushExperimentWhitelist"`
	NoRushExperimentWhitelist []string `bson:"no_rush_experiment_whitelist" json:"noRushExperimentWhitelist"`

	SmartDistributionDeprioritizationRatio float64                  `bson:"smart_distribution_deprioritization_ratio" json:"smartDistributionDeprioritizationRatio"`
	SmartDistributionGoodnessBiasLevel     prediction.BiasLevelType `bson:"smart_distribution_goodness_bias_level" json:"smartDistributionGoodnessBiasLevel,default=Bias_L0" binding:"omitempty,oneof=Bias_L0 Bias_L1 Bias_L2 Bias_L3 Bias_L4 Bias_L5 Bias_L6 Bias_L7"`
	SmartDistributionBlacklistZoneCodes    []string                 `bson:"smart_distribution_blacklist_zone_codes" json:"smartDistributionBlacklistZoneCodes"`

	DalianMPEnabled                 bool                            `bson:"dalian_mp_enabled" json:"dalianMPEnabled"`
	OverrideOrderMPDeferUntil       bool                            `bson:"override_order_mp_defer_until" json:"overrideOrderMPDeferUntil"`
	ServicePreference               ServicePreference               `bson:"service_preference" json:"servicePreference"`
	BikeB2BEnabled                  bool                            `bson:"bike_b2b_enabled" json:"bikeB2BEnabled"`
	BikeCrossServiceEnabled         bool                            `bson:"bike_cross_service_enabled" json:"bikeCrossServiceEnabled"`
	MartMOB2BEnabled                bool                            `bson:"mart_mo_b2b_enabled" json:"martMOB2BEnabled"`
	MartCrossServiceEnabled         bool                            `bson:"mart_cross_service_enabled" json:"martCrossServiceEnabled"`
	PredictionSwitchbackExperiments PredictionSwitchbackExperiments `bson:"prediction_switchback_experiments" json:"predictionSwitchbackExperiments" binding:"dive"`
	BikePriorityTimeSlots           BikePriorityTimeSlots           `bson:"bike_priority_time_slots" json:"bikePriorityTimeSlots"`
	MaxOrdersPerRider               int64                           `envconfig:"AUTOASSIGN_MAX_ORDERS_PER_RIDER" default:"4" bson:"max_orders_per_rider" json:"maxOrdersPerRider"`
}

func (ad AutoAssignDistribution) MarshalJSON() ([]byte, error) {
	type TmpAutoAssignDistribution AutoAssignDistribution
	res := struct {
		TmpAutoAssignDistribution
		Logic                                 DistributionLogic `json:"logic"`
		BackToBackAllowStatus                 string            `json:"backToBackAllowStatus"`
		RedistributionDelay                   float64           `json:"redistributionDelay"`
		BanPeriod                             int               `json:"banPeriod"`
		QuotaRefresh                          int               `json:"quotaRefresh"`
		DeferredDispatchDrivingDurationMinute int               `json:"deferredDispatchDrivingDurationMinute"`
		DeferredDispatchBufferDurationMinute  int               `json:"deferredDispatchBufferDurationMinute"`
	}{
		TmpAutoAssignDistribution:             TmpAutoAssignDistribution(ad),
		Logic:                                 ad.Logic(),
		BackToBackAllowStatus:                 StringFromStatusList(ad.BackToBackAllowStatus),
		RedistributionDelay:                   ad.RedistributionDelay.Seconds(),
		BanPeriod:                             int(ad.BanPeriod.Minutes()),
		QuotaRefresh:                          int(ad.QuotaRefresh.Minutes()),
		DeferredDispatchDrivingDurationMinute: int(ad.DeferredDispatchDrivingDuration.Minutes()),
		DeferredDispatchBufferDurationMinute:  int(ad.DeferredDispatchBufferDuration.Minutes()),
	}
	return json.Marshal(res)
}

func InitDefaultAutoAssignDistribution() AutoAssignDistribution {
	envconfig.MustProcess("", &DefaultAutoAssignConfig)

	return DefaultAutoAssignConfig
}

func NewAutoAssignDistribution() *AutoAssignDistribution {
	dist := DefaultAutoAssignConfig
	dist.SkipQuota = DefaultSkipQuota
	dist.BanPeriod = DefaultBanPeriod
	dist.QuotaRefresh = DefaultQuotaRefresh
	dist.CompleteTimeCalculation = DefaultCompleteTimeCalculation
	dist.BoxTypeScoreSettings = utils.CloneMap(dist.BoxTypeScoreSettings)

	return &dist
}

func (ad *AutoAssignDistribution) Logic() DistributionLogic {
	return DistributionLogicAutoAssign
}

func (ad *AutoAssignDistribution) MaxRadiusInKM(serviceType Service, searchRadiusByDistrictInKm float64) float64 {
	if serviceType == ServiceBike && searchRadiusByDistrictInKm != 0 {
		return searchRadiusByDistrictInKm
	}
	if overridden, km := ad.RadiusTimeSlots.GetOverriddenKM(time.Now(), serviceType); overridden {
		return km
	}
	return ad.MaxRadiusInKm
}

func (ad *AutoAssignDistribution) MaxRadiusInMeter(serviceType Service, searchRadiusByDistrictInKm float64) float64 {
	return ad.MaxRadiusInKM(serviceType, searchRadiusByDistrictInKm) * 1000
}

func (ad *AutoAssignDistribution) MaxRadiusInMeterByServices(searchRadiusByDistrictInKm float64) map[Service]float64 {
	radiusByServices := make(map[Service]float64)
	for _, serviceType := range CurrentSupportServices {
		radiusByServices[serviceType] = ad.MaxRadiusInMeter(serviceType, searchRadiusByDistrictInKm)
	}
	return radiusByServices
}

type PredictionModelVersions struct {
	SwitchbackExperimentName string `json:"switchbackExperimentName"`
	PredictVersion           string `json:"predictVersion"`
	OptimizeVersion          string `json:"optimizeVersion"`
	BatchOptimizeVersion     string `json:"batchOptimizeVersion"`
	RouteVersion             string `json:"routeVersion"`
}

func (ad *AutoAssignDistribution) GetPredictionModelVersions(t time.Time) PredictionModelVersions {
	if experiment := ad.PredictionSwitchbackExperiments.GetAtTime(t); experiment != nil {
		return PredictionModelVersions{
			SwitchbackExperimentName: experiment.Name,
			PredictVersion:           experiment.PredictVersion,
			OptimizeVersion:          experiment.OptimizeVersion,
			BatchOptimizeVersion:     experiment.BatchOptimizeVersion,
			RouteVersion:             experiment.RouteVersion,
		}
	}

	return PredictionModelVersions{
		PredictVersion:       ad.PredictionPredictVersion,
		OptimizeVersion:      ad.PredictionOptimizeVersion,
		BatchOptimizeVersion: ad.PredictionBatchOptimizeVersion,
		RouteVersion:         ad.PredictionRouteVersion,
	}
}

func (ad *AutoAssignDistribution) GetModelVersionWithSwitchbackExperiments(t time.Time, name PredictionModelName, switchbackExperiments *SwitchbackExperiments) string {
	if switchbackExperiments != nil {
		if predictionModel, ok := switchbackExperiments.Params.PredictionModels[name]; ok {
			return predictionModel.Hash
		}
	}
	switch name {
	case PredictModel:
		return ad.GetPredictionModelVersions(t).PredictVersion
	case OptimizeModel:
		return ad.GetPredictionModelVersions(t).OptimizeVersion
	case BatchOptimizeModel:
		return ad.GetPredictionModelVersions(t).BatchOptimizeVersion
	case RouteModel:
		return ad.GetPredictionModelVersions(t).RouteVersion
	default:
		return ""
	}
}

func (ad *AutoAssignDistribution) SetSkipQuota(skip int) {
	ad.SkipQuota = skip
}

func (ad *AutoAssignDistribution) SetBanPeriod(period time.Duration) {
	ad.BanPeriod = period
}

func (ad *AutoAssignDistribution) SetQuotaRefresh(refresh time.Duration) {
	ad.QuotaRefresh = refresh
}

func (ad *AutoAssignDistribution) SetNotifyViaSocketIOEnabled(enabled bool) {
	ad.NotifyViaSocketIOEnabled = enabled
}

func (ad *AutoAssignDistribution) SetRedistributionDelay(duration time.Duration) {
	if duration.Seconds() < 5 {
		ad.RedistributionDelay = 5 * time.Second
	} else {
		ad.RedistributionDelay = duration
	}
}

func (ad *AutoAssignDistribution) UpdateCompleteTimeCalculation(updater CompleteTimeCalculation) {
	ad.CompleteTimeCalculation.Update(updater)
}

type AreaDistributionConfig struct {
	NegativeBalanceGroupsConfig []NegativeBalanceGroupConfig
	TierNegativeBalanceConfig   TierNegativeBalanceConfig
	Revision                    string
}

func GetNegativeGroupByName(groups []NegativeBalanceGroupConfig, name string) NegativeBalanceGroupConfig {
	if len(groups) == 0 {
		return NegativeBalanceGroupConfig{}
	}

	for _, v := range groups {
		if v.Name == name {
			return v
		}
	}
	return NegativeBalanceGroupConfig{}
}

type ServicePreference struct {
	IsEnabled bool `bson:"is_enabled" json:"isEnabled"`
	CanEnable bool `bson:"can_enable" json:"canEnable"`
	// AllowedTypesForSelection is service types can opt out with rider side
	AllowedTypesForSelection []Service `bson:"allowed_types_for_selection" json:"allowedTypesForSelection"`
	AllTypes                 []Service `bson:"all_types" json:"allTypes"`
	IsMergeFoodMartEnabled   bool      `bson:"-" json:"isMergeFoodMartEnabled"`
}
