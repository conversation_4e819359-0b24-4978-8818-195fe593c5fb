package model_test

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestGetShiftAttendanceTime(t *testing.T) {
	t.Run("return items correctly", func(t *testing.T) {
		id1 := primitive.NewObjectID()
		id2 := primitive.NewObjectID()
		id3 := primitive.NewObjectID()

		shifts := []model.Shift{
			{
				Start:      generateTime(1, 8, 30),
				End:        generateTime(1, 10, 00),
				BreakQuota: 10,
				ID:         id1,
			},
			{
				Start:      generateTime(1, 12, 00),
				End:        generateTime(1, 14, 00),
				BreakQuota: 20,
				ID:         id2,
			},
			{
				Start:      generateTime(1, 18, 30),
				End:        generateTime(1, 21, 00),
				BreakQuota: 20,
				ID:         id3,
			},
		}

		expected := []model.AttendanceTimeItem{
			{
				Actual:    90,
				ShiftTime: 90,
				ShiftId:   id1.Hex(),
			},
			{
				Actual:    120,
				ShiftTime: 120,
				ShiftId:   id2.Hex(),
			},
			{
				Actual:    145,
				ShiftTime: 150,
				ShiftId:   id3.Hex(),
			},
		}

		logs := []model.AttendanceLog{
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTimeWithSec(1, 8, 30, 10),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTimeWithSec(1, 8, 30, 20),
			},
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTime(1, 8, 32),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTime(1, 9, 57),
			},
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTime(1, 11, 30),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTime(1, 12, 00),
			},
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTime(1, 12, 20),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTime(1, 15, 00),
			},
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTime(1, 18, 00),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTime(1, 20, 35),
			},
		}

		result := model.GetShiftAttendanceTime(logs, shifts)
		require.Equal(t, expected, result)
	})
}

func generateTime(day int, hr int, min int) time.Time {
	return time.Date(2022, 1, day, hr, min, 0, 0, timeutil.BangkokLocation())
}

func generateTimeWithSec(day int, hr int, min int, sec int) time.Time {
	return time.Date(2022, 1, day, hr, min, sec, 0, timeutil.BangkokLocation())
}

func generateTimeWithMonth(day int, hr int, min int, month time.Month) time.Time {
	return time.Date(2022, month, day, hr, min, 0, 0, timeutil.BangkokLocation())
}

func generateTimeWithMonthAndSec(day int, hr int, min int, sec int, month time.Month) time.Time {
	return time.Date(2022, month, day, hr, min, sec, 0, timeutil.BangkokLocation())
}

func TestGetLogFromShift(t *testing.T) {
	t.Run("return logs correctly", func(t *testing.T) {
		shifts := []model.Shift{
			{
				Start:      generateTime(1, 8, 30),
				End:        generateTime(1, 10, 00),
				BreakQuota: 10,
			},
		}

		expected := []model.AttendanceLog{
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTimeWithSec(1, 8, 30, 10),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTimeWithSec(1, 8, 30, 20),
			},
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTime(1, 8, 32),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTime(1, 9, 57),
			},
		}

		logs := []model.AttendanceLog{
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTimeWithSec(1, 8, 30, 10),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTimeWithSec(1, 8, 30, 20),
			},
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTime(1, 8, 32),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTime(1, 9, 57),
			},
			{
				Status: model.AttendanceStatusOnline,
				Time:   generateTime(1, 11, 30),
			},
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTime(1, 12, 00),
			},
		}

		result := model.GetLogsFromShift(logs, shifts[0].Start, shifts[0].End)
		require.Equal(t, expected, result)
	})

	t.Run("should not prefill log with one OFFLINE log", func(t *testing.T) {
		shifts := []model.Shift{
			{
				Start:      generateTime(1, 8, 30),
				End:        generateTime(1, 14, 00),
				BreakQuota: 10,
			},
		}
		logs := []model.AttendanceLog{
			{
				Status: model.AttendanceStatusOffline,
				Time:   generateTime(1, 8, 30),
			},
		}

		result := model.GetLogsFromShift(logs, shifts[0].Start, shifts[0].End)
		require.Equal(t, logs, result)
	})

	shift := model.Shift{
		Start:      generateTime(1, 8, 30),
		End:        generateTime(1, 14, 00),
		BreakQuota: 10,
	}

	shiftAllOnline := model.Shift{
		Start:      generateTime(1, 3, 00),
		End:        generateTime(1, 6, 59),
		BreakQuota: 0,
	}

	tests := []struct {
		name         string
		expectedLogs []model.AttendanceLog
		logs         []model.AttendanceLog
		shift        model.Shift
	}{
		{
			name: "dup online",
			expectedLogs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 30, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 8, 30, 20),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 40),
				},
			},
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 30, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 8, 30, 20),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 30),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 31),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 32),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 40),
				},
			},
			shift: shift,
		},
		{
			name: "dup offline",
			expectedLogs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 30, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 8, 30, 20),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 32),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 40),
				},
			},
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 30, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 8, 30, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 31),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 32),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 40),
				},
			},
			shift: shift,
		},
		{
			name: "when dup log online, should return previous log",
			expectedLogs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 3, 00, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 6, 59, 00),
				},
			},
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 2, 19, 27),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 3, 57, 03),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 4, 16, 11),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 4, 33, 59),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 4, 48, 36),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 5, 13, 42),
				},
			},
			shift: shiftAllOnline,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rate := model.GetLogsFromShift(tt.logs, tt.shift.Start, tt.shift.End)
			require.Equal(t, tt.expectedLogs, rate)
		})
	}
}

func TestCalculateBreakTime(t *testing.T) {
	shift := model.Shift{
		Start: generateTime(1, 8, 00),
		End:   generateTime(1, 10, 00),
	}

	pitLokShift := model.Shift{
		Start: generateTimeWithSec(1, 3, 00, 00),
		End:   generateTimeWithSec(1, 6, 59, 59),
	}

	tests := []struct {
		name              string
		expectedBreakTime float64
		items             []model.AttendanceLog
		now               time.Time
		shift             model.Shift
	}{
		{
			name:              "zero break time",
			expectedBreakTime: 0,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 9, 00),
				},
			},
			now:   generateTime(1, 9, 00),
			shift: shift,
		},
		{
			name:              "15.5 minutes break time",
			expectedBreakTime: 15.5,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 7, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 9, 00),
				},
			},
			now:   generateTimeWithSec(1, 9, 15, 30),
			shift: shift,
		},
		{
			name:              "now after shift end",
			expectedBreakTime: 60,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 9, 00),
				},
			},
			now:   generateTime(1, 10, 15),
			shift: shift,
		},
		{
			name:              "late attend shift",
			expectedBreakTime: 45.5,
			items: []model.AttendanceLog{
				//this log was prefilled by cron
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 30, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 9, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 9, 15),
				},
			},
			now:   generateTime(1, 9, 30),
			shift: shift,
		},
		{
			name:              "online offline many times",
			expectedBreakTime: 10.5 + 25 + 12,
			items: []model.AttendanceLog{
				//this log was prefilled by cron
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 10, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 8, 30),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 9, 00),
				},
			},
			now:   generateTime(1, 9, 12),
			shift: shift,
		},
		{
			name:              "verify two decimals",
			expectedBreakTime: 6.5,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 00, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 8, 30, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 35, 15),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 9, 00, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 9, 01, 15),
				},
			},
			now:   generateTimeWithSec(1, 9, 01, 20),
			shift: shift,
		},
		{
			name:              "Online - before shift start",
			expectedBreakTime: 0,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 2, 19, 27),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 3, 57, 03),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 7, 57, 03),
				},
			},
			now:   generateTimeWithSec(1, 20, 00, 00),
			shift: pitLokShift,
		},
		{
			name:              "Online - shift start",
			expectedBreakTime: 0,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 3, 00, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 3, 57, 03),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 7, 57, 03),
				},
			},
			now:   generateTimeWithSec(1, 20, 00, 00),
			shift: pitLokShift,
		},
		{
			name:              "Offline - before shift start",
			expectedBreakTime: 239.98,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 2, 19, 27),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 3, 57, 03),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 6, 25, 22),
				},
			},
			now:   generateTimeWithSec(1, 20, 00, 00),
			shift: pitLokShift,
		},
		{
			name:              "Offline - shift period",
			expectedBreakTime: 239.98,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 3, 00, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 3, 57, 03),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 7, 25, 22),
				},
			},
			now:   generateTimeWithSec(1, 20, 00, 00),
			shift: pitLokShift,
		},
		{
			name:              "On-Off",
			expectedBreakTime: 122.93,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 2, 19, 27),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 3, 57, 03),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 4, 57, 03),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 5, 07, 03),
				},
			},
			now:   generateTimeWithSec(1, 20, 00, 00),
			shift: pitLokShift,
		},
		{
			name:              "Off-On",
			expectedBreakTime: 117.05,
			items: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 2, 19, 27),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 3, 57, 03),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 4, 57, 03),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 5, 07, 03),
				},
			},
			now:   generateTimeWithSec(1, 20, 00, 00),
			shift: pitLokShift,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rate := model.CalculateBreakTime(tt.items, tt.shift, tt.now)
			require.Equal(t, tt.expectedBreakTime, rate)
		})
	}
}

func TestPerShift(t *testing.T) {
	s := model.Shift{
		Start:      generateTime(1, 10, 30),
		End:        generateTime(1, 13, 00),
		BreakQuota: 20,
	}

	shift := model.Shift{
		Start:      generateTime(1, 12, 00),
		End:        generateTime(1, 14, 00),
		BreakQuota: 20,
	}

	tests := []struct {
		name         string
		expectedRate float64
		logs         []model.AttendanceLog
		shift        model.Shift
	}{
		{
			name:         "one shift - Attended w/o break time",
			expectedRate: 100.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 10, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 13, 30),
				},
			},
			shift: s,
		},
		{
			name:         "one shift - Break time within quota",
			expectedRate: 100.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 10, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 12, 45),
				},
			},
			shift: s,
		},
		{
			name:         "one shift - Break time exceed quota",
			expectedRate: 93.33,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 10, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 11, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 11, 04),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 12, 16),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 12, 42),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 13, 00),
				},
			},
			shift: s,
		},
		{
			name:         "one shift - Break time exceed quota 2",
			expectedRate: 14,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 10, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 10, 31),
				},
			},
			shift: s,
		},
		{
			name:         "one shift - Break time exceed quota 3",
			expectedRate: 13.89,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 10, 31, 05),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 10, 31, 55),
				},
			},
			shift: s,
		},
		{
			name:         "many shift - Exceed break time in some period",
			expectedRate: 100,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 11, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 12, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 12, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 15, 00),
				},
			},
			shift: shift,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rate := model.CalculatePerShift(tt.logs, tt.shift)
			require.Equal(t, tt.expectedRate, rate)
		})
	}
}

func TestDaily(t *testing.T) {
	shifts := []model.Shift{
		{
			Start:      generateTime(1, 10, 30),
			End:        generateTime(1, 13, 00),
			BreakQuota: 20,
		},
	}
	ss := []model.Shift{
		{
			Start:      generateTime(1, 8, 30),
			End:        generateTime(1, 10, 00),
			BreakQuota: 10,
		},
		{
			Start:      generateTime(1, 12, 00),
			End:        generateTime(1, 14, 00),
			BreakQuota: 20,
		},
	}
	sss := []model.Shift{
		{
			Start:      generateTime(1, 8, 30),
			End:        generateTime(1, 10, 00),
			BreakQuota: 10,
		},
		{
			Start:      generateTime(1, 12, 00),
			End:        generateTime(1, 14, 00),
			BreakQuota: 20,
		},
		{
			Start:      generateTime(1, 18, 30),
			End:        generateTime(1, 21, 00),
			BreakQuota: 20,
		},
	}

	tests := []struct {
		name         string
		expectedRate float64
		logs         []model.AttendanceLog
		shifts       []model.Shift
	}{
		{
			name:         "one shift - Break time exceed quota",
			expectedRate: 93.33,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 10, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 11, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 11, 04),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 12, 16),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 12, 42),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 13, 00),
				},
			},
			shifts: shifts,
		},
		{
			name:         "many shift - Multiple period in 1 day (1)",
			expectedRate: 100.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 30, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 8, 30, 20),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 32),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 9, 57),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 11, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 12, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 12, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 15, 00),
				},
			},
			shifts: ss,
		},
		{
			name:         "many shift - Multiple period in 1 day (2)",
			expectedRate: 98.73,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 8, 30, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 8, 30, 20),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 8, 32),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 9, 57),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 11, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 12, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 12, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 15, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 18, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 20, 35, 25),
				},
			},
			shifts: sss,
		},
		{
			name:         "get zero if no attendance log",
			expectedRate: 0,
			logs:         []model.AttendanceLog{},
			shifts:       shifts,
		},
		{
			name:         "one offline log with equal shift start",
			expectedRate: 0,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 18, 11),
				},
			},
			shifts: []model.Shift{
				{
					Start:      generateTime(1, 18, 11),
					End:        generateTime(1, 18, 38),
					BreakQuota: 2,
				},
			},
		},
		{
			name:         "one offline log with before shift start",
			expectedRate: 0,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 18, 8),
				},
			},
			shifts: []model.Shift{
				{
					Start:      generateTime(1, 18, 11),
					End:        generateTime(1, 18, 38),
					BreakQuota: 2,
				},
			},
		},
		{
			name:         "first offline log with before shift start",
			expectedRate: 66.67,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 12, 37, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 12, 45, 47),
				},
			},
			shifts: []model.Shift{
				{
					Start:      generateTimeWithSec(1, 12, 37, 00),
					End:        generateTimeWithSec(1, 12, 42, 00),
					BreakQuota: 2,
				},
				{
					Start:      generateTimeWithSec(1, 12, 45, 00),
					End:        generateTimeWithSec(1, 12, 55, 00),
					BreakQuota: 2,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			items := model.GetShiftAttendanceTime(tt.logs, tt.shifts)
			rate := model.GetShiftAttendanceRate(items)
			require.Equal(t, tt.expectedRate, rate)
		})
	}
}

func TestWeekly(t *testing.T) {
	shifts := []model.Shift{
		{
			Start:      generateTime(1, 10, 30),
			End:        generateTime(1, 13, 00),
			BreakQuota: 20,
		},
	}

	tests := []struct {
		name         string
		expectedRate float64
		logs         []model.AttendanceLog
		shifts       []model.Shift
	}{
		{
			name:         "one shift - Break time within quota",
			expectedRate: 100.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(1, 10, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(1, 12, 45),
				},
			},
			shifts: shifts,
		},
	}

	for _, tt := range tests {
		t.Run(fmt.Sprintf("one shift %s", tt.name), func(t *testing.T) {
			items := model.GetShiftAttendanceTime(tt.logs, tt.shifts)
			rate := model.GetShiftAttendanceRate(items)
			require.Equal(t, tt.expectedRate, rate)
		})
	}

	oneWeekShifts := []model.Shift{
		{
			Start:      generateTime(3, 8, 30),
			End:        generateTime(3, 10, 00),
			BreakQuota: 10,
		},
		{
			Start:      generateTime(3, 18, 30),
			End:        generateTime(3, 20, 45),
			BreakQuota: 20,
		},
		{
			Start:      generateTime(4, 8, 30),
			End:        generateTime(4, 10, 00),
			BreakQuota: 10,
		},
	}
	manyWeeksShifts := []model.Shift{
		{
			Start:      generateTime(6, 8, 30),
			End:        generateTime(6, 10, 00),
			BreakQuota: 10,
		},
		{
			Start:      generateTime(8, 18, 30),
			End:        generateTime(8, 20, 45),
			BreakQuota: 20,
		},
		{
			Start:      generateTime(11, 8, 30),
			End:        generateTime(11, 10, 00),
			BreakQuota: 10,
		},
	}

	tts := []struct {
		name         string
		expectedRate float64
		logs         []model.AttendanceLog
		shifts       []model.Shift
	}{
		{
			name:         "multiple period in 1 week",
			expectedRate: 100.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(3, 7, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(3, 10, 30),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(3, 15, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(3, 18, 30, 10),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(3, 18, 30, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(3, 21, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(4, 8, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(4, 10, 00),
				},
			},
			shifts: oneWeekShifts,
		},
		{
			name:         "multiple period in different week (1)",
			expectedRate: 100.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 7, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 10, 30),
				},
			},
			shifts: manyWeeksShifts[0:1],
		},
		{
			name:         "multiple period in different week (2)",
			expectedRate: 100.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 7, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 10, 30),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 15, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(8, 18, 30, 10),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(8, 18, 30, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 21, 00),
				},
			},
			shifts: manyWeeksShifts[0:2],
		},
		{
			name:         "multiple period in different week (3)",
			expectedRate: 66.59,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(11, 6, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 8, 40),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(11, 9, 20, 4),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 10, 00),
				},
			},
			shifts: manyWeeksShifts[2:],
		},
	}

	for _, tt := range tts {
		t.Run(fmt.Sprintf("many shift %s", tt.name), func(t *testing.T) {
			items := model.GetShiftAttendanceTime(tt.logs, tt.shifts)
			rate := model.GetShiftAttendanceRate(items)
			require.Equal(t, tt.expectedRate, rate)
		})
	}

}

func TestMonthly(t *testing.T) {
	shifts := []model.Shift{
		{
			Start:      generateTime(3, 10, 30),
			End:        generateTime(3, 13, 00),
			BreakQuota: 20,
		},
	}
	diffMonthShifts := []model.Shift{
		{
			Start:      generateTime(20, 8, 30),
			End:        generateTime(20, 10, 00),
			BreakQuota: 10,
		},
		{
			Start:      generateTime(22, 18, 30),
			End:        generateTime(22, 20, 45),
			BreakQuota: 20,
		},
		{
			Start:      generateTimeWithMonth(1, 8, 30, time.February),
			End:        generateTimeWithMonth(1, 10, 00, time.February),
			BreakQuota: 10,
		},
		{
			Start:      generateTimeWithMonth(1, 14, 30, time.February),
			End:        generateTimeWithMonth(1, 16, 00, time.February),
			BreakQuota: 10,
		},
	}
	diffWeekShifts := []model.Shift{
		{
			Start:      generateTime(6, 8, 30),
			End:        generateTime(6, 10, 00),
			BreakQuota: 10,
		},
		{
			Start:      generateTime(8, 18, 30),
			End:        generateTime(8, 20, 45),
			BreakQuota: 20,
		},
		{
			Start:      generateTime(11, 8, 30),
			End:        generateTime(11, 10, 00),
			BreakQuota: 10,
		},
		{
			Start:      generateTime(11, 14, 30),
			End:        generateTime(11, 16, 00),
			BreakQuota: 10,
		},
	}
	diffMonthExceedQuotaShifts := []model.Shift{
		{
			Start:      generateTime(20, 8, 30),
			End:        generateTime(20, 10, 00),
			BreakQuota: 10,
		},
		{
			Start:      generateTime(29, 18, 30),
			End:        generateTime(29, 20, 45),
			BreakQuota: 20,
		},
		{
			Start:      generateTimeWithMonth(1, 8, 30, time.February),
			End:        generateTimeWithMonth(1, 10, 00, time.February),
			BreakQuota: 10,
		},
		{
			Start:      generateTimeWithMonth(1, 14, 30, time.February),
			End:        generateTimeWithMonth(1, 16, 00, time.February),
			BreakQuota: 10,
		},
	}

	prefillOfflineLog := []model.Shift{
		{
			Start:      generateTime(1, 13, 10),
			End:        generateTime(1, 16, 00),
			BreakQuota: 10,
		},
	}

	tests := []struct {
		name         string
		expectedRate float64
		logs         []model.AttendanceLog
		shifts       []model.Shift
	}{
		{
			name:         "one shift - Break time exceed quota",
			expectedRate: 14.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(3, 10, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(3, 10, 31),
				},
			},
			shifts: shifts,
		},
		{
			name:         "many shift - Multiple period different month(2.1)",
			expectedRate: 0,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(20, 2, 00),
				},
			},
			shifts: diffMonthShifts[0:1],
		},
		{
			name:         "many shift - Multiple period different month(2.2)",
			expectedRate: 0,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(20, 2, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(22, 15, 00, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(22, 15, 00, 20),
				},
			},
			shifts: diffMonthShifts[0:2],
		},
		{
			name:         "many shift - Multiple period different month(2.3)",
			expectedRate: 0,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(20, 2, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(22, 15, 00, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(22, 15, 00, 20),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 0, 00, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 0, 30, time.February),
				},
			},
			shifts: diffMonthShifts[2:3],
		},
		{
			name:         "many shift - Multiple period different month(2.4)",
			expectedRate: 32.88,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(20, 2, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(22, 15, 00, 10),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(22, 15, 00, 20),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 0, 00, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 0, 30, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 0, 00, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 0, 30, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 11, 00, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 14, 31, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 14, 36, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 15, 00, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonthAndSec(1, 15, 25, 0, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonthAndSec(1, 15, 29, 11, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 15, 35, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 15, 40, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 15, 45, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 17, 70, time.February),
				},
			},
			shifts: diffMonthShifts[2:],
		},
		{
			name:         "many shift - Multiple period in different week(2.1)",
			expectedRate: 45.19,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 8, 35),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(6, 9, 00, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(6, 9, 00, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 9, 01),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 9, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 10, 00),
				},
			},
			shifts: diffWeekShifts[0:1],
		},
		{
			name:         "many shift - Multiple period in different week(2.2)",
			expectedRate: 58.08,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 8, 35),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(6, 9, 00, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(6, 9, 00, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 9, 01),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 9, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 10, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 18, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 19, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 20, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 20, 30),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 20, 31),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 20, 41),
				},
			},
			shifts: diffWeekShifts[0:2],
		},
		{
			name:         "many shift - Multiple period in different week(2.3)",
			expectedRate: 70.05,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 8, 35),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(6, 9, 00, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(6, 9, 00, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 9, 01),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 9, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 10, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 18, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 19, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 20, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 20, 30),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 20, 31),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 20, 41),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(11, 8, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 9, 01),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(11, 9, 05, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(11, 9, 05, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(11, 9, 11),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 10, 00),
				},
			},
			shifts: diffWeekShifts[0:3],
		},
		{
			name:         "many shift - Multiple period in different week(2.4)",
			expectedRate: 69.3,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 8, 35),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(6, 9, 00, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(6, 9, 00, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 9, 01),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(6, 9, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(6, 10, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 18, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 19, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 20, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 20, 30),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(8, 20, 31),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(8, 20, 41),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(11, 8, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 9, 01),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(11, 9, 05, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(11, 9, 05, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(11, 9, 11),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 10, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(11, 14, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 15, 15),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(11, 15, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 16, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(11, 16, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(11, 16, 05),
				},
			},
			shifts: diffWeekShifts,
		},
		{
			name:         "many shift - Multiple period in different month(1.1)",
			expectedRate: 45.19,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(20, 8, 35),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(20, 9, 00, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(20, 9, 00, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(20, 9, 01),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(20, 9, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(20, 10, 00),
				},
			},
			shifts: diffMonthExceedQuotaShifts[0:1],
		},
		{
			name:         "many shift - Multiple period in different month(1.2)",
			expectedRate: 58.08,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(20, 8, 35),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(20, 9, 00, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(20, 9, 00, 20),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(20, 9, 01),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(20, 9, 55),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(20, 10, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(29, 18, 30),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(29, 19, 00),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(29, 20, 00),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(29, 20, 30),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTime(29, 20, 31),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTime(29, 20, 41),
				},
			},
			shifts: diffMonthExceedQuotaShifts[0:2],
		},
		{
			name:         "many shift - Multiple period in different month(1.3)",
			expectedRate: 100.00,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 8, 30, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 9, 01, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 9, 05, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 9, 05, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 9, 11, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 10, 00, time.February),
				},
			},
			shifts: diffMonthExceedQuotaShifts[2:3],
		},
		{
			name:         "many shift - Multiple period in different month(1.4)",
			expectedRate: 83.29,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 8, 30, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 9, 01, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 9, 05, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 9, 05, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 9, 11, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 10, 00, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 14, 30, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 15, 15, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 15, 55, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonthAndSec(1, 15, 59, 55, time.February),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithMonth(1, 16, 00, time.February),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithMonth(1, 16, 05, time.February),
				},
			},
			shifts: diffMonthExceedQuotaShifts[2:],
		},
		{
			name:         "prefill OFFLINE log",
			expectedRate: 6.57,
			logs: []model.AttendanceLog{
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 12, 31, 33),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 14, 34, 33),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 14, 34, 42),
				},
				{
					Status: model.AttendanceStatusOnline,
					Time:   generateTimeWithSec(1, 14, 35, 53),
				},
				{
					Status: model.AttendanceStatusOffline,
					Time:   generateTimeWithSec(1, 14, 36, 54),
				},
			},
			shifts: prefillOfflineLog,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			items := model.GetShiftAttendanceTime(tt.logs, tt.shifts)
			rate := model.GetShiftAttendanceRate(items)
			require.Equal(t, tt.expectedRate, rate)
		})
	}
}

func TestGetShiftAttendanceRate(t *testing.T) {
	tests := []struct {
		name         string
		expectedRate float64
		items        []model.AttendanceTimeItem
	}{
		{
			name:         "full attr",
			expectedRate: 100,
			items: []model.AttendanceTimeItem{
				{
					Actual:    100,
					ShiftTime: 100,
				},
			},
		},
		{
			name:         "half attr",
			expectedRate: 50,
			items: []model.AttendanceTimeItem{
				{
					Actual:    0,
					ShiftTime: 100,
				},
				{
					Actual:    100,
					ShiftTime: 100,
				},
			},
		},
		{
			name:         "zero attr",
			expectedRate: 0,
			items: []model.AttendanceTimeItem{
				{
					Actual:    0,
					ShiftTime: 100,
				},
			},
		},
		{
			name:         "no attr",
			expectedRate: 0,
			items:        []model.AttendanceTimeItem{},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rate := model.GetShiftAttendanceRate(tt.items)
			require.Equal(t, tt.expectedRate, rate)
		})
	}
}
