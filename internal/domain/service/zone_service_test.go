package service_test

import (
	"context"
	"fmt"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/twpayne/go-geom"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/lineman/fleet-distribution/internal/apis/zone"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
)

func TestCreateZone(t *testing.T) {
	t.<PERSON>l()

	coordinates := zone.Coordinates{[][]geom.Coord{{{21.374177627032168, -9.611831304962493}}}}
	request := service.CreateZoneRequest{
		Name:        "Bang Mod",
		DisplayName: "Bang Mod",
		Region:      "BKK",
		Coordinates: service.ZoneCoordinates(coordinates),
	}

	t.Run("should created zone without error", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		ctx := context.Background()
		deps.zoneRepo.EXPECT().
			Create(ctx, gomock.Any()).
			DoAndReturn(func(ctx context.Context, zone model.Zone) error {
				require.Equal(t, "BKK-BANGMOD", zone.ZoneCode)
				return nil
			})

		_, err := tr.Create(ctx, request)
		require.NoError(t, err)
	})

	t.Run("should return error when create duplicate zone", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		ctx := context.Background()
		deps.zoneRepo.EXPECT().
			Create(ctx, gomock.Any()).
			Return(service.ErrZoneDuplicate)

		_, err := tr.Create(ctx, request)
		require.Error(t, err, service.ErrZoneDuplicate.Error())
	})

	t.Run("should return error when face with unexpected error from db", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		ctx := context.Background()
		deps.zoneRepo.EXPECT().
			Create(ctx, gomock.Any()).
			Return(fmt.Errorf("can not create connection to database"))

		_, err := tr.Create(ctx, request)
		require.Error(t, err, "create zone repo error: can not create connection to database")
	})

}

func TestUpdateZone(t *testing.T) {
	t.Parallel()

	zoneId := "63fcd66481b11f2f49c9bb6b"
	coordinates := zone.Coordinates{[][]geom.Coord{{{98.30891332956276, -7.8652348996542685}}}}
	request := service.UpdateZoneRequest{
		DisplayName: "Bang Pakok Market",
		Active:      true,
		Coordinates: service.ZoneCoordinates(coordinates),
	}

	t.Run("should update zone without error", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		ctx := context.Background()
		defer finish()

		deps.zoneRepo.EXPECT().
			UpdateById(ctx, gomock.Any(), gomock.Any()).DoAndReturn(func(c context.Context, id string, zone model.UpdateZoneReq) error {
			require.Equal(t, request.Active, zone.Active)
			require.Equal(t, request.DisplayName, zone.DisplayName)
			require.Equal(t, model.NewZoneGeometry(coordinates), zone.Geometry)
			return nil
		})

		err := tr.UpdateByID(ctx, zoneId, request)
		require.NoError(t, err)
	})

	t.Run("should return error when update not exist zone", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		ctx := context.Background()
		defer finish()

		deps.zoneRepo.EXPECT().
			UpdateById(ctx, gomock.Any(), gomock.Any()).
			Return(service.ErrZoneNotFound)

		err := tr.UpdateByID(ctx, zoneId, request)
		require.Error(t, err, service.ErrZoneNotFound.Error())
	})

	t.Run("should return error when face with unexpected error from db", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		ctx := context.Background()
		defer finish()

		deps.zoneRepo.EXPECT().
			UpdateById(ctx, gomock.Any(), gomock.Any()).
			Return(fmt.Errorf("can not create connection to database"))

		err := tr.UpdateByID(ctx, zoneId, request)
		require.Error(t, err, "update zone repo by id error: can not create connection to database")
	})
}

func TestGetById(t *testing.T) {
	t.Parallel()
	zoneId := "63fcd66481b11f2f49c9bb6b"
	zoneName := "on nut"
	zoneCode := "BKK-ONNUT"
	displayName := "on nut"
	active := true
	region := "BKK"
	coordinates := zone.Coordinates{[][]geom.Coord{{{98.30891332956276, -7.8652348996542685}}}}
	geometry := model.NewZoneGeometry(coordinates)

	t.Run("should return zone without error", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		ctx := context.Background()
		hex, _ := primitive.ObjectIDFromHex(zoneId)

		deps.zoneRepo.EXPECT().
			FindById(ctx, zoneId, gomock.Any()).
			Return(model.Zone{
				ID:          hex,
				Name:        zoneName,
				DisplayName: displayName,
				ZoneCode:    zoneCode,
				Region:      region,
				Active:      active,
				Geometry:    geometry,
			}, nil)

		response, err := tr.GetByID(ctx, zoneId)
		require.NoError(t, err)
		require.Equal(t, zoneId, response.ID.Hex())
		require.Equal(t, zoneName, response.Name)
		require.Equal(t, displayName, response.DisplayName)
		require.Equal(t, zoneCode, response.ZoneCode)
		require.Equal(t, region, response.Region)
		require.Equal(t, active, response.Active)
		require.Equal(t, geometry, response.Geometry)
	})

	t.Run("should return error when zoneId is not exist", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()
		ctx := context.Background()

		deps.zoneRepo.EXPECT().
			FindById(ctx, gomock.Any(), gomock.Any()).
			Return(model.Zone{}, service.ErrZoneNotFound)

		_, err := tr.GetByID(ctx, zoneId)
		require.Error(t, err, service.ErrZoneNotFound)
	})

	t.Run("should return error when face with unexpected error from db", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()
		ctx := context.Background()

		deps.zoneRepo.EXPECT().
			FindById(ctx, gomock.Any(), gomock.Any()).
			Return(model.Zone{}, fmt.Errorf("can not create connection to database"))

		_, err := tr.GetByID(ctx, zoneId)
		require.Error(t, err, "get zone repo by id error: can not create connection to database")
	})
}

func TestGetByZoneCode(t *testing.T) {
	t.Parallel()

	zoneId := "63fcd66481b11f2f49c9bb6b"
	zoneName := "on nut"
	zoneCode := "BKK-ONNUT"
	displayName := "on nut"
	active := true
	region := "BKK"
	coordinates := zone.Coordinates{[][]geom.Coord{{{98.30891332956276, -7.8652348996542685}}}}
	geometry := model.NewZoneGeometry(coordinates)

	t.Run("should return zoneCode without error", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		hex, _ := primitive.ObjectIDFromHex(zoneId)
		ctx := context.Background()
		deps.zoneRepo.EXPECT().
			FindByZoneCode(ctx, zoneCode, gomock.Any()).
			Return(model.Zone{
				ID:          hex,
				Name:        zoneName,
				DisplayName: displayName,
				ZoneCode:    zoneCode,
				Region:      region,
				Active:      active,
				Geometry:    geometry}, nil)

		response, err := tr.GetByZoneCode(ctx, zoneCode)
		require.NoError(t, err)
		require.Equal(t, zoneId, response.ID.Hex())
		require.Equal(t, zoneName, response.Name)
		require.Equal(t, displayName, response.DisplayName)
		require.Equal(t, zoneCode, response.ZoneCode)
		require.Equal(t, region, response.Region)
		require.Equal(t, active, response.Active)
		require.Equal(t, geometry, response.Geometry)
	})

	t.Run("should return error when zoneCode is not exist", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		ctx := context.Background()
		deps.zoneRepo.EXPECT().
			FindByZoneCode(ctx, zoneCode, gomock.Any()).
			Return(model.Zone{}, service.ErrZoneNotFound)

		_, err := tr.GetByZoneCode(ctx, zoneCode)
		require.Error(t, err, service.ErrZoneNotFound)
	})

	t.Run("should return error when face with unexpected error from db", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		ctx := context.Background()
		deps.zoneRepo.EXPECT().
			FindByZoneCode(ctx, zoneCode, gomock.Any()).
			Return(model.Zone{}, fmt.Errorf("can not create connection to database"))

		_, err := tr.GetByZoneCode(ctx, zoneCode)
		require.Error(t, err, "get zone repo by zoneCode error: can not create connection to database")
	})
}

func TestListZone(t *testing.T) {
	t.Parallel()

	request := service.ListZoneRequest{
		Region: func() *string { region := "BKK"; return &region }(),
		Active: func() *bool { active := true; return &active }(),
	}

	t.Run("should return list of zone without error", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		active := true
		region := "BKK"
		coordinates := zone.Coordinates{[][]geom.Coord{{{98.30891332956276, -7.8652348996542685}}}}
		geometry := model.NewZoneGeometry(coordinates)
		zoneId1, _ := primitive.ObjectIDFromHex("63fcd66481b11f2f49c9bb6b")
		zoneId2, _ := primitive.ObjectIDFromHex("63fcd4bf81b11f2f49c9bb6a")
		zoneId3, _ := primitive.ObjectIDFromHex("63fcd35a81b11f2f49c9bb68")
		ctx := context.Background()
		query := model.ZoneQueryReq{
			Region: request.Region,
			Active: request.Active,
		}

		queryResult := []model.Zone{{
			ID:          zoneId1,
			Name:        "on nut",
			DisplayName: "on nut",
			ZoneCode:    "BKK-ONNUT",
			Region:      region,
			Active:      active,
			Geometry:    geometry,
		}, {
			ID:          zoneId2,
			Name:        "bang mod",
			DisplayName: "Bang mod",
			ZoneCode:    "BKK-BANGMOD",
			Region:      region,
			Active:      active,
			Geometry:    geometry,
		}, {
			ID:          zoneId3,
			Name:        "bang luk",
			DisplayName: "bang luk",
			ZoneCode:    "BKK-BANGLUK",
			Region:      region,
			Active:      active,
			Geometry:    geometry,
		}}

		deps.zoneRepo.EXPECT().
			Find(ctx, query, request.Skip, request.Limit, []string{"-created_at"}, gomock.Any()).
			Return(queryResult, int64(3), nil)

		response, err := tr.List(ctx, request)
		require.NoError(t, err)
		require.Equal(t, len(response.Zones), 3)
		require.Equal(t, 3, response.TotalCount)

	})

	t.Run("should return list of zone when sort is not empty", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		req := request
		req.Sort = []string{
			"zone_code",
		}
		active := true
		region := "BKK"
		coordinates := zone.Coordinates{[][]geom.Coord{{{98.30891332956276, -7.8652348996542685}}}}
		geometry := model.NewZoneGeometry(coordinates)
		zoneId1, _ := primitive.ObjectIDFromHex("63fcd66481b11f2f49c9bb6b")
		ctx := context.Background()
		query := model.ZoneQueryReq{
			Region: request.Region,
			Active: request.Active,
		}

		queryResult := []model.Zone{{
			ID:          zoneId1,
			Name:        "on nut",
			DisplayName: "on nut",
			ZoneCode:    "BKK-ONNUT",
			Region:      region,
			Active:      active,
			Geometry:    geometry,
		}}

		deps.zoneRepo.EXPECT().
			Find(ctx, query, req.Skip, req.Limit, req.Sort, gomock.Any()).
			Return(queryResult, int64(1), nil)

		response, err := tr.List(ctx, req)
		require.NoError(t, err)
		require.Equal(t, len(response.Zones), 1)
		require.Equal(t, 1, response.TotalCount)

	})

	t.Run("should return error when face with unexpected error from db", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		ctx := context.Background()
		deps.zoneRepo.EXPECT().
			Find(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, int64(0), fmt.Errorf("can not create connection to database"))

		_, err := tr.List(ctx, request)
		require.Error(t, err, "find zone error: can not create connection to database")

	})
}

func TestFindBriefZones(t *testing.T) {
	t.Parallel()

	t.Run("should pass region and active for query", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		ctx := context.WithValue(context.Background(), "this is", "my context")

		deps.zoneRepo.EXPECT().FindBriefZones(ctx, model.BriefZoneQueryReq{
			Region: fp.ToPointer("BKK"),
			Active: fp.ToPointer(true),
		}).Return(nil, nil)

		_, _ = tr.FindBriefZones(ctx, service.FindBriefZoneRequest{
			Region: fp.ToPointer("BKK"),
			Active: fp.ToPointer(true),
		})
	})

	t.Run("should response with brief zones", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		briefZones := []model.BriefZone{
			{ID: primitive.ObjectID{1}, DisplayName: "A", ZoneCode: "AAA"},
			{ID: primitive.ObjectID{2}, DisplayName: "B", ZoneCode: "BBB"},
		}

		deps.zoneRepo.EXPECT().FindBriefZones(gomock.Any(), gomock.Any()).
			Return(briefZones, nil)

		resp, _ := tr.FindBriefZones(context.Background(), service.FindBriefZoneRequest{})

		assert.Equal(t, briefZones, resp.Zones)
		assert.Equal(t, 2, resp.TotalCount)
	})

	t.Run("should sort brief zones by zone code", func(t *testing.T) {
		tr, deps, finish := newTestZoneService(t)
		defer finish()

		deps.zoneRepo.EXPECT().FindBriefZones(gomock.Any(), gomock.Any()).
			Return([]model.BriefZone{
				{ID: primitive.ObjectID{2}, DisplayName: "B", ZoneCode: "BBB"},
				{ID: primitive.ObjectID{1}, DisplayName: "A", ZoneCode: "AAA"},
			}, nil)

		resp, _ := tr.FindBriefZones(context.Background(), service.FindBriefZoneRequest{})

		assert.Equal(t, []model.BriefZone{
			{ID: primitive.ObjectID{1}, DisplayName: "A", ZoneCode: "AAA"},
			{ID: primitive.ObjectID{2}, DisplayName: "B", ZoneCode: "BBB"},
		}, resp.Zones)
	})
}

type zoneServiceDeps struct {
	zoneRepo *mock_repository.MockZoneRepository
}

func newTestZoneService(t *testing.T) (*service.ZoneServiceImpl, *zoneServiceDeps, func()) {
	ctl := gomock.NewController(t)

	zoneRepo := mock_repository.NewMockZoneRepository(ctl)

	deps := &zoneServiceDeps{
		zoneRepo: zoneRepo,
	}

	tr := service.ProvideZoneServiceImpl(zoneRepo)

	return tr, deps, func() { ctl.Finish() }

}
