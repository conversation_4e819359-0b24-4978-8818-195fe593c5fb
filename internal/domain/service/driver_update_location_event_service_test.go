package service_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"google.golang.org/protobuf/proto"

	mock_kafka "git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/mock_kafka"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
)

func TestDriverUpdateLocationEventServiceImpl_PublishDriverUpdateLocation_DriverAvailableCap(t *testing.T) {

	t.Run("publish single event", func(t *testing.T) {
		underTest, deps := newDriverUpdateLocationEventServiceDeps(t)

		ctx := context.Background()
		driverID := "LMD1"
		expectAvailableCap := []model.DriverUpdateLocationAvailableCapacity{{
			ServiceType: string(model.ServiceFood),
			Capacity:    2,
		}, {
			ServiceType: string(model.ServiceMart),
			Capacity:    1,
		}}
		req := model.DriverUpdateLocationEvent{
			AvailableCapacity: expectAvailableCap,
		}

		deps.imfKafkaProducer.EXPECT().SendMessage(ctx, deps.cfg.UpdateLocationTopic, driverID, gomock.Any(), gomock.Any()).
			Times(1).
			Do(func(_ context.Context, _ string, _ string, p []byte, headers map[string]string) {
				var result driverv1.UpdateLocationEvent
				err := proto.Unmarshal(p, &result)

				assert.NoError(t, err)
				assert.Len(t, result.AvailableCapacity, len(expectAvailableCap))
				for i, expected := range expectAvailableCap {
					assert.Equal(t, expected.ServiceType, result.AvailableCapacity[i].ServiceType)
					assert.Equal(t, expected.Capacity, result.AvailableCapacity[i].Capacity)
				}
			})

		err := underTest.PublishDriverUpdateLocation(ctx, driverID, req)

		assert.NoError(t, err)
	})

	t.Run("publish multiple events", func(t *testing.T) {
		underTest, deps := newDriverUpdateLocationEventServiceDeps(t)

		ctx := context.Background()
		driverID := "LMD1"
		expectAvailableCap := []model.DriverUpdateLocationAvailableCapacity{{
			ServiceType: string(model.ServiceFood),
			Capacity:    2,
		}, {
			ServiceType: string(model.ServiceMart),
			Capacity:    1,
		}}
		req := model.DriverUpdateLocationEvent{
			SavedLocations:    []model.DriverUpdateLocationSavedLocation{{Lat: 1, Lng: 2}, {Lat: 3, Lng: 4}},
			AvailableCapacity: expectAvailableCap,
		}

		deps.imfKafkaProducer.EXPECT().SendMessage(ctx, deps.cfg.UpdateLocationTopic, driverID, gomock.Any(), gomock.Any()).
			Times(3).
			Do(func(_ context.Context, _ string, _ string, p []byte, headers map[string]string) {
				var result driverv1.UpdateLocationEvent
				err := proto.Unmarshal(p, &result)

				assert.NoError(t, err)
				assert.Len(t, result.AvailableCapacity, len(expectAvailableCap))
				for i, expected := range expectAvailableCap {
					assert.Equal(t, expected.ServiceType, result.AvailableCapacity[i].ServiceType)
					assert.Equal(t, expected.Capacity, result.AvailableCapacity[i].Capacity)
				}
			})

		err := underTest.PublishDriverUpdateLocation(ctx, driverID, req)

		assert.NoError(t, err)
	})
}

type driverUpdateLocationEventServiceDeps struct {
	cfg              service.DriverUpdateLocationEventServiceConfig
	imfKafkaProducer *mock_kafka.MockIMFKafkaProducer
}

func newDriverUpdateLocationEventServiceDeps(t *testing.T) (service.DriverUpdateLocationEventService, *driverUpdateLocationEventServiceDeps) {
	ctrl := gomock.NewController(t)
	deps := &driverUpdateLocationEventServiceDeps{
		cfg: service.DriverUpdateLocationEventServiceConfig{
			EnablePublishUpdateLocation: true,
			UpdateLocationTopic:         "test",
		},
		imfKafkaProducer: mock_kafka.NewMockIMFKafkaProducer(ctrl),
	}
	svc := service.ProvideDriverLocationEventServiceImpl(deps.cfg, deps.imfKafkaProducer)
	return svc, deps
}
