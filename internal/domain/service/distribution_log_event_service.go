//go:generate mockgen -source=./distribution_log_event_service.go -destination=./mock_service/distribution_log_event_service.go -package=mock_service

package service

import (
	"context"

	"github.com/kelseyhightower/envconfig"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
)

var _ DistributionLogEventService = (*DistributionLogEventServiceImpl)(nil)

type DistributionLogEventService interface {
	PublishSearchEvent(ctx context.Context, req model.DistributionLogSearchEvent) error
	PublishOptimizeEvent(ctx context.Context, req model.DistributionLogOptimizeEvent) error
	PublishFilterEvent(ctx context.Context, req model.DistributionLogFilterEvent) error
}

type DistributionLogEventServiceImpl struct {
	cfg                    DistributionLogEventServiceConfig
	secureIMFKafkaProducer kafcclient.SecureIMFKafkaProducer
}

func (svc DistributionLogEventServiceImpl) PublishSearchEvent(ctx context.Context, req model.DistributionLogSearchEvent) error {
	event, err := mapSearchEvent(req)
	if err != nil {
		return err
	}

	headers := map[string]string{}
	message, err := proto.Marshal(event)
	if err != nil {
		return err
	}

	err = svc.secureIMFKafkaProducer.SendMessage(ctx, svc.cfg.SearchTopic, req.DistributionID, message, headers)
	if err != nil {
		return err
	}

	return nil
}

func (svc DistributionLogEventServiceImpl) PublishOptimizeEvent(ctx context.Context, req model.DistributionLogOptimizeEvent) error {
	event, err := mapOptimizeEvent(req)
	if err != nil {
		return err
	}

	headers := map[string]string{}
	message, err := proto.Marshal(event)
	if err != nil {
		return err
	}

	err = svc.secureIMFKafkaProducer.SendMessage(ctx, svc.cfg.OptimizeTopic, req.DistributionID, message, headers)
	if err != nil {
		return err
	}

	return nil
}

func (svc DistributionLogEventServiceImpl) PublishFilterEvent(ctx context.Context, req model.DistributionLogFilterEvent) error {
	event, err := mapFilterEvent(req)
	if err != nil {
		return err
	}

	headers := map[string]string{}
	message, err := proto.Marshal(event)
	if err != nil {
		return err
	}

	err = svc.secureIMFKafkaProducer.SendMessage(ctx, svc.cfg.FilterTopic, req.DistributionID, message, headers)
	if err != nil {
		return err
	}

	return nil
}

func ProvideDistributionLogEventService(cfg DistributionLogEventServiceConfig, p kafcclient.SecureIMFKafkaProducer) DistributionLogEventService {
	return &DistributionLogEventServiceImpl{
		cfg:                    cfg,
		secureIMFKafkaProducer: p,
	}
}

type DistributionLogEventServiceConfig struct {
	EnablePublishSearchEvent   bool   `envconfig:"ENABLE_PUBLISH_DISTRIBUTION_LOG_SEARCH_EVENT"  default:"false"`
	EnablePublishOptimizeEvent bool   `envconfig:"ENABLE_PUBLISH_DISTRIBUTION_LOG_OPTIMIZE_EVENT"  default:"false"`
	EnablePublishFilterEvent   bool   `envconfig:"ENABLE_PUBLISH_DISTRIBUTION_LOG_FILTER_EVENT"  default:"false"`
	SearchTopic                string `envconfig:"DISTRIBUTION_LOG_SEARCH_TOPIC"`
	OptimizeTopic              string `envconfig:"DISTRIBUTION_LOG_OPTIMIZE_TOPIC"`
	FilterTopic                string `envconfig:"DISTRIBUTION_LOG_FILTER_TOPIC"`
}

func ProvideDistributionLogEventServiceConfig() (cfg DistributionLogEventServiceConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

func mapSearchEvent(searchEvent model.DistributionLogSearchEvent) (*driverv1.SearchEvent, error) {
	return &driverv1.SearchEvent{
		DistributionId: searchEvent.DistributionID,
		Type:           string(searchEvent.Type),
		Region:         searchEvent.Region,
		Zone:           searchEvent.Zone,
		Drivers:        searchEvent.Drivers,
		Orders:         searchEvent.Orders,
		CapturedAt:     timestamppb.New(searchEvent.CapturedAt),
		StartedAt:      timestamppb.New(searchEvent.StartedAt),
	}, nil
}

func mapOptimizeEvent(optimizeEvent model.DistributionLogOptimizeEvent) (*driverv1.OptimizeEvent, error) {
	return &driverv1.OptimizeEvent{
		DistributionId:       optimizeEvent.DistributionID,
		Type:                 string(optimizeEvent.Type),
		Region:               optimizeEvent.Region,
		Zone:                 optimizeEvent.Zone,
		OptimizationRound:    string(optimizeEvent.OptimizationRound),
		Drivers:              optimizeEvent.Drivers,
		Orders:               optimizeEvent.Orders,
		OptimizationResponse: optimizeEvent.OptimizationResponse,
		CapturedAt:           timestamppb.New(optimizeEvent.CapturedAt),
	}, nil
}

func mapFilterEvent(filterEvent model.DistributionLogFilterEvent) (*driverv1.FilterEvent, error) {
	return &driverv1.FilterEvent{
		DistributionId:    filterEvent.DistributionID,
		Type:              string(filterEvent.Type),
		Region:            filterEvent.Region,
		Zone:              filterEvent.Zone,
		OptimizationRound: (*string)(filterEvent.OptimizationRound),
		TargetDrivers:     filterEvent.TargetDrivers,
		TargetOrders:      filterEvent.TargetOrders,
		Step:              string(filterEvent.Step),
		Filter:            string(filterEvent.Filter),
		CapturedAt:        timestamppb.New(filterEvent.CapturedAt),
	}, nil
}
