package service_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
)

func TestServiceAreaServiceImpl_GetByID(t *testing.T) {
	execute := func(srv service.ServiceAreaService) (*model.ServiceArea, error) {
		return srv.GetByID(context.Background(), "")
	}
	executeWithContext := func(srv service.ServiceAreaService, ctx context.Context) (*model.ServiceArea, error) {
		return srv.GetByID(ctx, "")
	}
	executeWithID := func(srv service.ServiceAreaService, id string) (*model.ServiceArea, error) {
		return srv.GetByID(context.Background(), id)
	}

	getFromRepoDummy := func(ctx context.Context, id_ string) (*model.ServiceArea, error) {
		return &model.ServiceArea{}, nil
	}
	mockGetFromRepo := func(deps *serviceAreaServiceTestDeps, f func(ctx context.Context, id string) (*model.ServiceArea, error)) {
		deps.serviceAreaRepo.EXPECT().Get(gomock.Any(), gomock.Any()).DoAndReturn(f)
	}
	mockGetFromRepoDummy := func(deps *serviceAreaServiceTestDeps) {
		mockGetFromRepo(deps, getFromRepoDummy)
	}
	mockGetFromRepoCaptureID := func(deps *serviceAreaServiceTestDeps, id *string) {
		mockGetFromRepo(deps, func(ctx_ context.Context, id_ string) (*model.ServiceArea, error) {
			*id = id_
			return getFromRepoDummy(ctx_, id_)
		})
	}
	mockGetFromRepoCaptureContext := func(deps *serviceAreaServiceTestDeps, ctx *context.Context) {
		mockGetFromRepo(deps, func(ctx_ context.Context, id_ string) (*model.ServiceArea, error) {
			*ctx = ctx_
			return getFromRepoDummy(ctx_, id_)
		})
	}
	mockGetFromRepoWithResult := func(deps *serviceAreaServiceTestDeps, result *model.ServiceArea) {
		mockGetFromRepo(deps, func(ctx_ context.Context, id_ string) (*model.ServiceArea, error) {
			return result, nil
		})
	}
	mockGetFromRepoWithError := func(deps *serviceAreaServiceTestDeps, err error) {
		mockGetFromRepo(deps, func(ctx_ context.Context, id_ string) (*model.ServiceArea, error) {
			return nil, err
		})
	}

	overridePreferenceDummy := func(ctx context.Context, pref model.ServicePreference) (model.ServicePreference, error) {
		return pref, nil
	}
	mockOverridePreference := func(deps *serviceAreaServiceTestDeps, f func(ctx context.Context, pref model.ServicePreference) (model.ServicePreference, error)) {
		deps.servicePreferenceService.EXPECT().OverridePreferenceForAdmin(gomock.Any(), gomock.Any()).DoAndReturn(f)
	}
	mockOverridePreferenceDummy := func(deps *serviceAreaServiceTestDeps) {
		mockOverridePreference(deps, overridePreferenceDummy)
	}
	mockOverridePreferenceCapturePref := func(deps *serviceAreaServiceTestDeps, pref *model.ServicePreference) {
		mockOverridePreference(deps, func(ctx_ context.Context, pref_ model.ServicePreference) (model.ServicePreference, error) {
			*pref = pref_
			return overridePreferenceDummy(ctx_, pref_)
		})
	}
	mockOverridePreferenceCaptureContext := func(deps *serviceAreaServiceTestDeps, ctx *context.Context) {
		mockOverridePreference(deps, func(ctx_ context.Context, pref_ model.ServicePreference) (model.ServicePreference, error) {
			*ctx = ctx_
			return overridePreferenceDummy(ctx_, pref_)
		})
	}
	mockOverridePreferenceWithResult := func(deps *serviceAreaServiceTestDeps, pref model.ServicePreference) {
		mockOverridePreference(deps, func(ctx_ context.Context, pref_ model.ServicePreference) (model.ServicePreference, error) {
			return pref, nil
		})
	}
	mockOverridePreferenceWithError := func(deps *serviceAreaServiceTestDeps, err error) {
		mockOverridePreference(deps, func(ctx_ context.Context, pref_ model.ServicePreference) (model.ServicePreference, error) {
			return model.ServicePreference{}, err
		})
	}

	t.Run("should get service area from repo with id", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		var capturedID string
		mockGetFromRepoCaptureID(deps, &capturedID)
		mockOverridePreferenceDummy(deps)

		_, _ = executeWithID(srv, "abc")

		require.Equal(t, "abc", capturedID)
	})

	t.Run("should get service area from repo with context", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		var capturedCtx context.Context
		mockGetFromRepoCaptureContext(deps, &capturedCtx)
		mockOverridePreferenceDummy(deps)

		ctx := context.WithValue(context.Background(), "hey", "yo")
		_, _ = executeWithContext(srv, ctx)

		require.Equal(t, ctx, capturedCtx)
	})

	t.Run("should return service area", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		mockGetFromRepoWithResult(deps, &model.ServiceArea{ID: "this one na ja"})
		mockOverridePreferenceDummy(deps)

		result, _ := execute(srv)

		require.Equal(t, &model.ServiceArea{ID: "this one na ja"}, result)
	})

	t.Run("should return unknown error", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		mockGetFromRepoWithError(deps, errors.New("boom"))

		_, err := execute(srv)

		require.Equal(t, "cannot get service area: boom", err.Error())
	})

	t.Run("should return service area not found error if repo returns not found error", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		mockGetFromRepoWithError(deps, repository.ErrNotFound)

		_, err := execute(srv)

		require.Equal(t, service.ErrServiceAreaNotFound, err)
	})

	t.Run("should override service preference with existing service preference", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		existingPreference := model.ServicePreference{
			IsEnabled:                true,
			AllowedTypesForSelection: []model.Service{model.ServiceBike, model.ServiceFood},
		}
		mockGetFromRepoWithResult(deps, &model.ServiceArea{Distribution: model.AutoAssignDistribution{ServicePreference: existingPreference}})

		var pref model.ServicePreference
		mockOverridePreferenceCapturePref(deps, &pref)

		_, _ = execute(srv)

		require.Equal(t, existingPreference, pref)
	})

	t.Run("should override service preference with context", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		mockGetFromRepoDummy(deps)

		var capturedCtx context.Context
		mockOverridePreferenceCaptureContext(deps, &capturedCtx)

		ctx := context.WithValue(context.Background(), "bond", "james bond")
		_, _ = executeWithContext(srv, ctx)

		require.Equal(t, ctx, capturedCtx)
	})

	t.Run("should return service area with overridden service preference", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		overriddenPref := model.ServicePreference{
			IsEnabled:                true,
			AllowedTypesForSelection: []model.Service{model.ServiceBike, model.ServiceFood},
		}
		mockGetFromRepoDummy(deps)
		mockOverridePreferenceWithResult(deps, overriddenPref)

		result, _ := execute(srv)

		require.Equal(t, overriddenPref, result.Distribution.ServicePreference)
	})

	t.Run("should return unknown error from override", func(t *testing.T) {
		srv, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		mockGetFromRepoDummy(deps)
		mockOverridePreferenceWithError(deps, errors.New("unknown"))

		_, err := execute(srv)

		require.Equal(t, "cannot override service preference: unknown", err.Error())
	})
}

func newTestServiceAreaService(t gomock.TestReporter) (service.ServiceAreaService, *serviceAreaServiceTestDeps, func()) {
	ctrl := gomock.NewController(t)

	serviceAreaRepository := mock_repository.NewMockServiceAreaRepository(ctrl)

	atomicPredictionServiceCfg := config.NewAtomicPredictionServiceConfig(
		config.PredictionServiceConfig{
			BackToBackAllowStatus: []string{string(model.StatusDriverMatched)},
		},
	)

	globalServiceAreaCfg := &config.AtomicGlobalServiceAreaConfig{}
	globalServiceAreaCfg.Parse()

	servicePreferenceService := mock_service.NewMockServicePreferenceService(ctrl)

	sv := service.ProvideServiceAreaServiceImpl(
		globalServiceAreaCfg,
		atomicPredictionServiceCfg,
		serviceAreaRepository,
		servicePreferenceService,
	)
	deps := &serviceAreaServiceTestDeps{
		atomicPredictionServiceCfg: atomicPredictionServiceCfg,
		serviceAreaRepo:            serviceAreaRepository,
		servicePreferenceService:   servicePreferenceService,
	}

	return sv, deps, func() {
		ctrl.Finish()
	}
}

type serviceAreaServiceTestDeps struct {
	atomicPredictionServiceCfg *config.AtomicPredictionServiceConfig
	serviceAreaRepo            *mock_repository.MockServiceAreaRepository
	servicePreferenceService   *mock_service.MockServicePreferenceService
}
