package service

import (
	"context"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/sets"
)

//go:generate mockgen -source=./service_preference_kill_switch_service.go -destination=./mock_service/mock_service_preference_kill_switch_service.go -package=mock_service

//goland:noinspection GoNameStartsWithPackageName
type ServicePreferenceKillSwitchService interface {
	ReadStatus(ctx context.Context) (ServicePreferenceKillSwitchStatus, error)
}

//goland:noinspection GoNameStartsWithPackageName
type ServicePreferenceKillSwitchServiceImpl struct {
	featureFlagService featureflag.Service
}

func (s ServicePreferenceKillSwitchServiceImpl) ReadStatus(ctx context.Context) (ServicePreferenceKillSwitchStatus, error) {
	ret := ServicePreferenceKillSwitchStatus{AllowedTypes: []model.Service{}}
	variant := s.featureFlagService.GetVariant(featureflag.ServicePreferenceAllowedTypesForSelection.Name)
	if !variant.Enabled {
		return ret, nil
	}

	// allow service type from unleash
	allowedTypes, err := model.NewServicesFromJSONString(variant.Payload.Value)
	if err != nil {
		logx.Error().Err(err).Context(ctx).
			Str("method", "ServicePreferenceKillSwitchServiceImpl.ReadStatus").
			Msgf("cannot parse services from feature flag payload [%#v]", variant.Payload.Value)
		allowedTypes = []model.Service{}
	}
	ret.IsFeatureEnabled = true
	ret.AllowedTypes = allowedTypes

	allowedTypesSet := sets.New(ret.AllowedTypes...)
	mergeFoodMartEnabled := s.featureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.ServicePreferenceMergeFoodMart.Name)
	if mergeFoodMartEnabled {
		ret.IsMergeFoodMartEnabled = mergeFoodMartEnabled
		if allowedTypesSet.HasAny(model.ServiceFood, model.ServiceMart) {
			if !allowedTypesSet.Has(model.ServiceFood) {
				ret.AllowedTypes = append(ret.AllowedTypes, model.ServiceFood)
			}
			if !allowedTypesSet.Has(model.ServiceMart) {
				ret.AllowedTypes = append(ret.AllowedTypes, model.ServiceMart)
			}
		}
	}
	return ret, nil
}

func ProvideServicePreferenceKillSwitchService(featureFlagService featureflag.Service) ServicePreferenceKillSwitchService {
	return ServicePreferenceKillSwitchServiceImpl{
		featureFlagService: featureFlagService,
	}
}

//goland:noinspection GoNameStartsWithPackageName
type ServicePreferenceKillSwitchStatus struct {
	IsFeatureEnabled       bool
	AllowedTypes           []model.Service
	IsMergeFoodMartEnabled bool
}
