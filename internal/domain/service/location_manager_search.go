package service

import (
	"context"
	"fmt"
	"time"

	"github.com/Unleash/unleash-client-go/v3"
	"github.com/pkg/errors"
	"github.com/twpayne/go-geom"

	"git.wndv.co/go/logx/v2"
	poolv1 "git.wndv.co/go/proto/lineman/fleet/pool/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/fleetpool/adapter"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
)

const (
	searchRidersInRadius       = "search_riders_in_radius"
	searchRidersInMultiPolygon = "search_riders_in_multi_polygon"
)

func (locmngr *LocationManagerImpl) SearchDriversLimit(ctx context.Context, loc model.Location, toM float64, limit int, srv model.Service, region model.RegionCode, orderID string) (SearchResult, error) {
	metrics := newSearchMetrics(time.Now())
	defer metrics.observeSearchDriversLimit(locmngr)

	flagOption := featureflag.NewFeatureFlagContext().WithRegion(region.String()).WithUnleashUserID(orderID).FeatureOption()
	strategies := locmngr.getSearchStrategies(ctx, flagOption)
	if len(strategies) == 0 {
		return SearchResult{}, errors.New("empty search strategies")
	}

	primaryStrategy := strategies[0]
	metrics.setMetricOperationAndRegion(primaryStrategy.name(), region.String())

	var err error
	var result SearchResult
	for i, strategy := range strategies {
		result, err = strategy.searchInRadius(ctx, loc, toM, limit, srv)
		if err == nil {
			metrics.setResultCount(len(result.Results))
			metrics.observeSearchDriversLimitStrategyStatus(locmngr, strategy.name(), "success")
			return result, nil
		}
		metrics.observeSearchDriversLimitStrategyStatus(locmngr, strategy.name(), "fail")

		isFallingBack := (i < len(strategies)-1)
		if isFallingBack {
			logx.Warn().
				Context(ctx).
				Err(err).
				Str(logutil.Method, primaryStrategy.name().String()).
				Str(logutil.Region, region.String()).
				Str(logutil.Service, srv.String()).
				Msgf("failed to searchInRadius drivers, falling back, attempt: %d, strategy: %s", i, strategy.name())
		} else {
			logx.Error().
				Context(ctx).
				Err(err).
				Str(logutil.Method, primaryStrategy.name().String()).
				Str(logutil.Region, region.String()).
				Str(logutil.Service, srv.String()).
				Msgf("failed to searchInRadius drivers, attempt: %d, strategy: %s", i, strategy.name())
		}
	}

	return SearchResult{}, err
}

// SearchDriversInMultiPolygon finds drivers within the given multi-polygon boundaries
func (locmngr *LocationManagerImpl) SearchDriversInMultiPolygon(ctx context.Context, batchZoneID string, coordinates [][][]geom.Coord, toM float64) (SearchResult, error) {
	metrics := newSearchMetrics(time.Now())
	defer metrics.observeSearchDriversInMultiPolygon(locmngr)

	offsetCoords, _ := locmngr.getOffsetCoordinates(ctx, batchZoneID, toM, coordinates)

	// Determine searchInMultiPolygon strategy based on unleash feature flag
	flagOption := featureflag.NewFeatureFlagContext().WithBatchZone(batchZoneID).FeatureOption()
	strategies := locmngr.getSearchStrategies(ctx, flagOption)
	if len(strategies) == 0 {
		return SearchResult{}, errors.New("empty search strategies")
	}
	primaryStrategy := strategies[0]
	metrics.setMetricStrategy(primaryStrategy.name())

	var err error
	var result SearchResult
	for i, strategy := range strategies {
		result, err = strategy.searchInMultiPolygon(ctx, offsetCoords)
		if err == nil {
			metrics.setResultCount(len(result.Results))
			metrics.observeSearchDriversInMultiPolygonStatus(locmngr, strategy.name(), "success")
			return result, nil
		}
		metrics.observeSearchDriversInMultiPolygonStatus(locmngr, strategy.name(), "fail")

		isFallingBack := (i < len(strategies)-1)
		if isFallingBack {
			logx.Warn().
				Context(ctx).
				Err(err).
				Str(logutil.Method, primaryStrategy.name().String()).
				Msgf("failed to searchInMultiPolygon drivers, falling back, attempt: %d, strategy: %s, batch_zone_id: %s", i, strategy.name(), batchZoneID)
		} else {
			logx.Error().
				Context(ctx).
				Err(err).
				Str(logutil.Method, primaryStrategy.name().String()).
				Msgf("failed to searchInMultiPolygon drivers, attempt: %d, strategy: %s, batch_zone_id: %s", i, strategy.name(), batchZoneID)
		}

	}

	return SearchResult{}, err
}

func (locmngr *LocationManagerImpl) getOffsetCoordinates(ctx context.Context, batchZoneID string, toM float64, coordinates [][][]geom.Coord) ([][][]geom.Coord, error) {
	margin := (toM / 1000.0) / 100.0 // 0.01 lat/lng ≈ 1 km in Thailand
	offset, err := locmngr.GetPreCalculationPolygonWithOffset(ctx, batchZoneID, margin, coordinates)
	if err != nil {
		safe.SentryErrorMessage(fmt.Sprintf("cannot offset polygon. error: %v", err))
		return coordinates, nil // fallback to original coordinates
	}
	return offset, nil
}

// getSearchStrategies returns a list of search strategies based on the provided feature options,
// arranged in a fallback order.
func (locmngr *LocationManagerImpl) getSearchStrategies(ctx context.Context, option unleash.FeatureOption) []searchStrategy {
	if locmngr.featureFlag.IsEnabledWithDefaultFalse(ctx, featureflag.IsEnabledFleetPoolService.Name, option) {
		return []searchStrategy{&fleetPoolStrategy{manager: locmngr}, &directSearchStrategy{manager: locmngr}}
	}
	return []searchStrategy{&directSearchStrategy{manager: locmngr}}
}

type searchMetrics struct {
	startTime time.Time
	region    string
	status    string
	count     int
	strategy  string
}

func newSearchMetrics(startTime time.Time) *searchMetrics {
	return &searchMetrics{
		startTime: startTime,
		status:    "fail",
	}
}

func (m *searchMetrics) setMetricOperationAndRegion(strategy model.SearchRiderStrategy, region string) {
	m.region = region
	m.strategy = strategy.String()
}

func (m *searchMetrics) setMetricStrategy(strategy model.SearchRiderStrategy) {
	m.strategy = strategy.String()
}

func (m *searchMetrics) observeSearchDriversInMultiPolygon(locmngr *LocationManagerImpl) {
	if m.strategy == "" {
		return
	}
	ctx := context.Background()
	latencyMs := float64(time.Since(m.startTime).Milliseconds())
	locmngr.metricsRegistry.ObserveSearchRidersInMultiPolygonLatencyMetric(ctx, latencyMs, m.strategy)
	locmngr.metricsRegistry.IncrSearchDriverInMultiPolygonCounter(ctx, m.strategy, m.status)
	if m.count < 1 {
		locmngr.metricsRegistry.IncrSearchNoDriverCountInMultiPolygon(ctx, m.strategy)
	}
}

func (m *searchMetrics) observeSearchDriversLimit(locmngr *LocationManagerImpl) {
	if m.strategy == "" || m.region == "" {
		return
	}
	ctx := context.Background()
	latencyMs := float64(time.Since(m.startTime).Milliseconds())
	locmngr.metricsRegistry.ObserveSearchRidersInRadiusLatencyMetric(ctx, latencyMs, m.strategy, m.region)
	locmngr.metricsRegistry.IncrSearchDriverInRadiusCounter(ctx, m.strategy, m.region, m.status)
	if m.count < 1 {
		locmngr.metricsRegistry.IncrSearchNoDriverCountInInRadius(ctx, m.strategy, m.region)
	}
}

func (m *searchMetrics) observeSearchDriversLimitStrategyStatus(locmngr *LocationManagerImpl, strategy model.SearchRiderStrategy, status string) {
	locmngr.metricsRegistry.IncrSearchDriverInRadiusCounter(context.Background(), strategy.String(), m.region, status)
}

func (m *searchMetrics) observeSearchDriversInMultiPolygonStatus(locmngr *LocationManagerImpl, strategy model.SearchRiderStrategy, status string) {
	locmngr.metricsRegistry.IncrSearchDriverInMultiPolygonCounter(context.Background(), strategy.String(), status)
}

func (m *searchMetrics) setResultCount(count int) {
	m.count = count
	m.status = "success"
}

type SearchResult struct {
	Results  []DriverWithLocation
	Strategy model.SearchRiderStrategy
}

// searchStrategy interface defines the contract for different search implementations
type searchStrategy interface {
	searchInMultiPolygon(ctx context.Context, coordinates [][][]geom.Coord) (SearchResult, error)
	searchInRadius(ctx context.Context, loc model.Location, toM float64, limit int, service model.Service) (SearchResult, error)
	name() model.SearchRiderStrategy
}

// fleetPoolStrategy implements searchStrategy using fleet pool service
type fleetPoolStrategy struct {
	manager *LocationManagerImpl
}

func (s *fleetPoolStrategy) searchInMultiPolygon(ctx context.Context, coordinates [][][]geom.Coord) (SearchResult, error) {
	driverWithLocation, err := s.fleetPoolGetRidersInMultiPolygon(ctx, coordinates)
	if err != nil {
		return SearchResult{}, err
	}
	return SearchResult{
		Results:  driverWithLocation,
		Strategy: s.name(),
	}, nil
}

func (s *fleetPoolStrategy) searchInRadius(ctx context.Context, loc model.Location, toM float64, limit int, service model.Service) (SearchResult, error) {
	query := repository.DriverLocationQuery{
		Location: model.Location{Lat: loc.Lat, Lng: loc.Lng},
		To:       toM,
		Limit:    limit,
		Service:  service,
	}

	result, err := s.manager.fleetPoolRiderSearch.GetRidersInRadius(ctx, adapter.ToGetRidersInRadiusRequest(ctx, query))
	if err != nil {
		logx.Error().Context(ctx).Err(err).Msg("failed to call fleet pool service")
		return SearchResult{}, err
	}
	res := s.toDriverWithLocationList(ctx, result)
	return SearchResult{
		Results:  res,
		Strategy: s.name(),
	}, nil
}

func (s *fleetPoolStrategy) toDriverWithLocationList(ctx context.Context, result *poolv1.GetRidersInRadiusResponse) []DriverWithLocation {
	dvs := adapter.FromProtoGetRidersInRadiusResponse(ctx, result).Drivers
	res := make([]DriverWithLocation, 0, len(dvs))
	for _, driver := range dvs {
		res = append(res, DriverWithLocation{
			DistanceMeter: driver.DistanceMeter,
			Driver:        driver.Driver,
			Location:      driver.Location,
		})
	}
	return res
}

func (s *fleetPoolStrategy) name() model.SearchRiderStrategy {
	return model.StrategyNameFleetPool
}

// directSearchStrategy implements searchStrategy using direct search
type directSearchStrategy struct {
	manager *LocationManagerImpl
}

func (s *directSearchStrategy) searchInMultiPolygon(ctx context.Context, coordinates [][][]geom.Coord) (SearchResult, error) {
	query := repository.DriverLocationInMultiPolygonQuery{
		Coordinates: coordinates,
	}

	driverWithLocation, err := s.manager.DirectSearchDriversInMultiPolygon(ctx, query)
	if err != nil {
		return SearchResult{}, err
	}
	return SearchResult{
		Results:  driverWithLocation,
		Strategy: s.name(),
	}, nil
}

func (s *directSearchStrategy) searchInRadius(ctx context.Context, loc model.Location, toM float64, limit int, service model.Service) (SearchResult, error) {
	bikeService := service == model.ServiceBike
	query := repository.DriverLocationQuery{
		IsBikeService: bikeService,
		Location:      model.Location{Lat: loc.Lat, Lng: loc.Lng},
		To:            toM,
		Limit:         limit,
		Service:       service,
	}
	driverWithLocation, err := s.manager.DirectSearchDrivers(ctx, query)
	if err != nil {
		return SearchResult{}, err
	}

	return SearchResult{
		Results:  driverWithLocation,
		Strategy: s.name(),
	}, nil
}

func (s *directSearchStrategy) name() model.SearchRiderStrategy {
	return model.StrategyNameDirectSearch
}

func (s *fleetPoolStrategy) fleetPoolGetRidersInMultiPolygon(ctx context.Context, coordinates [][][]geom.Coord) ([]DriverWithLocation, error) {
	request := adapter.ToGetRidersInMultiPolygonRequest(ctx, coordinates)
	result, err := s.manager.fleetPoolRiderSearch.GetRidersInMultiPolygon(ctx, request)
	if err != nil {
		logx.Error().Context(ctx).Err(err).Msg("failed to call fleet pool service")
		return nil, err
	}

	dvs := adapter.FromProtoGetRidersResponse(ctx, result).Drivers
	res := make([]DriverWithLocation, 0, len(dvs))
	for _, driver := range dvs {
		res = append(res, DriverWithLocation{
			Driver:   driver.Driver,
			Location: driver.Location,
		})
	}
	return res, nil
}
