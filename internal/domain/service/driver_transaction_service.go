package service

//go:generate mockgen -source=./driver_transaction_service.go -destination=./mock_service/mock_driver_transaction_service.go -package=mock_service

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	inventoryPb "git.wndv.co/go/proto/lineman/inventory/v1"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/accountinghub"
	"git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type DriverTransactionServiceV2 interface {
	GetDriverTransaction(ctx context.Context, driverID string, read repository.Option) (model.DriverTransaction, error)
	ProcessDriverTransaction(ctx context.Context, driverID string, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus, builder TransactionInfosBuilder, transactionOptions ...func(*ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error)
	ProcessOverdueInstallment(ctx context.Context, driverID string, amount types.Money) ([]model.Installment, error)
	GetAndUpdatePendingTransactionsByOrder(ctx context.Context, orderID string, updateStatus model.PendingTransactionCollectionStatus) ([]model.PendingTransaction, error)
}

type TransactionInfosBuilder func(dt model.DriverTransaction) ([]model.TransactionInfo, error)

func TransactionInfos(infos ...model.TransactionInfo) TransactionInfosBuilder {
	return func(_ model.DriverTransaction) ([]model.TransactionInfo, error) {
		return infos, nil
	}
}

type ProcessDriverTransactionOption struct {
	transactionOpts             []model.TransactionOptions
	installmentLogs             model.SortingInstallmentLogList
	ignoreEmptyTxnInfoListError bool
	isDryRunMode                bool
	isFullTimeDriver            bool
}

func WithTransactionOptions(opts ...model.TransactionOptions) func(*ProcessDriverTransactionOption) {
	return func(o *ProcessDriverTransactionOption) {
		o.transactionOpts = opts
	}
}

func WithInstallmentLogs(logs ...model.SortingInstallmentLog) func(*ProcessDriverTransactionOption) {
	return func(o *ProcessDriverTransactionOption) {
		o.installmentLogs = logs
	}
}

func WithIgnoreEmptyTxnInfoListError(isEnable bool) func(*ProcessDriverTransactionOption) {
	return func(o *ProcessDriverTransactionOption) {
		o.ignoreEmptyTxnInfoListError = isEnable
	}
}

func WithDryRunMode(isEnable bool) func(*ProcessDriverTransactionOption) {
	return func(o *ProcessDriverTransactionOption) {
		o.isDryRunMode = isEnable
	}
}

func WithFullTimeDriverFlag(isFullTimeDriver bool) func(*ProcessDriverTransactionOption) {
	return func(o *ProcessDriverTransactionOption) {
		o.isFullTimeDriver = isFullTimeDriver
	}
}

var ProcessDriverTransactionNoInfoError = fmt.Errorf("infos is required")

type DriverTransactionServiceImpl struct {
	config                       config.DriverTransactionConfig
	driverTransRepo              repository.DriverTransactionRepository
	transRepo                    repository.TransactionRepository
	installmentRepo              repository.InstallmentRepository
	productGroupRepo             repository.ProductGroupRepository
	productService               inventoryPb.ProductServiceClient
	txnHelper                    transaction.TxnHelper
	incomeAggregateService       aggregate.IncomeAggregateService
	pendingTransactionRepo       repository.PendingTransactionRepository
	accountingHubService         accountinghub.AccountingHubTransactionService
	accountingHubTransactionRepo repository.AccountingHubTransactionRepository
}

func (svc *DriverTransactionServiceImpl) GetDriverTransaction(ctx context.Context, driverID string, read repository.Option) (model.DriverTransaction, error) {
	driverTrans, err := svc.driverTransRepo.FindByID(ctx, driverID, read)
	if err != nil {
		if err == repository.ErrNotFound {
			driverTrans = model.NewDriverTransaction(driverID)
			if driverID != "" {
				err = svc.driverTransRepo.Create(ctx, driverTrans)
				if err != nil {
					return model.DriverTransaction{}, err
				}
				return *driverTrans, nil
			}
			return model.DriverTransaction{}, errors.New("driver ID is empty")
		}

		return model.DriverTransaction{}, err
	}

	return *driverTrans, nil
}

func (svc *DriverTransactionServiceImpl) ProcessDriverTransaction(ctx context.Context, driverID string,
	channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus,
	builder TransactionInfosBuilder, transactionOptions ...func(*ProcessDriverTransactionOption),
) (driverTrans model.DriverTransaction, trans []model.Transaction, err error) {
	type RetVal struct {
		dt        model.DriverTransaction
		t         []model.Transaction
		tTracking []model.Transaction
	}

	opt := ProcessDriverTransactionOption{}
	for _, o := range transactionOptions {
		o(&opt)
	}
	val, err := svc.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		driverTrans, err = svc.GetDriverTransaction(sessCtx, driverID, repository.WithReadPrimary)
		if err != nil {
			return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
		}

		var infos []model.TransactionInfo
		infos, err = builder(driverTrans)
		if err != nil {
			return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
		}

		if len(infos) == 0 {
			if opt.ignoreEmptyTxnInfoListError {
				return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, nil
			}
			return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, ProcessDriverTransactionNoInfoError
		}

		fullTimeDriverTransInfo := make([]model.TransactionInfo, 0)
		if opt.isFullTimeDriver {
			infos, fullTimeDriverTransInfo = partitionDriverTransactionInfos(infos, action)
		}

		infos, err = driverTrans.AddTransaction(infos)
		if err != nil {
			return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
		}

		installmentPayAmount := types.Money(0)
		transactions := make([]model.Transaction, 0, len(infos))
		transactionsTracking := make([]model.Transaction, 0, len(infos))
		processTxnInfoFunc := func(info model.TransactionInfo, target *[]model.Transaction) {
			uuid := utils.GenerateUUID()
			newTran := *model.NewTransaction(uuid,
				channel,
				action,
				status,
				info,
			)
			if len(info.IncentiveNames) != 0 && info.Type == model.IncentiveTransactionType {
				tranSource := model.TransactionSource{}
				tranSource.Name = info.IncentiveNames[0]
				tranSource.Amount = info.Amount

				if len(info.IncentiveSources) != 0 {
					tranSource.Source = info.IncentiveSources[0]
				}
				newTran.Sources = []model.TransactionSource{tranSource}
			}

			if opt.transactionOpts != nil {
				for _, txnOpt := range opt.transactionOpts {
					txnOpt(&newTran)
				}
			}

			switch newTran.Action {
			case model.AutoTransferWalletToCreditTransactionAction:
				switch newTran.Info.Category {
				case model.CreditTransactionCategory:
					newTran.Info.Operator = model.AdditionOperator
					newTran.Info.DisplayText = model.AutoTranferCreditDisplayText
				case model.WalletTransactionCategory:
					newTran.Info.Operator = model.SubtractionOperator
					newTran.Info.DisplayText = model.AutoTranferWalletDisplayText
				}
			}

			*target = append(*target, newTran)
			if newTran.Info.Category == model.CreditTransactionCategory &&
				(newTran.Info.Type == model.PurchaseTransactionType ||
					newTran.Info.Type == model.AddCreditInstallmentCancellationTransactionType) {
				installmentPayAmount += newTran.Info.InstallmentAfter.Sub(newTran.Info.InstallmentAmount)
			}
		}

		for _, info := range infos {
			processTxnInfoFunc(info, &transactions)
		}

		for _, info := range fullTimeDriverTransInfo {
			processTxnInfoFunc(info, &transactionsTracking)
		}

		if installmentPayAmount.GT(0) && action != model.AdjustNegativeBalanceTransactionAction {
			if opt.installmentLogs != nil {
				opt.installmentLogs.Rebalancing(installmentPayAmount, timeutil.BangkokNow())
			} else {
				// NOTE: lmf-5115 if installment logs wasn't provide, just need to find overdue installment sorting list and calling RebalancingV2
				installments, err := svc.ProcessOverdueInstallment(sessCtx, driverTrans.DriverID, installmentPayAmount)
				if err != nil {
					logrus.Errorf("ProcessDriverTransaction - process installment error: driverId=%s action=%s amount=%v time=%v err=%v", driverTrans.DriverID, action, installmentPayAmount, timeutil.BangkokNow(), err)
					return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
				}
				if !opt.isDryRunMode {
					if err := svc.installmentRepo.ReplaceAll(sessCtx, installments); err != nil {
						logrus.Errorf("ProcessDriverTransaction - installment replace all error: driverId=%s action=%s amount=%v time=%v err=%v", driverTrans.DriverID, action, installmentPayAmount, timeutil.BangkokNow(), err)
						return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
					}
				}
			}
		}

		if opt.isDryRunMode {
			return RetVal{dt: driverTrans, t: transactions}, nil
		}

		err = svc.CalculateNegativeCreditStatus(&driverTrans)
		if err != nil {
			return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
		}

		err = svc.driverTransRepo.Update(sessCtx, &driverTrans)
		if err != nil {
			return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
		}

		if len(transactions) > 0 {
			err = svc.transRepo.CreateAll(sessCtx, transactions)
			if err != nil {
				return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
			}
		}

		if len(transactionsTracking) > 0 {
			transactionsTracking := fp.FilterSlice(func(m model.Transaction) bool {
				if m.Info.Amount.LTE(0) {
					return false
				}
				return true
			}, transactionsTracking)
			if err := svc.transRepo.CreateAllTransactionTracking(sessCtx, transactionsTracking); err != nil {
				return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
			}
		}

		if backDate, detect := aggregate.DetectBackDateTransaction(transactions); detect {
			req := aggregate.IncomeIncRequest{
				DriverId: driverID,
				Date:     backDate,
				TransactionIDs: fp.MapSlice(func(m model.Transaction) string {
					return m.TransactionID
				}, transactions),
			}
			err := svc.incomeAggregateService.Inc(sessCtx, req)
			if err != nil {
				message := fmt.Errorf("unable to increment daily income from back-date transactions: %v", err)
				safe.SentryError(message,
					safe.WithDriverID(req.DriverId),
					safe.WithInfo("date", req.Date.Format(time.RFC3339)),
					safe.WithInfo("transactionIDs", strings.Join(req.TransactionIDs, ",")),
				)
				return RetVal{dt: model.DriverTransaction{}, t: []model.Transaction{}}, err
			}
		}

		if svc.accountingHubService.IsServiceEnabled(sessCtx) {
			accHubEvents := svc.accountingHubService.GenerateAccountingHubEvent(sessCtx, transactions...)
			logx.Info().
				Str(logutil.Module, "DriverTransactionService").
				Str(logutil.Method, "ProcessDriverTransaction").
				Str(logutil.DriverID, driverID).
				Msgf("there are %d accounting hub event(s)", len(accHubEvents))

			if len(accHubEvents) > 0 {
				accountingHubTxns := make([]model.AccountingHubTransaction, 0)
				for index := range accHubEvents {
					accountingHubTxns = append(accountingHubTxns, model.AccountingHubTransaction{
						Version:   1,
						Info:      accHubEvents[index],
						CreatedAt: timeutil.BangkokNow(),
					})
				}

				accHubRepoErr := svc.accountingHubTransactionRepo.CreateAll(sessCtx, accountingHubTxns...)
				if accHubRepoErr != nil {
					logx.Error().
						Str(logutil.Module, "DriverTransactionService").
						Str(logutil.Method, "ProcessDriverTransaction").
						Str(logutil.DriverID, driverID).
						Err(accHubRepoErr).
						Msg("unable to create an accounting hub transaction")
				}
			}
		}

		return RetVal{dt: driverTrans, t: transactions, tTracking: transactionsTracking}, nil
	}, transaction.WithLabel("DriverTransactionServiceImpl.ProcessDriverTransaction"))

	retVal := val.(RetVal)
	return retVal.dt, retVal.t, err
}

func partitionDriverTransactionInfos(infos []model.TransactionInfo, action model.TransactionAction) ([]model.TransactionInfo, []model.TransactionInfo) {
	switch action {
	case model.CommissionDeductionTransactionAction, model.IncentiveTransactionAction, model.ConvertCoinToCashAction:
		transactions, transactionsTracking := fp.PartitionSlice[model.TransactionInfo](partitionTransactionTransactionsTracking, infos)
		if transactions == nil {
			transactions = []model.TransactionInfo{}
		}
		if transactionsTracking == nil {
			transactionsTracking = []model.TransactionInfo{}
		}
		return transactions, transactionsTracking
	default:
		return []model.TransactionInfo{}, []model.TransactionInfo{}
	}
}

func (svc *DriverTransactionServiceImpl) CalculateNegativeCreditStatus(dr *model.DriverTransaction) error {
	if dr == nil {
		return errors.New("CalculateNegativeCreditStatus: driver transaction is nil")
	}

	timeNow := timeutil.BangkokNow()

	if dr.CreditBalance().LT(0) {
		// if already deadline then stamp then skip
		if dr.NegativeCreditDetail.IsCreditNegative == true && dr.NegativeCreditDetail.NegativeCreditStartTime != nil {
			return nil
		}
		dr.NegativeCreditDetail.NegativeCreditStartTime = &timeNow
		dr.NegativeCreditDetail.IsCreditNegative = true
	} else {
		// reset state when user has credit balance > 0
		dr.NegativeCreditDetail.IsCreditNegative = false
		dr.NegativeCreditDetail.NegativeCreditStartTime = nil
	}

	return nil
}

func (svc *DriverTransactionServiceImpl) ProcessOverdueInstallment(ctx context.Context, driverID string, amount types.Money) ([]model.Installment, error) {
	now := timeutil.BangkokNow()
	installments, err := svc.installmentRepo.FindOverdueInstallment(ctx, driverID, 0, 0, 0, []model.InstallmentStatus{}, repository.WithReadSecondaryPreferred)
	if err != nil {
		logrus.Errorf("ProcessInstallment - find installment error: driverId=%s amount=%v time=%v err=%v", driverID, amount, timeutil.BangkokNow(), err)
		return []model.Installment{}, err
	}

	if svc.config.EnableInventoryManagementService {
		installments, err = AttachProductInfoToInstallmentsFromProductService(ctx, svc.productService, installments)
		if err != nil {
			logrus.Errorf("ProcessInstallment - unable to get product/priority group from inventory management service: driverId=%s time=%v err=%v", driverID, timeutil.BangkokNow(), err)
			return []model.Installment{}, err
		}
	} else {
		// fill product group to installments
		{
			allProductGroups, err := svc.productGroupRepo.FindWithQueryAndSort(ctx, persistence.BuildProductGroupQuery(), 0, 0, nil, repository.WithReadSecondaryPreferred)
			if err != nil {
				return []model.Installment{}, err
			}

			productGroupMapper := make(map[string]*model.ProductGroup)
			for i, item := range allProductGroups {
				productGroupMapper[item.ID.Hex()] = &allProductGroups[i]
			}

			for i, item := range installments {
				if item.Product != nil {
					if targetProductGroup, ok := productGroupMapper[item.Product.ProductGroup.Hex()]; ok {
						installments[i].ProductGroup = targetProductGroup
					}
				}
			}
		}
	}

	toDeductInstallmentLogs := make(model.SortingInstallmentLogList, 0)
	toDeductInstallmentLogs.ConvertLogs(installments)
	sort.Sort(toDeductInstallmentLogs)
	toDeductInstallmentLogs.Rebalancing(amount, now)

	for i := range installments {
		installments[i].Product = nil
		installments[i].ProductGroup = nil
	}
	return installments, nil
}

func (svc *DriverTransactionServiceImpl) GetAndUpdatePendingTransactionsByOrder(ctx context.Context, orderID string, updateStatus model.PendingTransactionCollectionStatus) ([]model.PendingTransaction, error) {
	pendingTxns, err := svc.pendingTransactionRepo.FindByOrderID(ctx, orderID)
	if err != nil || len(pendingTxns) == 0 {
		return nil, errors.Wrap(err, "unable to find pending transactions by order id")
	}

	if !model.GetAllowStatusToUpdatePendingTransactionStatus().Has(string(updateStatus)) {
		return nil, fmt.Errorf("status %v is not allow to update pending transaction", updateStatus)
	}

	if err := svc.pendingTransactionRepo.UpdatePendingTransactionStatusByOrderID(ctx, orderID, updateStatus); err != nil {
		return nil, errors.Wrap(err, "unable to update pending transactions by order id")
	}

	logx.Info().Context(ctx).Str(logutil.OrderID, orderID).Msgf("update pending transaction status to %s success (%d docs)", updateStatus.String(), len(pendingTxns))

	return pendingTxns, nil
}

func ProvideDriverTransactionServiceV2(
	driverTransRepo repository.DriverTransactionRepository,
	transRepo repository.TransactionRepository,
	installmentRepo repository.InstallmentRepository,
	productGroupRepo repository.ProductGroupRepository,
	txnHelper transaction.TxnHelper,
	productService inventoryPb.ProductServiceClient,
	cfg config.DriverTransactionConfig,
	incomeAggregateService aggregate.IncomeAggregateService,
	pendingTransactionRepo repository.PendingTransactionRepository,
	accountingHubService accountinghub.AccountingHubTransactionService,
	accountingHubTransactionRepo repository.AccountingHubTransactionRepository,
) DriverTransactionServiceV2 {
	return &DriverTransactionServiceImpl{
		config:                       cfg,
		driverTransRepo:              driverTransRepo,
		transRepo:                    transRepo,
		installmentRepo:              installmentRepo,
		productGroupRepo:             productGroupRepo,
		productService:               productService,
		txnHelper:                    txnHelper,
		incomeAggregateService:       incomeAggregateService,
		pendingTransactionRepo:       pendingTransactionRepo,
		accountingHubService:         accountingHubService,
		accountingHubTransactionRepo: accountingHubTransactionRepo,
	}
}
