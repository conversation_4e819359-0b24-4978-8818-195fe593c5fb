package service

//go:generate mockgen -source=./driver_insurance_service.go -destination=./mock_service/mock_driver_insurance_service.go -package=mock_service

import (
	"context"
	"fmt"
	"io"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/kelseyhightower/envconfig"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	formServicePb "git.wndv.co/go/proto/lineman/form_service/v1"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

type DriverInsuranceCfg struct {
	UploadInsuranceConsolidateFileToVosInternalEnabled bool   `envconfig:"UPLOAD_RIDER_INSURANCE_CONSOLIDATE_FILE_TO_VOS_INTERNAL_ENABLED" default:"true"`
	DriverInsuranceBrokersMotorcycleMapper             string `envconfig:"DRIVER_INSURANCE_MOTORCYCLE_BROKERS_MAPPER" default:"rabbit:032024,chubb:032025"`
}

type DriverInsuranceServiceImpl struct {
	cfg                           DriverInsuranceCfg
	driverInsuranceRepo           repository.DriverInsuranceRepository
	vosService                    VOSService
	motorcycleInsuranceBrokerList InsuranceBrokerWithMonthCycleList
}

type UploadInsuranceFileResponse struct {
	l         sync.Mutex
	Successes []string
	Failures  []string
}

func (r *UploadInsuranceFileResponse) AddSuccess(id string) {
	r.l.Lock()
	r.Successes = append(r.Successes, id)
	r.l.Unlock()
}

func (r *UploadInsuranceFileResponse) AddFailure(id string) {
	r.l.Lock()
	r.Failures = append(r.Failures, id)
	r.l.Unlock()
}

func (r *UploadInsuranceFileResponse) isAllSuccess() bool {
	return len(r.Successes) == len(r.Successes)+len(r.Failures)
}

type DriverInsuranceService interface {
	ProcessApproveInsurance(ctx context.Context, insurances []model.DriverInsurance, imageField model.InsuranceFormImageField, admin string) error
	GetBrokerByMonthCycleString(ctx context.Context, insuranceType model.DriverInsuranceType, monthCycle string) (model.DriverInsuranceBroker, error)
}

func (di *DriverInsuranceServiceImpl) ProcessApproveInsurance(
	ctx context.Context,
	insurances []model.DriverInsurance,
	imageField model.InsuranceFormImageField,
	admin string,
) error {
	if len(insurances) == 0 {
		return fmt.Errorf("the latest driver insurance not found")
	}

	driverID := insurances[0].DriverID
	for index := range insurances {
		insurances[index].ApprovedBy = admin
	}

	if err := di.driverInsuranceRepo.UpsertAll(ctx, insurances...); err != nil {
		logrus.Errorf("ApproveInsurance UpsertAll Driver:%s, err: %v", driverID, err)
		return err
	}

	return nil
}

func getBytesFromS3ObjectOutput(obj *s3.GetObjectOutput) ([]byte, error) {
	defer obj.Body.Close()

	body, err := io.ReadAll(obj.Body)
	if err != nil {
		return nil, err
	}
	return body, nil
}

func (di *DriverInsuranceServiceImpl) GetBrokerByMonthCycleString(ctx context.Context, insuranceType model.DriverInsuranceType, monthCycle string) (model.DriverInsuranceBroker, error) {
	if insuranceType == model.DITPersonalAccident {
		return model.DIBChubb, nil
	}

	targetInsuranceBroker := di.motorcycleInsuranceBrokerList.Get(monthCycle)
	if targetInsuranceBroker.Label() == "" {
		return targetInsuranceBroker, errors.New("unable to find any insurance broker by the month cycle")
	}
	return targetInsuranceBroker, nil
}

func toImagesUploadResult(uploadImagesResult *UploadInsuranceFileResponse) model.ImagesUploadResult {
	return model.ImagesUploadResult{
		IsAllSuccess: uploadImagesResult.isAllSuccess(),
		Successes:    uploadImagesResult.Successes,
		Failures:     uploadImagesResult.Failures,
	}
}

// This function will return insurance month cycle depend on current time and
//
// Example: insuranceStartDatetime is "2022-10-15T01:00:00.000Z"
//
// If current time is "2022-11-11T12:00:00.000Z" then the current month cycle is "102022"
//
// If current time is "2022-11-21T12:00:00.000Z" then the current month cycle is "112022"
//
// insuranceTime will be "2022-11-15T01:00:00.000Z" for both case
func GetCurrentInsuranceMonthCycle(insuranceStartDatetime time.Time) string {
	currentTime := timeutil.BangkokNow()
	insuranceTime := time.Date(
		currentTime.Year(), currentTime.Month(), insuranceStartDatetime.Day(),
		insuranceStartDatetime.Hour(), insuranceStartDatetime.Minute(),
		insuranceStartDatetime.Second(), 0, timeutil.BangkokLocation())
	if currentTime.Before(insuranceTime) {
		return timeutil.ToMMYYYY(currentTime.AddDate(0, -1, 0))
	}
	return timeutil.ToMMYYYY(currentTime)
}

type formServiceInsuranceFormType string

const (
	// Deprecated
	fsPersonalAccidentInsuranceType formServiceInsuranceFormType = "PA"
	fsMotorcycleInsuranceType       formServiceInsuranceFormType = "Motor"
	// Deprecated
	fsPersonalAccidentAndMotorcycleInsuranceType formServiceInsuranceFormType = "Motor+PA"
)

func ConvertFormServicesToModels(srcs []*formServicePb.Form) []model.DriverInsurance {
	results := make([]model.DriverInsurance, 0)
	for _, src := range srcs {
		if src.FormType != formServicePb.FormType_FORM_TYPE_INSURANCE_REGISTRATION {
			continue
		}

		srcModel := model.DriverInsurance{
			FormID:    src.Id,
			DriverID:  src.RefId,
			CreatedAt: src.IssuedAt.AsTime(),
		}
		if srcModel.CreatedAt.IsZero() {
			srcModel.CreatedAt = src.CreatedAt.AsTime()
		}
		srcModel.CreatedAt = srcModel.CreatedAt.In(timeutil.BangkokLocation())

		srcModel.MonthCycle = timeutil.ToMMYYYY(srcModel.CreatedAt)

		switch src.Status {
		case formServicePb.FormStatus_FORM_STATUS_INIT:
			continue
		case formServicePb.FormStatus_FORM_STATUS_SUBMITED:
			srcModel.Status = model.DISWaitingForConfirmation
		case formServicePb.FormStatus_FORM_STATUS_APPROVED:
			srcModel.Status = model.DISWaitingForConfirmation
		case formServicePb.FormStatus_FORM_STATUS_REJECTED:
			srcModel.Status = model.DISRejectedByAdmin
		}

		formValue := src.Value.AsMap()
		types := make([]model.DriverInsuranceType, 0)
		if value, existed := formValue["type"]; !existed || value == nil {
			continue
		}

		typeString, isStringType := formValue["type"].(string)
		if !isStringType {
			continue
		}

		switch formServiceInsuranceFormType(typeString) {
		case fsPersonalAccidentInsuranceType:
			types = append(types, model.DITPersonalAccident)
		case fsMotorcycleInsuranceType:
			types = append(types, model.DITMotorcycle)
		case fsPersonalAccidentAndMotorcycleInsuranceType:
			types = append(types, model.DITMotorcycle, model.DITPersonalAccident)
		}

		srcModel.Detail = model.DriverInsuranceDetail{
			FirstName: crypt.NewLazyEncryptedString(formValue["firstName"].(string)),
			LastName:  crypt.NewLazyEncryptedString(formValue["lastName"].(string)),
		}

		if src.Status == formServicePb.FormStatus_FORM_STATUS_REJECTED {
			rejectedReasonDisplay := GetRejectedInsuranceRegistrationMessage("")
			if value, existed := formValue["rejectedReason"].(string); existed {
				rejectedReasonDisplay = GetRejectedInsuranceRegistrationMessage(value)
			}
			srcModel.Detail.RejectedReason = rejectedReasonDisplay
		}

		for _, insuranceType := range types {
			srcModel.Type = insuranceType
			results = append(results, srcModel)
		}
	}
	return results
}

func GetInsuranceRegistrationPeriod(currentTime time.Time, startDate time.Time, endDate time.Time) (time.Time, time.Time) {
	if startDate.IsZero() || endDate.IsZero() {
		logrus.Warnf("[GetInsuranceRegistrationPeriod] unable to get period, either start or end time is zero")
		return time.Time{}, time.Time{}
	}

	if endDate.Day() < currentTime.Day() {
		currentTime = currentTime.AddDate(0, 1, 0)
	}

	insuranceStartDate := time.Date(
		startDate.Year(), startDate.Month(),
		startDate.Day(), startDate.Hour(),
		startDate.Minute(), startDate.Second(), 0, currentTime.Location(),
	)

	insuranceEndDate := time.Date(
		endDate.Year(), endDate.Month(),
		endDate.Day(), endDate.Hour(),
		endDate.Minute(), endDate.Second(), 0, currentTime.Location(),
	)

	return insuranceStartDate, insuranceEndDate
}

type DriverInsuranceGroup []*model.DriverInsurance

// more priority item should be the first item to be added in the list
func (dig DriverInsuranceGroup) Add(srcs ...*model.DriverInsurance) DriverInsuranceGroup {
	for _, item := range srcs {
		if item != nil {
			dig = append(dig, item)
		}
	}
	return dig
}

// GetLatestDriverInsurance - For each item in the group, if a new item has month cycle MORE than existed item,
// then the new item is the lastest one
//
// So, the first item has more priority even if the next item has equal month cycle
func (dig DriverInsuranceGroup) GetLatestDriverInsurance(monthCycleParam ...string) *model.DriverInsurance {
	if len(dig) == 0 {
		return nil
	}

	var cmpMonthCycle int
	convertMonthCycleToInt := func(src string) int {
		if len(src) != 6 {
			return 0
		}
		resInt, _ := strconv.Atoi(src[2:] + src[0:2])
		return resInt
	}

	if len(monthCycleParam) > 0 {
		cmpMonthCycle = convertMonthCycleToInt(monthCycleParam[0])
	}

	targetIndex := -1
	if cmpMonthCycle == 0 {
		cmpMonthCycle = convertMonthCycleToInt(dig[0].MonthCycle)
		targetIndex = 0
	}
	for index, item := range dig {
		if index == targetIndex {
			continue
		}

		srcMonthCycle := convertMonthCycleToInt(item.MonthCycle)
		if srcMonthCycle > cmpMonthCycle {
			cmpMonthCycle = srcMonthCycle
			targetIndex = index
		}
	}

	if targetIndex == -1 {
		return nil
	}
	return dig[targetIndex]
}

// ConvertMonthCycleToInt - form's month cycle stored in format MMYYYY
// this method will return YYYYMM as integer
// e.g. 042025 --> 202504, 122024 --> 202412
func ConvertMonthCycleToInt(src string) int {
	if len(src) != 6 {
		return 0
	}
	resInt, _ := strconv.Atoi(src[2:] + src[0:2])
	return resInt
}

type InsuranceBrokerWithMonthCycleList []InsuranceBrokerWithMonthCycle

func (logs InsuranceBrokerWithMonthCycleList) Len() int {
	return len(logs)
}

func (logs InsuranceBrokerWithMonthCycleList) Less(i, j int) bool {
	return logs[i].MonthCycle > logs[j].MonthCycle
}

func (logs InsuranceBrokerWithMonthCycleList) Swap(i, j int) {
	logs[i], logs[j] = logs[j], logs[i]
}

func (logs InsuranceBrokerWithMonthCycleList) Get(monthCycle string) model.DriverInsuranceBroker {
	srcMonthCycle := ConvertMonthCycleToInt(monthCycle)
	for _, log := range logs {
		if srcMonthCycle >= log.MonthCycle {
			return log.InsuranceBroker
		}
	}
	return model.DriverInsuranceBroker("")
}

type InsuranceBrokerWithMonthCycle struct {
	InsuranceBroker model.DriverInsuranceBroker
	MonthCycle      int
}

func GenerateInsuranceBrokerList(csvSrc string) InsuranceBrokerWithMonthCycleList {
	csvVals := strings.Split(csvSrc, ",")

	insuranceBrokers := make(InsuranceBrokerWithMonthCycleList, 0, 2)
	for _, val := range csvVals {
		brokerInfos := strings.Split(val, ":")
		if len(brokerInfos) != 2 {
			continue
		}
		brokerStr := strings.TrimSpace(brokerInfos[0])
		monthCycleStr := strings.TrimSpace(brokerInfos[1])

		targetBroker := model.DriverInsuranceBroker(brokerStr)
		if targetBroker.Label() == "" {
			logx.Warn().
				Str(logutil.Method, "DriverInsuranceService").
				Msgf("unable to extract an Insurance Broker from the string: [%s]", brokerStr)
			continue
		}

		targetMonthCycle := ConvertMonthCycleToInt(monthCycleStr)
		if targetMonthCycle == 0 {
			logx.Warn().
				Str(logutil.Method, "DriverInsuranceService").
				Msgf("unable to extract a Month Cycle from the string: %s", monthCycleStr)
			continue
		}

		insuranceBrokers = append(insuranceBrokers, InsuranceBrokerWithMonthCycle{
			InsuranceBroker: targetBroker,
			MonthCycle:      targetMonthCycle,
		})
	}

	if len(csvSrc) > 0 && len(insuranceBrokers) == 0 {
		logx.Warn().
			Str(logutil.Method, "DriverInsuranceService").
			Msg("insurance broker source is not empty but can't extract any information")
		return insuranceBrokers
	}

	// TODO check that this func sort correctly
	sort.Sort(insuranceBrokers)
	return insuranceBrokers
}

func ProvideDriverInsuranceService(
	cfg DriverInsuranceCfg,
	driverInsuranceRepo repository.DriverInsuranceRepository,
	vosService VOSService,
) *DriverInsuranceServiceImpl {
	insuranceBrokerList := GenerateInsuranceBrokerList(cfg.DriverInsuranceBrokersMotorcycleMapper)
	return &DriverInsuranceServiceImpl{
		cfg:                           cfg,
		driverInsuranceRepo:           driverInsuranceRepo,
		vosService:                    vosService,
		motorcycleInsuranceBrokerList: insuranceBrokerList,
	}
}

func ProvideDriverInsuranceCfg() (cfg DriverInsuranceCfg) {
	envconfig.MustProcess("", &cfg)
	return
}
