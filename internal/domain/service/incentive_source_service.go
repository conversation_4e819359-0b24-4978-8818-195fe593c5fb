//go:generate mockgen -source=./incentive_source_service.go -destination=./mock_service/mock_incentive_source_service.go -package=mock_service

package service

import (
	"strings"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
)

type IncentiveSourceService interface {
	Get() ([]string, error)
}

type IncentiveSourceConfigGetter interface {
	Get() config.IncentiveSourceConfig
}

var _ IncentiveSourceService = (*IncentiveSourceServiceImpl)(nil)

type IncentiveSourceServiceImpl struct {
	cfg IncentiveSourceConfigGetter
}

// Get implements IncentiveSourceService
func (svc *IncentiveSourceServiceImpl) Get() ([]string, error) {
	cfg := svc.cfg.Get()
	s := strings.ReplaceAll(cfg.IncentiveSources, " ", "")
	return strings.Split(s, ","), nil
}

func ProvideIncentiveSourceServiceImpl(cfg IncentiveSourceConfigGetter) *IncentiveSourceServiceImpl {
	return &IncentiveSourceServiceImpl{
		cfg: cfg,
	}
}
