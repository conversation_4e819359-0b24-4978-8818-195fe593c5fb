package accountinghub

import (
	"context"

	"git.wndv.co/go/logx/v2"
	accHubPb "git.wndv.co/go/proto/erp/accounting_hub/common/v1"
	commonv1 "git.wndv.co/go/proto/erp/accounting_hub/common/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
)

//go:generate mockgen -source=./accounting_hub_transaction_service.go -destination=./mock_accountinghub/mock_accounting_hub_transaction_service.go -package=mock_accountinghub
type AccountingHubTransactionService interface {
	IsServiceEnabled(ctx context.Context) bool
	GenerateAccountingHubTransactionInfo(ctx context.Context, transactions model.Transaction) *TransactionInfo
	GenerateAccountingHubEvent(ctx context.Context, transactions ...model.Transaction) []model.AccountingHubEventInfo
}

type accHubCondition struct {
	TransactionAction   model.TransactionAction
	TransactionCategory model.TransactionCategory
	TransactionType     model.TransactionType
	TransactionSubType  model.TransactionSubType
}

type accountingHubTransactionServiceImpl struct {
	TxnConditionToActioner map[accHubCondition]TransactionActioner
	FeatureFlagService     featureflag.Service
}

var accountingHubServiceModule string = "AccountingHubService"

func (accHubTxnSvc *accountingHubTransactionServiceImpl) IsServiceEnabled(ctx context.Context) bool {
	if accHubTxnSvc.FeatureFlagService != nil {
		return accHubTxnSvc.FeatureFlagService.IsEnabledWithDefaultFalse(ctx, featureflag.IsAccountingHubTransactionCollectorEnabled.Name)
	}
	return false
}

func (accHubTxnSvc *accountingHubTransactionServiceImpl) IsEligibleTransaction(transaction model.Transaction) bool {
	logger := func(logx *logx.LogEvent) *logx.LogEvent {
		return logx.
			Str(logutil.Module, accountingHubServiceModule).
			Str(logutil.Method, "IsEligibleTransaction").
			Str(logutil.DriverID, transaction.Info.DriverID).
			Str(logutil.TripID, transaction.Info.TripID).
			Str(logutil.OrderID, transaction.Info.OrderID).
			Str(logutil.TransactionID, transaction.TransactionID)
	}

	// currently, only focus on a complete order transaction
	if transaction.Action != model.CommissionDeductionTransactionAction {
		logger(logx.Info()).Msg("transaction action is not a COMMISSION_DEDUCTION")
		return false
	}

	// check if the service type is supported
	if !accHubTxnSvc.hasValidServiceType(transaction.Info) {
		logger(logx.Info()).Msg("not supported service type or no food/mart transactions found")
		return false
	}

	if transaction.Info.Amount.LTE(0) {
		logger(logx.Info()).Msg("transaction.Info.Amount is less than or equal to 0")
		return false
	}

	return true
}

func (accHubTxnSvc *accountingHubTransactionServiceImpl) hasValidServiceType(info model.TransactionInfo) bool {
	if info.OrderID != "" {
		serviceType := getServiceTypeByOrderID(info.OrderID)
		return isSupportedServiceType(serviceType)
	}
	if len(info.Orders) > 0 {
		return accHubTxnSvc.hasFoodOrdersInTrip(info.Orders)
	}
	return false
}

func isSupportedServiceType(serviceType model.Service) bool {
	return serviceType == model.ServiceFood || serviceType == model.ServiceMart
}

func (accHubTxnSvc *accountingHubTransactionServiceImpl) hasFoodOrdersInTrip(src []model.TransactionOrders) bool {
	for _, txnOrder := range src {
		if txnOrder.OrderID != "" {
			serviceType := getServiceTypeByOrderID(txnOrder.OrderID)
			return isSupportedServiceType(serviceType)
		}
	}
	return false
}

func (accHubTxnSvc *accountingHubTransactionServiceImpl) GenerateAccountingHubTransactionInfo(ctx context.Context, transaction model.Transaction) *TransactionInfo {
	logger := func(logx *logx.LogEvent) *logx.LogEvent {
		return logx.
			Str(logutil.Module, accountingHubServiceModule).
			Str(logutil.Method, "GenerateAccountingHubTransactionInfo").
			Str(logutil.DriverID, transaction.Info.DriverID).
			Str(logutil.TripID, transaction.Info.TripID).
			Str(logutil.OrderID, transaction.Info.OrderID).
			Str(logutil.TransactionID, transaction.TransactionID)
	}

	if !accHubTxnSvc.IsEligibleTransaction(transaction) {
		logger(logx.Info()).Msg("transaction is not eligible to generate an accounting hub transaction")
		return nil
	}

	conditioner := accHubCondition{
		TransactionAction:   transaction.Action,
		TransactionCategory: transaction.Info.Category,
		TransactionType:     transaction.Info.Type,
		TransactionSubType:  transaction.Info.SubType,
	}

	logger(logx.Info()).Interface("acchub_conditioner", conditioner).
		Msg("GenerateAccountingHubTransactionInfo with a conditioner")

	txnActioner := accHubTxnSvc.TxnConditionToActioner[conditioner]
	if txnActioner == nil {
		logger(logx.Info()).Msg("unable to find Accounting Hub Action from a conditon")
		return nil
	}

	generatedTxn := txnActioner.GenerateTransactionInfo(ctx, &transaction)
	return &generatedTxn
}

// GenerateAccountingHubEvent generates accounting hub events from transactions
func (svc *accountingHubTransactionServiceImpl) GenerateAccountingHubEvent(ctx context.Context, transactions ...model.Transaction) []model.AccountingHubEventInfo {
	// Step 1: Generate transactions
	accHubTxns := svc.generateAccountingHubTransactions(ctx, transactions)

	// Step 2: Group transactions by key (trip ID or order ID)
	orderedKeys, groupedTxns := svc.groupAccountingHubTransactions(accHubTxns)

	// Step 3: Generate events from grouped transactions
	eventMap := svc.generateAccountingHubEvents(groupedTxns)

	// Step 4: Sort and return events in correct order
	return svc.sortEvents(orderedKeys, eventMap)
}

func (accHubTxnSvc *accountingHubTransactionServiceImpl) generateAccountingHubTransactions(ctx context.Context, transactions []model.Transaction) []*TransactionInfo {
	// Create a map to store aggregated transactions by type
	aggregatedTxns := make(map[accHubPb.FinancialTransactionType]*TransactionInfo)

	// Process each transaction
	for _, txn := range transactions {
		txnInfo := accHubTxnSvc.GenerateAccountingHubTransactionInfo(ctx, txn)
		if txnInfo == nil {
			continue
		}

		// Aggregate transactions by their type
		aggregateTransactionsByType(txnInfo, aggregatedTxns)
	}

	// converts the map of transactions back to a slice
	return fp.MapSlice(func(k accHubPb.FinancialTransactionType) *TransactionInfo {
		return aggregatedTxns[k]
	}, utils.CollectKeys(aggregatedTxns))
}

// groupAccountingHubTransactions groups transactions by their key (trip ID or order ID)
func (svc *accountingHubTransactionServiceImpl) groupAccountingHubTransactions(txnInfos []*TransactionInfo) ([]string, map[string][]*TransactionInfo) {
	grouped := make(map[string][]*TransactionInfo)
	orderedKeys := make([]string, 0)

	for _, txn := range txnInfos {
		key := svc.getTransactionKey(txn)
		if key == "" {
			logx.Warn().
				Str(logutil.Module, accountingHubServiceModule).
				Str(logutil.Method, "groupAccountingHubTransactions").
				Str(logutil.DriverID, txn.FleetTransaction.Info.DriverID).
				Str(logutil.TripID, txn.FleetTransaction.Info.TripID).
				Str(logutil.OrderID, txn.FleetTransaction.Info.OrderID).
				Str(logutil.TransactionID, txn.FleetTransaction.TransactionID).
				Msg("empty target key")
			continue
		}

		if _, exists := grouped[key]; !exists {
			grouped[key] = make([]*TransactionInfo, 0)
			orderedKeys = append(orderedKeys, key)
		}
		grouped[key] = append(grouped[key], txn)
	}

	return orderedKeys, grouped
}

// getTransactionKey determines the key for grouping transactions
func (svc *accountingHubTransactionServiceImpl) getTransactionKey(txn *TransactionInfo) string {
	if txn.GroupType == TripLevelTransactionGroupType {
		return txn.FleetTransaction.Info.TripID
	}
	if txn.GroupType == OrderLevelTransactionGroupType {
		return txn.FleetTransaction.Info.OrderID
	}
	return ""
}

// generateAccountingHubEvents creates accounting hub events from grouped transactions
func (svc *accountingHubTransactionServiceImpl) generateAccountingHubEvents(groupedTxns map[string][]*TransactionInfo) map[string]*model.AccountingHubEventInfo {
	events := make(map[string]*model.AccountingHubEventInfo)

	for keyID, txns := range groupedTxns {
		if event := svc.createEventFromTransactions(txns); event != nil {
			events[keyID] = event
		}
	}

	return events
}

// createEventFromTransactions creates a single event from a group of transactions
func (svc *accountingHubTransactionServiceImpl) createEventFromTransactions(txns []*TransactionInfo) *model.AccountingHubEventInfo {
	if len(txns) == 0 {
		return nil
	}

	firstTxn := txns[0]
	event := &model.AccountingHubEventInfo{
		PrimaryReferenceID:   firstTxn.FleetTransaction.Info.TripID,
		SecondaryReferenceID: svc.getSecondaryReferenceID(firstTxn),
		FinancialEventType:   svc.getFinancialEventType(firstTxn),
		TransactionTime:      firstTxn.FleetTransaction.CreatedAt,
		SourceName:           commonv1.SourceName_SOURCE_NAME_FLEET,
		Transactions:         make([]model.AccountingHubTransactionInfo, 0, len(txns)),
	}

	for _, txn := range txns {
		event.Transactions = append(event.Transactions, txn.Transactions...)
	}

	return event
}

// getSecondaryReferenceID determines the secondary reference ID based on group type
func (svc *accountingHubTransactionServiceImpl) getSecondaryReferenceID(txn *TransactionInfo) string {
	if txn.GroupType == OrderLevelTransactionGroupType {
		return txn.FleetTransaction.Info.OrderID
	}
	return txn.FleetTransaction.Info.TripID
}

// getFinancialEventType determines the financial event type based on group type
func (svc *accountingHubTransactionServiceImpl) getFinancialEventType(txn *TransactionInfo) commonv1.FinancialEventType {
	if txn.GroupType == OrderLevelTransactionGroupType {
		return commonv1.FinancialEventType_FINANCIAL_EVENT_TYPE_TRIP_ORDER_LINE_COMPLETED
	}
	return commonv1.FinancialEventType_FINANCIAL_EVENT_TYPE_TRIP_COMPLETED
}

// sortEvents orders events based on the provided ordered keys
func (svc *accountingHubTransactionServiceImpl) sortEvents(
	orderedKeys []string,
	eventMap map[string]*model.AccountingHubEventInfo,
) []model.AccountingHubEventInfo {
	events := make([]model.AccountingHubEventInfo, 0, len(orderedKeys))

	for _, key := range orderedKeys {
		event, exists := eventMap[key]
		if !exists {
			logx.Warn().
				Str(logutil.Module, accountingHubServiceModule).
				Str(logutil.Method, "sortEvents").
				Str("event_key", key).
				Msg("unable to find accounting hub event from key")
			continue
		}
		events = append(events, *event)
	}

	return events
}

func ProvideAccountingHubTransactionService(featureFlagService featureflag.Service) AccountingHubTransactionService {
	svc := accountingHubTransactionServiceImpl{
		FeatureFlagService: featureFlagService,
	}
	svc.generateMapper()
	return &svc
}
