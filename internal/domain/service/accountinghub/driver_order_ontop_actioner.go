package accountinghub

import (
	"context"

	"git.wndv.co/go/logx/v2"
	accHubPb "git.wndv.co/go/proto/erp/accounting_hub/common/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	xgoType "git.wndv.co/lineman/xgo/types"
)

type DriverOrderOnTopTxnActioner struct{}

func NewDriverOrderOnTopTxnActioner() TransactionActioner {
	return &DriverOrderOnTopTxnActioner{}
}

func (txnAct *DriverOrderOnTopTxnActioner) GenerateTransactionInfo(ctx context.Context, txn *model.Transaction) TransactionInfo {
	logger := func(logx *logx.LogEvent) *logx.LogEvent {
		return logx.
			Str(logutil.Module, accountingHubServiceModule).
			Str(logutil.Method, "GenerateOnTopTransactionInfo").
			Str(logutil.DriverID, txn.Info.DriverID).
			Str(logutil.TripID, txn.Info.TripID).
			Str(logutil.OrderID, txn.Info.OrderID).
			Str(logutil.TransactionID, txn.TransactionID)
	}

	// TODO remove this after fix user on top issue
	logger(logx.Info()).
		Interface("ontop_details", txn.Info.OnTopDetails).
		Msg("generate on top transaction info with ontop details")

	grouppingOnTop := make(map[model.OnTopFareScheme]types.Money)

	for _, ontopScheme := range txn.Info.OnTopDetails {
		if ontopScheme.Amount.LTE(0) {
			logger(logx.Info()).
				Str(logutil.OnTopScheme, string(ontopScheme.OnTopFareScheme)).
				Msgf("on top amount %f less than or equal 0 ", ontopScheme.Amount.Float64())
			continue
		}

		if amount, ok := grouppingOnTop[ontopScheme.OnTopFareScheme]; ok {
			grouppingOnTop[ontopScheme.OnTopFareScheme] = amount.Add(ontopScheme.Amount)
			continue
		}

		grouppingOnTop[ontopScheme.OnTopFareScheme] = ontopScheme.Amount
	}

	// TODO remove this after fix user on top issue
	logger(logx.Info()).
		Msgf("len of groupping on top transaction %d", len(grouppingOnTop))

	txns := make([]model.AccountingHubTransactionInfo, 0, len(grouppingOnTop))
	for onTopScheme, amount := range grouppingOnTop {
		financialTransactionType := onTopScheme.MappingOnTopSchemeToAccountingHubTransactionTypeOnTop()

		if financialTransactionType == accHubPb.FinancialTransactionType_FINANCIAL_TRANSACTION_TYPE_UNSPECIFIED {
			logger(logx.Warn()).
				Str(logutil.OnTopScheme, string(onTopScheme)).
				Msg("financial transaction type is unspecified")
			continue
		}

		// TODO remove this after fix user on top issue
		logger(logx.Info()).
			Str(logutil.OnTopScheme, string(onTopScheme)).
			Msg("adding on top transaction to accounting hub transaction info")

		txns = append(txns, model.AccountingHubTransactionInfo{
			Type:          financialTransactionType,
			Amount:        xgoType.NewMoneyFromFloat(amount.Float64()),
			TransactionID: txn.TransactionID,
			TransactionDetails: []model.TransactionDetail{
				{
					TransactionID: txn.TransactionID,
					Amount:        xgoType.NewMoneyFromFloat(amount.Float64()),
				},
			},
		})
	}

	return TransactionInfo{
		Transactions:     txns,
		FleetTransaction: txn,
		GroupType:        OrderLevelTransactionGroupType,
	}
}
