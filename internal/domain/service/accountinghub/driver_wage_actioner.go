package accountinghub

import (
	"context"

	accHubPb "git.wndv.co/go/proto/erp/accounting_hub/common/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	xgoType "git.wndv.co/lineman/xgo/types"
)

type DriverWageTxnActioner struct{}

func NewDriverWageTxnActioner() TransactionActioner {
	return &DriverWageTxnActioner{}
}

func (txnAct *DriverWageTxnActioner) GenerateTransactionInfo(ctx context.Context, txn *model.Transaction) TransactionInfo {
	return TransactionInfo{
		Transactions: []model.AccountingHubTransactionInfo{
			{
				Type:          accHubPb.FinancialTransactionType_FINANCIAL_TRANSACTION_TYPE_RIDER_WAGE,
				Amount:        xgoType.NewMoneyFromFloat(txn.Info.Amount.Float64()),
				TransactionID: txn.TransactionID,
				TransactionDetails: []model.TransactionDetail{
					{
						TransactionID: txn.TransactionID,
						Amount:        xgoType.NewMoneyFromFloat(txn.Info.Amount.Float64()),
					},
				},
			},
		},
		FleetTransaction: txn,
		GroupType:        TripLevelTransactionGroupType,
	}
}
