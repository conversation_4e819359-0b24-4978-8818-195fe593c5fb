package service

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	"git.wndv.co/lineman/absinthe/timeutils"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/egs/mock_grpc"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

func TestEGS_PurchasingPower(t *testing.T) {
	t.Run("valid purchasing power", func(t *testing.T) {
		e, deps, _ := NewEGSTest(t)

		driver := &model.Driver{DSCR: 20}
		deps.driverRepo.EXPECT().
			FindDriverID(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(driver, nil)

		inst := []model.Installment{
			{
				DailyAmount: 50,
			},
		}
		deps.instRepo.EXPECT().
			FindWithQueryAndSort(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(inst, nil)

		income := []model.IncomeDailySummary{
			{
				IncomeDetail: model.IncomeDailyDetail{
					TotalWage: 9000,
				},
			},
			{
				IncomeDetail: model.IncomeDailyDetail{
					TotalWage: 9000,
				},
			},
		}
		deps.incomeRepo.EXPECT().
			Query(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(income, nil)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultTrue(gomock.Any(), "fleet.driver.new-purchasing-power-calculation.kill-switch").
			Return(false).Times(1)

		pp, _, _, err := e.PurchasingPower(context.Background(), "driverID1", "batchID1", time.Time{})
		assert.NoError(t, err)

		assert.Equal(t, float64(70), pp)
	})

	t.Run("valid purchasing power with financial risk data", func(t *testing.T) {
		now := time.Date(2024, 03, 19, 0, 0, 0, 0, timeutil.BangkokLocation())

		type EGSTestCase struct {
			name                         string
			driverID                     string
			batchID                      string
			expectedResult               []float64
			expectedError                error
			expectedInstallmentRepoError error
			expectedIncomeRepoError      error
			expectedDriverRepoError      error
			mockDriver                   *model.Driver
			mockIncome                   []model.IncomeDailySummary
			mockInstall                  []model.Installment
		}
		testCases := []EGSTestCase{
			{
				name:           "Successful Calculation",
				driverID:       "driver123",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{66.67, 50000, 180},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 100},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(20000)}}},
				mockInstall:    []model.Installment{{DailyAmount: 100}, {DailyAmount: 200}, {DailyAmount: 300}},
			},
			{
				name:           "Successful Calculation DSCR 50",
				driverID:       "driver123",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{200, 50000, 180},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 80},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(30000)}}},
				mockInstall:    []model.Installment{{DailyAmount: 100}, {DailyAmount: 200}, {DailyAmount: 300}},
			},
			{
				name:           "Successful Calculation DSCR Before DSCR Effective Date",
				driverID:       "driver123",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{0, 5000, 100},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 10.55, DSCREffectiveDate: types.Period{StartAt: now.AddDate(0, -1, 0), EndAt: now.AddDate(0, -1, 0)}, MaxTenor: 100, MaxExposure: 5000},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(58015)}}},
				mockInstall:    []model.Installment{},
			},
			{
				name:           "Successful Calculation DSCR Within DSCR Effective Date",
				driverID:       "driver123",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{212.72, 5000, 100},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 10.55, DSCREffectiveDate: types.Period{StartAt: now.AddDate(0, -1, 0), EndAt: now.AddDate(0, 1, 0)}, MaxTenor: 100, MaxExposure: 5000},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(58015)}}},
				mockInstall:    []model.Installment{},
			},
			{
				name:           "Successful Calculation DSCR After DSCR Effective Date",
				driverID:       "driver123",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{0, 5000, 100},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 10.55, DSCREffectiveDate: types.Period{StartAt: now.AddDate(0, 1, 0), EndAt: now.AddDate(0, 1, 0)}, MaxTenor: 100, MaxExposure: 5000},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(58015)}}},
				mockInstall:    []model.Installment{},
			},
			{
				name:           "Successful Calculation With Default Financial Risk And Installment",
				driverID:       "driver123",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{66.67, 44600, 180},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 100, MaxExposure: 0, MaxTenor: 0},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(20000)}}},
				mockInstall: []model.Installment{
					{DailyAmount: 100, InitialAmount: 1000, InstallmentAmount: 100},
					{DailyAmount: 200, InitialAmount: 2000, InstallmentAmount: 200},
					{DailyAmount: 300, InitialAmount: 3000, InstallmentAmount: 300},
				},
			},
			{
				name:           "Successful Calculation With Financial Risk Data",
				driverID:       "driver123",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{66.67, 4600, 180},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 100, MaxExposure: 10000, MaxTenor: 180},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(20000)}}},
				mockInstall: []model.Installment{
					{DailyAmount: 100, InitialAmount: 1000, InstallmentAmount: 100},
					{DailyAmount: 200, InitialAmount: 2000, InstallmentAmount: 200},
					{DailyAmount: 300, InitialAmount: 3000, InstallmentAmount: 300},
				},
			},
			{
				// Total installment more than driver's max exposure
				name:           "Successful Calculation With 0 Exposure",
				driverID:       "driver123",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{66.67, -5400, 180},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 100, MaxExposure: 0, MaxTenor: 180},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(20000)}}},
				mockInstall: []model.Installment{
					{DailyAmount: 100, InitialAmount: 1000, InstallmentAmount: 100},
					{DailyAmount: 200, InitialAmount: 2000, InstallmentAmount: 200},
					{DailyAmount: 300, InitialAmount: 3000, InstallmentAmount: 300},
				},
			},
			{
				name:           "No Income Data",
				driverID:       "driver456",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{0, 50000, 180},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 100},
				mockIncome:     []model.IncomeDailySummary{},
				mockInstall:    []model.Installment{},
			},
			{
				name:           "No Installment Data",
				driverID:       "driver789",
				batchID:        "MOCK_PATCH_ID",
				expectedResult: []float64{333.33, 50000, 180},
				expectedError:  nil,
				mockDriver:     &model.Driver{DSCR: 100},
				mockIncome:     []model.IncomeDailySummary{{IncomeDetail: model.IncomeDailyDetail{TotalWage: types.NewMoney(10000)}}},
				mockInstall:    []model.Installment{},
			},
			{
				name:                    "Error Case: Driver Not Found",
				driverID:                "nonexistent",
				batchID:                 "MOCK_PATCH_ID",
				expectedResult:          []float64{0, 0, 0},
				expectedError:           errors.New("Driver not found"),
				expectedDriverRepoError: errors.New("Driver not found"),
				mockDriver:              nil,
				mockIncome:              []model.IncomeDailySummary{},
				mockInstall:             []model.Installment{},
			},
			{
				name:                         "Error Case: Error in Installment Repository",
				driverID:                     "driver789",
				batchID:                      "MOCK_PATCH_ID",
				expectedResult:               []float64{0, 0, 0},
				expectedError:                errors.New("Installment query error"),
				expectedInstallmentRepoError: errors.New("Installment query error"),
				mockDriver:                   &model.Driver{DSCR: 100},
				mockIncome:                   []model.IncomeDailySummary{},
				mockInstall:                  []model.Installment{},
			},
			{
				name:                    "Error Case: Error in Income Repository",
				driverID:                "driver789",
				batchID:                 "MOCK_PATCH_ID",
				expectedResult:          []float64{0, 0, 0},
				expectedError:           errors.New("Income query error"),
				expectedIncomeRepoError: errors.New("Income query error"),
				mockDriver:              &model.Driver{DSCR: 100},
				mockIncome:              []model.IncomeDailySummary{},
				mockInstall:             []model.Installment{},
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				egs, deps, finish := NewEGSTest(t)
				defer finish()
				ctx := context.Background()

				setCommonExpectations := func() {
					driverRepoExpectation := deps.driverRepo.EXPECT().FindDriverID(ctx, tc.driverID, gomock.Any()).Return(tc.mockDriver, tc.expectedDriverRepoError)
					if tc.expectedDriverRepoError == nil {
						instRepoExpectation := deps.instRepo.EXPECT().FindWithQueryAndSort(ctx, gomock.Any(), 0, 0, gomock.Any()).Return(tc.mockInstall, tc.expectedInstallmentRepoError)
						if tc.expectedDriverRepoError == nil && tc.expectedInstallmentRepoError == nil {
							deps.incomeRepo.EXPECT().Query(ctx, gomock.Any(), 0, 0, gomock.Any(), gomock.Any()).Return(tc.mockIncome, tc.expectedIncomeRepoError)
						}
						instRepoExpectation.Times(1)
					}
					driverRepoExpectation.Times(1)

					deps.featureFlagService.EXPECT().
						IsEnabledWithDefaultTrue(gomock.Any(), "fleet.driver.new-purchasing-power-calculation.kill-switch").
						Return(false).
						Times(1)
				}

				setCommonExpectations()

				pp, maxExposure, maxTenor, err := egs.PurchasingPower(ctx, tc.driverID, tc.batchID, now)
				assert.Equal(t, tc.expectedResult[0], pp)
				assert.Equal(t, tc.expectedError, err)

				if tc.expectedError == nil {
					income := types.NewMoney(0)
					instDaily := types.NewMoney(0)
					dayAmount := types.NewMoney(float64(30))
					for _, v := range tc.mockIncome {
						income = income.Add(v.IncomeDetail.TotalWage)
					}
					for _, v := range tc.mockInstall {
						instDaily = instDaily.Add(v.DailyAmount)
					}
					expected := income.Div(dayAmount).Mul(types.NewMoney(tc.mockDriver.GetDSCR(now) / 100)).Sub(instDaily).Float64()
					assert.Equal(t, expected, pp)
					assert.Equal(t, tc.expectedResult[1], maxExposure)
					assert.Equal(t, int(tc.expectedResult[2]), maxTenor)
				}
			})
		}
	})
}

func Test_calculatePurchasingPower(t *testing.T) {

	t.Run("valid purchasing power", func(t *testing.T) {

		e := &EGS{
			cfg: EGSCfg{
				IncomeBackwardDayAmount: 30,
			},
		}

		income := []model.IncomeDailySummary{
			{
				IncomeDetail: model.IncomeDailyDetail{
					TotalWage: 9000,
				},
			},
			{
				IncomeDetail: model.IncomeDailyDetail{
					TotalWage: 9000,
				},
			},
		}

		inst := []model.Installment{
			{
				DailyAmount: 50,
			},
		}

		// 18000 / 30 * 0.20 - 50
		r := e.calculatePurchasingPower(income, inst, 20)
		assert.Equal(t, float64(70), r)
	})
}

func Test_getTransactionDate(t *testing.T) {
	t.Run("valid getStartTransactionDate", func(t *testing.T) {
		date := time.Date(2023, 1, 24, 22, 0, 0, 0, time.UTC)
		e := &EGS{
			cfg: EGSCfg{
				IncomeBackwardDayAmount: 30,
			},
		}

		actual := e.getStartTransactionDate(date, nil)
		assert.Equal(t, 2022, actual.Year())
		assert.Equal(t, time.Month(12), actual.Month())
		assert.Equal(t, 25, actual.Day())
		assert.Equal(t, 0, actual.Hour())
		assert.Equal(t, 0, actual.Minute())
		assert.Equal(t, 0, actual.Second())
	})

	t.Run("valid getEndTransactionDate", func(t *testing.T) {
		date := time.Date(2023, 1, 24, 22, 0, 0, 0, time.UTC)
		e := &EGS{}
		actual := e.getEndTransactionDate(date)
		assert.Equal(t, 2023, actual.Year())
		assert.Equal(t, time.Month(1), actual.Month())
		assert.Equal(t, 23, actual.Day())
		assert.Equal(t, 23, actual.Hour())
		assert.Equal(t, 59, actual.Minute())
		assert.Equal(t, 59, actual.Second())
	})
}

func Test_PurchasingPowerDependencyPaymentData(t *testing.T) {
	t.Run("happy flow", func(tt *testing.T) {
		currentTime := time.Date(2024, 1, 15, 12, 0, 0, 0, time.Local)
		timeutils.FreezeWithTime(currentTime.UnixMilli())
		defer timeutils.Unfreeze()

		svc, deps, finish := NewEGSTest(tt)
		defer finish()

		ctx := context.Background()

		instQuery := persistence.BuildInstallmentQuery().
			WithDriverID("MOCKED_DRIVER_ID").
			WithStatuses([]model.InstallmentStatus{model.InstallmentActive, model.InstallmentInactive, model.InstallmentPending}).
			WithoutSKU(nil).
			WithoutPaymentType(model.InstallmentPaymentTypeOneTime)

		deps.instRepo.EXPECT().FindWithQueryAndSort(ctx, gomock.Any(), 0, 0, gomock.Any()).
			DoAndReturn(func(ctx context.Context, query repository.InstallmentQuery, skip, limit int, opts ...repository.Option) ([]model.Installment, error) {
				require.Equal(tt, instQuery, query)

				return []model.Installment{
					{
						DailyAmount:       types.NewMoney(4.4),
						InstallmentAmount: types.NewMoney(90),
						InitialAmount:     types.NewMoney(100),
					},
					{
						DailyAmount:       types.NewMoney(5.5),
						InstallmentAmount: types.NewMoney(80.5),
						InitialAmount:     types.NewMoney(100),
					},
					{
						DailyAmount:       types.NewMoney(6.6),
						InstallmentAmount: types.NewMoney(70.4),
						InitialAmount:     types.NewMoney(100),
					},
					{
						DailyAmount:       types.NewMoney(7.7),
						InstallmentAmount: types.NewMoney(49.5),
						InitialAmount:     types.NewMoney(50),
					},
				}, nil
			})

		incomeQuery := persistence.BuildIncomeDailySummaryQuery().
			WithDriverID("MOCKED_DRIVER_ID").
			WithFromInclusive(timeutil.DateTruncate(currentTime.AddDate(0, 0, -15))).
			WithToEqExclusive(timeutil.DateCeiling(currentTime.AddDate(0, 0, -1)))

		deps.incomeRepo.EXPECT().Query(ctx, gomock.Any(), 0, 0, []string{"-date"}, gomock.Any()).
			DoAndReturn(func(ctx context.Context, query repository.IncomeDailySummaryQuery, skip, limit int, sort []string, opts ...repository.Option) ([]model.IncomeDailySummary, error) {
				require.Equal(tt, incomeQuery, query)

				return []model.IncomeDailySummary{
					{
						IncomeDetail: model.IncomeDailyDetail{
							TotalWage: types.NewMoney(1.1),
						},
					},
					{
						IncomeDetail: model.IncomeDailyDetail{
							TotalWage: types.NewMoney(2.2),
						},
					},
					{
						IncomeDetail: model.IncomeDailyDetail{
							TotalWage: types.NewMoney(3.3),
						},
					},
				}, nil
			})

		incomePeriod := 15
		totalDailyIncome, totalDailyAmount, totalOverdueAmount, err := svc.PurchasingPowerDependencyPaymentData(ctx, "MOCKED_DRIVER_ID", &incomePeriod)

		require.NoError(tt, err)
		require.Equal(tt, types.Money(6.6), totalDailyIncome)
		require.Equal(tt, types.Money(24.2), totalDailyAmount)
		require.Equal(tt, types.Money(59.6), totalOverdueAmount)
	})

	t.Run("unable to get data from installment repository", func(tt *testing.T) {
		currentTime := time.Date(2024, 1, 15, 12, 0, 0, 0, time.Local)
		timeutils.FreezeWithTime(currentTime.UnixMilli())
		defer timeutils.Unfreeze()

		svc, deps, finish := NewEGSTest(tt)
		defer finish()

		ctx := context.Background()

		instQuery := persistence.BuildInstallmentQuery().
			WithDriverID("MOCKED_DRIVER_ID").
			WithStatuses([]model.InstallmentStatus{model.InstallmentActive, model.InstallmentInactive, model.InstallmentPending}).
			WithoutSKU(nil).
			WithoutPaymentType(model.InstallmentPaymentTypeOneTime)

		returnedError := errors.New("INSTALLMENT_REPO_ERROR")
		deps.instRepo.EXPECT().FindWithQueryAndSort(ctx, gomock.Any(), 0, 0, gomock.Any()).
			DoAndReturn(func(ctx context.Context, query repository.InstallmentQuery, skip, limit int, opts ...repository.Option) ([]model.Installment, error) {
				require.Equal(tt, instQuery, query)

				return []model.Installment{}, returnedError
			})

		totalDailyIncome, totalDailyAmount, totalOverdueAmount, err := svc.PurchasingPowerDependencyPaymentData(ctx, "MOCKED_DRIVER_ID", nil)

		require.ErrorIs(tt, err, returnedError)
		require.Equal(tt, types.Money(0), totalDailyIncome)
		require.Equal(tt, types.Money(0), totalDailyAmount)
		require.Equal(tt, types.Money(0), totalOverdueAmount)
	})

	t.Run("unable to get data from income summary repository", func(tt *testing.T) {
		currentTime := time.Date(2024, 1, 15, 12, 0, 0, 0, time.Local)
		timeutils.FreezeWithTime(currentTime.UnixMilli())
		defer timeutils.Unfreeze()

		svc, deps, finish := NewEGSTest(tt)
		defer finish()

		ctx := context.Background()

		instQuery := persistence.BuildInstallmentQuery().
			WithDriverID("MOCKED_DRIVER_ID").
			WithStatuses([]model.InstallmentStatus{model.InstallmentActive, model.InstallmentInactive, model.InstallmentPending}).
			WithoutSKU(nil).
			WithoutPaymentType(model.InstallmentPaymentTypeOneTime)

		deps.instRepo.EXPECT().FindWithQueryAndSort(ctx, gomock.Any(), 0, 0, gomock.Any()).
			DoAndReturn(func(ctx context.Context, query repository.InstallmentQuery, skip, limit int, opts ...repository.Option) ([]model.Installment, error) {
				require.Equal(tt, instQuery, query)

				return []model.Installment{}, nil
			})

		incomeQuery := persistence.BuildIncomeDailySummaryQuery().
			WithDriverID("MOCKED_DRIVER_ID").
			WithFromInclusive(timeutil.DateTruncate(currentTime.AddDate(0, 0, -30))).
			WithToEqExclusive(timeutil.DateCeiling(currentTime.AddDate(0, 0, -1)))

		returnedError := errors.New("INCOME_SUMMARY_REPO_ERROR")
		deps.incomeRepo.EXPECT().Query(ctx, gomock.Any(), 0, 0, []string{"-date"}, gomock.Any()).
			DoAndReturn(func(ctx context.Context, query repository.IncomeDailySummaryQuery, skip, limit int, sort []string, opts ...repository.Option) ([]model.IncomeDailySummary, error) {
				require.Equal(tt, incomeQuery, query)

				return []model.IncomeDailySummary{}, returnedError
			})

		totalDailyIncome, totalDailyAmount, totalOverdueAmount, err := svc.PurchasingPowerDependencyPaymentData(ctx, "MOCKED_DRIVER_ID", nil)

		require.ErrorIs(tt, err, returnedError)
		require.Equal(tt, types.Money(0), totalDailyIncome)
		require.Equal(tt, types.Money(0), totalDailyAmount)
		require.Equal(tt, types.Money(0), totalOverdueAmount)
	})
}

func Test_PurchasingPowerDependencyFinancialRiskData(t *testing.T) {
	t.Run("happy flow", func(tt *testing.T) {
		currentTime := time.Date(2024, 1, 15, 12, 0, 0, 0, time.Local)
		timeutils.FreezeWithTime(currentTime.UnixMilli())
		defer timeutils.Unfreeze()

		svc, deps, finish := NewEGSTest(tt)
		defer finish()

		ctx := context.Background()

		deps.driverRepo.EXPECT().FindDriverID(ctx, "MOCKED_DRIVER_ID", gomock.Any()).
			Return(&model.Driver{
				DSCR:        1.0,
				MaxTenor:    2,
				MaxExposure: 3.0,
				DSCREffectiveDate: types.Period{
					StartAt: time.Time{},
					EndAt:   time.Time{},
				},
			}, nil)

		driverDSCR, driverMaxTenor, driverMaxExposure, err := svc.PurchasingPowerDependencyFinancialRiskData(ctx, "MOCKED_DRIVER_ID", currentTime)

		require.NoError(tt, err)
		require.Equal(tt, 1.0, driverDSCR)
		require.Equal(tt, 2, driverMaxTenor)
		require.Equal(tt, 3.0, driverMaxExposure)
	})

	t.Run("driver has expired DSCR period", func(tt *testing.T) {
		currentTime := time.Date(2024, 1, 15, 12, 0, 0, 0, time.Local)
		timeutils.FreezeWithTime(currentTime.UnixMilli())
		defer timeutils.Unfreeze()

		svc, deps, finish := NewEGSTest(tt)
		defer finish()

		ctx := context.Background()

		deps.driverRepo.EXPECT().FindDriverID(ctx, "MOCKED_DRIVER_ID", gomock.Any()).
			Return(&model.Driver{
				DSCR:        1111.0,
				MaxTenor:    10,
				MaxExposure: 5.5,
				DSCREffectiveDate: types.Period{
					StartAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local),
					EndAt:   time.Date(2024, 1, 15, 0, 0, 0, 0, time.Local),
				},
			}, nil)

		driverDSCR, driverMaxTenor, driverMaxExposure, err := svc.PurchasingPowerDependencyFinancialRiskData(ctx, "MOCKED_DRIVER_ID", currentTime)

		require.NoError(tt, err)
		require.Equal(tt, 0.0, driverDSCR)
		require.Equal(tt, 10, driverMaxTenor)
		require.Equal(tt, 5.5, driverMaxExposure)
	})

	t.Run("driver has active DSCR period", func(tt *testing.T) {
		currentTime := time.Date(2024, 1, 15, 12, 0, 0, 0, time.Local)
		timeutils.FreezeWithTime(currentTime.UnixMilli())
		defer timeutils.Unfreeze()

		svc, deps, finish := NewEGSTest(tt)
		defer finish()

		ctx := context.Background()

		deps.driverRepo.EXPECT().FindDriverID(ctx, "MOCKED_DRIVER_ID", gomock.Any()).
			Return(&model.Driver{
				DSCR:        1111.0,
				MaxTenor:    10,
				MaxExposure: 5.5,
				DSCREffectiveDate: types.Period{
					StartAt: time.Date(2024, 1, 1, 0, 0, 0, 0, time.Local),
					EndAt:   time.Date(2024, 1, 30, 0, 0, 0, 0, time.Local),
				},
			}, nil)

		driverDSCR, driverMaxTenor, driverMaxExposure, err := svc.PurchasingPowerDependencyFinancialRiskData(ctx, "MOCKED_DRIVER_ID", currentTime)

		require.NoError(tt, err)
		require.Equal(tt, 1111.0, driverDSCR)
		require.Equal(tt, 10, driverMaxTenor)
		require.Equal(tt, 5.5, driverMaxExposure)
	})

	t.Run("happy flow", func(tt *testing.T) {
		currentTime := time.Date(2024, 1, 15, 12, 0, 0, 0, time.Local)
		timeutils.FreezeWithTime(currentTime.UnixMilli())
		defer timeutils.Unfreeze()

		svc, deps, finish := NewEGSTest(tt)
		defer finish()

		ctx := context.Background()

		returnedError := errors.New("DRIVER_REPO_ERROR")
		deps.driverRepo.EXPECT().FindDriverID(ctx, "MOCKED_DRIVER_ID", gomock.Any()).
			Return(nil, returnedError)

		driverDSCR, driverMaxTenor, driverMaxExposure, err := svc.PurchasingPowerDependencyFinancialRiskData(ctx, "MOCKED_DRIVER_ID", currentTime)

		require.ErrorIs(tt, err, returnedError)
		require.Equal(tt, 0.0, driverDSCR)
		require.Equal(tt, 0, driverMaxTenor)
		require.Equal(tt, 0.0, driverMaxExposure)
	})

}

func TestEGS_PurchasingPowerWithEGSFlagEnabled(t *testing.T) {
	t.Run("get purchasing power from egs successfully", func(tt *testing.T) {
		e, deps, _ := NewEGSTest(tt)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultTrue(gomock.Any(), "fleet.driver.new-purchasing-power-calculation.kill-switch").
			Return(true).Times(1)

		egsReq := &egsv1.GetPurchasingPowerRequest{
			DriverId: "driverID1",
			BatchId:  "batchID1",
		}
		deps.egsService.EXPECT().GetPurchasingPower(gomock.Any(), egsReq).
			Return(&egsv1.GetPurchasingPowerResponse{
				PurchasingPower: types.NewMoney(5.5).ToGRPCDecimalPtr(),
				FinancialRisk: &egsv1.FinancialRisk{
					MaxTenor:    500,
					MaxExposure: types.NewMoney(106.89).ToGRPCDecimalPtr(),
				},
			}, nil).Times(1)

		pp, mep, mt, err := e.PurchasingPower(context.Background(), "driverID1", "batchID1", time.Time{})
		assert.NoError(tt, err)
		assert.Equal(tt, float64(5.5), pp)
		assert.Equal(tt, float64(106.89), mep)
		assert.Equal(tt, int(500.0), mt)
	})

	t.Run("failed to get purchasing power from egs", func(tt *testing.T) {
		e, deps, _ := NewEGSTest(tt)

		deps.featureFlagService.EXPECT().
			IsEnabledWithDefaultTrue(gomock.Any(), "fleet.driver.new-purchasing-power-calculation.kill-switch").
			Return(true).Times(1)

		egsReq := &egsv1.GetPurchasingPowerRequest{
			DriverId: "driverID1",
			BatchId:  "batchID1",
		}

		returnedError := errors.New("something wrong")
		deps.egsService.EXPECT().GetPurchasingPower(gomock.Any(), egsReq).
			Return(nil, returnedError).Times(1)

		pp, mep, mt, err := e.PurchasingPower(context.Background(), "driverID1", "batchID1", time.Time{})
		assert.ErrorIs(tt, err, returnedError)
		assert.Equal(tt, float64(0), pp)
		assert.Equal(tt, float64(0), mep)
		assert.Equal(tt, int(0), mt)
	})
}

type egsDeps struct {
	cfg                EGSCfg
	instRepo           *mock_repository.MockInstallmentRepository
	incomeRepo         *mock_repository.MockIncomeDailySummaryRepository
	driverRepo         *mock_repository.MockDriverRepository
	egsService         *mock_grpc.MockEGSServiceClient
	featureFlagService *mock_featureflag.MockService
}

func NewEGSTest(t *testing.T) (*EGS, *egsDeps, func()) {

	ctrl := gomock.NewController(t)

	instRepo := mock_repository.NewMockInstallmentRepository(ctrl)
	incomeRepo := mock_repository.NewMockIncomeDailySummaryRepository(ctrl)
	driverRepo := mock_repository.NewMockDriverRepository(ctrl)
	mockEgsService := mock_grpc.NewMockEGSServiceClient(ctrl)
	mockFeatureFlagService := mock_featureflag.NewMockService(ctrl)

	e := ProvideEGSService(
		instRepo,
		incomeRepo,
		driverRepo,
		mockEgsService,
		mockFeatureFlagService,
		EGSCfg{
			IncomeBackwardDayAmount:      30,
			FinancialRiskDefaultExposure: 50000,
			FinancialRiskDefaultTenor:    180,
		},
	)
	ed := &egsDeps{
		cfg:                EGSCfg{},
		instRepo:           instRepo,
		incomeRepo:         incomeRepo,
		driverRepo:         driverRepo,
		egsService:         mockEgsService,
		featureFlagService: mockFeatureFlagService,
	}

	return e, ed, func() {
		ctrl.Finish()
	}
}
