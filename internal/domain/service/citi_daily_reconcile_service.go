package service

import (
	"context"
	"fmt"
	"time"

	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/txdiff"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fpresult"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type citiDailyReconcileDiff = fpresult.T[*txdiff.BoolFlagDiff[CitiTransactionToCheck, CitiTopupEODTransaction]]

type CitiTransactionDiffsResult struct {
	period          citiDailyReconcilePeriod
	masterEOD       []CitiTopupEODTransaction
	intraDiff       citiDailyReconcileDiff
	topupReportDiff citiDailyReconcileDiff
	actualDiff      citiDailyReconcileDiff
	processingAt    time.Time
}

func (r CitiTransactionDiffsResult) Period() (time.Time, time.Time) {
	return r.period.from, r.period.to
}

func (r CitiTransactionDiffsResult) ProcessingAt() time.Time {
	return r.processingAt
}

func (r CitiTransactionDiffsResult) IsSuccess() bool {
	eodCount := r.EODCount()
	return eodCount == r.ActualCount() &&
		eodCount == r.TopupCount() &&
		eodCount == r.INTRACount() &&
		!r.actualDiff.Value().HasUnseen()
}

func (r CitiTransactionDiffsResult) EODCount() fpresult.T[int] {
	return fpresult.Value(len(r.masterEOD))
}

func (r CitiTransactionDiffsResult) ActualUnseenCount() fpresult.T[int] {
	return fpresult.Fmap(func(diff *txdiff.BoolFlagDiff[CitiTransactionToCheck, CitiTopupEODTransaction]) (count int) {
		for range diff.IterateUnseen() {
			count++
		}
		return
	})(r.actualDiff)
}

func (r CitiTransactionDiffsResult) ActualCount() fpresult.T[int] {
	return fpresult.Fmap(r.countDiff)(r.actualDiff)
}

func (r CitiTransactionDiffsResult) TopupCount() fpresult.T[int] {
	return fpresult.Fmap(r.countDiff)(r.topupReportDiff)
}

func (r CitiTransactionDiffsResult) INTRACount() fpresult.T[int] {
	return fpresult.Fmap(r.countDiff)(r.intraDiff)
}

func (r CitiTransactionDiffsResult) countDiff(diff *txdiff.BoolFlagDiff[CitiTransactionToCheck, CitiTopupEODTransaction]) (count int) {
	diff.Visit(func(i int, exists bool) (stop bool) {
		if exists {
			count += 1
		}
		return false
	})
	return
}

func (r CitiTransactionDiffsResult) diffIdExists(diff citiDailyReconcileDiff, idx int) bool {
	if !diff.IsOk() {
		return false
	}
	return diff.Value().IsExisting(idx)
}

func (r CitiTransactionDiffsResult) ToReport() (CitiDailyReconcileReportData, error) {
	if err := r.assertTopupReportDiffAndActualDiffAvailable(); err != nil {
		return CitiDailyReconcileReportData{}, err
	}

	rows := make([]CitiDailyReconcileReportRow, 0)
	rows = r.addMissingRowsToReport(rows)
	rows = r.addUnseenRowsToReport(rows)

	return CitiDailyReconcileReportData{
		ProcessingAt:         r.processingAt,
		ReconcileDate:        r.period.from.Format(time.DateOnly),
		PeriodFrom:           r.period.from,
		PeriodTo:             r.period.to,
		EODCount:             len(r.masterEOD),
		ActualCount:          r.ActualCount(),
		ActualUnmatchedCount: r.ActualUnseenCount(),
		INTRACount:           r.INTRACount(),
		TopupCount:           r.TopupCount(),
		IsPassed:             r.IsSuccess(),
		Rows:                 rows,
	}, nil
}

func (r CitiTransactionDiffsResult) addUnseenRowsToReport(rows []CitiDailyReconcileReportRow) []CitiDailyReconcileReportRow {
	for tx := range r.actualDiff.Value().IterateUnseen() {
		rows = append(rows, CitiDailyReconcileReportRow{
			IsUnseen: true,
			Unseen: CitiDailyReconcileReportUnseenRow{
				TransactionId:        tx.TransactionId,
				TransactionRefId:     tx.TransactionRefId,
				TransactionCreatedAt: tx.TransactionCreatedAt.Format("02-Jan-06"),
				TransactionAmount:    tx.Amount,
				DriverId:             tx.DriverId,
			},
			MissingFromTransactions: !r.diffHasId(r.actualDiff, tx.TransactionId),
			MissingFromIntra:        !r.diffHasId(r.intraDiff, tx.TransactionId),
			MissingFromTopupReport:  !r.diffHasId(r.topupReportDiff, tx.TransactionId),
		})
	}
	return rows
}

func (r CitiTransactionDiffsResult) addMissingRowsToReport(rows []CitiDailyReconcileReportRow) []CitiDailyReconcileReportRow {
	for i := 0; i < len(r.masterEOD); i++ {
		if !r.diffIdExists(r.actualDiff, i) || !r.diffIdExists(r.topupReportDiff, i) {
			tx := r.masterEOD[i]

			rows = append(rows, CitiDailyReconcileReportRow{
				IsUnseen: false,
				Missing: CitiDailyReconcileReportMissingRow{
					UETR:                tx.UETR,
					TransCompletionDate: tx.TransCompletionDate.Format("02-Jan-06"),
					CitibankRefNumber:   tx.CitibankRefNumber,
					TransactionAmount:   tx.TransactionAmount,
					PostingRefNumber:    tx.PostingRefNumber,
				},
				MissingFromTransactions: !r.diffIdExists(r.actualDiff, i),
				MissingFromIntra:        !r.diffIdExists(r.intraDiff, i),
				MissingFromTopupReport:  !r.diffIdExists(r.topupReportDiff, i),
			})
		}
	}
	return rows
}

func (r CitiTransactionDiffsResult) assertTopupReportDiffAndActualDiffAvailable() error {
	if !r.topupReportDiff.IsOk() {
		return fmt.Errorf("CitiTransactionDiffsResult cannot calculate topupReport diff: %w", r.topupReportDiff.Error())
	}
	if !r.actualDiff.IsOk() {
		return fmt.Errorf("CitiTransactionDiffsResult cannot calculate actualTransactions diff: %w", r.actualDiff.Error())
	}
	if !r.intraDiff.IsOk() {
		return fmt.Errorf("CitiTransactionDiffsResult cannot calculate intra diff: %w", r.intraDiff.Error())
	}
	return nil
}

func (r CitiTransactionDiffsResult) IterateMissingEODsFromTopupReport() (<-chan fpresult.T[CitiTopupEODTransaction], error) {
	diff, err := r.topupReportDiff.Return()
	if err != nil {
		return nil, err
	}
	return diff.IterateMissing(), nil
}

func (r CitiTransactionDiffsResult) diffHasId(diff citiDailyReconcileDiff, id string) bool {
	if !diff.IsOk() {
		return false
	}
	return diff.Value().HasId(id)
}

func (r CitiTransactionDiffsResult) HasMissingTopupReport() bool {
	if r.topupReportDiff.IsOk() {
		return r.topupReportDiff.Value().HasMissing()
	}
	return false
}

func CalculateCitiTransactionDiffsFunc(
	ctx context.Context, now time.Time,
	fetchINTRA func(time.Time, time.Time) func(chan<- CitiTransactionToCheck) error,
	fetchTopupReport func(time.Time, time.Time) func(chan<- CitiTransactionToCheck) error,
	fetchActual func(time.Time, time.Time) func(chan<- CitiTransactionToCheck) error,
) func(masterEOD []CitiTopupEODTransaction) (CitiTransactionDiffsResult, error) {
	return func(masterEOD []CitiTopupEODTransaction) (CitiTransactionDiffsResult, error) {
		collection, err := txdiff.NewIndexedSliceMasterCollection(masterEOD)
		if err != nil {
			return CitiTransactionDiffsResult{}, err
		}

		period := newCitiDailyReconcileComparePeriod(now)
		initDiff := txdiff.NewBoolFlagDiff[CitiTransactionToCheck, CitiTopupEODTransaction](collection)

		intraStream := txdiff.NewTxStream(10, fetchINTRA(period.from, period.to))
		intraDiff := txdiff.GoCalculateDiffFromStream(ctx, collection, initDiff, intraStream)

		topupReportStream := txdiff.NewTxStream(10, fetchTopupReport(period.from, period.to))
		topupReportDiff := txdiff.GoCalculateDiffFromStream(ctx, collection, initDiff, topupReportStream)

		actualStream := txdiff.NewTxStream(10, fetchActual(period.from, period.to))
		actualDiff := txdiff.GoCalculateDiffFromStream(ctx, collection, initDiff, actualStream)

		return CitiTransactionDiffsResult{
			masterEOD:       masterEOD,
			intraDiff:       <-intraDiff,
			topupReportDiff: <-topupReportDiff,
			actualDiff:      <-actualDiff,
			period:          period,
			processingAt:    now,
		}, nil
	}
}

func newCitiDailyReconcileComparePeriod(now time.Time) citiDailyReconcilePeriod {
	now = now.In(timeutils.BangkokLocation())

	lastMidnight := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, timeutils.BangkokLocation())
	yesterdayMidnight := lastMidnight.Add(-24 * time.Hour)

	return citiDailyReconcilePeriod{
		from: yesterdayMidnight,
		to:   lastMidnight.Add(-1 * time.Second),
	}
}

type citiDailyReconcilePeriod struct {
	from time.Time
	to   time.Time
}

type CitiTransactionToCheck struct {
	TransactionId        string
	TransactionRefId     string
	TransactionCreatedAt time.Time
	Amount               float64
	DriverId             string
}

func (t CitiTransactionToCheck) Id() string {
	return t.TransactionId
}

type CitiTopupEODTransaction struct {
	UETR                string
	TransCompletionDate time.Time
	CitibankRefNumber   string
	TransactionAmount   float64
	PostingRefNumber    string
	Raw                 model.CitiRawTransactionEntry
	FromFile            string
}

func (t CitiTopupEODTransaction) Id() string {
	return t.UETR
}

type CitiDailyReconcileReportData struct {
	ProcessingAt         time.Time
	ReconcileDate        string
	Rows                 []CitiDailyReconcileReportRow
	PeriodFrom           time.Time
	PeriodTo             time.Time
	EODCount             int
	ActualCount          fpresult.T[int]
	ActualUnmatchedCount fpresult.T[int]
	TopupCount           fpresult.T[int]
	INTRACount           fpresult.T[int]
	IsPassed             bool
}

type CitiDailyReconcileReportRow struct {
	IsUnseen                bool
	Missing                 CitiDailyReconcileReportMissingRow
	Unseen                  CitiDailyReconcileReportUnseenRow
	MissingFromTransactions bool
	MissingFromIntra        bool
	MissingFromTopupReport  bool
}

type CitiDailyReconcileReportMissingRow struct {
	UETR                string
	TransCompletionDate string
	CitibankRefNumber   string
	TransactionAmount   float64
	PostingRefNumber    string
}

type CitiDailyReconcileReportUnseenRow struct {
	TransactionId        string
	TransactionRefId     string
	TransactionCreatedAt string
	TransactionAmount    float64
	DriverId             string
}
