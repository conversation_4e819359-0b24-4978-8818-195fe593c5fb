package service_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	common "git.wndv.co/go/proto/lineman/shared/common/v1"
	grpcTranslation "git.wndv.co/go/proto/lineman/translation/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/translationservice/mock_translation"
)

type translationServiceDeps struct {
	translationClient *mock_translation.MockTranslationServiceClient
}

func newTranslationService(ctrl *gomock.Controller) (service.TranslationService, *translationServiceDeps) {
	translationClient := mock_translation.NewMockTranslationServiceClient(ctrl)

	deps := &translationServiceDeps{
		translationClient: translationClient,
	}

	return service.ProvideTranslationServiceImpl(translationClient), deps
}

func Test_translationServiceImpl_Translate(t *testing.T) {
	t.Run("should translate success", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		srv, deps := newTranslationService(ctrl)
		deps.translationClient.EXPECT().Translate(gomock.Any(), gomock.Any()).Return(&grpcTranslation.TranslateResponse{
			Result: []*grpcTranslation.TranslateResponse_TranslateResult{
				{
					Message: "สวัสดี",
				},
			},
		}, nil)
		translate := srv.Translate(context.Background(), "hello", common.TranslationLanguage_TRANSLATION_LANGUAGE_THAI, common.TranslationLanguage_TRANSLATION_LANGUAGE_ENGLISH)
		require.Equal(tt, "สวัสดี", translate)
	})

	t.Run("translate error return original message", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		srv, deps := newTranslationService(ctrl)
		deps.translationClient.EXPECT().Translate(gomock.Any(), gomock.Any()).Return(&grpcTranslation.TranslateResponse{
			Result: []*grpcTranslation.TranslateResponse_TranslateResult{
				{
					Message: "สวัสดี",
				},
			},
		}, errors.New("translate error"))
		translate := srv.Translate(context.Background(), "hello", common.TranslationLanguage_TRANSLATION_LANGUAGE_THAI, common.TranslationLanguage_TRANSLATION_LANGUAGE_ENGLISH)
		require.Equal(tt, "hello", translate)
	})
}

func Test_translationServiceImpl_TranslateENToTH(t *testing.T) {
	t.Run("should translate success", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		srv, deps := newTranslationService(ctrl)
		deps.translationClient.EXPECT().Translate(gomock.Any(), gomock.Any()).Return(&grpcTranslation.TranslateResponse{
			Result: []*grpcTranslation.TranslateResponse_TranslateResult{
				{
					Message: "สวัสดี",
				},
			},
		}, nil)
		translate := srv.TranslateENToTH(context.Background(), "hello")
		require.Equal(tt, "สวัสดี", translate)
	})

	t.Run("translate error return original message", func(tt *testing.T) {
		ctrl := gomock.NewController(tt)
		defer ctrl.Finish()
		srv, deps := newTranslationService(ctrl)
		deps.translationClient.EXPECT().Translate(gomock.Any(), gomock.Any()).Return(&grpcTranslation.TranslateResponse{
			Result: []*grpcTranslation.TranslateResponse_TranslateResult{
				{
					Message: "สวัสดี",
				},
			},
		}, errors.New("translate error"))
		translate := srv.TranslateENToTH(context.Background(), "hello")
		require.Equal(tt, "hello", translate)
	})
}
