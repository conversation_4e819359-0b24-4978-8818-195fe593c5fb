package service

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestCitiDailyReconcile_newCitiDailyReconcilePeriod(t *testing.T) {
	t.Run("should return period from yesterday at 0am", func(tt *testing.T) {
		now := time.Date(2023, time.September, 18, 19, 6, 5, 123, timeutils.BangkokLocation())

		period := newCitiDailyReconcileComparePeriod(now)

		assert.Equal(tt, time.Date(2023, time.September, 17, 0, 0, 0, 0, timeutils.BangkokLocation()), period.from)
	})

	t.Run("should return period to one second before today", func(tt *testing.T) {
		now := time.Date(2023, time.September, 18, 19, 6, 5, 123, timeutils.BangkokLocation())

		period := newCitiDailyReconcileComparePeriod(now)

		assert.Equal(tt, time.Date(2023, time.September, 17, 23, 59, 59, 0, timeutils.BangkokLocation()), period.to)
	})

	t.Run("should convert timezone to Bangkok", func(tt *testing.T) {
		now := time.Date(2023, time.September, 18, 23, 6, 5, 123, time.UTC)
		//BKK time is (2023, time.September, 19, 6, 6, 5, 123, ...)

		period := newCitiDailyReconcileComparePeriod(now)

		assert.Equal(tt, time.Date(2023, time.September, 18, 0, 0, 0, 0, timeutils.BangkokLocation()), period.from)
		assert.Equal(tt, time.Date(2023, time.September, 18, 23, 59, 59, 0, timeutils.BangkokLocation()), period.to)
	})
}
