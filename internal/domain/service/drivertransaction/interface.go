package drivertransaction

import (
	"context"

	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

//go:generate go run -mod=mod  go.uber.org/mock/mockgen -destination=./mock_drivertransaction/mock_drivertransaction.go -package=mock_drivertransaction -source=interface.go

type Provider[T any] interface {
	ProvideTransaction(ctx context.Context, order domainModel.Order, focusedOnTopScheme []domainModel.OnTopScheme) (ProviderResult[T], error)
	GetName() string
}

type TransactionProviderSelector[I any, T any] interface {
	GetTransactionProvider(I) (Provider[T], bool)
}

type ExecutionMethod func(ctx context.Context) error

type ProviderResult[T any] struct {
	Result T
}
