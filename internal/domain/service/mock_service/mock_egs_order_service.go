// Code generated by MockGen. DO NOT EDIT.
// Source: ./egs_order_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	gomock "github.com/golang/mock/gomock"
)

// MockEGSOrderService is a mock of EGSOrderService interface.
type MockEGSOrderService struct {
	ctrl     *gomock.Controller
	recorder *MockEGSOrderServiceMockRecorder
}

// MockEGSOrderServiceMockRecorder is the mock recorder for MockEGSOrderService.
type MockEGSOrderServiceMockRecorder struct {
	mock *MockEGSOrderService
}

// NewMockEGSOrderService creates a new mock instance.
func NewMockEGSOrderService(ctrl *gomock.Controller) *MockEGSOrderService {
	mock := &MockEGSOrderService{ctrl: ctrl}
	mock.recorder = &MockEGSOrderServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEGSOrderService) EXPECT() *MockEGSOrderServiceMockRecorder {
	return m.recorder
}

// ConsumeEGSOrderEvent mocks base method.
func (m *MockEGSOrderService) ConsumeEGSOrderEvent(ctx context.Context, event *egsv1.EGSOrderEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConsumeEGSOrderEvent", ctx, event)
	ret0, _ := ret[0].(error)
	return ret0
}

// ConsumeEGSOrderEvent indicates an expected call of ConsumeEGSOrderEvent.
func (mr *MockEGSOrderServiceMockRecorder) ConsumeEGSOrderEvent(ctx, event interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConsumeEGSOrderEvent", reflect.TypeOf((*MockEGSOrderService)(nil).ConsumeEGSOrderEvent), ctx, event)
}

// PublishEGSCreateOrder mocks base method.
func (m *MockEGSOrderService) PublishEGSCreateOrder(ctx context.Context, req *egsv1.CreateOrderRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishEGSCreateOrder", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishEGSCreateOrder indicates an expected call of PublishEGSCreateOrder.
func (mr *MockEGSOrderServiceMockRecorder) PublishEGSCreateOrder(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishEGSCreateOrder", reflect.TypeOf((*MockEGSOrderService)(nil).PublishEGSCreateOrder), ctx, req)
}
