// Code generated by MockGen. DO NOT EDIT.
// Source: ./cancel_order_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	file "git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	gomock "github.com/golang/mock/gomock"
	io "io"
	reflect "reflect"
)

// MockCancelOrderService is a mock of CancelOrderService interface
type MockCancelOrderService struct {
	ctrl     *gomock.Controller
	recorder *MockCancelOrderServiceMockRecorder
}

// MockCancelOrderServiceMockRecorder is the mock recorder for MockCancelOrderService
type MockCancelOrderServiceMockRecorder struct {
	mock *MockCancelOrderService
}

// NewMockCancelOrderService creates a new mock instance
func NewMockCancelOrderService(ctrl *gomock.Controller) *MockCancelOrderService {
	mock := &MockCancelOrderService{ctrl: ctrl}
	mock.recorder = &MockCancelOrderServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockCancelOrderService) EXPECT() *MockCancelOrderServiceMockRecorder {
	return m.recorder
}

// UploadCancelOrderPhoto mocks base method
func (m *MockCancelOrderService) UploadCancelOrderPhoto(ctx context.Context, prefixName, contentType string, content io.Reader) (*file.File, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UploadCancelOrderPhoto", ctx, prefixName, contentType, content)
	ret0, _ := ret[0].(*file.File)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadCancelOrderPhoto indicates an expected call of UploadCancelOrderPhoto
func (mr *MockCancelOrderServiceMockRecorder) UploadCancelOrderPhoto(ctx, prefixName, contentType, content interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadCancelOrderPhoto", reflect.TypeOf((*MockCancelOrderService)(nil).UploadCancelOrderPhoto), ctx, prefixName, contentType, content)
}
