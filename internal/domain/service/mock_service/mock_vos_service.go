// Code generated by MockGen. DO NOT EDIT.
// Source: ./vos_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	io "io"
	reflect "reflect"
	time "time"

	file "git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	service "git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	s3 "github.com/aws/aws-sdk-go/service/s3"
	gomock "github.com/golang/mock/gomock"
)

// MockVOSService is a mock of VOSService interface.
type MockVOSService struct {
	ctrl     *gomock.Controller
	recorder *MockVOSServiceMockRecorder
}

// MockVOSServiceMockRecorder is the mock recorder for MockVOSService.
type MockVOSServiceMockRecorder struct {
	mock *MockVOSService
}

// NewMockVOSService creates a new mock instance.
func NewMockVOSService(ctrl *gomock.Controller) *MockVOSService {
	mock := &MockVOSService{ctrl: ctrl}
	mock.recorder = &MockVOSServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockVOSService) EXPECT() *MockVOSServiceMockRecorder {
	return m.recorder
}

// GetCDNObjectInternal mocks base method.
func (m *MockVOSService) GetCDNObjectInternal(ctx context.Context, key string) (*s3.GetObjectOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCDNObjectInternal", ctx, key)
	ret0, _ := ret[0].(*s3.GetObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCDNObjectInternal indicates an expected call of GetCDNObjectInternal.
func (mr *MockVOSServiceMockRecorder) GetCDNObjectInternal(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCDNObjectInternal", reflect.TypeOf((*MockVOSService)(nil).GetCDNObjectInternal), ctx, key)
}

// GetCDNPreSignedUrl mocks base method.
func (m *MockVOSService) GetCDNPreSignedUrl(ctx context.Context, key string, expiration time.Duration) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCDNPreSignedUrl", ctx, key, expiration)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCDNPreSignedUrl indicates an expected call of GetCDNPreSignedUrl.
func (mr *MockVOSServiceMockRecorder) GetCDNPreSignedUrl(ctx, key, expiration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCDNPreSignedUrl", reflect.TypeOf((*MockVOSService)(nil).GetCDNPreSignedUrl), ctx, key, expiration)
}

// GetConfigBucket mocks base method.
func (m *MockVOSService) GetConfigBucket(ctx context.Context, options ...service.VOSOption) (string, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConfigBucket", varargs...)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigBucket indicates an expected call of GetConfigBucket.
func (mr *MockVOSServiceMockRecorder) GetConfigBucket(ctx interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigBucket", reflect.TypeOf((*MockVOSService)(nil).GetConfigBucket), varargs...)
}

// GetObject mocks base method.
func (m *MockVOSService) GetObject(ctx context.Context, key string, options ...service.VOSOption) (*s3.GetObjectOutput, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, key}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetObject", varargs...)
	ret0, _ := ret[0].(*s3.GetObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *MockVOSServiceMockRecorder) GetObject(ctx, key interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, key}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*MockVOSService)(nil).GetObject), varargs...)
}

// GetObjectInternal mocks base method.
func (m *MockVOSService) GetObjectInternal(ctx context.Context, key string) (*s3.GetObjectOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObjectInternal", ctx, key)
	ret0, _ := ret[0].(*s3.GetObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObjectInternal indicates an expected call of GetObjectInternal.
func (mr *MockVOSServiceMockRecorder) GetObjectInternal(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObjectInternal", reflect.TypeOf((*MockVOSService)(nil).GetObjectInternal), ctx, key)
}

// GetPreSignedUrl mocks base method.
func (m *MockVOSService) GetPreSignedUrl(ctx context.Context, key string, expiration time.Duration) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPreSignedUrl", ctx, key, expiration)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPreSignedUrl indicates an expected call of GetPreSignedUrl.
func (mr *MockVOSServiceMockRecorder) GetPreSignedUrl(ctx, key, expiration interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPreSignedUrl", reflect.TypeOf((*MockVOSService)(nil).GetPreSignedUrl), ctx, key, expiration)
}

// GetSignedUrl mocks base method.
func (m *MockVOSService) GetSignedUrl(ctx context.Context, key string, expiration time.Duration, options ...service.VOSOption) (string, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, key, expiration}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSignedUrl", varargs...)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSignedUrl indicates an expected call of GetSignedUrl.
func (mr *MockVOSServiceMockRecorder) GetSignedUrl(ctx, key, expiration interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, key, expiration}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSignedUrl", reflect.TypeOf((*MockVOSService)(nil).GetSignedUrl), varargs...)
}

// HeadObjectCDN mocks base method.
func (m *MockVOSService) HeadObjectCDN(ctx context.Context, key string) (*s3.HeadObjectOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HeadObjectCDN", ctx, key)
	ret0, _ := ret[0].(*s3.HeadObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HeadObjectCDN indicates an expected call of HeadObjectCDN.
func (mr *MockVOSServiceMockRecorder) HeadObjectCDN(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HeadObjectCDN", reflect.TypeOf((*MockVOSService)(nil).HeadObjectCDN), ctx, key)
}

// HeadObjectInternal mocks base method.
func (m *MockVOSService) HeadObjectInternal(ctx context.Context, key string) (*s3.HeadObjectOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HeadObjectInternal", ctx, key)
	ret0, _ := ret[0].(*s3.HeadObjectOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HeadObjectInternal indicates an expected call of HeadObjectInternal.
func (mr *MockVOSServiceMockRecorder) HeadObjectInternal(ctx, key interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HeadObjectInternal", reflect.TypeOf((*MockVOSService)(nil).HeadObjectInternal), ctx, key)
}

// ListObjectsFromVOSInternal mocks base method.
func (m *MockVOSService) ListObjectsFromVOSInternal(ctx context.Context, prefix string) (*s3.ListObjectsOutput, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjectsFromVOSInternal", ctx, prefix)
	ret0, _ := ret[0].(*s3.ListObjectsOutput)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListObjectsFromVOSInternal indicates an expected call of ListObjectsFromVOSInternal.
func (mr *MockVOSServiceMockRecorder) ListObjectsFromVOSInternal(ctx, prefix interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjectsFromVOSInternal", reflect.TypeOf((*MockVOSService)(nil).ListObjectsFromVOSInternal), ctx, prefix)
}

// UploadTOVOSForInternal mocks base method.
func (m *MockVOSService) UploadTOVOSForInternal(ctx context.Context, prefixName, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, prefixName, contentType, content, path}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UploadTOVOSForInternal", varargs...)
	ret0, _ := ret[0].(*file.File)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadTOVOSForInternal indicates an expected call of UploadTOVOSForInternal.
func (mr *MockVOSServiceMockRecorder) UploadTOVOSForInternal(ctx, prefixName, contentType, content, path interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, prefixName, contentType, content, path}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadTOVOSForInternal", reflect.TypeOf((*MockVOSService)(nil).UploadTOVOSForInternal), varargs...)
}

// UploadToVOS mocks base method.
func (m *MockVOSService) UploadToVOS(ctx context.Context, key string, content io.Reader, options ...service.VOSOption) (*file.File, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, key, content}
	for _, a := range options {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UploadToVOS", varargs...)
	ret0, _ := ret[0].(*file.File)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadToVOS indicates an expected call of UploadToVOS.
func (mr *MockVOSServiceMockRecorder) UploadToVOS(ctx, key, content interface{}, options ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, key, content}, options...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadToVOS", reflect.TypeOf((*MockVOSService)(nil).UploadToVOS), varargs...)
}

// UploadToVOSCDN mocks base method.
func (m *MockVOSService) UploadToVOSCDN(ctx context.Context, prefixName, contentType string, content io.Reader, path string, opts ...file.SaveOption) (*file.File, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, prefixName, contentType, content, path}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UploadToVOSCDN", varargs...)
	ret0, _ := ret[0].(*file.File)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UploadToVOSCDN indicates an expected call of UploadToVOSCDN.
func (mr *MockVOSServiceMockRecorder) UploadToVOSCDN(ctx, prefixName, contentType, content, path interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, prefixName, contentType, content, path}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UploadToVOSCDN", reflect.TypeOf((*MockVOSService)(nil).UploadToVOSCDN), varargs...)
}
