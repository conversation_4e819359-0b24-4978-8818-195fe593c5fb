// Code generated by MockGen. DO NOT EDIT.
// Source: ./distribution_log_manager.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"
	time "time"

	prediction "git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	service "git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	gomock "github.com/golang/mock/gomock"
)

// MockDistributionLogManager is a mock of DistributionLogManager interface.
type MockDistributionLogManager struct {
	ctrl     *gomock.Controller
	recorder *MockDistributionLogManagerMockRecorder
}

// MockDistributionLogManagerMockRecorder is the mock recorder for MockDistributionLogManager.
type MockDistributionLogManagerMockRecorder struct {
	mock *MockDistributionLogManager
}

// NewMockDistributionLogManager creates a new mock instance.
func NewMockDistributionLogManager(ctrl *gomock.Controller) *MockDistributionLogManager {
	mock := &MockDistributionLogManager{ctrl: ctrl}
	mock.recorder = &MockDistributionLogManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDistributionLogManager) EXPECT() *MockDistributionLogManagerMockRecorder {
	return m.recorder
}

// CaptureOptimizeEvent mocks base method.
func (m *MockDistributionLogManager) CaptureOptimizeEvent(ctx context.Context, drivers []prediction.Rider, orders []prediction.Order, optimizationRound model.OptimizationRound, optimizeResponse string, metadata model.DistributionLogMetadata) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CaptureOptimizeEvent", ctx, drivers, orders, optimizationRound, optimizeResponse, metadata)
}

// CaptureOptimizeEvent indicates an expected call of CaptureOptimizeEvent.
func (mr *MockDistributionLogManagerMockRecorder) CaptureOptimizeEvent(ctx, drivers, orders, optimizationRound, optimizeResponse, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CaptureOptimizeEvent", reflect.TypeOf((*MockDistributionLogManager)(nil).CaptureOptimizeEvent), ctx, drivers, orders, optimizationRound, optimizeResponse, metadata)
}

// CapturePostFilterEvent mocks base method.
func (m *MockDistributionLogManager) CapturePostFilterEvent(ctx context.Context, ridersFilterData *model.RidersFilterData, metadata model.DistributionLogMetadata) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CapturePostFilterEvent", ctx, ridersFilterData, metadata)
}

// CapturePostFilterEvent indicates an expected call of CapturePostFilterEvent.
func (mr *MockDistributionLogManagerMockRecorder) CapturePostFilterEvent(ctx, ridersFilterData, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CapturePostFilterEvent", reflect.TypeOf((*MockDistributionLogManager)(nil).CapturePostFilterEvent), ctx, ridersFilterData, metadata)
}

// CapturePostFilterWithTargetOrdersEvent mocks base method.
func (m *MockDistributionLogManager) CapturePostFilterWithTargetOrdersEvent(ctx context.Context, ridersFilterData *model.RidersFilterData, orders []model.Order, metadata model.DistributionLogMetadata) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CapturePostFilterWithTargetOrdersEvent", ctx, ridersFilterData, orders, metadata)
}

// CapturePostFilterWithTargetOrdersEvent indicates an expected call of CapturePostFilterWithTargetOrdersEvent.
func (mr *MockDistributionLogManagerMockRecorder) CapturePostFilterWithTargetOrdersEvent(ctx, ridersFilterData, orders, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CapturePostFilterWithTargetOrdersEvent", reflect.TypeOf((*MockDistributionLogManager)(nil).CapturePostFilterWithTargetOrdersEvent), ctx, ridersFilterData, orders, metadata)
}

// CapturePreFilterEvent mocks base method.
func (m *MockDistributionLogManager) CapturePreFilterEvent(ctx context.Context, ridersFilterData *model.RidersFilterData, metadata model.DistributionLogMetadata) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CapturePreFilterEvent", ctx, ridersFilterData, metadata)
}

// CapturePreFilterEvent indicates an expected call of CapturePreFilterEvent.
func (mr *MockDistributionLogManagerMockRecorder) CapturePreFilterEvent(ctx, ridersFilterData, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CapturePreFilterEvent", reflect.TypeOf((*MockDistributionLogManager)(nil).CapturePreFilterEvent), ctx, ridersFilterData, metadata)
}

// CapturePreFilterWithTargetOrdersEvent mocks base method.
func (m *MockDistributionLogManager) CapturePreFilterWithTargetOrdersEvent(ctx context.Context, ridersFilterData *model.RidersFilterData, orders []model.Order, metadata model.DistributionLogMetadata) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CapturePreFilterWithTargetOrdersEvent", ctx, ridersFilterData, orders, metadata)
}

// CapturePreFilterWithTargetOrdersEvent indicates an expected call of CapturePreFilterWithTargetOrdersEvent.
func (mr *MockDistributionLogManagerMockRecorder) CapturePreFilterWithTargetOrdersEvent(ctx, ridersFilterData, orders, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CapturePreFilterWithTargetOrdersEvent", reflect.TypeOf((*MockDistributionLogManager)(nil).CapturePreFilterWithTargetOrdersEvent), ctx, ridersFilterData, orders, metadata)
}

// CapturePreOptimizeFilterEvent mocks base method.
func (m *MockDistributionLogManager) CapturePreOptimizeFilterEvent(ctx context.Context, ridersFilterData *model.RidersFilterData, optimizationRound model.OptimizationRound, metadata model.DistributionLogMetadata) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CapturePreOptimizeFilterEvent", ctx, ridersFilterData, optimizationRound, metadata)
}

// CapturePreOptimizeFilterEvent indicates an expected call of CapturePreOptimizeFilterEvent.
func (mr *MockDistributionLogManagerMockRecorder) CapturePreOptimizeFilterEvent(ctx, ridersFilterData, optimizationRound, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CapturePreOptimizeFilterEvent", reflect.TypeOf((*MockDistributionLogManager)(nil).CapturePreOptimizeFilterEvent), ctx, ridersFilterData, optimizationRound, metadata)
}

// CaptureSearchEvent mocks base method.
func (m *MockDistributionLogManager) CaptureSearchEvent(ctx context.Context, drivers []service.DriverWithLocation, orders []model.Order, startedAt time.Time, metadata model.DistributionLogMetadata) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CaptureSearchEvent", ctx, drivers, orders, startedAt, metadata)
}

// CaptureSearchEvent indicates an expected call of CaptureSearchEvent.
func (mr *MockDistributionLogManagerMockRecorder) CaptureSearchEvent(ctx, drivers, orders, startedAt, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CaptureSearchEvent", reflect.TypeOf((*MockDistributionLogManager)(nil).CaptureSearchEvent), ctx, drivers, orders, startedAt, metadata)
}
