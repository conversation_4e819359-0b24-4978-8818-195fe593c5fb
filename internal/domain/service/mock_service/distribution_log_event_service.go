// Code generated by MockGen. DO NOT EDIT.
// Source: ./distribution_log_event_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockDistributionLogEventService is a mock of DistributionLogEventService interface.
type MockDistributionLogEventService struct {
	ctrl     *gomock.Controller
	recorder *MockDistributionLogEventServiceMockRecorder
}

// MockDistributionLogEventServiceMockRecorder is the mock recorder for MockDistributionLogEventService.
type MockDistributionLogEventServiceMockRecorder struct {
	mock *MockDistributionLogEventService
}

// NewMockDistributionLogEventService creates a new mock instance.
func NewMockDistributionLogEventService(ctrl *gomock.Controller) *MockDistributionLogEventService {
	mock := &MockDistributionLogEventService{ctrl: ctrl}
	mock.recorder = &MockDistributionLogEventServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDistributionLogEventService) EXPECT() *MockDistributionLogEventServiceMockRecorder {
	return m.recorder
}

// PublishFilterEvent mocks base method.
func (m *MockDistributionLogEventService) PublishFilterEvent(ctx context.Context, req model.DistributionLogFilterEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishFilterEvent", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishFilterEvent indicates an expected call of PublishFilterEvent.
func (mr *MockDistributionLogEventServiceMockRecorder) PublishFilterEvent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishFilterEvent", reflect.TypeOf((*MockDistributionLogEventService)(nil).PublishFilterEvent), ctx, req)
}

// PublishOptimizeEvent mocks base method.
func (m *MockDistributionLogEventService) PublishOptimizeEvent(ctx context.Context, req model.DistributionLogOptimizeEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishOptimizeEvent", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishOptimizeEvent indicates an expected call of PublishOptimizeEvent.
func (mr *MockDistributionLogEventServiceMockRecorder) PublishOptimizeEvent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishOptimizeEvent", reflect.TypeOf((*MockDistributionLogEventService)(nil).PublishOptimizeEvent), ctx, req)
}

// PublishSearchEvent mocks base method.
func (m *MockDistributionLogEventService) PublishSearchEvent(ctx context.Context, req model.DistributionLogSearchEvent) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PublishSearchEvent", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// PublishSearchEvent indicates an expected call of PublishSearchEvent.
func (mr *MockDistributionLogEventServiceMockRecorder) PublishSearchEvent(ctx, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishSearchEvent", reflect.TypeOf((*MockDistributionLogEventService)(nil).PublishSearchEvent), ctx, req)
}
