// Code generated by MockGen. DO NOT EDIT.
// Source: ./statistic_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockStatisticService is a mock of StatisticService interface.
type MockStatisticService struct {
	ctrl     *gomock.Controller
	recorder *MockStatisticServiceMockRecorder
}

// MockStatisticServiceMockRecorder is the mock recorder for MockStatisticService.
type MockStatisticServiceMockRecorder struct {
	mock *MockStatisticService
}

// NewMockStatisticService creates a new mock instance.
func NewMockStatisticService(ctrl *gomock.Controller) *MockStatisticService {
	mock := &MockStatisticService{ctrl: ctrl}
	mock.recorder = &MockStatisticServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockStatisticService) EXPECT() *MockStatisticServiceMockRecorder {
	return m.recorder
}

// UpdateDone mocks base method.
func (m *MockStatisticService) UpdateDone(ctx context.Context, orderID, driverID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDone", ctx, orderID, driverID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDone indicates an expected call of UpdateDone.
func (mr *MockStatisticServiceMockRecorder) UpdateDone(ctx, orderID, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDone", reflect.TypeOf((*MockStatisticService)(nil).UpdateDone), ctx, orderID, driverID)
}

// UpdateNotified mocks base method.
func (m *MockStatisticService) UpdateNotified(ctx context.Context, orderID, driverID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateNotified", ctx, orderID, driverID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateNotified indicates an expected call of UpdateNotified.
func (mr *MockStatisticServiceMockRecorder) UpdateNotified(ctx, orderID, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateNotified", reflect.TypeOf((*MockStatisticService)(nil).UpdateNotified), ctx, orderID, driverID)
}
