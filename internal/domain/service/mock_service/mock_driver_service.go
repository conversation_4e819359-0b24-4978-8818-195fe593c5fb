// Code generated by MockGen. DO NOT EDIT.
// Source: ./driver_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"
	time "time"

	crypt "git.wndv.co/lineman/absinthe/crypt"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	service "git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	gin "github.com/gin-gonic/gin"
	gomock "github.com/golang/mock/gomock"
)

// MockDeviceManager is a mock of DeviceManager interface.
type MockDeviceManager struct {
	ctrl     *gomock.Controller
	recorder *MockDeviceManagerMockRecorder
}

// MockDeviceManagerMockRecorder is the mock recorder for MockDeviceManager.
type MockDeviceManagerMockRecorder struct {
	mock *MockDeviceManager
}

// NewMockDeviceManager creates a new mock instance.
func NewMockDeviceManager(ctrl *gomock.Controller) *MockDeviceManager {
	mock := &MockDeviceManager{ctrl: ctrl}
	mock.recorder = &MockDeviceManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDeviceManager) EXPECT() *MockDeviceManagerMockRecorder {
	return m.recorder
}

// GetDeviceToken mocks base method.
func (m *MockDeviceManager) GetDeviceToken(ctx context.Context, driverID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeviceToken", ctx, driverID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeviceToken indicates an expected call of GetDeviceToken.
func (mr *MockDeviceManagerMockRecorder) GetDeviceToken(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeviceToken", reflect.TypeOf((*MockDeviceManager)(nil).GetDeviceToken), ctx, driverID)
}

// GetDeviceTokens mocks base method.
func (m *MockDeviceManager) GetDeviceTokens(ctx context.Context, driverIDs []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeviceTokens", ctx, driverIDs)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeviceTokens indicates an expected call of GetDeviceTokens.
func (mr *MockDeviceManagerMockRecorder) GetDeviceTokens(ctx, driverIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeviceTokens", reflect.TypeOf((*MockDeviceManager)(nil).GetDeviceTokens), ctx, driverIDs)
}

// GetSocketID mocks base method.
func (m *MockDeviceManager) GetSocketID(ctx context.Context, driverID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocketID", ctx, driverID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSocketID indicates an expected call of GetSocketID.
func (mr *MockDeviceManagerMockRecorder) GetSocketID(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocketID", reflect.TypeOf((*MockDeviceManager)(nil).GetSocketID), ctx, driverID)
}

// GetSocketIDs mocks base method.
func (m *MockDeviceManager) GetSocketIDs(ctx context.Context, driverIDs []string) ([]model.DriverSocketID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocketIDs", ctx, driverIDs)
	ret0, _ := ret[0].([]model.DriverSocketID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSocketIDs indicates an expected call of GetSocketIDs.
func (mr *MockDeviceManagerMockRecorder) GetSocketIDs(ctx, driverIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocketIDs", reflect.TypeOf((*MockDeviceManager)(nil).GetSocketIDs), ctx, driverIDs)
}

// UpdateDeviceToken mocks base method.
func (m *MockDeviceManager) UpdateDeviceToken(ctx context.Context, driverID, token, deviceID, advertiseID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDeviceToken", ctx, driverID, token, deviceID, advertiseID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDeviceToken indicates an expected call of UpdateDeviceToken.
func (mr *MockDeviceManagerMockRecorder) UpdateDeviceToken(ctx, driverID, token, deviceID, advertiseID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDeviceToken", reflect.TypeOf((*MockDeviceManager)(nil).UpdateDeviceToken), ctx, driverID, token, deviceID, advertiseID)
}

// UpdateSocketID mocks base method.
func (m *MockDeviceManager) UpdateSocketID(ctx context.Context, driverID, socketID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSocketID", ctx, driverID, socketID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSocketID indicates an expected call of UpdateSocketID.
func (mr *MockDeviceManagerMockRecorder) UpdateSocketID(ctx, driverID, socketID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSocketID", reflect.TypeOf((*MockDeviceManager)(nil).UpdateSocketID), ctx, driverID, socketID)
}

// MockAssigner is a mock of Assigner interface.
type MockAssigner struct {
	ctrl     *gomock.Controller
	recorder *MockAssignerMockRecorder
}

// MockAssignerMockRecorder is the mock recorder for MockAssigner.
type MockAssignerMockRecorder struct {
	mock *MockAssigner
}

// NewMockAssigner creates a new mock instance.
func NewMockAssigner(ctrl *gomock.Controller) *MockAssigner {
	mock := &MockAssigner{ctrl: ctrl}
	mock.recorder = &MockAssignerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssigner) EXPECT() *MockAssignerMockRecorder {
	return m.recorder
}

// AssignOrderToDriver mocks base method.
func (m *MockAssigner) AssignOrderToDriver(ctx context.Context, driverID, orderID string, allowQueueing bool, tripID string, startLocation model.Location, lockedUntil time.Time, isAcknowledgementRequired bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssignOrderToDriver", ctx, driverID, orderID, allowQueueing, tripID, startLocation, lockedUntil, isAcknowledgementRequired)
	ret0, _ := ret[0].(error)
	return ret0
}

// AssignOrderToDriver indicates an expected call of AssignOrderToDriver.
func (mr *MockAssignerMockRecorder) AssignOrderToDriver(ctx, driverID, orderID, allowQueueing, tripID, startLocation, lockedUntil, isAcknowledgementRequired interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignOrderToDriver", reflect.TypeOf((*MockAssigner)(nil).AssignOrderToDriver), ctx, driverID, orderID, allowQueueing, tripID, startLocation, lockedUntil, isAcknowledgementRequired)
}

// DequeueOrder mocks base method.
func (m *MockAssigner) DequeueOrder(ctx context.Context, driver model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DequeueOrder", ctx, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// DequeueOrder indicates an expected call of DequeueOrder.
func (mr *MockAssignerMockRecorder) DequeueOrder(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DequeueOrder", reflect.TypeOf((*MockAssigner)(nil).DequeueOrder), ctx, driver)
}

// DequeueTrip mocks base method.
func (m *MockAssigner) DequeueTrip(ctx context.Context, driver model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DequeueTrip", ctx, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// DequeueTrip indicates an expected call of DequeueTrip.
func (mr *MockAssignerMockRecorder) DequeueTrip(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DequeueTrip", reflect.TypeOf((*MockAssigner)(nil).DequeueTrip), ctx, driver)
}

// QueueB2bOrderToDriver mocks base method.
func (m *MockAssigner) QueueB2bOrderToDriver(ctx context.Context, driver model.Driver, orderID, tripID string, isLastAssignedB2B bool, lockedUntil time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueueB2bOrderToDriver", ctx, driver, orderID, tripID, isLastAssignedB2B, lockedUntil)
	ret0, _ := ret[0].(error)
	return ret0
}

// QueueB2bOrderToDriver indicates an expected call of QueueB2bOrderToDriver.
func (mr *MockAssignerMockRecorder) QueueB2bOrderToDriver(ctx, driver, orderID, tripID, isLastAssignedB2B, lockedUntil interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueueB2bOrderToDriver", reflect.TypeOf((*MockAssigner)(nil).QueueB2bOrderToDriver), ctx, driver, orderID, tripID, isLastAssignedB2B, lockedUntil)
}

// QueueMoOrderToDriver mocks base method.
func (m *MockAssigner) QueueMoOrderToDriver(ctx context.Context, driver model.Driver, orderID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueueMoOrderToDriver", ctx, driver, orderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// QueueMoOrderToDriver indicates an expected call of QueueMoOrderToDriver.
func (mr *MockAssignerMockRecorder) QueueMoOrderToDriver(ctx, driver, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueueMoOrderToDriver", reflect.TypeOf((*MockAssigner)(nil).QueueMoOrderToDriver), ctx, driver, orderID)
}

// UnAssignTrip mocks base method.
func (m *MockAssigner) UnAssignTrip(ctx context.Context, driverID string) (*model.Driver, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnAssignTrip", ctx, driverID)
	ret0, _ := ret[0].(*model.Driver)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnAssignTrip indicates an expected call of UnAssignTrip.
func (mr *MockAssignerMockRecorder) UnAssignTrip(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnAssignTrip", reflect.TypeOf((*MockAssigner)(nil).UnAssignTrip), ctx, driverID)
}

// UnassignOrder mocks base method.
func (m *MockAssigner) UnassignOrder(ctx context.Context, driverID string) (*model.Driver, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnassignOrder", ctx, driverID)
	ret0, _ := ret[0].(*model.Driver)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnassignOrder indicates an expected call of UnassignOrder.
func (mr *MockAssignerMockRecorder) UnassignOrder(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnassignOrder", reflect.TypeOf((*MockAssigner)(nil).UnassignOrder), ctx, driverID)
}

// UnassignQueueOrder mocks base method.
func (m *MockAssigner) UnassignQueueOrder(ctx context.Context, driver model.Driver, orderID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnassignQueueOrder", ctx, driver, orderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnassignQueueOrder indicates an expected call of UnassignQueueOrder.
func (mr *MockAssignerMockRecorder) UnassignQueueOrder(ctx, driver, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnassignQueueOrder", reflect.TypeOf((*MockAssigner)(nil).UnassignQueueOrder), ctx, driver, orderID)
}

// MockMetric is a mock of Metric interface.
type MockMetric struct {
	ctrl     *gomock.Controller
	recorder *MockMetricMockRecorder
}

// MockMetricMockRecorder is the mock recorder for MockMetric.
type MockMetricMockRecorder struct {
	mock *MockMetric
}

// NewMockMetric creates a new mock instance.
func NewMockMetric(ctrl *gomock.Controller) *MockMetric {
	mock := &MockMetric{ctrl: ctrl}
	mock.recorder = &MockMetricMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMetric) EXPECT() *MockMetricMockRecorder {
	return m.recorder
}

// AcceptedDedicatedZoneMetric mocks base method.
func (m *MockMetric) AcceptedDedicatedZoneMetric(region model.RegionCode, isPrioritized bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AcceptedDedicatedZoneMetric", region, isPrioritized)
}

// AcceptedDedicatedZoneMetric indicates an expected call of AcceptedDedicatedZoneMetric.
func (mr *MockMetricMockRecorder) AcceptedDedicatedZoneMetric(region, isPrioritized interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptedDedicatedZoneMetric", reflect.TypeOf((*MockMetric)(nil).AcceptedDedicatedZoneMetric), region, isPrioritized)
}

// AssignedBackedOrderMetric mocks base method.
func (m *MockMetric) AssignedBackedOrderMetric(region model.RegionCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AssignedBackedOrderMetric", region)
}

// AssignedBackedOrderMetric indicates an expected call of AssignedBackedOrderMetric.
func (mr *MockMetricMockRecorder) AssignedBackedOrderMetric(region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignedBackedOrderMetric", reflect.TypeOf((*MockMetric)(nil).AssignedBackedOrderMetric), region)
}

// AssignedMultipleOrderMetric mocks base method.
func (m *MockMetric) AssignedMultipleOrderMetric(region model.RegionCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AssignedMultipleOrderMetric", region)
}

// AssignedMultipleOrderMetric indicates an expected call of AssignedMultipleOrderMetric.
func (mr *MockMetricMockRecorder) AssignedMultipleOrderMetric(region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignedMultipleOrderMetric", reflect.TypeOf((*MockMetric)(nil).AssignedMultipleOrderMetric), region)
}

// CanceledBackedOrderMetric mocks base method.
func (m *MockMetric) CanceledBackedOrderMetric(region model.RegionCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CanceledBackedOrderMetric", region)
}

// CanceledBackedOrderMetric indicates an expected call of CanceledBackedOrderMetric.
func (mr *MockMetricMockRecorder) CanceledBackedOrderMetric(region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CanceledBackedOrderMetric", reflect.TypeOf((*MockMetric)(nil).CanceledBackedOrderMetric), region)
}

// MockDriverServiceInterface is a mock of DriverServiceInterface interface.
type MockDriverServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockDriverServiceInterfaceMockRecorder
}

// MockDriverServiceInterfaceMockRecorder is the mock recorder for MockDriverServiceInterface.
type MockDriverServiceInterfaceMockRecorder struct {
	mock *MockDriverServiceInterface
}

// NewMockDriverServiceInterface creates a new mock instance.
func NewMockDriverServiceInterface(ctrl *gomock.Controller) *MockDriverServiceInterface {
	mock := &MockDriverServiceInterface{ctrl: ctrl}
	mock.recorder = &MockDriverServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDriverServiceInterface) EXPECT() *MockDriverServiceInterfaceMockRecorder {
	return m.recorder
}

// AcceptedDedicatedZoneMetric mocks base method.
func (m *MockDriverServiceInterface) AcceptedDedicatedZoneMetric(region model.RegionCode, isPrioritized bool) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AcceptedDedicatedZoneMetric", region, isPrioritized)
}

// AcceptedDedicatedZoneMetric indicates an expected call of AcceptedDedicatedZoneMetric.
func (mr *MockDriverServiceInterfaceMockRecorder) AcceptedDedicatedZoneMetric(region, isPrioritized interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AcceptedDedicatedZoneMetric", reflect.TypeOf((*MockDriverServiceInterface)(nil).AcceptedDedicatedZoneMetric), region, isPrioritized)
}

// AddCompletedOrderQuota mocks base method.
func (m *MockDriverServiceInterface) AddCompletedOrderQuota(context context.Context, driver *model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddCompletedOrderQuota", context, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddCompletedOrderQuota indicates an expected call of AddCompletedOrderQuota.
func (mr *MockDriverServiceInterfaceMockRecorder) AddCompletedOrderQuota(context, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddCompletedOrderQuota", reflect.TypeOf((*MockDriverServiceInterface)(nil).AddCompletedOrderQuota), context, driver)
}

// AddWithdrawalQuota mocks base method.
func (m *MockDriverServiceInterface) AddWithdrawalQuota(ctx context.Context, driverID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AddWithdrawalQuota", ctx, driverID)
	ret0, _ := ret[0].(error)
	return ret0
}

// AddWithdrawalQuota indicates an expected call of AddWithdrawalQuota.
func (mr *MockDriverServiceInterfaceMockRecorder) AddWithdrawalQuota(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddWithdrawalQuota", reflect.TypeOf((*MockDriverServiceInterface)(nil).AddWithdrawalQuota), ctx, driverID)
}

// AssignOrderToDriver mocks base method.
func (m *MockDriverServiceInterface) AssignOrderToDriver(ctx context.Context, driverID, orderID string, allowQueueing bool, tripID string, startLocation model.Location, lockedUntil time.Time, isAcknowledgementRequired bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssignOrderToDriver", ctx, driverID, orderID, allowQueueing, tripID, startLocation, lockedUntil, isAcknowledgementRequired)
	ret0, _ := ret[0].(error)
	return ret0
}

// AssignOrderToDriver indicates an expected call of AssignOrderToDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) AssignOrderToDriver(ctx, driverID, orderID, allowQueueing, tripID, startLocation, lockedUntil, isAcknowledgementRequired interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignOrderToDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).AssignOrderToDriver), ctx, driverID, orderID, allowQueueing, tripID, startLocation, lockedUntil, isAcknowledgementRequired)
}

// AssignUobRefToDriver mocks base method.
func (m *MockDriverServiceInterface) AssignUobRefToDriver(ctx context.Context, driver *model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AssignUobRefToDriver", ctx, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// AssignUobRefToDriver indicates an expected call of AssignUobRefToDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) AssignUobRefToDriver(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignUobRefToDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).AssignUobRefToDriver), ctx, driver)
}

// AssignedBackedOrderMetric mocks base method.
func (m *MockDriverServiceInterface) AssignedBackedOrderMetric(region model.RegionCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AssignedBackedOrderMetric", region)
}

// AssignedBackedOrderMetric indicates an expected call of AssignedBackedOrderMetric.
func (mr *MockDriverServiceInterfaceMockRecorder) AssignedBackedOrderMetric(region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignedBackedOrderMetric", reflect.TypeOf((*MockDriverServiceInterface)(nil).AssignedBackedOrderMetric), region)
}

// AssignedMultipleOrderMetric mocks base method.
func (m *MockDriverServiceInterface) AssignedMultipleOrderMetric(region model.RegionCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AssignedMultipleOrderMetric", region)
}

// AssignedMultipleOrderMetric indicates an expected call of AssignedMultipleOrderMetric.
func (mr *MockDriverServiceInterfaceMockRecorder) AssignedMultipleOrderMetric(region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AssignedMultipleOrderMetric", reflect.TypeOf((*MockDriverServiceInterface)(nil).AssignedMultipleOrderMetric), region)
}

// CanceledBackedOrderMetric mocks base method.
func (m *MockDriverServiceInterface) CanceledBackedOrderMetric(region model.RegionCode) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "CanceledBackedOrderMetric", region)
}

// CanceledBackedOrderMetric indicates an expected call of CanceledBackedOrderMetric.
func (mr *MockDriverServiceInterfaceMockRecorder) CanceledBackedOrderMetric(region interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CanceledBackedOrderMetric", reflect.TypeOf((*MockDriverServiceInterface)(nil).CanceledBackedOrderMetric), region)
}

// CreateDriver mocks base method.
func (m *MockDriverServiceInterface) CreateDriver(context context.Context, regis *model.DriverRegistration, area *model.ServiceArea) (*model.Driver, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDriver", context, regis, area)
	ret0, _ := ret[0].(*model.Driver)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDriver indicates an expected call of CreateDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) CreateDriver(context, regis, area interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).CreateDriver), context, regis, area)
}

// DeductOnTopQuota mocks base method.
func (m *MockDriverServiceInterface) DeductOnTopQuota(ctx context.Context, driverID string, ots []model.OnTopScheme) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeductOnTopQuota", ctx, driverID, ots)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeductOnTopQuota indicates an expected call of DeductOnTopQuota.
func (mr *MockDriverServiceInterfaceMockRecorder) DeductOnTopQuota(ctx, driverID, ots interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeductOnTopQuota", reflect.TypeOf((*MockDriverServiceInterface)(nil).DeductOnTopQuota), ctx, driverID, ots)
}

// DequeueOrder mocks base method.
func (m *MockDriverServiceInterface) DequeueOrder(ctx context.Context, driver model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DequeueOrder", ctx, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// DequeueOrder indicates an expected call of DequeueOrder.
func (mr *MockDriverServiceInterfaceMockRecorder) DequeueOrder(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DequeueOrder", reflect.TypeOf((*MockDriverServiceInterface)(nil).DequeueOrder), ctx, driver)
}

// DequeueTrip mocks base method.
func (m *MockDriverServiceInterface) DequeueTrip(ctx context.Context, driver model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DequeueTrip", ctx, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// DequeueTrip indicates an expected call of DequeueTrip.
func (mr *MockDriverServiceInterfaceMockRecorder) DequeueTrip(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DequeueTrip", reflect.TypeOf((*MockDriverServiceInterface)(nil).DequeueTrip), ctx, driver)
}

// GenerateCitiRefID mocks base method.
func (m *MockDriverServiceInterface) GenerateCitiRefID(prefix string) (crypt.LazyEncryptedString, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenerateCitiRefID", prefix)
	ret0, _ := ret[0].(crypt.LazyEncryptedString)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenerateCitiRefID indicates an expected call of GenerateCitiRefID.
func (mr *MockDriverServiceInterfaceMockRecorder) GenerateCitiRefID(prefix interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenerateCitiRefID", reflect.TypeOf((*MockDriverServiceInterface)(nil).GenerateCitiRefID), prefix)
}

// GetDeviceToken mocks base method.
func (m *MockDriverServiceInterface) GetDeviceToken(ctx context.Context, driverID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeviceToken", ctx, driverID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeviceToken indicates an expected call of GetDeviceToken.
func (mr *MockDriverServiceInterfaceMockRecorder) GetDeviceToken(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeviceToken", reflect.TypeOf((*MockDriverServiceInterface)(nil).GetDeviceToken), ctx, driverID)
}

// GetDeviceTokens mocks base method.
func (m *MockDriverServiceInterface) GetDeviceTokens(ctx context.Context, driverIDs []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeviceTokens", ctx, driverIDs)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeviceTokens indicates an expected call of GetDeviceTokens.
func (mr *MockDriverServiceInterfaceMockRecorder) GetDeviceTokens(ctx, driverIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeviceTokens", reflect.TypeOf((*MockDriverServiceInterface)(nil).GetDeviceTokens), ctx, driverIDs)
}

// GetDriverLocationWithUpdatedAt mocks base method.
func (m *MockDriverServiceInterface) GetDriverLocationWithUpdatedAt(ctx context.Context, driverID string) (*model.LocationWithUpdatedAt, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDriverLocationWithUpdatedAt", ctx, driverID)
	ret0, _ := ret[0].(*model.LocationWithUpdatedAt)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDriverLocationWithUpdatedAt indicates an expected call of GetDriverLocationWithUpdatedAt.
func (mr *MockDriverServiceInterfaceMockRecorder) GetDriverLocationWithUpdatedAt(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDriverLocationWithUpdatedAt", reflect.TypeOf((*MockDriverServiceInterface)(nil).GetDriverLocationWithUpdatedAt), ctx, driverID)
}

// GetOrCreateOnTopQuota mocks base method.
func (m *MockDriverServiceInterface) GetOrCreateOnTopQuota(ctx context.Context, driverID string, scheme model.OnTopFare, quotaType model.OnTopQuotaType) (model.OnTopQuota, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreateOnTopQuota", ctx, driverID, scheme, quotaType)
	ret0, _ := ret[0].(model.OnTopQuota)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrCreateOnTopQuota indicates an expected call of GetOrCreateOnTopQuota.
func (mr *MockDriverServiceInterfaceMockRecorder) GetOrCreateOnTopQuota(ctx, driverID, scheme, quotaType interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreateOnTopQuota", reflect.TypeOf((*MockDriverServiceInterface)(nil).GetOrCreateOnTopQuota), ctx, driverID, scheme, quotaType)
}

// GetProfileWithLatestWithdrawalQuota mocks base method.
func (m *MockDriverServiceInterface) GetProfileWithLatestWithdrawalQuota(ctx context.Context, driverID string) (*model.Driver, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetProfileWithLatestWithdrawalQuota", ctx, driverID)
	ret0, _ := ret[0].(*model.Driver)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetProfileWithLatestWithdrawalQuota indicates an expected call of GetProfileWithLatestWithdrawalQuota.
func (mr *MockDriverServiceInterfaceMockRecorder) GetProfileWithLatestWithdrawalQuota(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetProfileWithLatestWithdrawalQuota", reflect.TypeOf((*MockDriverServiceInterface)(nil).GetProfileWithLatestWithdrawalQuota), ctx, driverID)
}

// GetSocketID mocks base method.
func (m *MockDriverServiceInterface) GetSocketID(ctx context.Context, driverID string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocketID", ctx, driverID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSocketID indicates an expected call of GetSocketID.
func (mr *MockDriverServiceInterfaceMockRecorder) GetSocketID(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocketID", reflect.TypeOf((*MockDriverServiceInterface)(nil).GetSocketID), ctx, driverID)
}

// GetSocketIDs mocks base method.
func (m *MockDriverServiceInterface) GetSocketIDs(ctx context.Context, driverIDs []string) ([]model.DriverSocketID, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSocketIDs", ctx, driverIDs)
	ret0, _ := ret[0].([]model.DriverSocketID)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSocketIDs indicates an expected call of GetSocketIDs.
func (mr *MockDriverServiceInterfaceMockRecorder) GetSocketIDs(ctx, driverIDs interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSocketIDs", reflect.TypeOf((*MockDriverServiceInterface)(nil).GetSocketIDs), ctx, driverIDs)
}

// IncrementOnTopQuota mocks base method.
func (m *MockDriverServiceInterface) IncrementOnTopQuota(ctx context.Context, driv *model.Driver, ots []model.OnTopScheme) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IncrementOnTopQuota", ctx, driv, ots)
	ret0, _ := ret[0].(error)
	return ret0
}

// IncrementOnTopQuota indicates an expected call of IncrementOnTopQuota.
func (mr *MockDriverServiceInterfaceMockRecorder) IncrementOnTopQuota(ctx, driv, ots interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IncrementOnTopQuota", reflect.TypeOf((*MockDriverServiceInterface)(nil).IncrementOnTopQuota), ctx, driv, ots)
}

// OfflineDriver mocks base method.
func (m *MockDriverServiceInterface) OfflineDriver(ctx context.Context, driver *model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OfflineDriver", ctx, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// OfflineDriver indicates an expected call of OfflineDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) OfflineDriver(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OfflineDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).OfflineDriver), ctx, driver)
}

// QueueB2bOrderToDriver mocks base method.
func (m *MockDriverServiceInterface) QueueB2bOrderToDriver(ctx context.Context, driver model.Driver, orderID, tripID string, isLastAssignedB2B bool, lockedUntil time.Time) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueueB2bOrderToDriver", ctx, driver, orderID, tripID, isLastAssignedB2B, lockedUntil)
	ret0, _ := ret[0].(error)
	return ret0
}

// QueueB2bOrderToDriver indicates an expected call of QueueB2bOrderToDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) QueueB2bOrderToDriver(ctx, driver, orderID, tripID, isLastAssignedB2B, lockedUntil interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueueB2bOrderToDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).QueueB2bOrderToDriver), ctx, driver, orderID, tripID, isLastAssignedB2B, lockedUntil)
}

// QueueMoOrderToDriver mocks base method.
func (m *MockDriverServiceInterface) QueueMoOrderToDriver(ctx context.Context, driver model.Driver, orderID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "QueueMoOrderToDriver", ctx, driver, orderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// QueueMoOrderToDriver indicates an expected call of QueueMoOrderToDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) QueueMoOrderToDriver(ctx, driver, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "QueueMoOrderToDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).QueueMoOrderToDriver), ctx, driver, orderID)
}

// RatingDriver mocks base method.
func (m *MockDriverServiceInterface) RatingDriver(gctx *gin.Context, score uint32, comment string, tags []string, order *model.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RatingDriver", gctx, score, comment, tags, order)
	ret0, _ := ret[0].(error)
	return ret0
}

// RatingDriver indicates an expected call of RatingDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) RatingDriver(gctx, score, comment, tags, order interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RatingDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).RatingDriver), gctx, score, comment, tags, order)
}

// TryLockDriver mocks base method.
func (m *MockDriverServiceInterface) TryLockDriver(ctx context.Context, driverID string, opts ...service.LockDriverOptionsFunc) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, driverID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TryLockDriver", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// TryLockDriver indicates an expected call of TryLockDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) TryLockDriver(ctx, driverID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, driverID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryLockDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).TryLockDriver), varargs...)
}

// TryUpdateLeavePrevStopAt mocks base method.
func (m *MockDriverServiceInterface) TryUpdateLeavePrevStopAt(ctx context.Context, currentTrip *model.Trip) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "TryUpdateLeavePrevStopAt", ctx, currentTrip)
}

// TryUpdateLeavePrevStopAt indicates an expected call of TryUpdateLeavePrevStopAt.
func (mr *MockDriverServiceInterfaceMockRecorder) TryUpdateLeavePrevStopAt(ctx, currentTrip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryUpdateLeavePrevStopAt", reflect.TypeOf((*MockDriverServiceInterface)(nil).TryUpdateLeavePrevStopAt), ctx, currentTrip)
}

// UnAssignTrip mocks base method.
func (m *MockDriverServiceInterface) UnAssignTrip(ctx context.Context, driverID string) (*model.Driver, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnAssignTrip", ctx, driverID)
	ret0, _ := ret[0].(*model.Driver)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnAssignTrip indicates an expected call of UnAssignTrip.
func (mr *MockDriverServiceInterfaceMockRecorder) UnAssignTrip(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnAssignTrip", reflect.TypeOf((*MockDriverServiceInterface)(nil).UnAssignTrip), ctx, driverID)
}

// UnAssignUobRefFromDriver mocks base method.
func (m *MockDriverServiceInterface) UnAssignUobRefFromDriver(ctx context.Context, driver *model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnAssignUobRefFromDriver", ctx, driver)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnAssignUobRefFromDriver indicates an expected call of UnAssignUobRefFromDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) UnAssignUobRefFromDriver(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnAssignUobRefFromDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).UnAssignUobRefFromDriver), ctx, driver)
}

// UnassignOrder mocks base method.
func (m *MockDriverServiceInterface) UnassignOrder(ctx context.Context, driverID string) (*model.Driver, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnassignOrder", ctx, driverID)
	ret0, _ := ret[0].(*model.Driver)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UnassignOrder indicates an expected call of UnassignOrder.
func (mr *MockDriverServiceInterfaceMockRecorder) UnassignOrder(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnassignOrder", reflect.TypeOf((*MockDriverServiceInterface)(nil).UnassignOrder), ctx, driverID)
}

// UnassignQueueOrder mocks base method.
func (m *MockDriverServiceInterface) UnassignQueueOrder(ctx context.Context, driver model.Driver, orderID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnassignQueueOrder", ctx, driver, orderID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnassignQueueOrder indicates an expected call of UnassignQueueOrder.
func (mr *MockDriverServiceInterfaceMockRecorder) UnassignQueueOrder(ctx, driver, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnassignQueueOrder", reflect.TypeOf((*MockDriverServiceInterface)(nil).UnassignQueueOrder), ctx, driver, orderID)
}

// UnlockDriver mocks base method.
func (m *MockDriverServiceInterface) UnlockDriver(ctx context.Context, driverID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UnlockDriver", ctx, driverID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UnlockDriver indicates an expected call of UnlockDriver.
func (mr *MockDriverServiceInterfaceMockRecorder) UnlockDriver(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UnlockDriver", reflect.TypeOf((*MockDriverServiceInterface)(nil).UnlockDriver), ctx, driverID)
}

// UpdateDeviceToken mocks base method.
func (m *MockDriverServiceInterface) UpdateDeviceToken(ctx context.Context, driverID, token, deviceID, advertiseID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateDeviceToken", ctx, driverID, token, deviceID, advertiseID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDeviceToken indicates an expected call of UpdateDeviceToken.
func (mr *MockDriverServiceInterfaceMockRecorder) UpdateDeviceToken(ctx, driverID, token, deviceID, advertiseID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDeviceToken", reflect.TypeOf((*MockDriverServiceInterface)(nil).UpdateDeviceToken), ctx, driverID, token, deviceID, advertiseID)
}

// UpdateDriverLastAttempt mocks base method.
func (m *MockDriverServiceInterface) UpdateDriverLastAttempt(ctx context.Context, driverID string, opt ...service.SetDriverLatestAttemptOptionFunc) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, driverID}
	for _, a := range opt {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateDriverLastAttempt", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateDriverLastAttempt indicates an expected call of UpdateDriverLastAttempt.
func (mr *MockDriverServiceInterfaceMockRecorder) UpdateDriverLastAttempt(ctx, driverID interface{}, opt ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, driverID}, opt...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateDriverLastAttempt", reflect.TypeOf((*MockDriverServiceInterface)(nil).UpdateDriverLastAttempt), varargs...)
}

// UpdateSocketID mocks base method.
func (m *MockDriverServiceInterface) UpdateSocketID(ctx context.Context, driverID, socketID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSocketID", ctx, driverID, socketID)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSocketID indicates an expected call of UpdateSocketID.
func (mr *MockDriverServiceInterfaceMockRecorder) UpdateSocketID(ctx, driverID, socketID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSocketID", reflect.TypeOf((*MockDriverServiceInterface)(nil).UpdateSocketID), ctx, driverID, socketID)
}
