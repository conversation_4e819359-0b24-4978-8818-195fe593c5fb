// Code generated by MockGen. DO NOT EDIT.
// Source: ./ban_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"
	time "time"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockBanService is a mock of BanService interface.
type MockBanService struct {
	ctrl     *gomock.Controller
	recorder *MockBanServiceMockRecorder
}

// MockBanServiceMockRecorder is the mock recorder for MockBanService.
type MockBanServiceMockRecorder struct {
	mock *MockBanService
}

// NewMockBanService creates a new mock instance.
func NewMockBanService(ctrl *gomock.Controller) *MockBanService {
	mock := &MockBanService{ctrl: ctrl}
	mock.recorder = &MockBanServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBanService) EXPECT() *MockBanServiceMockRecorder {
	return m.recorder
}

// Ban mocks base method.
func (m *MockBanService) Ban(ctx context.Context, driver *model.Driver, info model.BanInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Ban", ctx, driver, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// Ban indicates an expected call of Ban.
func (mr *MockBanServiceMockRecorder) Ban(ctx, driver, info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Ban", reflect.TypeOf((*MockBanService)(nil).Ban), ctx, driver, info)
}

// BanAndSaveHistory mocks base method.
func (m *MockBanService) BanAndSaveHistory(ctx context.Context, driver *model.Driver, info model.BanInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BanAndSaveHistory", ctx, driver, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// BanAndSaveHistory indicates an expected call of BanAndSaveHistory.
func (mr *MockBanServiceMockRecorder) BanAndSaveHistory(ctx, driver, info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanAndSaveHistory", reflect.TypeOf((*MockBanService)(nil).BanAndSaveHistory), ctx, driver, info)
}

// BanWithHistory mocks base method.
func (m *MockBanService) BanWithHistory(ctx context.Context, driver *model.Driver, banHistory *model.BanHistory) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BanWithHistory", ctx, driver, banHistory)
	ret0, _ := ret[0].(error)
	return ret0
}

// BanWithHistory indicates an expected call of BanWithHistory.
func (mr *MockBanServiceMockRecorder) BanWithHistory(ctx, driver, banHistory interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanWithHistory", reflect.TypeOf((*MockBanService)(nil).BanWithHistory), ctx, driver, banHistory)
}

// BanWithMetadata mocks base method.
func (m *MockBanService) BanWithMetadata(ctx context.Context, driver *model.Driver, banReasons []*model.BanReason, banType model.BanType, bannedUntil *time.Time, req model.BanDriverDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BanWithMetadata", ctx, driver, banReasons, banType, bannedUntil, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// BanWithMetadata indicates an expected call of BanWithMetadata.
func (mr *MockBanServiceMockRecorder) BanWithMetadata(ctx, driver, banReasons, banType, bannedUntil, req interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanWithMetadata", reflect.TypeOf((*MockBanService)(nil).BanWithMetadata), ctx, driver, banReasons, banType, bannedUntil, req)
}

// BanWithdrawAndSaveHistory mocks base method.
func (m *MockBanService) BanWithdrawAndSaveHistory(ctx context.Context, driver *model.Driver, info model.BanInfo) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BanWithdrawAndSaveHistory", ctx, driver, info)
	ret0, _ := ret[0].(error)
	return ret0
}

// BanWithdrawAndSaveHistory indicates an expected call of BanWithdrawAndSaveHistory.
func (mr *MockBanServiceMockRecorder) BanWithdrawAndSaveHistory(ctx, driver, info interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BanWithdrawAndSaveHistory", reflect.TypeOf((*MockBanService)(nil).BanWithdrawAndSaveHistory), ctx, driver, info)
}

// CreateMetadata mocks base method.
func (m *MockBanService) CreateMetadata(ctx context.Context, metadata *model.BanMetadata) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateMetadata", ctx, metadata)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateMetadata indicates an expected call of CreateMetadata.
func (mr *MockBanServiceMockRecorder) CreateMetadata(ctx, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateMetadata", reflect.TypeOf((*MockBanService)(nil).CreateMetadata), ctx, metadata)
}

// DeleteMetadata mocks base method.
func (m *MockBanService) DeleteMetadata(ctx context.Context, hexID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteMetadata", ctx, hexID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteMetadata indicates an expected call of DeleteMetadata.
func (mr *MockBanServiceMockRecorder) DeleteMetadata(ctx, hexID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteMetadata", reflect.TypeOf((*MockBanService)(nil).DeleteMetadata), ctx, hexID)
}

// GetBanDriverDetail mocks base method.
func (m *MockBanService) GetBanDriverDetail(ctx context.Context, driverID string) model.BanDriverDetail {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBanDriverDetail", ctx, driverID)
	ret0, _ := ret[0].(model.BanDriverDetail)
	return ret0
}

// GetBanDriverDetail indicates an expected call of GetBanDriverDetail.
func (mr *MockBanServiceMockRecorder) GetBanDriverDetail(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanDriverDetail", reflect.TypeOf((*MockBanService)(nil).GetBanDriverDetail), ctx, driverID)
}

// GetBanReasons mocks base method.
func (m *MockBanService) GetBanReasons(ctx context.Context, reasonsMetadata []*model.BanReasonMetadata) ([]*model.BanReason, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBanReasons", ctx, reasonsMetadata)
	ret0, _ := ret[0].([]*model.BanReason)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBanReasons indicates an expected call of GetBanReasons.
func (mr *MockBanServiceMockRecorder) GetBanReasons(ctx, reasonsMetadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBanReasons", reflect.TypeOf((*MockBanService)(nil).GetBanReasons), ctx, reasonsMetadata)
}

// IsPermanentBannedDriver mocks base method.
func (m *MockBanService) IsPermanentBannedDriver(ctx context.Context, driver model.Driver) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsPermanentBannedDriver", ctx, driver)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsPermanentBannedDriver indicates an expected call of IsPermanentBannedDriver.
func (mr *MockBanServiceMockRecorder) IsPermanentBannedDriver(ctx, driver interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsPermanentBannedDriver", reflect.TypeOf((*MockBanService)(nil).IsPermanentBannedDriver), ctx, driver)
}

// ListMetadata mocks base method.
func (m *MockBanService) ListMetadata(ctx context.Context) ([]model.BanMetadata, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListMetadata", ctx)
	ret0, _ := ret[0].([]model.BanMetadata)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListMetadata indicates an expected call of ListMetadata.
func (mr *MockBanServiceMockRecorder) ListMetadata(ctx interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListMetadata", reflect.TypeOf((*MockBanService)(nil).ListMetadata), ctx)
}

// Unban mocks base method.
func (m *MockBanService) Unban(ctx context.Context, driver *model.Driver, getBanHistory func() (model.BanHistory, error)) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Unban", ctx, driver, getBanHistory)
	ret0, _ := ret[0].(error)
	return ret0
}

// Unban indicates an expected call of Unban.
func (mr *MockBanServiceMockRecorder) Unban(ctx, driver, getBanHistory interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Unban", reflect.TypeOf((*MockBanService)(nil).Unban), ctx, driver, getBanHistory)
}

// UpdateMetadata mocks base method.
func (m *MockBanService) UpdateMetadata(ctx context.Context, metadata *model.BanMetadata) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateMetadata", ctx, metadata)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateMetadata indicates an expected call of UpdateMetadata.
func (mr *MockBanServiceMockRecorder) UpdateMetadata(ctx, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateMetadata", reflect.TypeOf((*MockBanService)(nil).UpdateMetadata), ctx, metadata)
}

// VerifyMetadata mocks base method.
func (m *MockBanService) VerifyMetadata(ctx context.Context, metadata *model.BanMetadata) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "VerifyMetadata", ctx, metadata)
	ret0, _ := ret[0].(error)
	return ret0
}

// VerifyMetadata indicates an expected call of VerifyMetadata.
func (mr *MockBanServiceMockRecorder) VerifyMetadata(ctx, metadata interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VerifyMetadata", reflect.TypeOf((*MockBanService)(nil).VerifyMetadata), ctx, metadata)
}
