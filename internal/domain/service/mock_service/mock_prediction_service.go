// Code generated by MockGen. DO NOT EDIT.
// Source: ./prediction_service.go

// Package mock_service is a generated GoMock package.
package mock_service

import (
	context "context"
	reflect "reflect"

	prediction "git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	service "git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	gomock "github.com/golang/mock/gomock"
)

// MockPredictionService is a mock of PredictionService interface.
type MockPredictionService struct {
	ctrl     *gomock.Controller
	recorder *MockPredictionServiceMockRecorder
}

// MockPredictionServiceMockRecorder is the mock recorder for MockPredictionService.
type MockPredictionServiceMockRecorder struct {
	mock *MockPredictionService
}

// NewMockPredictionService creates a new mock instance.
func NewMockPredictionService(ctrl *gomock.Controller) *MockPredictionService {
	mock := &MockPredictionService{ctrl: ctrl}
	mock.recorder = &MockPredictionServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPredictionService) EXPECT() *MockPredictionServiceMockRecorder {
	return m.recorder
}

// BatchOptimize mocks base method.
func (m *MockPredictionService) BatchOptimize(ctx context.Context, orders []model.Order, riders []service.DriverWithLocation, setting service.OptimizeSetting) (prediction.BatchOptimizeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchOptimize", ctx, orders, riders, setting)
	ret0, _ := ret[0].(prediction.BatchOptimizeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchOptimize indicates an expected call of BatchOptimize.
func (mr *MockPredictionServiceMockRecorder) BatchOptimize(ctx, orders, riders, setting interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchOptimize", reflect.TypeOf((*MockPredictionService)(nil).BatchOptimize), ctx, orders, riders, setting)
}

// EstimateRoute mocks base method.
func (m *MockPredictionService) EstimateRoute(ctx context.Context, rider service.DriverWithLocation, setting service.EstimateRouteSetting) (prediction.RouteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EstimateRoute", ctx, rider, setting)
	ret0, _ := ret[0].(prediction.RouteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// EstimateRoute indicates an expected call of EstimateRoute.
func (mr *MockPredictionServiceMockRecorder) EstimateRoute(ctx, rider, setting interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EstimateRoute", reflect.TypeOf((*MockPredictionService)(nil).EstimateRoute), ctx, rider, setting)
}

// IsTripDistanceReduced mocks base method.
func (m *MockPredictionService) IsTripDistanceReduced(ctx context.Context, newOrderIDs []string, assignedTrips []model.Trip, dalianTrips model.DalianTrips, ordersMap map[string]model.Order) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsTripDistanceReduced", ctx, newOrderIDs, assignedTrips, dalianTrips, ordersMap)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsTripDistanceReduced indicates an expected call of IsTripDistanceReduced.
func (mr *MockPredictionServiceMockRecorder) IsTripDistanceReduced(ctx, newOrderIDs, assignedTrips, dalianTrips, ordersMap interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsTripDistanceReduced", reflect.TypeOf((*MockPredictionService)(nil).IsTripDistanceReduced), ctx, newOrderIDs, assignedTrips, dalianTrips, ordersMap)
}

// Optimize mocks base method.
func (m *MockPredictionService) Optimize(ctx context.Context, order *model.Order, riders []service.DriverWithLocation, setting service.OptimizeSetting) ([]service.DriverWithLocation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Optimize", ctx, order, riders, setting)
	ret0, _ := ret[0].([]service.DriverWithLocation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Optimize indicates an expected call of Optimize.
func (mr *MockPredictionServiceMockRecorder) Optimize(ctx, order, riders, setting interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Optimize", reflect.TypeOf((*MockPredictionService)(nil).Optimize), ctx, order, riders, setting)
}

// Predict mocks base method.
func (m *MockPredictionService) Predict(ctx context.Context, order *model.Order, setting service.PredictSetting) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Predict", ctx, order, setting)
	ret0, _ := ret[0].(error)
	return ret0
}

// Predict indicates an expected call of Predict.
func (mr *MockPredictionServiceMockRecorder) Predict(ctx, order, setting interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Predict", reflect.TypeOf((*MockPredictionService)(nil).Predict), ctx, order, setting)
}

// Route mocks base method.
func (m *MockPredictionService) Route(ctx context.Context, orders []model.Order, rider service.DriverWithLocation, setting service.RouteSetting) (prediction.RouteResponse, *model.RidersFilterData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Route", ctx, orders, rider, setting)
	ret0, _ := ret[0].(prediction.RouteResponse)
	ret1, _ := ret[1].(*model.RidersFilterData)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Route indicates an expected call of Route.
func (mr *MockPredictionServiceMockRecorder) Route(ctx, orders, rider, setting interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Route", reflect.TypeOf((*MockPredictionService)(nil).Route), ctx, orders, rider, setting)
}

// ValidatePlanRoute mocks base method.
func (m *MockPredictionService) ValidatePlanRoute(ctx context.Context, res prediction.RouteResponse, newOrderIDs []string, rider service.DriverWithLocation, setting service.ValidatePlanRouteSetting) (service.ValidateRouteErrRes, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidatePlanRoute", ctx, res, newOrderIDs, rider, setting)
	ret0, _ := ret[0].(service.ValidateRouteErrRes)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidatePlanRoute indicates an expected call of ValidatePlanRoute.
func (mr *MockPredictionServiceMockRecorder) ValidatePlanRoute(ctx, res, newOrderIDs, rider, setting interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidatePlanRoute", reflect.TypeOf((*MockPredictionService)(nil).ValidatePlanRoute), ctx, res, newOrderIDs, rider, setting)
}

// MockPredictionSetting is a mock of PredictionSetting interface.
type MockPredictionSetting struct {
	ctrl     *gomock.Controller
	recorder *MockPredictionSettingMockRecorder
}

// MockPredictionSettingMockRecorder is the mock recorder for MockPredictionSetting.
type MockPredictionSettingMockRecorder struct {
	mock *MockPredictionSetting
}

// NewMockPredictionSetting creates a new mock instance.
func NewMockPredictionSetting(ctrl *gomock.Controller) *MockPredictionSetting {
	mock := &MockPredictionSetting{ctrl: ctrl}
	mock.recorder = &MockPredictionSettingMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPredictionSetting) EXPECT() *MockPredictionSettingMockRecorder {
	return m.recorder
}

// SetPriceAreaScheme mocks base method.
func (m *MockPredictionSetting) SetPriceAreaScheme(schemes map[string]prediction.PriceAreaScheme) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "SetPriceAreaScheme", schemes)
}

// SetPriceAreaScheme indicates an expected call of SetPriceAreaScheme.
func (mr *MockPredictionSettingMockRecorder) SetPriceAreaScheme(schemes interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetPriceAreaScheme", reflect.TypeOf((*MockPredictionSetting)(nil).SetPriceAreaScheme), schemes)
}
