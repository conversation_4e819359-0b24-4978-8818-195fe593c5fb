package service_test

import (
	"context"
	"errors"
	"testing"
	"time"

	legacyGoMock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/mongo"
	gomock "go.uber.org/mock/gomock"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type assignmentBenchmarkServiceDeps struct {
	driverRepo              *mock_repository.MockDriverRepository
	assignmentLogRepo       *mock_repository.MockAssignmentLogRepository
	assignmentBenchmarkRepo *mock_repository.MockAssignmentBenchmarkRepository
	driverLocationRepo      *mock_repository.MockDriverLocationRepository
	driverLocationCfg       *persistence.AtomicDriverLocationConfig
}

func newAssignmentBenchmarkService(ctrl *gomock.Controller, legacyCtrl *legacyGoMock.Controller) (service.AssignmentBenchmarkService, *assignmentBenchmarkServiceDeps) {
	driverRepo := mock_repository.NewMockDriverRepository(legacyCtrl)
	assignmentLogRepo := mock_repository.NewMockAssignmentLogRepository(legacyCtrl)
	assignmentBenchmarkRepo := mock_repository.NewMockAssignmentBenchmarkRepository(ctrl)
	driverLocationRepo := mock_repository.NewMockDriverLocationRepository(legacyCtrl)
	deps := &assignmentBenchmarkServiceDeps{
		driverRepo:              driverRepo,
		assignmentLogRepo:       assignmentLogRepo,
		assignmentBenchmarkRepo: assignmentBenchmarkRepo,
		driverLocationRepo:      driverLocationRepo,
		driverLocationCfg:       persistence.ProvideDriverLocationConfig(&config.DBConfigUpdater{}), // Use default value
	}

	return service.ProvideAssignmentBenchmarkService(
		deps.driverRepo,
		deps.assignmentLogRepo,
		deps.assignmentBenchmarkRepo,
		deps.driverLocationRepo,
		deps.driverLocationCfg,
		service.AssignmentBenchmarkCfg{
			BatchSize: 2,
		},
	), deps
}

func TestAssignmentBenchmarkServiceImpl_GetDriverAssignmentStats(t *testing.T) {
	var (
		lat     = 1.0
		lng     = 1.0
		h3index = model.H3Index("867541ac7ffffff")
		now     = timeutil.BangkokNow()
	)

	t.Run("should show invalid driver tier error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var (
			ctx      = context.Background()
			driverID = "LMDMOCK"
		)

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		deps.driverRepo.EXPECT().FindDriverID(ctx, driverID, gomock.Any()).Return(&model.Driver{
			BaseDriver: model.BaseDriver{
				DriverTier: "",
			},
			DriverID:              driverID,
			LastAcceptedOrderID:   "",
			LastAcceptedOrderTime: nil,
		}, nil)

		res, err := srv.GetDriverAssignmentStats(ctx, driverID)

		require.Nil(tt, res)
		require.ErrorIs(tt, err, model.ErrInvalidDriverTier)
	})

	t.Run("should show driver error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var (
			ctx           = context.Background()
			driverID      = "LMDMOCK"
			unexpectedErr = errors.New("unexpected error")
		)

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		deps.driverRepo.EXPECT().FindDriverID(ctx, driverID, gomock.Any()).Return(nil, unexpectedErr)

		res, err := srv.GetDriverAssignmentStats(ctx, driverID)

		require.Nil(tt, res)
		require.ErrorIs(tt, err, unexpectedErr)
	})

	t.Run("should show assignment log error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var (
			ctx           = context.Background()
			driverID      = "LMDMOCK"
			driverTier    = model.DriverTierMember
			unexpectedErr = errors.New("unexpected error")
		)

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		deps.driverRepo.EXPECT().FindDriverID(ctx, driverID, gomock.Any()).Return(&model.Driver{
			BaseDriver: model.BaseDriver{
				DriverTier: driverTier,
			},
			DriverID:              driverID,
			LastAcceptedOrderID:   "",
			LastAcceptedOrderTime: nil,
		}, nil)

		deps.assignmentLogRepo.EXPECT().CountOrdersByDriverIDSince(ctx, driverID, gomock.Any(), gomock.Any()).Return(0, unexpectedErr)

		res, err := srv.GetDriverAssignmentStats(ctx, driverID)

		require.Nil(tt, res)
		require.ErrorIs(tt, err, unexpectedErr)
	})

	t.Run("should show driver location error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var (
			ctx           = context.Background()
			driverID      = "LMDMOCK"
			driverTier    = model.DriverTierMember
			unexpectedErr = errors.New("unexpected error")
		)

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		deps.driverRepo.EXPECT().FindDriverID(ctx, driverID, gomock.Any()).Return(&model.Driver{
			BaseDriver: model.BaseDriver{
				DriverTier: driverTier,
			},
			DriverID:              driverID,
			LastAcceptedOrderID:   "",
			LastAcceptedOrderTime: nil,
		}, nil)

		deps.assignmentLogRepo.EXPECT().CountOrdersByDriverIDSince(ctx, driverID, gomock.Any(), gomock.Any()).Return(0, nil)

		deps.driverLocationRepo.EXPECT().GetDriverLocation(ctx, driverID).Return(nil, unexpectedErr)

		res, err := srv.GetDriverAssignmentStats(ctx, driverID)

		require.Nil(tt, res)
		require.ErrorIs(tt, err, unexpectedErr)
	})

	t.Run("should show assignment benchmark error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var (
			ctx           = context.Background()
			driverID      = "LMDMOCK"
			driverTier    = model.DriverTierMember
			unexpectedErr = errors.New("unexpected error")
		)

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		deps.driverRepo.EXPECT().FindDriverID(ctx, driverID, gomock.Any()).Return(&model.Driver{
			BaseDriver: model.BaseDriver{
				DriverTier: driverTier,
			},
			DriverID:              driverID,
			LastAcceptedOrderID:   "",
			LastAcceptedOrderTime: nil,
		}, nil)

		deps.assignmentLogRepo.EXPECT().CountOrdersByDriverIDSince(ctx, driverID, gomock.Any(), gomock.Any()).Return(0, nil)

		deps.driverLocationRepo.EXPECT().GetDriverLocation(ctx, driverID).Return(&model.DriverLocation{
			DriverID: driverID,
			Location: model.Location{
				Lat: lat,
				Lng: lng,
			},
		}, nil)

		var dateType model.DateType
		if timeutil.IsWeekday(now) {
			dateType = model.DateTypeWeekday
		} else {
			dateType = model.DateTypeWeekend
		}

		deps.assignmentBenchmarkRepo.EXPECT().FindOne(ctx, &repository.FindAssignmentBenchmarkParams{
			DriverTier:     model.DriverTierMember,
			DateType:       dateType,
			OperatingHours: model.GetOperatingHour(now),
			H3Index:        h3index,
		}, gomock.Any()).Return(nil, unexpectedErr)

		res, err := srv.GetDriverAssignmentStats(ctx, driverID)

		require.Nil(tt, res)
		require.ErrorIs(tt, err, unexpectedErr)
	})

	t.Run("should return normal when benchmark not found", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var (
			ctx        = context.Background()
			driverID   = "LMDMOCK"
			driverTier = model.DriverTierMember
		)

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		deps.driverRepo.EXPECT().FindDriverID(ctx, driverID, gomock.Any()).Return(&model.Driver{
			BaseDriver: model.BaseDriver{
				DriverTier: driverTier,
			},
			DriverID:              driverID,
			LastAcceptedOrderID:   "",
			LastAcceptedOrderTime: nil,
		}, nil)

		deps.assignmentLogRepo.EXPECT().CountOrdersByDriverIDSince(ctx, driverID, gomock.Any(), gomock.Any()).Return(0, nil)

		deps.driverLocationRepo.EXPECT().GetDriverLocation(ctx, driverID).Return(&model.DriverLocation{
			DriverID: driverID,
			Location: model.Location{
				Lat: lat,
				Lng: lng,
			},
		}, nil)

		var dateType model.DateType
		if timeutil.IsWeekday(now) {
			dateType = model.DateTypeWeekday
		} else {
			dateType = model.DateTypeWeekend
		}

		deps.assignmentBenchmarkRepo.EXPECT().FindOne(ctx, &repository.FindAssignmentBenchmarkParams{
			DriverTier:     model.DriverTierMember,
			DateType:       dateType,
			OperatingHours: model.GetOperatingHour(now),
			H3Index:        h3index,
		}, gomock.Any()).Return(nil, nil)

		res, err := srv.GetDriverAssignmentStats(ctx, driverID)

		require.Nil(tt, err)
		require.Equal(tt, &service.GetDriverAssignmentStatsResponse{
			RiderOrderAcceptanceStatus: model.AcceptanceNormal,
			TotalOrderByHourBefore:     0,
			RecentlyAcceptedOrderID:    "",
			RecentlyAcceptedOrderTime:  nil,
		}, res)
	})

	type Data struct {
		caseName              string
		lastAcceptedOrderID   string
		lastAcceptedOrderTime *time.Time
		lastHourOrdersCount   int
		p10AssignedOrders     int
	}
	tests := []struct {
		data     Data
		expected *service.GetDriverAssignmentStatsResponse
	}{
		{
			data: Data{
				caseName:              "should return normal when has last accepted order and found benchmark",
				lastAcceptedOrderID:   "LMF-MOCK",
				lastAcceptedOrderTime: fp.ToPointer(now.Add(-30 * time.Minute)),
				lastHourOrdersCount:   2,
				p10AssignedOrders:     1,
			},
			expected: &service.GetDriverAssignmentStatsResponse{
				RiderOrderAcceptanceStatus: model.AcceptanceNormal,
				TotalOrderByHourBefore:     2,
				RecentlyAcceptedOrderID:    "LMF-MOCK",
				RecentlyAcceptedOrderTime:  fp.ToPointer(now.Add(-30 * time.Minute)),
			},
		},
		{
			data: Data{
				caseName:              "should return abnormal when has last accepted order and found benchmark",
				lastAcceptedOrderID:   "LMF-MOCK",
				lastAcceptedOrderTime: fp.ToPointer(now.Add(-30 * time.Minute)),
				lastHourOrdersCount:   1,
				p10AssignedOrders:     2,
			},
			expected: &service.GetDriverAssignmentStatsResponse{
				RiderOrderAcceptanceStatus: model.AcceptanceAbnormal,
				TotalOrderByHourBefore:     1,
				RecentlyAcceptedOrderID:    "LMF-MOCK",
				RecentlyAcceptedOrderTime:  fp.ToPointer(now.Add(-30 * time.Minute)),
			},
		},
		{
			data: Data{
				caseName:              "should return abnormal when no have last accepted order and found benchmark",
				lastAcceptedOrderID:   "",
				lastAcceptedOrderTime: nil,
				lastHourOrdersCount:   0,
				p10AssignedOrders:     2,
			},
			expected: &service.GetDriverAssignmentStatsResponse{
				RiderOrderAcceptanceStatus: model.AcceptanceAbnormal,
				TotalOrderByHourBefore:     0,
				RecentlyAcceptedOrderID:    "",
				RecentlyAcceptedOrderTime:  nil,
			},
		},
	}

	for _, test := range tests {
		t.Run(test.data.caseName, func(tt *testing.T) {
			tt.Parallel()

			ctrl := gomock.NewController(tt)
			legacyCtrl := legacyGoMock.NewController(tt)

			defer ctrl.Finish()
			defer legacyCtrl.Finish()

			var (
				ctx        = context.Background()
				driverID   = "LMDMOCK"
				driverTier = model.DriverTierMember
			)

			srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

			deps.driverRepo.EXPECT().FindDriverID(ctx, driverID, gomock.Any()).Return(&model.Driver{
				BaseDriver: model.BaseDriver{
					DriverTier: driverTier,
				},
				DriverID:              driverID,
				LastAcceptedOrderID:   test.data.lastAcceptedOrderID,
				LastAcceptedOrderTime: test.data.lastAcceptedOrderTime,
			}, nil)

			deps.assignmentLogRepo.EXPECT().CountOrdersByDriverIDSince(ctx, driverID, gomock.Any(), gomock.Any()).Return(test.data.lastHourOrdersCount, nil)

			deps.driverLocationRepo.EXPECT().GetDriverLocation(ctx, driverID).Return(&model.DriverLocation{
				DriverID: driverID,
				Location: model.Location{
					Lat: lat,
					Lng: lng,
				},
			}, nil)

			var dateType model.DateType
			if timeutil.IsWeekday(now) {
				dateType = model.DateTypeWeekday
			} else {
				dateType = model.DateTypeWeekend
			}

			deps.assignmentBenchmarkRepo.EXPECT().FindOne(ctx, &repository.FindAssignmentBenchmarkParams{
				DriverTier:     model.DriverTierMember,
				DateType:       dateType,
				OperatingHours: model.GetOperatingHour(now),
				H3Index:        h3index,
			}, gomock.Any()).Return(&model.AssignmentBenchmark{
				P10AssignedOrders: test.data.p10AssignedOrders,
			}, nil)

			res, err := srv.GetDriverAssignmentStats(ctx, driverID)

			require.Nil(tt, err)
			require.Equal(tt, test.expected, res)
		})
	}
}

func TestAssignmentBenchmarkServiceImpl_BulkUpsertAssignmentBenchmarks(t *testing.T) {
	timeutils.Freeze()
	t.Cleanup(func() {
		timeutils.Unfreeze()
	})
	t.Run("When bulkwrite assignment benchmarks to DB with correct data, then it should return correct result without error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var ctx = context.Background()

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		expectedModifiedCount := 1
		expectedUpsertedCount := 2
		expectedDeletedCount := 1
		expectedTotalAssignmentBenchmarksCount := 3
		expectedPreviousTotalAssignmentBenchmarksCount := 2

		input := []model.AssignmentBenchmark{
			{
				DateType:          model.DateTypeWeekday,
				OperatingHour:     model.HourAOP,
				DriverTier:        model.DriverTierBasic,
				H3Index:           model.H3Index("11111"),
				P10AssignedOrders: 1,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourLP,
				DriverTier:        model.DriverTierMember,
				H3Index:           model.H3Index("2222"),
				P10AssignedOrders: 2,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourNight,
				DriverTier:        model.DriverTierPro,
				H3Index:           model.H3Index("33333"),
				P10AssignedOrders: 3,
			},
		}

		previousTotalAssignmentBenchmarksCount := 0
		deps.assignmentBenchmarkRepo.EXPECT().Count(ctx, repository.NewFilter(), &previousTotalAssignmentBenchmarksCount, gomock.Any()).Return(nil).SetArg(2, expectedPreviousTotalAssignmentBenchmarksCount)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[0]),
					generateCSVModelToWriteModel(input[1]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1, ModifiedCount: 1}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[2]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1}, nil),
		)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{{}}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{}, nil),
		)
		deps.assignmentBenchmarkRepo.EXPECT().DeleteMany(ctx, gomock.Any(), gomock.Any()).Return(&mongo.DeleteResult{DeletedCount: 1}, nil)

		res, err := srv.BulkUpsertAssignmentBenchmarks(ctx, input)

		require.Equal(tt, expectedModifiedCount, *res.ModifiedCount)
		require.Equal(tt, expectedUpsertedCount, *res.UpsertedCount)
		require.Equal(tt, expectedDeletedCount, *res.DeletedCount)
		require.Equal(tt, expectedTotalAssignmentBenchmarksCount, *res.TotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedPreviousTotalAssignmentBenchmarksCount, *res.PreviousTotalAssignmentBenchmarksCount)
		require.Nil(tt, err)
	})

	t.Run("Given there are only modified assignment benchmarks, when bulkwrite assignment benchmarks with only different P10 assignment order, then it should return correct result without error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var ctx = context.Background()

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		expectedModifiedCount := 3
		expectedUpsertedCount := 0
		expectedDeletedCount := 0
		expectedTotalAssignmentBenchmarksCount := 3
		expectedPreviousTotalAssignmentBenchmarksCount := 3

		input := []model.AssignmentBenchmark{
			{
				DateType:          model.DateTypeWeekday,
				OperatingHour:     model.HourAOP,
				DriverTier:        model.DriverTierBasic,
				H3Index:           model.H3Index("11111"),
				P10AssignedOrders: 1,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourLP,
				DriverTier:        model.DriverTierMember,
				H3Index:           model.H3Index("2222"),
				P10AssignedOrders: 2,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourNight,
				DriverTier:        model.DriverTierPro,
				H3Index:           model.H3Index("33333"),
				P10AssignedOrders: 3,
			},
		}

		previousTotalAssignmentBenchmarksCount := 0
		deps.assignmentBenchmarkRepo.EXPECT().Count(ctx, repository.NewFilter(), &previousTotalAssignmentBenchmarksCount, gomock.Any()).Return(nil).SetArg(2, expectedPreviousTotalAssignmentBenchmarksCount)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[0]),
					generateCSVModelToWriteModel(input[1]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{ModifiedCount: 2}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[2]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{ModifiedCount: 1}, nil),
		)

		deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{}, nil)

		res, err := srv.BulkUpsertAssignmentBenchmarks(ctx, input)

		require.Equal(tt, expectedModifiedCount, *res.ModifiedCount)
		require.Equal(tt, expectedUpsertedCount, *res.UpsertedCount)
		require.Equal(tt, expectedDeletedCount, *res.DeletedCount)
		require.Equal(tt, expectedTotalAssignmentBenchmarksCount, *res.TotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedPreviousTotalAssignmentBenchmarksCount, *res.PreviousTotalAssignmentBenchmarksCount)
		require.Nil(tt, err)
	})

	t.Run("Given there are more than deleteMany need to be executed, when bulkwrite assignment benchmarks to DB with correct data, then it should return correct result without error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var ctx = context.Background()

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		expectedModifiedCount := 1
		expectedUpsertedCount := 2
		expectedDeletedCount := 4
		expectedTotalAssignmentBenchmarksCount := 3
		expectedPreviousTotalAssignmentBenchmarksCount := 5

		input := []model.AssignmentBenchmark{
			{
				DateType:          model.DateTypeWeekday,
				OperatingHour:     model.HourAOP,
				DriverTier:        model.DriverTierBasic,
				H3Index:           model.H3Index("11111"),
				P10AssignedOrders: 1,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourLP,
				DriverTier:        model.DriverTierMember,
				H3Index:           model.H3Index("2222"),
				P10AssignedOrders: 2,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourNight,
				DriverTier:        model.DriverTierPro,
				H3Index:           model.H3Index("33333"),
				P10AssignedOrders: 3,
			},
		}

		previousTotalAssignmentBenchmarksCount := 0
		deps.assignmentBenchmarkRepo.EXPECT().Count(ctx, repository.NewFilter(), &previousTotalAssignmentBenchmarksCount, gomock.Any()).Return(nil).SetArg(2, expectedPreviousTotalAssignmentBenchmarksCount)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[0]),
					generateCSVModelToWriteModel(input[1]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1, ModifiedCount: 1}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[2]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1}, nil),
		)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{{}, {}}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{{}, {}}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{}, nil),
		)
		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().DeleteMany(ctx, gomock.Any(), gomock.Any()).Return(&mongo.DeleteResult{DeletedCount: 2}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().DeleteMany(ctx, gomock.Any(), gomock.Any()).Return(&mongo.DeleteResult{DeletedCount: 2}, nil),
		)

		res, err := srv.BulkUpsertAssignmentBenchmarks(ctx, input)

		require.Equal(tt, expectedModifiedCount, *res.ModifiedCount)
		require.Equal(tt, expectedUpsertedCount, *res.UpsertedCount)
		require.Equal(tt, expectedDeletedCount, *res.DeletedCount)
		require.Equal(tt, expectedTotalAssignmentBenchmarksCount, *res.TotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedPreviousTotalAssignmentBenchmarksCount, *res.PreviousTotalAssignmentBenchmarksCount)
		require.Nil(tt, err)
	})

	t.Run("When bulkwrite assignment benchmarks cannot count current document in DB before bulk write , then it should return nil response and error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var ctx = context.Background()

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		expectedErr := errors.New("cannot count document")

		input := []model.AssignmentBenchmark{
			{
				DateType:          model.DateTypeWeekday,
				OperatingHour:     model.HourAOP,
				DriverTier:        model.DriverTierBasic,
				H3Index:           model.H3Index("11111"),
				P10AssignedOrders: 1,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourLP,
				DriverTier:        model.DriverTierMember,
				H3Index:           model.H3Index("2222"),
				P10AssignedOrders: 2,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourNight,
				DriverTier:        model.DriverTierPro,
				H3Index:           model.H3Index("33333"),
				P10AssignedOrders: 3,
			},
		}

		deps.assignmentBenchmarkRepo.EXPECT().Count(ctx, repository.NewFilter(), gomock.Any(), gomock.Any()).Return(expectedErr)
		res, err := srv.BulkUpsertAssignmentBenchmarks(ctx, input)

		require.Nil(tt, res)
		require.Equal(tt, expectedErr, err)
	})

	t.Run("When bulkwrite assignment benchmarks to DB got bulk write execute error, then it should return the result count that already executed and error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var ctx = context.Background()

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		expectedModifiedCount := 1
		expectedUpsertedCount := 1
		expectedDeletedCount := 0
		expectedTotalAssignmentBenchmarksCount := 6
		expectedPreviousTotalAssignmentBenchmarksCount := 5
		expectedErr := errors.New("bulk write error")

		input := []model.AssignmentBenchmark{
			{
				DateType:          model.DateTypeWeekday,
				OperatingHour:     model.HourAOP,
				DriverTier:        model.DriverTierBasic,
				H3Index:           model.H3Index("11111"),
				P10AssignedOrders: 1,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourLP,
				DriverTier:        model.DriverTierMember,
				H3Index:           model.H3Index("2222"),
				P10AssignedOrders: 2,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourNight,
				DriverTier:        model.DriverTierPro,
				H3Index:           model.H3Index("33333"),
				P10AssignedOrders: 3,
			},
		}

		previousTotalAssignmentBenchmarksCount := 0
		deps.assignmentBenchmarkRepo.EXPECT().Count(ctx, repository.NewFilter(), &previousTotalAssignmentBenchmarksCount, gomock.Any()).Return(nil).SetArg(2, expectedPreviousTotalAssignmentBenchmarksCount)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[0]),
					generateCSVModelToWriteModel(input[1]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1, ModifiedCount: 1}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[2]),
				}, writeModel)
			}).Return(nil, expectedErr),
		)

		res, err := srv.BulkUpsertAssignmentBenchmarks(ctx, input)

		require.Equal(tt, expectedModifiedCount, *res.ModifiedCount)
		require.Equal(tt, expectedUpsertedCount, *res.UpsertedCount)
		require.Equal(tt, expectedDeletedCount, *res.DeletedCount)
		require.Equal(tt, expectedTotalAssignmentBenchmarksCount, *res.TotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedPreviousTotalAssignmentBenchmarksCount, *res.PreviousTotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedErr, err)
	})

	t.Run("When bulkwrite assignment benchmarks to DB got find execute error, then it should return the result count that already executed and error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var ctx = context.Background()

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		expectedModifiedCount := 1
		expectedUpsertedCount := 2
		expectedDeletedCount := 2
		expectedTotalAssignmentBenchmarksCount := 5
		expectedPreviousTotalAssignmentBenchmarksCount := 5
		expectedErr := errors.New("find error")

		input := []model.AssignmentBenchmark{
			{
				DateType:          model.DateTypeWeekday,
				OperatingHour:     model.HourAOP,
				DriverTier:        model.DriverTierBasic,
				H3Index:           model.H3Index("11111"),
				P10AssignedOrders: 1,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourLP,
				DriverTier:        model.DriverTierMember,
				H3Index:           model.H3Index("2222"),
				P10AssignedOrders: 2,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourNight,
				DriverTier:        model.DriverTierPro,
				H3Index:           model.H3Index("33333"),
				P10AssignedOrders: 3,
			},
		}

		previousTotalAssignmentBenchmarksCount := 0
		deps.assignmentBenchmarkRepo.EXPECT().Count(ctx, repository.NewFilter(), &previousTotalAssignmentBenchmarksCount, gomock.Any()).Return(nil).SetArg(2, expectedPreviousTotalAssignmentBenchmarksCount)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[0]),
					generateCSVModelToWriteModel(input[1]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1, ModifiedCount: 1}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[2]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1}, nil),
		)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{{}, {}}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return(nil, expectedErr),
		)
		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().DeleteMany(ctx, gomock.Any(), gomock.Any()).Return(&mongo.DeleteResult{DeletedCount: 2}, nil),
		)

		res, err := srv.BulkUpsertAssignmentBenchmarks(ctx, input)

		require.Equal(tt, expectedModifiedCount, *res.ModifiedCount)
		require.Equal(tt, expectedUpsertedCount, *res.UpsertedCount)
		require.Equal(tt, expectedDeletedCount, *res.DeletedCount)
		require.Equal(tt, expectedTotalAssignmentBenchmarksCount, *res.TotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedPreviousTotalAssignmentBenchmarksCount, *res.PreviousTotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedErr, err)
	})

	t.Run("When bulkwrite assignment benchmarks to DB got deletemany execute error, then it should return the result count that already executed and error", func(tt *testing.T) {
		tt.Parallel()

		ctrl := gomock.NewController(tt)
		legacyCtrl := legacyGoMock.NewController(tt)

		defer ctrl.Finish()
		defer legacyCtrl.Finish()

		var ctx = context.Background()

		srv, deps := newAssignmentBenchmarkService(ctrl, legacyCtrl)

		expectedModifiedCount := 1
		expectedUpsertedCount := 2
		expectedDeletedCount := 2
		expectedTotalAssignmentBenchmarksCount := 5
		expectedPreviousTotalAssignmentBenchmarksCount := 5
		expectedErr := errors.New("deleteMany error")

		input := []model.AssignmentBenchmark{
			{
				DateType:          model.DateTypeWeekday,
				OperatingHour:     model.HourAOP,
				DriverTier:        model.DriverTierBasic,
				H3Index:           model.H3Index("11111"),
				P10AssignedOrders: 1,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourLP,
				DriverTier:        model.DriverTierMember,
				H3Index:           model.H3Index("2222"),
				P10AssignedOrders: 2,
			},
			{
				DateType:          model.DateTypeWeekend,
				OperatingHour:     model.HourNight,
				DriverTier:        model.DriverTierPro,
				H3Index:           model.H3Index("33333"),
				P10AssignedOrders: 3,
			},
		}

		previousTotalAssignmentBenchmarksCount := 0
		deps.assignmentBenchmarkRepo.EXPECT().Count(ctx, repository.NewFilter(), &previousTotalAssignmentBenchmarksCount, gomock.Any()).Return(nil).SetArg(2, expectedPreviousTotalAssignmentBenchmarksCount)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[0]),
					generateCSVModelToWriteModel(input[1]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1, ModifiedCount: 1}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().BulkWrite(ctx, gomock.Any(), gomock.Any()).Do(func(_ context.Context, writeModel []mongo.WriteModel, _ ...repository.Option) {
				require.Equal(tt, []mongo.WriteModel{
					generateCSVModelToWriteModel(input[2]),
				}, writeModel)
			}).Return(&mongo.BulkWriteResult{UpsertedCount: 1}, nil),
		)

		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{{}, {}}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().Find(ctx, repository.NewFilter().ByUpdatedAtLessThan(timeutil.BangkokNow()), 0, 2, gomock.Any()).Return([]model.AssignmentBenchmark{{}, {}}, nil),
		)
		gomock.InOrder(
			deps.assignmentBenchmarkRepo.EXPECT().DeleteMany(ctx, gomock.Any(), gomock.Any()).Return(&mongo.DeleteResult{DeletedCount: 2}, nil),
			deps.assignmentBenchmarkRepo.EXPECT().DeleteMany(ctx, gomock.Any(), gomock.Any()).Return(nil, expectedErr),
		)

		res, err := srv.BulkUpsertAssignmentBenchmarks(ctx, input)

		require.Equal(tt, expectedModifiedCount, *res.ModifiedCount)
		require.Equal(tt, expectedUpsertedCount, *res.UpsertedCount)
		require.Equal(tt, expectedDeletedCount, *res.DeletedCount)
		require.Equal(tt, expectedTotalAssignmentBenchmarksCount, *res.TotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedPreviousTotalAssignmentBenchmarksCount, *res.PreviousTotalAssignmentBenchmarksCount)
		require.Equal(tt, expectedErr, err)
	})

}

func generateCSVModelToWriteModel(assignmentBenchmark model.AssignmentBenchmark) mongo.WriteModel {
	filter := repository.NewFilter().
		ByDriverTier(assignmentBenchmark.DriverTier).
		ByDateType(assignmentBenchmark.DateType).
		ByOperatingHours(assignmentBenchmark.OperatingHour).
		ByH3Index(assignmentBenchmark.H3Index)
	setter := repository.NewSetter().
		SetDriverTierOnCreate(assignmentBenchmark.DriverTier).
		SetCreatedAtOnCreate().
		SetUpdatedAt().
		SetDateTypeOnCreate(assignmentBenchmark.DateType).
		SetOperatingHoursOnCreate(assignmentBenchmark.OperatingHour).
		SetH3IndexOnCreate(assignmentBenchmark.H3Index).
		SetP10AssignedOrders(assignmentBenchmark.P10AssignedOrders)
	return mongo.NewUpdateOneModel().
		SetUpsert(true).
		SetFilter(filter.Build()).
		SetUpdate(setter.Build())
}
