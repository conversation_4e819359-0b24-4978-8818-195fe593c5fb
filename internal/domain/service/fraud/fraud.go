package fraud

import (
	"context"

	chatv1 "git.wndv.co/go/proto/lineman/chat/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

type service struct {
	chatServiceGRPCClient chatv1.ChatServiceClient
	adminConfig           *config.AtomicAdminConfig
}

//go:generate go run go.uber.org/mock/mockgen -source=./fraud.go -destination=./mock_fraud/mock_fraud.go -package=mock_fraud

type Service interface {
	IsFraudCashCollection(ctx context.Context, cancelReasonName string, order model.Order) bool
}

// ProvideFraudService provide the fraud service
// @@wire-set-name@@ name:"ServiceSet"
func ProvideFraudService(chatServiceGRPCClient chatv1.ChatServiceClient, adminConfig *config.AtomicAdminConfig) Service {
	return &service{
		chatServiceGRPCClient: chatServiceGRPCClient,
		adminConfig:           adminConfig,
	}
}
