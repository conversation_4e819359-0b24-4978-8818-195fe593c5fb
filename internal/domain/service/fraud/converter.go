package fraud

import (
	chatv1 "git.wndv.co/go/proto/lineman/chat/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
)

func ConvertServiceTypeToChatGRPCServiceType(in model.Service) chatv1.ServiceType {
	switch in {
	case model.ServiceFood:
		return chatv1.ServiceType_SERVICE_TYPE_FOOD
	case model.ServiceMart:
		return chatv1.ServiceType_SERVICE_TYPE_MART
	case model.ServiceMessenger:
		return chatv1.ServiceType_SERVICE_TYPE_MESSENGER
	case model.ServiceBike:
		return chatv1.ServiceType_SERVICE_TYPE_TRANSPORTATION
	}
	return chatv1.ServiceType_SERVICE_TYPE_UNSPECIFIED
}
