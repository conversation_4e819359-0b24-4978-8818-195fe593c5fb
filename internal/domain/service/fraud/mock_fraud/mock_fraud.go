// Code generated by MockGen. DO NOT EDIT.
// Source: ./fraud.go
//
// Generated by this command:
//
//	mockgen -source=./fraud.go -destination=./mock_fraud/mock_fraud.go -package=mock_fraud
//

// Package mock_fraud is a generated GoMock package.
package mock_fraud

import (
	context "context"
	reflect "reflect"

	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "go.uber.org/mock/gomock"
)

// MockService is a mock of Service interface.
type MockService struct {
	ctrl     *gomock.Controller
	recorder *MockServiceMockRecorder
}

// MockServiceMockRecorder is the mock recorder for MockService.
type MockServiceMockRecorder struct {
	mock *MockService
}

// NewMockService creates a new mock instance.
func NewMockService(ctrl *gomock.Controller) *MockService {
	mock := &MockService{ctrl: ctrl}
	mock.recorder = &MockServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockService) EXPECT() *MockServiceMockRecorder {
	return m.recorder
}

// IsFraudCashCollection mocks base method.
func (m *MockService) IsFraudCashCollection(ctx context.Context, cancelReasonName string, order model.Order) bool {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsFraudCashCollection", ctx, cancelReasonName, order)
	ret0, _ := ret[0].(bool)
	return ret0
}

// IsFraudCashCollection indicates an expected call of IsFraudCashCollection.
func (mr *MockServiceMockRecorder) IsFraudCashCollection(ctx, cancelReasonName, order any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsFraudCashCollection", reflect.TypeOf((*MockService)(nil).IsFraudCashCollection), ctx, cancelReasonName, order)
}
