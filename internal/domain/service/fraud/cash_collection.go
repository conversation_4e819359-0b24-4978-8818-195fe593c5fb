package fraud

import (
	"context"
	"errors"

	"git.wndv.co/go/logx/v2"
	chatv1 "git.wndv.co/go/proto/lineman/chat/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/xgo/api"
)

const (
	FraudCashCollection1SentryLogCode                   = "RIDER_FRAUD_CASH_COLLECTION_1"
	FraudCashCollectionInvalidContactCountSentryLogCode = "RIDER_FRAUD_CASH_COLLECTION_INVALID_CONTACT_COUNT"
)

const (
	CancelReasonNameLogKey     = "cancel_reason_name"
	TextMessageCountLogKey     = "text_message_count"
	StickerMessageCountLogKey  = "sticker_message_count"
	LocationMessageCountLogKey = "location_message_count"
	ImageMessageCountLogKey    = "image_message_count"
	VOIPCalledCountLogKey      = "voip_called_count"
	VOIPNoAnswerCountLogKey    = "voip_no_answered_count"
)

var (
	ErrInvalidContactCountNumber = errors.New("invalid contact count number")
)

func (f service) IsFraudCashCollection(ctx context.Context, cancelReasonName string, order model.Order) bool {
	if !order.IsCashCollection() {
		logx.Info().
			Context(ctx).
			Str(CancelReasonNameLogKey, cancelReasonName).
			Str(logutil.DriverID, order.Driver).
			Str(logutil.OrderID, order.OrderID).
			Msg("order is not cash collection")
		return false
	}

	if order.Status != model.StatusDriverArrived {
		logx.Info().
			Context(ctx).
			Str(CancelReasonNameLogKey, cancelReasonName).
			Str(logutil.DriverID, order.Driver).
			Str(logutil.OrderID, order.OrderID).
			Msg("order is not in arrived at user destination status")
		return false
	}

	// If the config is not initialize, Means that it is the server fault.
	// So we allow the rider to be able to claim without checking.
	if f.adminConfig == nil || !f.adminConfig.Get().FraudCashCollection.IsInitialized() {
		return false
	} else {
		// If the admin configured reason don't include the requested reason, Then we skip the checking
		if !f.adminConfig.Get().FraudCashCollection.Has(cancelReasonName) {
			logx.Info().
				Context(ctx).
				Str(CancelReasonNameLogKey, cancelReasonName).
				Str(logutil.OrderID, order.OrderID).
				Str(logutil.DriverID, order.Driver).
				Msg("cancel reason is not matched the focused one of FraudCashCollection case")
			return false
		}
	}

	focusedDriverID := order.Driver
	result, err := f.chatServiceGRPCClient.GetChatRoomStatistic(ctx, &chatv1.GetChatRoomStatisticRequest{
		RefService: ConvertServiceTypeToChatGRPCServiceType(order.ServiceType),
		RefId:      order.OrderID,
	})
	if err != nil {
		var apiError *api.Error
		ok := errors.As(err, &apiError)

		// We focus the case that Chat service response gRPC code 5 and error code `ROOM_NOT_FOUND`
		// If found, then we continue the logic. As we found this could happen on LIFF order that
		// rider can't contact user through chat and voip.
		// If we can't parse the error into the xgo's API Error, Then return right away as well.
		if (ok && apiError.Code != "ROOM_NOT_FOUND") || !ok {
			logx.Error().
				Context(ctx).
				Err(err).
				Str(CancelReasonNameLogKey, cancelReasonName).
				Str(logutil.DriverID, order.Driver).
				Str(logutil.OrderID, order.OrderID).
				Str(logutil.SentryErrorCode, FraudCashCollection1SentryLogCode).
				Msg("fail to get chat room statistic from chat service")
			safe.SentryError(err,
				safe.WithOrderID(order.OrderID),
				safe.WithDriverID(order.Driver),
				safe.WithTripID(order.TripID),
				safe.WithInfo(CancelReasonNameLogKey, cancelReasonName),
				safe.WithInfo(logutil.SentryErrorCode, FraudCashCollection1SentryLogCode),
			)
			return false
		}
		logx.Info().
			Context(ctx).
			Err(err).
			Str(CancelReasonNameLogKey, cancelReasonName).
			Str(logutil.DriverID, order.Driver).
			Str(logutil.OrderID, order.OrderID).
			Msg("chat service response ROOM_NOT_FOUND")
	}
	if result == nil {
		result = &chatv1.GetChatRoomStatisticResponse{}
	}
	focusedUserStat := f.getFocusedUserStatFromChatRoomStatistic(result, focusedDriverID)
	if focusedUserStat == nil {
		// If we can't find the user in chat stat response,
		// means that the rider have never contact the user in both chat and voip.
		// So passing down to the condition below by using empty value instead
		focusedUserStat = &chatv1.UserStatistic{}
	}

	messageCount := focusedUserStat.GetMessageCount()
	voipCount := focusedUserStat.GetVoipCallCount()

	hasRiderContactUserViaMessage, hasRiderContactUserErr := HasRiderContactUserViaMessage(order, messageCount)
	if hasRiderContactUserErr != nil {
		// The error of the HasRiderContactUserViaMessage should not block the main flow
		// and the result should be treated as true because it is the server fault.
		// Since making the hasRiderContactUserViaMessage true will always make the condition below false
		// So we just return false right away.
		// Note that this is unexpected value and action should be made once sentry is alerted.
		logx.Warn().
			Err(ErrInvalidContactCountNumber).
			Context(ctx).
			Str(CancelReasonNameLogKey, cancelReasonName).
			Str("data", messageCount.String()).
			Str(logutil.OrderID, order.OrderID).
			Str(logutil.DriverID, order.Driver).
			Int(ImageMessageCountLogKey, int(messageCount.GetImage())).
			Int(TextMessageCountLogKey, int(messageCount.GetText())).
			Int(StickerMessageCountLogKey, int(messageCount.GetSticker())).
			Int(LocationMessageCountLogKey, int(messageCount.GetLocation())).
			Msg("message count has invalid value")
		safe.SentryError(
			ErrInvalidContactCountNumber,
			safe.WithInfo(logutil.SentryErrorCode, FraudCashCollectionInvalidContactCountSentryLogCode),
		)
		return false
	}
	if !hasRiderContactUserViaMessage {
		logx.Info().
			Context(ctx).
			Str(logutil.OrderID, order.OrderID).
			Str(logutil.DriverID, order.Driver).
			Str(CancelReasonNameLogKey, cancelReasonName).
			Int(ImageMessageCountLogKey, int(messageCount.GetImage())).
			Int(TextMessageCountLogKey, int(messageCount.GetText())).
			Int(StickerMessageCountLogKey, int(messageCount.GetSticker())).
			Int(LocationMessageCountLogKey, int(messageCount.GetLocation())).
			Msg("rider has been identified as no contact user via chat")
	}

	hasRiderContactUserViaVOIP, hasRiderContactUserErr := HasRiderContactUserViaVOIP(voipCount)
	if hasRiderContactUserErr != nil {
		// The error of the HasRiderContactUserViaVOIP should not block the main flow
		// and the result should be treated as true because it is the server fault.
		// Since making the hasRiderContactUserViaVOIP true will always make the condition below false
		// So we just return false right away.
		// Note that this is unexpected value and action should be made once sentry is alerted.
		logx.Warn().
			Err(ErrInvalidContactCountNumber).
			Context(ctx).
			Str(CancelReasonNameLogKey, cancelReasonName).
			Str("data", messageCount.String()).
			Str(logutil.OrderID, order.OrderID).
			Str(logutil.DriverID, order.Driver).
			Int(VOIPCalledCountLogKey, int(voipCount.GetCalled())).
			Int(VOIPNoAnswerCountLogKey, int(voipCount.GetNoAnswer())).
			Msg("voip count has invalid value")
		safe.SentryError(
			ErrInvalidContactCountNumber,
			safe.WithInfo(logutil.SentryErrorCode, FraudCashCollectionInvalidContactCountSentryLogCode),
		)
		return false
	}
	if !hasRiderContactUserViaVOIP {
		logx.Info().
			Context(ctx).
			Str(logutil.OrderID, order.OrderID).
			Str(logutil.DriverID, order.Driver).
			Str(CancelReasonNameLogKey, cancelReasonName).
			Int(VOIPCalledCountLogKey, int(voipCount.GetCalled())).
			Int(VOIPNoAnswerCountLogKey, int(voipCount.GetNoAnswer())).
			Msg("rider has been identified as no contact user via voip")
	}

	return !hasRiderContactUserViaMessage &&
		!hasRiderContactUserViaVOIP &&
		order.PhoneContact.DriverID != focusedDriverID
}

func HasRiderContactUserViaMessage(order model.Order, messageCount *chatv1.MessageCount) (bool, error) {
	if messageCount == nil {
		return false, nil
	}

	if messageCount.GetLocation() < 0 ||
		messageCount.GetImage() < 0 ||
		messageCount.GetSticker() < 0 ||
		messageCount.GetText() < 0 {
		return false, ErrInvalidContactCountNumber
	}

	// Some of the order require the order to send the delivering photo,
	// (Those order type can be identified by using the order.IsRequireDeliveringPhotoURL flag.)
	// which chat service will also count that one as a message from rider.
	// We don't want to count that one as it also included in the main flow
	// So this logic is to increase the number of the minimum quota by 1 to skip that.
	imageQuota := int32(0)
	if order.IsRequireDeliveringPhotoURL {
		imageQuota++
	}

	return messageCount.GetLocation() > 0 ||
		messageCount.GetImage() > imageQuota ||
		messageCount.GetSticker() > 0 ||
		messageCount.GetText() > 0, nil
}

func HasRiderContactUserViaVOIP(voipCount *chatv1.VoIPCallCount) (bool, error) {
	if voipCount == nil {
		return false, nil
	}

	if voipCount.GetCalled() < 0 ||
		voipCount.GetNoAnswer() < 0 {
		return false, ErrInvalidContactCountNumber
	}

	return voipCount.GetCalled() != 0 ||
		voipCount.GetNoAnswer() != 0, nil
}

func (f service) getFocusedUserStatFromChatRoomStatistic(result *chatv1.GetChatRoomStatisticResponse, driverID string) *chatv1.UserStatistic {
	userStats := result.GetUserStats()
	var focusedUserStat *chatv1.UserStatistic
	for _, userStat := range userStats {
		if userStat == nil {
			continue
		}
		if userStat.GetUserId() == driverID &&
			userStat.GetUserType() == chatv1.UserType_USER_TYPE_DRIVER {
			focusedUserStat = userStat
			break
		}
	}
	return focusedUserStat
}
