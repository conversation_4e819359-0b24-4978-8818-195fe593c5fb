package service

import (
	"context"
	"time"

	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
)

//go:generate mockgen -source=./shift_service.go -destination=./mock_service/mock_shift_services.go -package=mock_service

type ShiftServices interface {
	UnBookMultipleShift(ctx context.Context, driverID string, shiftID []string) error
	GetShiftIDInPeriodByDriverID(ctx context.Context, driverID string, start time.Time, end time.Time) ([]string, error)
	RemoveShiftByTime(ctx context.Context, driverID string, start, end time.Time) ([]string, error)
}

type ShiftService struct {
	shiftRepo  repository.ShiftRepository
	driverRepo repository.DriverRepository
	txnHelper  transaction.TxnHelper
}

func (s *ShiftService) UnBookMultipleShift(ctx context.Context, driverID string, shiftID []string) error {
	if len(shiftID) == 0 {
		return nil
	}

	_, err := s.txnHelper.WithTxn(ctx, func(ctx context.Context) (interface{}, error) {
		err := s.shiftRepo.UnBookMultipleShift(ctx, driverID, shiftID)
		if err != nil {
			return nil, err
		}

		err = s.driverRepo.RemoveShifts(ctx, driverID, shiftID)
		return nil, err
	}, transaction.WithLabel("ShiftService.UnBookMultipleShift"))

	return err
}

func (s *ShiftService) GetShiftIDInPeriodByDriverID(ctx context.Context, driverID string, start time.Time, end time.Time) ([]string, error) {
	var shiftIDs []string
	found, _, err := s.shiftRepo.Find(ctx, repository.ShiftQuery{
		StartDate: start,
		DriverIDs: []string{driverID},
	}, 0, 100, repository.WithReadSecondaryPreferred)

	if err != nil {
		return shiftIDs, err
	}

	for _, shift := range found {
		if end.IsZero() {
			shiftIDs = append(shiftIDs, shift.ID.Hex())
		} else {
			if shift.End.Before(end) || shift.End.Equal(end) {
				shiftIDs = append(shiftIDs, shift.ID.Hex())
			}
		}
	}

	return shiftIDs, nil
}

func (s *ShiftService) RemoveShiftByTime(ctx context.Context, driverID string, start, end time.Time) (id []string, err error) {

	sID, err := s.GetShiftIDInPeriodByDriverID(ctx, driverID, start, end)
	if err != nil {
		return []string{}, err
	}

	err = s.UnBookMultipleShift(ctx, driverID, sID)
	if err != nil {
		return []string{}, err
	}

	return sID, nil
}

func ProvideShiftServices(shiftRepo repository.ShiftRepository, driverRepo repository.DriverRepository, txn transaction.TxnHelper) ShiftServices {
	return &ShiftService{
		shiftRepo:  shiftRepo,
		driverRepo: driverRepo,
		txnHelper:  txn,
	}
}
