package service

import (
	"context"
	"time"

	"github.com/kelseyhightower/envconfig"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	driverv1 "git.wndv.co/lineman/fleet-distribution/internal/gen/proto/lineman/event/driver/v1"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
)

type OrderDistributionEventService interface {
	Publish(ctx context.Context, event OrderDistributionEvent) error
}

type OrderDistributionEventServiceImpl struct {
	cfg      OrderDistributionEventServiceConfig
	producer kafcclient.SecureIMFKafkaProducer
}

func (m OrderDistributionEventServiceImpl) Publish(ctx context.Context, data OrderDistributionEvent) error {
	event := m.newEvent(data.GetEventTime(), data.GetEventName(), data.GetOrder(), data.GetDriverID())
	ctx = safe.NewContextWithSameWaitGroup(ctx)
	safe.GoFunc(func() {
		if err := m.sendMessage(ctx, event.OrderId, event); err != nil {
			logx.Errorf(ctx, err, "cannot send message %s event for orderId=%s", data.GetEventName(), event.OrderId)
		}
	})
	return nil
}

func NewOrderDistributionEventService(cfg OrderDistributionEventServiceConfig, producer kafcclient.SecureIMFKafkaProducer) OrderDistributionEventService {
	return OrderDistributionEventServiceImpl{
		cfg:      cfg,
		producer: producer,
	}
}

func (m OrderDistributionEventServiceImpl) sendMessage(ctx context.Context, key string, event *driverv1.OrderDistributionEvent) error {
	message, err := proto.Marshal(event)
	if err != nil {
		return err
	}

	return m.producer.SendMessage(ctx, m.cfg.TopicName, key, message, map[string]string{})
}

func (m OrderDistributionEventServiceImpl) newEvent(eventTime time.Time, eventName model.OrderDistributionEventName, order *model.Order, driverID *string) *driverv1.OrderDistributionEvent {
	return &driverv1.OrderDistributionEvent{
		EventTime:       timestamppb.New(eventTime),
		OrderId:         order.OrderID,
		ServiceType:     string(order.ServiceType),
		DriverId:        driverID,
		CreatedAt:       timestamppb.New(order.CreatedAt),
		EventName:       string(eventName),
		FromLocationLat: order.Routes[0].Location.Lat,
		FromLocationLng: order.Routes[0].Location.Lng,
	}
}

func ProvideOrderDistributionEventService(
	cfg OrderDistributionEventServiceConfig,
	producer kafcclient.SecureIMFKafkaProducer,
) OrderDistributionEventService {
	return NewOrderDistributionEventService(cfg, producer)
}

type OrderDistributionEventServiceConfig struct {
	TopicName string `envconfig:"ORDER_DISTRIBUTION_EVENT_TOPIC"`
}

func ProvideOrderDistributionEventServiceConfig() (cfg OrderDistributionEventServiceConfig) {
	envconfig.MustProcess("", &cfg)
	return
}

type OrderDistributionEvent interface {
	GetEventName() model.OrderDistributionEventName
	GetOrder() *model.Order
	GetEventTime() time.Time
	GetDriverID() *string
}
