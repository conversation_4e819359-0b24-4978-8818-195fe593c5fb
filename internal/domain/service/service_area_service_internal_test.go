package service

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	apiErrors "git.wndv.co/lineman/fleet-distribution/internal/apis/errors"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
)

func TestGetBackToBackAllowStatus(t *testing.T) {
	region := model.NewRegion("TEST", "Test region")

	t.Run("extend status from DB config", func(t *testing.T) {
		s, d, f := newTestServiceAreaService(t)
		defer f()

		ad := &model.AutoAssignDistribution{
			BackToBackExtendStatusConfig: true,
		}

		sa := &model.ServiceArea{}
		sa.SetDistribution(ad)

		d.serviceAreaRepo.EXPECT().
			GetByRegion(gomock.Any(), region.Code().String()).
			Return(sa, nil)

		allowStatus := s.GetBackToBackAllowStatus(context.Background(), region.Code().String())
		expected := model.StatusFromStringList(s.atomicPredictionServiceConfig.Get().BackToBackAllowStatus)
		assert.Equal(t, expected, allowStatus)
	})

	t.Run("use status from service area config", func(t *testing.T) {
		s, d, f := newTestServiceAreaService(t)
		defer f()

		ad := &model.AutoAssignDistribution{
			BackToBackExtendStatusConfig: false,
			BackToBackAllowStatus: []model.Status{
				model.StatusDriverToRestaurant,
			},
		}

		sa := &model.ServiceArea{}
		sa.SetDistribution(ad)

		d.serviceAreaRepo.EXPECT().
			GetByRegion(gomock.Any(), region.Code().String()).
			Return(sa, nil)

		allowStatus := s.GetBackToBackAllowStatus(context.Background(), region.Code().String())

		assert.Equal(t, ad.BackToBackAllowStatus, allowStatus)
	})

	t.Run("error from get service area", func(t *testing.T) {
		s, d, f := newTestServiceAreaService(t)
		defer f()

		sa := &model.ServiceArea{}

		d.serviceAreaRepo.EXPECT().
			GetByRegion(gomock.Any(), region.Code().String()).
			Return(sa, apiErrors.ErrServiceAreaNotExists())

		allowStatus := s.GetBackToBackAllowStatus(context.Background(), region.Code().String())
		expected := model.StatusFromStringList(s.atomicPredictionServiceConfig.Get().BackToBackAllowStatus)
		assert.Equal(t, expected, allowStatus)
	})
}

func TestRemoveDriverFromShiftWhitelist(t *testing.T) {
	region := model.NewRegion("TEST", "Test region")

	t.Run("remove driver from whitelist correctly", func(t *testing.T) {
		s, d, f := newTestServiceAreaService(t)
		defer f()

		sa := &model.ServiceArea{}
		sa.SetShiftDriverIDs([]string{
			"a",
			"b",
		})

		d.serviceAreaRepo.EXPECT().
			GetByRegionWithoutCache(gomock.Any(), region.Code().String()).
			Return(sa, nil)

		d.serviceAreaRepo.EXPECT().
			SetShiftDriverIDs(gomock.Any(), gomock.Any(), gomock.Any(), []string{"b"}).
			Return(nil)

		err := s.RemoveDriverFromShiftWhitelist(context.Background(), "a", "TEST")
		require.NoError(t, err)
	})

	t.Run("do nothing if there are no shift driver ids", func(t *testing.T) {
		s, d, f := newTestServiceAreaService(t)
		defer f()

		sa := &model.ServiceArea{}
		sa.SetShiftDriverIDs([]string{
			"a",
			"b",
		})

		d.serviceAreaRepo.EXPECT().
			GetByRegionWithoutCache(gomock.Any(), region.Code().String()).
			Return(sa, nil)

		d.serviceAreaRepo.EXPECT().
			SetShiftDriverIDs(gomock.Any(), gomock.Any(), gomock.Any(), []string{"b"}).
			Return(nil)

		err := s.RemoveDriverFromShiftWhitelist(context.Background(), "a", "TEST")
		require.NoError(t, err)
	})

	t.Run("error from service area not exists", func(t *testing.T) {
		s, d, f := newTestServiceAreaService(t)
		defer f()

		sa := &model.ServiceArea{}
		sa.SetShiftDriverIDs([]string{
			"a",
			"b",
		})
		d.serviceAreaRepo.EXPECT().
			GetByRegionWithoutCache(gomock.Any(), region.Code().String()).
			Return(nil, apiErrors.ErrServiceAreaNotExists())
		err := s.RemoveDriverFromShiftWhitelist(context.Background(), "a", "TEST")
		require.Error(t, err)
	})
}

func TestServiceAreaServiceImpl_GetCompleteTimeCalculationParams(t *testing.T) {
	t.Run("should returns global config params when cannot get service area", func(t *testing.T) {
		ctx := context.Background()
		service, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		deps.serviceAreaRepo.EXPECT().
			GetByRegion(gomock.Any(), "TEST_REGION").
			Return(nil, apiErrors.ErrServiceAreaNotExists())

		params := service.GetCompleteTimeCalculationConfig(ctx, "TEST_REGION")
		require.Equal(t, &config.CompleteTimeCalculation{
			EnableSyncDelivery: true,
			MeanAbsoluteError:  3*time.Minute + 13*time.Second,
			AbsoluteError:      7*time.Minute + 24*time.Second,
		}, params)
	})

	t.Run("should return global config params when there is no config in service area", func(t *testing.T) {
		ctx := context.Background()
		service, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		distribution := &model.AutoAssignDistribution{CompleteTimeCalculation: model.DefaultCompleteTimeCalculation}
		serviceArea := model.ServiceArea{}
		serviceArea.SetDistribution(distribution)

		deps.serviceAreaRepo.EXPECT().
			GetByRegion(gomock.Any(), "TEST_REGION").
			Return(&serviceArea, nil)

		params := service.GetCompleteTimeCalculationConfig(ctx, "TEST_REGION")
		require.Equal(t, &config.CompleteTimeCalculation{
			EnableSyncDelivery: true,
			MeanAbsoluteError:  3*time.Minute + 13*time.Second,
			AbsoluteError:      7*time.Minute + 24*time.Second,
		}, params)
	})

	t.Run("should override global config params when there is some config in service area", func(t *testing.T) {
		ctx := context.Background()
		service, deps, done := newTestServiceAreaService(t)
		t.Cleanup(done)

		mae := 9*time.Minute + 23*time.Second
		ae := 10*time.Minute + 34*time.Second

		distribution := &model.AutoAssignDistribution{
			CompleteTimeCalculation: model.CompleteTimeCalculation{
				MeanAbsoluteError: &model.Duration{Duration: mae},
				AbsoluteError:     &model.Duration{Duration: ae},
			},
		}
		serviceArea := model.ServiceArea{}
		serviceArea.SetDistribution(distribution)

		deps.serviceAreaRepo.EXPECT().
			GetByRegion(gomock.Any(), "TEST_REGION").
			Return(&serviceArea, nil)

		params := service.GetCompleteTimeCalculationConfig(ctx, "TEST_REGION")
		require.Equal(t, &config.CompleteTimeCalculation{
			EnableSyncDelivery: true,
			MeanAbsoluteError:  9*time.Minute + 23*time.Second,
			AbsoluteError:      10*time.Minute + 34*time.Second,
		}, params)
	})
}

func newTestServiceAreaService(t gomock.TestReporter) (*ServiceAreaServiceImpl, *serviceAreaServiceTestDeps, func()) {
	ctrl := gomock.NewController(t)

	serviceAreaRepository := mock_repository.NewMockServiceAreaRepository(ctrl)

	atomicPredictionServiceCfg := config.NewAtomicPredictionServiceConfig(
		config.PredictionServiceConfig{
			BackToBackAllowStatus: []string{string(model.StatusDriverMatched)},
		},
	)

	globalServiceAreaCfg := &config.AtomicGlobalServiceAreaConfig{}
	globalServiceAreaCfg.Parse()

	sv := &ServiceAreaServiceImpl{
		globalServiceAreaConfig:       globalServiceAreaCfg,
		atomicPredictionServiceConfig: atomicPredictionServiceCfg,
		serviceAreaRepo:               serviceAreaRepository,
	}
	deps := &serviceAreaServiceTestDeps{
		atomicPredictionServiceCfg: atomicPredictionServiceCfg,
		serviceAreaRepo:            serviceAreaRepository,
	}

	return sv, deps, func() {
		ctrl.Finish()
	}
}

type serviceAreaServiceTestDeps struct {
	atomicPredictionServiceCfg *config.AtomicPredictionServiceConfig
	serviceAreaRepo            *mock_repository.MockServiceAreaRepository
}
