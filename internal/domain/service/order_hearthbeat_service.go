package service

import (
	"context"
	"sync"
	"time"

	"github.com/kelseyhightower/envconfig"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

//go:generate mockgen -source=./order_hearthbeat_service.go -destination=./mock_service/mock_order_hearthbeat_service.go -package=mock_service
type OrderHeartbeatService interface {
	New(ctx context.Context, orderID string) error

	Done(ctx context.Context, orderID string) error
	DoneBatch(ctx context.Context, orderIDs []string) error
	DoneUnhealthy(ctx context.Context, orderIDs []string) error

	FindUnhealthyOrderIDs(ctx context.Context) ([]model.OrderHearthBeatStateWithOrderID, error)
	UpdateStateBatch(ctx context.Context, orders []model.OrderHearthBeatStateWithOrderID) error
}

type OrderHeartbeatServiceDbConfig struct {
	Enable             bool          `envconfig:"ORDER_HEARTH_BEAT_ENABLE"  default:"true"`
	Interval           time.Duration `envconfig:"ORDER_HEARTH_BEAT_INTERVAL"  default:"5s"`
	UnhealthyThreshold time.Duration `envconfig:"ORDER_HEARTH_BEAT_UNHEALTHY_THRESHOLD" default:"3m"`
}

type AtomicOrderHeartbeatServiceDbConfig struct {
	lock   sync.RWMutex
	Config OrderHeartbeatServiceDbConfig
}

func (cfg *AtomicOrderHeartbeatServiceDbConfig) Parse() {
	cfg.lock.Lock()
	defer cfg.lock.Unlock()
	envconfig.MustProcess("", &cfg.Config)
}

func (cfg *AtomicOrderHeartbeatServiceDbConfig) Get() OrderHeartbeatServiceDbConfig {
	cfg.lock.RLock()
	defer cfg.lock.RUnlock()
	return cfg.Config
}

func ProvideOrderHeartbeatServiceConfig() *AtomicOrderHeartbeatServiceDbConfig {
	cfg := new(AtomicOrderHeartbeatServiceDbConfig)
	envconfig.MustProcess("", cfg)
	return cfg
}

type OrderHeartbeatServiceImpl struct {
	config             *AtomicOrderHeartbeatServiceDbConfig
	orderHeartbeatRepo repository.OrderHeartbeatRepository

	repoLocker bool
	locker     sync.RWMutex
	orders     map[string]*model.OrderHearthBeatState
}

func ProvideOrderHeartbeatService(
	cfg *AtomicOrderHeartbeatServiceDbConfig,
	orderHeartbeatRepo repository.OrderHeartbeatRepository,
) *OrderHeartbeatServiceImpl {
	impl := &OrderHeartbeatServiceImpl{
		config:             cfg,
		orderHeartbeatRepo: orderHeartbeatRepo,
		repoLocker:         false,
		orders:             make(map[string]*model.OrderHearthBeatState),
	}

	safe.GoFunc(func() {
		tick := time.NewTicker(cfg.Get().Interval)
		for {
			select {
			case <-tick.C:
				if err := impl.Beat(); err != nil {
					logx.Error().Msgf("OrderHeartbeatService.Beat: Beat with error %s", err.Error())
				}
			}
		}
	})

	return impl
}

var _ OrderHeartbeatService = (*OrderHeartbeatServiceImpl)(nil)

func (svc *OrderHeartbeatServiceImpl) Orders() map[string]*model.OrderHearthBeatState {
	return svc.orders
}

func (svc *OrderHeartbeatServiceImpl) Beat() error {
	if len(svc.orders) < 1 {
		return nil
	}

	svc.WaitAndLockRepo()
	defer svc.UnlockRepo()

	ctx := context.Background()
	now := timeutil.UTCNow()

	svc.locker.Lock()
	orderIDs := make([]string, 0, len(svc.orders))
	states := make([]model.OrderHearthBeatStateWithOrderID, 0, len(svc.orders))
	for orderID, state := range svc.orders {
		orderIDs = append(orderIDs, orderID)
		states = append(states, model.OrderHearthBeatStateWithOrderID{
			OrderID: orderID,
			State:   state,
		})
	}
	svc.locker.Unlock()

	err := svc.orderHeartbeatRepo.UpdateHearthBeat(ctx, orderIDs, now)
	if err != nil {
		logx.Error().
			Interface("order_ids", orderIDs).
			Msgf("OrderHeartbeatService.Beat: failed UpdateHearthBeat count=%v, error=%s", len(orderIDs), err.Error())
		return err
	}

	err = svc.orderHeartbeatRepo.UpdateOrderRedistributionState(ctx, states)
	if err != nil {
		logx.Error().
			Interface("order_ids", orderIDs).
			Msgf("OrderHeartbeatService.Beat: failed UpdateOrderRedistributionState count=%v, error=%s", len(states), err.Error())
		return err
	}

	return nil
}

func (svc *OrderHeartbeatServiceImpl) New(ctx context.Context, orderID string) error {
	if !svc.config.Get().Enable {
		return nil
	}

	now := timeutil.UTCNow()
	if err := svc.orderHeartbeatRepo.UpdateHearthBeat(ctx, []string{orderID}, now); err != nil {
		logx.Error().
			Str("order_id", orderID).
			Msgf("OrderHeartbeatService.New: beating failed with error. err=%s", err.Error())
	}

	state := &model.OrderHearthBeatState{
		UnhealthyDistributeCount:     0,
		UnhealthyDistributeWaitUntil: 0,
	}
	if err := svc.orderHeartbeatRepo.UpdateOrderRedistributionState(ctx, []model.OrderHearthBeatStateWithOrderID{
		{OrderID: orderID, State: state},
	}); err != nil {
		logx.Error().
			Str("order_id", orderID).
			Msgf("OrderHeartbeatService.New: save redistribution state failed with error. err=%s", err.Error())
	}

	svc.locker.Lock()
	svc.orders[orderID] = state
	svc.locker.Unlock()
	return nil
}

func (svc *OrderHeartbeatServiceImpl) Done(ctx context.Context, orderID string) error {
	svc.WaitAndLockRepo()
	defer svc.UnlockRepo()

	svc.locker.Lock()
	delete(svc.orders, orderID)
	svc.locker.Unlock()

	if err := svc.orderHeartbeatRepo.RemoveOrderHearthBeat(ctx, []string{orderID}); err != nil {
		logx.Error().
			Str("order_id", orderID).
			Msgf("OrderHeartbeatService.DoneTask: remove heartbeat failed with error. err=%s", err.Error())
	}

	if err := svc.orderHeartbeatRepo.RemoveOrderRedistributionState(ctx, []string{orderID}); err != nil {
		logx.Error().
			Str("order_id", orderID).
			Msgf("OrderHeartbeatService.DoneTask: remove redistribution state failed with error. err=%s", err.Error())
	}

	return nil
}

func (svc *OrderHeartbeatServiceImpl) DoneBatch(ctx context.Context, orderIDs []string) error {
	if len(orderIDs) < 1 {
		return nil
	}

	svc.WaitAndLockRepo()
	defer svc.UnlockRepo()

	svc.locker.Lock()
	for _, orderID := range orderIDs {
		delete(svc.orders, orderID)
	}
	svc.locker.Unlock()

	if err := svc.orderHeartbeatRepo.RemoveOrderHearthBeat(ctx, orderIDs); err != nil {
		logx.Error().
			Interface("order_ids", orderIDs).
			Msgf("OrderHeartbeatService.DoneBatchTask: remove heartbeat failed with error. err=%s", err.Error())
	}

	if err := svc.orderHeartbeatRepo.RemoveOrderRedistributionState(ctx, orderIDs); err != nil {
		logx.Error().
			Interface("order_ids", orderIDs).
			Msgf("OrderHeartbeatService.DoneBatchTask: remove redistribution state failed with error. err=%s", err.Error())
	}

	return nil
}

func (svc *OrderHeartbeatServiceImpl) DoneUnhealthy(ctx context.Context, orderIDs []string) error {
	if len(orderIDs) < 1 {
		return nil
	}

	svc.WaitAndLockRepo()
	defer svc.UnlockRepo()

	orderScores, err := svc.orderHeartbeatRepo.GetUnhealthyOrderScore(ctx, svc.config.Get().UnhealthyThreshold)
	if err != nil {
		logx.Error().
			Interface("order_ids", orderIDs).
			Msgf("OrderHeartbeatService.DoneUnhealthy: cannot get unhealthy order to validate, will skip and remove all orders. err=%s", err.Error())
	}

	orderToRemove := make([]string, 0)
	if len(orderScores) > 0 {
		for _, orderID := range orderIDs {
			if _, ok := orderScores[orderID]; ok {
				orderToRemove = append(orderToRemove, orderID)
				continue
			}
		}
	} else {
		orderToRemove = append(orderToRemove, orderIDs...)
	}

	if err := svc.orderHeartbeatRepo.RemoveOrderHearthBeat(ctx, orderToRemove); err != nil {
		logx.Error().
			Interface("order_ids", orderIDs).
			Msgf("OrderHeartbeatService.DoneUnhealthy: remove heartbeat failed with error. err=%s", err.Error())
	}

	if err := svc.orderHeartbeatRepo.RemoveOrderRedistributionState(ctx, orderToRemove); err != nil {
		logx.Error().
			Interface("order_ids", orderIDs).
			Msgf("OrderHeartbeatService.DoneUnhealthy: remove redistribution state failed with error. err=%s", err.Error())
	}

	return nil
}

func (svc *OrderHeartbeatServiceImpl) FindUnhealthyOrderIDs(ctx context.Context) ([]model.OrderHearthBeatStateWithOrderID, error) {
	return svc.orderHeartbeatRepo.GetUnhealthyOrders(ctx, svc.config.Get().UnhealthyThreshold)
}

func (svc *OrderHeartbeatServiceImpl) UpdateStateBatch(ctx context.Context, orders []model.OrderHearthBeatStateWithOrderID) error {
	err := svc.orderHeartbeatRepo.UpdateOrderRedistributionState(ctx, orders)
	if err != nil {
		logx.Error().
			Interface("order_with_states", orders).
			Msgf("OrderHeartbeatService.UpdateStateBatch: save redistribution state failed with error. err=%s", err.Error())
		return err
	}
	return nil
}

func (svc *OrderHeartbeatServiceImpl) WaitAndLockRepo() {
	for svc.repoLocker {
		time.Sleep(250 * time.Millisecond)
	}
	svc.repoLocker = true
	return
}

func (svc *OrderHeartbeatServiceImpl) UnlockRepo() {
	svc.repoLocker = false
	return
}
