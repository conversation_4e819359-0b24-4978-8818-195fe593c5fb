package service

//go:generate mockgen -source=./driver_service.go -destination=./mock_service/mock_driver_service.go -package=mock_service
//go:generate mockgen -source=./driver_service.go -destination=./mock_driver_service.go -package=service

import (
	"context"
	cRand "crypto/rand"
	"fmt"
	"math/big"
	"strconv"
	"strings"
	"time"

	"github.com/avast/retry-go"
	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/absinthe/crypt"
	mongodb "git.wndv.co/lineman/absinthe/database/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	dt "git.wndv.co/lineman/fleet-distribution/internal/domain/service/drivertransaction"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence/setter"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/logutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/mathutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/sets"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
)

var (
	ErrRatingInvalidOrderStatus = errors.New("invalid order status")
	ErrAlreadyHaveUobRefID      = errors.New("already have uobRefID")
	ErrNotHaveUobRefID          = errors.New("not have uobRefID")
	ErrNoUobRefIDLeft           = errors.New("no uobRefID left")
	ErrDriverLock               = errors.New("driver is locked")
)

// DeviceManager manage driver device token.
type DeviceManager interface {
	// UpdateDeviceToken update device token, device id, and advertise id of this driver id.
	UpdateDeviceToken(ctx context.Context, driverID, token, deviceID, advertiseID string) error

	// UpdateSocketID update socket id of this driver id.
	UpdateSocketID(ctx context.Context, driverID, socketID string) error

	// GetDeviceToken get driver's device token. If driver doesn't have token. This
	// method will return error
	GetDeviceToken(ctx context.Context, driverID string) (string, error)

	// GetDeviceTokens get multiple device tokens at once. If driver doesn't have token, it will be empty string
	GetDeviceTokens(ctx context.Context, driverIDs []string) (tokens []string, err error)

	// GetSocketIDs get multiple socketId at once. If driver doesn't have token, it will be empty DriverSocketID
	GetSocketIDs(ctx context.Context, driverIDs []string) (tokens []model.DriverSocketID, err error)

	// GetSocketID
	GetSocketID(ctx context.Context, driverID string) (string, error)
}

func ProvideDeviceManagerImpl(dataStore persistence.DriversDataStore, c datastore.RedisClient) *DeviceManagerImpl {
	return &DeviceManagerImpl{
		DataStore: dataStore,
		Cache:     c,
	}
}

type DeviceManagerImpl struct {
	DataStore persistence.DriversDataStore
	Cache     datastore.RedisClient
}

var _ DeviceManager = &DeviceManagerImpl{}

func (d DeviceManagerImpl) UpdateDeviceToken(ctx context.Context, driverID, token, deviceID, advertiseID string) error {
	err := d.DataStore.Update(
		ctx,
		bson.M{"driver_id": driverID},
		bson.M{
			"$set": bson.M{
				"device_token": token,
				"device_id":    deviceID,
				"advertise_id": advertiseID,
			},
		},
	)
	if err != nil {
		return err
	}

	status, err := d.Cache.
		Set(ctx, cache.DriverDeviceTokenKey(driverID), token, cache.DeviceTokenKeyExpiration).
		Result()
	if err != nil {
		return err
	}
	logrus.Info(status)

	return nil
}

func (d DeviceManagerImpl) UpdateSocketID(ctx context.Context, driverID, socketID string) error {
	err := d.DataStore.Update(
		ctx,
		bson.M{"driver_id": driverID},
		bson.M{
			"$set": bson.M{
				"socket_id": socketID,
			},
		},
	)
	if err != nil {
		return err
	}

	_, err = d.Cache.
		Set(ctx, cache.DriverSocketIDKey(driverID), socketID, cache.DeviceTokenKeyExpiration).
		Result()
	if err != nil {
		return err
	}

	return nil
}

func (d DeviceManagerImpl) GetSocketID(ctx context.Context, driverID string) (string, error) {
	return d.Cache.Get(ctx, cache.DriverSocketIDKey(driverID)).Result()
}

func (d DeviceManagerImpl) GetDeviceToken(ctx context.Context, driverID string) (string, error) {
	return d.Cache.Get(ctx, cache.DriverDeviceTokenKey(driverID)).Result()
}

func (d DeviceManagerImpl) GetDeviceTokens(ctx context.Context, driverIDs []string) (tokens []string, err error) {
	var keys []string
	for _, id := range driverIDs {
		keys = append(keys, cache.DriverDeviceTokenKey(id))
	}
	return d.getByKeys(ctx, keys)
}

func (d DeviceManagerImpl) GetSocketIDs(ctx context.Context, driverIDs []string) (tokens []model.DriverSocketID, err error) {
	keys := make(map[string]string)
	for _, id := range driverIDs {
		keys[cache.DriverSocketIDKey(id)] = id
	}
	return d.getByMapKeys(ctx, keys)
}

func (d DeviceManagerImpl) getByKeys(ctx context.Context, keys []string) (results []string, err error) {
	pipe := d.Cache.Pipeline()
	for _, key := range keys {
		pipe.Get(ctx, key)
	}
	cmds, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}

	results = make([]string, len(keys))
	for i, cmd := range cmds {
		scmd, ok := cmd.(*redis.StringCmd)
		if !ok {
			results[i] = ""
			continue
		}
		result, err := scmd.Result()
		if err != nil {
			if err != redis.Nil {
				return nil, err
			}
			result = ""
		}
		results[i] = result
	}
	return results, nil
}

func (d DeviceManagerImpl) getByMapKeys(ctx context.Context, keys map[string]string) (results []model.DriverSocketID, err error) {
	pipe := d.Cache.Pipeline()
	for key := range keys {
		pipe.Get(ctx, key)
	}
	cmds, err := pipe.Exec(ctx)
	if err != nil && err != redis.Nil {
		return nil, err
	}

	results = make([]model.DriverSocketID, len(keys))
	for i, cmd := range cmds {
		scmd, ok := cmd.(*redis.StringCmd)
		if !ok {
			results[i] = model.DriverSocketID{}
			continue
		}
		result, err := scmd.Result()
		if err != nil {
			if err != redis.Nil {
				return nil, err
			}
			result = ""
		}
		driverSocketID := model.DriverSocketID{
			SocketID: result,
		}
		if len(scmd.Args()) > 1 {
			driverSocketID.DriverID = keys[scmd.Args()[1].(string)]
		}

		results[i] = driverSocketID
	}
	return results, nil
}

// Assigner manage assign/unassign order to driver.
type Assigner interface {
	// AssignOrderToDriver assign order to a driver. Driver status will changes to
	// TAKING_ORDER
	AssignOrderToDriver(ctx context.Context, driverID string, orderID string, allowQueueing bool, tripID string, startLocation model.Location, lockedUntil time.Time, isAcknowledgementRequired bool) error
	QueueB2bOrderToDriver(ctx context.Context, driver model.Driver, orderID string, tripID string, isLastAssignedB2B bool, lockedUntil time.Time) error
	QueueMoOrderToDriver(ctx context.Context, driver model.Driver, orderID string) error
	DequeueOrder(ctx context.Context, driver model.Driver) error
	DequeueTrip(ctx context.Context, driver model.Driver) error
	UnassignQueueOrder(ctx context.Context, driver model.Driver, orderID string) error

	// UnassignOrder from driver. This will change driver status back to ACTIVE. This method doesn't
	// care order status. So caller must be check order status before unassign driver.
	UnassignOrder(ctx context.Context, driverID string) (*model.Driver, error)
	UnAssignTrip(ctx context.Context, driverID string) (*model.Driver, error)
}

type Metric interface {
	AssignedBackedOrderMetric(region model.RegionCode)
	AcceptedDedicatedZoneMetric(region model.RegionCode, isPrioritized bool)
	AssignedMultipleOrderMetric(region model.RegionCode)
	CanceledBackedOrderMetric(region model.RegionCode)
}

// DriverServiceInterface combine all functionality that can do with driver.
type DriverServiceInterface interface {
	DeviceManager
	Assigner
	Metric

	// RatingDriver rates driver by given order.
	RatingDriver(gctx *gin.Context, score uint32, comment string, tags []string, order *model.Order) error

	// GenerateCitiRefID generate citi virtual account
	GenerateCitiRefID(prefix string) (crypt.LazyEncryptedString, error)

	// AssignUobRefToDriver assign uob virtual account to driver
	AssignUobRefToDriver(ctx context.Context, driver *model.Driver) error

	// UnAssignUobRefFromDriver remove uob virtual account from driver
	UnAssignUobRefFromDriver(ctx context.Context, driver *model.Driver) error

	// CreateDriver with driver registration and service area
	CreateDriver(context context.Context, regis *model.DriverRegistration, area *model.ServiceArea) (*model.Driver, error)

	// AddCompletedOrderQuota adds cancellation rate free quota
	AddCompletedOrderQuota(context context.Context, driver *model.Driver) error

	// GetProfileWithLatestWithdrawalQuota gets the latest withdrawal quota that is updated for today use
	GetProfileWithLatestWithdrawalQuota(ctx context.Context, driverID string) (*model.Driver, error)

	// AddWithdrawalQuota adds quota to withdrawal quota model
	AddWithdrawalQuota(ctx context.Context, driverID string) error

	OfflineDriver(ctx context.Context, driver *model.Driver) error

	// TryLockDriver lock driver with retry logic
	TryLockDriver(ctx context.Context, driverID string, opts ...LockDriverOptionsFunc) error

	// UnlockDriver unlock driver
	UnlockDriver(ctx context.Context, driverID string) error

	UpdateDriverLastAttempt(ctx context.Context, driverID string, opt ...SetDriverLatestAttemptOptionFunc) error

	GetDriverLocationWithUpdatedAt(ctx context.Context, driverID string) (resp *model.LocationWithUpdatedAt, err error)

	TryUpdateLeavePrevStopAt(ctx context.Context, currentTrip *model.Trip)

	GetOrCreateOnTopQuota(ctx context.Context, driverID string, scheme model.OnTopFare, quotaType model.OnTopQuotaType) (model.OnTopQuota, error)

	DeductOnTopQuota(ctx context.Context, driverID string, ots []model.OnTopScheme) error

	IncrementOnTopQuota(ctx context.Context, driv *model.Driver, ots []model.OnTopScheme) error
}

type DriverService struct {
	DataStore                   persistence.DriversDataStore
	Cache                       datastore.RedisClient
	DriverLocationRepository    repository.DriverLocationRepository
	AuditLogRepo                repository.AuditLogRepository
	DriverRepository            repository.DriverRepository
	TransRepo                   repository.TransactionRepository
	TripRepository              repository.TripRepository
	OrderRepository             repository.OrderRepository
	UobRefRepository            repository.UobRefRepository
	ServiceAreaRepository       repository.ServiceAreaRepository
	DriverActiveTimeRepository  repository.DriverActiveTimeRepository
	RatingCfg                   config.DriverRatingConfig
	PaymentCfg                  config.PaymentConfig
	GlobalConfig                config.GlobalConfig
	CancellationRateCfg         *config.AtomicCancellationRateConfig
	assignedBackedOrderMetric   metric.Counter
	acceptedDedicatedZoneMetric metric.Counter
	assignedMultipleOrderMetric metric.Counter
	canceledBackedOrderMetric   metric.Counter
	lockingDriverRetryMetric    metric.Histogram
	locker                      locker.Locker
	driverServiceCfg            *AtomicDriverServiceConfig
	deviceManager               DeviceManager
	delivery                    delivery.Delivery
}

func NewDriverService(
	drivds persistence.DriversDataStore,
	cache datastore.RedisClient,
	auditLogRepo repository.AuditLogRepository,
	driverLocationRepository repository.DriverLocationRepository,
	driverRepository repository.DriverRepository,
	transRepo repository.TransactionRepository,
	tripRepository repository.TripRepository,
	orderRepository repository.OrderRepository,
	uobRefRepository repository.UobRefRepository,
	serviceAreaRepository repository.ServiceAreaRepository,
	driverActiveTimeRepository repository.DriverActiveTimeRepository,
	ratingCfg config.DriverRatingConfig,
	paymentCfg config.PaymentConfig,
	globalConfig config.GlobalConfig,
	cancellationRateCfg *config.AtomicCancellationRateConfig,
	meter metric.Meter,
	locker locker.Locker,
	driverServiceCfg *AtomicDriverServiceConfig,
	deviceManager DeviceManager,
	delivery delivery.Delivery,
) *DriverService {
	return &DriverService{
		DataStore:                   drivds,
		Cache:                       cache,
		AuditLogRepo:                auditLogRepo,
		DriverLocationRepository:    driverLocationRepository,
		DriverRepository:            driverRepository,
		TripRepository:              tripRepository,
		TransRepo:                   transRepo,
		OrderRepository:             orderRepository,
		UobRefRepository:            uobRefRepository,
		ServiceAreaRepository:       serviceAreaRepository,
		DriverActiveTimeRepository:  driverActiveTimeRepository,
		RatingCfg:                   ratingCfg,
		PaymentCfg:                  paymentCfg,
		GlobalConfig:                globalConfig,
		CancellationRateCfg:         cancellationRateCfg,
		assignedBackedOrderMetric:   meter.GetCounter("backed_order_assigned", "Number of notified drivers accepted B2B", "region"),
		assignedMultipleOrderMetric: meter.GetCounter("multiple_order_assigned", "Number of notified drivers accepted MO", "region"),
		acceptedDedicatedZoneMetric: meter.GetCounter("dedicated_zone_order_accepted", "Number of dedicated zone rider accepting an order", "region", "isPrioritized"),
		canceledBackedOrderMetric:   meter.GetCounter("backed_order_canceled", "Number of notified drivers canceled B2B", "region"),
		lockingDriverRetryMetric:    meter.GetHistogram("locking_driver_retry", "Retry count on locking driver", []float64{1, 2, 3, 4, 5, 6, 7, 8, 9, 10}),
		locker:                      locker,
		driverServiceCfg:            driverServiceCfg,
		deviceManager:               deviceManager,
		delivery:                    delivery,
	}
}

func (s *DriverService) AssignedBackedOrderMetric(region model.RegionCode) {
	s.assignedBackedOrderMetric.Inc(region.String())
}

func (s *DriverService) AcceptedDedicatedZoneMetric(region model.RegionCode, isPrioritized bool) {
	s.acceptedDedicatedZoneMetric.Inc(region.String(), strconv.FormatBool(isPrioritized))
}

func (s *DriverService) AssignedMultipleOrderMetric(region model.RegionCode) {
	s.assignedMultipleOrderMetric.Inc(region.String())
}

func (s *DriverService) CanceledBackedOrderMetric(region model.RegionCode) {
	s.canceledBackedOrderMetric.Inc(region.String())
}

func (s *DriverService) LogLockingDriverRetry(retry int) {
	s.lockingDriverRetryMetric.Observe(float64(retry))
}

func (s *DriverService) UpdateDeviceToken(ctx context.Context, driverID, token, deviceID, advertiseID string) error {
	return s.deviceManager.UpdateDeviceToken(ctx, driverID, token, deviceID, advertiseID)
}

func (s *DriverService) UpdateSocketID(ctx context.Context, driverID, socketID string) error {
	return s.deviceManager.UpdateSocketID(ctx, driverID, socketID)
}

func (s *DriverService) GetDeviceToken(ctx context.Context, driverID string) (string, error) {
	return s.deviceManager.GetDeviceToken(ctx, driverID)
}

func (s *DriverService) GetSocketID(ctx context.Context, driverID string) (string, error) {
	return s.deviceManager.GetSocketID(ctx, driverID)
}

func (s *DriverService) GetDeviceTokens(ctx context.Context, driverIDs []string) (tokens []string, err error) {
	return s.deviceManager.GetDeviceTokens(ctx, driverIDs)
}

func (s *DriverService) GetSocketIDs(ctx context.Context, driverIDs []string) (tokens []model.DriverSocketID, err error) {
	return s.deviceManager.GetSocketIDs(ctx, driverIDs)
}

func (s *DriverService) AddCompletedOrderQuota(ctx context.Context, driver *model.Driver) error {
	cancellationRateCfg := s.CancellationRateCfg.Get()
	driver.AddCompletedOrderQuota(cancellationRateCfg)
	return s.DriverRepository.SetCancellationQuota(ctx, driver.DriverID, *driver.CancellationQuota)
}

// AssignOrderToDriver implements DriverServiceInterface.
func (s *DriverService) AssignOrderToDriver(ctx context.Context, driverID string, orderID string, allowQueueing bool, tripID string, startLocation model.Location, lockedUntil time.Time, isAcknowledgementRequired bool) error {
	now := time.Now()
	q := bson.M{
		"current_order":                    orderID,
		"status":                           model.StatusAssigned,
		"allow_queueing":                   allowQueueing,
		"last_prediction_disruption":       now,
		"last_accept_attempt_at":           now,
		"last_online_to_assigned_location": startLocation,
		"is_acknowledgement_required":      isAcknowledgementRequired,
		"last_accepted_order_id":           orderID,
		"last_accepted_order_time":         now,
	}

	if isAcknowledgementRequired {
		logrus.Infof("[AcknowledgementDetail] %v is required to acknowledge %v at %v", driverID, tripID, timeutil.BangkokNow())
	}

	if tripID != "" {
		q["current_trip"] = tripID
	}

	if !lockedUntil.IsZero() {
		q["locked_from_queueing_until"] = lockedUntil
	}

	err := s.DataStore.Update(
		ctx,
		bson.M{
			"driver_id": driverID,
			// LMF-5656: Temporary allow driver to accept order even if he/she is OFFLINE
			// "status":    model.StatusOnline,
		},
		bson.M{
			"$set": q,
		},
	)
	if err != nil {
		return err
	}

	return cache.SetDriverStatusAndCurrentOrder(ctx, s.Cache, driverID, model.StatusAssigned, orderID, []string{}, allowQueueing)
}

func (s *DriverService) QueueB2bOrderToDriver(ctx context.Context, driver model.Driver, orderID string, tripID string, isLastAssignedB2B bool, lockedUntil time.Time) error {
	now := time.Now()
	hasB2B := len(driver.QueueingTrips) != 0

	var queueingOrders []string
	var queueingTrips []string

	if isLastAssignedB2B {
		queueingOrders = append(driver.QueueingOrders, orderID)
		queueingTrips = append(driver.QueueingTrips, tripID)
	} else {
		queueingOrders = append([]string{orderID}, driver.QueueingOrders...)
		queueingTrips = append([]string{tripID}, driver.QueueingTrips...)
	}

	err := s.DataStore.Update(
		ctx,
		bson.M{
			"driver_id":    driver.DriverID,
			"status":       model.StatusAssigned,
			"current_trip": driver.CurrentTrip,
		},
		bson.M{
			"$set": bson.M{
				"queueing_orders":            queueingOrders,
				"queueing_trips":             queueingTrips,
				"last_prediction_disruption": now,
				"last_accept_attempt_at":     now,
				"locked_from_queueing_until": lockedUntil,
			},
		},
	)

	if (err != nil && err != mongodb.ErrDataNotFound) || (!hasB2B && err == mongodb.ErrDataNotFound) {
		return err
	}

	if err == mongodb.ErrDataNotFound {
		queueingOrders = []string{orderID}
		queueingTrips = []string{tripID}

		if err = s.DataStore.Update(
			ctx,
			bson.M{
				"driver_id":    driver.DriverID,
				"status":       model.StatusAssigned,
				"current_trip": driver.QueueingTrips[0],
			},
			bson.M{
				"$set": bson.M{
					"queueing_orders":            queueingOrders,
					"queueing_trips":             queueingTrips,
					"last_prediction_disruption": now,
					"last_accept_attempt_at":     now,
					"locked_from_queueing_until": lockedUntil,
				},
			},
		); err != nil {
			return err
		}
	}

	return cache.SetQueueingOrders(ctx, s.Cache, driver.DriverID, queueingOrders)
}

func (s *DriverService) QueueMoOrderToDriver(ctx context.Context, driver model.Driver, orderID string) error {
	driver.QueueingOrders = append(driver.QueueingOrders, orderID)

	now := time.Now()
	if err := s.DataStore.Update(
		ctx,
		bson.M{
			"driver_id":    driver.DriverID,
			"status":       model.StatusAssigned,
			"current_trip": driver.CurrentTrip,
		},
		bson.M{
			"$set": bson.M{
				"queueing_orders":            driver.QueueingOrders,
				"last_prediction_disruption": now,
				"last_accept_attempt_at":     now,
			},
		},
	); err != nil {
		return err
	}
	return cache.SetQueueingOrders(ctx, s.Cache, driver.DriverID, driver.QueueingOrders)
}

func (s *DriverService) DequeueOrder(ctx context.Context, driver model.Driver) error {
	err := s.DataStore.Update(
		ctx,
		bson.M{
			"driver_id": driver.DriverID,
		},
		bson.M{
			"$set": bson.M{
				"current_order":   driver.QueueingOrders[0],
				"queueing_orders": driver.QueueingOrders[1:],
				"allow_queueing":  true,
			},
		},
	)
	if err != nil {
		return err
	}
	return cache.SetDriverStatusAndCurrentOrder(ctx, s.Cache, driver.DriverID, driver.Status, driver.QueueingOrders[0], driver.QueueingOrders[1:], true)
}

func (s *DriverService) DequeueTrip(ctx context.Context, driver model.Driver) error {
	if len(driver.QueueingTrips) == 0 {
		return s.DequeueOrder(ctx, driver)
	}

	nextTrip, err := s.TripRepository.GetTripByTripID(ctx, driver.QueueingTrips[0])
	if err != nil {
		return err
	}
	s.TryUpdateLeavePrevStopAt(ctx, &nextTrip)

	previousTrip := driver.CurrentTrip
	currentOrder := nextTrip.Routes[0].StopOrders[0].OrderID
	queueingOrders := sets.New(driver.QueueingOrders...).Without(nextTrip.GetOngoingOrderIDs()...).GetElements()

	err = s.DataStore.Update(
		ctx,
		bson.M{
			"driver_id": driver.DriverID,
		},
		bson.M{
			"$set": bson.M{
				"current_order":   currentOrder,
				"current_trip":    driver.QueueingTrips[0],
				"queueing_orders": queueingOrders,
				"queueing_trips":  driver.QueueingTrips[1:],
				"allow_queueing":  true,
			},
		},
	)
	if err != nil {
		return err
	}

	if err = s.TripRepository.SetPreviousTripID(ctx, driver.QueueingTrips[0], previousTrip); err != nil {
		return err
	}

	s.delivery.EmbarkedTripOrders(ctx, nextTrip.GetOngoingTripOrders())

	return cache.SetDriverStatusAndCurrentOrder(ctx, s.Cache, driver.DriverID, driver.Status, currentOrder, queueingOrders, true)
}

func (s *DriverService) TryUpdateLeavePrevStopAt(ctx context.Context, currentTrip *model.Trip) {
	if (currentTrip.Status != model.TripStatusDriveTo && currentTrip.Status != model.TripStatusArrivedAt) ||
		currentTrip.Routes[currentTrip.HeadTo].Action != model.TripActionPickUp {
		return
	}
	tripOrders, err := currentTrip.GetTripOrdersInRoute(currentTrip.HeadTo)
	if err != nil {
		logrus.Warnf("TryUpdateLeavePrevStopAt get tripOrders failed for tripID %s; error: %v", currentTrip.TripID, err)
	}
	hasDriverReallyEmbarked := false
	for _, o := range tripOrders {
		if o.Status != model.StatusDriverMatched && o.Status != model.StatusRestaurantAccepted {
			hasDriverReallyEmbarked = true
			break
		}
	}
	if !hasDriverReallyEmbarked {
		return
	}
	for _, o := range currentTrip.Routes[currentTrip.HeadTo].StopOrders {
		if o.StopID != 0 {
			continue
		}
		if err := s.OrderRepository.SetLeavePrevStopAt(ctx, o.OrderID); err != nil {
			logrus.Warnf("TryUpdateLeavePrevStopAt update db failed for order %s; error: %v", o.OrderID, err)
		}
	}
}

func (s *DriverService) GetOrCreateOnTopQuota(ctx context.Context, driverID string, otf model.OnTopFare, quotaType model.OnTopQuotaType) (model.OnTopQuota, error) {
	driver, err := s.DriverRepository.FindDriverID(ctx, driverID)
	if err != nil {
		return model.OnTopQuota{}, err
	}
	// return quota if exist
	for _, driverExistedOnTopQuota := range driver.OnTopQuotas {
		if driverExistedOnTopQuota.OnTopFareID == otf.ID {
			return driverExistedOnTopQuota, nil
		}
	}

	// if not existed then create new one
	var newOnTopQuota model.OnTopQuota
	newOnTopQuota, err = newOnTopQuota.CreateFromOnTopFare(otf, quotaType)
	if err != nil {
		return model.OnTopQuota{}, err
	}

	driver.UpsertNewOnTopQuota(newOnTopQuota)

	err = s.DriverRepository.SetOnTopQuota(ctx, driver.DriverID, driver.OnTopQuotas)
	logx.Info().Msgf("successfully created on-top quota for driver %s, onTopID: %s", driver.DriverID, newOnTopQuota.OnTopFareID)
	if err != nil {
		return model.OnTopQuota{}, err
	}

	return newOnTopQuota, nil
}

func (s *DriverService) UnassignQueueOrder(ctx context.Context, driver model.Driver, orderID string) error {
	var queueIndex int
	found := false
	for i, qid := range driver.QueueingOrders {
		if qid == orderID {
			queueIndex = i
			found = true
			break
		}
	}
	if !found {
		err := errors.New("trying to unassign a queue order, but the order is not in queue.")
		safe.SentryErrorMessage(err.Error(), safe.WithOrderID(orderID), safe.WithDriverID(driver.DriverID))
		return err
	}

	driver.QueueingOrders = append(driver.QueueingOrders[:queueIndex], driver.QueueingOrders[queueIndex+1:]...)
	err := s.DataStore.Update(
		ctx,
		bson.M{
			"driver_id": driver.DriverID,
		},
		bson.M{
			"$set": bson.M{
				"queueing_orders": driver.QueueingOrders,
				"allow_queueing":  true,
			},
		},
	)
	if err != nil {
		return err
	}
	return cache.SetDriverStatusAndCurrentOrder(ctx, s.Cache, driver.DriverID, driver.Status, driver.CurrentOrder, driver.QueueingOrders, true)
}

func (s *DriverService) UnAssignTrip(ctx context.Context, driverID string) (*model.Driver, error) {
	status := model.StatusOnline

	state, err := s.DriverRepository.CurrentStatus(ctx, driverID)
	if err != nil {
		return nil, err
	}
	if state.Status == string(model.StatusBanned) {
		status = model.StatusBanned
	}

	var driv *model.Driver
	driv, err = s.DriverRepository.UnAssignTrip(ctx, driverID, status)
	if err != nil {
		return driv, err
	}

	return driv, cache.SetDriverStatusAndCurrentOrder(ctx, s.Cache, driverID, status, "", []string{}, false)
}

// UnassignOrder implements DriverServiceInterface.
func (s *DriverService) UnassignOrder(ctx context.Context, driverID string) (*model.Driver, error) {
	status := model.StatusOnline

	state, err := s.DriverRepository.CurrentStatus(ctx, driverID)
	if err != nil {
		return nil, err
	}
	if state.Status == string(model.StatusBanned) {
		status = model.StatusBanned
	}

	var driv *model.Driver
	driv, err = s.DriverRepository.UnAssignOrder(ctx, driverID, status)
	if err != nil {
		return driv, err
	}

	return driv, cache.SetDriverStatusAndCurrentOrder(ctx, s.Cache, driverID, status, "", []string{}, false)
}

func (s *DriverService) SetTodayEarning(ctx context.Context, driverEarnings ...model.DriverEarning) error {
	return s.DriverRepository.SetTodayEarning(ctx, driverEarnings...)
}

func (s *DriverService) RatingDriver(gctx *gin.Context, score uint32, comment string, tags []string, order *model.Order) error {
	ctx := gctx.Request.Context()

	if order.Status != model.StatusDropOffDone && order.Status != model.StatusCompleted {
		return ErrRatingInvalidOrderStatus
	}

	order.RatingScore = score
	order.Comment = comment
	order.Tags = tags
	order.ExemptedWords = s.checkExemptedWordsOnRiderScoreRating(score, comment)
	order.HasExemptedWords = len(order.ExemptedWords) > 0

	if err := s.OrderRepository.UpdateOrder(ctx, order); err != nil {
		return err
	}

	if order.HasExemptedWords {
		logrus.Warnf("[RatingDriver]: order id [%v] have invalid rating [%v]: %v", order.OrderID, score, comment)
		return nil
	}

	driv, err := s.DriverRepository.GetProfile(ctx, order.Driver)
	if err != nil {
		return err
	}

	setScoreErr := driv.SetNewScore(score, s.RatingCfg)

	if err := s.DriverRepository.SetRatingScore(ctx, *driv); err != nil {
		return err
	}

	// TODO LMF-3687: Remove logic for handling latest ratings after 3 months
	if setScoreErr == model.ErrMissingSMARating {
		goRoutineCtx := gctx.Copy()
		safe.GoFuncWithCtx(goRoutineCtx, func() {
			latestRatings, err := s.OrderRepository.FindLatestRatings(goRoutineCtx, driv.DriverID, driv.GetMovingAverageSize(s.RatingCfg))
			if err != nil {
				logrus.Errorf("Unable to find latest ratings on driver %s", driv.DriverID)
				return
			}

			newAverage := mathutil.AverageUint32(latestRatings)

			err = s.DriverRepository.SetSMARating(goRoutineCtx, driv.DriverID, latestRatings, newAverage)
			if err != nil {
				logrus.Errorf("Unable to set latest ratings on driver %s", driv.DriverID)
			}
		})
	}

	return nil
}

func (s *DriverService) checkExemptedWordsOnRiderScoreRating(userScore uint32, comment string) []string {
	if s.driverServiceCfg == nil {
		return []string{}
	}

	cfg := s.driverServiceCfg.Get()
	words := cfg.RiderRatingScoreIgnoreWords
	checkedScore := cfg.RiderRatingMinScoreCheckIgnore

	if userScore > checkedScore || len(words) == 0 {
		return []string{}
	}

	exemptedWords := make([]string, 0)
	for _, w := range words {
		trimWord := strings.TrimSpace(w)
		if strings.Contains(comment, trimWord) {
			exemptedWords = append(exemptedWords, trimWord)
		}
	}

	return exemptedWords
}

func (s *DriverService) GenerateCitiRefID(prefix string) (crypt.LazyEncryptedString, error) {
	nBig, err := cRand.Int(cRand.Reader, big.NewInt(999999))
	if err != nil {
		return crypt.NewLazyEncryptedString(""), err
	}
	uniqueNumber := nBig.Int64() + 1
	refIDStr := fmt.Sprintf("%04s%06d", prefix, uniqueNumber)

	return crypt.NewLazyEncryptedString(refIDStr), nil
}

func (s *DriverService) AssignUobRefToDriver(ctx context.Context, driver *model.Driver) error {
	if driver.DriverUOBRefID() != "" {
		return ErrAlreadyHaveUobRefID
	}

	area, err := s.ServiceAreaRepository.GetByRegion(ctx, driver.Region.String())
	if err != nil {
		return err
	}

	if !area.TopUpConfig.GenerateUobRefID {
		return nil
	}

	return s.doAssignUobRefIDToDriver(ctx, driver)
}

func (s *DriverService) doAssignUobRefIDToDriver(ctx context.Context, driver *model.Driver) error {
	return retry.Do(
		func() error {
			uobRef, err := s.UobRefRepository.RandomFindOne(ctx)
			if err != nil {
				if err == repository.ErrNotFound {
					return ErrNoUobRefIDLeft
				}
				return err
			}

			// prevent race condition
			if err := s.UobRefRepository.UpdateDriverID(ctx, uobRef, driver.DriverID); err != nil {
				logrus.Warn("update uobRef error: ", err.Error())
				return err
			}
			if err := s.DriverRepository.UpdateUobRefID(ctx, driver, uobRef.UobRefID); err != nil {
				logrus.Warn("update driver error: ", err.Error())
				return err
			}

			logrus.Warn("AssignUobRef to driverID: ", driver.DriverID)

			driver.Banking.UOBRefID = uobRef.UobRefID

			afterUobRef, err := s.UobRefRepository.FindOne(ctx, persistence.NewUobRefQuery().WithUobRefID(uobRef.UobRefID))
			if err != nil {
				return err
			}

			err = s.insertUobRefAuditLog(ctx, "n/a", model.UobRefAssignAuditAction, uobRef, afterUobRef)
			if err != nil {
				return err
			}

			return nil
		},
		retry.Attempts(3),
		retry.RetryIf(func(err error) bool {
			return errors.Cause(err) == repository.ErrNotFound
		}),
		retry.LastErrorOnly(true),
	)
}

func (s *DriverService) UnAssignUobRefFromDriver(ctx context.Context, driver *model.Driver) error {
	if driver.DriverUOBRefID() == "" {
		return nil
	}

	query := persistence.NewUobRefQuery().WithUobRefID(driver.DriverUOBRefID()).WithDriverID(driver.DriverID)
	uobRef, err := s.UobRefRepository.FindOne(ctx, query)
	if err != nil {
		return err
	}

	logrus.Warn("UnAssignUobRef from driverID: ", driver.DriverID)

	if err := s.UobRefRepository.UpdateDriverID(ctx, uobRef, ""); err != nil {
		logrus.Warn("update uobRef error: ", err.Error())
		return err
	}

	if err := s.DriverRepository.UpdateUobRefID(ctx, driver, ""); err != nil {
		logrus.Warn("update driver error: ", err.Error())
		return err
	}
	driver.Banking.UOBRefID = ""

	afterUobRef, err := s.UobRefRepository.FindOne(ctx, persistence.NewUobRefQuery().WithUobRefID(uobRef.UobRefID))
	if err != nil {
		return err
	}

	err = s.insertUobRefAuditLog(ctx, "n/a", model.UobRefUnAssignAuditAction, uobRef, afterUobRef)
	if err != nil {
		return err
	}

	return nil
}

func (s *DriverService) insertUobRefAuditLog(ctx context.Context, actor string, action model.AuditAction, before interface{}, after interface{}) error {
	auditLog := model.NewAuditLog(
		model.AuditLogActor{ID: actor},
		model.AuditObject{ObjectType: model.UobRefAuditObject, ID: "n/a"},
		action,
		before,
		after,
	)
	return s.AuditLogRepo.Insert(ctx, &auditLog)
}

func (s *DriverService) CreateDriver(ctx context.Context, regis *model.DriverRegistration, area *model.ServiceArea) (*model.Driver, error) {
	var driver *model.Driver
	err := retry.Do(
		func() error {
			citiRefID, err := s.GenerateCitiRefID(s.GlobalConfig.RefIDPrefix)
			if err != nil {
				return err
			}
			driver = model.NewDriver(*regis, area.Region, citiRefID)
			if err := s.DriverRepository.Create(ctx, driver); err != nil {
				logrus.Warn("create driver error: ", err.Error())
				return err
			}
			logrus.Info("create driver success")
			return nil
		},
		retry.Attempts(3),
		retry.RetryIf(mongo.IsDuplicateKeyError),
		retry.LastErrorOnly(true),
	)
	return driver, err
}

type SetDriverLatestAttemptOption struct {
	Timestamp time.Time
}

type DriverOnTopQuotaWithIndex struct {
	OnTopQuota model.OnTopQuota
	Index      int
}

type SetDriverLatestAttemptOptionFunc func(opt SetDriverLatestAttemptOption) SetDriverLatestAttemptOption

func WithOverrideDriverLatestAttempt(t time.Time) SetDriverLatestAttemptOptionFunc {
	return func(opt SetDriverLatestAttemptOption) SetDriverLatestAttemptOption {
		opt.Timestamp = t
		return opt
	}
}

func (s *DriverService) UpdateDriverLastAttempt(ctx context.Context, driverID string, opt ...SetDriverLatestAttemptOptionFunc) error {
	var cfg SetDriverLatestAttemptOption
	cfg = SetDriverLatestAttemptOption{
		Timestamp: time.Now().UTC(),
	}
	for _, optionFunc := range opt {
		cfg = optionFunc(cfg)
	}
	return s.DriverActiveTimeRepository.UpdateLatestActiveTime(ctx, driverID, cfg.Timestamp)
}

func (s *DriverService) OfflineDriver(ctx context.Context, driver *model.Driver) error {
	driver.Status = model.StatusOffline
	driver.OfflineLater = false
	now := timeutil.BangkokNow()
	driver.LastOfflineAt = &now
	if err := s.DriverRepository.Update(ctx, driver); err != nil {
		return err
	}
	return nil
}

func (s *DriverService) GetProfileWithLatestWithdrawalQuota(ctx context.Context, driverID string) (*model.Driver, error) {
	driv, err := s.DriverRepository.GetProfile(ctx, driverID)
	if err != nil {
		return nil, err
	}

	if driv.WithdrawalQuota == nil || driv.WithdrawalQuota.IsResetRequired() {
		if err := s.updateWithdrawalQuota(ctx, driv); err != nil {
			return nil, err
		}
		if err := s.DriverRepository.SetWithdrawalQuota(ctx, driverID, *driv.WithdrawalQuota); err != nil {
			return nil, err
		}
	}

	return driv, nil
}

func (s *DriverService) AddWithdrawalQuota(ctx context.Context, driverID string) error {
	driv, err := s.DriverRepository.GetProfile(ctx, driverID)
	if err != nil {
		return err
	}

	if driv.WithdrawalQuota == nil || driv.WithdrawalQuota.IsResetRequired() {
		if err := s.updateWithdrawalQuota(ctx, driv); err != nil {
			return err
		}
	}

	driv.WithdrawalQuota.IncreaseForToday(s.PaymentCfg)
	if err := s.DriverRepository.SetWithdrawalQuota(ctx, driverID, *driv.WithdrawalQuota); err != nil {
		return err
	}

	return nil
}

func (s *DriverService) updateWithdrawalQuota(ctx context.Context, driv *model.Driver) error {
	// create withdrawal quota from the number of withdrawal transactions today
	if driv.WithdrawalQuota == nil {
		now := timeutil.BangkokNow()
		startTime, endTime := timeutil.DateTruncate(now), timeutil.DateCeiling(now)
		withdrawalCounts, err := s.TransRepo.CountWithdrawal(ctx, driv.DriverID, startTime, endTime, repository.WithReadPrimary)
		if err != nil {
			return err
		}

		driv.WithdrawalQuota = model.NewWithdrawalQuota(withdrawalCounts, s.PaymentCfg)
	}

	if driv.WithdrawalQuota.IsResetRequired() {
		driv.WithdrawalQuota.ResetQuotaToday(s.PaymentCfg)
	}

	return nil
}

func (s *DriverService) DeductOnTopQuota(ctx context.Context, driverID string, ots []model.OnTopScheme) error {
	// in this case is installment
	focusedOnTopSchemes := s.findInstallmentOnTopSchemeFromCurrentOrder(ots)

	if len(focusedOnTopSchemes) == 0 {
		logx.Info().
			Context(ctx).
			Str(logutil.DriverIDKey, driverID).
			Msg("DeductOnTopQuota: order has no installment on-top scheme")
		return nil
	}

	installmentOnTopQuotaByDriver, err := s.getDriverSelfInstallmentOnTopQuota(ctx, driverID)
	if err != nil {
		return nil
	}

	toReduceQuota := make([]DriverOnTopQuotaWithIndex, 0, len(installmentOnTopQuotaByDriver))

	for _, eachFocusedOnTopScheme := range focusedOnTopSchemes {
		focusedInstallmentOnTopQuota, ok := installmentOnTopQuotaByDriver[eachFocusedOnTopScheme.ID]
		if !ok {
			logx.Error().
				Context(ctx).
				Msg("Installment on top quota not found")
			continue
		}
		err = s.validateOnTopQuota(ctx, focusedInstallmentOnTopQuota.OnTopQuota)
		if err != nil {
			safe.SentryError(
				err,
				safe.WithDriverID(driverID),
			)
			continue
		}
		toReduceQuota = append(toReduceQuota, focusedInstallmentOnTopQuota)
	}

	return s.deductInstallmentOnTopQuota(ctx, driverID, toReduceQuota)
}

func (s *DriverService) IncrementOnTopQuota(ctx context.Context, driv *model.Driver, ots []model.OnTopScheme) error {
	// in this case is installment
	focusedOnTopSchemes := s.findInstallmentOnTopSchemeFromCurrentOrder(ots)

	if len(focusedOnTopSchemes) == 0 {
		logx.Info().
			Context(ctx).
			Str(logutil.DriverIDKey, driv.DriverID).
			Msg("IncrementOnTopQuota: order has no installment on-top scheme")
		return nil
	}

	installmentOnTopQuotaByDriver := s.getInstallmentOnTopQuotaWithIdx(driv.OnTopQuotas)
	if len(installmentOnTopQuotaByDriver) == 0 {
		logx.Error().
			Context(ctx).
			Err(dt.ErrNoInstallmentOnTopQuotaData).
			Str(logutil.DriverIDKey, driv.DriverID).
			Msg("DriverService_DeductOnTopQuota - Validate: No Installment On Top Quota in driver on top quota data")
		return nil
	}

	toIncrement := make([]DriverOnTopQuotaWithIndex, 0, len(installmentOnTopQuotaByDriver))

	for _, eachFocusedOnTopScheme := range focusedOnTopSchemes {
		focusedInstallmentOnTopQuota, ok := installmentOnTopQuotaByDriver[eachFocusedOnTopScheme.ID]
		if !ok {
			logx.Error().
				Context(ctx).
				Str(logutil.DriverIDKey, driv.DriverID).
				Msg("Installment on top quota not found")
			continue
		}
		toIncrement = append(toIncrement, focusedInstallmentOnTopQuota)
	}

	return s.incrementInstallmentOnTopQuota(driv, toIncrement)
}

func (s *DriverService) deductInstallmentOnTopQuota(ctx context.Context, driverID string, toDeductOnTopQuota []DriverOnTopQuotaWithIndex) error {
	for _, eachToDeductOnTopQuota := range toDeductOnTopQuota {
		driverSetter := setter.NewDriver().RemoveOnTopQuotaForSpecificPositionByN(eachToDeductOnTopQuota.Index, -1)
		err := s.DriverRepository.UpdateDriverBySetter(ctx, driverID, driverSetter)
		if err != nil {
			logx.Error().
				Err(err).
				Context(ctx).
				Str(logutil.DriverIDKey, driverID).
				Msg("fail to deduct the on-top quota")
			return err
		}
	}
	return nil
}

func (s *DriverService) findInstallmentOnTopSchemeFromCurrentOrder(ots []model.OnTopScheme) []model.OnTopScheme {
	var focusedOnTopSchemes []model.OnTopScheme
	for _, ot := range ots {
		if ot.Scheme == model.InstallmentOnTopScheme {
			focusedOnTopSchemes = append(focusedOnTopSchemes, ot)
		}
	}

	return focusedOnTopSchemes
}

func (s *DriverService) incrementInstallmentOnTopQuota(driv *model.Driver, toIncrementOnTopQuota []DriverOnTopQuotaWithIndex) error {
	for _, eachToIncrementOnTopQuota := range toIncrementOnTopQuota {
		driverOnTopQuota := &driv.OnTopQuotas[eachToIncrementOnTopQuota.Index]
		if driverOnTopQuota.OnTopFareID == eachToIncrementOnTopQuota.OnTopQuota.OnTopFareID {
			driverOnTopQuota.RemainingQuota += 1
		} else {
			logx.Error().
				Str(logutil.DriverIDKey, driv.DriverID).
				Msgf("on-top quota increment index mismatch driver's quota scheme: %v, order's quota scheme: %v",
					driverOnTopQuota.OnTopFareID, eachToIncrementOnTopQuota.OnTopQuota.OnTopFareID)
		}
	}
	return nil
}

func (s *DriverService) getDriverSelfInstallmentOnTopQuota(ctx context.Context, driverID string) (map[string]DriverOnTopQuotaWithIndex, error) {
	result, err := s.DriverRepository.GetMinimalProfilesByID(ctx, []string{driverID})
	if err != nil {
		logx.Error().
			Context(ctx).
			Err(err).
			Str(logutil.DriverIDKey, driverID).
			Msg("DriverService_DeductOnTopQuota - Validate: Fail to get driver minimal profile by id")
		return nil, err
	}

	driverData, ok := result[driverID]
	if !ok || driverData == nil {
		logx.Error().
			Context(ctx).
			Err(dt.ErrInvalidMinimalDriverData).
			Str(logutil.DriverIDKey, driverID).
			Msg("DriverService_DeductOnTopQuota - Validate: Driver Data is not found or nil")
		return nil, dt.ErrInvalidMinimalDriverData
	}

	onTopQuotaByID := s.getInstallmentOnTopQuotaWithIdx(driverData.OnTopQuotas)
	if len(onTopQuotaByID) == 0 {
		logx.Error().
			Context(ctx).
			Err(dt.ErrNoInstallmentOnTopQuotaData).
			Str(logutil.DriverIDKey, driverID).
			Msg("DriverService_DeductOnTopQuota - Validate: No Installment On Top Quota in driver on top quota data")
		return nil, dt.ErrNoInstallmentOnTopQuotaData
	}

	return onTopQuotaByID, nil
}

func (s *DriverService) getInstallmentOnTopQuotaWithIdx(driverOnTopQuota []model.OnTopQuota) map[string]DriverOnTopQuotaWithIndex {
	result := make(map[string]DriverOnTopQuotaWithIndex)
	if driverOnTopQuota == nil {
		return result
	}

	for index, eachOnTopQuota := range driverOnTopQuota {
		if eachOnTopQuota.QuotaType == model.InstallmentOnTopQuotaType {
			result[eachOnTopQuota.OnTopFareID] = DriverOnTopQuotaWithIndex{
				OnTopQuota: eachOnTopQuota,
				Index:      index,
			}
		}
	}
	return result
}

func (s *DriverService) validateOnTopQuota(ctx context.Context, installmentOnTopQuota model.OnTopQuota) error {
	if installmentOnTopQuota.RemainingQuota <= 0 {
		logx.Info().
			Context(ctx).
			Err(dt.ErrNotEnoughRemainingQuota).
			Msg("InstallmentOnTopTransactionProvider - Validate: Driver have not enough remaining quota")
		return dt.ErrNotEnoughRemainingQuota
	}
	return nil
}

func DriverLockKey(driverID string) string {
	return "lock_driver:" + driverID
}

type LockDriverOptions struct {
	RetryAttempts uint
	RetryDelay    time.Duration
}

type LockDriverOptionsFunc func(opt LockDriverOptions) LockDriverOptions

func WithRetryAttempts(retryAttempts uint) LockDriverOptionsFunc {
	return func(opt LockDriverOptions) LockDriverOptions {
		opt.RetryAttempts = retryAttempts
		return opt
	}
}

func WithRetryDelay(retryDelay time.Duration) LockDriverOptionsFunc {
	return func(opt LockDriverOptions) LockDriverOptions {
		opt.RetryDelay = retryDelay
		return opt
	}
}

func (s *DriverService) TryLockDriver(ctx context.Context, driverID string, opts ...LockDriverOptionsFunc) error {
	cfg := formatLockDriverConfig(s.driverServiceCfg.Get(), opts...)
	retryRound := 0
	err := retry.Do(
		func() error {
			ok, err := s.locker.Lock(ctx, DriverLockKey(driverID))
			if err != nil {
				return err
			}
			if !ok {
				return ErrDriverLock
			}
			return nil
		},
		retry.OnRetry(func(n uint, err error) {
			retryRound++
		}),
		retry.Delay(cfg.RetryDelay),
		retry.Attempts(cfg.RetryAttempts),
		retry.RetryIf(func(err error) bool {
			return err == ErrDriverLock
		}),
		retry.LastErrorOnly(true),
	)
	if retryRound > 0 {
		s.LogLockingDriverRetry(retryRound)
	}
	return err
}

func formatLockDriverConfig(defaultCfg DriverServiceConfig, opts ...LockDriverOptionsFunc) LockDriverOptions {
	var cfg LockDriverOptions
	cfg.RetryDelay = defaultCfg.LockingDriverRetryDelay
	cfg.RetryAttempts = defaultCfg.LockingDriverRetryAttempts

	for _, o := range opts {
		cfg = o(cfg)
	}
	return cfg
}

func (s *DriverService) UnlockDriver(ctx context.Context, driverID string) error {
	unlockDriverErr := s.locker.Unlock(ctx, DriverLockKey(driverID))
	if unlockDriverErr != nil {
		safe.SentryError(errors.New("cannot unlock driver redis lock"), safe.WithDriverID(driverID))
		return unlockDriverErr
	}
	return nil
}

func (s *DriverService) GetDriverLocationWithUpdatedAt(ctx context.Context, driverID string) (resp *model.LocationWithUpdatedAt, err error) {
	return s.DriverLocationRepository.GetDriverLocationWithUpdatedAt(ctx, driverID)
}

func ProvideDriverService(
	drivds persistence.DriversDataStore,
	redisClient datastore.RedisClient,
	auditLogRepo repository.AuditLogRepository,
	driverLocationRepository repository.DriverLocationRepository,
	driverRepository repository.DriverRepository,
	transRepo repository.TransactionRepository,
	tripRepository repository.TripRepository,
	orderRepository repository.OrderRepository,
	uobRefRepository repository.UobRefRepository,
	serviceAreaRepository repository.ServiceAreaRepository,
	driverActiveTimeRepository repository.DriverActiveTimeRepository,
	ratingCfg config.DriverRatingConfig,
	paymentCfg config.PaymentConfig,
	globalConfig config.GlobalConfig,
	cancellationRateCfg *config.AtomicCancellationRateConfig,
	meter metric.Meter,
	locker locker.Locker,
	driverServiceCfg *AtomicDriverServiceConfig,
	deviceManager DeviceManager,
	delivery delivery.Delivery,
) *DriverService {
	return NewDriverService(
		drivds,
		redisClient,
		auditLogRepo,
		driverLocationRepository,
		driverRepository,
		transRepo,
		tripRepository,
		orderRepository,
		uobRefRepository,
		serviceAreaRepository,
		driverActiveTimeRepository,
		ratingCfg,
		paymentCfg,
		globalConfig,
		cancellationRateCfg,
		meter,
		locker,
		driverServiceCfg,
		deviceManager,
		delivery,
	)
}
