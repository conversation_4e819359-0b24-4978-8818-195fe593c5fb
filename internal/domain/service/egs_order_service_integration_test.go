//go:build integration_test
// +build integration_test

package service_test

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"
	"google.golang.org/genproto/googleapis/type/decimal"
	"google.golang.org/grpc"

	commonv1 "git.wndv.co/go/proto/lineman/egs/common/v1"
	egsv1 "git.wndv.co/go/proto/lineman/egs/v1"
	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/ittest"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

func TestITTESTEGSOrderServiceImpl_ConsumeEGSOrderEvent(t *testing.T) {
	timeutils.Freeze()
	defer timeutils.Unfreeze()

	t.Run("Successfully consume coin EGS order event", func(t *testing.T) {
		ctn := ittest.NewContainer(t)
		if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_coin_egs_order"); err != nil {
			t.Errorf("Unexpected error initfixture: %v", err)
		}

		ctx := context.Background()
		driverID := "driver_1"
		orderID := "order_1"
		batchID := "batch_1"
		principalPrice := decimal.Decimal{Value: "500"}
		netAmount := decimal.Decimal{Value: "500"}

		event := &egsv1.EGSOrderEvent{
			EgsOrder: &egsv1.EGSOrder{
				OrderDetail: &egsv1.EGSOrder_OrderDetail{
					Id:   orderID,
					Name: "wallet_from_coin_1",
					Sku:  "wallet_from_coin_sku_1",
					ProductList: []*egsv1.EGSOrder_OrderDetail_Product{
						{
							Sku:            "wallet_from_coin_sku_1",
							Name:           "wallet_from_coin_1",
							StockId:        "stock_1",
							PrincipalPrice: &principalPrice,
							StockPriority:  1,
							IsReward:       false,
							Consumability: &egsv1.EGSOrder_OrderDetail_Product_Consumability{
								Type:   commonv1.ProductConsumabilityType_PRODUCT_CONSUMABILITY_TYPE_WALLET,
								Amount: 4000,
							},
						},
					},
					Metadata: &egsv1.EGSOrder_OrderDetail_ProductMetadata{
						MessageDisplay: "message1",
						Priority:       1,
					},
					IsBundle: false,
				},
				BuyerInformation: &egsv1.BuyerInformation{
					Contact: &commonv1.Contact{
						Name:           "Test Test",
						Phone:          "0888888888",
						Email:          "<EMAIL>",
						EmergencyPhone: "0888888888",
					},
					BuyerSource: &egsv1.BuyerInformation_BuyerSource{
						Type: commonv1.BuyerType_BUYER_TYPE_2W_RIDER,
						Id:   driverID,
					},
				},
				PaymentMethod: &egsv1.PaymentMethod{
					Type:         commonv1.PaymentMethodType_PAYMENT_METHOD_TYPE_ONE_TIME,
					NetAmount:    &netAmount,
					CurrencyType: commonv1.PaymentMethodCurrencyType_PAYMENT_METHOD_CURRENCY_TYPE_COIN,
				},
				Metadata: &egsv1.OrderMetadata{
					BatchId:          &batchID,
					ProductBatchType: commonv1.BatchGroupType_BATCH_GROUP_TYPE_COIN_STORE.Enum(),
				},
			},
		}

		ctn.StubGRPCMarketplaceService.EXPECT().UpdateEGSOrderStatus(ctx, gomock.Any()).DoAndReturn(
			func(ctx context.Context, in *egsv1.UpdateEGSOrderStatusRequest, opts ...grpc.CallOption) (*egsv1.UpdateEGSOrderStatusResponse, error) {
				assert.Equal(t, in.OrderId, orderID)
				assert.Equal(t, in.EgsStatus, commonv1.EGSOrderStatus_EGS_ORDER_STATUS_COMPLETED)
				assert.Equal(t, in.OrderId, orderID)
				assert.Equal(t, in.Metadata.ProductBatchType, commonv1.BatchGroupType_BATCH_GROUP_TYPE_COIN_STORE.Enum())
				return &egsv1.UpdateEGSOrderStatusResponse{}, nil
			})

		err := ctn.EGSOrderService.ConsumeEGSOrderEvent(ctx, event)
		assert.NoError(t, err)

		txs, err := ctn.DataStoreTransactionRepository.Find(ctx, &persistence.TransactionQuery{
			DriverID:   driverID,
			TransRefId: orderID,
		}, 0, 100)
		assert.NoError(t, err)
		assert.Len(t, txs, 3)
		tx := txs[0]
		withholdingTx := txs[1]
		outstandingTaxTx := txs[2]
		assert.Equal(t, model.Transaction{
			ID:            tx.ID,
			TransactionID: tx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          tx.Info.Source,
				Category:        model.WalletTransactionCategory,
				Type:            model.IncentiveTransactionType,
				Operator:        model.AdditionOperator,
				DisplayText:     "แลกเงินพิเศษด้วยเหรียญ",
				DriverRefID:     tx.Info.DriverRefID,
				Amount:          types.NewMoney(4000),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(4000),
				TransactionDate: tx.Info.TransactionDate,
				TaxRefID:        tx.Info.TaxRefID,
			},
			Remarks:   tx.Remarks,
			Sources:   tx.Sources,
			Audits:    tx.Audits,
			CreatedAt: tx.CreatedAt,
			UpdatedAt: tx.UpdatedAt,
		}, tx)

		assert.Equal(t, model.Transaction{
			ID:            withholdingTx.ID,
			TransactionID: withholdingTx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          withholdingTx.Info.Source,
				Category:        model.CreditTransactionCategory,
				Type:            model.WithholdingTransactionType,
				Operator:        model.SubtractionOperator,
				DisplayText:     withholdingTx.Info.DisplayText,
				DriverRefID:     withholdingTx.Info.DriverRefID,
				Amount:          types.NewMoney(4000 * 0.03),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(4000),
				CreditAfter:     types.NewMoney(-120),
				TransactionDate: withholdingTx.Info.TransactionDate,
				TaxRefID:        tx.Info.TaxRefID,
			},
			Remarks:   withholdingTx.Remarks,
			Sources:   withholdingTx.Sources,
			Audits:    withholdingTx.Audits,
			CreatedAt: withholdingTx.CreatedAt,
			UpdatedAt: withholdingTx.UpdatedAt,
		}, withholdingTx)

		assert.Equal(t, model.Transaction{
			ID:            outstandingTaxTx.ID,
			TransactionID: outstandingTaxTx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          outstandingTaxTx.Info.Source,
				Category:        model.CreditTransactionCategory,
				Type:            model.OutstandingTransactionType,
				DisplayText:     outstandingTaxTx.Info.DisplayText,
				DriverRefID:     outstandingTaxTx.Info.DriverRefID,
				Amount:          types.NewMoney(4000 * 0.03),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(4000),
				CreditAfter:     types.NewMoney(-120),
				TransactionDate: outstandingTaxTx.Info.TransactionDate,
			},
			Remarks:   outstandingTaxTx.Remarks,
			Sources:   outstandingTaxTx.Sources,
			Audits:    outstandingTaxTx.Audits,
			CreatedAt: outstandingTaxTx.CreatedAt,
			UpdatedAt: outstandingTaxTx.UpdatedAt,
		}, outstandingTaxTx)
	})

	t.Run("Successfully consume coin EGS order event when there is voided transaction", func(t *testing.T) {
		ctn := ittest.NewContainer(t)
		if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_coin_egs_order"); err != nil {
			t.Errorf("Unexpected error initfixture: %v", err)
		}

		ctx := context.Background()
		driverID := "driver_1"
		orderID := "order_1"
		batchID := "batch_1"
		principalPrice := decimal.Decimal{Value: "500"}
		netAmount := decimal.Decimal{Value: "500"}

		oldTx := model.Transaction{
			TransactionID: "tx-1",
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				Category:   model.WalletTransactionCategory,
				Type:       model.IncentiveTransactionType,
				DriverID:   driverID,
				TransRefID: crypt.EncryptedString(orderID),
			},
		}

		oldWithholdingTx := model.Transaction{
			TransactionID: "tx-wht-1",
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Info: model.TransactionInfo{
				Category:   model.CreditTransactionCategory,
				Type:       model.WithholdingTransactionType,
				DriverID:   driverID,
				TransRefID: crypt.EncryptedString(orderID),
			},
		}

		voidInfo, err := oldTx.Void()
		assert.NoError(t, err)
		assert.NotNil(t, voidInfo)
		voidWithholdingTxInfo, err := oldWithholdingTx.Void()
		assert.NoError(t, err)
		assert.NotNil(t, voidWithholdingTxInfo)
		assert.Equal(t, "tx-1", voidInfo.RefID)
		assert.Equal(t, model.DeductVoidTransactionType, voidInfo.Type)
		err = ctn.DataStoreTransactionRepository.CreateAll(ctx, []model.Transaction{
			oldTx,
			oldWithholdingTx,
			{
				TransactionID: "tx-2",
				Channel:       model.SystemTransactionChannel,
				Action:        model.VoidConvertVaultCoinsToCash,
				Info:          *voidInfo,
			},
			{
				TransactionID: "tx-wht-2",
				Channel:       model.SystemTransactionChannel,
				Action:        model.VoidConvertVaultCoinsToCash,
				Info:          *voidWithholdingTxInfo,
			},
		})
		assert.NoError(t, err)

		event := &egsv1.EGSOrderEvent{
			EgsOrder: &egsv1.EGSOrder{
				OrderDetail: &egsv1.EGSOrder_OrderDetail{
					Id:   orderID,
					Name: "wallet_from_coin_1",
					Sku:  "wallet_from_coin_sku_1",
					ProductList: []*egsv1.EGSOrder_OrderDetail_Product{
						{
							Sku:            "wallet_from_coin_sku_1",
							Name:           "wallet_from_coin_1",
							StockId:        "stock_1",
							PrincipalPrice: &principalPrice,
							StockPriority:  1,
							IsReward:       false,
							Consumability: &egsv1.EGSOrder_OrderDetail_Product_Consumability{
								Type:   commonv1.ProductConsumabilityType_PRODUCT_CONSUMABILITY_TYPE_WALLET,
								Amount: 4000,
							},
						},
					},
					Metadata: &egsv1.EGSOrder_OrderDetail_ProductMetadata{
						MessageDisplay: "message1",
						Priority:       1,
					},
					IsBundle: false,
				},
				BuyerInformation: &egsv1.BuyerInformation{
					Contact: &commonv1.Contact{
						Name:           "Test Test",
						Phone:          "0888888888",
						Email:          "<EMAIL>",
						EmergencyPhone: "0888888888",
					},
					BuyerSource: &egsv1.BuyerInformation_BuyerSource{
						Type: commonv1.BuyerType_BUYER_TYPE_2W_RIDER,
						Id:   driverID,
					},
				},
				PaymentMethod: &egsv1.PaymentMethod{
					Type:         commonv1.PaymentMethodType_PAYMENT_METHOD_TYPE_ONE_TIME,
					NetAmount:    &netAmount,
					CurrencyType: commonv1.PaymentMethodCurrencyType_PAYMENT_METHOD_CURRENCY_TYPE_COIN,
				},
				Metadata: &egsv1.OrderMetadata{
					BatchId:          &batchID,
					ProductBatchType: commonv1.BatchGroupType_BATCH_GROUP_TYPE_COIN_STORE.Enum(),
				},
			},
		}

		ctn.StubGRPCMarketplaceService.EXPECT().UpdateEGSOrderStatus(ctx, gomock.Any()).DoAndReturn(
			func(ctx context.Context, in *egsv1.UpdateEGSOrderStatusRequest, opts ...grpc.CallOption) (*egsv1.UpdateEGSOrderStatusResponse, error) {
				assert.Equal(t, in.OrderId, orderID)
				assert.Equal(t, in.EgsStatus, commonv1.EGSOrderStatus_EGS_ORDER_STATUS_COMPLETED)
				assert.Equal(t, in.OrderId, orderID)
				assert.Equal(t, in.Metadata.ProductBatchType, commonv1.BatchGroupType_BATCH_GROUP_TYPE_COIN_STORE.Enum())
				return &egsv1.UpdateEGSOrderStatusResponse{}, nil
			})

		err = ctn.EGSOrderService.ConsumeEGSOrderEvent(ctx, event)
		assert.NoError(t, err)

		txs, err := ctn.DataStoreTransactionRepository.Find(ctx, &persistence.TransactionQuery{
			DriverID:   driverID,
			TransRefId: orderID,
		}, 0, 100)
		assert.NoError(t, err)
		assert.Len(t, txs, 7)
		tx := txs[0]
		withholdingTx := txs[1]
		outstandingTaxTx := txs[2]
		assert.Equal(t, model.Transaction{
			ID:            tx.ID,
			TransactionID: tx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          tx.Info.Source,
				Category:        model.WalletTransactionCategory,
				Type:            model.IncentiveTransactionType,
				Operator:        model.AdditionOperator,
				DisplayText:     "แลกเงินพิเศษด้วยเหรียญ",
				DriverRefID:     tx.Info.DriverRefID,
				Amount:          types.NewMoney(4000),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(4000),
				TransactionDate: tx.Info.TransactionDate,
				TaxRefID:        tx.Info.TaxRefID,
			},
			Remarks:   tx.Remarks,
			Sources:   tx.Sources,
			Audits:    tx.Audits,
			CreatedAt: tx.CreatedAt,
			UpdatedAt: tx.UpdatedAt,
		}, tx)

		assert.Equal(t, model.Transaction{
			ID:            withholdingTx.ID,
			TransactionID: withholdingTx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          withholdingTx.Info.Source,
				Category:        model.CreditTransactionCategory,
				Type:            model.WithholdingTransactionType,
				Operator:        model.SubtractionOperator,
				DisplayText:     withholdingTx.Info.DisplayText,
				DriverRefID:     withholdingTx.Info.DriverRefID,
				Amount:          types.NewMoney(4000 * 0.03),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(4000),
				CreditAfter:     types.NewMoney(-120),
				TransactionDate: withholdingTx.Info.TransactionDate,
				TaxRefID:        tx.Info.TaxRefID,
			},
			Remarks:   withholdingTx.Remarks,
			Sources:   withholdingTx.Sources,
			Audits:    withholdingTx.Audits,
			CreatedAt: withholdingTx.CreatedAt,
			UpdatedAt: withholdingTx.UpdatedAt,
		}, withholdingTx)

		assert.Equal(t, model.Transaction{
			ID:            outstandingTaxTx.ID,
			TransactionID: outstandingTaxTx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          outstandingTaxTx.Info.Source,
				Category:        model.CreditTransactionCategory,
				Type:            model.OutstandingTransactionType,
				DisplayText:     outstandingTaxTx.Info.DisplayText,
				DriverRefID:     outstandingTaxTx.Info.DriverRefID,
				Amount:          types.NewMoney(4000 * 0.03),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(4000),
				CreditAfter:     types.NewMoney(-120),
				TransactionDate: outstandingTaxTx.Info.TransactionDate,
			},
			Remarks:   outstandingTaxTx.Remarks,
			Sources:   outstandingTaxTx.Sources,
			Audits:    outstandingTaxTx.Audits,
			CreatedAt: outstandingTaxTx.CreatedAt,
			UpdatedAt: outstandingTaxTx.UpdatedAt,
		}, outstandingTaxTx)
	})

	t.Run("Should fail when there already is successful transaction", func(t *testing.T) {
		ctn := ittest.NewContainer(t)
		if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_coin_egs_order"); err != nil {
			t.Errorf("Unexpected error initfixture: %v", err)
		}

		ctx := context.Background()
		driverID := "driver_1"
		orderID := "order_1"
		batchID := "batch_1"
		principalPrice := decimal.Decimal{Value: "500"}
		netAmount := decimal.Decimal{Value: "500"}

		oldTx := model.Transaction{
			TransactionID: "tx-1",
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				Category:   model.WalletTransactionCategory,
				Type:       model.IncentiveTransactionType,
				DriverID:   driverID,
				TransRefID: crypt.EncryptedString(orderID),
			},
		}

		oldWithholdingTx := model.Transaction{
			TransactionID: "tx-1",
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Info: model.TransactionInfo{
				Category:   model.CreditTransactionCategory,
				Type:       model.WithholdingTransactionType,
				DriverID:   driverID,
				TransRefID: crypt.EncryptedString(orderID),
			},
		}

		oldOutstandingTx := model.Transaction{
			TransactionID: "tx-1",
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Info: model.TransactionInfo{
				Category:   model.CreditTransactionCategory,
				Type:       model.OutstandingTransactionType,
				DriverID:   driverID,
				TransRefID: crypt.EncryptedString(orderID),
			},
		}

		err := ctn.DataStoreTransactionRepository.CreateAll(ctx, []model.Transaction{oldTx, oldWithholdingTx, oldOutstandingTx})
		assert.NoError(t, err)

		event := &egsv1.EGSOrderEvent{
			EgsOrder: &egsv1.EGSOrder{
				OrderDetail: &egsv1.EGSOrder_OrderDetail{
					Id:   orderID,
					Name: "wallet_from_coin_1",
					Sku:  "wallet_from_coin_sku_1",
					ProductList: []*egsv1.EGSOrder_OrderDetail_Product{
						{
							Sku:            "wallet_from_coin_sku_1",
							Name:           "wallet_from_coin_1",
							StockId:        "stock_1",
							PrincipalPrice: &principalPrice,
							StockPriority:  1,
							IsReward:       false,
							Consumability: &egsv1.EGSOrder_OrderDetail_Product_Consumability{
								Type:   commonv1.ProductConsumabilityType_PRODUCT_CONSUMABILITY_TYPE_WALLET,
								Amount: 4000,
							},
						},
					},
					Metadata: &egsv1.EGSOrder_OrderDetail_ProductMetadata{
						MessageDisplay: "message1",
						Priority:       1,
					},
					IsBundle: false,
				},
				BuyerInformation: &egsv1.BuyerInformation{
					Contact: &commonv1.Contact{
						Name:           "Test Test",
						Phone:          "0888888888",
						Email:          "<EMAIL>",
						EmergencyPhone: "0888888888",
					},
					BuyerSource: &egsv1.BuyerInformation_BuyerSource{
						Type: commonv1.BuyerType_BUYER_TYPE_2W_RIDER,
						Id:   driverID,
					},
				},
				PaymentMethod: &egsv1.PaymentMethod{
					Type:         commonv1.PaymentMethodType_PAYMENT_METHOD_TYPE_ONE_TIME,
					NetAmount:    &netAmount,
					CurrencyType: commonv1.PaymentMethodCurrencyType_PAYMENT_METHOD_CURRENCY_TYPE_COIN,
				},
				Metadata: &egsv1.OrderMetadata{
					BatchId:          &batchID,
					ProductBatchType: commonv1.BatchGroupType_BATCH_GROUP_TYPE_COIN_STORE.Enum(),
				},
			},
		}

		err = ctn.EGSOrderService.ConsumeEGSOrderEvent(ctx, event)
		assert.ErrorContains(t, err, "found duplicated transaction")

		txs, err := ctn.DataStoreTransactionRepository.Find(ctx, &persistence.TransactionQuery{
			DriverID:   driverID,
			TransRefId: orderID,
		}, 0, 100)
		assert.NoError(t, err)
		assert.Len(t, txs, 3)
	})

	t.Run("Should fail and void transaction when can't update egs order status", func(t *testing.T) {
		ctn := ittest.NewContainer(t)
		if err := ctn.Fixtures.InitFixture(ctn.DBConnectionForTest, "fixtures_coin_egs_order"); err != nil {
			t.Errorf("Unexpected error initfixture: %v", err)
		}

		ctx := context.Background()
		driverID := "driver_1"
		orderID := "order_1"
		batchID := "batch_1"
		principalPrice := decimal.Decimal{Value: "500"}
		netAmount := decimal.Decimal{Value: "500"}

		event := &egsv1.EGSOrderEvent{
			EgsOrder: &egsv1.EGSOrder{
				OrderDetail: &egsv1.EGSOrder_OrderDetail{
					Id:   orderID,
					Name: "wallet_from_coin_1",
					Sku:  "wallet_from_coin_sku_1",
					ProductList: []*egsv1.EGSOrder_OrderDetail_Product{
						{
							Sku:            "wallet_from_coin_sku_1",
							Name:           "wallet_from_coin_1",
							StockId:        "stock_1",
							PrincipalPrice: &principalPrice,
							StockPriority:  1,
							IsReward:       false,
							Consumability: &egsv1.EGSOrder_OrderDetail_Product_Consumability{
								Type:   commonv1.ProductConsumabilityType_PRODUCT_CONSUMABILITY_TYPE_WALLET,
								Amount: 4000,
							},
						},
					},
					Metadata: &egsv1.EGSOrder_OrderDetail_ProductMetadata{
						MessageDisplay: "message1",
						Priority:       1,
					},
					IsBundle: false,
				},
				BuyerInformation: &egsv1.BuyerInformation{
					Contact: &commonv1.Contact{
						Name:           "Test Test",
						Phone:          "0888888888",
						Email:          "<EMAIL>",
						EmergencyPhone: "0888888888",
					},
					BuyerSource: &egsv1.BuyerInformation_BuyerSource{
						Type: commonv1.BuyerType_BUYER_TYPE_2W_RIDER,
						Id:   driverID,
					},
				},
				PaymentMethod: &egsv1.PaymentMethod{
					Type:         commonv1.PaymentMethodType_PAYMENT_METHOD_TYPE_ONE_TIME,
					NetAmount:    &netAmount,
					CurrencyType: commonv1.PaymentMethodCurrencyType_PAYMENT_METHOD_CURRENCY_TYPE_COIN,
				},
				Metadata: &egsv1.OrderMetadata{
					BatchId:          &batchID,
					ProductBatchType: commonv1.BatchGroupType_BATCH_GROUP_TYPE_COIN_STORE.Enum(),
				},
			},
		}

		ctn.StubGRPCMarketplaceService.EXPECT().UpdateEGSOrderStatus(ctx, gomock.Any()).DoAndReturn(
			func(ctx context.Context, in *egsv1.UpdateEGSOrderStatusRequest, opts ...grpc.CallOption) (*egsv1.UpdateEGSOrderStatusResponse, error) {
				assert.Equal(t, in.OrderId, orderID)
				assert.Equal(t, in.EgsStatus, commonv1.EGSOrderStatus_EGS_ORDER_STATUS_COMPLETED)
				assert.Equal(t, in.OrderId, orderID)
				assert.Equal(t, in.Metadata.ProductBatchType, commonv1.BatchGroupType_BATCH_GROUP_TYPE_COIN_STORE.Enum())
				return nil, errors.New("TEST ERROR")
			})

		err := ctn.EGSOrderService.ConsumeEGSOrderEvent(ctx, event)
		assert.Error(t, err)

		txs, err := ctn.DataStoreTransactionRepository.Find(ctx, &persistence.TransactionQuery{
			DriverID:   driverID,
			TransRefId: orderID,
		}, 0, 100)
		assert.NoError(t, err)
		assert.Len(t, txs, 5)
		withHoldingTx := txs[3]
		tx := txs[2]
		voidTx := txs[1]
		voidWithholdingTx := txs[0]
		assert.Equal(t, model.Transaction{
			ID:            tx.ID,
			TransactionID: tx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.ConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          tx.Info.Source,
				Category:        model.WalletTransactionCategory,
				Type:            model.IncentiveTransactionType,
				Operator:        model.AdditionOperator,
				DisplayText:     "แลกเงินพิเศษด้วยเหรียญ",
				DriverRefID:     tx.Info.DriverRefID,
				Amount:          types.NewMoney(4000),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(4000),
				TransactionDate: tx.Info.TransactionDate,
				TaxRefID:        tx.Info.TaxRefID,
			},
			Remarks:   tx.Remarks,
			Sources:   tx.Sources,
			Audits:    tx.Audits,
			CreatedAt: tx.CreatedAt,
			UpdatedAt: tx.UpdatedAt,
		}, tx)
		assert.Equal(t, model.Transaction{
			ID:            voidTx.ID,
			TransactionID: voidTx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.VoidConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          voidTx.Info.Source,
				Category:        model.WalletTransactionCategory,
				Type:            model.DeductVoidTransactionType,
				DisplayText:     voidTx.Info.DisplayText,
				DriverRefID:     voidTx.Info.DriverRefID,
				Amount:          types.NewMoney(4000),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(0),
				CreditAfter:     types.NewMoney(-120),
				TransactionDate: voidTx.Info.TransactionDate,
				Operator:        model.SubtractionOperator,
				RefID:           tx.TransactionID,
			},
			Remarks:   voidTx.Remarks,
			Sources:   voidTx.Sources,
			Audits:    voidTx.Audits,
			CreatedAt: voidTx.CreatedAt,
			UpdatedAt: voidTx.UpdatedAt,
		}, voidTx)

		assert.Equal(t, model.Transaction{
			ID:            voidWithholdingTx.ID,
			TransactionID: voidWithholdingTx.TransactionID,
			Channel:       model.SystemTransactionChannel,
			Status:        model.SuccessTransactionStatus,
			Action:        model.VoidConvertVaultCoinsToCash,
			Info: model.TransactionInfo{
				DriverID:        driverID,
				Source:          voidWithholdingTx.Info.Source,
				Category:        model.CreditTransactionCategory,
				Type:            model.VoidReturnCreditTransactionType,
				DisplayText:     voidWithholdingTx.Info.DisplayText,
				DriverRefID:     voidWithholdingTx.Info.DriverRefID,
				Amount:          types.NewMoney(120),
				TransRefID:      crypt.EncryptedString(orderID),
				WalletAfter:     types.NewMoney(0),
				CreditAfter:     types.NewMoney(0),
				TransactionDate: voidWithholdingTx.Info.TransactionDate,
				Operator:        model.AdditionOperator,
				RefID:           withHoldingTx.TransactionID,
			},
			Remarks:   voidWithholdingTx.Remarks,
			Sources:   voidWithholdingTx.Sources,
			Audits:    voidWithholdingTx.Audits,
			CreatedAt: voidWithholdingTx.CreatedAt,
			UpdatedAt: voidWithholdingTx.UpdatedAt,
		}, voidWithholdingTx)
	})
}
