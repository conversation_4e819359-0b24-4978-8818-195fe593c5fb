package service_test

import (
	"context"
	"math"
	"testing"
	"time"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/require"

	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/mock_mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
)

func TestFormInfoBuilder(t *testing.T) {
	funcGenerateDeps := func() (func(), *mock_mapservice.MockMapService, *mock_service.MockVOSService) {
		ctrl := gomock.NewController(t)
		mockMapService := mock_mapservice.NewMockMapService(ctrl)
		mockVosService := mock_service.NewMockVOSService(ctrl)
		return ctrl.Finish, mockMapService, mockVosService
	}

	funcGetMapSvcLoc := func(srcLoc string, srcHistoryLoc map[string]model.LocationWithUpdatedAt) mapservice.Location {
		resLoc := srcHistoryLoc[srcLoc]
		return mapservice.Location{
			Lat: resLoc.Lat,
			Lng: resLoc.Lng,
		}
	}

	t.Run("food_long_waiting_time_cancelled_arrived", func(tt *testing.T) {
		finish, mockMapService, _ := funcGenerateDeps()
		defer finish()

		srcOrder := generatePlainCancelledOrder()
		srcOrder.ServiceType = model.ServiceFood
		srcOrder.History[string(model.StatusDriverArrived)] = srcOrder.History[string(model.StatusDriverArrivedRestaurant)].Add(time.Minute)
		srcOrder.HistoryLocation[string(model.StatusDriverArrived)] = srcOrder.HistoryLocation[string(model.StatusDriverArrivedRestaurant)]

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverArrived), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: -1.0, Lng: 1.0}).Return(
			&mapservice.Table{
				Distances: []float64{140},
			}, nil,
		)

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverArrivedRestaurant), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).Return(
			&mapservice.Table{
				Distances: []float64{10},
			}, nil,
		)

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverToDestination), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).Return(
			&mapservice.Table{
				Distances: []float64{15},
			}, nil,
		)

		ctx := context.Background()
		fcForm := service.NewLongWaitingTimeCompensationClaimForm(&model.Driver{}, &srcOrder, mockMapService)

		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(t, 140.0, formService.Prefill["D-ARRIVED-USER"])
		require.Equal(t, true, formService.Prefill["D-ARRIVED-USER-VALUE"])
		require.Equal(t, "", formService.Prefill["D-ARRIVED-USER-FLAG"])
		require.Equal(t, "", formService.Prefill["D-ARRIVED-USER-MESSAGE"])

		require.Equal(t, 10.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(t, true, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])

		require.Equal(t, 15.0, formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT"])
		require.Equal(t, true, formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-MESSAGE"])

		require.Equal(t, nil, formService.Prefill["T-ARRIVED-CANCELED"])
		require.Equal(t, nil, formService.Prefill["T-ARRIVED-CANCELED-VALUE"])
		require.Equal(t, nil, formService.Prefill["T-ARRIVED-CANCELED-FLAG"])
		require.Equal(t, nil, formService.Prefill["T-ARRIVED-CANCELED-MESSAGE"])

		require.Equal(t, 0.08, formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION"])
		require.Equal(t, "0", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-VALUE"])
		require.Equal(t, "", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-FLAG"])
		require.Equal(t, "", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-MESSAGE"])

		require.True(t, floatEqualsWithTwoDecimals(11078976.450337777, formService.Prefill["R-ARRIVED-USER"].(float64)))
		require.Equal(t, false, formService.Prefill["R-ARRIVED-USER-VALUE"])
		require.Equal(t, "", formService.Prefill["R-ARRIVED-USER-FLAG"])
		require.Equal(t, "", formService.Prefill["R-ARRIVED-USER-MESSAGE"])

		require.True(t, floatEqualsWithTwoDecimals(11271182.355271319, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"].(float64)))
		require.Equal(t, false, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])

		require.True(t, floatEqualsWithTwoDecimals(11271182.355271319, formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT"].(float64)))
		require.Equal(t, false, formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-MESSAGE"])
	})

	t.Run("mart_long_waiting_time_cancelled_driver_to_destination", func(tt *testing.T) {
		finish, mockMapService, _ := funcGenerateDeps()
		defer finish()

		srcOrder := generatePlainCancelledOrder()
		srcOrder.ServiceType = model.ServiceFood
		srcOrder.History[string(model.StatusDriverToDestination)] = srcOrder.History[string(model.StatusDriverArrivedRestaurant)].Add(time.Minute)
		srcOrder.HistoryLocation[string(model.StatusDriverToDestination)] = srcOrder.HistoryLocation[string(model.StatusDriverArrivedRestaurant)]

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverToDestination), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).Return(
			&mapservice.Table{
				Distances: []float64{300},
			}, nil,
		)

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusCanceled), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).Return(
			&mapservice.Table{
				Distances: []float64{200},
			}, nil,
		)

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverArrivedRestaurant), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).Return(
			&mapservice.Table{
				Distances: []float64{20},
			}, nil,
		)

		ctx := context.Background()
		fcForm := service.NewLongWaitingTimeCompensationClaimForm(&model.Driver{}, &srcOrder, mockMapService)

		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(t, 300.0, formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT"])
		require.Equal(t, false, formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-MESSAGE"])

		require.Equal(t, 200.0, formService.Prefill["D-CANCELED-RESTAURANT"])
		require.Equal(t, false, formService.Prefill["D-CANCELED-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["D-CANCELED-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["D-CANCELED-RESTAURANT-MESSAGE"])

		require.Equal(t, 20.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(t, true, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])

		require.Equal(t, 200.0, formService.Prefill["MIN-D-DRIVER_TO_DESTINATION-RESTAURANT-D-CANCELED-RESTAURANT"])

		require.Equal(t, nil, formService.Prefill["T-ARRIVED-CANCELED"])
		require.Equal(t, nil, formService.Prefill["T-ARRIVED-CANCELED-VALUE"])
		require.Equal(t, nil, formService.Prefill["T-ARRIVED-CANCELED-FLAG"])
		require.Equal(t, nil, formService.Prefill["T-ARRIVED-CANCELED-MESSAGE"])

		require.Equal(t, 1.0, formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION"])
		require.Equal(t, "0", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-VALUE"])
		require.Equal(t, "", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-FLAG"])
		require.Equal(t, "", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-MESSAGE"])

		require.True(t, floatEqualsWithTwoDecimals(11271182.355271319, formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT"].(float64)))
		require.Equal(t, false, formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-MESSAGE"])
		require.Equal(t, false, formService.Prefill["R-CANCELED-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["R-CANCELED-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["R-CANCELED-RESTAURANT-MESSAGE"])
		require.True(t, floatEqualsWithTwoDecimals(11271182.355271319, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"].(float64)))
		require.Equal(t, false, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(t, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(t, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])
		require.True(t, floatEqualsWithTwoDecimals(11271182.355271319, formService.Prefill["MIN-R-DRIVER_TO_DESTINATION-RESTAURANT-R-CANCELED-RESTAURANT"].(float64)))

	})

	t.Run("mart_food_claim_cancelled_arrived", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		srcOrder := generatePlainCancelledOrder()
		srcOrder.ServiceType = model.ServiceMart
		srcOrder.History[string(model.StatusDriverArrived)] = srcOrder.History[string(model.StatusDriverArrivedRestaurant)].Add(time.Minute)
		srcOrder.HistoryLocation[string(model.StatusDriverArrived)] = srcOrder.HistoryLocation[string(model.StatusDriverArrivedRestaurant)]

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverArrived), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: -1.0, Lng: 1.0}).Return(
			&mapservice.Table{}, nil,
		)

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)

		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		ctx := context.Background()
		fcForm := service.NewFoodClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService)
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 0.0, formService.Prefill["D-ARRIVED-USER"])
		require.Equal(tt, false, formService.Prefill["D-ARRIVED-USER-VALUE"])
		require.Equal(tt, "UNABLE_TO_FIND_DISTANCE", formService.Prefill["D-ARRIVED-USER-FLAG"])
		require.Equal(tt, "unable to find distance via map service", formService.Prefill["D-ARRIVED-USER-MESSAGE"])

		require.Equal(tt, 0.36, formService.Prefill["T-ARRIVED-CANCELED"])
		require.Equal(tt, "false", formService.Prefill["T-ARRIVED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-ARRIVED-CANCELED-FLAG"])
		require.Equal(tt, "", formService.Prefill["T-ARRIVED-CANCELED-MESSAGE"])

		require.True(t, floatEqualsWithTwoDecimals(11078976.450337777, formService.Prefill["R-ARRIVED-USER"].(float64)))
		require.Equal(tt, false, formService.Prefill["R-ARRIVED-USER-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-ARRIVED-USER-FLAG"])
		require.Equal(tt, "", formService.Prefill["R-ARRIVED-USER-MESSAGE"])
	})

	t.Run("food_food_claim_form_cancelled_driver_arrived_restaurant", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		srcOrder := generatePlainCancelledOrder()
		srcOrder.ServiceType = model.ServiceFood

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverArrivedRestaurant), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).
			Return(&mapservice.Table{
				Distances: []float64{100},
			}, nil)
		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusCanceled), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).
			Return(&mapservice.Table{
				Distances: []float64{20},
			}, nil)

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		ctx := context.Background()
		fcForm := service.NewFoodClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService)
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 20.0, formService.Prefill["D-CANCELED-RESTAURANT"])
		require.Equal(tt, true, formService.Prefill["D-CANCELED-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-CANCELED-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["D-CANCELED-RESTAURANT-MESSAGE"])

		require.Equal(tt, 100.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(tt, true, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])

		require.Equal(tt, 20.0, formService.Prefill["MIN-D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-D-CANCELED-RESTAURANT"])

		require.True(t, floatEqualsWithTwoDecimals(11271183.920626584, formService.Prefill["R-CANCELED-RESTAURANT"].(float64)))
		require.Equal(tt, false, formService.Prefill["R-CANCELED-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-CANCELED-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["R-CANCELED-RESTAURANT-MESSAGE"])

		require.True(t, floatEqualsWithTwoDecimals(11271182.355271319, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"].(float64)))
		require.Equal(tt, false, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])
		require.True(t, floatEqualsWithTwoDecimals(11271182.355271319, formService.Prefill["MIN-R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-R-CANCELED-RESTAURANT"].(float64)))
	})
}

func TestFormInfoBuilder_FlagCase(t *testing.T) {
	funcGenerateDeps := func() (func(), *mock_mapservice.MockMapService, *mock_service.MockVOSService) {
		ctrl := gomock.NewController(t)
		mockMapService := mock_mapservice.NewMockMapService(ctrl)
		mockVosService := mock_service.NewMockVOSService(ctrl)
		return ctrl.Finish, mockMapService, mockVosService
	}

	funcGetMapSvcLoc := func(srcLoc string, srcHistoryLoc map[string]model.LocationWithUpdatedAt) mapservice.Location {
		resLoc := srcHistoryLoc[srcLoc]
		return mapservice.Location{
			Lat: resLoc.Lat,
			Lng: resLoc.Lng,
		}
	}

	// food_long_waiting_time_cancelled_driver_arrived_restaurant
	t.Run("unable_to_get_time_from_status", func(tt *testing.T) {
		finish, mockMapService, _ := funcGenerateDeps()
		defer finish()

		srcOrder := generatePlainCancelledOrder()
		srcOrder.ServiceType = model.ServiceFood
		delete(srcOrder.History, string(model.StatusDriverToDestination))
		delete(srcOrder.HistoryLocation, string(model.StatusDriverToDestination))
		delete(srcOrder.HistoryLocation, string(model.StatusDriverArrivedRestaurant))

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			gomock.Any(),
			mapservice.Location{Lat: 1.0, Lng: -1.0}).Return(
			&mapservice.Table{
				Distances: []float64{50},
			}, nil,
		)

		ctx := context.Background()
		fcForm := service.NewLongWaitingTimeCompensationClaimForm(&model.Driver{}, &srcOrder, mockMapService)
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 0.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(tt, "unable to get order's location from status [DRIVER_ARRIVED_RESTAURANT]", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])

		require.Equal(tt, 50.0, formService.Prefill["D-CANCELED-RESTAURANT"])
		require.Equal(tt, true, formService.Prefill["D-CANCELED-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-CANCELED-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["D-CANCELED-RESTAURANT-MESSAGE"])

		require.Equal(tt, 50.0, formService.Prefill["MIN-D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-D-CANCELED-RESTAURANT"])

		require.Equal(tt, 1.36, formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-CANCELED"])
		require.Equal(tt, "0", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-CANCELED-FLAG"])
		require.Equal(tt, "", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-CANCELED-MESSAGE"])

		require.Equal(tt, 0.0, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(tt, "calculate radius unable to get order's location from status [DRIVER_ARRIVED_RESTAURANT]", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])
		require.True(t, floatEqualsWithTwoDecimals(11271183.920626584, formService.Prefill["R-CANCELED-RESTAURANT"].(float64)))
		require.Equal(tt, false, formService.Prefill["R-CANCELED-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-CANCELED-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["R-CANCELED-RESTAURANT-MESSAGE"])
		require.True(t, floatEqualsWithTwoDecimals(11271183.920626584, formService.Prefill["MIN-R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-R-CANCELED-RESTAURANT"].(float64)))

	})

	// msg_driver_compensation
	t.Run("unable_to_get_order_location_from_status", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.ServiceType = model.ServiceMessenger
		delete(srcOrder.HistoryLocation, "ARRIVED_AT_0")

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 0.0, formService.Prefill["D-ARRIVED_AT_0-CANCELED"])
		require.Equal(tt, false, formService.Prefill["D-ARRIVED_AT_0-CANCELED-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["D-ARRIVED_AT_0-CANCELED-FLAG"])
		require.Equal(tt, "unable to get order's location from status [ARRIVED_AT_0]", formService.Prefill["D-ARRIVED_AT_0-CANCELED-MESSAGE"])
		require.Equal(tt, 0.0, formService.Prefill["D-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["D-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["D-ARRIVED_AT_0-USER-FLAG"])
		require.Equal(tt, "unable to get order's location from status [ARRIVED_AT_0]", formService.Prefill["D-ARRIVED_AT_0-USER-MESSAGE"])
		require.Equal(tt, 0.0, formService.Prefill["MIN-D-ARRIVED_AT_0-CANCELED-D-ARRIVED_AT_0-USER"])

		require.Equal(tt, 0.0, formService.Prefill["R-ARRIVED_AT_0-CANCELED"])
		require.Equal(tt, false, formService.Prefill["R-ARRIVED_AT_0-CANCELED-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["R-ARRIVED_AT_0-CANCELED-FLAG"])
		require.Equal(tt, "calculate radius unable to get order's location from status [ARRIVED_AT_0]", formService.Prefill["R-ARRIVED_AT_0-CANCELED-MESSAGE"])
		require.Equal(tt, 0.0, formService.Prefill["R-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["R-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["R-ARRIVED_AT_0-USER-FLAG"])
		require.Equal(tt, "calculate radius unable to get order's location from status [ARRIVED_AT_0]", formService.Prefill["R-ARRIVED_AT_0-USER-MESSAGE"])
		require.Equal(tt, 0.0, formService.Prefill["MIN-R-ARRIVED_AT_0-CANCELED-R-ARRIVED_AT_0-USER"])
	})

	// bike_driver_compensation
	t.Run("bike_compensation_cancelled_driver_matched", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.History = map[string]time.Time{
			string(model.StatusDriverMatched): time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			string(model.StatusCanceled):      time.Date(2023, 05, 17, 06, 24, 06, 054, time.UTC),
		}
		srcOrder.HistoryLocation = map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverMatched): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       7.1943611,
				Lng:       100.5911157,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
		}
		srcOrder.ServiceType = model.ServiceBike
		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{{Lat: 7.1977923, Lng: 100.5952327}},
			mapservice.Location{Lat: 7.1943611, Lng: 100.5911157}).Return(
			&mapservice.Table{
				Distances: []float64{501},
			}, nil,
		)

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 501.0, formService.Prefill["D-DRIVER_MATCHED-CANCELED"])
		require.Equal(tt, true, formService.Prefill["D-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 0.0, formService.Prefill["D-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["D-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["D-ARRIVED_AT_0-USER-FLAG"])
		require.Equal(tt, 6.44, formService.Prefill["T-DRIVER_MATCHED-CANCELED"])
		require.Equal(tt, "true", formService.Prefill["T-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 0.0, formService.Prefill["T-ARRIVED_AT_0-CANCELED"])
		require.Equal(tt, false, formService.Prefill["T-ARRIVED_AT_0-CANCELED-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_TIME", formService.Prefill["T-ARRIVED_AT_0-CANCELED-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(593.1404534952723, formService.Prefill["R-DRIVER_MATCHED-CANCELED"].(float64)))
		require.Equal(tt, true, formService.Prefill["R-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 0.0, formService.Prefill["R-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["R-ARRIVED_AT_0-USER-VALUE"])
	})

	// bike_driver_compensation
	t.Run("bike_compensation_cancelled_driver_matched_lower_500", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.History = map[string]time.Time{
			string(model.StatusDriverMatched): time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			string(model.StatusCanceled):      time.Date(2023, 05, 17, 06, 24, 06, 054, time.UTC),
		}
		srcOrder.HistoryLocation = map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverMatched): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       7.1943611,
				Lng:       100.5911157,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
		}
		srcOrder.ServiceType = model.ServiceBike
		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{{Lat: 7.1977923, Lng: 100.5952327}},
			mapservice.Location{Lat: 7.1943611, Lng: 100.5911157}).Return(
			&mapservice.Table{
				Distances: []float64{499},
			}, nil,
		)

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 499.0, formService.Prefill["D-DRIVER_MATCHED-CANCELED"])
		require.Equal(tt, false, formService.Prefill["D-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 0.0, formService.Prefill["D-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["D-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["D-ARRIVED_AT_0-USER-FLAG"])
		require.Equal(tt, 6.44, formService.Prefill["T-DRIVER_MATCHED-CANCELED"])
		require.Equal(tt, "true", formService.Prefill["T-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 0.0, formService.Prefill["T-ARRIVED_AT_0-CANCELED"])
		require.Equal(tt, false, formService.Prefill["T-ARRIVED_AT_0-CANCELED-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_TIME", formService.Prefill["T-ARRIVED_AT_0-CANCELED-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(593.1404534952723, formService.Prefill["R-DRIVER_MATCHED-CANCELED"].(float64)))
		require.Equal(tt, true, formService.Prefill["R-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 0.0, formService.Prefill["R-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["R-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["R-ARRIVED_AT_0-USER-FLAG"])
	})

	// bike_driver_compensation
	t.Run("bike_compensation_cancelled_arrived_at_0", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.History = map[string]time.Time{
			string(model.StatusDriverMatched): time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			"ARRIVED_AT_0":                    time.Date(2023, 05, 17, 06, 20, 39, 302, time.UTC),
			string(model.StatusCanceled):      time.Date(2023, 05, 17, 06, 24, 06, 054, time.UTC),
		}
		srcOrder.HistoryLocation = map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverMatched): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			"ARRIVED_AT_0": {
				Lat:       7.1977929,
				Lng:       100.5952329,
				UpdatedAt: time.Date(2023, 05, 17, 06, 20, 39, 299, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       7.1943611,
				Lng:       100.5911157,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
		}
		srcOrder.ServiceType = model.ServiceBike
		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{{Lat: 7.1977923, Lng: 100.5952327}},
			mapservice.Location{Lat: 7.1943611, Lng: 100.5911157}).Return(
			&mapservice.Table{
				Distances: []float64{501},
			}, nil,
		)

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{{Lat: 7.1977929, Lng: 100.5952329}},
			mapservice.Location{Lat: -1, Lng: 1}).Return(
			&mapservice.Table{
				Distances: []float64{301},
			}, nil,
		)

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 501.0, formService.Prefill["D-DRIVER_MATCHED-CANCELED"])
		require.Equal(tt, true, formService.Prefill["D-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 301.0, formService.Prefill["D-ARRIVED_AT_0-USER"])
		require.Equal(tt, true, formService.Prefill["D-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-ARRIVED_AT_0-USER-FLAG"])
		require.Equal(tt, 6.44, formService.Prefill["T-DRIVER_MATCHED-CANCELED"])
		require.Equal(tt, "true", formService.Prefill["T-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 3.44, formService.Prefill["T-ARRIVED_AT_0-CANCELED"])
		require.Equal(tt, "false", formService.Prefill["T-ARRIVED_AT_0-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-ARRIVED_AT_0-CANCELED-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(593.1404534952723, formService.Prefill["R-DRIVER_MATCHED-CANCELED"].(float64)))
		require.Equal(tt, true, formService.Prefill["R-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_MATCHED-CANCELED-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(11079430.841011474, formService.Prefill["R-ARRIVED_AT_0-USER"].(float64)))
		require.Equal(tt, true, formService.Prefill["R-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-ARRIVED_AT_0-USER-FLAG"])
	})

	// bike_driver_compensation
	t.Run("bike_compensation_cancelled_arrived_at_0_lower_300", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.History = map[string]time.Time{
			string(model.StatusDriverMatched): time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			"ARRIVED_AT_0":                    time.Date(2023, 05, 17, 06, 01, 39, 302, time.UTC),
			string(model.StatusCanceled):      time.Date(2023, 05, 17, 06, 17, 06, 054, time.UTC),
		}
		srcOrder.HistoryLocation = map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverMatched): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			"ARRIVED_AT_0": {
				Lat:       7.1977929,
				Lng:       100.5952329,
				UpdatedAt: time.Date(2023, 05, 17, 06, 20, 39, 299, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       7.1943611,
				Lng:       100.5911157,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
		}
		srcOrder.ServiceType = model.ServiceBike
		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{{Lat: 7.1977923, Lng: 100.5952327}},
			mapservice.Location{Lat: 7.1943611, Lng: 100.5911157}).Return(
			&mapservice.Table{
				Distances: []float64{501},
			}, nil,
		)

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{{Lat: 7.1977929, Lng: 100.5952329}},
			mapservice.Location{Lat: -1, Lng: 1}).Return(
			&mapservice.Table{
				Distances: []float64{299},
			}, nil,
		)

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 501.0, formService.Prefill["D-DRIVER_MATCHED-CANCELED"])
		require.Equal(tt, true, formService.Prefill["D-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 299.0, formService.Prefill["D-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["D-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-ARRIVED_AT_0-USER-FLAG"])
		require.Equal(tt, 0.55, formService.Prefill["T-DRIVER_MATCHED-CANCELED"])
		require.Equal(tt, "false", formService.Prefill["T-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-DRIVER_MATCHED-CANCELED-FLAG"])
		require.Equal(tt, 15.44, formService.Prefill["T-ARRIVED_AT_0-CANCELED"])
		require.Equal(tt, "true", formService.Prefill["T-ARRIVED_AT_0-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-ARRIVED_AT_0-CANCELED-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(593.1404534952723, formService.Prefill["R-DRIVER_MATCHED-CANCELED"].(float64)))
		require.Equal(tt, true, formService.Prefill["R-DRIVER_MATCHED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_MATCHED-CANCELED-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(11079430.841011474, formService.Prefill["R-ARRIVED_AT_0-USER"].(float64)))
		require.Equal(tt, true, formService.Prefill["R-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-ARRIVED_AT_0-USER-FLAG"])
	})

	// mart_long_waiting_time_completed
	t.Run("invalid_order_location_status", func(tt *testing.T) {
		finish, mockMapService, _ := funcGenerateDeps()
		defer finish()

		srcOrder := generatePlainCompletedOrder()
		srcOrder.ServiceType = model.ServiceMart
		delete(srcOrder.HistoryLocation, string(model.StatusCompleted))

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverArrivedRestaurant), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).Return(
			&mapservice.Table{
				Distances: []float64{140},
			}, nil,
		)
		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverToDestination), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).Return(
			&mapservice.Table{
				Distances: []float64{180},
			}, nil,
		)

		ctx := context.Background()
		fcForm := service.NewLongWaitingTimeCompensationClaimForm(&model.Driver{}, &srcOrder, mockMapService)
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 140.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(tt, true, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])

		require.True(t, floatEqualsWithTwoDecimals(11242218.023612035, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"].(float64)))
		require.Equal(tt, false, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])

		require.Equal(tt, 0.86, formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION"])
		require.Equal(tt, "0", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-FLAG"])
		require.Equal(tt, "", formService.Prefill["T-DRIVER_ARRIVED_RESTAURANT-DRIVER_TO_DESTINATION-MESSAGE"])

		require.Equal(tt, 180.0, formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_TO_DESTINATION-RESTAURANT-MESSAGE"])

		require.Equal(tt, 0.0, formService.Prefill["D-COMPLETED-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["D-COMPLETED-RESTAURANT-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["D-COMPLETED-RESTAURANT-FLAG"])
		require.Equal(tt, "unable to get order's location from status [COMPLETED]", formService.Prefill["D-COMPLETED-RESTAURANT-MESSAGE"])

		require.True(t, floatEqualsWithTwoDecimals(11241455.11356319, formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT"].(float64)))
		require.Equal(tt, false, formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_TO_DESTINATION-RESTAURANT-MESSAGE"])

		require.Equal(tt, 0.0, formService.Prefill["R-COMPLETED-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["R-COMPLETED-RESTAURANT-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["R-COMPLETED-RESTAURANT-FLAG"])
		require.Equal(tt, "calculate radius unable to get order's location from status [COMPLETED]", formService.Prefill["R-COMPLETED-RESTAURANT-MESSAGE"])
	})

	// food_food_claim_cancelled_driver_arrived_restaurant
	t.Run("map_service_error", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		srcOrder := generatePlainCancelledOrder()
		srcOrder.ServiceType = model.ServiceFood

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverArrivedRestaurant), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).
			Return(&mapservice.Table{
				Distances: []float64{100},
			}, nil)
		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusCanceled), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: 1.0, Lng: -1.0}).
			Return(nil, errors.New("INTERNAL_SERVER_ERROR"))

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		ctx := context.Background()
		fcForm := service.NewFoodClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService)
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 0.0, formService.Prefill["D-CANCELED-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["D-CANCELED-RESTAURANT-VALUE"])
		require.Equal(tt, "MAP_SERVICE_ERROR", formService.Prefill["D-CANCELED-RESTAURANT-FLAG"])
		require.Equal(tt, "internal server error on map service", formService.Prefill["D-CANCELED-RESTAURANT-MESSAGE"])

		require.Equal(tt, 100.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(tt, true, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-MESSAGE"])

		require.Equal(tt, 100.0, formService.Prefill["MIN-D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-D-CANCELED-RESTAURANT"])
	})

	// mart_rider_compensation_cancelled_arrived
	t.Run("got_empty_distance_from_map_service", func(tt *testing.T) {
		finish, mockMapService, vosService := funcGenerateDeps()
		defer finish()

		srcOrder := generatePlainCancelledOrder()
		srcOrder.ServiceType = model.ServiceMart
		srcOrder.History[string(model.StatusDriverArrived)] = srcOrder.History[string(model.StatusDriverArrivedRestaurant)].Add(time.Minute)
		srcOrder.HistoryLocation[string(model.StatusDriverArrived)] = srcOrder.HistoryLocation[string(model.StatusDriverArrivedRestaurant)]

		mockMapService.EXPECT().FindDistances(
			gomock.Any(),
			[]mapservice.Location{funcGetMapSvcLoc(string(model.StatusDriverArrived), srcOrder.HistoryLocation)},
			mapservice.Location{Lat: -1.0, Lng: 1.0}).Return(
			&mapservice.Table{}, nil,
		)

		cancelReasonUrl := "cancel-reason-url"
		vosService.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosService.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosService, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)

		require.Equal(tt, 0.0, formService.Prefill["D-ARRIVED-USER"])
		require.Equal(tt, false, formService.Prefill["D-ARRIVED-USER-VALUE"])
		require.Equal(tt, "UNABLE_TO_FIND_DISTANCE", formService.Prefill["D-ARRIVED-USER-FLAG"])
		require.Equal(tt, "unable to find distance via map service", formService.Prefill["D-ARRIVED-USER-MESSAGE"])

		require.Equal(tt, 0.36, formService.Prefill["T-ARRIVED-CANCELED"])
		require.Equal(tt, "false", formService.Prefill["T-ARRIVED-CANCELED-VALUE"])
		require.Equal(tt, "", formService.Prefill["T-ARRIVED-CANCELED-FLAG"])
		require.Equal(tt, "", formService.Prefill["T-ARRIVED-CANCELED-MESSAGE"])
	})

	// mart_driver_compensation
	t.Run("mart_compensation_cancelled_driver_arrived_restaurant", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.History = map[string]time.Time{
			string(model.StatusDriverArrivedRestaurant): time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			string(model.StatusCanceled):                time.Date(2023, 05, 17, 06, 24, 06, 054, time.UTC),
		}
		srcOrder.HistoryLocation = map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverArrivedRestaurant): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       7.1943611,
				Lng:       100.5911157,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
		}
		srcOrder.ServiceType = model.ServiceMart
		mockMapService.EXPECT().FindDistances(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&mapservice.Table{
				Distances: []float64{501},
			}, nil,
		).Times(2)

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 501.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(11271621.122792876, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"].(float64)))
		require.Equal(tt, false, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
	})

	// food_driver_compensation
	t.Run("food_compensation_cancelled_driver_arrived_restaurant", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.History = map[string]time.Time{
			string(model.StatusDriverArrivedRestaurant): time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			string(model.StatusCanceled):                time.Date(2023, 05, 17, 06, 24, 06, 054, time.UTC),
		}
		srcOrder.HistoryLocation = map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverArrivedRestaurant): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       7.1943611,
				Lng:       100.5911157,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
		}
		srcOrder.ServiceType = model.ServiceFood
		mockMapService.EXPECT().FindDistances(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&mapservice.Table{
				Distances: []float64{501},
			}, nil,
		).Times(2)

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 501.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(11271621.122792876, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"].(float64)))
		require.Equal(tt, false, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
	})

	t.Run("food_compensation_cancelled_driver_arrived_restaurant_lower_150", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.History = map[string]time.Time{
			string(model.StatusDriverArrivedRestaurant): time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			string(model.StatusCanceled):                time.Date(2023, 05, 17, 06, 24, 06, 054, time.UTC),
		}
		srcOrder.HistoryLocation = map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverArrivedRestaurant): {
				Lat:       13.781091,
				Lng:       100.585689,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       13.781009,
				Lng:       100.584838,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
		}
		srcOrder.ServiceType = model.ServiceFood
		srcOrder.Routes[0].Location = model.Location{Lat: 13.781009, Lng: 100.584838}
		mockMapService.EXPECT().FindDistances(gomock.Any(), gomock.Any(), gomock.Any()).Return(
			&mapservice.Table{
				Distances: []float64{501},
			}, nil,
		).Times(2)

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 501.0, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"])
		require.Equal(tt, false, formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["D-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
		require.True(t, floatEqualsWithTwoDecimals(92.34964712056872, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT"].(float64)))
		require.Equal(tt, true, formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-VALUE"])
		require.Equal(tt, "", formService.Prefill["R-DRIVER_ARRIVED_RESTAURANT-RESTAURANT-FLAG"])
	})

	// messenger_driver_compensation
	t.Run("messenger_compensation_cancelled_driver_matched", func(tt *testing.T) {
		finish, mockMapService, vosSvc := funcGenerateDeps()
		defer finish()

		cancelReasonUrl := "cancel-reason-url"
		vosSvc.EXPECT().
			ListObjectsFromVOSInternal(gomock.Any(), gomock.Any()).
			Return(&s3.ListObjectsOutput{
				Contents: []*s3.Object{
					{
						Key: &cancelReasonUrl,
					},
				},
			}, nil)
		vosSvc.EXPECT().
			GetConfigBucket(gomock.Any(), gomock.Any()).
			Return("", nil)

		srcOrder := generatePlainCancelledOrder()
		srcOrder.History = map[string]time.Time{
			string(model.StatusDriverMatched): time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			string(model.StatusCanceled):      time.Date(2023, 05, 17, 06, 24, 06, 054, time.UTC),
		}
		srcOrder.HistoryLocation = map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverMatched): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       7.1943611,
				Lng:       100.5911157,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
		}
		srcOrder.ServiceType = model.ServiceMessenger

		ctx := context.Background()
		fcForm := service.NewRiderCompensationClaimForm(&model.Driver{}, &srcOrder, &model.Trip{}, vosSvc, mockMapService, &service.AtomicFormServiceConfig{}, config.FormConfig{
			IsEnableRiderCompensationTimeAndDistanceLogicFeature: true,
		})
		formService, err := fcForm.Get(ctx)
		require.NoError(tt, err)
		require.Equal(tt, 0.0, formService.Prefill["D-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["D-ARRIVED_AT_0-USER-VALUE"])
		require.Equal(tt, "UNABLE_TO_GET_LOCATION", formService.Prefill["D-ARRIVED_AT_0-USER-FLAG"])
		require.Equal(tt, 0.0, formService.Prefill["R-ARRIVED_AT_0-USER"])
		require.Equal(tt, false, formService.Prefill["R-ARRIVED_AT_0-USER-VALUE"])
	})
}

func generatePlainCompletedOrder() model.Order {
	return model.Order{
		Status: model.StatusCompleted,
		Quote: model.Quote{
			Routes: []model.Stop{{
				Location: model.Location{Lat: 1.0, Lng: -1.0},
			}, {
				Location: model.Location{Lat: -1.0, Lng: 1.0},
			}},
		},
		History: map[string]time.Time{
			string(model.StatusDriverMatched):           time.Date(2023, 05, 17, 02, 25, 58, 251, time.UTC),
			string(model.StatusRestaurantAccepted):      time.Date(2023, 05, 17, 02, 26, 20, 400, time.UTC),
			string(model.StatusDriverArrivedRestaurant): time.Date(2023, 05, 17, 02, 27, 55, 116, time.UTC),
			string(model.StatusDriverToRestaurant):      time.Date(2023, 05, 17, 02, 26, 31, 013, time.UTC),
			string(model.StatusCompleted):               time.Date(2023, 05, 17, 02, 33, 29, 482, time.UTC),
			string(model.StatusAssigningDriver):         time.Date(2023, 05, 17, 02, 25, 54, 163, time.UTC),
			string(model.StatusDriverToDestination):     time.Date(2023, 05, 17, 02, 28, 47, 525, time.UTC),
			string(model.StatusDriverArrived):           time.Date(2023, 05, 17, 02, 33, 24, 813, time.UTC),
			string(model.StatusDropOffDone):             time.Date(2023, 05, 17, 02, 33, 29, 451, time.UTC),
		},
		HistoryLocation: map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverMatched): {
				Lat:       14.2352662,
				Lng:       100.7236002,
				UpdatedAt: time.Date(2023, 05, 17, 02, 25, 58, 247, time.UTC),
			},
			string(model.StatusDriverToDestination): {
				Lat:       14.2321225,
				Lng:       100.7186202,
				UpdatedAt: time.Date(2023, 05, 17, 02, 28, 47, 525, time.UTC),
			},
			string(model.StatusDriverToRestaurant): {
				Lat:       14.2337495,
				Lng:       100.721375,
				UpdatedAt: time.Date(2023, 05, 17, 02, 26, 31, 013, time.UTC),
			},
			string(model.StatusDriverArrived): {
				Lat:       14.2320687,
				Lng:       100.7257115,
				UpdatedAt: time.Date(2023, 05, 17, 02, 33, 24, 813, time.UTC),
			},
			string(model.StatusCompleted): {
				Lat:       14.2320687,
				Lng:       100.7257115,
				UpdatedAt: time.Date(2023, 05, 17, 02, 33, 29, 451, time.UTC),
			},
			string(model.StatusDriverArrivedRestaurant): {
				Lat:       14.2320687,
				Lng:       100.7257115,
				UpdatedAt: time.Date(2023, 05, 17, 02, 27, 55, 116, time.UTC),
			},
		},
	}
}

func generatePlainCancelledOrder() model.Order {
	return model.Order{
		Status: model.StatusCanceled,
		Quote: model.Quote{
			Routes: []model.Stop{{
				Location: model.Location{Lat: 1.0, Lng: -1.0},
			}, {
				Location: model.Location{Lat: -1.0, Lng: 1.0},
			}},
		},
		History: map[string]time.Time{
			string(model.StatusDriverMatched):           time.Date(2023, 05, 17, 06, 17, 39, 302, time.UTC),
			string(model.StatusRestaurantAccepted):      time.Date(2023, 05, 17, 06, 17, 39, 922, time.UTC),
			string(model.StatusDriverArrivedRestaurant): time.Date(2023, 05, 17, 06, 22, 44, 531, time.UTC),
			string(model.StatusDriverToRestaurant):      time.Date(2023, 05, 17, 06, 17, 46, 212, time.UTC),
			string(model.StatusAssigningDriver):         time.Date(2023, 05, 17, 06, 17, 33, 372, time.UTC),
			string(model.StatusDriverToDestination):     time.Date(2023, 05, 17, 06, 22, 49, 288, time.UTC),
			string(model.StatusCanceled):                time.Date(2023, 05, 17, 06, 24, 06, 054, time.UTC),
		},
		HistoryLocation: map[string]model.LocationWithUpdatedAt{
			string(model.StatusDriverMatched): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 39, 299, time.UTC),
			},
			string(model.StatusDriverToDestination): {
				Lat:       7.1943566,
				Lng:       100.5911013,
				UpdatedAt: time.Date(2023, 05, 17, 06, 22, 49, 288, time.UTC),
			},
			string(model.StatusDriverToRestaurant): {
				Lat:       7.1977923,
				Lng:       100.5952327,
				UpdatedAt: time.Date(2023, 05, 17, 06, 17, 46, 211, time.UTC),
			},
			string(model.StatusCanceled): {
				Lat:       7.1943611,
				Lng:       100.5911157,
				UpdatedAt: time.Date(2023, 05, 17, 06, 23, 04, 985, time.UTC),
			},
			string(model.StatusDriverArrivedRestaurant): {
				Lat:       7.1943566,
				Lng:       100.5911013,
				UpdatedAt: time.Date(2023, 05, 17, 06, 22, 44, 531, time.UTC),
			},
		},
		CancelDetail: model.CancelDetail{
			Reason:             "ลูกค้าต้องการแก้ไขรายการอาหาร",
			CancelledBy:        "CS",
			Source:             "DRIVER",
			Requestor:          "<EMAIL>",
			Label:              "ลูกค้าต้องการแก้ไขรายการอาหาร",
			Name:               "USER_EDIT_MENU - ลูกค้าต้องการแก้ไขรายการอาหาร",
			CancellationSource: "USER",
		},
	}
}

func floatEqualsWithTwoDecimals(a, b float64) bool {
	roundedA := math.Round(a*100) / 100
	roundedB := math.Round(b*100) / 100
	return roundedA == roundedB
}
