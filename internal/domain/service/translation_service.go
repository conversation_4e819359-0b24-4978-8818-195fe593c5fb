package service

//go:generate mockgen -source=./translation_service.go -destination=./mock_service/mock_translation_service.go -package=mock_service

import (
	"context"

	"git.wndv.co/go/logx/v2"
	common "git.wndv.co/go/proto/lineman/shared/common/v1"
	translation "git.wndv.co/go/proto/lineman/shared/translation/v1"
	grpcTranslation "git.wndv.co/go/proto/lineman/translation/v1"
)

type TranslationService interface {
	Translate(ctx context.Context, message string, source, target common.TranslationLanguage) string
	TranslateENToTH(ctx context.Context, message string) string
}

type TranslationServiceImpl struct {
	translationClient grpcTranslation.TranslationServiceClient
}

func (ts TranslationServiceImpl) TranslateENToTH(ctx context.Context, message string) string {
	return ts.Translate(ctx, message, common.TranslationLanguage_TRANSLATION_LANGUAGE_ENGLISH, common.TranslationLanguage_TRANSLATION_LANGUAGE_THAI)
}

func (ts TranslationServiceImpl) Translate(ctx context.Context, message string, sourceLanguage, targetLanguage common.TranslationLanguage) string {
	req := toRequest(sourceLanguage, targetLanguage, message)
	result, err := ts.translationClient.Translate(ctx, req)
	if err != nil {
		logx.Error().Context(ctx).Err(err).Msg("[TranslateErr] - failed to translate message")
	} else if len(result.GetResult()) > 0 {
		return result.Result[0].Message
	}
	return message
}

func toRequest(sourceLanguage common.TranslationLanguage, targetLanguage common.TranslationLanguage, message string) *grpcTranslation.TranslateRequest {
	return &grpcTranslation.TranslateRequest{
		Provider:      translation.TranslationProvider_TRANSLATION_PROVIDER_PAPAGO,
		SourceService: "lm-driver",
		Payload: []*grpcTranslation.TranslateRequest_TranslatePayload{
			{
				SourceLanguage: sourceLanguage,
				TargetLanguage: targetLanguage,
				Message:        message,
			},
		},
	}
}

func ProvideTranslationServiceImpl(translationClient grpcTranslation.TranslationServiceClient) *TranslationServiceImpl {
	return &TranslationServiceImpl{
		translationClient: translationClient,
	}
}
