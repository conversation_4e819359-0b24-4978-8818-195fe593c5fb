package service

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"git.wndv.co/go/logx/v2"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"git.wndv.co/lineman/xgo/api"
)

var (
	ErrInvalidOrderStatusToClaim             error = errors.New("invalid order status to claim")
	ErrInvalidOrderHistoryToClaim            error = errors.New("invalid order history to claim")
	ErrInvalidOrderServiceTypeToClaim        error = errors.New("invalid order service type to claim")
	ErrInvalidOrderRegionToClaim             error = errors.New("invalid order region to claim")
	ErrInvalidOrderNotFound                  error = errors.New("invalid order not found")
	ErrInvalidTimeToClaim                    error = errors.New("invalid time to claim")
	ErrInvalidServiceTypeToClaim             error = errors.New("invalid service type")
	ErrInvalidCancelDetailToClaim            error = errors.New("cancel detail is not allowed to submit this form")
	ErrOrderNotMeetCriteria                  error = errors.New("Order not meet the criteria to claim")
	ErrOrderHistoryDontHaveEnoughInformation       = errors.New("order status history doesn't have enough information")
	ErrOrderStatusNoCancelTime                     = errors.New("order status has no cancel time")
	ErrNoInterestedOrderStatusInHistory            = errors.New("no interested order status in history")
	ErrFormAlreadyCreated                    error = errors.New("form already created")

	// QR payment
	ErrOrderDeliveryFeePaymentNotQR      error = errors.New("order delivery fee payment not QR")
	ErrOrderDeliveryFeeQRAlreadyResolved error = errors.New("order delivery fee payment QR already resolved")
	ErrFormNotAllowToCreate              error = errors.New("form not allow to create")
)

const googleDirectionURL = "https://www.google.com/maps/dir/?api=1&origin=%v,%v&destination=%v,%v&travelmode=two-wheeler"

type baseDriverClaimForm struct{}

func (b *baseDriverClaimForm) validateOrderToCreateClaimForm(order *model.Order, expectedStatus []string, expectedHistoryStatus []string, expectedServiceType []string, subtype model.FormSubtype, formConfig config.FormConfig) error {
	if order == nil {
		return ErrInvalidOrderNotFound
	}

	switch subtype {
	case model.FormSubtypeDriver2WQRPaymentClaim:
		if order.CreatedAt.Before(timeutil.BangkokNow().AddDate(0, 0, -formConfig.FormClaimQRPaymentAllowToClaimWithinDay)) {
			return ErrInvalidTimeToClaim
		}
	default:
		if order.CreatedAt.Before(timeutil.BangkokNow().AddDate(0, 0, -1)) {
			return ErrInvalidTimeToClaim
		}
	}

	isStatusMatched := true
	if len(expectedStatus) > 0 {
		isStatusMatched = stringutil.IsStringInList(expectedStatus, string(order.Status))
	}
	if !isStatusMatched {
		return ErrInvalidOrderStatusToClaim
	}

	isStatusHistoryMatched := true
	if len(expectedHistoryStatus) > 0 {
		for _, status := range expectedHistoryStatus {
			if _, existed := order.History[status]; !existed {
				isStatusHistoryMatched = false
				break
			}
		}
	}
	if !isStatusHistoryMatched {
		return ErrInvalidOrderHistoryToClaim
	}

	isServiceTypeMatched := true
	if len(expectedServiceType) > 0 {
		isServiceTypeMatched = stringutil.IsStringInList(expectedServiceType, string(order.ServiceType))
	}
	if !isServiceTypeMatched {
		return ErrInvalidOrderServiceTypeToClaim
	}

	return nil
}

func (b *baseDriverClaimForm) getDriversCancelReasonImageUrl(ctx context.Context, vosService VOSService, order *model.Order) ([]string, error) {
	prefix2 := fmt.Sprintf("%s/%s/%s/", file.PublicPath, CancelOrderFilePath, order.OrderID)
	listOutput2, err := vosService.ListObjectsFromVOSInternal(ctx, prefix2)
	if err != nil {
		return []string{}, err
	}
	vosInternalBucket, _ := vosService.GetConfigBucket(ctx, WithVOSTokyo)
	return b.getImageUrlFromS3ListObjectOutput(listOutput2, vosInternalBucket), nil
}

func (b *baseDriverClaimForm) getImageUrlFromS3ListObjectOutput(src *s3.ListObjectsOutput, bucket string) []string {
	res := make([]string, 0)
	for _, content := range src.Contents {
		key := content.Key
		if content.Size != nil && (*content.Size != 0) && key != nil {
			if bucket == "" {
				res = append(res, fmt.Sprintf("vos://%s", *key))
				continue
			}
			res = append(res, fmt.Sprintf("vos://%s/%s", bucket, *key))
		}
	}
	return res
}

type claimFormFoodClaim struct {
	baseDriverClaimForm
	Type       model.FormType
	Subtype    model.FormSubtype
	Driver     *model.Driver
	Order      *model.Order
	Trip       *model.Trip
	vosService VOSService
	mapService mapservice.MapService
}

func NewFoodClaimForm(driver *model.Driver, order *model.Order, trip *model.Trip, vosService VOSService, mapService mapservice.MapService) model.FormBuilder {
	return &claimFormFoodClaim{
		Type:       model.FormTypeDriversClaim,
		Subtype:    model.FormSubtypeFoodClaim,
		Driver:     driver,
		Order:      order,
		Trip:       trip,
		vosService: vosService,
		mapService: mapService,
	}
}

func (f *claimFormFoodClaim) Validate(_ context.Context) (bool, error) {
	if !stringutil.IsStringInList([]string{model.ServiceFood.String(), model.ServiceMart.String()},
		f.Order.ServiceType.String()) {
		return false, ErrInvalidServiceTypeToClaim
	}
	err := f.validateOrderToCreateClaimForm(
		f.Order,
		[]string{string(model.StatusCanceled)},
		[]string{string(model.StatusDriverArrivedRestaurant)},
		[]string{string(model.ServiceFood), string(model.ServiceMart)},
		f.Subtype,
		config.FormConfig{},
	)
	return err == nil, err
}

func (f *claimFormFoodClaim) Get(ctx context.Context) (model.FormService, error) {
	prefill := make(map[string]interface{})

	prefill[model.FormPrefillDriverId] = f.Driver.DriverID              // Driver ID
	prefill[model.FormPrefillFirstName] = f.Driver.Firstname.String()   // Driver's First Name
	prefill[model.FormPrefillLastName] = f.Driver.Lastname.String()     // Driver's Last Name
	prefill[model.FormPrefillOrderId] = f.Order.OrderID                 // Order ID
	prefill[model.FormPrefillTripId] = f.Order.TripID                   // Trip ID
	prefill[model.FormPrefillOrderStatus] = string(f.Order.Status)      // Order Status
	prefill[model.FormPrefillWorkingRegion] = f.Driver.Region.String()  // Working Region
	prefill[model.FormPrefillServiceType] = string(f.Order.ServiceType) // Service Type
	prefill[model.FormPrefillDriverType] = f.Driver.DriverType.String() // Driver Type
	prefill[model.FormPrefillDriverVendorID] = f.Driver.DriverVendorID  // Driver Vendor ID
	prefill[model.FormPrefillFoodPhotoUrls] = []any{}
	prefill[model.FormPrefillRequesterCantConnectPhotoUrls] = []any{}
	prefill[model.FormPrefillPaymentMethod] = string(f.Order.PriceSummary().DeliveryFee.PaymentMethod)
	if len(f.Order.Routes) > 1 {
		prefill[model.FormPrefillFoodCompensationAmount] = f.Order.Routes[1].PriceSummary.ItemFee.SubTotal
	}
	if f.Trip != nil {
		prefill[model.FormPrefillDriverWage] = f.Trip.DriverWageSummary.TotalDriverWage.Float64()
	}
	prefill[model.FormPrefillDriverMoneyFlow] = string(f.Order.Options.DriverMoneyFlow)
	f.prefillDriversCancelReasonImageUrlsIfPossible(ctx, f.vosService, f.Order, prefill)
	if f.Order.Status == model.StatusCanceled &&
		f.Order.CancelDetail.Source == model.SourceDriver &&
		f.Order.CancelDetail.CancelledBy == model.CancelledByLINEManRider &&
		f.Order.CancelDetail.Reason == "REQUESTER_CANNOT_CONTACT" {

		driverCancelReasonImageUrls, err := f.getDriversCancelReasonImageUrl(ctx, f.vosService, f.Order)
		if err != nil {
			logrus.
				WithField("method", "claimFormFoodClaim.Get").
				Errorf("unable to get driver cancel reason image urls: %v", err.Error())
		}

		if len(driverCancelReasonImageUrls) > 0 {
			prefill[model.FormPrefillFoodPhotoUrls] = []any{driverCancelReasonImageUrls[0]} // Cancel Reason Image
		}
		if len(driverCancelReasonImageUrls) > 1 {
			prefill[model.FormPrefillRequesterCantConnectPhotoUrls] = []any{driverCancelReasonImageUrls[1]} // Food Image for Cancel Reason
		}
	}
	f.addCalculateDistanceToPrefill(ctx, prefill)
	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

func (f *claimFormFoodClaim) addCalculateDistanceToPrefill(ctx context.Context, srcPrefill map[string]interface{}) map[string]interface{} {
	if f.mapService == nil || srcPrefill == nil {
		return srcPrefill
	}
	formDistance := NewFormInfo(f.mapService, f.Order)

	latestStatus := GetOrderLatestStatus(f.Order, []string{
		string(model.StatusDriverArrivedRestaurant),
		string(model.StatusDriverArrived),
	})

	switch latestStatus {
	case string(model.StatusDriverArrivedRestaurant):
		d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
		d1.AddToPrefill(srcPrefill)
		d2 := formDistance.CalculateDistance(ctx, string(model.StatusCanceled), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
		d2.AddToPrefill(srcPrefill)
		srcPrefill[fmt.Sprintf("MIN-%v-%v", d1.Key, d2.Key)] = d1.Min(d2)
		r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
		r1.AddToPrefill(srcPrefill)
		r2 := formDistance.CalculateRadius(ctx, string(model.StatusCanceled), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
		r2.AddToPrefill(srcPrefill)
		srcPrefill[fmt.Sprintf("MIN-%v-%v", r1.Key, r2.Key)] = r1.Min(r2)

	case string(model.StatusDriverArrived):
		d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrived), statusUserLocation, FormDistancCheckingAtMostBuilder(300.0))
		d1.AddToPrefill(srcPrefill)
		r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrived), statusUserLocation, FormDistancCheckingAtMostBuilder(300.0))
		r1.AddToPrefill(srcPrefill)
		t1 := formDistance.CalculateTimeInMinute(ctx, string(model.StatusDriverArrived), string(model.StatusCanceled), FormDistancCheckingAtLeastBuilder(10.0))
		t1.AddToPrefill(srcPrefill)
	}

	return srcPrefill
}

func (b *baseDriverClaimForm) prefillDriversCancelReasonImageUrlsIfPossible(ctx context.Context, vosService VOSService, order *model.Order, prefill map[string]interface{}) {
	driverCancelReasonImageUrls, err := b.getDriversCancelReasonImageUrl(ctx, vosService, order)
	if err != nil {
		logrus.
			WithField("method", "getDriversCancelReasonImageUrl").
			Errorf("unable to get food image urls: %v", err.Error())
	}
	prefill[model.FormPrefillRiderSelfCancelPhotoUrls] = []any{}         // Cancel Reason Image
	prefill[model.FormPrefillOriginalRiderSelfCancelPhotoURLs] = []any{} // Original Cancel Reason Image
	if len(driverCancelReasonImageUrls) > 0 {
		var imageUrls []any
		for _, url := range driverCancelReasonImageUrls {
			imageUrls = append(imageUrls, url)
		}
		prefill[model.FormPrefillRiderSelfCancelPhotoUrls] = imageUrls
		prefill[model.FormPrefillOriginalRiderSelfCancelPhotoURLs] = imageUrls
	}
}

type claimFormRiderCompensation struct {
	baseDriverClaimForm
	Type              model.FormType
	Subtype           model.FormSubtype
	Driver            *model.Driver
	Order             *model.Order
	Trip              *model.Trip
	vosService        VOSService
	mapService        mapservice.MapService
	formServiceConfig *AtomicFormServiceConfig
	config            config.FormConfig
}

func NewRiderCompensationClaimForm(
	driver *model.Driver,
	order *model.Order,
	trip *model.Trip,
	vosService VOSService,
	mapService mapservice.MapService,
	formServiceConfig *AtomicFormServiceConfig,
	config config.FormConfig,
) model.FormBuilder {
	return &claimFormRiderCompensation{
		Type:              model.FormTypeDriversClaim,
		Subtype:           model.FormSubtypeRiderCompensation,
		Driver:            driver,
		Order:             order,
		Trip:              trip,
		vosService:        vosService,
		mapService:        mapService,
		formServiceConfig: formServiceConfig,
		config:            config,
	}
}

func (f *claimFormRiderCompensation) Validate(ctx context.Context) (bool, error) {
	if !stringutil.IsStringInList([]string{model.ServiceFood.String(), model.ServiceMart.String(),
		model.ServiceMessenger.String(), model.ServiceBike.String()}, f.Order.ServiceType.String()) {
		logx.Info().
			Context(ctx).
			Str("form_type", "rider_compensation").
			Str("order_id", fp.GetValueOrDefault(f.Order, model.Order{}).OrderID).
			Str("driver_id", fp.GetValueOrDefault(f.Driver, model.Driver{}).DriverID).
			Msg("Service Type doesn't match with the desired ServiceType")
		return false, ErrInvalidServiceTypeToClaim
	}

	expectedHistoryStatus := getExpectedHistoryStatus(f.Order.ServiceType)
	if f.config.IsEnableRiderCompensationTimeAndDistanceLogicFeature {
		expectedHistoryStatus = f.getExpectedHistoryStatusForRiderCompensation(f.Order.ServiceType)
	}

	err := f.validateOrderToCreateClaimForm(
		f.Order,
		[]string{string(model.StatusCanceled)},
		expectedHistoryStatus,
		[]string{},
		f.Subtype,
		config.FormConfig{},
	)
	if err != nil {
		logx.Info().
			Context(ctx).
			Str("form_type", "rider_compensation").
			Str("order_id", fp.GetValueOrDefault(f.Order, model.Order{}).OrderID).
			Str("driver_id", fp.GetValueOrDefault(f.Driver, model.Driver{}).DriverID).
			Str("validation_step", "validateOrderToCreateClaimForm").
			Err(err).
			Msg("Order failed the validation")
		return false, err
	}

	// [FeatureFlag] Controlling the TimeCheck and DistanceCheck feature
	if f.config.IsEnableRiderCompensationTimeAndDistanceLogicFeature {
		// Validate the distance and the time condition.
		// On Flow Create the Prefilled Form since on that flow, The validator already got the MapService and the Full Order data from the repository.
		err = f.validateBikeOrderFormSubmissionLogic(ctx)
	}

	return err == nil, err
}

func (f *claimFormRiderCompensation) validateBikeOrderFormSubmissionLogic(ctx context.Context) error {
	// Focus on Bike Order only
	if f.Order.ServiceType != model.ServiceBike {
		return nil
	}

	// If the source of cancellation is user, we allow all case since Transportation don't send the cancel reason
	// to Fleet yet.
	if f.Order.CancelDetail.Source == model.SourceUSER {
		return nil
	}

	// If the cancel_detail is the configured value, We don't allow the driver to submit this form at all.
	underCheckCancelDetail := ""
	switch f.Order.CancelDetail.CancelledBy {
	case model.CancelledByRider.String():
		underCheckCancelDetail = f.Order.CancelDetail.Reason
	case model.CancelledByCS.String():
		underCheckCancelDetail = f.Order.CancelDetail.Name
	}

	if f.formServiceConfig.Get().BikeCancelDetailNameSubmitRiderCompensationFormPrevention.Has(underCheckCancelDetail) {
		logx.Info().
			Context(ctx).
			Str("form_type", "rider_compensation").
			Str("order_id", fp.GetValueOrDefault(f.Order, model.Order{}).OrderID).
			Str("driver_id", fp.GetValueOrDefault(f.Driver, model.Driver{}).DriverID).
			Str("validation_step", "validateBikeOrderFormSubmissionLogic").
			Msgf("The CancelDetail name matched with the form submission prevention: %s", underCheckCancelDetail)
		return api.NewAPIError(api.ErrCodeInvalidRequest, ErrInvalidCancelDetailToClaim.Error())
	}
	return f.validateBikeOrderDistanceAndDisplacement(ctx)
}

type timeWithStatus struct {
	time   time.Time
	status model.Status
}

func (f *claimFormRiderCompensation) validateBikeOrderDistanceAndDisplacement(ctx context.Context) error {
	if f.Order.CancelDetail.CancellationMetadata == nil {
		f.Order.CancelDetail.CancellationMetadata = &model.CancellationMetadata{}
	}
	cancelTime, ok := f.Order.History[model.StatusCanceled.String()]
	if !ok {
		logx.Info().
			Context(ctx).
			Str("form_type", "rider_compensation").
			Str("order_id", fp.GetValueOrDefault(f.Order, model.Order{}).OrderID).
			Str("driver_id", fp.GetValueOrDefault(f.Driver, model.Driver{}).DriverID).
			Str("validation_step", "validateBikeOrderDistanceAndDisplacement").
			Msg("Order no Cancel Time")
		return api.NewAPIError(api.ErrCodeInvalidRequest, ErrOrderStatusNoCancelTime.Error())
	}

	timeWithStatuses := make([]timeWithStatus, 0, len(f.Order.History))
	for status, statusTimestamp := range f.Order.History {
		timeWithStatuses = append(timeWithStatuses, timeWithStatus{
			time:   statusTimestamp,
			status: model.Status(status),
		})
	}

	sort.SliceStable(timeWithStatuses, func(i, j int) bool {
		return timeWithStatuses[i].time.After(timeWithStatuses[j].time)
	})

	if len(timeWithStatuses) < 2 {
		logx.Info().
			Context(ctx).
			Str("form_type", "rider_compensation").
			Str("order_id", fp.GetValueOrDefault(f.Order, model.Order{}).OrderID).
			Str("driver_id", fp.GetValueOrDefault(f.Driver, model.Driver{}).DriverID).
			Str("validation_step", "validateBikeOrderDistanceAndDisplacement").
			Msgf("Order History have %d status which is not enough for checking", len(timeWithStatuses))
		return api.NewAPIError(api.ErrCodeInternalError, ErrOrderHistoryDontHaveEnoughInformation.Error())
	}

	// Expected that the first (timeWithStatuses[1]) should be status canceled,
	// And the status before canceled should be at the 1 position.
	// Known issue: If there is the status with the same timestamp, It would break this logic since.
	// We sort it from latest to oldest. So the status before cancel might not be corrected.
	// But it is the rare case.
	switch timeWithStatuses[1].status {
	case model.StatusReady:
		fallthrough
	case model.StatusDriverMatched:
		fallthrough
	case model.NewDriveToOrderStatus(0):
		driverMatchedTime, ok := f.Order.History[model.StatusDriverMatched.String()]
		if !ok {
			logx.Info().
				Context(ctx).
				Str("form_type", "rider_compensation").
				Str("order_id", fp.GetValueOrDefault(f.Order, model.Order{}).OrderID).
				Str("driver_id", fp.GetValueOrDefault(f.Driver, model.Driver{}).DriverID).
				Str("validation_condition", "DRIVE_TO_0").
				Str("validation_step", "validateBikeOrderDistanceAndDisplacement").
				Msg("Order Status History don't contain the DRIVER_MATCHED")
			return api.NewAPIError(api.ErrCodeInvalidRequest, ErrNoInterestedOrderStatusInHistory.Error())
		}

		timeRangeCondition := cancelTime.Sub(driverMatchedTime) >= 5*time.Minute
		distanceCondition := f.Order.CancelDetail.CancellationMetadata.GetActualDistanceBetweenDriverMatchedAndCancelled() >= 500.0
		if timeRangeCondition &&
			distanceCondition {
			return nil
		}

		logx.Info().
			Context(ctx).
			Str("form_type", "rider_compensation").
			Str("order_id", fp.GetValueOrDefault(f.Order, model.Order{}).OrderID).
			Str("driver_id", fp.GetValueOrDefault(f.Driver, model.Driver{}).DriverID).
			Str("validation_step", "validateBikeOrderDistanceAndDisplacement").
			Str("validation_condition", "DRIVE_TO_0").
			Bool("time_range_condition", timeRangeCondition).
			Bool("distance_condition", distanceCondition).
			Msg("Order failed the validation")
		return api.NewAPIError(api.ErrCodeInvalidRequest, ErrOrderNotMeetCriteria.Error())
	case model.NewArrivedAtOrderStatus(0):
		arrivedAtTime := f.Order.History[model.NewArrivedAtOrderStatus(0).String()]

		timeRangeCondition := cancelTime.Sub(arrivedAtTime) >= 5*time.Minute
		distanceCondition := f.Order.CancelDetail.CancellationMetadata.GetDisplacementBetweenArrivedAtAndCancelled() <= 300

		// Check if the cancel reason is in the list that should wait longer than 10 mins
		if f.formServiceConfig.Get().BikeCancelReasonThatShouldWaitLongerThan10Mins.Has(f.Order.CancelDetail.Reason) {
			timeRangeCondition = cancelTime.Sub(arrivedAtTime) >= 10*time.Minute
		}

		if timeRangeCondition &&
			distanceCondition {
			return nil
		}

		logx.Info().
			Context(ctx).
			Str("form_type", "rider_compensation").
			Str("order_id", fp.GetValueOrDefault(f.Order, model.Order{}).OrderID).
			Str("driver_id", fp.GetValueOrDefault(f.Driver, model.Driver{}).DriverID).
			Str("validation_step", "validateBikeOrderDistanceAndDisplacement").
			Str("validation_condition", "ARRIVED_AT_0").
			Bool("time_range_condition", timeRangeCondition).
			Bool("distance_condition", distanceCondition).
			Msg("Order failed the validation")
		return api.NewAPIError(api.ErrCodeInvalidRequest, ErrOrderNotMeetCriteria.Error())
	}
	return nil
}

func (f *claimFormRiderCompensation) getExpectedHistoryStatusForRiderCompensation(s model.Service) []string {
	switch s {
	case model.ServiceFood, model.ServiceMart:
		return []string{string(model.StatusDriverArrivedRestaurant)}
	case model.ServiceMessenger:
		return []string{"ARRIVED_AT"}
	case model.ServiceBike:
		return []string{}
	default:
		return []string{string(model.StatusDriverArrivedRestaurant)}
	}
}

func getExpectedHistoryStatus(s model.Service) []string {
	switch s {
	case model.ServiceFood, model.ServiceMart:
		return []string{string(model.StatusDriverArrivedRestaurant)}
	case model.ServiceBike, model.ServiceMessenger:
		return []string{"ARRIVED_AT"}
	default:
		return []string{string(model.StatusDriverArrivedRestaurant)}
	}
}

func (f *claimFormRiderCompensation) Get(ctx context.Context) (model.FormService, error) {
	prefill := make(map[string]interface{})

	prefill[model.FormPrefillDriverId] = f.Driver.DriverID              // Driver ID
	prefill[model.FormPrefillFirstName] = f.Driver.Firstname.String()   // Driver's First Name
	prefill[model.FormPrefillLastName] = f.Driver.Lastname.String()     // Driver's Last Name
	prefill[model.FormPrefillOrderId] = f.Order.OrderID                 // Order ID
	prefill[model.FormPrefillTripId] = f.Order.TripID                   // Trip ID
	prefill[model.FormPrefillOrderStatus] = string(f.Order.Status)      // Order Status
	prefill[model.FormPrefillWorkingRegion] = f.Driver.Region.String()  // Working Region
	prefill[model.FormPrefillServiceType] = string(f.Order.ServiceType) // Service Type
	prefill[model.FormPrefillDriverType] = f.Driver.DriverType.String() // Driver Type
	prefill[model.FormPrefillDriverVendorID] = f.Driver.DriverVendorID  // Driver Vendor ID
	prefill[model.FormPrefillPaymentMethod] = string(f.Order.PriceSummary().DeliveryFee.PaymentMethod)
	if len(f.Order.Routes) > 1 {
		prefill[model.FormPrefillFoodCompensationAmount] = f.Order.Routes[1].PriceSummary.ItemFee.SubTotal
	}
	if f.Trip != nil {
		prefill[model.FormPrefillDriverWage] = f.Trip.DriverWageSummary.TotalDriverWage.Float64()
	}
	prefill[model.FormPrefillDriverMoneyFlow] = string(f.Order.Options.DriverMoneyFlow)
	f.prefillDriversCancelReasonImageUrlsIfPossible(ctx, f.vosService, f.Order, prefill)
	f.addCalculateDistanceToPrefill(ctx, prefill)
	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

func (f *claimFormRiderCompensation) addCalculateDistanceToPrefill(ctx context.Context, srcPrefill map[string]interface{}) map[string]interface{} {
	if f.mapService == nil || srcPrefill == nil {
		return srcPrefill
	}

	formDistance := NewFormInfo(f.mapService, f.Order)
	if f.Order.ServiceType == model.ServiceFood || f.Order.ServiceType == model.ServiceMart {
		latestStatus := GetOrderLatestStatus(f.Order, []string{
			string(model.StatusDriverArrivedRestaurant),
			string(model.StatusDriverArrived),
		})

		switch latestStatus {
		case string(model.StatusDriverArrivedRestaurant):
			d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			d1.AddToPrefill(srcPrefill)
			d2 := formDistance.CalculateDistance(ctx, string(model.StatusCanceled), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			d2.AddToPrefill(srcPrefill)
			srcPrefill[fmt.Sprintf("MIN-%v-%v", d1.Key, d2.Key)] = d1.Min(d2)
			r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			r1.AddToPrefill(srcPrefill)
			r2 := formDistance.CalculateRadius(ctx, string(model.StatusCanceled), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			r2.AddToPrefill(srcPrefill)
			srcPrefill[fmt.Sprintf("MIN-%v-%v", r1.Key, r2.Key)] = r1.Min(r2)

		case string(model.StatusDriverArrived):
			d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrived), statusUserLocation, FormDistancCheckingAtMostBuilder(300.0))
			d1.AddToPrefill(srcPrefill)
			r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrived), statusUserLocation, FormDistancCheckingAtMostBuilder(300.0))
			r1.AddToPrefill(srcPrefill)
			t1 := formDistance.CalculateTimeInMinute(ctx, string(model.StatusDriverArrived), string(model.StatusCanceled), FormDistancCheckingAtLeastBuilder(10.0))
			t1.AddToPrefill(srcPrefill)
		}
	} else if f.Order.ServiceType == model.ServiceMessenger {
		d1 := formDistance.CalculateDistance(ctx, "ARRIVED_AT_0", string(model.StatusCanceled), FormDistancCheckingAtMostBuilder(150.0))
		d1.AddToPrefill(srcPrefill)
		d2 := formDistance.CalculateDistance(ctx, "ARRIVED_AT_0", statusUserLocation, FormDistancCheckingAtMostBuilder(150.0))
		d2.AddToPrefill(srcPrefill)
		srcPrefill[fmt.Sprintf("MIN-%v-%v", d1.Key, d2.Key)] = d1.Min(d2)
		r1 := formDistance.CalculateRadius(ctx, "ARRIVED_AT_0", string(model.StatusCanceled), FormDistancCheckingAtMostBuilder(150.0))
		r1.AddToPrefill(srcPrefill)
		r2 := formDistance.CalculateRadius(ctx, "ARRIVED_AT_0", statusUserLocation, FormDistancCheckingAtMostBuilder(150.0))
		r2.AddToPrefill(srcPrefill)
		srcPrefill[fmt.Sprintf("MIN-%v-%v", r1.Key, r2.Key)] = r1.Min(r2)
	} else if f.Order.ServiceType == model.ServiceBike {
		d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverMatched), string(model.StatusCanceled), FormDistancCheckingAtLeastBuilder(500.0))
		d1.AddToPrefill(srcPrefill)
		d2 := formDistance.CalculateDistance(ctx, "ARRIVED_AT_0", statusUserLocation, FormDistancCheckingAtLeastBuilder(300.0))
		d2.AddToPrefill(srcPrefill)
		srcPrefill[fmt.Sprintf("MIN-%v-%v", d1.Key, d2.Key)] = d1.Min(d2)
		t1 := formDistance.CalculateTimeInMinute(ctx, string(model.StatusDriverMatched), string(model.StatusCanceled), FormDistancCheckingAtLeastBuilder(5.0))
		t1.AddToPrefill(srcPrefill)
		t2 := formDistance.CalculateTimeInMinute(ctx, "ARRIVED_AT_0", string(model.StatusCanceled), FormDistancCheckingAtLeastBuilder(10.0))
		t2.AddToPrefill(srcPrefill)
		r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverMatched), string(model.StatusCanceled), FormDistancCheckingAtLeastBuilder(500.0))
		r1.AddToPrefill(srcPrefill)
		r2 := formDistance.CalculateRadius(ctx, "ARRIVED_AT_0", statusUserLocation, FormDistancCheckingAtLeastBuilder(300.0))
		r2.AddToPrefill(srcPrefill)
		srcPrefill[fmt.Sprintf("MIN-%v-%v", r1.Key, r2.Key)] = r1.Min(r2)
	}

	return srcPrefill
}

type claimFormFairDispute struct {
	baseDriverClaimForm
	Type       model.FormType
	Subtype    model.FormSubtype
	Driver     *model.Driver
	Order      *model.Order
	vosService VOSService
}

func NewFairDisputeClaimForm(driver *model.Driver, order *model.Order, vosService VOSService) model.FormBuilder {
	return &claimFormFairDispute{
		Type:       model.FormTypeDriversClaim,
		Subtype:    model.FormSubtypeFairDisputeClaim,
		Driver:     driver,
		Order:      order,
		vosService: vosService,
	}
}

func (f *claimFormFairDispute) Validate(_ context.Context) (bool, error) {
	if !stringutil.IsStringInList([]string{model.ServiceFood.String(), model.ServiceMart.String(),
		model.ServiceMessenger.String(), model.ServiceBike.String()}, f.Order.ServiceType.String()) {
		return false, ErrInvalidServiceTypeToClaim
	}

	statusHistory := getExpectedHistoryStatus(f.Order.ServiceType)

	err := f.validateOrderToCreateClaimForm(
		f.Order,
		[]string{string(model.StatusCompleted)},
		statusHistory,
		[]string{},
		f.Subtype,
		config.FormConfig{},
	)
	return err == nil, err
}

func (f *claimFormFairDispute) Get(ctx context.Context) (model.FormService, error) {
	prefill := make(map[string]interface{})

	distanceInKmStr := fmt.Sprintf("%.2f", (f.Order.Distance / types.KM).Float64())
	prefill[model.FormPrefillDriverId] = f.Driver.DriverID                // Driver ID
	prefill[model.FormPrefillFirstName] = f.Driver.Firstname.String()     // Driver's First Name
	prefill[model.FormPrefillLastName] = f.Driver.Lastname.String()       // Driver's Last Name
	prefill[model.FormPrefillOrderId] = f.Order.OrderID                   // Order ID
	prefill[model.FormPrefillTripId] = f.Order.TripID                     // Trip ID
	prefill[model.FormPrefillOrderStatus] = string(f.Order.Status)        // Order Status
	prefill[model.FormPrefillWorkingRegion] = f.Driver.Region.String()    // Working Region
	prefill[model.FormPrefillOrderDistanceFromRiderApp] = distanceInKmStr // Distance Calculated from LINEMAN app (in KM)v
	prefill[model.FormPrefillServiceType] = string(f.Order.ServiceType)   // Service Type
	prefill[model.FormPrefillDriverType] = f.Driver.DriverType.String()   // Driver Type
	prefill[model.FormPrefillDriverVendorID] = f.Driver.DriverVendorID    // Driver Vendor ID
	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

type claimFormCreditTopup struct {
	Type    model.FormType
	Subtype model.FormSubtype
	Driver  *model.Driver
}

func NewCreditTopupClaimForm(driver *model.Driver) model.FormBuilder {
	return &claimFormCreditTopup{
		Type:    model.FormTypeDriversClaim,
		Subtype: model.FormSubtypeCreditTopupClaim,
		Driver:  driver,
	}
}

func (f *claimFormCreditTopup) Validate(_ context.Context) (bool, error) {
	return true, nil
}

func (f *claimFormCreditTopup) Get(ctx context.Context) (model.FormService, error) {
	prefill := make(map[string]interface{})

	prefill[model.FormPrefillDriverId] = f.Driver.DriverID                 // Driver ID
	prefill[model.FormPrefillFirstName] = f.Driver.Firstname.String()      // Driver's First Name
	prefill[model.FormPrefillLastName] = f.Driver.Lastname.String()        // Driver's Last Name
	prefill[model.FormPrefillSlipDetailName] = f.Driver.Firstname.String() // Name in Slip
	prefill[model.FormPrefillWorkingRegion] = f.Driver.Region.String()     // Working Region
	prefill[model.FormPrefillDriverType] = f.Driver.DriverType.String()    // Driver Type
	prefill[model.FormPrefillDriverVendorID] = f.Driver.DriverVendorID     // Driver Vendor ID

	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

type claimFormCovid struct {
	Type    model.FormType
	Subtype model.FormSubtype
	Driver  *model.Driver
	Order   *model.Order
}

func NewCovidClaimForm(driver *model.Driver, order *model.Order) model.FormBuilder {
	return &claimFormCovid{
		Type:    model.FormTypeDriversClaim,
		Subtype: model.FormSubtypeCovidClaim,
		Driver:  driver,
		Order:   order,
	}
}

func (f *claimFormCovid) Validate(_ context.Context) (bool, error) {
	return true, nil
}

func (f *claimFormCovid) Get(ctx context.Context) (model.FormService, error) {
	prefill := make(map[string]interface{})

	prefill[model.FormPrefillDriverId] = f.Driver.DriverID              // Driver ID
	prefill[model.FormPrefillFirstName] = f.Driver.Firstname.String()   // Driver's First Name
	prefill[model.FormPrefillLastName] = f.Driver.Lastname.String()     // Driver's Last Name
	prefill[model.FormPrefillPhoneNumber] = f.Driver.Phone.String()     // Driver's Phone Number
	prefill[model.FormPrefillWorkingRegion] = f.Driver.Region.String()  // Working Region
	prefill[model.FormPrefillDriverType] = f.Driver.DriverType.String() // Driver Type
	prefill[model.FormPrefillDriverVendorID] = f.Driver.DriverVendorID  // Driver Vendor ID

	if f.Order != nil {
		prefill[model.FormPrefillOrderId] = f.Order.OrderID                 // Order ID
		prefill[model.FormPrefillTripId] = f.Order.TripID                   // Trip ID
		prefill[model.FormPrefillOrderStatus] = string(f.Order.Status)      // Order Status
		prefill[model.FormPrefillServiceType] = string(f.Order.ServiceType) // Service Type
	}

	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

type claimFormParking struct {
	baseDriverClaimForm
	Type    model.FormType
	Subtype model.FormSubtype
	Driver  *model.Driver
	Order   *model.Order
}

func NewParkingClaimForm(driver *model.Driver, order *model.Order) model.FormBuilder {
	return &claimFormParking{
		Type:    model.FormTypeDriversClaim,
		Subtype: model.FormSubtypeParkingClaim,
		Driver:  driver,
		Order:   order,
	}
}

func (f *claimFormParking) Validate(_ context.Context) (bool, error) {
	if f.Order.Region != "BKK" {
		return false, ErrInvalidOrderRegionToClaim
	}
	if !stringutil.IsStringInList([]string{model.ServiceFood.String()},
		f.Order.ServiceType.String()) {
		return false, ErrInvalidServiceTypeToClaim
	}

	err := f.validateOrderToCreateClaimForm(
		f.Order,
		[]string{string(model.StatusCompleted)},
		[]string{string(model.StatusDriverArrivedRestaurant)},
		[]string{string(model.ServiceFood)},
		f.Subtype,
		config.FormConfig{},
	)
	return err == nil, err
}

func (f *claimFormParking) Get(ctx context.Context) (model.FormService, error) {
	prefill := make(map[string]interface{})

	prefill[model.FormPrefillDriverId] = f.Driver.DriverID              // Driver ID
	prefill[model.FormPrefillFirstName] = f.Driver.Firstname.String()   // Driver's First Name
	prefill[model.FormPrefillLastName] = f.Driver.Lastname.String()     // Driver's Last Name
	prefill[model.FormPrefillWorkingRegion] = f.Order.Region.String()   // Working Region
	prefill[model.FormPrefillOrderId] = f.Order.OrderID                 // Order ID
	prefill[model.FormPrefillTripId] = f.Order.TripID                   // Trip ID
	prefill[model.FormPrefillOrderStatus] = string(f.Order.Status)      // Order Status
	prefill[model.FormPrefillServiceType] = string(f.Order.ServiceType) // Service Type
	prefill[model.FormPrefillDriverType] = f.Driver.DriverType.String() // Driver Type
	prefill[model.FormPrefillDriverVendorID] = f.Driver.DriverVendorID  // Driver Vendor ID
	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

type claimFormAccident struct {
	Type             model.FormType
	Subtype          model.FormSubtype
	Driver           *model.Driver
	Order            *model.Order
	PrivacyPolicyURL string
}

func NewAccidentClaimForm(driver *model.Driver, order *model.Order, privacyPolicyURL string) model.FormBuilder {
	return &claimFormAccident{
		Type:             model.FormTypeDriversClaim,
		Subtype:          model.FormSubtypeAccidentClaim,
		Driver:           driver,
		Order:            order,
		PrivacyPolicyURL: privacyPolicyURL,
	}
}

func (f *claimFormAccident) Validate(_ context.Context) (bool, error) {
	return true, nil
}

func (f *claimFormAccident) Get(ctx context.Context) (model.FormService, error) {
	prefill := make(map[string]interface{})

	prefill[model.FormPrefillDriverId] = f.Driver.DriverID             // Driver ID
	prefill[model.FormPrefillFirstName] = f.Driver.Firstname.String()  // Driver's First Name
	prefill[model.FormPrefillLastName] = f.Driver.Lastname.String()    // Driver's Last Name
	prefill[model.FormPrefillWorkingRegion] = f.Driver.Region.String() // Working Region
	prefill[model.FormPrefillPrivacyPolicyURL] = f.PrivacyPolicyURL
	prefill[model.FormPrefillDriverType] = f.Driver.DriverType.String() // Driver Type
	prefill[model.FormPrefillDriverVendorID] = f.Driver.DriverVendorID  // Driver Vendor ID

	if f.Order != nil {
		prefill[model.FormPrefillOrderId] = f.Order.OrderID                 // Order ID
		prefill[model.FormPrefillTripId] = f.Order.TripID                   // Trip ID
		prefill[model.FormPrefillOrderStatus] = string(f.Order.Status)      // Order Status
		prefill[model.FormPrefillServiceType] = string(f.Order.ServiceType) // Service Type
	}

	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

type claimFormLongWaitingTimeCompensation struct {
	baseDriverClaimForm
	Type       model.FormType
	Subtype    model.FormSubtype
	Driver     *model.Driver
	Order      *model.Order
	mapService mapservice.MapService
}

func NewLongWaitingTimeCompensationClaimForm(driver *model.Driver, order *model.Order, mapService mapservice.MapService) model.FormBuilder {
	return &claimFormLongWaitingTimeCompensation{
		Type:       model.FormTypeDriversClaim,
		Subtype:    model.FormSubtypeLongWaitingTimeCompensation,
		Driver:     driver,
		Order:      order,
		mapService: mapService,
	}
}

func (f *claimFormLongWaitingTimeCompensation) Validate(_ context.Context) (bool, error) {
	if !stringutil.IsStringInList([]string{model.ServiceFood.String(), model.ServiceMart.String()},
		f.Order.ServiceType.String()) {
		return false, ErrInvalidServiceTypeToClaim
	}
	err := f.validateOrderToCreateClaimForm(
		f.Order,
		[]string{string(model.StatusCanceled), string(model.StatusCompleted)},
		[]string{string(model.StatusDriverArrivedRestaurant)},
		[]string{string(model.ServiceFood), string(model.ServiceMart)},
		f.Subtype,
		config.FormConfig{},
	)
	return err == nil, err
}

func (f *claimFormLongWaitingTimeCompensation) Get(ctx context.Context) (model.FormService, error) {
	prefill := make(map[string]interface{})

	prefill[model.FormPrefillDriverId] = f.Driver.DriverID              // Driver ID
	prefill[model.FormPrefillFirstName] = f.Driver.Firstname.String()   // Driver's First Name
	prefill[model.FormPrefillLastName] = f.Driver.Lastname.String()     // Driver's Last Name
	prefill[model.FormPrefillWorkingRegion] = f.Driver.Region.String()  // Working Region
	prefill[model.FormPrefillOrderId] = f.Order.OrderID                 // Order ID
	prefill[model.FormPrefillTripId] = f.Order.TripID                   // Trip ID
	prefill[model.FormPrefillOrderStatus] = string(f.Order.Status)      // Order Status
	prefill[model.FormPrefillServiceType] = string(f.Order.ServiceType) // Service Type
	prefill[model.FormPrefillDriverType] = f.Driver.DriverType.String() // Driver Type
	prefill[model.FormPrefillDriverVendorID] = f.Driver.DriverVendorID  // Driver Vendor ID
	f.addCalculateDistanceToPrefill(ctx, prefill)
	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

func (f *claimFormLongWaitingTimeCompensation) addCalculateDistanceToPrefill(ctx context.Context, srcPrefill map[string]interface{}) map[string]interface{} {
	if f.mapService == nil || srcPrefill == nil {
		return srcPrefill
	}
	formDistance := NewFormInfo(f.mapService, f.Order)

	switch f.Order.Status {
	case model.StatusCanceled:
		{
			latestStatus := GetOrderLatestStatus(f.Order, []string{
				string(model.StatusDriverArrivedRestaurant),
				string(model.StatusDriverArrived),
				string(model.StatusDriverToDestination),
			})

			switch model.Status(latestStatus) {
			case model.StatusDriverToDestination:
				d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverToDestination), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				d1.AddToPrefill(srcPrefill)
				d2 := formDistance.CalculateDistance(ctx, string(model.StatusCanceled), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				d2.AddToPrefill(srcPrefill)
				d3 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				d3.AddToPrefill(srcPrefill)
				srcPrefill[fmt.Sprintf("MIN-%v-%v", d1.Key, d2.Key)] = d1.Min(d2)
				t1 := formDistance.CalculateTimeInMinute(ctx, string(model.StatusDriverArrivedRestaurant), string(model.StatusDriverToDestination), FormDistancCheckingTimeStepBuilder())
				t1.AddToPrefill(srcPrefill)
				r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverToDestination), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				r1.AddToPrefill(srcPrefill)
				r2 := formDistance.CalculateRadius(ctx, string(model.StatusCanceled), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				r2.AddToPrefill(srcPrefill)
				r3 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				r3.AddToPrefill(srcPrefill)
				srcPrefill[fmt.Sprintf("MIN-%v-%v", r1.Key, r2.Key)] = r1.Min(r2)

			case model.StatusDriverArrivedRestaurant:
				d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				d1.AddToPrefill(srcPrefill)
				d2 := formDistance.CalculateDistance(ctx, string(model.StatusCanceled), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				d2.AddToPrefill(srcPrefill)
				srcPrefill[fmt.Sprintf("MIN-%v-%v", d1.Key, d2.Key)] = d1.Min(d2)
				t1 := formDistance.CalculateTimeInMinute(ctx, string(model.StatusDriverArrivedRestaurant), string(model.StatusCanceled), FormDistancCheckingTimeStepBuilder())
				t1.AddToPrefill(srcPrefill)
				r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				r1.AddToPrefill(srcPrefill)
				r2 := formDistance.CalculateRadius(ctx, string(model.StatusCanceled), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				r2.AddToPrefill(srcPrefill)
				srcPrefill[fmt.Sprintf("MIN-%v-%v", r1.Key, r2.Key)] = r1.Min(r2)

			case model.StatusDriverArrived:
				d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrived), statusUserLocation, FormDistancCheckingAtMostBuilder(300.0))
				d1.AddToPrefill(srcPrefill)
				d2 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				d2.AddToPrefill(srcPrefill)
				d3 := formDistance.CalculateDistance(ctx, string(model.StatusDriverToDestination), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				d3.AddToPrefill(srcPrefill)
				t1 := formDistance.CalculateTimeInMinute(ctx, string(model.StatusDriverArrivedRestaurant), string(model.StatusDriverToDestination), FormDistancCheckingTimeStepBuilder())
				t1.AddToPrefill(srcPrefill)
				r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrived), statusUserLocation, FormDistancCheckingAtMostBuilder(300.0))
				r1.AddToPrefill(srcPrefill)
				r2 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				r2.AddToPrefill(srcPrefill)
				r3 := formDistance.CalculateRadius(ctx, string(model.StatusDriverToDestination), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
				r3.AddToPrefill(srcPrefill)
			}
		}

	case model.StatusCompleted:
		{
			d1 := formDistance.CalculateDistance(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			d1.AddToPrefill(srcPrefill)
			t1 := formDistance.CalculateTimeInMinute(ctx, string(model.StatusDriverArrivedRestaurant), string(model.StatusDriverToDestination), FormDistancCheckingTimeStepBuilder())
			t1.AddToPrefill(srcPrefill)
			d2 := formDistance.CalculateDistance(ctx, string(model.StatusDriverToDestination), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			d2.AddToPrefill(srcPrefill)
			d3 := formDistance.CalculateDistance(ctx, string(model.StatusCompleted), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			d3.AddToPrefill(srcPrefill)
			srcPrefill[fmt.Sprintf("MIN-%v-%v", d2.Key, d3.Key)] = d2.Min(d3)
			r1 := formDistance.CalculateRadius(ctx, string(model.StatusDriverArrivedRestaurant), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			r1.AddToPrefill(srcPrefill)
			r2 := formDistance.CalculateRadius(ctx, string(model.StatusDriverToDestination), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			r2.AddToPrefill(srcPrefill)
			r3 := formDistance.CalculateRadius(ctx, string(model.StatusCompleted), statusRestaurantLocation, FormDistancCheckingAtMostBuilder(150.0))
			r3.AddToPrefill(srcPrefill)
			srcPrefill[fmt.Sprintf("MIN-%v-%v", r2.Key, r3.Key)] = r2.Min(r3)
		}
	}

	return srcPrefill
}

// GetOrderLatestStatus - get latest order status from history by giving status, return empty string if no status in the order's history
func GetOrderLatestStatus(ord *model.Order, checkingStatus []string) string {
	type TimeWithStatus struct {
		Datetime time.Time
		Status   string
	}

	if len(checkingStatus) == 0 {
		return ""
	}
	if ord == nil || len(ord.History) == 0 {
		return ""
	}

	timeWithStatus := make([]TimeWithStatus, 0)
	for status, datetime := range ord.History {
		timeWithStatus = append(timeWithStatus, TimeWithStatus{
			Status:   status,
			Datetime: datetime,
		})
	}

	sort.Slice(timeWithStatus, func(i, j int) bool {
		return timeWithStatus[i].Datetime.After(timeWithStatus[j].Datetime)
	})

	for _, item := range timeWithStatus {
		if stringutil.IsStringInList(checkingStatus, item.Status) {
			return item.Status
		}
	}
	return ""
}

type claimFormFairDisputePickup struct {
	baseDriverClaimForm
	Type        model.FormType
	Subtype     model.FormSubtype
	Driver      *model.Driver
	Order       *model.Order
	Trip        *model.Trip
	vosService  VOSService
	orderRepo   repository.OrderRepository
	tripService TripServices
}

func NewFairDisputePickupClaimForm(
	driver *model.Driver,
	order *model.Order,
	trip *model.Trip,
	vosService VOSService,
	orderRepo repository.OrderRepository,
	tripService TripServices) model.FormBuilder {
	return &claimFormFairDisputePickup{
		Type:        model.FormTypeDriversClaim,
		Subtype:     model.FormSubtypeFareDisputePickupClaim,
		Driver:      driver,
		Order:       order,
		Trip:        trip,
		vosService:  vosService,
		orderRepo:   orderRepo,
		tripService: tripService,
	}
}

func (f *claimFormFairDisputePickup) Validate(ctx context.Context) (bool, error) {
	if !stringutil.IsStringInList([]string{model.ServiceFood.String(), model.ServiceMart.String(),
		model.ServiceMessenger.String(), model.ServiceBike.String()}, f.Order.ServiceType.String()) {
		return false, ErrInvalidServiceTypeToClaim
	}

	statusHistory := getExpectedHistoryStatus(f.Order.ServiceType)

	err := f.validateOrderToCreateClaimForm(
		f.Order,
		[]string{string(model.StatusCompleted)},
		statusHistory,
		[]string{},
		f.Subtype,
		config.FormConfig{},
	)
	if err != nil {
		return false, err
	}

	isFirstPickupOrder, cErr := f.isFirstPickupOrder(ctx, f.Order)
	if cErr != nil {
		logx.Error().Context(ctx).Err(cErr).
			Str("method", "claimFormFairDisputePickup.Validate").
			Msg("unable to check a first pickup order")
		return false, cErr
	}

	if !isFirstPickupOrder {
		return false, errors.New("order is not a first to pickup order")
	}

	return true, nil
}

func (f *claimFormFairDisputePickup) Get(ctx context.Context) (model.FormService, error) {
	beforePickupLocation := f.getDriverBeforePickupLocationForOnTopCase(ctx, f.Order)
	pickupLocation := getPickupLocation(f.Order)
	prefill := map[string]interface{}{
		model.FormPrefillDriverId:                  f.Driver.DriverID,
		model.FormPrefillFirstName:                 f.Driver.Firstname.String(),
		model.FormPrefillLastName:                  f.Driver.Lastname.String(),
		model.FormPrefillOrderId:                   f.Order.OrderID,
		model.FormPrefillTripId:                    f.Order.TripID,
		model.FormPrefillOrderStatus:               string(f.Order.Status),
		model.FormPrefillWorkingRegion:             f.Driver.Region.String(),
		model.FormPrefillServiceType:               string(f.Order.ServiceType),
		model.FormPrefillDriverType:                f.Driver.DriverType.String(), // Driver Type
		model.FormPrefillDriverVendorID:            f.Driver.DriverVendorID,      // Driver Vendor ID
		model.FormPrefillOrderDistanceFromRiderApp: getDistanceInKmStr(f.Order),
		model.FormPrefillPickupLocation: map[string]interface{}{
			"lat": pickupLocation.Lat,
			"lng": pickupLocation.Lng,
		},
		// LMF-12201 - a value `driverMatchedLocation` might be removed later
		model.FormPrefillDriverMatchedLocation: map[string]interface{}{
			"lat": beforePickupLocation.Lat,
			"lng": beforePickupLocation.Lng,
		},
		model.FormPrefillBeforePickupLocation: map[string]interface{}{
			"lat": beforePickupLocation.Lat,
			"lng": beforePickupLocation.Lng,
		},
		model.FormPrefillGoogleDirectionClaimDistanceURL: getGoogleDirectionURL(beforePickupLocation, pickupLocation),
	}
	return model.FormService{
		FormType:    f.Type,
		FormSubtype: f.Subtype,
		Prefill:     prefill,
	}, nil
}

func getDistanceInKmStr(order *model.Order) string {
	distance := types.Distance(0)
	if len(order.Routes) > 0 {
		distance = order.Routes[0].Distance
	}
	return fmt.Sprintf("%.2f", (distance / types.KM).Float64())
}

func getGoogleDirectionURL(from model.Location, to model.Location) string {
	return fmt.Sprintf(googleDirectionURL, from.Lat, from.Lng, to.Lat, to.Lng)
}

func getDriverMatchedLocation(order *model.Order) model.Location {
	location, ok := order.HistoryLocation[model.StatusDriverMatched.String()]
	if !ok {
		logx.Warn().Str("method", "getDriverMatchedLocation").Str("driver_id", order.Driver).Msgf("claim form. driver: %s, order id: %s. order status history don't contain the DRIVER_MATCHED", order.Driver, order.OrderID)
		fallbackLocation, ok := order.HistoryLocation[model.StatusDriverToRestaurant.String()]
		if !ok {
			logx.Error().Str("method", "getDriverMatchedLocation").Str("driver_id", order.Driver).Msgf("claim form. driver: %s, order id: %s. order status history don't contain the DRIVER_TO_RESTAURANT", order.Driver, order.OrderID)
			return model.Location{}
		}
		return model.Location{
			Lat: fallbackLocation.Lat,
			Lng: fallbackLocation.Lng,
		}
	}
	return model.Location{
		Lat: location.Lat,
		Lng: location.Lng,
	}
}

func getDriverCancelledLocation(order *model.Order) model.Location {
	location, ok := order.HistoryLocation[model.StatusCanceled.String()]
	if !ok {
		logx.Error().Str("method", "getDriverCancelledLocation").Str("driver_id", order.Driver).Msgf("claim form. driver: %s, order id: %s. order status history don't contain the CANCELLED", order.Driver, order.OrderID)
		return model.Location{}
	}
	return model.Location{
		Lat: location.Lat,
		Lng: location.Lng,
	}
}

func (f *claimFormFairDisputePickup) isFirstPickupOrder(ctx context.Context, srcOrder *model.Order) (bool, error) {
	if srcOrder == nil {
		logx.Error().Context(ctx).
			Str("method", "claimFormFairDisputePickup").
			Msg("unable to get before pickup location, order is nil")
		return false, errors.New("order is null")
	}

	if f.Trip == nil {
		if f.tripService == nil {
			return false, errors.New("unable to get a trip, trip service is null")
		}

		trip, err := f.tripService.GetTripByID(ctx, srcOrder.TripID, repository.WithReadSecondaryPreferred)
		if err != nil {
			logx.Error().Context(ctx).Err(err).
				Str("order_id", srcOrder.OrderID).
				Str("method", "claimFormFairDisputePickup").
				Msg("unable to get a trip")
			return false, err
		}

		f.Trip = &trip
	}

	if f.Trip.GetFirstPickupOrderID() != srcOrder.OrderID {
		return false, errors.New("order is not a first to pickup order")
	}
	return true, nil
}

func (f *claimFormFairDisputePickup) getDriverBeforePickupLocationForOnTopCase(ctx context.Context, srcOrder *model.Order) model.Location {
	isFirstPickupOrder, err := f.isFirstPickupOrder(ctx, srcOrder)
	if err != nil {
		logx.Error().Context(ctx).Err(err).
			Str("order_id", srcOrder.OrderID).
			Str("method", "claimFormFairDisputePickup").
			Msg("unable to get before pickup location, order is nil")
		return model.Location{}
	}

	// On-top pick-up distance only apply on the first-pick-up order
	if !isFirstPickupOrder {
		logx.Error().Context(ctx).
			Str("driver_id", srcOrder.Driver).
			Str("order_id", srcOrder.OrderID).
			Str("trip_id", srcOrder.TripID).
			Str("method", "claimFormFairDisputePickup").
			Msg("the claim order is not a first-to-pickup order in the trip")
		return model.Location{}
	}

	if !f.Trip.CreatedFromB2BOrder {
		// just a normal Trip, not created from a B2B order
		if len(f.Trip.Orders) > 0 && f.Trip.Orders[0].OrderID != srcOrder.OrderID {
			// there is a previous order which is cancelled
			return f.findPreviousLocationForCancelledOrder(ctx, f.Trip.Orders[0].OrderID, srcOrder.Driver)
		}
		return getDriverMatchedLocation(srcOrder)
	}

	// Handle B2B to MO Case
	// [P1D1][P2D2] --> [P1D2][P3P2D2D3]

	// list all ACTIVE and INACTIVE orders
	// the INACTIVE orders will find from `order_revisions` collection
	tripOrders, err := f.tripService.GetOrdersByTrip(ctx, f.Trip, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().Context(ctx).Err(err).
			Str("driver_id", srcOrder.Driver).
			Str("order_id", srcOrder.OrderID).
			Str("trip_id", srcOrder.TripID).
			Str("method", "claimFormFairDisputePickup").
			Msg("unable to get orders from the trip")
		return model.Location{}
	}

	// Find a previous order ID, only from a "B2B" order
	var previousOrderIDB2B string
	for _, order := range tripOrders {
		if order.IsB2B {
			previousOrderIDB2B = order.QueuedAfterOrder
			break
		}
	}

	if previousOrderIDB2B == "" {
		logx.Error().Context(ctx).
			Str("driver_id", srcOrder.Driver).
			Str("trip_id", srcOrder.TripID).
			Str("method", "claimFormFairDisputePickup").
			Msg("the trip is created from a B2B order but can't find a previous order")
		return model.Location{}
	}

	// In case that the previous order is CANCALLED or reassigend, find the order from a `order_revisions` collection
	previousOrder, err := f.orderRepo.GetRevisionByOrderIdAndDriverId(ctx, previousOrderIDB2B, srcOrder.Driver, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().Context(ctx).Err(err).
			Str("driver_id", srcOrder.Driver).
			Str("order_id", srcOrder.OrderID).
			Str("method", "claimFormFairDisputePickup").
			Msgf("unable to get previous order before B2B: [%s]", previousOrderIDB2B)
		return model.Location{}
	}

	if previousOrder == nil {
		logx.Error().Context(ctx).Err(err).
			Str("driver_id", srcOrder.Driver).
			Str("order_id", srcOrder.OrderID).
			Str("method", "claimFormFairDisputePickup").
			Msgf("previous order before B2B is nil: [%s]", previousOrderIDB2B)
		return model.Location{}
	}

	previousTrip, err := f.tripService.GetTripByID(ctx, previousOrder.TripID, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().Context(ctx).Err(err).
			Str("driver_id", srcOrder.Driver).
			Str("order_id", srcOrder.OrderID).
			Str("trip_id", previousOrder.TripID).
			Str("method", "claimFormFairDisputePickup").
			Msgf("unable to get trip from a previous order: [%s]", previousOrderIDB2B)
		return model.Location{}
	}

	// if a trip status is cancelled, all order in the trip is cancelled
	if previousTrip.Status == model.TripStatusCanceled {
		return getDriverMatchedLocation(srcOrder)
	}

	// didn't use previousTrip.GetLastActiveDropOffRoute() because the order is not active (already completed)
	var lastDropOffRoute *model.TripRoute
	for i := len(previousTrip.Routes) - 1; i >= 0; i-- {
		if previousTrip.Routes[i].Action == model.TripActionDropOff {
			lastDropOffRoute = &previousTrip.Routes[i]
			break
		}
	}

	if lastDropOffRoute == nil {
		logx.Error().Context(ctx).Err(err).
			Str("driver_id", srcOrder.Driver).
			Str("order_id", srcOrder.OrderID).
			Str("trip_id", previousOrder.TripID).
			Str("method", "claimFormFairDisputePickup").
			Msgf("unable to get last drop off route for a trip: [%s]", previousTrip.TripID)
		return model.Location{}
	}

	// If the claim order is a B2B order (or B2B trip), get a Drop-off location from a previous order
	return lastDropOffRoute.Location
}

func (f *claimFormFairDisputePickup) findPreviousLocationForCancelledOrder(ctx context.Context, orderID string, driverID string) model.Location {
	cancelledOrder, err := f.orderRepo.GetRevisionByOrderIdAndDriverId(ctx, orderID, driverID, repository.WithReadSecondaryPreferred)
	if err != nil {
		logx.Error().Context(ctx).Err(err).
			Str("driver_id", driverID).
			Str("order_id", orderID).
			Str("method", "findPreviousLocationForCancelledOrder").
			Msgf("unable to get previous cancelled order")
		return model.Location{}
	}

	if cancelledOrder.Status != model.StatusCanceled {
		logx.Error().Context(ctx).Err(err).
			Str("driver_id", driverID).
			Str("order_id", orderID).
			Str("method", "findPreviousLocationForCancelledOrder").
			Msgf("the order status is not cancelled")
		return model.Location{}
	}

	switch cancelledOrder.ServiceType {
	case model.ServiceFood:
		return f.getPreviousPickupLocationFoodMart(cancelledOrder)
	case model.ServiceMart:
		return f.getPreviousPickupLocationFoodMart(cancelledOrder)
	case model.ServiceMessenger:
		return f.getPreviousPickupLocationMessengerBike(cancelledOrder)
	case model.ServiceBike:
		return f.getPreviousPickupLocationMessengerBike(cancelledOrder)
	}

	logx.Error().Context(ctx).Err(err).
		Str("driver_id", driverID).
		Str("order_id", orderID).
		Str("method", "findPreviousLocationForCancelledOrder").
		Msgf("the service type is not support")
	return model.Location{}
}

func (f *claimFormFairDisputePickup) getPreviousPickupLocationFoodMart(srcOrder *model.Order) model.Location {
	latestStatus := GetOrderLatestStatus(srcOrder, []string{
		string(model.StatusDriverArrivedRestaurant),
		string(model.StatusDriverMatched),
		string(model.StatusDriverToRestaurant),
	})

	switch model.Status(latestStatus) {
	case model.StatusDriverArrivedRestaurant:
		return srcOrder.Routes[0].Location
	case model.StatusDriverMatched:
		// We assume that DRIVER_MATCHED equal to DRIVER_TO_RESTAURANT because a Rider didn't arrived at a restaurant yet
		fallthrough
	case model.StatusDriverToRestaurant:
		return getDriverCancelledLocation(srcOrder)
	}

	return model.Location{}
}

func (f *claimFormFairDisputePickup) getPreviousPickupLocationMessengerBike(srcOrder *model.Order) model.Location {
	latestStatus := GetOrderLatestStatus(srcOrder, []string{
		string(model.NewDriveToOrderStatus(0)),
		string(model.NewArrivedAtOrderStatus(0)),
		string(model.StatusDriverMatched),
	})

	switch model.Status(latestStatus) {
	case model.NewArrivedAtOrderStatus(0):
		return srcOrder.Routes[0].Location
	case model.StatusDriverMatched:
		fallthrough
	case model.NewDriveToOrderStatus(0):
		return getDriverCancelledLocation(srcOrder)
	}

	return model.Location{}
}

func getPickupLocation(order *model.Order) model.Location {
	if order == nil || len(order.Routes) <= 0 {
		logx.Error().Str("method", "getPickupLocation").Str("driver_id", order.Driver).Msgf("claim form. driver: %s, order id: %s. No PickupLocation", order.Driver, order.OrderID)
		return model.Location{}
	}
	return order.Routes[0].Location
}

type basePurchaseForm struct {
	Type    model.FormType
	Subtype model.FormSubtype
	Driver  *model.Driver
	Request map[string]interface{}
}

func (b *basePurchaseForm) get() (model.FormService, error) {
	prefill := make(map[string]interface{})
	paymentTenorMap := make(map[string]interface{}, 0)

	driver := b.Driver

	prefill[model.FormPrefillDriverId] = driver.DriverID
	prefill[model.FormPrefillTier] = string(driver.DriverTier)
	prefill[model.FormPrefillFirstName] = driver.Firstname.String()
	prefill[model.FormPrefillLastName] = driver.Lastname.String()
	prefill[model.FormPrefillPhoneNumber] = driver.Phone.String()

	value := b.Request

	paymentTenor, ok := value[model.FormPrefillPaymentTenor].(map[string]interface{})
	if !ok {
		return model.FormService{}, errors.New("payment tenor id is empty")
	}

	batchId, ok := value[model.FormPrefillBatchId]
	if !ok {
		return model.FormService{}, errors.New("batch id is empty")
	}

	templateId, ok := value[model.FormPrefillTemplateId]
	if !ok {
		return model.FormService{}, errors.New("template id is empty")
	}

	productSku, ok := value[model.FormPrefillProductSku]
	if !ok {
		return model.FormService{}, errors.New("product sku is empty")
	}

	netPrice, ok := paymentTenor[model.FormPrefillNetPrice]
	if !ok {
		return model.FormService{}, errors.New("payment tenor: net price is empty")
	}

	tenor, ok := paymentTenor[model.FormPrefillTenor]
	if !ok {
		return model.FormService{}, errors.New("payment tenor: tenor is empty")
	}

	dailyAmount, ok := paymentTenor[model.FormPrefillDailyAmount]
	if !ok {
		return model.FormService{}, errors.New("payment tenor: daily amount is empty")
	}

	var rewardLabel interface{}
	rewardAmount, isRewardAmountExist := paymentTenor[model.FormPrefillRewardAmount]
	if isRewardAmountExist {
		rewardLabel, ok = paymentTenor[model.FormPrefillRewardLabel]
		if !ok {
			return model.FormService{}, errors.New("payment tenor: reward label is empty")
		}
	}

	productName, ok := value[model.FormPrefillProductName].(string)
	if !ok {
		return model.FormService{}, errors.New("product name is empty")
	}

	productBundleSkus := value[model.FormPrefillProductBundleSkus]

	productImageUrls, ok := value[model.FormPrefillProductImageUrls]
	if !ok {
		return model.FormService{}, errors.New("product image url is empty")
	}

	batchGroupType, ok := value[model.FormPrefillBatchGroupType].(string)
	if !ok {
		return model.FormService{}, errors.New("batch group type is empty")
	}

	paymentTenorType, ok := paymentTenor[model.FormPrefillType].(string)
	if !ok {
		return model.FormService{}, errors.New("payment tenor: type is empty")
	}

	if isRewardAmountExist {
		rewardSku, ok := paymentTenor[model.FormPrefillRewardSku].(string)
		if !ok {
			return model.FormService{}, errors.New("payment tenor: reward sku is empty")
		}
		paymentTenorMap[model.FormPrefillRewardSku] = rewardSku
	}

	prefill[model.FormPrefillProductName] = productName
	prefill[model.FormPrefillProductBundleSkus] = productBundleSkus
	prefill[model.FormPrefillProductImageUrls] = productImageUrls
	prefill[model.FormPrefillBatchGroupType] = batchGroupType

	paymentTenorMap[model.FormPrefillType] = paymentTenorType
	paymentTenorMap[model.FormPrefillNetPrice] = netPrice
	paymentTenorMap[model.FormPrefillTenor] = tenor
	paymentTenorMap[model.FormPrefillDailyAmount] = dailyAmount
	paymentTenorMap[model.FormPrefillRewardLabel] = rewardLabel
	paymentTenorMap[model.FormPrefillRewardAmount] = rewardAmount
	prefill[model.FormPrefillPaymentTenor] = paymentTenorMap

	prefill[model.FormPrefillBatchId] = batchId
	prefill[model.FormPrefillTemplateId] = templateId
	prefill[model.FormPrefillProductSku] = productSku

	return model.FormService{
		FormType:    b.Type,
		FormSubtype: b.Subtype,
		Prefill:     prefill,
	}, nil
}

type egsPurchaseForm struct {
	basePurchaseForm
}

func NewPurchaseEGSForm(driver *model.Driver, request map[string]interface{}) model.FormBuilder {
	return &egsPurchaseForm{
		basePurchaseForm: basePurchaseForm{
			Type:    model.FormTypePurchaseEGS,
			Subtype: "",
			Driver:  driver,
			Request: request,
		},
	}
}

func (f *egsPurchaseForm) Validate(_ context.Context) (bool, error) {
	return true, nil
}

func (f *egsPurchaseForm) Get(_ context.Context) (model.FormService, error) {
	form, err := f.get()
	if err != nil {
		return form, err
	}

	form.Prefill[model.FormPrefillTitle] = f.Driver.Title.String()

	form.Prefill[model.FormPrefillAddress] = map[string]interface{}{
		model.FormPrefillHouseNumberAndMoo: f.Driver.Address.HouseNumberAndMoo(),
		model.FormPrefillSubdistrict:       f.Driver.Address.Subdistrict.String(),
		model.FormPrefillDistrict:          f.Driver.Address.District.String(),
		model.FormPrefillProvince:          f.Driver.Address.Province.String(),
		model.FormPrefillZipcode:           f.Driver.Address.Zipcode.String(),
	}

	deliverBy, ok := f.Request[model.FormPrefillDeliverBy].(string)
	if !ok {
		return model.FormService{}, errors.New("deliver by is empty")
	}

	form.Prefill[model.FormPrefillDeliverBy] = deliverBy

	return form, nil
}

type positiveCreditPurchaseForm struct {
	basePurchaseForm
}

func NewPositiveCreditPurchaseForm(driver *model.Driver, request map[string]interface{}) model.FormBuilder {
	return &positiveCreditPurchaseForm{
		basePurchaseForm: basePurchaseForm{
			Type:    model.FormTypePurchaseEGS,
			Subtype: model.FormSubtypePositiveCredit,
			Driver:  driver,
			Request: request,
		},
	}
}

func (f *positiveCreditPurchaseForm) Validate(_ context.Context) (bool, error) {
	return true, nil
}

func (f *positiveCreditPurchaseForm) Get(_ context.Context) (model.FormService, error) {
	form, err := f.get()
	if err != nil {
		return form, err
	}

	if form.Prefill[model.FormPrefillBatchGroupType] != string(model.FormEGSBatchGroupTypePositiveCredit) {
		return model.FormService{}, errors.New("batch group type is not positive credit")
	}

	paymentTenor, ok := form.Prefill[model.FormPrefillPaymentTenor].(map[string]interface{})
	if !ok {
		return model.FormService{}, errors.New("payment tenor isn't a map")
	}

	paymentTenorType, ok := paymentTenor[model.FormPrefillType].(string)
	if !ok {
		return model.FormService{}, errors.New("payment tenor type isn't a string")
	}

	if paymentTenorType == string(model.InstallmentPaymentTypeOneTime) {
		return model.FormService{}, errors.New("payment tenor type cannot be ONE_TIME")
	}

	return form, nil
}
