package service

//go:generate mockgen -source=./location_manager.go -destination=./mock_service/mock_location_manager.go -package=mock_service
//go:generate mockgen -source=./location_manager.go -destination=./mock_location_manager.go -package=service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"github.com/twpayne/go-geom"

	poolv1 "git.wndv.co/go/proto/lineman/fleet/pool/v1"
	lmdelivery "git.wndv.co/lineman/delivery-service/pkg/client"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/localcache"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/polygonutil"
)

// LocationManager manage driver location.
type LocationManager interface {
	// SearchDrivers search drivers around location.
	SearchDrivers(ctx context.Context, loc model.Location, fromM, toM float64) ([]DriverWithLocation, error)

	// SearchDrivers search drivers with max distance (meter) and limit number of drivers.
	SearchDriversLimit(ctx context.Context, loc model.Location, toM float64, limit int, srv model.Service, region model.RegionCode, orderID string) (SearchResult, error)

	// SearchDriversInMultiPolygon search drivers with given coordinates
	SearchDriversInMultiPolygon(ctx context.Context, batchZoneName string, coordinates [][][]geom.Coord, toM float64) (SearchResult, error)

	// UpdateLocation update driver location.
	UpdateLocation(ctx context.Context, driver *model.DriverMinimal, loc model.Location) error

	DirectSearchDrivers(ctx context.Context, query repository.DriverLocationQuery) ([]DriverWithLocation, error)

	GetDriverLocationWithUpdatedAt(ctx context.Context, driverID string) (resp *model.LocationWithUpdatedAt, err error)

	GetDriverWithLocation(ctx context.Context, driverLocations []model.DriverLocation) ([]DriverWithLocation, error)

	GetDriverWithLocationByIDs(ctx context.Context, driverIDs []string) ([]DriverWithLocation, error)

	DirectSearchDriversInMultiPolygon(ctx context.Context, query repository.DriverLocationInMultiPolygonQuery) ([]DriverWithLocation, error)
}

type DriverWithLocationMinimal struct {
	DriverID                 string         `json:"driverID"`
	Location                 model.Location `json:"location"`
	DistanceMeter            float64        `json:"distanceMeter"`
	IsPrioritized            bool           `json:"isPrioritized"`
	IsDedicatedZone          bool           `json:"isDedicatedZone"`
	OverrideIdleTime         *int           `json:"overrideIdleTime"`
	PredictedFulFillmentRate *float64       `json:"predictedFulFillmentRate"`
	TripID                   *string        `json:"tripID"`
}

// DriverWithLocation CAUTION!
// Mutating fields will only work for scorers used in single-optimize
// To mutate fields in batch-optimize, you have to do it separately in ProcessBatch
type DriverWithLocation struct {
	Driver                   model.DriverMinimal
	Location                 model.Location
	DistanceMeter            float64
	IsPrioritized            bool
	IsDedicatedZone          bool
	OverrideIdleTime         *int
	PredictedFulFillmentRate *float64
	TripID                   *string
}

func (d DriverWithLocation) ToMinimal() DriverWithLocationMinimal {
	return DriverWithLocationMinimal{
		DriverID:                 d.Driver.DriverID,
		Location:                 d.Location,
		DistanceMeter:            d.DistanceMeter,
		IsPrioritized:            d.IsPrioritized,
		IsDedicatedZone:          d.IsDedicatedZone,
		OverrideIdleTime:         d.OverrideIdleTime,
		PredictedFulFillmentRate: d.PredictedFulFillmentRate,
	}
}

var _ LocationManager = &LocationManagerImpl{}

type LocationManagerImpl struct {
	lcCache                  localcache.Caches
	driverLocationRepository repository.DriverLocationRepository
	delivery                 delivery.Delivery
	driverRepository         repository.DriverRepository
	serviceAreaRepository    repository.ServiceAreaRepository
	locationManagerConfig    config.LocationManagerConfig
	serviceAreaService       ServiceAreaService
	tripRepository           repository.TripRepository
	orderRepository          repository.OrderRepository
	wg                       sync.WaitGroup
	featureFlag              featureflag.Service
	fleetPoolRiderSearch     poolv1.RiderSearchServiceClient
	metricsRegistry          metric.MetricsRegistry
}

const (
	precalculatePolygonWithOffsetPrefix = "polygon_with_offset:"
)

func (locmngr *LocationManagerImpl) GetPreCalculationPolygonWithOffset(ctx context.Context, batchZoneName string, offset float64, polygon [][][]geom.Coord) ([][][]geom.Coord, error) {
	key := fmt.Sprintf("%s%s:%.2f", precalculatePolygonWithOffsetPrefix, batchZoneName, offset)

	res, err := locmngr.lcCache.GetWithRefresh(ctx, key, func(ctx context.Context) (interface{}, error) {
		polygonWithOffset, err := polygonutil.OffsetMultiPolygon(polygon, offset)
		if err != nil {
			return nil, err
		}
		return polygonWithOffset, nil
	}, 45*time.Second)
	if err != nil {
		return nil, err
	}

	polygonWithOffset, ok := res.([][][]geom.Coord)
	if !ok {
		return nil, errors.New("cannot parse cache, type mismatch for polygon with offset")
	}
	return polygonWithOffset, nil
}

func (locmngr *LocationManagerImpl) DirectSearchDrivers(ctx context.Context, query repository.DriverLocationQuery) ([]DriverWithLocation, error) {
	collector, m := metric.GetCollector(), metric.NewAPILatency("locmgrsearch")
	defer collector.Save(m)
	driverLocations, err := locmngr.driverLocationRepository.GetDrivers(ctx, query)
	m.Lap("location")
	if err != nil {
		return nil, err
	}

	result, err := locmngr.GetDriverWithLocation(ctx, driverLocations)
	if err != nil {
		return nil, err
	}
	m.Lap("profile")
	return result, nil
}

func (locmngr *LocationManagerImpl) DirectSearchDriversInMultiPolygon(ctx context.Context, query repository.DriverLocationInMultiPolygonQuery) ([]DriverWithLocation, error) {
	collector, m := metric.GetCollector(), metric.NewAPILatency("locmgrsearch_polygon")
	defer collector.Save(m)
	driverLocations, err := locmngr.driverLocationRepository.GetDriversInMultiPolygon(ctx, query)
	m.Lap("location")
	if err != nil {
		return nil, err
	}

	result, err := locmngr.GetDriverWithLocation(ctx, driverLocations)
	if err != nil {
		return nil, err
	}
	m.Lap("profile")
	return result, nil
}

func (locmngr *LocationManagerImpl) GetDriverWithLocationByIDs(ctx context.Context, driverIDs []string) ([]DriverWithLocation, error) {
	driverLocations := locmngr.driverLocationRepository.GetDriversByIDs(ctx, driverIDs)
	result, err := locmngr.GetDriverWithLocation(ctx, driverLocations)
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (locmngr *LocationManagerImpl) GetDriverWithLocation(ctx context.Context, driverLocations []model.DriverLocation) ([]DriverWithLocation, error) {
	driverIDs := make([]string, len(driverLocations))
	for i, dl := range driverLocations {
		driverIDs[i] = dl.DriverID
	}
	driversById, err := locmngr.driverRepository.GetMinimalProfilesByID(ctx, driverIDs)
	if err != nil {
		return nil, err
	}

	result := make([]DriverWithLocation, 0, len(driverLocations))
	for _, dl := range driverLocations {
		driver := driversById[dl.DriverID]
		if driver == nil {
			logrus.Errorf("SearchDrivers: cannot get driver %v profile", dl.DriverID)
			continue
		}
		result = append(result, DriverWithLocation{
			Driver:        *driver,
			Location:      model.Location{Lat: dl.Location.Lat, Lng: dl.Location.Lng},
			DistanceMeter: dl.DistanceMeter,
		})
	}
	return result, nil
}

func (locmngr *LocationManagerImpl) GetDriverLocationWithUpdatedAt(ctx context.Context, driverID string) (resp *model.LocationWithUpdatedAt, err error) {
	return locmngr.driverLocationRepository.GetDriverLocationWithUpdatedAt(ctx, driverID)
}

// SearchDrivers implements LocationManager.
func (locmngr *LocationManagerImpl) SearchDrivers(ctx context.Context, loc model.Location, fromM, toM float64) ([]DriverWithLocation, error) {
	query := repository.DriverLocationQuery{
		Location: model.Location{Lat: loc.Lat, Lng: loc.Lng},
		From:     fromM,
		To:       toM,
	}
	return locmngr.DirectSearchDrivers(ctx, query)
}

func (locmngr *LocationManagerImpl) UpdateLocation(ctx context.Context, driver *model.DriverMinimal, loc model.Location) error {
	if driver.Status == model.StatusBanned {
		return nil
	}

	onGoingOrderIDs := types.NewStringSet()
	if driver.Status == model.StatusAssigned {
		if driver.CurrentOrder != "" {
			onGoingOrderIDs.Add(driver.CurrentOrder) // for backward compatibility
		}
		if driver.CurrentTrip != "" {
			trip, err := locmngr.tripRepository.GetTripByTripID(ctx, driver.CurrentTrip, repository.WithReadSecondaryPreferred)
			if err != nil {
				return err
			}
			onGoingOrderIDs.Add(trip.GetOngoingOrderIDs()...)
		}
		if len(driver.QueueingTrips) != 0 {
			trips, err := locmngr.tripRepository.GetMany(ctx, driver.QueueingTrips)
			if err != nil {
				logrus.Errorf("trying to update location to queueing orders failed because couldn't fetch trips: %v; err: %v", strings.Join(driver.QueueingTrips, ","), err)
			}
			for _, t := range trips {
				for _, o := range t.Orders {
					if o.IsOngoing() && o.ServiceType == model.ServiceBike {
						onGoingOrderIDs.Add(o.OrderID)
					}
				}
			}
		}
		for _, orderID := range onGoingOrderIDs.GetElements() {
			locmngr.updateClientDriverLocation(orderID, loc)
		}
	}

	shouldUpdate, err := locmngr.shouldUpdateDriverLocation(ctx, driver, onGoingOrderIDs.GetElements())
	if err != nil {
		return err
	}

	if !locmngr.locationManagerConfig.EnableUpdateLocationOnly && !shouldUpdate {
		return nil
	}

	return locmngr.driverLocationRepository.UpdateDriverLocation(ctx, repository.UpdateDriverLocationRequest{
		DriverID: driver.DriverID,
		Location: model.LocationWithUpdatedAt{
			Lat:       loc.Lat,
			Lng:       loc.Lng,
			UpdatedAt: time.Now(),
		},
		ShouldUpdateLocationOnly: !shouldUpdate,
		IsBikeDriver:             driver.IsServiceEligible(model.ServiceBike),
	})
}

// shouldUpdateDriverLocation will apply on only single order that can't bundle order
func (locmngr *LocationManagerImpl) shouldUpdateDriverLocation(ctx context.Context, driver *model.DriverMinimal, onGoingOrderIDs []string) (bool, error) {
	if locmngr.locationManagerConfig.DriverUpdateLocationFilter && driver.Status == model.StatusAssigned && !driver.AllowQueueing {
		// when driver is locked for queueing, the location should still be updated
		if driver.IsLockedFromQueueing() {
			return true, nil
		}
		if len(onGoingOrderIDs) != 1 {
			return false, errors.New("invalid on going order")
		}
		order, err := locmngr.orderRepository.Get(ctx, onGoingOrderIDs[0], repository.WithReadSecondaryPreferred)
		if err != nil {
			return false, err
		}
		if order.Status != model.StatusDriverArrived {
			return false, nil
		}
	}
	return true, nil
}

func (locmngr *LocationManagerImpl) updateClientDriverLocation(orderID string, loc model.Location) {
	locmngr.wg.Add(1)
	safe.GoFunc(func() {
		defer locmngr.wg.Done()
		err := locmngr.delivery.UpdateDriverLocation(context.Background(), &lmdelivery.UpdateDriverLocationsRequest{
			Locations: []lmdelivery.DriverLocation{
				{
					OrderID: orderID,
					Lat:     fmt.Sprintf("%.6f", loc.Lat),
					Lng:     fmt.Sprintf("%.6f", loc.Lng),
				},
			},
		})
		if err != nil {
			logrus.WithField("service", "lm-delivery").Errorf("[UpdateDriverLocation] cannot send update location: %v:", err)
		}
	})
}

// Stop all pendings tasks inside LocationManagerImpl.
func (locmngr *LocationManagerImpl) Stop() {
	locmngr.wg.Wait()
}

func NewLocationManagerImpl(
	lcCache localcache.Caches,
	driverLocationRepository repository.DriverLocationRepository,
	delivery delivery.Delivery,
	driverRepository repository.DriverRepository,
	serviceAreaRepository repository.ServiceAreaRepository,
	locationManagerConfig config.LocationManagerConfig,
	serviceAreaService ServiceAreaService,
	tripRepository repository.TripRepository,
	orderRepository repository.OrderRepository,
	featureFlag featureflag.Service,
	fleetPoolRiderSearch poolv1.RiderSearchServiceClient,
	metricsRegistry metric.MetricsRegistry,
) *LocationManagerImpl {
	return &LocationManagerImpl{
		lcCache:                  lcCache,
		driverLocationRepository: driverLocationRepository,
		delivery:                 delivery,
		driverRepository:         driverRepository,
		serviceAreaRepository:    serviceAreaRepository,
		locationManagerConfig:    locationManagerConfig,
		serviceAreaService:       serviceAreaService,
		tripRepository:           tripRepository,
		orderRepository:          orderRepository,
		featureFlag:              featureFlag,
		fleetPoolRiderSearch:     fleetPoolRiderSearch,
		metricsRegistry:          metricsRegistry,
	}
}

func ProvideLocationManagerImpl(
	lcCache localcache.Caches,
	driverLocationRepository repository.DriverLocationRepository,
	delivery delivery.Delivery,
	driverRepository repository.DriverRepository,
	serviceAreaRepository repository.ServiceAreaRepository,
	locationManagerConfig config.LocationManagerConfig,
	serviceAreaService ServiceAreaService,
	tripRepository repository.TripRepository,
	orderRepository repository.OrderRepository,
	featureFlag featureflag.Service,
	fleetPoolRiderSearch poolv1.RiderSearchServiceClient,
	metricsRegistry metric.MetricsRegistry,
) (*LocationManagerImpl, func()) {
	locmngr := NewLocationManagerImpl(lcCache, driverLocationRepository, delivery, driverRepository, serviceAreaRepository, locationManagerConfig, serviceAreaService, tripRepository, orderRepository, featureFlag, fleetPoolRiderSearch, metricsRegistry)
	return locmngr, func() { locmngr.Stop() }
}
