// Code generated by MockGen. DO NOT EDIT.
// Source: ./trip_service.go

// Package service is a generated GoMock package.
package service

import (
	context "context"
	reflect "reflect"

	client "git.wndv.co/lineman/delivery-service/pkg/client"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	repository "git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	gomock "github.com/golang/mock/gomock"
)

// MockTripServices is a mock of TripServices interface.
type MockTripServices struct {
	ctrl     *gomock.Controller
	recorder *MockTripServicesMockRecorder
}

// MockTripServicesMockRecorder is the mock recorder for MockTripServices.
type MockTripServicesMockRecorder struct {
	mock *MockTripServices
}

// NewMockTripServices creates a new mock instance.
func NewMockTripServices(ctrl *gomock.Controller) *MockTripServices {
	mock := &MockTripServices{ctrl: ctrl}
	mock.recorder = &MockTripServicesMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTripServices) EXPECT() *MockTripServicesMockRecorder {
	return m.recorder
}

// AdminCompleteTrip mocks base method.
func (m *MockTripServices) AdminCompleteTrip(ctx context.Context, tripID string, opts ...CompleteTripOptionFunc) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, tripID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AdminCompleteTrip", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// AdminCompleteTrip indicates an expected call of AdminCompleteTrip.
func (mr *MockTripServicesMockRecorder) AdminCompleteTrip(ctx, tripID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, tripID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdminCompleteTrip", reflect.TypeOf((*MockTripServices)(nil).AdminCompleteTrip), varargs...)
}

// CalculateMOSaving mocks base method.
func (m *MockTripServices) CalculateMOSaving(ctx context.Context, t *model.Trip, orders []model.Order) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CalculateMOSaving", ctx, t, orders)
	ret0, _ := ret[0].(error)
	return ret0
}

// CalculateMOSaving indicates an expected call of CalculateMOSaving.
func (mr *MockTripServicesMockRecorder) CalculateMOSaving(ctx, t, orders interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateMOSaving", reflect.TypeOf((*MockTripServices)(nil).CalculateMOSaving), ctx, t, orders)
}

// CalculateTripRoutes mocks base method.
func (m *MockTripServices) CalculateTripRoutes(ctx context.Context, trip *model.Trip, opts ...CalRoutesOptFn) ([]model.MapRoute, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, trip}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CalculateTripRoutes", varargs...)
	ret0, _ := ret[0].([]model.MapRoute)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CalculateTripRoutes indicates an expected call of CalculateTripRoutes.
func (mr *MockTripServicesMockRecorder) CalculateTripRoutes(ctx, trip interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, trip}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CalculateTripRoutes", reflect.TypeOf((*MockTripServices)(nil).CalculateTripRoutes), varargs...)
}

// CreateTripFromOrder mocks base method.
func (m *MockTripServices) CreateTripFromOrder(ctx context.Context, order model.Order, opts ...CreateTripOptFn) (model.Trip, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, order}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateTripFromOrder", varargs...)
	ret0, _ := ret[0].(model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateTripFromOrder indicates an expected call of CreateTripFromOrder.
func (mr *MockTripServicesMockRecorder) CreateTripFromOrder(ctx, order interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, order}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateTripFromOrder", reflect.TypeOf((*MockTripServices)(nil).CreateTripFromOrder), varargs...)
}

// ForceSyncTripWithFinishedOrders mocks base method.
func (m *MockTripServices) ForceSyncTripWithFinishedOrders(ctx context.Context, tripID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ForceSyncTripWithFinishedOrders", ctx, tripID)
	ret0, _ := ret[0].(error)
	return ret0
}

// ForceSyncTripWithFinishedOrders indicates an expected call of ForceSyncTripWithFinishedOrders.
func (mr *MockTripServicesMockRecorder) ForceSyncTripWithFinishedOrders(ctx, tripID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ForceSyncTripWithFinishedOrders", reflect.TypeOf((*MockTripServices)(nil).ForceSyncTripWithFinishedOrders), ctx, tripID)
}

// GetActiveOrdersByTrip mocks base method.
func (m *MockTripServices) GetActiveOrdersByTrip(ctx context.Context, trip *model.Trip) ([]model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetActiveOrdersByTrip", ctx, trip)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetActiveOrdersByTrip indicates an expected call of GetActiveOrdersByTrip.
func (mr *MockTripServicesMockRecorder) GetActiveOrdersByTrip(ctx, trip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetActiveOrdersByTrip", reflect.TypeOf((*MockTripServices)(nil).GetActiveOrdersByTrip), ctx, trip)
}

// GetOngoingOrdersByTripID mocks base method.
func (m *MockTripServices) GetOngoingOrdersByTripID(ctx context.Context, id string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOngoingOrdersByTripID", ctx, id)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOngoingOrdersByTripID indicates an expected call of GetOngoingOrdersByTripID.
func (mr *MockTripServicesMockRecorder) GetOngoingOrdersByTripID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOngoingOrdersByTripID", reflect.TypeOf((*MockTripServices)(nil).GetOngoingOrdersByTripID), ctx, id)
}

// GetOngoingTripIDsByDriverID mocks base method.
func (m *MockTripServices) GetOngoingTripIDsByDriverID(ctx context.Context, id string) ([]model.Trip, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOngoingTripIDsByDriverID", ctx, id)
	ret0, _ := ret[0].([]model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOngoingTripIDsByDriverID indicates an expected call of GetOngoingTripIDsByDriverID.
func (mr *MockTripServicesMockRecorder) GetOngoingTripIDsByDriverID(ctx, id interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOngoingTripIDsByDriverID", reflect.TypeOf((*MockTripServices)(nil).GetOngoingTripIDsByDriverID), ctx, id)
}

// GetOrCreateTripFromOrderID mocks base method.
func (m *MockTripServices) GetOrCreateTripFromOrderID(ctx context.Context, orderID string) (model.Trip, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrCreateTripFromOrderID", ctx, orderID)
	ret0, _ := ret[0].(model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrCreateTripFromOrderID indicates an expected call of GetOrCreateTripFromOrderID.
func (mr *MockTripServicesMockRecorder) GetOrCreateTripFromOrderID(ctx, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrCreateTripFromOrderID", reflect.TypeOf((*MockTripServices)(nil).GetOrCreateTripFromOrderID), ctx, orderID)
}

// GetOrdersByTrip mocks base method.
func (m *MockTripServices) GetOrdersByTrip(ctx context.Context, trip *model.Trip, opts ...repository.Option) ([]model.Order, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, trip}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetOrdersByTrip", varargs...)
	ret0, _ := ret[0].([]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrdersByTrip indicates an expected call of GetOrdersByTrip.
func (mr *MockTripServicesMockRecorder) GetOrdersByTrip(ctx, trip interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, trip}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrdersByTrip", reflect.TypeOf((*MockTripServices)(nil).GetOrdersByTrip), varargs...)
}

// GetOrdersMapFromTripIDs mocks base method.
func (m *MockTripServices) GetOrdersMapFromTripIDs(ctx context.Context, ids []string) (map[string][]model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOrdersMapFromTripIDs", ctx, ids)
	ret0, _ := ret[0].(map[string][]model.Order)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOrdersMapFromTripIDs indicates an expected call of GetOrdersMapFromTripIDs.
func (mr *MockTripServicesMockRecorder) GetOrdersMapFromTripIDs(ctx, ids interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOrdersMapFromTripIDs", reflect.TypeOf((*MockTripServices)(nil).GetOrdersMapFromTripIDs), ctx, ids)
}

// GetTripByID mocks base method.
func (m *MockTripServices) GetTripByID(ctx context.Context, tripID string, opts ...repository.Option) (model.Trip, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, tripID}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTripByID", varargs...)
	ret0, _ := ret[0].(model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTripByID indicates an expected call of GetTripByID.
func (mr *MockTripServicesMockRecorder) GetTripByID(ctx, tripID interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, tripID}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTripByID", reflect.TypeOf((*MockTripServices)(nil).GetTripByID), varargs...)
}

// GetTripFromOrderID mocks base method.
func (m *MockTripServices) GetTripFromOrderID(ctx context.Context, orderID string) (model.Trip, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTripFromOrderID", ctx, orderID)
	ret0, _ := ret[0].(model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTripFromOrderID indicates an expected call of GetTripFromOrderID.
func (mr *MockTripServicesMockRecorder) GetTripFromOrderID(ctx, orderID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTripFromOrderID", reflect.TypeOf((*MockTripServices)(nil).GetTripFromOrderID), ctx, orderID)
}

// GetWithOrders mocks base method.
func (m *MockTripServices) GetWithOrders(ctx context.Context, driverID string, serviceType model.Service, skip, limit int) ([]model.Trip, []model.Order, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetWithOrders", ctx, driverID, serviceType, skip, limit)
	ret0, _ := ret[0].([]model.Trip)
	ret1, _ := ret[1].([]model.Order)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// GetWithOrders indicates an expected call of GetWithOrders.
func (mr *MockTripServicesMockRecorder) GetWithOrders(ctx, driverID, serviceType, skip, limit interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetWithOrders", reflect.TypeOf((*MockTripServices)(nil).GetWithOrders), ctx, driverID, serviceType, skip, limit)
}

// HasOngoingWechatOrder mocks base method.
func (m *MockTripServices) HasOngoingWechatOrder(ctx context.Context, driverID string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HasOngoingWechatOrder", ctx, driverID)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HasOngoingWechatOrder indicates an expected call of HasOngoingWechatOrder.
func (mr *MockTripServicesMockRecorder) HasOngoingWechatOrder(ctx, driverID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HasOngoingWechatOrder", reflect.TypeOf((*MockTripServices)(nil).HasOngoingWechatOrder), ctx, driverID)
}

// OnForceCompletedOrder mocks base method.
func (m *MockTripServices) OnForceCompletedOrder(ctx context.Context, e TripOrderForceCompletedEvent, opts ...CompleteTripOptionFunc) (model.Trip, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, e}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "OnForceCompletedOrder", varargs...)
	ret0, _ := ret[0].(model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnForceCompletedOrder indicates an expected call of OnForceCompletedOrder.
func (mr *MockTripServicesMockRecorder) OnForceCompletedOrder(ctx, e interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, e}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnForceCompletedOrder", reflect.TypeOf((*MockTripServices)(nil).OnForceCompletedOrder), varargs...)
}

// OnOrderCanceled mocks base method.
func (m *MockTripServices) OnOrderCanceled(ctx context.Context, e TripOrderCanceledEvent) (model.Trip, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnOrderCanceled", ctx, e)
	ret0, _ := ret[0].(model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnOrderCanceled indicates an expected call of OnOrderCanceled.
func (mr *MockTripServicesMockRecorder) OnOrderCanceled(ctx, e interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnOrderCanceled", reflect.TypeOf((*MockTripServices)(nil).OnOrderCanceled), ctx, e)
}

// OnOrderChanged mocks base method.
func (m *MockTripServices) OnOrderChanged(ctx context.Context, e TripOrderEvent) (model.Trip, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnOrderChanged", ctx, e)
	ret0, _ := ret[0].(model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnOrderChanged indicates an expected call of OnOrderChanged.
func (mr *MockTripServicesMockRecorder) OnOrderChanged(ctx, e interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnOrderChanged", reflect.TypeOf((*MockTripServices)(nil).OnOrderChanged), ctx, e)
}

// OnUpdateDeliveryLocation mocks base method.
func (m *MockTripServices) OnUpdateDeliveryLocation(ctx context.Context, e TripOrderUpdateLocationEvent) (model.Trip, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "OnUpdateDeliveryLocation", ctx, e)
	ret0, _ := ret[0].(model.Trip)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// OnUpdateDeliveryLocation indicates an expected call of OnUpdateDeliveryLocation.
func (mr *MockTripServicesMockRecorder) OnUpdateDeliveryLocation(ctx, e interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "OnUpdateDeliveryLocation", reflect.TypeOf((*MockTripServices)(nil).OnUpdateDeliveryLocation), ctx, e)
}

// ProcessFee mocks base method.
func (m *MockTripServices) ProcessFee(ctx context.Context, trip model.Trip) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessFee", ctx, trip)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProcessFee indicates an expected call of ProcessFee.
func (mr *MockTripServicesMockRecorder) ProcessFee(ctx, trip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessFee", reflect.TypeOf((*MockTripServices)(nil).ProcessFee), ctx, trip)
}

// ProcessPendingFee mocks base method.
func (m *MockTripServices) ProcessPendingFee(ctx context.Context, trip model.Trip, isProcessByAdmin bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProcessPendingFee", ctx, trip, isProcessByAdmin)
	ret0, _ := ret[0].(error)
	return ret0
}

// ProcessPendingFee indicates an expected call of ProcessPendingFee.
func (mr *MockTripServicesMockRecorder) ProcessPendingFee(ctx, trip, isProcessByAdmin interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProcessPendingFee", reflect.TypeOf((*MockTripServices)(nil).ProcessPendingFee), ctx, trip, isProcessByAdmin)
}

// PublishMissionLogTripCompletedEvent mocks base method.
func (m *MockTripServices) PublishMissionLogTripCompletedEvent(ctx context.Context, trip model.Trip) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "PublishMissionLogTripCompletedEvent", ctx, trip)
}

// PublishMissionLogTripCompletedEvent indicates an expected call of PublishMissionLogTripCompletedEvent.
func (mr *MockTripServicesMockRecorder) PublishMissionLogTripCompletedEvent(ctx, trip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PublishMissionLogTripCompletedEvent", reflect.TypeOf((*MockTripServices)(nil).PublishMissionLogTripCompletedEvent), ctx, trip)
}

// RecalculateTripDeliveryFee mocks base method.
func (m *MockTripServices) RecalculateTripDeliveryFee(ctx context.Context, t *model.Trip, opts ...RecalOptFn) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, t}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecalculateTripDeliveryFee", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecalculateTripDeliveryFee indicates an expected call of RecalculateTripDeliveryFee.
func (mr *MockTripServicesMockRecorder) RecalculateTripDeliveryFee(ctx, t interface{}, opts ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, t}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateTripDeliveryFee", reflect.TypeOf((*MockTripServices)(nil).RecalculateTripDeliveryFee), varargs...)
}

// RecalculateTripWage mocks base method.
func (m *MockTripServices) RecalculateTripWage(ctx context.Context, trip *model.Trip, optFns ...RecalOptFn) error {
	m.ctrl.T.Helper()
	varargs := []interface{}{ctx, trip}
	for _, a := range optFns {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RecalculateTripWage", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// RecalculateTripWage indicates an expected call of RecalculateTripWage.
func (mr *MockTripServicesMockRecorder) RecalculateTripWage(ctx, trip interface{}, optFns ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{ctx, trip}, optFns...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RecalculateTripWage", reflect.TypeOf((*MockTripServices)(nil).RecalculateTripWage), varargs...)
}

// SetZeroTripIncomeForFullTimeDriver mocks base method.
func (m *MockTripServices) SetZeroTripIncomeForFullTimeDriver(ctx context.Context, trip *model.Trip) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetZeroTripIncomeForFullTimeDriver", ctx, trip)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetZeroTripIncomeForFullTimeDriver indicates an expected call of SetZeroTripIncomeForFullTimeDriver.
func (mr *MockTripServicesMockRecorder) SetZeroTripIncomeForFullTimeDriver(ctx, trip interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetZeroTripIncomeForFullTimeDriver", reflect.TypeOf((*MockTripServices)(nil).SetZeroTripIncomeForFullTimeDriver), ctx, trip)
}

// SyncTripOrderStatus mocks base method.
func (m *MockTripServices) SyncTripOrderStatus(ctx context.Context, ord *model.Order, pods []client.ProofOfDeliveryImage) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncTripOrderStatus", ctx, ord, pods)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncTripOrderStatus indicates an expected call of SyncTripOrderStatus.
func (mr *MockTripServicesMockRecorder) SyncTripOrderStatus(ctx, ord, pods interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncTripOrderStatus", reflect.TypeOf((*MockTripServices)(nil).SyncTripOrderStatus), ctx, ord, pods)
}

// SyncTripState mocks base method.
func (m *MockTripServices) SyncTripState(ctx context.Context, tripID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SyncTripState", ctx, tripID)
	ret0, _ := ret[0].(error)
	return ret0
}

// SyncTripState indicates an expected call of SyncTripState.
func (mr *MockTripServicesMockRecorder) SyncTripState(ctx, tripID interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SyncTripState", reflect.TypeOf((*MockTripServices)(nil).SyncTripState), ctx, tripID)
}

// TryUpdateDistance0 mocks base method.
func (m *MockTripServices) TryUpdateDistance0(ctx context.Context, selectedTrip, nextTrip *model.Trip, newOrderID string, driv *model.Driver) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TryUpdateDistance0", ctx, selectedTrip, nextTrip, newOrderID, driv)
	ret0, _ := ret[0].(error)
	return ret0
}

// TryUpdateDistance0 indicates an expected call of TryUpdateDistance0.
func (mr *MockTripServicesMockRecorder) TryUpdateDistance0(ctx, selectedTrip, nextTrip, newOrderID, driv interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TryUpdateDistance0", reflect.TypeOf((*MockTripServices)(nil).TryUpdateDistance0), ctx, selectedTrip, nextTrip, newOrderID, driv)
}

// UpdateRestaurantReviewedByTripIDAndRouteIndex mocks base method.
func (m *MockTripServices) UpdateRestaurantReviewedByTripIDAndRouteIndex(ctx context.Context, tripID string, routeIndex int, restaurantReviewed bool) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRestaurantReviewedByTripIDAndRouteIndex", ctx, tripID, routeIndex, restaurantReviewed)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRestaurantReviewedByTripIDAndRouteIndex indicates an expected call of UpdateRestaurantReviewedByTripIDAndRouteIndex.
func (mr *MockTripServicesMockRecorder) UpdateRestaurantReviewedByTripIDAndRouteIndex(ctx, tripID, routeIndex, restaurantReviewed interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRestaurantReviewedByTripIDAndRouteIndex", reflect.TypeOf((*MockTripServices)(nil).UpdateRestaurantReviewedByTripIDAndRouteIndex), ctx, tripID, routeIndex, restaurantReviewed)
}
