package service

//go:generate mockgen -source=./installment_service.go -destination=./mock_service/mock_installment_service.go -package=mock_service

import (
	"context"
	"sort"
	"strings"
	"time"

	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"go.mongodb.org/mongo-driver/bson/primitive"

	"git.wndv.co/go/logx/v2"
	inventoryPb "git.wndv.co/go/proto/lineman/inventory/v1"
	"git.wndv.co/lineman/absinthe/database/v2/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fp"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	timeutils "git.wndv.co/lineman/xgo/timeutil"
)

type InstallmentAuditLog struct {
	before model.Installment
	after  model.Installment
}

type OverdueInstallmentTransactionInfosBuilderOption struct {
	maximumAmount   types.Money
	processedAmount *types.Money
}

func WithMaximumAmount(maxAmount types.Money) func(*OverdueInstallmentTransactionInfosBuilderOption) {
	return func(opt *OverdueInstallmentTransactionInfosBuilderOption) {
		opt.maximumAmount = maxAmount
	}
}

func WithProcessedAmount(processedAmount *types.Money) func(*OverdueInstallmentTransactionInfosBuilderOption) {
	return func(opt *OverdueInstallmentTransactionInfosBuilderOption) {
		opt.processedAmount = processedAmount
	}
}

func OverdueInstallmentTransactionInfosBuilder(
	logs model.SortingInstallmentLogList,
	options ...func(*OverdueInstallmentTransactionInfosBuilderOption),
) TransactionInfosBuilder {
	return func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
		var opt OverdueInstallmentTransactionInfosBuilderOption
		for _, o := range options {
			o(&opt)
		}

		availableAmount := dt.WalletBalance
		if opt.maximumAmount.GT(types.NewMoney(0)) && availableAmount.GT(opt.maximumAmount) {
			availableAmount = opt.maximumAmount
		}

		if opt.processedAmount != nil {
			defer func(availableAmount types.Money, remainAmount *types.Money) {
				paidAmount := availableAmount.Sub(*remainAmount)
				*opt.processedAmount = paidAmount
			}(availableAmount, &availableAmount)
		}

		var infos []model.TransactionInfo

		// generate operation expense txn
		var operationExpense types.Money
		if dt.CreditBalance().LT(0) {
			operationExpense = dt.CreditBalance().Sub(dt.InstallmentAmount).Abs()
		}
		if operationExpense.GT(0) {
			toDeductAmount := operationExpense
			if toDeductAmount.GTE(availableAmount) {
				toDeductAmount = availableAmount
			}
			txns, err := dt.TransferWalletToCredit(toDeductAmount)
			if err != nil {
				logx.Error().Msgf("cannot generate credit operation expense from wallet: %v, driver id %s", err, dt.DriverID)
				return nil, err
			}

			availableAmount = availableAmount.Sub(toDeductAmount)
			infos = append(infos, txns...)
		}

		// generate installment log txn
		for _, l := range logs {
			var txnInfos []model.TransactionInfo
			var err error

			log := l.Log
			if !log.IsDeductionLog {
				if availableAmount.LTE(0) {
					return infos, nil
				}

				paidAmount := log.GetTotalActualPaidAmount()
				payAmount := log.Amount.Sub(paidAmount)
				if payAmount.GT(availableAmount) {
					payAmount = availableAmount
				}

				txnInfos, err = dt.TransferWalletToCredit(payAmount)
				if err != nil {
					return nil, err
				}
				availableAmount = availableAmount.Sub(payAmount)
				for index := range txnInfos {
					txnInfos[index].TransRefID = log.TransRefID
				}
				infos = append(infos, txnInfos...)
			}

		}

		return infos, nil
	}
}

func AttachProductInfoToInstallmentsFromProductService(ctx context.Context, ps inventoryPb.ProductServiceClient, installments []model.Installment) ([]model.Installment, error) {
	if len(installments) == 0 {
		err := errors.New("installment not found")
		logx.Error().Msgf("AttachProductInfoToInstallmentsFromProductService: %v", err)
		return installments, err
	}

	req := inventoryPb.GetProductWithPriorityGroupRequest{
		Skus: fp.MapSlice(model.InstallmentToSKU, installments),
	}
	res, err := ps.GetProductWithPriorityGroup(ctx, &req)
	if err != nil {
		logx.Error().Msgf("AttachProductInfoToInstallmentsFromProductService: %v", err)
		return installments, err
	}
	if res == nil {
		err := errors.New("unable to find Product with Product Group")
		logx.Error().Msgf("AttachProductInfoToInstallmentsFromProductService: %v", err)
		return installments, err
	}
	products := res.Data

	skuToProduct := InventoryProductsWithPriorityGroupToMapSKUToProduct(products)
	idToProductGroup := InventoryProductsWithPriorityGroupToMapPriorityGroupIDToProductGroup(products)

	AttachProductAndProductGroupToInstallments(installments, skuToProduct, idToProductGroup)
	AttachBatchTypeFromProductMapToInstallments(installments, skuToProduct)

	return installments, nil
}

func InventoryProductsWithPriorityGroupToMapPriorityGroupIDToProductGroup(products []*inventoryPb.ProductWithPriorityGroup) map[string]*model.ProductGroup {
	return utils.SliceToMapKeepFirst(InventoryProductWithPriorityGroupToPriorityGroup, InventoryProductWithPriorityGroupToProductGroup, products)
}

func InventoryProductsWithPriorityGroupToMapSKUToProduct(products []*inventoryPb.ProductWithPriorityGroup) map[string]*model.Product {
	return utils.SliceToMapKeepLast(InventoryProductWithPriorityGroupToSKU, InventoryProductWithPriorityGroupToProduct, products)
}

func AttachProductAndProductGroupToInstallmentsFunc(skuToProduct map[string]*model.Product, idToProductGroup map[string]*model.ProductGroup) func(installments []model.Installment) {
	return func(installments []model.Installment) {
		AttachProductAndProductGroupToInstallments(installments, skuToProduct, idToProductGroup)
	}
}

func AttachProductAndProductGroupToInstallments(installments []model.Installment, skuToProduct map[string]*model.Product, idToProductGroup map[string]*model.ProductGroup) {
	for i, targetInstallment := range installments {
		var product *model.Product
		var priorityGroup *model.ProductGroup
		if _, existed := skuToProduct[targetInstallment.SKU]; existed {
			product = skuToProduct[targetInstallment.SKU]
		}
		if product != nil {
			targetPrioiryGroupID := product.PriorityGroupID
			if _, existed := idToProductGroup[targetPrioiryGroupID]; existed {
				priorityGroup = idToProductGroup[targetPrioiryGroupID]
			}
			installments[i].ProductGroup = priorityGroup
		}
		installments[i].Product = product
	}
}

func convertGRPCToProductGroup(src *inventoryPb.PriorityGroup) *model.ProductGroup {
	if src == nil {
		return nil
	}
	return &model.ProductGroup{
		PriorityGroupID: src.PriorityGroupId,
		Name:            src.Name,
		Priority:        int(src.Priority),
		CreatedBy:       src.CreatedBy,
		CreatedAt:       src.CreatedAt.AsTime(),
		UpdatedBy:       src.UpdatedBy,
		UpdatedAt:       src.UpdatedAt.AsTime(),
	}
}

func convertGRPCToProduct(src *inventoryPb.Product) *model.Product {
	if src == nil {
		return nil
	}
	primitiveID, err := primitive.ObjectIDFromHex(src.Id)
	if err != nil {
		logrus.Warnf("AttachProductInfoToInstallmentsFromProductService: unable to parse product ID [%s] to ObjectID", src.Id)
	}

	return &model.Product{
		ID:              primitiveID,
		Name:            src.Name,
		SKU:             src.Sku,
		Priority:        int(src.Priority),
		Price:           float64(types.NewMoneyWithDecimal(src.Price)),
		Description:     src.Description,
		CreatedBy:       src.CreatedBy,
		CreatedAt:       src.CreatedAt.AsTime(),
		UpdatedBy:       src.UpdatedBy,
		UpdatedAt:       src.UpdatedAt.AsTime(),
		PriorityGroupID: src.PriorityGroupId,
		BatchType:       model.ProductBatchTypeFromPbEnum(src.BatchGroup.String()),
	}
}

var ErrNotEnoughBalanceToChargeOnetimePaymentInstallment error = errors.New("DRIVER_BALANCE_NOT_ENOUGH")
var ErrInvalidInstallmentPaymentType error = errors.New("INVALID_INSTALLMENT_PAYMENT_TYPE")
var ErrInvalidInstallmentStatus error = errors.New("INVALID_INSTALLMENT_STATUS")

func OnetimeInstallmentDeductionTransactionInfosBuilder(logs model.SortingInstallmentLogList, timeNowBKK time.Time) TransactionInfosBuilder {
	return func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
		if len(logs) != 1 {
			return []model.TransactionInfo{}, errors.New("invalid log size for onetime deduction")
		}

		var infos []model.TransactionInfo

		// Copy free credit to do deduction (simulation)
		backupFreeCredit := make([]model.TransactionInfo, len(dt.FreeCreditTransactions))
		copy(backupFreeCredit, dt.FreeCreditTransactions)
		dt.FreeCreditTransactions = backupFreeCredit

		targetLog := logs[0]
		log := targetLog.Log
		if dt.WalletBalance.Add(dt.CreditBalance()) < log.Amount {
			return []model.TransactionInfo{}, ErrNotEnoughBalanceToChargeOnetimePaymentInstallment
		}

		displayText := strings.Replace(targetLog.Installment.MessageDisplay, "ผ่อน", "", 1)
		txnInfos, err := dt.NewInstallmentDeductTransaction(log.Amount, targetLog.Installment.ID.Hex(), displayText)
		if err != nil {
			return nil, err
		}

		log.TransRefID = txnInfos[0].TransRefID
		infos = append(infos, txnInfos...)
		log.ActualAmountLogs = append(log.ActualAmountLogs, model.ActualAmountLog{
			ActualAmount: log.Amount,
			CreatedAt:    timeNowBKK,
		})
		log.InstallmentPaymentStatus = model.InstallmentPaymentStatusSuccessful

		return infos, nil
	}
}

type InstallmentProductDetailUpdater struct {
	InstallmentID    string
	StockSKU         string
	StockID          string
	NewProductDetail model.ProductDetail
}

type InstallmentServiceImpl struct {
	cfg                      InstallmentCfg
	driverTransactionService DriverTransactionServiceV2
	installmentRepo          repository.InstallmentRepository
	auditLogRepo             repository.AuditLogRepository
	txnHelper                transaction.TxnHelper
}
type InstallmentService interface {
	CancelInstallment(ctx context.Context, ins model.Installment, returnAmount types.Money, actionBy string) error
	ChargeOnetimeInstallment(ctx context.Context, ins model.Installment, isLastAttempt bool) error
	IsDriverOnEGSOrderCooldown(ctx context.Context, driverID string) bool
	UpdateInstallmentsProductDetail(ctx context.Context, updater ...InstallmentProductDetailUpdater) ([]model.Installment, error)
	ForceUpdateInstallmentsProductDetail(ctx context.Context, requester string, updater model.InstallmentProductDetailForceUpdater) error
	TerminateInstallment(ctx context.Context, installmentID string, actionBy string) error
	ActivateInstallment(ctx context.Context, installmentID string, actionBy string) error
	CreateInstallment(ctx context.Context, installment *model.Installment) (*primitive.ObjectID, error)
}

func ProvideInstallmentService(
	cfg InstallmentCfg,
	instRepo repository.InstallmentRepository,
	driverTransactionService DriverTransactionServiceV2,
	auditLogRepo repository.AuditLogRepository,
	txnHelper transaction.TxnHelper,
) InstallmentService {
	return &InstallmentServiceImpl{
		cfg:                      cfg,
		installmentRepo:          instRepo,
		driverTransactionService: driverTransactionService,
		auditLogRepo:             auditLogRepo,
		txnHelper:                txnHelper,
	}
}

func (srv InstallmentServiceImpl) CancelInstallment(ctx context.Context, ins model.Installment, returnAmount types.Money, actionBy string) error {
	oriIns := ins
	if err := srv.processDriverTransaction(ctx, ins, returnAmount); err != nil {
		logx.Error().Msgf("InstallmentServiceImpl refundWalletInstallment error: %v", err)
		return err
	}

	if err := srv.adjustOverdueAmount(ctx, ins); err != nil {
		logx.Error().Msgf("InstallmentServiceImpl adjustOverdueAmount error: %v", err)
		return err
	}

	if err := srv.updateInstallmentDetail(ctx, ins); err != nil {
		logx.Error().Msgf("InstallmentServiceImpl updateInstallmentDetail error: %v", err)
		return err
	}
	srv.insertAuditLog(ctx, ins, oriIns, actionBy)
	return nil
}

func (srv InstallmentServiceImpl) TerminateInstallment(ctx context.Context, installmentID string, actionBy string) error {
	oldIns, err := srv.installmentRepo.FindById(ctx, installmentID)
	if err != nil {
		errMsg := "TerminateInstallment find installment by id err"
		logx.Error().Err(err).Msgf(errMsg+": %v", err)
		return err
	}

	// only validate PENDING for now
	if oldIns.Status != model.InstallmentPending {
		errMsg := "cannot terminate installment since installment status is not INSTALLMENT_PENDING"
		err := errors.New(errMsg)
		logx.Error().Err(err).Msgf(errMsg+": %v "+installmentID, errMsg)
		return err
	}

	statusUpdater := model.NewInstallmentUpdater().SetInstallmentStatus(model.InstallmentTerminated)
	newIns, err := srv.installmentRepo.UpdateInstallmentByField(ctx, installmentID, statusUpdater)
	if err != nil {
		errMsg := "TerminateInstallment: error while TERMINATING the installment"
		logx.Error().Err(err).Msgf(errMsg+": %v", err)
		return err
	}
	srv.insertAuditLog(ctx, newIns, *oldIns, actionBy)
	return nil
}

func (srv InstallmentServiceImpl) ActivateInstallment(ctx context.Context, installmentID string, actionBy string) error {
	oldIns, err := srv.installmentRepo.FindById(ctx, installmentID)
	if err != nil {
		errMsg := "ActivateInstallment find installment by id err"
		logx.Error().Err(err).Msgf(errMsg+": %v", err)
		return err
	}

	if oldIns.Status != model.InstallmentPending {
		errMsg := "cannot activate installment since installment status is not INSTALLMENT_PENDING"
		err := errors.New(errMsg)
		logx.Error().Err(err).Msgf(errMsg+": %v "+installmentID, errMsg)
		return err
	}

	statusUpdater := model.NewInstallmentUpdater().
		SetInstallmentStatus(model.InstallmentActive).
		SetExpectedDeliveryDate(timeutil.BangkokNow())
	newIns, err := srv.installmentRepo.UpdateInstallmentByField(ctx, installmentID, statusUpdater)
	if err != nil {
		errMsg := "ActivateInstallment: error while ACTIVATING the installment"
		logx.Error().Err(err).Msgf(errMsg+": %v", err)
		return err
	}
	srv.insertAuditLog(ctx, newIns, *oldIns, actionBy)
	return nil
}

func (srv InstallmentServiceImpl) CreateInstallment(ctx context.Context, installment *model.Installment) (*primitive.ObjectID, error) {
	return srv.installmentRepo.CreateWithResult(ctx, installment)
}

func (srv InstallmentServiceImpl) adjustOverdueAmount(ctx context.Context, ins model.Installment) error {
	if ins.OverdueAmount.GT(0) {
		// flat map (ALL PENDING) log
		logs := model.SortingInstallmentLogList{}
		logs.ConvertLogs([]model.Installment{ins})
		sort.Sort(logs)

		// rebalancing overdue installment
		if _, _, err := srv.driverTransactionService.ProcessDriverTransaction(
			ctx,
			ins.DriverID,
			model.SystemTransactionChannel,
			model.InstallmentCancellationTransactionAction,
			model.SuccessTransactionStatus,
			addCreditInstallmentCancellationTransactionInfosBuilder(ins.OverdueAmount, ins.ID.Hex()),
			WithInstallmentLogs(logs...),
		); err != nil && !errors.Is(err, ProcessDriverTransactionNoInfoError) {
			logx.Error().Msgf("doCancellationInstallment return wallet error: %v", err)
			return err
		}
	}
	return nil
}

func (srv InstallmentServiceImpl) insertAuditLog(ctx context.Context, ins model.Installment, oriIns model.Installment, actionBy string) {
	updateCancelInstallmentAuditLog := InstallmentAuditLog{
		before: oriIns,
		after:  ins,
	}
	srv.insertSingleInstallmentAuditLogAsync(ctx, actionBy, updateCancelInstallmentAuditLog, model.UpdateAction)
}

func (srv InstallmentServiceImpl) processDriverTransaction(ctx context.Context, ins model.Installment, returnAmount types.Money) error {
	if returnAmount.LTE(0) {
		return nil
	}
	builder := refundWalletInstallmentCancellationTransactionInfosBuilder(returnAmount, ins.ID.Hex())
	_, _, err := srv.driverTransactionService.ProcessDriverTransaction(
		ctx,
		ins.DriverID,
		model.SystemTransactionChannel,
		model.InstallmentCancellationTransactionAction,
		model.SuccessTransactionStatus,
		builder,
	)
	if err != nil && !errors.Is(err, ProcessDriverTransactionNoInfoError) {
		return err
	}
	return nil
}

func (srv InstallmentServiceImpl) updateInstallmentDetail(ctx context.Context, ins model.Installment) error {
	ins.UpdateSelf(timeutil.BangkokNow())
	ins.Product = nil
	ins.ProductGroup = nil
	ins.Status = model.InstallmentCancelled

	if ins.OverdueAmount.GT(0) {
		return errors.New("doCancellationInstallment rebalancing has something wrong, overdue amount is not equal to 0")
	}

	if err := srv.installmentRepo.Update(ctx, ins); err != nil {
		logx.Error().Msgf("InstallmentServiceImpl Update Installment Err: %v", err)
		return err
	}

	return nil
}

func refundWalletInstallmentCancellationTransactionInfosBuilder(amount types.Money, installmentID string) TransactionInfosBuilder {
	return func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
		var infos []model.TransactionInfo
		txnInfos, err := dt.RefundWalletInstallmentCancellation(amount, installmentID)
		if err != nil {
			logx.Error().Msgf("cannot generate refund wallet: %v, driver id %s", err, dt.DriverID)
			return nil, err
		}

		infos = append(infos, txnInfos...)
		return infos, nil
	}
}

func addCreditInstallmentCancellationTransactionInfosBuilder(amount types.Money, installmentID string) TransactionInfosBuilder {
	return func(dt model.DriverTransaction) ([]model.TransactionInfo, error) {
		var infos []model.TransactionInfo
		txnInfos, err := dt.AddCreditInstallmentCancellation(amount, installmentID)
		if err != nil {
			logrus.WithFields(logrus.Fields{
				"method": "addCreditInstallmentCancellationTransactionInfosBuilder",
			}).Errorf("cannot generate add credit: %v, driver id %s", err, dt.DriverID)
			return nil, err
		}

		infos = append(infos, txnInfos...)
		return infos, nil
	}
}

func (srv InstallmentServiceImpl) insertSingleInstallmentAuditLogAsync(ctx context.Context, admin string, log InstallmentAuditLog, action model.AuditAction) {

	safe.GoFuncWithCtx(ctx, func() {
		id := "n/a"
		var before *model.Installment = nil
		if action == model.UpdateAction {
			id = log.before.ID.Hex()
			before = &log.before
		}

		auditLog := &model.AuditLog{
			Object: model.AuditObject{
				ObjectType: model.InstallmentObject,
				ID:         id,
			},
			Actor: model.AuditLogActor{
				ID: admin,
			},
			Timestamp: timeutil.BangkokNow(),
			Action:    action,
			Before:    before,
			After:     log.after,
		}

		if err := srv.auditLogRepo.Insert(ctx, auditLog); err != nil {
			logx.Error().Msgf("cannot insert auditlog of installment: %s", err)
		}
	})
}

func CalculateTotalOverdueAndActualAmount(installmentLogs []model.InstallmentLog) (types.Money, types.Money, int) {
	totalOverdue, totalActualAmount, totalAmount := types.Money(0.0), types.Money(0.0), types.Money(0.0)
	daysPastDue := 0
	for _, installmentLog := range installmentLogs {
		totalAmount = totalAmount.Add(installmentLog.Amount)

		if installmentLog.InstallmentPaymentStatus == model.InstallmentPaymentStatusPending {
			daysPastDue += 1
		}

		for _, actualAmountLog := range installmentLog.ActualAmountLogs {
			totalActualAmount = totalActualAmount.Add(actualAmountLog.ActualAmount)
		}
	}
	totalOverdue = totalAmount.Sub(totalActualAmount)
	return totalOverdue, totalActualAmount, daysPastDue
}

func (src InstallmentServiceImpl) validateChargeOnetimeInstallment(ins model.Installment) error {
	if ins.PaymentType != model.InstallmentPaymentTypeOneTime {
		return ErrInvalidInstallmentPaymentType
	}

	if ins.Status != model.InstallmentPending && !ins.IsCompleted() {
		return ErrInvalidInstallmentStatus
	}

	return nil
}

func (srv InstallmentServiceImpl) ChargeOnetimeInstallment(ctx context.Context, ins model.Installment, isLastAttempt bool) error {
	currentTime := timeutil.BangkokNow()
	if err := srv.validateChargeOnetimeInstallment(ins); err != nil {
		return err
	}

	if ins.IsCompleted() {
		logx.Warn().Context(ctx).
			Str("method", "ChargeOnetimePaymentOrder").
			Msgf("try to charge onetime installment id [%s] with completed status", ins.ID.Hex())
		return nil
	}

	if len(ins.InstallmentLogs) == 0 {
		ins.InstallmentLogs = append(ins.InstallmentLogs, model.InstallmentLog{
			Amount:                   ins.InitialAmount,
			InstallmentPayment:       len(ins.InstallmentLogs) + 1,
			InstallmentPaymentStatus: model.InstallmentPaymentStatusPending,
			CreatedAt:                currentTime,
			TransRefID:               "", // fill later
			IsDeductionLog:           true,
		})
	}

	if err := srv.doProcessDriverTransactionToChargeOnetimeInstallment(ctx, ins, currentTime, isLastAttempt); err != nil {
		return err
	}

	// Charge Successful
	ins.Start = timeutil.DateTruncate(currentTime)
	ins.End = ins.Start
	ins.DailyAmount = ins.InitialAmount
	ins.UpdateSelf(currentTime)
	if err := srv.installmentRepo.Update(ctx, ins); err != nil {
		logx.Error().Err(err).Context(ctx).
			Str("method", "ChargeOnetimeInstallment").
			Msgf("unable to update an installment id [%s]", ins.ID.Hex())
		return err
	}
	return nil
}

func (srv InstallmentServiceImpl) doProcessDriverTransactionToChargeOnetimeInstallment(ctx context.Context, ins model.Installment, processingTime time.Time, isLastAttempt bool) error {
	logs := model.SortingInstallmentLogList{}
	logs.ConvertLogs([]model.Installment{ins})
	sort.Sort(logs)
	_, _, processTxnErr := srv.driverTransactionService.ProcessDriverTransaction(
		ctx,
		ins.DriverID,
		model.SystemTransactionChannel,
		model.InstallmentDeductionTransactionAction,
		model.SuccessTransactionStatus,
		OnetimeInstallmentDeductionTransactionInfosBuilder(logs, processingTime),
		WithInstallmentLogs(logs...),
	)

	if processTxnErr != nil {
		// Charge Fail
		remark := "unable to charge driver"
		if !errors.Is(processTxnErr, ErrNotEnoughBalanceToChargeOnetimePaymentInstallment) {
			logx.Error().Err(processTxnErr).Context(ctx).
				Str("method", "ChargeOnetimeInstallment").
				Msgf("unable to ProcessDriverTransaction for an installment id [%s]", ins.ID.Hex())
			remark += " due to sytem"
		}
		ins.UpdatedAt = processingTime
		ins.Remarks = append(ins.Remarks, model.InstallmentRemark{
			Remark:    remark,
			CreatedAt: processingTime,
			CreatedBy: "SYSTEM",
		})
		if isLastAttempt {
			if len(logs) == 1 && logs[0].Log != nil {
				logs[0].Log.InstallmentPaymentStatus = model.InstallmentPaymentStatusFailed
			}
			ins.Status = model.InstallmentCancelled
		}

		if err := srv.installmentRepo.Update(ctx, ins); err != nil {
			logx.Error().Err(err).Context(ctx).
				Str("method", "ChargeOnetimeInstallment").
				Msgf("unable to update an installment id [%s]", ins.ID.Hex())
			return err
		}
	}
	return processTxnErr
}

func (svc InstallmentServiceImpl) IsDriverOnEGSOrderCooldown(ctx context.Context, driverID string) (isValid bool) {
	if svc.cfg.InstallmentDBConfig.Get().IsEGSCooldownDisabled {
		return false
	}

	installments, err := svc.installmentRepo.FindLatestOrderCooldownInstallment(ctx, driverID, 0, 2)
	if err != nil {
		logx.Error().Err(err).Msgf("Filter Out EGS Banner FindLatestOrderCooldownInstallment %s, err: %v", driverID, err)
		return
	}

	now := timeutils.Now()
	if len(installments) <= 0 {
		return false
	}
	if installments[0].Status == model.InstallmentPending {
		return true
	} else {
		return now.Before(installments[0].Start.AddDate(0, 0, svc.cfg.InstallmentDBConfig.Get().EGSOrderCooldownDays))
	}
}

func AttachBatchTypeFromProductMapToInstallmentsFunc(products map[string]*model.Product) func(installments []model.Installment) {
	return func(installments []model.Installment) {
		AttachBatchTypeFromProductMapToInstallments(installments, products)
	}
}

func AttachBatchTypeFromProductMapToInstallments(installments []model.Installment, products map[string]*model.Product) {
	for i, ins := range installments {
		product, productExists := products[ins.SKU]

		var batchType = model.InstallmentProductBatchTypeUnspecified // default
		if productExists {
			batchType = product.BatchType
		}
		installments[i].ProductBatchType = batchType
	}
}

func InventoryProductWithPriorityGroupToProductGroup(raw *inventoryPb.ProductWithPriorityGroup) *model.ProductGroup {
	return convertGRPCToProductGroup(raw.PriorityGroup)
}

func InventoryProductWithPriorityGroupToProduct(raw *inventoryPb.ProductWithPriorityGroup) *model.Product {
	return convertGRPCToProduct(raw.Product)
}

func InventoryProductWithPriorityGroupToSKU(product *inventoryPb.ProductWithPriorityGroup) string {
	return product.Product.Sku
}

func InventoryProductWithPriorityGroupToPriorityGroup(product *inventoryPb.ProductWithPriorityGroup) string {
	return product.PriorityGroup.PriorityGroupId
}

type InstallmentResult struct {
	Installment model.Installment
	Error       error
}

func (svc InstallmentServiceImpl) UpdateInstallmentsProductDetail(ctx context.Context, updaters ...InstallmentProductDetailUpdater) ([]model.Installment, error) {
	updatedInstallments := make([]model.Installment, 0)

	_, txnErr := svc.txnHelper.WithTxn(ctx, func(sessCtx context.Context) (interface{}, error) {
		for _, updater := range updaters {
			updatedInstalment, err := svc.doUpdateInstallmentsProductDetail(sessCtx, updater)
			if err != nil {
				logx.Error().Err(err).
					Str("method", "UpdateInstallmentsProductDetail").
					Msgf("unable to update product detail for installment [%s] within transaction", updater.InstallmentID)
				return nil, err
			}

			updatedInstallments = append(updatedInstallments, updatedInstalment)
		}
		return nil, nil
	}, transaction.WithLabel("InstallmentServiceImpl.UpdateInstallmentsProductDetail"))

	if txnErr != nil {
		return nil, txnErr
	}
	return updatedInstallments, nil
}

// doUpdateInstallmentsProductDetail - update Installment's product detail and its principal price
func (svc InstallmentServiceImpl) doUpdateInstallmentsProductDetail(ctx context.Context, updater InstallmentProductDetailUpdater) (model.Installment, error) {
	productDetailUpdater := model.NewInstallmentUpdater().
		SetProductDetailStockPrice(updater.NewProductDetail)

	updatedInstallment, err := svc.installmentRepo.FindAndUpdateInstallmentProductDetail(ctx, updater.InstallmentID, updater.StockSKU, updater.StockID, productDetailUpdater)
	if err != nil {
		logx.Error().Err(err).
			Msgf("unable to update product detail for installment [%s]", updater.InstallmentID)
		return model.Installment{}, err
	}

	var newPrincipalPrice types.Money
	for _, productDetail := range updatedInstallment.ProductDetails {
		newPrincipalPrice = newPrincipalPrice.Add(productDetail.PrincipalPrice)
	}

	principalPriceUpdater := model.NewInstallmentUpdater().SetPrincipalPrice(newPrincipalPrice)
	updatedInstallment, err = svc.installmentRepo.UpdateInstallmentByField(ctx, updater.InstallmentID, principalPriceUpdater)
	if err != nil {
		logx.Error().Err(err).
			Msgf("unable to update principal price for installment [%s]", updater.InstallmentID)
		return model.Installment{}, err
	}
	return updatedInstallment, nil
}

func (svc InstallmentServiceImpl) ForceUpdateInstallmentsProductDetail(ctx context.Context, requester string, updater model.InstallmentProductDetailForceUpdater) error {

	installment, err := svc.installmentRepo.FindById(ctx, updater.InstallmentID)
	if err != nil {
		logx.Error().Err(err).Str("InstallmentID", updater.InstallmentID).Msg("unable to get installment")
		return err
	}

	updatedInstallment, err := svc.installmentRepo.FindAndForceUpdateInstallmentProductDetail(ctx, updater, repository.WithReadPrimary)
	if err != nil {
		logx.Error().Err(err).
			Msgf("unable to update product detail for installment [%s]", updater.InstallmentID)
		return err
	}

	svc.insertAuditLog(ctx, updatedInstallment, *installment, requester)

	return nil
}

func (svc InstallmentServiceImpl) doResetEtaxInstallment(ctx context.Context, requester string, installmentID string, resetEtaxStrategy model.ResetEtaxInstallmentStrategy) error {
	return nil
}
