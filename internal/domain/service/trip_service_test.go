package service_test

import (
	"context"
	"fmt"
	"math"
	"testing"
	"time"

	legacygomock "github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/mock/gomock"

	"git.wndv.co/lineman/absinthe/crypt"
	"git.wndv.co/lineman/absinthe/database/v2/transaction/mock_transaction"
	lmdelivery "git.wndv.co/lineman/delivery-service/pkg/client"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery/mock_delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/mock_mapservice"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/rep/mock_rep"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	domainModel "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/drivertransaction/mock_drivertransaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/mock_service"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag/mock_featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric/testmetric"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
	"git.wndv.co/lineman/fleet-distribution/internal/types"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/stringutil"
	"git.wndv.co/lineman/fleet-distribution/internal/utils/timeutil"
	"git.wndv.co/lineman/fleet-distribution/toggle"
)

func TestRecalculateTripDeliveryFee(t *testing.T) {
	t.Run("should recalculate trip delivery fee for orders that are not cancelled", func(t *testing.T) {
		tr, deps, finish := newTestTripService(t)
		defer finish()

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Routes: model.TripRoutes{
				{
					Location: model.Location{Lat: 10, Lng: 11},
					Distance: 1.0,
					Action:   model.TripActionPickUp,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-1",
							StopID:  0,
							Done:    false,
						}, {
							OrderID: "order-2",
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					Location: model.Location{Lat: 12, Lng: 13},
					Distance: -1,
					Action:   model.TripActionDropOff,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-1",
							StopID:  1,
							Done:    false,
						},
					},
				},
				{
					Location: model.Location{Lat: 14, Lng: 15},
					Distance: -1,
					Action:   model.TripActionDropOff,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-2",
							StopID:  1,
							Done:    false,
						},
					},
				},
			},
			Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					Status:      model.StatusCanceled,
					ServiceType: model.ServiceFood,
				},
				{
					OrderID:     "order-2",
					Status:      model.StatusRestaurantAccepted,
					ServiceType: model.ServiceMart,
				},
			},
		}

		deps.orderRepo.EXPECT().
			GetMany(gomock.Any(), []string{"order-2"}).
			Return([]model.Order{
				{
					OrderID: "order-2",
					Region:  "BKK",
					Quote: model.Quote{
						Routes:            []model.Stop{{}, {}},
						ServiceType:       model.ServiceMart,
						DistributeRegions: model.DistributeRegions{"BKK"},
					},
				},
			}, nil)
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), model.ServiceMart, "BKK", gomock.Any(), gomock.Any()).
			Return(nil, *model.NewSettingDeliveryFeePriceScheme("tan", 0, model.RoadFeeTiers{}, []float64{1, 0.5, 0.33}, 10, 0), nil)

		err := tr.RecalculateTripDeliveryFee(context.Background(), &trip)
		require.NoError(t, err)
	})

	t.Run("should record wage history correctly", func(t *testing.T) {
		tr, deps, finish := newTestTripService(t)
		defer finish()

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Routes: model.TripRoutes{
				{
					Location: model.Location{Lat: 10, Lng: 11},
					Distance: 1.0,
					Action:   model.TripActionPickUp,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-1",
							StopID:  0,
							Done:    false,
						}, {
							OrderID: "order-2",
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					Location: model.Location{Lat: 12, Lng: 13},
					Distance: -1,
					Action:   model.TripActionDropOff,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-1",
							StopID:  1,
							Done:    false,
						},
					},
				},
				{
					Location: model.Location{Lat: 14, Lng: 15},
					Distance: -1,
					Action:   model.TripActionDropOff,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-2",
							StopID:  1,
							Done:    false,
						},
					},
				},
			},
			Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					Status:      model.StatusRestaurantAccepted,
					ServiceType: model.ServiceFood,
				},
				{
					OrderID:     "order-2",
					Status:      model.StatusRestaurantAccepted,
					ServiceType: model.ServiceMart,
				},
			},
		}

		deps.orderRepo.EXPECT().
			GetMany(gomock.Any(), gomock.Any()).
			Return([]model.Order{
				{
					OrderID: "order-1",
					Region:  "BKK",
					Quote: model.Quote{
						Routes:            []model.Stop{{}, {}},
						ServiceType:       model.ServiceFood,
						DistributeRegions: model.DistributeRegions{"BKK"},
					},
				}, {
					OrderID: "order-2",
					Region:  "BKK",
					Quote: model.Quote{
						Routes:            []model.Stop{{}, {}},
						ServiceType:       model.ServiceMart,
						DistributeRegions: model.DistributeRegions{"BKK"},
					},
				},
			}, nil).AnyTimes()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, *model.NewSettingDeliveryFeePriceScheme("tan", 0, model.RoadFeeTiers{}, []float64{1, 0.5, 0.33}, 10, 0), nil).AnyTimes()

		err := tr.RecalculateTripDeliveryFee(context.Background(), &trip, service.RecalAction("create"))
		require.NoError(t, err)
		require.Len(t, trip.DriverWageHistory, 1)
		require.ElementsMatch(t, trip.DriverWageHistory[0].OrderIDs, []string{"order-1", "order-2"})
		require.EqualValues(t, trip.DriverWageSummary, trip.DriverWageHistory[0].Summary)
		require.Equal(t, "create", trip.DriverWageHistory[0].Action)

		trip.Routes[1].Distance = 5
		err = tr.RecalculateTripDeliveryFee(context.Background(), &trip)
		require.NoError(t, err)
		require.Len(t, trip.DriverWageHistory, 2)
		require.EqualValues(t, trip.DriverWageSummary, trip.DriverWageHistory[1].Summary)
		require.Equal(t, "", trip.DriverWageHistory[1].Action)
		err = tr.RecalculateTripDeliveryFee(context.Background(), &trip)
		require.NoError(t, err)
		require.Len(t, trip.DriverWageHistory, 3)

		trip.Orders[1].Status = model.StatusCanceled
		trip.Routes[0].StopOrders = trip.Routes[0].StopOrders[:1]
		trip.Routes = trip.Routes[:2]
		err = tr.RecalculateTripDeliveryFee(context.Background(), &trip, service.RecalInmemOrders(model.Order{
			OrderID: "order-1",
			Region:  "BKK",
			Quote: model.Quote{
				Routes:            []model.Stop{{}, {}},
				ServiceType:       model.ServiceFood,
				DistributeRegions: model.DistributeRegions{"BKK"},
			},
		}))
		require.NoError(t, err)
		require.Len(t, trip.DriverWageHistory, 4)
		require.ElementsMatch(t, trip.DriverWageHistory[3].OrderIDs, []string{"order-1"})
		require.EqualValues(t, trip.DriverWageSummary, trip.DriverWageHistory[3].Summary)
	})

	t.Run("should record wage history correctly - messenger order", func(t *testing.T) {
		tr, deps, finish := newTestTripService(t)
		defer finish()

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Routes: model.TripRoutes{
				{
					Location: model.Location{Lat: 10, Lng: 11},
					Distance: 1.0,
					Action:   model.TripActionPickUp,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-1",
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					Location: model.Location{Lat: 12, Lng: 13},
					Distance: -1,
					Action:   model.TripActionDropOff,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-1",
							StopID:  1,
							Done:    false,
						},
					},
				},
			},
			Orders: model.TripOrders{
				{
					OrderID:     "order-1",
					Status:      model.StatusOnGoing,
					ServiceType: model.ServiceMessenger,
				},
			},
		}

		deps.orderRepo.EXPECT().
			GetMany(gomock.Any(), gomock.Any()).
			Return([]model.Order{
				{
					OrderID: "order-1",
					Region:  "BKK",
					Quote: model.Quote{
						Routes: []model.Stop{{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{
									AdditionalServiceFee: model.AdditionalServiceSummary{
										Total:      20,
										Commission: 3,
									},
								},
								Total: 0,
							},
						}, {}},
						ServiceType:       model.ServiceMessenger,
						DistributeRegions: model.DistributeRegions{"BKK"},
					},
				},
			}, nil).AnyTimes()
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, *model.NewSettingDeliveryFeePriceScheme("tan", 0, model.RoadFeeTiers{}, []float64{1, 0.5, 0.33}, 10, 0), nil).AnyTimes()
		err := tr.RecalculateTripDeliveryFee(context.Background(), &trip, service.RecalAction("CREATE_TRIP"))
		require.NoError(t, err)
		require.Len(t, trip.DriverWageHistory, 1)
		require.ElementsMatch(t, trip.DriverWageHistory[0].OrderIDs, []string{"order-1"})
		require.EqualValues(t, trip.DriverWageSummary, trip.DriverWageHistory[0].Summary)
		require.Equal(t, "CREATE_TRIP", trip.DriverWageHistory[0].Action)
	})
}

func TestCalculateMOSaving(t *testing.T) {
	t.Run("should recalculate mo savings for orders for active orders", func(t *testing.T) {
		tr, deps, finish := newTestTripService(t)
		defer finish()

		trip := model.Trip{
			DriverWageSummary: model.TripDriverWageSummary{
				BaseWage:        30,
				DistanceWage:    4.43,
				ShiftDeduction:  5,
				TotalDriverWage: 29.43,
			},
			Orders: []model.TripOrder{
				{OrderID: "order-1", Status: model.StatusCompleted},
				{OrderID: "order-2", Status: model.StatusCompleted},
			},
		}

		orders := []model.Order{
			{OrderID: "order-1", Quote: model.Quote{Routes: []model.Stop{{}, {PriceSummary: model.PriceSummary{DeliveryFee: model.DeliveryFeeSummary{RawBaseFee: 22.95}}}}, PayAtStop: 1}, Status: model.StatusCompleted},
			{OrderID: "order-2", Quote: model.Quote{Routes: []model.Stop{{}, {PriceSummary: model.PriceSummary{DeliveryFee: model.DeliveryFeeSummary{RawBaseFee: 22.95}}}}, PayAtStop: 1}, Status: model.StatusCompleted},
		}

		deps.orderRepo.EXPECT().SetMOSaving(gomock.Any(), gomock.Any(), 5.74)
		deps.orderRepo.EXPECT().SetMOSaving(gomock.Any(), gomock.Any(), 5.73)

		err := tr.CalculateMOSaving(context.Background(), &trip, orders)
		require.NoError(t, err)
	})

	t.Run("should not recalculate mo savings when there are just 1 active order", func(t *testing.T) {
		tr, _, finish := newTestTripService(t)
		defer finish()

		trip := model.Trip{
			DriverWageSummary: model.TripDriverWageSummary{
				BaseWage:     18.95,
				DistanceWage: 4,
			},
			Orders: []model.TripOrder{
				{OrderID: "order-1", Status: model.StatusCompleted},
				{OrderID: "order-2", Status: model.StatusCanceled},
			},
		}

		orders := []model.Order{
			{OrderID: "order-1", Quote: model.Quote{Routes: []model.Stop{{}, {PriceSummary: model.PriceSummary{DeliveryFee: model.DeliveryFeeSummary{RawBaseFee: 22.95}}}}, PayAtStop: 1}, Status: model.StatusCompleted},
		}

		err := tr.CalculateMOSaving(context.Background(), &trip, orders)
		require.NoError(t, err)
	})
}

func TestTripService_CalculateTripRoutes(t *testing.T) {
	t.Run("should not calculate new routes for trip if there are empty routes", func(tt *testing.T) {
		tr, _, finish := newTestTripService(tt)
		defer finish()

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Routes: model.TripRoutes{},
			Orders: model.TripOrders{
				{
					OrderID: "order-1",
					Status:  model.StatusCanceled,
				},
				{
					OrderID: "order-2",
					Status:  model.StatusCanceled,
				},
			},
		}

		_, err := tr.CalculateTripRoutes(context.Background(), &trip)
		require.NoError(tt, err)
	})

	t.Run("should calculate new routes for trip and still keep distance of trip route[0]", func(t *testing.T) {
		tr, deps, finish := newTestTripService(t)
		defer finish()

		expectedDistances := []types.Distance{1.0, 2.0, 3.0}
		expectedDeliveryTime := []model.DurationSecond{10, 20, 30}

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Routes: model.TripRoutes{
				{
					Location:              model.Location{Lat: 10, Lng: 11},
					Distance:              1.0,
					EstimatedDeliveryTime: 10,
					Action:                model.TripActionPickUp,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-1",
							StopID:  0,
							Done:    false,
						}, {
							OrderID: "order-2",
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					Location: model.Location{Lat: 12, Lng: 13},
					Distance: -1,
					Action:   model.TripActionDropOff,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-1",
							StopID:  1,
							Done:    false,
						},
					},
				},
				{
					Location: model.Location{Lat: 14, Lng: 15},
					Distance: -1,
					Action:   model.TripActionDropOff,
					StopOrders: model.TripStopOrders{
						{
							OrderID: "order-2",
							StopID:  1,
							Done:    false,
						},
					},
				},
			},
			Orders: model.TripOrders{
				{
					OrderID: "order-1",
					Status:  model.StatusRestaurantAccepted,
				},
				{
					OrderID: "order-2",
					Status:  model.StatusRestaurantAccepted,
				},
			},
		}

		deps.mapSvc.EXPECT().
			FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 10, Lng: 11}, mapservice.Location{Lat: 12, Lng: 13}, gomock.Any()).
			Return(&model.MapRoute{
				Distance: 2,
				Duration: 20.0,
			}, nil, nil)
		deps.mapSvc.EXPECT().
			FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 12, Lng: 13}, mapservice.Location{Lat: 14, Lng: 15}, gomock.Any()).
			Return(&model.MapRoute{
				Distance: 3,
				Duration: 30.0,
			}, nil, nil)

		newRoutes, err := tr.CalculateTripRoutes(context.Background(), &trip)
		require.NoError(t, err)

		for i, route := range newRoutes {
			require.Equal(t, expectedDistances[i], types.Distance(route.Distance))
			require.Equal(t, expectedDeliveryTime[i], model.DurationSecond(route.Duration))
		}
	})
}

func TestTripService_GetWithOrders(t *testing.T) {
	t.Parallel()

	ctx := context.Background()
	driverID := "<driver-id>"

	genSingleOrderTrips := func(startId, num int) []model.Trip {
		var trips []model.Trip
		for i := startId; i < startId+num; i++ {
			trips = append(
				trips,
				model.Trip{
					TripID: fmt.Sprintf("trip-%v", i),
					Orders: []model.TripOrder{
						{OrderID: fmt.Sprintf("order-%v", i)},
					},
					Routes: []model.TripRoute{{ID: "route-1"}, {ID: "route-2"}},
				})
		}
		return trips
	}

	genOrders := func(startId, num int) ([]model.Order, []string) {
		var orders []model.Order
		var orderIDs []string
		for i := startId; i < startId+num; i++ {
			order := model.Order{
				OrderID: fmt.Sprintf("order-%v", i),
				Quote: model.Quote{
					Routes: []model.Stop{{ID: "route-1"}, {ID: "route-2"}},
				},
				Status: model.StatusCompleted,
			}
			orders = append(orders, order)
			orderIDs = append(orderIDs, order.OrderID)
		}
		return orders, orderIDs
	}

	t.Run("get trips and related orders successfully (only single trips)", func(tt *testing.T) {
		tt.Parallel()

		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()
		query := persistence.TripQuery{
			DriverID: driverID,
			Status:   []model.TripStatus{model.TripStatusCompleted, model.TripStatusCanceled},
		}

		deps.tripRepo.EXPECT().
			FindWithQueryAndSort(ctx, query.Query(), 0, 10, gomock.Any()).
			Return(genSingleOrderTrips(0, 10), nil)

		orders, orderIDs := genOrders(0, 10)
		orderQuery := repository.NewOrderRevisionQuery().WithDriverID(driverID).WithOrderIDs(orderIDs)
		deps.orderRepo.EXPECT().
			FindRevision(ctx, orderQuery, 0, 0, gomock.Any()).
			Return(orders, nil)

		trips, orders, err := underTest.GetWithOrders(ctx, driverID, "", 0, 10)

		require.Nil(tt, err)
		require.Len(tt, trips, 10)
		require.Len(tt, orders, 10)
		for i := 0; i < 10; i++ {
			require.Equal(tt, fmt.Sprintf("trip-%v", i), trips[i].TripID)
		}
	})
}

func TestTripService_OnOrderCanceled(t *testing.T) {
	t.Run("cancel every order when driver got temp ban", func(t *testing.T) {
		toggle.Enable(toggle.TripEnabled)
		tr, deps, finish := newTestTripService(t)

		defer finish()

		defer toggle.DisableAll()
		tripID := "trip-id"
		orderID := "order-1"

		trip := model.Trip{
			TripID: tripID,
			Routes: model.TripRoutes{
				{
					Action: model.TripActionPickUp,
					StopOrders: model.TripStopOrders{
						{
							OrderID: orderID,
							StopID:  0,
							Done:    false,
						},
					},
				},
				{
					Action: model.TripActionDropOff,
					StopOrders: model.TripStopOrders{
						{
							OrderID: orderID,
							StopID:  1,
							Done:    false,
						},
					},
				},
			},
			Orders: model.TripOrders{
				{
					OrderID: orderID,
					Status:  model.StatusRestaurantAccepted,
				},
			},
		}

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(trip, nil)
		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		trip, err := tr.OnOrderCanceled(context.Background(), service.TripOrderCanceledEvent{
			TripID:  tripID,
			OrderID: orderID,
		})
		require.NoError(t, err)
		require.Equal(t, model.TripStatusCanceled, trip.Status)
	})
}

func TestOnUpdateDeliveryLocation(t *testing.T) {
	tripID := "TRIP-TEST"
	driverID := "LMD-TEST"
	event := service.TripOrderUpdateLocationEvent{
		TripID:                tripID,
		OrderID:               "order-1",
		OrderStopChangedIndex: 1,
		OrderLocation:         model.Location{Lat: 21, Lng: 22},
	}
	trip := model.Trip{
		TripID:   tripID,
		DriverID: driverID,
		Routes: model.TripRoutes{
			{
				Location: model.Location{Lat: 10, Lng: 11},
				Distance: 1.0,
				Action:   model.TripActionPickUp,
				StopOrders: model.TripStopOrders{
					{
						OrderID: "order-1",
						StopID:  0,
						Done:    false,
					}, {
						OrderID: "order-2",
						StopID:  0,
						Done:    false,
					},
				},
			},
			{
				Location: model.Location{Lat: 12, Lng: 13},
				Distance: 2.0,
				Action:   model.TripActionDropOff,
				StopOrders: model.TripStopOrders{
					{
						OrderID: "order-1",
						StopID:  1,
						Done:    false,
					},
				},
			},
			{
				Location: model.Location{Lat: 14, Lng: 15},
				Distance: 3.0,
				Action:   model.TripActionDropOff,
				StopOrders: model.TripStopOrders{
					{
						OrderID: "order-2",
						StopID:  1,
						Done:    false,
					},
				},
			},
		},
		Orders: model.TripOrders{
			{
				OrderID:     "order-1",
				Status:      model.StatusRestaurantAccepted,
				ServiceType: model.ServiceFood,
			},
			{
				OrderID:     "order-2",
				Status:      model.StatusRestaurantAccepted,
				ServiceType: model.ServiceFood,
			},
		},
		RevenuePrincipalModel: true,
	}

	expectedTrip := model.Trip{
		Routes: model.TripRoutes{
			{
				Location: model.Location{Lat: 10, Lng: 11},
				Distance: 1.0,
			},
			{
				Location: model.Location{Lat: 21, Lng: 22},
				Distance: 7.0,
			},
			{
				Location: model.Location{Lat: 14, Lng: 15},
				Distance: 9.0,
			},
		},
		DriverWageSummary: model.TripDriverWageSummary{
			BaseWage:        20,
			DistanceWage:    0,
			ShiftDeduction:  0,
			TotalDriverWage: 20,
			Commission:      0,
			WithHoldingTax:  0.6,
		},
		RevenuePrincipalModel: true,
	}

	tr, deps, finish := newTestTripService(t)
	defer finish()

	deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
	deps.tripRepo.EXPECT().
		GetTripByTripID(gomock.Any(), tripID).Return(trip, nil)
	deps.mapSvc.EXPECT().
		FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 10, Lng: 11}, mapservice.Location{Lat: 21, Lng: 22}, gomock.Any()).
		Return(&model.MapRoute{Distance: 7.0}, nil, nil)
	deps.mapSvc.EXPECT().
		FindFastestRoute(gomock.Any(), mapservice.Location{Lat: 21, Lng: 22}, mapservice.Location{Lat: 14, Lng: 15}, gomock.Any()).
		Return(&model.MapRoute{Distance: 9.0}, nil, nil)
	deps.orderRepo.EXPECT().
		GetMany(gomock.Any(), []string{"order-1", "order-2"}).
		Return([]model.Order{
			{
				OrderID: "order-1",
				Region:  "BKK",
				Quote: model.Quote{
					Routes:            []model.Stop{{}, {}},
					ServiceType:       model.ServiceFood,
					DistributeRegions: model.DistributeRegions{"BKK"},
				},
			},
			{
				OrderID: "order-2",
				Region:  "BKK",
				Quote: model.Quote{
					Routes:            []model.Stop{{}, {}},
					ServiceType:       model.ServiceFood,
					DistributeRegions: model.DistributeRegions{"BKK"},
				},
			},
		}, nil)
	deps.deliveryFeeSvc.EXPECT().
		GetDeliveryFeeCalculator(gomock.Any(), model.ServiceFood, "BKK", gomock.Any(), gomock.Any()).
		Return(nil, *model.NewSettingDeliveryFeePriceScheme("test", 0, model.RoadFeeTiers{}, []float64{1, 0.5, 0.33}, 10, 0), nil).Times(2)
	deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

	ctx := toggle.EnableCtx(context.Background(), toggle.TripEnabled)
	actualTrip, err := tr.OnUpdateDeliveryLocation(ctx, event)
	require.NoError(t, err)
	require.Equal(t, expectedTrip.Routes[0].Location, actualTrip.Routes[0].Location)
	require.Equal(t, expectedTrip.Routes[0].Distance, actualTrip.Routes[0].Distance)
	require.Equal(t, expectedTrip.Routes[1].Location, actualTrip.Routes[1].Location)
	require.Equal(t, expectedTrip.Routes[1].Distance, actualTrip.Routes[1].Distance)
	require.Equal(t, expectedTrip.Routes[2].Location, actualTrip.Routes[2].Location)
	require.Equal(t, expectedTrip.Routes[2].Distance, actualTrip.Routes[2].Distance)
	require.Equal(t, expectedTrip.DriverWageSummary, actualTrip.DriverWageSummary)
}

func TestTripService_SetEarning(t *testing.T) {
	t.Run("success set earning", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		orders := []model.Order{
			{
				Status: model.StatusCompleted,

				Quote: model.Quote{
					Routes: []model.Stop{
						{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{
									OnTopFare: 20,
								},
							},
						},
					},
				},
			},
			{
				Status: model.StatusCompleted,
				Quote: model.Quote{
					Routes: []model.Stop{
						{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{
									OnTopFare: 10,
								},
							},
						},
					},
				},
			},
		}

		dw := model.TripDriverWageSummary{
			TotalDriverWage: 4,
			Commission:      3,
			WithHoldingTax:  2,
		}

		en := model.DriverEarning{
			DriverID:   "driverID",
			Fee:        10,
			Commission: 10,
			Tax:        10,
			Tip:        10,
		}
		deps.driverRepo.EXPECT().GetTodayEarning(gomock.Any(), gomock.Any()).Return(en, nil)

		deps.driverRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ne ...model.DriverEarning) error {
			require.Equal(t, 1, len(ne))
			require.Equal(t, "driverID", ne[0].DriverID)
			require.Equal(t, types.Money(44), ne[0].Fee) // TotalDriverWage + ontop + fee
			require.Equal(t, types.Money(13), ne[0].Commission)
			require.Equal(t, types.Money(12), ne[0].Tax)
			require.Equal(t, types.Money(10), ne[0].Tip)
			return nil
		})

		err := underTest.SetEarning(context.Background(), dw, "driverID", orders)

		require.Nil(t, err)
	})

	t.Run("not return nil if set today earning error", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		var orders []model.Order
		dw := model.TripDriverWageSummary{
			TotalDriverWage: 4,
			Commission:      3,
			WithHoldingTax:  2,
		}

		en := model.DriverEarning{
			DriverID:   "driverID",
			Fee:        10,
			Commission: 10,
			Tax:        10,
			Tip:        10,
		}
		deps.driverRepo.EXPECT().GetTodayEarning(gomock.Any(), gomock.Any()).Return(en, nil)

		deps.driverRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).Return(fmt.Errorf("something wrong"))
		err := underTest.SetEarning(context.Background(), dw, "driverID", orders)
		require.Nil(t, err)
	})

	t.Run("success set earning with tip", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		orders := []model.Order{
			{
				Status: model.StatusCompleted,

				Quote: model.Quote{
					Routes: []model.Stop{
						{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{
									OnTopFare: 20,
								},
							},
						},
					},
				},

				Tips: model.TipRecords{
					{
						ID:          "ONGOING_TIP_ID",
						Amount:      10,
						OrderStatus: "DRIVER_TO_DESTINATION",
						CreatedAt:   timeutil.BangkokNow(),
					},
					{
						ID:          "COMPLETED_TIP_ID",
						Amount:      20,
						OrderStatus: "COMPLETED",
						CreatedAt:   timeutil.BangkokNow(),
					},
				},
			},
		}

		dw := model.TripDriverWageSummary{
			TotalDriverWage: 4,
			Commission:      3,
			WithHoldingTax:  2,
		}

		en := model.DriverEarning{
			DriverID:   "driverID",
			Fee:        10,
			Commission: 10,
			Tax:        10,
			Tip:        10,
		}
		deps.driverRepo.EXPECT().GetTodayEarning(gomock.Any(), gomock.Any()).Return(en, nil)

		deps.driverRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ne ...model.DriverEarning) error {
			require.Equal(t, 1, len(ne))
			require.Equal(t, "driverID", ne[0].DriverID)
			require.Equal(t, types.Money(34), ne[0].Fee) // TotalDriverWage + ontop + fee
			require.Equal(t, types.Money(13), ne[0].Commission)
			require.Equal(t, types.Money(12), ne[0].Tax)
			require.Equal(t, types.Money(20), ne[0].Tip)
			return nil
		})

		err := underTest.SetEarning(context.Background(), dw, "driverID", orders)

		require.Nil(t, err)
	})
}

func TestTripService_SyncTripOrderStatus(t *testing.T) {
	t.Run("sync to lm-delivery with flag `suspended = false when trip head to the order stop", func(tt *testing.T) {
		svc, deps, cleanup := newTestTripService(tt)
		defer cleanup()
		ctx := context.Background()
		tripID := "trip-1"
		deps.tripRepo.EXPECT().GetTripByTripID(ctx, tripID).Return(
			model.Trip{
				TripID: tripID,
				Routes: []model.TripRoute{
					{
						StopOrders: model.TripStopOrders{{OrderID: "LMF-1", StopID: 0, Done: true}, {OrderID: "LMF-2", StopID: 0, Done: true}},
					},
					{
						StopOrders: model.TripStopOrders{{OrderID: "LMF-1", StopID: 1}},
					},
					{
						StopOrders: model.TripStopOrders{{OrderID: "LMF-2", StopID: 1}},
					},
				},
				Orders: model.TripOrders{
					{
						OrderID:     "LMF-1",
						ServiceType: model.ServiceFood,
						Status:      model.StatusDriverArrivedRestaurant,
					},
					{
						OrderID:     "LMF-2",
						ServiceType: model.ServiceFood,
						Status:      model.StatusDriverArrivedRestaurant,
					},
				},
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
			}, nil)
		deps.delivery.EXPECT().
			UpdateStatus(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, request *lmdelivery.UpdateStatusRequest) error {
				require.Equal(tt, "LMF-1", request.Deliveries[0].OrderID)
				require.Equal(tt, "DRIVER_TO_DESTINATION", request.Deliveries[0].Status)
				require.Equal(tt, true, request.Deliveries[0].Suspended)
				require.Equal(tt, "QR_PROMPTPAY", request.Deliveries[0].DeliveryFeePaymentMethod, "should send delivery fee payment method")
				return nil
			})

		err := svc.SyncTripOrderStatus(ctx, &model.Order{
			OrderID: "LMF-1",
			TripID:  tripID,
			Status:  model.StatusDriverToDestination,
			HeadTo:  0,
			Quote:   model.Quote{Routes: []model.Stop{{Pauses: model.PauseSet{model.PauseQRPayment: true}, PriceSummary: model.PriceSummary{DeliveryFee: domainModel.DeliveryFeeSummary{PaymentMethod: "QR_PROMPTPAY"}}}, {}}},
		}, nil)
		require.NoError(tt, err)
	})

	t.Run("sync to lm-delivery with flag `suspended = true` when trip doesn't head to the order stop", func(tt *testing.T) {
		svc, deps, cleanup := newTestTripService(tt)
		defer cleanup()
		ctx := context.Background()
		tripID := "trip-1"
		deps.tripRepo.EXPECT().GetTripByTripID(ctx, tripID).Return(
			model.Trip{
				TripID: tripID,
				Routes: []model.TripRoute{
					{
						StopOrders: model.TripStopOrders{{OrderID: "LMF-1", StopID: 0, Done: true}, {OrderID: "LMF-2", StopID: 0, Done: true}},
					},
					{
						StopOrders: model.TripStopOrders{{OrderID: "LMF-1", StopID: 1}},
					},
					{
						StopOrders: model.TripStopOrders{{OrderID: "LMF-2", StopID: 1}},
					},
				},
				Orders: model.TripOrders{
					{
						OrderID:     "LMF-1",
						ServiceType: model.ServiceFood,
						Status:      model.StatusDriverToDestination,
					},
					{
						OrderID:     "LMF-2",
						ServiceType: model.ServiceFood,
						Status:      model.StatusDriverArrivedRestaurant,
					},
				},
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
			}, nil)
		deps.delivery.EXPECT().
			UpdateStatus(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, request *lmdelivery.UpdateStatusRequest) error {
				require.Equal(tt, "LMF-2", request.Deliveries[0].OrderID)
				require.Equal(tt, "DRIVER_TO_DESTINATION", request.Deliveries[0].Status)
				require.Equal(tt, true, request.Deliveries[0].Suspended)
				return nil
			})

		err := svc.SyncTripOrderStatus(ctx, &model.Order{OrderID: "LMF-2", TripID: tripID, Status: model.StatusDriverToDestination, HeadTo: 0, Quote: model.Quote{Routes: []model.Stop{{}, {}}}}, nil)
		require.NoError(tt, err)
	})
}

func TestTripService_SyncTripOrdersSuspend(t *testing.T) {
	tripID := "trip-1"
	orderID := "ord-1"
	orderID2 := "ord-2"
	orderID3 := "ord-3"
	mockTrip := func() model.Trip {
		return model.Trip{
			TripID: tripID,
			Status: model.TripStatusInit,
			HeadTo: 0,
			Orders: []model.TripOrder{
				{OrderID: orderID, Status: model.StatusDriverMatched, ServiceType: model.ServiceFood},
				{OrderID: orderID2, Status: model.StatusDriverMatched, ServiceType: model.ServiceFood},
				{OrderID: orderID3, Status: model.StatusDriverMatched, ServiceType: model.ServiceFood},
			},
			Routes: []model.TripRoute{
				{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
					{OrderID: orderID, StopID: 0},
					{OrderID: orderID2, StopID: 0},
					{OrderID: orderID3, StopID: 0},
				}},
				{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
				{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
			},
		}
	}

	t.Run("should sync orders in trip drop off route if current state is DRIVE_TO drop off", func(tt *testing.T) {
		trip := mockTrip()
		trip.HeadTo = 1
		trip.Status = model.TripStatusDriveTo

		svc, deps, cleanup := newTestTripService(tt)
		defer cleanup()
		ctx := context.Background()
		deps.delivery.EXPECT().
			UpdateStatus(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, request *lmdelivery.UpdateStatusRequest) error {
				require.Equal(tt, orderID, request.OrderID)
				return nil
			})
		deps.delivery.EXPECT().
			UpdateStatus(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, request *lmdelivery.UpdateStatusRequest) error {
				require.Equal(tt, orderID2, request.OrderID)
				return nil
			})
		deps.delivery.EXPECT().
			UpdateStatus(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, request *lmdelivery.UpdateStatusRequest) error {
				require.Equal(tt, orderID3, request.OrderID)
				return nil
			})
		err := svc.SyncTripOrdersSuspend(ctx, trip)
		require.NoError(tt, err)
	})

	t.Run("should not sync any orders if current route isn't drop off", func(tt *testing.T) {
		trip := mockTrip()
		trip.HeadTo = 0

		svc, _, cleanup := newTestTripService(tt)
		defer cleanup()
		ctx := context.Background()
		err := svc.SyncTripOrdersSuspend(ctx, trip)
		require.NoError(tt, err)
	})

	t.Run("shouldn't process messenger order", func(tt *testing.T) {
		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusDriveTo,
			HeadTo: 1,
			Orders: []model.TripOrder{
				{OrderID: orderID, Status: model.StatusDriveTo, ServiceType: model.ServiceMessenger},
			},
			Routes: []model.TripRoute{
				{ID: "sender", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0}}},
				{ID: "recipient", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
				{ID: "sender", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 2}}},
			},
		}

		svc, _, cleanup := newTestTripService(tt)
		defer cleanup()
		ctx := context.Background()
		err := svc.SyncTripOrdersSuspend(ctx, trip)
		require.NoError(tt, err)
	})

	t.Run("should not sync any orders if trip is ended", func(tt *testing.T) {
		tt.Run("COMPLETED", func(ttt *testing.T) {
			trip := mockTrip()
			trip.Status = model.TripStatusCompleted

			svc, _, cleanup := newTestTripService(tt)
			defer cleanup()
			ctx := context.Background()
			err := svc.SyncTripOrdersSuspend(ctx, trip)
			require.NoError(ttt, err)
		})

		tt.Run("CANCELED", func(ttt *testing.T) {
			trip := mockTrip()
			trip.Status = model.TripStatusCanceled

			svc, _, cleanup := newTestTripService(tt)
			defer cleanup()
			ctx := context.Background()
			err := svc.SyncTripOrdersSuspend(ctx, trip)
			require.NoError(ttt, err)
		})
	})
}

func TestTripService_ProcessB2Border(t *testing.T) {
	t.Run("success process b2b order (backward compatibility of migration queueing order to queueing trip)", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		driverId := "driver-1"
		orderId := "order-b2b"
		driver := &model.Driver{
			QueueingOrders: []string{orderId},
			QueueingTrips:  []string{},
			DriverID:       driverId,
		}

		b2bOrder := &model.Order{
			OrderID: orderId,
			Status:  model.StatusDriverMatched,
			Quote: model.Quote{
				ServiceType:       model.ServiceFood,
				Routes:            []model.Stop{{}, {}},
				DistributeRegions: []model.RegionCode{"BKK"},
			},
			Driver: driverId,
			IsB2B:  true,
		}

		deps.drivSvc.EXPECT().DequeueOrder(gomock.Any(), gomock.Any()).Return(nil)
		deps.orderRepo.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, ordID string, opt ...repository.Option) (*model.Order, error) {
			require.Equal(t, orderId, ordID)
			return b2bOrder, nil
		})

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), model.ServiceFood, "BKK", gomock.Any(), gomock.Any()).
			Return(nil, createValidPriceScheme(), nil)

		var generatedTripID string
		deps.tripRepo.EXPECT().IsExists(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tripID string) bool {
			generatedTripID = tripID
			return false
		})

		deps.tripRepo.EXPECT().Create(gomock.Any(), gomock.Any()).DoAndReturn(func(_ context.Context, tr model.Trip) error {
			require.Equal(t, true, tr.CreatedFromB2BOrder)
			return nil
		})
		deps.orderRepo.EXPECT().UpdateOrder(gomock.Any(), gomock.Any(), gomock.Any())
		deps.drivRepo.EXPECT().SetTripID(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driverID string, tripID string) error {
			require.Equal(t, driverId, driverID)
			require.Equal(t, generatedTripID, tripID)
			return nil
		})
		deps.delivery.EXPECT().EmbarkedTripOrders(gomock.Any(), gomock.Any())

		ctx := context.Background()
		ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
		defer toggle.DisableAll()
		err := underTest.ProcessB2BTrip(ctx, driver)
		require.Nil(t, err)
	})

	t.Run("success process b2b order", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		driverId := "driver-1"
		orderId := "order-b2b"
		preCreatedTripID := "trip-b2b"
		driver := &model.Driver{
			QueueingOrders: []string{orderId},
			QueueingTrips:  []string{preCreatedTripID},
			DriverID:       driverId,
		}

		deps.drivSvc.EXPECT().DequeueTrip(gomock.Any(), gomock.Any()).Return(nil)

		ctx := context.Background()
		ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
		defer toggle.DisableAll()
		err := underTest.ProcessB2BTrip(ctx, driver)
		require.Nil(t, err)
	})

	t.Run("not process b2b if len(QueueingOrders) == 0 and len(QueueingTrips) == 0", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, _, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		driver := &model.Driver{}
		err := underTest.ProcessB2BTrip(context.Background(), driver)
		require.Nil(t, err)
	})

	t.Run("trip created from b2b order still stamp created_from_b2b_order to true even if order.is_b2b is false", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		driverId := "driver-1"
		orderId := "order-b2b"
		preCreatedTripId := "trip-b2b"
		driver := &model.Driver{
			QueueingOrders: []string{orderId},
			QueueingTrips:  []string{preCreatedTripId},
			DriverID:       driverId,
		}

		deps.drivSvc.EXPECT().DequeueTrip(gomock.Any(), gomock.Any()).Return(nil)

		ctx := context.Background()
		ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
		defer toggle.DisableAll()
		err := underTest.ProcessB2BTrip(ctx, driver)
		require.Nil(t, err)
	})
}

func TestTripService_BanAfterCompleteOrder(t *testing.T) {
	t.Run("ban and save history when ban later is true", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		banUntil := time.Date(2022, 2, 2, 11, 0, 0, 0, timeutil.BangkokLocation())
		driver := &model.Driver{
			BanLater:    true,
			BannedUntil: &banUntil,
			Reason:      "just ban",
		}

		deps.banSvc.EXPECT().BanAndSaveHistory(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driv *model.Driver, bInfo model.BanInfo) error {
			require.Equal(t, "just ban", bInfo.Reason)
			require.Equal(t, 2022, bInfo.Until.Year())
			require.Equal(t, time.Month(2), bInfo.Until.Month())
			require.Equal(t, 2, bInfo.Until.Day())
			require.Equal(t, 11, bInfo.Until.Hour())
			require.Equal(t, 0, bInfo.Until.Minute())
			require.Equal(t, 0, bInfo.Until.Second())
			require.Equal(t, 0, bInfo.Until.Nanosecond())
			return nil
		})
		err := underTest.BanAfterCompleteOrder(context.Background(), driver, &model.ServiceArea{})
		require.Nil(t, err)
	})

	t.Run("ban with history", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		banHistory := &model.BanHistory{
			DriverID: "driver-1",
		}

		driver := &model.Driver{
			BanLater:            false,
			TemporaryBanHistory: banHistory,
		}

		deps.banSvc.EXPECT().BanWithHistory(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driv *model.Driver, bh *model.BanHistory) error {
			require.Equal(t, banHistory, bh)
			return nil
		})
		err := underTest.BanAfterCompleteOrder(context.Background(), driver, &model.ServiceArea{})
		require.Nil(t, err)
	})

	t.Run("ban with offline later", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		driver := &model.Driver{
			OfflineLater: true,
		}

		deps.banSvc.EXPECT().Ban(gomock.Any(), gomock.Any(), gomock.Any())
		err := underTest.BanAfterCompleteOrder(context.Background(), driver, &model.ServiceArea{})
		require.Nil(t, err)
	})
}

func TestTripService_SetZeroTripIncomeForFullTimeDriver(t *testing.T) {
	t.Run("set zero trip income for full time driver", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		driver := &model.Driver{DriverID: "driver-1"}
		driver.DriverType = crypt.NewLazyEncryptedString(string(model.DriverTypeFullTime))

		trip := model.Trip{
			TripID: "trip-1",
			DriverWageSummary: model.TripDriverWageSummary{
				BaseWage:        types.Money(40),
				TotalDriverWage: 40,
			},
		}

		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tr model.Trip) error {
			require.Equal(t, types.Money(0), tr.DriverWageSummary.BaseWage)
			require.Equal(t, types.Money(0), tr.DriverWageSummary.TotalDriverWage)
			return nil
		})

		err := underTest.SetZeroTripIncomeForFullTimeDriver(context.Background(), &trip)
		require.NoError(t, err)
	})
}

func TestTripService_GetOrCreateTripFromOrderID(t *testing.T) {
	orderID := "order-1"
	tripID := "trip-1"

	t.Run("get trip from order ID", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		deps.orderRepo.EXPECT().Get(gomock.Any(), orderID).Return(&model.Order{TripID: tripID}, nil)
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID).Return(model.Trip{TripID: tripID}, nil)

		gctx := testutil.NewContextWithRecorder().GinCtx()
		toggle.EnableTripOnGinContext(gctx)
		trip, err := underTest.GetOrCreateTripFromOrderID(gctx, orderID)
		require.Nil(t, err)
		require.Equal(t, tripID, trip.TripID)
	})

	t.Run("create new trip from order ID", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()
		underTest, deps, cleanUpFn := newTestTripService(t)
		defer cleanUpFn()

		deps.orderRepo.EXPECT().Get(gomock.Any(), orderID).Return(&model.Order{
			OrderID: orderID,
			Status:  model.StatusDriverMatched,
			Quote: model.Quote{
				Routes: []model.Stop{
					{},
					{},
				},
				DistributeRegions: model.DistributeRegions{"BKK"},
				ServiceType:       model.ServiceFood,
			},
		}, nil)

		var newTripID string
		deps.tripRepo.EXPECT().IsExists(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, tripID string) bool {
			newTripID = tripID
			return false
		})

		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), model.ServiceFood, "BKK", gomock.Any(), gomock.Any()).
			Return(nil, *model.NewSettingDeliveryFeePriceScheme("test", 0, model.RoadFeeTiers{}, []float64{1, 0.5, 0.33}, 10, 0), nil)
		deps.tripRepo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
		deps.orderRepo.EXPECT().SetTripID(gomock.Any(), orderID, gomock.Any())

		gctx := testutil.NewContextWithRecorder().GinCtx()
		toggle.EnableTripOnGinContext(gctx)
		trip, err := underTest.GetOrCreateTripFromOrderID(gctx, orderID)
		require.Nil(t, err)
		require.Equal(t, newTripID, trip.TripID)
	})
}

func createValidPriceScheme() model.SettingDeliveryFeePriceScheme {
	name := "price-scheme-01"
	ps := model.NewSettingDeliveryFeePriceScheme(name, 30, model.RoadFeeTiers{}, []float64{1, 0.5, 0.33}, 10, 0)
	return *ps
}

func TestOnForceCompletedOrder(t *testing.T) {
	orderID := "ord_100"
	orderID2 := "ord_200"
	orderID3 := "ord_300"
	driverID := "driver-A"
	tripID := "TRIP-ID"

	forceCompleteEvent := func(orderID string, revampedStatus bool) service.TripOrderForceCompletedEvent {
		return service.TripOrderForceCompletedEvent{
			TripID:              tripID,
			OrderID:             orderID,
			OrderStatus:         model.StatusCompleted,
			OrderRevampedStatus: revampedStatus,
		}
	}

	type testcase struct {
		name string
		skip string
		only bool
		// given
		trip           model.Trip
		driverModifier func(driver *model.Driver)
		// when
		event service.TripOrderForceCompletedEvent
		// then
		expectTripStatus  model.TripStatus
		expectOrderStatus map[string]model.Status
		expectError       error

		assertTrip  func(t *testing.T, trip *model.Trip)
		assertError func(t *testing.T, err error)
	}

	execTest := func(t *testing.T, testcases []testcase) {
		var hasOnlyFlag bool
		onlyTestcases := make([]testcase, 0, len(testcases))
		for _, tc := range testcases {
			if tc.only {
				hasOnlyFlag = true
				onlyTestcases = append(onlyTestcases, tc)
			}
		}
		if hasOnlyFlag {
			testcases = onlyTestcases
		}
		for i := range testcases {
			tc := &testcases[i]
			t.Run(tc.name, func(tt *testing.T) {
				if tc.skip != "" {
					t.Skip(tc.skip)
				}

				// record expected "unchanged" trip order status
				if tc.expectOrderStatus != nil {
					for _, tripOrder := range tc.trip.Orders {
						if _, ok := tc.expectOrderStatus[tripOrder.OrderID]; !ok {
							tc.expectOrderStatus[tripOrder.OrderID] = tripOrder.Status
						}
					}
				}

				tr, deps, finish := newTestTripService(tt)
				defer finish()

				deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
				deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(tc.trip, nil).AnyTimes()
				deps.delivery.EXPECT().UpdateStatus(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()
				driver := &model.Driver{DriverID: driverID, CurrentTrip: tc.trip.TripID}
				if tc.driverModifier != nil {
					tc.driverModifier(driver)
				}

				if tc.expectError == nil {
					deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(driver, nil)
				}

				if tc.expectTripStatus == model.TripStatusCompleted {
					deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, orderIDs []string, opts ...repository.Option) ([]model.Order, error) {
						var orders []model.Order
						for _, v := range orderIDs {
							orders = append(orders, model.Order{
								OrderID: v,
								Quote: model.Quote{
									PayAtStop: 1,
									Routes: []model.Stop{
										{},
										{PriceSummary: model.PriceSummary{DeliveryFee: model.DeliveryFeeSummary{RawBaseFee: 20}}},
									},
								},
							})
						}
						return orders, nil
					})
					deps.driverTransSvc.EXPECT().ProcessDriverTransaction(gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
					).AnyTimes()
					deps.drivRepo.EXPECT().GetTodayEarning(gomock.Any(), gomock.Any()).AnyTimes()
					deps.drivRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).AnyTimes()

					if tc.trip.TripID == driver.CurrentTrip {
						deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(driver, nil)
						deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
						deps.drivSvc.EXPECT().UnAssignTrip(gomock.Any(), gomock.Eq(driverID)).Return(&model.Driver{}, nil)
						deps.drivRepo.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), gomock.Eq(driverID), gomock.Any()).Return(nil)
						deps.repSvc.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil)
					}

					if len(tc.trip.Orders) > 1 {
						deps.orderRepo.EXPECT().SetMOSaving(gomock.Any(), gomock.Any(), 20.0).Times(len(tc.trip.Orders))
					}
				}

				trip, err := tr.OnForceCompletedOrder(context.Background(), tc.event)

				if tc.expectError == nil && tc.assertError == nil {
					assert.NoError(tt, err)
				} else {
					if tc.expectError != nil {
						assert.Equal(tt, tc.expectError, err)
					}
					if tc.assertError != nil {
						tc.assertError(tt, err)
					}
				}

				assert.Equal(tt, tc.expectTripStatus, trip.Status)

				if tc.expectOrderStatus != nil {
					for _, tripOrder := range trip.Orders {
						if expectOrderStatus, ok := tc.expectOrderStatus[tripOrder.OrderID]; ok {
							assert.Equal(tt, expectOrderStatus, tripOrder.Status)
						} else {
							// expected order status don't changed
							assert.Fail(tt, "Unexpected trip order status")
						}
					}
				}

				if tc.assertTrip != nil {
					tc.assertTrip(tt, &trip)
				}
			})
		}
	}

	t.Parallel()

	t.Run("Invalid order event", func(tt *testing.T) {
		testcases := []testcase{
			{
				name: "Not found order",
				// given
				trip: model.Trip{
					Status: model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0}}},
						{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent("ord_not_found", true),
				// then
				expectError: service.ErrOrderNotOnGoing,
			},
			{
				name: "Trip is already COMPLETED",
				// given
				trip: model.Trip{
					Status: model.TripStatusCompleted,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0, Done: true}}},
						{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, true),
				// then
				expectError: service.ErrOrderNotOnGoing,
			},
		}
		execTest(tt, testcases)
	})

	t.Run("Trip Force Complete[INIT]", func(tt *testing.T) {
		testcases := []testcase{
			{
				name: "[Single Order]Transition from INIT to COMPLETED when force complete order",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0}}},
						{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, true),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order]No Transition from INIT when force complete order 1",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverMatched},
						{OrderID: orderID2, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, true),
				// then
				expectTripStatus:  model.TripStatusInit,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusDriverMatched},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID)
				},
			},
			{
				name: "[Multiple Order]No Transition from INIT when force complete order 2",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverMatched},
						{OrderID: orderID2, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, true),
				// then
				expectTripStatus:  model.TripStatusInit,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusDriverMatched, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
			{
				name: "[Multiple Order]Trip completed when all order completed",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, true),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 2, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},

			// ===== Legacy Food/Mart order status =====
			{
				name: "[Single Order - Legacy Status]Transition from INIT to COMPLETED when force complete order",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0}}},
						{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order - Legacy Status]No Transition from INIT when force complete order 1",
				// given
				trip: model.Trip{
					Status: model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverMatched},
						{OrderID: orderID2, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusInit,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusDriverMatched},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]No Transition from INIT when force complete order 2",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverMatched},
						{OrderID: orderID2, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusInit,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusDriverMatched, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]Trip completed when all order completed",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusInit,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusDriverMatched},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 2, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
		}
		execTest(tt, testcases)
	})

	t.Run("Trip Force Complete[READY]", func(tt *testing.T) {
		testcases := []testcase{
			{
				name: "[Single Order]Transition from DRIVE_TO to COMPLETE when order status was force COMPLETE",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriveTo},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0}}},
						{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, true),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order]No Transition from READY when force complete order 1",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusReady},
						{OrderID: orderID2, Status: model.StatusReady},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, true),
				// then
				expectTripStatus:  model.TripStatusReady,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusReady},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID)
				},
			},
			{
				name: "[Multiple Order]No Transition from READY when force complete order 2",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusReady},
						{OrderID: orderID2, Status: model.StatusReady},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, true),
				// then
				expectTripStatus:  model.TripStatusReady,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusReady, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
			{
				name: "[Multiple Order]Trip completed when all order completed",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusReady},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, true),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 2, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},

			// ===== Legacy Food/Mart order status =====
			{
				name: "[Single Order - Legacy Status]Transition from READY to COMPLETE when order status was force COMPLETE",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusRestaurantAccepted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0}}},
						{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order - Legacy Status]No Transition from READY when force complete order 1",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusRestaurantAccepted},
						{OrderID: orderID2, Status: model.StatusRestaurantAccepted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusReady,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusRestaurantAccepted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]No Transition from READY when force complete order 2",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusRestaurantAccepted},
						{OrderID: orderID2, Status: model.StatusRestaurantAccepted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusReady,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusRestaurantAccepted, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]Trip completed when all order completed",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusRestaurantAccepted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 2, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]Trip completed when all order completed (max load 3)",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusReady,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusCompleted},
						{OrderID: orderID3, Status: model.StatusRestaurantAccepted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
							{OrderID: orderID3, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
						{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID3, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted, orderID3: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 3, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID3)
				},
			},
		}
		execTest(tt, testcases)
	})

	t.Run("Trip Force Complete[DRIVE_TO]", func(tt *testing.T) {
		testcases := []testcase{
			{
				name: "[Multiple Order]No Transition from DRIVE_TO when force complete order 1",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriveTo},
						{OrderID: orderID2, Status: model.StatusDriveTo},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, true),
				// then
				expectTripStatus:  model.TripStatusDriveTo,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusDriveTo},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID)
				},
			},
			{
				name: "[Multiple Order]No Transition from DRIVE_TO when force complete order 2",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriveTo},
						{OrderID: orderID2, Status: model.StatusDriveTo},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, true),
				// then
				expectTripStatus:  model.TripStatusDriveTo,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusDriveTo, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
			{
				name: "[Single Order - Legacy Status]Transition from DRIVE_TO[0] to COMPLETE when order 1 was force COMPLETE",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverToRestaurant},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0}}},
						{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Single Order - Legacy Status]Transition from DRIVE_TO[0] to COMPLETE when order status was force COMPLETE",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					HeadTo:   1,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverToDestination},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0, Done: true}}},
						{ID: "customer", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order - Legacy Status]No Transition from DRIVE_TO 0 when force complete order 1",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverToRestaurant},
						{OrderID: orderID2, Status: model.StatusDriverToRestaurant},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusDriveTo,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusDriverToRestaurant},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]No Transition from DRIVE_TO 0 when force complete order 2",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverToRestaurant},
						{OrderID: orderID2, Status: model.StatusDriverToRestaurant},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusDriveTo,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusDriverToRestaurant, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 0, trip.HeadTo)
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]Transition from DRIVE_TO 1 to DRIVE_TO 2 when force complete order 1",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					HeadTo:   1,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverToDestination},
						{OrderID: orderID2, Status: model.StatusDriverToDestination},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusDriveTo,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusDriverToDestination},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 2, trip.HeadTo)
					assertTripRoutesIsDone(t, trip, []int{0, 1})
					assertTripOrderIsCompleted(t, trip, orderID)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]No Transition from DRIVE_TO, head to = 1 when force complete order 2",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					HeadTo:   1,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverToDestination},
						{OrderID: orderID2, Status: model.StatusDriverToDestination},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusDriveTo,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusDriverToDestination, orderID2: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 1, trip.HeadTo)
					assertTripRoutesIsDone(t, trip, []int{0, 2})
					assertTripOrderIsCompleted(t, trip, orderID2)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]No Transition from DRIVE_TO, head to = 1 when force complete order 3",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					HeadTo:   1,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverToDestination},
						{OrderID: orderID2, Status: model.StatusDriverToDestination},
						{OrderID: orderID3, Status: model.StatusDriverToDestination},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
							{OrderID: orderID3, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
						{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID3, false),
				// then
				expectTripStatus:  model.TripStatusDriveTo,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusDriverToDestination, orderID2: model.StatusDriverToDestination, orderID3: model.StatusCompleted},
				assertTrip: func(t *testing.T, trip *model.Trip) {
					require.Equal(t, 1, trip.HeadTo)
					assertTripRoutesIsDone(t, trip, []int{0, 3})
					assertTripOrderIsCompleted(t, trip, orderID3)
				},
			},
			{
				name: "[Multiple Order - Legacy Status]Transition from DRIVE_TO, head to = 2 to COMPLETE when force complete order 2",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					HeadTo:   1,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusDriverToDestination},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order - Legacy Status]Transition from DRIVE_TO, head to = 3 to COMPLETE when force complete order 3",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusDriveTo,
					HeadTo:   1,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusCompleted},
						{OrderID: orderID3, Status: model.StatusDriverToDestination},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
							{OrderID: orderID3, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
						{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID3, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted, orderID3: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
		}
		execTest(tt, testcases)
	})

	t.Run("Trip Force Complete[ARRIVED_AT]", func(tt *testing.T) {
		testcases := []testcase{
			{
				name: "[Multiple Order - Legacy Status]Transition from ARRIVED_AT[0] to COMPLETED when force complete order1 and order 2 was already COMPLETED",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusArrivedAt,
					HeadTo:   0,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverArrived},
						{OrderID: orderID2, Status: model.StatusCompleted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order - Legacy Status]Transition from ARRIVED_AT[1] to COMPLETED when order1 status was COMPLETED and order 2 was already COMPLETED",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusArrivedAt,
					HeadTo:   1,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverArrived},
						{OrderID: orderID2, Status: model.StatusCompleted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order - Legacy Status]Transition from ARRIVED_AT[0] to COMPLETED when force complete order2 and order 1 was already COMPLETED",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusArrivedAt,
					HeadTo:   0,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusDriverArrived},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order - Legacy Status]Transition from ARRIVED_AT[0] to COMPLETED when force complete order2 and order 1 was already COMPLETED",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusArrivedAt,
					HeadTo:   2,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusDriverArrived},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID2, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
			{
				name: "[Multiple Order - Legacy Status]Transition from ARRIVED_AT[1] to COMPLETED when force complete order3 and order 1,2 were already COMPLETED",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusArrivedAt,
					HeadTo:   2,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusCompleted},
						{OrderID: orderID2, Status: model.StatusCompleted},
						{OrderID: orderID3, Status: model.StatusDriverArrived},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0, Done: true},
							{OrderID: orderID2, StopID: 0, Done: true},
							{OrderID: orderID3, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1, Done: true}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
						{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
					},
				},
				// when
				event: forceCompleteEvent(orderID3, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted, orderID3: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
		}
		execTest(tt, testcases)
	})

	t.Run("Trip Force Complete should process only fee of wanted force complete trip if driver's current trip not equal to wanted force complete trip", func(tt *testing.T) {
		testcases := []testcase{
			{
				name: "success",
				// given
				trip: model.Trip{
					DriverID: driverID,
					TripID:   tripID,
					Status:   model.TripStatusArrivedAt,
					HeadTo:   0,
					Orders: []model.TripOrder{
						{OrderID: orderID, Status: model.StatusDriverArrived},
						{OrderID: orderID2, Status: model.StatusCompleted},
					},
					Routes: []model.TripRoute{
						{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
							{OrderID: orderID, StopID: 0},
							{OrderID: orderID2, StopID: 0, Done: true},
						}},
						{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
						{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
					},
				},
				driverModifier: func(driver *model.Driver) {
					driver.CurrentTrip = "other-trip"
				},
				// when
				event: forceCompleteEvent(orderID, false),
				// then
				expectTripStatus:  model.TripStatusCompleted,
				expectOrderStatus: map[string]model.Status{orderID: model.StatusCompleted, orderID2: model.StatusCompleted},
				assertTrip:        assertTripCompleted,
			},
		}
		execTest(tt, testcases)
	})
}

func TestTripService_SyncTripState(t *testing.T) {
	t.Run("should sync trip state correctly", func(tt *testing.T) {
		tr, deps, finish := newTestTripService(tt)
		defer finish()

		orderID := "order-id"
		orders := []model.Order{{OrderID: orderID, Status: model.StatusDriverToDestination, History: map[string]time.Time{
			"DRIVER_MATCH":              time.Now(),
			"RESTAURANT_ACCEPT":         time.Now().Add(1),
			"DRIVER_TO_RESTAURANT":      time.Now().Add(2),
			"DRIVER_ARRIVED_RESTAURANT": time.Now().Add(3),
			"DRIVER_TO_DESTINATION":     time.Now().Add(4),
		}}}

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusArrivedAt,
			HeadTo: 1,
			Routes: model.TripRoutes{
				{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 0, Done: true}}},
				{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
			},
			Orders: model.TripOrders{
				{
					OrderID: "order-canceled",
					Status:  model.StatusCanceled,
				},
				{
					OrderID: orderID,
					Status:  model.StatusDriverArrived,
				},
			},
		}

		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		deps.orderRepo.EXPECT().GetManyByDeliveringRounds(gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)
		deps.delivery.EXPECT().UpdateStatus(gomock.Any(), gomock.Any()).Return(nil)
		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		err := tr.SyncTripState(context.Background(), tripID)
		require.NoError(tt, err)
	})

	t.Run("should error when trip is canceled", func(tt *testing.T) {
		tr, deps, finish := newTestTripService(tt)
		defer finish()

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusCanceled,
			Routes: model.TripRoutes{},
			Orders: model.TripOrders{
				{
					OrderID: "order-1",
					Status:  model.StatusCanceled,
				},
				{
					OrderID: "order-2",
					Status:  model.StatusCanceled,
				},
			},
		}

		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		err := tr.SyncTripState(context.Background(), tripID)
		require.ErrorIs(tt, service.ErrTripStatusNotAllow, err)
	})

	t.Run("should error when trip is completed", func(tt *testing.T) {
		tr, deps, finish := newTestTripService(tt)
		defer finish()

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusCompleted,
			Routes: model.TripRoutes{{}, {}, {}},
			Orders: model.TripOrders{
				{
					OrderID: "order-1",
					Status:  model.StatusCompleted,
				},
				{
					OrderID: "order-2",
					Status:  model.StatusCompleted,
				},
			},
		}

		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		err := tr.SyncTripState(context.Background(), tripID)
		require.ErrorIs(tt, service.ErrTripStatusNotAllow, err)
	})

	t.Run("should sync when some orders is CANCELED", func(tt *testing.T) {
		tr, deps, finish := newTestTripService(tt)
		defer finish()

		tripID := "trip-id"
		orderIDCanceled := "order-id-canceled"
		orderIDCanceled2 := "order-id-canceled-2"
		orderID := "order-id"

		orderCanceled := model.Order{
			OrderID: orderIDCanceled,
			Status:  model.StatusCanceled,
			TripID:  tripID,
			Quote:   model.Quote{ServiceType: model.ServiceFood, DistributeRegions: model.DistributeRegions{"BKK"}, Routes: []model.Stop{{}, {}}},
		}

		order := model.Order{
			OrderID: orderID,
			Status:  model.StatusDriverToDestination,
			TripID:  tripID,
			Quote:   model.Quote{ServiceType: model.ServiceFood, DistributeRegions: model.DistributeRegions{"BKK"}, Routes: []model.Stop{{}, {}}},
		}

		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusDriveTo,
			HeadTo: 1,
			Routes: model.TripRoutes{
				{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderIDCanceled, StopID: 0, Done: true}, {OrderID: orderID, StopID: 0, Done: true}}},
				{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderIDCanceled, StopID: 1}}},
				{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID, StopID: 1}}},
			},
			Orders: model.TripOrders{
				{
					OrderID: orderIDCanceled,
					Status:  model.StatusDriverToDestination,
				},
				{
					OrderID: orderID,
					Status:  model.StatusDriverToDestination,
				},
				{
					OrderID: orderIDCanceled2,
					Status:  model.StatusCanceled,
				},
			},
		}

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		deps.delivery.EXPECT().
			UpdateStatus(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, request *lmdelivery.UpdateStatusRequest) error {
				require.Equal(tt, orderID, request.OrderID)
				require.Equal(tt, "DRIVER_TO_DESTINATION", request.Status)
				require.Equal(tt, false, request.Deliveries[0].Suspended)
				return nil
			})
		deps.orderRepo.EXPECT().GetManyByDeliveringRounds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Order{
			orderCanceled,
			order,
		}, nil)

		// on order canceled
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		deps.mapSvc.EXPECT().FindFastestRoute(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
			Return(&model.MapRoute{
				Distance: 3,
				Duration: 30.0,
			}, nil, nil)
		deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Order{
			order,
		}, nil)
		deps.orderRepo.EXPECT().Get(gomock.Any(), gomock.Any(), gomock.Any()).Return(&order, nil)
		deps.deliveryFeeSvc.EXPECT().
			GetDeliveryFeeCalculator(gomock.Any(), model.ServiceFood, "BKK", gomock.Any(), gomock.Any()).
			Return(nil, *model.NewSettingDeliveryFeePriceScheme("tan", 0, model.RoadFeeTiers{}, []float64{1, 0.5, 0.33}, 10, 0), nil)
		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, trip model.Trip) error {
			require.Equal(tt, 2, len(trip.Routes))
			require.Equal(tt, 3, len(trip.Orders))
			require.Equal(tt, model.StatusCanceled, trip.Orders[0].Status)
			require.Equal(tt, model.StatusCanceled, trip.Orders[2].Status)
			return nil
		})
		err := tr.SyncTripState(context.Background(), tripID)
		require.NoError(tt, err)
	})
}

func TestTripService_ForceSyncTripWithFinishedOrders(t *testing.T) {
	t.Run("should sync trip state correctly max-load 2", func(tt *testing.T) {
		tr, deps, finish := newTestTripService(tt)
		defer finish()

		orderID1 := "order-id-1"
		orderID2 := "order-id-2"
		orders := []model.Order{{OrderID: orderID1, Status: model.StatusCompleted}, {OrderID: orderID2, Status: model.StatusCompleted}}

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusArrivedAt,
			HeadTo: 2,
			Routes: model.TripRoutes{
				{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 0, Done: true}, {OrderID: orderID2, StopID: 0, Done: true}}},
				{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1, Done: true}}},
				{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
			},
			Orders: model.TripOrders{
				{
					OrderID: orderID1,
					Status:  model.StatusCompleted,
				},
				{
					OrderID: orderID2,
					Status:  model.StatusDriverToDestination,
				},
			},
		}

		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		deps.orderRepo.EXPECT().GetManyByDeliveringRounds(gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)
		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, trip model.Trip) error {
			require.Equal(tt, model.TripStatusCompleted, trip.Status)
			require.Equal(tt, model.StatusCompleted, trip.Orders[0].Status)
			require.Equal(tt, model.StatusCompleted, trip.Orders[1].Status)
			return nil
		})

		err := tr.ForceSyncTripWithFinishedOrders(context.Background(), tripID)
		require.NoError(tt, err)
	})

	t.Run("should sync trip state correctly max-load 3", func(tt *testing.T) {
		tr, deps, finish := newTestTripService(tt)
		defer finish()

		orderID1 := "order-id-1"
		orderID2 := "order-id-2"
		orderID3 := "order-id-3"
		orders := []model.Order{{OrderID: orderID1, Status: model.StatusCompleted}, {OrderID: orderID2, Status: model.StatusCompleted}, {OrderID: orderID3, Status: model.StatusCompleted}}

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusArrivedAt,
			HeadTo: 2,
			Routes: model.TripRoutes{
				{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 0, Done: true}, {OrderID: orderID2, StopID: 0, Done: true}, {OrderID: orderID3, StopID: 0, Done: true}}},
				{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1, Done: true}}},
				{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
			},
			Orders: model.TripOrders{
				{
					OrderID: orderID1,
					Status:  model.StatusCompleted,
				},
				{
					OrderID: orderID2,
					Status:  model.StatusDriverToDestination,
				},
				{
					OrderID: orderID3,
					Status:  model.StatusDriverToDestination,
				},
			},
		}

		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		deps.orderRepo.EXPECT().GetManyByDeliveringRounds(gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)
		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, trip model.Trip) error {
			require.Equal(tt, model.TripStatusCompleted, trip.Status)
			require.Equal(tt, model.StatusCompleted, trip.Orders[0].Status)
			require.Equal(tt, model.StatusCompleted, trip.Orders[1].Status)
			require.Equal(tt, model.StatusCompleted, trip.Orders[2].Status)
			return nil
		})

		err := tr.ForceSyncTripWithFinishedOrders(context.Background(), tripID)
		require.NoError(tt, err)
	})

	t.Run("should error if some orders are not finished", func(tt *testing.T) {
		tr, deps, finish := newTestTripService(tt)
		defer finish()

		orderID1 := "order-id-1"
		orderID2 := "order-id-2"
		orders := []model.Order{{OrderID: orderID1, Status: model.StatusCompleted}, {OrderID: orderID2, Status: model.StatusDriverArrived}}

		tripID := "trip-id"
		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusArrivedAt,
			HeadTo: 2,
			Routes: model.TripRoutes{
				{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 0, Done: true}, {OrderID: orderID2, StopID: 0, Done: true}}},
				{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1, Done: true}}},
				{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
			},
			Orders: model.TripOrders{
				{
					OrderID: orderID1,
					Status:  model.StatusCompleted,
				},
				{
					OrderID: orderID2,
					Status:  model.StatusDriverToDestination,
				},
			},
		}

		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		deps.orderRepo.EXPECT().GetManyByDeliveringRounds(gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)

		err := tr.ForceSyncTripWithFinishedOrders(context.Background(), tripID)
		require.ErrorIs(tt, err, service.ErrSomeOrdersNotFinished)
	})

	t.Run("should sync when some orders is CANCELED", func(tt *testing.T) {
		tr, deps, finish := newTestTripService(tt)
		defer finish()

		tripID := "trip-id"
		orderIDCanceled := "order-id-canceled"
		orderIDCanceled2 := "order-id-canceled-2"

		orderCanceled := model.Order{
			OrderID: orderIDCanceled,
			Status:  model.StatusCanceled,
			TripID:  tripID,
			Quote:   model.Quote{ServiceType: model.ServiceFood, DistributeRegions: model.DistributeRegions{"BKK"}, Routes: []model.Stop{{}, {}}},
		}

		trip := model.Trip{
			TripID: tripID,
			Status: model.TripStatusReady,
			HeadTo: 0,
			Routes: model.TripRoutes{
				{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderIDCanceled, StopID: 0}}},
				{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderIDCanceled, StopID: 1}}},
			},
			Orders: model.TripOrders{
				{
					OrderID: orderIDCanceled,
					Status:  model.StatusRestaurantAccepted,
				},
				{
					OrderID: orderIDCanceled2,
					Status:  model.StatusCanceled,
				},
			},
		}

		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes()
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		deps.orderRepo.EXPECT().GetManyByDeliveringRounds(gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.Order{
			orderCanceled,
		}, nil)

		// on order canceled
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), tripID, gomock.Any()).Return(trip, nil)
		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, trip model.Trip) error {
			require.Equal(tt, 0, len(trip.Routes))
			require.Equal(tt, 2, len(trip.Orders))
			require.Equal(tt, model.TripStatusCanceled, trip.Status)
			require.Equal(tt, model.StatusCanceled, trip.Orders[0].Status)
			require.Equal(tt, model.StatusCanceled, trip.Orders[1].Status)
			return nil
		})
		err := tr.ForceSyncTripWithFinishedOrders(context.Background(), tripID)
		require.NoError(tt, err)
	})
}

type Timeline struct {
	OrderIDs []string
	Status   model.Status
	HeadTo   int
}

func mapTimelineToHistory(orderID string, timelines []Timeline) map[string]time.Time {
	sequenceTime := func(i time.Duration) time.Time {
		return time.Time{}.Add(i * time.Millisecond)
	}
	history := make(map[string]time.Time)
	for i, t := range timelines {
		if stringutil.IsStringInList(t.OrderIDs, orderID) {
			statusKey := string(t.Status)
			if t.Status == model.StatusDriveTo || t.Status == model.StatusArrivedAt {
				statusKey += fmt.Sprintf("_%d", t.HeadTo)
			}
			history[statusKey] = sequenceTime(time.Duration(i))
		}
	}
	return history
}

func mapTimelineToOrders(timelines []Timeline, baseOrders []model.Order) []model.Order {
	orderIDs := types.NewStringSet()
	latestOrderStatus := make(map[string]model.Status)
	latestOrderHeadTo := make(map[string]int)

	for _, t := range timelines {
		orderIDs.Add(t.OrderIDs...)
		for _, orderID := range t.OrderIDs {
			latestOrderStatus[orderID] = t.Status
			latestOrderHeadTo[orderID] = t.HeadTo
		}
	}

	orderIDToBaseOrder := make(map[string]model.Order)
	for _, o := range baseOrders {
		orderIDToBaseOrder[o.OrderID] = o
	}

	orders := make([]model.Order, orderIDs.Count())
	for _, orderID := range orderIDs.GetElements() {
		orders = append(orders, model.Order{
			OrderID:        orderID,
			Status:         latestOrderStatus[orderID],
			HeadTo:         latestOrderHeadTo[orderID],
			History:        mapTimelineToHistory(orderID, timelines),
			RevampedStatus: orderIDToBaseOrder[orderID].RevampedStatus,
			Quote:          orderIDToBaseOrder[orderID].Quote,
		})
	}
	return orders
}

func TestDoSyncTripState(t *testing.T) {
	t.Parallel()

	orderID1 := "order-1"
	orderID2 := "order-2"
	orderID3 := "order-3"

	type testcase struct {
		name        string
		timelines   []Timeline
		trip        model.Trip
		baseOrders  []model.Order
		assertTrip  func(t *testing.T, trip model.Trip)
		assertError func(t *testing.T, err error)
	}

	testcases := []testcase{
		{
			name: "[on going] trip state is older than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 0, trip.HeadTo)
				require.True(t, trip.Routes[0].StopOrders[0].Done)
				require.False(t, trip.Routes[0].StopOrders[1].Done)
				require.Equal(t, model.StatusDriverToDestination, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[1].Status)
			},
		},
		{
			name: "[on going] trip state is older than orders state - max load 3",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID3, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID3}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID3}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2, orderID3}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
						{OrderID: orderID3, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID3, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 0, trip.HeadTo)
				require.True(t, trip.Routes[0].StopOrders[0].Done)
				require.False(t, trip.Routes[0].StopOrders[1].Done)
				require.Equal(t, model.StatusDriverToDestination, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[1].Status)
				require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[2].Status)
			},
		},
		{
			name: "[on going][revert] trip state is newer than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 0, trip.HeadTo)
				require.False(t, trip.Routes[0].StopOrders[0].Done)
				require.False(t, trip.Routes[0].StopOrders[1].Done)
				require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[1].Status)
			},
		},
		{
			name: "[on going][include canceled] trip state is older than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID3, Status: model.StatusCanceled},
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 0, trip.HeadTo)
				require.True(t, trip.Routes[0].StopOrders[0].Done)
				require.False(t, trip.Routes[0].StopOrders[1].Done)
				require.Equal(t, model.StatusCanceled, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverToDestination, trip.Orders[1].Status)
				require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[2].Status)
			},
		},
		{
			name: "[on going][include completed][1] trip state is older than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},
				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverToDestination},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverArrived},
				{OrderIDs: []string{orderID1}, Status: model.StatusDropOffDone},
				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID2}, Status: model.StatusDriverArrived},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 2,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusCompleted},
					{OrderID: orderID2, Status: model.StatusDriverToDestination},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].StopOrders[0].Done)
				require.True(t, trip.Routes[0].StopOrders[1].Done)
				require.True(t, trip.Routes[1].StopOrders[0].Done)
				require.False(t, trip.Routes[2].StopOrders[0].Done)
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverArrived, trip.Orders[1].Status)
			},
		},
		{
			name: "[on going][include completed][2] trip state is older than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},
				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverToDestination},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverArrived},
				{OrderIDs: []string{orderID1}, Status: model.StatusDropOffDone},
				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrived},
					{OrderID: orderID2, Status: model.StatusDriverToDestination},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusDriveTo, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].StopOrders[0].Done)
				require.True(t, trip.Routes[0].StopOrders[1].Done)
				require.True(t, trip.Routes[1].StopOrders[0].Done)
				require.False(t, trip.Routes[2].StopOrders[0].Done)
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverToDestination, trip.Orders[1].Status)
			},
		},
		{
			name: "[on going][switch flow][drive to restaurant] trip state is older than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 0, trip.HeadTo)
				require.True(t, trip.Routes[0].StopOrders[0].Done)
				require.False(t, trip.Routes[0].StopOrders[1].Done)
				require.Equal(t, model.StatusDriverToDestination, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[1].Status)
			},
		},
		{
			name: "[on going][switch flow][arrived to restaurant] trip state is older than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},
				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 0, trip.HeadTo)
				require.True(t, trip.Routes[0].StopOrders[0].Done)
				require.False(t, trip.Routes[0].StopOrders[1].Done)
				require.Equal(t, model.StatusDriverToDestination, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverArrivedRestaurant, trip.Orders[1].Status)
			},
		},
		{
			name: "[completed] trip state is older than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToDestination},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverArrived},
				{OrderIDs: []string{orderID1}, Status: model.StatusDropOffDone},
				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID2}, Status: model.StatusDriverArrived},
				{OrderIDs: []string{orderID2}, Status: model.StatusDropOffDone},
				{OrderIDs: []string{orderID2}, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 2,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1, Done: true}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusCompleted},
					{OrderID: orderID2, Status: model.StatusDriverArrived},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
			},
		},
		{
			name: "[completed][revert] trip state is newer than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToDestination},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverArrived},
				{OrderIDs: []string{orderID1}, Status: model.StatusDropOffDone},
				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID2}, Status: model.StatusDriverArrived},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusCompleted,
				HeadTo: 2,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1, Done: true}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusCompleted},
					{OrderID: orderID2, Status: model.StatusCompleted},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.False(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverArrived, trip.Orders[1].Status)
			},
		},
		{
			name: "[force completed] orders state is newer than trip state",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusReady,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 0}}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusRestaurantAccepted},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 1, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
			},
		},
		{
			name: "[revamped status] trip state is older than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, RevampedStatus: true, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, RevampedStatus: true, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusReady},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriveTo, HeadTo: 0},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusReady},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriveTo, HeadTo: 0},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusReady,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusReady},
					{OrderID: orderID2, Status: model.StatusReady},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusDriveTo, trip.Status)
				require.Equal(t, 0, trip.HeadTo)
				require.False(t, trip.Routes[0].StopOrders[0].Done)
				require.False(t, trip.Routes[0].StopOrders[1].Done)
				require.False(t, trip.Routes[1].IsAllOrdersDone())
				require.False(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusDriveTo, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriveTo, trip.Orders[1].Status)
			},
		},
		{
			name: "[revamped status] trip state is older than orders state - max load 3",
			baseOrders: []model.Order{
				{OrderID: orderID1, RevampedStatus: true, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, RevampedStatus: true, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID3, RevampedStatus: true, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusReady},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriveTo, HeadTo: 0},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusReady},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriveTo, HeadTo: 0},

				{OrderIDs: []string{orderID3}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID3}, Status: model.StatusReady},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriveTo, HeadTo: 0},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusReady,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
						{OrderID: orderID3, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusReady},
					{OrderID: orderID2, Status: model.StatusReady},
					{OrderID: orderID3, Status: model.StatusReady},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusDriveTo, trip.Status)
				require.Equal(t, 0, trip.HeadTo)
				require.False(t, trip.Routes[0].StopOrders[0].Done)
				require.False(t, trip.Routes[0].StopOrders[1].Done)
				require.False(t, trip.Routes[0].StopOrders[2].Done)
				require.False(t, trip.Routes[1].IsAllOrdersDone())
				require.False(t, trip.Routes[2].IsAllOrdersDone())
				require.False(t, trip.Routes[3].IsAllOrdersDone())
				require.Equal(t, model.StatusDriveTo, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriveTo, trip.Orders[1].Status)
				require.Equal(t, model.StatusDriveTo, trip.Orders[2].Status)
			},
		},
		{
			name: "[revamped status][revert] trip state is newer than orders state",
			baseOrders: []model.Order{
				{OrderID: orderID1, RevampedStatus: true, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, RevampedStatus: true, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusReady},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriveTo, HeadTo: 0},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusReady},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriveTo, HeadTo: 0},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusArrivedAt, HeadTo: 0},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriveTo, HeadTo: 1},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriveTo, HeadTo: 1},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusArrivedAt, HeadTo: 1},
					{OrderID: orderID2, Status: model.StatusDriveTo, HeadTo: 1},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusDriveTo, trip.Status)
				require.Equal(t, 1, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.False(t, trip.Routes[1].IsAllOrdersDone())
				require.False(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusDriveTo, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriveTo, trip.Orders[1].Status)
				require.Equal(t, 1, trip.Orders[0].HeadTo)
				require.Equal(t, 1, trip.Orders[1].HeadTo)
			},
		},
		{
			name: "[revamped status][messenger][round trip] should sync correctly",
			baseOrders: []model.Order{
				{OrderID: orderID1, RevampedStatus: true, Quote: model.Quote{ServiceType: model.ServiceMessenger, Routes: []model.Stop{{}, {}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusReady},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriveTo, HeadTo: 0},
				{OrderIDs: []string{orderID1}, Status: model.StatusArrivedAt, HeadTo: 0},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriveTo, HeadTo: 1},
				{OrderIDs: []string{orderID1}, Status: model.StatusArrivedAt, HeadTo: 1},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriveTo, HeadTo: 2},
				{OrderIDs: []string{orderID1}, Status: model.StatusArrivedAt, HeadTo: 2},
				{OrderIDs: []string{orderID1}, Status: model.StatusDropOffDone, HeadTo: 2},
				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted, HeadTo: 2},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 2,
				Routes: []model.TripRoute{
					{ID: "customer1", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
					}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1, Done: true}}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 2}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusArrivedAt, HeadTo: 2, ServiceType: model.ServiceMessenger},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, 2, trip.Orders[0].HeadTo)
			},
		},
		{
			name: "[force completed][non-sequential] should sync successfully",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToDestination},

				{OrderIDs: []string{orderID2}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverArrived},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusDriveTo,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusCompleted},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 1, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.False(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusDriverArrived, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
			},
		},
		{
			name: "[force completed][1] should sync successfully",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToDestination},

				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID2}, Status: model.StatusDriverArrived},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusDriveTo,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusDriverToDestination},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.False(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusDriverArrived, trip.Orders[1].Status)
			},
		},
		{
			name: "[force completed][2] should sync successfully",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverArrived},
				{OrderIDs: []string{orderID2}, Status: model.StatusDropOffDone},
				{OrderIDs: []string{orderID2}, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusDriveTo,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusDriverToDestination},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
			},
		},
		{
			name: "[force completed][non-sequential] should sync successfully - max load 3",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID3, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID3}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID3}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2, orderID3}, Status: model.StatusDriverArrivedRestaurant},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverToDestination},

				{OrderIDs: []string{orderID3}, Status: model.StatusCompleted},
				{OrderIDs: []string{orderID2}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID1}, Status: model.StatusDriverArrived},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusDriveTo,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
						{OrderID: orderID3, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
					{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1, Done: true}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusCompleted},
					{OrderID: orderID3, Status: model.StatusCompleted},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 1, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.False(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.True(t, trip.Routes[3].IsAllOrdersDone())
				require.Equal(t, model.StatusDriverArrived, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[2].Status)
			},
		},
		{
			name: "[force completed][1] should sync successfully - max load 3",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID3, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID3}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID3}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2, orderID3}, Status: model.StatusDriverArrivedRestaurant},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverToDestination},

				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},
				{OrderIDs: []string{orderID2}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID3}, Status: model.StatusDriverArrived},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusDriveTo,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
						{OrderID: orderID3, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusDriverToDestination},
					{OrderID: orderID3, Status: model.StatusDriverToDestination},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusArrivedAt, trip.Status)
				require.Equal(t, 3, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.False(t, trip.Routes[3].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
				require.Equal(t, model.StatusDriverArrived, trip.Orders[2].Status)
			},
		},
		{
			name: "[force completed][2] should sync successfully - max load 3",
			baseOrders: []model.Order{
				{OrderID: orderID1, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID2, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
				{OrderID: orderID3, Quote: model.Quote{ServiceType: model.ServiceFood, Routes: []model.Stop{{}, {}}}},
			},
			timelines: []Timeline{
				{OrderIDs: []string{orderID1}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID1}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID1}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID2}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID2}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID2}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID3}, Status: model.StatusAssigningDriver},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverMatched},
				{OrderIDs: []string{orderID3}, Status: model.StatusRestaurantAccepted},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverToRestaurant},

				{OrderIDs: []string{orderID1, orderID2, orderID3}, Status: model.StatusDriverArrivedRestaurant},

				{OrderIDs: []string{orderID1}, Status: model.StatusCompleted},
				{OrderIDs: []string{orderID2}, Status: model.StatusCompleted},

				{OrderIDs: []string{orderID3}, Status: model.StatusDriverToDestination},
				{OrderIDs: []string{orderID3}, Status: model.StatusDriverArrived},
				{OrderIDs: []string{orderID3}, Status: model.StatusDropOffDone},
				{OrderIDs: []string{orderID3}, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusDriveTo,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
						{OrderID: orderID3, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusDriverToDestination},
					{OrderID: orderID3, Status: model.StatusDriverToDestination},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 3, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.True(t, trip.Routes[3].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[2].Status)
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			syncedTrip, err := service.DoSyncTripState(tc.trip, mapTimelineToOrders(tc.timelines, tc.baseOrders))
			if tc.assertError != nil {
				tc.assertError(tt, err)
			} else {
				require.NoError(t, err)
			}
			if tc.assertTrip != nil {
				tc.assertTrip(tt, syncedTrip)
			}
		})
	}
}

func TestDoSyncTripWithCompletedOrders(t *testing.T) {
	t.Parallel()

	orderID1 := "order-1"
	orderID2 := "order-2"
	orderID3 := "order-3"

	type testcase struct {
		name        string
		trip        model.Trip
		baseOrders  []model.Order
		assertTrip  func(t *testing.T, trip model.Trip)
		assertError func(t *testing.T, err error)
	}

	testcases := []testcase{
		{
			name: "completed orders",
			baseOrders: []model.Order{
				{OrderID: orderID1, Status: model.StatusCompleted},
				{OrderID: orderID2, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusDriveTo,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusCompleted},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
			},
		},
		{
			name: "completed orders - max load 3",
			baseOrders: []model.Order{
				{OrderID: orderID1, Status: model.StatusCompleted},
				{OrderID: orderID2, Status: model.StatusCompleted},
				{OrderID: orderID3, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusDriveTo,
				HeadTo: 1,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0, Done: true},
						{OrderID: orderID2, StopID: 0, Done: true},
						{OrderID: orderID3, StopID: 0, Done: true},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1, Done: true}}},
					{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1, Done: true}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverToDestination},
					{OrderID: orderID2, Status: model.StatusCompleted},
					{OrderID: orderID3, Status: model.StatusCompleted},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 3, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.True(t, trip.Routes[3].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[2].Status)
			},
		},
		{
			name: "sequence completed orders",
			baseOrders: []model.Order{
				{OrderID: orderID1, Status: model.StatusCompleted},
				{OrderID: orderID2, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
			},
		},
		{
			name: "sequence completed orders - max load 3",
			baseOrders: []model.Order{
				{OrderID: orderID1, Status: model.StatusCompleted},
				{OrderID: orderID2, Status: model.StatusCompleted},
				{OrderID: orderID3, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
						{OrderID: orderID3, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID3, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 3, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.True(t, trip.Routes[3].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[2].Status)
			},
		},
		{
			name: "reroute completed orders",
			baseOrders: []model.Order{
				{OrderID: orderID1, Status: model.StatusCompleted},
				{OrderID: orderID2, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
					}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 2, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
			},
		},
		{
			name: "reroute completed orders - max load 3",
			baseOrders: []model.Order{
				{OrderID: orderID1, Status: model.StatusCompleted},
				{OrderID: orderID2, Status: model.StatusCompleted},
				{OrderID: orderID3, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
						{OrderID: orderID2, StopID: 0},
						{OrderID: orderID3, StopID: 0},
					}},
					{ID: "customer3", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID3, StopID: 1}}},
					{ID: "customer2", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID2, StopID: 1}}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID3, Status: model.StatusDriverArrivedRestaurant},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 3, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.True(t, trip.Routes[2].IsAllOrdersDone())
				require.True(t, trip.Routes[3].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[1].Status)
				require.Equal(t, model.StatusCompleted, trip.Orders[2].Status)
			},
		},
		{
			name: "completed orders with canceled orders",
			baseOrders: []model.Order{
				{OrderID: orderID1, Status: model.StatusCompleted},
			},
			trip: model.Trip{
				TripID: "trip-id",
				Status: model.TripStatusArrivedAt,
				HeadTo: 0,
				Routes: []model.TripRoute{
					{ID: "merchant", Action: model.TripActionPickUp, StopOrders: []model.TripStopOrder{
						{OrderID: orderID1, StopID: 0},
					}},
					{ID: "customer1", Action: model.TripActionDropOff, StopOrders: []model.TripStopOrder{{OrderID: orderID1, StopID: 1}}},
				},
				Orders: []model.TripOrder{
					{OrderID: orderID1, Status: model.StatusDriverArrivedRestaurant},
					{OrderID: orderID2, Status: model.StatusCanceled},
				},
			},
			assertTrip: func(t *testing.T, trip model.Trip) {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				require.Equal(t, 1, trip.HeadTo)
				require.True(t, trip.Routes[0].IsAllOrdersDone())
				require.True(t, trip.Routes[1].IsAllOrdersDone())
				require.Equal(t, model.StatusCompleted, trip.Orders[0].Status)
				require.Equal(t, model.StatusCanceled, trip.Orders[1].Status)
			},
		},
	}

	for _, tc := range testcases {
		t.Run(tc.name, func(tt *testing.T) {
			syncedTrip, err := service.DoSyncTripWithCompletedOrders(tc.trip, tc.baseOrders)
			if tc.assertError != nil {
				tc.assertError(tt, err)
			} else {
				require.NoError(t, err)
			}
			if tc.assertTrip != nil {
				tc.assertTrip(tt, syncedTrip)
			}
		})
	}
}

func TestTripService_AdminCompleteTrip(t *testing.T) {
	checkOrderZeroFieldForFullTimeDriver := func(t *testing.T, order domainModel.Order) {
		deliveryFee := order.Routes[order.PayAtStop].PriceSummary.DeliveryFee
		require.Empty(t, deliveryFee.CustomOnTops)
		require.Empty(t, deliveryFee.OnTopScheme)
		require.Equal(t, 0.0, deliveryFee.RawBaseFee)
		require.Equal(t, 0.0, deliveryFee.BaseFee)
		require.Equal(t, 0.0, deliveryFee.RoadFee)
		require.Equal(t, 0.0, deliveryFee.CustomOnTop)
		require.Equal(t, 0.0, deliveryFee.CommissionRate)
		require.Equal(t, 0.0, deliveryFee.StartingFee)
		require.Equal(t, 0.0, deliveryFee.Commission)
		require.Equal(t, 0.0, deliveryFee.WithholdingTax)
		require.Equal(t, 0.0, deliveryFee.OnTopFare)
		require.Equal(t, 0.0, deliveryFee.RawOnTopFare)
		require.Equal(t, 0.0, deliveryFee.RawBundleOnTopFare)
		require.Equal(t, 0, deliveryFee.Coin)
		require.Equal(t, 0, deliveryFee.RawCoin)
		require.Equal(t, 0, deliveryFee.RawBundleCoin)
		require.Equal(t, 0.0, deliveryFee.OnTopCommissionFare)
		require.Equal(t, 0.0, deliveryFee.OnTopWithholdingTax)
		require.Equal(t, 0.0, deliveryFee.DistanceUnitFee)
	}

	checkTripZeroFieldForFullTimeDriver := func(t *testing.T, trip model.Trip) {
		driverWageSummary := trip.DriverWageSummary
		require.Equal(t, types.Money(0), driverWageSummary.BaseWage)
		require.Equal(t, types.Money(0), driverWageSummary.DistanceWage)
		require.Equal(t, types.Money(0), driverWageSummary.ExtraCharge)
		require.Equal(t, types.Money(0), driverWageSummary.ShiftDeduction)
		require.Equal(t, types.Money(0), driverWageSummary.TotalDriverWage)
		require.Equal(t, types.Money(0), driverWageSummary.Commission)
		require.Equal(t, types.Money(0), driverWageSummary.AdditionServiceCommission)
		require.Equal(t, types.Money(0), driverWageSummary.WithHoldingTax)
		require.Equal(t, types.Money(0), driverWageSummary.TransferAmount)
		require.Equal(t, types.Money(0), driverWageSummary.Outstanding)
	}

	t.Run("not complete trip when has one order status not completed", func(t *testing.T) {
		ts, deps, _ := newTestTripService(t)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.Trip{
			Orders: model.TripOrders{
				{
					OrderID: "order1",
					Status:  model.StatusDriverArrived,
				},
				{
					OrderID: "order2",
					Status:  model.StatusCompleted,
				},
			},
		}, nil)

		err := ts.AdminCompleteTrip(context.Background(), "tripID1")
		require.Equal(t, "order is not ready for complete trip", err.Error())
	})

	t.Run("not cancel trip when has one order status not canceled", func(t *testing.T) {
		ts, deps, _ := newTestTripService(t)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.Trip{
			Orders: model.TripOrders{
				{
					OrderID: "order1",
					Status:  model.StatusDriverArrived,
				},
				{
					OrderID: "order2",
					Status:  model.StatusCanceled,
				},
			},
		}, nil)

		err := ts.AdminCompleteTrip(context.Background(), "tripID1")
		require.Equal(t, "order is not ready for complete trip", err.Error())
	})

	t.Run("complete trip success with tripID equal driver current trip", func(t *testing.T) {
		driverId := "driver-1"
		tripId := "trip-1"
		tripB2BId := "trip-b2b"
		orderIds := []string{"order-1", "order-2"}
		orders := []model.Order{
			{
				OrderID: orderIds[0],
				TripID:  tripId,
				Driver:  "driver-1",
				Status:  model.StatusCompleted,
				Quote: model.Quote{
					ServiceType:           model.ServiceFood,
					RevenuePrincipalModel: true,
					DistributeRegions:     []model.RegionCode{"BKK"},
					Options: model.OrderOptions{
						DriverMoneyFlow: model.FlowCashCollection,
					},
					Routes: []model.Stop{
						{},
						{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{OnTopFare: 100, OnTopWithholdingTax: 10},
								ItemFee:     model.ItemFeeSummary{ItemFee: 100},
							},
						},
					},
				},
			},
			{
				OrderID: orderIds[1],
				TripID:  tripId,
				Driver:  driverId,
				Status:  model.StatusCompleted,
				Quote: model.Quote{
					ServiceType:           model.ServiceFood,
					RevenuePrincipalModel: true,
					DistributeRegions:     []model.RegionCode{"PATTAYA"},
					Options: model.OrderOptions{
						DriverMoneyFlow: model.FlowCashCollection,
					},
					Routes: []model.Stop{
						{},
						{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{OnTopFare: 100, OnTopWithholdingTax: 10},
								ItemFee:     model.ItemFeeSummary{ItemFee: 100},
							},
						},
					},
				},
			},
		}
		dt := model.DriverTransaction{
			FreeCreditTransactions: []model.TransactionInfo{
				{
					Amount: 100,
				},
			},
			PurchaseCreditBalance: 100,
			WalletBalance:         50,
		}
		trip := model.Trip{
			Status: model.TripStatusCompleted,
			TripID: tripId,
			Routes: model.TripRoutes{
				{
					Action:     model.TripActionPickUp,
					StopOrders: model.TripStopOrders{{OrderID: orderIds[0]}, {OrderID: orderIds[1]}},
				},
				{
					Action:     model.TripActionDropOff,
					StopOrders: model.TripStopOrders{{OrderID: orderIds[0]}},
				},
				{
					Action:     model.TripActionDropOff,
					StopOrders: model.TripStopOrders{{OrderID: orderIds[1]}},
				},
			},
			Orders: model.TripOrders{
				{OrderID: orderIds[0], Status: model.StatusCompleted},
				{OrderID: orderIds[1], Status: model.StatusCompleted},
			},
		}
		en := model.DriverEarning{
			DriverID:   driverId,
			Fee:        10,
			Commission: 10,
			Tax:        10,
			Tip:        10,
		}

		ts, deps, _ := newTestTripService(t)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(trip, nil)

		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{
			CurrentTrip: tripId,
		}, nil)

		// start process func OnTripCompleted
		deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.InAnyOrder(orderIds), gomock.Any()).Return(orders, nil)
		deps.orderRepo.EXPECT().SetMOSaving(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)

		var txn []model.Transaction

		deps.driverTransSvc.EXPECT().ProcessDriverTransaction(gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return(dt, txn, nil)

		deps.drivRepo.EXPECT().GetTodayEarning(gomock.Any(), gomock.Any()).Return(en, nil)
		deps.drivRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).Return(nil)

		driver := &model.Driver{
			QueueingOrders: []string{"order-2"},
			QueueingTrips:  []string{tripB2BId},
		}
		deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(driver, nil)

		deps.drivSvc.EXPECT().DequeueTrip(gomock.Any(), gomock.Any()).Return(nil)
		// end process func OnTripCompleted

		ctx := context.Background()
		ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
		defer toggle.DisableAll()
		err := ts.AdminCompleteTrip(ctx, tripId)
		require.NoError(t, err)
	})

	testCases := []struct {
		name          string
		ignorePayment bool
	}{
		{
			name:          "complete trip with tripID NOT equal driver current trip (process fee)",
			ignorePayment: false,
		},
		{
			name:          "complete trip with tripID NOT equal driver current trip (not process fee)",
			ignorePayment: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(tt *testing.T) {
			{
				driverId := "driver-1"
				tripId := "trip-1"
				orderIds := []string{"order-1", "order-2"}
				orders := []model.Order{
					{
						OrderID: orderIds[0],
						TripID:  tripId,
						Driver:  "driver-1",
						Status:  model.StatusCompleted,
						Quote: model.Quote{
							ServiceType:           model.ServiceFood,
							RevenuePrincipalModel: true,
							DistributeRegions:     []model.RegionCode{"BKK"},
							Options: model.OrderOptions{
								DriverMoneyFlow: model.FlowCashCollection,
							},
							Routes: []model.Stop{
								{},
								{
									PriceSummary: model.PriceSummary{
										DeliveryFee: model.DeliveryFeeSummary{OnTopFare: 100, OnTopWithholdingTax: 10},
										ItemFee:     model.ItemFeeSummary{ItemFee: 100},
									},
								},
							},
						},
					},
					{
						OrderID: orderIds[1],
						TripID:  tripId,
						Driver:  driverId,
						Status:  model.StatusCompleted,
						Quote: model.Quote{
							ServiceType:           model.ServiceFood,
							RevenuePrincipalModel: true,
							DistributeRegions:     []model.RegionCode{"PATTAYA"},
							Options: model.OrderOptions{
								DriverMoneyFlow: model.FlowCashCollection,
							},
							Routes: []model.Stop{
								{},
								{
									PriceSummary: model.PriceSummary{
										DeliveryFee: model.DeliveryFeeSummary{OnTopFare: 100, OnTopWithholdingTax: 10},
										ItemFee:     model.ItemFeeSummary{ItemFee: 100},
									},
								},
							},
						},
					},
				}
				dt := model.DriverTransaction{
					FreeCreditTransactions: []model.TransactionInfo{
						{
							Amount: 100,
						},
					},
					PurchaseCreditBalance: 100,
					WalletBalance:         50,
				}
				trip := model.Trip{
					Status: model.TripStatusCompleted,
					TripID: tripId,
					Routes: model.TripRoutes{
						{
							Action:     model.TripActionPickUp,
							StopOrders: model.TripStopOrders{{OrderID: orderIds[0]}, {OrderID: orderIds[1]}},
						},
						{
							Action:     model.TripActionDropOff,
							StopOrders: model.TripStopOrders{{OrderID: orderIds[0]}},
						},
						{
							Action:     model.TripActionDropOff,
							StopOrders: model.TripStopOrders{{OrderID: orderIds[1]}},
						},
					},
					Orders: model.TripOrders{
						{OrderID: orderIds[0], Status: model.StatusCompleted},
						{OrderID: orderIds[1], Status: model.StatusCompleted},
					},
				}
				en := model.DriverEarning{
					DriverID:   driverId,
					Fee:        10,
					Commission: 10,
					Tax:        10,
					Tip:        10,
				}

				ts, deps, _ := newTestTripService(t)
				deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(trip, nil)

				deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
				deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{
					CurrentTrip: "trip-2",
				}, nil)

				if !tc.ignorePayment {
					// start ProcessFee
					deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.InAnyOrder(orderIds), gomock.Any()).Return(orders, nil)
					deps.orderRepo.EXPECT().SetMOSaving(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)

					var txn []model.Transaction

					deps.driverTransSvc.EXPECT().ProcessDriverTransaction(gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
						gomock.Any(),
					).Return(dt, txn, nil)

					deps.drivRepo.EXPECT().GetTodayEarning(gomock.Any(), gomock.Any()).Return(en, nil)
					deps.drivRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).Return(nil)
					// end func ProcessFee
				}

				ctx := context.Background()
				ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
				defer toggle.DisableAll()
				err := ts.AdminCompleteTrip(ctx, tripId, service.WithIgnorePayment(tc.ignorePayment))

				require.NoError(t, err)
			}
		})
	}

	t.Run("cancel trip success with tripID equal driver current trip", func(t *testing.T) {
		ts, deps, _ := newTestTripService(t)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: "tripID1",
			Orders: model.TripOrders{
				{
					OrderID: "order1",
					Status:  model.StatusCanceled,
				},
				{
					OrderID: "order2",
					Status:  model.StatusCanceled,
				},
			},
		}, nil)
		deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{
			CurrentTrip: "tripID1",
		}, nil)

		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)

		// process next step
		deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{}, nil)
		deps.serviceAreaRepo.EXPECT().GetByRegion(gomock.Any(), gomock.Any()).Return(&model.ServiceArea{}, nil)
		deps.drivSvc.EXPECT().UnAssignTrip(gomock.Any(), gomock.Any()).Return(&model.Driver{}, nil)
		deps.drivRepo.EXPECT().SetRecoIdleTimeStartPoint(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
		deps.repSvc.EXPECT().Publish(gomock.Any(), gomock.Any()).Return(nil)

		err := ts.AdminCompleteTrip(context.Background(), "tripID1")
		require.Equal(t, nil, err)
	})

	t.Run("cancel trip success with tripID NOT equal driver current trip", func(t *testing.T) {
		ts, deps, _ := newTestTripService(t)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(model.Trip{
			TripID: "tripID1",
			Orders: model.TripOrders{
				{
					OrderID: "order1",
					Status:  model.StatusCanceled,
				},
				{
					OrderID: "order2",
					Status:  model.StatusCanceled,
				},
			},
		}, nil)
		deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{
			CurrentTrip: "tripID2",
		}, nil)

		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		err := ts.AdminCompleteTrip(context.Background(), "tripID1")
		require.Equal(t, nil, err)
	})

	t.Run("complete full time trip success", func(t *testing.T) {
		driverId := "driver-1"
		tripId := "trip-1"
		tripB2BId := "trip-b2b"

		orderIds := []string{"order-1", "order-2"}
		orders := []model.Order{
			{
				OrderID:    orderIds[0],
				TripID:     tripId,
				Driver:     driverId,
				Status:     model.StatusCompleted,
				DriverType: model.DriverTypeFullTime,
				Quote: model.Quote{
					ServiceType:           model.ServiceFood,
					RevenuePrincipalModel: true,
					DistributeRegions:     []model.RegionCode{"BKK"},
					Options: model.OrderOptions{
						DriverMoneyFlow: model.FlowCashCollection,
					},
					Routes: []model.Stop{
						{},
						{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{RawBaseFee: 22.95, OnTopFare: 100, OnTopWithholdingTax: 10},
								ItemFee:     model.ItemFeeSummary{ItemFee: 100},
							},
						},
					},
					PayAtStop: 1,
				},
				Tips: model.TipRecords{{Amount: 100}},
			},
			{
				OrderID:    orderIds[1],
				TripID:     tripId,
				Driver:     driverId,
				Status:     model.StatusCompleted,
				DriverType: model.DriverTypeFullTime,
				Quote: model.Quote{
					ServiceType:           model.ServiceFood,
					RevenuePrincipalModel: true,
					DistributeRegions:     []model.RegionCode{"PATTAYA"},
					Options: model.OrderOptions{
						DriverMoneyFlow: model.FlowCashCollection,
					},
					Routes: []model.Stop{
						{},
						{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{RawBaseFee: 22.94, OnTopFare: 100, OnTopWithholdingTax: 10},
								ItemFee:     model.ItemFeeSummary{ItemFee: 100},
							},
						},
					},
					PayAtStop: 1,
				},
			},
		}
		dt := model.DriverTransaction{
			FreeCreditTransactions: []model.TransactionInfo{
				{
					Amount: 100,
				},
			},
			PurchaseCreditBalance: 100,
			WalletBalance:         50,
		}
		trip := model.Trip{
			Status:     model.TripStatusCompleted,
			DriverType: model.DriverTypeFullTime,
			DriverID:   driverId,
			TripID:     tripId,
			DriverWageSummary: model.TripDriverWageSummary{
				BaseWage:        30,
				DistanceWage:    4.43,
				ShiftDeduction:  5,
				TotalDriverWage: 29.43,
			},
			Routes: model.TripRoutes{
				{
					Action:     model.TripActionPickUp,
					StopOrders: model.TripStopOrders{{OrderID: orderIds[0]}, {OrderID: orderIds[1]}},
				},
				{
					Action:     model.TripActionDropOff,
					StopOrders: model.TripStopOrders{{OrderID: orderIds[0]}},
				},
				{
					Action:     model.TripActionDropOff,
					StopOrders: model.TripStopOrders{{OrderID: orderIds[1]}},
				},
			},
			Orders: model.TripOrders{
				{OrderID: orderIds[0], Status: model.StatusCompleted},
				{OrderID: orderIds[1], Status: model.StatusCompleted},
			},
		}

		en := model.DriverEarning{
			DriverID:   driverId,
			Fee:        0,
			Commission: 0,
			Tax:        0,
			Tip:        0,
		}

		ts, deps, _ := newTestTripService(t)
		deps.txnHelper.EXPECT().WithTxn(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
		deps.tripRepo.EXPECT().GetTripByTripID(gomock.Any(), gomock.Any(), gomock.Any()).Return(trip, nil)

		deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).Return(nil)
		deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(&model.Driver{
			CurrentTrip: tripId,
		}, nil)

		// start process func OnTripCompleted
		deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.InAnyOrder(orderIds), gomock.Any()).Return(orders, nil)
		deps.orderRepo.EXPECT().SetMOSaving(gomock.Any(), gomock.Any(), gomock.Any()).Times(2)

		var txn []model.Transaction

		deps.driverTransSvc.EXPECT().ProcessDriverTransaction(gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
			gomock.Any(),
		).Return(dt, txn, nil)

		// Full time driver logic
		{
			for i, ord := range orders {
				deps.orderRepo.EXPECT().
					UpdateAndUpsertRevision(gomock.Any(), gomock.Any(), gomock.Any()).
					DoAndReturn(func(ctx context.Context, order *domainModel.Order, opts ...repository.Option) error {
						require.Equal(t, orderIds[i], ord.OrderID)
						require.Equal(t, model.StatusCompleted, ord.Status)
						checkOrderZeroFieldForFullTimeDriver(t, *order)
						return nil
					})
			}

			deps.tripRepo.EXPECT().Update(gomock.Any(), gomock.Any()).DoAndReturn(func(_ interface{}, trip domainModel.Trip) error {
				require.Equal(t, model.TripStatusCompleted, trip.Status)
				checkTripZeroFieldForFullTimeDriver(t, trip)
				return nil
			})
		}

		deps.drivRepo.EXPECT().GetTodayEarning(gomock.Any(), gomock.Any()).Return(en, nil)
		deps.drivRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driverEarnings ...model.DriverEarning) error {
			require.Equal(t, driverId, driverEarnings[0].DriverID)
			require.Equal(t, types.Money(0), driverEarnings[0].Fee)
			require.Equal(t, types.Money(0), driverEarnings[0].Commission)
			require.Equal(t, types.Money(0), driverEarnings[0].Tax)
			require.Equal(t, types.Money(100), driverEarnings[0].Tip)
			return nil
		})

		driver := &model.Driver{
			QueueingOrders: []string{"order-2"},
			QueueingTrips:  []string{tripB2BId},
		}
		deps.drivRepo.EXPECT().GetProfile(gomock.Any(), gomock.Any(), gomock.Any()).Return(driver, nil)

		deps.drivSvc.EXPECT().DequeueTrip(gomock.Any(), gomock.Any()).Return(nil)
		// end process func OnTripCompleted

		ctx := context.Background()
		ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
		defer toggle.DisableAll()
		err := ts.AdminCompleteTrip(ctx, tripId)
		require.NoError(t, err)
	})
}

func TestTripService_ProcessFee(t *testing.T) {
	t.Run("complete qr bike trip", func(t *testing.T) {
		driverId := "driver-1"
		tripId := "trip-1"
		orderIds := []string{"order-1"}

		mockOrders := func(ppInfo model.QRPromptPayInfo) []model.Order {
			return []model.Order{
				{
					OrderID: orderIds[0],
					TripID:  tripId,
					Driver:  "driver-1",
					Status:  model.StatusCompleted,
					Tips: model.TipRecords{{
						Amount:      9,
						OrderStatus: string(model.StatusArrivedAt),
					}},
					Quote: model.Quote{
						ServiceType:           model.ServiceBike,
						RevenuePrincipalModel: false,
						DistributeRegions:     []model.RegionCode{"BKK"},
						PayAtStop:             1,
						Routes: []model.Stop{
							{},
							{
								PriceSummary: model.PriceSummary{
									Total: 172,
									DeliveryFee: model.DeliveryFeeSummary{
										RawBaseFee:  173,
										BaseFee:     173,
										CustomOnTop: 29,
										SubTotal:    202,
										Discounts: model.DiscountList{{
											Type:     "COUPON",
											Category: "COUPON",
											Discount: 30,
										}},
										Total:      172,
										Commission: 20.2,

										OnTopScheme: []model.OnTopScheme{{
											Amount: 14,
										}},
										OnTopFare:           14,
										OnTopCommissionFare: 1.4,
										PaymentMethod:       model.PaymentMethodQRPromptPay,
										QRPromptPayInfo:     ppInfo,
									},
								},
							},
						},
					},
				},
			}
		}

		mockTrip := func() model.Trip {
			t := model.Trip{
				Status:   model.TripStatusCompleted,
				TripID:   tripId,
				DriverID: driverId,
				Routes: model.TripRoutes{
					{
						Action:     model.TripActionPickUp,
						StopOrders: model.TripStopOrders{{OrderID: orderIds[0]}},
					},
					{
						Action:     model.TripActionDropOff,
						StopOrders: model.TripStopOrders{{OrderID: orderIds[0]}},
					},
				},
				Orders: model.TripOrders{
					{OrderID: orderIds[0], Status: model.StatusCompleted},
				},
				DriverWageSummary: domainModel.TripDriverWageSummary{
					BaseWage:        56,
					DistanceWage:    117,
					TotalDriverWage: 173,
					Commission:      20.2,
				},
			}

			return t
		}

		mockedDriverTransaction := model.DriverTransaction{
			FreeCreditTransactions: []model.TransactionInfo{{Amount: 100}},
			PurchaseCreditBalance:  100,
			WalletBalance:          50,
		}

		en := model.DriverEarning{
			DriverID:   driverId,
			Fee:        100,
			Commission: 100,
			Tax:        100,
			Tip:        100,
		}

		t.Run("completing user has paid", func(t *testing.T) {
			ts, deps, _ := newTestTripService(t)

			deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.InAnyOrder(orderIds), gomock.Any()).Return(mockOrders(model.QRPromptPayInfo{
				Status:          model.QRPromptPayStatusResolvedByUser,
				IsUserResolveQR: true,
			}), nil)

			deps.driverTransSvc.EXPECT().ProcessDriverTransaction(gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
			).DoAndReturn(func(ctx context.Context, driverID string, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus, builder service.TransactionInfosBuilder, transactionOptions ...func(*service.ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error) {
				txnInfos, err := builder(mockedDriverTransaction)
				require.NoError(t, err)

				require.Len(t, txnInfos, 6)

				require.Equal(t, txnInfos[0].Category, model.WalletTransactionCategory)
				require.Equal(t, txnInfos[0].Type, model.CouponTransactionType, "should contain coupon transaction")
				require.Equal(t, txnInfos[0].Amount, types.NewMoney(30))
				require.Empty(t, txnInfos[0].PendingTransactionID)

				require.Equal(t, txnInfos[1].Category, model.WalletTransactionCategory)
				require.Equal(t, txnInfos[1].Type, model.DeliveryFeeTransactionType, "should contain delivery fee transaction")
				require.Equal(t, txnInfos[1].Amount, types.NewMoney(172))
				require.Empty(t, txnInfos[1].PendingTransactionID)

				require.Equal(t, txnInfos[2].Category, model.CreditTransactionCategory)
				require.Equal(t, txnInfos[2].Type, model.CommissionTransactionType, "should contain delivery fee commission transaction")
				require.Equal(t, txnInfos[2].SubType, model.CommissionDeliveryFeeUserTransactionSubType, "should set commission subtype")
				require.Equal(t, txnInfos[2].Amount, types.NewMoney(20.2))
				require.Empty(t, txnInfos[2].PendingTransactionID)

				require.Equal(t, txnInfos[3].Category, model.WalletTransactionCategory)
				require.Equal(t, txnInfos[3].Type, model.TipTransactionType, "should contain tip transaction")
				require.Equal(t, txnInfos[3].Amount, types.NewMoney(9))
				require.Empty(t, txnInfos[3].PendingTransactionID)

				require.Equal(t, txnInfos[4].Category, model.WalletTransactionCategory)
				require.Equal(t, txnInfos[4].Type, model.OnTopTransactionType, "should contain on-top transaction")
				require.Equal(t, txnInfos[4].Amount, types.NewMoney(14))
				require.Empty(t, txnInfos[4].PendingTransactionID)

				require.Equal(t, txnInfos[5].Category, model.CreditTransactionCategory)
				require.Equal(t, txnInfos[5].Type, model.CommissionTransactionType, "should contain on-top commission transaction")
				require.Equal(t, txnInfos[5].SubType, model.CommissionDeliveryFeeDynamicOnTopTransactionSubType, "should set commission subtype")
				require.Equal(t, txnInfos[5].Amount, types.NewMoney(1.4))
				require.Empty(t, txnInfos[5].PendingTransactionID)

				return mockedDriverTransaction, []model.Transaction{}, nil
			})

			deps.drivRepo.EXPECT().GetTodayEarning(gomock.Any(), driverId).Return(en, nil)
			deps.driverRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driverEarnings ...model.DriverEarning) error {
				driverEarning := driverEarnings[0]
				require.Equal(t, types.Money(100+173+14), driverEarning.Fee, "should set fee correctly")
				require.Equal(t, types.Money(100+20.2), driverEarning.Commission, "should set commission correctly")
				require.Equal(t, types.Money(100+9.0), driverEarning.Tip, "should set tip correctly")
				return nil
			})

			ctx := context.Background()
			ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
			defer toggle.DisableAll()
			err := ts.ProcessFee(ctx, mockTrip())
			require.NoError(t, err)
		})

		t.Run("completing user hasn't paid", func(t *testing.T) {
			ts, deps, _ := newTestTripService(t)

			deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.InAnyOrder(orderIds), gomock.Any()).Return(mockOrders(model.QRPromptPayInfo{
				Status:          model.QRPromptPayStatusWaitingForPayment,
				IsUserResolveQR: false,
			}), nil)

			deps.pendingTransactionRepo.EXPECT().CreateAll(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, pendingTransactions []model.PendingTransaction) error {
				require.Len(t, pendingTransactions, 3)

				require.Equal(t, pendingTransactions[0].TransactionInfo.Category, model.WalletTransactionCategory)
				require.Equal(t, pendingTransactions[0].TransactionInfo.Type, model.CouponTransactionType, "should set coupon transaction as pending")
				require.Equal(t, pendingTransactions[0].TransactionInfo.Amount, types.NewMoney(30))
				require.Equal(t, pendingTransactions[0].Status, model.PendingPendingTransactionCollectionStatus)
				require.True(t, pendingTransactions[0].TransactionInfo.IsPendingTransaction)

				require.Equal(t, pendingTransactions[1].TransactionInfo.Category, model.WalletTransactionCategory)
				require.Equal(t, pendingTransactions[1].TransactionInfo.Type, model.DeliveryFeeTransactionType, "should set delivery fee as pending")
				require.Equal(t, pendingTransactions[1].TransactionInfo.Amount, types.NewMoney(172))
				require.Equal(t, pendingTransactions[1].Status, model.PendingPendingTransactionCollectionStatus)
				require.True(t, pendingTransactions[1].TransactionInfo.IsPendingTransaction)

				require.Equal(t, pendingTransactions[2].TransactionInfo.Category, model.CreditTransactionCategory)
				require.Equal(t, pendingTransactions[2].TransactionInfo.Type, model.CommissionTransactionType, "should set delivery fee commission as pending")
				require.Equal(t, pendingTransactions[2].TransactionInfo.SubType, model.CommissionDeliveryFeeUserTransactionSubType, "should set commission subtype")
				require.Equal(t, pendingTransactions[2].TransactionInfo.Amount, types.NewMoney(20.2))
				require.Equal(t, pendingTransactions[2].Status, model.PendingPendingTransactionCollectionStatus)
				require.True(t, pendingTransactions[2].TransactionInfo.IsPendingTransaction)
				return nil
			})

			deps.driverTransSvc.EXPECT().ProcessDriverTransaction(gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
				gomock.Any(),
			).DoAndReturn(func(ctx context.Context, driverID string, channel model.TransactionChannel, action model.TransactionAction, status model.TransactionStatus, builder service.TransactionInfosBuilder, transactionOptions ...func(*service.ProcessDriverTransactionOption)) (model.DriverTransaction, []model.Transaction, error) {
				txnInfos, err := builder(mockedDriverTransaction)
				require.NoError(t, err)

				require.Len(t, txnInfos, 3)

				require.Equal(t, txnInfos[0].Category, model.WalletTransactionCategory)
				require.Equal(t, txnInfos[0].Type, model.TipTransactionType, "should contain tip transaction")
				require.Equal(t, txnInfos[0].Amount, types.NewMoney(9))
				require.Empty(t, txnInfos[0].PendingTransactionID)

				require.Equal(t, txnInfos[1].Type, model.OnTopTransactionType, "should contain on-top transaction")
				require.Equal(t, txnInfos[1].Category, model.WalletTransactionCategory)
				require.Equal(t, txnInfos[1].Amount, types.NewMoney(14))
				require.Empty(t, txnInfos[1].PendingTransactionID)

				require.Equal(t, txnInfos[2].Category, model.CreditTransactionCategory)
				require.Equal(t, txnInfos[2].Type, model.CommissionTransactionType, "should contain on-top commission transaction")
				require.Equal(t, txnInfos[2].SubType, model.CommissionDeliveryFeeDynamicOnTopTransactionSubType, "should set commission subtype")
				require.Equal(t, txnInfos[2].Amount, types.NewMoney(1.4))
				require.Empty(t, txnInfos[2].PendingTransactionID)

				return mockedDriverTransaction, []model.Transaction{}, nil
			})

			deps.drivRepo.EXPECT().GetTodayEarning(gomock.Any(), driverId).Return(en, nil)
			deps.driverRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driverEarnings ...model.DriverEarning) error {
				driverEarning := driverEarnings[0]
				require.Equal(t, types.Money(100+14), driverEarning.Fee, "should set fee correctly")
				require.Equal(t, types.Money(100), driverEarning.Commission, "should set commission correctly")
				require.Equal(t, types.Money(100+9.0), driverEarning.Tip, "should set tip correctly")
				return nil
			})

			ctx := context.Background()
			ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
			defer toggle.DisableAll()
			err := ts.ProcessFee(ctx, mockTrip())
			require.NoError(t, err)
		})
	})
}

func TestTripService_ProcessPendingFee(t *testing.T) {
	t.Parallel()

	driverId := "driver-1"
	tripId := "trip-1"
	orderId := "order-1"

	mockOrders := func() []model.Order {
		return []model.Order{
			{
				OrderID: orderId,
				TripID:  tripId,
				Driver:  driverId,
				Status:  model.StatusCompleted,
				Quote: model.Quote{
					ServiceType:           model.ServiceBike,
					RevenuePrincipalModel: false,
					DistributeRegions:     []model.RegionCode{"BKK"},
					Options:               model.OrderOptions{},
					PayAtStop:             1,
					Routes: []model.Stop{
						{},
						{
							PriceSummary: model.PriceSummary{
								DeliveryFee: model.DeliveryFeeSummary{
									OnTopFare:           100,
									OnTopWithholdingTax: 10,
									PaymentMethod:       model.PaymentMethodQRPromptPay,
								},
							},
						},
					},
				},
			},
		}
	}

	mockTrip := func() model.Trip {
		t := model.Trip{
			Status:   model.TripStatusCompleted,
			TripID:   tripId,
			DriverID: driverId,
			Routes: model.TripRoutes{
				{
					Action:     model.TripActionPickUp,
					StopOrders: model.TripStopOrders{{OrderID: orderId}},
				},
				{
					Action:     model.TripActionDropOff,
					StopOrders: model.TripStopOrders{{OrderID: orderId}},
				},
			},
			Orders: model.TripOrders{
				{OrderID: orderId, Status: model.StatusCompleted},
			},
			DriverWageSummary: model.TripDriverWageSummary{
				TotalDriverWage: 30,
				Commission:      2,
			},
		}

		return t
	}

	en := model.DriverEarning{
		DriverID:   driverId,
		Fee:        100,
		Commission: 100,
		Tax:        100,
		Tip:        100,
	}

	mockPendingTransactions := func() []model.PendingTransaction {
		return []model.PendingTransaction{
			{
				ID: primitive.NewObjectID(),
				TransactionInfo: domainModel.TransactionInfo{
					Category: model.WalletTransactionCategory,
					Type:     model.DeliveryFeeTransactionType,
					Amount:   30.0,
				},
			},
			{
				ID: primitive.NewObjectID(),
				TransactionInfo: domainModel.TransactionInfo{
					Category: model.WalletTransactionCategory,
					Type:     model.SubsidizeTransactionType,
					Amount:   10.0,
				},
			},
			{
				ID: primitive.NewObjectID(),
				TransactionInfo: domainModel.TransactionInfo{
					Category: model.CreditTransactionCategory,
					Type:     model.CommissionTransactionType,
					Amount:   2.0,
				},
			},
		}
	}

	mockTransactions := func(txnInfos []model.TransactionInfo) (txns []model.Transaction) {
		for _, txnInfo := range txnInfos {
			txns = append(txns, model.Transaction{
				Info: txnInfo,
			})
		}
		return
	}

	t.Run("process success when process by user resolved qr", func(t *testing.T) {
		t.Parallel()
		ts, deps, _ := newTestTripService(t)
		trip := mockTrip()
		orders := mockOrders()

		pendingTxns := mockPendingTransactions()

		txns := mockTransactions(model.ConvertPendingTransactionsToTransactionInfos(pendingTxns, false))

		deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)
		deps.driverTransSvc.EXPECT().GetAndUpdatePendingTransactionsByOrder(gomock.Any(), orderId, model.TransactionCreatedPendingTransactionCollectionStatus).Return(pendingTxns, nil)

		deps.driverTransSvc.EXPECT().ProcessDriverTransaction(gomock.Any(), driverId, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, txns, nil)

		deps.drivRepo.EXPECT().GetTodayEarning(gomock.Any(), driverId).Return(en, nil)
		deps.driverRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driverEarnings ...model.DriverEarning) error {
			driverEarning := driverEarnings[0]
			require.Equal(t, types.Money(100.0+20.0+10.0), driverEarning.Fee, "should set fee correctly")
			require.Equal(t, types.Money(100.0+2.0), driverEarning.Commission, "should set commission correctly")
			return nil
		})

		ctx := context.Background()
		ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
		defer toggle.DisableAll()
		err := ts.ProcessPendingFee(ctx, trip, false)
		require.NoError(t, err)
	})

	t.Run("process success when admin approved claim form", func(t *testing.T) {
		t.Parallel()
		ts, deps, _ := newTestTripService(t)
		trip := mockTrip()
		orders := mockOrders()

		pendingTxns := mockPendingTransactions()

		txns := mockTransactions(model.ConvertPendingTransactionsToTransactionInfos(pendingTxns, true))

		deps.orderRepo.EXPECT().GetMany(gomock.Any(), gomock.Any(), gomock.Any()).Return(orders, nil)
		deps.driverTransSvc.EXPECT().GetAndUpdatePendingTransactionsByOrder(gomock.Any(), orderId, model.TransactionCreatedPendingTransactionCollectionStatus).Return(pendingTxns, nil)

		deps.driverTransSvc.EXPECT().ProcessDriverTransaction(gomock.Any(), driverId, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(model.DriverTransaction{}, txns, nil)

		deps.drivRepo.EXPECT().GetTodayEarning(gomock.Any(), driverId).Return(en, nil)
		deps.driverRepo.EXPECT().SetTodayEarning(gomock.Any(), gomock.Any()).DoAndReturn(func(ctx context.Context, driverEarnings ...model.DriverEarning) error {
			driverEarning := driverEarnings[0]
			require.Equal(t, types.Money(100.0+20.0+10.0), driverEarning.Fee, "should set fee correctly")
			require.Equal(t, types.Money(100.0+2.0), driverEarning.Commission, "should set commission correctly")
			return nil
		})

		ctx := context.Background()
		ctx = toggle.EnableCtx(ctx, toggle.TripEnabled)
		defer toggle.DisableAll()
		err := ts.ProcessPendingFee(ctx, trip, true)
		require.NoError(t, err)
	})
}

func TestTripService_TryUpdateDistance0(t *testing.T) {
	t.Run("do nothing if everything is nil", func(t *testing.T) {
		ts, _, _ := newTestTripService(t)
		require.NoError(t, ts.TryUpdateDistance0(context.Background(), nil, nil, "", &model.Driver{}))
	})
	t.Run("do nothing if the new order is trivially of no consequence to other orders", func(t *testing.T) {
		ts, _, _ := newTestTripService(t)
		newOrderID := "newOrderID"
		require.NoError(t, ts.TryUpdateDistance0(context.Background(), &model.Trip{
			Routes: model.TripRoutes{{
				StopOrders: model.TripStopOrders{{OrderID: newOrderID, StopID: 0}},
			}, {
				StopOrders: model.TripStopOrders{{OrderID: newOrderID, StopID: 1}},
			}},
		}, nil, newOrderID, &model.Driver{}))
	})
	t.Run("don't update distance0 of past orders", func(t *testing.T) {
		ts, _, _ := newTestTripService(t)
		newOrderID := "newOrderID"
		require.NoError(t, ts.TryUpdateDistance0(context.Background(), &model.Trip{
			Routes: model.TripRoutes{{
				StopOrders: model.TripStopOrders{{OrderID: "oldOrderID", Done: true}},
			}, {
				StopOrders: model.TripStopOrders{{OrderID: newOrderID, StopID: 0}, {OrderID: "doneOrder", Done: true}},
			}, {
				StopOrders: model.TripStopOrders{{OrderID: newOrderID, StopID: 1}},
			}},
		}, nil, newOrderID, &model.Driver{}))
	})
	t.Run("update distance0 of next trip", func(t *testing.T) {
		ts, deps, _ := newTestTripService(t)
		newOrderID := "newOrderID"
		b2bOrderID := "b2bOrderID"
		b2bOrderID2 := "b2bOrderID2"
		nextTripID := "nextTripID"
		locA := model.Location{Lat: 50, Lng: 200}
		locB := model.Location{Lat: 75, Lng: 215}
		b2bOrder := &model.Order{OrderID: b2bOrderID, Quote: model.Quote{Routes: []model.Stop{{}, {}}}}
		deps.mapSvc.EXPECT().FindFastestRoute(gomock.Any(), mapservice.Location(locA), mapservice.Location(locB), gomock.Any()).Return(&model.MapRoute{Distance: 5.0, Duration: 3}, nil, nil)
		deps.onTopFareSvc.EXPECT().GetOnTopFareAtAccept(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]model.OnTopFare{}, nil).AnyTimes()
		deps.orderRepo.EXPECT().Get(gomock.Any(), b2bOrderID).Return(b2bOrder, nil)
		deps.orderRepo.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), *b2bOrder).Return(nil)
		deps.tripRepo.EXPECT().UpdateDistance0(gomock.Any(), gomock.Any())
		require.NoError(t, ts.TryUpdateDistance0(context.Background(), &model.Trip{
			Routes: model.TripRoutes{{
				StopOrders: model.TripStopOrders{{OrderID: newOrderID, StopID: 0}},
			}, {
				Location:   locA,
				StopOrders: model.TripStopOrders{{OrderID: newOrderID, StopID: 1}},
			}},
		}, &model.Trip{
			TripID: nextTripID,
			Routes: model.TripRoutes{{
				Location:   locB,
				StopOrders: model.TripStopOrders{{OrderID: b2bOrderID, StopID: 0}, {OrderID: b2bOrderID2, StopID: 0}},
			}, {
				StopOrders: model.TripStopOrders{{OrderID: b2bOrderID, StopID: 1}},
			}, {
				StopOrders: model.TripStopOrders{{OrderID: b2bOrderID2, StopID: 1}},
			}},
		}, newOrderID, &model.Driver{}))
	})
	t.Run("update distance0 within trip", func(t *testing.T) {
		ts, deps, _ := newTestTripService(t)
		newOrderID := "newOrderID"
		moOrderID := "moOrderID"
		moOrder2ID := "moOrder2ID"
		dist := types.Distance(5.0)
		dur := model.DurationSecond(2)
		dist2 := types.Distance(7.0)
		dur2 := model.DurationSecond(3)
		moOrder2 := &model.Order{OrderID: moOrder2ID, Quote: model.Quote{Routes: []model.Stop{{}, {}}}}
		deps.orderRepo.EXPECT().Get(gomock.Any(), moOrder2ID).Return(moOrder2, nil)
		deps.orderRepo.EXPECT().SetDistance0AndPriceSummary(gomock.Any(), gomock.Any()).
			DoAndReturn(func(_ context.Context, ord model.Order) error {
				require.Equal(t, moOrder2ID, ord.OrderID)
				return nil
			})
		require.NoError(t, ts.TryUpdateDistance0(context.Background(), &model.Trip{
			Routes: model.TripRoutes{{
				Distance:              dist,
				EstimatedDeliveryTime: dur,
				StopOrders:            model.TripStopOrders{{OrderID: newOrderID, StopID: 0}, {OrderID: moOrderID, StopID: 0}},
			}, {
				Distance:              dist2,
				EstimatedDeliveryTime: dur2,
				StopOrders:            model.TripStopOrders{{OrderID: moOrder2ID, StopID: 0}},
			}, {
				StopOrders: model.TripStopOrders{{OrderID: newOrderID, StopID: 1}},
			}, {
				StopOrders: model.TripStopOrders{{OrderID: moOrderID, StopID: 1}},
			}, {
				StopOrders: model.TripStopOrders{{OrderID: moOrder2ID, StopID: 1}},
			}},
		}, nil, newOrderID, &model.Driver{}))
	})
}

func TestValidateDriverBalanceEnoughAgent(t *testing.T) {
	tcs := []struct {
		orderFlow              string
		wallet                 float64
		credit                 float64
		sub                    float64
		coupon                 float64
		foodFee                float64
		expected               bool
		transfer               float64
		outstanding            float64
		DeliveryFee            float64
		Commission             float64
		WithholdingTax         float64
		totalAdditionalService float64
	}{
		// cash collection - credit enough
		{"CASH_COLLECTION", 135, 5, 10, 0, 140, true, 142.02, 0, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 140, 0, 10, 0, 140, true, 147.02, 0, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 150, 0, 10, 0, 140, true, 147.02, 0, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 0, 210, 0, 0, 200, true, 0, 0, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 0, 299, 40, 0, 300, true, 8.02, 0, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 0, 310, 40, 0, 300, true, 0, 0, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 100, 250, 0, 0, 300, true, 57.02, 0, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 100, 500, 0, 0, 300, true, 0, 0, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 50, 100, 30, 10, 140, true, 47.02, 0, 40, 0.15, 0.03, 0},

		// cash collection - credit not enough
		{"CASH_COLLECTION", 100, 100, 40, 0, 300, false, 140, 67.02, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 0, 100, 0, 40, 140, false, 40, 7.02, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 0, 100, 30, 10, 140, false, 40, 7.02, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 0, 100, 0, 0, 300, false, 0, 207.02, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", 0, -100, 40, 0, 300, false, 40, 267.02, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", -100, 100, 40, 0, 300, false, 0, 207.02, 40, 0.15, 0.03, 0},
		{"CASH_COLLECTION", -100, -100, 40, 0, 300, false, 0, 307.02, 40, 0.15, 0.03, 0},

		// non-cash collection - credit enough
		{"", 10, 10, 10, 0, 140, true, 0, 0, 40, 0.15, 0.03, 0},
		{"", 100, 500, 0, 0, 300, true, 0, 0, 40, 0.15, 0.03, 0},
		{"", 0, 0, 0, 10, 140, true, 7.02, 0, 40, 0.15, 0.03, 0},
		{"", 0, 0, 0, 10, 0, true, 8.52, 0, 40, 0.15, 0.03, 10},

		// non-cash collection - credit not enough
		{"", 0, 5, 0, 0, 140, false, 0, 2.02, 40, 0.15, 0.03, 0},
		{"", 0, 0, 0, 0, 140, false, 0, 7.02, 40, 0.15, 0.03, 0},
		{"", 0, -100, 0, 0, 140, false, 0, 7.02, 40, 0.15, 0.03, 0},
		{"", -100, 0, 0, 0, 140, false, 0, 7.02, 40, 0.15, 0.03, 0},
		{"", -100, -100, 0, 0, 140, false, 0, 7.02, 40, 0.15, 0.03, 0},
		{"", -100, -100, 0, 0, 0, false, 0, 28.02, 40, 0.15, 0.03, 140},

		// Without WithholdingTax
		{"CASH_COLLECTION", 135, 5, 10, 0, 140, true, 141, 0, 40, 0.15, 0.0, 0},
		{"CASH_COLLECTION", 135, 500, 10, 0, 140, true, 0, 0, 200, 0.15, 0.0, 0},
		{"CASH_COLLECTION", 100, 100, 40, 0, 300, false, 140, 66, 40, 0.15, 0.00, 0},
	}
	for _, tc := range tcs {
		driverTrans := *model.NewDriverTransaction("driverID")

		discounts := model.DiscountList{}
		if tc.coupon > 0 {
			discounts = append(discounts, model.Discount{Type: model.DiscountTypeCoupon, Category: "", Code: "", Discount: tc.coupon})
		}

		if tc.sub > 0 {
			discounts = append(discounts, model.Discount{Type: model.DiscountTypeSubsidize, Category: "", Code: "", Discount: tc.sub})
		}

		order := &model.Order{
			Quote: model.Quote{
				ServiceType: model.ServiceFood,
				Routes: []model.Stop{
					{},
					{
						CollectPayment: true,
						PriceSummary: model.PriceSummary{
							DeliveryFee: model.DeliveryFeeSummary{
								SubTotal:  tc.DeliveryFee,
								Discounts: discounts,
								AdditionalServiceFee: model.AdditionalServiceSummary{
									Total: tc.totalAdditionalService,
								},
							},
							ItemFee: model.ItemFeeSummary{Total: tc.foodFee},
						},
					},
				},
			},
		}

		order.Options.DriverMoneyFlow = model.DriverMoneyFlow(tc.orderFlow)
		driverTrans.WalletBalance = types.NewMoney(tc.wallet)
		driverTrans.PurchaseCreditBalance = types.NewMoney(tc.credit)
		order.SetCommission(tc.Commission, 0.0)
		order.SetWithholdingTax(tc.WithholdingTax)

		trip := model.Trip{}
		trip.DriverWageSummary = model.TripDriverWageSummary{
			BaseWage:                  types.NewMoney(order.PriceSummary().DeliveryFee.RawBaseFee),
			DistanceWage:              types.NewMoney(0),
			AdditionServiceWage:       types.NewMoney(order.PriceSummary().DeliveryFee.AdditionalServiceFee.Total),
			TotalDriverWage:           types.NewMoney(order.PriceSummary().DeliveryFee.SubTotal),
			ShiftDeduction:            types.NewMoney(order.PriceSummary().DeliveryFee.ShiftPriceValue),
			Commission:                types.NewMoney(order.PriceSummary().DeliveryFee.Commission),
			AdditionServiceCommission: types.NewMoney(order.PriceSummary().DeliveryFee.AdditionalServiceFee.Commission),
			WithHoldingTax:            types.NewMoney(order.PriceSummary().DeliveryFee.WithholdingTax),
		}

		act := (*model.Order).ValidateDriverBalanceEnoughAgent
		transfer, outstanding := act(order, trip, driverTrans)

		require.Equal(t, types.Money(tc.transfer), types.Money(math.Round(transfer.Float64()*100)/100))
		require.Equal(t, types.Money(tc.outstanding), types.Money(math.Round(outstanding.Float64()*100)/100))
	}
}

func newTestTripService(t *testing.T) (*service.TripService, *tripServiceDeps, func()) {
	return newTestTripServiceWithConfig(t, config.TripConfig{
		CommissionRate:                 0,
		WithHoldingTaxRate:             0.03,
		MultipleOrderWeight:            []float64{1, 0.7, 0.53},
		MultipleOrderMinimumDriverWage: 8,
	})
}

func newTestTripServiceWithConfig(t *testing.T, tripConfig config.TripConfig) (*service.TripService, *tripServiceDeps, func()) {
	ctrl := gomock.NewController(t)
	legacyGoMockCtrl := legacygomock.NewController(t)

	acfg := config.NewAtomicTripConfig(tripConfig)
	tripRepo := mock_repository.NewMockTripRepository(legacyGoMockCtrl)
	orderRepo := mock_repository.NewMockOrderRepository(legacyGoMockCtrl)
	driverRepo := mock_repository.NewMockDriverRepository(legacyGoMockCtrl)
	txnHelper := mock_transaction.NewMockTxnHelper(legacyGoMockCtrl)
	mapSvc := mock_mapservice.NewMockMapService(legacyGoMockCtrl)
	deliveryFeeSvc := mock_service.NewMockDeliveryFeeService(legacyGoMockCtrl)
	stubMeter := testmetric.NewStubMeter()
	banSvc := mock_service.NewMockBanService(legacyGoMockCtrl)
	drivSvc := mock_service.NewMockDriverServiceInterface(legacyGoMockCtrl)
	repSvc := mock_rep.NewMockREPService(legacyGoMockCtrl)
	driverTransSvc := mock_service.NewMockDriverTransactionServiceV2(legacyGoMockCtrl)
	serviceAreaRepo := mock_repository.NewMockServiceAreaRepository(legacyGoMockCtrl)
	onTopFareSvc := mock_service.NewMockOnTopFareService(legacyGoMockCtrl)
	rewardProcessor := mock_service.NewMockRewardTransactionProcessor(legacyGoMockCtrl)
	delivery := mock_delivery.NewMockDelivery(legacyGoMockCtrl)
	mockOnTopInstallmentTransactionProviderSelector := mock_drivertransaction.NewMockTransactionProviderSelector[model.OnTopFareScheme, *model.TransactionInfo](ctrl)
	pendingTransactionRepo := mock_repository.NewMockPendingTransactionRepository(legacyGoMockCtrl)
	missionlogEventService := mock_service.NewMockMissionLogEventService(legacyGoMockCtrl)
	featureflagService := mock_featureflag.NewMockService(legacyGoMockCtrl)
	serviceOptInReminder := mock_service.NewMockServiceOptInReminderService(legacyGoMockCtrl)

	serviceOptInReminder.EXPECT().TryToMarkForReminder(gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	deps := &tripServiceDeps{
		cfg:                    acfg,
		driverRepo:             driverRepo,
		tripRepo:               tripRepo,
		orderRepo:              orderRepo,
		driverTransSvc:         driverTransSvc,
		txnHelper:              txnHelper,
		mapSvc:                 mapSvc,
		banSvc:                 banSvc,
		drivSvc:                drivSvc,
		drivRepo:               driverRepo,
		repSvc:                 repSvc,
		deliveryFeeSvc:         deliveryFeeSvc,
		serviceAreaRepo:        serviceAreaRepo,
		onTopFareSvc:           onTopFareSvc,
		rewardProcessor:        rewardProcessor,
		delivery:               delivery,
		pendingTransactionRepo: pendingTransactionRepo,
		missionlogEventService: missionlogEventService,
		featureflagService:     featureflagService,
		serviceOptInReminder:   serviceOptInReminder,
	}

	tr := service.ProvideTripServices(
		deps.cfg,
		tripRepo,
		orderRepo,
		driverRepo,
		deliveryFeeSvc,
		txnHelper,
		mapSvc,
		stubMeter,
		banSvc,
		drivSvc,
		driverTransSvc,
		repSvc,
		serviceAreaRepo,
		onTopFareSvc,
		rewardProcessor,
		config.NewAtomicMapOverrideConfig(config.MapOverrideConfig{}),
		delivery,
		mockOnTopInstallmentTransactionProviderSelector,
		pendingTransactionRepo,
		missionlogEventService,
		featureflagService,
		serviceOptInReminder,
	)

	return tr, deps, func() { legacyGoMockCtrl.Finish() }
}

type tripServiceDeps struct {
	cfg                    *config.AtomicTripConfig
	tripRepo               *mock_repository.MockTripRepository
	orderRepo              *mock_repository.MockOrderRepository
	driverRepo             *mock_repository.MockDriverRepository
	driverTransSvc         *mock_service.MockDriverTransactionServiceV2
	txnHelper              *mock_transaction.MockTxnHelper
	mapSvc                 *mock_mapservice.MockMapService
	deliveryFeeSvc         *mock_service.MockDeliveryFeeService
	banSvc                 *mock_service.MockBanService
	drivSvc                *mock_service.MockDriverServiceInterface
	drivRepo               *mock_repository.MockDriverRepository
	repSvc                 *mock_rep.MockREPService
	serviceAreaRepo        *mock_repository.MockServiceAreaRepository
	onTopFareSvc           *mock_service.MockOnTopFareService
	rewardProcessor        *mock_service.MockRewardTransactionProcessor
	delivery               *mock_delivery.MockDelivery
	pendingTransactionRepo *mock_repository.MockPendingTransactionRepository
	missionlogEventService *mock_service.MockMissionLogEventService
	featureflagService     *mock_featureflag.MockService
	serviceOptInReminder   *mock_service.MockServiceOptInReminderService
}

func assertTripCompleted(t *testing.T, trip *model.Trip) {
	require.Equal(t, model.TripStatusCompleted, trip.Status)
	require.Equal(t, len(trip.Routes)-1, trip.HeadTo)
	require.Equal(t, 0, len(trip.GetOngoingOrderIDs()))
	for i, r := range trip.Routes {
		require.True(t, r.IsAllOrdersDone(), fmt.Sprintf("trip route#%v", i))
	}
}

func assertTripOrderIsCompleted(t *testing.T, trip *model.Trip, orderID string) {
	isTripOrderCompleted := false
	for _, order := range trip.Orders {
		if order.OrderID == orderID && order.Status == model.StatusCompleted {
			isTripOrderCompleted = true
			break
		}
	}
	require.True(t, isTripOrderCompleted)

	countStopOrderDone := 0
	for _, r := range trip.Routes {
		for _, o := range r.StopOrders {
			if o.OrderID == orderID && o.Done {
				countStopOrderDone++
			}
		}
	}
	require.Equal(t, 2, countStopOrderDone)
}

func assertTripRoutesIsDone(t *testing.T, trip *model.Trip, indexes []int) {
	for _, idx := range indexes {
		require.True(t, trip.Routes[idx].IsAllOrdersDone(), fmt.Sprintf("trip route#%v", idx))
	}
}
