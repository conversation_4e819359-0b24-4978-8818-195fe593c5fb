package httplog

import (
	"encoding/csv"
	"encoding/json"
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

const tsLayout = "2006-01-02 15:04:05.000"

// LogFile contains http log rows.
type LogFile struct {
	file                 *os.File
	reader               *csv.Reader
	positionByHeaderName map[string]int
	rowIndex             int
	location             *time.Location
}

// Row represents http request log.
type Row struct {
	Timestamp time.Time
	Uri       string
	Query     string
	Method    string
	Body      string
	Payload   string
	Headers   map[string]interface{}
	Region    string
}

// NewLogFile creates new LogFile.
func NewLogFile(filename string) (*LogFile, error) {
	hl := LogFile{}

	location, err := time.LoadLocation("Asia/Tokyo")
	if err != nil {
		return nil, err
	}
	hl.location = location

	if err := hl.init(filename); err != nil {
		return nil, err
	}

	return &hl, nil
}

func (hl *LogFile) init(filename string) error {
	file, err := os.Open(filename)
	if err != nil {
		return err
	}

	hl.file = file
	hl.reader = csv.NewReader(hl.file)
	if err := hl.initHeaderPositionByName(); err != nil {
		_ = file.Close()
		return err
	}
	hl.rowIndex = 1

	return nil
}

func (hl *LogFile) initHeaderPositionByName() error {
	header, err := hl.reader.Read()
	if err != nil {
		return err
	}
	positionByHeaderName := make(map[string]int)
	for i, h := range header {
		positionByHeaderName[h] = i
	}
	expectedHeaders := []string{"timestamp", "uri", "query", "method", "req_content", "req_headers", "req_payload"}
	for _, h := range expectedHeaders {
		_, ok := positionByHeaderName[h]
		if !ok {
			return fmt.Errorf("header %s is missing", h)
		}
	}
	optionalHeaders := []string{"region"}
	for _, h := range optionalHeaders {
		_, ok := positionByHeaderName[h]
		if !ok {
			logrus.Warnf("header=%v header is missing.", h)
		}
	}

	hl.positionByHeaderName = positionByHeaderName
	return nil
}

// Read returns next Row. If there is no more row, returns io.EOF.
func (hl *LogFile) Read() (Row, error) {
	record, err := hl.reader.Read()
	if err != nil {
		return Row{}, err
	}

	ts, err := hl.parseTimestamp(record[hl.positionByHeaderName["timestamp"]])
	if err != nil {
		return Row{}, err
	}

	headerString := record[hl.positionByHeaderName["req_headers"]]
	headerString = strings.ReplaceAll(headerString, "'", "\"")
	headers := make(map[string]interface{})
	if err := json.Unmarshal([]byte(headerString), &headers); err != nil {
		return Row{}, err
	}

	r := Row{
		Timestamp: ts,
		Uri:       record[hl.positionByHeaderName["uri"]],
		Query:     record[hl.positionByHeaderName["query"]],
		Method:    record[hl.positionByHeaderName["method"]],
		Body:      record[hl.positionByHeaderName["req_content"]],
		Payload:   record[hl.positionByHeaderName["req_payload"]],
		Headers:   headers,
	}

	regionIndex, ok := hl.positionByHeaderName["region"]
	if ok {
		r.Region = record[regionIndex]
	}

	return r, nil
}

// Close close file
func (hl *LogFile) Close() error {
	if err := hl.file.Close(); err != nil {
		return err
	}
	return nil
}

func (hl *LogFile) parseTimestamp(tsString string) (time.Time, error) {
	ts, err := time.ParseInLocation(tsLayout, tsString, hl.location)
	if err != nil {
		i, err := strconv.ParseInt(tsString, 10, 64)
		if err != nil {
			return time.Now(), err
		}
		return time.Unix(i, 0), nil
	}

	return ts, nil
}
