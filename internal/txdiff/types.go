package txdiff

import "git.wndv.co/lineman/fleet-distribution/internal/utils/fpresult"

type Id = string
type Index = int

type TxToCheck interface {
	Id() Id
}

type TxDiff[Tx TxToCheck] interface {
	MarkAsExisting(Index, Tx)
	MarkAsUnseen(Tx)
}

type MasterCollection[Master TxMaster] interface {
	Length() int
	AtIndex(Index) (Master, error)
}

type IndexedMasterCollection[Master TxMaster] interface {
	MasterCollection[Master]
	GetIdIndex(Id) fpresult.T[Index]
}

type TxMaster interface {
	Id() Id
}
