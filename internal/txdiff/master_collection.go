package txdiff

import (
	"github.com/pkg/errors"

	"git.wndv.co/lineman/fleet-distribution/internal/utils/fpresult"
)

type sliceMasterCollection[Master TxMaster] struct {
	data     []Master
	indexMap map[Id]Index
}

func (c sliceMasterCollection[Master]) Length() int {
	return len(c.data)
}

func (c sliceMasterCollection[Master]) AtIndex(idx Index) (Master, error) {
	if idx < 0 || len(c.data) <= idx {
		var zero Master
		return zero, errors.New("index out of range")
	}
	return c.data[idx], nil
}

func (c sliceMasterCollection[Master]) GetIdIndex(q Id) fpresult.T[Index] {
	idx, exists := c.indexMap[q]
	if !exists {
		return fpresult.Error[Index](errors.New("transaction id not found"))
	}
	return fpresult.Value(idx)
}

func NewIndexedSliceMasterCollection[Master TxMaster](data []Master) (IndexedMasterCollection[Master], error) {
	collection := sliceMasterCollection[Master]{data: data}
	indexMap, err := newIndexMapFromMasterCollection[Master](collection)
	if err != nil {
		return nil, err
	}
	collection.indexMap = indexMap
	return collection, nil
}
