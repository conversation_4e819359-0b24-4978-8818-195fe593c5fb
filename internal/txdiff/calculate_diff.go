package txdiff

import (
	"git.wndv.co/lineman/fleet-distribution/internal/utils/fpresult"
)

func calculateCitiTransactionDiffUsingIdIndex[Diff TxDiff[Tx], Tx TxToCheck](getIndexOfId func(Id) fpresult.T[Index]) func(fpresult.T[Diff], fpresult.T[Tx]) fpresult.T[Diff] {
	// core logic to calculate diff
	combineDiffAndTx := func(diff Diff, tx Tx) Diff {
		idx := getIndexOfId(tx.Id())
		if idx.IsOk() {
			diff.MarkAsExisting(idx.Value(), tx)
		} else {
			diff.<PERSON>en(tx)
		}
		return diff
	}

	// return a new function that can handle error cases
	return func(diff fpresult.T[Diff], tx fpresult.T[Tx]) fpresult.T[Diff] {
		if !diff.IsOk() {
			return diff
		}
		if !tx.IsOk() {
			return fpresult.Error[Diff](tx.Error())
		}
		return fpresult.Value(combineDiffAndTx(diff.Value(), tx.Value()))
	}
}
