// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package ittest

import (
	"git.wndv.co/go/unleash/lmwnunleash"
	"git.wndv.co/go/unleash/provider"
	"git.wndv.co/go/unleash/strategies"
	"git.wndv.co/go/unleash/test"
	"git.wndv.co/lineman/fleet-distribution/cmd/api/http/router"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/account"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/admin"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/assignmentbenchmarks"
	bulk2 "git.wndv.co/lineman/fleet-distribution/internal/apis/bulk"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulk/bulk_incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkcoinconversioncsv"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkincentivecsv"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkontopcsv"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/bulkprocess"
	cron2 "git.wndv.co/lineman/fleet-distribution/internal/apis/cron"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/dbconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/dedicated_zone"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/deliveryfee"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driver"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverinsurance"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/driverorderinfo"
	egs2 "git.wndv.co/lineman/fleet-distribution/internal/apis/egs"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/etax_invoice"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/event"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/featureflagconfig"
	fraud2 "git.wndv.co/lineman/fleet-distribution/internal/apis/fraud"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/heatmap"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/hook"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentive"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/incentivesource"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/installment"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/internalapi"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/middlewares"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/ontopfare"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/dispatcherconfig"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/distribution"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/order/orderassigner"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/otp"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/partners/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/payment"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/phones"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/product"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/province"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rain_situation"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/rating"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/region"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/reward"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/shift"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/srvarea"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/summaryofchange"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttled_order"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/throttleddispatchdetail"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/trip"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/withholdingtaxcertificate"
	"git.wndv.co/lineman/fleet-distribution/internal/apis/zone"
	"git.wndv.co/lineman/fleet-distribution/internal/asynqclient"
	"git.wndv.co/lineman/fleet-distribution/internal/auth"
	"git.wndv.co/lineman/fleet-distribution/internal/aws"
	"git.wndv.co/lineman/fleet-distribution/internal/bulk"
	"git.wndv.co/lineman/fleet-distribution/internal/config"
	"git.wndv.co/lineman/fleet-distribution/internal/configlocator"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/bcp"
	cache2 "git.wndv.co/lineman/fleet-distribution/internal/connector/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/delivery"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/dispatcher"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/experimentplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/file"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetarea"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fleetorder"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/fraudadvisor"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/heatmapdemand"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/inet_client"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/kafcclient/kafcdistribution"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/line"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/lineinternal"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/locker"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mapservice/manmap"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/matchrate"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mongoprofiler"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mongotxn"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/mq"
	polygon2 "git.wndv.co/lineman/fleet-distribution/internal/connector/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/prediction"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/riderlevel"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/slack"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/sms"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uobclient"
	"git.wndv.co/lineman/fleet-distribution/internal/connector/uwterror"
	"git.wndv.co/lineman/fleet-distribution/internal/cron"
	"git.wndv.co/lineman/fleet-distribution/internal/croninterval"
	"git.wndv.co/lineman/fleet-distribution/internal/datastore"
	"git.wndv.co/lineman/fleet-distribution/internal/di"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/accountinghub"
	bcp2 "git.wndv.co/lineman/fleet-distribution/internal/domain/service/bcp"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/drivertransaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/form"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/fraud"
	order2 "git.wndv.co/lineman/fleet-distribution/internal/domain/service/order"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/pendingtransaction"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/service/transaction"
	"git.wndv.co/lineman/fleet-distribution/internal/email"
	event2 "git.wndv.co/lineman/fleet-distribution/internal/eventrunner/event"
	"git.wndv.co/lineman/fleet-distribution/internal/featureflag"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/chat"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/coinplan"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/dapfeast"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/driverprovision"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/egs"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/egsbranch"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/egsorder"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/featureplatform"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/fleetpool"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/formservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/inventoryservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/marketplace"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/polygon"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/priceintervention"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/rainservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/translationservice"
	"git.wndv.co/lineman/fleet-distribution/internal/grpc/user"
	"git.wndv.co/lineman/fleet-distribution/internal/httpclient"
	"git.wndv.co/lineman/fleet-distribution/internal/income"
	"git.wndv.co/lineman/fleet-distribution/internal/income/aggregate"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/cache"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/executor"
	"git.wndv.co/lineman/fleet-distribution/internal/infrastructure/persistence"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/cleanup"
	"git.wndv.co/lineman/fleet-distribution/internal/lib/localcache"
	"git.wndv.co/lineman/fleet-distribution/internal/messages"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/firebase"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/socketio"
	"git.wndv.co/lineman/fleet-distribution/internal/notification/stub"
	"git.wndv.co/lineman/fleet-distribution/internal/pdf"
	"git.wndv.co/lineman/fleet-distribution/internal/preload"
	"git.wndv.co/lineman/fleet-distribution/internal/safe"
	"git.wndv.co/lineman/fleet-distribution/internal/scheduler"
	"git.wndv.co/lineman/fleet-distribution/internal/serviceprovider"
	"git.wndv.co/lineman/fleet-distribution/internal/telemetry/metric"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/providers"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil/testdata"
	"git.wndv.co/lineman/fleet-distribution/internal/tmpl"
	"github.com/golang/mock/gomock"
	gomock2 "go.uber.org/mock/gomock"
	"testing"
)

// Injectors from containers.go:

func InitializeContainer(t *testing.T, legacyController *gomock.Controller, controller *gomock2.Controller) (*IntegrationTestContainer, func(), error) {
	initFirst := di.ProvideInit1()
	initTestDBConn, cleanup2 := di.ProvideInitTestDBConn()
	conn, cleanup3 := di.ProvideDBConnectionForTest(initTestDBConn)
	topkekForTest := providers.ProvideTopkekForTest(conn)
	fixtures := testdata.ProvideFixtures()
	initTestData := di.ProvideInitTestData(fixtures, conn)
	initMetric := di.ProvideInitMetric()
	initModel := di.ProvideInitModel()
	containerInitializer := IntegrationTestInitializer(initFirst, initTestDBConn, topkekForTest, initTestData, initMetric, initModel)
	vosConfig := file.ProvideVosConfig()
	vosInternalConfig := service.ProvideVosInternalConfig()
	vosFleetConfig := service.ProvideVosFleetConfig()
	vosServiceImpl := service.ProvideVOSServiceImpl(vosConfig, vosInternalConfig, vosFleetConfig)
	driversDataStore := persistence.ProvideDriversDataStore(conn)
	redisConfig, cleanup4, err := providers.ProvideRedisTestConfig()
	if err != nil {
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	redisConn, cleanup5 := datastore.ProvideRedisConn(redisConfig)
	redisClient := datastore.ProvideRedis(redisConn, redisConfig)
	dataStoreInterface := persistence.ProvideAuditLogDataStore(conn)
	prometheusMeter := metric.ProvidePrometheusMeter()
	proxyAuditLogRepository := persistence.ProvideAuditLogRepository(dataStoreInterface, prometheusMeter)
	locationRedisConfig, cleanup6, err := providers.ProvideLocationRedisTestConfig()
	if err != nil {
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	locationRedisClient, cleanup7 := persistence.ProvideLocationRedisClient(locationRedisConfig)
	dbConfigDataStore := config.ProvideDBConfigDataStore(conn)
	proxyDBConfigRepository := config.ProvideDBConfigRepository(dbConfigDataStore, prometheusMeter)
	configUpdaterConfig := config.ProvideConfigUpdaterConfig()
	dbConfigUpdater := config.ProvideConfigUpdater(proxyDBConfigRepository, configUpdaterConfig)
	atomicDriverLocationConfig := persistence.ProvideDriverLocationTestConfig(dbConfigUpdater)
	stubMapService := manmap.ProvideStubMapService()
	proxyDriverLocationRepository, cleanup8 := persistence.ProvideDriverLocationRepositoryForTest(locationRedisClient, atomicDriverLocationConfig, stubMapService, prometheusMeter)
	attendanceRateConfig := config.ProvideAttendanceRateConfig()
	proxyDriverRepository := persistence.ProvideDriverRepository(driversDataStore, redisClient, attendanceRateConfig, prometheusMeter)
	transactionDataStore := persistence.ProvideTransactionDataStore(conn)
	proxyTransactionRepository := persistence.ProvideDataStoreTransactionRepository(transactionDataStore, prometheusMeter)
	tripDataStore := persistence.ProvideTripDataStore(conn)
	proxyTripRepository := persistence.ProvideTripRepository(tripDataStore, prometheusMeter)
	orderDataStore := persistence.ProvideOrderDataStore(conn)
	orderRevisionDataStore := persistence.ProvideOrderRevisionDataStore(conn)
	orderConfig := persistence.ProvideOrderConfig()
	atomicRevisionConfig := persistence.ProvideRevisionConfig(dbConfigUpdater)
	v := config.ProvideAtomicMongoTxnConfig(dbConfigUpdater)
	secondaryDBConnection, cleanup9 := datastore.ProvideSecondaryDBConnection()
	txnHelper := mongotxn.ProvideMongoTxnHelper(prometheusMeter, conn, v, secondaryDBConnection)
	proxyOrderRepository := persistence.ProvideMongoOrderRepository(orderDataStore, orderRevisionDataStore, redisClient, orderConfig, prometheusMeter, atomicRevisionConfig, txnHelper)
	uobRefDataStore := persistence.ProvideUobRefDataStore(conn)
	proxyUobRefRepository := persistence.ProvideMongoUobRefRepository(uobRefDataStore, prometheusMeter)
	serviceAreaDataStore := persistence.ProvideServiceAreaDataStore(conn)
	serviceAreaRepositoryConfig := config.ProvideServiceAreaRepositoryConfig()
	localcacheConfig := localcache.ProvideLocalCacheConfig()
	caches := localcache.ProvideLocalCache(localcacheConfig)
	fleetAreaClientConfig := fleetarea.ProvideFleetAreaClientConfig()
	fleetAreaClient, cleanup10 := fleetarea.ProvideFleetAreaClient(fleetAreaClientConfig)
	unleashConfig := featureflag.ProvideUnleashConfig()
	configUnleashConfig := provider.ProvideUnleashConfig()
	v2 := strategies.ProvideStrategies(configUnleashConfig)
	unleashCustomizer := lmwnunleash.ProvideDefaultCustomizer()
	simpleUnleasher := test.ProvideSimpleUnleasher()
	featureflagService, cleanup11 := featureflag.ProvideFeatureFlagServiceForIntegration(unleashConfig, v2, unleashCustomizer, simpleUnleasher)
	proxyServiceAreaRepository := persistence.ProvideMongoServiceAreaRepository(serviceAreaDataStore, redisClient, serviceAreaRepositoryConfig, caches, prometheusMeter, fleetAreaClient, featureflagService)
	proxyDriverActiveTimeRepository := persistence.ProvideRedisDriverActiveTimeRepository(redisClient, prometheusMeter)
	driverRatingConfig := config.ProvideDriverRatingConfig()
	paymentConfig := config.ProvidePaymentConfig()
	globalConfig := config.ProvideGlobalConfig()
	v3 := config.ProvideAtomicCancellationRateConfig(dbConfigUpdater)
	redisLocker := locker.ProvideRedisLocker(redisClient)
	atomicDriverServiceConfig := service.ProvideAtomicDriverServiceConfig(dbConfigUpdater)
	deviceManagerImpl := service.ProvideDeviceManagerImpl(driversDataStore, redisClient)
	deliveryConfig := delivery.ProvideDeliveryConfig()
	clientConfig := delivery.ProvideDeliveryClientConfig()
	client := httpclient.ProvideDefaultClient()
	stubDeliveryFleetApi := delivery.ProvideStubDeliveryFleetApi(clientConfig, client)
	deliveryDelivery := delivery.ProvideDelivery(deliveryConfig, stubDeliveryFleetApi)
	driverService := service.ProvideDriverService(driversDataStore, redisClient, proxyAuditLogRepository, proxyDriverLocationRepository, proxyDriverRepository, proxyTransactionRepository, proxyTripRepository, proxyOrderRepository, proxyUobRefRepository, proxyServiceAreaRepository, proxyDriverActiveTimeRepository, driverRatingConfig, paymentConfig, globalConfig, v3, prometheusMeter, redisLocker, atomicDriverServiceConfig, deviceManagerImpl, deliveryDelivery)
	locationManagerConfig := config.ProvideLocationManagerConfig()
	v4 := config.ProvideGlobalServiceAreaConfig(dbConfigUpdater)
	v5 := config.ProvideAtomicPredictionServiceConfig(dbConfigUpdater)
	servicePreferenceKillSwitchService := service.ProvideServicePreferenceKillSwitchService(featureflagService)
	servicePreferenceService := service.ProvideServicePreferenceService(servicePreferenceKillSwitchService, v4)
	serviceAreaService := service.ProvideServiceAreaServiceImpl(v4, v5, proxyServiceAreaRepository, servicePreferenceService)
	fleetpoolConfig := fleetpool.ProvideConfig()
	connectionFactory, err := providers.ProvideServiceGRPCConnFactory()
	if err != nil {
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	grpcClient, cleanup12 := fleetpool.ProvideFleetPoolGRPCClient(fleetpoolConfig, connectionFactory)
	riderSearchServiceClient := fleetpool.ProvideRiderSearchServiceClient(grpcClient)
	metricsRegistryImpl := metric.ProvideMetricsRegistry(prometheusMeter)
	locationManagerImpl, cleanup13 := service.ProvideLocationManagerImpl(caches, proxyDriverLocationRepository, deliveryDelivery, proxyDriverRepository, proxyServiceAreaRepository, locationManagerConfig, serviceAreaService, proxyTripRepository, proxyOrderRepository, featureflagService, riderSearchServiceClient, metricsRegistryImpl)
	endpoint := line.ProvideEnvironmentConfig()
	linehttpClientConfig := line.ProvideLINEHTTPClientConfig(endpoint)
	lineClient := line.ProvideClientStub(linehttpClientConfig)
	bcpOrderDataStore := persistence.ProvideBCPOrderDataStore(conn)
	bcpOrderRepository := persistence.ProvideBCPOrderRepository(bcpOrderDataStore, prometheusMeter)
	proxyMinimalOrderRepository := persistence.ProvideMongoMinimalOrderRepository(orderDataStore, prometheusMeter)
	bcpTestClientStub := bcp.ProvideBCPTestClientStub()
	bcpConfig := bcp.ProvideBCPTestConfig(t, bcpTestClientStub)
	bcpClient, err := bcp.ProvideBCPClient(bcpConfig)
	if err != nil {
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	bcpStatusTransitionerService := bcp2.ProvideBCPStatusTransitionerService(bcpOrderRepository, proxyMinimalOrderRepository, bcpClient)
	consoleLogMessagingClient := stub.ProvideConsoleFBMessagingClient()
	atomicFirebaseConfig := firebase.ProvideFirebaseConfig(dbConfigUpdater)
	firebasePushNotificationService := firebase.ProvideFirebasePushNotificationService(consoleLogMessagingClient, atomicFirebaseConfig)
	socketioConfig := socketio.ProvideConfig()
	socketioClient := socketio.ProvideSocketIOClient(socketioConfig, client)
	atomicSocketIOConfig := socketio.ProvideSocketIOConfig(dbConfigUpdater)
	socketIOPushNotificationService := socketio.ProvideSocketIOPushNotificationService(socketioClient, atomicSocketIOConfig)
	overrideNotifyConfig := service.ProvideConfig()
	pushNotifier, cleanup14 := service.ProvidePushNotifier(firebasePushNotificationService, socketIOPushNotificationService, deviceManagerImpl, overrideNotifyConfig)
	repConfig := infrastructure.ProvideRepConfig()
	repEventBus, cleanup15 := infrastructure.ProvideRepEventBus(repConfig, prometheusMeter)
	mongoBanMetadataDataStore := persistence.ProvideBanMetadataDataStore(conn)
	proxyBanMetadataRepository := persistence.ProvideBanMetadataRepository(mongoBanMetadataDataStore, prometheusMeter)
	banHistoriesDataStore := persistence.ProvideBanHistoriesDataStore(conn)
	proxyBanHistoryRepository := persistence.ProvideBanHistoryServiceImpl(banHistoriesDataStore, prometheusMeter)
	driverTransactionDataStore := persistence.ProvideDriverTransactionDataStore(conn)
	proxyDriverTransactionRepository := persistence.ProvideDataStoreDriverTransactionRepository(driverTransactionDataStore, prometheusMeter)
	banServiceImpl := service.ProvideBanServiceImpl(proxyDriverRepository, locationManagerImpl, redisClient, pushNotifier, repEventBus, proxyBanMetadataRepository, proxyBanHistoryRepository, proxyDriverTransactionRepository)
	fraudConfig := fraudadvisor.ProvideFraudConfig()
	fraudAdvisorHTTPClient, cleanup16, err := httpclient.ProvideFraudAdvisorHTTPClientStub()
	if err != nil {
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	fraudAdvisorServiceImpl := fraudadvisor.ProvideFraudAdvisorServiceImpl(fraudConfig, fraudAdvisorHTTPClient, featureflagService)
	transactionFraudScoreDataStore := persistence.ProvideTransactionFraudScoreDataStore(conn)
	proxyTransactionFraudScoreRepository := persistence.ProvideMongoTransactionFraudScoreRepository(transactionFraudScoreDataStore, prometheusMeter)
	installmentDataStore := persistence.ProvideInstallmentDataStore(conn)
	proxyInstallmentRepository := persistence.ProvideInstallmentRepository(installmentDataStore, prometheusMeter)
	productGroupDataStore := persistence.ProvideProductGroupDataStore(conn)
	inventoryserviceConfig := inventoryservice.ProvideConfig()
	stubGRPCPriorityGroupService := inventoryservice.ProvideStubGRPCPriorityGroupService(inventoryserviceConfig)
	proxyProductGroupRepository := persistence.ProvideProductGroupRepository(productGroupDataStore, prometheusMeter, stubGRPCPriorityGroupService)
	stubGRPCProductService := inventoryservice.ProvideStubGRPCProductService(inventoryserviceConfig)
	driverTransactionConfig := config.ProvideDriverTransactionConfig()
	mongoRewardTransactionDataStore := persistence.ProvideMongoRewardTransactionDataStore(conn)
	proxyRewardTransactionRepository := persistence.ProvideMongoRewardTransactionRepository(mongoRewardTransactionDataStore, prometheusMeter)
	incentiveDataStore := incentive.ProvideIncentiveDataStore(conn)
	proxyIncentiveRepository := incentive.ProvideDataStoreIncentiveRepository(incentiveDataStore, prometheusMeter)
	incomeDailySummaryStore := persistence.ProvideIncomeDailySummaryStore(conn)
	proxyIncomeDailySummaryRepository := persistence.ProvideIncomeDailySummaryRepository(incomeDailySummaryStore, prometheusMeter)
	incomeSummaryServiceImpl := income.ProvideIncomeSummaryService(proxyTransactionRepository, proxyRewardTransactionRepository, proxyIncentiveRepository, proxyOrderRepository, proxyIncomeDailySummaryRepository)
	incomeAggregateServiceImpl := aggregate.ProvideIncomeAggregateService(incomeSummaryServiceImpl, proxyIncomeDailySummaryRepository)
	pendingTransactionDataStore := persistence.ProvidePendingTransactionDataStore(conn)
	proxyPendingTransactionRepository := persistence.ProvideDataStorePendingTransactionRepository(pendingTransactionDataStore, prometheusMeter)
	accountingHubTransactionService := accountinghub.ProvideAccountingHubTransactionService(featureflagService)
	accountingHubTransactionDataStore := persistence.ProvideAccountingHubTransactionDataStore(conn)
	proxyAccountingHubTransactionRepository := persistence.ProvideDataStoreAccountingHubTransactionRepository(accountingHubTransactionDataStore, prometheusMeter)
	driverTransactionServiceV2 := service.ProvideDriverTransactionServiceV2(proxyDriverTransactionRepository, proxyTransactionRepository, proxyInstallmentRepository, proxyProductGroupRepository, txnHelper, stubGRPCProductService, driverTransactionConfig, incomeAggregateServiceImpl, proxyPendingTransactionRepository, accountingHubTransactionService, proxyAccountingHubTransactionRepository)
	driverTransactionService := payment.ProvideDriverTransactionService(banServiceImpl, proxyDriverTransactionRepository, proxyTransactionRepository, fraudAdvisorServiceImpl, proxyTransactionFraudScoreRepository, driverService, proxyDriverRepository, paymentConfig, txnHelper, proxyInstallmentRepository, proxyProductGroupRepository, driverTransactionServiceV2)
	serviceAreaConfig := config.ProvideServiceAreaConfig()
	stubPolygonApi := providers.ProvideStubPolygonApiForTest(serviceAreaConfig)
	termAndConditionDataStore := persistence.ProvideTermAndConditionDataStore(conn)
	proxyTermAndConditionRepository := persistence.ProvideMongoTermAndConditionRepository(termAndConditionDataStore, prometheusMeter)
	termAndConditionServiceImpl := service.ProvideTermAndConditionServiceImpl(proxyTermAndConditionRepository)
	transactionService := payment.ProvideTransactionService(proxyTransactionRepository, proxyDriverTransactionRepository, proxyDriverRepository, paymentConfig, txnHelper)
	accountConfig := account.ProvideConfig()
	pdpaConfig := account.ProvidePDPAConfig()
	redisTokenStorage := auth.ProvideRedisTokenStore(redisClient)
	stubKafkaConnector := infrastructure.ProvideStubKafkaConnector()
	driverPeriodCompletedTripsConfig := config.ProvideDriverPeriodCompletedTripsConfig()
	accountServiceInterface := account.ProvideAccountService(redisClient)
	atomicOrderDBConfig := order.ProvideAtomicOrderDBConfig(dbConfigUpdater)
	orderAPIConfig := order.ProvideOrderAPIConfig(atomicOrderDBConfig)
	pdpaDataStore := persistence.ProvidePdpaDataStore(conn)
	proxyPdpaRepository := persistence.ProvidePdpaRepository(pdpaDataStore, prometheusMeter, redisClient)
	v6 := order.ProvideContingencyConfig(dbConfigUpdater)
	atomicDBConfig := account.ProvideDBConfig(dbConfigUpdater)
	driverProfileRequestConfig := config.ProvideDriverProfileRequestConfig()
	transactionSchemeDataStore := persistence.ProvideTransactionSchemeDataStore(conn)
	proxyTransactionSchemeRepository := persistence.ProvideMongoTransactionSchemeRepository(transactionSchemeDataStore, prometheusMeter)
	driverOrderInfoDataStore := persistence.ProvideDriverOrderInfoDataStore(conn)
	proxyDriverOrderInfoRepository := persistence.ProvideDriverOrderInfoRepository(driverOrderInfoDataStore, prometheusMeter)
	shiftDataStore := persistence.ProvideShiftDataStore(conn)
	proxyShiftRepository := persistence.ProvideShiftRepository(shiftDataStore, prometheusMeter)
	attendances := service.ProvideAttendances(proxyShiftRepository, proxyDriverRepository, proxyDriverOrderInfoRepository, attendanceRateConfig)
	v7 := config.ProvideAtomicTripConfig(dbConfigUpdater)
	deliveryFeeSettingDataStore := persistence.ProvideDeliveryFeeSettingDataStore(conn)
	deliveryFeeSettingRepositoryConfig := config.ProvideDeliveryFeeSettingRepositoryConfig()
	proxyDeliveryFeeSettingRepository := persistence.ProvideDeliveryFeeSettingMongo(deliveryFeeSettingDataStore, redisClient, deliveryFeeSettingRepositoryConfig, prometheusMeter)
	settingDeliveryFeePriceSchemesDataStore := persistence.ProvideSettingDeliveryFeePriceSchemesDataStore(conn)
	deliveryFeeSettingPriceSchemesRepositoryConfig := config.ProvideDeliveryFeeSettingPriceSchemesRepositoryConfig()
	proxySettingDeliveryFeePriceSchemesRepository := persistence.ProvideSettingDeliveryFeePriceSchemesRepository(settingDeliveryFeePriceSchemesDataStore, redisClient, deliveryFeeSettingPriceSchemesRepositoryConfig, prometheusMeter)
	deliveryFeeService := service.ProvideDeliveryFeeService(proxyDeliveryFeeSettingRepository, proxySettingDeliveryFeePriceSchemesRepository)
	onTopFareDataStore := persistence.ProvideOnTopFareDataStore(conn)
	zoneDataStore := persistence.ProvideZoneDataStore(conn)
	proxyZoneRepository := persistence.ProvideZoneRepository(zoneDataStore, prometheusMeter, featureflagService, fleetAreaClient)
	proxyOnTopFareRepository := persistence.ProvideOnTopFareRepository(onTopFareDataStore, proxyZoneRepository, prometheusMeter)
	onTopFareService := service.ProvideOnTopFareService(proxyOnTopFareRepository, driverService)
	mongoRewardBalanceDataStore := persistence.ProvideRewardBalanceDataStore(conn)
	proxyRewardBalanceRepository := persistence.ProvideRewardBalanceRepository(mongoRewardBalanceDataStore, prometheusMeter)
	mongoDailyRewardDataStore := persistence.ProvideMongoDailyRewardDataStore(conn)
	proxyDailyRewardRepository := persistence.ProvideMongoDailyRewardRepository(mongoDailyRewardDataStore, prometheusMeter)
	rewardService := service.ProvideRewardService(proxyRewardBalanceRepository, proxyDailyRewardRepository, proxyRewardTransactionRepository, proxyDriverRepository, txnHelper)
	atomicMapOverrideConfig := config.ProvideAtomicMapOverrideConfig(dbConfigUpdater)
	installmentOnTopTransactionProvider := drivertransaction.ProvideInstallmentOnTopTransactionProvider(proxyDriverRepository)
	onTopSchemeTransactionProviderSelector := drivertransaction.ProvideOnTopSchemeTransactionProviderSelector(installmentOnTopTransactionProvider)
	missionLogEventServiceConfig := service.ProvideMissionLogEventServiceConfig()
	imfKafkaProducerConfig := kafcclient.ProvideIMFKafkaProducerConfig()
	kafkaProducerStubClient, cleanup17 := kafcclient.ProvideIMFKafkaProducerClientForTest(imfKafkaProducerConfig)
	preloadExecutor := preload.ProvidePreloadExecutor()
	proxyRegionRepository := persistence.ProvideRegionRepository(stubPolygonApi, serviceAreaConfig, preloadExecutor, prometheusMeter)
	clientAreaDataStore := persistence.ProvideClientAreaDataStore(conn)
	proxyClientAreaRepository := persistence.ProvideMongoClientAreaRepository(clientAreaDataStore, caches, prometheusMeter)
	areaServiceImpl := service.ProvideAreaServiceImpl(stubPolygonApi, proxyRegionRepository, proxyClientAreaRepository, caches, servicePreferenceService)
	atomicBackToBackConfig := config.ProvideBackToBackConfig(dbConfigUpdater)
	driverServiceTypeCapacityService := service.ProvideDriverServiceTypeCapacityService(proxyServiceAreaRepository, proxyTripRepository, areaServiceImpl, featureflagService, atomicBackToBackConfig, proxyDriverRepository)
	missionLogEventService := service.ProvideMissionLogEventService(missionLogEventServiceConfig, kafkaProducerStubClient, proxyOrderRepository, proxyTripRepository, driverServiceTypeCapacityService)
	serviceOptInReminderRepository := persistence.ProvideOptInReminderRepository(redisClient, prometheusMeter)
	v8 := service.ProvideAtomicServiceOptInReminderServiceConfig(dbConfigUpdater)
	serviceOptInReminderService := service.ProvideServiceOptInReminderService(proxyDriverRepository, serviceOptInReminderRepository, proxyClientAreaRepository, pushNotifier, v8)
	tripService := service.ProvideTripServices(v7, proxyTripRepository, proxyOrderRepository, proxyDriverRepository, deliveryFeeService, txnHelper, stubMapService, prometheusMeter, banServiceImpl, driverService, driverTransactionServiceV2, repEventBus, proxyServiceAreaRepository, onTopFareService, rewardService, atomicMapOverrideConfig, deliveryDelivery, onTopSchemeTransactionProviderSelector, proxyPendingTransactionRepository, missionLogEventService, featureflagService, serviceOptInReminderService)
	driverAssignmentLogsDataStore := persistence.ProvideDriverAssignmentLogsDataStore(conn)
	proxyAssignmentLogRepository := persistence.ProvideMongoAssignmentLogRepository(driverAssignmentLogsDataStore, prometheusMeter)
	shiftServices := service.ProvideShiftServices(proxyShiftRepository, proxyDriverRepository, txnHelper)
	driverStatisticDataStore := persistence.ProvideDriverStatisticDataStore(conn)
	proxyDriverStatisticRepository := persistence.ProvideDataStoreDriverStatisticRepository(driverStatisticDataStore, prometheusMeter)
	statisticServiceImpl := service.ProvideStatisticServiceImpl(proxyDriverStatisticRepository, proxyAssignmentLogRepository, proxyDriverRepository, proxyIncentiveRepository)
	v9 := dispatcherconfig.ProvideAtomicDistributionConfig(dbConfigUpdater)
	predictionConfig := prediction.ProvidePredictionConfig()
	dalianClient := httpclient.ProvideDalianClient()
	predictionPrediction := prediction.ProvidePrediction(predictionConfig, dalianClient)
	distributionLogEventServiceConfig := service.ProvideDistributionLogEventServiceConfig()
	secureIMFKafkaProducerConfig := kafcclient.ProvideSecureIMFKafkaProducerConfig()
	secureKafkaProducerStubClient, cleanup18 := kafcclient.ProvideSecureIMFKafkaProducerClientForTest(secureIMFKafkaProducerConfig)
	distributionLogEventService := service.ProvideDistributionLogEventService(distributionLogEventServiceConfig, secureKafkaProducerStubClient)
	distributionLogManager := service.ProvideDistributionLogManager(distributionLogEventService)
	predictionService := service.ProvidePredictionService(serviceAreaService, proxyOrderRepository, predictionPrediction, stubMapService, v5, proxyTripRepository, tripService, v7, atomicMapOverrideConfig, distributionLogManager, proxySettingDeliveryFeePriceSchemesRepository, servicePreferenceService)
	atomicAutoAcceptConfig := dispatcherconfig.ProvideAutoAcceptConfig(dbConfigUpdater)
	atomicAutoAssignDbConfig := dispatcherconfig.ProvideAutoAssignDbConfig(dbConfigUpdater)
	atomicSupplyPositioningConfig := config.ProvideAtomicSupplyPositioningConfig(dbConfigUpdater)
	atomicDedicatedPriorityScorerConfig := config.ProvideAtomicDedicatedPriorityScorerConfig(dbConfigUpdater)
	autoAssignConfig := distribution.ProvideAutoAssignConfig(atomicBackToBackConfig, atomicAutoAcceptConfig, atomicAutoAssignDbConfig, atomicSupplyPositioningConfig, atomicDedicatedPriorityScorerConfig)
	cookingTimeDelayRepository := persistence.ProvideCookingTimeDelayRepository(redisClient, prometheusMeter)
	assignmentDataStore := persistence.ProvideAssignmentDataStore(conn)
	assignmentRepository := persistence.ProvideAssignmentRepository(assignmentDataStore, prometheusMeter)
	throttledOrderDBConfig := config.ProvideThrottledOrderConfig()
	throttledOrderDataStore := persistence.ProvideThrottledOrderDataStore(conn, secondaryDBConnection, throttledOrderDBConfig)
	throttledOrderRepository := persistence.ProvideThrottledOrderRepository(throttledOrderDataStore, prometheusMeter)
	orderDistributionEventServiceConfig := service.ProvideOrderDistributionEventServiceConfig()
	orderDistributionEventService := service.ProvideOrderDistributionEventService(orderDistributionEventServiceConfig, secureKafkaProducerStubClient)
	orderDistributionEventManager := service.ProvideOrderDistributionEventManager(orderDistributionEventService)
	unAcknowledgeReassignReposity := persistence.ProvideUnAcknowledgeReassignRepository(redisClient, prometheusMeter)
	uwterrorConfig := uwterror.ProvideConfig()
	stubGRPCFeaturePlatformClient := featureplatform.ProvideStubGPRCFeaturePlatformClient()
	stubUWTErrorService := uwterror.ProvideStubUWTErrorService(uwterrorConfig, stubGRPCFeaturePlatformClient, caches)
	atomicDistributionExperimentPlatformDbConfig := experimentplatform.ProvideDistributionExperimentPlatformDbConfig(dbConfigUpdater)
	distributionExperimentPlatformClient, cleanup19 := experimentplatform.ProvideDistributionExperimentPlatformClient(atomicDistributionExperimentPlatformDbConfig)
	acceptorDeps := order.ProvideAcceptorDeps(proxyOrderRepository, proxyDriverLocationRepository, driverService, proxyDriverRepository, stubMapService, proxyAssignmentLogRepository, pushNotifier, redisLocker, banServiceImpl, driverTransactionService, orderAPIConfig, repEventBus, stubKafkaConnector, proxyServiceAreaRepository, v6, serviceAreaService, tripService, proxyTripRepository, cookingTimeDelayRepository, predictionService, onTopFareService, assignmentRepository, throttledOrderRepository, deliveryDelivery, prometheusMeter, atomicMapOverrideConfig, orderDistributionEventManager, unAcknowledgeReassignReposity, stubUWTErrorService, metricsRegistryImpl, serviceOptInReminderService, distributionExperimentPlatformClient)
	acceptor := order.ProvideAcceptor(acceptorDeps)
	dedicatedZoneDataStore := persistence.ProvideDedicatedZoneDataStore(conn)
	dedicatedZoneRepository := persistence.ProvideDedicatedZoneRepository(dedicatedZoneDataStore, caches, prometheusMeter)
	throttledDispatchDetailDataStore := persistence.ProvideThrottledDispatchDetailDataStore(conn)
	throttledDispatchDetailRepository := persistence.ProvideThrottledDispatchDetailRepository(throttledDispatchDetailDataStore, proxyZoneRepository, prometheusMeter)
	dispatcherConfig := dispatcher.ProvideDispatcherConfig()
	dispatcherDispatcher := dispatcher.ProvideDriverServiceDispatcher(dispatcherConfig, client, featureflagService)
	deferredOrderDataStore := persistence.ProvideDeferredOrderDataStore(conn)
	deferredOrderRepository := persistence.ProvideDeferredOrderRepository(deferredOrderDataStore, prometheusMeter)
	atomicRainSituationConfig := service.ProvideAtomicRainSituationConfig(dbConfigUpdater)
	rainSituationDataStore := persistence.ProvideRainSituationDataStore(conn)
	proxyRainSituationRepository := persistence.ProvideRainSituationRepository(rainSituationDataStore, prometheusMeter)
	priceinterventionConfig := priceintervention.ProvideConfig()
	priceInterventionClientStub := priceintervention.ProvidePriceInterventionServiceClientStub(priceinterventionConfig)
	rainserviceConfig := rainservice.ProvideConfig()
	mockRainServiceClient := rainservice.ProvideRainServiceClientStub(rainserviceConfig, legacyController)
	slackConfig := slack.ProvideSlackConfig()
	slackStub := slack.ProvideSlackStub(slackConfig)
	rainedCache := service.ProvideRainedCache()
	rainSituationServiceImpl := service.ProvideRainSituationService(atomicRainSituationConfig, proxyRainSituationRepository, vosServiceImpl, proxyServiceAreaRepository, priceInterventionClientStub, mockRainServiceClient, globalConfig, slackStub, rainedCache)
	userConfig := user.ProvideConfig()
	mockUserServiceClient := user.ProvideStubGRPCUserService(userConfig, legacyController)
	workerContextConfig := safe.ProvideWorkerContextConfig()
	cleanupPriority := cleanup.ProvideCleanupPriority()
	workerContext := safe.ProvideWorkerContext(workerContextConfig, cleanupPriority)
	atomicOrderHeartbeatServiceDbConfig := service.ProvideOrderHeartbeatServiceConfig()
	proxyOrderHeartbeatRepository := persistence.ProvideOrderHearthBeatRepository(redisClient, prometheusMeter, workerContext)
	orderHeartbeatServiceImpl := service.ProvideOrderHeartbeatService(atomicOrderHeartbeatServiceDbConfig, proxyOrderHeartbeatRepository)
	distributionServiceConfig := service.ProvideDistributionServiceConfig()
	secureIMFKafkaSyncProducerConfig := kafcclientdistribution.ProvideSecureIMFKafkaSyncProducerConfig()
	secureIMFKafkaSyncProducerStubClient, cleanup20 := kafcclientdistribution.ProvideSecureIMFKafkaSyncProducerClientForTest(secureIMFKafkaSyncProducerConfig)
	distributionService := service.ProvideDistributionService(distributionServiceConfig, secureIMFKafkaSyncProducerStubClient)
	fleetOrderClientConfig := fleetorder.ProvideDispatcherConfig()
	fleetOrderClient := fleetorder.ProvideFleetOrderClient(fleetOrderClientConfig, client)
	illegalDriverRepository := persistence.ProvideIllegalDriverRepository(redisClient, prometheusMeter)
	assigningStateManager := distribution.ProvideAssigningStateManager(redisClient)
	autoAssignOrderDistributorDeps := distribution.ProvideAutoAssignOrderDistributorDeps(locationManagerImpl, proxyOrderRepository, proxyDriverRepository, pushNotifier, proxyAssignmentLogRepository, driverTransactionService, proxyDriverStatisticRepository, statisticServiceImpl, proxyDriverOrderInfoRepository, proxyIncentiveRepository, redisLocker, orderAPIConfig, prometheusMeter, repEventBus, v6, stubKafkaConnector, v9, predictionService, txnHelper, autoAssignConfig, acceptor, driverService, stubMapService, dedicatedZoneRepository, proxyDriverLocationRepository, proxyTripRepository, onTopFareService, throttledDispatchDetailRepository, throttledOrderRepository, dispatcherDispatcher, deferredOrderRepository, assignmentRepository, rainSituationServiceImpl, proxyServiceAreaRepository, proxyZoneRepository, mockUserServiceClient, redisClient, deliveryDelivery, workerContext, servicePreferenceService, distributionLogManager, orderHeartbeatServiceImpl, orderDistributionEventManager, distributionExperimentPlatformClient, distributionService, featureflagService, metricsRegistryImpl, fleetOrderClient, illegalDriverRepository, assigningStateManager, throttledOrderDBConfig)
	autoAssignOrderDistributor, cleanup21 := distribution.ProvideAutoAssignOrderDistributor(autoAssignOrderDistributorDeps)
	cancellerImpl := order.ProvideCanceller(proxyOrderRepository, driverService, proxyAssignmentLogRepository, pushNotifier, proxyDriverRepository, banServiceImpl, repEventBus, stubKafkaConnector, driverService, shiftServices, orderAPIConfig, v3, autoAssignOrderDistributor, txnHelper, tripService, proxyTripRepository, cookingTimeDelayRepository, onTopFareService, proxyDriverLocationRepository, stubMapService, redisLocker, proxyServiceAreaRepository, throttledOrderRepository, deliveryDelivery, prometheusMeter, serviceOptInReminderService, dispatcherDispatcher, featureflagService)
	internalCancelReasonDataStore := persistence.ProvideInternalCancelReasonDataStore(conn)
	cancelReasonConfig := config.ProvideCancelReasonConfig()
	proxyInternalCancelReasonRepository := persistence.ProvideInternalCancelReasonRepository(internalCancelReasonDataStore, prometheusMeter, redisClient, cancelReasonConfig)
	requestUpdateProfileDataStore := persistence.ProvideRequestUpdateProfileDataStore(conn)
	proxyRequestUpdateProfileRepository := persistence.ProvideRequestUpdateProfileRepository(requestUpdateProfileDataStore, prometheusMeter)
	mockFormServiceClient := formservice.ProvideStubGRPCFormService(legacyController)
	driverInsuranceDataStore := persistence.ProvideDriverInsuranceDataStore(conn)
	proxyDriverInsuranceRepository := persistence.ProvideInsuranceRepository(driverInsuranceDataStore, prometheusMeter)
	driverInsuranceConfig := account.ProvideDriverInsuranceConfig()
	mockEGSServiceClient := egs.ProvideStubGRPCEGSService(legacyController)
	incentiveProgressDataStore := incentive.ProvideIncentiveProgressDataStore(conn)
	proxyIncentiveProgressRepository := incentive.ProvideDataStoreIncentiveProgressRepository(incentiveProgressDataStore, prometheusMeter)
	proxyDriverLastUpdateLocationTrackerRepository := persistence.ProvideRedisLastUpdateLocationTrackerRepository(redisClient, prometheusMeter)
	driverUpdateLocationEventServiceConfig := service.ProvideUpdateDriverLocationEventConfig()
	driverUpdateLocationEventService := service.ProvideDriverLocationEventServiceImpl(driverUpdateLocationEventServiceConfig, kafkaProducerStubClient)
	mongoCoinCashConversionRateDataStore := persistence.ProvideCoinCashConversionRateDataStore(conn)
	coinCashConversionRateLocalCacheConfig := cache.ProvideCoinCashConversionRateLocalCacheConfig()
	coinConversionRateLocalCache := cache.ProvideCoinConversionRateLocalCache(coinCashConversionRateLocalCacheConfig)
	coinConversionRateRedisCache := cache.ProvideCoinConversionRateRedisCache(redisConn)
	coinConversionRateMinimalRedisCache := cache.ProvideCoinConversionRateMinimalRedisCache(redisConn)
	proxyCoinCashConversionRateRepository := persistence.ProvideCoinCashConversionRateRepository(mongoCoinCashConversionRateDataStore, prometheusMeter, coinConversionRateLocalCache, coinConversionRateRedisCache, coinConversionRateMinimalRedisCache)
	driverprovisionConfig := driverprovision.ProvideConfig()
	stubGRPCRecommendationService := driverprovision.ProvideStubGRPCRecommendationService(driverprovisionConfig)
	asynqClient := asynqclient.ProvideAsynqClient(redisConfig)
	supplyPositioningRecommenderService, cleanup22 := service.ProvideSupplyPositioningRecommenderService(atomicSupplyPositioningConfig, stubGRPCRecommendationService, proxyDriverRepository, proxyServiceAreaRepository, featureflagService, pushNotifier, prometheusMeter, asynqClient)
	egsorderConfig := egsorder.ProvideConfig()
	stubGRPCEGSOrderService := egsorder.ProvideStubGRPCEGSOrderService(egsorderConfig)
	atomicInstallmentDBConfig := service.ProvideAtomicInstallmentDBConfig(dbConfigUpdater)
	installmentCfg := service.ProvideInstallmentConfig(atomicInstallmentDBConfig)
	installmentService := service.ProvideInstallmentService(installmentCfg, proxyInstallmentRepository, driverTransactionServiceV2, proxyAuditLogRepository, txnHelper)
	summaryOfChangeDataStore := persistence.ProvideSummaryOfChangeDataStore(conn)
	proxySummaryOfChangeRepository := persistence.ProvideSummaryOfChangeRepository(summaryOfChangeDataStore, redisClient, caches, prometheusMeter)
	v10 := service.ProvideAtomicFormServiceConfig(dbConfigUpdater)
	formConfig := config.ProvideFormConfig()
	v11 := config.ProvideAtomicNegativeCreditConfig(dbConfigUpdater)
	linehttpClient, cleanup23, err := httpclient.ProvideLINEInternalHTTPClientStub()
	if err != nil {
		cleanup22()
		cleanup21()
		cleanup20()
		cleanup19()
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	lineInternalConfig := lineinternal.ProvideLINEInternalConfig()
	lineStatelessTokenCacheRepository := persistence.ProvideLINEStatelessTokenCacheRepository(redisClient, prometheusMeter)
	lineinternalLINEClient := lineinternal.ProvideLINEClient(lineClient, lineStatelessTokenCacheRepository)
	lineinternalClient := lineinternal.ProvideLINEInternalClient(linehttpClient, lineInternalConfig, featureflagService, lineinternalLINEClient)
	mockChatServiceClient := chat.ProvideStubChatGRPCService(controller)
	atomicAdminConfig := config.ProvideAdminConfig(dbConfigUpdater)
	fraudService := fraud.ProvideFraudService(mockChatServiceClient, atomicAdminConfig)
	v12 := account.ProvideAtomicAuthConfig(dbConfigUpdater)
	service2 := transaction.ProvideTransactionServiceV2(proxyPendingTransactionRepository)
	tiersAndBenefitsService := service.ProvideTiersAndBenefitsService(proxyDriverRepository)
	assignmentBenchmarkDataStore := persistence.ProvideAssignmentBenchmarkDataStore(conn)
	assignmentBenchmarkRepository := persistence.ProvideAssignmentBenchmarkRepository(assignmentBenchmarkDataStore, prometheusMeter)
	assignmentBenchmarkCfg := service.ProvideAssignmentBenchmarkCfg()
	assignmentBenchmarkService := service.ProvideAssignmentBenchmarkService(proxyDriverRepository, proxyAssignmentLogRepository, assignmentBenchmarkRepository, proxyDriverLocationRepository, atomicDriverLocationConfig, assignmentBenchmarkCfg)
	driverInsuranceCfg := service.ProvideDriverInsuranceCfg()
	driverInsuranceServiceImpl := service.ProvideDriverInsuranceService(driverInsuranceCfg, proxyDriverInsuranceRepository, vosServiceImpl)
	accountAPI := account.ProvideAccountAPI(vosServiceImpl, driverService, proxyDriverRepository, locationManagerImpl, lineClient, proxyOrderRepository, bcpStatusTransitionerService, driverTransactionService, stubPolygonApi, termAndConditionServiceImpl, transactionService, accountConfig, pdpaConfig, redisTokenStorage, proxyServiceAreaRepository, proxyTransactionFraudScoreRepository, stubKafkaConnector, driverPeriodCompletedTripsConfig, paymentConfig, repEventBus, accountServiceInterface, proxyBanHistoryRepository, pushNotifier, orderAPIConfig, proxyPdpaRepository, vosConfig, v6, driverRatingConfig, atomicDBConfig, driverProfileRequestConfig, banServiceImpl, proxyTransactionSchemeRepository, proxyDriverOrderInfoRepository, txnHelper, attendances, proxyShiftRepository, tripService, proxyTripRepository, cancellerImpl, proxyInternalCancelReasonRepository, proxyRequestUpdateProfileRepository, mockFormServiceClient, proxyDriverInsuranceRepository, driverInsuranceConfig, areaServiceImpl, incomeSummaryServiceImpl, mockEGSServiceClient, deliveryFeeService, proxyIncentiveRepository, proxyIncentiveProgressRepository, stubGRPCProductService, stubMapService, proxyDriverLastUpdateLocationTrackerRepository, driverUpdateLocationEventService, proxyRewardBalanceRepository, proxyDailyRewardRepository, proxyCoinCashConversionRateRepository, rewardService, rainSituationServiceImpl, proxyDriverLocationRepository, proxyOnTopFareRepository, supplyPositioningRecommenderService, stubGRPCEGSOrderService, installmentService, servicePreferenceService, serviceOptInReminderService, proxySummaryOfChangeRepository, featureflagService, v10, formConfig, driverServiceTypeCapacityService, v11, lineinternalClient, fraudService, v12, service2, tiersAndBenefitsService, unAcknowledgeReassignReposity, assignmentBenchmarkService, driverInsuranceServiceImpl)
	cancelReasonDataStore := persistence.ProvideCancelReasonDataStore(conn)
	proxyCancelReasonRepository := persistence.ProvideDataStoreCancelReasonRepository(cancelReasonDataStore, prometheusMeter)
	driverRegistrationDataStore := persistence.ProvideDriverRegistrationDataStore(conn)
	proxyDriverRegistrationRepository := persistence.ProvideDriverRegistrationRepository(driverRegistrationDataStore, prometheusMeter)
	adminConfig := admin.ProvideAdminConfig()
	requestUpdateProfileSectionDataStore := persistence.ProvideRequestUpdateProfileSectionDataStore(conn)
	proxyRequestUpdateProfileSectionRepository := persistence.ProvideRequestUpdateProfileSectionRepository(requestUpdateProfileSectionDataStore, prometheusMeter)
	banEffectiveTimeDataStore := persistence.ProvideBanEffectiveTimeDataStore(conn)
	proxyBanEffectiveTimeRepository := persistence.ProvideBanEffectiveTimeRepository(banEffectiveTimeDataStore, prometheusMeter)
	bulkProcessInfoDataStore := persistence.ProvideBulkProcessInfoDataStore(conn)
	proxyBulkProcessInfoRepository := persistence.ProvideDataStoreBulkProcessInfoRepository(bulkProcessInfoDataStore, prometheusMeter)
	atomicCancelReasonConfig := service.ProvideAtomicCancelReasonConfig(dbConfigUpdater)
	generalConfig := bulk.ProvideBulkGeneralConfig()
	driverDataStore := bulk.ProvideDriverCollectionDataStore(driversDataStore, proxyDriverRepository, generalConfig)
	incentiveCollectionDataStore := bulk.ProvideIncentiveCollectionDataStore(incentiveDataStore, proxyIncentiveRepository, generalConfig)
	bulkConfig := bulk.ProvideBulkConfig(generalConfig, driverDataStore, incentiveCollectionDataStore)
	driverAdminAPI := admin.ProvideDriverOperationAdminAPI(proxyAssignmentLogRepository, banServiceImpl, proxyBanHistoryRepository, driverService, proxyDriverRepository, proxyCancelReasonRepository, proxyDriverOrderInfoRepository, proxyOrderRepository, proxyDriverRegistrationRepository, adminConfig, slackStub, vosServiceImpl, shiftServices, stubKafkaConnector, proxyRequestUpdateProfileSectionRepository, proxyRequestUpdateProfileRepository, txnHelper, incomeSummaryServiceImpl, dedicatedZoneRepository, proxyBanEffectiveTimeRepository, proxyAuditLogRepository, proxyServiceAreaRepository, featureflagService, proxyBulkProcessInfoRepository, globalConfig, atomicCancelReasonConfig, atomicAdminConfig, bulkConfig)
	banMetadataAPI := admin.ProvideBanMetadataAPI(banServiceImpl)
	api := admin.ProvideAPI(driverAdminAPI, banMetadataAPI)
	topupCreditReportDataStore := persistence.ProvideTopupCreditReportDataStore(conn)
	proxyTopupCreditReportRepository := persistence.ProvideTopupCreditReportRepository(topupCreditReportDataStore, prometheusMeter)
	topupCreditReportAPI := admin.ProvideTopupCreditReportAPI(proxyTopupCreditReportRepository, vosServiceImpl, adminConfig)
	assignmentBenchmarkAPI := assignmentbenchmarks.ProvideAssignmentBenchmarkAPI(proxyAuditLogRepository, assignmentBenchmarkService)
	bulkUpdateServiceTypeSilentBanned := bulk2.ProvideBulkUpdateServiceTypeSilentBanned(adminConfig, driversDataStore)
	bulkToggleOnRiderServicePreference := bulk2.ProvideBulkToggleOnRiderServicePreference(adminConfig, driversDataStore)
	zoneServiceImpl := service.ProvideZoneServiceImpl(proxyZoneRepository)
	updateBulkIncentive := bulk_incentive.ProvideUpdateBulkIncentive(incentiveDataStore, adminConfig, stubPolygonApi, zoneServiceImpl)
	createBulkIncentive := bulk_incentive.ProvideCreateBulkIncentive(incentiveDataStore, adminConfig, stubPolygonApi, zoneServiceImpl)
	deleteBulkIncentive := bulk_incentive.ProvideDeleteBulkIncentive(incentiveDataStore, adminConfig)
	exportBulkIncentive := bulk_incentive.ProvideExporterBulkIncentive(incentiveDataStore, adminConfig)
	operations, err := bulk2.ProvideBulkOperations(bulkUpdateServiceTypeSilentBanned, bulkToggleOnRiderServicePreference, updateBulkIncentive, createBulkIncentive, deleteBulkIncentive, exportBulkIncentive)
	if err != nil {
		cleanup23()
		cleanup22()
		cleanup21()
		cleanup20()
		cleanup19()
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
		return nil, nil, err
	}
	processInfoRepository := bulk.ProvideMongoProcessInfoRepository(bulkProcessInfoDataStore, prometheusMeter)
	processInfoService := bulk.ProvideProcessInfoService(bulkConfig, processInfoRepository)
	operationService := bulk.ProvideOperationService(bulkConfig, proxyAuditLogRepository)
	ginHandlersFn := bulk.ProvideBulkGinHandlers(globalConfig, operations, processInfoService, operationService, slackStub, bulkConfig)
	bulkAPI := bulk2.ProvideBulkAPI(ginHandlersFn)
	bulkcoinconversioncsvConfig := bulkcoinconversioncsv.ProvideBulkCoinConversionConfig()
	rewardConfig := reward.ProvideConfig()
	bulkCoinConversionCSVAPI := bulkcoinconversioncsv.ProvideBulkCoinConversionCSVAPI(bulkcoinconversioncsvConfig, proxyCoinCashConversionRateRepository, proxyAuditLogRepository, proxyRegionRepository, rewardConfig)
	bulkincentivecsvConfig := bulkincentivecsv.ProvideBulkIncentiveCSVConfig()
	v13 := config.ProvideIncentiveSourceConfig(dbConfigUpdater)
	incentiveSourceServiceImpl := service.ProvideIncentiveSourceServiceImpl(v13)
	bulkIncentiveCSVAPI := bulkincentivecsv.ProvideBulkIncentiveCSVAPI(bulkincentivecsvConfig, proxyIncentiveRepository, proxyAuditLogRepository, incentiveSourceServiceImpl, zoneServiceImpl, stubPolygonApi, proxyRegionRepository)
	bulkontopcsvConfig := bulkontopcsv.ProvideBulkOnTopCSVConfig()
	bulkOntopCSVAPI := bulkontopcsv.ProvideBulkOntopCSVAPI(bulkontopcsvConfig, proxyOnTopFareRepository, proxyAuditLogRepository, incentiveSourceServiceImpl, zoneServiceImpl, stubPolygonApi)
	bulkProcessInfoAPI := bulkprocess.ProvideBulkProcessInfoAPI(proxyBulkProcessInfoRepository, processInfoService)
	bulkSchedulerWorker := bulkprocess.ProvideBulkSchedulerWorker(proxyBulkProcessInfoRepository, slackStub, globalConfig, bulkConfig)
	dataStoreJobRepository := cron.ProvideDateStoreJobRepository(conn)
	localTaskExecutor := cron.ProvideLocalTaskExecutor(dataStoreJobRepository)
	incentiveConfig := cron.ProvideIncentiveConfig()
	incentiveTask := cron.ProvideIncentiveTask(proxyOrderRepository, driverTransactionService, proxyIncentiveRepository, proxyDriverRepository, proxyServiceAreaRepository, incentiveConfig, proxyDriverOrderInfoRepository)
	expiredFreeCreditTask := cron.ProvideExpiredFreeCreditTask(proxyDriverTransactionRepository, proxyTransactionRepository)
	todayEarningTask := cron.ProvideTodayEarningTask(proxyOrderRepository, proxyDriverRepository)
	dailyUnbanTask := cron.ProvideDailyUnbanTask(proxyDriverRepository, banServiceImpl, proxyBanHistoryRepository, txnHelper)
	heatMapMatchingRateDataStore := persistence.ProvideMatchingRateHeatMapDataStore(conn)
	proxyMatchingRateHeatMapRepository := persistence.ProvideMatchingRateHeatMapRepository(heatMapMatchingRateDataStore, prometheusMeter)
	heatmapConfig := heatmap.ProvideHeatMapConfig()
	matchingRateHeatMapTask := cron.ProvideMatchingRateHeatMapTask(proxyMatchingRateHeatMapRepository, proxyOrderRepository, heatmapConfig)
	partnersConfig := partners.ProvideUobApiConfig()
	uobTopupTransactionTask := cron.ProvideUobTopupTransactionTask(client, partnersConfig)
	quoteDataStore := persistence.ProvideQuoteDataStore(conn)
	proxyQuoteRepository := persistence.ProvideMongoQuoteRepository(quoteDataStore, prometheusMeter)
	manMapConfig := manmap.ProvideManMapConfig()
	experimentalMapService := manmap.ProvideExperimentalMapServiceClient(manMapConfig, client)
	executorLocalTaskExecutor, cleanup24 := executor.ProvideInfraLocalTaskExecutor()
	groupTransactionDataStore := persistence.ProvideGroupTransactionDataStore(conn)
	proxyGroupTransactionRepository := persistence.ProvideMongoGroupTransactionRepository(groupTransactionDataStore, prometheusMeter)
	configValidator := distribution.ProvideConfigValidator(proxyServiceAreaRepository, proxyOrderRepository, throttledDispatchDetailRepository, autoAssignConfig, v9, predictionService, distributionExperimentPlatformClient)
	translationConfig := translation.ProvideConfig()
	translationGRPCClient, cleanup25 := translation.ProvideTranslationGRPCClient(translationConfig, connectionFactory)
	translationServiceClient := translation.ProvideTranslationServiceClient(translationGRPCClient, prometheusMeter)
	translationServiceImpl := service.ProvideTranslationServiceImpl(translationServiceClient)
	polygonConfig := polygon.ProvideConfig()
	mockUserPolygonServiceClient := polygon.ProvideStubGRPCUserPolygonService(polygonConfig, legacyController)
	formService := form.ProvideService(mockFormServiceClient)
	pendingtransactionService := pendingtransaction.ProvidePendingTransactionService(proxyPendingTransactionRepository)
	orderService := order2.ProvideOrderService(proxyOrderRepository, pendingtransactionService, featureflagService)
	matchrateConfig := matchrate.ProvideConfig()
	matchrateService := matchrate.ProvideFeaturePlatformService(matchrateConfig, stubGRPCFeaturePlatformClient)
	providerDeps := order.ProvideProviderDeps(proxyQuoteRepository, proxyOrderRepository, proxyDriverLocationRepository, driverService, proxyDriverRepository, stubMapService, experimentalMapService, proxyAssignmentLogRepository, pushNotifier, deliveryDelivery, banServiceImpl, driverTransactionService, orderAPIConfig, fraudAdvisorServiceImpl, proxyBanHistoryRepository, proxyDriverTransactionRepository, repEventBus, stubKafkaConnector, driverService, autoAssignOrderDistributor, deliveryFeeService, areaServiceImpl, executorLocalTaskExecutor, statisticServiceImpl, proxyOnTopFareRepository, proxyTransactionRepository, proxyServiceAreaRepository, throttledOrderRepository, prometheusMeter, v6, predictionService, shiftServices, serviceAreaService, cancellerImpl, acceptor, tripService, proxyTripRepository, cookingTimeDelayRepository, onTopFareService, proxyGroupTransactionRepository, configValidator, rewardService, rainSituationServiceImpl, featureflagService, priceInterventionClientStub, orderHeartbeatServiceImpl, redisLocker, translationServiceImpl, mockUserPolygonServiceClient, missionLogEventService, formService, orderService, matchrateService, stubUWTErrorService, throttledDispatchDetailRepository, distributionService, unAcknowledgeReassignReposity, distributionExperimentPlatformClient)
	foodProviderImpl := order.ProvideFoodProvider(providerDeps, txnHelper, featureflagService, orderDistributionEventManager, serviceOptInReminderService)
	cronForceCompleteOrder := cron.ProvideFoodForceCompleteOrder(foodProviderImpl)
	v14 := cron.ProvideForceCompleteOrderConfig(dbConfigUpdater)
	forceCompleteOrderTask := cron.ProvideForceCompleteOrderTask(proxyOrderRepository, cronForceCompleteOrder, v14)
	atomicShiftInstructionNotificationConfig := cron.ProvideAtomicShiftInstructionNotificationConfig(dbConfigUpdater)
	shiftInstructionNotificationTask := cron.ProvideShiftInstructionNotificationTask(proxyShiftRepository, pushNotifier, atomicShiftInstructionNotificationConfig, proxyDriverRepository)
	withholdingTaxCertificateDataStore := persistence.ProvideWithholdingTaxCertificateDataStore(conn)
	proxyWithholdingTaxCertificateRepository := persistence.ProvideWithholdingTaxCertificateRepository(withholdingTaxCertificateDataStore, prometheusMeter)
	pdfBuilderCfg := pdf.ProvidePDFBuilderCfg()
	pdfGenerator := pdf.ProvidePDFGenerator()
	pdfBuilderImpl := pdf.ProvidePDFBuilder(pdfBuilderCfg, pdfGenerator)
	tawi50GeneratorConfig := cron.ProvideTawi50GeneratorConfig()
	tawi50GeneratorTask := cron.ProvideTawi50GeneratorTask(proxyWithholdingTaxCertificateRepository, proxyDriverRepository, vosServiceImpl, pdfBuilderImpl, tawi50GeneratorConfig)
	emailConfig := email.ProvideEmailConfig()
	emailService := email.ProvideEmailService(emailConfig)
	uobclientConfig := uobclient.ProvideUobConfig()
	uob, cleanup26 := uobclient.ProvideUobClient(uobclientConfig)
	withdrawalTransactionResultsDataStoreDataStore := persistence.ProvideWithdrawalTransactionResultsDataStore(conn)
	proxyWithdrawalTransactionResultsRepository := persistence.ProvideWithdrawalTransactionResultsRepository(withdrawalTransactionResultsDataStoreDataStore, prometheusMeter)
	paymentServiceImpl := payment.ProvidePaymentService(proxyTransactionRepository, transactionService, emailService, paymentConfig, uob, vosServiceImpl, proxyWithdrawalTransactionResultsRepository)
	payoutWithdrawalTask := cron.ProvidePayoutWithdrawalTask(proxyTransactionRepository, paymentServiceImpl, emailService, paymentConfig, featureflagService)
	payoutWithdrawalDailyEmailTask := cron.ProvidePayoutWithdrawalDailyEmailTask(paymentServiceImpl, paymentConfig)
	attendanceLogHistoryDataStore := persistence.ProvideAttendanceLogHistoryDataStore(conn)
	proxyAttendanceLogHistoryRepository := persistence.ProvideDataStoreAttendanceLogHistoryRepository(attendanceLogHistoryDataStore, prometheusMeter)
	attrLogArchiveConfig := cron.ProvideATTRLogArchiveConfig()
	attrLogArchiveTask := cron.ProvideATTRLogArchiveTask(proxyDriverRepository, proxyDriverOrderInfoRepository, proxyShiftRepository, txnHelper, proxyAttendanceLogHistoryRepository, attrLogArchiveConfig)
	expiredAttendanceStatConfig := cron.ProvideExpiredAttendanceStatConfig()
	expiredAttendanceStatTask := cron.ProvideExpiredAttendanceStatTask(expiredAttendanceStatConfig, proxyDriverOrderInfoRepository)
	cronInstallmentConfig := cron.ProvideCronInstallmentConfig()
	installmentDailyDeductionTask := cron.ProvideInstallmentDailyDeductionTask(proxyInstallmentRepository, proxyDriverTransactionRepository, proxyTransactionRepository, proxyDriverRepository, proxyProductGroupRepository, txnHelper, driverTransactionService, cronInstallmentConfig, stubGRPCProductService)
	groupTransactionTask := cron.ProvideGroupTransactionTask(proxyGroupTransactionRepository, proxyDriverTransactionRepository, proxyTransactionRepository, txnHelper, paymentConfig, emailService)
	driverDocumentDatastore := persistence.ProvideDriverDocumentDatastore(conn)
	proxyDriverDocumentRepository := persistence.ProvideDriverDocumentRepository(driverDocumentDatastore, prometheusMeter)
	bnplsoaFileTaskConfig := cron.ProvideBNPLSOAFileTaskConfig()
	bnplsoaFileTask := cron.ProvideBNPLSOAFileTask(proxyInstallmentRepository, pdfBuilderImpl, proxyDriverRepository, proxyDriverDocumentRepository, vosServiceImpl, bnplsoaFileTaskConfig, pushNotifier)
	dailyIncomeConfig := cron.ProvideDailyIncomeConfig()
	dailyIncomeTask := cron.ProvideDailyIncomeTask(dailyIncomeConfig, proxyDriverRepository, proxyTransactionRepository, incomeAggregateServiceImpl)
	cronEffectBanTimeConfig := cron.ProvideCronEffectBanTimeConfig()
	effectBanTimeTask := cron.ProvideEffectBanTimeTask(proxyDriverRepository, proxyBanEffectiveTimeRepository, txnHelper, banServiceImpl, shiftServices, cronEffectBanTimeConfig)
	atomicEtaxInvoiceDBConfig := service.ProvideAtomicEtaxInvoiceDBConfig(dbConfigUpdater)
	etaxInvoiceCfg := service.ProvideEtaxInvoiceCfg(atomicEtaxInvoiceDBConfig)
	etaxInvoiceDataStore := persistence.ProvideEtaxInvoiceDataStore(conn)
	proxyEtaxInvoiceRepository := persistence.ProvideEtaxInvoiceRepository(etaxInvoiceDataStore, prometheusMeter)
	counterDataStore := persistence.ProvideCounterDataStore(conn)
	proxyCounterRepository := persistence.ProvideCounterRepository(counterDataStore, prometheusMeter)
	inet_clientConfig := inet_client.ProvideINetClientConfig()
	iNetClient := inet_client.ProvideINetClient(inet_clientConfig)
	etaxInvoiceServiceImpl := service.ProvideEtaxInvoiceService(etaxInvoiceCfg, proxyEtaxInvoiceRepository, proxyCounterRepository, proxyInstallmentRepository, proxyDriverDocumentRepository, iNetClient, vosServiceImpl, pdfBuilderImpl, txnHelper, slackStub, globalConfig)
	etaxInvoiceGeneratorTask := cron.ProvideEtaxInvoiceGeneratorTask(etaxInvoiceServiceImpl)
	updateAbsoluteErrorStatsTask := cron.ProvideUpdateAbsoluteErrorStatsTask(proxyServiceAreaRepository, stubUWTErrorService)
	citiDailyReconcileTaskConfig := cron.ProvideCitiDailyReconcileTaskConfig()
	citiDailyReconcileTask := cron.ProvideCitiDailyReconcileTask(vosServiceImpl, proxyTransactionRepository, proxyTopupCreditReportRepository, slackStub, citiDailyReconcileTaskConfig, globalConfig, emailService)
	citiItemizeReportTaskConfig := cron.ProvideCitiItemizeReportTaskConfig()
	citiItemizeReportTask := cron.ProvideCitiItemizeReportTask(vosServiceImpl, proxyTopupCreditReportRepository, citiItemizeReportTaskConfig)
	convertCoinToCashConfig := cron.ProvideConvertCoinToCashConfig()
	mockCoinPlanGRPCClient := coinplan.ProvideStubCoinPlanGRPCClient(legacyController)
	convertCoinToCashTask := cron.ProvideConvertCoinToCashTask(convertCoinToCashConfig, proxyDriverRepository, proxyDailyRewardRepository, proxyRewardBalanceRepository, proxyCoinCashConversionRateRepository, driverTransactionServiceV2, rewardService, txnHelper, proxyDriverOrderInfoRepository, proxyServiceAreaRepository, featureflagService, mockCoinPlanGRPCClient)
	citiInquiryTransactionConfig := cron.ProvideCitiInquiryCheckConfig()
	partnerAuthConfig := partnerAuth.ProvideConfig()
	citiAuthenticationService := partnerAuth.ProvideCitiAuthenticationService(partnerAuthConfig)
	citiInquiryTransactionTask := cron.ProvideCitiInquiryTransactionTask(citiInquiryTransactionConfig, driverTransactionService, transactionService, proxyDriverRepository, citiAuthenticationService, globalConfig, slackStub)
	cronMigrateDriverMIDConfig := cron.ProvideCronMigrateDriverMIDConfigConfig()
	migrateDriverMIDTask := cron.ProvideMigrateDriverMIDTask(proxyDriverRepository, proxyDriverRegistrationRepository, lineinternalClient, cronMigrateDriverMIDConfig)
	listTaskProvider := &cron.ListTaskProvider{
		IncentiveTask:                    incentiveTask,
		ExpiredFreeCreditTask:            expiredFreeCreditTask,
		TodayEarningTask:                 todayEarningTask,
		DailyUnbanTask:                   dailyUnbanTask,
		MatchingRateHeatMapTask:          matchingRateHeatMapTask,
		UobTopupTransactionTask:          uobTopupTransactionTask,
		ForceCompleteOrderTask:           forceCompleteOrderTask,
		ShiftInstructionNotificationTask: shiftInstructionNotificationTask,
		Tawi50GeneratorTask:              tawi50GeneratorTask,
		PayoutWithdrawalTask:             payoutWithdrawalTask,
		PayoutWithdrawalDailyEmailTask:   payoutWithdrawalDailyEmailTask,
		ATTRLogArchiveTask:               attrLogArchiveTask,
		ExpiredAttendanceStat:            expiredAttendanceStatTask,
		InstallmentDailyDeductionTask:    installmentDailyDeductionTask,
		GroupTransactionTask:             groupTransactionTask,
		TaskNameBNPLSOAFile:              bnplsoaFileTask,
		DailyIncomeTask:                  dailyIncomeTask,
		EffectBanTimeTask:                effectBanTimeTask,
		EtaxInvoiceGeneratorTask:         etaxInvoiceGeneratorTask,
		UpdateAbsoluteErrorStatsTask:     updateAbsoluteErrorStatsTask,
		CitiDailyReconcileTask:           citiDailyReconcileTask,
		CitiItemizeReportTask:            citiItemizeReportTask,
		ConvertCoinToCashTask:            convertCoinToCashTask,
		CitiInquiryTransactionTask:       citiInquiryTransactionTask,
		MigrateDriverMIDTask:             migrateDriverMIDTask,
	}
	cronAPI := cron2.ProvideCronAPI(localTaskExecutor, listTaskProvider)
	schedulerScheduler := scheduler.ProvideScheduler(prometheusMeter)
	taskReassignDelayedTripConfig := scheduler.ProvideTaskReassignDelayedTripConfig()
	taskReassignDelayedTrip := scheduler.ProvideTaskReassignDelayedTrip(schedulerScheduler, taskReassignDelayedTripConfig, providerDeps, featureflagService, dispatcherDispatcher)
	taskServiceOptInReminderConfig := scheduler.ProvideTaskServiceOptInReminderConfig()
	taskServiceOptInReminder := scheduler.ProvideTaskServiceOptInReminder(schedulerScheduler, taskServiceOptInReminderConfig, serviceOptInReminderService)
	taskOrderExpireConfig := scheduler.ProvideTaskOrderExpireConfig()
	taskExpireOrder := scheduler.ProvideTaskExpireOrder(schedulerScheduler, proxyOrderRepository, deliveryDelivery, taskOrderExpireConfig, asynqClient)
	taskQRIncidentOrderCompletionConfig := scheduler.ProvideTaskQRIncidentOrderCompletionConfigConfig(atomicOrderDBConfig)
	qrIncidentOrderServiceImpl := service.ProvideQRIncidentOrderService(proxyOrderRepository, pushNotifier)
	taskQRIncidentOrderCompletion := scheduler.ProvideTaskQRIncidentOrderCompletion(schedulerScheduler, asynqClient, featureflagService, taskQRIncidentOrderCompletionConfig, qrIncidentOrderServiceImpl)
	taskFetchRainSituationConfig := scheduler.ProvideTaskFetchRainSituationConfig()
	taskFetchRainSituation := scheduler.ProvideTaskFetchRainSituation(schedulerScheduler, rainSituationServiceImpl, taskFetchRainSituationConfig, asynqClient)
	taskUpdateDriverServicesConfig := scheduler.ProvideTaskUpdateDriverServicesConfig()
	taskUpdateDriverServices := scheduler.ProvideTaskUpdateDriverServices(schedulerScheduler, taskUpdateDriverServicesConfig, proxyBulkProcessInfoRepository, proxyDriverRepository, asynqClient, bulkSchedulerWorker)
	schedulerAPI := cron2.ProvideSchedulerAPI(taskReassignDelayedTrip, taskServiceOptInReminder, taskExpireOrder, taskQRIncidentOrderCompletion, taskFetchRainSituation, taskUpdateDriverServices)
	adminAPI := dbconfig.ProvideDbConfigAdminAPI(proxyDBConfigRepository, proxyAuditLogRepository, dbConfigUpdater, v4, txnHelper)
	dedicatedZoneAPI := dedicated_zone.ProvideDedicatedZoneAPI(dedicatedZoneRepository)
	deliveryFeeAPI := deliveryfee.ProvideDeliveryFeeAPI(proxyDeliveryFeeSettingRepository, proxyRegionRepository, proxySettingDeliveryFeePriceSchemesRepository)
	driverDocumentConfig := driver.ProvideDriverDocumentConfig()
	document := driver.ProvideDocument(proxyWithholdingTaxCertificateRepository, proxyDriverDocumentRepository, vosServiceImpl, driverDocumentConfig)
	driverOrderConfig := driver.ProvideOrderConfig()
	financialRiskConfig := driver.ProvideFinancialRiskConfig()
	configRiderLevel := riderlevel.ProvideRiderLevelConnectorConfig()
	stubRiderLevelConnector := riderlevel.ProvideStubRiderLevelConnector(configRiderLevel)
	driverDriverAdminAPI := driver.ProvideDriverAdminAPI(proxyDriverRepository, driverService, driverService, proxyServiceAreaRepository, proxyBanHistoryRepository, proxyAuditLogRepository, repEventBus, redisClient, proxyDriverRegistrationRepository, pushNotifier, attendances, serviceAreaService, proxyShiftRepository, proxyOrderRepository, dedicatedZoneRepository, driverOrderConfig, driverUpdateLocationEventService, financialRiskConfig, stubRiderLevelConnector, featureflagService, onTopFareService)
	driverNotifyAPI := driver.ProvideDriverNotifyAPI(proxyDriverRepository, pushNotifier)
	driverSalesforceAPI := driver.ProvideDriverSalesforceAPI(driverDriverAdminAPI)
	driverStatusAPI := driver.ProvideDriverStatusAPI(proxyDriverRepository, pushNotifier)
	pdpaApi := driver.ProvidePdpaApi(proxyPdpaRepository)
	stubMessageService := messages.ProvideStubMessageService()
	provinceDataStore := persistence.ProvideProvinceDataStore(conn)
	proxyProvinceRepository := persistence.ProvideProvinceRepository(provinceDataStore, prometheusMeter)
	registerHandlerConfig := driver.ProvideRegisterHandlerConfig()
	registrationAdminAPI := driver.ProvideRegistrationAdminAPI(proxyDriverRegistrationRepository, stubMessageService, driverService, driverService, proxyServiceAreaRepository, proxyDriverRepository, proxyDriverTransactionRepository, proxyTransactionRepository, globalConfig, proxyProvinceRepository, repEventBus, proxyAuditLogRepository, registerHandlerConfig, featureflagService)
	questionsConfig := persistence.ProvideQuestionConfigs()
	template := tmpl.ProvideTemplateService()
	proxyQuestionRepository := persistence.ProvideFileQuestionRepository(questionsConfig, prometheusMeter, featureflagService, template)
	whitelistPhoneDataStore := persistence.ProvideWhitelistPhoneDataStore(conn)
	proxyWhitelistPhoneRepository := persistence.ProvideWhitelistPhoneRepository(whitelistPhoneDataStore, prometheusMeter)
	cacheCache := cache2.ProvideCache(redisClient)
	otpConfig := model.ProvideOTPConfig()
	timeNowFunc := model.ProvideTimeNow()
	newOTPSession := model.ProvideNewOTPSession(otpConfig, timeNowFunc)
	proxyOTPSessionRepo := persistence.ProvideOTPSessionRepo(cacheCache, newOTPSession, prometheusMeter)
	registrationAPI := driver.ProvideRegistrationAPI(registerHandlerConfig, globalConfig, vosServiceImpl, driverService, proxyDriverRegistrationRepository, stubMessageService, proxyQuestionRepository, proxyWhitelistPhoneRepository, proxyServiceAreaRepository, proxyDriverRepository, proxyTransactionRepository, proxyDriverTransactionRepository, executorLocalTaskExecutor, proxyProvinceRepository, proxyOTPSessionRepo, repEventBus, vosConfig, driverProfileRequestConfig, lineClient, lineinternalClient, featureflagService)
	driverinsuranceConfig := driverinsurance.ProvideInsuranceConfig()
	consolidatedInsuranceDataStore := persistence.ProvideConsolidatedInsuranceDataStore(conn)
	proxyConsolidatedInsuranceRepository := persistence.ProvideConsolidatedInsuranceRepository(consolidatedInsuranceDataStore, prometheusMeter)
	insuranceAPI := driverinsurance.ProvideInsuranceAPI(driverinsuranceConfig, txnHelper, driverTransactionServiceV2, vosServiceImpl, proxyDriverRepository, proxyDriverInsuranceRepository, proxyConsolidatedInsuranceRepository, driverInsuranceServiceImpl)
	v15 := driverorderinfo.ProvideAutoBanCompleteOrderTooOftenCfg(dbConfigUpdater)
	v16 := driverorderinfo.ProvideAutoBanCompleteOrderTooFarCfg(dbConfigUpdater)
	driverOrderInfoAPI := driverorderinfo.ProvideDriverOrderInfoAPI(proxyOrderRepository, proxyDriverOrderInfoRepository, proxyDriverRepository, banServiceImpl, stubMapService, v15, v16, shiftServices)
	egsConfig := egs2.ProvideHookConfig()
	egsCfg := service.ProvideEGSCfg()
	serviceEGS := service.ProvideEGSService(proxyInstallmentRepository, proxyIncomeDailySummaryRepository, proxyDriverRepository, mockEGSServiceClient, featureflagService, egsCfg)
	egsAPI := egs2.ProvideEgsAPI(egsConfig, globalConfig, serviceEGS, proxyInstallmentRepository, installmentService, pushNotifier, slackStub)
	etax_invoiceConfig := etax_invoice.ProvideConfig()
	etaxInvoiceAPI := etax_invoice.ProvideEtaxInvoiceAPI(etax_invoiceConfig, proxyEtaxInvoiceRepository, vosServiceImpl)
	eventAPI := event.ProvideEventAPI()
	featureFlagConfigAPI := featureflagconfig.ProvideFeatureFlagConfigAPI(featureflagService, proxyAuditLogRepository)
	fraudAPI := fraud2.ProvideFraudAPI(proxyTransactionFraudScoreRepository)
	heatMapDataStore := persistence.ProvideHeatMapDataStore(conn)
	proxyHeatMapRepository := persistence.ProvideHeatMapRepository(heatMapDataStore, prometheusMeter)
	heatMapDemand := heatmapdemand.ProvideHeatMapDemand(client)
	heatmapAPI := heatmap.ProvideHeatMapAPI(proxyHeatMapRepository, heatmapConfig, heatMapDemand, proxyDriverRepository, proxyOnTopFareRepository, proxyOrderRepository, proxyMatchingRateHeatMapRepository, proxyServiceAreaRepository, proxyZoneRepository, stubPolygonApi)
	hookConfig := hook.ProvideHookConfig()
	egsbranchConfig := egsbranch.ProvideConfig()
	stubGRPCEGSBranchService := egsbranch.ProvideStubGRPCEGSBranchService(egsbranchConfig)
	egsOrderServiceConfig := service.ProvideEGSOrderServiceConfig()
	configEGSConfig := config.ProvideEGSConfig()
	mockMarketplaceServiceClient := marketplace.ProvideStubGRPCMarketplaceService(legacyController)
	v17 := config.ProvideAtomicEGSDBConfig(dbConfigUpdater)
	egsOrderService := service.ProvideEGSOrderService(egsOrderServiceConfig, kafkaProducerStubClient, serviceEGS, proxyBanHistoryRepository, proxyDriverRepository, driverTransactionServiceV2, installmentService, configEGSConfig, mockMarketplaceServiceClient, pushNotifier, v17, proxyTransactionRepository)
	hookAtomicDBConfig := hook.ProvideHookDBConfig(dbConfigUpdater)
	hookAPI := hook.ProvideHookAPI(hookConfig, proxyDriverRepository, stubGRPCProductService, proxyBanHistoryRepository, serviceEGS, proxyDriverOrderInfoRepository, pushNotifier, mockEGSServiceClient, stubGRPCEGSBranchService, stubGRPCEGSOrderService, proxyInstallmentRepository, proxyDriverInsuranceRepository, driverinsuranceConfig, driverInsuranceServiceImpl, egsOrderService, installmentService, orderService, tripService, txnHelper, mockMarketplaceServiceClient, featureflagService, hookAtomicDBConfig)
	incentiveAPI := incentive.ProvideIncentiveAPI(proxyIncentiveRepository, proxyAuditLogRepository, stubPolygonApi, proxyRegionRepository)
	incentiveSourceAPI := incentivesource.ProvideIncentiveSourceAPI(incentiveSourceServiceImpl)
	installmentConfig := installment.ProvideInstallmentConfig()
	productDataStore := persistence.ProvideProductDataStore(conn)
	proxyProductRepository := persistence.ProvideProductRepository(productDataStore, prometheusMeter, stubGRPCProductService)
	installmentAPI := installment.ProvideInstallmentAPI(installmentConfig, proxyInstallmentRepository, proxyDriverRepository, proxyProductRepository, proxyProductGroupRepository, proxyAuditLogRepository, driverTransactionServiceV2, proxyDriverTransactionRepository, txnHelper, stubGRPCProductService, proxyEtaxInvoiceRepository, etaxInvoiceServiceImpl, vosServiceImpl, installmentService, proxyDriverDocumentRepository)
	internalAPI := internalapi.ProvideInternalAPI(proxyOrderRepository, proxyAssignmentLogRepository, proxyDriverRepository, driverService, proxyDriverRegistrationRepository, driversDataStore, proxyDriverTransactionRepository, driverTransactionService, driverTransactionServiceV2, proxyIncentiveRepository, proxyTransactionRepository, proxyBanHistoryRepository, proxyServiceAreaRepository, dataStoreJobRepository, proxyClientAreaRepository, proxyRegionRepository, proxyDriverLocationRepository, cacheCache, proxyOTPSessionRepo, transactionService, locationManagerImpl, repEventBus, stubKafkaConnector, redisClient, proxyDriverStatisticRepository, proxyTransactionSchemeRepository, tripService, proxyTripRepository, accountServiceInterface, driverService, vosServiceImpl, proxyDriverActiveTimeRepository, proxyIncomeDailySummaryRepository, incomeSummaryServiceImpl, serviceEGS, redisLocker, kafkaProducerStubClient, proxyGroupTransactionRepository, txnHelper, throttledOrderRepository, deferredOrderRepository, autoAssignOrderDistributor, dispatcherDispatcher, dbConfigUpdater, priceInterventionClientStub, stubGRPCRecommendationService, featureflagService, proxyPendingTransactionRepository, deliveryDelivery, distributionService, proxyZoneRepository, throttledOrderDBConfig)
	middlewareConfig := middlewares.ProvideMiddlewareConfig()
	builder := middlewares.ProvideMiddlewareBuilder(redisClient, redisLocker, middlewareConfig)
	onTopFareAPI := ontopfare.ProvideOnTopFareAPI(proxyOnTopFareRepository, proxyAuditLogRepository, stubPolygonApi)
	orderAdminAPI := order.ProvideAdminAPI(proxyOrderRepository, proxyTripRepository, tripService, proxyDriverRepository, driverTransactionService, orderAPIConfig, deliveryFeeService, proxyTransactionRepository, proxyInternalCancelReasonRepository, proxyAuditLogRepository, cancellerImpl, atomicCancelReasonConfig, onTopFareService, bcpOrderRepository)
	deliveryPortalAPI := order.ProvideDeliveryAPIPortal(foodProviderImpl)
	memLocker := order.ProvideMemLocker(redisClient)
	assignmentRejectionDataStore := persistence.ProvideAssignmentRejectionDataStore(conn)
	proxyAssignmentRejectionRepository := persistence.ProvideDataStoreAssignmentRejectionRepository(assignmentRejectionDataStore, prometheusMeter)
	ratingRestaurantDataStore := persistence.ProvideRatingRestaurantDataStore(conn)
	proxyRatingRestaurantRepository := persistence.ProvideRatingRestaurantRepository(ratingRestaurantDataStore, prometheusMeter)
	awsConfig := aws.ProvideConfig()
	rekognitionImpl := aws.ProvideRekognitionClient(awsConfig)
	awsServiceImpl := service.ProvideAwsServiceImpl(rekognitionImpl)
	orderAPI := order.ProvideOrderAPI(proxyOrderRepository, proxyAssignmentLogRepository, proxyDriverRepository, assignmentRepository, proxyAssignmentRejectionRepository, orderAPIConfig, proxyDriverTransactionRepository, proxyRatingRestaurantRepository, proxyCancelReasonRepository, proxyServiceAreaRepository, proxyCoinCashConversionRateRepository, driverService, banServiceImpl, pushNotifier, driverService, repEventBus, stubKafkaConnector, vosServiceImpl, deliveryDelivery, vosConfig, v6, shiftServices, awsServiceImpl, autoAssignOrderDistributor, cancellerImpl, tripService, fraudService, featureflagService)
	orderPortalAPI := order.ProvideOrderPortalAPI(orderAPIConfig, foodProviderImpl, proxyOrderRepository, proxyDriverRepository, redisLocker, tripService, driverService, unAcknowledgeReassignReposity)
	orderAssignerHandler := orderassigner.ProvideOrderAssignerHandler(atomicAutoAcceptConfig, atomicAutoAssignDbConfig, v9, proxyAssignmentLogRepository, illegalDriverRepository, redisLocker, proxyDriverRepository, proxyOrderRepository, proxyServiceAreaRepository, rainSituationServiceImpl, onTopFareService, proxyDriverLocationRepository, stubMapService, orderAPIConfig, assignmentRepository, statisticServiceImpl, stubKafkaConnector, driverService, pushNotifier, txnHelper, prometheusMeter, locationManagerImpl, featureflagService, distributionService, dispatcherDispatcher, distributionLogManager, servicePreferenceService, predictionService, acceptor, workerContext, metricsRegistryImpl)
	smsConfig := sms.ProvideSMSConfig()
	smsSMS := sms.ProvideSMS(smsConfig, client)
	otpapi := otp.ProvideOTPAPI(smsSMS, proxyOTPSessionRepo, lineClient)
	atomicCitiConfig := partners.ProvideCitiApiConfig(dbConfigUpdater)
	citiAPI := partners.ProvideCitiAPI(pushNotifier, proxyDriverRepository, driverTransactionService, transactionService, atomicCitiConfig)
	uobAPI := partners.ProvideUobAPI(partnersConfig, pushNotifier, proxyDriverRepository, driverTransactionService, transactionService)
	approvalDataStore := persistence.ProvideApprovalDataStore(conn)
	proxyApprovalRepository := persistence.ProvideDataStoreApprovalRepository(approvalDataStore, prometheusMeter)
	approvalService := payment.ProvideApprovalService(proxyApprovalRepository, proxyDriverTransactionRepository, proxyTransactionRepository, proxyDriverRepository, driverTransactionServiceV2, txnHelper)
	approvalCreator := payment.ProvideApprovalCreator(proxyApprovalRepository, proxyDriverRepository, proxyDriverTransactionRepository, proxyTransactionRepository, paymentConfig, proxyOrderRepository)
	approvalAPI := payment.ProvideApprovalAPI(proxyApprovalRepository, approvalService, approvalCreator, proxyDriverTransactionRepository, proxyTransactionRepository, paymentConfig)
	driverTransactionAPI := payment.ProvideDriverTransactionAPI(proxyDriverTransactionRepository, proxyBanHistoryRepository, driverTransactionService, paymentConfig, banServiceImpl, vosServiceImpl)
	groupTransactionAPI := payment.ProvideGroupTransactionAPI(proxyGroupTransactionRepository, proxyDriverTransactionRepository, proxyTransactionRepository, proxyOrderRepository, proxyTransactionSchemeRepository, paymentConfig)
	uobPayoutMetric := payment.ProvideUOBPayoutMetric(transactionDataStore, paymentConfig, featureflagService)
	transactionAPI := payment.ProvideTransactionAPI(proxyTransactionRepository, proxyDriverTransactionRepository, transactionService, emailService, proxyDriverRepository, driverTransactionService, pushNotifier, paymentConfig, vosServiceImpl, uob, paymentServiceImpl)
	transactionSchemeAPI := payment.ProvideTransactionSchemeAPI(proxyTransactionSchemeRepository, proxyAuditLogRepository)
	phonesConfig := phones.ProvidePhonesConfig()
	phonesAPI := phones.ProvidePhoneAPI(proxyWhitelistPhoneRepository, phonesConfig)
	productConfig := product.ProvideProductConfig()
	productAPI := product.ProvideProductAPI(productConfig, proxyProductRepository, proxyProductGroupRepository, proxyInstallmentRepository, txnHelper)
	provinceAdminAPI := province.ProvideProvinceAdminAPI(proxyProvinceRepository)
	provinceAPI := province.ProvideProvinceAPI(proxyProvinceRepository)
	rain_situationConfig := rain_situation.ProvideConfig()
	rainSituationAdminAPI := rain_situation.ProvideRainSituationAdminAPI(rain_situationConfig, proxyRainSituationRepository, rainSituationServiceImpl, txnHelper, slackStub, atomicRainSituationConfig, globalConfig)
	rainSituationAPI := rain_situation.ProvideRainSituationAPI(rain_situationConfig, rainSituationServiceImpl, proxyDriverRepository, stubPolygonApi, proxyRainSituationRepository, proxyAuditLogRepository, slackStub, atomicRainSituationConfig, globalConfig, stubMapService)
	ratingOptionDataStore := persistence.ProvideRatingOptionDataStore(conn)
	proxyRatingOptionRepository := persistence.ProvideRatingOptionRepository(ratingOptionDataStore, prometheusMeter)
	ratingAdminAPI := rating.ProvideRatingAdminAPI(proxyRatingOptionRepository)
	ratingAPI := rating.ProvideRatingOptionAPI(proxyRatingOptionRepository, featureflagService)
	regionAdminAPI := region.ProvideRegionAdminAPI(proxyRegionRepository)
	v18 := region.ProvideAtomicRegionConfig(dbConfigUpdater)
	regionAPI := region.ProvideRegionAPI(proxyProvinceRepository, v18)
	rewardAdminAPI := reward.ProvideRewardAdminAPI(proxyRewardBalanceRepository, proxyDailyRewardRepository, rewardService, proxyCoinCashConversionRateRepository, proxyRewardTransactionRepository, rewardConfig)
	shiftCancelDataStore := persistence.ProvideShiftCancelDataStore(conn)
	proxyShiftCancelRepository := persistence.ProvideShiftCancelRepository(shiftCancelDataStore, prometheusMeter)
	atomicShiftConfig := shift.ProvideShiftCancelReasonConfig(dbConfigUpdater)
	shiftConfig := shift.ProvideShiftConfig(atomicShiftConfig)
	shiftAPI := shift.ProvideShiftAPI(proxyShiftRepository, proxyDriverRepository, proxyShiftCancelRepository, txnHelper, shiftConfig, proxyServiceAreaRepository, attendances)
	clientAreaAPI := srvarea.ProvideClientAreaAPI(proxyClientAreaRepository, proxyServiceAreaRepository)
	serviceAreaSettingAPI := srvarea.ProvideServiceAreaSettingAPI(proxyServiceAreaRepository, proxyRegionRepository, proxyAuditLogRepository, shiftServices, txnHelper, slackStub, serviceAreaConfig, globalConfig, proxyZoneRepository, serviceAreaService)
	summaryOfChangeAPI := summaryofchange.ProvideSummaryOfChangeAPI(proxySummaryOfChangeRepository, proxyAuditLogRepository)
	throttledOrderService := service.ProvideThrottledOrderService(throttledOrderRepository)
	throttledOrderAPI := throttled_order.ProvideThrottledOrderAPI(throttledOrderService)
	throttledDispatchDetailAdminAPI := throttleddispatchdetail.ProvideThrottledDispatchDetailAdminAPI(throttledDispatchDetailRepository, proxyAuditLogRepository, proxyZoneRepository)
	tripAPI := trip.ProvideTripAPI(tripService, deliveryDelivery, orderAPIConfig, proxyDriverRepository, proxyTripRepository, proxyOrderRepository, proxyAuditLogRepository, proxyServiceAreaRepository, proxyCoinCashConversionRateRepository, proxyAssignmentLogRepository, assignmentRepository, proxyDriverTransactionRepository, proxyRatingRestaurantRepository, featureflagService)
	withholdingtaxcertificateConfig := withholdingtaxcertificate.ProvideConfig()
	withholdingTaxCertificateAPI := withholdingtaxcertificate.ProvideWithholdingTaxCertificateAPI(proxyWithholdingTaxCertificateRepository, vosServiceImpl, withholdingtaxcertificateConfig)
	zoneAdminAPI := zone.ProvideZoneAdminAPI(zoneServiceImpl)
	atomicConfigLocator := configlocator.ProvideAtomicConfigLocator(atomicDriverLocationConfig)
	verdaKafkaCfg := kafcclient.ProvideVerdaKafkaCfg()
	distributionKafkaConsumerConfig := kafcclientdistribution.ProvideDistributionKafkaConsumerConfig()
	distributionKafcConsumerConfig := kafcclientdistribution.ProvideDistributionKafcConsumerConfig()
	distributionKafkaConsumer, cleanup27 := kafcclientdistribution.ProvideDistributionKafkaConsumer(distributionKafkaConsumerConfig, distributionKafcConsumerConfig, dispatcherDispatcher)
	monitorConn, cleanup28 := di.ProvideDBMonitorConnectionForTest(initTestDBConn)
	mongoProfilerConfig := config.ProvideMongoProfilerConfig()
	v19 := config.ProvideAtomicMongoProfilerConfig(dbConfigUpdater)
	mongoProfiler, cleanup29 := mongoprofiler.ProvideMongoProfiler(monitorConn, vosServiceImpl, mongoProfilerConfig, v19, prometheusMeter)
	kafkaConsumerConfig := mq.ProvideKafkaConsumerConfig()
	kafkaConsumer := mq.ProvideKafkaConsumer(kafkaConsumerConfig)
	config2 := polygon2.ProvidePolygonConfig()
	httpPolygon := polygon2.ProvideHttpPolygon(config2, client)
	riderLevelClient := riderlevel.ProvideRiderLevelConnector(stubGRPCFeaturePlatformClient, configRiderLevel)
	cronCron := cron.ProvideCron(localTaskExecutor, listTaskProvider)
	syncLocalTaskExecutor := cron.ProvideSyncLocalTaskExecutor(dataStoreJobRepository)
	regionRefresher := croninterval.ProvideRegionRefresher(proxyRegionRepository)
	cronIntervalRunner, cleanup30 := croninterval.ProvideCronIntervalRunner(regionRefresher)
	paymentAPI := &payment.API{
		TransactionAPI:       transactionAPI,
		TransactionSchemeAPI: transactionSchemeAPI,
		ApprovalAPI:          approvalAPI,
		DriverTransactionAPI: driverTransactionAPI,
		GroupTransactionAPI:  groupTransactionAPI,
	}
	apiSet := &router.APISet{
		AccountAPI:                      accountAPI,
		OTPAPI:                          otpapi,
		DeliveryPortalAPI:               deliveryPortalAPI,
		OrderPortalAPI:                  orderPortalAPI,
		OrderAPI:                        orderAPI,
		TripAPI:                         tripAPI,
		OrderAdminAPI:                   orderAdminAPI,
		CronApi:                         cronAPI,
		DriverAdminAPI:                  driverDriverAdminAPI,
		PhonesAPI:                       phonesAPI,
		DriverRegisAPI:                  registrationAPI,
		CitiAPI:                         citiAPI,
		UobAPI:                          uobAPI,
		SettingIncentiveAPI:             incentiveAPI,
		SettingOnTopFareAPI:             onTopFareAPI,
		BulkOntopCSVAPI:                 bulkOntopCSVAPI,
		BulkIncentiveCSVAPI:             bulkIncentiveCSVAPI,
		RegionAPI:                       regionAPI,
		RegionAdminAPI:                  regionAdminAPI,
		RegistrationAdminAPI:            registrationAdminAPI,
		DeliveryFee:                     deliveryFeeAPI,
		PaymentAPI:                      paymentAPI,
		SettingServiceAreaAPI:           serviceAreaSettingAPI,
		ClientAreaAPI:                   clientAreaAPI,
		AdminAPI:                        api,
		FraudAPI:                        fraudAPI,
		InternalAPI:                     internalAPI,
		HeatMapAPI:                      heatmapAPI,
		ProvinceAPI:                     provinceAPI,
		ProvinceAdminAPI:                provinceAdminAPI,
		EventAPI:                        eventAPI,
		RatingAPI:                       ratingAPI,
		RatingAdminAPI:                  ratingAdminAPI,
		PdpaApi:                         pdpaApi,
		DbConfigAdminAPI:                adminAPI,
		ShiftAPI:                        shiftAPI,
		IncentiveSourceAPI:              incentiveSourceAPI,
		WithholdingTaxCertificateApi:    withholdingTaxCertificateAPI,
		InstallmentApi:                  installmentAPI,
		ProductApi:                      productAPI,
		DriverInsuranceApi:              insuranceAPI,
		DriverDocsAPI:                   document,
		SchedulerAPI:                    schedulerAPI,
		HookAPI:                         hookAPI,
		DedicatedZoneAPI:                dedicatedZoneAPI,
		ZoneAdminAPI:                    zoneAdminAPI,
		BulkProcessAPI:                  bulkProcessInfoAPI,
		EtaxInvoiceAPI:                  etaxInvoiceAPI,
		ThrottledDispatchDetailAdminAPI: throttledDispatchDetailAdminAPI,
		TopupReportAPI:                  topupCreditReportAPI,
		RewardAdminAPI:                  rewardAdminAPI,
		RainSituationAPI:                rainSituationAPI,
		RainSituationAdminAPI:           rainSituationAdminAPI,
		BulkCoinConversionAPI:           bulkCoinConversionCSVAPI,
		EgsAPI:                          egsAPI,
		SummaryOfChangeAPI:              summaryOfChangeAPI,
		BulkAPI:                         bulkAPI,
		FeatureFlagConfigAPI:            featureFlagConfigAPI,
		AssignmentBenchmarkAPI:          assignmentBenchmarkAPI,
		OrderAssignerHandler:            orderAssignerHandler,
		SalesforceAdminAPI:              driverSalesforceAPI,
		ThrottledOrderAPI:               throttledOrderAPI,
	}
	apiSpecConfig := config.ProvideAPISpecConfig()
	routerConfig := config.ProvideRouterConfig()
	engine := di.ProvideGinEngineRouter(apiSet, builder, apiSpecConfig, routerConfig, featureflagService)
	initToggle := di.ProvideInitToggle(dbConfigUpdater, prometheusMeter)
	driverStatRedisConfig := providers.ProvideDriverStatRedisTestConfig(redisConfig)
	driverStatRedisClient, cleanup31 := event2.ProvideDriverStatRedisClient(driverStatRedisConfig)
	driverMarker := event2.ProvideDriverMarker()
	driverStatisticConfig := event2.ProvideDriverStatisticConfig()
	duplicatePreventionStoreInterface := event2.ProvideDuplicatePreventionStore(redisClient, driverStatisticConfig)
	v20 := event2.ProvideAtomicDriverStatisticConfig(dbConfigUpdater)
	exceedARDetector := event2.ProvideExceedArDetector()
	driverOrderInfoProcessor := event2.ProvideDriverOrderInfoProcessor(driverMarker, duplicatePreventionStoreInterface, driverStatRedisClient, proxyDriverRepository, proxyIncentiveRepository, proxyIncentiveProgressRepository, kafkaConsumerConfig, driverOrderInfoAPI, v20, kafkaConsumer, featureflagService, exceedARDetector)
	gokaStream, cleanup32 := event2.ProvideGokaStream(driverOrderInfoProcessor)
	defaultUnleashAdmin := featureflag.ProvideStubUnleashAdmin()
	defaultLMWNUnleasher := featureflag.ProvideStubLMWNUnleasher()
	dapfeastConfig := dapfeast.ProvideConfig()
	stubGRPCFeatureV2ServiceClient := dapfeast.ProvideStubGRPCFeatureV2ServiceClient(dapfeastConfig)
	featureplatformConfig := featureplatform.ProvideConfig()
	featureplatformClient, cleanup33 := featureplatform.ProvideGRPCFeaturePlatformClient(featureplatformConfig)
	driverAssignmentLogsRevisionsDataStore := persistence.ProvideDriverAssignmentLogsRevisionsDataStore(conn)
	lazyRepEventBus, cleanup34 := infrastructure.ProvideLazyRepEventBus(repConfig, prometheusMeter)
	messagesConfig := messages.ProvideConfig()
	lineBotNotificationServiceWorker, cleanup35 := messages.ProvideLineBotNotificationServiceWorker(messagesConfig, client)
	atomicH3Global := scheduler.ProvideH3Global(dbConfigUpdater)
	taskBulkScheduler := scheduler.ProvideTaskBulkScheduler(schedulerScheduler, globalConfig, bulkConfig, asynqClient, operations, processInfoService, operationService, slackStub)
	taskDetectInactiveDriverConfig := scheduler.ProvideTaskDetectInactiveDriverConfig()
	taskDetectInactiveDriver := scheduler.ProvideTaskDetectInactiveDriver(schedulerScheduler, proxyDriverLastUpdateLocationTrackerRepository, taskDetectInactiveDriverConfig, driverUpdateLocationEventService, proxyDriverRepository)
	taskDistributeDeferredOrdersConfig := scheduler.ProvideTaskDistributeDeferredOrdersConfig()
	taskDistributeDeferredOrders := scheduler.ProvideTaskDistributeDeferredOrders(schedulerScheduler, deferredOrderRepository, taskDistributeDeferredOrdersConfig, dispatcherDispatcher, txnHelper, proxyOrderRepository, deliveryDelivery, orderDistributionEventManager, featureflagService, distributionService)
	taskDummyConfig := scheduler.ProvideTaskDummyConfig()
	taskDummy := scheduler.ProvideTaskDummy(schedulerScheduler, taskDummyConfig, asynqClient)
	taskExpireRecommendationConfig := scheduler.ProvideTaskExpireRecommendationConfig()
	taskExpireRecommendation := scheduler.ProvideTaskExpireRecommendation(taskExpireRecommendationConfig, proxyDriverRepository, pushNotifier)
	taskForceOfflineDriverConfig := scheduler.ProvideTaskForceOfflineDriverConfig()
	taskForceOfflineDriver := scheduler.ProvideTaskForceOfflineDriver(schedulerScheduler, proxyDriverActiveTimeRepository, driverStatusAPI, taskForceOfflineDriverConfig)
	taskMonitorHelperConfig := scheduler.ProvideTaskMonitorHelperConfig()
	taskMonitorHelper := scheduler.ProvideTaskMonitorHelper(schedulerScheduler, uobPayoutMetric, taskMonitorHelperConfig)
	atomicTaskOrderHealthCheckDbConfig := scheduler.ProvideTaskOrderHealthCheckDbConfig(dbConfigUpdater)
	taskOrderHealthCheck := scheduler.ProvideTaskOrderHealthCheck(schedulerScheduler, atomicTaskOrderHealthCheckDbConfig, redisLocker, dispatcherDispatcher, orderHeartbeatServiceImpl, prometheusMeter, distributionService, featureflagService, proxyOrderRepository)
	taskPushRunnerOrderConfig := scheduler.ProvideTaskPushRunnerOrderConfig()
	atomicRunnerOrderConfig := scheduler.ProvideRunnerOrderConfig(dbConfigUpdater)
	taskPushRunnerOrder := scheduler.ProvideTaskPushRunnerOrderScheduler(schedulerScheduler, proxyOrderRepository, taskPushRunnerOrderConfig, atomicRunnerOrderConfig)
	taskThrottleJobControllerConfig := scheduler.ProvideTaskThrottleJobControllerConfig()
	taskThrottleJobController := scheduler.ProvideTaskThrottleJobController(schedulerScheduler, throttledDispatchDetailRepository, throttledOrderRepository, taskThrottleJobControllerConfig, dispatcherDispatcher, atomicH3Global)
	taskUnacknowledgeReassignConfig := scheduler.ProvideTaskUnacknowledgeReassignConfig()
	taskUnacknowledgeReassign := scheduler.ProvideTaskUnacknowledgeReassign(schedulerScheduler, taskUnacknowledgeReassignConfig, providerDeps, driverStatusAPI, prometheusMeter)
	taskUpdateDriverDedicatedZonesConfig := scheduler.ProvideTaskUpdateDriverDedicatedZonesConfig()
	taskUpdateDriverDedicatedZones := scheduler.ProvideTaskUpdateDriverDedicatedZones(schedulerScheduler, taskUpdateDriverDedicatedZonesConfig, proxyBulkProcessInfoRepository, proxyDriverRepository, asynqClient, bulkSchedulerWorker)
	providerRegistry := serviceprovider.ProvideRegistry()
	serviceSelector := serviceprovider.ProvideServiceSelectorAPI(proxyOrderRepository, providerRegistry)
	driverRegistrationTestData := testdata.ProvideDriverRegistrationTestData(driverRegistrationDataStore)
	otpSessionTestData := testdata.ProvideOTPSessionTestData(proxyOTPSessionRepo)
	provincesTestData := testdata.ProvideProvincesTestData(provinceDataStore)
	ratingRestaurantTestData := testdata.ProvideRatingRestaurantTestData(ratingRestaurantDataStore)
	whitelistPhonesTestData := testdata.ProvideWhitelistPhonesTestData(whitelistPhoneDataStore)
	locator := &di.Locator{
		AccountAPI:                              accountAPI,
		AccountService:                          accountServiceInterface,
		API:                                     api,
		BanMetadataAPI:                          banMetadataAPI,
		DriverOperationAdminAPI:                 driverAdminAPI,
		TopupCreditReportAPI:                    topupCreditReportAPI,
		AssignmentBenchmarkAPI:                  assignmentBenchmarkAPI,
		BulkAPI:                                 bulkAPI,
		BulkOperations:                          operations,
		CreateBulkIncentive:                     createBulkIncentive,
		DeleteBulkIncentive:                     deleteBulkIncentive,
		ExporterBulkIncentive:                   exportBulkIncentive,
		UpdateBulkIncentive:                     updateBulkIncentive,
		BulkToggleOnRiderServicePreference:      bulkToggleOnRiderServicePreference,
		BulkUpdateServiceTypeSilentBanned:       bulkUpdateServiceTypeSilentBanned,
		BulkCoinConversionCSVAPI:                bulkCoinConversionCSVAPI,
		BulkIncentiveCSVAPI:                     bulkIncentiveCSVAPI,
		BulkOntopCSVAPI:                         bulkOntopCSVAPI,
		BulkProcessInfoAPI:                      bulkProcessInfoAPI,
		BulkSchedulerWorker:                     bulkSchedulerWorker,
		CronAPI:                                 cronAPI,
		SchedulerAPI:                            schedulerAPI,
		DbConfigAdminAPI:                        adminAPI,
		DedicatedZoneAPI:                        dedicatedZoneAPI,
		DeliveryFeeAPI:                          deliveryFeeAPI,
		Document:                                document,
		DriverAdminAPI:                          driverDriverAdminAPI,
		DriverNotifyAPI:                         driverNotifyAPI,
		DriverSalesforceAPI:                     driverSalesforceAPI,
		DriverStatusAPI:                         driverStatusAPI,
		PdpaApi:                                 pdpaApi,
		RegistrationAdminAPI:                    registrationAdminAPI,
		RegistrationAPI:                         registrationAPI,
		InsuranceAPI:                            insuranceAPI,
		AutoBanCompleteOrderTooOftenCfg:         v15,
		AutoBanCompleteOrderTooFarCfg:           v16,
		DriverOrderInfoAPI:                      driverOrderInfoAPI,
		EgsAPI:                                  egsAPI,
		EtaxInvoiceAPI:                          etaxInvoiceAPI,
		EventAPI:                                eventAPI,
		FeatureFlagConfigAPI:                    featureFlagConfigAPI,
		FraudAPI:                                fraudAPI,
		HeatMapAPI:                              heatmapAPI,
		HookAPI:                                 hookAPI,
		IncentiveAPI:                            incentiveAPI,
		IncentiveProgressDataStore:              incentiveProgressDataStore,
		DataStoreIncentiveProgressRepository:    proxyIncentiveProgressRepository,
		IncentiveDataStore:                      incentiveDataStore,
		DataStoreIncentiveRepository:            proxyIncentiveRepository,
		IncentiveSourceAPI:                      incentiveSourceAPI,
		InstallmentAPI:                          installmentAPI,
		InternalAPI:                             internalAPI,
		MiddlewareBuilder:                       builder,
		OnTopFareAPI:                            onTopFareAPI,
		AcceptorDeps:                            acceptorDeps,
		Acceptor:                                acceptor,
		AdminAPI:                                orderAdminAPI,
		DeliveryAPIPortal:                       deliveryPortalAPI,
		Canceller:                               cancellerImpl,
		AssigningStateManager:                   assigningStateManager,
		AutoAssignOrderDistributor:              autoAssignOrderDistributor,
		AutoAssignOrderDistributorDeps:          autoAssignOrderDistributorDeps,
		ConfigValidator:                         configValidator,
		MemLocker:                               memLocker,
		OrderAPI:                                orderAPI,
		OrderPortalAPI:                          orderPortalAPI,
		OrderAssignerHandler:                    orderAssignerHandler,
		FoodProvider:                            foodProviderImpl,
		ProviderDeps:                            providerDeps,
		OTPAPI:                                  otpapi,
		CitiAuthenticationService:               citiAuthenticationService,
		CitiAPI:                                 citiAPI,
		UobAPI:                                  uobAPI,
		ApprovalAPI:                             approvalAPI,
		ApprovalCreator:                         approvalCreator,
		ApprovalService:                         approvalService,
		DriverTransactionAPI:                    driverTransactionAPI,
		DriverTransactionService:                driverTransactionService,
		GroupTransactionAPI:                     groupTransactionAPI,
		PaymentService:                          paymentServiceImpl,
		UOBPayoutMetric:                         uobPayoutMetric,
		TransactionAPI:                          transactionAPI,
		TransactionSchemeAPI:                    transactionSchemeAPI,
		TransactionService:                      transactionService,
		PhoneAPI:                                phonesAPI,
		ProductAPI:                              productAPI,
		ProvinceAdminAPI:                        provinceAdminAPI,
		ProvinceAPI:                             provinceAPI,
		RainSituationAdminAPI:                   rainSituationAdminAPI,
		RainSituationAPI:                        rainSituationAPI,
		RatingAdminAPI:                          ratingAdminAPI,
		RatingOptionAPI:                         ratingAPI,
		RegionAdminAPI:                          regionAdminAPI,
		RegionAPI:                               regionAPI,
		RewardAdminAPI:                          rewardAdminAPI,
		ShiftAPI:                                shiftAPI,
		ClientAreaAPI:                           clientAreaAPI,
		ServiceAreaSettingAPI:                   serviceAreaSettingAPI,
		SummaryOfChangeAPI:                      summaryOfChangeAPI,
		ThrottledOrderAPI:                       throttledOrderAPI,
		ThrottledDispatchDetailAdminAPI:         throttledDispatchDetailAdminAPI,
		TripAPI:                                 tripAPI,
		WithholdingTaxCertificateAPI:            withholdingTaxCertificateAPI,
		ZoneAdminAPI:                            zoneAdminAPI,
		AsynqClient:                             asynqClient,
		RedisTokenStore:                         redisTokenStorage,
		RekognitionClient:                       rekognitionImpl,
		DriverCollectionDataStore:               driverDataStore,
		BulkGinHandlers:                         ginHandlersFn,
		IncentiveCollectionDataStore:            incentiveCollectionDataStore,
		OperationService:                        operationService,
		MongoProcessInfoRepository:              processInfoRepository,
		ProcessInfoService:                      processInfoService,
		ConfigUpdater:                           dbConfigUpdater,
		DBConfigDataStore:                       dbConfigDataStore,
		DBConfigRepository:                      proxyDBConfigRepository,
		AtomicConfigLocator:                     atomicConfigLocator,
		BCPClient:                               bcpClient,
		BCPTestClientStub:                       bcpTestClientStub,
		Cache:                                   cacheCache,
		Delivery:                                deliveryDelivery,
		StubDeliveryFleetApi:                    stubDeliveryFleetApi,
		DriverServiceDispatcher:                 dispatcherDispatcher,
		DistributionExperimentPlatformClient:    distributionExperimentPlatformClient,
		FleetAreaClient:                         fleetAreaClient,
		FleetOrderClient:                        fleetOrderClient,
		FraudAdvisorServiceImpl:                 fraudAdvisorServiceImpl,
		HeatMapDemand:                           heatMapDemand,
		INetClient:                              iNetClient,
		VerdaKafkaCfg:                           verdaKafkaCfg,
		DistributionKafkaConsumer:               distributionKafkaConsumer,
		SecureIMFKafkaSyncProducerClientForTest: secureIMFKafkaSyncProducerStubClient,
		IMFKafkaProducerClientForTest:           kafkaProducerStubClient,
		SecureIMFKafkaProducerClientForTest:     secureKafkaProducerStubClient,
		LINEClient:                              lineinternalLINEClient,
		LINEInternalClient:                      lineinternalClient,
		RedisLocker:                             redisLocker,
		ExperimentalMapServiceClient:            experimentalMapService,
		StubMapService:                          stubMapService,
		FeaturePlatformService:                  matchrateService,
		MongoProfiler:                           mongoProfiler,
		MongoTxnHelper:                          txnHelper,
		KafkaConsumer:                           kafkaConsumer,
		HttpPolygon:                             httpPolygon,
		Prediction:                              predictionPrediction,
		RiderLevelConnector:                     riderLevelClient,
		StubRiderLevelConnector:                 stubRiderLevelConnector,
		Slack:                                   slackStub,
		SlackStub:                               slackStub,
		SMS:                                     smsSMS,
		UobClient:                               uob,
		UWTErrorService:                         stubUWTErrorService,
		StubUWTErrorService:                     stubUWTErrorService,
		Cron:                                    cronCron,
		DateStoreJobRepository:                  dataStoreJobRepository,
		ATTRLogArchiveTask:                      attrLogArchiveTask,
		BNPLSOAFileTask:                         bnplsoaFileTask,
		CitiDailyReconcileTask:                  citiDailyReconcileTask,
		CitiInquiryTransactionTask:              citiInquiryTransactionTask,
		CitiItemizeReportTask:                   citiItemizeReportTask,
		ConvertCoinToCashTask:                   convertCoinToCashTask,
		DailyIncomeTask:                         dailyIncomeTask,
		DailyUnbanTask:                          dailyUnbanTask,
		EffectBanTimeTask:                       effectBanTimeTask,
		EtaxInvoiceGeneratorTask:                etaxInvoiceGeneratorTask,
		LocalTaskExecutor:                       localTaskExecutor,
		SyncLocalTaskExecutor:                   syncLocalTaskExecutor,
		ExpiredAttendanceStatTask:               expiredAttendanceStatTask,
		ExpiredFreeCreditTask:                   expiredFreeCreditTask,
		ForceCompleteOrderTask:                  forceCompleteOrderTask,
		FoodForceCompleteOrder:                  cronForceCompleteOrder,
		GroupTransactionTask:                    groupTransactionTask,
		IncentiveTask:                           incentiveTask,
		InstallmentDailyDeductionTask:           installmentDailyDeductionTask,
		MatchingRateHeatMapTask:                 matchingRateHeatMapTask,
		MigrateDriverMIDTask:                    migrateDriverMIDTask,
		PayoutWithdrawalTask:                    payoutWithdrawalTask,
		PayoutWithdrawalDailyEmailTask:          payoutWithdrawalDailyEmailTask,
		ShiftInstructionNotificationTask:        shiftInstructionNotificationTask,
		Tawi50GeneratorTask:                     tawi50GeneratorTask,
		TodayEarningTask:                        todayEarningTask,
		UobTopupTransactionTask:                 uobTopupTransactionTask,
		UpdateAbsoluteErrorStatsTask:            updateAbsoluteErrorStatsTask,
		CronIntervalRunner:                      cronIntervalRunner,
		RegionRefresher:                         regionRefresher,
		RedisConn:                               redisConn,
		Redis:                                   redisClient,
		SecondaryDBConnection:                   secondaryDBConnection,
		DBConnectionForTest:                     conn,
		DBMonitorConnectionForTest:              monitorConn,
		GinEngineRouter:                         engine,
		InitToggle:                              initToggle,
		NewOTPSession:                           newOTPSession,
		TimeNow:                                 timeNowFunc,
		AccountingHubTransactionService:         accountingHubTransactionService,
		AreaServiceImpl:                         areaServiceImpl,
		AssignmentBenchmarkCfg:                  assignmentBenchmarkCfg,
		AssignmentBenchmarkService:              assignmentBenchmarkService,
		Attendances:                             attendances,
		AwsServiceImpl:                          awsServiceImpl,
		BanServiceImpl:                          banServiceImpl,
		BCPStatusTransitionerService:            bcpStatusTransitionerService,
		DeliveryFeeService:                      deliveryFeeService,
		DistributionLogEventService:             distributionLogEventService,
		DistributionLogManager:                  distributionLogManager,
		DistributionService:                     distributionService,
		DriverInsuranceService:                  driverInsuranceServiceImpl,
		DriverInsuranceCfg:                      driverInsuranceCfg,
		QRIncidentOrderService:                  qrIncidentOrderServiceImpl,
		DeviceManagerImpl:                       deviceManagerImpl,
		DriverService:                           driverService,
		DriverServiceTypeCapacityService:        driverServiceTypeCapacityService,
		DriverTransactionServiceV2:              driverTransactionServiceV2,
		DriverLocationEventServiceImpl:          driverUpdateLocationEventService,
		InstallmentOnTopTransactionProvider:     installmentOnTopTransactionProvider,
		OnTopSchemeTransactionProviderSelector:  onTopSchemeTransactionProviderSelector,
		EGSService:                              serviceEGS,
		EGSCfg:                                  egsCfg,
		EGSOrderService:                         egsOrderService,
		EtaxInvoiceService:                      etaxInvoiceServiceImpl,
		EtaxInvoiceCfg:                          etaxInvoiceCfg,
		Service:                                 formService,
		FraudService:                            fraudService,
		IncentiveSourceServiceImpl:              incentiveSourceServiceImpl,
		InstallmentService:                      installmentService,
		LocationManagerImpl:                     locationManagerImpl,
		MissionLogEventService:                  missionLogEventService,
		PushNotifier:                            pushNotifier,
		OnTopFareService:                        onTopFareService,
		OrderService:                            orderService,
		OrderDistributionEventManager:           orderDistributionEventManager,
		OrderDistributionEventService:           orderDistributionEventService,
		OrderHeartbeatService:                   orderHeartbeatServiceImpl,
		PendingTransactionService:               pendingtransactionService,
		PredictionService:                       predictionService,
		RainSituationService:                    rainSituationServiceImpl,
		RewardService:                           rewardService,
		ServiceAreaServiceImpl:                  serviceAreaService,
		ServiceOptInReminderService:             serviceOptInReminderService,
		ServicePreferenceKillSwitchService:      servicePreferenceKillSwitchService,
		ServicePreferenceService:                servicePreferenceService,
		ShiftServices:                           shiftServices,
		StatisticServiceImpl:                    statisticServiceImpl,
		SupplyPositioningRecommenderService:     supplyPositioningRecommenderService,
		TermAndConditionServiceImpl:             termAndConditionServiceImpl,
		ThrottledOrderService:                   throttledOrderService,
		TiersAndBenefitsService:                 tiersAndBenefitsService,
		TransactionServiceV2:                    service2,
		TranslationServiceImpl:                  translationServiceImpl,
		TripServices:                            tripService,
		VOSServiceImpl:                          vosServiceImpl,
		ZoneServiceImpl:                         zoneServiceImpl,
		EmailService:                            emailService,
		DriverStatRedisClient:                   driverStatRedisClient,
		DriverOrderInfoProcessor:                driverOrderInfoProcessor,
		DuplicatePreventionStore:                duplicatePreventionStoreInterface,
		ExceedArDetector:                        exceedARDetector,
		GokaStream:                              gokaStream,
		StubUnleashAdmin:                        defaultUnleashAdmin,
		StubLMWNUnleasher:                       defaultLMWNUnleasher,
		FeatureFlagService:                      featureflagService,
		ChatServiceClient:                       mockChatServiceClient,
		StubChatGRPCService:                     mockChatServiceClient,
		CoinPlanGRPCClient:                      mockCoinPlanGRPCClient,
		StubCoinPlanGRPCClient:                  mockCoinPlanGRPCClient,
		GRPCFeatureV2ServiceClient:              stubGRPCFeatureV2ServiceClient,
		StubGRPCFeatureV2ServiceClient:          stubGRPCFeatureV2ServiceClient,
		GRPCDriverProvisionClient:               stubGRPCRecommendationService,
		StubGRPCRecommendationService:           stubGRPCRecommendationService,
		GRPCEGSServiceClient:                    mockEGSServiceClient,
		StubGRPCEGSService:                      mockEGSServiceClient,
		GRPCEGSBranchServiceClient:              stubGRPCEGSBranchService,
		StubGRPCEGSBranchService:                stubGRPCEGSBranchService,
		GRPCEGSOrderServiceClient:               stubGRPCEGSOrderService,
		StubGRPCEGSOrderService:                 stubGRPCEGSOrderService,
		GRPCFeaturePlatformClient:               featureplatformClient,
		StubGPRCFeaturePlatformClient:           stubGRPCFeaturePlatformClient,
		FleetPoolGRPCClient:                     grpcClient,
		RiderSearchServiceClient:                riderSearchServiceClient,
		GRPCFormServiceClient:                   mockFormServiceClient,
		StubGRPCFormService:                     mockFormServiceClient,
		GRPCPriorityGroupServiceClient:          stubGRPCPriorityGroupService,
		StubGRPCPriorityGroupService:            stubGRPCPriorityGroupService,
		GRPCProductServiceClient:                stubGRPCProductService,
		StubGRPCProductService:                  stubGRPCProductService,
		GRPCMarketplaceServiceClient:            mockMarketplaceServiceClient,
		StubGRPCMarketplaceService:              mockMarketplaceServiceClient,
		GRPCUserPolygonServiceClient:            mockUserPolygonServiceClient,
		StubGRPCUserPolygonService:              mockUserPolygonServiceClient,
		PriceInterventionClient:                 priceInterventionClientStub,
		PriceInterventionServiceClientStub:      priceInterventionClientStub,
		RainServiceClient:                       mockRainServiceClient,
		RainServiceClientStub:                   mockRainServiceClient,
		TranslationGRPCClient:                   translationGRPCClient,
		TranslationServiceClient:                translationServiceClient,
		GRPCUserServiceClient:                   mockUserServiceClient,
		StubGRPCUserService:                     mockUserServiceClient,
		FraudAdvisorHTTPClient:                  fraudAdvisorHTTPClient,
		DefaultClient:                           client,
		DalianClient:                            dalianClient,
		IncomeAggregateService:                  incomeAggregateServiceImpl,
		IncomeSummaryService:                    incomeSummaryServiceImpl,
		CoinConversionRateLocalCache:            coinConversionRateLocalCache,
		CoinConversionRateRedisCache:            coinConversionRateRedisCache,
		CoinConversionRateMinimalRedisCache:     coinConversionRateMinimalRedisCache,
		InfraLocalTaskExecutor:                  executorLocalTaskExecutor,
		StubKafkaConnector:                      stubKafkaConnector,
		DataStoreAccountingHubTransactionRepository: proxyAccountingHubTransactionRepository,
		AccountingHubTransactionDataStore:           accountingHubTransactionDataStore,
		DataStoreApprovalRepository:                 proxyApprovalRepository,
		ApprovalDataStore:                           approvalDataStore,
		AssignmentBenchmarkDataStore:                assignmentBenchmarkDataStore,
		AssignmentBenchmarkRepository:               assignmentBenchmarkRepository,
		MongoAssignmentLogRepository:                proxyAssignmentLogRepository,
		DriverAssignmentLogsDataStore:               driverAssignmentLogsDataStore,
		DriverAssignmentLogsRevisionsDataStore:      driverAssignmentLogsRevisionsDataStore,
		DataStoreAssignmentRejectionRepository:      proxyAssignmentRejectionRepository,
		AssignmentRejectionDataStore:                assignmentRejectionDataStore,
		AssignmentDataStore:                         assignmentDataStore,
		AssignmentRepository:                        assignmentRepository,
		DataStoreAttendanceLogHistoryRepository:     proxyAttendanceLogHistoryRepository,
		AttendanceLogHistoryDataStore:               attendanceLogHistoryDataStore,
		AuditLogRepository:                          proxyAuditLogRepository,
		AuditLogDataStore:                           dataStoreInterface,
		BanEffectiveTimeRepository:                  proxyBanEffectiveTimeRepository,
		BanEffectiveTimeDataStore:                   banEffectiveTimeDataStore,
		BanHistoryServiceImpl:                       proxyBanHistoryRepository,
		BanHistoriesDataStore:                       banHistoriesDataStore,
		BanMetadataDataStore:                        mongoBanMetadataDataStore,
		BanMetadataRepository:                       proxyBanMetadataRepository,
		BCPOrderDataStore:                           bcpOrderDataStore,
		BCPOrderRepository:                          bcpOrderRepository,
		DataStoreBulkProcessInfoRepository:          proxyBulkProcessInfoRepository,
		BulkProcessInfoDataStore:                    bulkProcessInfoDataStore,
		DataStoreCancelReasonRepository:             proxyCancelReasonRepository,
		CancelReasonDataStore:                       cancelReasonDataStore,
		MongoClientAreaRepository:                   proxyClientAreaRepository,
		ClientAreaDataStore:                         clientAreaDataStore,
		CoinCashConversionRateRepository:            proxyCoinCashConversionRateRepository,
		CoinCashConversionRateDataStore:             mongoCoinCashConversionRateDataStore,
		ConsolidatedInsuranceRepository:             proxyConsolidatedInsuranceRepository,
		ConsolidatedInsuranceDataStore:              consolidatedInsuranceDataStore,
		CookingTimeDelayRepository:                  cookingTimeDelayRepository,
		CounterDataStore:                            counterDataStore,
		CounterRepository:                           proxyCounterRepository,
		MongoDailyRewardRepository:                  proxyDailyRewardRepository,
		MongoDailyRewardDataStore:                   mongoDailyRewardDataStore,
		DedicatedZoneDataStore:                      dedicatedZoneDataStore,
		DedicatedZoneRepository:                     dedicatedZoneRepository,
		DeferredOrderDataStore:                      deferredOrderDataStore,
		DeferredOrderRepository:                     deferredOrderRepository,
		DeliveryFeeSettingDataStore:                 deliveryFeeSettingDataStore,
		DeliveryFeeSettingMongo:                     proxyDeliveryFeeSettingRepository,
		RedisDriverActiveTimeRepository:             proxyDriverActiveTimeRepository,
		DriverDocumentRepository:                    proxyDriverDocumentRepository,
		DriverDocumentDatastore:                     driverDocumentDatastore,
		InsuranceRepository:                         proxyDriverInsuranceRepository,
		DriverInsuranceDataStore:                    driverInsuranceDataStore,
		RedisLastUpdateLocationTrackerRepository:    proxyDriverLastUpdateLocationTrackerRepository,
		LocationRedisClient:                         locationRedisClient,
		DriverLocationRepositoryForTest:             proxyDriverLocationRepository,
		DriverOrderInfoRepository:                   proxyDriverOrderInfoRepository,
		DriverOrderInfoDataStore:                    driverOrderInfoDataStore,
		DriverRegistrationRepository:                proxyDriverRegistrationRepository,
		DriverRegistrationDataStore:                 driverRegistrationDataStore,
		DriverRepository:                            proxyDriverRepository,
		DriversDataStore:                            driversDataStore,
		DataStoreDriverStatisticRepository:          proxyDriverStatisticRepository,
		DriverStatisticDataStore:                    driverStatisticDataStore,
		DataStoreDriverTransactionRepository:        proxyDriverTransactionRepository,
		DriverTransactionDataStore:                  driverTransactionDataStore,
		EtaxInvoiceDataStore:                        etaxInvoiceDataStore,
		EtaxInvoiceRepository:                       proxyEtaxInvoiceRepository,
		MongoGroupTransactionRepository:             proxyGroupTransactionRepository,
		GroupTransactionDataStore:                   groupTransactionDataStore,
		HeatMapDataStore:                            heatMapDataStore,
		HeatMapRepository:                           proxyHeatMapRepository,
		IllegalDriverRepository:                     illegalDriverRepository,
		IncomeDailySummaryStore:                     incomeDailySummaryStore,
		IncomeDailySummaryRepository:                proxyIncomeDailySummaryRepository,
		InstallmentRepository:                       proxyInstallmentRepository,
		InstallmentDataStore:                        installmentDataStore,
		InternalCancelReasonDataStore:               internalCancelReasonDataStore,
		InternalCancelReasonRepository:              proxyInternalCancelReasonRepository,
		LINEStatelessTokenCacheRepository:           lineStatelessTokenCacheRepository,
		MatchingRateHeatMapDataStore:                heatMapMatchingRateDataStore,
		MatchingRateHeatMapRepository:               proxyMatchingRateHeatMapRepository,
		MongoMinimalOrderRepository:                 proxyMinimalOrderRepository,
		OnTopFareDataStore:                          onTopFareDataStore,
		OnTopFareRepository:                         proxyOnTopFareRepository,
		OrderHearthBeatRepository:                   proxyOrderHeartbeatRepository,
		MongoOrderRepository:                        proxyOrderRepository,
		OrderRevisionDataStore:                      orderRevisionDataStore,
		OrderDataStore:                              orderDataStore,
		OTPSessionRepo:                              proxyOTPSessionRepo,
		PdpaRepository:                              proxyPdpaRepository,
		PdpaDataStore:                               pdpaDataStore,
		DataStorePendingTransactionRepository:       proxyPendingTransactionRepository,
		PendingTransactionDataStore:                 pendingTransactionDataStore,
		ProductGroupRepository:                      proxyProductGroupRepository,
		ProductGroupDataStore:                       productGroupDataStore,
		ProductRepository:                           proxyProductRepository,
		ProductDataStore:                            productDataStore,
		ProvinceRepository:                          proxyProvinceRepository,
		ProvinceDataStore:                           provinceDataStore,
		QuestionConfigs:                             questionsConfig,
		FileQuestionRepository:                      proxyQuestionRepository,
		MongoQuoteRepository:                        proxyQuoteRepository,
		QuoteDataStore:                              quoteDataStore,
		RainSituationDataStore:                      rainSituationDataStore,
		RainSituationRepository:                     proxyRainSituationRepository,
		RatingOptionRepository:                      proxyRatingOptionRepository,
		RatingOptionDataStore:                       ratingOptionDataStore,
		RatingRestaurantRepository:                  proxyRatingRestaurantRepository,
		RatingRestaurantDataStore:                   ratingRestaurantDataStore,
		RegionRepository:                            proxyRegionRepository,
		RequestUpdateProfileRepository:              proxyRequestUpdateProfileRepository,
		RequestUpdateProfileDataStore:               requestUpdateProfileDataStore,
		RequestUpdateProfileSectionRepository:       proxyRequestUpdateProfileSectionRepository,
		RequestUpdateProfileSectionDataStore:        requestUpdateProfileSectionDataStore,
		RewardBalanceRepository:                     proxyRewardBalanceRepository,
		RewardBalanceDataStore:                      mongoRewardBalanceDataStore,
		MongoRewardTransactionRepository:            proxyRewardTransactionRepository,
		MongoRewardTransactionDataStore:             mongoRewardTransactionDataStore,
		MongoServiceAreaRepository:                  proxyServiceAreaRepository,
		ServiceAreaDataStore:                        serviceAreaDataStore,
		OptInReminderRepository:                     serviceOptInReminderRepository,
		SettingDeliveryFeePriceSchemesDataStore:     settingDeliveryFeePriceSchemesDataStore,
		SettingDeliveryFeePriceSchemesRepository:    proxySettingDeliveryFeePriceSchemesRepository,
		ShiftCancelRepository:                       proxyShiftCancelRepository,
		ShiftCancelDataStore:                        shiftCancelDataStore,
		ShiftRepository:                             proxyShiftRepository,
		ShiftDataStore:                              shiftDataStore,
		SummaryOfChangeRepository:                   proxySummaryOfChangeRepository,
		SummaryOfChangeDataStore:                    summaryOfChangeDataStore,
		MongoTermAndConditionRepository:             proxyTermAndConditionRepository,
		TermAndConditionDataStore:                   termAndConditionDataStore,
		ThrottledDispatchDetailDataStore:            throttledDispatchDetailDataStore,
		ThrottledDispatchDetailRepository:           throttledDispatchDetailRepository,
		ThrottledOrderDataStore:                     throttledOrderDataStore,
		ThrottledOrderRepository:                    throttledOrderRepository,
		TopupCreditReportDataStore:                  topupCreditReportDataStore,
		TopupCreditReportRepository:                 proxyTopupCreditReportRepository,
		MongoTransactionFraudScoreRepository:        proxyTransactionFraudScoreRepository,
		TransactionFraudScoreDataStore:              transactionFraudScoreDataStore,
		DataStoreTransactionRepository:              proxyTransactionRepository,
		TransactionDataStore:                        transactionDataStore,
		MongoTransactionSchemeRepository:            proxyTransactionSchemeRepository,
		TransactionSchemeDataStore:                  transactionSchemeDataStore,
		TripDataStore:                               tripDataStore,
		TripRepository:                              proxyTripRepository,
		UnAcknowledgeReassignRepository:             unAcknowledgeReassignReposity,
		MongoUobRefRepository:                       proxyUobRefRepository,
		UobRefDataStore:                             uobRefDataStore,
		WhitelistPhoneRepository:                    proxyWhitelistPhoneRepository,
		WhitelistPhoneDataStore:                     whitelistPhoneDataStore,
		WithdrawalTransactionResultsRepository:      proxyWithdrawalTransactionResultsRepository,
		WithdrawalTransactionResultsDataStore:       withdrawalTransactionResultsDataStoreDataStore,
		WithholdingTaxCertificateRepository:         proxyWithholdingTaxCertificateRepository,
		WithholdingTaxCertificateDataStore:          withholdingTaxCertificateDataStore,
		ZoneDataStore:                               zoneDataStore,
		ZoneRepository:                              proxyZoneRepository,
		LazyRepEventBus:                             lazyRepEventBus,
		RepEventBus:                                 repEventBus,
		CleanupPriority:                             cleanupPriority,
		LocalCache:                                  caches,
		LineBotNotificationServiceWorker:            lineBotNotificationServiceWorker,
		StubMessageService:                          stubMessageService,
		FirebasePushNotificationService:             firebasePushNotificationService,
		SocketIOPushNotificationService:             socketIOPushNotificationService,
		SocketIOClient:                              socketioClient,
		ConsoleFBMessagingClient:                    consoleLogMessagingClient,
		PDFBuilder:                                  pdfBuilderImpl,
		PDFBuilderCfg:                               pdfBuilderCfg,
		PDFGenerator:                                pdfGenerator,
		PreloadExecutor:                             preloadExecutor,
		WorkerContext:                               workerContext,
		H3Global:                                    atomicH3Global,
		Scheduler:                                   schedulerScheduler,
		TaskBulkScheduler:                           taskBulkScheduler,
		TaskDetectInactiveDriver:                    taskDetectInactiveDriver,
		TaskDistributeDeferredOrders:                taskDistributeDeferredOrders,
		TaskDummy:                                   taskDummy,
		TaskExpireOrder:                             taskExpireOrder,
		TaskExpireRecommendation:                    taskExpireRecommendation,
		TaskFetchRainSituation:                      taskFetchRainSituation,
		TaskForceOfflineDriver:                      taskForceOfflineDriver,
		TaskMonitorHelper:                           taskMonitorHelper,
		TaskOrderHealthCheck:                        taskOrderHealthCheck,
		TaskPushRunnerOrderScheduler:                taskPushRunnerOrder,
		TaskQRIncidentOrderCompletion:               taskQRIncidentOrderCompletion,
		TaskReassignDelayedTrip:                     taskReassignDelayedTrip,
		TaskServiceOptInReminder:                    taskServiceOptInReminder,
		TaskThrottleJobController:                   taskThrottleJobController,
		TaskUnacknowledgeReassign:                   taskUnacknowledgeReassign,
		TaskUpdateDriverDedicatedZones:              taskUpdateDriverDedicatedZones,
		TaskUpdateDriverServices:                    taskUpdateDriverServices,
		Registry:                                    providerRegistry,
		ServiceSelectorAPI:                          serviceSelector,
		MetricsRegistry:                             metricsRegistryImpl,
		PrometheusMeter:                             prometheusMeter,
		ServiceGRPCConnFactory:                      connectionFactory,
		StubPolygonApiForTest:                       stubPolygonApi,
		TopkekForTest:                               topkekForTest,
		DriverRegistrationTestData:                  driverRegistrationTestData,
		Fixtures:                                    fixtures,
		OTPSessionTestData:                          otpSessionTestData,
		ProvincesTestData:                           provincesTestData,
		RatingRestaurantTestData:                    ratingRestaurantTestData,
		WhitelistPhonesTestData:                     whitelistPhonesTestData,
		TemplateService:                             template,
	}
	integrationTestContainer := &IntegrationTestContainer{
		init:          containerInitializer,
		Locator:       locator,
		SimpleUnleash: simpleUnleasher,
	}
	return integrationTestContainer, func() {
		cleanup35()
		cleanup34()
		cleanup33()
		cleanup32()
		cleanup31()
		cleanup30()
		cleanup29()
		cleanup28()
		cleanup27()
		cleanup26()
		cleanup25()
		cleanup24()
		cleanup23()
		cleanup22()
		cleanup21()
		cleanup20()
		cleanup19()
		cleanup18()
		cleanup17()
		cleanup16()
		cleanup15()
		cleanup14()
		cleanup13()
		cleanup12()
		cleanup11()
		cleanup10()
		cleanup9()
		cleanup8()
		cleanup7()
		cleanup6()
		cleanup5()
		cleanup4()
		cleanup3()
		cleanup2()
	}, nil
}
