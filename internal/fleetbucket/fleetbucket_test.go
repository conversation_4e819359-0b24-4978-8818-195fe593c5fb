package fleetbucket

import (
	"context"
	"io"
	"os"

	gomock "github.com/golang/mock/gomock"
	"github.com/onsi/ginkgo"
	. "github.com/onsi/gomega"
)

var _ = ginkgo.Describe("DownloadDriverServiceAccountFile", func() {
	ginkgo.It("write downloaded file to given path", func() {
		defer os.RemoveAll("./testdata")

		os.MkdirAll("./testdata", 0755)
		path := "./testdata/driver-service-account.json"

		downloader, finish := setup(ginkgo.GinkgoT())
		defer finish()

		downloader.
			EXPECT().
			Download(gomock.Any(), gomock.Any(), gomock.Any()).
			DoAndReturn(func(ctx context.Context, w io.WriterAt, path string) error {
				w.WriteAt([]byte("{}"), 0)
				return nil
			})

		DownloadDriverServiceAccountFile(path, downloader)
		Expect(path).To(BeAnExistingFile())
	})
})

func setup(t ginkgo.GinkgoTInterface) (*MockDownloader, func()) {
	ctrl := gomock.NewController(ginkgo.GinkgoT())
	return NewMockDownloader(ctrl), func() { ctrl.Finish() }
}
