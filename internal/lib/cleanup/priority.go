package cleanup

import (
	"sort"
)

//go:generate mockgen -source=./priority.go -destination=./mock_cleanup/mock_priority.go -package=mock_cleanup

const (
	CleanUpFirstPriority = 1
)

type CleanupPriority interface {
	Add(priority int, fn func())
	Cleanup()
}

var _ CleanupPriority = (*CleanupPriorityImpl)(nil)

type CleanupPriorityImpl struct {
	cleanups map[int][]func()
}

func (cp *CleanupPriorityImpl) Add(priority int, fn func()) {
	cp.cleanups[priority] = append(cp.cleanups[priority], fn)
}

func (cp *CleanupPriorityImpl) Cleanup() {
	if len(cp.cleanups) == 0 {
		return
	}

	priorities := []int{}
	for p := range cp.cleanups {
		priorities = append(priorities, p)
	}
	sort.Sort(sort.Reverse(sort.IntSlice(priorities)))

	for _, p := range priorities {
		for _, cleanup := range cp.cleanups[p] {
			cleanup()
		}
	}
}

func ProvideCleanupPriority() CleanupPriority {
	return &CleanupPriorityImpl{
		cleanups: make(map[int][]func()),
	}
}
