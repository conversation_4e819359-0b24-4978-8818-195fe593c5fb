package riderapp

import (
	"fmt"
	"net/http"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestInfo_ClientVersion(t *testing.T) {
	tests := []struct {
		userAgent string
		major     int
		minor     int
		patch     int
	}{
		{"LINEMANRIDER/1.13.2-beta.1000 (Android; Realme RMX2040; 10)", 1, 13, 2},
		{"LINEMANRIDER/1.13.2  (Android; Realme RMX2040; 10)", 1, 13, 2},
		{"LINEMANRIDER/1.13.2-prod (Android; Xiaomi 3.12.4)", 1, 13, 2},
		{"Android LINEMANRIDER/1.13.2-prod (Xiaomi 3.12.4)", 1, 13, 2},
		{"Android Unknown", 0, 0, 0},
	}
	for _, test := range tests {
		name := fmt.Sprintf("%s -> %d.%d.%d", test.userAgent, test.major, test.minor, test.patch)
		t.Run(name, func(t *testing.T) {
			info := infoFromUserAgent(test.userAgent)
			assert.Equal(t, test.major, info.ClientVersionMajor())
			assert.Equal(t, test.minor, info.ClientVersionMinor())
			assert.Equal(t, test.patch, info.ClientVersionPatch())
			assert.Equal(t, fmt.Sprintf("%d.%d.%d", test.major, test.minor, test.patch), info.ClientVersion())
		})
	}
}

func TestInfo_ClientVersionNewer(t *testing.T) {
	tests := []struct {
		userAgent string
		major     int
		minor     int
		patch     int
		satisfy   bool
	}{
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 1, 13, 2, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 1, 13, 1, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 1, 0, 0, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 1, 1, 0, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 1, 12, 0, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 1, 12, 999, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 0, 12, 0, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 0, 999, 0, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 0, 999, 999, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 0, 1, 999, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 0, 13, 999, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 0, 14, 999, true},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 0, 12, 999, true},

		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 1, 13, 3, false},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 2, 0, 0, false},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 1, 14, 0, false},
		{"LINEMANRIDER/1.13.2 (Android; Realme RMX2040; 10)", 2, 13, 0, false},
	}
	for _, test := range tests {
		t.Run(fmt.Sprintf("%s satisfy=%v", test.userAgent, test.satisfy), func(t *testing.T) {
			info := infoFromUserAgent(test.userAgent)
			satisfy := info.ClientVersionSatisfy(test.major, test.minor, test.patch)
			assert.Equal(t, test.satisfy, satisfy, fmt.Sprintf("expect %d.%d.%d to satisfy=%v with %s", test.major, test.minor, test.patch, test.satisfy, test.userAgent))
		})
	}
}

func infoFromUserAgent(ua string) *Info {
	header := http.Header{}
	header.Set("User-Agent", ua)
	return NewInfo(header)
}
