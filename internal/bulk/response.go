package bulk

import "sync"

type Result struct {
	l         sync.Mutex
	Successes []string  `json:"successes"`
	Failures  []Failure `json:"failures"`
}

type Failure struct {
	ID     string `json:"id"`
	Reason string `json:"reason"`
}

func (r *Result) AddSuccess(id string) {
	r.l.Lock()
	r.Success<PERSON> = append(r.<PERSON>, id)
	r.l.Unlock()
}

func (r *Result) AddManySuccess(ids []string) {
	r.l.Lock()
	r.Successes = append(r.Successes, ids...)
	r.l.Unlock()
}

func (r *Result) SuccessLength() int {
	return len(r.Successes)
}

func (r *Result) AddFailure(id string, reason string) {
	r.l.Lock()
	r.Failures = append(r.Failures, Failure{
		ID:     id,
		Reason: reason,
	})
	r.l.Unlock()
}

func (r *Result) AddManyFailure(ids []string, reason string) {
	r.l.Lock()
	for _, id := range ids {
		r.Failures = append(r.Failures, Failure{
			ID:     id,
			Reason: reason,
		})
	}
	r.l.Unlock()
}

func (r *Result) mergeFailures(failures []Failure) {
	r.l.Lock()
	r.Failures = append(r.Failures, failures...)
	r.l.Unlock()
}

func (r *Result) FailureLength() int {
	return len(r.Failures)
}

func (r *Result) GetFailureKeys() []string {
	keys := make([]string, len(r.Failures))
	for i, f := range r.Failures {
		keys[i] = f.ID
	}
	return keys
}

func (r *Result) IsSuccess() bool {
	return len(r.Failures) == 0
}
