// Code generated by MockGen. DO NOT EDIT.
// Source: git.wndv.co/lineman/fleet-distribution/internal/bulk (interfaces: SingleCollectionOperationDeleter)

// Package mock_bulk is a generated GoMock package.
package mock_bulk

import (
	context "context"
	io "io"
	reflect "reflect"
	time "time"

	v2 "git.wndv.co/lineman/absinthe/database/v2"
	gomock "github.com/golang/mock/gomock"
)

// MockSingleCollectionOperationDeleter is a mock of SingleCollectionOperationDeleter interface.
type MockSingleCollectionOperationDeleter struct {
	ctrl     *gomock.Controller
	recorder *MockSingleCollectionOperationDeleterMockRecorder
}

// MockSingleCollectionOperationDeleterMockRecorder is the mock recorder for MockSingleCollectionOperationDeleter.
type MockSingleCollectionOperationDeleterMockRecorder struct {
	mock *MockSingleCollectionOperationDeleter
}

// NewMockSingleCollectionOperationDeleter creates a new mock instance.
func NewMockSingleCollectionOperationDeleter(ctrl *gomock.Controller) *MockSingleCollectionOperationDeleter {
	mock := &MockSingleCollectionOperationDeleter{ctrl: ctrl}
	mock.recorder = &MockSingleCollectionOperationDeleterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSingleCollectionOperationDeleter) EXPECT() *MockSingleCollectionOperationDeleterMockRecorder {
	return m.recorder
}

// ConvertData mocks base method.
func (m *MockSingleCollectionOperationDeleter) ConvertData(arg0 context.Context, arg1 io.Reader) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ConvertData", arg0, arg1)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ConvertData indicates an expected call of ConvertData.
func (mr *MockSingleCollectionOperationDeleterMockRecorder) ConvertData(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ConvertData", reflect.TypeOf((*MockSingleCollectionOperationDeleter)(nil).ConvertData), arg0, arg1)
}

// DataStore mocks base method.
func (m *MockSingleCollectionOperationDeleter) DataStore() v2.DataStoreInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DataStore")
	ret0, _ := ret[0].(v2.DataStoreInterface)
	return ret0
}

// DataStore indicates an expected call of DataStore.
func (mr *MockSingleCollectionOperationDeleterMockRecorder) DataStore() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DataStore", reflect.TypeOf((*MockSingleCollectionOperationDeleter)(nil).DataStore))
}

// GetBatchDelay mocks base method.
func (m *MockSingleCollectionOperationDeleter) GetBatchDelay() time.Duration {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchDelay")
	ret0, _ := ret[0].(time.Duration)
	return ret0
}

// GetBatchDelay indicates an expected call of GetBatchDelay.
func (mr *MockSingleCollectionOperationDeleterMockRecorder) GetBatchDelay() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchDelay", reflect.TypeOf((*MockSingleCollectionOperationDeleter)(nil).GetBatchDelay))
}

// GetBatchSize mocks base method.
func (m *MockSingleCollectionOperationDeleter) GetBatchSize() int {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBatchSize")
	ret0, _ := ret[0].(int)
	return ret0
}

// GetBatchSize indicates an expected call of GetBatchSize.
func (mr *MockSingleCollectionOperationDeleterMockRecorder) GetBatchSize() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBatchSize", reflect.TypeOf((*MockSingleCollectionOperationDeleter)(nil).GetBatchSize))
}

// Type mocks base method.
func (m *MockSingleCollectionOperationDeleter) Type() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Type")
	ret0, _ := ret[0].(string)
	return ret0
}

// Type indicates an expected call of Type.
func (mr *MockSingleCollectionOperationDeleterMockRecorder) Type() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Type", reflect.TypeOf((*MockSingleCollectionOperationDeleter)(nil).Type))
}
