// Code generated by MockGen. DO NOT EDIT.
// Source: git.wndv.co/lineman/fleet-distribution/internal/bulk (interfaces: DataStoreWithValidateCreate)

// Package mock_bulk is a generated GoMock package.
package mock_bulk

import (
	context "context"
	reflect "reflect"

	v2 "git.wndv.co/lineman/absinthe/database/v2"
	bulk "git.wndv.co/lineman/fleet-distribution/internal/bulk"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockDataStoreWithValidateCreate is a mock of DataStoreWithValidateCreate interface.
type MockDataStoreWithValidateCreate struct {
	ctrl     *gomock.Controller
	recorder *MockDataStoreWithValidateCreateMockRecorder
}

// MockDataStoreWithValidateCreateMockRecorder is the mock recorder for MockDataStoreWithValidateCreate.
type MockDataStoreWithValidateCreateMockRecorder struct {
	mock *MockDataStoreWithValidateCreate
}

// NewMockDataStoreWithValidateCreate creates a new mock instance.
func NewMockDataStoreWithValidateCreate(ctrl *gomock.Controller) *MockDataStoreWithValidateCreate {
	mock := &MockDataStoreWithValidateCreate{ctrl: ctrl}
	mock.recorder = &MockDataStoreWithValidateCreateMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataStoreWithValidateCreate) EXPECT() *MockDataStoreWithValidateCreateMockRecorder {
	return m.recorder
}

// DataStore mocks base method.
func (m *MockDataStoreWithValidateCreate) DataStore() v2.DataStoreInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DataStore")
	ret0, _ := ret[0].(v2.DataStoreInterface)
	return ret0
}

// DataStore indicates an expected call of DataStore.
func (mr *MockDataStoreWithValidateCreateMockRecorder) DataStore() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DataStore", reflect.TypeOf((*MockDataStoreWithValidateCreate)(nil).DataStore))
}

// Key mocks base method.
func (m *MockDataStoreWithValidateCreate) Key() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Key")
	ret0, _ := ret[0].(string)
	return ret0
}

// Key indicates an expected call of Key.
func (mr *MockDataStoreWithValidateCreateMockRecorder) Key() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Key", reflect.TypeOf((*MockDataStoreWithValidateCreate)(nil).Key))
}

// Validate mocks base method.
func (m *MockDataStoreWithValidateCreate) Validate(arg0 context.Context, arg1 map[string]map[string]interface{}) (*bulk.Result, []model.AuditLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate", arg0, arg1)
	ret0, _ := ret[0].(*bulk.Result)
	ret1, _ := ret[1].([]model.AuditLog)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Validate indicates an expected call of Validate.
func (mr *MockDataStoreWithValidateCreateMockRecorder) Validate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockDataStoreWithValidateCreate)(nil).Validate), arg0, arg1)
}

// ValidateCreate mocks base method.
func (m *MockDataStoreWithValidateCreate) ValidateCreate(arg0 context.Context, arg1 map[string]map[string]interface{}) (*bulk.Result, []model.AuditLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateCreate", arg0, arg1)
	ret0, _ := ret[0].(*bulk.Result)
	ret1, _ := ret[1].([]model.AuditLog)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ValidateCreate indicates an expected call of ValidateCreate.
func (mr *MockDataStoreWithValidateCreateMockRecorder) ValidateCreate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateCreate", reflect.TypeOf((*MockDataStoreWithValidateCreate)(nil).ValidateCreate), arg0, arg1)
}
