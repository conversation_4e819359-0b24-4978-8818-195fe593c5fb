// Code generated by MockGen. DO NOT EDIT.
// Source: git.wndv.co/lineman/fleet-distribution/internal/bulk (interfaces: DataStore)

// Package mock_bulk is a generated GoMock package.
package mock_bulk

import (
	context "context"
	reflect "reflect"

	v2 "git.wndv.co/lineman/absinthe/database/v2"
	bulk "git.wndv.co/lineman/fleet-distribution/internal/bulk"
	model "git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	gomock "github.com/golang/mock/gomock"
)

// MockDataStore is a mock of DataStore interface.
type MockDataStore struct {
	ctrl     *gomock.Controller
	recorder *MockDataStoreMockRecorder
}

// MockDataStoreMockRecorder is the mock recorder for MockDataStore.
type MockDataStoreMockRecorder struct {
	mock *MockDataStore
}

// NewMockDataStore creates a new mock instance.
func NewMockDataStore(ctrl *gomock.Controller) *MockDataStore {
	mock := &MockDataStore{ctrl: ctrl}
	mock.recorder = &MockDataStoreMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockDataStore) EXPECT() *MockDataStoreMockRecorder {
	return m.recorder
}

// DataStore mocks base method.
func (m *MockDataStore) DataStore() v2.DataStoreInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DataStore")
	ret0, _ := ret[0].(v2.DataStoreInterface)
	return ret0
}

// DataStore indicates an expected call of DataStore.
func (mr *MockDataStoreMockRecorder) DataStore() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DataStore", reflect.TypeOf((*MockDataStore)(nil).DataStore))
}

// Key mocks base method.
func (m *MockDataStore) Key() string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Key")
	ret0, _ := ret[0].(string)
	return ret0
}

// Key indicates an expected call of Key.
func (mr *MockDataStoreMockRecorder) Key() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Key", reflect.TypeOf((*MockDataStore)(nil).Key))
}

// Validate mocks base method.
func (m *MockDataStore) Validate(arg0 context.Context, arg1 map[string]map[string]interface{}) (*bulk.Result, []model.AuditLog, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Validate", arg0, arg1)
	ret0, _ := ret[0].(*bulk.Result)
	ret1, _ := ret[1].([]model.AuditLog)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// Validate indicates an expected call of Validate.
func (mr *MockDataStoreMockRecorder) Validate(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Validate", reflect.TypeOf((*MockDataStore)(nil).Validate), arg0, arg1)
}
