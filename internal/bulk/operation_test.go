package bulk_test

import (
	"context"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/pkg/errors"
	"github.com/stretchr/testify/assert"

	"git.wndv.co/lineman/absinthe/database/v2/mock_mongodb"
	"git.wndv.co/lineman/fleet-distribution/internal/bulk"
	bulk_config "git.wndv.co/lineman/fleet-distribution/internal/bulk/bulkconfig"
	mock_bulk "git.wndv.co/lineman/fleet-distribution/internal/bulk/mock"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/model"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository"
	"git.wndv.co/lineman/fleet-distribution/internal/domain/repository/mock_repository"
	"git.wndv.co/lineman/fleet-distribution/internal/testutil"
)

func setUpOperation(ds bulk.DataStore, auditLogRepository repository.AuditLogRepository, overrideCfg *bulk_config.GeneralConfig) bulk.OperationService {
	cfg := bulk_config.GeneralConfig{
		BatchSize: 100,
	}

	if overrideCfg != nil {
		cfg = *overrideCfg
	}

	config := bulk.GetBulkConfigForTest(cfg, ds)

	return bulk.ProvideOperationService(config, auditLogRepository)
}

func TestOperation_Operate(t *testing.T) {
	t.Parallel()
	t.Run("should return success result", func(t *testing.T) {
		ctrl := gomock.NewController(t)
		defer ctrl.Finish()

		type Operation int
		const (
			UPDATE = Operation(iota)
			CREATE = Operation(iota)
			DELETE = Operation(iota)
		)
		testcases := []struct {
			Operation Operation
			Cases     []map[string]map[string]interface{}
		}{
			{Operation: CREATE, Cases: []map[string]map[string]interface{}{
				{
					"1": {
						"key": "1",
					},
					"2": {
						"key": "2",
					},
				},
				{
					"1": {
						"key": "1",
					},
					"2": {
						"key": "2",
					},
					"3": {
						"key": "3",
					},
				},
			}},
			{Operation: UPDATE, Cases: []map[string]map[string]interface{}{
				{
					"1": {
						"data": "1",
					},
					"2": {
						"data": "2",
					},
					"3": {
						"data": "3",
					},
				},
			}},
			{Operation: DELETE, Cases: []map[string]map[string]interface{}{
				{
					"1": nil,
					"2": nil,
					"3": nil,
				},
			}},
		}
		for _, testcase := range testcases {
			for _, data := range testcase.Cases {
				// Arrange
				ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
				bulkDs := mock_bulk.NewMockDataStore(ctrl)
				var ops bulk.SingleCollectionOperation

				switch testcase.Operation {
				case CREATE:
					ops = mock_bulk.NewMockSingleCollectionOperationCreator(ctrl)
					ops.(*mock_bulk.MockSingleCollectionOperationCreator).EXPECT().GetBatchSize().Return(100).AnyTimes()
					ops.(*mock_bulk.MockSingleCollectionOperationCreator).EXPECT().GetBatchDelay().Return(time.Millisecond).AnyTimes()
					ops.(*mock_bulk.MockSingleCollectionOperationCreator).EXPECT().Type().Return("TYPE").AnyTimes()
				case UPDATE:
					ops = mock_bulk.NewMockSingleCollectionOperationUpdater(ctrl)
					ops.(*mock_bulk.MockSingleCollectionOperationUpdater).EXPECT().
						Updater(gomock.Any(), gomock.Any()).
						Return(map[string]interface{}{"$set": map[string]interface{}{
							"data": "1",
						}}).
						AnyTimes()
					ops.(*mock_bulk.MockSingleCollectionOperationUpdater).EXPECT().GetBatchSize().Return(100).AnyTimes()
					ops.(*mock_bulk.MockSingleCollectionOperationUpdater).EXPECT().GetBatchDelay().Return(time.Millisecond).AnyTimes()
					ops.(*mock_bulk.MockSingleCollectionOperationUpdater).EXPECT().Type().Return("TYPE").AnyTimes()
				case DELETE:
					ops = mock_bulk.NewMockSingleCollectionOperationDeleter(ctrl)
					ops.(*mock_bulk.MockSingleCollectionOperationDeleter).EXPECT().GetBatchSize().Return(100).AnyTimes()
					ops.(*mock_bulk.MockSingleCollectionOperationDeleter).EXPECT().GetBatchDelay().Return(time.Millisecond).AnyTimes()
					ops.(*mock_bulk.MockSingleCollectionOperationDeleter).EXPECT().Type().Return("TYPE").AnyTimes()
				}

				ds.EXPECT().
					BulkWrite(gomock.Any(), gomock.Any(), gomock.Any()).
					Return(nil, nil)
				bulkDs.EXPECT().
					Key().Return("key").AnyTimes()
				bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()

				operationService := setUpOperation(bulkDs, nil, nil)
				// Act
				result := operationService.Operate(context.Background(), ds, data, ops, nil)
				// Assert
				assert.Equal(t, len(data), len(result.Successes))
				assert.Equal(t, 0, len(result.Failures))
			}
		}
	})

	t.Run("should return failure result", func(t *testing.T) {
		// Arrange
		data := map[string]map[string]interface{}{
			"1": {
				"key": "1",
			},
			"2": {
				"key": "2",
			},
		}
		ctrl := gomock.NewController(t)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		ops := mock_bulk.NewMockSingleCollectionOperationCreator(ctrl)
		ds.EXPECT().
			BulkWrite(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, errors.New("error"))
		bulkDs.EXPECT().
			Key().Return("key").AnyTimes()
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		ops.EXPECT().GetBatchSize().Return(100).AnyTimes()
		ops.EXPECT().GetBatchDelay().Return(time.Millisecond).AnyTimes()
		ops.EXPECT().Type().Return("TYPE").AnyTimes()
		operationService := setUpOperation(bulkDs, nil, nil)
		// Act
		result := operationService.Operate(context.Background(), ds, data, ops, nil)
		// Assert
		assert.Equal(t, 0, len(result.Successes))
		assert.Equal(t, len(data), len(result.Failures))
	})

	t.Run("should add audit logs if any", func(t *testing.T) {
		// Arrange
		data := map[string]map[string]interface{}{
			"1": {
				"key": "1",
			},
			"2": {
				"key": "2",
			},
		}
		ctrl := gomock.NewController(t)
		auditLogRepositorySpy := mock_repository.NewMockAuditLogRepository(ctrl)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		ops := mock_bulk.NewMockSingleCollectionOperationCreator(ctrl)
		ds.EXPECT().
			BulkWrite(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)
		bulkDs.EXPECT().
			Key().Return("key").AnyTimes()
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		ops.EXPECT().GetBatchSize().Return(0).AnyTimes()
		ops.EXPECT().GetBatchDelay().Return(time.Millisecond).AnyTimes()
		ops.EXPECT().Type().Return("TYPE").AnyTimes()
		operationService := setUpOperation(bulkDs, auditLogRepositorySpy, nil)

		auditLogs := []model.AuditLog{
			{Object: model.AuditObject{ID: "1"}, Actor: struct{ ID string }{ID: "tester"}, Timestamp: time.Now(), Event: "test", Action: "test", Before: nil, After: data},
			{Object: model.AuditObject{ID: "2"}, Actor: struct{ ID string }{ID: "tester"}, Timestamp: time.Now(), Event: "test", Action: "test", Before: nil, After: data},
		}

		auditLogRepositorySpy.EXPECT().InsertMany(gomock.Any(), gomock.Any()).Return(nil).Times(1)
		// Act
		result := operationService.Operate(context.Background(), ds, data, ops, auditLogs)
		// Assert
		assert.Equal(t, len(data), len(result.Successes))
		assert.Equal(t, 0, len(result.Failures))
	})

	t.Run("should add error if audit logs insert fail", func(t *testing.T) {
		// Arrange
		data := map[string]map[string]interface{}{
			"1": {
				"key": "1",
			},
			"2": {
				"key": "2",
			},
		}
		ctrl := gomock.NewController(t)
		auditLogRepositorySpy := mock_repository.NewMockAuditLogRepository(ctrl)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		ops := mock_bulk.NewMockSingleCollectionOperationCreator(ctrl)
		ds.EXPECT().
			BulkWrite(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil)
		bulkDs.EXPECT().
			Key().Return("key").AnyTimes()
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		ops.EXPECT().GetBatchSize().Return(0).AnyTimes()
		ops.EXPECT().GetBatchDelay().Return(time.Millisecond).AnyTimes()
		ops.EXPECT().Type().Return("TYPE").AnyTimes()
		operationService := setUpOperation(bulkDs, auditLogRepositorySpy, nil)

		auditLogs := []model.AuditLog{
			{Object: model.AuditObject{ID: "1"}, Actor: struct{ ID string }{ID: "tester"}, Timestamp: time.Now(), Event: "test", Action: "test", Before: nil, After: data},
			{Object: model.AuditObject{ID: "2"}, Actor: struct{ ID string }{ID: "tester"}, Timestamp: time.Now(), Event: "test", Action: "test", Before: nil, After: data},
		}

		auditLogRepositorySpy.EXPECT().InsertMany(gomock.Any(), gomock.Any()).Return(errors.New("Add logs failed")).Times(1)
		// Act
		result := operationService.Operate(context.Background(), ds, data, ops, auditLogs)
		// Assert
		assert.Equal(t, len(data), len(result.Successes))
		assert.Equal(t, len(data), len(result.Failures))
	})

	t.Run("should apply delay between batch", func(t *testing.T) {
		// Arrange
		data := map[string]map[string]interface{}{
			"1": {
				"key": "1",
			},
			"2": {
				"key": "2",
			},
		}
		ctrl := gomock.NewController(t)
		auditLogRepositorySpy := mock_repository.NewMockAuditLogRepository(ctrl)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		ops := mock_bulk.NewMockSingleCollectionOperationCreator(ctrl)
		ds.EXPECT().
			BulkWrite(gomock.Any(), gomock.Any(), gomock.Any()).
			Return(nil, nil).Times(2)
		bulkDs.EXPECT().
			Key().Return("key").AnyTimes()
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		ops.EXPECT().GetBatchSize().Return(1).AnyTimes()
		ops.EXPECT().GetBatchDelay().Return(500 * time.Millisecond).AnyTimes()
		ops.EXPECT().Type().Return("TYPE").AnyTimes()
		operationService := setUpOperation(bulkDs, auditLogRepositorySpy, nil)

		auditLogs := []model.AuditLog{
			{Object: model.AuditObject{ID: "1"}, Actor: struct{ ID string }{ID: "tester"}, Timestamp: time.Now(), Event: "test", Action: "test", Before: nil, After: data},
			{Object: model.AuditObject{ID: "2"}, Actor: struct{ ID string }{ID: "tester"}, Timestamp: time.Now(), Event: "test", Action: "test", Before: nil, After: data},
		}

		auditLogRepositorySpy.EXPECT().InsertMany(gomock.Any(), gomock.Any()).Return(nil).Times(2)
		// Act
		start := time.Now()
		result := operationService.Operate(context.Background(), ds, data, ops, auditLogs)
		assert.True(t, time.Since(start) >= (1*time.Second))
		assert.True(t, time.Since(start) < (2*time.Second))
		// Assert
		assert.Equal(t, len(data), len(result.Successes))
		assert.Equal(t, 0, len(result.Failures))
	})
}

func TestOperation_ExportOperate(t *testing.T) {
	t.Parallel()
	t.Run("should return csv data", func(t *testing.T) {
		// Arrange
		ctrl := gomock.NewController(t)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		data := []map[string]string{
			{
				"id":   "1",
				"data": "d1",
			},
			{
				"id":   "2",
				"data": "d2",
			},
			{
				"id":   "3",
				"data": "d3",
			},
		}

		operationService := setUpOperation(bulkDs, nil, nil)
		// Act
		csvData, err := operationService.ExportOperate([]string{"id", "data"}, data)
		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, csvData)
		assert.Equal(t, "id,data\n1,d1\n2,d2\n3,d3\n", string(*csvData))
	})

}

func TestOperation_ValidateFromDS(t *testing.T) {
	t.Parallel()
	t.Run("should return data, result and audit logs", func(t *testing.T) {
		// Arrange
		ctrl := gomock.NewController(t)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		data := map[string]map[string]interface{}{
			"1": {
				"key": "1",
			},
			"2": {
				"key": "2",
			},
			"3": {
				"key": "3",
			},
		}
		bulkDs.EXPECT().Validate(gomock.Any(), gomock.Any()).Return(&bulk.Result{
			Successes: []string{"1", "2"},
			Failures: []bulk.Failure{
				{
					ID:     "3",
					Reason: "Error",
				},
			}}, nil, nil)
		operationService := setUpOperation(bulkDs, nil, nil)
		// Act
		newData, result, auditLogsResult, err := operationService.ValidateFromDS(context.Background(), ds, data)
		// Assert
		ExpectedNewData := map[string]map[string]interface{}{
			"1": {
				"key": "1",
			},
			"2": {
				"key": "2",
			},
		}
		assert.NoError(t, err)
		assert.NotNil(t, newData)
		assert.NotNil(t, result)
		assert.Nil(t, auditLogsResult)
		assert.Equal(t, ExpectedNewData, newData)
		assert.Equal(t, 2, len(result.Successes))
		assert.Equal(t, 1, len(result.Failures))
	})
}

func TestOperation_ValidateCreationFromDS(t *testing.T) {
	t.Run("should return data, result and audit logs", func(t *testing.T) {
		// Arrange
		ctrl := gomock.NewController(t)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStoreWithValidateCreate(ctrl)
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		data := map[string]map[string]interface{}{
			"1": {
				"key": "1",
			},
			"2": {
				"key": "2",
			},
			"3": {
				"key": "3",
			},
		}
		bulkDs.EXPECT().ValidateCreate(gomock.Any(), gomock.Any()).Return(&bulk.Result{
			Successes: []string{"1", "2"},
			Failures: []bulk.Failure{
				{
					ID:     "3",
					Reason: "Error",
				},
			}}, nil, nil)
		operationService := setUpOperation(bulkDs, nil, nil)
		// Act
		newData, result, auditLogsResult, err := operationService.ValidateCreationFromDS(context.Background(), ds, data)
		// Assert
		ExpectedNewData := map[string]map[string]interface{}{
			"1": {
				"key": "1",
			},
			"2": {
				"key": "2",
			},
		}
		assert.NoError(t, err)
		assert.NotNil(t, newData)
		assert.NotNil(t, result)
		assert.Nil(t, auditLogsResult)
		assert.Equal(t, ExpectedNewData, newData)
		assert.Equal(t, 2, len(result.Successes))
		assert.Equal(t, 1, len(result.Failures))
	})
}

func TestOperation_ValidateDeletionFromDS(t *testing.T) {
	t.Run("should return data, result and audit logs", func(t *testing.T) {
		// Arrange
		ctrl := gomock.NewController(t)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStoreWithValidateDelete(ctrl)
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		data := []string{"1", "2", "3"}
		bulkDs.EXPECT().ValidateDelete(gomock.Any(), gomock.Any()).Return(&bulk.Result{
			Successes: []string{"1", "2"},
			Failures: []bulk.Failure{
				{
					ID:     "3",
					Reason: "Error",
				},
			}}, nil, nil)
		operationService := setUpOperation(bulkDs, nil, nil)
		// Act
		newData, result, auditLogsResult, err := operationService.ValidateDeletionFromDS(context.Background(), ds, data)
		// Assert
		assert.NoError(t, err)
		assert.NotNil(t, newData)
		assert.NotNil(t, result)
		assert.Nil(t, auditLogsResult)
		assert.Equal(t, 2, len(result.Successes))
		assert.Equal(t, 1, len(result.Failures))
	})
}

func TestOperation_WriteToCSV(t *testing.T) {
	t.Parallel()
	t.Run("should write csv data", func(t *testing.T) {
		// Arrange
		ctx := testutil.NewContextWithRecorder()
		ctrl := gomock.NewController(t)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		data := "id,data\n1,d1\n2,d2\n3,d3\n"
		dataByte := []byte(data)
		operationService := setUpOperation(bulkDs, nil, nil)
		// Act
		err := operationService.WriteToCSV(ctx.GinCtx(), "test.csv", &dataByte)
		// Assert
		assert.NoError(t, err)
		assert.Equal(t, "id,data\n1,d1\n2,d2\n3,d3\n", ctx.ResponseRecorder.Body.String())
		assert.Equal(t, "text/csv", ctx.ResponseRecorder.Header().Get("Content-Type"))
		assert.Equal(t, "attachment;filename=test.csv", ctx.ResponseRecorder.Header().Get("Content-Disposition"))
	})

	t.Run("should return error if data is nil", func(t *testing.T) {
		// Arrange
		ctx := testutil.NewContextWithRecorder()
		ctrl := gomock.NewController(t)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		var dataByte *[]byte
		operationService := setUpOperation(bulkDs, nil, nil)
		// Act
		err := operationService.WriteToCSV(ctx.GinCtx(), "test.csv", dataByte)
		// Assert
		assert.Error(t, err)
		assert.Equal(t, "Data is nil", err.Error())
	})

	t.Run("shoukd return default filename", func(t *testing.T) {
		// Arrange
		ctx := testutil.NewContextWithRecorder()
		ctrl := gomock.NewController(t)
		ds := mock_mongodb.NewMockDataStoreInterface(ctrl)
		bulkDs := mock_bulk.NewMockDataStore(ctrl)
		bulkDs.EXPECT().DataStore().Return(ds).AnyTimes()
		data := "id,data\n1,d1\n2,d2\n3,d3\n"
		dataByte := []byte(data)
		operationService := setUpOperation(bulkDs, nil, nil)
		// Act
		err := operationService.WriteToCSV(ctx.GinCtx(), "", &dataByte)
		// Assert
		assert.NoError(t, err)
		assert.Equal(t, "id,data\n1,d1\n2,d2\n3,d3\n", ctx.ResponseRecorder.Body.String())
		assert.Equal(t, "text/csv", ctx.ResponseRecorder.Header().Get("Content-Type"))
		assert.Equal(t, "attachment;filename=export.csv", ctx.ResponseRecorder.Header().Get("Content-Disposition"))
	})
}
