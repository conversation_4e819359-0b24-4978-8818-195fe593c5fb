# This file is managed by makew
# DO NOT EDIT! IT WILL BE REPLACED
# The canonical copy is at https://git.wndv.co/go/makew/-/blob/master/plugin/golangci/golangci.yml

linters:
  enable:
    - bodyclose
    - errorlint
    - copyloopvar
    - gci
    - gocritic
    - gofmt
    - gosec
    - makezero
    - nilerr
    - noctx
    - sqlclosecheck
    - stylecheck
    - unconvert
    - spancheck
    - gomodguard
#    - houselint
  disable:
    - errcheck
linters-settings:
  gci:
    sections:
      - standard
      - default
      - prefix(git.linecorp.com)
      - prefix(git.wndv.co)
  gomodguard:
    blocked:
      versions:
        - google.golang.org/grpc:
            version: ">1.58.3, <1.68.0"
            reason: "gRPC may trigger a bug in xDS server: https://github.com/wongnai/xds/issues/11 . When using gRPC newer than 1.58.3, grpclib 1.18.0 must be used for a workaround"
        - go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc:
            version: "<0.46.0"
            reason: "otelgrpc <0.46.0 provide high cardinality metrics: https://github.com/open-telemetry/opentelemetry-go-contrib/security/advisories/GHSA-8pgv-569h-w5rw"
  custom:
    houselint:
      type: module
      description: LMWN in house linter
      original-url: git.wndv.co/go/analysis
run:
  timeout: 10m
  build-tags:
    - test
