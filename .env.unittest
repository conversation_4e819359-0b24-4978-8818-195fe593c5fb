DB_HOST=localhost
DB_NAME=driver_test
DB_USERNAME=lineman
DB_PASSWORD=lineman

REDIS_HOST=localhost:7000,localhost:7001,localhost:7002
REDIS_PASSWORD=

LINE_API_URL=https://api.line-beta.me/v2/
LINE_CHANNEL_ACCESS_TOKEN=mock-line-channel-access-token

ENCRYPTED_KEY=sakunakosakunako
ENABLE_ENCRYPT=true

PASETO_SIGNED_KEY = "--THIS-MUST-BE-32-BYTES-STRING--"
PASETO_EXPIRED_MINUTES = 60

WHITELIST_PHOTO_URL = https://test1/,https://test2/

DRIVER_REFID_PREFIX = "1234"

TRANSACTION_FEE=5

PAYMENT_CONSOLIDATE_WITHDRAW_WALLET_EMAIL_TOS="<EMAIL>,<EMAIL>"
PAYMENT_CONSOLIDATE_WITHDRAW_WALLET_EMAIL_FROM="<EMAIL>"
PAYMENT_CONSOLIDATE_WITHDRAW_WALLET_EMAIL_PASSWORD="password_email"

EMAIL_SERVER="smtp.linecorp.com:587"

LALAMOVE_PROVINCE="กรุงเทพมหานคร,สมุทรสาคร,สมุทรปราการ"

DRIVER_LOCATION_REPOSITORY_REDIS_HOSTS=localhost:7000,localhost:7001,localhost:7002
DRIVER_LOCATION_REPOSITORY_REDIS_PASSWORD=

CGO_ENABLED=1

ADMIN_MAX_EXPORTED_CRIMINAL_CHECK_STATUS_ROWS=40000
FLEET_VOS_BUCKET=lm-fleet

PROFILING_ENABLED=false
