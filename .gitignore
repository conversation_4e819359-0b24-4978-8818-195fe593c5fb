# Binaries for programs and plugins
*.exe
*.dll
*.so
*.dylib
tmp/
.DS_Store
lineman-driver
bin

# Test binary, build with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
*.coverprofile
coverage.xml

# Project-local glide cache, RE: https://github.com/Masterminds/glide/issues/736
.glide/

# Library
vendor

# Environment
.env*
!.env.example
!.env.unittest
!.env.integration

# log
log/

### Intellij ###
.idea
*.iml

### vscode ###
.vscode/*

### firebase creds ###
creds

junit_*.xml

gomock_reflect_*

**/driver-service-account.json

### gotestsum artifacts
report.xml
test-output.log


# output from pdf generator
example.pdf

# .netrc
.netrc

# topkek
.bin/keyset.json

# makew
/.bin/**
/makefile.mk
/cover.xml
/cover.json
/cover.profile

temp/
.run/*
.tool-versions
