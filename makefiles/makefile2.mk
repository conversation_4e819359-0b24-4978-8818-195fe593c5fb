SHELL=/bin/bash
GOCMD=go
GOBIN=$(shell go env GOPATH)/bin
# To check OS arch
ARCH:=$(shell uname -m)
ifeq ($(ARCH), arm64)
TARGET_PLATFORM=--platform linux/amd64
endif
REDIS_DOCKER=harbor.linecorp.com/lineman-infra/redis-for-test:4.0.10
MONGO_DOCKER=harbor.linecorp.com/lineman-infra/mongo:4.2.12
RABBITMQ_DOCKER=harbor.linecorp.com/lineman-infra/rabbitmq-for-test:3
MINIO_DOCKER=harbor.linecorp.com/lineman-infra/minio-for-test:latest
REDIS_TEST_CTN_NAME=redis-for-test
MONGO_TEST_CTN_NAME=mongo-for-test
MINIO_TEST_CTN_NAME=minio-for-test
RABBITMQ_TEST_CTN_NAME=rabbitmq-for-test
TEST_TIMEOUT=700s
GOTEST=go test
PROJECT_GO_UNIT_TEST_CMD=$(GOTEST) -short -timeout $(TEST_TIMEOUT)

installdeps:
	go get ./...

integration_deps: stop_integration_deps start_integration_deps

start_integration_deps:
	echo "Start integration test dependencies"
    # Use --user redis instead. To fix `gosu` issue in docker-entrypoint.sh on MBP M1.
	if [ -z "$$(docker ps -f name=$(REDIS_TEST_CTN_NAME) -q)" ]; then \
		docker run -d --rm $(TARGET_PLATFORM) --name $(REDIS_TEST_CTN_NAME) -p 36379:6379 --user redis $(REDIS_DOCKER); \
	fi; \
	if [ -z "$$(docker ps -f name=$(MONGO_TEST_CTN_NAME) -q)" ]; then \
		docker run -d --rm $(TARGET_PLATFORM) --name $(MONGO_TEST_CTN_NAME) -e MONGO_INITDB_ROOT_USERNAME=test_user -e MONGO_INITDB_ROOT_PASSWORD=test_password -p 47017:47017 $(MONGO_DOCKER) mongod --replSet my-mongo-set --port 47017; \
	fi; \
	if [ -z "$$(docker ps -f name=$(MINIO_TEST_CTN_NAME) -q)" ]; then \
		docker run -d --rm $(TARGET_PLATFORM) --name $(MINIO_TEST_CTN_NAME) -e MINIO_ACCESS_KEY=admin -e MINIO_SECRET_KEY=password -p 9000:9000 $(MINIO_DOCKER) server /tmp; \
	fi; \
	if [ -z "$$(docker ps -f name=$(RABBITMQ_TEST_CTN_NAME) -q)" ]; then \
		docker run -d --rm $(TARGET_PLATFORM) --name $(RABBITMQ_TEST_CTN_NAME)  -p 35672:35672 -p 45672:15672 $(RABBITMQ_DOCKER); \
		until curl -s --http0.9 -o /dev/null **********************************/api/aliveness-test/%2F; do echo waiting for rabbitmq; sleep 2; done; \
	fi; \
	if [ "$$(docker ps -f name=$(MONGO_TEST_CTN_NAME) -q)" ]; then \
		docker cp ./scripts/setup_mongo.sh $(MONGO_TEST_CTN_NAME):/setup_mongo.sh; \
		docker exec -t $(MONGO_TEST_CTN_NAME) /setup_mongo.sh; \
	fi; \
	if [ "$$(docker ps -f name=$(MINIO_TEST_CTN_NAME) -q)" ]; then \
    	go run ./scripts/init_test_bucket -access-key=admin -secret-key=password -endpoint=http://localhost:9000;\
	fi; \

stop_integration_deps:
	echo "Stop integration test dependencies"
	docker stop $(REDIS_TEST_CTN_NAME) $(MONGO_TEST_CTN_NAME) $(MINIO_TEST_CTN_NAME) $(RABBITMQ_TEST_CTN_NAME) 2> /dev/null || true
	docker rm $(REDIS_TEST_CTN_NAME) $(MONGO_TEST_CTN_NAME) $(MINIO_TEST_CTN_NAME) $(RABBITMQ_TEST_CTN_NAME) 2> /dev/null || true
